<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT DISTINCT ZBMC FROM (
		SELECT 
		     A.AREA_ID,
			A.ZBID,A.ZBBM,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBMC,
			A.C<PERSON>,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群')
		ORDER BY A.XH
) M
WHERE ZBMC IS NOT NULL]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		 select
		 branch_no,branch_name,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2," and up_branch_no='"+pany+"' and branch_no!='9999'","and tree_level in ('3') and branch_no='"+pany+"'"))} 	
		 and branch_no not in ('2097')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="company"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)  
		AND A.YEAR=substr('${date}',1,4) 
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')    
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	   SELECT 
	   CASE WHEN A.TREE_LEVEL=1 THEN '华福证券总部' ELSE A.BRANCH_NAME END BRANCH_NAME,
	   CASE WHEN NVL(DNZ,0)=0 THEN DRZ ELSE DNZ END ZBZ,
	   A.ZBID
	  /** CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN ( NVL(DNZ,0) = 0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ**/ 
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID  
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+company+"'"))} 
)  
SELECT * FROM (
	SELECT 
	DATA.BRANCH_NAME,
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	TAB.ZBMC 指标名称,
	NVL(DATA.ZBZ,0) 指标值,
	TAB.XH
	/**DATA.DNZ 当年值,
	DATA.TQZZ 较同期增长**/
	FROM TAB
	INNER JOIN DATA ON DATA.ZBID=TAB.ZBID 
) M
ORDER BY CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END,XH

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="单指标查询" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240702]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="ZB"/>
<O>
<![CDATA[dyll_20230925132406]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1 AND A.ZBID='${ZB}'
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level,BRANCH_NAME
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' and branch_no='9999'","and tree_level in ('3') and branch_no='"+pany+"'"))} 		 
) 
, DATA AS (
	   SELECT
	   DS,branch_no,A.ZBID,TREE_LEVEL,A.branch_name,dnz drz
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
SELECT * FROM (
	select 
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	GS.branch_name ,
	DATA.TREE_LEVEL ,
	GS.branch_no,
	TAB.ZBMC 指标名称,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	NVL(DATA.drz,0) 指标值
	FROM TAB
	LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
	INNER join GS on DATA.branch_no = gs.branch_no and data.tree_level=gs.tree_level
-- where TAB.ZBNM='${ZB}'
) M
ORDER BY m.branch_no desc,CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END 

 




 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("company").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="11ab8167-9496-4e2f-ad1d-f6ca2475e26d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="yyb_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="company_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<f.<PA.Mob]Am_>Fd,`@;(7nFC!m-\Q]A*0;Mh(FX;Pao;"Hn7H7&<B;*@2EadNK/W8dQ%P"/
32;!X&c?6\#_7P5E3RBE$Z:3TluVf3RR&[:l$WDr,EIceeQPq[:@"2Usm7r8R7m55X]ABJ\I/
`B:g3Hi-g"O'k$8_:qlSTB<Y_OFDfljb[6u,eg;@P$\%\"U5i>k79M<\Q4%I;MjgVm5%X-=m
,PI]AgGW)H'bjb+<jP_Er9M1?]A3:hVna![AT7/(qII,]AAI_@CXq8`KKF.&AM_1Z1MF09erHPM
4lnelOD.\]A(838\V`I86Pnc5LHg`g;OQ3%"fE6/R/?*;K*X<CqRXagom5a+22;loEC8gW@h*
]Ag^*8s+1E(:<*"tU\Bg<`GP)bT(TXa=S$kIs0[?;V:OBEmGG,\4uSO9d9!j"^%is^q8\qX`I
GghgS,D^IVjMDs21-$ZRiX=5JR+jkPaF]Aehca#0*(=!@8[iLGa=NZJ!\,b2YZ+5;U2/WHVhS
5IF&_#HgbLAgA_C/P>*n=+'pSps)98'HOG,R(Vk7P/$4FUhOf$cK<>AbW.YHIEMdB7;n/&2m
/;!;9KA(%MPf*Ks8(MK__pn6=%stignFHiqi-?(&+;GUAVsr*etuu\%I]A/A=*+jGo$"G[<Ng
;jqLOO1LYp"/1F+J7#OFY_IUHI]A57f-RJH(E)'e>3TAVUn9qg&A<!UA;`d59`AP]AAjr&FogD
iKE2eJ&<St_le7ok:`E]A8*Xn:#KF8Jj]Atbh$J?<+OhaEZ>tYomQr@\]AE``iLC6oW]AMa'D1bh
p@uVlu1p%=OgeaE&La'+oOKjtc>X--=;6\`@#Q>(]A0(CG4.3QN]A!s+bAd]AW=MjLQPAID4hn2
$,\86O10I]AN,)F`M4/nIm:@2NdUpqC0pan8b?%163m[OCA^g)4WRWMhJ6X]A.X_KXG-i.God^
?"'"51j1c4;C4&"JInQ$7]AE.;tfsZ7+p1R;`$C6]AO@spT#:A=GMg,l9@I5(m%Dp)5OcTA@uu
PRZbnAC_=b[)?9$DB%;Me,H(:Bq;9(#hWbrU@rDcf*;U$Ls+j+SlIf88!+\YV",KFSIGpk>L
l]Aab!l,h?U_#XF#7ONJoe#Nkd-8Qq),t2ZlM^-"prLrQ^SnItI<5dAYaoMmpKhPAs@L`ZK[`
]A5A7>&K7f1gXg&^U]Akg2cmr7U:tnOS4+.-<Y3$)6tt<da!03deV*sH8gqt)]A4<Ng28ClP)Z2
&#^PSOL0D5.Fq2l(#'Z;&/`JODdO\,C8eGl>qWhq2'@#@21lV18dHk"X:"3s)&bj']ARjf;2m
D\0#>8Em5%?nnod+-N?pC0t2ZTf8TUVC:Ch+AWJD4MVo(Xu*PQ`6W-h?MGqLFDm>aJ#L\AgM
`pST<A3Go2S@46YU:5W`,<dEYt#<X-g;Vi&N0WLTr_`am6('P^P4E4L=cn6,dmr((:Eg?-:C
8UBW24L2[<R`ZPt'8]AoBccYH0XY8WeEA6eS5)<?,F*?imljI=Th*A]A_+t[Nl'ae1m0jkJ#kq
OD_Om(a4*V40F7*X"09Jl\s]A0&aEK[5kIJ7H`T94bh^>EWT%kocnICiO\G&ht1Zi!*XbA[6K
fbJ@AfL(YEYQ8KtX(tudh&C]Ag&"mjJGcuV9g>mD,;jkh@77'dkc%>L6!O?7T.NFbc08,\RBU
+7\D5krOMX+Q&!.B8`%,B2l>[q0gc=-fR_"g/%XN!SJ`8D;;GMd]A-sF1_uD*rbJC@hH>A[%f
"6%GD@]A1^fbb?1;a(H`jn#XsG/I@$GF6.DT@t3*SpI+3c`3=3N=p-Mpj'\RqmDI4K7.[+<J"
2f+H=S7Skf4Y,2jg0;M1h_9d/>,3+Xr8)+hs6W18F5rdW2E`FOotr*(Be^pj7fBLWMX:gC'P
XF'C_]AcV]AS>X(Z+JJ]ARXo-FR0prM+SouKKu8V$.&pC7FcBi*_r3l#mVH9.%'X$$<j8aa10O[
OVBPl#q2u9b$^#%-.qdI1ni""%CE&p\N;Z]A$@a1\UA7DOE&0lRcSibUS*jq862"s?#TT6WkQ
cV;5(^nu6Ma1.0A3lF*mjGF`YtgJ.iZl[K?g5"?IhV#K-rU[@,:0F5YhP%)D*e4tOrZrJ.Vp
[\+>I*$]A.loZ.R9MJ%k$PGUA),uSMJ2uqcIs.+EK=?4W?HJR]Au8D`!;^q*ej54p.+Cr8q)UE
<lEdcC5cQ\OE`RcPJR1A"L4M1%n4unXQiiaU+Wo'TSMHe3DsU1U4?ATfPnN`0,0jbWnW,6LX
LOY>.\rmb-3I`Kt9)[R2kX0&Z5]A8dt=ZTK/n::I%3-C5+.MA-X<O#?$<-ZMB;CB,!bK0G>_e
R$1BoNp*qtf=4k^OL!^N=`ahI9q#'?C9I"sfA$Q)ad'SD7rO8in?HR<9Y(&Uf[G+r%\-D$NQ
A;1PQ_eP6X)iGep``J3)<L4K&U<S0hNbKR@&<8n3Rl3'n^uAWbH3G^dfCqf:\9A?an/s0aDt
J-9Ph4r2F^YVk:M6F,d=e7J8!,:Xj_+MF>t%3guZR/!N-+&h,LH8Ll)\[D^9XA$\"H9Cd]Aee
n[e?u;W.n&Utr]A.eG<1sG,]Aiu'8fm4<q2Z*S8p;/0+\ptNq&PN$WFl4'F!E2)H50FeV]AZGWK
ES&L26W*%ZPWVYmoLZ-n4Y(d1(b,Hn+\1^X:bWUDMh.i+QtWo@o*Z_kq7.p2!_=dt=t.k<.W
EpCkj?SLrRE_0X>JRRPHs\)o<RQ:.ZJ]AL,u]A*_:1f'[%bo*gdFl8+qc8LbO'A1LX89*>9]A5D
qVZZKiFVr?)t!+AO2Lq!n5o&&^Y^!M"tWql.UG5&l2+UP:'Gb)$klKqR<K<[!_>!m3k[i=uH
lF21"\4R\%5Is05D_eOrrQnZTXO\X!G\=VMmL3&?Y?%_-6sb9aW6,WqB-=r22ip*\7bjT2VG
a?j&X0?JETA^m!$5,*rVU[ou!kYIQ2Chn/G=?/!82\,"grt4+d`AXkPdGAQRO.IaQ3`qV;6g
l=qor416QC=K)rZ;?e9_)ec"-%#HjRkp-"4sY-#"fqjp76Q?=.C]A%!th+XABY7"e8.0(?2Qg
"HUMdM#1@_3WsP'(+^VT3*(U5QfCK">*L;iDV303n/sR'59U`JsO]Ac#/RNhp1"UI+I*dod'O
c<;,<OW2N=6.fn37k)0G1tskKJJjYb[!9D*5*U:Qt%'8PQt?dWU'p#/kSodlcH/-V4cI-/(4
Sp0;+'>`&t:#5ieaY-0Yc"Q``DsJ&fK@8'u:Xs-1P"ebW_MfN"\K]ACJH`StEN38$W3G5)t]A$
!L^N1odh7#DrRlW?2a]AKRHB*#Fi1<P"Tfa2NF?"lBj<paaLb-$!:Tl&lRqm#TZVYQF%^e@<9
@:'FJgTA!,(B*Q0LPp.[F+^mJ-,Fcfb55W6peLU\'s)W-j=F^5/*92&68u)dY2Oqu.*=LqB#
>@A^Fg2LNYI0Kuur11*j/Tt+EL8P3]ATTc0"A3C0RYq7o^Z)GAr,GfQ3(F&_LB^bHV_Pto[!N
L!_XJh%l(>F%#TfaIpAMOm=WUc3WE?6kE1X.YkJ\M0AKV2Vb5F*\)77T2_:*ac8pn]A6!Q#>&
I15c!U]A`n7pp;,"?cbE1\O!,CWH.KcS<c7]A8A)8&iJVWZlrX6G2*jhUg!J:^.\.-M]AD[e"ss
bbG5^ftbQ#M8nnVW%6m'L:\,'9[(SML.S!t2Vg/2E]A\#R_.\)>Xhr\(!3[0#PZhF)(QamRr+
dQ9EH?TmrLek_-&8;"!n/Ggkh'3fhpJY%$R1*VXb8dZh=TElO!)ArJQE<)E^Q7t;+UEKX@-H
,\"NM=FcO#7!6u'o7ToWZ[j$rQIU2#K)^nN-8f@TFnrqTMn=FtG#7Xfgcm\bnASF^s:jEe1U
=UE/WDa0[]AmLHN7T3tQ$XLeX!(>F<-mZ$T\\+:P?(:f0B=tMR*[5LsN_#@$P\fahPmtnVbSi
9qI!&J["+1"F02rWQ*!1NQR3Wgc*1E7D?BX;2,q)GAm\%9V3%@:S"fe-OVeRS0Q_3s'>#=0H
.T/,a^cK/Q]A:Pr#B!4a`p6q>E9m`3CpkXG3(80J"&'#bTH-4(pUHq/$;3g;/S6O0p]AWEdX%/
;RE[W2m(,;1\BMo+*7Tm8+_b]AgA$`k`'G$o&MJi'QV0[>onMoq#$2aj38r<#`7Hp%,&96+m#
qZuu&Tb(<L8KK"AO27dU"WD/,ZaNeii0g4L"4#kB5&9RC.a]A;d2ergZSpYg8V^kM]ANq`#F-q
U'u^0gU1I7dEVj9TPL/C^fJ_>g+,/HD.PV`dr[6r\uC1<Q8&[*bm-J=e,[@$\dakg<7">p)G
u+e4AGllX3<@m!pD.h3.N':pVt)b*bSO#>a0(BVN%B&!-Ascu[X74S.I@l?'Kj@5P5PKKPrN
f3#pVju_2\IWL>2B9o&!X%rmT`d#JS2`'a\$0T>:JnhmEIJ6R$2Y>E/it+MrSUcZT/p`']A7?
b.933/VKqc$u(*]ATc>:Q2U?9[FmdM=X?4lZT=K:r;X"(Qj3u%(;5;=.g-l.3kZAlnoa)`;?9
2?!]AqM)VdMQPUMK4SQE`u%>X^hY"nQ&m-S&aWiM(.KELf6/0V9lCU.C7.!Y2lcW4?<L9H8/(
Og-]A#8cD<4=F4Dh-'M)6R+9`M_WiI3A&>a%<C<GJMVBZX<[fhKup=[mOk7j@jT;BkNJ<,a=c
lY&\)P^0e4YZ6]A5,Ek(8^LWZt0K=CCna9Gf1(jk5.d@=KIB`l4Y_N/#ie="8I&58Vcg!foHL
nQ.ni]A&L6`e&2Is;E2Y"61Pi'?$if2H@p]A7K;(o.bOX\_V^e(\FSXdJL@Onm-dIH^2uLm'JK
1QV)`!R<FaeiuRaosLfm`p;2X]A[(O<^<ESge1V&ia\&Pan"`H8ruW7>@p9AQJ`[?*:k>O\<[
W%im@Z[b%-TBL:Bm'm.[>GBK%]A-.iLH_EQ(ITSS;q$7#Lr+.f`5r[.-GCiiA*pHA7/%i?a!]A
2M6/i!Ca$>jd2r]A>M=SMY;?t!koLaY$JJ(nUf)8FXLeCBJg1*m]AlV%B:V;']AVN6(b3k(MrjB
R]A%0>'M/sCAV[BQ-F(p,NCkY[67ak1!%''L5*`+>e0N5T8JMn[h6]A[FJ!UsGERrl,r607C6*
l!li5!7DOZfj#eq,Cb'C]A#<O+q-5CcjX/'HXUo0n8e>(ffYXj!]A<_Vi3=8M&1pc/"Q"qlb,c
DVI[t@Z;rGb4l_pO=R;[84$$<\j6DsA45=IU=C1*B2\9^gR*15dbWD2/:+U;fa,A%a%-ND=l
rH/W(jKP?TF?rNaN#KK!C=nYJ8@";?rROd.)q@d=\.?=dW"\u0KY/g*r$fS/"F;HY*J54KL,
/Zd"pn:]Ag>nPO,=h,)P(n9Lt85U]A-L51O8BUGHD2@.62%eW9bWR^jsgnXtTG$,lH$Y]AU:;:3
7<Xd<hcU[U(6fZ(/0f_Sh?0n6U\%Mf*n&pStH;Pi*U5oIaA5f#<-#Q.ZS@eoqkL(]AW?h9\8.
/Q&NR:e^W'E`OCM-M`'+[`h&?d@R9'Q'D5N*qJKRUF%^<">\-N'>sgEZ00NU3^o`EUI0S.r>
&11HYP+u[^;LAa?(K?71X,7bhm6;f%b'dM.a\$@s^_e^&2!F.P*T)CHu>AFcPkjXkN$8`_ri
[6Ts\G6_c1!p/)K_KiU>!BS7qOhPM^RF1og@1HgqtCc!_(cn@F%kcQUNjJqR!'uK"*Mk0Gbc
+o$.,[:+n<nH"8lIljYMeO]Au$PB;'88(R$mhB_Rgr7.WAXhI=PqIde@DDI.7nI$:UR(3ZhCq
6,mKG=n3-fJI%CQH4-3WpQMZ'O%hp16['0N3I%N2/1.(?1BminY[7]A(*8Tf\TDpi<^VrVYNE
1ittV=p5b]AO%&s-D@$Mjeh9sJ/,iokfYTUAcCu2SA@2FS]A,@=d-_2PnB0jk(4!Du1fJUEdI1
sl:0;81Ma<MW[G.:iZ(Is4+^^bAq#hH6maJGL#%48j;eA7]A(!cp=f]ALrD"0E!U'B9E"-@t>V
UI.Ts=DEpVgT:A3:Y#/AaLpb(s"tb:n"N8kp><*YLS+q1n$K#jpeO"YpXK;LXHFnZ9g@)l0V
_>jcWUtZR.WNl#H(oKo4mo3;_g+g+*R)J]A<$t6P+`>Jp4!+TpQdg6@?c9c?FQ@31;(E#<*sg
;j^-lkT<umcPiS'&6`Knf8I$4AtIW+DDR(TnVq@9@N/"Qng)\LdN,M_6]A]A?d6]ATS'TjQZkm/
0a]A^OA`EgUfbPV'#/smEJ/NI0`soW3%Jo"M1J;qcdg,VL!u$Z=;iPcrJ?o4!fXiQW*h:shGR
NXl8F&CJ8,/B9*%n^K[Fmk53_S\plr3DLL$Bb85\)'-<$\MX=gKMgj<CkC5_T&$)!Q[.D)bJ
-'GY7SEnRp@D_A`nbB,6B9_o\T`0UnH/UE7!n>UATB=N-AU;K^^F_8d]A2_pi_5;f6pjE-"bD
IOQnrc.#KF!fDH:<RjU)S,^s"_^NW/cP/SUSJ^Dnr.,pFSdhopIVp4GR-WK`2]A"@50$Y]A64Z
Gj$,E"DRAcD16m#]AVbJj"^3lf=NEFE<4:tC@qleIR/@4odl6URrJYbe(@6cH!6Bf/u6G:`jM
#*.uAW)5Y@CV/[Q.!/bHrYfhA@dGaD,H:>%<:qHe@mG_oYW!e1J]A@[$J]AJt-*/lbu,,lM)33
?3o8[CGm3D76@d50i68C6HG2X(,00FA'+FRq>k$C85V!6c;_:$gcdrRC)O!GTZrg8E'%.!E'
$o#`iT,/Pb5FGSS=R)W(kZIluY-p:@_^r=)!0M(b.fVB1W`Ois\br2s_FK/Dtk*WmpI[_-J(
q':?jW#R_cb3CJr\4jsB?60Dmun'!*AWp3j0JDO^aCpX,V;e<0EA'UF<NA,%Y8ms+e`+9PBc
pTQNJpQFQaT2<Fc!HbopYW<jYS\]AiCg^Xo#GVoF,&7+!R,Q^:jQ\WU>[R^@p!`Ct3"_ZnrQh
H>Ot0cEDq0nrpM;;:$:S)8UUjrgVg@C*<a<:*&:]A<puG^0/1QZlSc2KMZ1tc-G=F#>1XodTL
cPp3S-"]Anr7kmeTl&Tnbj*S[.jmaZinOj:]Ak,/dY3up@pD`FY)DuQVj=p2cN"]Atgd7TMXc"7
()J,nu3OUH\%MlA%]Ad@NpCosCi%Kc-G`_Jjp_PG&?7PA"`?Ae#*4"2>mdZ?RM!lg$5(bIV^q
^8uC_mg*lIRDg*?lL;4?cjqB0Y$;Q?4#rAm&ge6a!M5Le,Lblph;Q-Vf"H-k4L"f3%]Ak>6e8
IE%c'U-;4(<8LRPi]A:$EA-hZNhAFN1lJobJ;f!pGcsZ+c`"e7P)30SOJMX:g9JG*98oeQ/:@
SsjNeIG+:DE_ZR-amgtRpW*;A;:4U:%6u@Y&M:W>VdQe0^'lf)&,$+gg:.L^IkSs&T[EAY`9
_%rY<jeHGeMSFJ;DLS&h"'M5+/Ro3j<WFA&=&$am^";rqSc^*@+MIc$K1*n&n(4M6_*s*U/F
k/T%r-95heJgGTI;A74h\)-^Y&R93[$E%C6Ho7i5L,7=2bd+DmTcb=fo9DI`@SE"'ehnWj9"
O-VC)^<Mte"e`T0HaI+L@R3mN!kU@nY1l0k3J",jlGIJ#'<>S--H[kI@OWh%:YlP)]A!]Adg@n
3EU";Cg-bC\5JY-[(,R.8*s5Id]A!B$+7/g\aI5'WEPJSt7oppN36C\lcgNC@uGq8I$@kCB_(
\/hP9<4XZ'!Q=I5bTpp4JTpG=5QS9@"XRpq.NesYS_=.CCFmh1>2Vb^L"doIaPD@\g^D$-J.
sdncDJ<$`I(*?qA.*f(UJJumu^0.*\^h,+3IZ#%*0Z9/(/mfo?.0@)SoF*D#&S+?%knW%jc/
*-B("s%SE<;C1@Xs#q/c-28fJu,i<&HDk\jgiAS2X]AemF9MT:'2B'jP!P:hMqK\<BThOaA9f
g'ZG4Gg#mRTA\t(3[5>&^&&'/<"\)=]AkF_O0s-`ae"26HOm_ofl]A_U<U`u)j?+;?IjTPaY!%
_qeK%TFJ"rL)13@.%M9Bdj=mVU&>:5iNJge0[Nf=fbC@cPo_Mc#bSR*r@]A8=n@6a%TAA"kH(
,V;su/Za"N&B8f[]A'QlA@P0%@4.$.C%L/8=J1HN<41TP+Vb;p@T-i?<ihbjK(KO5!OLGDQ?3
7r)*GW@UgA\2^<ZlY.0PcG!.Db=aE\mLn@n;2c]ATumG-.0-QHo9a(3(OnSCH^=JmWuq(%e*#
@=I<\'#<EP&gAgc<c_eX^9FuF5!9Haq/I>'BGA[k/_M2ok3X%'gSdC=d:k>?N!1Lj&Wh6gZp
Kf\_gq?)mD4"3h#=M2<NW3>:A(W[@<0/3640=0QEj,PIRDTK5B]AuZ=q0G_O\C$O)mJ!AA33j
e:b\5%VB4U`dC)6&kH&$J*]AH/Y'Aagl<i,+mLT7R--!?n\;ULc%b,Aas"1;`O6M!4SCOcYt/
W%MK<iAbR_TVi05IhH#mQ.;^nZ\3IOJq#3f#?e86$M3mASP('njETB?lH'!EA3N1^DQNU.XQ
0[UdPnt03!=Gt<%2g<ZXuA^]A(UqkPd\!BW`4Mf!WuS2gSLU\1p2A1I6(>V0O>Hrg#2)t#W-J
BRi\[_dK6k+=>H3+r*5pTEkKgm(8UMKA+am@8LE-!1GU?q/),@fZI*K`DRIaDr*gH<^]AVnt;
)V%U%K]AQS1L7QM$W1GYT_U4qk2:QpK,8LlP\Ts%YN!iJ;]ArAVCq8W9ZNIl1);R$Xm-(=V\N$
HMq94.+iP6,;WNk,'WYJq@ghRQpWD/cp7[2B@,1@ok1`LNdEXCiufnu5fCgg"N_\mQ#@J1JZ
NnA._;nMdZDGnA+Cnh;TlH@CQ)!u&g<7Q>9D-.OX9X23;s16#K]A/H?:h::-*f0iQI'g;lU#7
fd3ZuMl:99`7M,VBM(:*h"kcr0`.Y"pRY&DD`kp365sZ=<b;;T2Zajj46:\K3"LS'8Q1N>a2
/mR2)K42/oa$mZGp#l>gErXGFATC+k%VQ.gnIbn<lQGUH,`aap!F\sLWqGV27!OabaWaE]At0
)+15=94]AIcdlT;Q1GRjPiqUiE\Mi_+:'3nZG#eIb/=[j5#9WjSS<=(Fp.B*U0M[7isC=lRn-
K:iSH<\jq.,rp'$pod@<hY\]AP[AH_L?AW9B*EN(8c2F(/ReP"+KUne9::r/XC*%uCHuC>a1P
&c22Pfp1H0_sraOX=o4/?fk9'=1MaoAf//s$\(:J['8eWFP?%p5C^jK6bmHF-tl1.[:7/MR1
B[$NO=!Q\Q@D2a1bJ+'[R>Qdkr3h^,<XjjerB-YM?O85/Z'.'Re?k8P!d#[f3+qB(#UNQ`GL
&V7q_br,8d3r5o'''Q82Xs81XN]A9g]AV;&]A5DkXP`Q'Q`?Ip^i.4b&B?@65kfoCtUrHe+df@J
Q;:?R"$0@WfVBuQ]A!"J-5BSt,Z\g@g4D`tmVF`;DJ$(-[&)VPo(-:$XP-2Ela?0!&I@iFJVI
qHk=\SbW<sc>qiH_a=P%<Zob=MNZG<a3UWfh5fYTagdE5)\;jUGVpJ&>prG*l\kWat24S%e<
HanQlqrI]A=ok#`+b=\BQM/ng?B\-<&>t)'3'D]AZ)g4E+K2M62Kd\TU4oS3`!QZI<lo]A_U9k!
W,CPlLPR!D3%P1WM+N9M9j,.IUOY:JRD;;se_1#ld>*fh7YGMnNsZ'(qS&+-4K2RW^.GdA'4
d$=RDgO-4C/jInX'G'<n\ZEfX7:[6utl\g7[Qh0&mEa^5AS++Vc**;aHGRa"8oV,H/J#)L&5
6)ZX3&Y(.,)qQ;Gd,hGU/&JC!TpfII*9MiMo1FVOkK5qBD?VL["gGmfMeuGGn(uT;Y+^>6rl
YB/D1N7LdAeeY<bU'na;$J93,F6$".a4!BcbEE?csL@QPr;k#2eH.XIrBKJ%-89[['B1N)Q*
r4L42fj?H)".Y-8l0Cl0;@SssRj+Rmg;Bh&Q%SWI)cgu8Z+Y/;^Sg/T-'/:QTNi6ui[@>jAN
H-=1XJCl3OPed)-jlMZA87`[.UC6gD-46.;JQ7><D?9XmUo8_U2*!ZRed,a9lan[RF`B-YP'
Ep.rEL%7!gVHrZ+G0/#/-L[6stOc,)VXJ?E<%A/7']A'79$Z%!f6#[k?sc]AiJfToo-@U3uYF"
<=2=YY2-W?7d,1PD#(gQBnm3_+irF:q19CAO0*9Z*(..N`%r;TB]A0N-+"73mKORjW4dJP[^t
cE1Hk_%gXH4[!T,C+Gf7^a#-jPILiS&F%(3eGnT@2;o\s`FH/mKLXj#HT(FmY6!OM0ZQ=mlb
0s5?@H_Mp?d&<VG\73!e^ML;fHW!3sBl2[HfYCI=o]AhhkpNacGSqm_uI_eD5)p]A.04*(da8i
IH+;Yn:(mfFTIIsYm<r7/Nkm[KgDMR;O;0DSm7p1FLclmK7FD+a=6S$@Ikc<<\jVhN=7o\IV
!HD9@Wh,$]Aeql2@4\N^::e'KGD)s_L#dIoKYnF<P&j%:dI)#(TdE6o^JDJu1:^OL*B"1>BQ`
cH`cY/*To*VK`PbQ*%d$M9JV!K2\JD=9(_>jrZ_FsHj0'qq7`;E$'u<D"+[P9>J9d4X7Xd8G
6s'q"']A84^DEP*k^TT6&5"NnLp;$Eei>EAu&W@;Q%&(sQ4>mCUtbDL+Tb8(D+,MR\#V^rcD&
1LbRD;4.1oHh\@rW?5`C_L%aWl?5&-6j9*C,ltK,T$XsH1<1EMmIK.#DYjd@/5/h23CYfi<o
XC&H55S>,crLuZ<qk`27EH31j^;D2IKsRe,M6q_n*J["h+4reegK;lD;KH8uK4f3F3.MRkdc
77A;I%ZL6D7>F:8?@Gf-n\`Xtt'p;G32<cY0ZX*'H8P2e6&sPM&U/kXX@T`<jp*/[uEnL(bg
Nh:1@diT*T`@0i5lEe=P5,b[5s`Y_V<>goVZNC++@sPQOl?X580IJG6:TMl$n"HG/]AO'*lA5
I$9,1E;H5:R=o1@.kZ#Aoaf$.?,<F)9oRWOEW9AnS&353822M*<3EgQ`4-EQB'mQ+lg4YYV$
3>u;82O385i/f==gd5m#kh_,cS/2m(ds0-qb_/EL@(u*.>BumHBmQLqC,rnfZ,#'%o[T[AHp
&J%K&0%$gg!2%?=lb=]A?%@^9dlQT8VCLKo_TgkYlaJc'<MV@$4A5?Z>0"t+k%<mifjb$)@[F
LgoHBbZqP@UBjVHr`mpO]A\\)d+\/C#1$t*CWE2flFF`Nj39icBa\#)_t8-$SR^a7c%_m[)a^
5iDsD+[5D8U+l=rifWA'bpW4<f\PEj5+o7`tHXq"m/quB@#.2cOpc_%KUFp--tDHlFD80IYO
Yt#u@t"m1Z69`%Li)ngHDs%'Ja%loi]A#&VM5/=0#hhHRHcTlOh/.g(.30od7tleiM.V19F3^
"AoF+O87V:a0W0E0;&"\("2doh4QZkF@D1m/NuTS:\af*%j9cR&]A>Ym6FBH8:K(-RaIMb3_$
d_g`6iXjQ!F?RdqQ2+HPrhsLafh[!9WE35Nb!A$A1agIm]A+T#H9mj.XmggoXR;/:d1JLm4q+
0,:W/@js85e;-t]AJhXnrlVbn`^8"8"*&MiK8_S>a(,;JNOgj3o-GtE1TP=PL*1+t\4kXP`W*
>5r8YE'$"Ze79%<WKtSe7a0*m-9p#D>NE!^`"VHiis&"@GB*n&.1BUW2`]A)DL#T&"O^MbLFs
JNrd13E!K:,VE<_!k,?roP6.%gPWSTd1O*-boi(q&EX^ETr-J=,bY.$?]ATISU;m)ff=9bfSZ
kFu;d&;mZ$HcF',GGuL?bETIl9@@U0fCHXPP3Z)X=J)o;(nNCQ'4[(^4l3E=;O]A0cL\M9!,)
?a4?%]AfVM0AQ[X@TIBYrD<#j!Zj3nuYm%bN$G`']A4jl=l6n&h>#mW*BXWe4j1s`*'8`B\rCi
iQb;A2]A6[GS^]AU&!@+O!&G,IZYK'q6XYIE/^[";PRQm38fY+\.P%nJ6XU6I5VFe>r9("-#V+
!j?qJFgV.]A&[NX\'.U5;DrZ\hV`QmZ5D_jFcF*"aHDs'l5Q:P9f97DAG+Wt]Af3D7:gLJ5jq]A
inS32.?!5M3`%rI</'6!0hT9CGV<>]A/"J\s'SmHdSPW\,[]A6(Rc4R\K"/EC80+N`3+pOsSr7
$]A\R%mP\c2=Y`cSR#Z7,<76JbI%g>a(=QI',[t;Le4gBOeg?9Z:uo7pa69VY)"S!]A:o``QZ8
]A\_o=qDOYse6-/Q+^TZ6?)?^]A(MMB4G364#D$,IfoR)%:g3YVbg^BH7W*_YN7PP#ZB9d[[(C
1:'`tIY@RX,O!7MEhHPet845kg6j*@^`271+jm;qc904Q!iQV:E,=PD`$c*t=0#@4FUD4RV]A
o3op_b@A.?DI<q5o8>L$Je/:qN7E4kfPRh_KKm"678\H9YRZ%2h#:UIe^s]A-&"l.@B9tf`NW
.7a/Z0c`Eb=[PigP+$C,m&PgWq\Sub$#_k6ce:^RDKYOuT9,nB-f1R&ORSRnXTkt6<,^-hfd
O%XU7[r^rI=06JT`jPRrG11ZhB5l?7gT5>+,7KV\bDQbJLT_YA[57t+m95uRTSu,V*2:+(^g
Kfu>sfiIc<>[8(:T+eaVI0ST?kT:7B<R?-",;p?O"@:Y$6,a]Ajn$Ro$k1]A./9*1=QAHrN=V:
sd6stTK^9uUKKf9BIs4D>i6]A!%dPSAcZ_l5GkoX2B8[9DpkU[F<2cGIRH!4*"1<5d:>KF4.o
J&u[0a%M`aFE3'\(FHQXMa7iWjB'KW7^XK[_R(p7go<4>.g2n]Arru2<<T`fI1cuU&6qlZ^R'
kXm9J?SSL-OhasO+p)@24tWXm$'^lMj'EjdbmcNmV>?8E,J\0`]AKF:Imo?S*dJ%dtHt-T/V4
kn*:r&T)FTmW87A)\)8eP!UI8F_JBf)(7-adeC%Tc:*<G`c((>U5\lB+JmXh`TaQfX;Uq'B4
fIm3k7Y]AVWP20$_5:W?9$=Q%0F$Z$!&fT2dC2;WZea>2kO;7E]AhFH@7OBklZq^.p>GHUP'p$
4.!S\n`]A"Dsf1*fY7W7\!h0]A]A3`du?tr)"?cfGi&p]AU_)Wk.'$(-V7(ZTGV"d8G6r/;6F/-`
bBT4E:e1AI?G7!e$6_O3L$g0I3&VC20@><&M$"TZ\036irR-s.;l]A#q7cJML#OW.k[`LrkO9
f&_Q5Np1f=mQD"uG^ak#ome]A9;Jah\#m]AIi6+Nc7@KA@c9L?67&M1n5*)3#oB,CY>FPD!OAM
BUlFAm7g#]A0j_Oqd'Eg"dASgrY")Vd\cN\]AE^Wa_\0AF0_H9XQ"jU$),ALeViYgn$[fR-Q;m
1FI.1KtVZ7__7$6-2GkOR\6oLTSF<$s^V<*h'ghpgTq@%/LBcZA!?NC)WH.hq=g+>4(S9\EU
XNUENBNX!21G.c^5HS[$N9H$$pNb5V^(V7J%7WU:_7H`]AC:&B*M_f@&Q-T,6F'mJ[N0?(;7"
K`I^pgsMK>CYI,F7UL0AE0&:m-,2CW7rrE*F_)hpbHMKi3hnTA4Bd2Gimg77\aGkaS(uAn]A7
'i`^+CpP2@#;&#Yd@D'j/>0)V"pl<W7+&s@(@p>P'q`5;<"L.3[d;P_/KceR9i.oD$-kW;g_
V?oVoar/f]An8GP0)J9;<[%@'1P+LZ&Z'7kt#K=$i(0*JWWD*2b]ALk!"4>[L.VV>cigEMFrg:
LDjEn5YYj0lAUkr$qpOhfM&F:1T4C5Scb!1D]AnSFW5XN<Ec*k,45=G%k\`"4S)j5O"=EPZ/O
8$:cp5[rR5HYRq?\)"$Du9l4L9erNgm%tbgPA+qHq0V27Y1,.A0*Sc!/_(Nek?[Odu$nIGq7
'3lA2&43Mm@T>6P-'7^8pC]A0?J*qDN7l:HILp+_(G2D?=s2tc,aB)c^P<t4,U$%P7Wq!KiQi
$9#e06V5Z[rSDN5)#(u+YjR:B%HaR4+.<<U3Or3#Z`Sp/F&#cUk<h&]ArjNpmWfH!M1Z5)We9
$Sf(b=U`tt<[e^O,^g7IJ]Akm;#m$3koG?O1+lYglDi^2k,NS$R/#u(3?:D6pS$nR4[u]A=cV=
4Q=Epl8-(0Iuoh\\Fn0I'eQlZea-g4$d8*93Q=H95E*%,hXs$hE9NZHG7hTNsB?M(Ag7Y@BO
bB1;\;KQ5Q?rc]An1>L)CZ=O4<OUGn9)_,jFmR=2T#Z]Afk7H?FY0dgBR2$`i$ef)_^&\qKk+[
j;nj(qTW,B(=]A9gW/o>481=T]A)'mJFM+6n>!J,';U`]Ap>@j?IR_@t/8<9lW@rYXl'hme(0G-
T7Fn!OH3EKO)]A5MMQ`^N#8QO+#cWE)Gc2O"*U/r2T*gTWNc-KO@L7m>OY]AnP[!+FB;++U#5^
aYa+?cO>Qpnl@F$AmgdUL3qhHN2..g?$Ii\kuB#j2V;s?Rj)G>p!+/$B4m[%&ui!(>[S%,J^
D&1%bK61MtAKj8oQu/&O(F1997@li_C'qBIV+mn)n8@nH?aip0)1PY]ADbjo*:C)IBPl5rK'.
L5btpI#+WNe)njEd*m*[V&H^&_9LJj^Fs<e9CXPh+ek'Yi*#__G5)..XjRVltmqfGrVa>#OR
p0pjc_`?I1f&.Nkit5'YX.rFZJk8$9g4U\O.Ch[A9BHJI(<n\/%%A2bF:T(o777#."O$iCU!
S&+iO[:2O0ETd/sU7"cT)(>&g_^[i=F'oMU#(&BoXo^pEnT(R&'0T+UI.3&*cfo3)=pD->_[
F0(fZ>HYDciNtRHkV\=CfJ-aG9C;Ci^TceH,0U[Wq.WC#iaGTinS@<D7T4/<4/Cacog'>UU?
SiYi;G=Hi'Y)Zij+E0_lI-V3G'gQWHVpJ)1+5@P7sB:*tfmBPTbu_L=?uOJ"`i'M$X7>5t6D
fDV;(noV^Y2LmhYCg\NPS6A?uo;:>MXZ6P>TW%VEW-P]AnFn=6k-^j5Y*+:;XmD(QIT1D#:k(
B9O6oP4%E2Cj,@Te&TF0b2g-+%8*8C`1h[72X(7s#PRD7MkO+Rp.=@"hcI"k_2f1Q6DmCcZ&
51=>lO_N#p\kRe(Gi)+_#1IO1aT/h"X;-DMMMeiQUoA`"1qFqqH':fKt?kH@Kq;%q6Cr4+7;
7b5P)f`\tE'7,A<:,'G4%Zc>!4\nL+)ng1<b$3iepD&&iS6=>>XA2@^d>WSZ]Ach#.m>(SlB4
C(mqaJh-S[Add(.M>p]AJZ@sL2h-]AS2g.0,H7iRp<bhLc]Am27Ip74HBec3e=W#0K=P[k>,%_O
&>]A)'Y;C3X&=`YW'VouRgO:bWB^0*/*50<olJ^@2Cn>gY!\:H4egrAZ[]A4,:#HWRjUaMlpb&
h(`:Q#eaNls'ui-""7kS,_%YPsc$s0HW"Co+:+3Fla?oCCJhY63PTZL,,m8WD"tCr,Z:/2&a
]A1#gj\hc%lm]AF^/K6Lq^9/7d2oGq+jn?PKg(@*kd3dhChZ'<(ZmTpsY4G61+(Sf(UUg6A_;7
+54>$;.4:EDlk%Bc&_5u,r6S-G8,Z"8_N97RltFDokd]AB]As4gK\3H4\SM65$:s/#";P99I/P
,ifl5t;JTXlTjMTX`L394g`.e7&f+O$"n0jj]A+M\`*4+5_%GQ'M'q\G9EfeTVILCD?[s5O.^
7OrGAt,1SP@o(R]AJI*&j]AIR4.u;\tYSJLIVP7TrpH86G6_<rQqK<F0_6_TSNt6g&YGi(m;%f
MbLG2&NNm+/%sOjX_p@X+M@eTR:MOl(ROL,gYF*#iWQok/=^L19g_4)J_?fFjbc3Roro?-N3
9kXH!!DI`6=Cak^Y%\<7GB268)p2+c3XZmsT8["3BY=i8e6LmU*[YE$A4<W;`.I%7kr8Y!MV
eY<jMK)QWU`O0[f_Vsnt-:3!@B,p';g$`R5qbB\T4']AIo_,L-f[]A@9G0VEJ<@r-<c3Cb0aU1
1BOU7J)0DY7<4b@gN03po$7i-KDC4Ch,\[<L4jr"WnSFEDW.)u;[j-2mGb;"?h^0pS3!7)qr
e/ra?>a7XmtB,)a8EC0\R$'U7rrPE*"G\c.5A&"G/W'=puD9p&.7g>&:&`SD3D:6n(;:l[7H
MdO#C1nOX7)VD/XoqsE(SMX=Y8fb^?at==Q]A8,[U\dB8c1h=si(A#k%JV>.jH"&"RaNJ=\m"
rt@YSGCK7<!efqaD^)GjeTT4U3W*fu/3ghBl[;"F+nheiGbL3k#E>_:S!LUqR45]AHRk7DZkf
nG/1EqMOa;O#lc7s3RlcNQNX(Fbd;%$sJ8`\R[A6?kKp&C3qJu"2N"tB0)`=^Ro?CjEDN3%k
KRY7\M+2U[<s9[9&s/=%"CkOl#:7fDC+8L(0?]A@O)fK(Z%C:Ss>,]A--]A7N7s,PhbS,U%W:rM
nQT*bR00%oBJ^]A,aO&bum#`JO7ZD$SIcqWB$ZB,7HUUku)R4EjK6m>/.%RsAQ,K!S]AXQmk=k
<B@uSRSJ:e)WOk\dJ_N]A1n:&k?0>'[.-^WQpuU!it#>=*C0A7bPkTS2]A_)NUrcdsU2K[[DDR
-\JVnis52o?#4^-L#=:qg/8<5JRhoZFUDM+/$%?pSQSk/UmB-/4D%mSk8#.?_lJN:M51AT:A
BKS*m)@qg_`^Ad9%3lZj."$dDh:MPZ)$07\Uo3j55,nVE^Hfp0FWO?DM1/Xf&9\=@KH7o>@>
D'SfHKSmFpL0_*Pr:pLTj.u+m]Ab`#Wm_<Q\;p/:*&BnoIk%5`eh%:IeWi&6SBs<>X7erhfaM
CfcRfoh`SaPIgAii55t*aFpL0_*Pr:pLTj.u+m]Ab`#Wm\[_@C"0YX@%dlk"=G4,!PKK@P<3r
"n5Ah`0iU\G/:K+-'>3@>D'SfHKSmFpL0_*Pr:pLTj.u+m]Ab`#Wm\[_@C"0YlBbms-RN5anq
F\TAAG:Y[6Slr8lm>p&ATj\BH!V]A`J/&%dtct>GaHh5Q1N7Iff~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="104"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="104"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="1"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,2224585,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBNM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&;JG%/H_.+Z8uJZDd4&k:SCGR6:iRgTYsf<X.g:!q/GC/"OdRZ+1bmadgr;119!QTG@F
CB4Kbf['+<a0>6mQr/pGf!Jchm_9rqBZ:?!\?&kB$'4TATGrhr!h3E6uL>[>)E8U.'u@]A0ge
[(+i^ES,6F('GtP(\(S9);%V'%pFe^e"d?hq2m/^!,2qR2?M2RSPJV.BZbA*\`,+YgW9lIue
%U;;\pK:#Q0h>;Fk/V>L/qEeDpGtC&GR!PeSC`-.j*ETDtdpl74357`M?WA0sa,AFOT*hE9S
5#XPbEqk"ePq-]Ar>QLqIuDk98cIp`\8VGibk:Z>@qGT9ko4l/MDf9X36CcAPnj1foIDO48#9
qgLU&-\")PLYg3kiVR.cT149A>3k/Nd7p@Y1QT_R"tpY650oF;+63oHO$h^-83jrcqMYfq#O
ZJ))nc.^ria(,Id7^7s'O5]A>E:jEE;cOt>o?Gue(@F6+`t(mS3LccKPd)<IJp(kduLt5.r*8
kec32$eFkOJYSNRTE1^sSheV;9:QRbhRoj;VkZ\T5nb'C\<ppOA[XicIHWGR//EW%uUSC,o<
(R2Kmc$ajSa*qO);sb=U$eb$IpT7MIK0^^J#^h1iqk(>h-#BUK-7NZ`[+^ddrA<L^)MDPl(u
a4Z!%^k<4=e/+1h<t+eL[k4m]Aj"080YX-9/"2c&@'c2ESPfrT3+Sld#dq(=t/K]AjifM]A.:/Z
cRZ-]Ab3jCErWne*c-TLL5Ckru7rt/+5=D7CI#*?5\_tm,U&QYd`5F]A9-\<ggJk2d',9OQ]A<l
:EdHMfM(X5b(L^ODNAo-gtK4kpaTL"tR5s4b.geo=^]A9.\g'ZP>aQ,AB1V">k,s(dnNhU)4g
X*.Jj9faWh$]AY_$5nFJCX!OEr#!Pi"-.l;m,?IIEr]AfhC[aSlOm)cFeC1p670:)9\l3mlkfD
+H6d1A4O,s3!JPL(9i&gC`*mP&?A1NL+rF-m1Ps(&c'YjP3p79aC)VDO<VK&QENYn&!66rG3
O7NU^%0^%meDUr8(gX>I(5!e/M74H_\)3MI'e/GFg"Dom=9H4N*.]AH6h#*S0UiT/+9o?.@D/
\k3T`UcKtd0j`OFH2;*P2'YSn$#3[\AYcRaO0FoY61)u.$;`Eug?C)9ifSucT"^qKm`q_+7q
W?ZC^lDbB\GF;<)M@S\UtcUf&X2(V1bZe/^6,iCP!NM%$O,DHK?tSA`ps4V3L/!qS'jrG`i%
(dt)CU07t7:r]AO!..Q0=C;]As%t^\;^@9(AokpT'd_#ZS$"3qZu7hg-.g"^5u@Qa:_kZ+=4q_
@=ZlSB1(f[ARmfptG8]A]A\=S"8HZ&(9Od^$Im[IOC\V5lPkVr25Ge''+%Q[D1@D`b_$G>=2_j
:N@I_YMZkc?9G/UMII1>5G+-&03Z!&Ej=T:G4XVGX+YPH%0?s3)U?@2N+FI(uM]A@kQnj)eDo
6ph"TIJX:b(:dnBEQi-&qjDYoB3o53N$\jG@d-Bd2@Us0L\l;hAf-+YTapjE8:##H@VCr?`N
+R5$cA"IhlTimkg\!Y1G$Mn]ALQ4-5bbqYC4SKE,,_,Z>/k60HQ@hDP+O5+%UI(%SKc7g?.jC
+jOT=AT,(C3Ff7\%./RZGlnTg/pE\]Aq0&ui^>($)gOW6$$O(hgmj+$`PR5-!OEi".UI\f*%"
-pmulq..A+hmB2m\YTcO@(H,4R1"AUGXnq]A3j[U<c1n6i-3]Au.sK4gki/O6riu&''[;-:YD-
hE[Cc1Hl[("aiMGm]A/F08O,s]AnUo119$RBX<Z"L/uQZo>3Z&'&&)-sfjBOYG)5pZU/.4DaVM
+/Nd\cG_:"%YrO:3e=GRFt$Lj'Ch9sijdW4,MKtZ'NkEU`$utJ)Cjbe[HAQQCt"%P:>d.EpK
5s;Q]AdIgXPG/*f,Z,+miW01bl_D]A.EM+6G8@BRm^\M<9F1%..5NnL_\N%r[@Cegd-^G-b5(j
j.pMaCEKS&ok-@,c7c9Wr\+``7jdi:8(:tH2_H<kk=_!js>a4pc*p.n3aEVFEEtLR#<+rsBp
i40.rKJa&HGdX&:-Kq;[G)+EjZCF>Q#O&(9doO2s7sd]AOVSWC#btL1]AeM_4P]A5R9i*K=.]A_P
<LgtE&ZjhBNT:!BU2:;ldedNj:?\@ATZiFqJ%h__o:=h\\#hf57RB"ZR1c,e367LF=6cT)QQ
5'AJ?TI:0@/u"d";F"o8ah[Vjig?ZNd9d\G1l@KI]AG+&I'8k*-?_smSC":[S<h.dfQ2+Gph`
P:_^>Q_T%I=0-,OKQgO;ceT/JUCHM-GkfpUY@t_&0)515M!1/PJKcOV4MhJ46OqSb)A^]AF":
!dE[ToYh<3[:7=h1_Qa)4_q0^LV:?@+MANlWOYNga'F#p?'+/K@2.8SQP:/u5h"%0Og1,N?)
0u'Qq13aN4Gtd9'Vj7?Up6DjIEKi\pH=>GVe`[NFSZ0j.7NeWnEKaRl_bDGAHc>\GoCAt?^&
hY?f._k"N*d:=nIs@W#r9=k-fdTWK\Ir_f'NgK&(uGARg3[DMi0EUCAL1f>1i3$24Wq7oQ;>
NKpB$b,=)dX,Fo@l6Fk_(?GV&Vk27JnRF=qqndEbR^noYmL#^uHZe7.D%oo>U4jWuhitc(4-
pNT*DSlqPO-<'RH(C3!D`27UuX4E*a1D5^J0P8_\,`H?@5OF_#(NgQ;8)B$3/f3VHbg@]AH'X
]A:C$,mm21'?f`pkXTf)ANX71Gtcs3TNC6!gU6X)XZrD&^on%1jL">80?aR+u$`E+Hl70BJ\U
A3;WFrm\SC[n^0[5J,b94,-%`)2^/M9C/Tr3(Zl-j%K3hD6Hq3C\QW4lD:I*(Y?fjEGkEQ\@
"#$7bHm1^4h?#UOr5KQOh3ZE!&d1Xf_`h3?sRp\'8fQ4"3(n"c_tkbGm)6j*)SU5jH<%*o92
72I0&/o8X;@Zl2J'+YC/ZRsaSmdP'I[iNRNHEda4&9"g\*uFQ^&Wd2!V4$=$+Lh%!N\7j.BT
tl4K?8bt>QPM*74?FB'ueRU7t`pZ`+U)!Y_ThfKsNKnm`5T7%WO8QE`T_e,tf]A*Mpj>\`SZ_
^boN`?Zd2*)[(o5EqU`EQ\*M3^m]ANd?+5jC,4%\J<d@QRJo9>)[!AE:2&I9kt27nA!>n)MW/
Rdc[IU-7XjI7as<K9J/90<>HH8XghZG%>:3Sg:5gl.Oe,[6%(5?Z\n+D0TA=qL5_K6:n3L)2
J<$P_(u]AcGraDZ%MjBK!&F0OKk-[&H`N,([q%MS&DSM*C'BDH[gXgU/T9.:'DW`IG@?1VU/J
Z=Z:K=`8iD?@d\Xf]AG#]AMG[QW*!Bfp!\P&3g/o^g\"LYUO%A=)f[:c\D8dN4+u?n-h)0Jj`o
(Bsm%f4k7r4Z?r9EJWI^-0&j!W44RUF8phA;kA5J8S?lJ0Qr8M&^YTKKk8B+S%W'2ML[K[I9
S-V5#afXQ-1\aqV3+;AbaU<"?Sls5F84ho"4rO78:CTc/Ff>NjTFI0-O^q9GQ9-4E;L8?H=o
CQ`GTk?bPK+$]AQ6t,FMOI1d>A;d8tSmnf^:LC?SBg<F$aT[Hd'd^'cg(4(7K9"JP'Qu1u<tu
a_;psaHBn1$@/(p7j<>%o`keG:RqkF&VN/BR'Wg(dZ%;I,GZ`C>M)XYc).V@?M/YuYDDr9d^
+o)E\h_-dm[#:DiLd"NdE>bZD<^^qCM.VLBp7D+rL>`LhIIoU+r\%h()mE?)RY0;k&NA5ILW
Sg(;&BO-YC18'om**c3ON_-8FQ+dq7GPb\VU_i,$(4Sp5[g/40S1W\s,YMl5?jt"L==]AWKj+
[KN[2fYN@jp&Zh0$IVJRqNi[!eD#-K2CZ[N\BFhk1h_9qhr'E6,b9Ij$IM8DOl$iuOV^$>fW
L%4c&'I^"rA8iDq>[&GBN_n<!_(#dN_>,hB$)!+'mtD554XG<;G9!TQB$?9Bu:3$bG+8FYL=
V7Q*]AgWc6Hc8`PLo\Z*1O@bGj!amMe79KXr9lkm=5-Db_t@G^[RSK.a0TQ@3)VMqgJ^H^88Z
>s_G[^;K>Ha'>!54nar0KfO'4\q%-emW)"\+WaU9c*Z&iBur)@%<V`K6S)OtA#\R$E9E^V`:
'7POaQ7s2nJJYAE^-NgfL.kP\VA`V]A[I6$VrHuD]A+/40qM;(7/,$ELdIVg%s_@p*q*C-.8p)
8;F\r>mp/C!H3G%o[l.N,Yph[o^Z]AO"hr6t^9sMC,RCk_U-V-3/l&>tf6/aiWjW9,tiHaST_
n-kuCU;`?GJ-VH`ePH9%Y2Ll]A06KTPm=D2nQM5.qt\gf64T8.XU?4EF\J<ST+NjrJ:l455cU
%ha>%_0dGj"&n3o`hXN*H`EdXa!(PK9r4gkCH$q>Xb2rQI]AoSUZr^T`K?rhJO'/l]A2PO1=;&
E#"t`WBDc?'LW*pZj)^U"Y>P9+%+#6(+?"l#R7l@iQRTuAcIMWYp%efI3DGQlT".0%;_Pnnp
`^r,*I%FcY@Mabp9g=rQ5YWWJm_kCp0T`^F5p6`in72fg\`Y&+Xb)qe)#XFb?YgP<_GS1!Xc
BEugldd5&W=,AQi^FWEK!2k/9^0Gn\TICnCaKX*%1X8MY_)+u`'YhE'=:i%(BgPk.C;S2>iJ
r.G%A`cR?k87&"a_'j(]AmGJ\#]AcNc='FFm*lRSmqR*:FFJ5g]AZmiUGFR:Y94e*m1['uKql5V
unT_j8-e0.&X'4FO,A;Xm/H87qJ=1*k"r=)L3c1p#AT1pF0b,T\<QW96@3T3/_H!Sj'lAd:D
Fa,5kIU\t91D<\uN?S/r'.u[S2n%Noaa"Q$7XdbSd8Q6uR3CbW^FpA+-j5=kK99*i7C5aIku
1t%j"9N)B)qbYp=:m;m6FseJW0=O1m*:.=2Ugf#9:$rI%]A'Zh2*X',Cc97e-%7Bh*<B%k@rr
JP73_%JIY]A'mW_\S"m_^G8Y2>PMTOhVNf0Z3M_T[W;"VPkMIE9;50`(^\iK9(o5A:S5n45#f
DJ%$M&TKGi==[.XKncHqLM)b#NJ$.4!9%Fi.E0*YO[Z_]A6>AH"nmPl`6!+RL`8ES'pAJs%Qk
NnH,FZuS/ppRWg(ZpI$&`V_6=p8*C!9L;J@?Vp_G(mcu,NgZ.rF,kIQ>bj..f:B*]AK0<e#CQ
Ch*CC*^P>6eKs8C>0\'7$1ahiLOnjFDHAMh,eI>6l)AU9<,AY<Bcm(#"<[ZTbb2YZI/imA>9
EsE4"D[0iZZS:S@=%i%Qmr%lKL+AG]As$i;cj8$i(t0$3@@tNY-k?TC,&^*eNEjT^W!Ys_c%t
Eesq?@J,m`SMn&=`0!&iG?qg-e:36cfUCc4<lIoRJ0NZVnZ*?B<86ZCsc`_-k-m#1MFFW715
V$R9X+P2JS%,kZW=UrYcpuBr`*4LU'LX[I#?DNO$F;CLH$Bm\j+&LB><d'se$ohi0=`LukEb
-QN4+MMg(r"?pZ&030k8D6^lh?-b-P:F3rroL@AeoVji;.?-t3eATfAs0Np2`m/SZ`\Q!'B$
Z"]ApC_E#PohoIun?3G='%^-*UZRDYLkM2B_>?B7m!P>(/I6'?"n;$jj,$Uf'%!_"N_lGlQ.=
F%M9),'6X9f0]A*KH"ic>A>a4\_dOd2LI7KrLuALVCQf9G%WFq-a.=Sm=1,.Ro>Bb58Do2Qe8
O3JN@]AF^0^3BB;6De&T<R;1F[^goiVHI1ai/b]AW&@.JGE%JE<07*F>ob03-XBU*_H5"Z\Z"S
Y#.a"%R7`ep-jWmuDIj0*og_G4\V)5JA>4S!,BZ28trFdBLZc7TNCV!Bh[:?V*A`PZBi#\F0
WpgNkcpW2WCbMt_.7Z[]A7e&76l`&TdWB^F$JL<85l-O'<JTm^nRb#/Tj;TT?9Wbr*XoDHcED
?5nB;c7l&EKK\$l5`f?XN#Ss<HcFWR%(_?K]Aij'\DIbSjB\q=OmPma@"BQkBVX4?L'[;KBV4
'bU<c\!!3a-QQPY<`T4Z:=\P@Q&SlCG\fXQnRW)d&eRA]Ac+fCaPd*^#SWBjP_SJ\Lq&M]A&i7
;[*Vmbaai\hd$5eXfK!qM,#R@G=_$GPT<oC@n)!(NF^3,V]Ai.lbjAXjcN9!\Do27/h9L:%2B
u*n'YHKJ^1A([A/A]A%W"@P0X()Me&kUTk"#nS<>pldqJR%2P#]AN!Z&Fh5U5L:P%iQNjT57]AC
WgQ@2H,4HOKVP'5Olpb<A&IP&*;=bN6TH*ERbA."@(4o+(eII'!DA`ie(Q><8B.OOhgTMWJH
1(iC>G!7$XcM)?Ab9D`DZ@arR0%Ug5nI-P5-*qe&EqcdZpW$4k`T6Cr+"$)7Vi#.u@NC]A<A)
0(l5L^:9_?V,hBKSjL`bQ&.T<Va.0in`adA(Ui.L0ke(OmTR&=shpN/fFsIB-<&=8N>@Ik(#
;%!TA>`-b_I_S_>!A5m-S!oM]ADeCbWY0_N_8L:'YBV\?4!<LsT(@0=a*%L,m()5ZlarS-PY:
+QYcaqZV]Ad2PSm%?g8KRLYOr&&mT1LT"(UWKLhc\<liT/gL7*<_u,-A'hN^9`a"_fYQn+(p=
d.f`5?S:R[2l\mZ1`Ua/<]A8.6TG(%A^!,6[ULDdnXKV#?n@\);GhB-nLA.<qIiE%mOCR9@"X
[H\eO_0K7aZE4Y4^-i"$/Gs0Ls..U>K.`Vd+.`nc9GS>]AZH+.q5=sWn?K.3p=AM-Oq40W9En
;IA\@$_lpfaC)c%s':'f'+%clsA$KPV8.WUt+o1+FdG)qJ8Xl]A:IjgkA8*L3td`if]AENVQ'"
3TWM"U)P$E!:u;)bH>5GKP_Cf5s.a?Sq=L@112?F"hXGlg;0>WbD<LJ"\h&.NpeY^*=)8[iO
/cX?1CETbB"rmmUl,+p<Ci6GULH6`CMt,Tf>V<Z$gjZ+fmTCa=Abnl;X53]AiWA.^:U`\B'-Z
&cY8@"7I6=nQa+R3TiL@tc6m:h0_emRWMMM/%c3CF3O9unJ[ZQ/"01[.O(oc<G'iJEcQKGTc
Dom9UAViLY%#Tf`Xu*Sh_FlXtn!448ipeZJT4D3nb:l+(\S)IkHLPa_XVhu73/hAW"!U)(a-
)_Mk(fL2OcDj87c89cq-Ko#_mt5q&4JjP''@_K(&QP8k*4!rkj46)/).LuHC3q3jI)dP'VrC
W"Ti8*D<RWjX:3e>#=gZAP#TPgSXeH2=T)J]AK&7u"T!"\L4"S*mU21/l(MJ?J2Lt+Y*Q_h@O
$1lWGrBZK-BN&mOXgf_QM7jINf#V)hBjrSWD2pEisMj>"?aP1^Xri=Rd>pr!pq#:\43tg^X%
RGjN7h5h/@Y+9=eHVj:Fm'-5b'G=F+4?#bS'@\S-S&M;_\f-nkE#:CEm^ej39CHUb#.18sn;
+T&`HX+n+JiLG<^G:BCG?`-7^ISrF3BtcsOAA^QTF+Wr>'%7L<r@i>_8[:Ug88d>I>0m9@;%
MWJ;fnb6@";-e"R&XJNaaXQc4>E+QE@Y[MooGj^9o&Cnb=j<.jJZ;Z.F;R2n'@TB7o;K/g%\
.@i7N=J?1Ls3)TI;ie)GkgX4qGG[stBS,bEIIEW(gPlqfc(>Z34-9(Mb1-8H]Ar+6W$eZI]A-"
sG%9_n+etTT<X&FD9I(Hnor]ApQ<8Uhg&S5"lqeXrl"EO/,r?g&SOHk`,?go]Aj="P'+J5a5ut
hR*"O"NQIb5!/I1R.C!u'=/;6Ekn0u8<m9)f?rOn,U9mP&U%'oHLOUIY,[__7@lr2b)31>UU
`N\[kV""JoJbqYF+&_H<rd"Qt@t#E+4[?ME@s4qFHICA!,%5dM#O5[6M))B7Hr^T]A\fWoE(=
(F:TKPHoTmZX^9Oc-TnLAcM-+&Vsbc)$7O!N!hoi+Dt-Lq8VcS$^@D;$u3h/NYB6`8,p6]A9b
.l$F5k*`pCpbhcD3[M3LXqUM)$Q'<oPeFen#`d-Qr=5]A19bKTggbc9_3_6?YET=rj[0'qZ2K
L'ti:i*n%iR>NB7JtqOa`M7/8IjhK-Jn7Ds5'`?5_lOlQTk2_?G;G4i1-h2fc%D8@D=Z%3id
J=2n18M/-'Y>0lit?GpK&M;7K"!A;faCR5[?P$>qTjG;&$T6]AK$:m,+J<[,#OdhjUE;A[3fp
3BlV-6]AZP^#b#,EgfidYB'VKX-d1C#=fLe,a9>2HWZ07sna`6-af#aM6)V<!Gu)bn]A#9pQ!T
%'1[4E#9>>[!g6dRUS4o##?j"F'DGY[J>Sk%"]A>jtYC\'?VjX&`16V^=?]Ahp-PdFV;A<-/4?
5OTE$T:EL"0@i_G4(P:[7/p"`XUkc)b5*tM&`tfH\gPPa\>]Ab8j.Z9t&o?Qp?Sf1R\C/>A>5
rGSFeY!g[K5+3lrr59@C9i?,gqhnI<8l5U)lruJI")Bl5L\ccr+t'X-QY47T1k6D0f>iJe<h
.Q@qn\sWA-9-`mg@o;e'Q9NN"Wip>:Tf/HU>ZpLUm/25'QY^WeCU^NUO'l1<YpM[\B*^OZ'F
lhnB_e[]AEgNN"WiVT.,Q)H1-\9T?#+25\CCQl8n5C.sVdZ2[/W+=[CQpZq]Ar<G91-jU]AkqWb
P4eWA-9-`mg@o;e'Q9NN"WiVT.,Q)H1-\m(WW!VEE1[1HS4)c2D\?j0?42fC;*)#6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="164" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="1aaf1d28-933f-418c-9e4f-386608efbcd3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="ZB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="company_c" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="ZB"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ZB_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBMC" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_tab,Key:ZBMC}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="ZB"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$ZB + "-明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+4,P1eQdRLYsUcKlEib-[S*)5kQu0uYtl3geB\1o70caJbr1-A[P(!id+KUT$s_Be%QY1Q
\T/+HVkWGp!j<5U=;o7DKQ\,-Wg!Y1_^9n*g7lO8$6:^$bR-lZq'&=R0,YT>,n>2Ap?h!WW4
i:Af5#/-)p*HMQ6u:>\G(m!T<C*(W/2m'(\#nY?.B#t<:sVE&ap'k/GMqqEbJ.SR5*UoDng^
=s6c_/ICaZu*nhmbN%td!13=0r%BkT65rNS@7#4mk[FoI[;oNU9UJ.&Uun6"P,/d3LsQK4!d
>SAmN-t-B,oehm!RT/s\)FEn?(QL"MgZkPqrq^^JQeV;84CnL&Wb\eWUsE;kWTDtNGqr>Bm_
SMrd>?3L=6WAn43+S>AMa8aO#VsHnpg=BSXET4n5T35gaq0"-1[2sR(4,.tIfn0!c^0M:)2Y
b7_laCscs51=&-UL/S1\12*Vr".)a+'s;D/%>Or3S7e]AA5kNrl@2->+66@<d2#CV]AoO`<BDr
2[\9e,#$n0JNd42\iX3HIn_f8Vk-a>k73K+*!mRXYiOVCNlZQ8)85:.s*$oU74Ra8@BJDTkd
#DT'P7_La.RMGWb[Xif"PJ:m-mN&_%>p5-&hOo3Dgm%r]A#OA=oWQ,,p=9sUGCB2pGlI^+>HC
DQ.<a[\!03I!qM^"5k^%9KK3HH=V7AP"lc@CX)"K4u5fsboSCaSgM71U8;sA1>'W+?WROrU0
KYOqLLE8ZO'>)=!D&bN-;fY9U,u!*R',UsL?'?_*3]A'Iq/8S:,.#`VApP2HjZqFje<33T6\s
N9mI_]AJa*e%g&[bbl[0T(:u6V9Q0;H=e/l3;;h:eC=K/I\PHWd#:H;e\!%$]AF!($^Gfo>+h!
fFgC#ZmPbYEU#1R+R&%+InZ=p[Dj&QV9iKoOZ"Xcji[GM!Fr!jcCDY^[ifRLe(phhsl8L8X,
uq9#-@5J\n/(:8qq7ECFEMV"<Zp-c4.Rm79P;8H0+;DrJB6qB^$K\$1*fFh1A-EF**1SjV)f
"(_)?4M-)E_0[D#Cma"36D;)4"?,O"[MNm8:6%4uTKjS,[$VW`&[.L4K<U-@,UcD-W7.Oq1L
"h/S"6/a,ic7lOpfn\21-$P)GokF&6\h.*W/>Nkq<&a@O1<uO^L4VotTGfm7la.IEkKK>%m`
G$8CQ3TP"YLmS8PGHDjC7,u";e^s:u$pLll9;NkKY_%$k\X5+_L?gO&Q%'3otfU9lS8j-V2+
!mird1[%83bfP*46+oBtMnL&JBX'7iX.*%`XE=T$c'"dW`g<?YkEi@)hk#CW[3`ZQ%-ul.9X
@mK)Am(UmG.LpbV:&RE8CpDkne02X(a[))fDD'&ARuT%XPhe$iUrLQ_6=72\)BBV.#Bnt!;o
Tj*`A.%M+k=-KI5llVfFOaEf?/^jQ*IMAX`+`qXQiMq'3`",WQjFk&9)tR2YAIQGi7nan0J\
7[Cuh`MZq[qshWp3VAdr[n%s7PiWr=Q1Xn<@>>n+E\j=jj&9D`mr%ri/@G%>::R,KG]AJ?*Ts
[OI=s1k9hWj2h.k?&4&VFq+Vn,r]Ao9AX(@j?3$Y(u)(iLR2i)okt/I[YX7<O"5oMG5cW5]A2R
8L`_82ka(9qO]A@VIlIjU'P\<93B#4"j!=,4j?1fD.krPMufY$sq5Z\PRKZf%5mERj[G<?DC3
4TRD&c"0uq`S5@ihWTl^ic0>H^rlT)]ATeMFki8t$(!u<D^n^X:r92HQ=e"#)0)k:B7D%-d4`
icLqa%`qV8j)YO$Hl)'V#FlMKXNMNngeekO!SU?0iO0A.?&)q`Z%g1cdiFqpD,j%)F:86mQp
9I"[6L/&tka=DbOJDI7:&iGZ5ndZM5(er('\l68)M=(L*+_$,(`obuq./)+K2'F$\M4,]Amm3
##W%To\%\csE1=F(:oii+YG7d8"5(2@HUfgL\RcV.5=^NaaJ)GHIGq7Bji*\%qqKrXPLB&l>
T\5FEi(V^%Kgo)^l6TbBBLjf&/Y?W3;#\>b6^9p%S$2^cWrcn(>0m!Ds+UV5Qdp;9Cg)"?"r
<lL_Q:;[;i>:03Z3c]AC*-0tZVXMOJ&*?<,#7bp#Tac8JG<rVmq3:l+Ze`FWN%oZW\GA<Z.!A
UF,G#$=M9b]A!D$llkkN?C/n<=L6fUX^:U`UtM\b'b]A^QR]A>43tT?c&MYp'"7Rcl@7FER9n#C
Y:Gb(JsdcB#>5@^X/?_N;jnc<5B3DVJ<1rM7Q6E_#l&;A:I5e(5p(#foK_@/ksbU='V/9!.I
cAa[ikoldpRIlijji-Q?K/rl40aN-7_m>:f?ubq$D^<kEjf,lCFe8g_S3ap!-+%ce%/6eXBB
:b*\_B$bA1#ffB`pMCk5JNgIJUNfBV=S@LXEjM,Gla(:>=Yj/sM8<\\$2oqC_!_IEq-@o*"d
QdasX"";#.gY5$Vg_rPkEp=Op*#d/hKg9nD-gUC=5f8!75D?^>*q9Pdmb*=ChNai9t;)P^'b
>rr8Ro9DCGJ7UH/6k#Nm\`_tVbtag^lGeU>Hb8BQT!Tb1=R(:[LQVQ5nlU?c`\I/]ADk+?HGs
/[\+U7fY_CZ5@"Sd^H1]Ag;k62,L0S2C"N+2pY?&qF-VKPNr0gCcps@[fc^[X8X`2Kg-=NNGu
Q_4o]AA`:Z<oU>HOM?]Aj%c@VH'S#RKbuFZW..;k/dYES,AW"oR^t,a]ArKJ?Qnnu'ni'[l/,r*
=Z=;p0I8jpomh2d.\C\58JtW(Z\r?5&dEL=F'>WqJ3nm1%)D('JeJFBr]A]ASb?(T)!#iN6_CP
-'3YrF6-QWg2pE7SbJ<U>`SQqe(STTuQ%J$_AkqpM6dThSXKu065L)eKOFX1*j=iOA;WH4Yu
4.BYrF8ji.ui;=+Vd?F%k:cLPZnLsC$\eJeQT`>*":oks$T1HC:6&l9lqPLNm0\/C6+,mMo'
Y,.N>,O):VZ7c:Tj7pFLC9J6-EM'f,^*sAEo<UA"-6i=sk-b<fe,_lRF1Q$J"=u$M^aA?RSn
kGE5KOpXfNJ'*5+D"N#$&fKEK45F4>V1DY'SceDh>Pnenmal,Xo7X/4p_dVp\$25+G=5;B6:
N;VU6QGjQh21q*&P[/#ULNt`C;d1h3.Vfc\g%(Et0KrgP>r#,umMX`6]AQ=6D4$h<9)9._YuC
GZ2qi81M]AY(%>YSkBGf5*#;^]Ag).E0Gl33%f7]A^duK3cqKU6Lg_01/HLD^-C;81EQon=&2#=
#*g<V':3!<Z3(7,</AagXO>6Q;R1te6o3m_!PY7'g?i%nbG<:5=`CU2\&Gd:$Z%Y@:gOVnj"
#ndj"Qf3k5c+gJ(j`G<pmEcr8m.,8SR3*]A8X7\a%]AM-PD4q^3e#n8Wf0GYICN^Lm(L9N<qZS
jg3r2Op^0^JMlr2Xm=['aIl`aMTjXT;-+BD>i>/l5%b@'d<K%iFdgScr*sG*;VgM$@VeNSfJ
"'SOI*R+>#(p:!3[ca7klNetF?iq`[N?[u:r)`&Tgd[)5%JFO+DBs[B(DfFoXB"U/P8XG`KK
Z`"UT6+5QM->NX2Y7qM'nM%mW'i%niJ\Y',KR-3[Z7XQg3)/)msf?*P7Xa36^/aB0-I$Q#ti
C9B]A1V#(d;(Pi#tBg@<C7/iAiNe5>S%<K=>[^KhlDATf0@!>aUoH`]A<YN.Ir$.rcBHEj/0i>
nb7'!b*DShfk@7]AoT!ug:lL#BBD0.R`^>g&1LciGFAT5n%4N"]A_u\`Bg^C<<]A.iQQm&*b4hZ
)BdB$8&-GSj0]A8Q,69]ANC?_E0'fCn#$E2ril"oZ)B]AuNeO:>U8E`OEuYC=HIe[q*lD?M^5[%
eTKqZRr8*Z;SVuo$7F;tC]A1blEPc>T5o<(tXH_07s5q'4(B>+^5IAdoE?gp)A2a")!9&f002
X+[Mf(sZ:H,tGp-tXf2*1*f9#su%[)"Wt3[LpDkSRSRr%F5=jU^%8(]Ab"'&U)8T4'n9DUg,t
>a&9`@d6=NQRE$$mlGj$SEbk/^]As/-MF;a7M>Pms->I#>/mQ0`)'k5&9umFQ97l$PPhqp&->
`!9dshJ-Ela''4e"\=X?VBqb/oXH%f:G_%rHO2`jcYjFE_%4%)P$bthj>3J8N@qBjO*]A47/o
>-&3AFf)S.L04"=Yah:XK'Ce*nU5#3-%$@@Q]AkTK3>E^p6:>q9\n[1FZejQ2Y&Bc!9k,B()(
PY!,XKP\cI>Y\aKiIJ)_`nW\j52,Q=dL<=DD@8'&1RfB.BKIiD>%@5coQ>qgFM^W_?p<tKR#
Ze<gG=\4K8L8T52l!"YS%`qPf>QQ8a^ROq2RJAo%q-V[hL8H,5!XY_Tt,`T@N]A:sA,$0-;`-
F_>uTO;R!In<4$FaDn4ZU9oO@>QB>$4Ig1T:7Tr]AMT*4='prp1of+W4s]Am^+UbAZqML_Z*DY
]A%?VrqOo7`BfXg?]AbI)_qle?q7)c'/>%pMW`S\Xs/Uq-W\.eSeVt74J3FJ[mlKe;AKHho9hr
Tb=hWGRs6#g#7qH?#B=_7`KiX0GeVD1\+Qtd+>n<:8:bqfBQ%2\J3Ct\1!QV"(]A0SqCc<s#p
6m^$PTC^CL.J[&6K.da@Qbt>C2a^*?KPAl"I&?"Nj,$.bk>=;\6qLmr%D8VBrVU*)!Xo*U=:
(r`W\?%soX2j-kUl%?FjM]AV6e$gV-oAr^o41j@,HI_Y63#uinJXYmC6s0G:U0q5a+s10k.>p
t!R9+`LikW1$3l^!ABC1D@8^@X&Kq9k@,5n[qK0<LM2?mHig[Yj).1g@mEi%9CJ'm[^5a;N=
/1[hi-=C*t/um#?XFh2ZT]AG9'SE7#c)qgc<+&/gi:;)8!8l<?PpT[$gNBFj`A-\j4YmXa7Ff
9NN>4:0?Z3=sn1PH]AOdWm"3G%1!A)n5.sUAPOg/TrF;)i'e<@)4d1cWc;U[L5L^dAShMei5*
X^6dr8=JmB>GC+7URlmI!Q&BJ39$]AMpmu"'JZ%o:>h$6c,q_/iEX!8_Y#4+$/"#N7F1dD."5
Q`i."#N7F1dD."5Q`i."8o?V>,J.fo"PIGVfTFq]A02d:!J&3LQLF;D:QKn5P6;^E>eXtUll#
aUbWm(;")/keUk(@a#1>aT8-<QK%A\P3O9X,urn!@7gM69tc$I\YQdH+`n1ipb?a'DI~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="224"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="224"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="0"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3467100,2641600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O>
<![CDATA[公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/3XPl:VHY'i_f#pH;'p>SP29Mm3R(pYMnaDU?39!ejM,^Y8#p+Q\\9MpoY@2R`q=_@qK)o
IWnHd2Nbh!]AH2]Ag(\$cL5tkkBu`>mXMC:fCdjhO6-1b?9L2$dcK==[%E87Mu/t"'iQ`f4\3l
uHZoQf)R$-1io8+d^NS_EGt@'K.6,3B,At]An`?i4b0r#U>mqpg&q+B:\q5`RTVbS-OHCh@Mk
K9Kh=5d%)p\tg+3HX<F8"MrJ>EZ6%mBpi]A1H+DT<gB0WZj6M1B>$Aq6P`$AM@G`5Ph_5!3lP
c8l%.oe!]A0?.RKOFnB#)E;*F/XChniC?5%N@E:+h!dDgO1qZZlBH#HihUGDP.+`uEO&ET$:u
?V\I^C[I&rRuIkAp@kmpQh@jup21*GE&2&)IWk-FP0i('4!Q80kH4`4G&+RsntB!/2f>UT<<
)p`h*EWRP?/6r^%\[Ng\0ONmKo'N$Jt[V0)4r)D]AZOeqcb6I*.JoI_(LK'gU7"KLIs^ERo1Q
tg[;c8?2`Y]Ao0.-[d\C`J]At\IhmF:UJDL20V'%c36s1+X5>;,lZ\1W3a_tnjlHXd>J*h?P\n
BVP)XN6YCrVq"oS.U8,13gThR76Ug/mpPVs3#/aN>^Ne-l1_OIm:NbL.aLIBkb'*ILNl3Jlp
L?(APLds##X8*b<5*gOW"@C.#_^9NZRIn\\pS++&e1>Gh2`^OJ*[a4[77]A>37981Tkg4-:/\
g]A,up8Dls>M>D;hJmSp`,.D.@A0:YL7'(,*F+!UX*#7tsVhDH6`/1XFnF&R\i8q&t1_F'K!5
<[P+#E&Lk:%310;ntSIVIl<^NYHk,n:o0N"UO4lj$/<n\:\sMZ%ZSVC`+hrH>*Knnd(\rmPY
okB=o4#*)45*t-h]AN'?=T(VJDW(IcDnY79a=`kom9kEt%QKn_La>OTH;=#Jc[SGC,DFM,UOe
*qF,`=U51LC[bCf[PpSPY/^\VDgM.%_&A+q6DQqDX"hYT(U"s[iHl.b%GM,lli2aYJoV=Qku
`6UsEY,PV<<`d`ARGY\tH"Ll0D.hkGQTPXi'5:%]ASsc>U3<]A'MkkJ)AQ"%=(gTht7\on/S#m
3F_1*9X7'\AkfnL]A;<aZ%D6hk_N2V_=_ga80+/JtMMPHeqbLB46cg$A,9k7@R3?L(=a-S5K=
C_jm7%#f5OYmQB'g;?Lc`FQlB'6fQBttFqn3lS;a`dIZ*c(K84CWAd@sNdB+F@qMWTr:VDM2
TSm6rpgFssW<4^jiMNn)'aV.T$<MNMP4M7%.#M0Ad^$VHN$=!FAM67/]AXJisIRlj1*OP[A-]A
!'S@'u2kW=()&:BfsItWH.:djDZ#<cJ6e4>HTqV<k16lHG(bni7;UX102Q+2[k=&n`VJm8Xn
dIninBMd6K"B+m`"+^L0r3Hk%'I]Afu^]AYu\hEe^md?'>_M32K&NS+uhLUS@eoq%//FXgK,@g
e!-b/kjcPsD`ij^aN^3Xp@IiYOrC"0<]AWUJ4hMEeW*0)9q:$?H<Q8q6X;aP)f^#,@%[Q#[f$
uSMlbb?lI!;=9-]A-]A]A>B.eVb6V^+G/Hb<g=_4<,uo_t%JqQ+Ugq0qfIPO8!r*HI0u@?m:K%S
Skk1S6WTPrIlh_VFL25tbf/FJ:O#Jo8&?;s9*qL*cB%2DjlqnfPj'IZ.[c_6O;J%EEm#h-UF
2kj^Z:6eK)E+k'Y0KKmc$,NfrhF,SZe,;f#qGN1IT,`>KB5NGL+\NRG_nVp@'PX8YD2L[b5E
Bq2*=ErYe(N#DB7CV&%Zs*(f`?#++aieK%Fo1CPsu`]AqQoJ3rSPJB1DTT"q<l5\W,%'E=m)7
Sjh=/l6rj197756mg?8>lMZIA3<pB[gdb`CB&Ni4'9#@ln?#_n<VO7a[/TI#(id&_a)Kt(Z3
Pq7Cj\/44>]AuNoQ:2b37R9eYbFM7ZCRR16gd+d.1t)4HQe(HgX*,VeH/jZ>b.g-T=6ZI\HM7
TgA#WcaiWrC.e.-ca9gq>HW-`2lQeRuAQN!#3iD&iZ+,6F;,eQmfU)<E9mq(jZ?Bc(a6n^ao
]A0BlS;Zb+.DDLVC,D=;3ssB!j"ni0!23TEK.sITc,POm@)EC[Y71F9U`!sRCVDeWs1K\L:\;
Mp7m\[;0tL@Gb@Yo,`Nt0ta7nc\VoX4ql`oW)_8Z@a_aOq@$Q6YJ`:EhTT7NE1;G>LM)7ZSD
ns;.$WW%iPXX0;,l0qlK/X\\:oPY$&/n4+sQ?5.T_^N@aX3$t"B>U[k^fbZHlcH\\7G;]AW_4
0(hh/c1WV8ODeg/DHM81E5lo<N)=E>,Sk7i<#&0YodDN\CA[RKs7l+%#0R'.VH?;eJ'h]A?4.
7k=f4C(:n2I2:U:;<,[P#PY>_FgUlBB2<rk;FVg:uHRA0?l2M7SA_J4$d1q8\#E]APPhn=A4B
k"KBM'M42ACnA"FuDjC,d0BlHHdDrNK'^F4b*ID(rkPAF07eNro)T)B_LBc!Mn#If/#uqUQ*
)O,'h=[KD8ai@W&k.1W42%F0[*9eG$#>\D&*t+H)-V5_XSDbo$joGV:'aipK'GA+gt+CapcT
UbY&PD"0a*a7RmBefH1`qYE=`G&LX`m0`:3U1E<b&.HZY$P8qu^=58D=a?%Agu&_l2H9!r5`
DMe/&8U;kMLp+`"8#Fg(XSDVP?^X!VT.%SsF_0o^SOQ(X7Ol]AO$qu9hDO[.dG;t/I"M?jR+[
l^@Vi`(e^8b-b0''NR1L$eWiA>3:Y'7U7$=i'\nYK/iH8($58ls!cRaZ4b(Q&B'\NmB;tQ*T
U^S28g./^#0S*MTCC$c'K)P_"#mKI9+>t_V/!ZnGep_;]A!eIacT:m8m&i_#>)SB+a'c"gmBQ
q?h20]AY@k*Y[kN&WM>P</UmDqX]AX)Y"GKZ6g_:HE?*^"\BZ7%]AoY?&4pk`?Hj1*e""7Lj4Ca
;ZI1"9t*q"'u`'f8!,;-Sn*@po_.o$[;WV?@@+"+@%*]A_W;/@l6n6hH'ja[u0AA-&?V2k'&i
"ea(28[<V6.^ILQ/AG!c`%flbG7H@It=75J16ZdV0-W^<B\Dn2Q_Z?'"mO2:%oVCo&11Bdi.
`qe4f7jORL"nBr=tWgjpEVt1XD-obb4<Z,V[?Umo[Eb3KV;qQlB!(J3'^;UJqpW'053'"Z[I
T*oTGgFl4@\Fp@6j[1;LFCJT*H@1IXuQsk=&=`<jTJR)dr#?Ci"PIiZb#dV<lcP'U+=^A'EO
jlI]Aa'E]ARAE2U&tH31L9N*"Dg&mgZA'%@r)B<&%:'OLTR\<DbTO>)J)eV,'Q.B')Ht9Qr=uH
LnOsD2\AR%!+@CTSt2qJO19GY7iYMY-Z\9=^Ai:fnqju7$#`0<,%j)X3(lCoS(ON-;Z1s$\d
WiS8VmHJ")*%D5%F0hOdI8Y[:/3$Z17(J6-:ImRu_$tSj]A4"36![Oge,n4/F:P15C#*J!"r!
F5U,p!d4<H']AD/crrZ9Hgoe-M1VbJT`DF3\.Js6VZkX(qr;@"TMa]A*XD@#'Z$-+MOp8")"O2
3lAgPuP<PbB4hG$ndK?2.;ZLgP1H0<cuA2('aL14=\.H#M?)YA+\=l%*@6)kEoW#n:`<TqkO
Q:io6!A;fY#U-PH<R$(^hBm9W,#+2".j$co9ahQ2t@?5Vg['O::VO<W]A$J=.J0[DU_DiI7RW
bVtgfa=STO`A1f@nDBT*;VNXFm3@68S%N@A[&g>0N85'sO.T^!8fP/k]Am3q:YQ%8VrhFrg\N
3Ik3(19XLSCeCf^c;lJR]AHMKKVGl&3q_!SYdWYVapS$7noU*]A!PC:UGgo4_ht!VTW^SnkRYs
s@W_E@Be3M08XpLko*uc?):W'1(sI$DJL)Rm\?tIe&9KH(:^k:46kT)h-^]ArS4W`u_%^JQN4
0>7fmf#Xmb+6b+F66s&2[Wjl[sGAfXguTEgd;,'V^r9%'17R>>Wh]AlEumMKn!S5[7haXKY*9
+t=FJZ@j>5?"HF9=Y\N4aX7G1U.@r?^$@ui/n450@?).a61k>oa#2Qm`Ih:\9APJ*HT4!ck[
0[bkG9kBF]A[4(M/KXE7tYj=P,N(jLJckPk'G*uQ:ZWU0<Jd+jj>unG8"o6NcJ,EEJ@:ltfA1
Ku+6BCKYdSBfGm+h'XR^AmlRX#MfDiXE,ngNf$9.-N:4*T/s1h-i=OZURr%j2U')rJ$#aAa;
9;nG8Z[pRMUW8?0^I(&a^'e4Y%U91`q#QYMa@=P!oN%pqeV3<;W]AYJ:X-d>Qg$j7h9"Q?I+o
&uJqUWOG=E;[?tZ/eB'iUt2:1iWhpDR5?3o\S14]A#LX^:Qn',k$sUB.@+V/J5Rq\L5;;L#>B
?f,mYq1H%jBPiC7.<)V()keg0,C.m'laA\ZQ!(BBUL!rCEs-YrWS)>of>k3lur^DpSKf90["
hTIkSMYeUa*LQgjTTK#H#7Zf"Rl_NeI<m63kJl4aJnW0!!ohR2,M_:WR_D\#JA0"C7a*gmc_
es9VtGI.c;IDl!!?o_AA3rMQhY.UN+1U2q_7Uf^jj]AI:OrDtQTUhbr;,P68tY:h92`?qMa(g
[>W$86Unl/8l``V(m"jmISYB6'4iq&E)%fRZ<h[n!/^;NCel0hI&imeTfAbMr8c9N!Y1A_Zm
fU2/([7mqb?s![\143_.CZF75%6,&.sAc=pGKJIGBOL!R<b8O-35[a5M.Z]A2e/joOGE`/Ha_
2n)Dh*D)*T/cJ"<Li#3A_5R>JU5kf7>IBD8T)/LPM8e,.,19?-V!@sk&ZGIgL/RbDI]A/R\4*
6:/Vu1RTB3&2Ch/.DaIKY;orS/P;X/F0P0f"leTknDU$IN#)&I!FQ?:!De)-Q^j>I,\4cC>R
l5AEQS<i`iL!KEaW(I<0]AOPp$=Apj0abRYXU4/f5RVh[==*4bPOM_Hd<26>2_40h+qQP'7;;
:s7HF#E)G[BJAcAOj@C2#>6(aD"7R\Poe=0]A1r,hr=^@3I$tDG_it2@-n*kZhOVRD`76270)
M<H3*$$%[@5NYonhjj9+%3^,pTK>>6U"&:SbdKDpuJPGr("ojm2Lq4N8nR=0sZClmIUI3EV5
J9[sHRhe`)\AK=HQIhn9'[*D'7h'(;[u0-B'@RpRdYe[>K>UA3-2]AdZQknkakedTlVnfOaGa
$Fm==mQH\R:QpN$fArEN4ndTRjPFUlA%jaMQ'dC50&H@Yp?E]AJ5saS$2fAu<@p@5e;8C]A<rP
#,,>sgL;-/'VkD"aTG&0UkCK=:IP-+KUnAJA7i,Q&Kmn#rbm4Yr%#Q4o\6"=skD]AVMV<h9@N
T9RS@,B:T3fe$A+"jBV?WZt".%cc?=p'k_E+"3s`^Q8V,I/e2/N#276t"V$kC,Qo(Y@*0BV/
`K>1?FdMY`".Nk*l*pWiOm,_cKH)ANSE'&>juf]Aqs1%9(8niE1r=P5%j0*'W3@?S?n8c:'N_
ilH6!e%#J`/D$C\7A,_=Mjp*$3t),H18akL"ChD**pV+iuNCfUM5;7TJQ+q-Yar('TRSK1f`
*$,78T`)D(_L!2O1r8bIY.ub4i#]AieLst=KLm\8?RQEGXns;>uk"U:gUbf?+,uLI;e&_"4[i
<KKX2Do<P(&G2PW$g)$qlR$(au5>Y(^OOM0aZD5lcoVk$]A-Qn<B7\Ct``Io9+d.lte786om]A
\-4#4o:hF4fU!-3)VE0t[`e.dIlXI]Ar\;rF/gPe+jbu"$1HAF_q/eJIVm/"f,OSN9":qn;[&
,29uAcV9q7UeOk#,Dj&k=Z"31qG?@d!NBX$*TUWqZ%o.qf,*?Y;)8BXQoM5i!>C)'EP9LAM!
+QZ0A&DaW`"+NnL7ekGF.JLdMhEZ%9!JW0/cTeiJV?hAPcH3ds5gkAh$O*uh(3^<A*TJ+?fM
4u;aJ^nQ,K/d+jriV;EbTr&BAEOJrOhXa\0R,Q:l_@flkX,miTE:f2r?#2J(4d]Ac.EH#25-Y
h[@4tZfZS!?fHn7L55/-Qal)?45<LTYPH/&3PDIUU.;V]ACu\!s<%-'OO4LShc",X1>UP]AZ+j
Y9.!,G1qAs+Z0'Upb9"kj[75F<2qoNL25c(3>n&sPo[_pe8>5X4RJMk"ck@cZeNp/a`P'ArL
<jcINfCC5p'F:Uh^tDNGBS^f%+f)K.24&9-:eo#OGPGI,&'^[&*qFJ%ZF;+qgA:X]A+d""OGq
ed74l(W<7JG$96AAp&eK\p*"(QXn4\53**t7Gj%YKF7(&E="e)p<Adi*^I&C@87u\-M<)!tm
]Arkd@@T!-S7#MAFM2i#)aj)HnY0C0.]AYB&c'e,*.H:^n&^AX1L+P@)@`n^n"/5Zh6CP=J)2'
?,PH\qj-"@$Z9!D1LqSBfjMdJ\]AahWAF(ZS&'.!<Q-J:TdbqIp`Ce`s+X"hR#pR8^:E&<\0Z
u<%eKO7dp$C=sYK1YV+*SP&2<-jq]A-_IOg+Q]AfU2l,kt4LQ.kp)g@;_+prTBfm.A!(0L^tDi
Eiu)3Q_#\)dI??3RG52@sesAPF<97*V*n&IAmCWan?G.,]Ae*G\i(3QNq/1b@oF=4n?c@(?Yr
m7)/YrRg/jkGXa-Fiqt4dOjr_.&400#?IKNL/MrT).H8"a:Gs]A'0AVHUjSZedmaLj3$hPppS
rFAa/dP02:p#Pbl4CB#6FF&usf\=EB8:f6c`[?NAbPnmn1'N30$:1lLbor^XYM5^OY/Gtoo'
EYcE2T)TcJ*LmB\1OY0C6]AAN&U_qG:Fu>,VM`**NL3+/p>gYHm@6I>GhTK,FRHaP[C<7mFD8
lQ8Iq=E7$UDfum_'qCTb*$RpE:c`%0:L4WjXMrFFTlh"dnS[)%`>TpbK2m:B[>f`M,raBnA"
b)F!;IP5F4uKD8'#%e#F%(S*s8?4.?5JAIIZJEn!TTVrc5NSg]Aj)$KE(/hoCMVWJ7P5^,8]Au
;5^-FM$qaL7]AFTORr_=gg?+PRib,i/E>5;GqaZ0lbCD7kt92,2]AqmQuEg9?buLPLrm=J*PU`
qrbMaNtg!u`0>@e=&p/,G0j9\G@KUi.@W%5*gY_B^?X8KlMUF7k#m=<1R@<ljdZ(bk+R)9&*
1N2Mrst^.<EMAIL>%U(;tARN%AoAUKj?0^Kgk;aV+YA8Fk?8-\tW]Ag@UACMo
mJ8XD$l[.XUU?+5H5:^ZR023)()HkXu(%QBI%6!(8BBmbXa"31U4f@[e6_WRmo*nh&l@mB+r
crPu3e#Y_Ze'LWH9^tV;&4g03#r)MauC2TO7[;H0si>t6L'njDmnR9)lqk:d"`^jI\^$Q5b=
UZ\g#W;Q[$B1PNmhL7XGXL;kN!c-5<p&M+t;L<T\Y63aO?.%]ALbL;Qs`uG<^%%!)g/L'%U$8
A/V_+2JF&eW4Xi<_=+gP[T`+JDE6ge"^l'U.cYl;oBW6]A2SG>2.fFE=hi3*.i\\+JhpBbZb`
eo(`jQWaqus4r=SMEb&cp/rM@]ATH9djLNiaLG5XPVetPEUg,6-hLBOgQ]A$#;NT_$HAh31r97
[\#K$i<%;,&7h[p&E^()1F+5sYH>9;rs%HN067a2\<2]Anme4Lh-(bA]A1']A=fuTY;`>#69pl#
Y&CV.&a^iIjt>.k<DO#qOfIm/%=!s<-jMZXtS>.>u7S!6.;d4%C<u5fCkQ'^%AVU46^^o$CF
o5qYm]AR^lJcO"SC`_\?NW^q<sn$,rlCi>U6r#;X=%S\eo`#@Q1o'A^$@a-I0h\%Ft)VITtd=
m^/KF-OjRgKWVIB4Kb@,N^\[]AFj1p@<AYitPmR3bIET?hm`'/t?i-kj+ilh4DWh_G1u20JJ5
+6McHWB?H1q)VU6(78'Hm.<CX*@i%[9fKPfPHd7M.#uK;dTIC7d)$:(Jp).U)7+-mJPZ7]AU`
rLMY>oj)6aR!C?u!i.d[?6n1a]A'C!A`hO+WdF5C%m8Eh1B%rqc>VN7"lh9sfF6EW;CQU\.Eb
/R,Bfh(f[n98-d.H)ic3'e(CUhLWmWlXA6-9qta<LgD.OUfI(*MReZha.?gd@%ug-?.?Lofh
V4DZ*Di7Osshg<<BO'1u--_[/=>-3L]AH@se=H@P/kgRRCh]AUm8e$&1J[6;dLLF-bUW,Fs`&1
Yc_-Xc5BeX1Jj@_0*58@c$k+U3+*&)!efTY`-U)i:^n<le\@q@h]AG5rf6STL\]A):\mR3@VH0
gH8B!=IG^&-&S-jdODjAaWrScf+GXF8S!@>egaaMF8U_]A:u*R1\0S-u_SnI-g)1M4.^"Oinh
h=GcaS!'D##E"Cb>/26kbFO99'e:o9I*QF;XVruB?HUudQ,QLHAIi,!7rHYs\;IQb'Ztl%O_
KA%2*4"@KC[c0>X(ShR2qhhli=#u>44%d:SAci`?8cc,>9tN2f1;m@P".Qk/qtuuP/'L]AX[`
F[*%Gl4etB4o12<51I_Z'07"2i`nB26C$-]AokDL6Ne8M=lfOHt&L+NuY[?8IiJJL&%>e#A<M
qu*h!p(!(5I3pk"Y/Fd2h2`XA'[7)*mD#6&b2T'jmEN,L,LM^0C#u-URqalef#D*f5.DsF;J
'bOpmr7.S*<\'I_,-u3lLJgqUAC#0#M5`/:]A)M`qi?18uP[Z=fp063jYZX5?p9VG9EOnOoVP
\7BrJaZ5Y?`G2iI59iVlBHYBf'ZY:.6#[h>'J\-tlfF:534X?rNIq@Q,SSgZ=e\(L_r-?M'4
Z4\Fb.dcrN]AEiFmlLHE<68J^!:o[kW=O"LXcBtRS(I"s='TLMr'2XX6,qm_,iDU5WY@\KiOn
G%,X.:hp^qbLaT\:j>Y?#%)(.TT1>:[rmr4(ngG/?DoDXMF:>_Ib0&`H/24uBP0+l[SW$iGJ
.8o9e_`B3B^N[2%m>"B0QYf*C+Xu&09r(;O63r/)3N6Ar0lB[=%PCJu!/IZ4iXg,4IK&Zg?F
1*UH3en+AF\,2Q)rgZn<0A@id\ID-abB9]A#HP9cD621ah$WA;:nE$&1@7KY`FS[1/lT)G&Z6
3\$4(<htMIR-^Z`IN2&U<.J3C4($La`k\s)R]A9@EpM_>FGeT:H@(ei@Q'=i)Fd?n!`<@C%%j
*jA:iX\ZQ7mK7C1D-rEI[SFs0o#sgGZeqY+fCUJbp,#o93^kuU*jSs(X/<Wi8c)L==(IL1IM
`]AGe1-<("G$-R&sSZSLJ'mi<D>G:&gUOGaT;crs)LBRUtW5@7>1qIAI_,&-s<U6l_+M1q0ja
NJlla*C)%\aXYrol-\"WV6A2S7h&GIg-2Fg!C1M2[G6I?Z/3DLi/Ft2LPM.Q+IE:W)6iqRJ%
-_=_P[/c"&1B-9N[-?fo@+An>K@TE/#je/o?up&Be'qlR^l:.ld2[MdsJO4"@e=lKXS.h,IV
VT;:sV;01$2B7p/KC?uI5Xif^)e]A^U5+>(%EA';#;k*&<uBYs+MqDs'c+IN'Y@_3YrR;?.@N
?D/%/`l68iJHnf-O(aeY`D(F*jsR3m(I<Q?kWiCNY8<KBLZ$X"kJugQNJ=8+CtM\[HER;.bL
l;jZ+JuS6eld]Ai3u'**-(14f!6,-DV5Sl><sjmXZg0.FZggl^UD/ERM-V3"6+A3GZLKN431O
7itZccS@t)D5s,0fr]AGV<_$m6.70ge8`VS=?jheZ\]A)QiF84!#]A&oso2`\uGNeMBpR^WA3YE
*?2H1G0Zr,V,;NXYQscp&mKrj("0CO=["i#CEFgl>#tkroanq5X8dkdR./+.SC!4a(Ud#BW5
UH,d>4lh\:h:9)<[J7!VuJjXUjF.YXDQm)0g&5B_gp0,>u<[9LEFm.@XgqoQVnN&mgZ!+\*g
JA7:W)A@dSmT<VhAK9>mtlr:e<VVckKR"T[XFi(KAHUV]AnN'bZ5aVf.k2Ur>a3G=o>:NN"^\
=SK+MB'*^*[,h'NJ&/"A4UXH7Baqmqd4AK+b4#fe+[`,Q-$TapZU_&IMd%ZEHtJsFs+d<;#:
MEVN[Vc=MHdG&l51KVYG06gFELSgD^lm?r:X1YJB1139XNS+Ur3m-a!GG5)FR$o8dj36mbVR
:V^U)Oi!/5%P,@.MW5c2N>:#en^TBFf&g0.Ttd7[e9L<cW*$7EfrWmToH7b<2o^"5r+e*!$?
\T>>R(A7ErGhPI*#FG%'n%.-%dPahH/>_$?c$/K&0>U/gpO.8a!PBma#(o@$DQpMpgY1f'c/
*tNt);.)\WE-bM[QLuRe\Z6(jOfaS4:b^<3J1s)8QJQ>oG2V;"&@Pg@'^iq5?)UFJ+`BVi1A
o?/@rI*Mh.R#)'=iga=>]Aa1X9%fRr8"<`V4jV\VU3.8!1m]A;u3WnU?=<<c=o>lMf^fTIH2ZI
T\is&r^4]Ars2IiIP(qOnB*[9cQ`5%S41c#k<O"16b[W^1Y@\q1SH=5I0XLZ>96;Pj"j:S)$u
h+Ts1:cKNmR"uU-!>)\$"K!<,e\l344EhSZD)@7!S"-0:s\Y4E\MU4)cjnY*Nlei4:?9dnft
7XS0Ep@UJc)f@!F,3,GmqhpLqc0-!b^!m*="3>Hl49H8e9s2!Ro29O?8GS#u`R^UH'`[jjt6
e]A8Zme)9&8D:;2l^&fW`h;$-X\s4=D9mQq^@eAi&M&:id8ql^<6J@JiDh]A9Qn<AE\.TFi5us
*eGu&m'^=(2r="e>oC-R./I0b0YGMbo=9C=ufJ@Nae4BHB@<hWHu=7(CLI`mpFB5f*AClC84
+^,V5[Il?V1a."E+*]A>ocSE"lJ(ps6GX)<EMAIL>+#c<qNl(IM%4B`oDTF"TSqYo2<i'BFa
ODiKCbmXAn0,_LBP?>bp=jaUBge/GdN/7`GVb1Qf&r!8c=Q;9f_c9UL?.=#Ih>\[S%Mk$D=L
JmOq!Q^RIaW%A$KWEf[:u'*1N=FG+n9HAjaGHm/['maCXZn>_LD0h8j0O,l[!FUSTQ$I<TL6
nG4rX[\?$5[5?plamSKAa96B"*g$UoVjdKJbmVj`r[Ja*SI/3Z<'8W,./*<H<1VH?;dZ,H,3
GnCTWW/K2pEo;l<@o^/GO^3'\plBNdN))872\`h#@\aqefM9roin_m4O.;?W@/a"Y.cUX-=?
+jRFA,5Rhb(N'LQb:U3@q$dQ8qT1g"`\>G;NR+0k:9a"p%&PN]AXO*S%I;63#2ie,MW#u'F'(
XWS<rh+tWjdY`/FKF^<d,_E>^\(Rd5rZZp:JU'D6>6%IM-kcQTdF#YM,6=<hn[#Aqd!@gV3c
hVs%<<O^GfX;(9sE%j*u'ITL3Qn]A0/'q<3q@VXgDRK*h9rdE&kqrJ%:W"ni-)nN:Y@>6&O77
jG%WdI&<o*Y#K@*1/Zu@5T?cMI\,/?e,h0#ORF-o80K*gjrr8r1"g)mBboQnSo"G&RjuGG$:
qP(9m6U>C:00L%XC4YN0#cTB&65kEH\Z$">m_OeXd@c%h7^\A@Gb,G)B-#-WpRI:\?@\aQT?
hWYj1*c<ms?A'W&nHoUO2,Rr<hJe6rB&`=TMb>s^S$aBKb4UFgbsD4rX51AX=NC44$NO<gbl
Dl:p/pQMlg!c$A78Nb6tlktT;]Ai'e.>UQW%!d2pj+0?$Ksiuj)JlPrUmkjecNL]A7Co4O.>Dh
On!`gH?i((>iMk^j2M;!mm8S*82C4biPM`eJk^nmTrq0@AI^PEJ/o"p@0::j>Mt/!6HO?g7i
>A%;D#+gfc,n^.p*)D,a,Mse.J6!<gED17)1W^H(8:j=4TkrMhYRC;!\L-iC"@U8bh*<`%20
.6$b+DT.nJpLcV=SLa"\9XCk@K5!]A#,R\@cU6?Y2fqBRsIc=jl,oH1#&9.K*El&u7iMk&%O;
op6,k72Q$S9AsW9`O.%FJO=8sqJNr_[GF?]A7@\eP)L+>=D]A;nb;uHR,;$igd<4q#mBMhP(dX
Gu9X_'h2Q,Q=<=qcRaL@7$Ve9h(R)WbY\bco1F;%mb*A)H#:L+c\2g1N6OS!n6jYH=2.:d$g
7Hi6ST+d5)Mg*Z7(rNl1RpkpXL&:>WO'pXpt;/*g!@prd3U^:E;Pe;[<R$.SnIOM%R.``-Fm
&n\"1tfHl`eoLY)R?EE,hZUoH@ZA,S"M(Cqp/F2p-4kXnKnGS\9@IL=%R+[guZW"EoC@a`%m
@gpSC#DE0jh5?WWQ)0p]AKkJM+mCVqP4):7]AhmQb@*rV&&RW"f&#Ig=bO>@]AEd\dB-?\fUUa7
XQ-+Xb/PnIC]A0CL*g2*d3U0mu&caa&E\0ZF_N+]A$IFPc8?STK_*S*Lr)c`E0="j#<8;6%1'U
fF5jNV#)IE'4?9s9$@g`%"EU,:\(`3hZ>`a#'E4WO3qiIt;uB>/g*D]AMd(S<ksgLtLA23gNi
RX55jm$^gc'Wc3>9oGXSj?s;$7*qcE.:Ck;_-bs=a*Ae'=4I@VR=)(a[SU`6STj66*r:dY3+
#CkL#5I8s:VEZ?0Lj&ZR5Oo'P,u&rpM%1r";RID5^)Adl^__s36Kqi8X(SI'$\Q#[(5!@7#t
&[LNkA`6q4&W108:H7N,Pd6@`.*c^;+VN\mUokT;"b5>-6rUT.D5l"q%Rju74miYcnoYDYV*
>32Kj'P^0*"HUC=>\lI5^Q-mYcGmA:NJ;:-NVN,km%d-1,ml&&'bfgo;o*,77?=!<6eGh!fL
(RiG.jurn^]A9tb(WRdMP#M9I.VbYS-'Ho8k]A:4CV(Otbf;&]A`5Na8S#W*iDkCc;PoH=%7X95
E)\_j9`d__7EJ7W]AM&b<0-+r]AGEECS]AoL&7A[9i..7%9n">3D9^nW/8Gm7W%!qT<W+.jURU^
&HKo.U1V-/&L-$^?6W@M&kbH:#7n%XlWtRXl'.)Lu/p%=&AJKace%AaB2Eka,+jU8_N(oTum
?E>fQVV5I'o5AU1Z"!S<URG[@:F0Y9/uT7/q2S?(bVcU(+g+F0c3fc"R-kPID&Jan,Z'!`$.
:?^mg\Iqp[)gcs.e+NYaF$ceQqm&:J7dnuN<g):)CW&XTYLra$M$DE2a53HT]Ao4V5NTCr<[/
;apL<L?[B.>fDoflE\[dSTW?o*;ZeL\.YPJ`2i.8O1G$j@G(%ipUETSrAGf40TC8t+YNCQ[)
&Ma*\#mb\&OUB9Q^_8eWk4YlWSLYPfn42!/Hjl4Grfk=aA>`-[!qRa@c=l"?4/P29Nln82+>
b^nk]AB#qi5GCo&dg0<OPtCK)Q/UPR8P<eh).[U`q"XK;.15Vh02^\SMDV[G?qaThfsHSerGA
NN]AmmnVkL.:]A1!jSRk!FRDoB4G"?YH\TT64!?83C_"j53CSXmOK%DfRM=.+Qf;A9)PVr/a1g
\;6;`VIlolEKEW#P+./tT-%`I,B3$M*j[;]AJ@qa<-\$%,l@[p=!4q[1cYn\an$SO!:DlVil2
n!DK1sddNJ:D8.oWXF7_(U>B+#=[q!iE4+,VRq&-f#_e-A$8UX*1pH%GG:YuTT>a,^rh;TZ'
9->Y46:gH"-6L6nt/H`UkLi<($<)7c8pi4"F[a:U?@J(.b75Vb&b+G*n)#$YS#C7DK'e)r3\
gmc,kmo\_F_+2hdD=n8aUG=TFA[A"cbcn]A7Z5]Ad,%ck0')K$O2ECit*m4bh[>G1gCAi1AcgV
dE[lCTa5p7b/o@>YH5FF*;C_$]A>%LB;X6mi<9-<SP_ZZuMt5JEQE$HgR(?%UL(q8r\nMJ)tF
f^^$O`nh0hh<4ehS%0Q96.gKCcr+800Zn-^TOW`^iMB0:`rKT)U(p$fk&4;WHB4an'fi.PWa
dd``N2@=NL`JIOb+fi-VZ1#WpqA$l'!L=f#@;H-FZ7rKD6,uZml@"oP(0$OOBr8go>(QFF$4
b3+nQKn!9tTcX"NSS0"s\P"h/[RiC[Tn')BJWIM0ce0[eSP6@uNO;0EG3gomT]Akh:Ni+/_\N
3`:b1/rZA6&j"5F>I)[!(UJM48Y8J`).+@J4:27?ejYUY;9tBXn-\N0S*''701n-?4]AiuekA
K%LYeQN6<_0h1d"d<pSi\[]AGYU-F3tHRL]APB4(DjT!D1[rPn:BT15^e!G:?b.+",D%1c&Tub
K19l,&&o#ZSIN7-"SnbP_Nf7ngUSIA.kb@[.GQbSs$jh!o`fbZ=N9Sm(?fd1;U:ga"5<@a5=
1\`e>>V"PWlf.Pb#NKs#%!6O[.^@*mniS7NJ<gFo2)GdY$$L<L)Nc`(BM79A3C)\5J@bI-YA
&4L#$/\q!-N'YesDf)Z3g```fg<8E=_M'1=n2g2'TdlAWB&!)-p8Q^>&[I!alo\phcKqNNp*
Sf6?<bOj$[RU$oNtEZeN4"NNXOi,:.hf_qO9M-41kioH0qH\sO]AXhse%bI7X_E]A(B%nLW+lC
Hk_'B%DGj:@H7gS:b,S5H7022D/HI2"DU;7/BpmkbBYcC-<3kMJ/m^sP\LAg@$d:V1?#/7?\
<7ePV-.s2mZn9aBh]An^<=gskb8PB77.=:bqJHi?VB.hH'!>CF0>8%X*N!qAqEgn+`VamHC:H
h;n$]ATPaqkBS86lH3<L.@,r!,K:$j.(S7"3u45igt\r*NGYdAnqH_U>2?:\di;RM?KsRFM2e
$I0NJiHJr2![1+^%WaZ%d?5_<Zj)be4Wa^h/4=I;a4@`Ie[W?D=q+^>6\5e#HgV[dZHFn;Mo
q81\fbU+&#dc,c9G$XS"]AB<12Q!B;?gOWdQQpm*B0i;1J_nEEVUX^r0KR0&@7t&c-hBkZX'?
"XnlT7f$qTgQ3Z44@e=%LhQ9=g7r')gJTC,&JZ2gVSok%9UEkR6Dm?ZPM[6,!2iY![#HiSCY
L/feD;5JPYXI$/1W^Dm\@h.1F8SX&`hd7?m(Xf+c0XN7WX&"dB/B]AuC.!6drV#55F#;6*NDU
1@]AWddcUf]A?d@K:3(@Q\T[&7`NnDpWAb\`,UlPE+:*!H>J7Qc2&!EA4Ee,&,0Q\lN!AX.N+"
9&ghI*#Y$8g=^K/XE7AL',,oau>3d`GRoYV_cb)Mtg-M\kVs"*0,:/S[Z3%?@%0_]A>_QCMq^
p0cZ;t/(KNO\=&_jp.Q7QIt*JApA_JCjUe'=o:-.E\>#!?Hab$K*kns!I]AYA1&\j84E<m.<R
(a!oS)Y"hT:>QT:]A1Wf\r`(oh5hCD<I"k>dPfaPJ=^$68l-'lrTQ&(7h8-WER`CY("[d%chh
k,TBZg9FjT.U`fJB*>$Ts)8+*rmMP-VkF2V!`HD3nAl-#\N/@>I8d@54==IW_NNU(#:_?l7`
ep?-4RjEI,,]AMZPX2M*1Z2m`>A>:"/Ha53gV:6N/\c=n7k9W)8Of$@UKu\#eFc;/Q]A3@0nb$
&5N4DdE#!Np+:>[$X<MXZ*j-A,IY1R9ht)k15$$j#.7pg'Gl=NT/bj\f;[q.>(rF2hr2/#I?
1b%k]A=^*qpkc"`s2[#shD6%qr"bWDJl,m8BkeadXq6(fOMDbR9*-,`\Kq_C=eQIBbOL0VRoF
*Hp,U0*`@+!a(jRN;3SJ&Og^YQp.W<@$=OK;h;C&CNWJapFC"c.k`feM]A0Y[70c"m1*a-S(?
Y-cXL3GGudTKMWY'bs/cZ2^d(oTkLDTMOPmN?\5SesE9d+!(tiJQOfl=L.UkP+1h9"AI?MVU
PZ:rN*Tso]A2t1J<('#?IX-$%*K*?Q!t71RW9$7ASl:1C#Z(9,9LAT()cQ;mbN5eXU*R_*>(3
57H&XAimCi?B*7b"-OU3aUn]AS\$u1HtX@Z($kGM&2h15l]Ac`E84l"8D:]AAk%tIn[W$6fC"19
TngQ<M,pjEI?llY=LI&c\'-4`]A9JA^Y6;Pnep(i59EO9%"[m%DGOnuH>A,n8:RAU!G#7IY#P
.MrZRa)OE[ogC'IMF=B\J\kTN8=_,<mpi\eA?ON>\(/m!Y.IY-d?!2_K?dR15*qDgK?CM[.i
0$sj#!Qq*PSS=kT\k)2+H34/AHP+8&n^r_b\?)8=XJ_Cr7EY8BKH/\a(HU#k%e+=o.d,.W-p
[BW-T@*(kYO&.,(7DK;4)P0!leXAb3Ph^.,FXpOfaIL><.\:bLt%s#Cg&Vi4PAEbg#aT@-^p
HiXZu#e)mP:D^l<#`(8I3nh!,Ial4kIe?gZ$C(pfDjrQ=^MQt<AS7h?@IEha<(lM@/=L]Aa'M
pFN@;cb>c\in#eZ1s9Z6n%C=?C@L+)Gtem>pZ!F5YfcWUD^\Ra@'HO4U=JI+JK!RQL@Kp2)r
%_i]A,eJe^i:uI#W"qkuCUN`V<lmkDu+A:Q,aO-/cB,os4REd1?BK]A!5hO\N=7?C?3jDUW8CP
jV;QG?JOhu=5A2e-h')%2p+p*cIV<6A5fuTS:UuY<EI<$ZkU*$ht)5'?1paLif!:oja^=`Y_
ah3`gfiE(i1^*Zg5\8K1<&'ps.CEYdUam@u"B\ZYlZ(MRjTfiKl!h[]A@+6]AE*h7=Ob@-3%3$
-##TI-I<aE/`Y.@nNq[$T@2!M(F"Z,:dc%ptRjA)4&4s*'F2r(^g%B`ZZHBE"ZC&?>Z7f*@p
?"]APdr7(g@)6[IjTcR)ZUEY'<`D8:nJYL[JD:-Ze#YNKj@Ff,Z[XK*<iV_Lhj.9]AXXXfD&f2
U]A8NXkC`7.FRIt?h*R2*0=[YgXQkJ=0Ni2OGf#sKH&k1b1a9P[Qg9:8.C$hjbsd7g)03#Mb)
'/Z4Hg1N:ic-mD58D\('RDR)fDcZ&OiF=i)Vo%&9.Jn1ks3%'_Q_/gp]AVZshoS?Vt_M3-<+9
WY'9q&'n<jdg,!804t(`N-_#0j=4f$JdE5=AWX::,EZi"M)j7.(g.Tk[(VeFGRC!S+=,LNIP
sMMgY(dKCm\Up2u="BOEMdL1+M*3qnVoZb+?X4rQ.V>-*?D6XUDY`k89a@P,8]ApTON8OL@\i
_W=q>\&t6rK>&OGR5pFF#,ERH9l8XbXrqYYNMb1W2)gJfD3n7d*U#71/9chQ_9VAg17_Oi=i
'^'U8is1E4,*dq&?1772*r5Yu'i1"!-##;:bDS!uV%V`)Ni*%-cHqA"B%8HfbekIRgY5/bj-
Dt<Mabe@b/Fh-mIYsML<[,sPrOf184QT*8+f3hB-`=hsG@cQIV^oc+J:tdAnZ,<-NA4bh+F3
#n$.=@]A527_;eB"(FA%b-VI<(/U&X>EJaa_)&-bA>PsY18IO5A+jI%Nm&,?^J3CE,&#)ISCF
?Uf`:3p+4_23&&PjjY^k]AQ*P[/cItDNqIV-I+k>P%K0e('XJjX]A-W\nDWhdfAnB=\fisEC0V
`uP56:rfq&e?]ArfX:WrA%kk=d:n+]ApXFG!`;HGSfXX'M^u`<k[IM44DpJ9VG3m&I5*RG"_TX
AmM%iX;_\*a]AEJLj_LV7*3im;=)b-e1[KDo#+:+(NH`?Tik."aN?M1@Bt,-(5eS6h98.Q3Jc
N6YLlgS5%8C`GZK4cO,'ZH$S7W+]A.p3_<>8W8fmXZ'\WQ:VmrU)TJ4[;_ll)m!Thdh$>n+]Ar
AfD=I)@CI9B?9,V_MH'-;WX'QTYF!^m-sY-+qh>'(T$iRF*n7Id!dT:=lNi.%K;?=]A3qjam'
iRaTX4\Q#miUc:u*lA2Z?Au=#6C;^\/!uafqq']A;a@W_T#r0[29<E9OF.5e*`k1eXm-S!!$g
AUb]AJV[5'\EDf?L%I[2nNW[Y[oQ13[)9/Y]AI%@I\qSI\3u([P,Dpu&J9sBe.>3U6K[._d9M(
VM(1jqd]A/qM?^nS9D+ltTmFoqLc.4ePk@o7(b&emX9+!2Qi_\tBss6RhKVk#>(R6/+dkh05q
\W#U7a9]AT*kS?<,UcTZPRNA4!%sdc(6O*T,5B^Y!-!mkl?mGn0NIjR$%A9K`8(@X@$B0Go`f
2QQgH+68PV0=%#pcF@.VkG##1i]AgLAUd`Isn;,L0tmY-\^A2,t6*X!8nR]A\Ae_(S3ee@4T3\
bL;5nD3I0=:HM]A<>21*NsC=5hargZM+\UuU&*9K>-p+cuKo';YC,.jkj4#9lb7]AW_0o<,o_f
kXG7!D&c"Is$2[QTts2gZ0AA?;!J?COc6GdjKJk!4+MU!=_4i_e%-rZ[oGCRpUT@a@K+qeI7
W<-[kO:LX^O%9\iTeQT:DMedDI?h1QFC+r(q\HB5cAm&hPF;[V716*DX.;[;0>#P2]A5IEN4>
5<.D!6;1%f?%"HMn-bA.[Lr^;E4UJ0*3Y*MPk82L^KS.GMbfT_T#mFMi]A@!Y"U:Qge4fDo\`
]Ae`Tj52lX]A/W-Y;b&_1uMQY3_%[Co]ADLZEJR1JrR"9,M`K:uN$Xg_B"*B-hCW`_D:0&QK+X*
3+q6hZ9N8Z<RZf/XJTU8t!nVQ=0:_Zt#Q.@2R/"#/s6F05iJ.R'dj(81?:0,;*Zs=,I[N81K
/cm=J<!X;.i.L(*94>8aV5L%#em9Z7_KT.Q3h^GUBe9=^Tk7H9652sg_(A:%+Qsmd$4^i+p5
&HC8n61.#uiYnFLPk_KL_obPs)IToDq*ib2#NYc;o#[a#Ml0(Idc68-4.61u-\@Io_t&iV[B
iPP!4fh+baK!)W`?iOqno:0A`M]A,Y*Ti16,qth,fW4U&iZEa[u?deV4mge-+D1"gkDSKR>pZ
U/.it1?d3+T+I:$Aro)=34Tpk7PKlF'XK_l&e4`G(hfa1#tGH<G@OM6X!tNr[<1m&T_6K=nb
#01AV8(oLRY5a8Prf2G@'q%'$L#cX#hrT5Ah$U1&gj"'1s:00K#S"IFkiPUF>'Ch,!a!F5,d
XJ0MSNCSDfJLri#&at4]A5g*0mH5<urp0:1eI#.6O-cATPKa+nS9_RN(6`7,?.`8uLaBOgcA9
RpX3$Z1PVk:.L1h8]A;N\ZAK]AO5d3Mi-Q_!IA/J[\>97KY:)i0OWBqQdAQq@GEJA48akoA$D]A
IahbfNP8p^NbA\$VT>FWj'/!T!SR@!ffq94k]AO@0HZQu,>94WfR19.jI]A9mdEqc^?5VG-d&&
UsUL3\Z`7W/)rW=n,uOot,D8[RQo<pOfKQ74Rh1n3LB*'`Y,WlPp=B,Z$t\V/t+5QfJU8]A1u
:JM;5UD4XjY42+K/"F?UaUt>On`[n(+O3)(@%2+T0YPb*6WHEXW1`#.TQ+(NYXE*XLj*Z<u.
52u-2:Ht<q82hS\=B^+Ts:eYmC[\1KF5bVC1&8M?H6nANS<pq;B4Z/VGD<o-Ve_/4X[NYcCo
C@7pDGXe<b9uB]AJr;s'M*6IkQPLR"C2%No#*emEO5EQSrdRX[EC([Pl?\2b2"78TiX(Oe,Kq
F.,hP]AeO^9=ltDBl%E"rp$Ke7m2H-NS8\*]ASIN*kmRoW_h]AKT[;S%6l``%%"PZVfsoaRfVBb
r*u-:%DJdghRN#k&1OH$ND8<N2e8,88Q'G(KmhIh%.8FLM>7^j?i`7aC\n0:`N]AfeB"2a/P1
l6Q0]A)!E30gRetPR,!XMe1"#kO!D[2:[;d!]AaR.0Oo4fNAK@OYr3/mf0Q+f#Ma:C'\0JM`jq
P?UMgdl4)<6LPT>Vo-.m3SlMl(?Hb3P>'lR@o]A`D91R2^!4160onU),Lqb./k9Hf#B+@lf*7
:qbCnLEqn!7M8%=*`/klCqSX+UkXRdAaCa`f^8QZ?Oj`K=!'XDCsBhG0&Ms"%&fj@?Ug\FP5
1Zh^QWe^-lMBO:-Z6AGJPSC):!)4pU7+KaF#+O7bOH>Pb+`QLjA7Zn(iDj6c0Ns^OCU`rMC;
NB\6q\EC1D+q@8pPKLQ*]AIaV*CL4A66+7D<KBBJ.%oH(IpK_rtlj@]As,T\7;4`0MVm\m=.7.
cR^F$uaAQZ(5#"l*\$[5rC`V9ch>ZXpHX]A_Bgs5IIrSYMAhokC/BLuW2V/!u9U:4(T\rU[^G
=MAR47LY=n><iJJ2"_"4./Oj,J5ljcIAXkRd5%qU=s3Tr`CUr1iQj$KW%<'&6(F?P8_\el%+
ZT*_PHFQ4TY^,j2e+pY*46@\*bTH&H.BKnUtGkupg6MJ,J\5qIfEH]A^p803?C_rCCB]AL4?6o
79BFlj*jXg#gecVSG4_ag8?H%@:K$.iZ$0E:DtK8/oRK1F,SIPa3!gO?r:T57\n&Z;D[@c=K
U3Q88)*>VkdA[i#`!@*6Z^jWF[7)#5*!PXl@WsXerdq8Ka42\H-.FSYF>]Ai?Ht1,1DhfY^AR
mi$dts&d[*`oO'Ve[1sfJ2$U,;E(9p=Obh`H5fc3JCT8s:llA@!M*Z@R<+;0o)DX/-nFLLd>
V4+T\Ec92T]A#W8$OXY]Am2C?l#025od_>bk#Xf%MA*Lfc?h8;CnYpS0j3X.?:<RBkqh@AA3h=
\X4;!a!?>_t*cqgaY?*J^XpiB8NeV?r>5R^oZr0nWrPNAB-:XGk1E)^/r\*3Np43T299DX4l
m9%Y8emGMYM?9Xi`bO[;aNFP`=iZN5[LAqN2KO"b&W:OoEBYh@Ah#^kC=uQV]AB&DT>-f$U_f
)pFLQEg&.B2*DK%[Ta6g[u>%.12F'#$CgB\At$K+@=lehB0OUT,>gW'2>OiAksGPZ6o"&suu
6P.EpIG:gT.nVN&r^&R."e_q"R%5_2j+$\j(Q>f/br]A5f_[X*jLj?N6#Ol43Zmd*SG<=jr.(
iN-(qChjC\8S`1^tbF[juY/OopPd4n_ec?r5@c/n;:qj9oiKP1a6UNaCZ]AUGY-mB0lhY+dOZ
k]A;sOn6GG.Pa"VfC.:*Zjtk2!,gaE+=-<Nje!ZWmOSig4(lHBZ%iBYZKE$B^ircbL)0WXO?!
3]Ah^'fS>'DaUcn<*Vp7OH&&0Xp!7d7=PJiTVa_DRWEU=.''Yq[L,>Cm_<G(QHl&gRNs-EU&r
X'$"IoPWMXV]Aso3X`7e:2CnX16Vc3k0Mlnk(bo.I=7N=U"dk@-To0U#`tGra4ls?Gi89?G0O
CC(OYK<*1F>*U.XhLqtu'I93+X)b_E:D5)!_7eefoS;c31reZUBS"g%k:-U\ZXC9GL/d/8i#
m8C>Yt(FApObcK*9YKnj2"E>ei<oi<;,sHQ>L/-Z$[%i\XqrCQZ[OgRKhQp15AmSi.LE%5OZ
EY'T3tlP2Gr>gI\Z<7fpf`n8X4I_l(RT.(Ze'!Djulg:'\+aR6r"18[FZ<8aU"UI^X=c8njQ
69oe9+sYi)LLHp7-pJL@Z>q<b9(U,RWco_5_$D&lCbKB;[rY*&NlK:G4='Qa=H;(L^&?>RPA
hRiQHi(R'ZU0p]A6aJ>)AN7?#6[,a`T]A6J"H1V5E)0kZL5_sQMD(e_%n<012GP5tXU.<3.*Hl
>ObYtI`fn$L/&&8>bSmUS1N&lg.M9rT<e)Y1#'.EF(InoeVcjX!(1.b0QS1RZCFH:p*YV<]Af
A95O+1JY6UoE]ANp7a^,o6XZ]AVP:cOSAO!:W47FV$b5'b,GR:]A9/8W;3&RGrlLpN&55"diSS^
OMl%)qec+j69W2=Yjo?X!9P@6Up%jIH)c\d[$M,@UL+@VUAYYB0,SkFJ-PG13FR:I>SBf>e\
!"o>-]AV=gJbQbbHGMh)(kt\,fToi6RC?3jNE[EUe"+?N'n#\ei:@lWS-hX<%j#*EF=(sDcn(
$30VO84`Q[DA+.N+R0,1U.t[^J&PPo'#M['_<O!0<ub`WQ_mrPR^Nb9]Akr>q<H75<jY`0?JW
c<D6lVMJ!*920ug%,D[4']A['?O/LB;sVEI*e.Gs[T$m*hh/OahGBFoQD;7shH!QJV_]AW<KD1
gMHZ$OnoYH)LT>EFM7aIEer)Fr4841,I\\KM*A6ek`=P*GKq2%.9CbI2:_5l+[jpI=7le5\q
GVEBle'a2LKc=b7]AM>Bh@qRbcN;_'0O1/J"[l3Ot$d`_[Sm$X&TSCT2@c+68MS3\(\_:02O?
<Ss>+%34N_L/3`D1!s!qg(_7Wf2"M(9E]AhQaa2jE\RI)m)sOb'#H#'-i_qK]A/`G*P[@@@3ou
QCP2ORt!)^QBtmmqM5SNO$0I.p"srZa9[Who'TV.u8$!a)gK.!0TN3\[*ZlL<i9/LG/6YD"T
VHp0RiNq_I0pUq4MZr'r`qId.OYd1Bs7&p&m^,$Rb/E^Qn@gPl_kaKLe><,doDcMY\liitr#
(!0]AY3B6Xi=b>&Nk`GT8bZmdT!REJ&a(ZGG&:;#\0bX(<04lWcBWdpa5)%^`uX%+1CFYfq6/
t0PVRl@7+ReraFMKYf'L(F+Fk$PocjR(o-A-s&0j0%aK(Q#`Y`"Tn%ph02\mdkN!8*JmrZ>/
h`A<(h':)/UXC?`@jqd;]AKTn9'K7bgkKF"Cf5,j1j$^+K]AjWVGG2Qlt%NUG2:SXP[Q%R@n9B
2Cj".fQ!DqPGsg"uF6&!J!Ki7o)KB@`^C4UT<W_g2\"F%/;OkF/>`JVQ$`!ETD+L=q*reYY!
&jjOI<r0OV0I=1f-@>q`8Cu\Uefro+h0>&.A+JD)Z+-JqHAlEHtR]AnKqq@9c5B'(LRO^7*2l
>Ae(U?Nd[C$4kqYHtr7.bd!CZRM<Qd!.RCAJ5#mg,JW<Us_DdhjU!KS?d!01F7/^RrQkWBb>
KbEk+n*fj\ROG$QFm;H"53aqh<_@To;MUe/_ULe<Y3OM":cR[CXY,%+Q:&=aFFX2mH$gpt[Z
E9D@mikt*T50q.<SF:X8S4DLm\cAfcg>[4&SXk(M/U4-kPpH:3g.L`AG_qth%.4aoc,hC%BL
Q,j9lX,0N1BUac]Aet;o9u#J(C6I(?b0T/!gWB4ji+.;?+a:@3B]Ag4)8/hinfo1UrtoTu4B1^
igV;qJ&W1(Ub;hj%=IcZ@HbqbT(H=!YGZFDAQ6g$_V%('K%XpLJ`f7&M1YsiLp%@\IU3kRm1
dZ_)P_T\/p$,n0fhcU$6/6/?\laDEo3@Q&:YXD)*:SN`9W+%W%VGLYf[eB)k9ZgJV!ALQ<+C
)FRB^9U9&!tr$A@s<%=/HT3s<q@^[t;81qe#Up9$:_4gsu$<P#IJhZm$(J9$2i`C;!XUPkfk
$pLfq;d8Yfa3(,1WSPrY(DSiX?8LbdH_jfq]AdTJUYM?@$U+pZ[)kZSO9Nl)4Z94BJiL\rdnV
&Qh]AB,<Pl4gc0D>L^Vk94/;:0[<ZbDpsJ^+o(F_YgUM9KKWi7ld??W&&FR&g]AFc27n*+b3rC
6Rl&4!I&D8H"?T)4;2cU&bnltpNus9q>M#RCOX!f?%Np5-S%"fWd'D+FND>%0W2Do[G$@[ZS
J;k]A@<7R`]A%)]A5]A,dY\B)ct8P+*SoDTT)nTCmb7Ej8'f'h.*&U"a5,*-3)qSt=prAMr-#l3%
0BM%)@O9:3MtalI]AcRd,Z'OCj;kfJ3iDg:4i/oF-:Kp=+q>H]AH.%CU+ga)'f.O3ZSm4Qmt6V
44nSk(cq2D0XNgM.8EQ"Es>,m+N=nM]AEoD*p"KXO-61&c`Zoc3lrs;&Ir&BN#_ku==eu$BVH
q2s?J4/TF0N2id^rSpMok7X\Vu]A%X49'EcY.!?:p#RiD^pLmLSL%URo2AQU:cNJ7RG(")MXo
`Uiheo7WQpJX"TkH4dBeD=Y2mtah^J*fQ:h]A@1R@i0j&Yhpf?(mdX!:F/`.;F=1%5p[[R/oJ
Z=3R(ql1IWl2L/(mjSfG#bCh:3I=M'T5n*#nd?Gnc;8:'-E%njS69knuKp=(7UP7W>f'DE$M
pVSlp5p'p<r0?nc`Aibi&m0Fi8:4*.R,P9fFXNCt0=7Vt_P"u5t,HjLa)g&a/:F/o,(pLhf`
bg;:<3npm'NgbA_NZpZ3NkCmFjbHGLGg;Mfr;f')m+k!8EkDr515r[rr4ug&I8p0!SH6<gh6
AeGkU`^-^Si)=:/YVoMG^EFCW9R,7_DJ\T-8;c=H0e$l?AT^+YjF)p5sH[:/$SZl\Eb#/*\Z
Y:$IE`mOY,.,ga'eAU^?ZAV]AWkAkOJ*Uin1=0W@.sS\5Yql'q]AG0QX#CTB-hHpA@2MrReN!N
h3Y!9<a?N]A(i\Qrl?\T)1I?+R(q-q=[#lu;,aM&N?CRl>K;G9GF!E=9.PD8EViMBQ&;t.Ohh
)tG?E^aJ(V;.n^dbQ4u]A`=\]AAoCf1,J7%/''LR>Mp<GM3Nu>?Y/tc<fU!7ME*prjH9n)Ke8E
S0C5)R5s58($`Yk)PTMll&]AE%"hPjq7UBuM=Z=!?^"JJW+=j7#Mop\:Xt0\g8i,[BQNS"^"D
]Au$k/lH]Ab[h(6(P+&6S,JJ`@q?Ph+G*qLBtqLM42J+arR\HXiom#<%]ATc.h$>1g`GJaF^;U^
bTh"s`C!TeL'<+jqTS&t%Qhrq+CK+MuY_h'#MU6%[\%Udf%RGoCI<O\T7i,mFgRj-IdL0O;1
b\Qc==O8"6>TbBg`e7d9P',[KpV*#bUfdn>D4>Kf:TMpOuD2%D^R]AZT2']A"olYc!c>)Eo.IQ
]Aip?3]A7.;F;hU?*M9W!V4!MaRu83SQ[f,IVGZ''!<HrOKBLDD!LFB\#*j$8S;VHWcnAFALq7
bb_r"(du84pLdQ]AiDdJ&P=`BGTWO\1YNCYh$u%U9Z2S>oT?E&3RJAjE\g$57Rl^J%)m+qO3R
hfU6PS5CHA3Lr@og4Dqn\&`(9F&OBQ)98h^#^a-6u5^lk2.n(^!C9qF/s$7?"<9XK(+hCg3D
q5qaq4`@V=rSUR\RTk:5]AjGo3Q.(1OP5QM@meTgSe;=XS$%4:`6Lr2SV<T,A6U_cTbq==k/g
Z:_OQt%OEY6n+Ugt>@S$HaW4RMMh=8LGLW!N]AB?Nh5-3SS-6(Z`&d$:*9>:l,N>'rGL':3f6
_!+*uk,\.U=E1bk&6nQ>tOT9BCK5MIBg;%5>qi]A[PRMDL'S3!<CC%WDH89gl8"O)3Q8CCs7N
T6Y@d8l"GUGT2t&1Gg)2Qlm!n7h\)1L$m4*(0(?6As;*_YJ7Pt@9C0!#fpmq%H=lr*5UCi-2
ZY:o9u>F'/+_uX<ik?F>snoKJrL(f&B1Whb;UGgJ>;^oGLf3h<nH?bD1Hr=$L4mdjtA#qN96
NhY]AsSFhEN$CtM62$Lug.L8i_Sm*66@>0^\m7/*IGo$dfG&h]A!OEZUjKq["7F.ScE1ho(tV1
"qric]As,%!Y;'?,Zu:RSiTuZSRd6nmnT!&g3B=S=QNVL(>IDM.M$V,)Z'V]A)uF^-eEqBg]AmG
aumA&:hjoG5X_sE'cXRd__</$8d`]A?!G;C0U!V#<`_pBgE7DW;<#fXeK*00""58cJ^bBD`_'
nk"LckEdWK"mu_3ZrgASh^A__X/^33)Cqkk(tQ#jpp>dP_V?)Z`qK4\_W8Uc5_W_t'ed,]A&h
MmB=0<Wc+::BS+IoAK4HJhloXM1d?$+f50ZS;ro+l_Uh2[c%2T-\%Qkl9A]A-[uc/*2B+JtRm
5"'%QB>uWuBk[M+5\bJoWoCH*\UkQp<-4`B`jj(TpD!P9&IEc?aB7Xa4EBd2XjNN>iNX'8M)
46]A=4OISppc-H;/I/I$)>%D##MOnK/EidZ)LZ:d,IhdPQ\;.Q+S(sTP3io$7aeuHfnC5)]A@m
R'[i9L[&-=dLBl1fK;t;]A!Bt@1d@G'&UgnQ'uH]Asn1[%K7hTfr[(Oge`XnA3le9%BnsK>2]A`
j'MFY@W@e+QtEN38r421(Sne6)J\)7i(gB1o(o/9Q06KKe=XjqGP-[WJO'SYd8LR0>Z.9as6
CPB:!E43h(1nXH%6AbO`9\4bP>%.!l>YWZQqd]A)VL_t3>F^)0D%Hk2`p;cBbhsHHJiDki6;>
L$>F&a(0JgRT#Ck5m*:13*YX#::j_eed\0E^HE4-X!(eg1WZrF,$+3,09G!,oA(NbNYjP/L3
5s(43R]A_dr2Z="$FNs+]A@K\5^.sXAohUYFdXEHLhUE(#`14@,OIPmNOW`\d=TBSBYU9599>0
Sf<rMe:W]A"j;gHYPP9MuAk:<9'!4gU_8e1`oP7q6VeXqEsX0aAOuiqlCWbYI<7P_3AK0*9L#
Xj"jSo]AK["I1f`DDZ2@-Pnt'bei,GE8ZlFKqKPO,Y=pk[5[K_F-m`^NPW*,]AatZ0oc/Q"WVp
iisLWm#5)^YmKdW599]AMWJ"rc5>:K$t3$dZggMIUS%gW!S3u?gIX)=0!49_=YSm/KY.Tb#2=
E+uI6R1p7,<8K2no_@Q<,04(U@W(g>*8G8Z^h5_f3C7k0GYm0tmVJKJ*8>JlWh<*0l$!\ClI
9e7rY]ASaKfJ.jnqcGFX/1=L)0J?D*CR9eW`uH@;N&#C=C?=+F2YdDqB+UJBr\r7e#?J'\'s(
dH.p0Up<j7/jXC)2^=MY>FIoRh%Okos*0/N9u*ij4V\kKP4gjblB=5dhqq7Vu2-hBFE?%*#d
%)uZ``aB4p?jt%_%)uZ``aB4p?jt%_%)u]AADhpijbaC?f3R()q==cH*'dO<=D*I`4lTa'+I,
nd$'6KRU5#h\p0Eu#@#%K=k@k\*s0Eu#@#%K=k@k\*s0Eu$:5HHUuCYH;B*94%pAj?+nDQCV
kN56?i_&/-If`)79Abe\$O#VN'anRgpXT;Yor"u%$*r~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="284" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&dmk=sIk(j-i=Z\h3>>?VC(1Q&7F6Qi_#k6qB]AVe='!8r93TQ!I$?f>T_.DSf>8Cd%XI
>e5E_(_2.k2*<#m>p3Jk\',!KdMD8K'nq0APA)s7D^A:S)q]Ao9l$@IHR/h?fjq;O-bWfL9Up
c6!Bj.='l52/":sj<0_r3k@nQ06,&JQY0J-.(@[]A"G7(*>>:),u5Ib[Zda5*h"m<53c!WKf=
`;olFVepafY5E[WsIh6mAoSafc091NEfi!"nPF4(,rNHce$W/$R778@r8H\/1n>mKSsTE5mZ
2&_T+\?ILVK&a-TXu8n`JL45%O=;o/U1qo,^?_%6cb9R@b0:lu\!IIdbb[DgYhTfXmSV6-sb
rnVkF'sQ?u-a)clj(I:_C0aWZ(8:KSqmrkI>>*l3X(^Z!cS.8sW"s<p0NM01qi9ODdUm'4$=
-O$EA.-GH]AE13^(OGb.+T0BgRI9[>M(F*Zf#i"g7_Qh51pR$=.8l1s'`A2pU8O!/Z:9"IY3K
O\oFqD!A8!\OZ90:HI*`;l<-$e5)d1f*u(!2(Cl=]A]AgmoWR1DK=(JERol(EBA[1rX;N@9$Ie
5IH5r9h.+jV<EP4I7%oH#r`7qBF^ueZ0fiCQ]A.)I#iE=1e#[DNdVrs@`f,3,<DlCUYR>$nEg
?/r4M,]A1/O4O@D\<elFhEl9IdP'1fC6<?_<BWKlp6a^mLSe(ZcI)(R9s$C7MZ>/-M[D/D\EE
lCh;5R\6@iKA;m2Nl2r6I7uZPnmKOe<dadK7UE:"=:_IU=]AM=-[!^ko-d)`n#'7NSJqZg$QE
'qV*#Reg.gE[n[-SM<`1(,=BrPil1jNOb6^Ma;%AcM#q?8V/]A!JghR_bp=G=Uh[@R"d\_FEN
3d^eL!`C#3o?)G\YP>C/fmb1X-h2_e[@tQM1QJKsQZ:cY_$U*X#>aXg<(ak0iFQT8gP_]A&DO
(l*>QleX(URE8Gb>,D.%E$/7/]A9ioHf%`S.Q<!qRH3O'7[]AFI'Oc]AX<-![P:rG.#r35p1>VJ
AZ)i"mk9Rg:YeXq7&fiN8/?>ZSp^IHXJ]A7tYCgb.KnD0Q7"_b8QVPg[VZ>T.KY7t4cZ9L>p3
1.7C,BP-iZW[6q"5l&oXU^?K<%/[hbo9[N5b2eq9Z'1AOJOmTUktGO1fA]A0ACK4R`LFsf"&W
C>EEFB'a3[)6)_75?`?@bB[iU0$5E+URQ)E#ochPr:`FGtLth.%*NH'UgGQs4Mi29&F83/0@
DS[d!2ftT_-;jdQ6nQQq6e'Pp;86Psp+313#nJ7X![>PYJD*_(>ON_M2Bi$m(S_RIYjZPl/I
o8C0jNTQeO7OkV1L$BRaX>;+4^O$Pq,CgFFL"\L<`a!'gMO,<U+VdZ/3#[kNS:di5$95,LF9
k-7jNe+4'YWf`QEg7lYOe=T)=W*r)+q<h=!p([hrG>)1Rm+f5Z15Gq3H(b89/k6An$tAhPGi
UA\R+?Lhg,+Sd*rBEqTKAhf/&[J_=^BHH\i4Q6_9OAuauR2'O-`g^lU;B8m&X-5QYoqg1o@Z
A8H'gE3^Lnt$;pr';IB*J#CDj#Mso3=Y$]AoG2fI,-Q0kJDhB1AOV6X"!@,7=2,MiXc^LT!F3
C8.sq*Zf!![Q^lVW@s=pXGMqeHW7kGc=.P.\cg.Hb5NRgNRr/#/1ZEHk(Ipu8cu)Y$pIWcn!
l%:WD8(5?gW*k38eMV1CDgHSRdit(A;NU!0E6;Y'TkeoMIm?7@LSjfRYotY?S0ksB2BXk!TL
?IoNg2]AT"0BY(ncSZbaF<NG?=QB3NI!FO(]A2E^>J5+Y1Dq2YlEaOW>?b:4k6*LouQd#-q&Wc
K-D*GSh=nfkIntI(k,0a_S'2)T,^qqE.aq/2OE!jP$UhBbcjHIR`o]A!6"k2T>>\ff>Y<3u/o
FR'qKW;XPGsCQ0cbNo)UAmQ."35HmYq5Kc1>>1\\.=i"OV0O6%O/74&Xacn[&a^giQJ!'o)J
9\Y'6>btW]At-XcSI3r*+/4P>ZdQq>$(k2b`dH<KC_1i=P_SolZ%\<:[X7O-l[><CuKH>+Dh9
D=PRVoHL$4W<0-T/k=beA(Z&UGB0^@0NQ]A=_m3KEabF_McoFIb_2"MKYO+.Q?(',-_LpqO2u
0QFacL(D)GCU9MQ]ADa1ZJ9K'@Uu'as_>mmD[m$<oPLI=p*^'2GW5q0E%tH%'/cPhY$!o(acl
p9W)H?aeL_3IXB#H\[;+-b0so%t:1[U5[qo]AJO1WW'#IaQnY0!GDmLiRPT&BLR`WSQ?i`b`=
[TeE&[ZlMuTu*cM_*GBcf=OeGa57PX>X,k)H+!?Stf$4qIA:6cp4'TSs'8PQhgu5QM$&:d>$
b'Mq$q"VacurFW&]AZ+n.EEUW-C$PYP1U0%80m_T#$L,aCd!(#iAF#OpIe;$)Yr98)(0Rs04?
iU9G^]A4QmJ,p'd!!=(R!!Y--!UTQ:IGU["5$M2C8`X[PT/N4ims-B9'H>lkrYG]A'jV`/OZOf
c6V#hLL!!Y--!=W?9!uSfR"u(Q.$t'&;(r-.F42WP/&XLal-:o/0hki5FJ88/8!=W?9!uSfR
"u(Q.$t'&;(r5!c/Z8$cE'9ub(,B+bFRsN!m6N,u"9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="单指标查询" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="eb964ad9-cfe6-4899-8446-421fe3892538"/>
</TemplateIdAttMark>
</Form>
