<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="tabn"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}'
AND TAB_ID='${tabn}'
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="yybdd" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH db_jyr AS (
    select jyr,分区
    FROM(
				SELECT substr(jyr,1,4)||substr(jyr,5,2)||substr(jyr,7,2) jyr,'上日' as 分区
				FROM
					ggzb.txtjyr 
				where zrr=to_char(add_months(to_date('${date}','yyyy-mm-dd'),-1) ,'yyyyMMdd')
				union ALL
				SELECT substr(jyr,1,4)||substr(jyr,5,2)||substr(jyr,7,2) jyr,'上月' as 分区
		FROM
					ggzb.txtjyr
				where zrr=to_char(add_months(to_date('${date}','yyyy-mm-dd'),-1) ,'yyyyMMdd')
				union all
				select replace('${date}','-','') jyr,'本期' as 分区 from dual
		)  t1
		group by jyr,分区
 ) 
, a as (select max(ddcs) ddcs,yybfl,simple_name,branch_no,max(score) score,max(rank) rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where  oc_date=(select jyr from db_jyr where 分区='本期') ${if(pany='9999',"","and up_branch_no='"+pany+"'")}  group by branch_no ,yybfl,simple_name --当前排名得分
)
, b as(
select branch_no,max(score) score,max(rank) rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where oc_date=(select jyr from db_jyr where 分区='上日') group by branch_no  --上日排名得分
)
, c as(
select branch_no,max(score) score,max(rank) rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where oc_date=(select jyr from db_jyr where 分区='上月') group by branch_no --上月排名得分
)
select 
    a.ddcs, a.simple_name,a.yybfl, a.branch_no 营业部编号,a.score 当前得分,a.rank 当前排名,a.rank-b.rank 较上日排名,a.rank-c.rank 较上月排名 from a left join b on a.branch_no=b.branch_no left join c on a.branch_no=c.branch_no
  where ddcs > 0


   -- select * from ads.ads_hfbi_zqfxs_ddfx_yybpm where ddcs >0
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[营业部督导跟踪]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.url = location.href; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop = '10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="7" s="2">
<O>
<![CDATA[营业部督导列表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r<'P1dRiD=?_0\kLj592i(J2:^dKA]Au\tfhK$ubI#C^L6n(1dM,=dKnSl[(YgfP/.EJ6Z4
%>i"9eh6N%$E)"U0IA!Yk_F!<n%6flPG[r]A96?HQN1EH.M0W^5M`pf)#BsDt.lFBTW7_PoEe
.=]Aqj5pt>D/Q.8dJ_IphCo+h&uLC#K3+8#ZK#EEF\RM9;o8&GM3Brmng1d&?(DE&,.ck7?1'
fZ'HHcBjY,Q6B`MX63=?l-,lh^gprbO4)OW[>!0I/_78n+0j,hm$nOp;>dk4$1_Y/Oa/%k3V
D\QuNDuOnMbNgIgbp^nI!>[;I&o&SUF(]AkNXinZG%7eL/rZH6+mJ@Wa%YaNks^E<TU]Afh?92
I/T`FDf9(/mH3H[J:`R6D$KsX$mC)a]A+XPpO0(+F")WC2*^or$5BM_M_L:0RQZfq/s3R[Gi,
nsJ_@o.7Y''=N(tFlB=#5&JSYfM+/aa8NjCaW1FTWo.1d1ltgH#\]A\^(9*lXBMLRl87[QA[o
:;11N9),8d$EIh32;`ksiJ[2NJPZkSUUSoS48W;b38+Qmt]Ac?;>l`2uhE&(2MGUm1+C?i`Y"
bgZP2^eo'$66]A6Uc=%3JO>*8Q,s92@d$eg,V3Wg=j#*LGT4#j8*49RJUq#jRQ0h9Y[LdV2^E
'ABJO<73DSd37/)8oE0ZXsA!6[,A;L#Cef1+#mP&(57^(:SI-OGU:Z[+rpuq=dVFK;:X/i0I
O^9G4U9ui&i+Q=9D)6?2g`R$-.^@Pp/*C*eU20C2ra6dQ;brPak9XF?=B7MZ(lI-RiN;H0m$
c-f%DDma]AKc?ukZA#lX#`B<Wen*[OYngSFEU\lM6NU)nKa*,.#*LL<B;ari>n-5AfLI7c\@?
"hau%!T;j3SZBJmsm4Pd;U]A^Rr3M0Bob/OW@*e%ib>)<[:54eMtNmcG9"l?h^6A5JNdSlD*R
6S.1?Oda#`V+,[YLD^#&&Oa1>I24F+,[emN)(Nl8^&8gA>AeM_)GJBTp?ks2TN'=D=#R<l":
jX,>*(WU$.8fhG@Ph1^Ah!X!L^.7$LMJb4RZO#:gYr3K1gI.e+d;&ei*Y:IWM!VeW&Xg^-S(
`1n$+nuSRaMZMPEf_KQPFJ!5;+9lT$ctYlug`Mo+>'gK=rs-]AF_Mb_-1m)$Ve5.R^,?<3HQq
OW2$9s!L'.ajLH<p.?m*"PCPA8'erYki>gW5,eZ=OUH\P?%7Rkp>;?fE]Aia'9r2%0PXQ':&,
o+2[4T-Nq>HBs^!(7Y?7(FJa@[\2&`A,cQ6gQ3H=W:!U"bLJV8XQ`dnK:;FQ!.Qh^^MA-hi'
sBJC0h`KI>N,*rL%j)^[H@j6cCpbPYJ\SUIYB.d!$oPCVljs-PaFg&9o3f'59aZRkt3t_Cf6
/gDIdVZ0C#?8IH7Cu::&3gH)d*`OdkEM?D(S?"(HDd7:`5cbL"+soRe^OpkK!o@`l9mlSemr
niegOl5XX0V,<_Qn*./qY)8UGWuEB3I]Agb!['M;;"(e&LJ`$S;+:,gLq@tlk^38uGE@Q=ciA
fjD!M!AD04ulBq\np"/'D.:@HVU<H,e18kB`%'rBb*bW#t2-IrNuN&=tZI7,3b\eEYN$T;u(
-ZX;93<GHEQ#cM[t^utu5ch)%F2enaQTWU@(")jWpo$_.!47M6tQK:lZ5u/1ffoZL`oY#P&q
Od&)E5qmT4p0JZmBJ)l013'+/(lt<7_9X&gN)Vr\RcUbJ+NFfh]ACd;A\K3HK/\;i\q@*@-\l
c[G%'@&l,,6JINsp)K_fYJ?`&Yg$LZ3\QY.aWiE*$HQZ4'XNo&Wl;9-PNXU?q5;9AIq:`gc^
"FG_fMA9.G8$pcH4%(dsNEirLS93Pr0i;X%@\_&`bY'1]A@L&)%CWbA`g*Wf.+K\W9-praqL>
e=Y'5kH7VYZf0L0#6B*Q5k$(i?"q*<\-d1JT@!*;K:e)#XbC8@Pe."tVK!^3+2q&q35foL\9
5_"ZZ#1RnD:Kj><Q]AUH>\Te:tdWkrPpgF/^6NF3;b$rDs5ZIgRAK6s8aqh`G1R-TEfVntuXI
=YhOKTQ(&M=ok\^eatKBa?5i@>&Nt;IX6F3K\0"D$)K`@OD<21rnaZ2sLX^mj"M/17n&5>6h
Hq.QK)9ppK9KoR-QRM?BPBDJUHid@FW`<3aG=7?i-Y3%raaX74Pmc18RJh+pB[CXA+Qq@an4
a+H+Z'K_@Hgi$%cO(j6'^e*<^5p"U#>HHsU;>n0jHKCdX":+*UqPS\9o@#R/POAKl._FqJP>
EWUh#"@<-Kk>t$!+=ah>HF`kKM[MS_oo7,`-gp6_N!F^b.oM`kBejI14Dp%UMc^T>adf#;4K
([cXGg-2DNfi[;%iTF=2Po++Ei,U\?"g/O;_[6p"_3S'C6)G^p+g5aQA!'h4=S*JFU"@2$Vi
<P:>lc!B-Z=<01l,):="a;BMVe@4Q<kACjX>)*hc\!OnN"9DA0+R"TU:E:Jpm$,>+eP:kJ?J
b5e;]Acc\qJ@<c#E)V\jK-*9%$l%jTof8$=-/d3hg7WjRe:g^X?tWi:;%7Y-G=Nr_#)1kuDn"
*+peoQothHc=AkEI#l-6Z[TsGc):0@V1!k=?XK-)CX:YX(`rY]AbG6iW*sAf+RW!U.,aj<Kq]A
"H%8+T^KZ!(f,\1Vt'>_9*^V6?KO#4d`;R%"=KpkPj?ILB:uZMQQRMP3"[O&rRA`q1<1=Q\e
s>7Kk;YDMp3VscFo0?_?\ZqbOAnD`O3;T4/j?D,l6\r]AfWAI.\A"_lT9;]AVdJNd=:SVZ\?RR
`d;A9+dKn\fJ9OGj(9P^.Z4Vn_//G1FOSCXVUCK*-8B=%E]A9I@4HoeW5;^S$HS*>WI1qTNrS
ZT4-5W-Wn%OS[ZK>46hp<?<u!PL:JHC7mQTN]A,0>n5h!JX[iuf/f"2SL;QHh#P?I"HRNFS94
LZe5AnO6;<6u#?W/XB)a-?U'Fk?%r`aHY76o"L#R$`).3SNK=UdoEPa&7Yn-,2O8.`sG)TfP
]A=QPq*Rlq/X;rSkB`M+3k^OQh&PQCRE2n<1*%CeV!+:,4CQ[j2BIGs"YlO:?9g<m3%b8WSc;
$+H_?1C.8CIdDt9G$VGTG"e0Q$ET*dLfb`5^?eD*A$`[D?`s?d-MI?1+RNE^8fhg.&ojrApZ
AdqnAb7o'7CiQqDo1i"gWAd5U-]A-%$'+N\9qs5t.?R5HNkW:E5Y:)G(M6>+TR7kuOPD5TC,+
Y'qT!$-![2V'gZ",SSBX`kG*)t!I2Vj-@amL\c]Ai.dLIWNX'F^S0G=6Nn"mtQ#_i4BW3P0Ds
%WCNAT*R8I%/&Uq-laM%:1GYT9:eG%m*SP0m+/V_3LH1l,Ir]Ap8L\;UHo)]Ao8oA@8\^eC^:d
OG?Wfh#IS[96PA@9M/#F:(*m+nlM6O!W)M$QkaQA-BHd"E&W+cT'@2&bL21,Hd!`*s&.iEfd
O]A5mIL;I[QC;+dCjWo5O8`J0LGQ#:Y@Rp_A)aCV-.=_f'R@Jep!/rH<26jCNlB1c,i6C\c(G
5Ymo5%m2f\lt.?Q0'DY!C[JI;1'q=FgaQI=-mll>!8&Eqg@Sd.Ack7#=m74`^\]A+%jicUs,k
dFK:KQE:D).G85WW/9E#dZku7q.S"C^:34;4u\$%<-jS<Jf.4V+[\RP2Mon38)RG_`]ABbWlB
PaSOf]A6.O$3:E;]A)8oFi4ZM7=!,ug0^l>+X5s,oVo\SC+N?grjWJGTnj$EkF%)MWU4Ep)GRH
SP+q";?[hTK`J?0VmdUaC,jBGCZa,B[_`'@T&f,\2Q>3i^Iq6qu<rkUPF`'<9YJ)8jl_Ap`m
-jG4-b-=c='^LYdDFT>#@?Pl`j\!J8soV%USLK=Z-92>.-P%8!qiN)@\?oDKl_N#2"lXf[%s
*UV0p3^VQ[?I=I/,=AF[N.HP#BK'*//UQ4SX]AHm[dFu@@)mGMBMUs@HIRm3?Jrp?%PjEN6^X
NMgmC0c*RRcA(?NC(\tV44(WT;sQdG3WL'"O6"r?<,K^W_/(033UW@:;`.9pV<qU$5=Q6sg\
W,1VC'&o5.8'8M)d]Anrjn!*Pq*U!)A7-hhj`7g\Uor<L-/;LSqLN!D`>jA3m3>H$S7>@=1>^
]AM'bnN8p0bRfj)<XQbBS*5.oRbeS:cD-QU1^8n-JLDlnfRP,2."Q\[-+rgqfQY+Z]Aju%gnb;
h5H.3#W(S`R.8(AHP:C[@nnoKW`>(`mU`(=IiKuY)?IN-oe8ZZ08DEX3GId\4Ceshp<jBU)]A
KVd,.bI,?0]AN8!6X]A]A8e,M1JH2KT-'rM"Oe(q8lOG]A\?5/n+UO^C8OWXSXel3uDST8:-p)^J
WHdV,s2#$"")KIntc0/&@Zb-rJsWuo&FY8l>9++eH30[ZZ9]AC$@.)WW'<mho1hFSgu',CI5,
YHP^]A$BJJEIAr\O/*cDb4[#iM=!,h=mC6WsQ`[FVX+P6u+UHJXR'`ji&2(i#\hSY78fdr0$!
4PEWekqEXG)ppGSL\<9NNV)1KJ5,q;XGNPb(O39lK;8A!9J*[n`VN7`?;X&5&#60aWc!b2(f
B?hd3(eU5=hrMtWAq<(\@eYY\L[%IRSBL*#0d"3%?TEQo\5mplCJek`Os#2bi!W0;V[/MsnW
o'-B<R@Vhs3+c_\#>)U0"aQ\$4lpelf<7*d3;a^MeE\SU.)GF(===07#P^j/YY\@L_fN1,;/
f&As:asj570Y.)7LW\,*"d=8r^~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="124"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="124"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA2').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA2"/>
<WidgetID widgetID="cb1d2383-2abe-4ce5-bdb6-50cc01fc0826"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[0,381000,2232837,1905000,114300,1152000,1152000,1152000,1152000,1152000,1257300,1257300,1152000,1152000,1152000,1152000,1152000,1152000,1152000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,1984188,4240404,4240404,2819400,2438400,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<O>
<![CDATA[类别]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="2">
<O>
<![CDATA[机构]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="2">
<O>
<![CDATA[财富综合\\n排名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="2" s="2">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="2">
<O>
<![CDATA[年度累计\\n督导次数]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="yybdd" columnName="YYBFL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__413A4477FA5DC879A7062C04A560B7BD">
<IM>
<![CDATA[!>5b,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$/%m<5u`*!m@.sO>ZDZ^ca
GpuSCsI$h(A.#Ta;fW;1gSq6pi\[@*oMs:lA5@(srK*01KI$-JEQYU*!3I-u'$]AgF^#nF7-^
t]A:i,#"CHs%D.B;l,_?%R[.:*2d[@Z7rh"7Ep1;CnfpQ"2b^*(Ucd'hk\bD]AQr6ICLF3_=T,
pb!38P2mDP*Daf,q2DWb*j+'&?YR?hr$_M6G/=E\?=g!?BO./(ATZ&RM@J8PIJtN4MY"?1\g
R5ipVJ$ZhbEldC>l%3mZ_c6*+5ZJns1AIi^h]A7=Fs=m]Ag-8h:i1M)S.[)Q[h`E[F0!)X`0r!
WOm4Wh1sp!.t.<]A'r"f'LC?t)8t9i0E?E&N=jN0op.VaK[J6P7a_sN'E%JQ?1W1:n2NM>ifA
AOAM@lJ8[LNq[*!"cG)gnpD7AtG?J;h:dh!-D8%:Hih<FUl=T\38CXGcPZVI4R-`@>Gu<@pc
HE#Wb4c7Op3!<$ZJrM$?g_Kl/#Qq_J;O+QC6JhCrk.f'l",639@\"Q_"W&s'CV^-K(=dK096
s(@?!672X`;ko!*!4S'COiJ1!Sj3pWg5M%Ln]A@7!!_>uGEa)f\Nf;Io!=pN^\2g!k+P]Ade$^
^OH_%=8cb[FH@9HpqR;srcEbVXLmh:4[]AFSsM*-4*W]AKu`HMWuEO*Wg$W80V^M(F^&>$3Ud&
O2+1,9M(jp%SXSe/8)\)c]A(]APAnu(4!H*p&-$?S$SJN1a_4o6^;L!/NH$MBO&:sEZMN7,H=c
qo2%1[o**+p(^o@5[6f`284F'NXBflJ%>DSVBer0aX0=ZA8K\"k3e;e%Dq/ZJuaC>Gj7H7o>
d>:8@klekT:EgHuSBFNhoElVGdE*i[VmfQH*:``$d*iY(oY/:7K&)_=*\@4>JF#6R\X&O;ur
0cJp-H0.T<1mAXqB8YlC]Al:c,b^EOEn?K73(?m;;nn_!1VfN4(/R<sF[?HPa1M]A1:I)r$q-*
j.\H1!lh[nCYMJ6\'k_IU3V%FWtX;+bc?>t18LAX@O3,1e!3k0e\go(+FGh?eb.cslDYu[k+
i6rNOO25\f1cU-R-;3duQ[pAXebi#K%QK)j*5I"rEEB=k3,uXpED?TIhn#Dp8V$\@"DhrH;Y
47m!:DZ/!Sdp!o9?_1CCP,PHHLW-YP?0]AJf=d$O.dan]AlChga8n+*3e,Ml?&P3PB2$Yj$34r
bi6E8QE8hl<pQq8@/]Aj6$!aMV[_.=1ap!\5>SghK`nTf^dRnYQ95JYP/5^-^'!Gua)>X9$M'
M[uP4b8CHg/Y-H#D[=\;^(fE\>#dJm9`!XpP`mD*M,m\hCIaigeiUVe8O87&?Gc8B9fhlTf+
iegbZFa;lOM-c>0;oi,JB7iSAnb%KjR"a;oV+5m4HQ`OFS)c5P$>9&A+!9Z&_ca+C;G7!SGo
]A\iiZkWrM`_/2\gjYkEto9HU$ZHFIDSD1u6;N>@J/%opCL[-_MrBo83=M^[t)km#B&Pr*+>g
4kjS[?\H#gO@3TWd[PlP,t7o5ZZgE8bVZ!cFLo@YSBhgIuTL>+cH>\FKd\9f-*Vlbt_8nt-=
$:7N7R3'b(Y6bV#1\0Vq7BKk[F`2BW=$#XPnXukPZggBuNlGA,HA?Le)S87\";;jaHH,Hh1-
N=T+NDtj$$?nWIQm7p#`-uAQc?6g,$>]AoJ*nPmoEqWak'OM$Vmff$$qc<P!c:@JPJ(<^>q('
tEgUS+j%GqpS3$kGAj"IkF:*C(9*SKE4`MK.E^<i9DD$]A=EUGW6Z;_Yb:ee24:KBoB+&n4tc
17tGab8WtErjS;Xm6MnD)k,_P>Q%em=L!JLUG[UGqj*Scl0M,EUcWa^1Ri!0!JRE$K@PK.;K
,tIb3@M$c28X*N!?V`LYH(iVe5q+Gekj!m&Kmt<6QeIaU6s+B5'XZUXokFR]A6O1[.p^=T`qL
AVtHu\I@M?ab?=3jRXII8#I"UEB65,=@A*Dag4gBRIj&-;L,e#*W!7Ke?Nl"%AQH$-IC@"Il
`,\uU]A@fa@@dcb"t@^s@g$crfBHQd;26LtfYO6A`=8uP5,AFqai^o'SdJk)la#s[,c!o[4HB
J"ZuRL*CS52Ccm$F:cK$X#HK,T"Ynun-g3q.DRi,Z6e/55$c@FHl7@cH@?bdQWWGZUFi7S:=
bH<KHCXE,9)hG'<U5N?Y?(pk`?Z^?poPrE+_("n)T,*;GYCu?Zq,_5c%EYO'GD8/-\HpKSk7
Wa'Ec"8!oN7I<Sqn+$ij_(`VEJn!e>ij05"&h6>D1Jrd9_uD))ur0,Ci+4)JRhWMEAC@o.Z'
Z+"t.`n8:Ybh>I*a1sL*?H.q=+]Arr^5">1%Cac4B$&V?hCo<39=c7ABIZ%<@bd>J)_D0DL0o
#Cc[8FM>_Rg.>7i!tdR8faH$$ufoNJGr6!?/7>;gD&8X$4amd3j^YLmh?biV0`5]AfVcNN2@g
E[g<jI#S,G\MIZ1j&7!eK;gA^3b+!mY1%NmE[3ntUg:EqrHH`Z.1;\$DDjduBL#F4f+J,KB$
TJHYGG?E^PSFZc73=Cg?^dHb*o7%#'"G*?8@?)M>i4:9slJIi9b;!P?b?TKF462`K4L!8V2c
?]AYKS,D/LYfVB;O8U\.q5/sTge5\i3O's[mrN@[_nQHe-WXR-*Mr1j]A8!,gOtHC>_gJ*G''j
\.GbI=5Jl`0F'dRQEa4ARMGLDoSB2kRQ/uVH8;J/=GVm<8)Wqq_XAujWLYGRIe5%MZCCmOQF
1"[3F+?0u2rJ?R__XfdKaYJKl./u7JQbXZWA/PG5N%LJ'Lp^Q>s:HZ2UX<%^C9%&T7BQfM)I
tmA2^[1Z.M>oPMSB!&mkjMTa%kf!%1i_:-Wf=$2Ug>`sli^`Un_MQ'TUeod`"JB]ABc9ktY^"
*fD*D\BP#o"pNcdhiOK&j4di&"54?GneV9G)^KC0#C$M?l?a+)4m_LjO`-W1:4&9dJ-"UKJ7
F3;:+,3j=_'3od,U7lXu3R_M<>GNm=aDBg;#l(i#'8Ws#6W,J+ME"1gZG@nJn^467"8b1e(Y
Ei$_hJ6hB,n9$nuVQb!<3!uRkLq4Nt!B4,:RJ38"gK%!L-ibo=IHk`PY&X=`"1]Aj:dWKE_I1
=#KMYN-pgid8@:G"Z4S=JYs-E-0;nJBQktn&B]A9>pXAYr:_>9#=O*LOH4F4'#/W"533PYh[Q
ZL.g%;gFc+'<i7L;H91i*FP*Daf,q2FM^AJMT(Xr0kOK$_0!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__C6BB7A0212D9D7AF5B063C7AE49C5CB6">
<IM>
<![CDATA[!@eH,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$1UST5u`*!m@8N>*5rr=a0
?(oi`[U]AU@dFDJS5]AhOe>P7,=7[jVj1Em/V19,X&R5KD:M%N84AB(R)J^Smd0;rmDEfSgDBa
1AfY@oPR+9QH%]A(q+B>EiU4e[dB`c3Z+Ur.)*;Z5:?2OQ:kIYO'cJ=Vsg;iUKb^Ah;^\uF;N
fZ6>.8Fl?;k2h]AV`;XE:;nOEpp6r^gAV<?&9YpZb:_P!P?O\NbODPU16\^Qg`<?/T:u!(P:]A
6(kj)&nomsIJg%;=9T`rjrnLF0>LeLgei5BFm*&Y))I2jG0'eQp5$5D^3.KC7LA$lI-eLCP7
K8[Rbi8t/d]AFpJf!gWSO:K/DqS'NEBB8(.Di[8l8s&sXA&->l?gW`n<s,lI=5>4-;RSK4,"5
TMiq=eu44K-qblAg)(Yf/#"`OV[FSe!\GA?)XE>`4?Z_HQpr*>j9g]A&#eja0*_jf(B%;&HH$
ir?^)S!ZV5#P(5Su:>2BE"E98!XpO$P"5HN)&RX'ncS?J(/bCdeV',di!46PhB[mNOT\tL6H
kkQ[<>BPn0TNo"c^%Kgf>)_/:]AU]Ao&rOTuqq->t3!+'/`Zp5lin3pi!*h%GE>S'!(H-3mZPM
`,S./XRJ<RPHql9a8R64NB]A/a=%>V_ipO/0&aPpa01.2^24U%]A`)-V</"BSj)dZo!(VXoas)
2\1BeF2SmIcKoDL4'+)]AeN,[t0.X'r!N=8e.R4:jL]A+q)_+f(7+h5g^#N0l#m7?3Mb_4LJ*0
HNqE+D7bPEjKk0kjp^`Q8nHVr/8>Wrok#!*9\%Eb)"DD(!g5E;7YXRfq$\K1c3;HnKIV+uo?
O$)mo0"2HeQge*+O!-!1`MC/Dg'mq@GR8\Zs5mICm+5hSVE45r);EV:*'"Emm\%%td!3UtEB
hG&?;:emtiSZe/jeO,n!-_unbHB?3Ik/ed"I+,(pRncO_M_('Fm#GW@)3&_I1bD?$GdUa\5$
U`?81#^F$U\@QEumm_7HqE6"O<bC]AL3"^fK;VMRVe8^q(KC=qir;@=e;(.Zo;=WC2eVQ@O9G
+PqqP(+fZ.NG(e*!o-HK1LPofY:IVShcbPGWb$/5$2>%)nGg3BcmC;>]AT41<_tMef"P4'eb6
i'3e(k8:ee5K=5VjU<<WEZgY-!.l"t*!3rr@be(cetMmlA+?Z7V7$CM.*]A<<KB[V#o'!W9'Y
Q5_[Aj<@7N(P\Z8c\cg39X"(om)mUQB33#XLXYNp$hP->2*L5@d4:b^P3QHHZaa-LVWW9Mf,
$)&J%Wpb^dR4-(.@Li-_0UGXAU1l++(YGl=KbV&J3u,+eSR$=Tr']Ae*<)V!)'Xeqr!hK@s/?
DhIY!s@S#ZX]A&R$.BX-c\g`!_=[/N9ffKX0PPh%G&aoka8aLR8+C1Ep_:M6n07CP&r7W`8'k
rO9$aO"b@2R/5j]A8.U,IGJ3S_HN(N5[Hf0.m]AVNaS6XG##W\%<G(hDh']A<jG#=nN_\N\^UX4
pjPk?lYST%#OuNKJg.Vu.IXnt3]AjTeb3]A#[<;I"lPcQ-P%jnoIY0[jr/Cm/e=HO`M>YT,6aF
TLbGNkrj?9UEf/6&iUHqNgS^p"K4&gpOA@O4*EC]ApK.9&8/Pggj:CjOa'HCSkT;"%>/>e7ha
*#uuN47k65hM.[D&^50fL.=*:3XX#!G+\Z@U'2!Z,[-&e^;UfEqm4m%Ei,!\oXA50$i&75nD
:g`c1n:kTQH]A*Z'_(F%M.*NYEH_m*km2m%!"\7/"MQYJ:P-3`sR'X\05VgD`foapFJmU%W3Y
YbdcD7$k5`o)-cadC8:pjpX+\):?%Gh"Bp-`E`HpVC\Llp7cu;inJoj*kp`lOW%=RHPsqim*
g:!m-@YmUiH[@-ueai4\/Ja=e^'5,C]A7s&dbko*4RljGV9,'YtarF8AW,L]AK&1uj,YP9@O#2
/W3ceS"!iF]AA2;&ZdD^q[.+R^IPeA%8D(iIjHdn?a6OJ"RPS;M+oSmp&RkFTZr&t+-bF"?_[
DbA)"m:]A)"t>`RnZ]AC5Ft-HU<t.YC-;6a0>*+d:!91lsU9WN"aTh?/#rN_?hBrEo6sA@V?uO
@!>$`FNK0*;cS';OTJG8XH=u]A?ONm[LQN./(Okf]AT2?=$R59r,]A0,smeV)_F$R]Au`kq\C<r(
gP9@H%A=MgCL^Mqai("$o%'\2I0VLakn9[]ASVn1%_MW@M&=uA[j"@tV8LQSpd%6O=QR0bhs1
p`(of:h%=rG\_,5-#bG`p?8Oo.L?qLia3GNrpd?2?,I[S2]A,E4hTNV.Ce\DsAg'>B"M+I:Ja
2*@WsMS;i,b4BIDZk%2f[>n*c%2qq6BaOX=?!P&&:)C&@g';uMfo^O]AP]Ana8/7g]Aa`gj0[q8
/^=q-LG%YKFe7b$Pk,K'nVY>>o4]AR!Bbl_B7TRo4l`Z('HAoa=!3ZUT";S18g*ojj$,#5i7h
-Ci-mtublH?@JSB-2L!A-fWOtuMr(UaQ)_i=]AF0slXT#HFIBU/ql+tU0$qCOj\,/HEOAm5^W
E//%`6^l!#poII'()P+.R1^Vnomp"H"MUu#@'qSKK$uK(K4]AtDTg3O\:l-uYn9kU0;bg]A,i2
t.D9,n&P9>P@aq;ODhcSGQFNV%Gcc&4]A&JUdWF$$i,_(UXaQNte*m%MOIXeqee9Drt=3>9&@
#q(/W3$@pfcF)hlbkcEA"L_CCDoH?5iL>tL`!:D\Fm5jJZ,>DSO>gN1\?k'$r=c(`fbPMSsl
&W3]AiLk1@BYGM'IIkR!N4NI[T[d*K\3.EMC(:/chA(#uWA@YOFT/<EUp[<5_jY<@-g+Lu0>Z
p+Qc<]AnD[`a"H><EMAIL>/%L&$68IW*[55J++Gc$p>X:XW/Oj+=WMi*WJnFn8;6CX4
b?Ac@A8gI#[!I2bHVSdtHY+a?21h%'Q\I^nMFt<:LZ\CgJ?,k"E$;kq+p)s\6A*d+^M44Jlr
2aHT/YX]AV-KYd1@ea'U"(nXH*.Hl[4Ee-RFOte2G5p>FWUAF6[kYl!s8o3S/_`ra7j$>4>":
6:`M1D^L4SL3U1kTANm+&$OiQ\Y=F\e!QT\_:5T!FU0oG556>4gA%S?*oFdp75g@\/<,hZ*8
*7!Z"V;.hQ`RoSb0Dk<,l]AgfLsb>_Sp#B;:&Eh65Bnuq!Gk:"rsE!5c<k.mi`:0RfBKHiMf[
327fl4o%*(R=(pDuT1Uq?b3qo,rNpQ/QpASYe4a7-/^iHgf/l$DD;k2h]AV`;Ypr:F>pY&*d4
+$MI\!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__941F354D1F9462B64D43B5EFBB8169AC">
<IM>
<![CDATA[!UU.*pPD^A7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^#dd/k5u`*!m@7s.*7YeEf5
#3#+b;l[#XQ<B,!^emPa7Z[UHFePHltO%5bb!<.$YopLuW`(B@dGUJ6M`"J0Me6aj]AeJll(a
H#m%"?%>h*<d#-6Ud\]Au/5iIhBa:n/,pWhE$a0'W!2jmfmY4U>kbg,!,pG_8!cJ1K#Ma]A'e;
2S89U(jFP74Ci+.PBg623M]Aq.<UPcWHZbA!U8/&PWBK.T\*(p(#3m>mm2@&l6(UhUP@-HZdW
Y74V9A)/T+rLqXJM`2ltqD%2t[*f;$tp"Uf2bK,hQ;!ar16XX8!?LB*^BCEd^0\-Uk\=r.6J
Y%ECu>`KkI"PnuU#=g9YKB2LfPklWi@>ODH/u]Ao;VptZj$Y@gnBGS/kh2tL5\X`C"f;tb!rW
\>hNBGgfWM(8kr"!c,!$Ju.]A>G?H?N>B\(fpasCg`clVGNWd\V+_U\es@7q)49h.&gr@YCUI
a!npeIF!\jJ-,72:!g.oFDeJssOTt&Y"W+LD]A"lJ6W:a%*$F16L2mYXV5X,ku3B8HucIE(jR
/IW4RIa<gXJjH:Wc8)qm>JKWdfmcL)3,`dT5PHrMrbbT)&2(*$DmrXm/l:Z/23C82>^Mmap@
C3Hme*\?@_f%U`GhgJ]AR:Sj<jhihe;(0a#h>A)OqMC7XL:#m_u^@!`_/0fi/m"0#2TiT':qW
^Lq1!%pdfuZg,BG$3KQ[!c/5".Gthk^srE;%\-]AFeIA:Kj?(0-GenM_i$?Y!NYda:E3_-Y4Q
'rT2\,;?2Xa^tHNH/bOPWA)7:/%N'Q)d?0#0=HlS?8H!4[=kS3ZCTXWh^71#/fE$V^6@%<pM
?A6#\0@T)#%E-6E^<5?bP@-[g@bBNj1JPh.<hkU/647oA?<:,55);q@l0!<$eNAh2@nRf6TO
^gaqXX8"0ia%)@f`&CJEF)KbdqTh0mKVHd@hIKA$IS2?4"LsFq/>9!&P2S)!ZX\%!P#Om$^b
#nAfk#3K+5k%1UoK)rjgM)+1^#<GX,KPbrWNV_:@L]A%oAA$7hj\8Wk0/IUtWW?LW9Q&bl_kl
i6DWl\E,;mCK4FibkN`&BV'Bc%6a,)f1,#+7%jYE(c57\Ek_GQIpYK\I>mb5aZ+57quMKQgk
lk1)Qtf)e4UN+n2KG&-`U<<3Ym,gdqWJbPH&oe!>TCB*?oIY8Tdg8*\^WhD6hZH=\E$md%74
E[G:no.0=A.S</7ga4("e2.+:#fcn6q>?Iiac?%\<W_ui_.XE&\C'"Vf]Ar8:SGZu\Q<MWn%e
Fm-N>p/(l2Ut&-H`U+O`t1hTDY!Dn#7QV6<8tbShY)8l]AT%;>%sn*,VU!@V:gYBL>LBNi6Ld
=WFgnC)>XD_Y:K8qMh1Jd6k66Xme`1eeXd%EP/=#CQ:CIpWj0fWbl6!3o\"Pa9GageXE'@U;
X!1k?S(TtqSJ!>17c]AnI;lZ`^i;Q#+f*hnEhGrRlN-Jn5N)n([\A"7"eZ;$P@Jk`po1kE;Ip
P[HO"bqh624WrkW/8H"(msR]Auu_0Wo:U-_!<?<HT)#o(P#2bs#[Adb-<nHpHS$5Y_sM3V::]A
[Jce=UL4!4dSc7C7H)d"jicI8U:+@eMO1;k:1o#RW'/GlAP`b9Yg/O'&<$s>9<bIP5$BHd]A(
92dYT\q6>`Y,;mDQ67.$st:XOgtO;ek;Uj'amd^eO,U\a^Z&QU8@IGU;:2@6E'X-T\si:#4k
fn5)([16lJQeZloU>-PnOhirX5NZY8iWU!5^MFeR!$bN<SZQZMQF6>>h'Diibm96s6KS.3+I
=15&2JL?*6Pq?UF9$^U";"VOa2/pe4/q$HPq-K`AY&3p\3?8MHghpI^Rt8$O!4C[.f@@0='8
R/^mQFW=Z[>'f$5QlMc[#@Bgk"&eKF(sE)B@U%BVZ_M%K0%nq/Q>tEhERGNo'"&^P:L?_rr>
jrH3<^a1GqdKOq1EOsX+=ZcPQ[Es_0U*'<Qq'O93eYN<or=4g"a^W\aFldCe*8*\C:"fE0f,
R.s$S]A':Egb?OY(FV7t#>a$=s6=>lkE8Z.#`u^kXp[o<m0D6(0Qtp_&b@J;*'IU<322skT\s
j&)8i=,$F(OXa5gNF$+#e/3,fem6:VnN]A1d1gJV'WsB63f/I2=*0U"?k/QE9e1,7kq%\>*nb
iF11)3!8\o^Tje>7U6f]A\2H497f#&-$QosDF1*XY8e-C;Cf8;pCb_Kr<fYqA7i1fM9:<20Ho
@]A>5&]A""fKCf_E&fASV9[,SJ,1S3!MDuF+4i1t<C^Be!4ImS]Aq4_-#EY5PDRp4;o1hYkq#SN
YO.q@o_)ZLG/[#,#+,/??>B;;Kl2c)>qjVH/KPMk%r1%hp'@`Z>]A[iMD!tK[]A(V@Qb:'0k,.
EoP"/ArUB-OG_rfL7u4E<,D0g=+=k`6t;]AC*!#>(`I?A%_QG&GebA-m?CaX\$+;YJ^3TdfB0
;f'<c2ilaHTlES/g4;B=CL.?*1qmB8VahB+SM6^1V5!B6d'<I1%JGlYPi32j[?HEh8J:clQ"
0!,fls#G%+7f2G\kA`<<eGr@ElpZ*fP;n=j#CuMAG>c1qF,6CG"D;Fn(Lm==J=q;uZmTS>`c
lde\>^*[ns[s:[QEjHQf]A*fN;<f!R^/O"b,@dKg1[j\6>\pgi&4u\-"979(]Ac#]A(H[,InO:E
JV:AqoIkchm2N/r$mQBe%SF[Ykn!=>s(\t^<^2;pq62uZs@)H(LVk<*lc8f\ok57*Fl%+&=!
bsZ6XXn=;Meh%Wrbh9ig<KZt$(SY;3It]A'k#:OT=jOpu]AG.j**r'4oWRffa[Xlbb>XCTlV_$
&[aKYq&[&O0DV'nb]A8l:IDPbM-+q>g^?_C*<C!@e'L!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="yybdd" columnName="SIMPLE_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="yybdd" columnName="当前排名"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="yybdd" columnName="较上月排名"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="yybdd" columnName="DDCS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand upParentDefault="false" up="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="6">
<FRFont name="simhei" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^HP$8kYCi/oagLNC8XOQ(%jd;Lrlu"Y$WYFWi,./CH&P&XBZ8=)pJ]AuUH'h;(3W8("%MJ
7m(5_Y)QJ.nY$E3R%5rP%Inl^[:2lasmsqte3G?*'OYH.I&?[=7l=GM`cMSoqKrY]Af25"U0S
hp8:]AV=&AZpD<:6<?fX@)bJOu#$D:Aq'1^m<ng>;I0D5JU!+?GB/Z6mbYWE5AHZ%)HC%SZS2
q'CN3gI2Q>en%2:Y!nlM?<1^Nd=t86@M#$BjckFd;;+_gCX@>qoQWpkM.uk7^\'uUF?DOF6Q
G;O<C3C$ofr&g<6bL1>]ALLcKO#^T_rApN/@FZhE_[>,iJs/\P^kgKK14u:+t_AP+$XR>,e;P
n#G#"mgAJu$#b$UV?hq-cr*]AM1%%$pU&,M!`IVppm)56EffH<APgc10<6Vt@KoXI)*KgjWkK
>q[NJmKN\"djq#j7SC1s,#.AucU[*.s=*h+L3*nfR4U$Fqbs(.YK-:rr(9PIb'P*7.qt_#?G
IV9H7\'bh,o.&j1L$qA0@[3V#ePe_Pi,mAu1(-WmTl2T&M]AMpI`^HK5Nmc5/Z=880s,!-lgi
f&F>\1RSBDNZOpZFE%N%:7.0!<\3#?N*B6r,Z%.4l]A^!,h8/.AdMqLG&KSm[pi>gN55+FWLj
.Xn&=Jhb6:NI=Ye.Y2W95bh7=!nm@+']AreHhar6$*3M*Q_W16urWra<`q\EX%`rn[ico))JJ
,I,8ZaT/os%%!eTcV"*N$e&14I<0s/auEQ[n)a@ZgFKcsh3=k7O$>)*]ATFJZ&(AskkF*35iA
-o<_"'^\Y6DX&jqpHu`!@Wj0n?(<h[5Bm4Q,2aQaC#&NWd?$N+'5NiQ0H_e)j>G/9@-UD+4c
,-XA-aH!b.peu/\!B,4?cG`@5#l)rU;6k/m;)0I7<Hdk%'+V$;RNLJbmjNiTZo'`V9aS(_(_
"!VE.#b"hPtm4B7Vl:=Q\FAN3Jpq5+Zl;LrKrag5C";QKb@%>gNX67Rh]AcZplG5YS6&jP!fU
i@F'U=L*"PKbq9S#QB!X/@TG1JM;TAlL>M!G+W`X32]A^Kqm\,BJ:620Nnl*>]A[p)5NmWst$f
Q)%JcA\;j)=XH@r5CMcZ$,`3L\XR=kWm^aoY.uO*CNX'=GUg[->mE.#mCj8HC24LSPnWLPhg
h4lm5533"P%TT$ZlXeL1SLGfKA`#ePs"DMM>Y$3,gC(ZNqU/LT;FD@TA8/USjus\shKC/^Cn
CB+_[t`J'V0rnd?^/!R'9XL39uI[MdB&ZuR>BainT8]A+.Lc)`sq)#G53e1Qq?mMl&b2pum#E
gLJ:^jdq8@qp8>;uV+KK%oko6ecIdP!r?-X`T\m&g!MN;mki$:!smgnAp[MF5"UG&Zqj?f@E
AM^:#5,[u9q\%Wun59D6mY0^o_KPE[_@aWsN;KsA)EafDO$=p<Ji<e!sDK:"dqn0Cr06Bjo/
NA[c47,'u?]ALK3A9_V?bN-,=L4J0RQ-SHZHgFPKg?15A:nFeAHX/q..?)I^mg/jRXT$6dKRS
5P@o&<G8%;`4/'X?.(*n\7=f$=Y17QQoJJ!iV)R'igo0-PM;>fX4!F;'\s>E7%SDV)@f.Fp3
*\Cga6V4!OCSn^@X4>;elg8D4F/kno3%V7r@bOI/$6.[d$G`61TQ_%,]A3q0&bgLpUe3O?d-H
R9BC>nsjuB;09PXu/H\SqguH<oKLk-5MO^iK^`4-=LD=JY&Ji2eb$R]A3+Qk^ZAmYcM_4r,N`
u<&4Rl*K%T\D%=&'u_Yd3Pdd-.l"nnBqS';_s+Rn%#JjAUEmjonP%\fC0jONsOM"?gbrp^MV
H,`RkHU&;B`EW;CpWK*X0T94XJKBKiR>M=Om$Y>k9mYJ$'.NM^+@GKKkI@eqm$H>a0c\G4`%
TNEjB7rnTe0nJ'S/pT>'jn4TrkbamD9RBiVKsrA&$.ufm%)O]A.tL$RoCpq+3.:cpTqU1*GZT
$>+L!&C&*$EL$'U-7As97-V1_knO<<.O-%8m:0:_h/4fB?]Aa7t_*FG9YmIVQ/436o1I?M]AnQ
"3Z.?7/>r?jgdPODRhlYtha69lPC]A9hAW%IPSA/>\b^F&:X.no3V5\$S@m$$cPgr6fN<(l8o
rR=l"g]AP:2$)=((@'UQtF'(RS4%O0+qU/A*KVdA,WcU0Xl;D[ns2+9>[\PNM;_D:a!Q!26UM
C,)/i76PBdkOk!)\bK\93jDttgO+t]A@2X&DNEGqOle>eqMC\Z2kAj>T.l)QpL88KgmdBj?ee
uo!C!PmiIL^H(d^!9>iG?-1J3AAP=CG:\[`HJY`)aTSm2RCt3JO))'d!KYR<a5&BU+M;YeA*
8r6h-&No/XJ)=,/XG?Y0'_]AOMZV`nb.cn!;#5J$(Ig,.+lFm.)jak!9?_8Ke_)PdO&O#h?Wi
8Y9`]A[5ihX>e0#!Mj)+HG)4;cnsJuI>!sY[!L:+_OQ$XHO5d2UQjBR^tNV:XBtVFZq(n1/nV
cTBHHT?<P5G5B:JD8/$QT-:l-I'>T!A3NEim?6j;"^\"fp`s/82TY#PHnSpo9->bhSd:GI>C
du^gRLu2eH-H5uuik)3pp`J1*_+`uV"#55U*D(^6CXn5-L1RuGO3g_Jnc7="rHu4C`<(YHQr
[#d8fY]Aj9]AZ.,lD!k3K]A?/mJ$<1AbY;7\b^1$r1eehK4VhZ"#7#'1M@fN:c55\MmVK9dQ[J!
cF*1;\lY2us*lEr:nrQW<W<G3(G;u2>KD[J&p3>:!O`BZOeXV#5Gl/+;R;o/REV6q*?cj0G(
%'-cF<"UembU'Y5Wr8^RldCJ@UZT:->i?(9/KLBGh_aLd:?hCns`#?jsW]Ac+*>0cm)oFtH"c
?p"4U^<*!W6]A)]A!QLUPAVleRE*0l(%]AaB1BPk(KiaoAa@t'f8)G^.Xc\mXurr]AZ?FhB7F"r(
r1C$e#%.CjbHBQcURfdNU0db\$ZM!3nJ;qh=Ng*F63;Sd<@RAF)4VHSql))09=8(0*+V+&U+
a&PM`O<\!c\^Rh?0)8T'VuqP@N8a/OEI8mBK5W?!_`_ArDjGj!QQL%23>=.YRqn*dc))?'\"
&JnSk+i7mF-W]AE>m2kI^SaT"7Nb:c*6.e.EA\29gbQ,n^i+&hhu8^2ROd*cM[M&?D6C$1!>b
'H-ABAna"6R.ERqgj:f@TuB"]AF:@q'7g[:Hh1JW2/C[1@:H;XH\NFA?,g4[`?dt(,NgXNOOl
0Hc1b"CfIm_1T`q`gPI:3YO*UkseC5qcV7'P[f_V]A@dfLS]AoQ>N71:dPB+]A5lsJ?maB2d`nP
iul2*SWT`>q\OHP_EJfs1d2j;KaqY?10]AuM58$sH_gMn;`TSphI[Z?c4&5&J3F0"1A]AB`mm@
J2c7N=gJ#1JOu:F2Xq8<mW,]Aeai*ahQ<_cSZ8m[MFX!-Or'p^9':(0a0m;<f:n)hP0VT()DL
eAYo2AcodI^f$&sd=aW4N#l$1$%YG^L;.%$el41,@Rm9'RAPH,R`cqX)7]AR$f5@I@\^_R/$2
2BS$^`B#O]AT?1d*RgN!V%PSLpTM[^d(DI&/(A")mQBcV7s8@S/KDNaR.7!Un&pM8J,t2RGQ`
;q+"jhiLt2@kXJe"!0NF2!BKp<%E17tQp(Me_f@CI1/.b7UP%i:YZ_j,s@ah:Rc,7n::iOEK
d(Fqe4FQf:c*J,hACCQ9_p<p7;kDkqd7P0oB#JMu_"*a*/387b^.C"$H>oEd+bL?52P4'3RQ
E>=WYOXt"9jKVielb@(T$h\'3:hKDCWn?40mV&-&>V<Sj!)iRW$ghG.dB>5qg,85!S%=:-:d
QoAsJ]ANjh2(k1f]ANB8VE6&F4YgdSS<C93*gJ[8GUTJ"']ApKmT@4'o.%+<rVIPf<3'Qk,G:;*
a*LSiho,\T8f+2TK&bR*Y-*+:JK`k8oDedE4K2#7.p6g*0)\W=&%4kCYt]A]AG5>733CL0(jji
RJl8Bp@*-I(1pK1X?'#[Y"#-6%DM"&;ce6jR3j2/uF6HE:&>#KEE%>'PX#9>&"?/=^3\!I""
DguL'T6AG,B)7Xr=*?3%.;f@ibYU7C7bVi&/[072M%mktrj19-'R_!?0iV;426)u+X\+Cp:'
h5%eG[JfllqmkpdN0#Sk*.mA2Q;J!F3+r9T\gL5kn9Y5DHuX;c_1`Knu>Bd++?)rYY%UI%:$
+?@Se20'L:.mgPl]A:r-kjG8G2R"H14A=Tn2@@C\c&91-JC]An,kBf@iKARV_[W[-\@2"]A0U`5
NPB)#K`I?Y>_UpK6:tc`5C"(Nh6L=(hrdMV4shLN1MRuIUTjaJfV9mR[8Ef1]A.5Mg1%$;Z?I
gDr)W5gSNX-3pR=sDhul"+FpIAg[5Xr#Krke>o\V52aXbSnHjp'LNDF<R>ds):R#lMU,<Hf*
]A-smgfEVR`N(2B6E2'*Cjh#BU\QcUPP4M7`:#>+n9PL5a>-om!GD-HQ,C#inN9dH?O6@MpC]A
K`N#5>C97r@p72&:ti66d($>jo[-jM4bi>[u<l?>:U&0jWd#G9qq>MQ)M76>p<i&jN.2a>G=
%.-M#S0!UQ`3h6W-gk7ASLdIoDRN245F\Y77L(bQ1M>HGgl]AF#`SJca<ZuJRQVYh7p2_*`S`
j8*,(66F"7re&M,c8nKkARX*CUH3/*F&=&'&\789Bhto.DOKlFDObG`h3T<TG08e]A+<?Wf@T
lT+tHtmT5NJ4n1o+Y%5=k^P-`,:Pa%+i>l)N<%&^]AHNS#hb&E*urG)2;"%bMit/H(Sq@)f+<
plQ3p"7Xd-f1ujfh4u*2e%*`MOrc%aS"4U@J=*Yc$utsLI$_4?>7O`%lDoDAF`$eJ]Ak]A=!kJ
Ld.+-#oHpi%O5fmtZ^>+A^>n^DE'q)8&CVsSt7O<"Sc);o]AN[TXPO_O!1Q&o3cB;6,NiB;7U
]Ai"#A6OS=Y3"l(.]A7W%,"A$T`2)p^WdS<QEg]A\OdqfA#C&cJuBMh+OIS7q7_c7n4Wp"V=-rB
5hH4mtR=6AqqcPE7pdPjtp/W*!IQ:S2\.;GlK48%-aY<K5BD'T,ZQZPP=F&YcE-I+\UBC8RP
TY`7"PJFWVsNO4hY:n5hkH&]Ada0E^a#j[Bl/^SPMJ`TmQ;R,00iG1'1YX_KghL1aSLF9+B4h
BN4lB+mZrCe$65HJuof*]AND%d%=g'<c)[8jO1a:5c$Tu)EK:)RpKQH3V2!IKl_8Kd]ATTmbC\
;S:I8@!pKL<c2LKKe28,60)Z2r^g">'1iO:N"<oAq?='A0rcLlR]A<WG[e[<IFgQ/WTnrN?s@
Ah"W+-r:4MHbPrWGkLHY3)E?CPaTsUt;X;:\1/D(-.UDEH/*/Eg(S16h`o%:'OK5BMBs'Io`
KOW3P>.Q"&7g@+C-J"i^M7olp8'/[PM45_4kDQLIJ>P5kdU/R*JD15n[.'?q<"J-\L^'H#09
7Uf"TbHkcKj3@2NpB=),3pXY5$t\`t`?Y,Beu9r6jT`)0[^FX6R>_fa]Ag[nFPPiG%aSh8ch9
m&)g+GRDS`]AMdO\"I_=8J]AQ^'^HNfkA/lA&<u7J$;O=ek]AH8eD'>2t$jAjhATVd^0qQOd8$G
p>-atJVb:c(1*HLtc_5Ht'NF6o6LGCs9K3</97<&jtpM*,jLjOOR,r#AG6l.7:V,//nA]A>l_
6W[k03SnSk1!ISol9+CT7hVL1jC)OOmIQj2AFF>_;R`V."WV5d:jlF"s[r>McPSqOYX]A8<!^
UtNQp0?)6UF4]ApnDW%A]AFj?_3\C1Fl&P"F-7aB7_kY.-^X++'%`\'*+7(\nB-,i$[AnQVPLI
!a6N_X([$#H8Kb%Q(q[eAEl;g)<3fmH'Qg"1ICeO")YiAqrNl,Or?O<'8SEpR<p'"?B<\Tt$
rF%Lj`5:HpEB3@fYA)ih*T*9s[bGg%o,EHVHg;0,^"94s#3GXsJW;H"%=%UD<uC@JoRLJI"_
)n3X'mVBBC,pXdd[k>0aUYAl&N<m$eN8WbhUH2e$EQC:<j/?'gY!g0KPp:!Kc,2Q[SZPC5G<
GJ!)=:)Ku/P0)gN@/0eQ(0q2ZKUm2o/3$AOuYm@m]AUCA,K/W;-T8H3WX3KNj1AUTW\^j+C3I
7gE3lM,&d(L:N<Xih64;f.HEn(=_2Zlfm>iPF]AtZ6*fum0fDm/-97<0Nuj/ECFn&5Eh&j2In
U:\-]A,ua%16V/Dl2A_iKm.qbh9')VKLt$q>khcAA3<c;0Gsei3<2<nI6E[(/p)2.5"E9:&e4
ND/s'8,t&-=acbQL-[%04.'poC\%r*E8W9k_>\[BA%3leg*ZOFgh@A]Aqq4j<"XO.&iqUdW0X
sLB4tX]A46hq/,-1c2*cbK'NGs0u<eG(#]AH1YLuXPt8N8A0p&R]An:H!S>9bOipo3ceC,!jFD@
sCn2+"fIIo;qBUf(<a^$ilg$Ss?XC2b=ei:iEgY%i:O:q/lu(6JjSdRT34kbZ[H<1JIdl42N
[rIb6hisqV]AjG`)X7\g,*gXCQ`M4J,g!B@-W<BN[CPg[*qq&e7k7e13bYG#M\Z2rP,(-'Z/A
,,I]A6X>Df"J9,)^bC:(h'nhLFV?ebmi3rm[BZ8??ge1!6+=IKcY2T,q53JC6-E7o\+gAV*>a
MA;&6>Aeaua-n8[`.eotAB6FMbp1B-NX^'$=@m.YYm[It?ZBE!kF]A)":pcsIB#M+qPCOgWo-
FQlkW4cs'GNU0*1VaTI_DdW,jPfAU5ORa139a,$]Aak5]Abu%>BeP<:k(qW#Q%Y.t,+FH;22A3
I\b8IbqV/+Y@rWa/!$.R_##ft8<CtkAkNPAH8:,I<@Kb8Q'4Zt0^\-Qa5S/.CS@>tF)7"-qn
'?OIg'=dG@fcZ]AbgGVs@C\OcN#'&[h*%+k%&=t)9qK@"ob_3[]AdU>F27Ro_aQq]A;&$_/'..d
*!ksksW*m'#S%@tg'Wm)$>DfWr*D@>`g]An#kh.p)T!DjC&aM>J?jGa$uTRrqQ[U*O@c%Jo0l
5j$=1c:ejue.PF$"jIXtf$WPKN#H;A>Am#J0>k5\fIst$DZ;n#:8!Oq*.::-Xb!j2s%IG#3\
;-pjW5G\Wl\IWZAJ2ga>a7.=5$$3.RE<k5P[X\[JZHk8cQ^9OrLL`r?90H0"hfY(uKWu/*Z?
:q\lV`H]AI!XmS7b,s*PrOcJ:A]AEN*BIYK7t+Xoc_K6%eQ1(9"m5hAGQ&hK2muXkEL)ll#qqX
dY(VKpRe6B5U\$btgd:It[LifbiTu43oF:A@Ou^.#kFlmp$=eH1"^Qi7E-@#:0iIq+_@aW1?
4K6rbn3B2np1X'J$Y<*\+LTK?I1"+CpECk\p7krb5Vef+Nu4ma\tHj>,+4`l$tP[g4RJG[cA
PEFH<]AKI3G(`?36N58"EM=r:JD&edXoV,8`'Or_ca,^s-)F]A<:A4iqH5d]Aneri,8'8's%<#T
HT`Bt?u@QLHnVG_fVr&N0c@SjQ?-&!?E0*R&@i@fQ_Io.*3U=]AW,`grkUeLd!I=b13k3+jFR
Aql(=R6ksShIXVB9P8q\O\&'riZ!Q3Y<H?j,l1H>Ci"ON/eBO1jq^YH\"O[21%8s&$3?o9gn
m<A1*8C1/UlC.@BpZrH;+)PC(OFtGdkVsqI6T?FTVa#B8T(H4]A"h&]AIicn?`^l.p(S*`JFLo
6A$i5J2_p*YdY!^V@ShBcp3L?6SloD#j329r(i34GSUtMS/@j".8J([R-m-$1^2AkY5G6hED
=+>AcW+),j^PWiVdN.D!i#-*\,1KVsI+XhFLgaA7%IX^H2a%I2]A\F7f@&$.?9QFIh:sjkfa.
i\PDMGDSDDKL%NnDU1j3`'KbOmr!mBEM+[O6-uSBJB$50\MV58-,r]AP\,RU,fXZ3On?^Q^K6
_E%8<"9]A'X<A@"^$4tUJ*d6<lfq"We,OCG%H6.F7M=Y;d/#e!;E!#B??baJi[Na<=<O8f:5m
SJ;sPpNA<HODB-JfIQlQ8$$.T=gPWOlW5.O-e%RmAKf]AG4'Ifd\4MG>VkC$bZgcAZEQL:b5I
pGE0I0AWF6sOh[\+Y;5q\^m:2rHm!'1tJLr?uVShMYb4QU3oUFfN*L_[uH#2KgjFD]A[r/HJ#
?OK3J9FdXDLLP(93O,W+gu'%fKJdM[?VH_kW:Xk.[E"QhAl=Y`rG8u9TP.QXIa3'EDtAmSF_
eB3BZ0[=*IE6:NuL_VA`J;:m>ZcPJ/1!B8u4"E!]At'r>oXgOXBKfE4)eJY/D/@RX5reP$RX'
Y?t"2#o\rK[Fo;KqN'dIaYPHokf(IYQ.ZTs;4KCYGbRn6o&rf3tCH#GbQuT`7p)_+B(+4NEd
Jbb:*m!G;O@@GeSRfDESU-g%fV\;n#i]Ac/$JK5,,`sKJ?`b\M:b#s>3B_UA;=/P-`(8L`ILS
Kh(sa`"NB/k9q-KB?ZjJ0JUgg$Ca%FuMfttjdH'q#P+k1k4(97KJHS(@%NP.GnK2X9t3J88M
Q?>!d%5p5m1ck0I^$?cao1A&ON\Q+_Z<Kga'53?RNnj>"`DP1[H\sFi=%+K3O6ctGo00#jkm
(Z:1&kNLO:V0ff^Zkc)F`8Jlnd2Xf0.Z^ale1o``8B<d=h8GWm`NZL:4KNC`RsR$8nquJ1[;
70097hB]All`jG1*nUV#0&SrPH=7+k&aE`GkJ^3#AAP/Qjp+tZ#WF@W:\+-+-]A6k9cP:\0Gg9
aD%6>bM6K%TSJp?P=f2#L"de,.^7Z.7]A#6\<L64lZ8T=WL?knW+0A3R`b4:b4%0CJ(?:"hgS
U&G_s&cj_rC7rbT7E?3tQA_,AGfD2OI=SL+=K.oSe]A`qX691U[V"p!N/5i;D30M>CNuphiBt
r4B^s=0Q%"CM))#mAjsd+^`]Asq^n%C4[7utgeNk^\)/nh7s+;)1:%,@p,dKBbIHWX@ASUa1i
]AF+8WL^jIbSl)3TXpPZ;`drLX0,SA7&%0QClV)eSt#mMd@pog'F8HZgNr\1^i44WA1'XWClo
sE+A3'.b\qrln_reYYs`,WN[qaA]A4(?K-qS(bD4]As?*e^KVm@hqnbe<C23N>@_^0*O:LmC$n
Ud[,nU*cR;B&Vqk7TKXr00d6M'_F,N8^0"io.5tE/`cTIE!>ElT\JP5j"t$Ij)kGn:DT#MHk
&22'YAMfNTq..k%L.>tN!/%p%AJK%`CLD!uR@TE_0]A^HB\21B,oQ&hKShir';3$\6/WD&ll\
!@<U!E&^0>2QZ7B=LZpfKcXZr?M=.cG-k:0e)>_5^C#$AIfcb@=#SgG2!c/f*nS6q+tg9o,9
,*AZo)Zpk3moWl#KA;Fpct$qG*>:>im@^:eXmCD_-p$b/AQ'%82'".Ob3,Hd(na=*9.f27Ro
<Rn'%ckY%49>1!f8;/%4VFlVODg'_;7/0e$#(UXL<n(5"RqAa"Z#($'n?0+]AS/T#LkEGp#[L
@H<4:A,@3\)Gu=fg%"t*l=J)m:ie4T.+''U-Ua'+JWpPIa545Zr4ZM[#u<@\CtUSAptiRBU(
A0Z7W=J!;e_$m5e[_^>NHK/eTMr;Mmp5?Z[7$]A+cOMIJ*o)8b*sX"4Yb(O(!==Z9P(^0`]AUZ
9dl&8:to$K/4k;lE+Gg-B(W-3r<D<:$3#OA'OXhQC7T3^hqc'YrIs\kN&31tTj"(PjAJX2hN
<$hoD7_bMLRD.]AFBNbfId^1dnC@\H=8SU,D1QBi4hB0RbRE%=j.r:pN'32n$(m!Sa;Qd@U\7
_'St,T>goC!gi%Sd^Wc483mhmT>&Z>=i0W$lPHo=lLM1/]AouVg1qp@!t*T2P&d-jiZI!W)Uc
:VX'*oX*;ar:0;.E<MNk7i.nInM.t$(-*\NnLp)8n3;4mi"G1.\8Y$-^,DENj!_F1"6/mEem
jGp_nD8=scVTL1c,)8*d=aT)e#Is%&V@WH')8I*ZLqXlQ`n:[$YWdI,]A#E;;3h"e4m8%++S:
;AfT6AD&%L,k7CR@aT5m+MC)tlX_j9OYK9[PYIi&]AIZ91.2"mkV1Qp\(GJj]A@_CVpP:CfH.P
]A9T2r)]A=7TY)D6$FpCbCkF,L6fVfhdQ=%r;Tedq1#aN?PUmgae.\W]A`6Rt\hb2E<>TiVCX5!
H`Pn)F7_5/e!%kdh/dH5tp[GBX#.\5A5`M61$J3?l7I9UoK+-L=89)c%l?t-/FP(5;j1e>3*
;Lk:]AO\QnJ:W"/(^\W/2?DJkq<)>F+o&Ne%:Mp$5PLY'Q/rHlPcoi4PnO7P7mZd6XQ=A%A=`
s[rdD*1i?ZX4/t]APh_E4a:QXN00.hD1ATNi?W7<sMsH%UV&i+mO957X&=#bR2>p;7,1co7U-
?,;s(87QO7i^%*k8duq^Hc+q&9ODDbP8ZnBNeaK1/*T9`/snYQrgglmm]AIE-9^rp33H-;MNR
RL#rplt8"W)+3qf5]AFRJaUl06s>;_ZpSca%mE#of?=RojS:[#D@QLZgBfPjl+Y#LaDtBi^0[
t4Tnn*&YeGe6pfql8M_IIWc+YQqu4l,>Ufg"7UD`*GF>_&D(lCg,Fp<jkfqo2l`fa+o',DOe
K$N/7@5T&r[_'[9JrO>c2oK[C:GtQqZLf5kQ2RkAC^+RX%qQYHHW.f@u]A*h_VN,Bcsu@PW=9
H/`%>3)+YBCKNfsP!#11)mVr!dqQQl?UTOh+[J&oLYqZ2fCf'REG+MH7Cm-C3bCV]AbB-K"+l
;k_q'Toh:QWCst<Buj`qK7rp8$sb7D!=#_AdqR[4PlV#U6pRi%F#T)de;&&;#<ae=:E7agLh
@/S72*usD^0PVhF\;C\Vg8V[A/Kb%:h7!QnA2G3G8#)ZX&m*&-6+5cJgCCO7snsJK>!YjVb?
4^7?s#<uI2>:7DGYj)8ii?2.SreecO)(fkZB8*jAG$b+rS&l2'=m">?rd\KClf(/OuiEU!=3
j8H=F#:A2Kum^Q[OS@A>Wn5uRAVo1@)bKk.S<$7?/ZNQ%5Dh,'#Rtcf>h*JLl!ZiI6/9OOaj
'JS(qFP#0@T!%DjjuR0IN,eJ2UiFFcWDjbn>4*f_+#UJj1>e]A02:ZF@[e4+a;/1#bkG)WbJ1
[h(TcT?0H<\op/'Z4gAt*n9]A!B]Au$34Ps7,pk^faP\_X8@4)Vj$GJ']AN73fW*&<dJojAZhT9
6:J;Yc@)`0/oP-kDsOIpf/,mItJpr88hB%(GH7H9s_IB"lFAk1aDe`<M%Y5P;0;lba>NC'K$
E^#sHc&KI^<oKpVa1-I2sFfaH4+gAUbN:TOc1?1@ui\gdF3q^pkD?nel,-qtj#T,gjs3tqZL
b'.D$_D$h0`(_!IOm<!;%+R"G%)@6dON`kl]ArlZ[f1#Ef:d$tF:6]A<-2H1/8rOiQ`lR*$1NG
l)^o4WVq1i.-AdH`Z[Ct]A\2ubm6'WMNNMs'pjB/0Y`&L%H`Rgk@4(%ZDG^dK\M5:B5=(*e/d
-n/<P#4W-=U+0WF3Nk9$U$-'j,5h832_j&Q:k1iYNO.`4inMGtA3QG'iIQh^cFsH;^,f@B6X
*/!fEQtZ=al%qT9%N`AGYe,kQk;tRZS"@49%nn-)h1?D:srk3q/eflr?ZEf<K;=<M3[Ybe#_
N8ZfsG2@4G^I?]A!@/6mp7RSW!%d'?+iE*,G9!+>"oMoKBJEOJ-ZOdA`(/57ePD4RWjo:NLWc
.^l;euAI1`7t@G;GjK_ZSBcB4sg01-;.4)a%7LFqB1uW/YQW\%!SgK`>#G82l^LU=a41CO7h
H-qo.VVDX[-]AiTCH&DgkF^<Mo85g]AKS_=5Ub$mWekTIT\mAR[7gj@Y?[:h#RtQQ/*QD_tBhi
<EMAIL>@07VK!ZU3nClPi='o\g.d&0G+;\3\"M>D!Kha1rSX<.%b"g_,(c4ZcS/$,I6a=Xm
41U8`Pt6l7R;.!/+U.j(">n,"/#"X]AoAX2bra'Ua>X-aOUl6O%*k3&kANg9Nr\diG!&:j`UQ
$\*QG+7a.^nk%j!'=]A1YH`Wq^XR0O9c;_MU,US,7rJcB6ROlp":HQKHh2u0g6Kc#Ah/`*$bB
md5S_s=<"nTnh]A?)OY]A_U,4m"Kc?h_2X)Z!6DQJ/O&cX@"p*nCpr;#^36+"Am`6aWhOXjFNn
P5RO<,CWtt='Gc?.*^Pd>F?Xe-dY&2AMXO^H8boqnqSc*n8#"d,6#&)8V-DK,KeN5$N5*ZG>
@'Y9m131Q%[Z(a%*:4=/72oAa_pLjGJ(pgZCTGDs?l0HdI,bclR6D5Fjb'93U_#P&HO0/H;T
en'l@OLGTkT.Q#c"O>@M>(<k*0B-?5-["f?G_K5"7"fJr%(_Bs,L+JQB<4=oGe7<uhC5SG&u
hb-=fk^q5u+$u^M"BmsaK/d*bQC.(AhW62s/M[)B!BT<=Erde((ra<[^H9\D$r?ajNca4g+E
8eNfN`ID!ZaY&Udf(bWXk0gd7',XdMfPV5"r]ADF.aj.LIRG8O@]A@b;3Yr3s8-%``aY7\?YVK
,9IP9nZS,3bK-)>GTc&ZtGipuboI5;dsiX>T.5=K&%93i4JE:[W?q-#KcP!'DJK[&[D[rM3b
F*]Au77;O,!5W4&Tos\`$"(<Hn^d4pKPG,+!FA%8"0#)\?/$6aa:6#cHHDZ$aC1?%52NOulq$
W08,0Di/%;Qm("Df`G<MX&dFY2F#hW2Dp')MAS2CsnrL/Z+geb^):Msc-T3-u&aqLgA=NJ<H
[]As]A7bOgdJ'#eNbBRAdbmEqj0WB";p;_rDOls/K[)psK<KE.T)`8ZE"J_kHZ^[L_%]A>e>Y2$
am\NqpgR"Htpi?MSuQ9,'T]Aa.(0jV+8/j#:[qLV'BX8@2]A?i)o`g('ZiWB-!]AtY,XZ$hY^'W
"AQHq.0J=(\#T1F\Xn]ALnt,/fb/;!r9E0CsA*p2J_#qLPQ$5f<1@1&N&h#U\A9JqD--OOTk]A
bTi-074ol*)O5Nl,%DBomUVUD76_<D*DX*8pYO7Xeb-16j1Okbr$rP[-+V00_CF`pPnTN$eg
dY]AdQa1Efn_"g&L>13`/RGsZm=OGj44Du1,=,50/AccmUaWO&lLl#c#5Xco:qTnPpNWDdn3;
$FA!-AS`=.;[O`]AtBdttO4P1*gJmim+:Y.Zqj"gcn(Y7h0IX.MKn_qoFE"_cqHF98sW1>`lc
lTG7C#oIGiD(kl?B6f30n4(N[5:p#kAikOc`r`e&eU6oRXZORWcWNkbTfhM^Xf[kFuDV:'h^
6@%#]AXW8[B=#Z[2(.0?l.[L?5NL0F9\Rp;e*3';Q</LJUk$<[nSpj)HqX\Y6TMhIsbJL,dm%
Z+"9D)7=PX5Fu5^%JIKfD2I'lY&eFV1R,_Zq=[?Ll>\G[^Gnq)S`plR,<L"Im@-idIO50IN7
stOgE;JumYM&.\hJ$lnN.73+UHdQAB/%6)//?r"jW-Oia1AFCN)f*ONMa#",bIdrS"RVR>K,
sM$l0?,1-3G*M^6GMFWo\AEZ;uQ1eS8eg.+jmek+]AEjJOH2+87;7J(O-6[5*r8SO?np\`"S$
\9aQk_1o`GoZ:f!kd:V'.I;CcOi%a^8aBHZbr=%"GeQS(*lS<Dg);KGOhK%]A5rhB>^S5o*O:
X4p>5!Hd53&,9`#GXme2F+C8c^J=)`p=EJ4g3$9oame6UjKVIoT_.>c\^:Vo<(CMJ3.6.si;
)p5G[CNFHR))H"bA*#jTlFj[j$_R>u<,#?1"*atc`$?/%c'fc7[egeKNg[,hc#re5b-oKcQ=
!Zn_KnTJB6+`%)4k+%q1Urj@NN]A&H`I]A5GSRAg3!!1AcXYA\"5tZrei;hAdY%dc3ma__/&TN
?QXHbQ=6Lk>2blB_L)oAVaN3%:Op5auUq*gic/N\`8.qV:aUbNmS5M_CFncO@B2Dq04JUSt/
%l6sJ9qf0U]A#/BXUB_<cqDY_^7QPW3a[jf<nBoqaPDbZM=_"HB@Z)R@uX'rA(`^+;o_F%/BR
hm]A.F<5#o]A`shlLun-\/TuVag%f2)qd)U]A;bbYVi=4ZLSSKHd"<bU!E_I8;?feK]AO=sY=\!q
GHhq;/9ff-YO&V-g"l*n7_=&<</.DegR5Km?QD_1fWroQQ]A@@/Oi?8ge@P.Hf54j+NN+O?`3
LYE4rtA@OiZhZM1cp/lm[a>ZA]AS-LpI!k7[6jo+spP"9ab3sC,6s5.iYRLVF%1[T6o:HR19A
;Tqa6L7@s]AWDg2R*5AeAGCJ/.ZMJC=(Wou%CJQ([F:(9Bo'oV7`D0>UNMJ=/1/S=)n*VZ1Tj
KT]A&<6mhnj]AToP+"(&rQjLq+3)hcpUh=Z9Pd\Q4%HP`+n&HF^"XKq!671;PV:6#&&S_S>pkT
Z<jk/i9=iLJmJ.Yp%3>s]A^:"u4L^rVe[$4Ee^7cgLgYi!@eJs.WC.FG<1HIgc4#Bo)p,MM%F
Zb88$/7U"6X9gVc(@Xhb_R7.A]AL;r+2aA@hYq!b\Vn,3==W0gmp%[JYC0\Co^LLtR_rM8poL
.<QE6LX.`qqp5O-eT4#]AnN(p"3.X&mh*NW<B6[,,iU4</@,s4lFMSbG\qp;W'Co"a#nfBHpf
.8StKDq5d8Q`o74bIJpUZ#j>C<IK.1LC6lp[BHQOMd@^;2<\!@@^.^[k7T]AEA<3BI?`7IIg8
_a!Qj5U[Q"gM,60K[eHS`e3X5=GDIV/8&5=fAq7G;NutXde'<25[N0/O0Usjj/[E`B9C1.?C
7'%=Dj7>tbYZSnq#!Se"?0i7tkkje#--6XTM^?Tf\OJP'S[!bh.i(#tQjmlYhN!3s<B0Y'qK
^_P:GJ^*"J/VCGt(#>nPR<FopSJ[RTY)D>sq%;(TZho;`fh)Q,reLj.0&=pRgo/!<g\E%Tp.
N=kSXG0BDECA8Ci^W+Y@%$u@QI4s8UG<Z.GbqY1G2T_`3Bo&EW_OYQ03HN8<-&ONX8LZ22X]A
G'T:W0?h<^5'^:!L,\=DW%F0rU"5]A'><$rmAc9X:s_T`jk[Ke2X?G$&"5FpS9d-E9Qm,G!HB
R2"RFRYpeNBe?Kj;,"[1(m2E\>?-Dm5qo"8VW$^Xm#\Uh1+\!4L_S3YHtJ9JQXD11tP"^6JJ
ZaUejgnd@m=LZp[3oGr3A8AkW4-.4W)$)@C`.5is?D1T#3mT^*1SS_D%j@\L'fq$+YkRN"+Y
$je/)BWk4jf:C&f)4IEb&D_@mQhWg>Id>b3W7<pfhX@VKB)L%JH@%W5R[k?Vk#i"K-R6+FRW
Y5$>?VR,WIf<LSlSpc<?udXL&8L@]Aj":>I_23j=ftos&R*-(21?p>.=2rAhJfpi]A6UjNlc;t
/6/fe"%^10%HpOadQE$h0>bZYUB4i%[.TmdJ)0XN4"2Ad%e\T86pg9/u!qIR6F0!8.5%kn5:
D2NnU/ln?\KR_X7c4d@A`'0jVYsliVo7a'mJk@$4sA6gq<me%.07V7Q@e7%:o,#ZI`IOiN0G
c4"CB@l;;%nVJp`0)#['l0b)l#]A</Ud_mtn&5Bl/-I595TM<5)XJ:XrE9?n0SL\;E1ZjNMX\
>8=3i&!M(\ZGQ"sjF+^^BWdrhq=4HPge7N-L[&nUBHE.J9Ckf<#\gSc>R2d""JM:%11l8%*s
"HL9R`qsaDf<n&U$Z!^*AnQdW<gkT.2J*WS[6io1Q,n;-hkb!moT>G0O&DphtXXGM(OXK7#p
0o)NaM[4BATZ11L4bK9%8L=TFK9I]A7H8KU#L`^a"iGXgf,rp``V4'7jX'SXkR.(4@u.)&U-N
.uSG^XGHBFAE)%G+LB6a3lE)R-h[$?tTO`-6Zki(,A^;,pkJ$TQO$NbM4"&YO\^S!Ztl<G`+
9\gh:/(3BEf>4W1"23EF<Re2&NJ,(1Yuq?)5]A`H?R-V`>%Q!O(78I!\[4K12L>JllbmPKinE
2a8="rG^pbJ[>\7Gs-WlSUL<U]Aq<Ci+JP+^VJ"RE+`/8j+KLf!YK/*H$T*'2\0%9,[[o[BIR
&&Mi@1D+)f)8%LcZdulCW=s5-'Uo.?Dp'F?C%?X>`QETQ:pZ2^Y8Ne0pj)E,$$$((V$RpL-[
qd1g<nZ;[&#:B6J%O+7,/FS5#rWl1C?^eQ+\q+bCnYJ%]A)BcbZ3fk9j2@aLNhoU=&C+@$c]A!
RD^!#ba*!2GRrJP'2gYmp]A&JZW&s"s3K.3DMFW<02(j`@M41Ajp"0bk,qbjh<3&iJanl>,sf
%l_r9e4<N/+\FC#)I19[G*#a7(Yog7?@9a;fR.[b6ZfQVH<(WZp9qohS3&TlgIX>[AX6>DX,
\`3")NT>fQ!=G,^Qu@fR!2WZf9]Amo4HG^/0;Z%)=Q(-o^*%5G</o!T@MIj_>=9q.')SG<%V*
jTnJ^"XC\JN7$RLtj,'6s.oZm/._Sc=#1`[4PB(lFKPfP*V6`oN:gd+HAC\9p)r7*jTh=lgE
t[V"EH10EYh9RSmG2bakn//_Afn^KgLY@N.(_3X%Ud>&'qR#e-+r`oGh5Mbb'p7@%tBCc_Ip
mG*)QJ:!V5*.H`d$Q3-G%<X4^<8.BY!&-V0@Ugls1M<9[+/X?^/^_j;ie!%_8/dP8,0,'8+Z
5c$<]AOi:;Z*bGQ]AbYA*Z<.a9g*n;VJ"rnTk:8]A<9+dgD;75/j3^HclP*J</cW:%=!MDCBL%#
BGqE2m-q'OqMX&b=-hEAd>)(n5"hgAgEr>BpBI3Z"P-RT&>]ALh/cW[ig]A_(9,<F0532tpI1l
bfdI$n(Je$g1?SUYtMF\#tf<?$:/=eNnFK9_tb`&'e\<4g''c4]A`XCu&NJXp`ZSPEZ69lNE^
K5?C6J7RIE6bMVn5M0Y.jnba_/1*U._-5uQ1V[k'JXY<_$:!<0hDJh;ToM$e=)+oYIT#)PF@
CW<Q2I%n-_Hrm6r%a6['cpH)DYmFn?&]A+M.*@F2LF'VOoiXt*L!Nlp+(&(poEc\<0B]AH:.$g
URSFgM%a#KB`.l8A+`8_N`ko$dX%-EO@ChQ!@qUJjtjg+>:>$3r8+jhS:*gC-+S2T'Q]A$2V%
\F3a13+6fkjU@ZBa&c-=@KcA3Ss$m&bI_"/qsh;nCHgH.M!7RTr23Ht<%RJHSl7pWRRk=aaQ
gj'p@8cQDLT"e2d$aGe.4JCkf5>6:cT]Aoq:bMRUAh'<UZG+B%o7;sCa^r%Yq'76%0Z?A5lgC
(-^i&uZaS]Aa=3/_in#"Ei0DS5G]A,Il9k6@tL`Yp/poDjra*gFs[(J8acCXn@O7l9hSC0r:r7
?05pO_7%IEh8XSo;^6'jm:/%pp*[+ls#l#PBj1a\(7R68I37B;>,W$&#3pi/P=>.*rSW!Zm4
ZoXcM*s!`Yp[:\(<;nr>LII3GA,"f+SWb$`o<V.ZlXK(:gSmt_MYPP?R5<d<?i7qHOap#spA
1o%pmFe#T9n_LRJiI0IgY3g8WT!*k-n/-HSNf2C67DM>rNHW93h+_MEc3T+aV`!.38#&:#%P
:CQbI@71B%MZcjZ\9rje5>bFk!;XePq97B$@qY%V>887"s9[#R,\L6nLK;U@U71!`9M_o&)/
*3::]AZ#kj<oh&<@TG.PVO4'h-,*`oc-f9"JaW`J#0-,*UH5BWDVJ(sE/l#B[t';FoB]A]AWeFN
Tidnh:.$H;QXU%O]Au2P?`r2jR+uLDW1,cs4L>,CF=h#eGdWnc&gMt4VGR5oO5b1s(>Ac)MmI
?O'h5LPSfu^_-fDUKH['eR`)%\KBUujMi>/Pf4_dk^MK?eW(9Pso=,(h2j??D!$[l8iUS;$c
"o2SV(Nb]Ah.fF)j$.4-i?r-2`M9;G\Bi7,emQ^r3k*fsE_D7^(D,\TJ7nQXP**F8J$R970'*
*m2Vh2E5/#c"_*9SLWb'm:O"8b[4XAP&qnD[\bC(0<4l?;'#n$H'jJN(@&/<5pcQk#/$]AD\U
m2"f.(96&.-A0!NBZu/.4^o$'MWPj%s"d(&0eos^XfJ`'S@3Z&Vm<"_%5Rg0!'hKmWSA,&Ec
oqG;E&kN.`/]A)q8\L#:5<NTuN%>Y)ou,1h?:6/8,_/5i\nsL;*IDoY[9#u^*#?X+E?^#>q*s
,K4k+_N+)0luZ.=;G_ZY7r:I&aJ7P6X)9`iUDR=mmET&5l[mhf)&*kh;0Q$#m[aFok6OMjM*
)J7D\!<'U<&D0K14AM&t[ds(sliR;Ghb]A<%\WEB)l`M*[)8DbM1GeRoV"RUA;dH4CLFh<>St
8HW7&'D2[!VEN7`_^EI@>lnVQg+S@^99t1:/JJmbZt.Q%<_Zm<e:$pt=Bpa.gRCnI_C#+7:t
0QCWY/\VD)b_^8Ci&AtG"KN'IO"7R4n['pl)j:R4liH]A[C4g$M-QCtPqSphEeJmfIeXV;n*K
uJ*fXu1#DT<]A%WA8:]A;<FqHV2"C2m'&YMZEVIlU/dm+*8>ZnRT8)F3O@WQ*)>^)9Og-ka/uI
%7aCFK0SIrBDV!;e4I*koO1@,9(5WF;\%:&a?Vd[7aLORFH2f0l%a8"67!)]At)?",(C/]A>SO
LAFD=^@?Qfp?W;-2T&Vb<53Aa#f]AJ*^T,Zm=9'pG^6<ercEWS\P!LQ*k)l%oB$@Grfgp0['/
)(aF,c0N<&r$6]AjhR4mrVBWj0up1/X[TXQ+2YPjVLi/QUI8=OK,"AZ2P$><Z'4*54cQ8V`Tc
\l*=;63nB9`5)\-OWhqFD5@1DQK:_j<:"c3C,V`B[(hm<>^DTK,;jW"F2Aq+J9=q,-m^TUp5
%_9J"%Z01`GpW%?e^$FrNQ8gYs2oC&c7SZ*htRdHlDAM@]AeB$FXBa"CbqPJ$tZZ#8#l/0h\2
2h>JihMK14Z)=7W8DXAr4p>a#UKMc#?'&fB5J:8_]A*7oo>?B(>#Y_'f5b&,D1W95c_44d0'L
oYY/eB'dRE]AePK[<FW=!?.VSe&/J!/EE`l8/2G#ep!c,s=)sMUR/ZYF#Jm[bO]A]A_SIBeSNIc
o/Qe@K0@)<CBL4(o?pU\o3>6TpV9oSgD:&Uh[qiR9'a"Hpp6SMRdAbosUr9:Hei48Z;L$W14
*k,c\NrZ:^moCAa?Gl-`jP/f7P2pR#CkooDambVBY^p)/^B(.IN4af`@'u8^:]Al?\Z2i*Rt&
"EHS2@apa)/`d<(]A7T&'"\*teaG)EP:Q<Wm4#"kZTccMj$+4p;?S2InR`D?b>L3Z^$d1F/Yh
YOr%t&!RfKp(HgL,#a(%i)T(:f?@[OW/:M1(hq/oX<PFYcAi)(?KB`@!l1QB^:"T9&^MF=-9
Jq)HZk(K+Zr5/9i'BpTOK#S]ALaNb8gJrY0r8-WNdh4#pQkn]AonF5!7?huMO*;;FRT\h6s+Cj
ba/e>t7@_<V8ne3X7D%=b2HoQFF(8jIY0a#P#WUYCH^$_CKub/fB!,hOT6XKV284Z@,M_L;0
:W.+t:JD,"kj%;S@]AhH]A%rI,N.QiBii\0o9GEaHVE'Bb#/=>*Nu"GHjk0i6T%YhkUklm$h+1
U-^uC.Q8nPH6dq1aj^=.dj@Z9/O=:GAP%a!TLL%q=3]Ar?A'hgp@]AlBm0pCCf<nFqA%5ID98k
0/::pKHJK,.^3X[K-lf@s?H_h.tr(B6WonF&d6;91*SkajIG;9K2AeaJ,MTY#FRQL9#qE%8G
ef@2u&;6<"m8KGtcYo+sd_o)qUa7k,ShW=\R2>bj48nN04;#VX@C]ACtDI"dU?E$rM5<i0*.;
>U<7`&FMrd",IA60#k<]A7G;_fqP/"4n23E@crEWJDs<a/0[b(+/]A'5(D)oFY0,5!rTC<f;b*
aG,5.<(g^4gp^7gr/RpV9]AWRQ28:.um(CZ933t?_cKfp4EO-o]A$Xh_/\93Q8IOUl3g2TGHj(
99OPjH2U\0U=rq9da?=9T<:jDC):$qU0VfB&=YW4)'fGBeL@A8etcC5t,Mrak_oj%ocumLph
q%K5%jqW2Ta:0DJ1e:DgWsPk$52YQ"G!!7Vm;GCr=r$JCL?Fjdl!-rJjDI2)=e4JAG;AFklU
8,K([dJdg/Zp37dm>psj]Aq(>:JaP%kI(gmQcJM3CX/'^n^^?!#\<-gkhXB=mgIgL?DtffGr@
n4)r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4>$bBRO$k4n\`nPNFSiqq2
3']A5MX/!$bfrp*0TnTs(b;uLOo[fQn$N'E:$s"ko7P4Pg$@';[1>PVfZdeQ;]AYm\&?#6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="514"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="124" width="375" height="514"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="638"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="674"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('REPORT4').style.top='-10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m94@/;X(c9G1/;8Aoc>5a-dX4fhU;Em)p*^ct9Sj*4<nQ9SLV[c'/nS7?Jpp%kBNXPf_8"SQ
%'e.c^iGbD\ni2CdW$OI;aS,2eVPJHO-?55UilkPr(FIW/TJlX&n&htMDgkC0"H>q>)?OPcs
chY_!o$]A@fAq"l,AYZM8gr]A^>&E!<TEG0[tAp"0l6Dbm0o]Ab:tRU+g5IMR2'+;h?MGE=\+ao
R)+ok&3I-BS4&^Z`b$!fQN%`G^eaA_Y:K?XhNQH4l"W@RX91$6\&)o##]A#J.t,]AI)`E#Nq?,
@8[^U;&Im:NThY%cj;Tt,_"L)0f;FYW%[rtV.4q9)r%6u52l7t1X]AgAV6:f"SJN&AXMja1dO
mQUT&_#1u%]A>`sX<hTB0TYK5&B\RVcYNUm2D[EtH"*irEU":6/-Vi*@S3/C(og8iXW-lVF^+
WDD*Vh&bapq%K4'!)u:YofiFST-[J+gG%]A)dnPNIcF>_IXGB%2HAAf/]ADt/C5Es`[c2`4]AJ6
:[%8B<!5<K'k$t1Zj/D6VLU,+\(>[&AkW[*PRUR1(cqIbm-[Qkt8;V(g;O+g@BHenA8&:=%P
?7E+0A+HTf/\,Pk*moLdO+Tt/kMfLUW;d3;)<45-l04tGXo:7KSh6ma,5Q!L#D#h,Z.V%6dT
759&7X;]A3OQe2.1\6qQ)Ehf?[jboY3(D&:;Y/V7pc5qX.YZq"*h+"1"?l;1^]A"^>6>?lg;X?
WMRRNQ)]AHaK.jq(MBA"b(`p#Nq+!LoC%^bo!]AsBH*HgnL]A]Asssd^r@VHusIP^)I.n4&G&gMA
M#DC\qAiWu%FTER%H\daZH_kPIo&mXUk1D\]Ae0=K*JoKl]AfR!J+o:VF1+G<CTSB:XN@PdaHS
)SbNro^e*)Ps0^p5W5#hIA:GcA/e/q'KlCNa!Z3O><;jaTcd(T2A1.a+RAoC9ODDXWEX.rY\
;[#o!fPZV+.)Shkn-</OC[h".@Auq12RsPR.4:jX-J6oNLa520%Oa.&r0U8^<1T)<05$$<Pk
A7R[eSYTOlDs23'ZDH@D*96mR$F6"Z4o/eO6)M"I&Xl;WGj$TgQbo43#]A&K7!bjq(J#"_%fN
EX!O"XOSMahf*Y'6!gN(N-(>P^%5dPlZ)?-]ACS<)a7Zm$T1a>N3`gA:`T'Z)ooZB!JE`mj5g
%\'<D'<ql%oH]A@Ye3DC7!=q*nTbYQ.WIV_u@GR]Ap@HV(,@XmGcVA"O<mNlCXbg`i&0/8b%[)
blP00k,cI"!2V]AH@;s#4_MdtY5DYPEPC?i)>BSG+#C"iHS]AJW2XqR-S5:hhE>Np>kr->/RI'
eP:%V?A**e%%n!;_FA?Sof(69-u?.%?KFZA(0jnijVa=4XmSf&l?6542p&nMQuQbCOBGcRjS
qS`PPf[0O<\RV4aDjVI^C#m=1eYoE)hfe!J^oq/JH1ke0ajFi$SX`7a+9hNli]A^cI*Z%kD#L
#FJjXMK_QkiI);mPrngo\DD)<`j(\q,>u#M*Si[8*$E4Qpr^bj?67>Th9+O29YW0'p`R7?`V
.]A#PYD^FrJ7Z`#&e?.ktKpYYHd1N(nKYNK1kHRF44:$F8*qMmT(b/]ARX=&Qsjt!ndDO),:q/
4O\XU=@`_rD@Q/(hIHJC&(UR]AQ0E0'S-quW9:XPKf(n>%_/G9dSYa3fDMo'F%4I)!($&gSd6
7hlJFFJap.`RE_ZY8;SA,)c"(?,MA?ounoLVXcs(/DJ-Fi"Qt2Z#gFQ1@4Z.)9FtpQ5^hfa\
\b5\;\BAS4DRFSa>JDWRHYnRrG3C>L>tp'`E[euY,/q0*h37dip>Z>6&bGEt#3f"THeA\Z-<
.r&j9ggouCm<n_3MfaIc*ICT]A'OD^k$jF;@>seje<M]A38Z(\-3e/:6YCVDg$`]ADi4_O?BP[H
H`"ps6g8>jViu$;nD*=!p<d%Y'_^AeSMN\1S/SWH[=Sg,ah%LTWdbn3ERp/'foIJ*s:UU;Me
3+dridV"(4a$+31g$\(4H(.NOfqa<"U_8ok^3c101J9#$8^\ZE3j=]ANeq)2&9d!$t.YWY_N#
*h1<<:Q`"S5-"\W2T[7]AYHJ)6A=?B?C_0\q:@4o=Ns:]AP1PQ.4Y;Lrh%JpU[.bt&kFW[4kh:
V&WC6qQ00.G'QT<!Ik4%BdN8!4pm8@9FgN@6$\R90KH>4<g^JL8Zaf,6fg81):)"J:`Wtt%.
^;$+nT>h^Q1Q4@LeC:nU6IC\JlQR`.2G;%YeTTk,oiT98fjQCWP*Rfi4k`d%(=0U.SLH`gm[
sF$,T6*^n<>t>3HjnJP88L@^3_Q-%PDtkQRB:H;$?l?R_8Rk2u=R_pOH9.H$4m")`N'H'&2*
f;%XaUK?."'CM.^d!:/C*fY8Q_fj7+eV&_]AJkX\dof^(T.+M49[J0usl,PoPF<IS1e"A<T+5
1ME4.i1QpM-/cg8'lrDG]A5IoQ9Lh7io+u<XmDmfherLnLKs$JGKi"peR]A_*:WpH.RPJ=X5'2
r'i9N,!7RjFt@"W%ank3r"6@i@4K7$[(:e5k"N:PJhSa!)V./n%N=sGQgR%<?-2Yj$[Vbs4\
]A$T$0lYI)P'\)W?mYk^H]Aq_<glL$&;l_J/0hulGeq;&`;Y`KY_$K@ol3=TN?^894!,mWR#=D
-'^"C!s#lWI_BiSu[nG8+e=adP#OM._2[]A7C);hqb,.6k*=L1UfYC>u,8#N0&.Gra&\8@qXN
2dms>&F^;LrWB^>SQc?Rcc918)U]AF)-5pAi6d4Br<nX;mpr$a0M%CCj)Osce<^2XCJ3(f_O=
m(KXL65Pu[kcZL+#=9j4lBc6`Rl^-N)u1m9Dkbj>,Ya*DaU'GYtuL6p0'Vj=R0?PW1*8sPcV
m5\S",Cfbs#01Bp;%!a45G5T?87h\rliKYnt1m7Sg4B6SISInT!Zm2Uhi'W"`bj[D7!K^bGg
rJDC>^,.T5CeMN=P`tEj"XgUm.cpnKLiaVKaI>lkT^sJ[Q1%At(e]A<WYl-N:"lPu(49on6*P
j2gdD\TrnZ+fJ*gJp[qJARN3=HmB%!-:7Q[tIKdU>N2Lnn[!D-#$u[\(kfV6"k&r@ME^\X0l
dMLbkGY$JfF:)p1R$<EMAIL>[S-+t5312]A!C`haMEn(c#3Qk[i(#mDmphkUEij'I%So#
X;kQXrT%]AAFqC%CF)O:]A'q%$kKp?WnaTZ?"QhU?)3lK."WNU=8XS?V3`PC'K)UGs^'>Y7/8;
5b%&r2UUAMPQQht6toTRlIFFkE!)0K=FOJ@&(1GdIAs.sMT^ac6I#P'@T@?QlP;N6Xi:RA[h
,dU_]AZl!riU,L2a)UBdX8MR&j",nilmu+5S>,lCt%@0`1J46<H*"O"*eQP_A57&p=KkI4d`0
M;k7[mbA=L@g/6b?2$\1*1I2.4G*!sDCUe6OZM%8,NF:(.c%"YYl&6W=jH@sBQ0)[/Be`72M
n,us41?@5.PmE5p-]A9c,jnW:#Ghl"N66Jt'2Mo'4Zf[a)RlFP@qZLgU7]A@(3H^-umY6:FFk5
cNK:,jBJPR@t:5j\#,+hL;f7*nI0W`o2qGl]AEXZUe=4e\lA,L/^AQQ%Z;"_-X'O2^H.-]A5$Z
\$DCR^NePJ'*JU*uaOejD-AF<B,Bi>FEpWT:)pRTY5:)O@`P^Mlqou1g*Pf0X*ICEuGjS2Zb
J_kN6G6RkhEAj4W46'pZr7pWC7H;D+gkN2>cF%Ju%]An7??AO'#2ABHqMta\-WC)a#<SJh;F=
/V-(u`,Yjkn8&:hS>;ZiMd0h@9([?G/[H(TTGL7j*JO^5hoP"Pn*InVoB=<fM^RRYN@B?8pi
8qYlA1YSjD3dC0q[QP?m=@gsJ)Z"WcfUWUVCXPVUg]AqTr0JM$M3icnr^XnBoV:b=+B6%_*s@
uKos*Z:<#aBBA@bO\)K6GjBLYNl*+mFG0GX\`#k?4Bn[pT!')qro5^qHjIZ"X/GQnSQ2!cR[
J`p[gF4RF9,@9;n=b8Gr!5c:PTb3?1k5l*"pQ?u]A#[lXr;":QM(2U/:]A0MTS^94qt@XH8@h/
\Kg'/U7WCS2Z=imO/V)NTB#Hr?EQR(lk6F.k8ohm82t9bEJnZV9H;OUMk[71XBou!9j]A)>W6
!E2l?R)q\D\igWbf=?D_CpDBI?sqW]ARCKR2rAS%j"Ie?ek-n1,BGo70u&ARt--1Tcd'*7C"u
/0N-cMk4-Cg(*q,Yo-tW%g4T'<?XTipSm:8/^GpOJHMntRkNIpUJ*U85Y%@06l[NR>#gD,W4
AbSj^a'pE9)hsn^JRafNE\E&K\lm]A+A?ul7_4';k=n+q4%,@oInJA^+bJSSin&mbAuZ?sl?!
u1ab5V<V70*q12$$0"HBu2,mM`32RA3$UGP4k=kn3s`IK2%?[2DU"#INS=Z,'GK.2F2S47)[
6'&Y`]An>&P;*r/%mI/:RN87Frct'>cTV+PJP!s0HgGigU=N3Y]A#licZn"l]AnEOf/4=d">VD#
MJq2pQt*^87[&>X1f9C2d=:DXY2nQe0]A?)Nn#fg)Kurc4-u,G>Hu*QO^@#f>MD,<e0]AYZVsd
srPP50KQ12b5e36XhAkH4LQk;OUaa+;`/43nU)?p]AW@>)6fZrXR)nY/qS?U2gn+=KV+%J2tH
J>7%e<LnL[--(/G&'3$"[<RsgDNe6jF<]Aipq&),pA>0BQF+PkEfC2tidX#(/AU?E5Uk'no<M
j`?f,U'09[Brn2C9N38IsL4,X2b:$3?$h]A+F/6SgPd1GhHT1da&U6SgPd1GhHT1da&U_uCft
5qa2QgA!tQjnj?2oZT7\ra]AGWH4.f9V9&:/K14Fcn_K\RaWl83QXOLS9U\O/BdV*#R5C+>dS
-*$1MJ2\UmX''B(GsI6\fjrmuAu!Qu4p).*.bQh<?Hi"T~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="20" y="20" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="20" y="20" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(0);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="20" y="20" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="20" y="20" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TITLE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8s`p;qDqID%SB:O^8.+#=9rlTKL./8fB)da#'Uqd9<dt'J3.H)CU.J+G!.C+X"q7W#.kdag
Kc%!Z!CA%H(&KT(0T2'GD[0JH1B8'*9<`ga$iJIm\Q<PF8bk^"<8W\+Y$sRFsSj8;),ORk.D
be2lTE!Cp!81ViLh=K@MX%c?M=Tj1*E!IgZ:'`T?MA<tG"1s)YER4>8H,UMjU6GQqanYFD).
M:\rhW*efeop\VWiNUh"mjP*LFg7dZX:$:O'@$ErZA#n:&eFa[lYn6V$;/2.OWDk#1FjX1]AM
4J2T=6ReOU;lj!"_p'e]Aaq"u`G[WZK]A2]A"(!hlH<h0GZ%?)O43`!'kac5^21;&ZE'"[OX63X
frZ_dA]ApQ$^`*0i+%NT/1eX%&9dU-Onj8Mf-J'q;_[935,bab<FPY9[T<k=(Ls:.JL/Lld`r
LpW?!cCG(i@7^C?[)U_ZT8COBfhC[Iq:`8C3(u#Vg5W\MMEADfN>tVljJ5Nh,fOD12'Em@`^
UHVkr"6g1jkl]A#LX'S9Of"8GW9g7aST%[bK\b&$k%p:qV$G<[Um5X$1fl<`Ii9X_';=2+r)R
\=ol*ie-C&(V]Ara9N_IF0C_=j"=$fRju)_U5TZNaU"`#9TqG`Td3`P,@JPu>PA0P9HVHj(*0
,KmZ.-04.3_V"@&&-3H3)mRrQ/*EW&9=XNb[d/<\htAbctnUJ]A]AL()<%R4'%MQ&\MZs>`glO
I*qkoT<d?5oqei97+lsYV!J8g\6?dQZL$@+%e!P**BC9'gNt:tHKY+"KVb0[(iuV*]Aq%W6S=
*"`YDT9G1X]AXNX3$lV)pTUs0:ZL1-["i!>Ajh6j41KMNS8hQ$$TMKO78X!RB*@OoDU]AJ/O'S
R]Absd;?4/EVNYZ.!c/A%@T[QpCQ"\)j/p6;n>5SDGi</m`,X#S34U'1$A2_fbS[bX3RT4A:/
7eAY2YDlc"+?k5?.1aa2V"1h-u[=kHVO>.>*dqtl$*h8:C5k>c<,=fY-GD<#t#>CA4?VJp-D
`r]AcU9ukS[F=H[2K6;j;[7Ya"Tq!_jaOWQO;^AW#K_]A\M6lLX%FPTO,O+Yu4^-Sr`QIlqVZs
>j;NuL=+m?60V'39_aocUII9N%Z'a[m]A2E9r^00n+3Edhe3*1HYY,,)M$2<8V+OO/'U@3Jl^
C=9&C]AN4bMs2o<=?<Aj5J_Y%BJ47<iCsh91oemM*B(,CRhdnE#,Q31^-8<]Ao697micC9+60k
Q2]A<t>pd%3a9&2Ftc`)boZu`GakNJ3@q>/<Jc$SEKH2J,RT<8HIqE8#]Ae8L2<m^u=d-tLC@Y
djgM;HK^lAQ,Wn#,N'[Go,c=UOq5<1u3<F?.q.Yd(#'`\`!GK[f`:sbh_+a5oVCMa@P'Y>8n
pNDDh)^*7Qc!_uuo7Kg[UV:n)a=C?"</_gQR*SM>[oN#`JBNaNB^<n2Z:a9lN6W*A2WWPmBJ
7;k@`ZmBK(TZ1@c$+eVqnq:7e"LDV(q^4KH"![7mo^c@FIl_P.2f$F+q0q:rrL;d-k)Z95L&
XUsoJ`DnVi!:$!<M0gD7/;FBGV!@[BVp>qu[18Cf;NJ^emK_>AO4m'Wgi_dUGA-q&)%#M^2>
BZEir'$]AfLe4F[6tVMsdi<$c#H_IYHQm$dkU3Zm^`'m_FWeVnjO<0R.6='u]A`"*rn)MqH#B]A
'1niTI]A4VqZ*>elSfKA3cj]AX/>q"^\lR^$ZZQ^u/l9B!3T^;E[E.^u8Jd"^q&P/^Z\m=FX]Ac
L&nl>/R7V`BN)uO_cgq%J"3;[p@N5_3_8J2hXL1Ef"A85RlChW`>P82dP"JJiaiO82(;9qtQ
A3+3oe(eQl'f7L'$gT*ZA*&oeci(u3U'n7!\Dm-niVgGhPMCI/KPKgpHA=%*399cLA!ic$8\
Zk]A!cqQeVtB2TF-i;oTM%9f:G]A@VGH/;S\7)fB.RGOn&c0dkF]A3CtNctNk+:aXhdpp<C'-s=
FG#TH!F\47""jbp*TH,X4]AK/"r>o7rUprP_kok1%UB?e&[ST@u'#KI)]AD5<C-4Iu1gW:.]AQ$
-rK?lKfOT:%`c5%kjpOeM?7Qgsc'hgOh3@Zpe!T`\Wf1Z@AJBDKV(B><m@P:?#pekE[g;'PY
tH:^--W:Y6-tb4o`u)NPYVF@BUc!.bDD+9E.25Qi8BJI"Xd!YL5R"<nD.#XmTX"HGB85J(QQ
DK<meqW@4S*5hC2JFSp;[Hjd]A^,E@6G^\8`=s8ClDr[[0`Oj+td7da\j'C^$%mpmX6:IgY'q
lgNKSiS<.RHS&#Sj$W<.p-*s+]A%j*WI'\,lVngdX[,R.=gmeq8N@9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="11" y="13" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5041900,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[营业部督导跟踪]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="104">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?miRP$&jASS?6d*HQQN1LFgnSXRQq``glJ@[f,g.N(%MI#/c>9G,N("@Ju*e&4Of-XTK"m:
F46?mZ$c'lkXp;T;9r&k#D%;,fptOOr%&g"g`C+5!p_q;F?sIdZBEm^^[<\_lk(c7SfH/C`K
mXdb6,Mu1<mqi87t/G>XLQ7eTk_:XK3?!OfF%dr(m$s,Hd_#K;3`!c5goCB%"D7C=qq=F"H%
<oE^F0GTUZ=iiSj:`Wl?LdcY1UkpF*pY2aYr7s8Et9o0?rbhRr#E<m0^gd[h0T=Wf.D#MhJQ
inkfTk<M,j8Si>lEaKJI81)Nq%/)'.`g;jQHQB,[>+fLaaRI^a:>'nj%=nX[5D4JB)pG?'Kc
F_9pq$h)>_S;Ohg*l$KShS$M"H$448kZl:Wn0@-Ac^2i<>*k6`'TIhEf<*l.G?';Bb()rsML
<eaU&-TjYt(!ki#`W!=teY'j2Q`rVRK8R:`j3eUq'D*Jo+@IB9>?UQejPGm$glA/s(/'g@Th
*g`C\hM[<Q(PS1a7g)pp'HmUepGiM"lor!KQoPjb>ZrP)m:"_P@boH`PX[3(e*$sZ#AS2n5l
)ZRXj_$RE5X@$Qf63)Y__OMW%UAZ6,lEg_+*V5/3mA5>2Sd\'Clhh7+*7($VAesW[l$hAK2E
X(+uHk)6d17dD7NhqnEsB-1?*tW1"Nnc.h6<cJUfQPCreA=>9)$;o3F99aGk-j?<[`8:k^Ol
n?P1t0ZcfWgZ9H^Gp$_B`JP\J,m$o7_5*mmRa+SBU>gK]AfVbNui&eNIC,5gseRBp_&[b_#<r
IheIp&/(P>S%-NQUm%NlW54l.L#.Cs,/hl(fM"=6c56WLsA%:/dPkaCE;]A53(t[,ItsA_s)P
,4^AuIppa*!@W#59d!#$-5c)@lc7S?9'$a<R#$E0paq+He2,C!<28p!)B,n[rh`2:UN)remI
3]A*g0,hCHA+C$e:iTc3dh>Q4O5*^b9-p%Hb*/="!E.bGGk(<a%!;oklD!*LT2dYjoMS]A\S+M
<,q:1Uj9FPE/Qp9ruV!Z*VXLpT7m7<VAP:!gKRHPEB94Os/7d6c[N%C`=0`+u[A\G0blB)'b
Yu4sEg;mO\BJEsJX9QsC#o!TKW^>tKPs:fR)7U.reF@de1!=*!+!g6PIb+:2;5`tAj3?f:iT
]AlU9ea9/f#lPi%F[uRl[-_1,K7?93'Lf7F=YJ1-)&/rAu!((l_FYt:ckss)Y5b?:$IOO%'J1
GXN?CH0'92c"t3n<6e6MS8&7JJlA]Ai]A=WP/8prf\>[)bnu$"m3H5284\+f>fE+0&Okn]A=`Y.
e]A]Af,oT7ug)/m:Bec4oRWaqDU(RC=b[`gkH4"(8Y4NEG$PC+h@@a9GgjAV3Qq(4Ug-j;KXAa
]AK2\VUj%FB=^k5)uJaHu`@Zk'*HMt24s"]A#H-(p;iF)$OU`H<>e]A8T\(go\HTk\m@9>l$0_p
_kPl!`8hm3/!\i5^+r5]AD919=J1Wl-$==2nH$j2==,'p^RUe3M\S<Mg9[X.Q%/Tc_Bh_0*O@
]AchWjm7S,uN#?EAKRlCY3)fB]AigcCj9C*Npjf=<F3o>:9Bdg8,)&>(g7\qSio+t\d@(XC5dE
Cf=(%4F/l$R#j!]AS[&BfK,oeA6-UbJce_*BFj3h@%Cdi>AaXR'8fX\fE\SAR/TKFK<FF,8u/
9B=V)<[]AN_s4nk8kcmJR]A=5P4=k"laGF_O:uZH]A#-Flej&l]A"3H-C0@,8/q_pI&%IdB+$/EC
koE'.>`e648=>VVe'L1./b$1\o%V>j9tbk?(djA7`tP/pgHr\6@&PhMP"_K[2MN%L(AoiBQi
ApRmJGXRM,^00NbEGSW\7%o)O0D,h0Sh>d>iA(J?dYZY;PTooYMF=]A?N:^P*d[@<!s"NOd_e
gAh!Y*,[.f0A*ca3)<24\JTQ[J<:M(Ytm\r.QH$M_W%NH8D[NKPoFI+'ph!/8?`@Y;O[$3=m
3&*\@mN9<SqEp1eNrq#4SMAqtkD>Gt*k)Q'K\$)J`PTTb(KI6%b*)M-Bp0iA/H1Pd=M#c_>m
78O-TW]A0WYOFe]A$!KL8!)_\c`7;cR$[%SBn\aPcoPuN^2A(78)"T2ZbgB39o12bBr!?PdTg^
0E__i#8`C['7'&ckt";tBSWU<Ih$fDGRE+XbSW57#YJjO:WH=:kd,Yf+&olXpDV4e6]ATaN`+
B8@,BXqG@'3PS:>;`FI1eM$TVHAtV;fck.dXGuO_J3eOh?mV)1d2&Y/=<.k2:#C?)XboN6)J
m70AGA!,7<bc*gpGduCS7NnDLQp4!?WNP7&r_tUYS!!NDAI<'Q.l4]Ai@^`&3-`4[4n_a3-8a
;\BcGRe>ItgNId%l#uA9=W.!a;N:+1t2&lQf+??XmNdjT2X7qW4%)dCYhk3gepC^[);>+9`O
UkC.Ul2F.AM*he>-6DBHRaWtJ>b!Mda=qF/9.YZU[K7]A;9\_oDHk-!d>^7/;`?Mg\ofV4EUB
47k%[ncIJ0[(CU%hiF>ig9KhA.CDm^ka\*.RlA!;J&UFd\<j52Y&$T(P'YYWc9km:s['t)Jk
,i\UU7!Z368.WXsVE%tp!>RVj1^t*Y?SMm>]AU0ABRD@W6A4G2<KSp(l,.X3k`G^c=V+@.S5:
e8#Ht'1]A(eI0;?NhbMl!WbJr7*1g$aK-uT:*H,A(FRK]A!Wbt3#u<)H/uh4db\!?g$,^ZEp`b
O=iZ@CX/Ct1\YopS'Fki"f(dT;q2bo>5XL-?d9$pu([Ls>Xe2Vqisls9_K\^n@Jjb9Ok;hNO
^dtB2!CXbg3#3JCodOQ84W&*E>e\2&Uu$WG)eSL@']A#%"CfFc`%S_Y\:/'+s6T2H<3R6C-T[
1/72a!oNX_*+ZD_5WRS&g,h!L&Vh`+X$YUsch=LkYiF#Cc@d;1ZcFb2XNA?a%;;F,3p1fQ?3
=Wf[uP(P`lMJkb)B0Qo+U2iRJ%WTqE6r>[b]A5T1UW_LL'VJ"JF`rtL8V9@[%Yd&Kq3$p_Z<6
"Q]A23XA:Fsl;bRhKiY<KaLp?;)`JAeM?)#ZV@dkd]AZ/Ke_Q9*9hZJK=<A>&mV]AG_9WjuOkY@
Yo4q+qp,#A$pc.)D6EHpYrfo\.`6jZm%sC"25FZ;i%X581diqH.N+bo'c&pc")oi0o+hKMgI
SGO&T,d93^_okOaR+4"37U:Q;SJSMI&QY^ep$Bc9!Qh@A)R?!8_-qLp?Z%IU%?q<5!:P[nKp
(<RVo0iI&l%WlPma4gf;01*4ta5OZG*(Y<.9VLTKj[9\sUac<9,ROLj=GaYkG:C"$`l0L@:s
4GiN_Uofu$&n,\Dn&T:O8"u%2H6)+";Ht,HN;b`m5HUj,pP6)t#W[rHNYmbdcCsZXj!l*sk_
cds':q!PN<'F3q7V:o(>7VqrbXIFo^YJp[>!GLpt=+g^?`Q5qhQ;3]A>(lFoi?j?bM0Q?6UJt
uLRmDqO&#9-Pg8OhGAqPAEdetfY;b@YgLX^mJ,fWV+FjFn#U"Wt!XKp!!!3EKJ,fZGs,XV,S
XB`F)s]A=U'Oo>aQ)?flHgZ0YjR&)<92sO@RAA$rK5XQ<!=;:^!YUTF"=,)j#tR;_&s.VH,U!
+g,Uh1BY\EKW#kgA$$jk\/q#&X,If]A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="163" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="43" y="15" width="163" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="url"/>
<Widget widgetName="BACK"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
<Widget widgetName="SCTJ"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="yybdd" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds4" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="2d98af8b-6f84-4df6-8e09-8d9150046e06"/>
</TemplateIdAttMark>
</Form>
