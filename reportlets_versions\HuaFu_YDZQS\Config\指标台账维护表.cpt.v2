<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="para_指标ID" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select zbid,zbmc,zbbm from DIM_FILL_ZQFXS_ZBWH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="AREA"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="TAB"/>
<O>
<![CDATA[产品销售分析]]></O>
</Parameter>
<Parameter>
<Attributes name="PAGE"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="Z"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="YEAR"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="LEVEL"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
B.PAGENAME,B.TABNAME,B.AREANAME,
A.AREA_ID,A.YEAR,A.ZBID,A.CJ,
A.XH,A.CREATED_BY,A.CREATED_TIME,A.UPDATED_BY,A.UPDATED_TIME,
A.CHART_TYPE,A.BZ,A.IFQJSD,
CASE WHEN LENGTH(A.ZBBM)>0 THEN A.ZBBM ELSE C.ZBBM END ZBBM
FROM DIM_FILL_ZQFXS_ZBTZ A
INNER JOIN DIM_FILL_ZQFXS_PAGE B ON A.AREA_ID=B.AREA_ID
INNER JOIN DIM_FILL_ZQFXS_ZBWH C ON A.ZBID=C.ZBID
WHERE 1=1 
${IF(LEN(PAGE)=0,"","AND B.PAGENAME IN ('"+PAGE+"')")}
${IF(LEN(TAB)=0,"","AND B.TABNAME IN ('"+TAB+"')")}
${IF(LEN(AREA)=0,"","AND B.AREANAME IN ('"+AREA+"')")}
AND A.YEAR='${YEAR}'
AND A.CJ='${LEVEL}'
AND 
(
B.PAGENAME LIKE '%${Z}%'
OR
B.TABNAME LIKE '%${Z}%'
OR
B.AREANAME LIKE '%${Z}%'
OR
A.AREA_ID LIKE '%${Z}%'
OR
A.ZBID LIKE '%${Z}%' 
OR
C.ZBMC LIKE '%${Z}%' 
)
ORDER BY B.PAGENAME,B.TABNAME,B.AREANAME,
A.AREA_ID,A.YEAR,A.ZBID,A.CJ,
A.XH,A.CREATED_BY,A.CREATED_TIME,A.UPDATED_BY,A.UPDATED_TIME,
A.CHART_TYPE,A.BZ,A.IFQJSD,
CASE WHEN LENGTH(A.ZBBM)>0 THEN A.ZBBM ELSE C.ZBBM END

-- SELECT * FROM DIM_FILL_ZQFXS_ZBTZ WHERE AREA_ID='fgssy__jzjxkh'
-- SELECT * FROM DIM_FILL_ZQFXS_ZBTZ WHERE AREA_ID='zbsy__jzjxkh'

 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_sx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT     
PAGENAME,
TABNAME,
AREANAME||MODNAME AS AREANAME,
CASE WHEN TABNAME IS NOT NULL THEN PAGENAME||'/'||TABNAME||'/'||AREANAME||MODNAME ELSE PAGENAME||'/'||AREANAME||MODNAME END NAME,
AREA_ID
FROM DIM_FILL_ZQFXS_PAGE



 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_page" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT PAGENAME 
FROM (
SELECT     
DISTINCT PAGENAME 
FROM DIM_FILL_ZQFXS_PAGE
) M
ORDER BY DECODE(PAGENAME,'点此新增',0,'总部首页',1,'分公司首页',2,'营业部首页',3,'总部考核督导',4,'分公司考核督导',5,'营业部考核督导',6) ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="PAGE"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT     
DISTINCT TABNAME
FROM DIM_FILL_ZQFXS_PAGE
WHERE 1=1 ${IF(LEN(PAGE)==0,"","AND PAGENAME IN ('"+PAGE+"')")} ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_area" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="PAGE"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="TAB"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT     
DISTINCT AREANAME 
FROM DIM_FILL_ZQFXS_PAGE
WHERE 1=1 
${IF(LEN(PAGE)==0,"","AND PAGENAME IN ('"+PAGE+"')")}
${IF(LEN(TAB)==0,"","AND TABNAME IN ('"+TAB+"')")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebWriteContent>
<Listener event="afterload">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="usr"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(len($fine_username)==0,'admin',$fine_username)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.user=usr;
_g().curLGP.hideSelectFrame();]]></Content>
</JavaScript>
</Listener>
<Listener event="writesuccess">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().parameterCommit();]]></Content>
</JavaScript>
</Listener>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.write.Submit">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[保 存]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[submit]]></IconName>
<Verify failVerifySubmit="false" value="false"/>
<Sheet onlySubmitSelect="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<EditRowColor setColor="false"/>
<WebWrite SheetPosition="3"/>
<RptLocation isShowAtLeft="true"/>
<UnloadCheck check="false"/>
<ShowWidgets/>
<OtherAttr autoStash="false"/>
</WebWriteContent>
</ReportWebAttr>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[876300,1152000,288000,864000,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[144000,144000,0,3962400,3962400,17373600,6667500,6667500,3314700,3600000,4381500,6172200,3600000,6172200,3600000,6172200,1600200,1600200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Widget class="com.fr.report.web.button.write.AppendRowButton">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[添加]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[add]]></IconName>
<FixCell row="2" col="5"/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="14" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="15" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="16" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="17" r="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="1">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="1">
<O>
<![CDATA[年份]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<O>
<![CDATA[层级]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="1">
<O>
<![CDATA[请选择所属页面区域]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<O>
<![CDATA[指标ID]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="1">
<O>
<![CDATA[指标别名]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="1">
<O>
<![CDATA[图表类型]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="1">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="1" s="1">
<O>
<![CDATA[区间/时点指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="1">
<O>
<![CDATA[备注]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="1">
<O>
<![CDATA[创建人]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="1" s="1">
<O>
<![CDATA[创建时间]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="14" r="1" s="1">
<O>
<![CDATA[更新人]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="15" r="1" s="1">
<O>
<![CDATA[更新时间]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="17" r="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="2" r="2" rs="2" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=seq()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand leftParentDefault="false" left="B3">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" rs="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="YEAR"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.DateEditor">
<Listener event="afteredit" name="编辑后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq); ]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择年份！]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<DateAttr format="yyyy"/>
<widgetValue/>
</Widget>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" rs="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="CJ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.ComboBox">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择层级！]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<DirectEdit>
<![CDATA[false]]></DirectEdit>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="1" value="总部"/>
<Dict key="2" value="分公司"/>
<Dict key="3" value="营业部"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="1" value="总部"/>
<Dict key="2" value="分公司"/>
<Dict key="3" value="营业部"/>
</CustomDictAttr>
</Dictionary>
</Present>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" rs="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="AREA_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.TreeComboBoxEditor">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TreeAttr selectLeafOnly="true"/>
<LayerConfig layerIndex="1" modelColumn="0" viewColumn="0">
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="PAGENAME" viName="PAGENAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</LayerConfig>
<LayerConfig layerIndex="2" modelColumn="1" viewColumn="1">
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="TABNAME" viName="TABNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<LayerDependence layerIndex="1" thisColumnIndex="0"/>
</LayerConfig>
<LayerConfig layerIndex="3" modelColumn="4" viewColumn="2">
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<LayerDependence layerIndex="1" thisColumnIndex="0"/>
<LayerDependence layerIndex="2" thisColumnIndex="1"/>
</LayerConfig>
<isLayerBuild isLayerBuild="true"/>
<isAutoBuild autoBuild="false"/>
<isPerformanceFirst performanceFirst="false"/>
<widgetValue/>
<ReturnTypeAttr delimiter="," startSymbol="" endSymbol="" returnString="false"/>
</Widget>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" rs="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.ComboBox">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var b=this.getValue(); 
var mc=FR.remoteEvaluate('=value("para_指标ID",3,1,"'+b+'")'); 
var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);
contentPane.setCellValue(7, ro1, mc);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择指标ID！]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_指标ID]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_指标ID]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" rs="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBBM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" rs="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="CHART_TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.ComboBox">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="0" value="柱形图"/>
<Dict key="1" value="折线图"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
</Widget>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="0" value="柱形图"/>
<Dict key="1" value="折线图"/>
</CustomDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="XH"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<Widget class="com.fr.form.ui.TextEditor">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.TextArea">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="false" textInputMode="1" isSupportManual="true" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="false" showWordCount="false"/>
</Widget>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="CREATED_BY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(len($fine_username) = 0,'admin',$fine_username)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="CREATED_TIME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NOW()]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="14" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="UPDATED_BY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="15" r="2" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="UPDATED_TIME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NOW()]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="16" r="2" rs="2" s="0">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var cell = this.options.location;
_g().deleteReportRC(cell);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ImageBackground" layout="1">
<FineImage fm="png" imageId="__ImageCache__CC78C69AD79D23B7C2F568E7663ED7C7">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n%,mbeXK/jZic@"B&/\>oZ3]Ajs2;o@/fm!rd';D7R+%$>ZP8s,2
D\*8Do!p,NI\=hS*Tflj3XHaNX'/d3S7$I"MU_oWtGU1YS!.\8Kc_`@kjKiP$X5J?A+ImK>8
bD1=Q&fc1@D2_iVP0P,LV4#c-"_t+VcSdG<%=#S:^$iSoB_(>+Rg0pcO$D@qAGNGTiS89*MV
`aScm>VbgHP4KR(`Kp!ZnJSL[2nHp##A;bQZZ%09`P~
]]></IM>
</FineImage>
</Background>
</initial>
<isCustomType isCustomType="true"/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="17" r="2" rs="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="10" r="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="IFQJSD"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.RadioGroup">
<Listener event="statechange" name="状态改变1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=format(now(),"yyyy-mm-dd HH:mm:ss")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var location = this.options.location; //获取当前控件的位置
var cr = FR.cellStr2ColumnRow(location);
var col = cr.col; //列号 
var ro1 = cr.row; 
contentPane.setCellValue(14, ro1, user);
contentPane.setCellValue(15, ro1, rq);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="0" value="区间"/>
<Dict key="1" value="时点"/>
</CustomDictAttr>
</Dictionary>
<widgetValue/>
<MaxRowsMobileAttr maxShowRows="5"/>
</Widget>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="5" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
<C c="12" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="14" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="15" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="17" r="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<FrozenColumnRow columnrow="B3"/>
<PaperSetting>
<PaperSize width="100800000" height="42768000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<ReportWriteAttr>
<SubmitVisitor class="com.fr.report.write.BuiltInSQLSubmiter">
<Name>
<![CDATA[update]]></Name>
<Attributes dsName="hfzj"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_ZBTZ"/>
<ColumnConfig name="AREA_ID" isKey="true" skipUnmodified="false">
<ColumnRow column="5" row="2"/>
</ColumnConfig>
<ColumnConfig name="YEAR" isKey="true" skipUnmodified="false">
<ColumnRow column="3" row="2"/>
</ColumnConfig>
<ColumnConfig name="ZBID" isKey="true" skipUnmodified="false">
<ColumnRow column="6" row="2"/>
</ColumnConfig>
<ColumnConfig name="CJ" isKey="true" skipUnmodified="false">
<ColumnRow column="4" row="2"/>
</ColumnConfig>
<ColumnConfig name="XH" isKey="false" skipUnmodified="false">
<ColumnRow column="9" row="2"/>
</ColumnConfig>
<ColumnConfig name="CREATED_BY" isKey="false" skipUnmodified="false">
<ColumnRow column="12" row="2"/>
</ColumnConfig>
<ColumnConfig name="CREATED_TIME" isKey="false" skipUnmodified="false">
<ColumnRow column="13" row="2"/>
</ColumnConfig>
<ColumnConfig name="UPDATED_BY" isKey="false" skipUnmodified="false">
<ColumnRow column="14" row="2"/>
</ColumnConfig>
<ColumnConfig name="UPDATED_TIME" isKey="false" skipUnmodified="false">
<ColumnRow column="15" row="2"/>
</ColumnConfig>
<ColumnConfig name="CHART_TYPE" isKey="false" skipUnmodified="false">
<ColumnRow column="8" row="2"/>
</ColumnConfig>
<ColumnConfig name="BZ" isKey="false" skipUnmodified="false">
<ColumnRow column="11" row="2"/>
</ColumnConfig>
<ColumnConfig name="IFQJSD" isKey="false" skipUnmodified="false">
<ColumnRow column="10" row="3"/>
</ColumnConfig>
<ColumnConfig name="ZBBM" isKey="false" skipUnmodified="false">
<ColumnRow column="7" row="2"/>
</ColumnConfig>
<Condition class="com.fr.data.condition.ListCondition"/>
<UpdateAttr updateStatus="true"/>
</DMLConfig>
</SubmitVisitor>
</ReportWriteAttr>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="false" windowPosition="1" align="0" useParamsTemplate="false" currentIndex="6"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[setTimeout(function() { $('.parameter-container-collapseimg-up').hide(); }, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="LEVEL"/>
<WidgetID widgetID="12b263dc-a41d-4d6b-b9b9-5c2d0f7bc4da"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="1" value="总部"/>
<Dict key="2" value="分公司"/>
<Dict key="3" value="营业部"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[总部]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="184" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="LabelPAGE_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[层级:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="132" y="9" width="52" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="LabelPAGE_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[年份:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="0" y="9" width="52" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="YEAR"/>
<WidgetID widgetID="6b9a981b-d81d-4829-a951-19acf0b45eb0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="dateEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr format="yyyy"/>
<widgetValue>
<O t="Date">
<![CDATA[1703001600000]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="52" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="AREA"/>
<WidgetID widgetID="761a8847-577c-48a0-b46b-390387221540"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="comboCheckBox0"/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREANAME" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_area]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="680" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="LabelAREA"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[区域/模块:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="600" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="TAB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="TABNAME" viName="TABNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="520" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="LabelTAB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[TAB名称:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="440" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="PAGE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="PAGENAME" viName="PAGENAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_page]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="346" y="9" width="98" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="LabelPAGE"/>
<LabelName name="年份:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[页面名称:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="266" y="9" width="80" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="Z"/>
<WidgetID widgetID="7f54c46e-6a8e-42e3-b07d-6e3210d4265d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="IDN"/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<watermark>
<![CDATA[可搜索(指标名称、指标ID、指标别名)]]></watermark>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="767" y="9" width="242" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="formSubmit0"/>
<WidgetID widgetID="baa6366a-508e-4d41-a7d9-95d2c722e2c4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="1019" y="9" width="62" height="27"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="YEAR"/>
<Widget widgetName="LEVEL"/>
<Widget widgetName="PAGE"/>
<Widget widgetName="TAB"/>
<Widget widgetName="AREA"/>
<Widget widgetName="Z"/>
<Widget widgetName="formSubmit0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="false"/>
<UseParamsTemplate use="false"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="1237"/>
<NameTagModified/>
<WidgetNameTagMap/>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="1237" height="45"/>
</ParameterUI>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportParameterAttr>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1313794" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Left style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1">
<color>
<FineColor color="-2299142" hor="-1" ver="-1"/>
</color>
</Bottom>
</Border>
</Style>
<Style horizontal_alignment="0" vertical_alignment="1" imageLayout="1">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1">
<color>
<FineColor color="-2299142" hor="-1" ver="-1"/>
</color>
</Bottom>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1">
<color>
<FineColor color="-2299142" hor="-1" ver="-1"/>
</color>
</Bottom>
</Border>
</Style>
<Style horizontal_alignment="2" textStyle="1" imageLayout="1">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1">
<color>
<FineColor color="-2299142" hor="-1" ver="-1"/>
</color>
</Bottom>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="1"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="para_page" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_sx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_指标ID" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1682388634488"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="0faeaeb0-ab2e-476c-9c7c-23772ab5bd0b"/>
</TemplateIdAttMark>
</WorkBook>
