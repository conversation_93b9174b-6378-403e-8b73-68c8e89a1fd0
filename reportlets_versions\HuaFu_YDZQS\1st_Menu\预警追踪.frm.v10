<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="body_yjzz" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-12-18]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
		SELECT 
			A.AREA_ID,
			C.AREANAME,
			A.ZBID,
			A.ZBBM ZBMC,
			${IF(level='1',"B.ZBDW",IF(level='2',"B.FGSDW","B.YYBDW"))} DW,
			A.XH
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		INNER JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE CJ='${level}' AND PAGENAME='预警追踪' 
		AND YEAR=SUBSTR('${date}',1,4)
		AND B.STATUS=1
		ORDER BY A.XH
)
, RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	    SELECT 
		A.ZBID,A.DRZ
	    FROM ADS_HFBI_ZQFXS_JGZBMX A
	    INNER JOIN TAB ON TAB.ZBID=A.ZBID
	    INNER JOIN RQ ON A.OC_DATE=TO_CHAR(RQ.JYR)
	    WHERE BRANCH_NO='${pany}' AND TREE_LEVEL='${level}'
	    UNION ALL
	    SELECT 
		A.ZBID,A.ZBZ DRZ
	    FROM ADS_HFBI_ZQFXS_DDFX_YYBKHMX A  
	    INNER JOIN TAB ON TAB.ZBID=A.ZBID
	    INNER JOIN RQ ON A.OC_DATE=TO_CHAR(RQ.JYR)
	    WHERE BRANCH_NO='${pany}'
)  
, DATA2 AS (
	  SELECT
	  A.ZBID,A.WARN_REASON REASON,A.WARN_STATUS STATUS,A.VOLATILITY
	  FROM ADS_HFBI_ZQFXS_WARN A
	  INNER JOIN RQ ON TO_CHAR(RQ.JYR)=A.OC_DATE
	  WHERE BRANCH_NO='${pany}' AND TREE_LEVEL='${level}'
) 
		SELECT  
			TAB.XH,
		     TAB.AREA_ID,
		     TAB.AREANAME,
			TAB.ZBID 指标ID,
			TAB.ZBMC 指标名称,  
			D.REASON,
			D.STATUS,
			TAB.DW,
			NVL(AHZJ.DRZ,0) DRZ,
			D.VOLATILITY 
		FROM TAB
		INNER JOIN DATA AHZJ ON TAB.ZBID=AHZJ.ZBID  
		INNER JOIN DATA2 D ON D.ZBID=TAB.ZBID   
		WHERE 1=1 ${IF(LEN(status)==0,"","AND D.STATUS='"+status+"'")}
		ORDER BY TAB.XH


--		SELECT *  FROM ADS_HFBI_ZQFXS_JGZBMX WHERE OC_DATE='20231218' AND BRANCH_NO='9999' AND TREE_LEVEL=1 AND ZBID='khsdzc_20230820182807'
  ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[预警追踪]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.user=user;
window.level=level;
window.pany=pany;
window.w = window.innerWidth; 
window.url = location.href;  
window.grnum='';  
window.parent.document.getElementById('YJZZ').style.margin = '0px';
const elements = window.parent.document.querySelectorAll('#YJZZ *');
for (let i = 0; i < elements.length; i++) {
	elements[i]A.style.width = '100%';
}

window.bakdis = function() {
	var u = navigator.userAgent;
	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	var isiOS = !!u.match(/\(i[^;]A+;( U;)? CPU.+Mac OS X/); //ios终端
	if (isAndroid) {
		var parentDiv = document.getElementsByClassName('css-1dbjc4n r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';

	}
	if (isiOS) {
		var parentDiv = document.getElementsByClassName('css-mbp0r r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
	} 
}]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[//预警追踪切换
window.yjtab2 = function(obj) {
	if(grnum.length>0){
	  var n=grnum.substring(grnum.length - 1);  
	  if(n==0){ 
		colo='FAF2D5';
	  }else if(n==1){ 
		colo='DBEAFF'; 
	  }else if(n==2){ 
		colo='FFE8DD';  
	  }
	  const ment=document.getElementById(grnum);
	  ment.style.background='linear-gradient(-45deg,#E55C17 10px, #'+colo+' 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right';
	  ment.style.border='0px solid #E55C17';
	}
	const ment=document.getElementById(obj);
	ment.style.background='linear-gradient(-45deg,#E55C17 10px, white 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right';
	ment.style.border='2px solid #E55C17';
	window.grnum=obj;
  }]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA3').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA3"/>
<WidgetID widgetID="cb1d2383-2abe-4ce5-bdb6-50cc01fc0826"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[400334,571500,152400,566057,304800,304800,952500,1152000,952500,304800,304800,566057,1152000,1152000,1152000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[190500,274320,457200,2743200,1524000,1066800,4099560,274320,190500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50">
<outerBackground>
<FineColor ver="-1" color="-657670" hor="-1"/>
</outerBackground>
</CellPropertyErrorMarker>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:13px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[body_yjzz]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" cs="5" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:11px;height:11px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" cs="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:11px;height:11px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.parent.FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="3" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" rs="7" s="3">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="5" cs="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="4" s="8">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:9px;'><div><img src='../../help/HuaFu/sj.png' style='width:9px;height:9px;float:right;/'></div><div style='float:right;height:9px;line-height:9px;'>详情</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="zbid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A5]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="zbmc"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C7]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="status"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="zbz"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=D8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="dw"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=E8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="reason"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(len(C9)==0,'当前暂无预警信息',C9)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="px"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(F8>=0,"DESC","ASC")]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[/**
     var branch=ObjGet("get","branch");
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date");
     var pm=ObjGet("get","fgspm");
**/
	window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/预警指标穿透.frm&zbid="+zbid+"&zbmc="+zbmc+"&status="+status+"&zbz="+zbz+"&dw="+dw+"&reason="+reason+"&px="+px+"&date="+date+"&pany="+pany+"&level="+level, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 68.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 10.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" s="9">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="7" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="11">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="STATUS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#"+if($$$='正常','6cd591','DE554F')+"'>●</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="DRZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(FIND(".",$$$)>0,FORMAT($$$,'#,##0.00'),$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="7" s="7">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="13">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="VOLATILITY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="6" r="7" s="14">
<O>
<![CDATA[近一周预警次数：0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="7" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="8" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="5" s="7">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="REASON"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[UP]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) > 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[find("上升",$$$)>0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="64">
<foreground>
<FineColor color="-2206385" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[DOWN]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) > 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[find("下降",$$$)>0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="64">
<foreground>
<FineColor color="-11426439" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(len($$$)==0,'当前暂无预警信息',$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="8" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="8" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="9" cs="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="9" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="11" s="15">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="11" s="15">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="11" s="15">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="48"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-9644655" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^HdTQH3DIo#,V;kKhk#V;R:%".4H`L1Nk%'m,C166kmOQ^p=c1XdWK7LaV5B*;qZ`62Un
XZ^W$nI;L^9f4-jY4*AY/(Qq>C9)Ib3*lFnL4TI[tQJZeY<@Fe<`$E:0h+et70c/0uUMp!h+
D^I_"6"K_Qaq+:-HV72m9-$AnEWT-KR@e8a%A)LE@+Da9G42*jm/)8\NU@Ff:TKbY2oY@G;0
,aXiO!BBDmQC7;^A6#rG2`0pZk$uo9^.LgX!.$n%il=HKEZH.(K]A2r3-rDQUqd=?14-2RMEt
ioE4e/^q1bNMm!,IW2G`JKNdGr3UiNF]As22s7Nprm-!1gJ,5BG.-UMZ1'1U=a6*IdYG'6%*r
e)G@-5h[mA97q[nQf95(JEnNMXIW_VTi0F-X"NY5pj[Xu)ddYX%pUIa==5D;ZbQkA5=OWO%7
oDtZorYJa`G,PiLkoY@.=1&Icmb[fS@b\PF)3SYAtYmm4RnZ0eRcCp'tGC0*SL0]A)9-%A@(@
;_tL(82b/M0"4sT'/I5V?i<rWS)a^akBsp#o?9G3GC<hg&L>N`&'R0WJa<\Y%cR:YRn'T7#a
ZmcRA\Z`N*\&r)S4E-Uil;qnapcqho?DZQP#'0srM0W4_R92/,oAFlD<&s5*Mh5nLcj+CEn,
CJ72ZlI@WuaJ:BJKFV?-C&J4dqX_BFI;qSVAp':QGA9K8t*d)j,2E0pM+^K*#oZ_(np"7`EX
*^iVMPGnM_2[*[fELm#J6oT`Nr/>:YOqDJ1q(VZ@%tbu+deP"ADIu$0[LKd:`^o4A-h5X[$O
Jh>D@=s*Z9QQZa_<qf4GkOT^Suej5,[$[6Fn'-f6M$"/\&q<6rEejYYQ`id"^E_bIBa!jN%n
#%C/7MKO=>Q`,8SlaYZB4Yo`4!:k0#;`^Cd:m8@L.\,;8Y&.7XW6&?\)[7gSHO6bjsp>='Ua
YQjL#_@i9[\SKCM&&;XS3lE##=BFWm`eXHLNXS<=61:Ci01SLMAX_nL,LH*%&Xc:(4l4b&^m
4XfA[oo)jNR)KY=I*&X,]A_Et!Ib7H^5CUZ0d.S(Wc(0WV5T"H";B/KUDqr5K9p7\o#6&m1-r
[4(Qj8[E20p*XJ@PcKdfEbIq4AL\*e4XJVKBW:]A[O3DQ<^uN"T/U;W6MmI#L=jGkmW-_FLc5
rEF*nRS`ZN$iYO/6GH4'ur<k!epVYX-G4Cs@Tf?W#\"2q?5TI_MeW3SG$6Q$;sLS7FN>gGX2
a;/n;[KudG^/:]AK:$1Y5LNNt`/+ObFunO\24MhW3b@`r(#AN2E6%mce)(0E1_$rI/k+mpC63
.tF7EJm.ojAG15Lk2mubdo!c%DPf5U#X#>D8R%Q;.BDY_j?PT,-Qd"p[fZanO':(1:J^!FR[
,i.spd%>AlL7/8KOA'p.`MEH_!UF#UBJ@KA)r!^3d`q`G`'T"_uoa$Uro#!+&h2E`t69u,kS
52Yo)',5/"9<ICQj6S&Jj"d<U*#;@IAW!ljloJMC\nXIdi^K)VPPa&roM1DpM-egK4iQA<+`
-m#\]A4>U(=_\VWfIX#c&%<HHm5FqS=a^:2#h!?OZL7tWRQ<0B'(.[J\P_]A>=hdrY!j^$\aC\
W[?Z\mjo)@="rpB$B$=GNj:j_:Ci:7.^!:=$5-hgnZaG4lR:]AYeZ]AjBd_Cr7b"h#*2i@D*0X
QM2lQ4.c+i/36=lcLWn`/.9X:?^8D?9WZ/(PfhN\+cFG+*`+BbjXSa[g$ip=U=']A-"kGBLS4
_ss%.3EBVH/-<175q<jo5o<?Vp_7L>.?B8BsTAQYR]An+7XFQ$;r'eqG_^6R\:Vk-]A*u(='K(
=D_i+R%'+l>9!r7K"W"&!0mtoW2Y`09Cq,.\b<]A23J-6&0iqAkWuXEBB)E=(JHdR:\N2,o<:
l/`YuuN#O"S1>Z7K4S]AeVY&AdDQ(5'F;[JRjiiI^o\%@Qb`29;KdO'S&(lSPJ7r[lQEU%!i6
.L3g]Aj^9;L>ourPcP/g&B,MZIOCiZH*Eq9rUR;`)G)88G1^Q6(/Lfp9(/;<EMAIL>.I
A<ZeNRPCO*^j447LV;;AmW;m$5s^ZCAi^,Z.ro\D,N^0!*=C5/s>';5M&A']An`hNSm1o#TOC
5SYe;0'?Q7"dL4A`R&Z9`,o""$?'5,)HC2MhnY:1=6VisuPXO-2OYNV*5LNnQMr'^GP#"o>[
!`6hjAFoUt<Q?fk!g0c:0ac)o";Jd7bR$["$m[=do>pOMkqKBn(=m#38]Apc6.Oe$*42DH_SZ
PZgH.b)FM01)-DR<"-FGLl7O&G[tkbHGeo4mWW5&;j:52.qoenQ9iD]Ac[28T2LNAR(K)D7(P
\M1FHOaF*<,-%\5+G'G)M.WOd.:M-#f(M-G4V*Q7MO<L&M"s>f$cR2HOqP\XZ=#/L`14"A3&
IN^@M#=FT@O*KjM&Og3CW8!+C>HYIq@10eVMr5$(0SSq$;3;UeQsIc).QH]AgADH'1=kfn'CY
8hkPd@eX0D3B*#1^]ADYZHs,3%ZG:XJoJDN$^b2-(i%0O?N_=RTSs9fXujWElX-eTYYid,+8W
Z*b'o5UhSYpanQ<cG]A&j[j8rH$XfqJ9halB(_+b=&KQ9S&HpYAkr#Gd(SU:\ia-uREB<*s9O
AATaBI*G@9eM@LdgYK<'<Q_?W1ch!VOj%cs.*Mg;Rj$=`<'m_BbNAG(iaN$41#ZRUr^9,i_e
s'>EMH#0g%WTP5)9U1S_eqZZslenW9Qn@+4p4bulB8p9F1COWX1ZU)LLam9R@jU2!7YH<'"_
>e)5bc:Q%VJESa8lNB]A"3$HpACIN(N0(TZ_Y4*J3XIlN6.IWsn@+<>C6,`M_6&F?"cpgA,o9
I^?18l27"pPiNOtrE?+:![&1ET(Zd2I+94AmM!0tP,X7>+-/"gZ[H5OY&0LCl%[$\S(OBSQ;
$2&n#(u1EY;*rBH(6?,e@[]A)eicC(S'73k.*^lX::'-+K,qLY;a+alNQ!Ea3r'n%kA[)$.7?
;H1b5"6]A\p]A.X>O9P30O#L<qN=)1']A5P9^H>:<bm>/+0V1[6oKq,3YYAG7s!ddHVaS;pR:D\
H20]AS?kJmMR70+h/h^IVV_q61L[(H[Ycoj`7.:/h[860SY[Nk]A5Rs:1;N@ER,WTk)ck3S(a\
S=Ga4*RHif2f"ajXIPg(_!k9$_MADA`P+gnf!pBTE^WB$$\$(TO*/0`+1D+[.)D1$)bNNUH1
9*ddCS>C:60oB+I3AK?X27^d>?<n1k5,$a#=EDGUNdko,_OkIfj+5FFC/LB$hiNt1KKOMLrN
PWaNq8sr;8&X'!Ak3-2g+Bi5WINrkV)tnO5hc9XO#X"3/qh"T8P?L<`LG\A7f.>>s6#:Z9=/
SD,W\B<4>.kh6Ni`R,T)"Z>"Q;j-$RJQmBAaAr^6Y#%Z\CGW<iWf=c)fY=1g<cjm=r=b*c1u
!%/A$)GY<[mDfj8o'$#9U1MZLf=GfOq%]AWJ2%]AX6FB*GfR4$qA!0oK.kTAJ1F=<FAtcT=CoX
,1?Y7PZ'?0pdRb0Z[t1:dfL!g__K),2*V"N0//C>.&f`l*@(4[uVdK7<U&j"U_LX;*7PeS([
["L`e_q1QcDP#/:-8+HVSsA'HBZ"W26i^p0m76Sg.(N]A/5R]A+1`kW`JtE+DFj5CsCOn.N<YX
m(Pu?*e#ebYcpeS>6`)6$XeNjB'sTmE10j%ab6)2)8T5Let[Sp1fJaj)*aN>Cq3pZ&Mh:'<*
b'b5?dt9E-$NoTIPFK5ug.n8`UqHMT$hMe3siUe*."VMSiD"<U7_`lGf$LeH$pE&N(3k?uO_
5=jD'Wo(ele;/`_L<oDU[(s3K$-%]A0#$+&&W]AWm9.*mCS!blFGd2@&=@D4]APSSUS/NX9.UII
*alZanI>FSERq=er<SRhb]AtlmnBtV-Vs'\@(EesX.mJ45]A#<6'k%,f;a:!n8OKi]AAXl,@lS)
L1T&%$X$0,=rJVI=;kHY%RI'pmQ;kbV9g,W"R__p!ffPE?,6A`]A\f^<<kbqYP@psVfg)^c,4
VdUqJ\[jWoCr?FuG+r.+gLlZ?K4Ypgm):IX8EV,F/0cg/4]A&4uR7j\QOU7h"<$tF94Xu8((;
]A;)ilX4.LKY+5c*!*nea/M?>)`_7IKF\HGK'm3M6FjV[.>>*+o9-MB._IrH66.V&E50=hE=g
oP&`W?/''Pna/@t$']A1n7SIa(3SX*1d,500]AHZ$JJJ@ED-BXgWoN"Fh/\?G%?GG`tZ:LT1;8
_;P&`]A9XKo1>Ch$dtA'IT,'FipKh2LZLms?'sda,*Yf^9S^&$,):ahro9#cY=c9M$or+e,q2
WV?JOl2U(UG]A#A20eTgjRD%Z/FK]A.`EnX/6b.*4$T6>fXKh\THqk&a!Q!]A3j>'-qG5#:I_Ir
a8Jm!:-`UkXMH]A1[QZ-dlJj[Y&,0uFd(?#QKIgnijd'l##M7?sbGAh]AQ0Nu+]A,jA1i!i7I-J
lR9HJ@A#k7OO8^/Z755P/`]A0CE(bf.&@^`d=]AQlF?Jj#i#7EaMn%gIa)274[@[#>p!sKjIqp
Udr[FI8IY*a_,"(shXW31$k3PCI&@d9U^UC-'m`Yd;?#(QHhQA[NOHUhVfe>H48u/64+P^5,
LsHEEYhVNFj>Vsr_"7:,09^q2k!B#$W#2KHf87t0UK8R>HjR3cu4qVQUbrcDGG6)BlX+D<D&
!I6H!1@]AtkpGH+%S/^IRtn8D:Z8Tl]A6(YF)'+8ofGNoii(k,GjhFX$O<6rAphXZk3_Wj,K[_
,WL@f>*0F9&_:C(Y787Jh_u;%R7\A_$Y*q?aT\`h5m*74JG7Y&n<O=S5;9l7G:GX!?Zegd]Af
D>d4:sC:pY_Bq\h8UjSj=mR-$Q/hBMU<F2$tmL:!]ACkX[YrB(f32=,0PW908aOS9\!>\iUio
qUVO>&[XHB<ls?b.1udnrZpCm[k1@Z2.8HE-$qPR1Lj==R>,B!J]A(D7?bNWelY.GsM?$/9ld
q%M/cUtnr_l"^P;iNCJ[]A0>4PFsD1k^.e_lM22[(PYSQi29MV"rPp5;g#)eC2Y%T"7%u!I?i
?[(oR"O7\g6uV<f;)P_Nmha".]Au$GX.;2S>#rTgdU)(RM\)/B@Y>34->DMl(=bFG6OrX.q+A
E3CH\Rl)_dj:a`TftY+1?2FCl2qss6<3=dMC<KBp2Ch#k^I$@md@?.,cCMo+[Q-5P?@E?QTP
_K@C+Z,M#!cg$s6FHZW0iZ1/<t5a@MZ`P7@_I8!5!sD,W1%uY[RE1eSb<GCr;7e7b_O(rO.S
RmimYU@5^l"fu6J=9ObHh1S&>_U4'H,TE"'JCGu?'QE5goAAhn(fl*Q3FT%C9X/TGP/Ef=e%
.GjM-:X.A?98(4\;^+bVW!PbPe(jJnQQ]AND)DC)3gl5am3a-p9-ZcA@0bGY)Io=He`V?7+e(
Q^/IGdJI!B_k?bl0k^P^D:2[;h:m%C*(CPInP]AAl6u^)JI,7s[\qUTQC+m!O@Qe=;EDLSg`,
r&Wso0pIi,OjO-J;D\e-%5E,H0cr1aqH;C="KQ-:*c!b#M$J&@*<@$p9RrA#Mu#^#kY8J4/=
VDfRLRLD`,3^3eH'QQ+b+&[6=#.c2r/(Rn'_EYn\2eRC.`fgj(UYP`5RW(IUL#caXVQn5&[k
Uf-O2`.=#f"2kE%pqN;7oRVONjVq)$8Ej:XM!u5WsrJ_3Qj99=AFlH[)"j3C`TRqe>^@(Z&#
pkitUM3!1CN!,NGOj,CR'EjG(K^j4`LeiNdbC@YY"K0eX]Adr5\p8#6KAI0mEUR9<q6_Y'SQ[
lIb2Q]A1=V"WMHMQ"'1^9;=ZJV02(SYo<'scher's8ibE32j9<AOX3/Hpi=%$AWBPtX!-*&Y_
6CM;/@D_A.]Ae:&Pj?&>c"42ST.e1RL.#:B/_E%>M1a<"3k`BQsQs4`+1'UBtN*:Ygpl&b%V5
?qX3.H>E4M4r<Yui@s3gN=/L+=F.1"W.?6i%M5mm!EO`'8%5s&!Z`U!?uaJi0ou`I.b`/l=/
YSX*GmHC?JCA6PJDib&WT9Cs8'FU7)cljJ$A`Uip_O2gtdA>QB*h&^7QhO'mVW\1St.tAHlp
3Wa,\Tp7,!2`sTQade#:eBOhk?c0g5I;HE*Y*`jSU'm\c'u3ko/?MaZgl)T4l1[]A<.9bj+n:
F]Afjlj2n95NMMi9FGr<K2ekY"e<0mcc?Z(7j!60+,I52)dMdE//D2:EY/RbGJqZVh)M!9B,k
5`7ag+S(+fNFH>N.rq"s1f-5ZOD6#l*-Zh<-,_5=1AJdIMckDMa$h4_#JbXaK#4ld'=Pr6pZ
[NCR;I5Gf'R[ff6^+WU$R`k^=!q53oW;U)%;p_0;IHF]ApElCB20*:N'Hf7Ps34Sp_,TeS5IG
UN5c+kLd^7`'S@V1h?r*;O1Bj<1(@XI?HfQj0k$eg[=r:#rpL#\4PDV&"D3bl/L`3K/p(TVE
q,!nS9di+`9FsiEIg^@!MjZFQtAp0TSQ&-eSHpd'0V[kAN=(@c)mDL*,[U>?E/T;5f>eT_^-
Y,H6UN"A6i+1oKhBV$@3%RFNB4m8G#<EQakTEIJ0-*fp.m.=9.(j3e7qZ3L+]AJ01ZQ>m8Ig^
QQ475:1VGeUrhJtKB6\STd\S5H6#h5Sd:UurF$?,)U_SN9U/?*,Llj5?]A*onXNG!55AtpheV
@Aj]AN+(7eL5"kW)5t;UQ'hWO*ao\d)PLN`<;XG-,bQ4^B0XtAp`o!'n+Z0Xj[n_=l0(ulsG6
L_A[V1Y+BWGihsr4a/OjIRE3oL51U-*6KLUE)f>DKp`E+E,).fQDqaoR5>.O!m.4mXCV9+k-
"cba".]AM'c"E,ME4,IC5ggg3!?!-AJkh2i@>=5g>o@,dAQ\:8[bIt]AbO$o0-mF-mkh,8#\S^
k6M:LJ\<G9/_Sc$Rdi]Ae-J4kZDnET*%"%5/iUH:VoSYN:.]A"B*UP3#>u'A'Gi6S5o9CFe3=r
774%=/%cYBi,$_=`cg!0q58%+pGS$K)I#YC0p=[DTD3ED^lp^t/ARKp>s4R;dJEPW:'[4_b%
hjo$BnP:itpjFS2rpa$fNqfSG!"G>!%M=i/ZFcF(n(fZ,of-*?49nMjg"iBlo/Dd`Q]A,ZkCo
Q\nb>:i:p2U9E]A!Agq4>I]Amj&uFrfR>P]A/7Ka&uj6p^C.^1LIg;(GacD4l6&,hgP1aP4[_h\
)3u`IG/Ni=0O&4JWN,Dl0c9!^ZU^/e>)X+N0q/<"=%^M$`>dUZ#/7440FN>.AEZnd5\@dj+E
unF]AN/a)]A4J!25cs!N:0DtGc.io-HLt_?bEqkC=afB6CW2S?u=iVqp&?:,<ig,UOrYL*THks
a8uh@ZHahhZh4TUYrY4%+=d%]A]A?0%9=5%`?e^*s]A#&8mFQn<43UL*e!8p`+o]A4%u"#Dogi(B
<XLBUP6X27P_UR2(AE=`JOT?;(1fnt#l<OZm?Wku'Ish`hk-dNq4m\*s:mMR/TN'Wl((bOAS
c1Opq`GtGjb5Q0ATehV4LX9@$dTb^&bp&[+Xpi"A@1lLe$EoJ;kP/\AP5nT.*E*2I2XT9(Zq
:pc=e_>i=7Q:iMr)D01O4-L_mhaHZ.Sk[0WOX9MEOY\j_I>u.9D*m<#uZFVgM\lb%-neO:rJ
Wf-T>l4c4bV$ES-m/(>PYYGGFZK#J1bNRc5i2X=Ino[P9_lP;5`cqfKN$gY(4MpVnuN'uOc/
U&..t."8sAJS$.&';itBr\/-:F;<!T.BaG?Z07;U(6bghYXtS>'"?/8BPfZ?M_Hbe-A0d:=V
TuX$%6l46U;dkk.V:fNp%te2WN^>pYliNhpKY`YEZ$Wk0=U3Koi#^/;Y`q0YKu2N,`U#jJBH
aOrfZFf-ToqOX=MG]A?<5d4T,D1V8H+H^sC!<`BO=>Yg/Pl+Nt_6d-#(rQI.>I8ds`aB0:TW$
X^Xe5m,LYDY8VN;YAK8j87F:f@;F-4h+pb0iFJ@BdOs(-6Q'KED/k%9V6f!c=Z<,U?B0CN8W
=1:RnnsdP!DSg=Ykj[>UnJrHC(6T<q_,\rURrhiFbG/3Du#8/0J),RF(:07?/.Ea8#l[Gb(3
.Ag:d/Eo+CPB<6;%3#(gIO?G=rJ<!o&5-&K3L"'#RqN(6_,r*tN9@S,"a@*M,j]A(6-i7g$.*
Y5OfA-Y2_c]AIZV>Y,7(`AKmVX)\U^h2ZEGpeQ80AKJ#j+K17f[[q`M&Sj.\*d.P@2lq&2N96
YY-<l`<'K7RJ#FDOmT\C065YXc:`=t6=EWn_bu(&5`maq;i1@l$!4&*h3`K4ofUmfI94Lg'E
R:7R%VY2W_8$.id5m-V7^0'gG:9P"gJI+HF_,dG/(^"5IHoD9IOH.]A/pnk]AJJ1.U]A]A,`a$-:
!&0t`*C5%%lm)g`W$oLXM/&:"dMe\WCL$lYip;Qo+"@.WYS_3jd[22nYGlPlG6Whh-!$;Q&:
08iIm9`6K[:?$7b+2;Y!>uLs]AcqcZ]Ap(&&BZsQ&UmU0#1PG]AQ@5$'=5d0m/N!?1f`?A&msF;
s7Ic*5\4mgP6:rS)ASP@j;b#Y14E6d]AnIcN)%ZK0Z*7DG9V"Y7Xh`l%`\2(LMLN@b@;.SU.u
RjY2@-`*'WVjg[OJV`Xjs3b@qs4jEi+f<>=S`6%UgOd7\0a.Z`A41hQ0`aMUpY1VBsq40XKr
<K;G45uuA?;H44g0>g0=chK_5UJVVmd/7eT<lI&p\;jI83#&@MBq4ND3-bI2*pPACQqhdVR'
@XPVHZ$&r?>r_jXqW_[u57djuVUWFjZgHebVg?q2G#$4[lRCB4t]A1:J_78pT'SS\cOA']A0ZQ
++;PJEcbiZ";VMN2[U1u2#c0D?hTgkg[Tu`;,H6^WXi-,9sE#:M<_VC=Q*H(EMIfK-=7a=LE
U&Rh6=@L:pd6]Al1,?X>&G/NKtcmV>&YD?IA]A/kIBk$iWs,9J>\;?\gRCVBD7>MVq[8f7LjcU
,"Ku3#j+6J_g3Pq_g(4<4kp%M08<)]A^JhND:#AQWGW#A3Y=5n<P)seg>Eu(WI.ld,`+4<Bg0
#d.Aq4sRm+0#CH8Z>`]AZ=*quPp]AOo=*eAR;Z$F.I?RThV"d>"kpNo?4Il3^C[MEGX_[Efpq<
l^6:;"BIeoZPq]A-*7=3JmL2l#da#(VH!eR>UtFj,MiW+.m-VZrBC*t0QcNgWB2Nu\YL04m#/
FnEBD@W*&6j."IgQ;RK"ok]ACu>33/:a!+9tV:MZ^d[a!Vm[4+=?pU5.dD-aQGqG$hlEl4)2I
D%>WS1ZQg:N<D]A?\VY"Vdm4I?ht34DQhSp5IU(JC^F$d'k#)&#p&-/F8^u7KX*,5[c-pO/kM
R'a*DNMS%]A.iK>X9$sK@a&$Xf5ff8Ip5id$?KV:o9_?b)M<+A\q4r[D(O/)Gm?V2;4>R)m`%
1/+p5:0YWAhEiF^0op.Em(NRjQ"P2DX'J+nHoEP4I]A5`<?VU?&UYWW1[PEuabW#Ca`e*ig[b
*sTr7;m=rbIql^(oKJ,M2^rV_<GH:@8Pm`GE[^=+U"'e`N&<&SUAbq<X;\No:cRScI7<hJ4N
8oc?TF'm\cZ6qh'K;.Y=ABENQr%DG+WQJ';;N(8DU%%QP,l!"JrOrplAf78-OT)LmH5ALO1+
8sJMc$&dDQ5U<)uZ15*qf1@1?kbY@$>H%j+gV=iU]A-nPqhLNBf5Kq4*Ro:aE604KAPDs`dY1
/O'uP292[g9g<cMJ&XL5-\=)qbesggu(que&'`IaTIDpB::5=u_';$&7YhY^Ls)K)uht5RQ@
E=?"SUU=F(4dJ7O'>J'V5[F-rK=&+9X%oLK\UJ#@;ENi]A6$.,FH:ASX)'C?'rR!nY-H-/QG2
.@AQae`(&7l\K,L5(b*mb'#5W#dH<pk!Xb5l/6)dO0q[=/qaDIPmH!jpS)dK6/%qBJ3K3&:S
o\.FjE3k&QFm_@#Eg4n&Lu1A/&P>AA,/mekH5sbADsQ\QfFX8o)`8SB%X.G$hbt]AR:3k5WaB
otRYq#@mqB^QVnWFEmLJ(FnXUL0AC2E<;'KU(JfBJrd47ShG6a'fMpS3*A":R,N\Lb-=G4/C
%HQ4i51sKR`"qSIIq[#bQ'sOA>FV/;2buGd?'bm\=4t:)t0=ug,&pi:sgr\fqh%A_!&s,tCc
5go#re(uVqk&lBgD6nuI(VQ&pA68gQqn<WOck1M;7._YWM-FKg:YiE/P.a:=''FI^$jhQ[;t
n&?^krUU3bHKLNACB&J%9YGZ4bb+-IJGs,91ALDiDc;R'!.Lb9OIoB#TcoYW?5":s?NkW=.g
*\Bp0rGWhn3/+>L5d.!._A[WQh#!Nect0ZEQ!cD2!B]ArIMM\U\)&E9rbLE2Xf::D3FTuR89W
&nb&lkmm=O@Vpr!h%:eGUNBX6S+`cu[CV1=)Omp50:NL6Zo(R,Xe7%o3V7:bUJWn,O9KLqM5
Sliq0l"cL\BWQ<VVFIrG#@%Omr7/(ArO?n.oq0ZneoKAfBN&@+sUoma4*8C$M6]Ad-==`)=UC
=KWI$BSrmhrlJ-ql<Y"lgN,dWo!kM6di56d*H.l$cguq\SSj,bhf>^aRnC2gkC]AEXai5<_>D
cSTLe]A.Zncbiou5I.*[R%eK8/;WHP`AA%mD'Xqo#n[]A;k.C)t\P@6>"6nd]AtAC,*ZHofgW5t
r?dJFY0-cjnPWqk`ZW\GkQ:UtiA!6,Z`MG65ARF7_p,%sn&beQCpGG2pV$rQ;2R:/8=+ODhF
YC#*U+H@$"G\OiWC>&OlthA8cB*O1+#<[TWoCKJsqbD7lqC\b7W$97b+`S`6<`QO+QKqqp3[
M%qR&AQMq):AULkqB<*reKa.4XbY]ABN[tbbM7e.u$G6=_`+-I5I(IR]Ak*bTP0%lqa[Qq?(m.
Os_B=V-as$sdq=(^`O?="6=k"Wf]At37=rC[H$3W+@rk9'>)\'8r@4b__L8MMs3lg,LnH(Fl.
7#6j7.0\9MZu;^ir\`Z!Xgel,`SI=D7o,e-;cqd?KO.`NKWrRaloq!1uphj&r2A3EsX1/k:3
]AHEU+;Qj&egPWQ,0"6P3kNQSrHFo^hOaV5j5He#YSeE0#I;>D1X'E`%cXUV2<pPnqh9-)HHP
W;)*>4+6_gbJ0)_GpYp$YMrTUiP"4VAqR.rB[DT^i([89r.1^fI]A01sk?X7d86)Ii&$0nXo7
D;]An719B=uMcf`_$cK$oBF;0&(W8++C1lu8KjZ6js3T]A9!^Y,&cPc<1:B!QQ18sKS*/bL->*
d;Fo$$ZDVBc@YqlQ:^e_p46^D;+nLR*+n'Ik06\h.U#IM]A(UVC*@:K=Z15:GlA+F1Q!t>7kj
Nt8g=n0P&P1Z@H]A+dg('>Ai14JY"AG'j;ZEq?\/@MN1mm5]A2)Z(t*.NFK\Q94AM0MKUU=!Ws
,XF#bnF'KjVGY:ja1o)b`9K!bkBUZ@AH@D9'j3fr5FOb7=K5>t[`g+MYq3te'YP6?'"*bpf\
60')uh&b4FF'52^Ag!5n_MdeQVlUQPuQRdl;J\r:_Rd;'J_BH^_6,DB-,.'S'\b\T)XHLDYW
(Rr%%A93&2EL/kB*a8rV0<G<lb5<;i?gf&1"[I3kb'PZ$(:Z>iS3?<^Wjh48P7*l>^fl;[u!
@PrdR^]Aru)BD*N-63NI7&*YoB_<U1p7*`49TrFVV_70l3JNeuQI$<+S[la>U)?uA3>JGBj1.
Gks(>p.5]AeRFM8:(/&,E<P,(%rkB6-7-F7idLMG]ACHr!=<KNgPG2Qu"3=',?&2*Y&"7,74(=
OO("A*/ar>D%;C?>J\iXn8=Zj:;=&EFct2Njj[I+E("8hK]AH(M8XDpd!eWsHBnr2VlTpd*l%
HDd[fjNC^=-JVs._=*R)&3^kc=5J^7:'G`Z&l_0NW_k<4f,nK2t.ld?6@\.CrA'"0e&pHsXH
Z6r0qQ>+"c#`;mt&a!W4..I>N]AK:fai!dg@ciY%E(2qrq;UXTh@p'TuKejsIO$^b"7-P$(B9
`6gC"Pc+MB_#ON^MPj-]A<7EjaoKhq(kQHX_HFdpM6q_)npGB:!heIqCc_,a4^0,p>??,9VnN
Ei:-m+gVNpMP3!"ME/r.1WoidmDEKkr`;ctgTT-_=i-sHYel\q0m93i&+[k-4':T)GiY3o%4
fjLrijq]AJQPY6rkYu-haIVlt9&uQrUM(E(F'1dLdC%/rEY^l-);9-6`hj[Ue8t_W8%p^&R:]A
Y/N%aj-'Tu`9kWX!]AWON'"m4"]AL*P;%!P!jtS#i)bHWo2>a3GI&S*]AK@(Z]AVK^KfurFV"t$E
PIQ<V282K7),DCtG[c*ij(7U-U"VgL)&mH`UjiJL&i,65Wc]AN_nnPVLso8=A!PbZT<iGk=@s
,els,IQ?OH:eq<;?m]A!RL3:CT*bF[X[8XSk@K`a-^kb5J'dX@X$;bp<*^W:>C;fIokt7,U5L
g)/[M4L\.D8t%X812?UE#A1sk=5PZ[6u`ILNrY?.(./CsU'cs@A*ai?[[O5q4!f$\LKd<R"p
E=/qIm7EKCH.O&hWkdHmaM(bi-rnr5D-LitD=p<-/l'K+40l_oT!'2sE/OlcO%4<*$37<fS;
<nMl^I?GQtf]AeA;^5>Am8;QDGSYrG]A>7lj;s.Knhb!17+2g/bpti`/+gos'04MbSEY$1I#EC
;hu-]A+4$LaP9NeTmD`4Q/d$iA^Z0-N76iHVf,X[`Wp=?m\A+b'FfRr(UK1l\D8OIb17L!a)Y
J\E3&j@na.N$eoI\b`Q>?7pUhIm5IOC"(dI\!Pp?JmW3$7X0!QN7Y)H6G)gVr<QU3`]A#eYl'
\p_XMFD@)f7Z7r!IM\$g'JZ!lf>L3lg#;]AuVnV`XU4Te#C>7gIZ(PHOHp\b-*7=0KO3Nt?$#
ab[<ob3nffP`14A+JLWPH3P)P?ub3_9C9<h>9Gg)@4&U<qFMAuL8RUpE3%[!5a=aA'\@!nY>
LiK]A\J8N1\[O@OUd;(YOj4m5M+f2.oo=t/#h$6hSj<*??WN1nRmQNl$ou_"_]A6RU&.cn69TU
Qq;dqP_!036?X,R"Gj0ESU)lk@(9^P<Cj[T\Z??8;@(88BeW42l9L\J\=,R8*#KIoOMAF5HX
m8#k^hUp[CPQ81+]AKK&?A<O%b0ua^`?71%*A68BrrlqI>Kc^6n:T>YT@N]AM>;PF^o,IG#A/Z
7X0BER$FY\M:(4\F_ih5!"!T3+?jl%'($+"?sChja;Gf;*E!;gh?*u#OPh?jBmV8T#eXc&Cc
aDBX^B2s0^=c(2VL0SnYmn<8tZOu(PXF1]A,`:K^X,Vd#leZ$Y&83#n@Q26Bb7\,?Sj]A0ms;?
_n9os!ELX6$39U>G&3HTdYsfPH;$AIR=mgD/dO4qrmc:O<gA;%KIq/I,0,W"*&YP4L<WW,3a
rGajDGjJ+Q5%%+I^@L?.C^no2J(Q)cRMltZq\TTbA7WFhj:,(,jC:*LSio@W38/0E8go2Le/
>Z4iBpFl_H`,b8a>6n3%W++j>87aj&*pAc<]A&OoO1!QLC.kC0s3m%9VPEI2**\@sV>]A5n0/7
8kjFu;G?h`K3j`\o+M%B]A%o)aD95OZXpcI">^b!8$0GBdgRXlWk.mq"4X>1?pI_\m;mP)_;5
@@WR*1LBhT3>!0USuMY;UI)t(^ur`G"PJ)baQ<O#F$%#OS%NU>2Dm!)Z?m33\AoS-b.\Et9'
a]AibjujHq)+(C/4V@q!cZ!j$F%FoSD==YC%jf^1LC+T&hLgH@%ut[KX7YKAVW*OLW(^RG+XR
\['Z`S9Db9meM,6=3U>n?F_@k?JQjbtbUNu!p/eU2p1X:U218_Gbjd.5?6]A%6!)9YP[hn-j3
k*^%?@+jZKjpm>/?+Olp^^j'1I+.dd"QmbpfUY\$)<bi&A_prnMo8'cN#8Q8@gcC&l\X2KaL
3#-ToZn_J9oGL?!PmhAqI*,>/;m^%H3IeML+]A&%gKhB6Rk8+3^H<V@bJI.;n:6D'jjdD1$h3
BuBs+iKYD*0F`.[91SX=')ttfBn&!*3mDr(:J+9*(09<C+l]AHLjWM_+EkaI2,!Mp7$4sp+j<
\IZD<<lo[s.,o)9[TqMNn'Rnhh3gj>;gFFe-#IIuqU[SRnr/4^8HbYW8j'OIYS5_@7Th\aA$
T):Q`oM>+oq$BMD\)iUVS5[Wko=59NfZ8^mUF,qfir;%6o)R<*9_>a2<>dk8^I@^bG?F\uMp
lBj"$`QUc@=QoIJ(to3O*[[u+dh-":^;KHPOY:Q:o(_96$)it/%b&)r.Kkl?rZOc=Q=!g]AV?
\oJ9AJ9Bk_?&Q5O%hJ4N^d&';qFr[]Ac9?^UKSHSOHXPOa6uSUgB-q9AJ-SM2:K]APGO/FqEZ-
_Do]A'JpuQNr7AlpjE-GDFq<==4^5!G)`#i@iWWDoJ9&B^Q@.;/h*COhc2BM;C"3]AYed%CjV"
:,$GJ4BF1\MZc</mM)_nBE\ltNI9&o%o=mdFi=.SXPM3^6[`mk*7`OBe`GLM]A_[(,gdi0>Ka
Rp7E`ob%p<<R0lRbjZqX0Zj6Ka#=R92"X(1A9heS8^;u'-4Z#^(q9_`!#'"MO308ke^hmAJ-
)19=nBrqf8q!/Chk%l`rj-e\!s*Yr>hZhNXQD==;P!4`K\Dng[-:\Kj\X>H`qs>S`meH7X(p
(+E+NBk=fo_2<M+EWn&(O^Su6"?>#OTSJFtKLZ4I<?rT[.Xk?12Ql+_al%6MT,MB(WM9WfXm
,\!gUX75h6j"[0!s+DEt1d-D(f]Alp&-%pgaYMn"GKX43.@/&Kb,)]ATRm#DI&?i!>)99?(sR1
ic/l/rQMDBjT_h[Fi*-6TjleitIFr7[<*Nd*u/VI/fb$\&,6D\-"BpDn&B52YTbF3]A:@KjV<
F+=%l41;)7Pf5_(cIihKEXfHj*4L\/D:G9O$4.1!0m!T8f)&2lG`.II1fCUgeb\("+MNCnt%
@_s5L`uOc-h-Ku#e5.^s!'+J(.OC64P[&IVCZqBYrL_SIT(sf5PDfJ=YJgO/CsFEOa'C^.bi
]Ar%bY7]AnuP0*f=!0b`tm%:A?LBk#eVO[8$,fOXgE6e?`?r^J6f6\Up7R!o#45u*?L6igBu[8
BLB)=&O297Q)"4\3QA@a[-S'0W&tO_%#@TD=.$?>$=jRgD7<(tQk<[WfSM/G]A4fi[(n3&RHY
hL5Ic$-QMHBG6nr0l73LW]A/XIn8K,d6>--r]A_&AF0-:9$uS6/1mRZOZji?))5*[Z!lgX.oGe
u$OZE+l(di3'luHg3STGaY3c5CF^/Y.[.4T;ASCjG_T'2U$l)m1!QpJ^G9A,*1Z5ffAUYBO(
jpP=Sl:MuG2XB:Zd_-ndQ<X"O.)^A=Ft0YU6dqgclTk7^MQmSkh8<fr9j4k,95<24Qd1hYMj
MnrSKGl"5Ia.D%k:mj#[""KXflp!@T.$PZc+3n9,/\iO4M-i[k/c#rGi(8':mQ?=#r>C[#Rb
f#Nh11ahM]A?+%P`K-k7?B(nHtDT[g``p;H00;H!G]A>i`skALN#GG_YmqC^D?m47"aNaaML?d
r%THrOZ]AVXL/0j#9+cX@2[m#c@8I+`KM0U?)b)"4lM7"g$/&V:g@jo.8KA@LCp)aB"A\HL-G
Oe?L7%-^`\-[:?XH(`]AmYi@^Li$X8jsPNJ:^l;T]A(,"lOfDsUco($H<`VJPs>`019nc39_o<
R.DR9f@3m]A8\>DD&f3>K9F4;TTZjsI@["RBtG`SW]ATS+4-!S+Y5!?34`oGVrU\?`(KMWYb29
=m\"!SkGbq.%'*]AKtmn9EN-;[5,lq!JVLJkV6mGtrd:^1DjRW49/IC!\?jA`6U2O+^Yrh3=0
^=0<E5++c3;BO!SdYJ87Gpd4j]APMriiJR`cFIA^UUApO>UX"b7_$Njb&4L-O0i;C*/=@]AaMG
#?cQ(Xh4T&#`;@(u]Aq)J)ULQIE#2^df#%.^kQ/-E03hFVF(on;6[l,\?<LS+ra:/&XDUAb]A4
-7j,.-.aYbXkClb'bo]A6P9[fqD2:*hJ+Kr%I0:TG8UWK\RNaaYQ.6P0)@ul]A&:jcMZI2&`.m
n=Pj.Vh"Y?UHl`oGobR8[2A@4PJiuoAnes83D.q/mf_u@LbLt)nQX'o;H/RR.^=C\7Dnj6#G
BO_dgeS$MR4bj8k!#`.PjP/JoNZ_N;.kkA0n>p3>>prC>b)^2Ghk(V5%0kpREh\4IN9-f:NR
nW0.4i]AUWR*,>URP7L.AroFOCi&oY2Q&uYQ.VCh3VP%SEIU<[N$f!iu[o%?Kh=_T1Pl<.9=D
-a_+/8\\Nmq=TkPNg"!iuPM.mbF,kotV]AP/-2e5-^`,raW0iF&u\4'K!3kE<Emb!CR\L)dTr
,FteN8H8(ZVB^tM3ROfTFpiE`k*+Kd<ieVG<.-P9S&Ba993,4fO&Up['ag9P7Lpp=Wb"b<HW
/%V#7MuN&>fn/BB-7-cO?dLe$%V]A/,M:Li9$I=k44sg+H+3@bZAF<uHgtiFEUHPp"4+RR&lg
s^l7Tqi99V+*PEr(54]AI+J7%;@**!gd!M%aBV?!AY<rAlk.05?#2IPFU7kpW+Qq8dhK2@,rb
C_]ApiXu)-G-I0iJLa$)##@]A>P#Q4\N2:bLe&scDZJ>li6UX7KfaO*8j;f#+,O'Hn0+u%cF7T
D2N+a:<-hOl?W?N28U))mPR?Y`okb)k=7,mM=?DT-fU!40VG%c=bNm?:<K:%UR64?jC3;pVE
oqA]AUr`GF(=7f["Y==apY&$5.&Nt&N3#uke>=-Uba(K"]AZ?K.XHFMFPbs54o&Z%VLMm79-,Y
JY3#).rqTP<_fZ#:Oai>M;Ae6sX8%)HD,"N_@AC86,'C)6drj_.<K*c4DHC&J7reRc4p%@Pe
R=X6)2+nRCNbN)d+No\LSVhoYeiqWCZ/m1qio[q1_`hcs:l@/=b8#?hor<;&)uZCFcGf[&3X
6hGqJDKi%C$0)?_e$:nA>(MgH5PN04AhH.h)YXhAa)`s-I(j=afH3.PWN:fibjJk2<CqZ;Rh
/X09ZuU;*B,U!f[W'$lE44&e?r**#l:E=eENL]A#'6u:,aZosb67<c4En0i)*%edX_@P&hW_(
$5$@P+l8bWh(tNA\`koWXBD!+8H@?N*+?u0qV!";L]A.XHP;0Vu+HVL^\9UeDq0\s1.8OaVk5
:tB<%l-L-b\;@h3^D1C?3?p9O8dn*;<nLZ1,NZDNNg<W5EBO,=JJFdLOf=K?r''`]AcmGIjA2
6WH3FFk%sp.cG3)XLB?@hML%=35^I@oWpJ/X9Q)k32Fbo>Il0Eb_8PE,r#1@16-ufud`"@5Q
,Jb+[Q0a\Obo4QN,aE3R-.3c^O9YI*343pe@_Z!4ek7FO!=I/\)7YFA6c;+>Vo-Nphs\@oD:
H-Tf4D&_!;1'?dC#6Xd\n'!R:,eUp"2T6H"Ap0?Ie[*OVQf\%G:,OU[gYJm1#,7Q^._Llns_
`,pug,A'cF:#M9%+_2Ea6d@eX>K[gdkeVV5p*6d\IP9<:hrGf[E>FfJXoQ#^qZiG!!rd<>e'
;8Fs\"f60oGU!gI5On^(]A@oRoa6kJWfc(3@Ieu;n+dTQ+HY-qHY'`OV:VP&s3$fqQ(JhSf)R
n#jo7=omtupB$#%3ur)f]Au`!5V3P<T?jV6g3WGfWM:K5-pjSMptjM`>_6:$=d=I&0PN?_a.*
o_i_QZO8Er[!>KP&TL1>^cYKdm;bYoena"N*Se;GZF7iQ)KSU:j6*eG_EOH-k9HJGJO@h>L,
&u\6s?@"G(OXf0,`/VfjF`Z$sCBJd/SZ"YPTVOhlCMdGSj.R+LJY]An'c$q[#L']Aph\rh9*4d
bcI^A8J@4>NbKYo9B#3hl19]A;?n'L?!MZ+?DBgS`$3?,$VgonI%=L+7'g(6%u&.4Q6kH@8:B
'5gLO]A]AZ:9^LHQ\Nkbmmn9+)[f7<.L!^,Cio/->4m(C:$emQ$59:'`$.(^XOo4Jtq.,>J/9.
NgMZ^#F>jDg3d0BH=/_ZT"D81^3ZY`u1X?#09MMhCO>D!-+<^3*unffaVCO5FmlH`r3HTW4h
4EeL:Slr1Bpfus&quFlc;ubYnH)L(g+n*uf/L>0B_XH9$3k^Aua<aL@l)-?:!MA5h]AS4f:f.
r?0#UP_q=sifL`HY\mV5>Y'6RE;K6jeg]Aj<!Yb+(jE;4I[:=_`Vu0KB^;h:I<Z@DZ@=bNd[l
68SaYMki51@!nJ35QZ0NT2p\@)V1+<.(aFY:+0/9;c\QAs4^6sWNV\o(1Y:TF!GqG-AjcG'Q
^MtOX*ZIcb5mJ-?T!*[h7CMReL6KI?61]AaCd9(B0Jt/%41Y9OYqog\&X7=2F0K>"%X'I@l*b
%:<sDKQ\(Z;bmMC>YKA>9^"eZLq7I?)[;7/r5PAA!98Ri]A^#MqP7?P'.pb<7(]AP7&f4E/WH'
<JAUuJIFg0cTH91>I.00)f7V)Y,Vmq=76OqcW":E+#^LfhuOtsBmAE*PZ`jCrRnLr.b/tiH:
`EfDr7eZel_;om8&4`#"S@H">&k5S9UBK=S*Xse=]A%47'k*Zlu`T4Bp.rTpnrQ9k"-Ymg3)(
J\BK\Nae1V0.lOogeG?e42Z?-c$J,kmIF-MiD:9k"U3U"ifb$6@UJRfh&8PbT?KInL7P5g2c
('+"#H3$P=D3M379l`^d6^grE(b=un/+s@2N@%/b%ai/AIhkY9GNkKG"i*-_%@Oa`=GQ2D]A1
n8UPo+T%+j8(rb!MiG,p9,BH9_*3G6<k5Eu+eL2T<,/sKV=ZRLC\VWk22j61p5Se@C&(Cr*5
91/)7:iN/^=_t`>6t-kcmJaZl/lc^^q2UQ*er.tE`_$9R0SQ['Mnl[pNJBj?>0:2fp]A?X`k:
O)FoQiBCJ\c'4%W"X!\3@\_CbkGKWNfR;+n9r.ol0lkq9cGS.kCiBEl[&,qDP![JO_8*'sR4
]AJ!V0HTcDFWW;:8,3;iMM$=3["\$BS=9bJXG]AM]AZgqKq4221=;W_`P^hkSr]AJ>2rZHC7[ih4
oQt*WoX;qBsbT/j637sN(OD]Ak8Uct8"s8Nn4W;84JgjW%3JsSL+M"2n(8XYjW+Z#e?aK<"V*
!#CE\n0hLr\L=5(!f_@N[s4uVg4#/auf6QosrNF\(57]ABr=oQQQZ@4a2,o$g.'.gqN!E:-h"
dC$0A"Vg40)J,*3'k6p(EuN9i=UgMc4_TW8YHD*s8laGf#/e`;52Yfc5NL5QGH<7mo@%tZRb
f>LF7#adfel&llE)tuq4sa@>2G:+iD0s05)=Y-!]A)a<&#Bh?H+A'=?0d$f@k*oE%i;@P.qOt
lnT_^j5+E8f11R*BM$Qp7`6RXV=7_0a*M"-n%[LL+A$71mNY@>&P!!%?f9>DF7@^3f\jNhW6
Kp6^[I8p;>a2uK[`(RZd]A"M<!3=npn[odnlR-`MXs(QN9BWbBm3>=OM@d)V:ms\BZ,W'-."A
20aq7u/UB+%%[HP(WPkIG3rn9MJl5ehiju,j[PSl8*1?+Y`?OYV/0)_'op8<@.bcoZYABW]AD
ai2jtTO+gVaq3]A(++5[Fb5+)i1t4:Gp%.)Oh?*`A2U:(;&rLV@n$k.AOER$`3Go6T"ZSOk@n
B+]A:qRi07;99sR`eIll>Idngr.c8<e\Ak65,Y4=mWjkKsa>[17hG]ApA?9t`VWEZ,pG"Y+VKt
^$SIrs'q`09HK_3bDI='A$?f+e/@PYZ;&I4`2eOd&Ya9cQWJpiDX#!uWJH,-Pbsn5aD.eXEM
8%H5%5G6Uf[2&]AE+\\i5M^@XL1YVJkX/UogpI6Q:MBc,Eu)Wa)[2RVFT0$AU,H>h8cQOfrAN
U1EM\.S7_\!CFW"E_.qn#_D4@N:B"4&8mG#;V?b#HPcM)hCMe&_(4rN,N?c#(6KJ"\Uf3@_B
Ak5Ys@+3>cn+RBf!3QU\`Ljk--6rN^h>LDdL\1Sp^m)Ig0?U,?IXSA%mX=M1]A8s&OBrR=Yhu
?%HmIpWg;,/ls>;kBKbu)";U<5FKgZJ9an;UWM!aH8Eqq=uj'&*CnmFV/*7enCBY]A\c/8ESL
<OOl`qIj#I`HpPp3]A_R'-dtHBCrKH8^J9Ce=hig?1n)'MrD&.Q(CCSB-+faNM'K%qTj?*015
NuA@r4=[K>K_IRbU=-,W'KgOrcX^iG9*[9dnft&TC]AAO$PapLmbf.i\En-Ns6I\Gis0bRF:#
^1\#pYFp#70gmsa>^gX"dEs)s_S:j_Bo-nC)A4bQUgIR_`Wea>H#eXmTGlZ_)Q#i@:(3de]Ag
+p>+BP*ArFf27_mIH8R);NG^F0gTR3`\1mqSbr4UqkTg"s"ZQ3LO]A7X"B%1.qrN`5;U9Rc.,
[Sr5770_^lJQrs&spiWG\]AKL>VM%_[YL4V70Z$lIE&@-#Q+u.7!@XQgZRLq-*d["(-qto^GN
HJ):0.S'mQTL&Z]A&JGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGpQml
]A:r;g=%'k]Am%JuPeGL0qWeCY#P88n#PA%3W(a7#;D..n2=IrN;^]Aj37if9jqu6Z~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="610"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="28" width="375" height="610"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop = '10px';
yjtab('GR0')]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[76200,2667000,0,457200,0,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[190500,3810000,190500,3810000,190500,3810000,190500,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR0' onclick=yjtab2('GR0') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, rgba(250,242,213) 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat;border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + B5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>全部指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="1" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR1' onclick=yjtab2('GR1') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, #DBEAFF 0) bottom right,linear-gradient(45deg,#DBEAFF 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#DBEAFF 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#DBEAFF 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat; border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + D5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>正常指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[正常]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR2' onclick=yjtab2('GR2') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, #FFE8DD 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat; border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + F5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>预警指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[预警]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="PAGENAMEALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[STATUS]]></CNAME>
<Compare op="0">
<O>
<![CDATA[正常]]></O>
</Compare>
</Condition>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[STATUS]]></CNAME>
<Compare op="0">
<O>
<![CDATA[预警]]></O>
</Compare>
</Condition>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-878336" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-14386946" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCW[+dok9UlFbE`TVIGgU.A-L:f%_f!f7?e&J,N<0GkPh@\f2uCdSF>"f+Sd;?MU/13BKm>3
O?:#`o%@&4.N$re"eE^%\&oSaX13c?&Hb`5dXpr;M&H^%U/qs41A#'6_W&Dtd#SS_LSigMNU
5a_]A]Ai^0KOpT6C:-e/>2VD;,j)Mnu7]AfDSVr'Dtk>/-6M%0<-Ne3d`:4Vk\_]AeG]A'&Lu+un\
$<.2L1!\^)#b/3E%0iH:C!d$rtV-.EIN7grn*;"mcT%HEIPsbT._5a-2m>PD$PbN?!c-o1iS
BLq&]A>N03I<4=tGW\dmsLJ%'uVC;7ECplM6Ek]AFs.&24GWf#kIj&Gu":PJ=gHb\>eUW"]Af2&
%TlEK+B^p8I`_HVh,3YPgU>oQ1iiQnQgcBW!iBMS8MKtLD*&PXf51>dCXS]A&j``%:6V3X(6k
;MJ3l1t]Adt7`s^6_S5fQM3U57d7qp9G7VM9J7=Zc^Aki6,t#WjDa%&br?]A/=ZOSR0UiiAYB2
Ab<gto=MUi<%GGOu1=dm#OKl5ff6`ReeR,YC]Aa"MgdHhjMojc_L=^`OV?D!X77e<?'@cpR39
=IX?=i6"YNc,:^L_Jlm2.mV)ZU8oVHc)YCgB%GK,?6T8Is:,&gqdbhccZak0lpQl;;=s&9r<
IgU&USCqpOQ-K!gF)a27`b9sZn[odm#%<RAhB[LsaY$@HPuB$Y76qg1b<Rk!"D^(*:^$+7%u
JC6ZW+aK(^j#=1=5l^[Fo3bM%8DmpA0H&D^4*3.F@,H(%!\!KHi$JHfQR?.O_<tt!@t3S@rZ
=ql;nX%<Lbn]AB^'!!1D[Zebi3I^'?#TA9=r\>m%pmJfQ=OOhQ']AcQ=0).mg$W9L5@HX[E?e$
`^F25U:X(Q`S/LTP1mhS#@3Jpl4_m^N'N*c]AS-kDT,a^DRGBA"t`2cLp=&2%J)@e!H&?1?a#
b:q19`2kJ,.u=TpkA(kZVfJCTiNTXrl"</)=HrI"XgH1f;E)4D,2u_#%[Ki4J7>U"_cG_-./
+-nb^?1(#Be[ruLgj'K+]A34T%clEUQ^E`_G31F/2TBI/XU#$*GKP9krOlFBDC/Wqk/BNJaO%
rW_6"TC>%Nk\jsCfemcrSA,:"A[4O27`V)3<&"&:N0j042coZYXI6WoIC,.5EIlhO7a:HDn]A
7MWB(VDpU&5k^FVO4G&bGtQVDjJXGf%d`6g[CT>#sHjD`EQ4alWu77am)Gd.^<HkVdjphAj.
FdkS7-`ACYdL>*LUO]AbD;XFe0I3bL;L'sjk]A`%YX2G#NnJ9Vc:3o9>TBD,li_)S\$b4?7_Td
$+Q6mA/!rKt>pTL43go!&@ISYD`u+Guo)&,bE[qX'*iQ<[CP/[?MB%c1h2'DA2aaJ1X,1Z(2
n84LgAp*>.0U/Oo''mm^5k:@E!pM%4YTe-*l2p?ph!/,Zfi9PSJMN%N8GO=6o(CJU`G@^Nj/
3"3TgB=u"bEkKtj<d'apJ*TmV&\1M05[oY^P`Y58n::bKin$%AIr*ZSZ)W]AJ)"bW",$pkH?b
L>3l)_tNH#7[cY42faO;mb9S+RVV65\"+,`t4*iGS8I58V\dn$09\JW[FP,CD[hLPcX'?d8E
O96[X\G@4[X\\Y25PnN(dKrR61>-&I>e[)]APRi]A-3[#%SYJNZl<=H7/[OUjLjW=;-tVMSE$q
*8-59ho`]A6+a]Angu3).Yfe]A@i:F4GXgMK8o_O_8PY?mBZ0F$673e.92[4^H6a0K7W=o>($(M
7E?b:6)@UTNYN`d:!U<;nhD;0h4AuhNMj#$iBF`EXEg$O!WCtZ!e$"Yr3]A[OIkr>e\C"L1Ro
R-:,^l]Ar%BrFo7_+^rkhX[84((-W>D^(co'9VF*h88pd1UC\3"&(9i]A,GrmYg<bK`F'LtDVV
G!U0r,IajbZ>eX&^.?>ITrrAB'YtnsHEGmI9hBTd([V%ptrpa<#nG=ZM8!4rh!tYAMBDNHbd
P%LurT=0:i_k%%C'M3;:9bI"8\@L,28h?C&O?Pb\sSt0DKTqS6lib'/ib#CET3tfe]A7MTnR6
Qp^U/nWkQ\,r`91j)/Tlcs$OQAtC\b910d)OIA$2a8jV<Zd.@<1]A_keDr<W0L>e+1-aNES%"
bn&8dn2&H_E9W#^q7-/Rjc"6Gk>HYMRQ5R3Et!fl`/l8e4V:udrp"#bUKXh;fW'V[LeIRUe,
)5;77%2goE86W\[SfPntKYLce_5<.g=6\k"j(I)JJS%Kqiqr)?S]APZ]A`6-'/=`</!)"L2C^.
OK\6\c6g0_R/Ng^eH[Pn*7_jMbO#`ll&ph1f8pV@T*a[XG9C)I@536Yp0dUoVjW/YqZ\=)s9
I@joBk%3q]A?5b/AHi*+-#,q5u1W0*M@RjmGV%@>&Sp#76L2EpZ?ZXGXRlPn"n?7`EK`K0UXf
onCS8AP*5Rb+/T-6sSc0D?u0]A5eZENc'kI#dEQfS/.lVPT[7lXq5_73g)A7-r9e9*Qr?e^WE
5E0<!/lJW^;VbsKf`^"CaGO\74Yi-ZJQNBPm%lFa2lEnofeN1'"Bc('alNP!Aq"gC#5,eeJ<
+XmDYSOHm4Ht_fCbSrb\Zc6c;IBP&RXYmke2QKHoc_i1O9F]Af/W8Em/h!NVX\a'm:Qajb+[(
m9u_X!VfR`p$be2,M\a;/4kVfRfKQo>iMHaprFq2#n>BrJr))P)WHW,,@@fu"ERF0[7'lKuS
on$!]A\ja(b[-W4Wc`fW^5kcl/Wb+RZSG.*C1<uPPRcQZ#=X4;6eb@i!0VNekQUd_!O6kMa&3
nnfc$Ab^dJaICVqhuX="A0U.J?mm!&4=Y,Q:`;h1QLWY(,0T58Cn=1C>]ARghCFPl[GE^OTNG
A(1&FrB>)mVkaeXSu6ai/a6s%Z_hfW%#N@:cg@5ko&'o%$ILMAHN62B/m0mUL(?%n@J(*AoN
B*:erT_2k3A`6O?W:`W,6D%;BW]AuUge.7M]A39I8=.geXdr_<fEA'*K;Q714D?;G+BMrQq?lP
78UVOX9Q*o#=giRjp7*KgWLDfMlu@4KaU<FkOT5QAS:HuuM@;P#PRKN=34g-us+XMs1g"UPs
BF4g)8;)Mh.PtrN]A\U0%'K[VaE)r?GfCM2N7dNr,a3e8ScIu)0n[Ho[:lbFN'^8rm;Kro@9:
hUq2ok?CSVX2h2?'=g8=meCDS@Bf'Wq5s`-3U*e\HO58g=cGD\I/gg4uK<Q&1kr$eSM$.#SU
A7:=\;ZjYkp)diVnX4CJ@1?o*Xi<1GEtqdp:Q.7Trp^+HWW&G=H;^#Ik9(K73;3pOOMB@,`c
n=Fd)Z@s26s)C&B<K<RYER^S,p.gDPrQcmT1KhH'<YK'(+Sq6h\e3AZE<85,hSQMSMca6q3)
+X5,S9cMIFVp+BQW-.8GgegB_M>ng#HJp(bbR(r?7NsbNO&uU'WjfiX<ht_KKf!L*X3Y4]A9>
jl/)#k+""&OKg,Tcqn(:G(0W%eS%4\Yo9`HnUZm050eF1^"KN%d*&E`/ULDoO8Y3/aX3SU]Aq
D"uDK'-nfnq%7^dY:U<H%5.XQag*M?Ia!g%3uM8?5,;5%F=JAAE4R0\5Cub@6[E#XMd]ArTjG
$6a;!]AP#[<bE!hZ\20(`Hu6AVBi`W\Q$J>"Ai"qfLna1epkkqlfiAi+mLKWq:cJ@RTH/F5'L
AfbIL++Gnc]AC(VV?-hZiD'9qad2@RF7_"P.!65-MJ>Os]A4(1*4!;Fp?mO>P&#tlL'GC&YY9U
^iE>7[CA;*l->br;WXDH@7un&B*U4"7tVY]A<*2M`?[4'^=X3@MH([^-Qk+lmVFaj@DBhhYB,
8=hKn=b+@;<^@4=^]A)H53,-?/eS+c/0\kQ=\)OpnIFUdX"CItXK3C^Y"JS\_&7\cXt16jVWr
O<<g@2-TX\DSE<fr2G07Z=K*8Jf.C>k2Y)"$,P)d<l]AX37&/E\0`7;QH9IT;kc<*[(3tY"uU
,1l;<&8f5XSh@U^9')qa`qQHaA,q8,(F\c)?se!`Q95uRS+mBem0'lQmi'<*s!b;RiCI(H4-
\1p/7L4A"gh54!^R7M)jF/@!CdqtWG>1=WFTM_[a+#?nF(#1nJQj7&%ChZ.[6al^A@!:B(1]A
KM-Q=P^'NqZqR.'4j?KaWhTJ2+^S2O8=*\73?0eL@*,rl-<4Ve5!c`M]A@V-VPO9k9%i:b:-2
/-]ATm]AK!9s[Z1mHHiU%FC<[#:PCTEO2m>M?dN4?HS2mpe`r4qhKb)$!r6e:HF\o!)Jn#Tp4i
(L-<IoSi0HcNp^cO:O*e?@cgCQ2P3>LU8B`i?*8q71E*^n,.ji4cU6c1cI/fb;WU@?V0Sb&:
\N^G3L=F(RLFLq7S]A*;kboCD.S!P<Di1JMC!;SXQt3OnS)]AYojYX>p63-^6-Ht_cH8G@R?#a
h5l,sN8dZJ1h3cBO]Aum$=tJdS+8/t?[;)'PmB(3RjH"%)^mXg?%2cA]A';h@gr*o4(3<iRE]AO
+n_Z5KBc[34Zr$?uTqkMs(!K9"[YKe0CW/H&sq8e>O6rMk&'nQl6^Tf9qEk\OgZA]A'Whcd5-
u_ch[@r<$A\#>"OM@N0#W+F)*'^J)^+DZ/K1lrZ4t16"]A]ARq^RS7?Rs"jJb6WWI,X;,<!PL;
1(P+q"3H"*+3LVfc8'TI4\>9?LH-cOsQhQG5mFt)`d+UA[S#QZf5ZhX[mWFk^NN$<ifJ1+^6
/sS&uX]Ac]AmZFL:Cr)fCl:04)*C7cR(#sZP<Uk7hZ8I0MBk)"g)V\mHGk-17XcVRLI'N-U-@;
m"&UJSHX#d(([!2)prT3pSNa38&Z@@7j3YU,7C7KoQ?[eep8goLUHd@I"r1R:YrH&,9t^8Hh
b`D,Z;&a$PLJ"#+;Rg.'@80m6fmEr!usGZ^JZC-<BLeca5]ACjLJQoQ@k&B1e`5_I*Y_)7<Fh
./\2K?T*_IPKSFbZO82&4`5&\Kq3]Aq[_=3/1C6@M+X3KN8eh/eT6j<p[C>PD=jKN8(i+`^7B
F:MB.I;RKcb=el2kB11_btArh.:"b2o)UtrT.)]A1Fon"5$5.s_b?<LVrA2ti?bJj>g#Z!\0e
U*dV`Tb;B4Mrgu_G=.A]ABX,p`VFn=NYeP1JdXo6Hi#r2klE=KDMn+O1egJE"-AMt?$Ekmq;u
.mH%V^7ID>C(X^,4/GA<PQhMl+o\?+Q!D%0N\Zakr;nt>j-PgO&]AF8RK&V30)q"ll[RWX?6Z
iVCF]A*kM-Ge!2T'd%Z[>QTF?e!rnebnL.Rr-&Kf6sCa0LM#]Ag''!XV0+USJ-D&tMch/L"?5.
"bV6/@1WPUugF5CDpp(Mr?)6HQ(e1#7Vi=FLDRK0tZtL=LHV=k&S:"X9%7?.53k_-M6OfDRU
^i%"a*(4o02dLcpE\Y4#6#7lXVDu*U)"#"GV]A3?7"&VJCj.W&oh#g,)1bf;993FFagJ8.Ha$
iR^psa9fQ7nI-1dN@g_a"]AD_-ZjeX"EMK%[[;-,h]Aj8q\dnbA);bB_OW%0$qN/lhm*9;<`Gt
PD!S*pmu4[.jt$:dYhZmSkpDDNC)bBR0jYUK>sY.5JX8(b`R&Fcd,/q<e%RZRo4.?8'q/scW
-bjk3k3)dr7[:,3"Og19i).c^\S1Sci%6E(eG$eIsL`AIF<\&!CV&?TKT8ngie5dPg5R.$8U
]A<"6s@QU6D_#V3c'BB(\n,='KA"pDDOF^gePDtW%@PALN;=?gVDni&W6`p0enUY!*U(i_j$3
ZM'>A4MZ,Vt;?TjaXW.niPWh<nP3cE=1@cs.n%@[VPa".t9PT65MR^91slC0Z7I;Q0B/Cp(B
%]A86Z)U)UM,Q3G1B]A/;@"RW=UaI#os+pZH*B#^6f/uC8tXKnHK7X,WEA=V9)R3b+D`//miXM
0*[rUHhC+(MFI/r<^_)`Tr"b#Lc1X`bU\:b>B#8t'_pHjdU!%X-mE=g(qtn"C&#0&<TPi)bu
L(Vnf/rL(,dNs+RcRUcHL5gJ@8sFkh->\6%MC)@YA6m$gEJ?CZk8lRXaiJJ[=e"W!u)SDQmY
>;#)0r;;fRS+`^>LgH^Kt]Ah9rb6:7T/L'A]AFs(C<J*qJ=%E(@pjF%QC,8'G?-QKi$g[Vh<ss
1UP'9OoFC1K,.VVEGMe$sW,4\@NVO-9;kL(bcL>7IV0d:=Ul^"?+AMr#nEh0MuVPXd`>;%l`
WCM9`dHd>hVTH7eo7RLJ#2lgN(jT1Hn+&fY&n052D3N;S5p3c<!?H%rua`TcQY/`"W0rm""r
o7^8`G\lE>DNfCZ31--RD5!4cRP.&4'u49Shi.)ZoHon5oIh>\N?UGl-BV$<k+g]A'7Qh3[,p
q\c0q8Q*MuX5^DK2!!^Ks:0;:"&5<-APi>K4u\XJXTB@A\Qn3s#m`"1D.k_11q1o8hWf-,XR
M0R8^3FRT(c2]AuQ_5T'=\]AXPXk(59,-^Z:N`ZR;b<Q+pO[EL5#3G@g9PkLa7?2B%P\Nlm^#q
d[N^RpJEqc]AqK-j1TaS"6UqLc$)^L.$2T1:Z#f<kB8H/B?8?\i+dc2jKSEk*!aSA;0hHWjXY
h6@bJ^690;-C9KBNT2P"#%`%gZgZJEGKnADS\r4DH%TH*n41%<MM7-unL2hOh4;@^t=DV#I&
]A/+1,DaQ9_$<.nW9?UjY+rg+3R7_A6\o%mUdqF2W;2!2YZ`ooZK@_n?QFED<J2bc%@phIlnB
_HbI^E[:9#e'uMe;-Dh*-bf'"&e&hNP^+rBEiEQ)cm1@N,th1j3)js!XdY#Hf\_4I<cB+d[g
bFnRkVbnp8XAF-G0Gi^,,at\L7IW15K5NnjZBi[j)CK5PgR->IheGdWib`TN<aFGoOqE#5S_
6#@i@,%'IWf4t7]A$%*k7Q>#Io@Y5P="SdK=JnJq#-n2_@Y#ZlBgt<VLV#HWEdQ]Ar'l5M%2*8
RYVBeMB=ZV&>eQ[LdUQ&iq>'\"4I26Xo6$%j8kWPXNq`1]AUCDL!q^!VlI*H"ap$69IhWDjs>
9^B@')P:W%8kr[?;Y?j@lEunA..=qQ3Zd"Ne,%$gapYY!+ceX4@q-$Eg4.3\L;b8DF+-`FbN
phqB[+O\:a>XCllVF.l(\?]AS*X,S+;P?<NDlorjsBIgZ^IMK4!a!qYFt\]AD#]A*l`(mBXk5]Ao
5J(Fq$eT"35@cZ2Qj&XE[K-e?"U#Af&.c@\.a%\p]A]A/_eARi_/%4F;J<>84&6PW(s'n;KYg]A
JZY&6WR"0\m$j6"4"AKY&WAO_(nY74,!![)9P_Oc?ReIN3j9D!Q+Pbr3$XOP/hD`l%C:%K)8
W5j&WO,mSMR(,'271G5S52SY.kIFGCm:n[R.8DE35Ts'cl_R/;bae$tSWr420r"oLi\>'V-,
\XbGAicTtTo&Q.?#ip^RBMZ7g\Pj_;V:1/QkQ;CiOB"(Jpf,CfYW6UqU&qCGb%l9$VZ4`XO5
hqnI-gO9pZ:pt67AXnX-q%>g7!21-WU9kn3t!(&013#!oYc74EodH-,"(0GB9GbO^NE7iR\B
ZDc\n$(N:b`LqKC#b[r,qUIUV7(suZ'rKuA90$bR!=mlO16M?3tiOSKP(W\WPc4jR[QI5W#Q
^/%-If`g)+K4ZGO4:F+#(gYVM:-[%S7W7WaiusJDk9@/Yj]AL+5[2AR_U2t]A7tt4k@,\s1A'h
XdV,C&`S0JD4#eo3hT)C]AXQ.;?kjD/f>O%C>0.(ZC&10u<1D"\csEE&j,)KTV$)'.^KT$6LD
DU6*=@$mD/b&_q/2U)/*OaS4(s$M\8Tui2</^m8jq_P(\#ic4)_Bk.\!e(/<9g"IO_I7KYnT
UMcXLkFf`Htr/Y@'7d>I+(Y*WZjVD)CVSja"FX\fI,e3UW56ac%9%nhBhlT!_^RqF?Dl+#8&
QHi6a);4a,!:;.0rgD)13O-F#rKb:RU`Tl!'^,>eK#1]Ar&kT:^U><(c9c>^)FDq@e_:Fd@^f
*&,\aI"na31]Af)9]A!6dqHk+ArhDc]AeTh$kFj9pi<P+W3iE]AX(Hc"C#$`Xt)/DC6Ipk)s9ig\
3*J8>>!]A4hN_*ML48_5!inK3=F8]A_DqQ%d`Ftf@,Pb_R`IHbO4l,]Ah31p?%bsmkE]A)3c5DI9
b%q%SESGCmOhhmu*S)\C*:4$(><T^%4=,@I4?RrIL<81_hZVi0I"8T%?[[g6]A]At0VqYU@s[e
&GaXNko[d4iDBeI&9XJA[]A=X*V1AR8il0h*EXMmC&oM<WU^`I;+j>$Y@Xhdf-?#8n6VjKR,q
]AO(!I*r!5+DNY?GU'g%>!1qI\/c?"Z&(V1m[W'/%`YPTnnnQ;5dCb_8t+.8;noF*FXO@_12M
e]AhK>*'^Jkl163j?tq`WA^iLk*\j&jnsY]A?6Yb&WF@%`&n%N4+qWtUB1TKk2'TP6/40tq'YK
"YT[Dj+Rb5nk'i4.VJma.Qon-,u6\^NYGXS`&J3sZ&>UuB\Z`QMThnM8?BiN0<"6',f9fU&O
-s,M_[<l=,p(mN32Oh??e-f-!!)X"TclFD$;O.6>LuTj7WTEh7KUS[W[.6CO#Ll1EKt,hV!O
42Of!k##a1m0\M/&lm`]AkbMj`b%Ff>WYF8UB!\,;$-t>!lmSUR%TT6<J9t4G*t-AMf_Tf!:8
p15\m.2pCdbaED7TCji!JT9a8cU=)hE4c9j<nmM$50E"U"/ZB*./g'<8Y2rS'!\l)Pb]A3N1A
h#QSoaV;_j2>es2`fBuWYK=@%mW$@-On8UkRSnGG>C-@#`5npf6aZH68(Jm76\G$#)3]AK5$:
Q@CO&b]AksLjO-^c38<FfiQlSAmXdKOg#L9.@QhlbXIV-*A`rmIKYX*5.-+l^\=;MF*N#&lM<
mC]AWpA56Df)+m]AWUqA!PIY+uXfsMSA1IOpW!G\CnK]A.i)dM.XnqL5S+^`lLhan5_%DimSGP6
Xi/98ZkG3GWap/.GRH`mq_jEorB)Hn4nt/EX7@^N9QT_273K3uERirgG,R3QfH2hP3]A(jXPA
kf_#Bhrh,GQ0Y)V'0*?=+J\50#SuY3:9o.4+=7gS4,q7Xs%bVQU4lUWAqVZKQ,:0^I-2&I!>
r-5ESE3Ku,%.JR8+cg>I,,hJ&kV<njK)p$jP/T;^FpWAm.aU4`<KWfI^S(FKlUL1/0)!efCQ
$P@8osD,'Kl/0-YQ+PGj!8.&#?hkLVC]Aj]Ad-BX7.KQguC:ZRH@g4hZP5TOsRQ"Jtt#Z`5Ws=
K0D,P%Sf*]ADuXDQU.aFer+3fTVDH_6H#1D$Tg$JI(B:uKoIX6blKrR*HOsDKAT9b?&&L*[4V
U)JKW%05J625kl3TuL#g>9($NWCpqEV\f>=A65VE@?9@V.L5Ho`NN@6b*>A4Ta\'mt$Mdo5Y
gY`0L@IU\I1d8G#mBEB7iB)K>/N9pbQ:Rep7MD#tb(J3r#g>IY+bUu)uaOSghIUoj&&=p&O6
)9Sr^iK_-m1&e2O$QnDP5R2pfqshODHM[1U,f2]AR[7<JaKI43;b/,C_AX&!T1M%_bEJt%HR8
urTKX1R<Ou\iL+T\`qDjI&6FLC3`.N)`#ck:e_0coQn04C4E9\/%\ddfO`m`!L!TK$Xc:[\o
4:SVY&3J=_Tl#Rul5+DA#pE:*=R<6+'_7eBDCfUi\.@*!U[cd[jYscQfo1r`EX^Kub;J]Ae2Y
DoN1BtP'$4.6G%u1Z;ojP7)454">eSHpc_fDBmGGaNk)i?iQdsGmmi0m.Sa71h5(lHrrU0`h
*#W$<miDWaE3n:V-KcCmWF5?mtQN1i?_hd#'61o9-PDa)7T@9HC7u<2[W:tb?#HX=e*]A`"sW
2P>_D7[F/"25$D[ZoYb_jHn',)Ed0%BZ/l%Zc7ZIT_DCUO\K,AK09`aWT%rs&Q'h$m,@MUgt
sif$VcMn)PmDI*YKSS)iS61Z,*FBp9!LCrtql5=Bg0DAI]AO&G3:%j/uFZY$8Ti(/M;t=24F%
?H,C@>e$W2gN5p!6F=Q49.,&-6o*2_asq?`2:XFCJWX=p)dh0.MVOhea0&0^a7CI`0p:n%%B
6lpAGp4P"..n.MnR8J?9<lkd$2^<XnTPe9gR@BIg!0746rcV=LQ+8lZZ_ulH#mmiO)M0JVb(
s"[atOMKF([!J9MkOGX7drQ$I\&;6dr7XAs!^dlGZR?\he+G=4jpOa[V8s>73?@-S<kr,p_5
@sHO6f:h/@(J,A=CL+K\mr4]A6,NQ.n^1OS20CG"Sp5#FP7iu%g"q$,8"2;O.ogZ&0ip4L0e8
k9UuJ16SYMSH'jp@H^6g)2&%RqD*')o[H31`6OQgI"Ll*<+Q]Afp(T#Nra@d+3_Zq,7OEkcQ#
7$o.mC5R+N(h%f9SjVggeYR4iU(Z=VdJnUV!S\F"\ct"iI38.)H(I4d3=X)`KkIN)S1BA8q\
]A9GN!)Z2K;]AXCVKhtg82;h,Q6a.6(a_R2EGG"&i"RZtP<uCdUnHK7E&]Ae_UrmlA5V=fT3h-E
ICgi^/0g:8&Z7(gERbJ.FUd-,!l3OoU"7"=OT.K^%Y$-m%IK@jiGf-L?jE?()4)$C13TB1\%
Fg#nMt`34OkZ`4NIe-SN?3G8l>\-(&]A+('Ef/II2kh;'Fmbp0;(L1S6.77R2DR,_?;rAqk4?
N5g.?;I72pf`e>3iS+@/@SZIg0C+('7SV.>jn""`D$J6!'EqlEQd@&$aIE]AQXOR&F-iGf0CT
7*F-N,n=1+g)6;@i:]AJBGDAsQ!%u`)CLT-h\TFnW=O^%bB[a@XYSV631_CY_`oMe$qAR,Bir
#_N9-W75^<Y'M78Wn6h:,pO]ASLre(^Aq3lO(_?0?3^F3XnG6`$-+u.$6P]A:=mmfodeN[L@C;
P_0i>%PAk0')o;"9=;5n'RCUBaY%YZX"]A&e;CB:+.fD6Bj$;Dqg6e>8ffX$"-PAYp\Mf371i
<l8oI5Ia$s)uJ/1:^(6HM0=C@m&6!%Ahdp8,pQ8o5^DhUsI<U;)4BAm#\qs)1%;EDVtDL"3j
OC!]A>GjA3lf!3CiNCSN9#!h^jE+l,+^ef]ALel]AYab)o`36p(WkJd3Sr!Pg.Npbo5".&I%3QF
hDlo&O56$`aq.,KJhN$<D*_I'NS<MYOp^q$+M/_=nQ8LH+?T$3KCP$<0;,`HV5/sT]A]A;#gVT
k3IV+%0a\>C&M;U8?E#V#m"ZVoB)1e(,5LEfT.?t[j_/i<EJ=-M4\>*7?kWmA$$ngZke+n<+
*-YtSUaQ;PWpWi`%"g,>cD]A@o@pd+ZoAJJl]A#ViS%[P$=551X&i^]A*:\D0k)Cc5!<%;3kfVc
[N*HU`/+D5"ML>e(Ap8DO8KneC?R07jRH]AkF;;&+W=5Q#Gf8,>R!5E<qL*:rAsQe"8HW=6+u
jqF<SF#:ip%bpp@-c?2K"G.V'-9DMn(4(fBf2+LGp)!g5(g43q^0]A:%=ISiS/V6!naSpas`u
h+HBr'414N@iFYhM&VAHZ&+&g)jmsg38:TZXbF^bNj:E#qAKll:V$:BLoj9!b`R&dHKUI2&P
r4q+t]AcEbNRkUAQ!:N5tOr?:X))dAa&J7@OCh%f=JP"YSVlL2'De`'[iK![?&MqI8Q$!/FEG
%>l?/4r-M"NG-r(EDSR%Xr,c/UGk]A'_(6QY7*]AFpC23,@/.qUa4fq!QS_=!^mR3oPdI;GW9>
"GeeUrQ.lPuI6sbqDUq<p\a&VPZEYXF.^E>;1r'>a+6("qJ@:$,sl3NC)j\7_F[5R*OqY7QM
oNLHk%\fsT_rRT7AjKVom',k&Tn#(-dD!lqHW6ZI[445j\I$f<^-`![J(?UXd6X]Ak.!5HCTZ
g[qI`rIsEgB'A&^FS_$:(X)l?(+"KuR:L&BajK(jXH]A3<)g9)P,#'jSg3Tpun3)ObYQcUa>j
F`#M:DO\W7j?%EM9r#3)X.Y*Fk<b)").N4#eh9/DQ:<=RpH5()4\%p6.,e7VGpf(.j>U"1BB
C'q^ui_k/b]A2s7RFnF+B2l+k$ciJH6CE1D>1\TCloo+VE_3PR&`#87=DW/sP.kr/fe0YhB:(
XoOr!u@/ho_1W"':G\3i!B0!+=$`ul19^G/`e9J@0UR,QXXF=nEC6b;3HU3J,Q$81S5&)Nsk
85+h/mC2*+&s:c(mDE^e`1GE!4sdLQBH>D>&D/AX'+VsD6o@OjW-r$HT!(&<(UE<@s4=qAAs
2CH8F_5RORLIb-5q6n.-3dB07M:AFc9o,Wus.MZN1KM>?iA=(1:-$%tP!8esk)fPoa0ELp0#
tMAAbaRt/X\/<%D)u@:"GFcT3f3!lE&gQO%1\/"+/LFpo:AX+3ZH?&&.5b#57""38]A(s,`Kq
aHAM;F48-qeN\'e7jd"j50B$P,c=C$K7ukZ#j_(OFiAcK"+1^@HM#V)@pMn)K@Z&[5HR&MpW
u(4hGB[Ljh9@Y>mfD#U<*o4,!QEN*`27]A*C7,YdDLP</4('I8j)=ha`ESgDF=kHIj@h:n(Mo
RCW[gVV^12^L%3^?2h<P-,ir]AD@1W8=tV8X-nVB`4;NF%>L,Rkm^<6k2H*=BolQfOLj@6`T/
C(@S@rAaQ>p=1UNXMbdNncJ+e)(GTOb8:I1f+)Z\DljeCNms3?D$hM8`9TH/r?Y!S"54's8-
1,3WTPlj/'ES<Z4(h=V"Aj0e\r++=7\T0\H0pjA6-%WG&aXkiO"AbBBa?.48j9/Ig04k+%`U
/?u<&adg_*[TaMl9g&O>pWj1^OaqfI"D[K=2BM=*""buTl`32*40>e_@3nJ%oa>+F>f@8aY1
.AQi\::Eb/b6HH"?6<'*[:Q*OeO`ZAGe-gdO0]Af>A0M(C[A_E43h(i6.5-d4X[.8?,%fg2;F
QIT+J-m9ZIOF)B`Y-R`M4qq"Wn\k8SBJEeB5G/+oc/nC>.#l`b@@XJL<%5X8n'H_m?IY<Kld
5.L65^2D9d"56+a*ipsAD'E[Qd.sSXD"g_B;L=:X$1_k.#loP8F%bJRamm2iA0iUgT=-FB<k
o^l?QqnS[FR714,sE\2EY`UJ(O_G['P2O!&f@]AS0g6H`#+r,B\=ds#'2'-7skK;/$\?5+O;s
M&,N,]AdpgXr_S##$S3Ab&V`t-Xn9ZCfF`2\0)#)c1!60!62c=q"!IVL%bTsJh3J`[309/5)(
6(0uOo`<_hSXJ\]AFeu\#`r6I-(7<7FDI?;X%I8).?TQ$A`Q7F1E/R2o9A3WcS>,UI#n%,(6$
/8SWi40.c6@[*?/(F4=PjOfY&^5SJDH2;6JUIT67<?%FU))![sgrBS+9>ndB298i>]ABJ\'?V
QD"QW5Mm1A37sUS3[)3oVph-i#]A#d``uiaAkOre0A[$Pi)H,,1XKq7[Ji+9f<))QJ1(F)lRk
FRYp'kDMeJnq+b:#^;PEl6kLEjPNg5QoiOne[tD09;J_SjG5I_j.$etG%_%tG9>[(ZT"HBfA
j99O6&^5`EZc@k`:@"s&j`Ts0gQhGuk%1pAF-Pr>AVtPcu2tqR[h*Bo"(du:S)I=gP_X?9L+
ak=;?]ARW^D!+&h@DdnG9q&0b('2e2r@nS1h2AQrLVIb-1b-(PQ^dQ:eba\m:C1(3f7'ggdWq
[p3,"5bV@A%/gP/,[2[+8\*\/u;%kL!g@n;H=;P--/q\HhmXnugn6=89@>VlKb)"[C5VZ#f(
,2^V@bO@ZdKl89*RZS<RfIW;?:)t!%$(TM76o\&q7.tRbl*fJBFJ,:OiUk@sG7j68l'B]A*hQ
b&/))&W9,%7UNimIG-9u-nNP"S4LUB=g/U>sV$(LlhR?a(ST)5$bRece6:AM?t"e6-odi:;L
GjE=c'H!iWPgVBrr<c_2#Ac'rhnGV=%PE(]AV6?dmNrO%6kopS,B*Y"o@?l'$d>TKX-jQLI_P
.^]A&JVmY+NH,@K=h2\'m=rjNAd+K]AK/Ut-bJM9HdXGk(Rh?8$_P'#Cq>mA)paZT%R^%0!9[2
SfFSHR7=t04,+IZ$9jm*dq%[V-.ZX?)]A7*!-qs&tA&!t_/3mMg6LPO'\<3DG<:5[=u`!g0pc
"Qh2-W[f-V$=Nsal@995L:*JT2*JALOpVce`u&&j38j.<A-g()1X7>jkXD9BVYcN]A(V?#H3.
*%b`ll`d2XSiT2V1"GEW\rQ;jg"C/tN[o&09F%/`etlWN:_#Ha<WfJHdc]A=1FIS+:MF/ZpjB
b(8FfpA!q"7(m[3<jXiJaGNL8*,b6"ASL04Cea?h$RU;6oZUo>D6_'B^K*)/3oe[r.9!S-+,
8m++a1@\u2$a5O8TG;<a\=[Hml@e,]AjF!Ui)C$sk6jFKMJm;XbUC).mpWCKIHQWE:e[tD2I!
L0CHp,X,ip+EKB7eIRcUk7iW*[4%'W>i0D-OC3AE*X%!hB>+`U8js*.ZMEP.U'`D>K@C;`2@
eW%9*cW2813jJR1I'&W<#Bh6Eb:%)_<)Am(qi).m=m!e3U\U>@aQ@B#np/2SUl^1ONViJ^pX
s>38j4<uomYM:HZX4Baj&F/Yq)jY88^BiF=hL<&R$<Q`-oWuj^`p9H2pu7>STcn#GjRA0bT+
NZiio0hhg^3bkMenO6<KC@1r[NG0LecI$F,7+Ih)CO6C1E???IaV>Xk/)fQ]ABgm4kp\t-4r^
b]ARZo[0IcSn$]A?,dqd%!sNN,&@\_u@$02Gh^8<Rm0X6)qg=;_KrF=%[iZ]AnehA`0Z-!BjQrE
Er<grRUp/mn;Yk3,iK,]A9"AN1/=*BAD7"-\)o&\C1#63p6Qbu@kuPA[uD2pY3FSZ).p5XdG1
hP2BC#h0?=L^.>Coo-22^=p(7l7+tQ."'dK*\=oOCmR15puGnPVEGibhu+oBUHhd?(ZrdLS'
&C"je2#))qZ-b7Lh%c/faVt`3X@lrr'ii'DnS*<oR:\@p_(qFj?>df)WGI#*1d9Z<!GlW40Z
tiU<tK7]AGP6&-!V)XnFk*CN,p9X!#MO<I1(e39,0f=1CqRVPaK:jMt9]A[+keYT`S$@>RQGUp
hM#l`N1X-S/9k04EWTJ=.2kR]AaD-Nl]As[%37b&4VC0Qg0@?tp/Q+7:;o88mYQnDn/u[@.U+C
G"PMR#]A`(U3:oQIu#^Q`(UZaB7`6H`J41CY\tQI1$[s(tWP9i451\X`o82>EbB3LHq<_Os+-
q0?\!DY^0HTrd*SlqaIWAnbQSEt)?AaN>$H\/QlW9[R0n>2r=L\_B7\;dN0_HX\XnIU;t)UZ
V@):*:UikBA5F+jB"UcBPXp2AYp))<JfE/dc6TFp41n-q)Hu8,g2\<)2c3,U<'i.pg1WSA]A\
lP3L@R`E^BPo,[6Q^]Aje.AX^A#m/pO/(fm2/^Xa-Mj'GJePBK_VC#GO_`:qK$?k-9p8!\<L$
:l$R#P@:h,6h6R':O@-A[FDSYNH"ubT]A$hKC3p.id6hgq[e09s"-1Y?PQ\%7i0cs"AR(B=6+
3[Dn3:IQHNGPpP$W2#*QHDHK+?+KN)[k5B;e&8i[pb=G[W(='Yce1-&]AhHs+"E#2Q_7]AY%-@
9#<Q4''OU7ZVPc9]AKjch1l"OcdIWVMRT3]AfI9D\3++0,AiTEX9-"Jn)Y0Vnd_EO.;d3LZu%6
aGaBhXQ.3QOUH;PbPKVpPK9/k?gD'Sa`VLB4_DjM,uOAgChtV%t<OEf0M$BU.Z,lQ;3[809F
solG)[GW'8DR=Sm"h%,]AH`I9UL+.@t\pNe-Jij1ICFLl5*9?NH>;e^9nU;mB"dD^I32^)C\0
aZAk_"d0YG%-8sp5>C+0B[s.iRJj<FO>*]A#Pt_"UtlWeQHDhfSah.R80Es=kjEjZ_ipf:":_
S.=rqSem7X!j:@4#E4[U95$3GHF/;BWl47bFG5l5oR.jK[1(OtK(OsKKeNV:M*6lS,ZrfH*5
4YN7;7gN)CkFs4V.t6=gDQ&A"7TnrB!'iM(9kRH>\]A%SYT-m"NA"n4':^5,$"KehjmU8Ar"7
ck:<]Ak]A+qkO?HG'5n6gb81qTC`68^kbA--s0>kKFK[NhN&mVni4X_"VHr=OH(=hK4')l-e8h
T_kb'aiD'3H8GXXf-TaEhHf_T;`s_mGK&8do^P#;1RF;,?iq%Yo-5\k=rXY"[5>bS_T3*`GD
[EjTR/3_<cX1R(ep%%6q;m)_.)8dZ6f\)?<0mR-jmqb<#p%oeSL`'O)OL8M_,RBYbo[Z<!GD
H/KQZUr0Htgtp[5)P\N7;r+1+m&k0g^Kgbk"Xq6,Caf&>;coehn'(4P4<IbATqc[-uKq9/I@
m$Dp'[8igi^CKntNb)%W+o&S*VRV.ScG]Ase@.P24A=NV[5eju/HELr?`$9'Y=YnngRr"1!TC
Wtb-_'FM[t]AGjWC2(#;;DV04Hq8phCq#d(Lp''518=8e(%uIp\I-+Y8<lUi!iIXNn1k?^2='
*fh]A/6]ASAS]AksO_f#Mnkd?>RR@?!1l5)+7$Tl;qbG(EHtP=.O$!h0r.h2NB\/b86-kB<P<mI
&Vqib:-W.2u^A-:!-S9@Z@X7o'4X.R\Kbb&n/29=<`hV.H5V+W7rJ'P"!Rq$hI-6_:P$aiQ:
k&T,'o'U-tjLVlU19l'ZL#!B(W7hs+oHE2Wt^4-]A4>%\^IikEY`;[mM'Z(\,;WJTg<TLB.Q4
2Dh\/I)<MQD,1;e'-QV91SOgIQ'/8L]A"bG?!#%R]Ajs]Au`kFPYX#=I51G_u9SZR7&,]A_`C/.>
KIb;X($o#>,#n*Se789jp+F;c!W!7lJm5DsCc.BXtQk_t8'%T?FOBn^MZYi.BshlHjaq.:qI
WY&&#a8%h4#6YT&!/((Qi?B9`h0Y#_pe6kE"<i<2EWKFBhl3"B9:Q+W_TV!XbS)>?$3+iE6"
ThYN7r%L^"M$R0>h$I>HeMk3UU_1&nBga%+k-&P4"C-^Bf%'TQlu!6(bY/B*%RBmn/;i!&#Q
Qmm6=^'Xq*^!n5f7P`T7fC!K>dAZRp.5Pj6I4R3TIh'qQ[n>h/p/J@3\(CrgO&buTtd#<m"-
LPM?j-@^IA'ZVStP*9W6Ei`qp^?oN`qFkfsF&RDuomMp2VgMZli^*J?._E(A8'$nm4b5c/K;
3d7`6BOMc!kX;,biE`fSrF.jiKZ>QdN.6%V%P,>u[Vl<m4T9f58AuH=%^8nM$lVE\:QO8">A
V4hR?Zr_1-'QL;5;_4*id-d\X@9,m#5klri39SsK%btgeB*(3u.D\Ls?mp8^TZL;+/I&ukKI
@\s85spTc*CgYUcJ4O@H[C#*8(@3#dQlFG'^^k)VAA#sHEfdM`Cj:S%m]A.O5XQ-r;^6`U]AqA
\NR]A/:NWj;CUY94G"<CgeKD?=%Q=h&q#GWX22Y[nI$eRl4ZS%M33f3IQ%I-,TFpld<8q]A?8R
7[<<E99(\rc4`A+GtB\sASg"1T>_`=ES&f$b3pJ=V9\dhj<_LdEV3=QJn7Sm>jFRr."e,'PU
H^\&%8)oE%*ls#!Rp3\_G!Clgkmo!j*XW?9Yo6>#hFPAuL,)9A[Z]A486j+6h_]AE/!bR[VH6W
$@Z'#s/m%]A)(5`Y2*J"khjS,?&a":(^d)+2d[<(0LrrM)/jO0S^odZYY#N&(#\tV,98m9=u5
NE1'$Xpafp(.!":dC\=Y_6+('if1+_V.S;:LCI_lBG,m3`H1OdhPO+BR@Q8aiE5qA,,#V[pL
]A\o1g"'_lfF=[<%SeJ"Gd_Stj'f9,d#\!r6)-MV(FNe]A.i(75;^,EliPNYI3+.jOKF8,2@@E
fn'[h/*2gYXE/luj6*>3V"\BkGsk(H^^<T^(F,.H-_tO=VJ!-9*^nIo>:k/e+Q2Hs)S4b'FX
m0'Sq4:.)L[32)egI)hCjZgeiCOMnYR<R]A_`ajm4Q<B_h^CKYrpe>m8E#!\FnIHrGnDQ4q??
/1>UD90F<UZQW@^H8lpqI771cqq)h5Y[geL_'l7ljKccp[A>aZJaJher2Eb@g5&T#2rBYT%3
=QmHnKqM'FG=NbB>D/l&Qd\.a4Yir"?YRUiDA-?8;$oaJ@>:7&%nHRQK2eokF`hRo$\f[?dh
ZXPbuf%VgL=Lc=l&?a\1a6L&^.;5;c$*nqf?Y>Ep"3EJeH+_&EYa1F#-*M$_5\:Z(j[(HESp
]A`]Ap?aq\0fasi(*Y?&_[*RCN,<-V/J"Oo`$j^T9Cl<9-!fl,KiGkhCKIhU[I=;RFg7m;ht$Z
N`$]ACe34?67fWE4eC`7>Ti`D5I+6Fm/A-KY>D04_m*--omjK6-i$.^\Dh6i.[=g<0r%mei<J
s&8_No)Q,`u.Xc]At_$lp[!dT!Dbd:q:MfH>b!M*3ChO)O3R2FH+D2hM=LYpQ=osH$V/K[+Wc
G-F>#PjX1Agc&]AbEV+GHZ6)K5hUM+&FN*?hG"W%=Wa1sYR@fZ=8&i[`gk4_"s[?hD(r?Mf_?
"\a>p<5&H/eVBj_*>alkq0.A;nN(De8AaJN[aip%[G<r_S?,Ghi_6G1-+@-*maf0>jN_a6EQ
DMh*g[S=l3N_baedZRH7'".H=),S9eC%)k&H@>hH*bsJg*Ol1($l(JUgA'hEORe-JXkTGH.N
]AU]Aou_Tb!,_0`CNIm#E:UB\N`;k#NLlfVTkdBXL6R6m&+OR5HFe;u1$F^#cn/9#,uLR!$Aa[
aM:FrZm[)3K3h$AcK2*?n!UBdbY:/c"8?]ArX&(p"MM_o,1YI1PrTn^#]Adooor[JJ`,*YSQ-i
hrC^W*SqUPul0nO=\(I1!;CjqJ`(9"T.4T8e/21>!lqIS#^YIi'WD(MbY;A*fZ'pOBXg81=Z
`>("+G.[>+;H:p&3Mo!nfJ7tsdD=9^?c@Csp8XK*BOYgm^k:@*mTihEcN$h*MdqhPHKSVR=<
o0Z7@MbQ)_B6['SY0H(So`B<D&>>i*]Ai%11)-rV(b2FA*V\=Pg"0:ZUjsM4@q_OI\<o15*56
[rtp0Doe4..Z6+7/#E?e*\U)o_@S3'[#Y]AX."J#td,n^2>"(7ZZ8-.:IN<KuV[+ge,+O(`8M
,<,ZQoOg!-@6X^[-qftc_<h$2YS4DFc^gK)ZmfKST5]AOWZrfkTB7*hG-)%h=6\41APhTE!Q=
Ud'Vc!KuZWBuMJG(F;,lrP9/4SoVhOmZO7KSrW^/Ep3aCM@*g93ci%FW^7M-[5&siQX]Aq)=p
=Wjp$o0N0WF?0`n?0\FL.,UJ^KW^Q=7NV>uorrT4TH[mi0ZF,f=9D)'[rCDm6!D8TO1UW1jm
bE"HCC<Dukf5N+DHef>2W4X@'*=#Duil$^HqiSm_TgOWBq9$9RqI%0HAWorknb%`>N)q1@M:
T&7X"Dp[p0JrgGqKeN(/==Z0XgdF-'WArO)ViG<NGbmDNYlUKLGd?RFZ,Ej@XZO1tZ%i6HG:
TH5:)Lfet`qI(T\gWapa-3!"-^VR(Q#]Ab':U;+Z6R192S_3fkdF6apBo%3ft^[b&hiH\Mr%8
jTT]As)\`1-fP,9]AXbXudho9V-P'l:lo42u[#2=*ZYrA!S=1\K925,,VR/2Q:5X8-jd/k(F3T
GV_s1":I.WE6Tssk:/S+'=AZ(2:[3HJDa'aU'4H=Z\Q(UbA4f_CO;k>=##@nT2'Ho[-4Q;ta
p1kH8e`iUElS'><5dXC7oa-6;m?gsj;[oh/"&l9VDAeehW,]A'Pf\.n3mdWVoduQme1E.+e/Z
3e\3He#8B,#d%i&;VHi_2fLCt0k:/:XTX8.6NtLBdT`:sQ<7-dB&l0N*NLL'6d:ZB5XJ-G1l
-A,K1PKk7`Sg?R@/ih#B2!dU8K'J)q-!U1(e9Kerq5e:X$1sfJ44K&82H9^mfNZJY&WE&#PS
*-XgiL<fEWl"'c$*dP*jO(T!>5r8*6F:7sRu%5U]A#b'LGW4qTZIH+6=7)DOCH]A<bRS/PNkQ]A
`L'1U>XF$VF_D0SLt"@_e6"56j-e1<\'[)?P.'^e'V_ujW1dmSmu6Y]AS@6NUYI=F7RHlWh4u
C'JS:2af.)n-lS9D.[jqaF.VV:NQ0Z]A5[aMS3e,>Y)fSBZ4cU6Ol#O]A7/kH@bKp/X/a)t6Tk
MIT4:\KjgN:@7R0a&S*n1Rq%Z*r;eJu?5lI05NW5fqPddP&lU3tP66O;,'mMP8i]A-s>D#rO+
.Eab6aVmYun3Dfh*/u>951`q,?D2Fc^WM0`E1d*M1@"S+pr\+[=+#gjCX%Gl*-N*_Jn$B0-k
rqNsh@!Ro.ti#P+kEZ"N^h'XX]A;J[8]AkN*#Y=i*.X,Wm3<%s]A=m_6cT;l=e[#$V4$fWhHoun
=4IM29UF)%#'Ucp!KE4e@qYl;I,YtG/Yj$'c')FlcH.@H$=O]A&]Ar.8B[sF@9B']A6`5&%3:?l
QAIq.jq8.e7a#jIEH4<NYD'7[[NdFp_bgGS,DbPT:7``7O(#t's6Et*!p]A\'q<d*dSgVbc%R
dK"mgFF@CU*Z$h"O$ThYKHVn"9ghqhN2e/ZeoK/f":o'@:0amWJJX.(AbCh`Gdfr/YBOe8p+
!&6X/)rQ=0-6g%?/N.:<P?*at?Do3VU[)2^0@_C<D06]A:!YEc'2^YZqV_(p7bP(NEhLYob2a
EtU%s1a>0_*Pg3]AJkUY\SdFa58S,Tep%(M;L:%mjj!6k]AK\!fHi8,PGA!<;g4F1X[U<=D/_C
hT$ap5nJo/?4?sBiefG892[KYE;D8EYgs#\(aFgRri^\k[[/Q]AU]Aj,M<%gnhdI((Ygd/cL%k
>8"5$p0/#I$ap5nJo/?4?sBiefG892[KYE;D8EYg>Hf^3(2OAe"l"Y-mfdJu',Ta.r-=hp.O
+I;q5q$h(2OAe"l"WG_7!`UYV"KCC^[ZTgOa:Y[U<=D/_ChT$hjU5Qn*=j5OXp\fnb>f64\d
Zd]A-NU/_L)Y@5:n>Y7S[>gL'H!1XJ"n<-7*.8,ie~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA3"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="638"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="674"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('REPORT4').style.top='-12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[38100,1143000,38100,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFH\fdT><+E:7Z*]A!^=6h,]AQkmUiRpHgts,_V3.I0"nH&U4VAG,*sVbaVR@==_Z`lXDM^diD
rlM<$Du4hWcO97E&R5Po!@u9FqCPp]AfMgle6DCIm&9DJ,Zr1Y)=CRq4.'TSb##%Eshm]AG&7-
7\,P4"_.!;!j%$h'E*/=/npP`Lnip?R&6K/BruY#V3?4GiV,`TrX4+ct(r"XfHko)fi\";[j
o(0gal,7efAja1kjhb:X_EttBZe%F"5j(HhDamaNLi<VpgW/f?J"r%`GN$N1pZ^Ag)``Zq^q
,:5!+/<"s=s5Xc9?\/'r%obn*C+%;M_;8^-J+,2uD<'Dc<m70HMr]A.J)D0"C":DhU0ofX:=+
/ThF=>F/i@n<NFX\Xf]A]AC_s0UI_$)aacm$g*=m%g37B^<fdaa9rnS#'SZ?Fg"Mto!$]A!$_+C
0TXgm4EV9Xp^Y=0E',W1,gt#FYL:,"I&^3oo^ECKp.sNc-)b9[JQ9/.1tIai\6K0EeP2?Y(L
G2TjUUXkRu:/N<'@\lcaKYh4oa;"S24S@2X"b^dSLUN*aQVU,2q6<qa+ack/8j93A6#P<>'g
4M)&/O+VH(g8VA8RZs1j)<+h9O>.6'L1?=R,'J>PZq=Vj^2]A.MeHGO"D<johiUpX[+BS*Yg4
jnT.jR"l\@9D'pKI,+CR0)=1mp\pjB6CFD(HT$Il??&*Luf>*JdA3Gj96)CNr@3K.@2j$hNQ
Y-$>Qgla4*k0?Zm/p:7i-W:re+brJ^DD1jLqV&>mC8#6MAAf^@)\#dLreD".e(!?2j:F7BA[
;@/_CZ.gMB2.ujB&'<S"kXC@&T#mdreHQq!OG;2M3iU@E=sA(2^^nl"N:`0SEsjicU5\?qED
j,4@cr:#adq>.,$S,>oqD.>JATV/EM?5AX,/Pt/j0#]AMVlVS_1bNSs-Wop]A!K84/VC?Re,dg
umj5qP'6rB1&fQP[SNSTurmC)(++ob'`3S1+bNjo!=+[1)M@L2+a;MT8j8`=RnKXk"@rc/q0
@>P<lc!8$>g)V.D?QD.@JANjcoV+"VI1<E@Wf;5):Zr<G,hq_ZSEk`5s+@QLqhrcIIP(Ab(@
A5Z9sCKtR:-ofE2MN36CE^ET5Ian:Ze%A:N[XBH'`kK%$pA?4S31dcn&D/A07,>NG-dPV(P6
;f7ALd9Oe,!Ni<(qp1'iW^1^HK?(VW#MDTa)D_!N\WLBni#O^SLXIOpoTOh1@>;ON$=$<,Fs
]ATc.3V-!fj9bH=T/hn^L[q%M'6Lo2\1WnVssTPJcokY'K4Lu^+Clo*+l287FS>+Y+O6<C<O"
U$_&N:FhR`Ct20;bJd_;L:0j;tM+]A>6D,?Qgmtof(5R,)D*nA?i<Yc<49iHX#_]AGjoM%#cWH
c0-(L/R;ij^)@L)'Nl<I_r7`^h6ALN;P)Z:gBB81'60BMKo%8oos/D[:RkOS6[U,\oJK)MZ8
DcT]A![jZ<8,iATJD#C:.kjeNXer=a-ACF6`"jC1SatPWe.a0rS3*#]A;VJp;&V;^LSX#^1@kN
((sl4*AV9[NMe+2#;Y\iR01qG9%Hf5?Z@hV3R:Q'?R3c!MijT=QGBeM)lQVD[A4.Z@akQAfR
jPPQsW4F#H)d:M(\q;qN&[d99a1\BtbRh<Bg,\Xe@Sgf,;jU-?G85iM-prej%]A(E_C#jQ)`l
00p`h;Q*b^Rme(Ka'!TX$]A3?W:\ND;p&4!;#[9TBcAeHC;LNnYb&2lPX\f;TNX%;hP^3L?T)
7HS3#@'>PKZ+<SjH:Di7CThjnC*Q'[MM9.[1gC3_\bIV3=-dB&nb`Zlu9;K`;j(&^7U=YNt8
?4Q$diB:i2(T19#jY[$Nka!^QnS6glXIR4LW-KeZDTt*rHO0iW=n>X]Ak"9e@QcF>@Gt?,Zhk
g^Y2g"`Njb,s&'cs8&$FHW8g:9/-,(mq`7AY^)JIL]A>[\7jA`(R'4"cnAKFD$D`)mdGe>5FA
JPriRl-InjNEW@2HXiuI7;4Mc=bC;)^/nIqn)s^?'P0Ir+j5`6:[K0M5l/SV01fVZBk*;oeN
Z(s_3fDe\UlKqCb-,Ro_23@aWXU#2Cjg/'I+B<8+=YdN0^IAN?mtm:h5"H*W6[o@RKm%g^iJ
dgiAt2+$[%\T$Po=<C3QsD:j3/RUn0,9D)`8A*mNtH]AF'q-eF?o%ef$1gNF/lWmn'mS*]AeVg
,:He+B;TU0'Wdm035GIc;',E7oTqE#PaGL(+e,*/gKq2kWYFKXX7bf8dWK89NAhM4Cs+B1m$
CB:78WlEI@rH#Wm"U&k:j$6_8LAOq1s`jNDX^%d$Wpi,m"f/gFN?lT'8r#OUF%_YD<b[oH\,
C[($[^3Ek9MMp%:%,[.H-8ERHK?a2>^A2t=l.Yb7]AJc8Jb:sdXc,Q`:H,;cMkF%P"*;)-q86
>bu2Z?jiq7<VoioCi[q]AanBkPDWd>lZ<a?T5<4-B/DQhE9uR@[^&OiEuPe&"q4RiN\*0%WOZ
p`Yf1uOXjg#=Es"Jde[*tWps$RAmOkT)hGn5*jC\Ofi'5C0$%$b%Xl^LfPA81?!Bp:N4.X#A
YdP-(E2A3De4@c#1etPqZ%VY[;m`Kn`2[@O^0;</qY^G2I%tL9Ck<RSROuF]AVs0Jn$W5DQ+(
9R,Bu-N8_0i9)hZ-i9N%*TY$]AP0'&j8f;-jqUX[2,GIaSA&<gY>0rq,adgMp<ebVT=!b0-C,
M35m<:b`s#WJkHUI4rHlunqPbM[&L#m*p7Xs:PlG]A(U4;eqPhf"]A%Ac_"!=!kfTKodA5%'=5
]AJ?6@\NIhUC6<Mn&.2p1./7P??T>qdkm5XQ6RRb3'A3dR-]AM>K)9KioI\S'n#BD)mGYG?<_j
A43O+<?COFdZ>d+QFDdFZCEI0R`<jq^@jn$eL/ck,1+lQc,&i+iX3-sVS76W?-mMcM:/Th>j
`4loflN]A+\iiKs4ml>M0o@u'gUS5b_B-'me@or5OP,XsR?dqQ(h6%-Ylqc-QgS#aRom59N\<
WVJF]A_uK>MT<;VMnki%XOW^aP-;L3G>;1QS84i)3T`)Vfm?O/]AiIl$dQMT\l#$)s5c-VeS2/
tf/`L$J04U9PV?(uFHOBc&oqB#EI[YRodfcZrDJgG<ob=-/"/<3kj1c\Tt1%6NM4[.5!X&2m
t:s,h"pLp7(i8dnU+Qpn)o$1*P8aTnCJsqrGJ/XKStYD3E>>cE,MiM^FqZGFFu11R;qZ5cbI
V)=RsmF<tmep7('HSmi<#mQ5iLBUVilsjiTUHXkCme)c]ARi0S=SsA0SY($YCW$J_s%D8D/P!
jh#d,=MhTqYGfIer$7+".79sej1*HrIqt0_5H(a%s/qUSV50rHB9#>S$"^aAO9-W%p9,Jq8X
-+mPLdj[Hn2o*_h5t3.I0O&>tb`,3Sqe5#UUgcj?TeeBsr)t'"4.N'dc6P"u)*%BYM$!I%]A4
ajd,U_$I12:K(C;9AG)'G?M+3fY==Z\V[R/Qhs)ApcYO1Z(<I$GSp5CMokZ/AbWd]A42hopg5
CR[ab=^#EEO2o)`J'"388gFAb)LI+($]Ai.b?i3<n6Y;Nlacs&d8_j5ei6dOg_IP8DsY0Zp8M
1G?b=4:8OM4,\;9;Z&n-TM-[QmfX2!Ysg>;^t6E[b"(Hj`V%5ci,q0B;^f28RjY!%ir>8aBs
M2HrKd$:luK4N`"#:i2jYPLh0c"+hh>s$mG[;`YKjK((WXQe+Mn4T?E"]Ar@%_M-?.\[/\ue^
??;o=<Z6)<TD$5_LNRPKYOe@Uc=^O=\lAc3>_I&>q*iK!oO&IG;n9/]A;E:qudKa7m"MZPXR6
#hP<F)SK#52f\+OQJ$md:8a@5\\4YWPO9g-3c\qSpce;VT:i+@h:latQj&XUljbjKG8V]A0'>
;+\Y:+NkU053ORj08C5)89i*hYgtBIYW,*Y24IFhC;W%AVV@6BBOup?XLN3s(HQB*Bs>)D7-
;9NWU9Z!!bL!!=j(!"!$5!#<9L"%WHq#*8ghss5c.&#eXY9D19<n9m$s"oC;D?02kpk-6.Nk
YB/=]AIQhXVFo+@J4\X_bC:=_&Hl^2gT3dQc?iU9G^]A4QmJ,p'd!!=(R!!Y--!=W?9!uSfRrk
IJ(atGSKNt\trXur77)KqqPMhKmc!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="status"/>
<WidgetID widgetID="0a94d597-ede2-47e9-9a55-29965f037fc4"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(0);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a = this.options.form.getWidgetByName("DATE").getValue();
var rq = FR.remoteEvaluate('=format(DATEDELTA("' + a + '",-1),"yyyy-MM-dd")');
this.options.form.getWidgetByName("DATE").setValue(rq);
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a = this.options.form.getWidgetByName("DATE").getValue();
var rq = FR.remoteEvaluate('=format(DATEDELTA("' + a + '",+1),"yyyy-MM-dd")');
this.options.form.getWidgetByName("DATE").setValue(rq);
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var rq= this.getValue(); 
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr enddatefm="=DATEDELTA(TODAY(),-1)"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[预警追踪]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r;t;cb$\6d,<k?<aOf&h'mt]A2_[+)Bb.:W+F^t6#/P?"VR(jUbiSg+i/+2="TOY:o"8sK_
."$D.U\:,[a/p"B2oU8M([/+UQa'Jk\QC8ci6a2FM1js4VsHB'9/5kFO]A+htPoIIeWRukKGr
O#;adqJi3fV&;ZE,rq>&-#XffBr2a)\X-p26TFm"I>Mj[Bn%5YJ]A:lIj`PT<d_=`qeT,n8#Z
g-uOblPo$F^MUo`,GY9r0monlN6g8msR8[r*e6KD6*,Crr0hl.unO.']A(fhH:,U61I6f5A'Y
9orp+Vg++LnY]Ag`n.s&gg`Ij_c63j)eJ"#A`nV_BO*X_?^Bk/4pU[Aj<@3IPI/[hAjl4kVVj
)$sm8V[Fm'>d:F'<W!Kmil>WjI&5:oTedEg#k/T/W!'3&LVeG80/N!_bYF2%KE).W6K_[o&>
bZ'4#O55-jb46_Ptjjiq\kMG0);[9*\4,_:`ILVP@![=[K-oZRG0rJ`*>M++h773Qn(bY'm+
d-Dfj:Gu*VdN4T^WCYVi2?#_d/3;Q/[L">^uKa9RnF+WJ6)AM(^S^d(OH)a-9icFMRT4Ac9h
)V%1-S?Y6).]A4.)]A#crLfs@,B5PC5lD@]Ae>3m9s8D2e:e5tRd(d^i=H)FiPMjW9oa",BSjL-
U:Y&S:6dY]A`2MuRF&mM0SX![c5-@rP#101EF0@pT(Ro/#_GB^0KLP[A/VJ1ULeZ<N`X'V`Rb
c5*ZfZb\4p+=m(`YGS5i)W.XIB%&gHVs;&ieYt$RV]AkTN*&9>pBh7$6-)8b;D'"M9:m]AatR2
kUrq"X&_Et5m"@$>9d'\H)G:8:t,\C/_N4PID!+Z^NIcMC+-KfJ0n:J=N)KR:n+nQm-4"BN[
&4+Ee6JNFR&Tu5G)SljA*makC='A`I$j%KV2P/C[SIKRQKN',R+7i-TEJX^$lbBV*r33bL2U
t'PFU,Ju0\C-d[8MZ-aeo?Xc4D&D9[1On8Z$![E9'9ec[U-furcm)EpIH`C#7Cfi'=4fuC8T
'k!`,r]A(JI'rI.omd]A8"-,3u%7I9`/:]A\;7IJVO,\;1"[BW6Y-[>jH(*,#acP3?P^1lZBfDk
2%R@o'guTedL.ZbadmLJbYoEUJ(L3^3K7@HG@YK=rcgT5**Wf>^-EsD:L.&WWlsrsX1Hj-Eo
0O]A-8#dAA>h\BVY:FBBIE,.85$-?km5uo^sl*pW2'`DC7D>m>a(k)-e/7q'J$th#9]A;3+gKZ
j``$AY+O-[!,jT%'F$Je[6fslD:C-nrm<hR/K[)%[[Pp+0\_.FNY'VeHlSi.IZs)^TN?-4Na
U]Aa0W&=j5p$,Nl\4t40]A=G#t)Bkdnai2]A-fNdUs'kg+$@aQ%g5ZWJ4%j@%@]A]A[9c.X($'oo'
/ulMBsh]Af2`BR[tbGX`98>QV&4K4T0:,`QT.UP-O0qqn#)2n(2-+*]AI@Q?7?(93]AEL/Xt;:\
G41e[]A!!S>R/4YWAM0=&-U%*H*1bqlMuD[drYF3*Ac$f?U8ggB>>#s;"!XJ`T;WfPCTVbfk`
rZ6Z6#G7Cf[+<2D&cRS>i8mlQ[BdiD7+V&a)bJNi1asf4UBcj7aM9e#7SArJ,QmClN*6f)_,
q>jBU9dkulmcIk,#YO'[*<%7F]A9k%b5G=i+3dOu<c4HPrb.R[n[193[&.ci0,2cP7t++gnub
"IpY]A;p3=)Ok&B/I^e5UF`lRT[5^jL!Dp+0Xhgbq,gKe/Cd;,1cf^;,GFED;GNk*bHX_.(I-
ciItSO9X`h"?KDQD:A>5!:C/$>&`%(&V#kt$66g]ALl+pg'8Z@Wco)p(*kO4uSMQk,.:6LK:&
GI%J7Bs(gLj(Sibd\-iPBZ3eHdrDb<:r3FtA?SLY-M:#s/n__='U1FT$0mnMn`#J`D\@.4F`
)`2b]A%58"k?kWG/'-]AnJW3g<*O,SZ7'5WZk<4@Q1A$kY)"fQ]AE;WYHD3>]AZo4A:#J'jZRbn&
*A2mOL":V]AWUKe!a)mGIta4?R/^C[H3^c&:0RD_g*]A_t`lQX\!?Wcik&KH3!#>_C=oA7g^%C
)/h%NN]AHnXgfFYr0maY.$qO5,lP$1RaM*/LTud)O(I`PB(\;6/hOIBD]AUpgS=2LI:;]AN4d`O
8GMQQ1t)l7*5S8hWMH>&fUBcOGnq\AMPEm&FrH.&X@7LuE1RVUnA,YkC3,9HI+n)3YlcZs;N
/#2@b`$drE.[O`%7C\IB4RZ7lb*lre\aJP8^GIX!\HSj?6KbDLL#AQu/'U#p!c`n>,">ADNI
R2KF83_P5$.';UQ9e*M_?6%agTUNF))C[(;TG,+8=6ZCi?=Op<egC$u*,n9l%sC-F8/B'A:+
,0cm*9pA0)M@1Q%S3BZO@OrZI1,,)\=)a%"-d/-L*]AZ^MJUYfh><sAAcTW;k%K"bIhUn,c60
#S(8SflQQKXH'f!p0)-H1p1X+t'.HBdeQ/%lYgn+K[uDN1"-H6?Pm$4R"KuX%<rM)bH+nPLg
Q"@abeTE7lLO_,.qe.87[5L]Ak<;5`D9K3`,BPitC*3p/6#cM:]A_WcLll8Q'DtjDgUp0H)0$O
Xn90oh<A3q=X/fo2VoQD+-0\q=YqCpB<nY:KPcc-1$Td$3P*tNL!\e?i.@oMNj,aT-q[S<;G
kDq@);a1?2aD@MeM@ZlHV=@Je4TfIQK<@98E:4FNNQC%?*D*=mVH4!-bl16]ArgH4k7Le84Ur
io`]A]A+o)aPnk!H*iNu"\LdpSW]A;j1aNH@@J>[?)m.OATA``sNRY1EN^m3?l_2`/LQIqqT,\,
92+-8r^PeE"aq!Gbd%\#D:S=6KcR(>Kk3TZ>3C&GC.N99k,(Y>JOcp:L'69q*3L'"3H6:5GM
]A$6au::CHT6YJ6D).CTW!4S)n4lnc2P]A?a^SBDf1N!8,!9UQ;-(MI9kK:$cH`YPNh=cIJ2dI
_s(2#X?pak__/^L_[qPVKHUb4#X]AHG&;Dll+Uhcb6Q!ZOL+n;>ruTJ-9C8(3msFdKoFAHG;4
f!C>P%b_qJY3_>@76VIos9<eh9(qc[U8tF7)W;X,!$[8e>dmr9_Bg=9o:C+Y8;#YRhSe6<FL
$@3I3"58R?EoSpJ#PMT9a0>f^YAA!)6hpF7[!W~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="15" y="15" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SCTYPE"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds4" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_cwmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_zdzb_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="body_yjzz" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="a8af5bc9-bc8a-4045-924b-c5d06fdc6c06"/>
</TemplateIdAttMark>
</Form>
