<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_khzb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in ('jyhx_zhkp_zbyjdt_srzb')
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)
SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_jygl_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID, MODNAME FROM ggzb.DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '经营概览']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_jygl_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cfzysrgc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm}' 
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_kqfc_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rqsx5"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID,MODNAME FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '客群分层' AND AREANAME = '${rqsx5}' ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_kqfc_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm2"/>
<O>
<![CDATA[jyhx_kqfc_zbyjdt_kq_hj]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm2}' 
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}' 
)
SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="tabn"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}'
AND TAB_ID='${tabn}'
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="a"/>
<O>
<![CDATA[0]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[经营画像]]></O>
</Parameter>
<Parameter>
<Attributes name="kqfc_area_name"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx5"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("bp_jyhx_kqfc_tab",1,1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("bp_jyhx_jygl_tab",1,1)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="typen"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE('TAB',SUM($type))]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth;
window.objTab = typen;
window.obj1 = "";
window.obj2 = "";
window.tabnm = "tabpane0";
window.url = location.href;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.tabck = function(obj) {
	if (obj1.length > 0) {
		const ment1 = document.getElementById(obj1);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj2).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07';
	_g().options.form.getWidgetByName(tabnm).showCardByIndex(n);
	_g().options.form.getWidgetByName("TABN").setValue(n);  
	window.obj1 = obj;
	window.obj2 = ftname;
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="3cf08ad8-a13c-4108-8c39-f3d0986d3047"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题2]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="2"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('D2').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="D2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="D2"/>
<WidgetID widgetID="8126ff04-6ba2-4bfe-ab28-2d09f47083cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1238250,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[14084489,436728,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[*注释：点击指标卡可跳转相关指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i5s;camQYKs5!TWJR,KN`-,'uZif)kVp%"ePoc'!*4`49Gf/gYs]Ao"OKWBJO!tLU-'`hp]A
(pYQ3_/uKHs)Z,(]As=99"4:JLn0NqbLj;s4s5mBC=RNID7RNcCFd&n!lBSp4\:U/6]ARi!!&M
E]A65.oJ9e:Rmt1AaObP7>cN(cPqX,A.&*;/;;QF"NkcNXL<o9M[\N/iSd!+"f#H>a\6iQ5D3
+0I?bFNpo+lH1.d-WEfh4HK!PccISmlt(r&YFA\8@J0dHL$l_\KUi(rUW8,3rA`ESSj>ijl>
o'\ZrS7'+@p7q.sR#?lkT:6ApYBbS;(uj^sF<e99-WYqooZls'NQC?-&+a+W(;m2M!+rtI.i
dZTeYELd75G-80G(8-rp_!/j/]A7K(uBQF1sc5j=boNAdDD')6:)n0S9!'pG0^M6qf1Sq<;*V
,gA7r_R?YnPih2QJ/u4qHN8SU/LfUD=go]A\]A6E,K8Ue0lY?8U\,=[i'&Ni*%A;T#hG4J8def
MbH`p\r/lgbnmk`9RoF3oGYM*Fa)^Y<HV&46@>_0'PV1q$!FU>t4B*<<EMAIL>;(73G-Rgbn
rP6^RE/BA),>2UB"A%mjjtUu-7U).,U[t<*caiu5T]A)'97mjZ7j-?8,VgYm0mW@d\d`DHJ30
0$70'b^a_a;'dCe4'1YJ0TT+k-b;rs"$*3h%V&r4LX*bE/7F^oOhXB'1t_Ye'IeIQOPDkT3p
f+;ZkUXTThI950N/T.b$obg*HHepJ<deUe&fIQBTbKChV,<STMkF\`UM9bI]AEQ>4W+NgNn.U
d$KYpON&Q(1iA7jU\&NG7qb'%`OF@]At`7b7a0k4B]A\YnhrfpS0d%IXCKe_^e^2>/ZauB3P`S
liJP>1K)p!I!r!.h]APc`cGRW'R,?O[Xhr%/m/cF*J`Ne0_CI@VRD([_+WKc0Jpgj(:D&UN>0
Kt:sl-EGZ)IIE<'A5?mQrF`$V$fP@rO,+ZLm;##O<b/h3?t)+_1EeQK5R'3;TnY:^(_Fe1N$
nKPZcN&,$*,(9CSuKMqa,P=I50.k29=5:T)'LWlQOOblsrpPX?ap#MddJb7?M8#O6*mF7XVn
q8#hV/lq#(YPB-T7;kqLK<&p?>a5JVk4Y\0m`2?"Ss0G4%PsGVD*mGch,^d%:[/YhP0WSI[!
#`"7;r.ca'G7Md;oM3=KVD!iP(V4C5kKW_3fYX6JBg@>2WETU*KVf;H]A=T.6Y'nA%Y%`W7N]A
u,?b&_]AQl1h$"%OK96s]A4[f:(ENONYTXQ^"m('4GEaV_FUJd$#[^H6ZF8U/<SZEbcFI8Z;/V
pC`&7He&51)=S1Un!UGJ#g,U$Vn(BNi)PEBo82]A2E>J15(]A3KD;^ngP56J-bg@e)X%5&8)?]A
WNm%=Ot9M-SAk!pH8c"!*\3GU(Xjq/Z?\/<15oN0/d1<t#Ap*FIm7)=U&O+O^q`(]AO94^F>o
#$Z%KH=jo87bddGolVK7B=,J;EboF!VT84\6gGh8I=ZDh$U'eeF')d8jM!R/n*5ZK)NZ$!U6
A;ObaV;pAC1I29=\.o+IV8K-=o3nPB(iMXIGb$;2"IY=WQHdj0.4jhD$J)8TnYs0XW^$\_Fc
HJ-GWbo+UKW)^`OqUu^O!;PC"nT"jCr^\YM7R7u4UM5&>L<)P9?)El[^``iugmo/0q\mG#2C
;DS[f8oNBU"YI-j4'167'.YM03fLn>OC(!=t6Q0=ZWH;qFa;cbH!2bc3^9-n!6ne1@GbkmR?
(Jq%&\MLJ',WS>=/s=g;g2)f(-oMCk"gAPiIsDouBW.%;-Rb^Rtfa6]A]AfWokGJ0cQ?L^FL>:
gAS4Ca:8In]A1SBf$W?_"oRT:R<`&Ra\=G@gc0MSCFuo<K.iho4;n;Y[=s;KWO2@!,#%ub->j
"4kF2@\ZN=8ld&$"QZ%<S-,XLQ#0fRctiA<!-.Tj4_jE^6%2)lT9T_q^:4S`Glr4?kF8F*\r
s%O$c"HNdr-]A%F\ZI@g.:fko"-^<bB+Gu-s8]AYcTkquZlO:i(L!D,[tnTI"Nl?5KT&%=B7K2
]APL6\@<mA%]AUt6_l$/cQ(mp*Z//6Xn.kuA*/e-.jkV-!q6P!7"r/8o<sBc0AU>1c4[fc3MEr
qdksOL;WU<9G>f$s)jDVhR(K^G9[>^:]A'(#ZAV%ks6[?_OA>Ls;D=-..k?^1KF#u3fVc$(C^
Xp0=!rfOc$#(Aaie89*o\d(`k0CD>"<NZ.U+U^Sl#4J-[YT9U)oF5[t%V_n%f:7oO=6+dO\[
^)FEa;b7Jm6>GFR'QE98p%9hncE.SWl'=GMbOU/jb<K4'LPXBQ4X-%Z+Sr5-2R]A,6bhf)[Pa
^`;$?h.pY.am>X:+)q0rrUjZT,VQY*dShT*Nq&S*N<@&Y,^i6BbPt:\SYoK6$Ls;\)+?Z9J^
APC`DLq:@l,QunOF/h&qcrHH'Y6ti%%Tg\Oq;!51_liu&U@pEHQ*4WoA1r9\qZ$;!R0`i5T9
j]Afi]A"l=.tad,2--E;==/"_&J8jG95F8A.m\^;D3GqE73)EnVfg)CWDjW.KA!:s#Q,0jbCVd
q:ica>0_JIQc$)l:k:%885X`s&N'<L9[jIB=lV7OKC%fW3Rt,m&L9#&[I@+jK2'6?"bQ@/+q
=9K,i!m$F9F,(VJU%=cm?Kh)(Es^"ljg<id6F6\h[s,FBp>*r3k`[SO(sL>mF^/ptOZc;$@8
q74ckEV_DiS?!(!H`Ok7O^4'ed#Rai9G$0FS:Q^k4i!:uiO$,%Y^p!"&s"7%>fm:[=;@a%nV
Gr0lS?n^V#V;^A/m4c94kD]AJBk\`t7hg.Ygd&]A(B1.U-#Fh_NC*`pg9X<]A-bTRP1UqTK?m_i
f2+*LiFKhC/17\DHIQ:OrnoK;R:\2/S=s'A?BlP^Ue_!oJ]AcHCc1=@J_ni0AV4b'I[SMjSYR
-AtabduP9YZ&a082AM.o52kFKf./Qs&c:VS<G*W^3JR.<L:W>\0UsT38X?D.g!oc!YuLgWJ+
540j[LPrkG="nRaa:IX#FNI5828=\"7Cp#*C0dI7X,,lo_M>*j`dbnr(7`eJor&9;^lj68gH
)\IhOhS&Ps&T;mm6hLI^T`kR^N<?fod"gSV['HW&_bXI#H;\8!c^7rFl#`NO(,9<aILI:Gt;
9%lDhnmr[ol3pd&)!2P)oC^+kSnS@,AFP>!KV>H4NH\H2I:u+)2^)n,t'1O*&h)h@9rCjb*M
3T7Z=^]AmYU:o-2pd/rq]AIApQDn.<n,q)mK>FR;?3at++D+[D<E@s;0'McT>-2b%\J0mme;*-
:Id$DS1=AW)PS7a56>F<I-/kFmX`j%S^IMr]AH)Y:63n`f&4-XGKFgHU+bUCn#U+j46MUS9-;
Klli_4U?1Q.WEbKb=_K5EERM#^rF[rCKi4u@>?#i*!NF0Q!9&sA?0,U=N>8Ou/\P)t>B,U!I
c8H(9bfpa$9gpP?aAB>8I%LM#i^F_)4?N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="70"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="541" width="375" height="70"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[524786,723900,723900,302149,492980,270344,952500,206733,952500,206733,952500,492980,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="11" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗2">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/CMPY_SLY/演示/YDZQS/经营画像_弹窗1.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=FORMAT($$$,"#,##0") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="较同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'-',''))+""+if(ISNULL($$$)='true','--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="11" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="13" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="13" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="13" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="9525000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="4572000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCRX?;d%^9C;2>E+J>GOZeYma5nQs"6:*1PNG)-#O==$E'p/bWL(IQ9Ba%Zb$5,V2"dL5d.o
R?_0J\7ljqpD$O:]AD7&sD$kcmJPeWk[iV=8/>69<5-]ASTW67qs;#IZ^s$"js7fK>#Iq4eZ)W
t(;CnS]AkWfL"T6#X%<.*Td+5L;Q-HDLiIbcp#`O4;he'>3;aE0Rg2ammL/XSuU3:']A8UW@\O
3j-*n(RWcdTpfjF0ET9,#+'pZZb/"q(7Bm4$3.YG'kdLR<88]A`M3eqq:Rg_%Qb]AET'MV'b:]A
6_Le;tkK5SQ'q>!l,f$VTl;kZt_'IOFc-Jg*NI4ipgc4TjG3B^R55OA#6U,]ABU?a@=^<KiK$
;l"4RG%!/4`[IsIq_0H+[3[3+5Ff:@PAl;Q!'GEf&Y6]A%Qm+lrd1s.:/2?).<\8pKosRE(fJ
EE#"j@LmSXU6'WCLT7Y!7lu":1+)NE$dpaoC%C!c!5@JLS35!T&)8JJUED4"utM%r,GCeg,*
:,f'_U-f<*?C\n<(J1Ssql]Ag5_1IpAEN]A@(.Fi<s/@X3I.YHRp,m<89/)-i7"6,GjCL5RDX8
Q;cc=O#,?(J]ARWs8FDol-VCk^t[a*9B5"aGT(C]AHbA$#%9tmI0#_gYVY:8=NFMY)%_['DAa,
Y;cVR@FIT`KD5#m:'O6#YJdb<>C<+)6ldV9ld;61<51CHp6-Ge.`esQgCl#9UVj2BCo1D<H3
cDMPmJhAgCqXT#.)#W/rf2Dn>ie9lmb$NT3._4>A3X;]A4?T>SQr7T:4o%81o^FE]APO7Y]Al`s
8C60fqLk_;/,NlAH+#bqKXj4O5UV,Yh3d=?PiJ+7[mZhdb-'3s[l_^3h`C"-4g&-j^[Jo6^@
_FiDotl;U,?dY4\a%mE5\c<\:\>!Pttqci%a]AEJgEHqNjmrWbQf"6t]AbTGsK858RqqVHlS(M
&N;!Ir+a'cG-1u>gLH'EXh;6kFWObb)JROqC]A/eIRd_A3UqhadAT&sT3cJCQoNYsY=i%=,UY
d#$5^G'[(P;J3s%KKm+iV2AIVTEf,#qSqY#sQmAa+TCZ$W(SF;Rum$kZ:;Yg5LDT,R)B&^4&
r.8!8jlkq`d&>NV4$d6F,^+L0"qTJ=Ye@EAn=ar(lti`e3bJ&$iq?GO]AaqJTL::1Uh,n`0&%
5?I1Y[A52ENcT:;Lcf,K?SD\^W*.Zg5N2+7Il5B;3s]Ai^9Q>pb`r.C[j7NPXk81-IkG=N[5?
NB$iC3,@,6o[QJqBmEX7763//Uf/"/r\VKuU,C]A0IDeaf(+>$J7D!1-.dMWB:[".a:TfP&FY
4EXF,_9DpVP:JV]A14FR^\YZZs#KMbR]A(@de+G:J<btp&Tg-c<Pia`:&JhXKE$I[A!q8WM?ML
RC:"%elc8D\FC;*o#-\Q/!qQk>pJ=,\MG+LL7ChKL:T2?EHf#cQlc:JtPnY_ip!q!iP4insn
H?)Gd/uYap`U5\Q##pe98.lp?;-[_\?ee(>AY3TCnN_BMnXtu`rKKLrSe=bhP[4"9X&eYTW?
:7-p/b1ACMgi@H=?8MD(Og^L3h$)fO:hQRNCnkZIG*ujONF&:5N!(j#>c?%'dYdf[LUQHN`*
SDYhi<b)%4Y<@49HPnEf#O1ri1.ga&%EFg_#Hb=rjpFH"&%q1'fG6Y:+jqp\6gGp8gO6QuW'
WWI:,1Np0_S.e$p@oH;?mX\<5>n(qQWtQ49u^Zi**k2KbU",3?J2f=3:REV@32mkE@mG=9^H
))B0FiNOXiMoR]AO8jIqs6G,G/VM<"LnZM$JDQbcDFk!F9*B4u#"(Z%rgNNf*=_8U\+/8_'ia
]Af`?m(,ere`b-cI>XQOm?m!=(dCWfc[+=Dr_<IECPgl@%0tfYk.gM.lV;uBTp>0_2G..UM/m
_g#HYbG4DQAh)j_jrY_me=,gO;+(n<jUSH$OM;`/id9`LS$?iMd_#3)NkkN`/HV48"jDcg[q
:=<g6J6QOVV$4e]A9#%JdTB/_mk?UfHgj"Joe?Ub"e55!Lb^,%WhlF=h4efm4@[Io9Z"dCrnc
=)e5$hN=T]A)eF`n+H'Nk;o]ABDhb'A:7"):64)#>T.&4kJs)&h&XUQ$F;=>kX"s)R[#XhGk@S
JV9e'rW0D/7bRG1psfl``cr?lu0@_&\1plQV0H0aJmY`RO`QS(SWd9Pe'j/L+d>%KuI2"hUV
]A"NTTGp9eDIttnDRslC(1K1hEc]A5GKJ%R)BMgP4-rBoRiE61)o]A67bJb=YSS"&U]AN2&!-cZ9
\oJ/MNc@24-jO9]AdUchm2!-[''mDW&rLE%CKR;`NFR..Ab1`^XB1)ek`51jl<m_nAI'*7C5L
tjP1=9b0N8\O:hm+q=$&I,cQ_^qSUZs7K(C%`TFVAa&Hsjm`6'sQ=A[)1:5do&a\K(q&6A<]A
g[nV6^qb(V9.s!/RQ`Op*TVgek@nb7-`7CFj#";pmfF5OBVtip,V\.Mh[IL4F-bc]A/EDc"5g
eP4\d0_3i%lRVsCaI_2VbnV4C^`^YS4T>%MQCcp;5[!;(=<k\>r0Y0ZF[pYSJ3aB]A-o+".Xp
W8CmX_r9aa54bO\JT4Jg/qY%T^@:jjA!lcUaIT)X\>Fn2rlc07B6_N5EU.\WAu*8@!m7Ob`U
BF`*No7'b@LE2/&Z`qCDssf60ETEb4Rj?]AHT@]AjOWjdhCUhSD?npN(h*TT[=>./LRbi9eh$;
35eUDMEiQ\t7Z*<_2Df/fQ/`=:Uq<aRi2/2?J4u5DNT_PS;^Y@V'*q'V_fR\E(B>fkUY5I7B
PX';M]Aqr\bA:b<a6P+9%4Y$ZYtA4SMZC]AY2S3d10hDM^j8*)Woe<.Ks*X/q#]AJk=d_]AGE5so
`ohePILZ!(Gsr&ng`nD6L5neU8>2f_rIpZo8iKfIrcEpC`R&:O)GhE7,cIkl@Mg#2F(AT,JG
#_V%W!QSk]A#44UjM-PAt6^oZ<]A9*Vnd2d_(GSicB8Y!Xddp-Nld"^ga_mlHJkubF"Y.TF56X
>M8S]Atj]A[Q@qDL2=B2!m`fjEd+Q92qVRQX@H)a.aQkJ]ACst;]Au,=>>J.ndmOB2:GJ5@r>O.q
qMnKT5!nO<?[1G_"Da:7T:0'52J?[]AA`m^teRGi)]A1R-HjGd4q!552ls(c6?T>VOUnOiEI5C
,XnW&B;c^L&0Cj>#5gMW-mCQUP%N6$),W)B[qX9pgta3@77:Nb;RD7#dBr2?6il#V6E%'CMC
F/#10&#$0L]Ae%p(D?V6f<kTdurX"I"s<iMk8LK4Me]A30q0Y\M6.B_o:uFMoX;Vr?6d9d-e^\
elI0&:Edj/=AmhM@]AgAW&U+:RS!q4PN-?)bB+bp,;GBK5hVBoGJa6gVZb!S;NijGAYoXk\#)
)p+dho.LFLD[<^m"<'GOQ,Hr"9\n?Ddo>WmP)!*LlGdo(38tG_eiO2Ej<.>+%N09BWRJ]AWu%
*P(?''7e2.ss5t+elRN@B]A<+%+hSMJ%Njl):KmTd9)]A0H'a37@*+gQW\;16ZI4W%IM</Wb;h
H#:F602:>"U*#JQI9(:d.Yse=J609*Qid=<&$.slsk]Afb',.5S=6p>FZqO@2iV:I<a"..IW*
pp^6([d"nlE:?<9+9F+ZqI"S;5+5sPmiaGVfMl/)232O[bt#=0n%Gj;S@=GZb4B2o)/^XR`Q
"0rbm6^#/4NoD%7ICYJb>?/)t>SFq%%:CID<+QmuLfm+X30"hgP@nDXX3$VuK%ERnr9_I]A@i
S%4"]A>W`#;qZ4BMTWfB[(>=/KK0]AF:=c#m,7pQ\`h_0PLErs?1%1Z@NRrYFE.6uJT8he'/9r
AG&VVkMkO'7[i:+j]AU51XL<`lrMJY^o?gdkuNjb/SnusA4om@77Unq2O>'31`q+BmbL2f**Z
7RYoS,7O,@@;;,m^;bHdX@FLj#oF[=/.FtB"t+Of)Gh`#)N;ZQ0]AK+-e?TB1ddKoG'B8k=@2
Cd^1+)DD<P,4Z6>(WPW)\G1-WdE`S9SB+f&o'O%dao3j1OOoJG'P@U=ha+n)SqmKHZffsONa
AH>J:8Uj!D::&&96d^27+of8(nSmn]A9BEl1Mg\bu_Vcp/lC*[l8^j;9;VRB6HV/<Q[&hV<`<
C0k1A/XKjjfUFE)R,4UdsMl@$Yb^L_&$9ZS`JZ6+"j@P(8J6dC\u>9oL&`^;gZ.Vo41/^f.2
n^b#(j:2Q#')BQ')5br@k[T"0XgA_#VKX0R%o&,rbBeOjTS>gII7Xa:+F0[,Y8,UP*(%%@"G
M"4p^Xp*9ArWm\dnCQrJ5c:CI'dYOX:]AS)0(98N2YSB41m$0.BGWEcEpE=*S8eOml;)CPA$[
XE"9#VB9Q-OSVpMEP\dVm-dVC-kVt-L]A:Jmpl"ECiB8%tPf*]AET$S^+s@D6q`p^i4=bE;[6L
$bVc7;TP\rp:uskl?AN[_ibk4BgE1Fj))O3;Sq+ic.]AW'(6,2.;G,`hoR:i=1joZ1!D_HfOI
*.F=N?YPN42[-EW+)t"7//DMePA>JRkSf?1FM-]Ag4ihES:7#'Q$9TqULlLOFMXRGJe3+%>QP
fq`ck(kk=B&['^*ZfVQkpWIRA7$;C^-'9aPs8\6I:]A==OI+p3D/3:)*EQ;cnD[.kJo8?+6O(
or;5+_Na[JJ5-RdBjbUl#F9&e^SOG10(Hhc3N<GF[%hdUE4uI`LWb%M9b#T'nFQ&MG>UeeVc
))kKf,g2+Hu?GT"l:\1j]A'!+Ec-<b^ujY96j<FtcCG:+_''D#9D5_4GL7S?fs+1IbTT55r'(
?'g[D';7FDn++=!#Zmn`+YLk5gr6Al3qg9O:H7[P!E5Tmp0(kTL$X0Ls(&>>#ZpYs5(uk6,!
]A\BK`j0BXub`U\P^etSJgF<=p.*Cogqcp.*!0hJgkKr8\EuoVa<l/i@=#nb'f0D&;aikID1:
DK$@VmfaDK]A4Ek\uFT%_8>uRKST[\8DkGfQ#[NnGC`A;j6=KEjb+.2*aG?em$0-'W^(U;+UN
TC9+;PO[U\dR$"!E[472l.q'6@XSQQ$R@e5?g$C3Z^!C=EM[IF-fOI^N[Tm"RSJ9R>F1,R;_
!5]A)C4`A*hoOASiQWaR6sM.a$C$J^7;u[2^aXB"@[:GbS#m)q%ROp,BfghV)9#8U9DdX>#`@
XUPi[9-\0U4p+POHej^1Sue&#p'&oD0Ij/d;B#VbM(4%b9ZY@.IIUS,1d"l2q[lrpVLnmS#l
mhO>4pW9,3*0(qX6)n=P%Ba#9KbQ'4$^/?rTrqg`B!RT%K9*$&XFj@o:BMDUoPpaC_6O6=j[
Pf4J=R.#R7<`hWYi]A)AZsOG8!/T%R@G=e91.m='@HEc]Ap$M($$fTFFJP7A?gN='-E5@6C+fa
sfFL<P(4If-=Ss-"@ra=\Te;SZdRr+UNu@Hf?>7TX"9`h7dCe>M"*K&tc^(+->noI*lAfq'!
r)0#\fFYO_pnZ8NrWe-P@ZI[s,/;`Un*Aj\9t-2.ScHq%V6A/9<h4k:m$58V*<G"\?)85ktV
Yj0p(Q"cAerTLrd1Z#gacK[Or2a4_9d9J[U^b/MMYg:$Mds_G.=nkW+>c1*NS!PYPe#^/TmA
1A5%`!%Ba;&(9F`XGPj!RoerLU)%J4d[H'Fs'q5fAVV\34d@^icX<]A@=fZIrQk;c!shi=b%H
+I)=_BU[ZOQ%W3N%Zq$"\IEd_Ybjp$@l6!CKd-Ls5Il<P*#ZGTI.\Pu;hkkio#A#>,e2A!.1
;@+D:.M8f?-KU)?VR-eM/[alQ]A:[d-'[j,E^SRsTtX[<Igrs#^r(8(cNCs%Z1QLFQ04qcA^S
[b2S0"lU#*PcqtuH(8P)HUN(@oC8dMi]AUY@$nc0PrbnXIB3G!murNK?t`l_Q7:D2#M"Kn@#f
2'?.!55Y9R.TGBh[?16gTnIXMC<[GaLt&5tA)sr1m]A<(lr_Rl8HNW:,fD)4BQ_<s%"0_[RAD
aR/b)*olc!SQfFkP<)F2b:S:-Nek=<7[nEJSl7NRXurmOHa8r6V_1BBq'0@@4Vc<D"OKW(K'
7*!k$#Q9?").N*0WJ$!#,%Y^H0f:H`tQUtPpKD#bFhD[L!</:)J0Ygm5MZ-.OMVh_I1W<ZtU
Zgj`Yu?&bSdtlKX#a-NI;NnI6+GrNKV]A548?b?<%;gi>qX24XcW/d\OcHj%@r`?6^,gj:a-%
>[ig8]A1@Rhh;pp297g#k[[[CM-\quS__R\-CD8hmr,15f>[h^B2KF+[#i>mJ:!n6`9VoM$rq
68[j\LVO(+H'j_W2[Wh:Q6sKh+2;[Z$;aJuccbo;4oad<X'&k<rgOJ2:Df@.dp-ONmhE=`SO
hK3\N[]A9CGuUc5P@CPA,BER]Accd'ieL;;Y/E5OU[u7[60B\qdIsIdq\o$o=S4i?rb?1F94Z*
r_=LFGmb@SBTj\>XT@M+u;9JCWf6J$C[0Z]Aba%J.l*ml)#R+df7hje6CFe8(L4^;5mOB0XV9
3TuP*8*CN9Xt#*BVMM%26[TFI@7TPN<2:+4(VuYFP;oB1T%?qfB"lMFEGLqs(rpj?I8-+oq3
*PmEBY=R!+c6e3TDH=15%VWs"&XT8MQp@F""r"3RV.1fqAo)NKIT,;kUa4\>kTrIj5%eODPn
;cD!eY7@MHFrC)c]Ah@YnP7BZs"`UrC6u!\\JL@Uc\QR.MTRCfEM9&JJ`3Fh`Ho%;50TBK?IY
ia'mu"o1rgFHJ"a57U%,M?*?o8ilF3TnN*Y'l#cb'KJA/T-$3Bioa"hAbb=a.P=72$aN_%`e
S3Xe'HJ<3PB&RD#]AKSL-iQ!"1*d1@0i"f9^aj2r1F#^u6;YG`f>Di2Q_-(+%PCA(Pfo1/Ecd
sU'CA3iEMB^OHTRsrGFq`mFK7q"p(&lc=rr#<<>kfVfsN#jDR;%fj!Yk]Ar:l::`'+_V#5D.^
;?GMKOmk^VP+MbGbNm'YV/-$QP]AI-#"'@)rmGh:-DE3"k+e[`l?Fh.YR"SdToF'?`3H*<527
$Act<5X=4fbe%hiLAo-7J"LFbe:g8D=C\BX.[=FF"(hoR[M*QQ'UFqfZdpcS9[?(_@fR.V89
ZY'j@L))'J]AXaC@\^`s,_G*%95`\pXS.5R`&(m3[3;4#>t7+b3O%XG#$=s?sfiT6X!)@IL%>
2.`8+=$-#83%'&f/o]A(Wr$&JAf/MHcij0q8<]ACe_s17B9nY/YP_>W#ZYWr1,T9/q!]Ah2_osa
.]A\(k/N@Pn6V)d,YrN1q^cbm;"1_p9M5;i,]AfYH^u%F;^5eWc5#V@P&LN!PkYEo)d-WJ`O2>
Dn9PE1NTjK_^;#/5R7rH.%o_^BmfI[3aXfb10(P[=ed=;,A*33O*%Ip1R<kr<I>FR`ced`N"
0Y1AAdu5+sCViEYF%QS$,KAi?\k%hJ.Jka+ftG1)!JK=Z*T!r)MS,6lPm/VXntKEZ2Y;':j%
T^K_V8MD^7X9#TVQg#@T_:GpBcC/F[o^ITr;IH]A,Za8F4C9QePY%Ra:P>F52l3p_BaPj0EFF
Uk'ud8=Vd.,CWN.6+5h5j;#Xi4@JanD@8aGS;X3#UI,E^`0@RDH7uCE&m0K<\YeG6\?#KK1H
g,E4*N)u&^H$8o"jV<Z"WtRVBYkVOm]A=""=O&@1U7P$1H?SqgJA%)gB"H_WVsLls($g5/=B(
t;kjRn.daXgqY4)?TfuAC5'TVaBi9aq3*&dIZ0'dCrq:m'(1(+o]AcE]ACT\kd!_R-KnWI3G+&
b8X#9c)802?QD_!('JBpW9D)a2XJ4QK4DG!eo[F1]AGaJJA^6g@`g6R/IOKr1@5AZc]A1fnc[&
c^1CDUEM&=HuQGX%K!KM#<".F$rY1J+Bsnt#^g$H*,)!t#[Y,X(6Qd,OAW\%s]A0\Z)#O5<$h
@b/eDi4'%"RBh9]ANRYDdMb[EY9]A7mVJ5=@\#]AON?VR!O148tlpq=btA?M)pDK`Hqt%^B%[YK
<\7n";_CAr0mFVD1T,a\m4U6"*Ea-s#<!%5b^_=aAo+SeR02W(.'fGTFr5uYL'I/[u!=9%&,
9m2u"n=+UQP/aZW9:.8dQ$3lKoD.U'g.EdmU1$tNUmDOhZR")AOT;h%iV\!OaAffl=c6(49B
:_']A0d"a[0p%=RcN^G/K8mI;W-d&jp03<!E=.)B<LSuUC0X#H#1?Hir^P!!JO>9QV#uI.W#O
4Pn3`WS_BMG-=eECr)bI$J*\#QO+/3%6ZCl\.8&*jlQFSN*V.B*9:hA0ni='?)&-Mo0<P[W]A
'/flo1(Z2r.=03\A5<r,L[09_E,$u;KG:Xs@^kA=*4F>GSe<`FA/K?b_6JC\nLXp<SGCp-f\
&st'PujXMlbjL--:_\^LCACK?CY$K+"(n^SENk=2MO6?AkME8O?F+)03C67'SO&;d:SG'2Z"
8)*'Q9H721!l3o8h:kBP4IJeM3l]AV:250`AQ=1n`s'338I"'2jlEq/M^B_O8@12[Va'KQThu
Rc!92]A.9Jj.bu_TXul9RRGnA.UNrW&qFfo$"!UW#YH/3-#RZ]A3kp^mc'uCAh5M%b(Khj/"MD
KV.1m/LLF+.brR6CeWb9E#'>\UA,EY69r.)>Rta7B#[#qFL[qFKq9ZkrSVD.AF#SfEGEGPaO
oX;2D\92Tro!62(qh/Fp49?=,Cj3bT)hBi+$??66d]AA2tYg;Fm[@qME17P;\m5d"V!#YX!I7
Y%2")gM,$$=<VWO,@/Dh(=Vn^PheK"RD)\p:-k1n$8Ra?o3+4?VQjd,A6;d#JSCll"1NaZ1U
.KGk%Q;mVYG)ER/MYW4gf3QG_O34F/_piJXUF4]ABaS>/TrO(X\R3Fu:-qZiLc#LVKNb20ptD
J.'6"Q>h$>c-DBd)b"+7dHO=2>E>/E/:2h/g/-G\j^"0>9UL`/_/:^CNc1YCXBdceIs&1C`=
6I'O!dgX1`M#gh>KQMT`.r>q#><*,L[20TED<[$R6-he<-?4hOE4E\!.Q:kbY\8F:ZIN]A/Lt
/!Y!'1+6WPGinjLpI&1?@]AjloA<s0gH`:c8(ggntge_&7ciuQ-?"a&=B(,]Agb+tKUBrWG#.!
O@B=CSQ.PWNGk#fF.MaC0o($.p5ZCIB.A)(\nI5NT#J/>T'HT</)cs?\_PJlX?k;[t&Nf<fN
V:^ECs[)j@sEF&Wb>[>\<$nZotBUBpeU6m]An".[o0joii05MNf!VI[-A$`bTISaYoBSj,.S,
%T8ph%c@JJieCP2JnD_"EMiV';5N]A?%iJZum.j,VP07/'`:)I,@Rb8.<c0=oH61:DAXR?_7e
I1[YR@Z,\Un>;Uo+b6(d-uq&)95dRhe%!j$WL7hA8fXqVh*L;S/!"h(MM5`8r>YJtHm#=>1/
S[\OPHFFRF,AQ@:e09`s1UA87!jh^%i'\eJppS:2!i/7YV!s7HC[F)FF*>QYkho'Su(#5[]An
o(*)VJd\=&f<#T?POBah2`.@(5s%;3@shRX:4FPBFCW;1[rlr`UH:.P+PsJ.;pn70ZLZH*2k
'`0jg[uY+@G'/YN"OT1T'MOsG">':%4"RA"k,4R;oXq<dZeFM:SSa)%il=i6B?R<sGL\#[a2
BC3:)RLe/(;ZO%*flWTi\#BCM(DN=JTtIZ8SZg2Aa)KPQH?HhopMaPZ&(I`'M!qi($,L:YBc
T@oT1?>=!4-iOZYbe.an_'/P'4Wg[Zk"-D)IOR<tX6=+uQW6Q=>eo24HF]A%2BX>$N8_%c&B.
)`75oHZguL91Ob`+]ARDlC&0M-*na;[G-oTK@-FpQK5*E@?GpHgIrue9o)80+''a!@EYagA<W
j+$VRPQVZg_/a$hJpJj=o)<5<*V):5CB@Tm*Fo(kILkTCrHKn:0X7u5RS"rl)2/*7Q=G785%
EpJ_--;b,ZRZCKW$B%:r!(]A7>M:5MKEj`CMfocSr6,K_>jror09V4'/ku)eZ<&_]A)P9;:[SW
oX!'C_h0q4`E:]AZdg'0;C$.GB3GN^#B_jEnb543"GG%L2j)"0k$RfnO\2mX(a7S@B:L*=00.
ogDaNr\/(nEs_)r5'?VHrDKS0SS4$"$!%o2:):E'r&I[qs]A-GY)BpbW)D?Nrs_'=cQu`AYrE
aR1pa/4S:g20VtBQ[!?IGDf\c\7git>Qc21OU!$/E.-Yu1bi+WLGZU%?[qk@rHPP+lo7Skam
2/rM1:bG6/Ylhd_OO4J]AkR0=D6bUg&b72gRh4>WJ3+X4-CLW0QFB`c;u<?s@$P)]AVT<pCm]A[
Eng6.X%-K10uY@R)4+P,>_gd6i:.?R,sB"+`pc&%0U_@d9MOiO>Y@s=J*54(D47S<Hqd<R0q
nX(U:Pqlf+flM&d[:lZ?$*_c8<]AF89F^6YLHM7t5+F(3W./i(E%`qA%^t>tQ[RU)@8F?XN]Ap
HKua?PX1[[dsAr<PN(K1KS^ID,<J63aoB[4Z_0_1\BB_sD\/HHVTahp;/+3Hesd5o'XpdUs:
>O)Gn4_'!\Gdf[$hOCYUPIXp1&Mm@KcAdG::'2EZ4[8nA7$&7J3B<4F5.1X%hG^'bd[,5.(7
`c(5bl-)OX6mjkHPLme'9tnn#FPh"TY&!E."/;Els6O3^_CZI9P99P$5R1+op%:o-`a,n.'Q
9t[4-UPj2Lg;:>d<Jh;PVsC(Ai5L6P_W5NlkO8%&IcMeW1^ro/O:$A2CuB*f[PYbi^^Log=o
U+"L&qiNDE+4SngP<Lm[PGIWMZ!2'/4?57J@KiBQ;kec'G,NJ^X-Y#2B,?&.Y]A?b\6S0\W%K
tdf.V-^,L@+CP(=T4N<DT5C`L(,Vrp*mqXQ0*te(E)5Oiq01f+m#d9X?24!a)Ick^B[g@*II
?P'29SZ_uE6R]ATI^*[+Si5-:uh;8Y"M-N_[==b'\WX.eC?i4SS*[6c"e]AuiR#U4I00?`+KO@
[,@[KAN)iSPRiY:D$%,DJ96=q^+eg)SnGH'6faOS\c8Q5;YqZ_XH,P`iUpt5?mlrfr[k"*Nu
(UO!2]AB&f;YX]A_\&PMm%QeMD;M/R8I!nUHCnPI0D'TD>+b42mrj=\M#"[W"#e@67oG1C7V?[
-`Y/Qo)-+3cip>F^8SR_[Ei<mWI2%u^U4?-ll@]ACL\22&=eCbn`Bk]A[SBB/RKZX/P3IFo"M[
f89PP55uj0N>s`VY)JFgqpN3>#iD#Q3$*r*VY/\-H.X1kk=iPhP4["l!`\U[r]AP#qFSV-p]AX
D1;IUMe#,%s]A)<QZ)'uRCWP4=RU'YuVb!TiP.QI"04X%n+`<\:i1RZC:!B)ANNc>r*e-RShf
*3"okfgTNNDW0CPB%G`gMiZbou/QV6cQ`GPlk@kG@ma8h'S<DK`Y"bfMIPgs$QCC9)\h@A0D
rS9hs6iqmf5LWh1DO?$Ajoi#:?toW_+.94El2=#et&R(?sHf<M%_2$g:C0ok*@\\X6V[#HfC
ITtN,bNa??X[@#2En;LaYI-0e]A!NbTcXa@[*:HUH]AT4@$crX/mp`=YGE1M4XC'l]AS&rcPF*j
G0I2bRCYS5lNK2u\ZGR\/6frB.:VfSEsd9nhdM%n5MqSkfD2$Fuo]AD&^f2FeIQg8Dg[X%din
c\[:e(!SP2T`TAl;"kPX9m-q]AC=!N.@LO7-`%ojjLe1J;#?$7KB]A<2q*EJqu1"7gj5;"P4;)
Zk0@X;sm+8'fk#74c1[TQ<1@GNc?h"!I*BE>;K;m52Lhhb[g+e9mIBEDq+W=LghtNIMEFSh.
QfrV30j(M9*()%aqr([%]ApCRN=Oddf'Z=o/^l9JN+&YF#@2D&LNp%DC84WEn:_W/FlMWhZT.
rcgd;Y$.(8HEV99Tf=CXU#]A`p(Ohe,!bapF>H6PcV5mFn`JH"+i5bI(3E71?d.uun]A"t[Jd8
<@U+.?kdcR]A59Set2U&62DR/.+$gs76K:EiS$!L9+tf>o5XC%i.dSdoET&n\d4PK`"?`(:_F
Q+e_APTGkXb0rKCP\#=iqh,UIq.uQd."ccFVlcGQuKB8EJI:*s?@cuokbHjc**3V#FM\b:B%
jDfpJY'UMO`?e2ngQjmaoP88!c62"h9026^(d]A]Ad]A+O',6Zu_N!k#e^+BqRp$bAkc,t(?]ACX
kV4mD*OOl&9S)@m3n0nf+aWp9I1:4@b"WFoVNcg61`gpK,0qo9BEd)Ik?6cM`ug!f-N0Dr+O
I`7^9C2LH;;Q_,&]AI3a-;PQcJ:rUO$?29'OB7@TZ$@g)L4F"5RWU4CUc*_)7W*J8B_s>dsI<
EA;e$$2-Oq#(;N^14i79gq]A\K?#j4bettrcZZRgHpN<A:3FPCa'%S'c5Ibd6i0t0<qf4!31e
q==l_Tj.<TJ0@;>Phame9-P6!Jf1&?Z!Q4@>ZrrR9B?(d#LU:uaHs9q0-$PECkM9((PFf(2T
XN.6'ZLqRA8JX7B*(H&Rb[`6Dj3#g2#4CKkgp2qJP*llf'(tj-?j"R;&,#.^R=e9$/-3N<@Y
RL>$[khs$.AHaD53+9-k/!FM=jhZ,pNl0I(t<:a8*n'P\lPBO1$bqZ?=L5e@cEeX%8YV5LQi
^M5s"#^k1qORR0DOi??L=3a\#s.(;:Q$=j'oA=,\DfVU\^[F9Ghtr"pXOo=!q\>[=f)QJpAF
'6&;n**@q3Jh7a#51'+!KJ(.sd/*+eEf^rh<#9++G8F^Zt&eLt'O?I\`nu=]A-8[qV;5*AA-e
9(fjV[!oDbES5og;meZK,Jcgif^Y&M)`c!]Atho;/VDg)&B#W]AVGec2+Ebl=`BpO@DoX@7Q$O
8e>@dJINSF`(C_PGl.Cq4C(THk[#KT\kY+$Bn!$o]ANj:O+E94d<m!*fJl:=X%Ql60&9R"*Pu
"eg6\8/.^;+P7"cOhIXo]A0V/GFQB@_4pKLlhX`OZ_Sa'hQa.0X,4Bn,Qfn#d)d9.Jsj@"7=d
=J`Q2Yg]AW9_V<s&LT'Yg^4Ob\e=.")#F878biG<rTnZdFR+[U&X%7ESo;d8"L;^(43]A.<jl6
Z.GM]AqgM\l83Odko_m6g6X86QR]Almo43)($@XOiAjMnH*LQRU=WePs#@[<Q$aD.'fs`E7Fe1
<rjjO#)+9BV)k.Bf/OPj_[+*3gNlArJkU(qAG1O>&)k[(u$MK-Dk*8j%i:0!Z@/&D\mCkK$J
e?(lpN,Age*f,4RsMqTjjMUX'B!g`?QHt#3CEaTjE^'K:]A&,FRN.*qV.LXSBR#>WpAfWs^-<
?+@IHn$Y5<Ji7Lg'>$esl!GUW]A$O_L(-GhWjTEOar$XQe_TCqB4D-;Ld1\(<EMAIL><DF.
7u[D+F[CQ6\&eG(:m@f%UdK2\?aFXnbCY_La8PI5L",Xg<M5P?%_`\ihI*j$_QL5]AUOB5VXk
u+j@=$d13:BHHAC%Vt;d4Y]A1<Ke0qKZLj.Ad_Wde=#4%=+7975;Q1g)>"Qi#2gX"'bip2/^E
4L^c00#1L2"R0SoYh=NOoTOtRZ'c2tKA+;d<b7<;`;=2L$g,KE<'EJ3$<j-t1Xb#)5rJjGA3
W5WgkaF7JLEL`:XA8P1YKIidK-O9aKEDFrN.PXB/tL[F);8@3a>/`'[OPj>NFG16XE(Pk\EA
-eMl55"hn/dY7gnk_&U7:`q0[Oe_61ff@i_]A?46OIQVB1)[@G0p!G^,$EQqlV5^5LQ#i]A1%8
F&X]AYFU1!H)&iG@Vs)*JKt+a)b0J:J+o90f2NmdE'?D'!PQUF%L."2RO`(uRFjH3_"F(932\
M7Q'gM:3dV>M13ed@XK//9kL,7Z=+\&.`-IpZMRTSY(qUd8A!.B]AtHoIbI8s0Rl4519ShYtQ
*?X>lU46op4)Zi)8UB3(CRn>Y<:XdL1>M;DG)fdj:mCIl;j#l%mK(W5c`og#A+WBK':9UR:2
eX1BgdI=aS3B/9"`?fM(5f(:#6B!S[;*09#j?$K;Qmi>1H"gu@cPmt$p=Eunm"_+Ck\eW'FA
@,M["huYab%lD]A$iRI.$o&#FFqP^cqD2)g-)."0jq5HUM]A8Zf?fZiVr>MNo;A$1%9;k3X)B]A
@Dr(<?dJKT(geQl%k"'HR1Xcb<oabX\*N@GN]A_3$9Dig@1Cf!9pO5G)lI2V["d,J4b=>@c5Z
WR-I560e@-`dr=>%>?T&D9GbKPc$(Y6"9*#Nt^Ts2^K2?qEns7#:Y%M38;]AVAmfE!6X,NWnj
1d:UlD`!>"'9+8=s$CE-iS<H!22VVgme0q-Mo+tPdRj999AGVk.9n;SBb]A*]A+at"Lu]Ak?Y6F
KM9m8LciF_6d-f$ZHs%j'Z47d?^\j@H8jZ_RT!d".c>YX&iVu)s[bdYR_82hsE9#98]At0I+R
&<kH$IAKj^2*H1hS4ZA'\Xi^T+YDP^[_/t]Am;prlFOBIIpd*"N]Ah?RjWnRYK%HA0*M_mN;;.
-/B!]Ag;$M'ZFbh%Kb+U;<#(^c`&(duR3&&EX[okgD'q"O"gBg`"dkZd:KFtG`9J'-0"?(MZ/
ei5/2Ve1`H8uA90`cH'sgAraJ$MDpZHR7$k6V"\O(q1@g0>C/FjMIF`2W^%\,<3oV&uhPD=;
o2>QqNaisgW:Xu-VT)s%)T(Jlob-_YG3Iqp`l@M^IXK]A;lXJkk$e]A?$-H?:"",b#@1QcOA.3
UY[-ki[<DWU#p@m?q"l$_FX:1@$]A3lTPIK:7kWdfVTj6/]AqaFdX_p,&0b5+95&PYXnj8I(-\
@t[qR-X%P@Wq_QtkIG?<!iYch7jA%DL0&0@niQ8P-=(Se<A`ZcVG[HR:"%P$qa>?Ep[Vk<et
Lu4bGI7(!!=PSPVnTGJ5mTj+l!tp]A74(nR')kg^?KCYS;I3"iVD(6lD4a%AWPA=PJJHtp_?N
@kqUY'K7Ssoa(`T(17HW75sa+]A_M.;p::"Y'A_'_(9lJ1";N\D5WiNWkHCGum+eh'IIjoBW!
=6'tC5=B/k>nTiu.:B3f0o5UeM\JVR820gb+"S>@O0OLIBPIcG-WILlE0q+D1*eu<FHHq$Mr
=W%'G0'6@]Aq$(Ge?#`d&i;&]AFssr_4JKJsZ#Qa5TJ_jO#^RsKBf03u#I2d3i\OfWN8(g<b"9
n<fGB%<21"3LVh-@#plJ6'.FhsYn570.$l#K_J8Kh]AeE1A1CBOIS,NNBo))'N'\6;Pu,.A8K
MbL,tT0&YLa+gDo2Ejf:*#M"RJd:ANda0hI+?SG:[cq.=_>b69q]ABPjS]A=NT'0Z8sWR_E==9
$%Q^jjN2s"1,IbEr2da8c$2'tYsk:EiEqs5GGo%dP:$QIiC'^FMmIofQ%g='b:M7T^-!iVV*
ulhB`>li0JX+,(bY116AjeS7:.\PDEK\\AUt5WDpnA[HP`B4r6/H2.[d]AR1NE(Od0L(Od0L(
Od0L(Od0L(Od0L(Od0L(Od0L(Od0L(Od0L(Od2"s%`>KJFNm>a"K#29%1uVd,2RuOl_!MJsW
h4/ATH>T-.Ohk^)<6iHO7Dn-H/k'=QKOjot7O`=^&IgdC7Br'(~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="516"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="516"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[jyhx_zhkp_khzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="考核指标"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗1">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/HuaFu_YDZQS/3nt_Menu/经营画像_弹窗.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WtUe(M2`UZ7E-ATSUu&;3oFJ3X[-?K*pM+qUPC=,Mej6\gb2=8F$c;DV2S&HnnnTL(%5OH
C_R-jkX!6inX0OnT=_k')<!^&.5Dle_BVHZ+2\b0!n#J+ks9n$C?sT=rR-]A*^FkC#AH/6:+"
'R9heW()#='')_b.2k[(KGMl7?+hJgjNIJ9pb[$XhgFG/M$Fteh^Mr>$*PQCgcVq7omrqn4n
MJ]AU3R`H'537M,7n$4?%g9ZG"]Ak>=fC\IHB6pdecF9luLD=[mZd6a=KXe\g\<*sTRNg>3B59
$>5c&%OMPX0SC2%J5Q$Gg:ckpVRg:ItWR8nsdnelf?4'7]A?kcd]ANBXWCk6V&WmL/WEPWU9DC
.C*?JciIQqd8N>(:ZEA\&J@<a2a&^*$6WC)5,!$qs7k:d8f&'S\umUq\mtrNU>/nu=eU%`Y!
V.P]AU*be/8^+/If1McW.6fap_iq)OAPh60mn_MqY"m-O&&-EILpi'+P9l5DhS/JRb[h?m.Tf
,s-OZ=QLLNjri;'rJ&X#prUdkMr85oToD2iMhL'[[U6GW?=8q''ikN3;miQ<Ef9oF7J39s1K
S8dmc9;\N764-f\>Dk27MRsWK^dS9QCSG7pU'#j%<>Pphg?OJZD4PVG#0UG':J4+%Jgg,W_u
/L^'*J;rbu"*HCE37]AV1KnV(4^"I*oieb<j3(dnX%!bCb8RX.*]A#j?\f]AAAR_,^o0U)aop22
AVd0Egk.pp\!MBuQ>oCJ.CGp^mAkP"(LGaXM[3%DN4b_mfKuc<ki@C^]AP6"HH\J`q$It2M0)
+aWn0s(R5quG/Y+/%SqE\e%h-#<uNtmcS@6M5]AZmmX(2?R;u1E%VBW\*R0p/-r,bW_^BW_I6
d?`j&IDQVhl^h`'OBYF44.aF[U8Z]Ao7*N;VF>V,JbIur[7XGB%,*(>6/3Zu"=]A>na+.qGf_[
h;tn^1EbHBCq@5@/o#lVDe96-pQE^O7oXj2u>Xeedp#hme'D_IPMQ[FSB6p>R-p>M>Br<"3o
C,l5%)'>*`i'8#XS"$HI,.8H.XS>=M)6cWj)jZ]A4WH#P_hp6=K;C6;'d/=n7)^GQr!l1/16K
"&djC[A0D6Pm95?Kb$aV6s:!7PuFu,UYBFm;RBb7EZL.ZI"%+;rtJq5&Y;7/6&DEK<]AU3J)D
tHtP3po[UXb9bbm>WAr?q'gN<V45jXE:ACUSVKO(*fB1ii#d@%Tt'pLCf"olCO"HL"Y$!q^5
(iRC$q(%OJ`<?F]AVW$b0,\ZQYh7[m?$Rp05>*3HJU>!u#BdC/cbk/_@rR2/)4:Tg$t-!b9m%
j2L-m%n4+]AiU*kqLLHAhnihMs!TM_M<o5L=c4/S5WI^"ZK:ES7Vk(_>Kg2u`#tP]AgpZ<'Y]A2
X<aU3@@^>P=m,FE%t0d"]A`9pXB-.&@TZUr'`(roVH1AZCo\MlP?-Ve;?m/\BQ5TYHcI^Gc7c
<*U2d7-K]A?oX&m)ToD^L..28<Ql'-;8^7TB=C2/()M,^%C1bL@dEr7R)4Q-9!o5'k\X*8_o9
5p6=X405;mQ\Rb*iTAX%Dtd=is@8X`DtA,HZG:7)182$5-?]A>PG_]AN8p/\/TM,F`":&`ng6s
_;+ek&NI>"n8$L)jS<YrHDj5$no@E)XWn&tC@e7$?3u3_B#r-RV,/6:&=]Ao)1af0XrK(#[>+
#\L5%]A4=!LmH;?Vece24_ue6jDNX@?K3602cIQf*cW]AMe;Z)pH`Onspr]AuMf`O,GHrV.U/k+
J:?b+jRdS5KKZG4P*l&JoH3r\qKFP0&cZ8S`Kks3pC?)b9g6:V'94DW7(IX;,6*uU2gCKhUE
T'#99N]A9QPcN;[_+,Va%M,$inJ8SWDhH/O#\ESe6n!B(),'4KZmr*5ce&dumE[SCcePkWd'2
gXXB!\f^;AKWSKDNu!`qtr=r"U[3(&USeF%KqWjA&.*VgF2m!r-_lA(/LeK+=OF4@;>^.R8\
+aSO\jiiQQ@mOQ,Aq5?sqIiHncZU1tldVBGEY@G?-%*kF1Di.uQUAH9_e;0@1KbmOZOo<h]Ai
($>D"1#G:9']AO2D8&-2Wuj$d9L=eI'h38,rR?3^*[IguAW8LoE6$2*nBQ9)GpVVMS%lN?##h
bXfqZ.\fjS[(*%f(X4=SV7m+!?"5.ur3XfV>`d"aPP(IR)+_G^65U#HJN\7_1c8g5H`.q>ul
ZT\h+XC'B%`8I,L_T$T`VE,*a6oAfg`N^pf9#@ntGTL0ApE!]An/sQ?XDTUH;ShcgO96T">s)
+Nj[5ZFAb+t?bs2sG;ou<C_oR,j,M[P;C[[W58S(5$?'J!/VHq]As"\$DX-%fVGB^0aZ;g;;7
<O)74UJ,66i.F\ZM/<-$!8"DARE-ZVYUJYC>e[LE"-8O&Tn>EcFlVC[,\QY47;6o]A$jSh@(B
s<DNRu24\A.s<*YIeE.^T;?CL*-4UXcF1KU:dVL"4OWc)]A%[6P12.,'t6h.a8#CX]AKbH$0l'
pET@KH*;r.2FF.IqWo:.e9L,/gla;o87XH&5"q3Ju[]Af#,3RNj"lk;1RepN*Wib%[1(Q1)p:
"V-sdm7BkI:`hqX<iun"mk=51nD^o)@tsTg=mA#8Y14T'DBcgicQGCrBKh_H@&?7;+I?okGi
XTHX9PRC_JNZUY[=7$TX;H,'$Jbih'Ff-U%RI22g]AI#JXPOA`0Xo;F'1]AO,h!EIqhjWe8b98
OEtZ'f5!r5A='@hd%$k)>e3P\Z=(;UkC\iP[F1la<XYWPS2Kn[`D"e3("MJNf?kHlXSZsnHO
5MmOo!->X7S$B='B9,t]A/H::r4n^(6*bb$QMaQ)iR&A:ob>-GBeEU1ZnBdmBN50>r"`8i0jS
3$pUSl)f0oY=[r4f[nC[SOWc3rNQ4gEDh=:/M$<8?dfHS-7m%*$nOOIuSH;u1iSJg&=bt),8
2[d^5AV6heV>6=C'/c8E0uqkCUZTAD1(;k<jr[>1h*b'Y;`+rJ-&:BXQ>9B.7npIfY5+i#qD
*%H(=TeEY6T8u&!cI:;k#)@nOP[./RKSm3Z1ZnENhn?]A:*7r!'AbVc-?EGTG>)9<==3'f&t%
cO(e%p?XF$]Ar9=A3R]AWSVhX;%LF:*MIIuacLkT2g(MNqg?BOtXdPYIi@P*+;9D95O6S^>E!/
&0>)FI9daO1ApA)a1RJA"i]AqKAJQ.""UO]ABEqF#'V@182W/Z.&>K\X5n8&X*GZY8D3>1XRXn
-fWbF3ZHF7&,n-X?1U=oc9M1N]AAqCRG\p:"iV]A_OIWprVeZ6SYcbWid*p]A>n-?-lSH&Ns7po
bcVa'pN&LQVlF\8k&s]ATlH<7?=n2V80"jTO[VAq_`rsZL5<l\sV$e:W38UL9NC!9#B2+h[cf
;j/omqLG2hRmMr4/K&'&P$Uh!eGs,>Bk5@Z!*Q+l!DQB,O?Jm[WJOim\2F(_j`4Z7>L:iVJI
")Tkal'GE3i.ViL`aVpd1**HVV@[@5V!POJp)>^%gl"M=T&C6;+b.cH8XlVJ*3XbM/^]A)aJM
5cO\pM7o#jgQB[Cb'2i]AJGl?>Z7(5?-YX-V"T^3oOs2/;uuN.Rqu\ScA!CJm5)ROGG]AV(,N0
j!=G&n`;17?Vfk3mjjR*=qG%j,`Z<&9tcXkDP]AKcVmfd&(@%G"!f(?816N2gnP?G2$+%"WE>
K8@L!_=Wd81+2G>:I(!r6c!G:Y>9=R![1Um#5m[E@R^aiahTYd.OquR#R?0C7&qe7e/rP/^7
[_=l@/KPMd*S/=rRT>nT-([f+PkN(%XNsqrrYbOaZ25da;W?=URuIm+@?BS<c(J-B!`)RjUF
#]A9h>k.Ha=VY@8GA7&t,T,p8!tq<bFf,'t"f\X'uEnYZ2dOcE23_NgJY]AlN51?o;[qPZbU\#
!'Z+*[Je,_$Z"9g,9Njjgpmo0=knJ;j!3@j!NtE]A[SIf[=+Gp.rn[/_Wf`u;kZFK,o:]A+F6^
hmYllcrC=F:fAWX"Dj7?ZlfIa6@+bn'sYoH`L1h:k@&kNrW@A\7gBdu@fLpER/0r1^Z/r.^t
Bk]A%QRn#/?pm6?E2/$k<g7QV]A6*;"BX;"I5[/c@m/;p/22)JN`g2TaPi^PETEMg_/5)3X<1N
'?fG:E!K%d&\iYpJ.*,muMW>D]AgE"S-J&eXMiPi3.C\#E[18URB9MFEH-`0+^l17'b*CZm#?
[M%2iLZC;9%8aF15=.sEe0#C"1%:0KSYI91FGOO9Vf9b[_cZo3(8CqH2>WLM3TLE5^ngN.\=
uEe@@1Dj=7U,W"13QS!LW)"iTuOlJ!jCqOFQ-\9q<,eLp`;51;j$OIi5c)-UXJTY6273ghP_
1ZA,4Mspqd?TnJGDh*jr9"2lfg.]AnJO>j%[6]AohC<4&tC?"XL3tu+hU*3db*@H2oTq?E$6<#
TjgL7hf/Yc``*tp\7FVPA$:#tG0GPPO^3:dR@G!6Lu(8s%4'k$OYI(IMfX]AI3j_r7jS2sNET
sZ&O:+kF]AHISCEVn2@0NR!JW+'^28a^#N!1)2DbeZ2TbgkXTM,@!,?7hXoDqo%90lo0#4R:=
)h&o*F*%:p?2pn61YtU$\cL?9OPDkrDPjsK1K?qa]A@008+M`UH2HBE-C7@q3%><!S'_YY%cq
m8+XAg7[T8\LZ_$mTCE,Y'6:(]AX<id=)a?0m,o:n<tn]A/fQL=8DX5il,`Y=hTP_-8)3$eQQ'
pa%V#Kr[/mHl`HbDl(UK8=:cBQA&BIC]A;_/:klg%O7NfnF]AF6.72BpOjJQrZ')gV#sm?)VB'
m"(lGT@>W1o'D^+plCOn#$ocNTUJN,:WZ44CRKLVZ6h"9@o)t[]A8Fl^I[V);2qmQt:#&G$(j
(M&0H>mtlWHgu"2jh%pd_E'k(p]A/>]A]A2qFJV.0_aO3aCE*]AA2:$Mk<?an`]AI9WLnS%M)GAi`
,lD&m#n##WaBNiPWOXt7iej;ta(GVL^UVBSBJ5fWO:0UZM(Sr@T#8&lkP^sq^n+1U!0E7FlY
g`Sj,FKbr3e5D?F$>/jpB#CnRWHXiQCp$Oil_GqQ^c9A"?8K,d$LZ^i7lapklJ%1%c:@u&^T
b)(_/?:]A5:JuKC>a3OCnTUQfs$<NM^g#!-q>"ZrljO;.qa#-F%!W"id=#X:iSo8>eCT[J!VY
&s&_^mSNbM`#M?B/Q'2'g1h<jC/"3IoIi^DNq'!@EBML.MV+Zb\@@%C?f7HYe4-<N#3PO9Q&
@I#.Rm)4FV2L[KR=,E$8d,"r;%tWN<qPcY\fao_M-r%fH'+!eXEs$=6)1D=l??kC(Hu2[u.3
imfJ_]AD-nTo-Y6&#"p6SRTKQql(oI\koU175ZEC$dq)\a:GSGfq"535jOQHE>1?12.Z)<`JA
U^;<=$32ChD,^543]Ae8R[^T9OmFn'jrE*XV7XS^2'3c1j/#!c?&sp52s\,CpKN`W=bH<)rNu
LmT\]A,gUf_n!#0tHM>pq:'lR@QT`_pP@:Um\X/<1AJJF"Ok!e>Bp#P'M9M#[AiLlqBm2UEj9
=:O'FX7V$WE-aHY*^-p6s2q#bOL5C*5EecW"$(TMo:kM8Kq7Z:US5pt0Cmk?@<b)EdCj%(=m
%;0<_C?d@pn>&p3!W8)3HV@G(N>U5,St//hC_s5B>Y/<535OmPADH=Ot(eI]A@@o+<^HpiGU"
+VV5o,4G/Y_0gf.&VU/,[A(t/A56AAuB2<S;96[35`rnMY^"(dpRV(7H40cjarCL'GH6HJ?M
>R\3-[TANGbsp^7MX"NopsRfg<Ss!VS0D^\l-ngRBP:\*&HWbj0DRdTW?lHR?]AoDXn9(HEBq
D819Kmm[.F%?6O]A$:"d]AXtXokOtV#R^cnP><*LO`W&2KnDXbiZV8'T.aa;9k*Y1D>ffO!8>!
UpY9A<OP!oQb1O>%iGe8,')=Z:b8BEWfQj=4Kdl(jCY<U-BFY`=6[oV,nEsK4?Q+\G&RFU-J
(2<!0QYX&&JoW"A[DKk-V#ug$>ijI8^Tf/5:ceYSAaiWqU9I4'Fgk>$-Mf[O=XB8aZd+Y^.e
-047"A]A^/g2Eh]A!`FmZO/=(q&mCLU"l/hDq4mlHu1^D"mVGYcEU3g>:L!FV9-[0L4O"e]A"(f
`>uoO'rr=CSOLV[3gEBFU8$+i]AiW&kg_Lt2dT]A"[K]A]AUeqVkCX0bJMH`97-EXF<+_Fh-aABt
k8;l_\o4)!fp?^"T6<873Eg?0i`eQ6n7"%l2RfN,ff(E"-@df?QP=;X+55R"H8LLG;:gD&,.
'/a$4B0k`0r^q>L>Q9K3H_8IG,rA8PTNo'Q.[#IVkBXYg-S0.`*^XiW+c>)$As-"JddQl\!"
KqsC.4"lmHUDP$"[4)o*In]AT$+fWj1"Ued\FN?&fWX.`*:D`X4P*-MJ`!&P4.p#Q*1hlFh^A
HN(AC&:Pb4RB34Vaf0MNX^/?=^8@kK&<sM*Cn0]AC&Rq21^BI^3N`j/5Fh+I'^NtLbal=FX;j
pZ%7\Oec?ipBQ\@7RUJcbBJ\r@h+\et,UkC&_n35p=dqWD05MLd3#._dLG-omK`B[G8)0-.%
UJ2N65?8G&G:*EkO*ESSht5GG'"A^&P\0$u[7fq:S3'#N$-;YVg.;:j!/nXlPAL)*cTp6(<5
+\W5oejfg&[ba\/80!b:COT&U'#G#qHC.K%L^IJ^ZeVmsbV[ipeTNtd6nS:VV+4X67TJGNj+
NIP]A95r\g0+$%/:"Z4]A]A)?:^/85J"JJXZDm_V0;6^rapF8*V*MaGMnVu(IN=n<[qcct@UctD
8:]Ae8)ba2*\Wu3(,1;!c(pit)`#EjU?(?,#EM8k?s[COV59D+6)fsKES`\NWK+KIU`e8"NKF
JcnaN5(6+o+U.lf]A^;38iJo;MN!FXQXLiM8!(P(K?7L;oef3SH6PNEBD"E>P/^XI;1)/gnai
AG0=uHK00DpQe752;KF#;,Vf7kKU94^=;MWm6#JaqXehhtrbc"BVk"mqVPiE:,e`a%_Z)t`7
10\*sqFtL\/_6Oi+=,9,VXUbdF.YucDK8.GXNWg2[0f3C<T9\sZ"Lp%EC4pZ26MBI+XkCI!S
fmXQU]Ark=O43#V5GZV%cJ@@1ZNggdG_OR01KkQHJYG#HT]AJtmQXL$bOcGR<N`nbMlNg1WkiH
I#esR2e7Jo_IuHNG0Tu'K>);a=%/['L@eJ![lspu/R3eSTYrW0$Q;]AloQ'0"1IeLnlH"02.W
@kR@>;%!I@nj10iA_d"?10(b[[(ToKDG@Bcn-W"!,<Arh8"HfaF&l8%/ZU"iU?4/a,.SAfjJ
j2=_Li:]ArXrpHit'MC5E3gJQmK7.En("c.-IcM^28HXVDfVlm%S`b@LFpI<j3mK$6;V$:X\2
PgRUMN(0=iqp&"6P7e5u<_N*2>+hW\B=!b2[:.gSG@FF'flgD@!&1NF2dCO2^U5-bZFfXm"\
MTBgh(]A:6cAOI,/998V)`AFG`,e-Jgaj)<`SOJN9`4k#Ub+HG/h`4bbPnO[$2NtI/T.a`]A_g
IK']AC_dZa9<U,FZ;B=c[kO#AVO#2i(jAT[]A)gf@[8r;05G0h8:%dbL\rf"'W`>P[!B?f:5l:
"BNUN'UZX<BMX`=R<'?BO4)ICmLip!#^h0m4bn^9?C:PeCn0OO%VGepY;"JDZNuUC9$mKC6o
@r"ta]AuKJuW-NAKmg)5'OG.hej6Lfd-UVkjpeT[[u\^"p`NVhM:*U3%Z6[G.l<%MW-g*e;U9
;nN7T=$C6LQ$3%@^7[5Q<`a-gkKCX0BPP=Z!_-CkGZ62XSN@6-g=).')o3R`#=bVuD;nOUgd
+8NOf!qI]A7^ZiGg(J.\Qr8u(asOCY3"";Z)etK)E(""]AHf3A.+2WJ2'\,5eJm-EfsEIc:G<q
YkJ@ouINH6GN*LKe:sb.5$R=cj+<ucH2@qN:e<_ck=@79=Kb`ZL`;Q_5"UA+W]A#8P,$@k@d@
&@8#0\d1d?/AthZWCfg0"=C=Di8hYBXhEl8T:^7MM1r(ahBHh'(C&>UtiE&pmu<lU>9^h@Ll
cLdOn8Dqco&B;&H%W?>Z@"7Ha#:#%"^P:YPd<rVpoDZ@`46]ASGibEF@]A0Q-"\dD&\iGJL'oG
es)q&$qEQV>Du"<2H!XJ?N*/(XF.X#s/V0TaFhi`#u$DPL]A7H&m@6)FNjAZuie*RJs-$mj*u
7OXbJ+;h$(]Aj*h'[;>s0aBWF<7>T<M.HGo-Ti'a+UUrM#s+(>'<]A;i'QT2^1l1j64'Mgj)CE
q0p]A_`pm:<>^Rb*5gqo/]A/+lq)="XGm0Ct5G-UA8UkGC^):0:/.WH;Y57`K2`@#l?m/FhnK6
-o@Vm4f]AUh)V"enZQ66[#`bH&^9,60BQ$F%sdu!Jij_%]A50o,C)LRO=mo)Mf@S&;P,/K\1""
:@YHlHk)(0ar0o`B:WiT$Q1Cm<O5o6o[l)@%R:-I(Hp[f`TUo_jXHI<20e(QJM*ZoL*!f)4r
M]A>K[7.Ve7@<K:jm'Q8!iIfSWQ&Wjl_^Ed[p2g&.QKXS@k=cs?GKcIg>E)QM-1"ra($LmO0X
>NSL=d@B[jjVSQ[s!2G%G4F>-uG"I73$3]AVmeOmqrEjA#H>ZIp93_<6.Fa77UA)XE]AH0\ZOR
iid/]AX3NX<!WARWWpn>W2WE)'oN(Z70m$ttS_G)"*?dOY&l29rf4T'NT5p\0/@$H(Z,c*)Sn
Vk(G6[\\8/1B$Xf1_Zi]AlS1=b`m*645HV_E%C5T1M_*8$XL3tZs6s\>&oiu`4V5g-kB#g<"_
]AgfI,+5R'R"9K=$Od.2rKQZ0>t:@U1`F?)q8;QNVK_o.D7^ps=ls6fKR4p8?EK(=1s[>aep%
j"LQMmN^kkWhL<Q*a`>QoK[>g2QM%ihbjhP2:R8YhKG,8A$=rNr^[4P8q1I72>$6inE\k1fO
R\bU;;qE:=Vo<XKTp6B5g;(4?JI*2=Z6;<HY`E5j]A'7b9]AtQ3,tb'))4eUkd\^)R'AIf)%JC
ZN@1sO-7G376MD?F&^sY:<`kDWmeF]A9;G53X%Ge`u&Q!=F_g#2sa`UaMZ0i5_je/id+@;UYb
YBYmRF9b!m%OC1YY/*"?3JL]AOPO+MD`Un>2UTp/JoO"E8]A@i"G.+,#2f\SfG%3.V'G&7o@%0
bTL.R#Hc;^o.eTH\"8AHkoHS%Xql%GUE$!Ws.Z'AUa$m?!Xc1[SUlSDs\C:D9jXT3n(33g16
-3ls#P+jPmiqVG>qXeB.MtY^gd#Ml^H`2A6JF:Bi6P1.)Cgh&"%3sLGcF6.'*9`IBe1hYeia
ZD`Sl:rZ2"D@A!f#*Lmrm^."UuF->qLe@Y'>_>Vft`^1sGI1p!4&Yp`cp_gD+J<0qhL_^fms
S7;rH0J()4-geM@se'I$#J--q[$4*W:/Yq1fh!t'`K;5.capV9kYM/-TWJK"7m3E!4bM8KE=
'_Aoh9?qaGn_ePnln!G)>^X(hcHYB=8'mp.[&T,o2N(\/\GdV8L>*;FU@[HVP1`XdYsMHafq
+1:?pJ(K`BK"oZ&`7hq@qJ56KA/<X9bB%hm.b=;;\6F_BGPcCsh@PM?==aKcVp4VCb2;3[Q[
&LkL@1F8l>VH3U&e0161-@\-pCD%f'L:B:IV0(0<l.!gN3oG2[E3><0;$?iHR\sEJ0US;]AM%
PqUK--r#.l!rc%:itfR)oZ6AufEsJgRfQ[-P'L0k)1]AnWCN"jlH.k.$4&mg<E$UDHB7)a?&/
8/BEoT%>@NiCG_HdYmce#CJ2'&<79rPf;)HDT1ni)E]A_G(d;6F*L1Oh<]A\M9"?o9mHMN4;()
ET9K\-.>HK>VPi?1/R;oKFS!CHm-JWcmJMEnh&O(om,*c8AP":!6h&\if%(*>rS"omHS48me
m2T><cSFpT_!"j+iDA`[*XUcdG]A/H`ml`^mq@p.g$&P"\#-itlV]A-:N>Vb'P(i?Q5?0!d#\U
;I%'CVlc8GUF'?WcG*^h?_j\q/3Cc`Vl02W]A_s\cn^udMhNG,(_q(l6iejd@DW_S\Q^"<AAl
&!XH4l&GE>$<VO.X#gNm@cSV+]AoiRVu0N8BP]A@W^M5L!dQ$DoXDuU3j2\\Q.1,g>FWa2JogK
AmfnK[(C_NRK<9.bjkV,H##g-^hW3@`Pc6F1RE`URR7#`fk;OMFdm`IemNau0f3Y+(YU4s;Y
h9C&#JhVuD*#su\IJ`*#@`VC:#loLN?<`bZ#O;jpu9Fg7pK5+_d`'FE3&0uoZr/m!LERU#F2
A!o^=2Wh%ZM0S=^Y>790)(^%.GACZs>nfBNHnl7Y/$5;c)@gM$0h+.RWY66gCWq&Mg[j7YOH
3pado*9493<`RqA9>olMp;VSA)c)D$RSd;?Bm;3EQ/D?jW4Y[U$?N8SO2^RXs2Ik?`NO0%bj
GS7@j0SjcD0OU>`E)5@>Z0U&S<VbI%.J5!_'^<:UBWAEHHIW;4-TVF?NJ@_]A<7/&r3laCG%!
W,nfZcol&Q]Alr(G^05,145s<'<EbW'm;nnGpX8]A]A2aEViTip#D;>l=l_+MQn)4k"1EO/D@^q
p@p=1Dk)E`]Ae!R1s\V.=N?Q4RA\5G3Y?-XZE;b?(!t0kH>R$@]A"&WB1,@_K/SRS3[\^#81&0
RgNN"Y:$S>r"*s,gd,sfunDY0]A$$l4^an-ekUK#j"CEmApn>"u]AsWHe1SSE\(0W5i?9W`IFu
kU\,kosm1)+*U<eH&NNg@eNuGcrW)l]A@k)M2L8(r:.HA-?0TH!mLGqL_Z)"m=H#4Wo$IX#j,
<m#de(>1S3ZGp3;gGH&2p-.Z@L7UM6G[XeL5_BG?JT)JPuOsX.3d5"[,n*/'*)A>nH%J**Gg
\MRtb9fcc2N.'>*^oo[Pt"lIunZ=;<mKpk9(9L%:i)e_t3:jkhl[nL+5os]A<pUfJ3D;sADDG
R>AC;&pXhe4CVU"QNZgloZ3&jsD/2>K;nj&t6)%%Z*U]A`ku`J%]AbSt04ff@;a&^6B6sTf'Rt
AZIU_a0L`GrtX8WTc]ADRnRXLt8KOmSNR.dWH+`BU!7g3L8ACSo,9:HtUDR^XJ`E8>5&qsD;P
?1Kc"?n0fE?pg@6EhK>]AElK8nC9Y!TgWQ1.7o;LD6jg]AJUJW@4QbM16mls=U@1Sq!`kgl7kA
JrNAQY-AZ<%F=?[3]A.p,Z%/W_:#UplORXN=dYd!^`o`>t84k8ofVuUC/_69GJ>Q,SNOOEH&E
iYln_8NgC-Elh%&p?$oH$M)LhCi06,!jaYe@aA;%(Ou!l[+"[gDj#F[+!/qIs)<cGP$qoAME
'?079V5B,4(I'V0lo9?Duo9!OSpqO4m=]AeP'CD$Y)=_$QItQKH7o*lb,]Au6lT6`1MCK2ZkNs
4o0'c'(eK3h:_PZqCZ`IE)d^#=QVPWA,^ASQ5KKKMad-sDBL<"#+gttOp=VE8dMI$HHNJD.l
)"h0E*`+u)Hjk/3b8d"i<)>ngB$ss<D]A%81!mP\=c<A?70%WUr2rjo:_&aE(Ou"+YR(2/&b`
`g]Ac*XKWiHD*\,r*#=YsX7s'N>'%L,SXh!715&bVaVH;_G\ebqZ]AT\7o=_K8HcTpCB&!>YDH
u^1Q9QZBi)Nos4!"%e0u:TU&2bcd=3[clc#<f3RUqjV%Q\Bic4Np2+MOp)Im',k6qn`#t/QX
sFX+!ohWH,0._FX1]A33EFemk7AF/(QB1[H@V0;)/t\Y/P9\fB;Zkk42H/+E4B[i$rQguie2,
]AcYYGP(1a)]Au,>nXJ</<cf2(n7("16M'@ArL'Bd]ASt_(g7J/!7;dnCSoeDX.p"\s36]A;Sc@Y
``2NhO0s-cQ/r!$i+d19k3EJ[RV_IB2VUJFT]ATXTa&*).qdW4g:?Bhj28R0R(?XG[=*X%.ju
#`Fq@Sc1m2uWY$R=]AR1\nL4`MZE?qrtMf$rSgsFH.E&dIFLGW`B-gA#$`sk[F"FU8/CgOJ%j
SGsj#d:cn9XdBA3@;a!7KX.b%&(Z4@)h-<M5=EJl`HH'qeNpUY!ohE\q;`^<Rm\aS\Bi\;qE
BqNZ7Z5=;6bCE!goW,SZBr_`,?qs"Et^u#_^/XN-CQA/V"#6kqM7gHo9VA;aB+luqlE%*Jn1
j0i_cTGX=@[l:1!urF*[>bgip8`,p"7bG>>HSPE(ZVb(+E9ZpF]A@Db:7qO/.I,maO=qql)O^
LL@>pn).QeHnFSDDeN%;QJD<@iF(kbP$$_A6(hPJW(5KGneeqrT%hiKX+A;93/?d(OTf8KIn
rkm&`u6QplG-mbPin<Tr5[jN^3X!R7hOnY3gR!F`;^+=MaHt44\i23b2@Q4nS4Pk>:JhII7H
QjnC(rOLB3u9;3HVYaITl;e2["+O%/cP#F4afZmNd-Y6W^X.@#IN;S9sTn=A9-@pkEPpJfM&
_n2Wl%V<okOQ,Wq/Zj"BHtVV=9OjkD;I?D>#H=,K_'biWFa=[L:]AuL<2iW]A_E@(G]AkEsD;Ka
huh.fpj5hTt9ILi=6pCqM2+i8W+8;1d1l\NohPX<)27_)Ruese(Qi`a*M?+K]A_kgh;FEfW`n
jR^`oMSBF7`BE"IMBdKm(XCA"U5qMo[5sR3EcW?t?8UW8<dg75lG_*WSn%VVfD(pX5D;[)K/
<+`nM<%NS$[@=JM@&_D4"hsYkh20);ptUliXgr.FU^!\t'iX=GBQ&..f`OdsJqN/i8OaZ#(i
Ll#or&Kec17f$:fokg/uY5.lb+ZRO,j<Vj\p87La5;_kh]AOXAk@>KG9BQXU^j_`'TWi>+YH;
0_M0X!u*1RYfB^mI9SZoY\_GRbH*Z36fAq;nk^\KhT8:nWM'O4:XP)Ec%&(A>OhR;$Ie?(HC
?R,=+,>L%%$ddS5aD*l-%`EELh[X#[lVqo`2Nir$u!$.5eh'?)&YfD7pQ<ZTLi-<G+r;-6WO
L&Y`"DYTf;FX^QtN1=P.Sb+cN4miqE!o7>sp]A@>j^>WddADq;d%E<g8EAclT5]Ak8F%q*9L\<
/%ck/4\D7Iog<&o*o\M:ct5VX.'H3?Zq9Pphr@X5J$RKC24'D:FV!\OsmL#;;*aY.QI1mo<4
J)gHRiB_LX(9Vl/9Gm/CBY;=A.CTM%[`RS:BI5Yos!TWj%5f:VnbKS"^5`>V17c=V&e>1g@j
c]Ag8da'.>I'7[1G6^>DJEX+Biq>tI2Fi`j8(H<n@fY\Wl4J=U2UFkJlAI,*/[h^GL99u-iQr
3YX&GbVQKR_L#bT'!<esbA;E^F6AgU%FX4#=9'X+V#:Vup]A/-KX@G+>Gblo?h(d`'l4iCb+I
eB\R`^C4B;UhY)OTYrDF=^4Wmr2qh.@5A'A=cqnN%paXfP)I0D\+V<8=2E@c'/@?,/*Oh=V+
nb9Fj3OtrhF.gD[(P(:&ZTXR&:dJ<&mN<msI5@@_*ls.K.NdfOX["fkSiiFsUgQkL$?74-PS
ZI1o%W:3h4WcXiK(,Je[9T2Q+a:MNeZ<lSkt.N;+e?'1XtE@^A+q2#`Ia(aR\R`WVKO"uFf1
i=hu`?.rb=<5*RqNHq5gn71$D6Bq;l*.g*N,NT0dr6s@i4WVuU6S$9ci.TOqDIef]A[`:t;&/
+Gc1liKmTJAiQP8sOq"_pPcHtQ@r_F5KLg>&*ob)[W,3^,),aJ_\KXe]ACVVNUkGuotO%eG<a
R)96*@L5%HOEtp\f6WCi:_l/VBj0nE.kKdLooU`O_Y"FTUkn(Ys.b(!*Vp>m]ANK\5Ct-;H)o
)WY@P9Nm8k;=Aj;O`DaVFUX$sRg9H=g1"$JE6-Imf5Hr)qJ2-oFtAW:MDbWo@M0\O4u!]AC<p
lFGWB"06^>:3S+Wt*4AVWX;V`:[3jnn@u!N)a2cgn-O-2>;17t=mcVlW%eDWZ(G"StV"N:IS
g)*4"g66(Yk]A8;SgkpM_"gs@:32F.3kQn5$?Cp]A&PcOe-s$W^=1N%4ir&DZ\d1qI[ntKiWcu
o:-/`Ks%[[oc3c!quahsl5/k.fd*jhHk/FWtG;-bs3)/"8!nbUtqkSc<Kda]A]AN]A-mSKDH<fc
dqlQNLpD_^n16Qe1p<fphY?`><"^$\E,oOjs!uK_m@jSa:=J#:l)uS!`4M%`$?<@".M#'c!o
;;F#Nm,r$*2dE)3ouqS*>1S>SHQLBlh-("/>ria^")iC3(2Gp-R=FZm*&Zqi:2A*R3;28=`[
4=q!=i0.q'p[qD#[Sf6/l<0]A^c\%(Rk4?6![PG\tU11\Xf7]A@R&@U]APWUV6WE[c=(TWSPl>9
3oSPW`4j(F2UklpIT8Zb3==Ce^V8>_e8%fi^PG2O?mIg'gr^)jldY;2QcdTOl=$Z**o&QOCC
J2rdQMmV5Kl!Qe3b0RN=Y_qEr[qV_SlI%p/W1q!NZ.qTaGXmBR_g2"bXY^tFSNSu6ei2:bW$
bPdLfB\P2iG9?j_lof8]Aoc"("^O:h9IN@_]A:K!u:bA3"8A1b(:M4._Kp1I.g*`]A;a?"1*8'*
V1+J`,fFHk;d:V]A2MFZ#gj2Oj;In#(?H6@++9X:;f=5XVZoJ#X_:prEs>qH!OEmdRJ.!7]AnR
/)9nnh'0LcqKWLV<\c&D#@&"CT2RKR?6$R/*g!ps:/YTIA=\t^]A<hXtCQWR!Ih838;hOLOlk
MU@FCB6Z1A%_0G8X,#FcKG-P`'2Ba9\UVh'@1R.DpYsc\lgFjccBjAL1I1iFkFODY;3jaRks
24>PjlQ:Eq\hBh)oV^VZ$%@5t&A;i^44ZdVh?a*AH2/Zu/?r7qt+Y#_D7K6j3$0uZFY063m=
%ue=W<)(WX"k(\9o<3&l1Bip*csWQYL#b9tDl>1[8),q1/*k$/LRq,ed\iB-MZ$b\%h`ZKLm
Z`4H+2,"Hi<S#RoWTZ,@YN&3e,ku[a@EgdFI=s5'?<Q_:os%GulkG_0o]A\AX=P-nhu6II(Z$
9Z017a<-OUVmXd6+.RFS>,FVct:pSKj,O"s!*;%\MZ97?-ie)3L5F7d8"XLb`D4hmdAVSka"
LNAs"P3fB1Bl89r7sLFOF_T6#dCj)qdhEgE^9O#+^di+]A6k2E&"oI/+;^\6]ADA)s48X6qn*J
rb^U&E&s3CV7rB'n#pA`=gq</K<gMB*XoBXnkIY8%7.EuaS]A_Qu&pidNuf_4cmp7:uLpmq2N
pmq2Npmq2Npmq2Npmq2Npmq2Npmq2Npmq2Npmq2Npmq3[)0%)#`-,;Zmqhuj`3ESjTkiK=A`
rA,GKS$gG,id,G)FWG%H<G'i7_4]A2%XT9=FCKj<-e$1rVll~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="D2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('D22').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="D22"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="D22"/>
<WidgetID widgetID="8126ff04-6ba2-4bfe-ab28-2d09f47083cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="D2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1238250,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[14084489,436728,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[*注释：点击指标卡可跳转相关指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFHV\;cak;n5':&&]AM.H#7AkPXBJ%(&[alc.T#2+'Ld9m<&UH^!fFX["N"3(B<<TQN.hTh!<
T(X7U@f1RNcLBQim[A+HHdR7#ac/6BVMG45H<ql#^P;lC9r"R<.f0kP*g0c2#*/b?kL!Rsno
<&I\k"p%dn"&K&`*Gk#E_ZuG/SGDA0qbZ!G@a<N[P6N"]Ar3A/F+ou<lq7T!4Uk?hBfm$8*h6
'T]Ai%S8c_YXY7pLY`2(]A79T1Hd9M%1UtJ_1'bQmNWoHop+k%uauOR_r:69%94+8>4-fibd(j
Fr5$G]AE\e:o/-nAh<Jr*(sm,Mb3P1%;W<BbQ8T;'fDd:foIa\m]AS?%ZnJ*E1VM!EHLp&(btD
_idf>)#,J[=?K*Gp^dhfm^G$`;)Mh4HuJ-'`Gt%iT>/aO,#M`Yh`kH=0PcikU"Bh4Q.$_H&W
^"57/P3=ODl8XkU#n$[4$XePGjUuCGAC,I5E>o6/o1AOBB'"o/dSc=1tNK\c(K5ng['tg1t.
AT$a\\VBm%`6f2()M@f3/*\C^pl@pXqNf@OoE7-^<0jA0a2cpLU(Z21GFqh>^ljHl0kuBa(G
o$-gk,s;D*,E&Q>*!f]A,XED*+p3%e@4*PSXY'LUr1`-q,JGce+gu=#@R"YKIXF8a0nO&(O=\
`)5FJ*fO[j#;'Q'q9?2*fJ5>'?G^2nkY`qW\/33Jlj9LZON._7@0P0Fsng?iP%hMu)+`rkY"
d`:C;FK&]AjCL_[#h3CbQG1:,:N,VSim(%e081(Jp>`jN"h%[)uOmuA]A>)1]AA/JfN.m^na2Z#
*5#\Ir2kc9OI+k4r^$%nV"b7c.S!.LmAb@\Fcr\6o):-9_*L@+m!nd'S+*F]A#C'%e[_H&:**
Y3ij@-d[`SFLnC;"h:_SuA[%)%"*s@:XtC8'EB<<+>s5U)Whp[6F*`pQ-:-@$n$3KldPMaZM
:%PBUlr!MNScnOE8<*-9VZ=b]ArE:(7P]AYQO^T7sW`i%'MIKl[=p53sT@nV;\4.0PiQWa_!nu
AQ=HYasq0iE8VUlTEF2bPre`ofg.!<+XQ)nT5..>>c>VF+]A806P=pJW4<9s#1c^S!cnmN#jc
$#9$H58&J;Rb2p]AI2=LUT9nUd\;^.h\!B.KJ/?D4UdZfnK^PJT^8"qGXIbE3B1c9eWbb-3,E
d1r8F<0jgKc*lGF=sEAZGmu6]A0tMaeo@;q+$]Ak>Q"/nO3[5XI!,5TCjZ+:!ch(hnAHR"[6&e
^>,^K_AZQqQ2Q\aN^+Z#Uou$F1%0'3m)Xj5%JY\[*.RYV>X#K_'Wa-m\j<dTX@n$cK[p#K(N
-CQDoBlMlZ>q/5noU,cc3LVArmb#>4Z_cjZ[E]A8,Q<JNYW)C/gPWF;;tB:t&Ce$VA$*WqCPg
rSDs>^so<_Z3:<QOI=Q$#Ug;,X&@jBd/)2VOob3G9LEMO!2TX]ARIj7A)c-H3i3Vu+/@E=?BS
Q5PV-ZdH99H6u:)H@(nA$6.U0;[W1OZ9=6i?;i^PlMm62>,o=tk=tSi2_uYTC)YS>N/R!g.$
dnjc\)mEOrWp.H\rqF)_TIp,X0(-kF?VX]AI6nh\I#PUW4g0/Qq=5E^+Yc%c1(HIXGq1Yb1[u
!ikI"G$]A-7sk`8LK,Zr_CXAK>;Heg5VT1n<Qd101cl]AFQaFr>C(-#9,c3gsFYXfV._MXZ9AX
<^tpM0gm8K+V.5na2CO9qm"t`U:9@-cUJbqW+H=#Uf%1Bb*TSSJYYs@U89fl<m>*&[FE7YW+
^@/00QRH545hqSLWTl$Gc1.9>Ea<-"YVIQ.Q/X7r@jDn=N'eA$*4"1ottV&J3Ocsr9DqEjA@
M/Tbp,0K+s2b(n(\8[9:-HGI^gmSKmZYr3h^Gp^CP89lG=LDY#d6GM@U0AZ()CrSQ=ngRLV/
:l9\BG]AC:/U_]ANNL)b.cCtF2<Igf7;cJ%CR:,jBreo8EoY5F3BHH&'jpT;hJXrUp?4j?*FRp
.dg(_m4CVEIP^`pTc5=,h]A]AFu<pbXsg^WZ)s^4mhUB\P/LCFEmK[c>b&AP#1YPH4I$>l4(1g
742bei@+p,f^VKWfiPN@X5ReP0AE$C>V\K18HCg3dK<^[l-]A$C5Sek0pcq@mD@F`j)Q6OjQi
69VV*U]AUWr*Wb19+;)A>m>,[S4qNQ^]AV8UB9-1!tc'']AjN2>sG<lfWq8CM#S/@d;i6pZc1T?
f97;3L-!<?>%hW%CAnkcd\I]A8/L?2(0?@2LSaJu-@[LAWjkD[0eH]AotJf@%Y.Ut4iiEHtfqj
O+37kJE%oGnDFfn6&'-.6p,Z(85%gpIF4Cim:(?>f^!f=T\"fndPs9Hq58Sb6m&k4oO-57)g
-4.Eqkd`t3+"U9fE*Fpm0-l04-jU.lL[+<`7+ThE84lHNdNJ:0cd#+\);lOQQ>Up\s+):-lQ
TW.npA9$R0/PCODJVEgnX#F,irTp=`A<j7(0QrIED5baP;5!opC>e*(f`\8h*o@Qb9qG4Af[
1P.Q,\2jV+bN8l!k![@0'HA^Z"e#3T2(Q[NMEmb.0)l@O"uJ"PGJ0jenVVmHgXJ]AKhY;:7]Ac
j0@MK[n/4?9=(]AOhEft^r2V/Icel%TTEJiffc^K9F?tTH[cL9]AR=uojf;fdU%seW\]AFNAeD#
(8\C_T"bL>hmoVfp6M]Aq0)/(q6Mq,U5-A2T9u<BRKi+_/4e,^p+m,K#;(j]A/jE1$hmkd-=9;
)I?G.)Zl/i)X%p#r6^b9SLof8mf53IM$Qt1H8=`u(0@74Y#[9aOk!!o1PW<8139JI%Ab0j:)
lO*JG89QY'dDur7_)jV!f"8k:FP4`>O^`kF",H8Ws1@Eq>9`tC1+o9'Ar`H"n"K7,a2a8kLN
=j*9Ecrk2$(':20)R>D%>1ITj8Sqi?]A?o![<HYbgB)l%ma8aC&^(_dFQ;))0^cV2l6+bh.0>
^Ve:>Q8/p(Zt[X:*B:9&['7bJ!!*?j!!3^^!!FDE!=1pj!YBk^"<[^Cje5@r1jdK&mh%-;@_
=kiPJseZnM7WET?ABYhcgS*1qL=\q_ui23PsTHb:t6dQCe44J,p'd!!=(R!!Y--!=W?9!uSf
R"u(Q.b[6UV*2"AN?ub>&8%ID:*,,\A"rP,U"u(Q.$t'&;(r-+U0Rs04?iU9G^]A<MXR%"HQp
PZ?[ch+lek.dH@!(m(f1M2uW>d_5o;U/O;q03)`CAK]AKphp~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="40"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="571" width="375" height="40"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="372ed700-67b8-40a0-bb95-19ce2b0ef700"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[524786,723900,723900,302149,492980,270344,952500,206733,952500,206733,952500,492980,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[seq()%3]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="11" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="7" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=FORMAT($$$,"#,##0") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="较同期增长"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$)='true','',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="11" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="12" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="13" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="7620000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="3048000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WtUP?HM@1Fe>-?lMp.cr#W,JqusZP&V%1d9reA#meBR8.8KjOqC/\6)#%W!A#ISZp5Hh,Q
_E\/LjX"KHMQ`.6ruJOEM:6_0OY6ql]AU?J)-#u\K:>>c?%,lHiJ7LkO'4lQ/_'\X-YDBVKh+
qd<!KsXTZ`Eru`\;K5H)3B%aIg.o.-JEo@4W8OX!HKA[E,=Q,[UnX>kiDLql#S(7O$Hf0S?(
@'`ID8p[`n=`9Y?`&k@:Oa_YBo9=J4h'TM*NOs!jejTV2JR#]AkVQ-c\Q:k7[(G]AYjCb]AD1Di
hP'A'9gH/WuV"%+-7[a@@"%*l`\V\jBcE%9(G`5u0Y0:n+::DFFB9=WkPB=6".f`C,+J,-H[
L[]A6Rk[OYF\G@?eSVG,HG2MA1l6OPbrYp&E_"rU8k*52*6GD/Gl'p^i5_mFOK-&nTS@D-M.r
,cB(VT.E/m)mRqSLHCP<T:7^7RH8G>T#*Bp4qYMjiuPfL]AF&rUGU^=M2B1pPb,pD;\(gAXVn
MbCIX5=/<WBAa,8J.pe@hha)`ST_YFf[=G\9k-B.PrJt>%5s[o%pR3M:ZV642%,K"o51K=5U
XXqg7jbj.h31<73VAs8rM[uB$D9/<rFfaeqfqZh^r.tq_#Y(MOWS+dND/dAgI?HfG<mn:li[
Oua2rW7m.8CS:);MhTt'oVKm_bLCh.;2D`4*,mJ3=ARRODNr^l]A;SII2`VdKT?IpD*A'f#<2
HuLjh3jd3h4i\FUM.*F1%=c%>YF["u4&*\g^jZA9';qn2c0Zud@Xo0Q3I*a.`tM%Em&p)WmY
T"tVU4BmTA%ZsL@R^#9?<KQ=5Olu`GV+abfR%*CNC.O[k(P11li2UN!%9_PRtBqb7tnIn'Ba
EZ3hd,>HS"jJo%0Fs6,Y["DC.-\VU]A_Ge%(kQ;a7sBVq'2#/_HKo3Jt?V\uj01NJJfVXR_Sp
#BErZPL<Y!]ANr9iNI/@6O93,Cd`.rji<ZZaqmq??+Pf7-rXO?LkKPuNBSMMF1ZicmJYffWJ@
W^6F7SQp<oC=?C2B\n_/0^q.rT50Pd^nW+ClN<*;t&olSX]A%kiTI2)2+WihQ2c.`/7G96)<u
-]A3sX7Gu4N!qX*pE7]AD.e5Gb1K><7t+._"[Csm69cMt;U"ee"2Eobu[A9=@QpO5Kq"(*Z^:+
pZh?*Mko@68-s;rS,b9$N/4:;P>A7%?R2k5DG9V[QH!#8A5)@:6;'.IP$(EmAd9_%n*R51!C
RjN_JT2]Aos$7.Y!%rb"tfd.gC4RbJ'N89fh+$>rn8PX^C!6p(NA7hW,bX!P_]A4!ni@=!.'Wl
t/bQI?t"PlnplE!etKn?YC$#NOiUnIo8`LH4p6FA7X/gB#q+=-T)@FX:oWR<M<@M@27<q[,5
p$Z9/<*hq\8D1RaL:1flC3L;;$kE*%&VBR.j5Bs3""24fisTMCa#>>e+:;=P?U,_6btTFBjC
\D!5ThkcO5@MWEOUsN<E0f(\S&Ps,a>KUbU51RkD:H>((<l6JIOehH+GQ<$IBhr<nH'N\A;I
)onW7U%n"E3S>Qs4E12A1KYie:8Thlc9/pA!hCOsV/#ep"Vd1=67hh2Y0&4(=-[c9Q<Q']AbM
.B2Xeac>&iY+##,Uf)f0FFi1[$28Yc1n1Qk&fE9$6b57#KlX(@]AhdYg:aMJaUblsP_9j3%u&
_o;X3*@q-Z95edK*S+tMqP57lX\T%C:sup\#/TuIlJ%)"SK&N`9Tt&^=GQ<p_t4f3K9hg\8X
6$$?@ZD*!KACh?M5,+GF8U,ITP)B*UOu7,Efj%h'cTDn0Xh.7J)2iDMSdQqmLrVpLO=cMPgI
q&?\9h&-$+\^Cn#LkI+b:ge2r2:Z;>)MX8\e,^*?ApG7a3`j1ZW-;HUQq[1`]Aeb?_34hEj6-
)/GY",/g/]A5lpjrE[8EPVPKDH+cXD+loB0Q0Cr*3Id2c0md'@aN$\8jKB$IbRVM@eiBrB]Anu
P;UUH$G5NDMIsut<V!SRdg*S/XKO$1Qo64["]A`6G,_inolWW)c\IfP[T5Ec+d,qRd[[oZ[W!
*IKoRGNhCXb;-YC;2gf;=Sr.MM":"n#Oi6=#$@"8ik_CE=-\HHpnR1&MDK0R/D\Vj.ig\cC\
5e%>NWCHV+`[!75NGJ6#CY7bnuSa@$0h7KA]Air^[7S*'K%l.d8\FQn\*Q^R#g%?HqL7/"OYL
8t7gV+"uj]A[`>&!E77mW'&\8SF2[jZ8LIC!R[:0AlOiTY$2'pg7>9^sDeg^mV8^+U8AEQ?Yg
60o\>OC4plNFhdu6ME2MrQIT!shjlR@qu2<*>=OM/5OD`#iuLJqti@\]A%<fc,n&s+9hM-:0P
F![4XhU%3!Ef(;GI1]A$jr+=[g9Xm?l5kA;;?*=E:(F$s=KDVqH^[n/UT1"EBgU]AgMmYHpC"*
)8^+J@SuZ(_VM>Rk(^/A0<,+#lan*XC2ac/;i?]A,_5dC8lN[NF"H_5fHG6O:=9CG`LP%c]AK)
mHEij+V7L7Xe7uN@6IFHh40`/^AN0B4kg0&+YiK'.+_qI)4:IhR?!u3h8(P(..UG:Ocl:KmT
PVF+9(u&GkWae7ZMNBM$>(Q)Be)m2r!A,M):iAJoeY>:j2*c4&a:PT/C[8`MbMb#Qc#@9'Lg
?u]A.u&Q/*a]AL2W]A'UQ=UFECW++.QU#MZ`3,F87^pEVi3HU.MVpMf=cY\b99ru@lr+L@HaINK
oER$XLJ:C:^*$c`9Fbk'gXUu4(T[cbL.H80''/li*F>qQO1<047;kn"F=Q?*CXnYB8'YMk.D
:Tl7Y"R<h#.MtUMBIYG6BbuWaeCou^F"LMFajqsE(a1^b&o,LD^lnhn0"QY+5[r^A\bjL;lP
OSg2;N,(3=e.Bj[6u^"A<uB203knD?%>:L>9r#?R-^gJOQS)=O"+[HH1N)gUH]A88:,J]Ak0P8
F3lVr6Uj+7bhiT-,"M@A'<WMGR"A80a'+so7@Z"",(7]Aj#H%rU"3Asn.<?!2M=QPc9D$'qCe
cSSBD/+cgL1eg-E*`bea<4D(-&dgQe'd0pc'7nE;+.`d10;@db+TFf1_8HA?U[RlZ`l7N%_t
2erq(%Z^fu$d(+:Le=XOE9ljkCm\6ME4fA76.N^U]AUWg77mcF,;f*h<@Z4WaHJgc6(Mf,u8^
/e]Ab@^3Ti2#(:2I!(Ar@u[S]AbK`D;>(VsN7k#uY[Pu>@kAfL,Y3O2tl!;tdU%<7=Un(&f?%H
I\FQ]A'Bl&=^$7f"cl1ah?:E.UY[(\YJODPgE@?/6N2g:k23ks5#I2r')fO7K#Id2(D'GM@!<
fW9pV!@eAMH`6;T#rY"cGk*b4YK<cp%$1UqeTZ*GRF3&d1f/Q)4fn[+c2':,Zcf0tq[2:nSM
Ea'4P-b!5MH?0g%eNb_q7$MT<B?mca+l63s`9#Mac6Z:068jV3c@cN4ndb#AA.M\#6q6cusG
4nk^V8W6@BgDZd5$fl?ONp7ZeqBA_*,,M+sm[2n!(erVpm3\T*]A3p^;^@7t[o+(cN?1k'4M=
/!6Lh/1-$.VU`'/)>V07;0^"TJ>8#UTe?;R9OH;Q.#.&P/_b69&<!9BTlr`\]Aolc(J<6Ub+=
8Z-sde:jl0E48Dct52E&&0K-Ks>O6%Ba#fLrj'2XhM2)FbG?9>pgnqb@ko3&t`m2!P7fDAo5
9%ej>E4UAl95Nn"&HG,[@@bR>KBulAnl.1u5%IA,+&)2R6@C]AC5:7[qp22S@/Ep"Qf"Q*rcH
TJ3EBMO2%gC^j!gt_s(=\/ume_T_kZu7sZ6"4%jllCqk:uFu.C2hI(#+(AO^<g^U+8SS0\k$
&1dCV=WXu@)/tV8hT)L0Vi$*i2^uYIpWbg7,*-Y3L%>5:O.CVN/#OeA@U'A;VXRuQ8hQ@0eb
Md)>I4+Oe*bshPZ@W$8+*[p0._))+k/1]AJU8J1\Deph4Xl!YpL'92O&rW&EKQth/P^#.7+23
iC6gG;M1+]A#^ha:1kk5+>fH.uJ!,kX?:jIF(i`>OZj]ABK,"`\b@@:eBlLrq5[-E85LW-0_HL
Pi`V90BRS@jXUtX,r8jWR]A@$AUA<R3RDM5>ELV+F4-G!(7MKAk[cLC,\rcXWl:]A572D?,9/F
%L@Dk'_kq"ZF/=CZGjrl[R5_".FhSXH5OY(%;Wf\k,?VM'AL;-C\s5PlP\S+;(Ao<em'A'3"
Y:Z$$r?ning]A!noU<Y[*S,XPQiD32>bU'!HT(M3h`O2d":Ah2=8L=gpCo3m;m`ML2L[5NtFU
fOe$9K!,?W;s._lN'-a(oD8dZR>pkNXYt%&7fZQB95V;dZ*Fm?$IU%m+h?\CB-H3-.+sbT.c
+m*o)4%%770XetKYOgUFsp^PR=7*(tr_QMVin#PpS?CHFKmJY/.RPMKW5bnuEiiu(_oXQprn
MT11eaCcZOh*]A@Qql9F';S%%.B8qtfr/Z\]A;U@Ti9r)`k"s!O+)*X]ArT4kgD6n`G`HXT>^S_
!pPcra2$?+s85@3'&M+pbuON`SrC,>#K[,D+Z*:H($+9RmZ`UW]Ak/BG,5g>8["h^edhV$-MV
J+19AD'C'Pc#^(Y6ZZ7$]A^,>6mXbiE"O?.h?.k8tTUcE]AMGn\ckX4Su]A(lT\N*M@*"Z:6FXo
_BhaNB)IoX7F,$bJ_GnK"Ic1dP48I3oh+-nD5o;9auaeQIa?A^Ug,OBEkRUSO]A#$^;!@.C-b
ak)9,Pl"K`rH"h`^YjVp.hV1)k9(dL\h10W5mHHg;=ktW<h%FZ3,GZBD-cC60)Tl!4Nl?+uj
_lSo)WLslT6V&b.SNi[MG=dI&515YMOQm3!G4P,h^W]A$s!C7d+!+UAh-RZsX/oIur?(PNicb
Th>I+(o,h9id"MT8nWdN(n-p?CpjaC7DC35U9M]AXbNaRN>'s-EM)'N;B]AuMi7Z0!5;<l>$u$
AEsR0.Ng1D*&VAq2;9iTtQ!q2%.9X*o:%9"c!&-s031EKm;qA(+)7b&7JoC;Oc#0bKdd7c2&
p9`q[/SJM1"B@!_m7cKA1mPteP'Ok=KLg(c[bqc#!BckT[P[gAA):Ioi!SLp#A9<lUY$]AL:S
DRp":?#J`<;_j,H9?"S@Lr$&Y?"+%mHKZ2`9;m.Hjr_iN)YXY@s>?TC@fL0Lno3UJ&_BI'AV
1-:E"mXB0(Q8%*^B6S0_+]AA&^*b>opE:)#(H^6E*2Fmf3fqXLB?Znb!Be2r9X2`Lj0S7%akg
DSd6e@mu6@<l`\P:L;I:pjjoZ-.K?PjOT;(Ht&e.C>i<&p`"_UB2mVCsOLK82/\Tn\>cA2?a
_F)_.0P$./7i$ToO#?sbLqNfC[m9hrUF-1*.UG!q:m1)2T9<I.j0Q_=f3\_,;7qh"Y>.DuuL
IX`6@K*pogVI^YkCoD%)2Ffr%M,S(Y%2iYn9sr%(HGbX+a&2=`;?lAE.BTp>$&4"\#_elO]A#
kh$3*t.a1Z_dn02mWfN]A-GdoYI.Qe)^;")Pna-.)XX2[QO2U>P^*jOFZ@.%>VtaD9Sp8*W("
0V>n0*t?MW`NVABI[A"H!gGO4odPIC7'=aA2T]Ak>Q[FYWc=59jS+`-@DG>r0(,Xl64*WU^ip
(>p>4f6W8ks@aLg#W-)*@e,7NT=_f0nR.51dH<8<1@*'&c=VI'D/\8hT#b>5-pO"IbffS05'
cJraFb`?Q@IVcQZ"Znb@N"4Ea=r%l>on$BAbXFqljQ'Zr(c#;_K\"-eI183V:$k,`!FP6r%@
NQXD0Zb)@]A+p.69]A_M'<%da3"5d93)tGe]AI._gngu2np-_>p:h1C&r;0;R:Y#(Q41Z/#VoSM
j3JM$:A3T]A:9frG"O,iU;Tk&3MBb@aV8JF0#Dg@5)bXZg*^#'nXS5>:J5)V]AX0rl;-(d3H?d
aR6gH6:a4OFlA1?oX(aBAld>[G1m*2*`DJb;=]Ald:YOiiD-9Ei*l9mC@%Ok=_Q2l7\T`TGs2
QgP%3)'X\$s85hdZ87m+290.\;4p4c&m,/2)=6#Os2/b/bemSe6$iO9k-9;h`?Q*l(1l#MBe
!>P2IO#;R7Hj&gF)*IYbT-0#/O8clclg-ZrqC5h]A.d7J8Z@WUk1h"8tSI/ks5#4!U]AJ4D/S)
Yo_iB.KW'a*#$UR+3dRWbpUh+Km2;eP=B3Yj&ENVlO1;LA>?6^a:UJMR_$(BV(Z4f/Qun\?C
JVUpX.2^O<RR?(%D?hCC5SVH<"WI-NAS?/BZ.D/jn35Es]A);3^)Rm2gJ:hR-SpfFNcS%[b+#
>9eJ?:W%L)b6'SF=%F.^<(cei)f@6!gulb,lUuS>)8ded6n<,FKt%F-n.CQ`d(L*3ja*<Xk,
pON@n,-Z>e%O*Vr?7ReTuPGhXmsGJp9>aQE*/1Oh')jlVIfqEMFhqaXN<j%$d2MQA+-D1kOs
u&A?9aGVqY'LHYWrg@#:Dop/<Q-VG+:[ubSJ'hBLJ[^BsA[;9i6a6l3\EbMNP1)p?.#4HZUG
31<oH@_nY..,%4IEe9*e\[AJ0[@?dG:-Oua"F0#FL-Or0?Gl[s/*Hr.h./+^lO!eUT_>dg=S
,K]A5U@aXJC1q>U%[p@eYU3@=tcBH;'Memmlc'J=!lmm04pL5:8[b!<5UfI3C**WTPtGB)-l0
UX_AI%Rlt.IJ".I(0jM@p!F[dUV&&0n*2b+P=#rOQKBt?*Rs96mKVDq2C1iOXoJZ\TK<o/N`
eNeq9ItBjTm4,e,k9VBs0TUF#@fSKO_YNBsZ9cfI#HUb5%gY%-cT4cVT)0%0PQk%TZh;+O$c
m6qPt3\Kn5Ah>c^Pe*')n/cEq>N%ZhLEbPL9S<-:IXLT5pT;I1e"!>tfRe.=RXb>A:?)mYa5
dnUs1\*'@B=ckqeL%g>bPa%1hbsnsWS![I>qI\00U"p7DT6uogD9N.9-1QXgZHdT<:ft'"kQ
AGga@W2#S+NJHL1JAL!i50jM!qd3F.Q$4<XOZ?BF+d,7A@Pr9gpSj^7875jJQD4^mVaDpMp>
WIjqn8Ta#2csaTe2@Sd/?-3?u@md?p>GgKu6B@B.BZ2!!8l4!:$c&.dS[L=4?=;)(#E"lm9q
S]A-/YR8:M5^>SBecO/bfh_qh"_:h5cC:Bb`KI>+iiKKTZj:mI2rB8:6\XDD*Br0Ik@6bXfoD
b-dlmdhQ#.,Xj!Q-20+EdH1aS?RBeqrL`S9FKL-gbKa"?AT!Zn0C#t-O4F(H&`J&?73S5Xb0
6H94i<Y@i?;\a/b-sf:>(gu.NNmj7oAm8aRhDs-ES=]AkB;>,@6o/]Ag;F5>TT>Zd$UQ>m.lFl
HJ>%)JXaA=<[_s%2fTPsjr85,NKUl6jZ$*`.+\#5">T+p]AW/=o^uT2&u:7Yn0`&:$.liBbpK
H3h6[6p75\MkWVU0msh:S1bK(8opK\"g'cD=R&u$7n5ReoLZDl91Mu7-8Hr2(<mB@)"Sa4aY
FSh(u\a-OI(Q\e\X(q.,M2$:K%O7\OMg-#Mn<,oh>&JPh:kq1I9@cOnME6VEsffVOi%ENn:L
#Ml-U^=H8ncCUBa.RT5D*$Z)fTQicr64='73[8_cf<,+9?g*e0FHN$6FEuN&4Q[8V`bP(2dW
q(*j^j*a9=6b;G7;:/Ghsn8P;Y[28,chrZ#P&f]A6cJ[`[np-/rBl',%lYH%N#7,7c]AB9YLY(
ANo6<`@JIp]AEbANo#9j5Y_$B$#S7iRjjlFHG)Oe(s@`A^Yk'Bj,S^,&#9(f($F!YsEp6oM52
W+2%9]A^nYEM@hj1mWPfdi@pd[V?sJQ)d-_L8`rm9]AkaT1#_s3_k:9Rtj:_$8iGXg$HS_sq5]A
+eS]AGt?%AJ@%&Ui+Wa%1h.mj8?(EMtNanBll4dkKB!/V?X/WHbqkhrD]AjUKt5j/;Y7W-0J$4
2;+e"aglL>"j0\;9e+*3ZOs>RJg??L9cDrc9d,J2=_F_)Nrq\MG#U08"`bP>`Xis4NN='='B
/.[+9XlAiEH_*mp6q,:XDLS',?I0b+DJ4q<K6RUE<a!C[.ml\fAd`EpaQDDnRAqn_UK,CjuM
G*J^@4:7Ba&o[E5o\cZQK!N"((95A*o0`i3nu<V)!C>G8T-A]A[3[nI9[:f%,J@U^U.hc1M_`
II$c8Td[`!.FS<NaunIC3mF#FI[*jrMcDC.G^[i5=EIK%7d-`d=Tn7B/ohCG911%@B8A+2F$
:A,?T+WaBLAW%RWSMTqm9($dnOh#h1ncuQ>L?HM".<i-;1CjSa_RX+9au*LiT^%#8W-H;OV2
W^NQ["BEt.--KLe"I?eU[_X<\mji"a%\hC)`-Mu9q-p&/MY)faGbV\I/J^\<lJc+qbFpa.h$
dp45ZkDh5d&Ui$%1UVtQT,-fEiPlO;(G_2m0$Y#jGZPF7Ot1ZBm:&TosrS@/\/e=<`%>K1=D
;P-I!9SINMlrF6$LE\s12f,`BA&q]A%$Meq<<2^V*1rAXBa5E_YBX@u96V\iq.I]Af5FLOWena
^GiLX1o`.h$t\86&4,u`r_[@mC)&b'*42p1RU`ShLPuu#CB58GKr?e6hWJqZA(RrAm$"@!1d
`S(5-3s$X7?q>P9WI25u$lsNaCd=`IT1gD=B((Co=49;uAT;N:sdQ6KIob,tu9R)P`#?ZO&p
V%VAI7P[>`;7(LqM9YUlCdo[[)6;omO0jfIRYVT?RfMOIb(?p;A;(b7N:,1)4e"uKEUQLnpj
M@K\aGk>21C_Mn0A&8qc00@(+tLXN]AW)M[Y'M8;a`aNRVm(J>#s2fiJ%'ZR`M[p2M@-\0K25
4JL)?@WNgss#OK0f=UCY$m1$<r.YRNJu^koZim+RTRWjVm6T?+D/K/sR1M2LMZ&,rE4b(UsV
<ajqEqn"jSSZcchWMO'>)Mstn=s<$C$k0CmiZ`!0$/P:ubVp-qR7C_/\e0<O_W3Md>JkqjF:
(sZ&>*VR5/Ut?P!?JbSA3d$7"6D@H)f^.c[eDN%=gg_^MnffN?[(L*T<h+^S?&;oneiKJkKO
gdk%BARoI[(f6F^s3'YL@;cT3t@Ef2%V3!*uZ%.S9jb)>T]At$@f(LWc5c"Nc?Gb4?Ti(6!O8
BK=Yb#cfo%6dB^?HHu\MVgl?:J@Stpc#^JImClaHftWPX,5H259g;C2:e;^Sk,L(l.?N;'p?
\J:ln;dPVsWK+t#$N\q8@%Kf0Wp8seiA68eO"riPLqjXgP"B@j5Q:$n+*k,YSP!-u.j9rQB\
kP`KA7se([_n_%1!OM+H,BO_O\P2DQo7uH=jLj6R08A7'L/G*N+(k=n(Tut;83`!Z#tZOuaI
4c_^PD9.^QXAF%Wb)t4+:&,I+oA@4hc\X[b:1Fq%r1#ZVo>Q@(VG0-P06&%GQ-:LPKVqaY22
U2V^_3[iXK(aLZb8D[3KBba5=e0A)P>S_FG&E+:YN0+?Iun/@u[=4@XCMcnN,ihWR&4J/_FE
R"Y-hs3_`BIt9(RM<MNQ&ZF)fCmGp1g/S$d[NK,[Z3F8DD$20d5eE$5sq.32cXQd!(7!ck+q
FP2E%I/M\?r:pks<+T&:ff'8qOrCYW;4G9ZbGW-Xa(;8^c\fa[YZ6oY?BXD]AL@^EVUA`d_Zr
X_LiB4`%N.Qt(b9UEhYrBr1u0Xl=iSb!:4)j7dY\DJ=PnpmUD.(JiK&8(&nPaMM,m.omLb,U
-qu9sNeIX-p#-V>Kn&VYf_K3=C4.a<k_=IGYl39<R!^.?-mh,lfC;(DdjNP;DhV#>NRT$HP/
WiGPmBH@XpMW\%EoS2pCp]AdY,LefhJeq\BEtmH62@22j[u"e`;4iLjor=G\66"-Ctfi8ME6.
*/JRFmb?0XQ&.'Xm#pT"fG90mDJe>r1dGR<K-gqb94m[X=\SOE8dIe?E^ZuCFu(Hd@4=Mqjq
N(%3sf_K:SU&D-jC:ETtI<80+l*]AUkAf,6!p/buk1fHqd[cKu??Yd7&7`BY2#1Ick=YL*Z*8
_Y`5?Y215iLE3mmr+GE[-;J?_<h-*9Xe7m`O*ijpGhE6:WJFh[?;)9>r_h6+n`n'j<b8?7=E
"'d^&PApr,#TX3Uc0\=_V6S0+0T9\&R<AVJNR<!_)0MrYtLe?C(Bk>5pfnju7AV>bDS:ZOpS
D0i=to&^MK)V\o9_hIc.W*LCR^2"huiEPS?#Ch#C%*kAieGpYZOfjE$i+@nuVBTb%1GfqA^^
?W]A`^q3!1l;c4H6kM<0^ruY:l9=+M-)bV.7C!4_Y*Q6a;\>*u3L;#BE>31"2)A0AO<hUn1pf
^o\V[:77-V=u(2hU+q+IL-(Lnrke0]Ab#KDp>#?Z"Nk7X!1rLF&BNFD\1oV(eKR(+cMPXD3WL
KLZmhhg@Ad346&3Z@JWkMFJ1X31i:WYln26B2?JW-TJDsRQu-FF>=QVV\AY!GTWAGk8CB69P
[=%KT)llI)h5Zhu@iDNW8G*%P)6EU?"+=c6Ke^p0G%_5;rY\"Y#!.\Y5g@NI]Aa?=4u@I*j$!
af+GZC19sC>.q/EZ=`nmn_T8W?g/E6jP<[0iNal`N&HBo@/Q_Y50>+lM,&2M:TJA8q14o4"f
R?"gOX?\J+.U5&>P`-0O?Y'[Kcg#Q'cnoAoE@Up4Rn2dk"mLN+)=X"GIK3pPn!6,$IuU3619
Nb>ZF%_WLV:o[nbFq4s,rm>j:bA@jDpIU007`^d>Bj1$V5W]At@Ooo,rV^$@1CSW?Q1ga4>b[
@Zs=3T"<[6!XF(\F%CAeLe!Y>[BjH[HA9)X*81a-r%du#=m/5=CXF.kZh/-G'IeZf'4."n^9
Z%6eUoPtO.:m^9!LZ,"?!np&1V']AFr684)^/<&Qa2f#n`/YXl%VABWce<U;"]AZZiTtSB+,De
4'r/(<!h]AXhqI5n;I,*$1;In>D1+lWo\'3B3(WZ[0.^k.6&n/P28]ADUqO]AJ%9(i8fT)!jYYr
A1^IH,\q?i@BXNTo3%BLY'\[.$3]A?!t.L0[?R94VH_0\9tM:4Dd%XjC+MHeHtu&Q%WUM9U.%
pSLliT`mY%:LeA)ZJO5O6mE_q_)nqdiHBXP8nR9$ep`k7&PLRt!+^D3(\V)3i4^]A8.*rVjH?
O%JGUiQNQaFcRfG57-tK#'sVspZbAF;\BfoAS(BH`4@d%G'.qeNL\JPkaWcP"/;.lO#(42!O
PR9h,u:eWK@t!NU/\0+U(Uu,s5im>J*^-/6Z$Q`]A_V7rin0^.DbDmQuh8giCKW+)1';!.Pe'
Wo_InSS.tsnR)Zd_7BQ^ROe8.uE>iaJQPX<2d<ncGa8R!<PoX)=_)lb$/"E0jZ`AA"K#Fati
9nNeEFPhl!9`gOD>43aUMhD,`W>]APDLsM4.cPJ['5#(*a119J?f,8W3#l<#gS^@cI%^B@I)0
m:Qb"cX3eU+WeU)I<XbV=?K6lb.6V0k5AkfuU=\5=UE%#?)CWG0Z4/VkJW@/Bce2);dEKDj)
_Y?(X5og#qn-X8Tcl(6h\[s-,#G3Z\1O0;%F787OA$Iho.ooEB:W95<7h/]AOC77YG7uaGp&B
QO97mmDa9hc>s;J)%-SKYu/m:l+-;iMU;(UiN_bWfTH>]AlLFc`(`T:E"N&VFe[`J^.G:Za9F
^i;:n;OXnVINgT[gIej>ln&$2b.?*+s.L+360=bLiX>W50HLCU7E.rj5gBrM*>hT&`qpP#^4
m+Uq&cFZM`E)[MB-s_*Cts^:RD?c-KaZiKa,o`ecFOupd$rFlcUouX/K">pHYC.LF3)>CnZ&
S>-DcTj8VTd@:f-K(=fuREn<ArZbgj`OCaRK5_<U[_.k(#B;&gWQe.BB<8U(.C!23D5IJWYb
5+G?c"sn/6%c!"_7+S.fp\)*7S'kOq),JDPRN!Gh!1>UoEIs(OA)d`/jU8m:XRHg!)#mLT1Z
EX5LBKjXF)$oddBTu-?B>(/44@^+h@M<Y94D>5X)r03Sar)!URe*J,-LJnO46G2/eO<?Ot9B
I.=g:!5BVYs?R67fF_h0/"h`Da0B]A#%M]A(H6$P^9Q"i/jo]A\X/>j).SD(Rr>g(jIr_XY7dOk
Of#roh%TJdLaO3pR(1jkN=1Fr@Rm5!ZTera1g+ajp=0f0&2D)M>)DD[p&Ri#rI*P_Vf"rF&f
8#g#Y3+\ZWg0Gh)`KZr^QhPPR;PHlI=\\JTE_.Ltc79XhIh'Uanb@IPkd>-1qcB"&(#J$ibL
J(_1.ruAgF#Pd;3r_$^aD:ZHe;P!bF.-RDE-K^60'#=)6>`1$m]Aj%p=(q^nDQS\#XF[GeKgM
5Aeg%3KBQkcpie$Vj<'"e?#_+X81]AEJ6f__Vp;fCh5oYlpQY[f*e=WoUa>^AY20cW0o+RbKd
FID#b9JP4o6\-+F89AgGm">56ui"EO&/qH[R'"A&sJP4o6\-2dq"[3E.5*5aAo,C86CVa2I[
+WW0^p7_F1'Q]A<ZA6`P:>)E#LW7MU=8a&m3-UI5_@h5Qrtj;.rrW~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="451"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="120" width="375" height="451"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM04').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="PARA01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="PARA01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM04_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[571500,1143000,38100,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[426346,3981691,426346,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="1" value="0" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="PARA01" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report0" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tnm=$$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80">
<foreground>
<FineColor color="-759737" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_jygl_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i/uP3F%.WMX8G1FoAnSCAl9asC]A'j1^d9R59RT[^^ms5VAHJ,2iU"m;A>[UUZR8@u6SAVP
mI/N%6@YDLdS"MLpG;9Ai7=HgX&[rd"OU0B[<"o6ij<p\d7i+4'h437".*&/WT0Fh\*25ani
G%fHD0XTgG.^KI>C6>l2U!.i?D>n7r8E@#CjF1MY"]ALMc0Iq;'A^[nS29bK'rWkZ8sUZKf")
o=jn[G#M8a5T!nLpXJ;hnID.*p?:6anD:aDJ#1XB=CZ\`36ClHQD)(%*lY7R0SB+,J&eO9lD
XEY]AHGJ4%;e<Z]A5)W76(aQVu"1FVFO?iPtScrW`al?FWKY;GL,Zc#WS:K9-K;=(Ng>pp-;Y2
,F6^1Mn+?++7$BFmTbdCGeuq9RNKA^(?2d',<B3==kaED\1Y\U4&M+442(jo)1o>DIKA"X[r
8/s&'Io'*seI.oMOKRBkTQS&@tn"gi'Y6r:NPE0(l?%<)<Ge:`Z[?0t?Yp'+b%GU-MK/D;HV
in7k?mKBnhuUJ?im?qZ;^m@=nH<OoVBlI569U780UH0XuHl/YG`H]AfhH#gH3:=/c9"4&qR=C
u&T6#j7Sch,R7Q_kS!UJZ(&Y)`r<$Q=V!,3LMCcIm27%rF@<Y:R$$!bFs=TDC5PUi[-SMc_&
Bqk#m+]AF'1Ze3`o/<_Yal!RE.(BRI$]A'=aEOT_JFO+:>1"T;[uhel_j/b)b?.+6hN>bgfo,9
&_1hZVGBJk4F9h^rd_kZ7cEjl(gQ=1<W;F*Msr"4iE&o[)87n6=L[o,/7A8bB1JtX\O]AaXYK
V='CTm[uK[s.K!A%Pc^B%YE.;^7]Aa^3DaEdTTShDI;jB+\H)1X&=2)-[/Pm\XT?NgJ6CRt,S
B$j=$KPP:=kVh<-X\;U31XE`,V2Dae>bmekpS"J_\q6Xq#hRutO"W)Ho76Npu77fe+QaW!8P
+@ir`Xj!$ri"c35qOmR@`fo7)"D-S'US0'W9s"4ImGc>iVE_^8]AeNqLr2,\,Ni62lhGYT,TD
rJ/(dCle@9aqMa#k>$]AtH;8;(n=^m.J$;[ql+fV!ofXC_r4DXqLQM&/(&e"I@R]A=XFLCbBmt
8Di4F[9TM&-,MIA@c"9<DUIQo7$LQb.4(_QYs=&raNu4^80:(JGl]ANg4!!O4TpC&oa+KdMXO
1P2nA`_NK:H@rMH;qHSgbV95GtVcD`s2[n'[Wa>FC+VSie$V;oqhp2sWtj:Zb'*<P`?C/d>1
6I?=R=GhU8<JHZWJbYfF[o0J9PeN(Nrrk>`C71ZN$1gA&seDOX]AF`<-U-0a`LU"WgbG+=^g?
EtSC$H'bM4l9SK]AlP3b3RdN^+CSc;KlrP"-)QWI\2r$CpXMrL84?=.)hfpdSk@A?O;=;[rGe
.T"ccP%AMC_4&qT"@Tm4D+B,kk_ZL[g9,u5u5b,7"pSJLA/D1DM$]A;,9N]A2?VPVMG*lS^VU!
.UgT9qZ0LZ7g^7r@S+YdU9VI;enRQGq!TQd]A@DcP;PAJH6sjE]A@6556h)Oqurg&i.*mkR'>s
jgcfZ3)5CJQfH;Nr5[O.'IIOPJIs2S`'`r?FAN,'.i/*JDt$nL!uA,T^)qWH.-4LJM+YTG(7
rP:F4$Ck<n3QF+$I":oqGC8Qa_&U;PsMq8HF1b`?V',p>Kj,Dhu0D\9&1%nX%A`%?Z"[k@rb
O0q_SXMK4Ol5/':Jr";JT\gi[b`Y[nmZlNc!nts/>dr%b^raA.u.n&?l9;i<+r<mh3Kg7Jf:
fV95+"3pnH0*0r-Vm^m:T<hS;DVG546XeQ\pcQPpN<HX$'d[Zi]A;2Jb+Mm-!5R30TPfIlPB9
"cLMmA;7W6Ib7.&T.7"4?,4`@AHn:Q)bC]AkQO'\lW#$l)^4!;<K?9Et-@,1MZ5tOl"SER#D/
b8k-Tad[TAFt<-\Pt2nK<._rT?6,P@#ZAo/n>2H5La.XE7OuZqSrKs0%B)W$L""dFsT<btVn
2ch8poQ\!pH4Hq#j9n=_$J6/\H)rM?mPfbFCQ53\F(e7>6LG]AmV4_(4YS.uqsB6$n+ff0+lh
H[-J2fYn#'&-6b.l/<P\FY#b4O9SGI-fIp3+e-IjG]AoUr#lB/a3-kiR+K&QL-*iUXm.J*:-M
7'"?uMmb!ZbpX2tpOfY'[Q"GoDY.DR-_+.$O"k/1CTHMU%%D)$;no(ss;`Q2sWP($5?!_#:2
jU^$9E6"8oQ&dZKn!DXGZI:p%m8>(j\l[Hh3]AS&i1eqMcUJeHS=]A/-n9e5jt>b$V=I,r_Mm;
?k1^9/kjqApBsEEZnYQg1O>W`:^@[J)1"*``^KNmEqhCH4t##c=D'5ul5..Mt.T?<c6h]A_E4
&?q`f5GH4.DUND6UhpVItO,j#QX0Ku=eZ<3V`PY:%Qs[8.:Ya>%Vk'g%R>0cm(^6+8bi"GT<
lTCN&mn9+h9=N=<$'U(."lp0i'.K"d:Mh#DA3rhQdG[\lf=O2H+pe05FXRS_,/&%*3,#[omj
lIpT`9XM;mGog[KahJU2NlqCTkI5]AIK-&OLuEheom^78h]AC1G4.La8BI3h)j`(b2Ku!M]A%4$
$(b^G<T9L[+d_I4+)9<L`jR\OXa@:BKt;B`YU7:AE"5X*BEN2Oo>RU:n72FI]A;``MYb3%Z--
)oZ#9&W@8t.I.o[3'no#I_&.qCNT>s&r_iElODKkHA/a3p_]A8p^Hr1g;l1L<4<U_[ZrBh6W!
Y^?NuubNeOr^<rsJ5gFeU0'3!#2(,]Ar8+PYg5bB,B<C0$hTlUBBqUBj&GCn2Y!l6AI>--2n2
&mtaR3QQ*bWP$5DQ$`<bjJh<qQ=Pa6dNg=[f5=/Mfr;:mK7Xk#I*F(R8eC,="m(8?kZf8Bc4
^i2E&Ng3L4Tqr;&[JilC/OYH;'2qjh"J08d52E*[g77>5F%)Jq=318VZJ3;iAVjo+P1'!?Th
P7I7`"r9C>%OP,Bj(.+)``ph]Aj"jZq'o[)g1oj1(C9L/VoBrrKPQkXnD,m2Hp0biT0R[Bd#!
P0_in[*BYO8_eQ?%+?7BmVKcB>s<9'JFSV*L62dp3#@#.[`\Xh6SP2lqK9>Wt+]A>Ca_$Eg.#
-]A(R>%GpjuW>BE:if2(m]A-guSag/W7Qo'"Q!.US,Y;,Bg-$/RugeHJ/r:^#.@D\Fn[peuS0K
3C:E&q,lR;IM'!`kScSRB.%t)6)C=)B7ILf2cJ9F8igg!O?NL'A/"5@+n"/$9Rg0Q74%UQ"r
p]A)%?$c$B<I.Gn48qap<"p__\LhH@Uf1V=GP'\jdB^r'H-5k2&;o(^@QG=?m-$dA%-+k<7nT
g]AP!,NCJX\)^a;TA.1e1IV)aWSE00!fXgTVetOZGHKlqn,*EVm?9R!8`"d)3Y:<]AirPk6[BC
7]AVQhd)Jq9$9=Rgl?CjGTUT0Xj#>=@Sp<@gCl#e4E)9N$*thL(lhWBWH>g^g2a78+QJ=hpp$
<s&6_kcM3!*",#^NGY;T"`eXTjs-81?*e7Q]AG#cS7G'pT_mEU4b,Tm*o83d4hOFRHZ+9;d>5
QVR[J-7,?s+aKDf!A9sQu89tH>.9f'a+ZfYYcLqRLH1!QeYN3oY#EK*sr&\J,f]Ap!!*?j!!3
^^!!FDE!=1pjrh$ukZ_kg2hhhdnQ^-m?O@."4riuJ+~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="99"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="99"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM04').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM04"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM04"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[jyhx_jygl_zbyjdt]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="指标业绩地图"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗1">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/HuaFu_YDZQS/3nt_Menu/经营画像_弹窗.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCRRAP?HWmmQ3k.)3n&q5hZKY5am4f8BK*&PU`Va8BFEd<BrT:7NDjoK-6ij,$bb9+nQO?8q
[0V&4-Ya&I8Xf6qR_1,WQ1VqgS#.chYl^IcmeTeo1Acjbc-Dp3u_LpKarfhOC;k%b?EM4o+K
ZIJXL]A^1?38Dr9.Hn\9_R7GhJW7c*RfkB?L'`D>)p;2jaXl;<!OTT@jUhG.ejmsWf8CA$>"%
pT-=Vmd]A5J)C1j_!ld4a)[''-[NL$[_A6eG_]Amh/A7QVm[@4')-9TOoaT#%Ybte5aZF9&EZn
kEK;ML-Rj&Z(VK,o52U!@J]AUcnK@$[b11>m7lFHC`ZR:kpYZDM%mVJ%`_DdT=-gNQq1XoIt>
]AgiO]AIsGCd"&M]Apr&djT5M$5_ft\]A8Zk,Huo;[=j4MOE?I4Th"(&sUPak=AYj3u*^a*t)#lB
(p^X[q'@s4(QWf*D8hdE:A%eMdG3bJ=-@h$s=#[+A2N.RnjZCcO$;hcoO/RfBK!q4A/nJ"q/
;<(O[-B)i]A*l$F>Z[\2GV#@AF*iCVDHa>-lM^1T0WYm:,tXQt;Dn![1O$:'b]A9KML7?De-tK
*N\cIf*gTc.efgd[8P5as11p:nQOVEB_gaSSZ<"(m1t"d2+V?Q59#]Ao[E]AC?G-.^gc5h/qK(
K!*3#]A'A`3YK6sHsB9!itG(]A'`JbJIh]A`8@PN^E`c=Y;UQI*tg)G:9R=_K+'Odi&CPs<H`_h
Rks\&I]A_s!/[D!]AmS?hOn$2@W,d?\>4Oo0A\aJ2;$lmA*i)nX(XoumD!6/dkhq>`N[J?^\7I
VVFgQ@nCOkSb!hO:B#V5+p7iKsn7_J.t9-'SlL#Ske!pa;gpbmelFV9qB%H7EPG7e!);;I03
>MqXuR]AT1HX\%!TGl\5Lo>+P2td5,@/Fnrb?97?-f"5,OE5(4R9J[@#1)0WSM+Y.Wk's0W75
Er\DJGa!##8m47WSU!<$sK]ASH,5>BV?%Q>f?\s1$0anS;#7??iH%m]A/Loq1CXN.R`r*0t`g"
eXgNIEjgcTo+o'3e"6J>^8V/8CLQU3&1%LG!Sc0,P$Pj4eVhi/:,r?jGXOIC(>;f:7OaMqmW
Pk%Y7\j$%VW]A6E)BV&ts<@1<ipoimFVpAl9!:+H\69)7+(jEB^O;X%AYkubq`]A3<<at\q6N/
dh(gI6DFAW82`V#(04:]AoG<d&cAT?M/Mm7:\+8>T8!HN)d9<5`<)hRk4fAD8pD^0\__bq0\<
imHZDm"_[%]A9_<RF9#@u7/@^en"rkM+Vs4`)nQ^Qq49X_"E1%Vt++18.3P":@`3$e-S75<6!
4E^@_KXF3o%8&!@u,!B%>YH76/nUL0;9X2*5Q%O/&eH%d1fH:dkS6d=gZ7hf$slm0=N("32E
O36a*IA01/4;-"8pILhD(qFX,)ke+1.MG3NCccO&n.n**%a>:a&oPP6S`qW/#hVYa3)ff=2\
8'LTup0gIt*Ui*]Aja[LI)W[_+_jJN$a7a,]APgGfM*qb:/!gh4*VhL\4Dk:)3>mj=M5?u>qib
+`T.f]Ag#,Ttr3C)>qS]A\Tj,O`$O`NO^fic!r72)!M[>KS#`M`I>@O6e't?f(@2ULB7>j>!B<
pI)<QO_OQ:t\MuTsnRG3mVP^M2NscX=\(\-4ei!E(JS0q)3p3c(M"Y#.8.FY*XTer>ApAi-0
^0o-SaBLIFpV*+b*nQUjlQcNDa'!Ce))!$?6?mo\TF>ThThL1h17ZM@TBD;(h$/eiJ4MY9&!
e)h\N+O""-"LBBkH&/a_$CO,9mZ,o5a'"SuReD=FHQD;S-(S]Aesc%2&rr(NIN3^Q6glVcDE<
NhGXhOl(V.'[=\&rV^0pI+mJ3d>Vflp5@pGP.Xr9Kf4C+gNl>D1tYcgg<+g$`,6uE93&&GG^
8H+!<D)5Z[o7(eL8'.UR9JNJ2sr`SQn[U@E7NFP*[SoDG&+O46R"M[HOmk(3@\s%R&@C"dsK
.EAV`k3_I-[T@Gc7.=L@9e?ls'2?PT3`JP$tls-h,F*=cJ,I3VKY>W]Ar:?0;r\W)Q8n@l$#9
Y_n%Ei5aIg6[V@>Tc$G&e9l)]A00mn]A)uq\Pdd8s7T"WVpM-*hW-=P"$>0s+(!-qN=#WSloR0
d0dQ4uA/qK>I`nR&G.WJL@(Pf'11+(T+/Hl#ZPL?a8RJ#@hKKP$2n*C.kqr!B"a.*ja6sCd&
=bI:i"['FOiQW79qZo83r=#/Z?\\3so*4X7\R1-U[lI4M'68a-n?V/t8C8Uao8\FN#j68!l/
)7U+4EA$WBfISm6)(G1#D72&Y&Njg0i:)8Th+ZU0?OAmBMn[!H:`a6AU4`Z+DrYb?H@+eg3W
h,j+=mpNm6Ss(\hshfmTQ^X.2466*o>(206T[?Xgr\iVF-W!,&0ZOg5c#l$@gTL^C%h<U$VY
rAS[pV6AI:0eVs;=.p:_7B]A"56%^k'@f:9XR<1gjKs0Sa:<r#V0'MV)i[Q4p-?$`D_/C%>(`
"1EH/@6?qW[m*tcW(Dt;l2#]AE3d^\^jip7hH.HRJ"52oJk8UGOQser")ee(o6E1=3H*lAOVs
Jp(K@_t`qN>5HrAQg+*rpd9lW:b%LXBbp>l?1KN]A^(OA7R%@0ikc)aS0O-#Chj6*R*HJ>PIJ
$ft)Lud0A@4'ESJTu%Q]A&%U;2Jd94J0W+lo/=kPM<AuSWiD4BhKZti;p":EK!^3dgb8%i8dt
am;ZnF0n*ZF(4H*^/I(rf3+TAk(]A2F9'p['IXB:h6?m?eh7r.oY;AlnqeKd6sX5W]APp1@7V*
-(l?h]AZS]ARMrL'M#Z2Dln<AB@pj4p2j[,t"mHB6<[N92ZTTdq/mnq1"D5O]ASgB8:`hF<[_40
fLFKbto52MRo7dnV)E)5"id^`1Jb<k&_)CO?ceUMq?l]AHjm-sV`r-@g8(G*TiSFh4Tck@U6`
8B9qJ>X)RXY77,]AZ"V;F'5SnH7W$nd.6'L%h0)Ecn??.<SC[lkE[Y@JKs1_K#XGc(d)l-7DM
(1U*=8>5a;1b5[6);FYeE&]A\1pN7!8@F9;%j-Cg+!,C9.8N2FmVVn%;Q<lX[`2hEk$<;&e4^
+IX.S=EX2pN=i"\N0P+Q:RTmmIJupl3PC`7nrgeg_50[?]A.uooQ'CLqn7M!aehb+e]A=\G-rU
WafVjsN[KSHL9WqaV0/`DNY03/<OKgqPaD6iAu5T5lF,cM0sE^ell06+/Y8Gu*P*2kC>mR6O
#_b;BJ;LKa^m+V%IFf>Vq)J)]AR-$W=9GjB;d^]A2bc]Aq+>T'Ne@GOTE6!.B6gai@$Ue&&0@8I
`\=Wqk_Le33pjl[hl58`"%Fo=`k%'p#lU`\YAhrfR+8HEVQI)2qtcH.Al3oeIob[]AMY,QW'i
.S?0Wc"gU<'BWKEcTe+SEK++^Na`7/iW+D]AGmqqd9lUUB.U:0]A639?6*lEN@)!O_Je/fhi2]A
<GQDC!JoGe?08(XsaiS[O6@h4^jA#g6%U-A@NnMdTX_Ug>nD#`G,aQI9E,Eku*\^ND>oanD5
2-gV=I:NYcD[8:2]Ad*NaZ:qnbhhSHF#rGdd'Qat[?2cmb40IQp9[Gh6PnTkA9YoQg=nK:g!/
0rql2(r$MUtpe,lu"AggP-2c%m;(*f1T%rM<R0<YZ1"''Q7rXoH29"6b><ZgPo>^2adJ6Ted
jJJajGi_LGR1X;sn?K?]A4!_=dlY4;P$-t%imF$Z8g/4\HBeB(9br&NQ_>'_g<h%2,jA)3[`A
2T*:4HdE"-@I('ZTNjS-JTJ+OD7$%l'Q9hmo!Ocud)5cZ[2H:eH,gPHCu>;PEY"9BUGR`gK%
8efS/<X(bMZ$#FeH[a;+42i)l2>-4g;LLJpJe!obp31gWoY90NN^:MZFCR3G"r!o\JJ^#HbL
@\9!oj6?[^#U57T>dd!AaND^8C4A[bJIuqY/D77VI=)ed2W"WLKm,cc#]Au3]AZAi*)XnaMRk`
,ZF=nK'FUL;T%adR\qtnb2.gJ8V;k(XW?HEckjB<'dMW\UA/^dBXIOG.=!m8:kXKj+)>`<Xm
,F2'uDOY4$0juDshX4S&%abcd.)Hc.[&7F;d]AiJo<p^:@#,TOCH)eoHGQ^++`XB:t+1-Ir0K
?/&meqD*@G2!DDg&J=fqI!'j'ol/$J;/EmGcK2OVUgGHJ-3PD"g<a/YogC+Ac,0[DbAObb/:
7@1dr:"Li]A/0D1W.f3%#,^.FOUNt?A(RA\E-\@/335S!7Q(fK4OG3'k9o4PF]APM[B?,/6j8U
3k3R=UDI%MsUR(-HmlYp49TK;=VJZeu55oEpt^l^!D$sYV"XjWA%Q",HkUPJ0hsr6V:V1ZUa
:>3q;HfE5sk.JAXM4bA)8r1O8hbNsN>A-Zgsf4>\qB;D;UgDq*n6q=uN-]AWnn[+"*Fm@<W*/
#Ofr1>>I==aEC);Y%$An4,\j6chps%B,Kq/<ctX]AcY-q9i(KsfO,1M+P^M3Q_GS7s?)?r#j\
>:fa=no7#gdCAV;eKe1h@Z33n:O2qUW-O6LJP7euXE]A-PWe<<[K@fOoNS+[TP*b5^2cPM29,
(L(26"kSL#*_(1KP.`m+123%Y>o9!sSdusu(b3R.J<(fi%K&V;&^".WTbETDW]AhGF,13f'cW
+[m_,U7Bfq:PX'-q<;UqL!_,W)_itYR<\KoBH$h-%^'N22>MNk)G_B:<\">R37(blaIG<!np
D#'@LjM_c8ZF$CAUW*=M]A\frZrsJ4V7J?0dg+,i]AadS2o7>6@DO&4o1g[B*g.l\cmh0g1\rN
i]A$tO3LdA(al7Fa-13BG_HW?No:BRZS8no*W[i"&Ve`:\)CB8T*b)&RrlhnS7C7`ia&e=M;E
:?H;NQnof1.p=eW&u+*>mCue*6=*/g'S_2,GA*<fBhuMS-(rR%&B\@UGKeD%blD2YiNQf2mj
a9O^*jnf='K2(]As@/6E&B\K\E^d'h4'RZlIBIGlt]A*>RG\"8,/*6d.lZ:3+:_.slGNHYkAfp
Bl.\m&[BY/UG4ASld>n&<S/uEb8P"0KAMIJM2$TmW5+-k/'4#Wo6T(-tuc%`o;FMN$"QqT:1
._-qn3t"S?"(`m)9?GZYR`B5q=HpQ'Zce,&s[8djIHOO`qsNFdoUM.'jIBIi&\SbU.[a)ANT
*RWN,LPW<ji,aFSF5l#a`7FP;'3`EsFC[@4WYGT2cZi,0kZIh+nnY_9;Or'GAGikic2Dh3k,
uE2[!DuJm<!M/mtgm,dq.[2INN^MH@<o;N"SJ59"CfmB1P?5\A)/KqL56V*;A@k0'^V]A^Ku%
rHufC?gO9:_IWULd.oP@Bh#_*5RQPRl^th3t@0#]AQ7d0\t9QMeK.tbM[A,)7T#59"'#M27Wp
,/j[qt3Coc6.5OaK'/3:HrLoZHTQqpi/?ff+oc-T4<]Ad)0'.Ka8`4MEHpP)WdVGC`CVif/&u
X5hHqmS[S)'^1kr?7!SHh1OqMpqLg1n)4eWrSXpQT+'s""&IC!W=aRUFX_q,>D9LAuiA*._l
Fb/[)F!Ak;*73U2V-KCOAuo!19<+OXfYE"Nf`QrEpO/6A>GZ%RS<kJ%A[SB=)SU_Vaaa%?,J
-Oj*Or1mcIfJ0KRiYk2`R&Zr*daV_H39JdV!tmqUf,iFA>fc<53kl?l2"GI^TVJ-!b_ODb!q
D>2+r*>`f0]Aq%AA=lsM,AKF98Il,Y(3c1md'+WXV*,$.<YMXs2hpV'nfSKdU[@BP`k6u+/OO
NqFm2m-3"=HUQPk-9cKP371,%brUV!k&5l<L"d?\b/6\4;#l?=b7o@d+lc5k;FT=+!n!GBEC
@nA:JNUbkj%pg5/`s0&9aPCL(6"DJ41T%NM!E1.lHS>;>Tpd`,rrqNAXJn;sL>?HF:`o2Vm@
IDfs-+GNdDlYC+u@CM5Ur;.c8TsLO63MtRNF-.?F"-[QHiMUtR&9:;t`fDNBL!V2).<3GSQ\
daJGP]AnF2BlTerl*m:B@YVSRl[;k,"X+#"1pD165/9./A3CBTbFgq[:i%d`'Q9]AL1)kb?4U[
3Zh4#[+pqgKFFK7ZCG'j;j4\8YPV&#)6*;6K`*/V;ee_RN#711/S(h4CCI%AFP]AegP^j%UF=
tb%+q?hR\-91<dia5b-Cp&[5:M>5>bq%<soo%R_F"2-&q)Nt_%QDVHIWjQW=!=9i0/U(o5Oj
7Npl]A`s,Ieo=p=7spLWOK&%^k/RB_840hVo5`X:f&:UsN&[]A8-I-<=7Ht3(i[`6bD$pW[ia;
5D2hn^0Q<=n&_\]A'46-YH1mk\X&Z9CaHSK'Mj2e:7G0S'6["A6#!Qprj3D%fhVaD[h<YF'IY
<tsMUXCH=gh:G#MgEXjG.irMl[o$?d;^)N6lG6i+mf#ea5So3urY<BkFk"aZ"*``RBp"%B#0
pO0".Xk!`3)i<P%O^UmL!UFY1g+;SN#c;hpKhGI@&JZ]A@mGOhp&In^.lm06^Z3t9-TIN_t]A@
>=puC']A(iEIXqV^^"&ERPob7Y2B3Q^4`6Tkf=sRgY2<iHS=DpieCB,LVk#HgTI:fUL!eS<4J
4Uc`U5J'V9D-(J=!Mj`L[LF;QjN#"XQF#I3#[eT_C>7ukRd)F5f'@@ATVUcg0TM!!X(>gQ4m
W6YJj'1[BjXo!k@4`Gtem"1;e?8%,&M,WVW.#D'`h&qDg:J<l[,K(JRG^4uVJT%in+=r*IF.
i.EBG#jE:CN!?<hZg^jMd&$E_PC>I(603hl"X!\tTj?nCS&rj!hm,$0_dhUFU'316s:\'^*[
qCTNcF"Pkk&`(#Ot@raOj>_7&_$!+`1FRec@9UY6Xq6]AN,XXN'QFtJ<!4"CX+2hUn7Q6UL4i
Js&FKXP9ko2`D18&,?Q_*S`BZ@7X$?Le3@W_>$\0+&W8G`>5X4db&SbiWLj]Aq\rADUn/?0]A9
$.gdXI:ea^IR]A3cs#Vo_?r43&_D!W*g4;b^]AYXY.t<QV'.l9XkgL%'-Y-c]A\J?P!L9Z$qb-:
-.?)E/n$pII5,),).+'_8NAR/RoO9*At#!s-^6c+Y65bFp*`?j3S5mL[T=Ci>Q!$RM>=$W3H
XSl;bf<da)qLK-eB'F6M^,K__EDL*7omj:V9&o%q^0-]AE&Mi#-rkG=GR"Hangg9!*(pl4Io&
\Jqnj5h^9TRLCs_E:i]A6ulL*_EI"1$q>DsY@9nB1Z=OW#,V;S(K`dLs/U]Ad`+ZKgiUR\BdHZ
SN;:,[k9g/EAQYPkb6L=aVs[Nue?TH6YSI>n%piWPQMh9o=a2o[^F!l0?X]Ar?HbQ"`NJP14-
nApe]At/.0"^kOC6HpIT!!,=L#n4U%Pjt;DnXObeYt*MUo8\TA,(J)E5qt_Sa28/'d1:$<6:P
U"t_GL*&jk#R*>\"FRgsO&g)-gaI=#e'9rR3d/1l\8RqOF(Wp,ZI@Xn9R\6E=bHcA"(M/9J^
UnF7!pJXgY9O.B((KrRJ4s7_ESpKk18k9f`4Na8h+%sKQW?TSMU]A1^KR/X%\`VD7^LZ@rY%7
AJbXG5r3bXGDZ9l$:kVI>U'c);rCn<%We/J3W+pR]ALmDp'G(Y``<X1b?<A'&P2lCIVf!_\5q
o`gpMc<b?[8cuI_HF3aPQABTW;DrGO60BfqX+JSik3FZ`Unjj\M3ieGS+?]AT(=@q(Y.9B_e,
a8T<t)*=;lgG4iPR=i?&TGc[3WVSbA6sL\&XWJe$Wo/$sGVR@VInM3'46iGp&5]AGkJ*)[[eO
Eb1$3)O.n6fA@BHWl.1Y`qW0Em6YTA,0B[\Dt&"]A6k@9>2YqI!]A2\5\A?B*YDNp"2.ZiY.iR
8RrhSSESYA23(T9A]AjW9EtOorQko`?f!J[p2Na!O>cYq+2Tg&1B<02\frkb(jV9MocRk9nnN
Rs+1%DA.DtegOV9T5^JB!6d>(=!Z`<'ociut(T@Y5io&eC(4E\eUY/".\BrG+C^JQ\Tf]A!ol
1.6EC=fcJ9cu,cQ/mPeSDK.]AGi>.gDkrSc;=27A`mito%&8p+qEfT*,u=oJg8`<E@7HS@F;0
kS1NYm+>bEFl[^(7qG#jbUV]Ag2]AjoO=ri=Jk5-aZ0lbm@-fD(9hd%N4SD/@5u?Gub.>SuE*s
$-lc16:>CA3#:5?gH^L!-hc*#2&YbpU@e[#G%c^E^`Rn+HE2<q1#F,7jOs_A0BB0Eo<eb.g6
8+40L%;kX&\(?Q+&@!m,W7rR&gp"YX\>gp%^cTbnt;[YG\\8h-9=NF=#kSl7N5Zb[(OSh3Xg
^l`\&@!^,uX'M.=PTEWS<(Ti.V3R;UP?:nhM%8WcF)"\#hMa[rgjMG<)\M6jCA"#:2Yp@sRr
+!^mo_e'EB`CD:=S:,*1]A9-7LnnX@`8\Jj"_S'Ib&`U$L%(i[]A;`Ipo=AC\gC1^fW7&+Ic-<
MqN<GAn5(9JWBc4IK!oEV8,,ZEoRq<C!I:q%n6lXu.]A*Xb^I$<3Xm]A"*;;;2!7!)?d'TFaA?
[9_U&eK5o0:bi2%3UCKonA)U@pb8UQFXGSNVs`ku]A>eF]Ao!g@4U9'AjIOHd4"/^V"iPinXVM
t\c=p2#=R/PG&rc"5uA;1L_2?eLu"I,uWq&=V?p9XS187*i9D$6U@YjAhnIG;Kj8th2L%38^
#EmLlXJ:h8lgO5KO2Xja`b,7;g5bYcB=#%#HOT]A-?P"HC(h,8Q#iZ@m%,D"l*m=+r1kfjXU7
IV,W^6[/Tkf1I^&b&XTR+Brq";0$g@g4i)]ANTK:XXY-?=7kc`*BpNhP[qId`d.5&ViMYU+6&
_pj=tV79F\b-*Bp>:9h,+1@Cga'NM`<FQ4#i*3DsV4?_\ka2(hOBhH.VN)8'gH3i61!rTf54
oWC,(kap#M;k`!SRFFt!FuANuKNX<nVZOeD@d'M0m**]A6F>@@&=rO?)drWhE%S.uXBQ'8T%-
ZXi'5&V8kc<>IUo'W"H)PP>80=\YH\X%iU:oCWj2joTe/(@'A9seiFJVtn_!S@7,T[l:Yt")
E_cPBnWp0p=7(i&(H:_-0MM2YRVSD_\hgDacNuK']A=Eqr651KD&5Mf$m$G([@]ArJ59npP!-3
Sq7BnoCocBUe$H*$AE%j`Yd4>iaG,>=mg9$El$!<V<g^r4YIr@]A8be[fX.tP'l_HKU9EsBWJ
-;Ck;@_bN>Cg>If!#aR;P<B&>CI]A(Z=(h_Tflj!ri6HT!("q#>aR&V$2CIpt;8-RP"'fgh^I
R0%_Lp]A#nM,\O=Z(PA7.#0]AXhn(Y=4DTa^0I\1]A<Sq4l[j#?>l,QGh7A"X9>Sc?24AI3Idl/
2/5<`[c\f=c5nB1YB8l1*n]A+1SK[^Kl]AmFK2qNDl9hJHY96`U*%fV+(%q"K<`I:;936o-[ht
,<;)>T/O.4jjN!>=]A^iZXl7kWXo:`pS1OA\A7bur-=FBE1f:;cb(B)@I!fPRUUD\7fOU$rIW
IMebc5%02qosjj=o@ZCP/ShKPQ,cX`Q$r&HTaYqr$LdIp6m6q6/Bm(i)%MQ2XEYLTFc:t%8&
N19>i)5o=_i_bX`inM9;@55Kt^TXY!:O!.h@7PF;q*7pEMeXRU@4VR/imjAm/<@ulm;(E-i-
"628sBsBP9K+)44GNHt]AfcWXJe+N67]AQW''Ec)9/Y<UQX-XmYBIPG8hilBf*SmC3qbb3`7h;
@JpG-*1>`+SpTNB%S?)LphLm6DTSj&Jnl'82_B,q+%dGi<leWhM%=]AP@Df!H9EYlNtRdHc!F
ki-1HR[#=XWcWXsc.AVutHcJS%XoL$q*AJ2?=I!Ltok"'@5`lE8HKO2g/SLh)`Z*i<Ye7lqG
sc_]Anhi^FMVtMYbq/pNiRhRLC7@jLje+Plqtnt*('=mLEAo9FV8nCO8@XFf@mnpiiV;NO8^?
I&0KT\-=0&)jr;g3-?lX$HDZ[Tui-QdBpm?6W*,hYOE5kMHRnFFo<RR4^9Cs^okZ,Tk>HqSJ
/6)\TmtZ'pebA_3mBhk!Cerj2epkE44^e30Y(bd*6_7u'U2`JhWt_iT?4ANcMOL,K<Ff]A":-
5Tk^FQtfMaO2[e%m#o#.Ra3kP"fmCb6K:6OgYJ%!a;EpkpnBl=.^.)IR?3#i2f!lE"j_-mE7
rI,l(#jaen3&;I2U1E@ASf`U,Whp]A9p=.S<;Ah2iMK-KZB9Cu&ATeEq;YD7f[P:3mb=W,K*i
E`J,+u*u5!S<Y3I;(Gom!IQ@oJa$YJ/B%*W?%2&S9mg:LT6Bes8HADLlu'QTNd?3@&&(k@@)
F/Mtu>#qaIf*8p1XU,i;gl>JbT2d1'@q'48*@5S86SF/(;O'fo%6N`6`XUU7:T,`j_8/_f'Q
<3?&D!>>/H.`[WF4V;)NSafJ"R,n:oUj$'gTg;Fui@gdLi9rMY4!\N"#r]A0EFGnem+>2+A.#
;$+%pkcIggWY`W0c=^K%A"HD//ii%FYup!54$W9<NAT&.F:KMUP;h?Bj5*Yb!+/pBJe>c2f"
?7VQNHNUA_F)XU<5S)Mil\lDjIipp5l3QA=-X$+=7COTm:Dn1KFGdJW\ND<T%a*8cEoH&>Nn
_M[W8,=jMfp.&j'M@3WXY#-m]Ac1jY9f$(HS,K?NG-JlhJ`F%Y"-(dP6",RT`mG6e-`ECK$nD
1f>AGFi1EG^gXD[7N>9)F^2b5C_ZF2M#I*<un-XM/D4@*6k#mur'CR>+,PO`Djnd-4rb69a-
Ol!7.E$mgtq=`ZeYgR6?D1"q."PVkb_f@NKFlHnfSHCMk!=nA!]A0GZM4pR';1M?940a#tr4&
ItB^9;]AeitUY`9[3/4"8(!D0t?CT&RUBJ?eaWSTWcf*6_Il?:`;lA#-KcJpArFnrID''!Ym+
,agCmdBLg,R"_biZ^kTa5<n(5[GK5@PO'=b>_J>#Rbt`M9nC=->nWAu\/NV@u0]AXT*4V5sP.
i^X?^=Hm-3`nROB11d9%j'JQAdU#V(=UGu9Ct*.e"(qc[RL##<]Aj6fMj+)[NGAeQe;8^M.SP
u/JdSnK(Rcr"S3Ce5LiE09U<MnnHC(VZp1?O%PpWW&@nj+]A8t&`pjcH;C[Y]AKja;29-&k<s!
2c_1ar\uj_krbUU^bqiqg+8,WOZ2p3qq%K5d,=:I@FtWkftJ&JWo^@`M9pS'0&Js>."?-s>$
CJ&2eo<5Q+E"!dB'7EU9KK(/B7l;)?r2FUaF+p3oXela7gnR@)&ju&.1k[+eVgX^.:R2p#`_
LKr'XC*n@Mull&TG.:Endr<"bARif<">tJ$)gh^&/R2bD\7!r,)iF+@PQieX1>-ZdD(#TO9>
>YA9X\S?TiqFZa,Efh>@K<A9]AGRks]AuPo\GI=/a:tU71E'\.1XB!lC]AWZ1m%i#q,6jsPNQBJ
_Y'hPG^GD'm_@,W=DLs/>.R8'087$n;D`hc,jDC6/j/8e!g<b(r3(E<VpA7ttD`j+C&'f/>a
/C7a)X4`EC,e9dDBk/-#n:&jij4$?3l'MPIcK6^DK92C0MUm!0*X3e6;kFFF[iQt%D0Me/*&
$!fX+BRbCKTg[g/QG429`=%;MTIh)4,W8M-*TS'7je(I7S?>0RbgD864>X+P`27_,`K0H7m.
1]A-!5:H]Ap1;K<JbSmf6C*;MZT1qlad\M-itQbH[9M"Zgr%:9Am.*6ZX7FAb4'WJ5%o,mlc`[
p,AJ3Sa$[=]AYO'kqQ'Sa$H:"Ne=a`knh*,9pi<ca2t-PYLk.Ae>#VX;cm!G:\u"!\sj]A`6"B
hW("C]As4tbYe_9d;;2Z:&4nQZqD+ZckV>9carnGgl0XS/8s]A3HV-l;!r&(MUi8PtDb4P3]AFi
[0=HoQ5>)Q.9!Jj9QMLU\MI:TUbL4HGC#n:+Gl-^nQ)U<'3K^aUJB[FmK9HDDP)M%ErL'(bM
EM&>BAeX_U2a!f7@Qn'86Z*'C4m5;9fZboPtal_Z^#9Y0J[2=IYgTDjk7poJl(N/Map)O&AU
1JW]A9e.YIU';]A[EKqHLY&bQqaG5ZF7qO+S%e9a9(;E+cg;OP&_B]A8Xgb&CMlQP(PE``4J*L^
YY-+&arJL\:A[jPrL!=Rs\q%2&6OE#:ZopT$.,9LJqjnhg(&ecYgoLA4n-X0#:D6oqD`<#<H
0+=0S:K4k;]A^,_:#/mQR"Ve?X1i!H;Pfi'Jg-3Mbc-aNud:ktWOmFj4@Q6qo8EXDRKgkH+;-
1Y;du?05Ob8.3Y^^K[\-oW%Z21+F2(1BnF5Rf(mW$+o-(Clcc2#H'Nb0N6_m92UNfh\.%\b,
P$'4O`OR7B>.VXad=uFPI77?o)ASNO>7%n\Qgs<"iIYUIeL'j"TQR@8j6D'TNfg#@R-WSZ(J
K,$h66:42F=:$+D^WaZs9DuSaN8986UPbohVLUH[jU=GSIV9i.cJHiRNiuZ`iAjVb>JJL?:6
\5%ZiLEJ2'WQc019o`K,o6uS9d1WLiBa4klFnr5p$H.-M5j3(d>Jf)Ee6IZ,iN0Z*sC/tLON
PWq>"]AJ&1Ij.kJR7[<3IbSPO.9$3nZ"no"2I0lO2aPAZ4+Nl^N+k=66UJbFETFXD(-]A>ed3A
#dR4kSQ?ZX.ataClLBE>fW>XihC/YoUl@=:F+HE7ffE@p&u8Nq[_A8uB:H,7h&40F>ilE#BE
B=!3PI'VT+/)\fcNrO!7pGU':q83i'!q^YPjSbaOrR(L)mc#HpJ&9:IEGlXU_X.N\2<lDd3h
u55!)a=euc\$#WVZ0"@A?ZXts?ON?9(a#9d/B$G5^4R2jI,*gp\!^N5kb&"_d;l@26]AOor&'
7J-=_XL%u0>FD^b6BmFH9RR[Am4.id*Fc2@XLs=.jOk'8iXIWAoM4%9AUF4gCgu![i6,Y,32
EV?XfTE)a*Flkm3r/YMH=Dm"'3N^L+?[@`L]A\Gq6dj2m*=>H9'ka%@ZVR_=`()'Dk2>W0.@'
&@$RQU^"gr!D)fP7M.9G_(%o7@&sUOqkJY(Ch=%O3FCkqD$5;^lL+jbM=O;6h5D-_5@oo!rI
(oLN>MEZ$KiuCS;h_FiO\hnZYS!\5fc%(E=<SGff1Fc,;DB9I$nJG4A+Csr<e`u=#?InK,Va
#$>E%AQR>t]A=qMBk'=(kXFN5VJc[N(Kl,#Q=RuJ8Q35R5mN2UM]A>aq*_n7h(mf09NBM>VOA7
\qJWYmBJ44Rf[iiqA;j-18*F^.c-o2Vl!%Kmq(Zqm+oq4SAP0B466pB=hGn?N4p([#*!<m33
/MrPaa'8fmT`02;m]AIou3)LPsJ#f6%UUr.=')jrP@"->;qorcZ\jb]A(Y';9SMfOPfeHOpt&D
[+sSZ^s*du>Ud0d3mlfA81+]A_>/>`NN7n*e3WVH$4*iRB*^"#AjF4]AgFZ6J=;]A3H168uM]A)f
V)r]A>jJ,&Ul`\$@]ATN[/lQXFKd-X@$r1[_TaEh4`)Ol'kCnLUiFX0<*DNRhMKH00%_pP%1.S
u:Hsk3s&i9.Wn!7Z>7PXB9tL$aEq:"er>hOr_7adj><g*89eIlp)Z?ZA]AX5e)8UR*>"l5\s_
XrFlCOe^?XD(,uSGqE(?(!M5MS#;RG??I2=66nm2VK`iXCi;b^Y&2Yg6P<ki5lF5&<R\sXI3
T1hJpuEoSNr_Pp`Q,RS6J>k[_PZ8/mQss5G<//NaaZIshO@^Qe"(Hm,`:8W9C,?11,8XG='3
e(KZ>_UiO.rH5k+Q(ltWg#YX*nQg]Ad7)Eh>LUdaWcMhA*\H]ApH^P^C=qm+=H/TRsVPO?1pBW
1T.OIo7,nDb@,0,.",]Au+Qtbu8UTam>Qg9UQ<W&q@+XLQb]A::#gh#i<$,;hS5g>G.k1$i>0Q
"0Lrc(R&H\'Ksgfl-(QM#<j'J+go-8VNW+@Ho"GgrV`"%n[X(nE2>(P1LTf3/,plSCqaO^4]A
03%NCS7BOUF(4KFY937fR-6A@=>TP:$C+3VW#%L@0X5*fOL-gKtC)9MVR8Pkc@pa-@`OeJA-
9B?Jr3GSVgaKKjXNYLfRbdnFEq\\Shk"kOPb.EmFVFV"RAN%E/=ri=5*Aq/gcYioZok<oM)P
!a#BcRa]ArX3m)U"SDgfhX/V#CVDs2;o&#!hKpQm'pc)KuhKF$6fSrJT9d?P[`OVfp]A(!C)c0
hJ@A.,3'ei@kE8pSZmfo!,*En%#McomrOeZd;I&IcM7Zqqs`lrKQrj%QD1B=jG@o$f[B1^Im
6WXCNBrrISk,hj^$C-h8m/`HZ]A'<Rkn1K-DPm4dVtdd)2QF7&pdKbsEq?N7j,=ol7]A.MVomF
BW4c)9o/JZ&+<=P3En*8rNlN!n34Qhp2q;;`-$q;WUNn2[<,.fSWQ(AqQ'(c2;2%?YTAH;M0
#j0sH9:UYHi8R0RC'IT\2nBYNM8G#I3i18KV(`"3oER4ek8+>9h:Bs@)AHCiAYk.>hqUJ(Q_
AkuS.Foi*?o\J(9@BP_mWK'SXp8HF*)VllR*Q;FT)I6n6PEBUm:PhSHWM$A1rTU`<W3GlQlY
aU/6(j"Sl9gpD\sj9j;@V+rT$9;@B0.@5AcXOl4Bb_=XkGDJMinG"c0c<)L3>Krn+X\:Sk2\
^fTB2q1s>4\7SrPs@M/FKf4WlQl8UbWq(a@1_\T&R`YL9aR1NL6,"2.j9*=#!J\7S[Sq[t+J
jmWc/H@3r[11UE7k=jBs/T*gO&'%Wh4[DY71kId?O@:7.jL5UR^Cjk`>#BZ6OGq_#9g9oR.C
`uFd,iheAf<srNW6A_Xq7op<j^@F.H*%+h#[a#<6!DO9?lR`[9;Gd'?%\lg9$6Q#r_Irn574
;q'&!T\LiR"[<#\S\mDjZ5b<LgouHj[HJj5p`p5'dd9@'';*&"H@OfY$9"(=[]ACY-lP_gXdE
I8`s4:1]Am_)2`CNhKi5Y-,AQjJ3JnK.F+>:#oWIEfTFk<>eQ>iL4oQ'g$M<XJ6,hM/]A!A%In
@gU#<^#n*XoTD\MlQ"G-0"0q<XH_FXFpdMiUc4poKL.m[WVQ6!fVKC^SPcg91:p)amFsM.13
\'g>858PY8Yh9kZ2'^'T\m^;ROqXH"Nkbtdirn?5rK`=!A[1^CTIT@UiA?<l1`"GF,XupLZV
B_n!YLDm7H&)&E?SeJ5<dqEM9"eUKh6`^V!o+/qP?Ve-5[4([(k,TLm,#Zo$TG`nHn.rT*TU
$J?Mqj>1gP3LkhqX_YnIp5M`F7eZ!oZsS7N<1P?a,Ls>^QM[&,5m!gIYB@_!Q[Gjmm]AR?N'>
)F;$QPInB(gC48s'Dd?WeR#A`SYc5p8]A!T?8-s6>a"nDrZ@<OX^W[h-6;Ba*=K7`iFc)0BPT
EM<39._3_p4Ic8ZeD/6S_B@Y[fSJn6C&011+!u+G@f:T@4ij+ANNB-oV$orU'>#-k_Lb)r"c
P\-/0h8#G=#]Adqn%>t<3pSFJd7LkL.D2YcdGm[F<&U@)5JuCS0JO`d.ZK5&s8$WCYX@IOF*4
1^Utj"KZ9tBfjTGO?+21un;HRs?Mh>P"cbA6V5'jE'O^eI'[;7;(I7K?6/ct"7<t_\bC-#'1
kG`0H;6#o)CPIQ&./R__OhFY`m]A#hOQBE_%Z\hW=pqC?1&#\W/HDPM,e]Af=oD3?>8F*4Ot9r
<u,P:_u/O\1UZnZhJX<<Vs/([4PM/(&-,c%M6gadT8?&cPjl+\g$F<m[Nr=)fl[F7KopNmPm
p]ABe8s-6%uEcab+!>kXeE+LYIFbA6Nug@\(t6@/$jM5r),]A;g41r=Q:u?b+qUUJk]AiQ(]AF<0
D39J>-AJZYk;ON75S+EG*O9L573)DSLWYYi>2IJKuj:32:MG\`BI3AcL6R@6\gU*!VlQPXQ+
XraCiFTEP92qDBijpW@E<P+DJTapekP-RD=1<mnE`QlP2Uh=jiZF&Ltb?F*B]A@(7N3+Y9@:7
<sk""CCnI7WA9HmJ4.`/pGmGl;t5(kGLN&:7>-`,@Q7mKHCHcgmJ;ou&?,'4G<UNMkmjsIPB
Hf>HlUS+fX]A\>K!XX-9G#?A<X2R5=/+<doJY@0;&$=.:I=$Zo?37-j5UD5k)C[Hq_.cI'VQE
tZtm=qVdj`<@XW`lCs"eIkb$DP7X+0?7:L,M7?GkpHt/+p%3j8_@D!]A%Cao^lf`$SXVT"f)A
l1jjFP2O$TT"3\2/<G$/e+gooam_T([g,$hqs5V72'<l,bJ.LA]ABBs>qF:$3Y'b@d[]A&@:&+
]AZ4>3^/Gh#l[.*RORa?LS[G1YSPogNa08F>bc`dHlJ;fK:+CICr4DM+eGpLc8A?0/<X>@X!2
q3qU=J%95",M5&MN,h'jl)A-4.BY$6UU!3B\XZ(*V&iNG-lHrU#DhhWfA>$XOMbJ@hGCi\+C
]Ab.9p-\agW"nX)2OXsn[j/\l1@>nY_I%t?I6\7cAWDR@Y2<]A!7/oTEWcW3ll-J721_6A+SHR
N*3gG]AHpRjLc$cA8$`'TkdouOsl\1Om441st%2U`qSjH*Q)g)YeiQ6FLigR,]A`8\'c"\HS''
#s/W/Omk20(Tb%kn]AF;j4>JlJ/$XBD6P$,J%B?m9p=3/[G`9O5_T;$1U2YW>]A:'N\4Om<)jD
9NlAe'k$(9JC5/CJ-%(4%-FABBV)lleW9;c1HX29)1Y]A80e<^Abo'L6]A>s00HZGu:h;lM,/.
OqQ3=\K()'9ChkQ!'36WX,k)Ps$A$n7rmY,%paOsGdo8`]Arg5++`5)1g[+q\FLC>!A_P[=->
3C+G4Abb$B*pR0,%SN\51B+VIQ7nq,(L`_*jp1SJFq+gcd'*&(1!d2^2$/4b*A^nfQ8#l"BO
kgV/*6Mbmc;5G)2#pd4n(=nQk1ceUg0s4mQopWqY[GQ6IsF8+#:s1S:[Tc`77hN,D1_jQ:o]A
.5?0^W-3.3%t<a)ueC6T=TIFBsc4'cbe#Fcbe#Fcbe#Fcbe#Fcbe#Fcbe#Fcbe#Fcbe#Fcbe
#Fcbe#Fs'H'O+HQ5RaS,i4k_B[TM19s6c!K*!f2J<aaU^R8R882[5(=@IQborU:2:jEa1t"-
?;]AS-_4pYd?gmq(~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM04"/>
<Widget widgetName="PARA01"/>
<Widget widgetName="report0"/>
<Widget widgetName="D22"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab20"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="27a8ef6d-ba93-4b1b-ae41-ae099f5363e0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[40943,723900,723900,224392,492980,67317,723900,114300,718056,206733,718056,571500,571500,224392,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,38100,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="13" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=‘’]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="5" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=FORMAT($$$,"#,##0") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="8" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="较同期增长"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'-',''))+""+if(ISNULL($$$)='true','',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="10" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" cs="2" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='float: left;margin-right: 2px;'><span style='background-color: #ffeecd;border-radius: 5px;font-size: 4px;'><font style='color: #FEB524;font-size: 4px;'>&nbsp;个人&nbsp;</font></span><span><font style='color: #FEB524;font-size: 4px;'>98%</font></span></div><div style='float: right;margin-left: 2px;'><span style='background-color: #faded1;border-radius: 5px;font-size: 4px;'><font style='color: #E55C17;font-size: 4px;'>&nbsp;机构&nbsp;</font></span><span><font style='color: #E55C17;font-size: 4px;'>2%</font></span></div>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent"/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="12" cs="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:10px;float:left;'>      <div style='background-image: linear-gradient(to right, #FDAB07, #FFCF70);  width:60%;  height:100%;float:left;border-top-left-radius: 20px;border-bottom-left-radius: 20px;clip-path: polygon(0 0, 100% 0, calc(100% - 4px) 100%, 0 100%);'></div>            <div style='height:100%;width:10%;margin:0 -4px 0 -2.5px;float:left;background: linear-gradient(-69deg, transparent 39.5%, #8D96A8 71.5%, #8D96A8 -4.5%, transparent 49.5%);'></div>   <div style='width:30%;height:100%;float:left;background-image: linear-gradient(to right, #E76929, #FF8445);border-top-right-radius: 20px;border-bottom-right-radius: 20px;clip-path: polygon(0 100%, 100% 100%, 100% 0, 4px 0);'></div>   </div>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="13" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="14" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="15" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="6477000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="3048000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="32"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[XLrg@PjeK5XLH?8U/[FHV4(pf_?co7,be31,pDSVZSPP5.U+bR%$P5FQ>Pt8<#XM%O0LMMZ4
,DuQn^^Echio*TBH)6hr!bo7t45#qnB61]A$BV(O5i?`o6u']A7q3pE+=@O)b>-=*(N6O^C]Aa_
<fXE[9-bm4j$O]AUMYNEgq2gpe:"$=YYC]A[Ud&;`q%0t8=;mIR/;#lZ%gO$=Vq*3=%%LV<ZJ?
5`F6TD%QH4\C'E6r:'Hnk**U$iMnLj/QkuBAhB;b/NT59l%L`8MC!2Gl&0M.rl+Yqq%n*2/1
*WGhID,:YSc0P/eApe'1Lk2%-O%o1h159ojIUWpbWmAa+)@q/W8?GF=0HeLC6Nll`s`UK>&Z
3Wj`H![`j&S3@B"@!e't/`%.%_(7,ZJe@0,4H6&)TEc+Rhs+c5#?Y;to[1/<Ycu=nM6ka#kp
mA/Zhh]ApL,eh`iK!A%%RM]AtG?/m;1ic?/4o?efa;]AsSB?Du&K)HM;_F9>N\PNE9-H^HAj.^Q
hOOaO"Ej+\tYBk,?+VaakT18hX")>Am[2CJ/!W4C"nL)]A4,j(^ekNB<'Y!gTI8<(+0QE_:)3
.U%AAH%F5(,p$S_X`qnS-q_d'Da>rYF]A_i[iMb(JFX5oIHAe`N6C?cMVFa9XET*HIR`5-`d\
B>3FSX=EpNH-6(s*U)E1,f=Ep9h=P[(/T"=?h#SU`Y\Sp+6MQk#AqPr/`3bE$84%/#-F(UHg
3@h-``:S4);CLipf]AU:+<.'.BRh:P!9aaF<;.2(U"hASaf.MP=S&+`tM%D]A"lEU]ADHdEQD@B
Q&FE5WK:ek`eu-RsU0E9^`%p/NfeJ6F3Kb;jEYDqb$1Eet,UCLP03]Ar[3udIhC@?[]AcIRF_d
ua9BWM1qsDSf=)5?[I_7>!<qY8DSuTpX/f_4==>C$).l*JX505C0RKI0?<gGj]A#p@%h)j+g$
T>Q$Bab#8h0Naafj#S<($Rq]A:@"+seW;(<::Q/j1oe&EZ"DT\/<o=?5Lg[a8*9s8)R(h%kE)
h4rX"8QEMQ+*C8gKU!KTSD=aT,qHjdH.DiKrDjlQEN9g`a%e-1+?cajB<?Cs>V(oWfc@+E=j
3-pWiDAN\=%)JAa=='s"D6h=Yqo"/+Q'6nD?6rmFkN?fs3aV!`G$p"Z=qeeqqVp$!r;$NYAb
Qu&I;]A2SO8aI`S,5^&rnHPHKV&V=?A[G\?1fEl2&\g#d&9pr=l4MaXJA;0C6Qa0Ha^bC\L.)
!P_t=i`5)Pdrmqf,r;0URFCcE,;k3&Ic48S)(.NN3.$7><EMAIL>,*GJpmNcfs($87
?=]AO"fJOg-L:pHM!BguYG7e9#itP;k/Em?gZ,-lhd;.#]A27Yb5<(QdQ;\"df+3<%,-44+f?A
Y(hu]AMI^l-krCbG%_pg)-SXEip1:SV"]A9]AP;fP)/J)X+Fg@a#[GFXs$'/J9ah(Z3njp1<6C<
MLM+GG-!!0SP[ZU9H2&hR;7@=+qGiLB2Up1@6&EL);rP"07N]ANg\o7<rI'[F3IUOTp(`L"[m
!2<lXKHO4[ZsD8ar3Jrr&EOAoL,CE;j'7Tf=gFTZc&iY/[(T4kD]AX_eE]A7NlTV+M"8!Q5dKZ
,!H6fC06^S<(->[eMUH1A\=T]Aq=71c>Ad1X_Xmui)UiT8RkJOnSd>(f@qe!4#nr<r"XH)@Wk
pa`.-:*^kS=f]AkBoAl1HZ[h&I=<LMIjY2Jh=g47)p"DLpCoiq`,Un;C+on]A>.t-OO@d-8"Gc
AX$lT:m)n$(SiI\D^7PWb5grt4P&p[IW>3)%7f'h1/&?braJ(6^#%<JD5>g"JBO$JY/T:\:^
LIE:]AE39g4*jcba6-#p1W_?4sgJsfQP<^\]A3MZYHDVhhD,_aH`,XZ)K3CO1<]AE6@qHPmDcT3
6-fDJr72RsHtgg+R$K]AOXN>p)=W7Q;\EBWlEVN%64pK"SRL`h/"Pu.CO$Xi=+H*,FO))-n/N
g2H_]AYBk_VWM8"k$\mXa_W>e]A8rY$!HBGE:W4?KoF[d+YEjVrlVdq?3$40Q_@`CUMpjGM$,!
(V%J[=Rfqhm%D5<uZIoCa=lRIA?@gn"\O9T:=sa2&j9A?)r_Q'%bJ"3FiOI^Tp$E_`!sh*UP
uIB1p%_*\CANh`"S>[JjX2%sd<DHm5&=Tk_VsC%Km0.qH=%*VrTUILS17ohp7Kdpjd*`AW]A!
5q+ZTSUZ%Er+eJ/-USr&]A^7DnK9*rPHI&<l\/_IEi&BjZ:uR7Wgat+&Vt.ZO;f^4mb"ekr<N
\7C98+t&OsXrUkka7<aXO`JJm6.oKpf0cMB^O'/WUG"4CGi@di)D4mj\(JG'h@4)ec9Fca`.
:GrfBmk*fb(/2"E&1-P(Uh`lJ-GR\r.006,U/[G_+@(H#BNsX$0m61C%qZkfE1$]Ami2<RpBN
ST_NRln[[^!cD&R:rN0MhZT?K*Mp=69&g<G*:*7qWmHKF.G`8`:l?s0$gL\MUciQ>35$iDFo
pW8XFBY\5hm+ra[TaB99TQ^HF`j18VBTqk\<UF`I+g.7]A8B#WXb;_J$[,R\m!`Z;aamT\(\8
n^S`I.reNGab4Xm`#ak<nL%D;QIkoMYB"_^WsPI)es+A&^p5`8&CHOlPKE9WNe\eQNg4Y;$>
D?5dV^WFEGg#?d<Tr;<Fn>EFo?sKb&sse&U<J]A%D&^iqtV]A<Tko1O(@;iX<W]A_dB!rtoCI'V
-KXS&]AYrKF`5-+(crQYb;EW.DL(S4l1khgBkge:1!Vmn%CX`lnqBX`]ACr]A3SaDo3aHSBpJ*K
D2XBIBuAVl;(e*0rd0.:_b9J03a]ARX)u`:OOK&dn25GAeAG@8*4'Q.H3GNGIIu\q+,'918<q
XA*eM8\ig2mU(0s?JR&qfF:1lMOm9MF5h#>Y=bQok3qoLQOBF`hb!nK[jQclo[TB#[&S@IgL
k\B2AT(mpt=bj63&<?3t38d[l(TJ"pcPDCu3u5jQe<)7N$bYS]Ahg%INT]A2.SrCV;T[cf_Cm@
&RdPiIpcSc$J.NUpu=JmE>c*f8Xs;4+RuSoLj]AU;W`5;dp%@FY\N)[!oJA$R+Q;coZ@ToA##
;Dp6_?@7d)c%an8Li[(tUS]Aq6j=0WW/4[.\^7F;Rs+QU[<C.')'HQ8:$-eKQ.\rU74da=PA&
iWZjce[YV,?U]AujiD=oVg,$Rnd*ca<E.+R-k=.q2]A^fm$:urVjsrT0-.Scq->PV%6Rfm61S*
It5o1%ZQ!0rJjZ5e(6\N5VihAuX$b8J().mI7MHh;:$hmPRQ('Ws8b_.4W,)^4SkT)jKItX>
ds,@EW]AeMiMpnP*^KVM[<#Pp;#LW<Hr"`4qWsiOZ5ZdFC9m/I^YH\Tf54J-%I60lU,S09HbY
jKsh1BRYCgNI8=%Yh`H)5a@2Gbd&090+8Gtnb%'5-_l!=_%,f+F>tkL_A$ZQNK.MaI343D);
-324D6W74?96pE>1:[aDabtX1@_a6OU*h_$_40[!(H^7,X/N3K[q*K@@a#`^(QNP*gZA)`fq
PnP_\-f2^po2]Aa=lc*8,:^cVO1E;l^*g(]A9qS1COnB6655XXj@oh)bJPZEqP&^c+j?6H1l]AZ
h-=go";\Vt4o/8-.sG3+.bJF$QQ6-03L_/@g+?Cd9cfpG]Ami,T[*>.I!:E[\g0e\OhHT:EA6
A.=bS``JR/IiU`i3T>Rs)$gR=fLqDroJbI1BQ?OS7JE.*GAP15kJUU&pS$#t'@i&W?4;$!?%
#)$k\%n'o[S6G:`MRAgc1>lGSJ:1P(%a2oZj2Oq4_j7WJ8SHe<V35#H=oXC-j<()C\<45(XI
m(=$//J[`*.s35;;B<<SU6un5c2K[>r^^C\?.CFi:4-^T5+fia3?3T3ID7Dcff#n%9dLDZ.<
-R8&kQ0l."`"\ZOhaCF?_?Z:i&-sMa&Rh.n#*AQa6]Acl`!9]A7[r/&\N'W_Cdm`S*3V<>\Xt:
qpn9HQuh6HF^ohu4*.q3\dM0uYt>LJ4,#(=OW%?DIO9=5SH''f&J@U00fQ<N`HV^=ds47\j:
:^7]ArQ+qmm<sS\:ks9N5n=?r[;?l3<52Ht4YGpAhE@htcJ(k&2HekUK22$C7cs1`j'99!e8)
92.]A;='+LVM'20[iP"[MY'_l1_c;qqLLIV^pbZNdrcH;>dQ,ROdLS2B.$7djn^<'6Mg6O\AP
5D]ASeH(3CO8D0AB$L>J-qD[XL*7VO<j[Vc"G$Y6m)FAj'^UHQ\@*G;k0IZg:]AlG#DV`"%LA?
G3A+T%/R"7it#dj9Sm9G3'q"of%R2@:>d.SL;ctNnR_*Q&.+N.k8)Yq:5J/2,0pPN)1qWL!R
7)B%jYgX%?/\1Wq$1;o:M1JWfpL=7OQ9<%^RK@LV5Ho9QDa$nJ#Zo`Om[At"TIU/fg"&CA&!
A9tE7MX8GRs,/B]A;I@OFm"HbcfAu3O\bm2P>!QDg]A/G7Fn&dK$+BS@t1Qbf7U;fo_'uQnon@
91epX?P1h%(C@Zl'6??(;`El\&[b6^r3EnUjN.egH0A.)0'b0^MCq[h.3b<q`HWok8%kFQ+a
m]A(LKn0X&UGTLFKnd1o6WnA9n@6mk0S>2-^'g(Ne"C330AeSJqW#qBtI/+E=p*"ot"/lB:!Q
E0X'1."!2F^k+jW$[JcW6_GUYUcm@IE8:)5_(u6():/#^V9odhq+2sMGmefeU=,p?G:eK0!:
[YSs"SH8A$HD-UC4%c5C-jmM(!C?0i<\H+A8g@\kPu>*4\0g??]ALAH+s+bEW7+\rjVqGN]AP%
am(cdq6Cq^TK)F3">5?[oK$s)0h*-m3!_1DY$=/WqI=MGnsNOL;%+-2hVj>B5)&&QQhri30[
ZI]A$`@eIoH[Icc4B6WZE4iPo@s0Q1@YTle5\TME2[nY:l*3,LM2=C'luN%8`Rc&_4dhFBJM%
</Ien)GAXt12_`nJp840M#SU@A[SCob_Ua-lgL5)=co0ML+o8fHe[sAL&3Lk'o<J5M1bfIQ0
mrMJ)&S]An;%]Aa3;IEn*C9C9+S`V)^s/]A@*(nZOU?h&eUf>FQ7VDu)["0F1b<j$*NiYQ-)E9A
J3FhEFG$Cdtfkcdon$+[kC.37Od'W%.R2DZ$g2Y.rKJX`LoY)hl5E5r9"1nf6EEK8(5lJ/b:
4\-k^K/`V*HbQE/e'DY3$EK`W0,_8WRB_r`rM'^UUh?C!N0NX$d['O8IfWe`#OCt'_..l!rT
+E]AiI'(XG8SPM"(@+0;lh7H;6Ir]A+YEmCSLZhlMB]AQ+EU&C*hQj1X^+7dV-_CXP5NkgPLnLt
ka15sOgjTru\YI6_o>'K6,mn(j?[`W(1FS=&.UGk<A1uB"7e+r7$V"#u+NHWLlWmt)]A[uB^9
MqR4`\bDO[a-hS]A<#++3F7!=gj,Oo-`$+hl<Og>mWOfkOn4EJH3giP![r4WRE>^,">rPX"tt
!'Ri;L#oA1,!lsuWS&j%M\5LYUGjcLo)gtSt\%#]AJ3UE/IPSRWAig"4#!PVq,h..>R<(qY!X
bIt$@58U#$[kGeYZRb"a;d?KI0g`337(G3B:/uXPe5tZTdU8mG<Edb++,9L9g-fNd1khL0[.
[tlbi/2jfk`TNh!n9[b3A%l8DD_l+%8aJ)kgGMb5('pmNNF$U!NKPo_M[W1m&f*o&ri^A4cP
HmTc0/#i9dSl$$TAS^0(jBVH^RSoLVh3B=VneY]A&3*F7tkc&XoJ0pELhpf]AJ\f2ek`;]As838
R1']A7PpN3bQF3Ci%[PsA.i3Khid]Ac-6K&enBs;EPeH'Xh1VO4.$CFHVH8&;fkQ"Dent1L"*n
L/#&9XPO.')jIA+,n9X/_"a=2:6#sCU?<k*I(NA`%IL\3!pp^`8SJP%XjGV_UslnQ(NUa!bI
F8^Ltkr9_cN8()5;RTk=Htm\0D7U"^QBBj@Y*/@W)]A%/*P"a7%D(2$8n+00%kOi)\]A`X%n8#
Op/B6dshGg5la4L:dfHf=n/D%u`*p/Oc?mjrTENGH'%kM:H[km$/fj-%M-iIE#X.b?C:g8gU
BWY2OMfB/DTWpp'<Q`aY2a1i\'/b-#IE(:\s;//-BE?nt_.n"e!['Gq5=_a558^'j-2AF21e
mB5-hA=l[8H'dhrtXMd*+I[T(j4j%%X7M?>JBJ#?f.m-_m\f20S2`5,G:)43kL;f*Bb0PVf<
>JmR?EYWP`Ms[)W?pFcM]AE)s"-X=WBXKMJKBfR6:'3*qDb(B&N9u7UKci\@h4L:NLk.LWNXO
q-tiP+Uf=5+HM1H#?]At)DFTsPp-;6[)\.1`iYu!lW`NY3ra@g"*cW'@I^$c<+Jk"hK`).iV5
ic#Y%9Z$0V?kfRZm4Aqh8lR3l!dN5B_1a84WpgT?CU6s3g]A#bb>A;<*uIeRXhFdhQBL3i7Y%
)+Tue&5BAQ$Q$G.Y?9,afZuf&^.@P_GK[H6*,tP8W."e[7$KeSl3HZRlUKW($:Y"'uf&$Z!f
"Q\7m3tUN3b,XkVu6Od\fh>W"g[]AY(^8St]A9P71_X0fPqD<j32"_.*Q#,jF>mgmNX<5`N7"u
RCH3_n:JEj@Fgdg]ApWKJ>+HnXpBX7MAae#58l&(7BTLYn5dS+s'&/gX:@qs)L9)#J#KnN["*
oVIQMOm#p%eG/6ED-tuh\fK4!WCQndd^lO&J8EShK;h+^iY\.Tq_NHp`:C)UN'X8-cC=]A`I<
'n8ZdrnkLW?Cd.O0"[%?1ub/g[^cA\/70O<RM<X[V)nKC$0"70X77epi^=EZK<Y(h%U+[Nn&
,^7/PgbVZ5QOnSo=.%=;Ylg]AJ<,H;`&!(M=8PJ9f6Y_,V;J%qOU7m>YWSY&CWXkr=cb=<4W(
gbpG4IRsTGj5jg.J!<EMAIL>[\:8u.>4c?-.Hf-CF.OO%n_nNC5pinGigHSd,%4#
<15CQSmON/B9JhW:QD*qb*.>jaH)W[B%rQ5FA4^"9hB34pjPiAHeVinNga++9AE2b^pMYnbh
bse^V.UcF+B/*P:cg2!ZYE^ls8bi)E\$'F^r]AQ1n3paA0H.?peEW.uVU)Dm/N3`1'RpOIiau
#2-[!W)ZBtsQHDki2J>.L7<#r48+X(pFqU\,@r)_uf9A&Jga"iW(,[OA`TkYrB\84.Om7m2T
p6KFHYhn^\JlBbBbs,MUMZ*=YC&!7H)K_?/,`qBH%[;PDRH7tL\@%[8h<fN[mG#U7#)h*bqG
3F5[&+:0,.`D!n,JeF)%Y;_CXbuHB,tZF8'ZCKf^l"_6CD89:^qeRlHAe/\SFmmsGcj1j3Wn
h%lSGhN-GHscEplY%DK:(o+fPO2adWc4N(C&?g4ks=dD(.._Q%O?R>]A6>m^5ct]A;qp(e6b:o
ZTN_-b]A(UgRr^N#>+SkZSk]A9Q>u:%NC3pkc]AZ]A4_G8jOJIF*,9b@&#ab\[LVd`K7I)rIY5!S
^S/=-;&b@F-iL;P0jf(($u(;'R3/i)UV!NE]AtVQ:A<9]AXA\l_B5o#\sgc'p\Pe$GeS!m6eG]A
TeZ.6'"R*nf-q^fsXt$3[Anbt9?JJ)<0SgKBSEEP/s/=Iu&(F?6nm?$WjH+lY+Xebj!jad`p
hH\(@o;0VGBMg&G2fYO="AC4#=8%U^]AHt0h.mUm:k'g^9LI.C.$ZDO3G,1O>deCf>Fu%+&!8
\8^6L]Aum5PtLG]AKS-[268B/V!,TC62pbGKudtT92O]AKeu^(Kt@:Dg]AZpHA]A`+-0QX!'H;9#k
_cgZMr)NnS=o^A^1(M1_8XQ+kSFP-W.u0YOBr`9/BAl#3$,<&ub-D>lgj\F?FZnPM[b4/.1[
Fb?8t4![.@lSq6PBZ2Ke,`Ac$7mJ[^INWOWWCY4(joL]A*UK'1p8q#m0E@PGAEjeUbpeI.1G"
tn8EW?DgQQRU\<Y)mX9"Jqh//K-)NepGE&H6l#q1sMFK,BUMnNA7N0ibSbc0$`<6f3*iaDQ;
1`rD]ArF9*X6*1fdS6pAd2s!^OkXiqT(uh+<KuSR1q_-1qosig+&*9Uhk\e)=#&T3_EG<rJ5+
ZdisA*sB$$B"k*YK!_G$lG3:?K^(P!-4(Aj+sUW8$2g2J$;T2&O;MU9^ChJ"`0H%YhNg)FK>
@6DYY&@>rDP0^:[1e6Dt5a;qAc2SIFa2HnijOh.j#q>dSWUUd/V(Q)F-uI%*-07(0T?9qebq
+GE1M`R<?:;[XJYG2nRC\^SM=K"Z;VrJ&ZM[0*;'V6nL^4ppE_eF%`H4/0&^]AQe3r$^f%i.2
0/J1Br"s!G"MggBOH7P#7E"[rK3cnu.i`u8A$dF]A3"^F'IlnA)Mf4_@>Wt;@hhd*jrHe4dWC
1Z`Qs1(EN8*t$R.LmUr'CR3dbCq,a^DA1jmKA*N\@@4!SLDtu'lF0G(]A1!P<OGUI=5U>MIa"
=M[BKh<]AI$[O$qa)T2#-k721NI93,%586,/lu^8bdL+Bkt;5lCQ?btP`N#1C0sF0n$N^_gP0
pk#njC@)A!ET!'nb!r*eWk8iTLs=htnY:)q)'$4[Q[X.D@tc^KM@u8VJC"mX-aCXf[OY^7Z:
IXLOe00l0=i<CPg:O]ADQpecmt.6737*Qo+fB[&!bi*F8:3[?SXCBIV!5gt``/!1s4TKQUJ6<
XG?a&!(pmoG:.)1Q7Zges6WEi08ZHc&,9kg;@Nq?U]A;2C@k+VPI(il'tB$lB?5-pWAWY#\cn
U:*5F3FWj/8/1HP$alS2,H?,Lnp#*p#KVIN4TaU7bZ/c]ArSbglj5o6m@nNS3@']AM'JPSR:FO
0K&78CYA?['(P9h]A/X*bN?X4b!QB=l>YW02!JdR?=V_T]A2%E6;?))Mi\jmjB4cGQV;5;_c89
4/K8^GE6jKd_5*V9\`p='9<+[QT?1:(`XM`?"tkFeaBh'5MCcFh;;r?1`ba;nqhh<(tQas=<
Y!ANR[r<?t_iseuRO-#^[mN>/[75Ucp+:bD7ocL"9@YqEbCWc%=[Tc=MuGN.$%uk)XACJ'J"
r3:Kb-YRXM/!W*5JecrC)CI#S#'K!pr0A(dCrp:P$s"=qkGPc!R%"77E-PPg)I#;`[C\i`1`
Gss*FNCK8=em$8W8)Ho>H*Go?W0mU;P-S-kcHQ`&1(!HkAA]A/&"ec=<Iqn(.<k&/b^\$l_CS
V$@MQ^W46)4pRQmqUX=igG^m1:lMXAS_<q`)^1^4]A,"+7,Gm`Uo#^*oi<3lkmM&kI6e=<r_8
g#<\`#>ArI-qVN"G*GD/apCs*hOlfBGrbagRAiP"K700cpK49Xqp`ZIBJe8195D8TjUSn%lB
9>cLqEeZ9nN_!'uSjE$auL8'j5umR4=6tK"CnLYi=+]AAE?ltYC3Cn"gF`]AA7sK)M6TTe"BRt
Y%63)pTsD.&X*Ahi-c)!r%p()D>7MG)1R2e=F8A'Xl1;r.ZD(AY()7:p!jRt_Xh/DaRfR?NF
"4Y@c$kfOr3NL%.IJ%K0j9idEajDId06D:(!S:<L+b[\U6RsOH9n-?[loH]A;f2(^mWI"On6P
ir#Yg<iZKesnV\Qi@#b49ijhZh3V$YT[I^JZa%sP,:FlHN6Q%)1.:%R&.c'@0OdrecE9j%aR
+Rrch03MGW+CLk7=gbh-/+fa`E2QFR,&R7ZLR6G]AM/'+4OT@=b]AGl/OnJbjGM^.T?J`NHfB"
0jW:bmM0'T\7aUH(gNX&-"Q1I2/AR+b&B=dDRK=^_um9,G1pWo<"+]Ao*k[rn]A!H5qo$>/%$F
6+SU"K',>EQ?55:O2+=k?SCqQ4+B4,uV8g\Gqn.+moZ@-??RRf*qd31UqW,qmT^6N(,o+<n`
$)!gi<E5a?5pC*:mQT.]A$j^#4CITPJ#YU`bGNsb4REON[:%SDm08<!KM\A&[5&5QJ&Ci9jh0
\rqPf,br5QFN1B@8pIT)$K4X;dgm3fIf$G*%:&US6hl%hUrJpb6V)Pc34c<T0;I9k1j\$u3>
=n1Zn"t0>@)+@KCs%HA&p?@6FeK+uq/9BWgef^_<h0%',$icp0B?W:4cH0Q8Ad:IQ^@sk<:;
uPIr\r1Iq[tXEf#"&<<_a7R5$V<)(9\PQ//+\G/Lci''O;046LV%YY,j,T<UPkUh=Xf=o7_j
?5"Q,F/QA8fs*gr2?#UZl^tf.Ak*q@qYmA_,KLIq0Ap4N'+Vki,Vd9J&,Q0l%ZD[*?Omhc:r
;:WpkGl]A@]A09Nmd#M")3j!A4iplBP.c>kEq]AJ-,#PhAl+JG"k'/u;N2Skj\KJO/le=30hF&[
nq9#*0XAg^CaK-`cFG]A[[1,O#9,SmAfnS1"oRo[j[GRfb[_1om*m/u/NIqW#R43OY(4n?mEW
majV3VV7C($,TU9aT18<N[T6KIha\jXc/RG:Dq5*e"`!iZ%\i;V:"4uNpB9t?Nen5h/sbuh7
.5s1(!CY[4;<+<r6@pN9bj`QnXf^%YFgs6k\RFk=Z"W&(QXIHToSsr`kphKUh\4j5\1ZMCNF
C=%8'c>Q_`Kk)d4aj<ms?B*[ZM+-aBj>eN(;A^SVbLIcTn0`sL0cBl.n53,=+L?n(lSps#u=
j2IjlO.8hiS_sKL$[L/#[LRV`a1b\eN"3P']AN'A,H8C-oA@UC[o\P>VQJsFO!S1S188o)@:^
B92<.m(F&u^-`%+h<N]A1H72Bk$#S6Qo/ZL>DY#s'Hc.]A\Ic8?&Tg-gR<[ND*6mBk^CLS#]A1%
>YM>*)+P-ukJf]A;+ENV0a%-MsH1nV3-[RAW>mR&?kgQLk;'($=@(BSHG)+AYH:DJ);pEF2>l
1gPX2T)mB!nUIBE<Zf[9hu*"]A5rYH9)VYGS_0b1<[@d?LTON=mfA]A?cV[KH#<g&'u)=qgs^I
)=aA!DSuNfBeWXVm(X6q=/@JlaY[Zt1-0a^7b@&'iF-=cAp>qZAP)J\+!=hGjqYQH/6)gl_4
bH2+\gaMA+RW=n)iu@PplQU^($:e(j=1<bU\7Y>Mb^AgU*VpF;"#+=4eA:-$nt0uXm.oo?MR
+mCrRgIN3$DYYOYttfj10H1*+ul\&<>J<f!:D;"I]A67[UQ^aJ$j9L^`+>==:hs'OHE(('^Y-
kY=AQ6W*RM=>*tZF/$E%Y9(1H$(S!aS:WFX*rmHYbM>\aR$E?ae^)7bJ3F]AEe'RQIX/NqmrS
.Nf0g>VqH#e;c^@@ed.QeRM`[dCUmhtroN_U-17QT]AJ:9!lPH3&_Ce7kDl#64jg2C]AtaZZ-B
I-Nt7+-2i]ApVa3/(liOM6kpk,id&Jp\BUc:Z[M"JGe%jM#',GK9\/[n,n)O0H%Qk*MI1(H>`
o*1fF_+l>XT6eIUbIi`c=9Ii,2%<l"l1p!o`a[\1,i5#<aBZm/lR#V/t<u8P#6<=XE(l(PTN
c!4?qD$A.'2d)f[pN#p\bt<HX]AKk729-U%ASQpqoQ^T.Tt3-e:hU<SW3C41Q:VE[^M5G5ONY
`J%n.m2d(C(O.[0fF(>5_[V.>8gb!gZ+i3]AaKMs;1NmU<lrgNnSL@:6[%Y$G=#P.mD8Np+C"
4UpKk<2J?/15FXW9VN;\K(Ne\45I$95e-B,L!GVL5S\`qn>ka%jt<5Do=eKZXTjI&[++q_f+
:8rOkVoNh6d8UDNMo1=[^F]Aa&Hm`WqSJ1+HB/C*mB@Ca"0A`F0'&-i'6gT;W@rM>[d1EiZpo
jlUo_-&k$5D(?8]AX,R'a0o[Taft0jP>ZRuq[Y;)-Obu6cN_Zu"pRF7"WG6W<sa$/k&X>`L-L
F]A)#4m1IqRoRDR-5P\HlcT>[fuLW>hr4(U%!95gfEaQIbZ?X0X1sL/qKkL!_K@TVP?RaRpDS
pjrPSh(mQER-e/D1/q1b1.:N4$TDF?>pl+((Ju!m_93]A5(`3<ndO\c%2`=[RNj;.PRqHqf/s
*)&-!bn**s[sH+8G.2JMR.Y=V0$SJgCNjKeV.KJY_)PCKE#@hHO+-RG[=@FUS!5)T[1fSLDB
[r&5o(=j#J@;-OWV<s*q*1l8D=@CdWV:$jS<Nbce?eSGN<Rc%Qu?'02RB3s1@]AFfZuCa@:rQ
&n#&gkQ:@Y7LH;7ptmHP.tp&&S[p,q^k56!?aA,]A!AR.Z1u!a/6s("QLq@9$)ZSX5a`la1SV
1bVsRe_!jsT.2Y;DXPH8YK#Xg6'pf6St+L!<qlFbOlk\5#@<F4(c6U+Dg7=5?4[O&2-N`#9;
:-LHh/&"k'kp-hDCE-SW<>Z'Fn:,;M!U>[T2\l+ckn',oZF&rBmm>E]A4Ejghp<G*U]A0t@(Aj
%T>--r[@3Qq:2j3OTlUTUlO<3es85i'1PkO%.Cga#u(k2&"sZEGp%(Ptb\_Mi-R0p\\a1G6b
K_ZIueW[f!6(Fg:6!M2ONJDoQO2T$8bnf7Zk+^WTed-b-P9"Z5bj5dUkD1XCna,j<q*m+G(c
E2S'ESrllLV"CDRREkh/c$=r++9fJa-h5G%L"NQfIu3)_G;K>mr<7GBo,50$Ha'EK3dCo1,]A
EJ6>;coAPdsJaj&a[O%sALF>&`$CPWa/%g%DW5W4,e!@qC2P??Y,ZTN_JQjn*CefB7RVsbcL
]At^Gi^h$So2Z<>';YY9No0V=D6,]ATOG09iq!U7FV2!A.<B@/qF92Gllj:;q2(n0t@0Lo#aLP
+I0o_idjeqC^9MnepJ^qMsDhK6X3p[;mIfS]A[AO,?IRJ4V:*s4^lk>]A8OF`gOGKO22@RbKHO
V]AaYB^(@qJ+O8jgT@r0'!A1U\Ehjb`C4;G15mB:$_d:]A(;?ap==dobYrYs<T'R9'd^)aiYV.
r3:2Z9oZ3>2kgaRIbRR3<kLH`\9D:26d#j4sCS`]ACUrfh.fUBJ)'&)+JeVb-8+qNiPKQBhQa
Y9cEi/Rn/GLGJ!uC$*VT)gGLIXYF]AWMVd[$bVL<Q1G(Xc\7J'9g6';Ub'<&D&QEE)e?KZ0P8
,X@$-F3<F%D=2kk2Z1F+7h]AIFG@@VeO=A<LM!!1dqng[^o8e?J8Sh&M&:"=E#u@2oWWpmG?E
O3%EU0@eqdVkO:*YI?Br"W7"&hhUB3t]AdO@D-$9HR01>D^#H[i2d$\nsq54q#P44pIbqD5sW
:.dJUg_kei_^WuK*;kY<.4>"X0;2gi6S>4l.pWR=0Es4@:><NB<b(+]AChKLi-T&bF7e+12d^
K+_i2b5c4]A@cl+hg[*^70KL7!CPq$_Sh>l!G(em;IE;Ts//^#lEkSpQnj;Y4srI]A1m6;(^^p
m!`nUMOck/I9jC+\[C4AiXW$2(nU$CIrQ`,fZe^W4VB-p&\/@jYM`=I\&`3%@?GK^j6UDefQ
K`U:qq2^T'oYTPu7mMgCF,C%_8r_NX+%]A8Z9XEl1@0DnZq/it1s+.Eb@"IBrC&fO]AY#Xt""#
HO56R5JXro?sA5<iTE.I"-V%l*%Jd<V;lpO'T3kN>Log'Qa@,c'd^R.;5ongC4eXIk?(as-a
bk!q"tb\&.EZ>>$*Z&<o<RD)ngTDFXD#O7cAL2A9A%2T8]AmbsK>hT.ophA>8J76F`6pACYp>
9&\&>e>]A26CY-Ig]Aa_PMWA5_f6c,eRiGl"j\<H8o@\Va"+sdl;ZGtaAI(fP70[Zk&\@a(S#.
?7TVqp^9@nm&Q?=6?'/M4bSYl:g@N*O-=EY,k&P0Xc=ojl0SX(V7/7(XEWSYE?T*:mrKQ6O+
7fu]A!O(#"\R/I)+6$1J^N<82d5Kk:t_o/TL>/bC%e/:%u.h^V?Xd%1J)IQ:uIR$%3(P_FWbg
6r,J``lCBDTN7BL9tgnZnO7,t%VeMQb<C/ErP&HXF&V:o$,6FM"-D!kc"h`e%^C$fHZVF:Jl
UJK&$DB`<p*q]A!d'ktYUaOkYYJ?ONsnSUfEUj[/#%n`N6`g\.oP>hd_9#]AA2C+1"$:@9/*i5
^G5WU^Pc/<E4;&[a05a^egso_fjs_r#kgOpEcSIRZEdtNoV-8A]Ab]A^gERV2#%P+cr!t_KkjC
E+Bd%9gF\=NpOp%#SNdX_bFic]ATdUSd54/Tk/.CW)-25/,'B)Q7[SE!Z-<O!gJBd!S);G-m2
r!C+Za8KH+TQQoFY3s/USbBZ)a6II)1^\t$*%jBfi^G1(T;>le='SKJqR6GCHGu5`TnbOrBP
0hhUUc'g=ArqLJ$p8ED?%Sf7o#LNQ*W?T4Qg/P..%q*\Hb'd.),)ANI*kA%!^o,K91!q56Y]A
,`'K8G8b^s'+%KH?QJf-tPcK!&2_p;aH7V=Z'7tT`ff'f#?;>3+--(;W1saSN0Dq!X<%G>HI
og2V!.tK61t>WtgA8uDTBDKn1rWq"/!5:0GN`tImE0tWC8+D#CNNJucTR?3nW8u0-fn[rMPs
"olD9=@^L!BhqGlE@VW]AgQ.0WoAA007i:/8@/Al4pu2(m/TZ$FjjC`'bQ,IP1Z38s!E,ao)5
H`DuLIcb-:0eM!<Od0dMBoJlZI0U(S*dc#A`4dI%pDC#EFhYsD17#P>#/1kfRJ"#m,IU54!r
:RPR^dNd=khM-YMpOgVd?Y7>PZipg$uulOP6U/;@dnaC,W(FWFKNDFE#\ZRrV?`:;JSq.Dsa
"I"h\OAh#b0>#OHMQ_lfKO^i>/Q!3Ladka@PK^aKHR--L(O`+9@LJXMT*F,KRq@t#D8H?V%&
<L(+k8k0oRE9U(XqM#?*"QoAjD?BXEgVAt05%RlTDc?8>+e=;FCci<l%#SXnTpYl'4pG(Z'C
%5FkbBKO?cG7odC;O.jVHHURXJaHArL3\`g)Q>FcBAjR"4oAnl!8Nm@fMPKkes;b(ET:[)n*
puRIk**A@X/c.86,Z7KMfCh6iUde05Dk:PLq=1&!p2d@8%/]A2-hn.'hie4;;2Cq"u*WfLK:d
O2eGW(*,DVp_7'$-hk\*^t!s67@7RTcL/.QahC12(>#\gFk?7\dfsB1?XND0g'nZu#qT0bBa
KQJd<Tb\N\e`r^&CoDD]AIG@1^;5k2DiQhq=0/]A+!Yo5!2Q%F@k`o9@=3";Q!9rHFe'XH4:4m
Y311jZ$OdV;K^sF>;&''R95p'F(Y_9?5Rk1_Ib1<K4UB1fTB`BHt2iFQ_Zt[ST'SMir1'[u^
DSF@$RJ]A+4[O<K;jdEr=(5L,lk-S6i<#3cn&#/=83*hHg=[g-mY;JWd7(*pGUhci@#.*Eo$R
Jb)F,P(FCMGW)*QZOuJ%52=$=<EMAIL>`_5uKc\EFfs1)2\BdSD-[/0=*jipEkXgQI*IG/
lI-8]AfDSa#?<JJNN6an@u60!'&k8)\iD='K:5rfFf8`.e]Al#>%%3>$M4%X+Tm6m]A7:PXo6Ml
[(eX*-C?GET9c<+Nr#h<g#I@:(":$%h'//uK2I]A8sZT<$!`ZJ"=(]Ahblg;O.8m\^J2D@1%Dq
fGf'3baQa5G[]AEmX==kL1?TO\b&^L<J*cSapAVU)?D@Zmams.C)'IhcS[-loEBOg/m+3u7GE
+fItZ5,_C["pk?!lE'),3LBJG;>TKB#Uap=]A@q;/kVpbYDMpXQeYOnIClJc9[#OXP;,'.3$9
+R>4%kd4Y,YtZ9eoNR-\Q_#sf!(\ISm60Yp/oiLDf;QoYd)2)FKRcm#9IUD,[jdJQO!Z+BG;
A2P1sBFd6ne&PiHWS7WB[PTGQ@`0V3*Qsr?_X$VA-n%R/.Z_%t@ipA3Za-m%S0+Fk9Ke0D_U
#=:*,(B\+]A^NP/6Y>i+VT-t%t'/&),YA\d)hnCT&_4+ABC]AfLdsP_#FaC[Q]A`:BZ4,%iVcLY
0M/%be_9mMZVL=>7O./nahD/kn"#BR#d+dn[$2'$nCKP\<Y'a><2cF(oE5Q91WNJcaD?b,nc
X&Ve;^mO2mjNrZD34G_;CB-8GCF(+W>lSB$NS4),CV*`B]ARjE>>&NX^B_jZ:^%R^]AW7^..Ju
.e^9ZVai3FqP[E#rW^km\hcG`,6(HCRFX^pN3QN%]A-X%g0k%ucFoQD4>U7TS1m[_32NE<i:3
YFbori1TgY`iON/D?KHn6he9+<$MdNJ:nM8.G#j%TKmB`GQVs5Pg5TO@[YTg^>'4R]A0GD]A.L
fX';M;R,\S;>0T9cFqhZ3/1dF2UNV0p0XH0RfuHRj=6ME9F-:,/>LWU(]A!h2B$%b38-i3Mg$
.d;*=TaJNpU5#,ciAX,7]A-\%G:tO#.LT=]A[1k7XE=4:`d>/(a[2u4=!YFfY`C[8Z"#[o4]A7C
L#<+r0Z=Y%I;"[u%lHf_M@`02/`IqEO[FEHSB,gCAW:T]Au+bLEW&UOcYS,Q(fP3$@D2M6!78
ka\2G]A,QVMi-6lpZ6+RhcF\qaOI=g29-a-tWBE3qr7uOh!R\cI%O?ECS)^[6*8pG="&dgl!]A
k#pLC[QWJ">I%i"6sUFqi[<M_N0X>:1nR/rcXaCS99Hrh83uSh7Q>>&bU[rpC;#J!n[b*?C?
<]A.N(9ZhRFbFi-Y_]A%\K9i2hZsH>`S$?upnujaqR6)ci?G$&ShROa`6FV5occ#N*ro4Q[@j-
S*jF(_+'d)p,5tE.?j#S9&.(<f^^5>\Gt5mHU^nc_&JW\FtdLq!3k`$/Tq,[-AY2cD;S?opQ
sZT`+Uo^90^2*H67$B[rQ_6jSG<EnUFQlGATC4IRNg=,hT,:7ZDgj]A@GE$Cp$hqVn"OEN(4W
VZ<T7HrE4dbE#g+0pDpcA8P6\P5)YuJ97/)]Aq0ehrS+1j5PET73gpjaTc&Hi]A4u$V9G%2$*H
Wfjp-MPf,nA93SHp;:fC9ULBW.!L%_-Y>-=QUd*K0sf`lWAU#sp@c#'Z?2"S[3-7HuIo.;<3
ZGG=_p/U)VL[f3=cpel@_\'4O@G/ZO.kF]AO8^JeLE=MTd\m\a6r;':I5bqD#$PPWX*bZ`-]Ag
\Y39Q^!rM<T2u?8L![[<hE9jX/f56!B/F7oF+u@Z8d1E;l;W9pFV2J-Hf&\cF.7k,Y&lV=LI
\+PG*f5rd'6m4&5r]AqO:*hQiah@#,=^jGG!^+r/L&:ODA8^1J:7?n"#I+,8]APc5mj%[E`Ufu
g**7j^8ntjaiHODA<lp7]A@]ADXI)!ah=0Pu$VVp[Ig7ZUTK43!tcp-\,N"DYP#PWOIeao)^S_
_S-.4rQjK#RKJm#p5p0QE\mrG2UPWJDtORX5_8F=%^[JT3V3RHd_MGoM^@&)ZjmOp'e3@)SJ
V0%VTmY4E9^5E>XKn_HfTN!WehI0*&&8B+,8*j5RGb[Z!W!FFf9?^*!=R9r+#S-6psR;KPf8
At`pTR]A$)aM+EKf9YA_!27r1Oq(PGqREcD8t2fMSHN\rG6*+,ZaL?7ZPbhqqpCsM1)nVX+^j
qR#'UrL/W3Rl3H)bs[LA\5FEq"5;FGYeNO_>rAKeSW.^)tgd%9-l@pG)V<EkKq>Bq1O'RkX7
9ZL<SH[ZU3%?MdIbc@1N`'g<Z4(`G5]A'0c(EQ;7n0o\[o"00`pP?d\hReC\">iri.P%ccuYT
fK<'BM5bJ=#!Dim$BdEc(@lfobpi>#Hkq#C6Y(70=HpL(#.,1.10936lre:Ze:$Sk)/Xa`2[
8pWkjEO0;_$P:DS_:+WBCW'pBM9aC72dXC194P^hNDQ&m!!hYZhV(DSRHa]A`jgg;_$p6>MGP
YnjG&taf61K3"79Hp(8Jq!ipTgPIjTIW:;9:=\[c"E?-*#)!L:asC'#Hp9-@@Q=:R7.G0;J5
,jMRL"G&r;AS4.e`e@b[BlH,>]AY,I&X`$lR0rjEA"1[E<RE)nVSYKBe!UBCaM#_n6oQ,j'tX
/AcBLljd,0$FIZDWAsL;]A%.@fI19,"K>`c,S?T]AOm(-oKL%itrZGJM9cPZ#L4u$@%]A.b!K]A(
Klti8q1>6t0$_rF=((d+KX*IjTSGi.XW,NRbBW-A$+QXS-"NdjGY/MTej"6G'"1V*T2H.n"W
bXb[ddeLUgUGE;*g=628*,+)C$/h.O+AM8B)Q9^t`7q5O>.CL,V)lELc5Qj9/E6E>dICPAk0
a/+5h@r_cDgbFi#jmdJA]At-0i(RP]AP@31"",/DtP51CV-8u\hPCDT1aRf0s<q82c`adcA`lH
pU7\^+PVQSjRJ0[e/?:o(@pdQ8%kZW4L^Yto'FYE(!#M2:@+?%cTg=<1LQrUh[oW'f]A8\BeX
A"mGDlWnS(8WQECZ]A]A,K_gZa\B*:an(Kq-l2\_=;!IanASHa/PYCj9qQO&qh-AMG&k]A9Z(J6
2a*bG8oMM`f3%Q#^Z_2RDYc/7s=T>JEr@)0rGSM.$OD&fZ9koOQnU]AY2d,N'UkXJgL1:NDE&
l.RSNr4Z2Nk,eSQQ@q@K7W%V'rjLuY]ABX^3/=IF3Wr8j;J;_8R2L</.f8H`hg6O,`[j'JfQ7
V-@X%$`X:\\djfIH+>.7E)0!6^-?&]A[D.(9O1A^Ja[5j7\ANBAqnY?I^a:f),2T-EW-/hc>,
2T3"9Aec>b%uqmF5P7!Go$+:o2mPPK)'F:__<>YAh'E)54aFi;E,,?VoebI37URUa\tM&*j?
V?fFR=l9j82t"5o(Af?Tg9ROloA'807"ob@pI#6je,ArlCL/]A9_=KnMTY);t8F85%L$(o#J/
d.[h99lQX#!Ceh4*)4*fG:_qr"%J=F`S`V0?21#.[]AQlD9+DkL,Q;en8nGm&cKp<@-+YCNsG
fI.NV.RN6Z5?qbsI!9atGRSdREQ\so??UB_)'=.:PWMM71n;+>mb4kGF4#C.DC=V2;-0#k0]A
ZGn:T!-2GIPRJ?.;th.%("RJTHM3H3-=>-`VkDk&f4jRUt"85!u;_8ih1B/MR&j7\BO<rMj4
VE!I!tIfI)jc?X#g-G)nGq58s6OYtiV7hEjrHnD]ARrYU>[/"Ab.c5e6j8$<U_Yr%r+fMS&L_
AuEVd+G0*-pt"*,8CB!Ybk%I)e`gPs\+C's3[t0d8!rJuCZ4FOO!Cgg^q44`.TLCFSNA?S<!
H<T%au-\[nBEj*T0&u$qFWMh40YaqD(!\i^]Ata-Nd3r]A,gjTl!&8dQ\LkE1^]A&\S@>;1FNV/
#opQhS*U%i=RLt"Qg%+ZDmkfK@h^Mj=>h+O?g"FDoMs(>*p<+E@`/$)m9Ad;U0BZ1Ddqg$Wd
#PZa4F?pa&kIFhQ9`>NIE!^6CjP>p:ZCG5_6K&3V9hCCOG(h-L*j.<J(#Jdaquh"0joF=nX*
,.FDhcR<Yc1d%_sk`=]Ann\;Bbp1(1c1e2FabZI\_1r6\=%h-1er940W2Z-WkIkpNV]AqOY@Q@
DBp$0IlCD?oXj),a]A):$J>#,cdl%U9]A#:r1]AUbja+Npl1G8mi9+krT7E4FUAhhomEr<86B6-
Or!nGEsfn]AXW7GF17g@!(jH#pe:a`C?N\>bmYCkL8Z^#K*/:.Kde\bi6Du9.<8-.5OJ]A=)9b
qMobi8G'>\'*[-2Tn@IX/UT0lmf6$gVcl5L31B[`si1$SToQA`A0<U#b0\Y8-JMA!$d[pJHp
pQQXGf/dqeJ.i/[q7E_o[PJFWk'dP+Apqdm-M<Q,f)O<?><_[pEj_ne(oElbrFa&W"'3`cKc
MVEks6H:M5bd++`l8eD(UlZ[f1=g>K$gh4Od84O_J(4>/;\kd6pTP"Y'MieE\/,bZFNDEKd3
F72i+IFcV\K(-hO8K0r[oD4+_FC,sIig?:>$hEOr>&c>N>u_6G(^5Q6<1ao*?JNREfD;%Wo2
1dHr[=:C;'#AaCro^GmD$c(b/,E'oNe%_'3C5e#-r:B'i7"R:t`$5a,/*8j?9hq=d$<LQqoD
oS%6$A`u^WGaNH!D;8A.E(ZhUQ;kqrV:XWT@FU0Ju:aX\c#KTtZ[`C]AOM]A[Pd!"88PXpH#Na
J?"NQiFqR$:q>\`2]ALsTXBcYf"K-%eq2$[+SL*YHZXWO=qiZ`OdGs'h6e!Dma%X(!?]A<8c9b
7eA(C]AGU(^=g*$F\F&PH$^Xi#$d`l`%Joc/Ca^D1rQFX+U:"s_Fjps&29[dr#f=T^O*m-r&7
&S?a[ok:e/!&NOo:<4V$]AR5\`#rF3Y+-2f!`Ajc#Zm7KJG*0W9+?*:25NO4$c:*$QRuYp$m<
TmfHm!l$5V;k*+S4>NY4h?Aq2\t)<@X2/#k_OR)_R4D(2l?::`??;e(=+N]A]A&8C*brQi@\AK
\hGUtZFJ5YngF6bZZXkgBIfsFE^7Dg1'e?QKd$^J67f2anV<l8L$X4&EL+uo`NOTNVEuj`IM
b%($Gn&h:3,QE9Jth8Q0b+^&H'4ehBCbh?SOuBlO@sdlG=n&:*B!3]A3Z]AofjSAaI9'K(-r#p
i8aI.8J]AQF;'0iTBJdY1&&Fgd&id^k\YI#i/s^T)1\=C9kgjF5N90be*RM[/XUB3_j&"OEkS
3AY%G#Jk^%+C2j&o8/%-R[*s$EZ`LkM3;mCE/C!$]A!:I-Hm^XDhbb+biU(^%bhCBZ`mVGqD\
&CJ5AsBRo'(4-+Y7B$a;.;3=D[<fTlahnY=AqC0PPBn]AX3nY4f>"&MNbn!fr+02n)-u;+sjo
-4F?:Y.OcgY"'(Z%"o5o=El/rV>D;csW=[cT%X=jY0.:3TFPeaGYs7/oNC9QPKB5QgW8AI<Z
>R^!g%')A-(-22++m!&@k*9<X:bN8TV_?j]A=4Bc+\:]A"jdH>3^DZ>)'o=kL&X[gsQhrY^()&
.[c80TQ?8t5dI$`L4H5C:thg-Nbjlcr0Qg^FMFY,EtBRXjj-]Ad"t+9!67k]A*e(ot<Mb1)\*u
O`ZCLkijLU>0ObA(8.3]AdS_[>r*2t[j@E^C'J7]A5YP[l)@@aaJ`V\o'8?%YLN5*5[D$Z6kRX
$j<4!3m:"9UV=1DA@G=Tlih*X+9/6rdj#<EMAIL>@OfsIH)9^nm;N3+Q&e,=W6eY3n3Do
=2VkA*/%_>34l!d=S?5)R1A^g-2oNpR&&qP9jm^BS&CO`^B>MsCU5B'"S!#[\iVsgQ@m'V"p
auIIFOTPP/jaX>MnnW0""9Dk061gPmO\-6XWI@n*+RlP7%A0bf*K$hq)M?O,@i5iCo_1f)@*
+k*ik(hN''*f2lLCBn"O*o[EttPCLfdA!hQimHfEW#rW$--QEHE>`E^I(PLd7TY&'O`I+X#1
IPSokZr&ltpGc(!Lo\An`p(HuJt='Vm,NQDHh[7$Dr6p"*N3ej!".[TY4UE8:eeuE.cr[Sr`
JC^;8[3h9CJKU'dQRDg/G$PWH/"ImTT$J%$@!!::gG+a;N(81AeEO[[u7dB+Xd]A"fVD1!$*A
hQh&*VG@,oGSoWmeX*\P%!#XukTR/ppWco8sOC)sH'7(P1<O.TVHD[\:&sErjMoNlqD>#ub[
Z3)f?"^U,ZRq^%Ypmk?nBEcDTq.p`Db2#:n0o!_.97(A\t`bt*9?uHA/!_SeXf5Q0"`J^!2Z
7!U=jBW%fE-d1sP%pGOa-hl*K7<'k`U/JL,k)$a!%eRE7=mN)5t6]AbC_tX5-RV'u0YMJB\$*
-WmG:]A%[mMa?[S?eN73#['8$'2Q7W4_W11PPt%Q[<b'rmgG+,tN*jW\&.5VMl+LEf.n<Wl-k
k'hLIqgMVGLd3GM06=lH0M:!+p"G`*0r.CJ+_pVUPrcIM&R01^B!p.<VfeaFl:_:2gMGF/8<
DBqG*P]AmGo:c#\Yh>[5>q>pgp?kYp4uG*8LnH>btpNV<;FP.slUGR/R.4'-ah3q(4-:eE&0$
P'\j3AhnUV(HMd@V9T]A1PK"qFLasTg47,lGMP&1nrB*Wa9)cp5>nun1C8/3O(OfAkQ%>*'ZJ
g^)_sC^T=";&3t$u8F`4K;o?/6;a0]A:XjP3',V]A7&Ss5-'0-g522@<VXJ@"?QhjBNY/ao>9R
[!V!$_Z1PNn`[<CRHooBW9[CCc'Pf?gWl8&-Y6%RcA`2rIp0DUYpnk(o</Zh]AQWJVC'jbqZ+
\cH^6jCp3@=^1jF2II&*clf\,9qf[e&iK^\&[8e;*pVN]A_]A3\b"/SSeqFnhgKt.H$3B>-1]Am
Poeji;j2f$@J9b_Upp<Z@h;!6DRePD6<4ieNbF=B;3ir]Ar\)H!oDNV=go7"#'MhdG2o-[64D
%o3BO#I9q5n/>KY[#[Wc5.!C%7KC.fg.&eNQd67!rIK=*k:!DBbI+_Wk:p'EqPYMk.Ck7\'>
07(oH_R$[D]AQY8hFqBDWWJ@&qHgL`VU=?5LEtUUSU#hEqKL`&A+JO/s]AYFHM;G.RiY[:ZN;n
WSp?@Ms0HB=(hlMIG!?hHJ?.#oqeP.K"isYSQ@oNOpaG2Kr==)0Q]A0K8Qli*j(/9P5sQL0<"
eG[Ndud*Y*n#EqAp]ATk3o)fHH,caC%kY-WrFeP``XU(HMnY_HVTt%!c"8:5eqWe5S51!Rg-"
DI-%qR(l$)cS*'q8bO5GnHoXY'\b/&YRe[_em8P3:^\FTf"B!S\c6kdHGiYL:$p6Y%IWc[f2
.u'hlMX1r\/VO(_S<P!`78:Qj*lI7^^k1&Ih$qU$uE<2fRbRlFnu#!cd*J9iR8*%*IaH`>Pk
%Diu8*$,,ZMbYr5qbH*;l#"lu#_cWC]AQh_AnajR$"L_*=9[4VTWtQ[d5Rm+i-(0:89`9Sma*
*h`d.?J>I+kUfEbg2p7K^%#TnC2t'O*?.$9`OOU@;ebH`oD<XBY31Rt4r\Sgll1Q4lb-;uN3
%WDqHVD2^?2m3?INIM>"&6k:G[I*+SBf)*$CI6?*6[^Xi(Z$@S3^]Ao>?9f--#PoU.;#%'M!o
R*,&ZS;V9u\F`6aE2@Mbq@.<?^M%Qh5<!u0D@,I\SHDs,273+M=E3a1hMD.p40!^9pp??Jhe
X6YanPNZ-?[F95kQ'ILhZ-")k4hb;2TfI+'_X$A)mrl1$Yti"IXMZdC\>I5o;*e3K#&d3(,`
.N\YXr.=rFA:apJQ,@j0jbH/7C\PP=EC'K`6BM_SLik`MSJBp6KlHoKVk<OL5S*?3#>!TMRg
5<XlMe;%JJro@cOTT7U>nB$b[,Ij]AVj+J96fI4V;rIh1$/8S9/5:u*dLoWVsWr,I*K4=]ACs$
,naG?C'^[QeYtYp#hsMFs(05t;M!klQ#t`LrqL;BjPb,b]As]A`!4io2:<`]A.nGZ,":,PB"@J*
',_Q^Q;Bge@P5lcJ2_QX*F3j<FHdCJgTBGr2j$,9Wh='3ImBo0Zm'YHf)ph=?.@LUtrD[>f#
;7Hj9'SZms.noCoGC#;l'\,l]A6@i_P6S!e*?S+`l1tl`]ACncgAe&PA.Kta'kL\g[oQ57$0#)
_6.'iSs)lRea"+7!7i&a@L$a!m#\K!-IL4Df]A5f&E9ka%V#a&0CM>>.K=$m^;e%blGkW7+j,
-.a#rkB6E[CU,pk4[m\E.k]AZ-N:3KG8n2N,hm5t5#WQ[rn(r"U?nu$F!%sk1WejOAlY0srLr
uullbk.(jXidjJ;-5Qc0gUV=7sMlVT%.-Vt3LTcfBQ(Y\gqk#o">_TU<QjC!K$W(s;(k3D-)
9kTrOaoZH72G8ll_`0g8R]A`W__OEH1-ftpSla<XW+P\mf9%ln$pA4<W!0<nspN9)ERU:mq5T
d9*H23^8IdMe%WHfU57l1[lV2XeEr(/IC-;4&A]A/Jl7&&`3?f`j!R`Qn:[Cj->?-UDm?i]AKi
?U_a(g^mePjF[rR%kO]A\P#b\%ni]A(L+I)CFY2E9J$"c&^R/G%e&hNl`D8OldVK)(j;k^UnBK
R#O.[L-$Cu2E`j_Uc0g-hqZmJ^QY2u[M,S3>L9T%0S,4=Bd.]ARTW1lHOIr$@PT?@VX_XQH<;
Th;$7U;b]AuBu`!rlE[i,9lB;,g<.3ouk^(*/\^]A;\1o[1Cl!9N7-4C=CWHJ]AQg%0mVP6PR&?
k.b_Oe_W-Ih$2r/G2R%FkL5KR@DD<OZ5%hc-BZFV!T/C@78*C8A#j[PLaQ9=7e6*j3N"so0V
p6&M./4*[`)GoVj:*KId4;@(Ag<Vsb/=pJ6OblM5o$6tgG)k,At7l-"tLHuZID(>&gnt:_-j
5+a!EP1m!a/.'DQS]AoYP2<JGtT\\<J#a0ZtK)qQOFP)L7Ru)^D_u4I\_6q2?Nq4\MsqDKYGh
$OE#%oC6gQ@3TX2UPX[u`4P7`DSM;HT3W^?+HIlDO,mR`7r$TgJu@^>@gdQsmkRO[^MY^"T+
1WW3F@rC.;(emp(?CK$D%^cmB*S8Q6UOojQUn&Q7XmFC@d9rYOCZ`e1Cb721OYbCjY&KPi:-
"non/!qsh38>1=7!mHg.e+p>+d505"%l\0?eC"ZCM]A=:8(dVn]Ac.,=3ce&9Z#%="_0$rVO!M
4\*YZKn=h8m\QGMC:db%@J2:ZF[2oZE]A[B*/jKs*=k9$MG$^%=p%3DiYnI3BQ":K/_dm,3J?
MFDQ_O0g[_&e1t_U(E:7_A3UMgMcPY'_S\q%F<h`pbK.FuQA_Wr:+Y<rD#(-jg1Z6UgXsp]An
YM#F@Bm@Ho!pfU@ChF@9Oc/#PGQV6XkH`CsQ%^`Gdb@[3f]ABCNhRb-=\SJaAmg??fGrCd/9o
g'o6?G7F$.]A\D`,nE:%$=,-Qh=),+/lP$V^fM'/_0#a@C\)Na#8\aXoM%CgoqW#XuUd_H91d
d";0Dgab'Z6jBM@XF<*</YX`:IAcr;T@eRO1W_Y^L"j_u1Us\sFS+q_T*a4)HN_Pm=T<dI*j
H#bTm['iCV-%nSj^b&%h.pe*63ga87H\J1]AK=rDmeW)Z$9QP:]AU"D+;C*9KC+.eWcfJABF)-
u@=c=T(CtD:T;l7(CgENX,Z*h,dPAH^dXLm@-7Ah2o;^:<_<Ngg:a5_nu"8U-e..GW8o1>EY
Hna8:5W'f^pUl58Z`>$c[]A5.s?MF`7$3Tha?sPi=dGW"GpC!+XN@<i&B(eMM;-Yk,7/IEIG!
n=aF5*&dJaSsPFDH,#R#93E3\C]A+?Vtr=*i2IlnRt^*@)J5/FFtC3j_D#nq__#<4-cn=_%g0
pDO9p2c)-*9oGl3M>MM#Z&X?QpBI.=Z"rWf:o'hjT#J`XLbGShd1t'W^%b@kjfObY&SMa=9V
Ol]A'=t4a7?O*pMdlm@l*C@*?No4OS::I!B#-6Sg8N37^@NC-T.3e=E6AL:>55@lJN*$Q)1kX
#6T5SFo/E048OdK&&]A%FRQ`_t=^Z4J(f@;:V]ASMu7JI?&)LkeZ8901De2*KD`*g-""a*8n;q
;r/ReCBTbVGqhF7JUp,g(e1O`G%^!>CQMD7F\Q&^1m6l7#X=!K-6T98T#eYu\C]A,akcE<Z+O
FPaa=XdHi[ssY'd8l%XU6-)5XW>AoPC1qW-R5S(.:AoNp1s2_Jt:prW83ATRpuX+qil@i(V[
o;/.&J?^#28$NUpOV'Xc,,loK4mK/]AD_e<.e6gN4@;_jPq@Q@2-WLS%_a%\ds(GE[k>AcH--
q$dU<+M/DBd2\i<^"0flf><bSrukW0@4fP>I[Dap--5Kk#D$>1ErLj7BVcb)>ECho!.(0$Si
&\?>mBMQW]AL"jrDm.%&%RQ*ca6^ls0?8pJhD0YBJub$J'Ad=rY+u.kK.reGf#ZSYtuU7<O&o
%5%HI&E,B:mSGp7cFU-oD]Ap+<8EVmdN[#skJG1U]AT9ZN6q5WKW;,)OHg$UokM`X[f$ur78P*
6+SP>uLdahV4g,V2('/WOah_0Y*0!N]Aa=M?W$Qc"]A^n;J3K#+R1c>+t<!t+63TXXau7u)"ZP
iS3NYHd$BR&TD.TkK!Wb`]AIOP=`s[TSCXOLEdQZfDQKL>6a!3E6jX:UA"\$U@?1AtiH#dle-
a2jU"q\47+Aa7jc*ZG&9b[pqBr9Y:jd?C@cnC[(i'TT[]AE1l]AF"Rk`R-(gf7ErgMLYd=8?3[
/u@o^.ImaLjjq?Y]Ae_cg$<DU+gtUE*-%ETkL6SqK"G<S+R:"^"(#?VXG_e5RkRVWFdj>R!.'
J!S06Uf,+fBB-n*qW&*T/mHfDH,nS?okgJA?8^Q56.r61ac(81?'qQ@[b?%Vit;i!$.N'm1,
_0]A%#/'oRY21Qg>5QA+m-rijKVZ!FKW:FnOaj[CG_f3$eodSAO=ODHc0o>""YSAHuQM"Q]Abd
4V8ETqKDE&3i2b!UAF<&)"[gm^rIfTd-s>kk<9dVq-Rnb'0dH=K]AFM0F.+t"p?*'C2RV]A,EH
o\XLYBj(\[e#UEj<]A^p\0FY)q]ARpu3iEofi>nf?/sG6#oK.u'cY,LQ:>)K-'ArQRDDdLarSh
lXj)WNR>oQ(GPoDWWg_[iR.-NmkeBI.*RLHRF'PGL2(-L=FJl;3F=799uVrc%]A(F:%K?$Ak^
HO56m(HWAt#mJS.[]AjP7i@sgWd<9rKHG5[25^D.<`P`8LSDW_[m5$/>K9.n\o>?K<lX4-_Cl
jYXdk<cH%gt2TLu+,;N=PPICAQ2;pN?fQ.5&dhGoqcO1IlF9rlP#!V6_K[H*)!4,nhbBWXS7
5onH.dPY2`[,PV,BMa]A!K"(0^BO>LNB;rIjhD#uNap:AKH_PfrPGcE#n;bl_sF`'p3X+FS:S
:6'S;35h^Ke;&[CoOUqjIhE(SsOp^>dZc44ZGoi+)Q8Zn9dM*3PNo31ZlP:qX6P9Vn$WIi-a
o$mQP'WGk&*61'qF+jM[+\+)h\&oN#$>jDF'7K&=k:6%-TAB*$s"[G_2Y95m_FPg%[TVb'6E
Or>oYTG4[ej=(6a@+DkBUEl'!>+t`Wgtlq]AkOro@BnJh%KAl/1;ls4[rmPY[Mg=+smgQPs'9
uY*__VH$*j<=Jo@Q*lEf>4&GHHJp8711P_6hXQ5N&(H=L;L)Cn;i&k=shnVN:jpMnbVqjh2T
dk0)"nd7n<+n,77EAfR_%O!bX@e[Q)=@h9^G_A?OjC)Z,!>DSPo[.4_[(1f']AO@ClC6$Wj0Z
!`,NmskeK,`0QOMA)QTRXIPOc7?*4msA;(iDs(9_!trGPm6j&aMLpb&Lqo`MbQ)&W;Qac6Me
De&EW7seh5LW5X!EFDFW9ta-=l5]AA?2-QiF=7m(p'C@Y'hVX"XE0"*Mb7n'uX.lT#25nr:J9
U0qK+J!G$X>E^EFip2$56'(W"i-XT)@O0li9@e)uU]A?0B+'WfWYY;7u"r2np(Z3ls`G:m0fL
YdQ&P=M\RXh4)gZ^j9jQO3Po7VjTa^[IOh7WD:Jt]AkX(1de!%]A"]AcQr-CASSlfE67GLt]APpq
H;28(h7Z2q9kBG#NnZUgt(pFo8BF*7"#@=eZ6_!d=X."nBMZ)0>m'WM_*9g;+@mJ/MeShiR,
Jmc\`FbfBH%k>,n:c\7.:F'sel5IS\iakh$D,eSi0EA9;Y<4Tn#%!GQb[>Y$RP";UKki4^7Q
kF0[ck716%YamN70AN,/<JV^F8V3dVbmGnoC\?BaD\J*?&DaPGY9X>(^S[u!_F=IdBm`:l"*
QI%U:E@F&hd;2Z]A0LonZ<AO"/2N@cB3CO56?pp)c1<B(;[(TY[hYrkfE\[pAQT*9p5A>M;CZ
2%RDO$&>DPe3mIOFIU)%W!m`FOe%&!uf9Na@)jb=NMedAiPlKhdPQ_iZ_jl4JjE2&BnN6FSL
Mpo8@gn;(sr]A:&+tP`2Qs^\NP9q%AL/gbDHXeBVs`,RE:3(D@Q\^Ce!OU>(:\H[\(dY]AT]Apf
blVnqpm-.;pHG3Wu[iDU:-_'KW\uunahntiXmOeT"I';lW;^=!i>7S9_WdW5&ep-rCuG9pKG
"7_K_JV&Lfg)oe2sAf>D6l6iI<E;W<T>qkltkq=u>-h-ZVB8NmV8X5@PTf>3f,oa"MKO=A[S
\DNrMC4REp%XqM^1G=.KG8`T$5hEth)K3bBNAVWp(XR#dhS.=1oKl/C_O_cV/COm,%ZM"bXR
K9<i]A%Ta$pR8AN8PieC&HRH&*\C\U8bVZBIqHB$(C*E1`L`n)%OnX/m:)C&/mJhbWQf$JIPT
:Ud0saTgSb(l!lFDYR\k#3\]As,D5T2/J@*-Z\ZHO*P>'Hc9bd-k_.3G;BE:0Tds7]A!TWQejh
D?Y(X@Uu#ABh>sIEoL9dNSa1BkhF)WX;neh)ZdiVVAd+UMu!<>i//-ST[:"Q_jQgK@Le_5an
M?)uXkaY.VCm$jY";VPE0mkjnln'?ROrZpf-u8J&R>.&aiefJGgLck.1`2dKV[k15>D?hhWM
G^Rqq@ihm\ePc$beElOV]Al"#4D\L)nrQ^?[pgHrWeYs(YMSf,b[hceX\PUtfl/UqQ;+\jVr+
"sXELR*W;UTL*kTKR1(C8$VW^L(9aJ'W['\\POYWsp'%YFWQd.SY*MHq>\!.-i2Cs*;n<bR/
-6FZ8"KRbKo+6dnHr6-njEq<k=cL0^-`RB!'(,M6lB+Rg1r/qUY!d\p8[Of<a**HM#/UD+6J
M6,X`bK8]AVhubj1R<"b,mD11j"&31#Ms]At]Al[D5(1N3-)lN=HA=f\1-R->E34FF;d(;q-"Jm
n(+9]AZG6Np]A/+Lca^e6-F1k6Y\9979>beA]A^AbmHf*%J6.[[P\T%#>3#TbqeiYe$#E_Y5K?=
]AA]AE6%=OGrqF%m,7O(K/>"&gX<Pin%XBb?.CD5-<-i#.Rfd*Yk;udqH]AJk=Kj;A$5?M<]A9MY
^2%_@ofN\XO<M,usmql@nU`nYYj*duj$m93J)S.9L+.Ap_Zt#?&,Kp9!QgFHUrT$XO%le1F:
VG@IrM><6oX9>=-27;EKW[Wp@b=JuM"%`%!"XFOHGU1P6)g7<C4C'/C#]AWW3<0,u7C*6OlL$
E@t^0qp?KU&9Tr\bJje@&9BJS.#EbX%;sZI8'<7EBm`]AXhQ+drmo(km;riifX!2P%q\>@;IJ
,D)L1:>0`O^"O(lDlVKAq*.Wjf*W(H^IBHc=Wo%0=F9=AIO:Pqd9nY0Zlm'-DSP^WD7A5&"K
CI^$tdW?lHZ74fS1r-J6.U&cDG%PA!?-8?S$?j:&<Di#>UkOkY1.uZEYllgAq0!$5etchA#R
H,8!k*^)Y9;/Y9d(=Y.JQ)%EUAo:h'@cK[fk7J6Mlka=A+Xb[:cHcrD1",Q4mT-eL%J+;YCc
9c'6AM&Xh$"]A@%XR4"Dg=Z*8F3eYetoj#DC11N)rNqSZV4Q2+-\HJPV_O5VZ9W2Ab5g_RRAI
fhG!/@AnRR1FE17TJt1HUaX^isMdF`Q03I4U3:l"XM&^kP@cq!<^56,Fs%!-5nfXlF",BFuX
c*U`>hH!S]As00[@Cmh"#l]AG]A!`O#PPq<>2=]As,M4g%E,.%qU3%eR$hs<(l<5A;+)3PM+&+FA
87iD]AWR+uD)GP065_*4fC2tR?,jJ!TBh<T?'P+B8M;Hu5,\S>R.d,0oMs\r*qq0c#fn&/Q>i
Q&kj#qMZ\VXj>1Bmfk(tl@e)I9s^;LE:sXV!SL>Ahgi3Fa"%T-`X*3jOll2U2dccsZgZ;c/s
e?AC<'$_L\7>U'\<;X0*t?g?_1U[+MjhB':78#6"O3S3jfX1p>XV*#ileZb0Gp#JNXqNB60g
HmT]Apfi<3cgg.!mTUp_a[uf3%,;:q]AS!EsI#_r^I1HWuOmpr3'U=$Vph;%t]AaV&/M`ZikZ+'
?0#fdti"[Xp!$iSAr(Bm9V#8,dWS^-p6G@C3Z>[+V&(Y7'D?jaOBg@P0,3t5#CGb=D0UF5i<
W/[CE[e``S.9s<`'Fm+(Q.g,[4jNFaO*%k4+4@Or0KA(lb>#J[j/0<SFPou_pK>20\nj/9H&
(r![,:c$bdEK+M:rTOA'd$8h$RF<LM[rYC9s(#g4"AabcpZ1pZVTU8^>pbHZ,eN?;a;_-S.'
Sghb=]A$A(NZmVMONYBf?!\0^t"<i:"3aLeW61\qJBoaNmK%a-`j@C<LG-nA,^LkQ@JnA-2`,
l%AFY>D-)W^NJLgS<U#Z"jtB"KFBmQQs9>\-8Y7OWY^*%s0gnOMgpV4Q2e[kt3F5r+\/TGYD
6_0d)c'h8_%6;1fnVMaFe&c#TQj_;#pQheOcJbC2io=Fkf9j/3@MnTp$T<f6;sW-k0p"!/)g
=Gk@3RruY`M_;&:R$F3/<0M_O"CpbM$1""3&im'MjXGU+Q9$9<'0d?j0R@8Y9,Hl)f((l(da
*jhoca:-MqF%4l6OVr%:0h+dnPbXMAboI89'i=ndGh%r4*pgi<cK*.sacI\DrVp'uPHF3iX*
]A,6SOCq\MJ/i*a)b5*U%T'Z!%G66*(8V+(k$,lVn*8`!i[Vo]ALMRgud]A9t8LBZ7RSr^h+"[P
goTY]A3RR-g($"30BAV2):fo_lf=2BJOK<gc(3[X$]AO';d'U;Of7#!C)^^Y*UiZ2sh(:E-=i?
9J3&.(OWXY8t>oq3=$abpa(%%>Y<2bq9cPS"jXb_NQ#4tIRl5M?i(nVBXKLn%kZG56JB>:J-
;7sO!G/,]AOE_`C>mC%aY(@4Ht&ql0@<Dd&d=*f,a&#VE<ema?*heFaOB,(k&BelpPSfZZMft
(%`)0,Rg4H8U]A'dYBkDSB0W$Phi93a='T5@2ChQ]A3)J0.d`B4.ZN`%Z@?9QUC-&CesUmMh1J
l2#I)Jb3G<0%-4=;5I-im!`apZ1IVr^G_2$K(=nhO<rJ&3BM%3AKrCQ'efR9<Xpce/)7#knn
,7EfRg\+<q,Qe>_@kQTlC#2Md;mB;I9S@WAkQ..(L]Aam`n(+ScP:*U>SV1FGQ#f<s+@.9XjW
5/$2II</u.?4O#tbBl.g%PB(PnlB,/9^S_`IH?'!S=,H$&bqA6J6]AOJ_q9IkIQ""QJ8?&2Tm
Z+tf(DZY#7Hhg*9d7"#kqSX6&b'1b_.XB5!<YC\9erit5n8L44DM,gf)Ys9$*0@u/"r)o'.V
o>>7>M$:#!^XUYC)]A#a)3FAgc&:hgh$aglZ<o7rs?A7@9*_K<g$f0O&*$dP#:fjWgm8#kt"i
558S@^q9FA=*/$0f7qfXJVX_=j3o2Dd^UeJeja%9oV!*,&+8+e#C/jY9Kcdmj=iPC?:uB1$D
2!2R+L``&Lb$7>+`*)aYI.^W9p3sMnr.#T!P7&uL)4ICG)k^d?U;CsX*Q7DD:m>7r@JO(2lf
k;EuuQ)O>"pWmADjm`+(gG_%8R(^dE/a24"8Is1;/ME0&B]AX)&'3D7LOp$PUobeZ7hGf"3ZP
APT:V"kA1eG3eqNiq,TbT6mcu.PpDEl@CW6Q$hHm!5YH0UEPaijuXGZSkAZsQWXtG^uYQnIe
7TDFN/U4K.GUTMH2XtduR$o#b+t#0["n9\fdSc/<E1=gH^.heCkc<_ku8&0CfVmXFcse9D_c
\!QH^:%<4C!,8RGo_0jOUlb%Xs>:(tKG13'!Rfsg-#!#p2\2HITg`nZcC]A?(LfX56MCtL/(]A
pR$+r,.YQI#CjB<UJ_GC\:=E)VD!<&4e:M^YJ^A?Z`WUb;[TAB[TEjqZ$Pu^[,!*n]A0_Qc86
H^=g?lWP[itudjsSc]A!-sZ5pQ='fBqb,-GhV9H((+m(<nI6/VH,[q%(XRFL.BI>9H&tB/:"<
9D-_ppX+k]ArUU,;&$p+QkN6(5B@lB9,oIq/P-)5i3pKX"Ubg$W*I(Re/mU@!')Tnc]AM2Eo?e
Nf8>:.bPpN5]An36DU^[S?*.(6q'mfm<2"XSBHP=uh42=-Cj?SKF8I[<"AZNJ_YCRU.rIl(o<
p?VNeFPb?@]AM4#hJEb;<p?_0eN55:KYY08&uLB_?oi/qQ5J$nTT"ZpYSd4oSEUj#@Eb@[KRG
BPK0+$Mh\LPWg(CX\?cBFQb'P^*&+Q7d9U!>'=lNpK>1L::;I$jMRX14aP6GZAlB[f2$AJ#4
"n[ZEMal`-7gN[h!_FiMR`*uajT8]A94/#I_D2$s97+(q&JGQ,mo'=J(f(Nfo2h,2Y#Jc>Fd)
7Ja%c^bS9C7FPY]AhVjRd028BT%^=Ed*HUG:%HS_KQ`,%(i:S4S/-/%03EkcG;[hG\/'=AKqN
U+@Ba^nkn09)X]AAtR<qHO@O!@iOg#3>C#@H:OC^'*38q[sZ*VZZ>3m^l!:lCsdt1$h5XXBp=
+fg_:FS69J[8n]Ap&;C)=V6\<Hq<'nKA+@[t:`R9Lg4'X3>^<`6`rF7A"<M;[PElJ24XJ&J\^
;6BB7Sm0NC0`a8LVSQNpQ(?6NZeHa./s.DlQkeEGH]A:[m$(d:Y5uAXZ'VlGna!;9pZ-QWRL0
;,$5nEkjY)=5EoW:3P#$eg^&ZU$Qqi(_%bSM.OW%6LdC]AL:H8U6cpas4S,imF6L"ET9:tKg,
i:(Z3JY7jlCA33jU(&&&S`Q)8:TDW6@^;/u@\EJJDK_fN7\WVimIj9"P-.;U,P4pO(#N0VWa
--=nB6;kkAR#2#N#phES]AJ@TI"h*r+kWZUh-_!Y<T>=DsE^(+V7mTFRdU5iWEAue"8lH>Y,\
3M'22;**goMn@$U#4Y?KU8p`Za^17V=JZL#f<(&OO'e/2RXlp\=1SA?H5N:h@$)j#G>-E@RW
XXc(IaH>D"G,_(C)<?B%^q.eBf[1?jla^E5mXg?dY.ABn)1le^rf>%<M`d_D+[)hou0<cn?&
&F6p;0TW**\Q3ftVsIO!DPFggM^mU2/l@8EaXcI?egq"O*9e/]A>4/dF%,YJcWY%CY&]AIs+$O
;+q,'_dVU>HLu)jZB4!:PO:K(pkW?#J.qhtRL+L4IPl@k;fn..YZZ:Xnm"K4?V=]ARB/9HMQ:
s<dT;>'kFG-C\DqkG84U71)ekEmBV4@-LaWtC:q`pfTT[Y_6^#V\nl*'\fg,&nW?^flA0A0T
%OhsRp%=(q+LcZD(/.C)sBr8$ho&u!1>CjEJk+S0d!jat?O?%ZR[ft55IcQo,#cW6^bq]A?p0
?$Mh%d?*\a5SZ=d-'CC2Zh7VCq:NW?bpm=Zf0itIHiul$rD$OCNi2!&j11(47,C#)KWi7Fp0
l;9'"t85Qre,#m\T*9E(@REln2.N^!I(&+P90PVQ*hd1<KWn(%F,mAA,^mn&L']AZKc)2UB[g
&7_&Rkq1Xlm72`^mq4npNga/Cj&3B,:qErAeeau!M*=%GV+)TO\\J&lbMYBfk.\=ak^VoV'F
)l<Um+.F]AR^E/18!kOQ-jPLn%bG(C=&ecn2,nsAk9m=%6KN.UuN3!g!WPN!ZdZOWOrQn&9J`
hpk"&Bc,*P/JRVuP1Qb*XD,R?V9*(9Qa1^$JHO*th1a@LF"+pO5Z_bfX`9\Lmk6YBhILY@in
+uP53$nN91mt-js%Gp;HGph9e(7pkPPRIqmDXZj7K('@DI354>`RW**K]Aek5@9I]AkhNZUGhp
OUgKGXHHG+X"R`XJ;Fj-?oK,e@cq/`7IZElWa1u4@&Gr@:eD2YAk*)M>&S>0e&jPAM;JfeaG
cWSR\#6XgqAfHhIci/RljYXW[0b%=5F<njeStq[e2jDp"a.3<Yfa8iE^QuMbE&6OlKeEc+.]A
%au32+S%A:D12\iaY<'.g2B2Y_[KF6kqe.QIWE(mI_=3=r!UonM5t-M1/^N[WUVK@_I0b\4k
gIY;G79Q3r=?ZOFmIEp%V%G%Fn93eKe[\[02n+irPoQRAB5+R*S6E>Um]ARt.rbp8Zsd*?s4d
+rXd>&!PXVE`uI=/?#\<?LR8'pg$^gPc#b&AV.i=?;mP8X?\pL]Ac"*ff`Ht^,nfr*d12&V33
<Ma]AD&V<Z`"+T:RC\;$<jkq!G4UO?pFg#;ju-H.JjqQ/oCOh\:TCa&1oklW<bG6M0KeBQ-UL
=fH-^4K%h%*+nLOk\<r5qjG*_B=0E1K:+,FeFZ:ZGKro/$#oElVFbIqXd-1Gs*F^DT_p$[MQ
<Xg$m3Q/&I^sL%4AeT!\-ZoVWTuOs7ObC?h?B<0=RS4I4;?@DRnsu=@@G]AY.b"JqE@C2=I4F
uR_o9.H5-r84%b.M:3=EbkmZHtAU<BVTh>A.*;@c@]A^krm@^.H:B7Y0E-N`;;-1]A82HQq8.4
FHqK@PG6/1CMpK8C;4_VE^-+WADbt/=H%f\)gcMOG[#pH+/:BgitT<:"?#_gNR\F-dA*VHI=
uS:>*;ZOfml(!.*ksjO.D9m-n?Roo:r!?g?mqWno`CdYZbJ^\*0PlA2"g2r1Vtrr0mZisk[h
@:(@[_oU5CZK2jl`ZB=-0:ba92[`i9?k#+]Am5<A!p>VsXh;F>qT1GgC_3kG!B>jS]AlgbINF#
aRs3rq&MK^+E%7]AOjr8r#9d.ogt5cATjLAO@G`cPW,Q4ps(iiTh3[/oFrP9*[J:Z0c$s\k$I
Abt+`oE*_IRZlSHl;N,Rk_k02O9M-BPm\lQ&VT%B,L,/bb'l\gBk^6R6JarF_Uhn2S6_%="Z
$LS]A&1]ASf/%uDm$RLAKW4*"qB$<u_7:DJ!SX!S*0@nKiLR?)OZ,=`MNnlthnk]A8iahVarOti
U6QqQ,CU="4!qO'-&>'!Qhb%pZ+To,]A+44>?oW:BDCGj8J3J):LGN;[QKWpLA5ek7h>^RUl]A
FFn^CX92brMKn1U`KQ19?1VZTPtHa!=([dG<W;U"`gkXFoNlrchX'QUD=q8^S)pL_X3Su]A?D
$$GSjf/.n$2P+:9<#.Ao!?XiS'1qQct1%/MNb82P4LPO!8^R4Bbl$O8*bTT1&!;EPLLgpgC)
oFgC4d-tK;>=_@X$LC+.2]Am8J8CGZ"h*\D7^r20WD">'JC6mr#F\btW9J#2sJ,M+mIG>?Z]As
0,"mN;odX0$[Mq/&C"t.i85Nk'r@q+(%%PUgof+nu`^fSi:R-4)65&_Y+L@,KFS@kAd:"><=
&(9j$i&Q8t$)J`-@*$8UPGlbD^DDqpp:<aRP!hst\9^Z)O"I:8+?eM[Wtd(FW_e\2Mbl'6JL
ib7GCep,WFH1P';6C9PR>I[joZp"kcVnW.XnEY2\6\*:SoA7DSQm<]A:p:"al2Fno2=hm-]ApL
0GR#fgPSU0]A_WYACAV7f.,\RQ20+reEYfhD"`&"-u:,?eHI3eQ2Td@aY47>j78[UHdN2[>P[
tC4-sGIgJf7qHWh,6MJ5;QRj89rcD`5IsHIsWNq!W9`C=8plCT]A"2Lb@F77u`0<]A:)*r8t3s
1]A0dIk(_Q.gH$P\K/Y1[:FS/"uZ?9GcY!-&%H]AKDU@dC9XdV-_]A(=bB&M+1OZ[:(iml;l]An>
dRTRc!MFKoT50JD=kiI[)"`=9/c8qs,"gT4<Ic@i's"WWgam]A>SF7A1;ABW'au!@33s/na=W
e2;.:D4dDOEkG7#0On`tN_qn<Ap]AZNs+l]ALiQ.5A@"pK6"B%0hr/96%*'-ng.s^8j->tSgbL
_.Ib\#NZ27ekCL4Acn]A",tIYqun<?XF<e0_og'iiW)`d3,8C(%4C&\]AXDBa+-:Lg9";qGA5e
nSI;tWLSG[k41g\g]ASinp^gWc0&@dG\"(_!K\CE:(@SFpY0.90H_+u'Rl[\;VT4^j;kM1o8,
2!4:+sXkg(8!\#^N^Q$.Pf/q;JV[\"T2*6&K:07C<h$iOq.\dmJF8Li35mBC<"hUcVa)QBKV
p6c'\4Jq$hYD#5J]A:@o:mMF[FdY,_D5B+a%gaNB;kjr0ToM_+@`%MtP\uCJ[q2JX-k-eL^Q3
NddYkQ(nq`JC$[OOct)IpD1_qLg.KuVs=?UZ[T%=L3ntVFN'?5;p2'IY!bd@N7M]Ao0>Ie#j%
;gQ<furI.b<BN<Z,(W2XtDV,9ETm?p#%b.r,tCW[+d:nbc1c<<#NS;AD33gKE#D)dn("Pb(>
))=;S;V#bc\,chjo3/l4Wa(WM*@G?*46YSs,hAsU7?_64-W*0u0a1^i(NU?'AoEVJCX/6k1A
EiJ@:*$'S$ol=AA24AU58K(eVRsfCW9eCW)LJQ<1>jaKXW1:46sr@Xe(ME5N)t&:4W]A,(W7P
jAB'7e(hEmpDes8!.jd&h]AZi-QVe5Io^$W>"%[IN!X4LbVP9aI60'CcsAj;h?'2;Th?qJFCc
i)6'dSnV_tLM#L4>aPV>!X)N87`d<n4D(GU((MC;LM0,LD7suI*fLP)o;:&WEQD!nb^(;]ABq
o:K&)p6KW[G\&=.fCee:)"%:V.2V&1t\8-qO=@b&5/@3[12]ALDEFim)=BG^2E2VM[#B+J<N,
p`R4eH:cU&>+=P:(nXXm$-U),Y_^@*rj2qX7H6SDiK7*M]A:F(G._"8[^_:Zu>(V3r+C/?S/e
3l9X:HDES,\jSHQ`r?<auZ7&)%Xm]A3fo)`\c2GaC<k?K*.W.<\4+4uZH4$;0I6Z\MjG*uI"*
Te,bLPT5P5W*\.1([_B[s(pgXK\Gc9-\W1O#eFPI``'@1b*2ih/22]A[Bt:t"o>A3+GjJTa>Y
#4FFI/T'lgHATGn5a+d8b^;&p1kl#H3I<1skeDY2Ea*n8eGG[L`J=mLmE:PXK?Yj"ZLTMmn7
F?2lH/4//"`GkC?&Z_,1#S'hiN\EbTKdNp*JO;DXAOpX\_T\J\P:JU,Fg/"'iH1+9K+nei"%
:)=,P;&1I^q72I*\7M8"\!i;?!j]A.rgP)1)5jJh3e7G2K:hnpSAX@1eeki%Pt-?n"cOa2YX1
Dap"3#0<"'M@.4_%-@*ED6CmZ0[_@L0I!<%\eDf$1&Q/Ru/<(Ws+CZ5.%26ptZ?KLUo4Qo#U
*lN3"<)<*W/nd=;>n.p)VMhR&u,Z.g,<K+D^_N#RO<OGeFG?"_Z@DSoS$rNZD^0KKm6b>]A[I
VU?Q#6edOS4:m4-3_QX4U>PNjJ@ad]A;4pbs>MO)@+h1AfSfYTZVse%Ua.+i2=@#-?Gn+9$E!
8Re\S5P^61B'H7Fnce@Dd"sltFc_Gb`SMdJZi'iC5%%T1]A>IU*[:Y>l6u";2W79G^)g4]A@$]A
C?.;+mHY\s#:pVYhRO,e@''>$Gr&!lb\o2iaj;3o8'Gt!A$A2b89UGM4MpQXXJDe$f11*AV7
#lS60jUnF`0Hu1Apr"Q45]A-R!)MS$3(m"h&[P15T/gJ.ag4T?8k1OoF*NeZ,cunbkr>,X+fS
^-`C!)mJXKNKFF*WQ?"D-+80)&ee\$0o^?pd(/XQQ!5^oA;T#&1[p`-sB:f22K?H&n&I!Xpu
F;_`-*D#!p%2;=S1$a*uebHOR4g-iJ>J*+HdSY/PS`Pj9njV=DX=(-LarkH!ShdE',DcajBb
@drMC(?U[Zm<`0]A@FJ5Sjc7ZN%!)YB6kp3=#cEfh*nOX(DDc91foG-,-8,J0CmmKMA'lK:?Z
W/V;rMHXTZ,>cO1<WL>3OTZ!&PJV'8?6O#=fk3Z"aM]AZe4B;?>aX/h8[?)bm$=a<c5@sYu)X
#TNO\)3lm)eQYR[@W8B\KYm(o(n5#s5:MpGp7%5gW79#Pnb4"1FMm6N-^HA=Hi@;pocEuCdg
.BUNJ[`k=A",ri/HG*iuLl$Ll?qGq.fb(cBblq/7m0!!/-]A2M-)'mRQ5k?3#$1C@>4u&m/OC
S3^c*mu$-_70*X^'%U-'eml69,K5N,D*20X_uu>/A1uq@Cbe-m\H4`O.Rq98Y@7VtGaRp-pW
-fm'QcO?%e.[qV)Z^"cnF!!5b5sj8O'uXqrSCZVJa'8]ArHRGL/R"TT>kuZNek%I+)\DWWK/f
`<p/P/_DP84P7gn"Zj:64`gqT]Afpja]Am`,l[N9K7*(ii=.aZ'MM_q$fcXPeCYDVXe9R1<Om8
_E><`%g]Ai[Hm1WL2]AN/!FU>gFI%crr,(9+/W-#Kc91S9WtlZ(!9ZcHqL]AdIhWdSA33^Xj9pO
j4le-0Ki@J5g+:*f8.P,#=#"-]A\%_Vse(+eNgld0=drcYqWrcN,:^^#&99lb9X.nh$$4[ol*
rg,c(F2H>Z<gmXO3!/GSG.#H@3[Z6GfD0R>`1W%pWp!fjm9ltnJCK3IQn6<!A/_8>0/[@&EJ
d`TRE)7^dd52hg6ci*Sr9(ff9f0fU<S%n?rOM5m<:gA4&%`:^\"Qi0BJh-mK9:HPeCRLp'-Y
f6SlQhNK:r*^Q`F80n#uoBXsh!]ASs/#?<J,u?W^slX?A"?YNfk=i"sgIP>'[8L[\1&K'`DiX
/eVhStu?sP_j^X:20l!6X>:S78ZDEeZ7LZ_Vpm@]AsV9?F/Q+J(0%HVE!>mN1S<J-*41=O*@$
iqdG4=6?<\_jlD7%6YT43e@"'u16jL`9=+qZW'YR92fF&+`cJil`:e%(QTG]A";##FO1?fs=R
8:kla9`e3tN3XBF/639-lsl<3m+q"_\ioK"2g<PKp"*1nDaZ3G/tT#8n7BYr0<jsIHG8Z+^7
\*T&p#,771-SVIX#IN>"T'"5FN&F3h%!S;Pdp"lj1PXW:.9UDiM9+4?BiEPCRqoSLL34q1t)
/qt/9?f+h%s>E`*r('N^!B,;fim7i8WUjXuY4;c7A9rHl0e(6'>Mqf;/(K;D.-*'B=(8VrBo
g#KDZdqE@Fba'4Hs^r`7%KT8"16KF&&,(BIP8Ud\%;,MNsE18B]AHY5_t<MYq9>9q&-5NiipB
OfhF.'u:ZeTs-(57UT*i\cpCZ]Amr=`M;lOT0N4B,B6(pk3M#gpSDZ%O,!St-`O@8CVp:VFVa
q"6u+5[E\P(u(DlX%45cC`.a+X'YM39-QarJY8&q4d5I;mTf[M41d26?ZO)mMfs7r_]AOLPq4
]AeZO'T'1mVT"+;ICB_?Jh/Zm8aA?hcq\-9b/piXp:.\=.R,3,Ehd19)+DK1"<qE_<gUg(/'8
SF>q]A-<HZ-3YK8X!6qVQ^,ak2lTBb2(+pC0Z!a">Yq14TunT$"lLAJ>18Y%l&VK7<gA!ClNp
dIII?Wm&%ri^S,^"_I(<K"2f!/.Hu,8rBj"Kp-MTVON)'W6":9Os5,dB-:uJp3^SB2/s-T]A_
JkVT*5b^g2J$I1Y=L$c/PASVu5"J.qLtRMLs8Q!m2ce_;S1YNP6;13W*b$'R=$Rj>6iO-(p2
njACa5Og!<Q$ZVX<%<qZG4`h@;_Y*'V?]A6r\r#b*_aZN#+jDGjBp*Kfc`8COW0/a_^XoUc^(
E8RU>Jh\mH#jS[3`6l/%N4Sm@48Hn$>1/Wm!s^$2$NNQkKo(*L#6%H<Qq=!=nSE;,;Fq:/r<
k%ELJ^SO#fb5_[b$Y[%Q.UH7VP';8B[A=(Nm,@+*oqcU#;4D8&YltXL\<9@k>kiWlr4M=f9,
.';VQ`V,bYbW[$-5)<="W]A)n1g9$?d=&S:OJ$[=acqCJma`N")+P'JmeVs3g%_Paie0uGlHj
5aL$/fb<:qi++`P>rQ6=rT;3>DW#r(\)Dp3M`!r\u'#i*8.5-ZI.1]A,oV*.[TJ#PG-=*-*D3
6.I7F/iKOrj^q+'9N#)HQqQQDqHeW,!CfWaRSF0=S##50,aJ?uK,s]Ak4TlRpkAKZV`i'B0O?
"'LTd9Z0^`+sf8W&""_148H#S#KL;C*RC:`mE"j*!hhX4)#lX1(=<5-QU<_o2cic`%%khack
oQiq^D[,t93[:.pi*EW-fm2b,>Ma2L?F\d:Amq>>=j6AAtS'<%b4SuNU"jhG:rOPOlq4*C<7
^?5#>9+qm@b(';"[Oq3pniD@)"Go;jVi1JRU_;CHEAmtnee7LJ>aAQQb3m1[4c8B[lT,gg^^
AhEeSIe)\rJAgQQ"M*"L$SV8-o9hl[s=Kc4>QR'I0?M+Ab?Qbb%fH(W9o_!?W-70)PaKoT+p
ES$*"$Ws1ck=FrJ]A9ZNofI=tD0`"8s'^:OmGD!OiUgosAjAH=PVR$;"KG>:c>%n\rjsg2A;k
n2\<-2nC(AfPiNYJfuK]Ae!=p1E"=;_<NX7DjIfG.fmSeEEX=E]AU(4`Qra&ZuWo,3(4YPrTQa
`;<?IYqh(-,IBjQ]A7@TIBqsNj&S!_C^O**M)Kqg/LC+GKkW6(kHW(BY1g0>ZXOccklI)YCC4
]Al?VT,8sX&6V#GMk,<CY4W*;4bd$eA>[!P@S^llZ'/E6+S-,fqCLLh-:KVUT(>$VN+A4)[#4
LZ?H)233Te?M0)m;m5ake*O3[0MOY1HF<g"ll[35)8\c)p8Q@^O`NeuO!qpq\C9OB21ZZWGJ
k6Yc)/fY=b)H74o*L7YKEh)HHq,4M5``BdW%k)[@h^3ch]A"&<9A_MT2]A:[ST?RrudLO*@2qr
M@?$#l(RCl&?m*D^^__if'Ud;LknM=fl)GtUoT?9g1CE`LV%o`#__m0&<iP^-gVJ9X5_pZV(
84-P.4g\Uj@:Kt8U>dr[Jd]Am"p[QQadIe!BkkH(E54h/g4:T&Y(Q'$J<j8/R/WWgSP+8%iVB
Kj;Hpu8Z@l-gH8']A6'IXN<Z<^"atLj>VbV.M6bV7710XLDdNabdF)-f/T-a,ZSjeq&$KPHi>
qMJo12/EU@II\bKH*!W<goX1u/:Vn1jYk'K>l)+0[3[Vk]AJNS_-<7nQicT@Nu.l)LKtM'$!)
0L)+'OOE62OkP\Bp?.q/IIJ$^W<Q"fmue=^rCW'M"36R"poOJN;+eXEC7kb&=M5t-'b-h#gB
j5L#q8aPC+TF"f>KtY]A;F>5gu4m[r@"D?-?E0aeDWK)&OSfeG!UNOp^>SS'DS%u\4i^bop8c
G8"&eCB%rbKAltDmCLog:?7P2`O(i1lSWng3<SH`S3i;f85mEmO5\BE^,&sADf3K*Kn:G8QT
T8ZWqa."%>759ukgMg*1KggXU>YVhrd=pK)H]AcQA<A-k/JOX"U%,Y1kQ;IIUaB8,Limqm2aO
H.DYlJ3^Ume-bs6!eB+DI3$4k+d\LDhkm,'Y$M[OE9865Vqj0;Bd4HMol^Pt;JS'7V"5n0pq
LSL"J$D5e2d=Y^VRd@3iO<c#+V/ZNg@ff'o88M#Y9m\kB,`e6"/Qs/0)qC"db)8<@T5;uB>'
Qs[P.3HER]AM]A&EsIgRq2F+Y$)AZ$h@F48rS7A,Sd6uBCGpZIb"`6X#:4=lJLgrh(X`)T2_JU
R%ADXnpuK9W[;2WkGnW]A;Z\6ZH/jMk7\,@^TZUM`hkI/^)Ti+rNJ?^DKf,2[kYeb""STM-Mk
rXp"$RUc\<=ecgLG>$gF<hCZ2U;#"Asm3JXSSusV9o"6==!V'$o3ZkA9=Ap<;1sC?5N"ON.k
J,Q/>sHWdEfsI9+skD=`X._IVWT>,+jnM:S*:Q*d$(VTgDL>m1E;K^#tJ.?9CQk=CEj+NMjI
ba]A:Va[R9:8qJ*:n!1)+cbh1FV7f14YrmUl`60UIl>@$UErM.&P22KfPc[8oresZk1(9AKdu
`k&CjTb7B]A+2Q'=Mj-OlP]A3@MP3&E[l=lB*Gp+-N4]AU=_tA/H!!>"jTUP"UNW\Qr]A0_A6W,K
1`jT1=1c/TKc*tq86@4=RdG5R9B'9`AZa+m"d6HrCC&*_k]AtT)tLS+Bg5^#H1M;3YJ'oHC,4
^Kn:+rlnIl).@-M50A9Hu-[M9?W:U+;o++Ph;sU#*SArbAC"Z^%Xs>ki\T;F/P^3)<EiG<^_
*_B]AtA(=D6\.kgM+'/4%5;E$6;*<t,nuM6\)r^-)9*+^J?5&3@$8?jo.Cn1_/q.$A[sl=0gL
b+p3J"4ep7&S#JaW6Zc'SSZ'%5_;`P3P\TV&juLI85arrKsJ?*k/%Rn!r1.;^qc&M$35Z76]A
"d+iYp\k0<KVg\i@kbjlMMM`biFr^D/(pPMk1oM]A31u$7&"o=7,iHVOJY*N%)t/7?Bh?grsi
a(`XGGGJGdJ-u8*W09eSHLY-,6SIJVXYuUO"CHSkcEcb.fH"t!=5IIR?cF0Lu61Xm;6JS5$n
!h:rV>0Ei-UJWhp,RXR:k@#cYc\r9TS^*alB:OdVZ:F#j`+t4-[l^uG=5c9&W8Tp'!'5Im<>
EqW@bqI.@P^2MG)o(nRV)aftjI"+<Jq%E)Ykc0jk;>q[.gF!WtfhNisp:,`Yg^@.d%8,<SF"
VqfWR1fcmX'(=@CM]AZOWCD7D%SSFl&;%nN!N&&e_=,'sgOEAqhZ8$g3r$H`NCgpj(Tg/eJTg
V!9]A1k$/+SP:2oT@8BXU\^i[BfioZa#g?F^K1m2*j4`S]A;:T9Ku_^*7FRJeO*5<hKtADG2bO
sooh<.UIlHUbe8nPaD>@Z`+)ok?t+n:UOhC(/Qcb[cY[3m61JGW!,h;C:n[XVC`hPSG+D)bh
PgfP>\R=%Bp9iq[PED3Xd4\.&WRl!=1#</I?&7?r(UT"!'=Mc#7_#D.OY$dfHrhP.bjb3pPd
DPJEkJFO`S>eFc$FPUKd+'?NfBjV`*+I'?Tm6gtcDTn>j+8['#jVDS,Aon44A.@5L5k%!UeQ
fP,Sl3$^ufO)1dHB:W4*##qOE8tJ7lUa'KV9$jIf>]At_8$_\8r09Cj9#nVS%O#SG1LSQXtK?
)Dr<.!^^93:b@kZ^qFIK8t<hKF]A]Abq>*f8p:pLb+'[%Vh*1@)'WtK\Y'QuSgaHeA(:D9TAEh
n%GWhsi*akloTE]AnAiSnMpJ.gNWYj+-D6&[ad`R),=RCfp<_im0YL?jV:k-tKgZr:udB2!fq
1]AJFUZ(!lj*Uo"6s4nDd)9I#j/i&:Z`!=]A0^77b[*75E':hB8q6Q0VXAr_.S-Sbe*J4?%ELr
RY>p#k%idH>naQ]A:R=t1=WCcj<(Z7Mk?,QHu_2-R%hSRkG2/C/T#I`%CNk"X0C@<pde^ns/&
L?c`F(kWlmYg849b<SDkG)BYVBYe*G=KfhlaI(o,0JNu8QHdHk'^#Y$5b[pGccjeg350o^$L
Ch:hTIDjlsaoR&A/dCid[f`Al-Xlad*T3HAGP^>:KK%1m'p:f;X0\I]A!Gmb#BefoJ<N#4f>2
+Ua>%P(i&3;\?=5bqOK0un/Fmf.1e@kj!^2Uqbu.)]A2BT2Jl6&f)e.XirL49i>+H@e,FK/k/
Kr;$W&%\!`WX&#C9/^neQ[&J1XHG@B$"P08?mbL&d-)b*%N6D2@5rI[`oOMT:Ku+?Q?`9r2e
%@=qRK=,'c6E174UPkeNYR,D+15SJC*qQaHs9K[QGkC)=kF$pO5_!EI(4)0[<Z%=Gk!*B'\i
Gkm%$Th\=fqX)2q6(/nO-(]AD)"RhTu9"EGDTtJ<J)2a!F(QiGBN(o6lQ-b(eY[rotSFIRMN4
-]A9=`SPEHq*J"WA$'m16.#!nmhT[7M_X.B[#ST9V`IIEk#&31;_tTD%^H,fB#h=+QBPAd<Wj
P+D_qG@Jm)+QST\A:0u</%,!9h2OWo$9:W.MG.MJBRL`BFL0pLCZ]AU.%Oo^(,=:5:M]Ap:n6c
K!.srq]AJUc*gm0=>-l/J"%PAXIcc&3#,(ZRU@Fo0>l5\]ADSoRLcqD3Q%3Z:".?g/(8il16@>
HL=kPZoQKB[kWo5Y`"XpETp()L4apV<ign;8.c2[Ae%:nGk"3NZi7X$gmeS06?jCt"a6D"#R
dA`+f%f=[Z@moAp,6UZF.-no$AaZ9U3NnFC4H4F)#&^Wt555YeGS$'m3BpXP383r6^\bcO@i
]A_I21UM;6r0?>eL8r/-'s?-cC.XYf[=?.c^`^2fA2r^a:B3b49WeK-oE%slCsMN6U*MQ`S;%
U]Ai`l3hKu4S:(upo9,$c2_.MPud/5ma6b*[?a@+:Vk^M9jCNOQf_:"q4B%S,GU!U4+O]AF_lL
5#j4Us(HJ6QO!@Ggc9<8F<$^cs3m@/ij1@@fq=nrCgM-O?$:3.h'u//]A<[0O3oU$&rm.(0j^
<:-]A=ZOcOaHK5nTig08SFU&&68nYI_'6X%se.TR=A$X$1Th#j6qE`k3)B7&FK%VpL,pMY\@<
M4KV#B=H:$#1#13]Ae!+_Z.^?P*FtLseo#,OV;gbJKsLC>,nr4crg1(Y4,_EXkhL]A^1-nFCq%
0"o]AYNc5WV,d]A[r0R,Me3bF4G?&5>9?AX0SZ1'e+#"elrWcTo_PSqAH92SN++-ekOrRks/A:
=Ta%+Z_5UOldp0nkK63>Q2RcNBO.bE$>^d:IQeshme&.4[#93;<e#>>.>5nT)enJ5"Un'fIS
UnJR2K#8LJY.l(m=&ZF9_dG'SQ;F=0DFVj+XCM/%Ocj!dQX.NnXnL"(QaDlCo(K;)`UH!4a'
514s%]AVh]AILO-]Aiq$0q*M#Je,o06mk?[Vp:7qZ]AguLks*pDgOS^fW50\g!gQNL$W1dfl)Q\V
o$gl;Pp\'Hm^K%K"]AB&k[a8aGm/"PQakE_986NrKif.2gWfYK!AeT0$Hm^*Rf#gahD(kTMfg
,a:<JEUr>Ho)Fq0K:]A5'\\]A>Y^;EdN&CR]Ai/e]A3oT72O]A5LcW&_8!C"hPOCp,K9HKGq9.OS<
'c=9r^flq9u5q<*c-!&9"n-^lQ=uoPU\;`ZXF$a=rK2UBmpqmKS=^TR:(ejkMbf8)qd5jiZp
>cVd[Z&32A<VU[U6ULsm!@]A.TtsK<k1q!%mV%rE!W*X]A@rf<i")i8$.VOc,T-;q*&1+9^a.C
,EBBJJ7Pm2sG`$=]A`h#;"1X34JN/HP,5p@]ADUQ<ho-@c4WN(SP(a7Bj)gOK_RTE5:&Om*J,B
>ND$.`c%d8bqB%%GP']A<OqfE,T408iPVQotITs3tX>skIFKH6<9RM#)oMf!6fRT'sgS'^YlH
_LA-c&(<klrN52nOJ.apgRWO3Yf\\(ccZ[EI^T+Dh[[Fb86si@PHJ131$A:je,t@#LPo:6_"
#bJTWie7>-G-C`W-_cguQ:Zd&48u?hQdebn_OT!T!pS0\BCmU11@(F:/`iBI`4.$^XpU=[*n
u(\sg-Mb=<MO0iDbEk9lj-Y:7$bUOEnrMLgh:J\,b\gg0:%0K#Bm%@AVc`hG]ACP<'%UaDO:1
`j58co84`0g6-Nt6[AqVUt,iU>Og'1GQ9SOVA_$,Hb_I6>)4_O1@NC%9tCOsGPHlf(fnE;N\
]AH'\g*?PGm%>kC*A%.XcprHs8kG"e7-;3EX=R>&=^``icF7#"Z_j122RAi)3X[$$Egqj)o,G
^^WF-3A(C>79r77+6JV7B/^Qt*e"P&)\Jl12f,:n@&diKT6$4qmLtTa9MB:9#nXKTebS>Z2>
]AKG8]AXm]A1Y^<j2,l7im6<]A61.B5&Yn;q;6?6r5gJII8K1#rqV0F^[GXqN%k$FGT`g"Xgku^h
S+VLRIKUkNT'1>cash[Q1FsHB10n7B(t0-W/gn?Tmnj<rb&C"mh;gfossXC7#+!d.m[eqC?(
kQD>PCmHDN^p0.aWmkB[(tFSO=p[,()eqps%0^AjUp2jaFG`n.D_s*mFl5E3jcZ"X"!%a]Add
s4#EE!Z(e4$P'k\nGe:SIi;BG`b0=c%H`E6Iqn0icD#f<_>f5(q1"M:Ii;D<&)-t5W?a>kkK
Qp[C-p0L0^j-]AK"Pd54R'U2OW$EaJc@9`p&KK?<?26,2Vp,i$@L]A4WA,K>Cusj)^t4iLo`Y%
PE"`\.ls'%)j1cQ5h>$HYIlIA=n_]A?+DZ"_=^Dm4/pj]AL;B?@g"[NQ$;@*5pnpS3L0LN3l0]A
_;@*~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="411"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="200" width="375" height="411"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA13').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA13"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA13"/>
<WidgetID widgetID="aaa7f1cb-21ec-4c20-8e61-7d30e705a23e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[571500,1143000,571500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[426346,3020704,426346,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0">
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="1" value="0" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA13" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tnm2=$$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80">
<foreground>
<FineColor color="-759737" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_kqfc_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"(!;csl*mEr@ZTl!;96q#b[BZnh]AXC4slO[e?]AZA5qT:dTtH$cisI!Ljj0-9q\XBcC7r'+
tTQ"pf3W?NL:#N%>Q-Lie.Q(m9tt&jfAg:"B01?W6W(eZ1agq<klh]AR0,Cm\u\(*9+<uMbRO
q)&]ARL4lUn<aQP9"rVQC_SVPqPgLFdd_fB,Q"$\(F-AO;90_X%_R4'R99i03uWZ3;QWtjb_R
F+Nk7r5CjaF!ch5NZA.pCB0Rb,s;rnb?_4..j]A?'?^mR@G`TF#MJrffJMcc,Q#[sR[2;h-TN
VWk,:r&pP?Jh4!*R^$WEh?TaE9775#nMSte]A7qb<V8NOb2Nd1R,GcFa0_lJ]An9lZ[_[E.'*b
@S>;9.9PLH$P7%$c9pueFVo7qXkoBKQ+B5D<jd)IBru9)d9l3fmg<?F<hnR:c"D*gD8tm,bB
2mM6f3&!*m,CLp&5=A8CGi0X7bUSX`l)W&ok1uI:o/%[/K!>lTsIKFT'Eg8K*Sun[r)plsXH
c>C6f]AXu#g)d58&'^7p36Il<s1j/b%_[6NlkHP/hJLa-NUo7'P$T%n>X?B9tH7[\\n`^XW`+
W:PDFk&q<5(+]Ad-.CHWrFJm9q.PBJO,=#G7p!B?b$GEr@5A1/6)j-cRl97eb[pH>s2`AcMsL
(uSP0Vkn`JUq<G&2B(1K&s/e]AsWNl(ZqVUN,e?0Y5AOam"YG#Y$,fN#d+G)#5JX:kl'B%o]A#
ZXg6]A\ajiR@e7[,<6aGW[#hEUlIAU5)s--ch]AWG_6nLNh`(R3%JCV_qPm*JR1M*n66j_cJ7m
je`0b4Csouk6nE@nXN\5`)ONfK*Rn$$Asj165?eu/cX7"a]A9gu@\=4r><X]A1ARI[m.Bb]A1^L
;4DnsQ+-E;=2sZVo1&Si>k9K#6dA+=,EWIT7N6:4<5@W:3;^$V?@WLKoX;36spt=3rju1u*^
>(^cJ(cZ[orNWG=lMG#GuH[?cU*:pj_k@`H@;2C/,AJ.J`p7J#\G#E8-r,6ELr1BZ=2E(QdO
C4NpZW2E0_k0Y4=C!4?.toEqco@4R04kY<pmOrg9Zn]AAOL\+gt(p(]AMurX15W7cO*<NLlR&s
gUaW+I1rWj6$*LKMA'</-LiHWd`JKWSdfeKhCWoFMUGJ?_22uO;,8kZear)-J%'`4_*EAEDJ
H?oM8lP94EhHm7o1%em%9PAR/KuDKS*Y^\tm<+6Wt$jja8K`"q<>2qtQa@p349'Q7]A(3>U5A
f@s@;e5<TgD<6$WYOts`o,$f7b2WH86LJBda2"3=OA,]As*m;FHsp;:.koO(A:p-($4(OTLr%
%_hc_I:);CgKKf,n3E7(Mec_'J38EY1qOA+@[@n:;sN)$cX6[#gI^AI2.3lcXuktl2g,i0,'
R2lfs@-NbFkZbmoC]A3fDKh,DR&dEg:qYaM_*8_jP3s8"GTM9X3q8,RD8_EMfBhf<3UndVGtP
^4gVELlqt(r(\/u.YdG"h_7@4O)k["f)1-S9mM2N5$*AVW!!d=,Pgj*XS)gP]AASNS]AAJ!(U:
uIEJ>TSVdjBb'G'B?EO#;Z$hUFd]A_!=Gj2lbj/(<u1:?+lE9+LfWblIu2r9K^T!I:rm\'4j2
9$R1<8.1,O;r,e>F^\@)qCdn[8$A0YK7!="33BcC`(WR]ABMPJ%J^\M;&iA5nN+6obuBsFP5S
6kaF8ECl]Ai`p8s@HXX"2ONHf"CBg;M6>"GiarG8C)f=D:3%O'Sm@%40:f/]A#7!<[h9&Zb,A$
*9'?SrC-McS66F]AM4id%dhg5T?7Ok"*BUq!"oZ.^SM%U190*#Y*WXUl0Q7nPMK.WWcf^T+98
[Qa)/?ZVDs:ipCcQXTpmD$r&Z(mg=BgB\=[FS2BI\t*Sm2KBK)pD$3(DgQ4_*E3hgNY>8PHc
UTcP"N65%Dk0*9/q:;-1cNO$Ti"WVX@'qJRHMADDPelP[p1+nV4t>!7MjXpY;/cRED)TU%YJ
XL)MB^P=mbTh.k:B<&W#EU>?1g_+k1jXi+AI;:6RKOWpnW1q40E\DKXljLqA[qfSlokq]A>W?
S%soAd3o7Lm6hs:WJ+lTs9F/+mfXji>WkJN.n0'e^9ZD+go(4Ms2Y2eQPMhlINiAlQ7mG$*'
&_FN/:2ZG2I.;m.W]Ah>@u7S&i,mT6s3kWgNM6(n<=Tp;O<'ap==e&%pG*h+O27Xr!)(YI!fW
P[\fm.<XT]A%ad4J?eupfXg+pT7]A*RXM8Vb9Lm2o9`]AqM,B/ro&dn!hL\:3/&NU)eXepOGiRC
%C:^@>b0`t@9'^@oAW5nZOL4cn-!1L'tj#e;@Zc*ii,>V*.M?Z:=J*1)9:[6G\W#q?<Un&jA
cM5@O=%&M4snM;>0<iFSGo?`)$]A\jR8+iTuiDl8i^7s;',ak'6\_s/`rU*j4b%eW@7441?P%
kVo?-5Ul9MrUQP9C+s2[*H9M@3E3]A2L!@ijW#rP[X2Zq;^K@7l9_4[lr80m;'!QU7bH;7C%/
2Y;n`H&QWAa,?\(bHFN!KJE<g[Aic;d.p4H*SLoXf6]AmgVQmiopCK6UF[/"/CccIZ329INTi
<>X4:!^`#Ln;FRqfc\&NT0E+pmeeu5h+hh84?^!_MAt="-H*eCA7&OO.d=@DRP*:O@^g+DV;
nDR*&8*Q*L&eZfLF&7F<dMAaaOZ:*Oj.OgHs8nk1^C7]A1QjV?0`n?c42AbMte%c-iNTH>[Qa
2JRMV2$/.UOK!EiK6@\$RP)^oaNM&>90bHU3HS6f4:cMRe&D)0Y#J[qpkVk:13_mPR+n+$e>
fG7F`p5Q2oN4O;e]A&Ct9^"&ST$[^uggAs=27`mDg\Gj;K2O8ah``lIX@_S,e7j9b?o0Qj',;
'"2^&m0$GpR?df`V=qMYEQBfI.Q3;;j(O!,69`-N"YCDf?n/f3#@0DpicM;nml[FafY&OcG/
M!N"!`IjH,@EWLuDDI#VME.]An(GR)8-_SM4,E%]A78./"sEXNLSo754Vbt\`T*4%_3./*T7M9
(:,nE]A[S*!S?,q+Pk>UK2m0S/IG5#k\8MK`=h^,>HnU&uT,b10<PZOqcH$@\?NHo3dEM+%:R
aUT[nIL!Pg3-1ug?G6u%Eb-DaN_>smtnJ8UsX%QuUb5K.h1H0CiUUm1,77(_8.$'=G6H.dk'
p:c[:>@J3m5Yjof4PU#n5PCOb+h:@l,3c4+0Qa,?kVIs+'(HR52"]AXj+h)@C3#Z93A6=uY(8
5Hq..3<,Q7Qk^\/\e0d2QE$'fL)q15j8J<@+FNVg:!K9op-('t65_m,I]A\om6E>+5\eQJ_BX
,Bl6^+*LD\5*pUj0<OjTcD4$o+N9('H20!L[Z\nogN[Je_YkQP.Q$IPGmmgH."4k[\20J$WD
BS'fbcUadsB09G<unj?CQ@:D55]Ag`Gip@cOJmCL*K"1MEa3HQ$m\$BPY:\$mnReMJS=0)o-2
i0]A.@>hqVHT!QrF\QYLBS;O&[&=Rg$T2]A-,QA'nJ%YGYY'UL@g!$c8`tL..Ri7fP-Lj1j-<N
W6"Xoj'-ULor8sV,@0tj?5LtILDGl(R:@ngZuCP1!@Pn/Ttp:cLB([<WE7F!!*?j!!3^^!!F
DE!=1pj!YBk^"Q9;1<#l&g*RPg15A1X+6(\gnA1l`")4AJrhY.2]A/mbWMZ!+e4"%[qF8:Uh6
J-8Dn"A"%G8:Uh6J-8Dn"N^Tgf9Hl%,3qGJ?+a\;4Tt;P*'A<0T)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="116"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="84" width="375" height="116"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RQSX5').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="rqsx5"/>
<WidgetID widgetID="8f84bf28-639f-456d-b954-83ac1f6d0375"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="radioGroup0" frozen="false" index="-1" oldWidgetName="rqsx3_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.radiogroup.UnitedMobileStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" leftPadding="15.0" rightPadding="15.0" topPadding="15.0" bottomPadding="3.0" buttonAlign="1">
<ExtraBackground>
<initialBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</initialBackgroundColor>
<selectedBackgroundColor>
<FineColor color="-657670" hor="-1" ver="-1"/>
</selectedBackgroundColor>
</ExtraBackground>
<ExtraBorder borderType="1" borderRadius="3.0">
<borderColor>
<FineColor color="-1577998" hor="-1" ver="-1"/>
</borderColor>
</ExtraBorder>
<InitialFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-6577229" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</InitialFont>
<SelectedFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-13947856" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</SelectedFont>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaButtonTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="指标业绩地图_客群" value="客群"/>
<Dict key="指标业绩地图_两融客群" value="两融客群"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[客群]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="63"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj7=document.getElementById('KJSM07');
kj7.style.borderRadius = '12px 12px 0px 0px'; 
kj7.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj7=document.getElementById('KJSM07');
kj7.style.borderRadius = '12px 12px 0px 0px'; 
kj7.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM07"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM07"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM04_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[xyjcngq_cngq zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="重点指标"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗1">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/HuaFu_YDZQS/3nt_Menu/经营画像_弹窗.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes reserveInWeb="false">
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<a+X;s2`@D/X4+W`:A\J_#BL5TE%DK\D"b1<>E5PsNP$,%Da0X/Lt+K-2qF,*4S'P_:2OMa
1gU6jOr$$B[=lL-PeXaEKuDhV7_RHbZuFp&=QbS@)JbSEHF*GP9n>ZhOIdI7VL<S'-/TO%A`
n[;2tb'f5_m]A0;Vss*a_M0_1Fll-3MGI'"sD/ipuedANe/Mu8c4h\\FVQ_GgsWq/&OOL5bI[
aj*lK-eQFND5?`)n)D3FCIMCgF^sN@rC]AY*S:VXjF:G7nAOKk5!<o[7*PrY^,U/;dq2kIM]A5
RGYP]APKi814%%SRX'(5C+("\a!2\:qlFBk-:U$%'bGC5@iik%A::AGG>7glgC!bAh"f'd2)1
:Mu\c2E1[V=dk[i`.EH(h.^^1*1AA!MSS?<.t?EY9U#tg/q<gd@o@]A`Cs<I]A!H<Ib/4)fg\a
TQ8E^kd8pjr3Ze<`&n7_\gm2XD9"fPd_kVV:_A7=.]A8E;.N1dtj('_HG*]AMJk_4GO>Ve&!]A"
[Hc>Jd<Cd'1hlsb0#9E`WK)Ru4rBF(!);fgi7kFKOs1]AB<6uq79dd.79ctWg#mleNJ=Jl'&e
c.^WieJWDg/SkC/p\b^X4#Fer$:U&F\oY`*Iqm>YK5Bs-IQ;^jn_Y1X:n/?T5=&Yrm^fg.RL
Cibj(*ra3G+-\d-rs"RGYuHPfPV=n&LLG2O=?XMiPGHkZ-Wq>5;5J"><&+?PndlFKon=I1>I
Q5f_9iZZV.48UQ"j0HK240nijA*GROeIuNjoFJ*\*9Kbk#(0Ee7fk5c'[Lp%g]AOWF<nD\)XV
n_AlMGL<$c8+R^lqGC&/mWgGg4q9cK"G1!<*_4s%C>?dV2D34FLQ?)Ai#B`SI;JM3d)iD[mP
e"q&YU;.(U,asr]A=N<=/%9_i3?9f_4b&!A%VQ+,Xc\rH^#J,h2N)p[8B#'I%Yb%L<+#_AU1o
$&W[G_>Gpf$W\_G1MI2`iGK)iGbIuCr9h:0GTTani.K`V=t9KNBWlhBml`5k/,?u_]A"_Fm)P
oE7V)<cj^-$2&Q.s3<Vin"m.'U;ptQ?hrT>HD\QnYMrWJ)LT-h[7SUGVNC7_&29ikkdL2^44
m'lKMC&7P4++`+O7Ua>"hlR5Z/,(]A?^L_,!T]A"o\YE$+cdV-R2$=]A,aZRg<FYDk,`$DjEn(%
jq#N7GN&LJG7b+:3&B>740VL&1cQeAS:&C(f-E"U?th]A+RK<2R(1tLPh(\2HE?VZr@W[.@pj
I6O9nYj?.;RKHlR$3e$3:8%:Zbh6]AZWo*)3RCK#?+%YGKO_M;RT<t;\</fp/;gE#MBKOAQ:<
R%%jB=knig(oKZYK]A#AeX"LHn+]AaeQ$orb;(6:<$'"&*jPu\1qn9JjmP:Hj=;X*'&20j;#@T
)B/6rOlIi/b1nYoJ_4GQ\W_6-8Z=,1kc)0C64iK+/'PA6gd\7g9'UY%?jR%<9b`&GGnYBm<^
-_t"J*c4=7>7pfn5W1HB3"T4T[XL&XXJV?]ARj*!]AV]AqU"JC4a`na(O:Sm\6Oer/!fb&=TJ6J
."A,,L[s)e=Vp(\*r$[4,o8Gu8HX"%qQQq/G8?![G/_?e@&C3_)/#'j)+[1##ao1q9-g%AJO
T%sB&nI=3nalaa"Q3=:4CmLO6`L:72lk7%cXe`KX[J.2Qmo%!rOaV6"FUQR62+(n>Z$NVR5:
_>LS\CIo(dI6Vg]Ah]AjqHk:Lb%>-KVEJU?^NeS'%<+j[+2tHKj3V$-#eIsU32[tO*:4s2eVfV
1=f3#)3`E#@^A^<0&brcYBh:?OGY:[,d3p@dKr,anA;b7:dd<JPMYD;).o_Q'iD[Bs^[=Vb5
X%u)0ogu?mn*4G!-pmRaGikcDB^LpF:Bq7:!gnfP`pL9T;jRl&fM]AA$`LFFN"^Cmoj`,t;^2
efd^DAS.#m%:Xa@N0fi\nDk3RN)0^pdN).a[E5q]AP*eC<>lp$e=iVPN$)K07VWV*9V)[UZX4
6Ft$[UIIZFtf6A-N$-=7eY++[h?E2qUn!+GT9S+5/5H<Y.,d-g_.%<7.SdUINAJi@mfX>Kii
]A&O_d9'U3QT$MeQSNqlm"2&lKN+(qb[F<TQ&jZ*\Xq:[:Bthi9g9lu-%DAA$cLU(FjfOLAh2
r3+ESu`";?r=H2n,))O$`F-a3*pQt<gH?+I@GP<D[^=YI#:8JhBhj`?1E]AJu$pK^"&EQta?r
6`@oMB+N'_k<ZTJ@BG)E8_\hKRFF\C%gdS&6$7T9(3cS7K$Z/q[e]Af<"uD9Igbu8m,o`^p&2
o(!\)Z3FgJ'WqR(dOBER"&Jg)),o"(299qIV\Q=l;[1p<N-r6;G:Td8HCJn^2ea5m5]AC]A8,B
DlSa(WhJhKP=_fi;W"oKK?51u;&<SI*!seG36cH<B"\/JoEA]AoWT&?_7(L6Z5`:L\';Y.99S
>e0;O7RK[Z.&4Dd"G9Z>!b3^]Ak?tOejD#*rD4AB<-j]Ao29/4?hl$u;^*V,CVUe3*`<JpSoea
3Yo,tP1gLa*R$2gA7F3b>;E[g.<Wp6\[7[;/qg)01;h9_brc0[WlQ,N9tHq(4V4H$Mke01C\
J/DcJ_k@G"%GH"W;G0@Jg@>)a.7sg]Ab7'q<F`ruWe4q[q_b_Ud(TrX>KgDXG2pdF/?*1DtV%
VE]AbAt6;$*"q[Q<3eBNT<;6__Y+'eXGmeJ/`s3]ArDD\&Jp;jSj<$rb\X9`6)E:=;9"s]A0>N0
J.iN.Z,Bu\ca7FY)'e<Gsb'`'PgeQp*m99W-_:Fl[?ZY9_rS10X8#dd/aa4`A_UC\"M]A<>6#
V+%$'0uQQ9"1u9osoTSh@9-,Iml'IB!nVq88X[-UpBg^DUh>-eC:'d)\Z7R"_BoCo8brsV_Q
_Gh!.2r>%qk0^.0.DR5[h,cG51FM71@$TgFBX#-*udlb$Dm%Po$#\m[^2aSmrnXTdANK#Jdd
K';rc]A[Wpo7KF^5N&5is-)O,]A7W7mt.IZL*6aR-VrWPpA'm/`TJr++5_rBI!QRX1%1e"Dp?8
u+!h.ThI*H=.t=DSu3WUJga4^+0L;B2.O:O*Zqe8pX1?ZM5SNe_?r0shD^XCF<O+EHl5NoUC
jX$SER^U/]AAPH9+X&kAcA<nV0^YXm%<Z(hkVK/FqeL`?]A8r@^oO#p''0VetK[\^Oo+H?GiH(
ehl%R6HI0\S9p8?n0miEN"79[1L9S2f/^]AmHO7I)C[$T\S@4"fe<8Os(JoU7V-oN"`F_pe^:
l%bke]A3+n:lGBj3-RiZbBtVoj;N6<S#;FL3jAAk@P1WYl!g`hE6[m0q`RF1P5ebJ+%3YnURT
9<DUY9=R[tU5'IF+e$Qlk:_L(:SP_*i#@A[%R4hAMZgq/E/fX]AmucF(+jBAjH$R0c$Qr4U>%
lr,1s+48l\PtZ:@*>gg=m;q>d>8pA^#4M6SoMj2^mNug%inFTuh0Un&b51#i7)m1)^F*q;0K
#]A6mh\6V:2XH89V;UW[lo+&B`GTKD;C2aVp\k'#h+prtYn+]AkqreV6deM4?,A@bFZW5*Iq?A
s,D&#29rgKA2/gW.A)#)?f>_GS)]AmHaI^.cJ1>aNEUgWfs\_iD"`;9/\R@A%1seKPA2KpO"`
;KZlnG^LTg<C9DOQGmBO+46[2?[hO1sW$W*6<WaLol,<C_;>85Yc0aZu!^s@d*%2gd#NR)*J
RDBR.e_\.c+mN(*]A$5.#jqa)D_Wg`NMU899)mKXn)WQ.`*9-6G=rWefl9?;o-'=Ud0=Fqu[t
3Is>r%oS!Ws>DU,gZ=Z1m$gF@&Y<!D!um-VgH_+Qc<o'&VJA$(7`Zd_&Q;oKIEiIn@c78N,W
n7.8O55CP;B3BQW.'Eoa#.s33fYheeAYm;IFX!SSCfGIWJ(u-Adf3^INl)L2MiE7Nn/U;3B=
3Z@J?JMsrL]A--i^OARoVt.hW6;)N:B"K)SKF'T=d9'unZQICD(k/#L"5FQ5;[nk6bNm(6QBm
@fJ9tNF$=S!.d.0FLq\jcWOuNF;+X!&Ikim_n7J>+qhM12-pY#.d_/2m94pun1DT>'!'6W*r
WFKAr&F>/9%Q#H]AH+N$i2,-G(DQQ#DVOrF`(Bh*>T!a(D*m/rmdS.k,85nK13S(V&k2;H&FE
C1I",E3CUPRWe0'EVM_Ch;Cp?>u:7l'$Y19C>*d:u\7Iu*-P/\eBk;qj'=CrNs^!d0ub%U^W
Fg)KXgFFP1mN1Mg#99FkW4\;h.Eod>H+bb@/0*&S^8oFc?J15u3?s8_CRA!lF8Bqk1kG`s.k
cuTq%"%U#mb$:?pDU\RMRm>7?[mHo-DYJaQKu#4@Xe8`\!>qG/$[EK?0&F(@`I.h5AQjVp]A"
fTpO;N#r2Nj^Y9>aGjrHY(5Z>Ac+D8nY)[p/3;>PSnf2T[H7@^f:F\8X85J,^J20l8h>0HJC
V.H@3ITYo@?3Qrg45pKiR)jM)9#1nn+9@"0L<Frd=lN`$,K$_dD8St2Aq%:>BTIbj^ssU3*X
#9SO@Itik6%A?bPE[V/(>POjDu)12]APL]AcW*:JTA6mn?[\pur/R_Z_Ya;G&U5ATdtO`rDCr%
Cd)IR@ihW;d=\D0:b4TGkR\!p6a#BX%G"8acU2;U@5rW=GP6n<Imn$$m%[n%%KpRTc$E0"-$
srh8.JaaZbtSCkQsV^.mS81VdGrNfp56:TII^L/K'n`1/4!!]AlNm>BamM#D/'S`G@\F17WnJ
-ccK0O_"cg/tJbL75Y!-"b'u7>,QfeS\EY(F\cKi06`d"N8SX$hD''+_'?5XE49KD.If%&0S
YFl@Q/bA::_A*sOiP[rE;Ye:R#g-csbgFYQ,tQISQ?R9$?/*;#)6s<mZb%D@]A1(8<I3)H;/d
=E1,DFHi6M3^cYMg?c-4H3)No+frO)bYoj'QHHpP-^Oc_Z0CJ,a+2W.$u'boLi7TI8N5LKnQ
*UJAA['"<>GK0I;KY)i'sm0C"o.K1#\.(KsnnlR%M`X.1W-#Kt#_f[rqZ2bTk+j.%nPL0d-@
4rDR2<DnaS4=J*80KHuUK-0?SI1@mFfcD;r2Z8hDl]A8+eUK.dj(IFa5,ZSc`_qZe4j.E>"g;
1KNWOen9T+FIjq`uNjh932G]ANH"qa\!+[;&g+dp#2Eqh3?F2Gah3JpUb/2O8/f7_6/pF*8'b
U'>j?:$#X]A(<CNI+@880]At>O\07:@k#sO<_IofYF]A-]Aj`aL.Q:rfB^`=)a="+igF0W`Fh-pO
frUeo6Yb'nm(Y>f:KO@ChAd''gI"Qc=N[osg\^+Z0!%SE>tald"G99hFu,<hm]A&?E)I<K/71
O]AmmqUq.E9H5^uqVAXpPEd1='`ik;Cm#FLG]AQ1h]A/Xo[;S$0tZ,oZObuVEieOXeaAO5))h5f
483II^k(`B)&t8)1KaR'Q6);7j]AU;W!m=E>TRq/g\M'%6_"@_5Ma7!POZj,Ja!<4huSMUl9;
qqVa!"#J3P[\Y+5Gt<CA@*nu:V<MAG$7*uC-$YNjITFdhL5T\uDk3lAk\d(:1./icZoP)41>
\'oHuZ6gN"9Hhp!X^u66]A#R/ZT/um7^p'?Vn%=lA/'T.@QRMfl#p1VsVRRE?I/CW[j_LI%OI
X"j5?J0X3o--9qqL,hf*`f(au`>.dYJW/O4nk81UB`//KsbclgDg=cjmh1X1[gQAaBlsq*s/
X3I->9Bl/%^i&`/$*&pbK=Q#'.H1?7G4/%2FRLIO>(VX!.H`)Y,RG#i`TK`=UQRVsC%h=C7@
_#kDh4m54qgb:3p=V!fajoY#CGBX3HQumC4BL%KqVgu`K%\;:.8^R^Og,A"%B#GXe.oW`-_P
@ca+,RCJi45=du5Am'h%14))iu^a6nY[+$2"Hc"hBib$5L&9)WTZ=R$=^?.3O&`NC:0T6-M!
8^oJa*R%"6HRB:]AWrs*.\i9K>GiWUdN!M00\o&%D@"$]AOrF8BnYUJ)n8eY(9m_pY,K&'+FV:
!SbpLY<K7`P*!3(H/*M-Xa^p,DHTq2J/HE^_1[NY4)Em/BOnFhKj<2Io_fq[L&UC,<L*Bnb"
K`l(Rk7me7>$#XPJG)K[AMb''.q9bu2paOOk@2H*oIF6W!@T`.;Xm7&R#LlKXfK@0GU2[3D0
KJX>/,%ZuA=]AQ/'IK%(eM*p9CO3QmchcUK"&OgeLg(QKeA6$%6VQPIiqt0J45!eC+@+\"0Gr
-$/KIH-V+5]Au`?--=e=5Yt3_F-b"Y-mOqcKLk87J(E>;e=$9NaPtYTWP&+qo,dA>m(!]A0Kf5
iE$:%p?M(ckA]AIf`j?p;nk_'g\Ac9,a&0\5Fu7KCY6V)=I0?pZ$/2h@)Ynk'gt/h2VU<70m&
<=m`"q$-MVh0#&"8V.>YPac(cBqV;>9^tQ<a2kHapD?UUO"NH0d_?YhMi*aj>Qn[;N(o'aXu
[o%NIXVHDhp@_Wcp01-('>Y=`<gI2P\.4;cG#c-IVq;83X)-FV8(\;mFMTTO1#4Pr^F)%KuN
;[$O[DO@e(ak<LnK.M(s$lnC4%"krg15!k,e7Kd]Ahg:BbUk_\gL[7o1+*L"jLZFD5RPrp:'F
j6/0J=[,jD[JNh^U<C7gJ7`'7iI4Z'B8hRl>;<f_bQ<T[a8XpPEXI?'`fa<AoCb\$LBA]A&O<
!.E=7-,R3-3'o_)SX3Tg%dB_0_oU<7Tu6NAr[@6&d>TZG-ZmklP7#eC$YrYTDI-U5p@Ol/>g
B*u,1uRU`J-O8mc!rn5"7DAItC*Z`M5l5r_kIDpYFbfP(aN>*J5Nj*!"-U<P\l\niW5*BC9n
uZEBXt"p*7,boK^IM,`L#Pb8Dr[\m>7HtSWu\J6A=cIdGp#6A:94D^LpUqD%6KURUeNqrIpB
4^r(C>)_iZ#p)Ph`:$6nmQi#1.^/2B6;F6n\H3>GtR.^2**c:E</JoY-&kd`=&he83M/6ngF
P/`aq97mBo+;S[M,&a<NY`?R,U.ee/@'F5:S8fD?98PknFZYfo$nMN/S>o/gYLb.WU+EJQ#$
&cF+;kFoBPl?!s<Z7:P$d3&bKb5BM1M_2[S`(=RV%3t7r9I9s1h7-P_7>c%pE$[rkaBar<@r
5N0G?e\aDg!H&'Es^d=EfT^K<GB!lLr<NlT'03YF)EI:#6fGB,kt)'h=i?kG]A?LJ"*`Q:lL8
c-a'8(Wc18M.N'5mdAM3(FiP_bgW*<b=nIQ1&Cal.o+h"hUC`_HB^6B?`Pk/Whe_PfV)-9&q
U\cnSR:dYl;.#o>j3urI.NSMIDO)JmnJ;WG4?sd9j1baU/W,gD4^P?_DuI;n#"a$PBTpk!"-
H$/")N<6gdIuP;ggl>c,s?CKGemK]A@ACaND_EI-Ac=hYIKC!!2W:ek-,8G7CrPH(EM"eG7=3
PsroiH+l3]AS62,FdbF*:fLiu4iIhn7IE0T/+u=`TIWW`O)iXsG*'-*E4m2B^cA'm+'A8S,L_
D-41u=\8Y+RG$dY*88SgHB`@XI2FXEY&JRu=gcHdF?$'!k[,R_l&_+mc\cqQ\LR1a`#gqQtt
KPD^19aR8.^)&lC8['+arl\4:=G45'jR>"Rp6I^C&^;:PkU:)i@B:$]AX7p_T:$]AO@U4C^Kne
f>LcqcOU.FVmhTGhT5gB)1)s+?NO]Aj@JOFF9[qoSF;RBO6:-ka59]A9Gb[9@cuoJhmW^FBlD2
kc5kQTVNZ@?E"WR:l749f[22V%5T!Uf+5R$a9PudhTD+",,ZFdoEqd[+PS;\\t<Su4L]AY[[Y
rh^kGklt9s$>?6g?FSPbfNt[XRG;eF3q'5+0YT7lXFhqQfJ_H`OZZ%&d@NC9Ji:FH)7+hPUi
O)Y6<H;g=$c@:@gZ63phSr-a>.q/TIY@C9bg_2;5mFFT2Q5!73"i'ddHuDN3=lh[/4u2LTSp
:j,_q>Y!13dT"Q(B_U:Zs-oR`q/PsI@0BuCu:9M=1Uo*V%F4PTugnE_">+>AacZtiqH<fH%c
3D6<Np.9[S)O7;.gWO3Cnn(4MKGBe;>d3TZs;gZ15TS@h30f:)cirtdSRmuaLTORiOsUb@nu
R=`dHLdkA<ps[4/a@,,i#LH%4p^RRObq'?bWN)omGp)_$IaT,0'#Klra<DO7t?CiAu<4_B+/
$=/R&9]AN5Abu:I+@h/+LYqlp"R-gPC`tPIt>!21q@]A[9M1LsrRi;2?c6+^e^-%.fopR9&dc\
nQ,kmh_h//,X.?)Urf+.B/XlDIV#?(!R9"2)7GEm2bS0%Zp8R9,Us9,K_&;"HZ`h23RUYb<W
\Agb<9n]A'T=+))^rrt:"c&kFDp^kf4.V*m)2)-[/*(kk@UGkW7o2Z-[;Z)'lb++[;L[$qR$V
RO/&"]A?Ft_,jl-LFM'-6XsDeDer+1!hg/+]A&JT"iO9'ORc_S\!c.'1*5VF@gOeRg4m2qekA!
kGY&cj0QS$H5#'n_>CS32cD![Z$Pj=fE[<\%4IQ`sf&/2[XYbX3"J;T>gXVfZhlbE+mW/2^J
\`nj+eO(FB87%);gt6A26h*^GhLX7r6#2n&BBkdHae#ds)VVoB]A^:4u4q2';X)i\UC/_[eq<
6Hkb4+_I\jrs/X?>O"BeS0+81W3?gcW3uSrH=6>HQpnB>8L&q7a<g107'mdX5dm;_8d@O)Q^
pXSO1\a0*"gPgeofNFcC&jJI)&MBD;l<B=CP1i^t7obaY^iFr'h!6=q9S\X("D(6b;rY8a)j
aO'baneqAG9*afVd+/T/46F7>.CNaGQUcH3C?$:m8p6oI<V*/mcd:017o(EVsZk%pa&elrgk
-i$I68q>O#g=gVBr;<+n&UKH3HqV\@n3/%\;d(WL#)[gQ5i@FAi^1`sk"WPRuF>:U+N2RRnl
G(lbYZ?rBFNU)<=Ec.`?_YG'"k^7.0h!.'K33Ddg(gLgMVlN*al^,g_VAfOpL>lCW>qm;-dY
D.!HK+Bj*Z%_OVs>PTA@6`+]ATSGjlB5d!L;;#qb[FCgRphlV8-`7X3qG>@HQh,*q3!lD[-s/
=OQF7%V*Pa-'B-h2>g0r\=.t(fDnYj8D%H0'ne9c+i9aa#ZEni(*r`I_:0.)97butUICej9X
a+]A`<B!<C.q&lQ?r@VP)f4dslAF12`-e'*>?0^n+TAie.CWo6L#Mtk*NpRV.c5VJ>C,=Q[^]A
95+R)W>_m`S5TM-\j?+UpK3B\1_b410a7Ekql%IRn9:5$.(qg0=2]A-@h2YGG)&&/\n1HG3]Ai
Fc"*GW(hHe"noVbakT4sri)fX>Zd]ABPadO`*U\P>)Dt_KL&@BZ),Qi=Jdt"ZKk/E95p@J(fO
+Wge\t5"!*K.`n:\:Ddba*l`LB&,i9_U\DEmhf9rX2qRE9b3=]ANdN,bAuG+D@E14$o[8'BP]A
eX@H<FbIMf$e*O9Ho2h%[lkr`V#J+pY]AVlode9&O\]AA8'b_:ljR[Kp&2A/CjWm&Z0cE.'1]AZ
o3c_ojcK7*IS?p><9"P\2Xd,4l:j1?d*Su'L+&9#m!S79P:<[FCJ3Y2Yp9g7b;\JVC-6F7;C
G"SD,+W(#9mKa3'-*V@qG"I([(edGs^LD5peX"Ul]A1ek%"L7Eo>7ofXNPIJL5pPG21*)XEGH
rF_$0m)7Z!##-JYqrE`o8F*:/qii!ks28!,1n!=tS2!l8GB^8@j$mTq>5l0[Q2Nhor?oq4N<
oVb9;aXZK\r&`rFitp@*'KiHh<Y"n0=E&%"C@F?;:);?gf%*ESZM"cBCdiHgj`#eKW1+bY<<
?L<%6^b#1Z]A<aEEJ1,gIM6p'S4f1X,*L8d5TC(m6?q.T!Ccq1k"`-MiP=t$8BIS\p0%OBXpX
S`t,o/j@N4aWPWfX7;E3/)Vsih8Vn-p#heE@rS9Z_Q"qD>8FC=6?%U=2jRgTut[57nhKsq:c
0554*RWKt;)&Q7Lu85`ub.^3d#Y=;[@di88Ae@3sfZf;U/s_jT:k(8l@rFP*>Q`Q&=W;sWJ_
2BU"G:YHZ@)#Yp>"6no$T6,B/K\Ff6-1-8QYNRt9+P7hoBm(q9CQmQfoY%:>8Z0C&%@+4s$@
;=@4Ir<cHW*cfmY#f,X3*2A<0/tW,sKTt[[PdE0j4@">,+!uR+5HU<N/P'V@ZPHRhMTZ,pY;
ZA$+>,^42j2Q!SlICpr$;r4jJXdN_HuM950CAZ;Hp/2s1WD/gG#3lB7X%=DL1kd[>>M8[K^n
o8nbEQ,EgMu8,!--+W%aG<e>h9h`f]A>3p#%>H[93:e)oF3LZdgr>I)m*XXsf:1CD"F.NeoBF
RN!"/UBeR;e.&;]Ah*I8J"rJFtPSWN.B+U1j9N-t8c$jB6$;jp'Q8X[BC\EOuZL&]A^I<c2CMW
+E,1`C2Y&u/;4&BaiNSPdbDTYY_T)d9n`A[Hu\cf$9r-3GJidnj/R5SUS32YNnK"g[#G*\Mt
0S*)"os]AYcMU=nmM:O'>b=?2!WK9bg'jIH?pXCV_^r[GDho)isjVch%]A>WUElqmF%0knI<@`
f]A;km/K,ZsB%3;!!]Al__Z&7+q':24PQV$-ADZ4o)kQZ+Yr@$<;tLu]A)h`EFmZLPQT`jIMUgm
4d\/]A)=HUR"?\bGaIF]Aj&TN+Gcr%%=3cpsU)DHhO,GM@15B:S$kBVTeZI_j>t$aWE-2l18j_
jk#?+OS/muB^C'^K\[?j21V[6jG\k0)V^t-MSpe?*hQ_>t03&S"r02V?u]AC`(_eN+))XQ#&t
CfJ^+3@@bqj5EoX\iS=-`_>S4\oG%q>q;m)MtEiYp>N%FZJ'W+,LsH>>)sXkgM(<&md6;%a2
aa'\tH%>p=nCun^lla<bYi4o"Kp_jm',D>Nu$0(u,2\5!)_Ko:aNae63:%\PQ>E\O2[)/km`
eQpV%q]AI(m<3IfBXR2WM;^gu_QDpCho&DTF]Al_Z/ra0<^u1K%m^.)VUfli@1E\*gK<*3nr\6
&ilpC0AqTT^(2HOa6pUkm=?+6e1pZ7`QQp,HI2_VqfGi?."^\r8!K@?*SM""fK/Rp-6oQ(:/
pAqn*rJGJ74T2/1'WLtP9V>k$#PL!/moOg^`9jOE#RARgIte'T"X2iiYkcH4nj(tX]A:e\^#4
eB#\D1"CkKH?'hqrM;rWQ!AP;*n]AEt-;4LroZ?^s0*f8@]A[m#6aiGZe\DHi<")=`k,-^1gE0
cRTT0WB-5RR$:(ar%5D=c<h)["c=*I]AHRNP4X5aLdPe?6jk$01U9O8!5nolM/DR?\!ZQXAS_
ac*V!$c4l.#gRJ"a^C@CHeh[@Oe)sk*G4->r1'b,+7H,ra1&bOL7cFrrAfUFBcjKWY[aBSQ=
::NIHQ_X0KrkN3$KlITES>d/!r%`/#0ie`gl`5AOSC5QqhEup>l^@9ib<sL!\Dk<7Mt^?#t&
Vge<9.%[+/7mR!"n)Naa9M4:WaIJRU6G^qnc;qV5]AVj`ra5>HGuGm9rrco@16Y5*63d2hT$_
P9\3Q+/cJ(M39s&:A4YY0H[<,Hu#ZZcuM(X3&)t]A^G\!YA(]A[M.X.eq"H]AUP@6Yj[/""tKm4
^\m-#OL7k?`'Ugn%IK+"%a]A?P(6dH3(pNa-eJXT#t(8Z@0efp(64pRYs*k>bK)mmM,4P*@$B
p;=nt>=]A]AgsXheZE?Y\qTFjIN165^_>Pl;C,4q.HLp!4p_:)=ea/?lah[k,ih8UmIdM]AO$["
^dp4[`Anm3Wkcr;9gnSHL)u6+kI;A=KNDY#">%&^2C='g>?hf\DoH+.!QNtZ8'`Ob1?1>;Ts
`qH^5:dnjiNaR@sX%Ns<k2pgNnT+"#;+-RB>5-D\7l#TbMKKXs,>D>as29/)C:e32"#$b3BV
N7TB>>5:8>]Ack6ac<>gM$b>r6roJ3@[o.V7(@2kV3"GAX-ZFJ'K4IM.,T2aR8VSKXOs.r/A4
SI$)>^t=D,X!LQ(=U;;=J-uWL@`i&,YtDkt_cf4?7Lbn^!EFhG*iWVpZ4Xlt*),'E;TSVk=_
'J&'et873\s&f:q6"@`G;JW[`=?mIE=(^Z-(7KOfcOhaLq,VTjus1_uiLA+8r:]ADSk0g=.a5
u(lG$&f!V![E8Y^dqiY0G53/N!)TO,Ctum873\s&f:q6"@`IahdF0jB_pjYnbGV9[rJ>-[i\
8%-@q;p873\ss,/T*G0PD`HgQ`X]A2ljO(?RMeF^KXqj8T,~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM07"/>
<Widget widgetName="rqsx5"/>
<Widget widgetName="DATA13"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="2" tabNameIndex="2"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="108" width="375" height="647"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m99<iP3MgeVJ4c(CcNjp;l=2I-;,ZnGB7B%[';I2^iIUa1H08?<!:oebY"E]A,o:)391oFX:!
!jA//^am1.4QA>`]AY^'EJ5c"6OaOotA((S3Ps$"4tNTq7Q1&gs>rSpZI".2sZE`Vt'U(l]A$j
^N)+bFS2E'*D6<i?rERr@l?_3#A7Qd)dc5]ASe<1\%lAm'%(b]A\GOgt.loK?Ek$;Q#X-Z%+^a
6e@mrH@5tr4=hqbV":l(ifG!p('6o\ia0r3]AckMq9\62>>9.N_kufC7jVt9(HME_rr1NKd:\
rCU9=FNjg`JEE]AT_V;Tmt`'9q86juuf*=Hap]A#0E"coHTO%5rD`l/-o,!'dh$t(#-c7'Ga0(
IUZ>BbtHV^nEN"ViDjORcp6mb*T<Xj>4:$E6BmGro]A]A+ko\t7uH_;a0Cgo%KB)`Y420e6n?%
\QcIrFOlh6b1\["&J2Tg&q&kTN]Al'AJph#obBF3aDdM2>VmOSZb)L89)0/obhA(eQFpu=@M_
I)L!aXb1"-!A^Rrf*IBP'_8?R9$bU<uJu[gF&#he06$8)^Au&W[2d?%(iZ#@1J"l8rqZjZWe
kC@;^*5X2&h*gA]ALg(ul1m4a5_2kn`*[;E:!Ch@(<YL;,rHoi&Ibr3`FnXL[U.>GroZD\XRc
Jh<620S[ch@RC=(@b_D_XpJ:H5Q&NW:m!HX*:/%O[=1iLl^Z4K.^qM$eD@d0>=;.BS!*t(m-
D:^i9Hk`KJL/DJk5fnREUqX5F>JeUk)5fZYe&VY-Zm:[=VKipb^EL^3\Z.3WRX+X12U)jpF&
16E,)4c<LDQ*$JYF3M%61?mn#eP2_[BD9eIW_e.B-dDV,957+O6QBU75Y4(9[W5Tl%B7i\-c
cW.ad=S/Qm'I2`1O1O#.!960)Iif_&[=\%Y3)mYrr"R(.I^PZHXB8WJm3V9N/-3NQeGFMW-!
JUITQ\S'meZOFhn)!I)d;S]A;YI@Y=a7CYah6gg*ARb\RZ6$_9KF_]A#bMB4rA13]AOQ17,a8W^
6n>.!ehi]AnLKFa!b=MZE,q,B)#cdSc:7^"<cC,O^P/c24bVB?&S1?*E/BZ6R88]A,6d.`2U5,
6`V\-eU)Q:;V-)kl9[CY`%P3G)I:tI3aX1kj2A4$iq%THm+P<&.<>,#BY:@O,:0XXAkQEuG:
-cuf+q-,$IUh6<)e7>FO?^6a08pk_@sq`\,a\2(aTbREW7P-8l-J1cWT54S,JAp26=-8DC%l
G(Zgk:`?l+qG?UjBI>rFs#KCVH%aLgjACpD2-*^G+5V4p!]A#ZTh&F_thm[:+I<:6#E>1]A?-f
6:53Yt?t)cGN]Arm%u53=VYE!!MrE=>Le.7^^]Ap*VS=Hjfa7ELm.QRHIE=K3\lQNeN**(tQ>)
EALFs%_Nlh?Q!3Pf1'HOm%e<I!%nN6"PoY0J)h1E\S<]AMn5Btj<K_pUR@rNXL*7hBL@NfPkW
G]ABFnYG3?%$^GL=@N=RbW\s!pU=gG+<W)TL)fftTde<&jpBg(LkfGV[lEe(!;K>7rpP2&]A-c
7@qT$aJ-LbEl1/DP5-krq#QHt^VWpoN0q@n/-s,U/)":,Y&WXdt_VJ^hG7cuJlK1K7S7i#Tr
:[u=_/1)IW#Y&,e[NOGM6c!)uk_C'%^iT*+[Q5XCEnc7dC=\7csdWOCS(u&9*_ce,)BbVkMG
ib_0^5_hhcr7uPrYfOACX<_df^ZCA+lCp/3;F#.q@\#Up9j@od"Z3L^;9Wo&2I$h$X*1Di(J
Eh+i3:nWc%,qT2^oD$nGY%!"=KAKN#1:N5PN)7u//ja=+)NGt`GL''T\6.75gcrEADs+c*VF
K^,FDFCNFa4:W7+SI'hL[XA#bC2J5@nN<_:Ie<:1]A(]Ap/\U/ZcO+-t8]A^9]AQ_&q*c\Phd!:W
po>fg59F'7thaJd#Tp9iWZo&,d*kDWTalA!QTCKdT6a/G!i%:BFC+)mY;3h9m!%!8B,IS)J.
EEKE[gl'^?@^AbIA)2fTf"O]AiU/\T4slet_r>0de_7]Ao`?f6r0TOl"5BI(=#)6bj<rAqWp^k
Eh6"0;-e6)UiH<Z"/Ys5nZYH*JQfJp.=PA8=N&+9D%IkQ7s*s8d0UaAKqX%dK('Vp@]A]A1VTq
H*,G=B#TnXEOPm<>%8JIfjp3%Jk8BFSfbma0;/PPC$e*ON_2t37SdTmtr1u.%9'UHutB3Su;
KauX3-KrEl.uaX>*;lSF+>&]A$Os58\OsC2R$YqkF[V8Hu`&=i>`_3Ep<$k1Y?4P*7;*A;R>7
PsnNb4!:IThRT=:/8bjY+6(*NT>A\FbAYc#t4_;Dff`%-gO/fL8@=pKdk1c&![WZ2!]AfLr)&
d8GX$,bTVnbV>9X<F@bEq)<)Nt&@<H4OsX"LG@[1Q8@2RbctpF,ZJ/q=6I!c;5>mE#glpBUV
I==oQ&kgU_<rI@nY7a3X/C^92SrYVO9DAUJ5<MVloj1kVj*)I9"H`a/I^/Z[22T%rC6pf:o6
ks.;%!9_KUF+RTlUE8fSmd7\$G$h/'3E0AZ%!@o4hXfbWs0?uQ*E:@Fg]AL,6Rf5@[&OUnp0b
f#F;tatJfE]AG,b_?STL"nSkJ[dhEA!`1!SX7*T(eh,[Q+"u\^`.8@>8\qQJ+E^#-sOi-*JXf
X34;/9j<IM:ISG/^`!KLIML=bjfZC+EJVc+F]A"_Ta6:ihQs%>MT;>L#eoW!M@Z?KJ?'e0b\[
jlM+Iq+LERaS*_o8(s7t+oqBQSm'Kb,`HK0+UKN3EpW;<L8PM!`%#qI05;8-L3<P`q*+WY>&
k\XK:`n!Ff5_k+n95EX#u:@3_BW)5SJ<d6JC2?RmPV+HW/]A9=c7NIeCl!TM6)]ApfkLA`%=uo
%=Q[[=JT5.19Y>7]A:6307dLmk.XJg4ApU.YQW")g3`nhTeG>WXOg`oL%P&t*A1jRMJLP"$I_
Y4A=[a0fkJROqq.Lj^b!-bANT`ci6#=ec[GO[*0D=`N=Bk&C@UeI*PqAFm0%:?B[+PL'd8Y[
2h5qa@maoADn._WP`4oAOC:i,DMf,\>3&T:`tH11Yks01?:]AR8d&slR:/gGOo[kkQIp\8^Ln
WZ2gp:<mU/+\!bQ7AVj<IWA/:-&NA06\,t/KfZlW1WG[JK*j(r9K+O*B_hF"g[1P:-?-B\W^
VMK74*ikh_RW68ZQ&V2ZS$0eUp\o]AKFcQ<OM"a1i?Aq0)8>Q#F<8D&feWPPho,3*T<JL>7_=
+Hj#GBe&p(p0"T#.)r`@-_\rj`%rCTWfIE84$YQZ2YI]AKctmpUU#CTl'Y6?MRj9j)b>)B9KI
VjNQ?MWL)r\_k7]Aa&Bu^Zh'9a)+D&GgHsnSN,d5(P[;+p8`N\!2kW,&!Zp:UZqD%sgCU)0Tq
sXd:P_/nfluIEa_1H&mQ#GS%<B$FYt7\.00=mgVm,AY!3*A,qK<UB6HuaKU99glgncfXrJIV
FJ<P6?ka-92mG:5Z5*qPDJamRJErif6TZO46$6CkSK9fidfTD8_5$mA(fc_ndK8m7(:GKb6(
K[JMk7a".,`jm8/\bMa(>`Hs3ek^ZH^M&i.J&m5..oXc^]A]AK]A#=Hk4Ta$H?3b@qr;[mZ:_N&
tG%45f/mHbFk__@^%I-sb-q.mG!npXs6d6f$i(mI'Z$_\TL!S&`]ARIt(>o6c:9m'[@C0lL7L
*'s`VIWcdRY=t7_$O#YB4e9#h*X+<$(Z@rX!t#BrU6P5.<_&K_9EnlL,(18ZE8@]Am/fXc^fL
2RJH7F+X9DGhc0%P?ZNO6"PDUO(mks2[L-;p[,:bZ@T4#^U[0=9bbP_<B$7UuXKqR8Rb,+d,
S)$*PbFh4TaPZ.[aMWO([`e&9,o3#PloD$]Apr$4e&+C_f*`Mn?oV\Y0aguDl`F#R(I:Sp[^$
S!rEnqo-t.A;c"(Oo\LAn>+=-W\.7Rd6$JZ08(s*;3Nk'&?;n'RXRdSJrf)8E(N+/q:lnC'S
1#r*>p(K699NpRtKU\jR,@;(<8m*_naL92a7[8J[p#^:?t1%Ytikqs00^/dTWcFhJNPN>\:-
"`/TX[b.-b%QWn2;)J,3WMutK0(\OE1u<LH(><N9d-%//q9</cKKh;c/ZIeB.&'q]AUrb6hn=
eF_L1D/ZfKX#T'iQn`n%]A-Ob.1t<fl\D]A/PVWa9`;E%5r7_X'3:Gq)j^eJ&"nX]A66g*=Bb(9
7X/A#DEcp1dKU[$N-pC0uf;"E50]AhO90M,5)S[MhZ.SaWcTW;^c(!:\Bhj7oKgKFW+1=ts07
>,>Fb/oB:<;sF[f`-b-m"#Q9r9395BZsTNT:<kJ>'n0oXbt504;@R)A1m'-SI=p?6k(jE+9g
"H./lX`MgP^O)N\O,1$#%$^9U*HSSuM;O`XVI?MUa%1Dge*)Uc41XZaFY/K?eb\h<qjO12_D
@fFs!HK=ec:0h<CA1;H3T"c_9j^7+9[CG.";<l(_:X/\\'`@E_OkLZWF=Rr(*<LC[Y$b1IJ_
*6o.uhg@QbLMWH_PRaj8peC,t&k[.6f)Pe.f;pTn$D4f%<m*2"P19<Q;?h:&3ok67PB_bB*C
gF\DU]A1SE3,LeX'M<it#<*^Jjq(oft>"SL<D9p%-lF'8ipS\&W+\(17a'U!X!3qF>`3gPI;E
OPdB9V=CE^$BRMRTc$"o9W(?/SCgY<4K.sdL&!5lT$5\I90$=i:?Y=a6Fn;)P&@r?Q`J!5TG
9-65^o03ThA6_,@i^':W&QY.Jt.#VMVVX,r*:mEBZo>$O'n\;#\6=ZB>GI.S3?J'6j":KH,L
]AN]AZD7NY8TO$4*7=.LuI@^BWFldu?6j[=]A8!p3h@g*.pD\n?&-6#s.Nn\2KV,Wj5?Rf$X&kn
7b6TJ\nI)(V7i@=j:?E;FD9.ifbMGacHr)mH11C[!N#'7Mkt7-Oiqo3ChKH$\Yin`Pt"FHXd
bEiO]AM?%pDmjE-ms#>6k'D2CD%:)L,O-HR`=92ifJ:cJ+pQrF!jUuOMp;^Tq.DI)=%Hj<e8G
\>bsj[C<d4I*K_gUWOZO0g(d&*_D3M_7>1_KD-8Y:P3*ACo]A\UhSmTO]A;rEnOZQWW.BA]AD9:
1`_ej_u0J178#EXJEKt$J4h&9rRqM6H(-c^4;9pMR>e*I_e1AQN,GKjTD`(52j4!,EAWN6:9
PkFXmZ.'ZAp!k!0+`3Gl[8Ml"#XJ7sqU"Sen164^Nj*XL-=gKO8fE#j.UbAuK[U1FU&Ulqbp
B379m?nLL87UH)_*k)/IPn%R(Km@<\RD_2cX'JcNOqcK7ZZ]A:V_jPZ#ks))dGN0cTf/lNnbC
ciE?O,H8['X/QC6YPgV1qq6V@,S4d?CMP2lpL8SR4K!B;9K@"t)S@%ZWVsrnE+8mDZJ5="sY
Iu/08QM1i*[]A+0]A*]A(XoPd^b*&h^_c'[tlA$\TP#Q3#'FeFTgdYF%H\le@Vkh?Pj;_RbTF>`
>a#Z<i*-dl/s'J6Q)9g5)3gBO;SoS4L0-$RH=R2M2#N.M=]A>r`81/bg4NXei/NfqN#(`TbQI
ba=A)dqE!P%pON:1&:8FhEE+Q4rO.AXLoLr87T?pG5p.u]AksseGbE/acCp"hH&M@GLf&QBZM
tkL?+]AIj7^k8nTE,t(!?.U3)P@*LTE,t(!?.U3)P@*Li;YK9e;=HR/N<[+-/l^a`Ts_<"9-_
U5T3q^p$qREQZ"[>[O!Q@AKVGXJM`GmdQe^A")/keUk(@a#1>aT8-<QKs4>?P$:!.UY?gkf5
=@7fH/-GmpqrrorrN~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg.png)'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tnm2"/>
<WidgetID widgetID="7e246acf-52f0-4140-aa4a-20495f63be44"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("bp_jyhx_kqfc_tab",1,1)]]></Attributes>
</O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="156" y="44" width="203" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground">
<color>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</color>
</Background>
</initial>
<over>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</over>
<click>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</click>
<FRFont name="Default" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(n);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="移动战情室">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="rqsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_self]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HuaFu_YDZQS/1st_Menu/Home_page.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="20" y="23" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="215" y="22" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="329" y="22" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[='']]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="215" y="22" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[8763000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[经营画像]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mF?P[;cak;nG#>t-qmq!9bAT;TG8"P'eE-`ClrI$$:Zib?:C*3!*UEE6S)5?77"&;ZHC)%!.
"-/G_G7;`!(h6:-qJQ6ngk.8W&T4#YA&N;ZGMN]AlWVcS[mXG\\IU)msOl"gS[jCDVb6TAdAO
FeaN:<D]AfT9dIOumkhX=.YPn&(aK[7Ea1R%&SkK[RWGN7RHY3-U^PX/7/?;Z]A6oYZBId%q:G
ULWPamV@AIE?b^mj%#Y^LtD9rp,Pd)2V4:049!BV#@d;Oq7F1^Hh8((F1=T(!UJ#4W0-2_NC
g+Pbp^P3360?"Uj%;@?s%Co&\&j$gA%^G,)j!).`QSIP6_gTcVgo8M"6;Z8*'*,JsS(1rEcK
Yi"$:eTkfJes4$5;f#ua?Ym(^AGO1DYk<8=\eX:O2((bi)JUlMN(DFb\ua2%7'=/aN:C$sO^
#&XBDPaXL%?0[s6X:uLpM(6Oa3j^@MS0*->4"0)Wadqp-(dZK\T&UXI]A`2\L4(Yb[8Du;:RF
#d>AR&?lpmRd1?lm4!21eY1MHp5*>OW.k"G@&_Rg'h`P\:7kNFZFnQI,g_SDT>uaap-"eF30
Wt=^*-1fC<4]ANL18dcqDQR3_.pkkbriVs0c1aE43X%pa>K('A169?IjJmQ#HQB1'_I:ZhHc?
k@K^!;%;r6]A:NjW(@7h3#9'WGk.f45DAm$WuCjp%"CoS^]A:?^s/[+hLSbQaMZN*47Jt@>]AX^
;ng1F=)-K0"<7X=Tr-M%%&"%4:1VI<bZS0D:g6l_i\OfIHWUE9[YpClZ*]A:U96"V)(VK'[Jo
j^]AFM.tM<a,d)BX["EO_<SX#oN$/`Q\quE8)f&.W&'\SON8m^=O$pX\J2'/Y"JZcJ>&u,eME
9^M]Ao7T47LCL:VRgg-8Lj@e<Ei:jQq@mAGd\r)+DfD`4ZnAFS=<Eh,b.ZSR1<gu?>hGML*f5
`+C17i^M3$Lh#nd(^sb\;R`Wo>X5f8Qs5fWZkZEB5.s?$'/2/)GfduVJstmgl,9(Wdh(.0&g
,ojU/M$I%?WPpE%sE9'dTs*)u[/pV&0L9o5C]A\rh1-6UsT!2DEkIl89:[BCTNa2JBMb0'@bA
UCS\`9O[8Y(D\7G#I]Aqbd[L(^*).;1e[eWI($hO]AXQAr)n<J;&FY!2a:C_s/<[c?N2p6TU'9
S34m(6:$4<,@o8!hGu<Rh7(B>rgBmJ+LNil=0*2QU%VgHPI4;sBc.1-G<=B2iPu%Z+gaX_G/
0Zm_:DNK52a%eer"'!X-&%iOgt>JO_rgHLg%#=d<No4gg2m*<.'mT.qH+*C-#b7J!Z'%W#![
,"`bnkgEbh'&OS#uql^r^G?ZMpI['Al<@5kRMXS`OFg]Ab+>Vp+HMGUo08BEB?k+5$-;d.5pg
]AS=8d-Wio]A^R]AO^ACDL\24A@C#]Aopfq!8-@nY'YIVOq_V)#Zu+r;+LD'HE:c^f_F.G&AZ#of
Dl#1r5=<sY_PcUJ)6qYqc:5YQN5N]ALLSb>+5$pe!-44CIq/]AP6%`OuoGeoJ!*:EEm_e[r63W
9u_R@E</niK_M7s?UY.<]AmkL,G)DPaU6Xo+tdEZ+*l!*kFgLlu;0ka#U3d`t5:dS>`'i4bXN
Tgl#*N0LUV\%^fdSg'4kK[-#?02-nt)pUSMX8$=#k$Z+qci'b"T:N!+,9D/0X[7&6W.u-)_,
$&H!U9FQ,WrUEh$OE]AX*KM;GAftNWSqeK6GRb_-4I3JB4E?P,1hR*`r*`G1h3I)uqS%*JCZ,
,ZqFF]Amhu7*+CK4`$;OiEej*51gIAIu!..KJJP-F[`bEsEp->o!+nk_=.]AKG^m'0'AH.La2'
/UjTKPPuWPmj(e=,jTl"_pcW!r]AC<@p09TaGLi^!*Mb&C.*h9h-.\&8\DXSt8N\a!6t0oj<F
q)qRS/NQJ6tQ6P/:U_k(<O=a87Og5"`VKo]AVBV[=%R@HBEF&-0!Oi[8%0skAHj[Ue>O2r,[m
F#N\=cT8/Mm&5#17\di(P6o_;r+O-+5As_'Y5$@hW"$:2'Dps<&do$DEF)H#B4Fp)"+%^dVR
q_]Aa/m_1i3V1j$G:TB>WHW]AY-DD*#':G_a4j9t&lM->;0k9U6\L?^KB6]AP6i)SW)aN%DbFpU
a1'[VY*X0632?jK(l$'b4E./gdBDD<dgA4qGE5t]AChVRemGh,d;)mm`*]AN)npXp=>7k?jCPU
,*H%Sd1$K\.t:L%k1$MH\5uUhCe1+uR#kS_`Hu)9Il%8\!]AQa@75"'MlUcoWP>,lf=-7WQ3#
Nb8Mo/lV9W,Ke[BD%.@BqXnnl<jmZ=H9mBrXi!*q>u9Q9ZllA]A#9-aso@#R#,?r7Vk)kYM$5
SNd8V>$Fbi=-"b@X1I_m*Wha&\P:]AG:O@NFFe="V'kd,5mUffR&*eLYDUFK(R]A>jSmfFif7j
+TU`lbl2u`NIar\!_JiaqAIVqp8:V[dJ"$nK3PF$\aS6HMWUR@:T+J_R>`>*KrFb?$Y\j)>:
i_qu4^eE2l3I)YP>qdA-0""EOIPU#.#/$n!]A=X^Pc`lOB&3Ct]A"7!!*?j!!3^^!!FDE!=1pj
!YBk^"<[^C3U"&g3]A_?\Efcu19=g8?^A5J&D33Lr)KLP")ID.4ah[iAL&m&.gQ9SA[3>>V+$
Ko`J,p'd!!=(R!!Y--!=W?9!uSfR"u(Q.bl8u1FF);[ft(LkYI"_IWEK[e"rP,U"u(Q.$t'&
;(r-+U0Rs04?iU9G^]A<MH0rNK`N\:W!-hV01?t1Z7g7MqM8$<VF4?&lM]AH$`qEU"D>+(6&e"
T~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="164" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="52" y="22" width="164" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 

setTimeout(function() {
	tabck(objTab);
}, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="true" aspectRatioBackup="9.868421052631579" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ImageBackground" layout="2">
<FineImage fm="png" imageId="__ImageCache__E06A636B9F69CEC5CD01541A3C3A3E5F">
<IM>
<![CDATA[[8^:.;caZ]A%%;u=&?,\`->I+k`=NXH_!Hq,&?.EfOH<EF'UPa)!Pr6DFGU8L(/h"R=>]A8o(4
r@m!<jIsBUB6.1f?T4KHO\1P6C,30E1b27t9AV_`dn`Zer;PD4f^'@j<KK"@8![pX&gM`%V8
#ceoV7hh/gRiXo^@PW-iCn4_e[=5+&#*cmD@5PCITRCk;cCNIPc(PCMDAmskN:G&8bY,aD/H
ZH[>b,Fu2f'p,EdMle4r&$&3Y*70MZPuRaDL)#i0p/dkpS[Y\aGEg2ppu$UZLDmS5^7LSl=I
;Cl*1h#R%!:7CMLIHqFi1B,ZX+Q89.R(h2RhV/SatPkt01!Ht#cd3BNT]A3I=%4gEQ+GE&A='
S16V_f,(Q+GUKU;D!W?FXRnumpR%9^5AYN/F28S@K3:)=>c+NqSa.7VeL%Z'kF9>I$G!`eR%
oX9g@TUJ'341pGs#)8ItQ1BVp.TXa`X$mm<rpH[$-,\s#IUJ6"$0D<*e=K?@e#GOK8-(UKpe
MSA0fReKI5TP\L)n[,?2LdY\-seY(dCc(L(gKAD6LQ#YfHJ!d@`an@74n2B92f9TKtpQ+oZV
(Q)eDp_C0_MTbN4#maRa]ArCK*>OR<_B.gZk;XOfn:2>1e3k[5$ooVEmd+B8R^_O[/r8^fd,&
iikg;/?Q3]A\QWHikiCqR,<:5X?gYO*FP7E8s#2&O9XarS+Tg9[ikk^M(5#P?EjKoJDWZ-+f,
<p)A*3oh^6002mQ8g\oel5=i;C8(OsVpAfE*;O-"^FWH3COGm>"73FVTu0.KO;4Pf@Z2>14J
kV?0S(^3B%oef"&.4TbjE+:<EMAIL>-r[W^^45pjDgU/GZG[HOZ'kju
FK:FPlX)&uH07^3]A-@35Q&cVk(imH;D90l.=*@VQ5oNE)a^^gVKFt:F.1=D?d;4mE^SFL2?C
5r3n4;X^&><qE207q1]Ag"l?8R^YYZmcGY[;0IoNDe2>D1^?:kofGe;X:IYtRI@2[OIRAa;4%
CU&Y&S0FN$RbP(j)fVbF*Hf`L_[2A7?]AB\i]AQ=`X]AG9X5&GO6/&^/W\u((RknEApnZtCZd_\
e1tT,?A;Te8"`M(%>TQF36lYiGEFlmNC_Wn>)'98NaUoj3?^"HKuL;f8YKh.p[&"CoQAPE:@
&Tq]AI8rtK0CU$lu\C'+)kFtS%'!-Cn>KL/fJmDUClE&;]ARo64<l/3`Am-qE)p\.hDjXg<_0d
r)U8oPY6ge;<G-qnb3(Y21.R1Z(BG&3?DWnYC!*M3#o9(siQoS*8RhJe4!\^]AgA#Pn<;-tZ.
OW-3L9`KRjG=Pb1NTss?Ju8lDn5<si`HOqB8#t;q1:_TG8#Kk\NVVp0b'Tb#H.1-92(d5Ml7
AN4+0I#Ua7gL2`nQ)")S7p54=FJ&=:`i";4O@)6kL#1l&aYI]AkC$5ijH1-3u&9GU4@DM?;\%
d3a(YBEZ\QC0@;.-iIl,k(`99bYMYnQHuZV\Xeh!kmFrnK'NJ1'ZLHZo<Lis'g.p2d&HCL)L
?ioYkA,!fMYdh&>.GuMM=df<tkrZ+H9oE.4"5Q>O0l=nF*ONd!%%(9:9ZjJMc2<19G'6XXcj
XJn+/#AL*4lIkX0TD"3k(?%Ug_\?:AF(X^!RH$XK-,NhV^8l'DB7B.!mIU]AaAL/!^VbJ5njr
LiA&-QP$qd3@1@:fX0A]A)@8+1.L-Mj!3O^LGCI,eF0g6;I9ed\DFjqBGEU3kPkV~
]]></IM>
</FineImage>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[228600,685800,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[647700,1496290,1496290,1496290,647700,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[综合考评]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB0' style='width:100%;text-algin:center;' onclick=tabck('TAB0')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[经营概览]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB1' style='width:100%;text-algin:center;' onclick=tabck('TAB1')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[客群分层]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB2' style='width:100%;text-algin:center;' onclick=tabck('TAB2')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB0')><div id='Font0' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB1')><div id='Font1' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB2')><div id='Font2' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^,S,"#tgKbVK[Y%bXC0K)ke^R?)>&CZI9<0Hj7^\IO:phSp<@r&&SSD#+7VT8d=Hcj=<_
5AFC(3KrQ.R::,UANm3qN7Ijugucj.'b-S``#R+'u/*FXQYT]AA;B1o=_;bLQ;'d1:rfqDq_Q
ik1nWgQSr^70spqdF2sa2$2E0DVG2$%F/Mc<Nm+(8Q6IAnI\AumG4<?(/MA3e97>)F6S=3Oo
/gnYmc(4pVg^`YC\KZ4HbjFlM\i2XmT/&ch2QZ.OaPK&a3sTj#3dG&B[4TU,-^#BBBshAQ$"
l*$HHlul$CmJ5p(7]AFgse'4[!UcUlDG4]ABQff:6M;&CMh_"5^U(`84!$G6<qCpcZdW@5#ZoU
3?@sn%48K+o='IF6\!neJ'Y&L2;MM:MU*3NAFo/Nos<me11<&Aoi?QCN)eR"ptos_5Kn8Kma
ipiZ7PKBk`LAF,PS53WjO50@f7qBoZ3'[s-9kNa]A$5eeLmG[\QI?ne2*-B\n4(e[13,mAPDC
jpKkab\2O@4g!0&:F<AP54Y4dbCqA&q_a>6ARgr2R\p2`]Ap=I/u*rij%Woj?\G_[!g[Rf:E"
*cn9#uOGhEGdBbGFo?;l2;k+Vr$qsDuV[D\'9PB!I1&bE9KpH)pRtm29puFh4D>LgUq\g6f5
=Q:_quFHeMr*n"MoBi)7@C9:hl6qO)h/F/IG`I#sCECQ6U/m`f*KpS,cPo^MNC9I)O(B>aFm
S@Mct\aQ'"[MW6qC2CiGkUe-3R\O59.=fS6DiH:IkF'edJ9$E\#*EQ?3e+W;-GFZe?V$U@s-
:utRIKS"hS5O]AR\rYP>I30h3><IBjhJ]A8;':>b^Zqll^WH@a&BbfM^gkOT"Pss$S(cR4di/u
"1R#&j^amK10Zr=k`1DuCnKhiA)bE3cRMS&&38&[/LDhgR4*^*9HPD6BJebo&O[d4<;sj',,
54Bj#o6LO0PP.ADcuojBV1LhE+e@<l]Ar1^E^N?V\;YMG\"AWNPm:A_,.pss=^H5u)?#rmXD;
KuDVWGhInoXmOfJaZ)=1!)L-U*+0@k9H?Hk,VnRYf5b-!V(l0!+4`r33g"'_b-+6(oViYp+9
W_f^0\E,lD"GMBf[ku;TOXarYZaX03eTsgNq@_UpDELKk@ODeYf\2Ytjcg07"f1'A06nd2MH
r!H7o-2#D)G$Q@hF!FCL6p$3]AFLLZt$6j:U?*jN32+?'?'ArbVI8Q9;D<MQ,Q'Y+l[eiNg9%
0jX%'..>kW$Ki'`9qesp4pra_'3lHO+EC1_#/ncqsPV&^PKMit`g>R.YR+te.\uIK[[MNO6n
LV&p;^0@4;I!0t';h/,0JAX&mO;.Y!/9^:l*,=UbNkW&;7Cl*9%7a,HW^(jchWa)"L,V$26G
"7\V(Xj/s!T<Ms-alikhJ4j\p#XC8']ALTS_V&#rWJhLe/2_Dm-nKCY)Q#esasXGbu9`-l\0Q
n?@s)d:H933ZB`6',puNjSZuoCO7q&)kBVg#7&`^!?s_/a@qn+8efY#I:7al*s@f91NSgSh_
6(7WdX&aQAZh7)a2=tkRn$^j%II`481?4h]A<>[)\.1a=_hZ1F.K@>@"^RSO76S6q/>Qpk@R[
*ZYkpBe9<2JE<&tm!Up8ul0)VPA,.IHMN3D-7V3FbF+-a]AlE(dG9^SQ+'s^r:O(S:MT,JRgU
gRcMi`Ysal>2h93F]AQ$&?UlXN^i^+=\9;67Akd,"Pg,r'$ard8$3P&kS:u.Mir)&+NQ!jTQ#
6n"_ab$9:>?M;!:*4H:;!NfL7!)%u-tY-d)T!.q>m.J"cD370k/%?4B5o86d)[rTfAsl:\fF
'^F!CG(V@YW:6l(WE]AYIHuoD!T1!a]A-*+T&kZf/85NC$a6E74'(SPi@=O<]AR;D%?7&QNo?`k
Dl!HaDG]AD1R=>S-N:m7NX118'X-IWLR!>#(#5g#-(lTUnEDE^9j(9WV1-"OW(T;V%QHgpI)\
%"'E>Dpd-"jd5j6uQ4UB^5@>Q)0tk.A0eFi)Gq_NAAcf1R/t,a-UDSa&\!59pXej:pf.57'@
1513>j-==5D6UJI(?Mu&%\Ln0>)m(M))XH"YK".*-^j;5tmo+G%3:o&mJMWP2iF6[4$M_c4O
oPg,N0+mi5LQXpi9$"KOtNYLbO#74*_Q&*/f:(&Eq>W@)_-P(bk8SV-7Xej@ZG>0`Up6`:R1
&-.h(VK8:#Dam+W?D_td7%#u?dJ)SXM*1Sq=1QlHX]ALZ*!dKASp!heT4(O(?53>?DNSg/gjt
Ta)qcnL$fIU;tI<[TLD"'i89G+rH>t5>W7`>"qr.V6A_&5fPZ7oo;3B$4ZD8`AJD2CBE9pd+
iV"78grR?0Y==72s$%n$2R5*D$ZSUqL!:!c:-4)-QCci<TOCWSNLg#k5=Q;9Z.ATOPs5BJed
%gf:mM.b?m/!FRV,lcs+:AMNco,Q"7g4Hp1f?g_P@@?q/%pV6TEF,m[0l/[Q!T6sg-,!pd]A-
/@K0^SMUo(YZ`/FqLRTnDleF<Ud3@Or"Dl1(O=6DhbiKm'WK:JenZkZ:/o6CqGCbsUgGVVmJ
D\_b?K0u!1HL3YO.is#`ZVi&aZ(`Q(nS'euU#!B,\r,e:(-gWec+CbIU7pW76.DogC?n(0c8
P#]A1s@Fe9OAC\]AL9%VNeu@\YlE&3*ZdAkP'qVKGp*3-Kmd@f%oc#HK#]AFL8&2NA=N9<.Bn3i
Hh"UX/YG_ZO4@Fe^4]A*m;En!lYI10@tX<1ggX9^nM\6An_`hgoLk%O8teYFAJ4j2n+bo!h*"
uI84lJB$D:0?emQg!eBe?ne+fsn95+rbPXS,=V_0$an]A':)IKft!@MFH]A5RnIH.Fho49YO@B
b]A0;]AO/Cr4rG2:M<GV\C<Fk!!PT3)Ssab-)7X-;,0gPL^T?=@eAlY*\Fap(h'WP.YNI]AoiQ%
q2+7gbZgT2;JsY\^(Ts&W&rmJN?2!D,W20mQ'EG55/)D(2FC\%#C?=5<*>e?p#FWnrSKY2<Z
1Ff#'YRk?7t.fKIiPrk[kH(adT^.a:18"9_Z3GWpLg7Mu(o7`-UE]AkuLQ="Q.ATrZtOQ/Mhc
M(ef#*_d'h!RDUE$GhMcpL=a]A!3T0*:B","NpaDls"('i\:PaN`kM@6c-\K>i>X\;WCrYUX*
495?$2AN\PGWa+BbN#pB^c,E4B,"3L+pWPGE;#oDi)S1H?gCX4CLQ:l*rpPC[ta1B,b=%1tU
Lq)h(Ejj/P6s)5'c>&U2dZ;*ONo$iB:13=!3&p\goebOef4&I!*"keJ&p@VBk9%MpO0jeH>B
4c*$3LOhfpKh]A/I^S.S<=%:Wb7r5WO4&q:aK.n3PEn;X"/Q/bHkq#5N9LeS?8j<ntBbq/`3B
8"pLSN_5d?X,P<)t\ea^Q_1]A4+<;)$8uTToZE1dTh^=a(Ba$b[gj$@eBuH34D+i&N#1>7mN-
!J2]AN+rKUI,!hK#aaM?Or;1'tfnum[FE<6@'):fi$BA_uU.^%&r2^nZG4mS3-,cAKr0RZ9Ek
:gD1f?<[nSi)+d'NNG/eP1OOZ<UDuanK'[ZMBY/+_@s&=L7r_F_UM639pk=H&uRFH,,AUGYL
ST'p_W:.-^4b4+\9`BnCd.ntbb9NIg<Qr4I)!GG*ODUY7N0e&J6);-NaH"mrce32D5LY'ZTM
"R/N&,*iAp^M9Pi>Ke:DmY>U)*)Xj)b%>pG8Fjo0pH=$hS?gA5htE1pcfRHD=p:c#Cmk8)a_
a-;0<HsG1D9*Yp^B9@l$$2/W@k?dJ7N&AG^kSt,4:34m+!2$5p#Ujg^,jV9hZCo_rF6?TaSC
a[G41Bf#1r.H.S/E\X?S_qkD$8&bTQ?VOI`(P,"SLJUaPq&;L]AAN"W[h2*q?<Ud_P/k[_SJG
$"2#p9:Waq0Wj$d%S<^6Td<G=34=g3M0dWP@Ss>@?^(c%&C&umQ%C5XJ:5pR&"h;l#EBC8B\
A[r3abqionK%N,'0"L?"QL`ufW)U)E<+_;7U46+.]A;cF_-F8:m"QTMaaY#k.<""o>o7RX'%&
JGqnAc:cYQd=6OV8J1ou,;GP7\`]A<ko5`]AS"[h?sFEn)l[@@CSiWs'M^#2[Kh5O=+p&uY^4n
21%5B)`+p;eLp_>WHHKK3#nXY(+g`"&JB]AI-bAq\F<p,jcRB),a$fZ)/B=l.m(DZ=\^7l@sh
1%Ri'A@ChpBgdr9VnF"Ja^UbHRM)XA'M:lu:"=1$$V<7#!J$/;j:/]AZ=3uAn'%]A#7Chl5?,o
G8@0d.[gMa6cA22P9%jMLLWFEN2VlP2?Q5rLcec?#jQ,l_mt!:C-_(1#HVmlord!;Y0FEL[R
p>1!@?;KsR#5.br!^f`pn=X[J;g.[iGPiktC&b,&7dUZ091s&hOj]A0kd[CEJ^DhSI)=$fh1I
6N:kReZJ3RA%,Tl"#E]A:\,..1d*jbJ,L+Mmi!gX-\<QPcr.Z;L_`BrK/XCV7os`.3.T2-t^^
MEfejKA0@9qXcdS_)#Jm*S>1R_65lDVp;ghNj<_<&H/q8C@#OJ8^\N[PfhOaggGciZOUOT@9
,-6UUR#p)S3C[:skUo5P.`$gLfc-#VV'<gOs=8.[fAd!?#nQ+ViLFGI.&Nou+>a5e.iA(^KT
O"WHCJR^N'+&d1[=XOMqXEJ?T?>G1l!0T1bkn]A`\mm5W05ZY:Y6uj3MJ_]AZ`Gu8*nHG2khDM
\#>c^G4_hqrMVsVfPU^3,+$-sT=E6nZY@R_'qJ.L0b*<PGID&l/I6/AW`oc*94OfknZYfq@O
-$1=Q)1_5Xl,G\&Q;=ja\H>L&*j6+UfDH2T/4rGq/j4k`KV0&i<i=\/Pt9A#nES]A'n@"#1_j
i%qM0=Ca4_7mtfkGV6J*F?:Du<5bcVcE@#=.u=r&M*,&hYQ$OMAt)rE+CKI9fS7,IXL-afHQ
jUWNt%!QFIXaDi)8YN:CkF"-pn\CG;qDX7!).;Y4oWH./uJFN1Q?gd;VE$uZc?a62Pm7/u*p
8Agq@M;6K*%Cf;>^^3l8&8_8p]A[%7DcWRWh2fGSFgCnG,r_cDCS!k?Vg_^4XE%I$ko&`>R\8
M#2RI(=E@C-Q[3%8Mbp0Sp@=&AJ.YXY0Z9j[dTIXg4<N:n%oR"`gW,:_K(?'TG7d@4aA5r?R
A"5?!59a^8U:Bts68e.V:UAu)SiA2<':MJ,%(I0/jm54Er4*Wa*nEWL2**IT2Z9MmV(ICB+,
ZNUN+ChnZkk@MUV^ea5MjW[>qMt#@A"VYFM>=6gSXM.OP;c"^K^C`0.I.;=8l:u8=<^1=p+6
T"Insp66V;J4L=046ea>%_pN,OZ[*18<+a#t.E*nr`EbBpQX7*UJ&Z>;E!@V6(QE>,,D\b./
etci#SA&cPtA9Q)BmtV_G^T'rr.5uN:_gqkP`C>l)RO`CWiLmA/V_qoiGd"qE-ts,u9bKs,o
b"NYl/&\4B6;2)+"EM,R^.2%d=7\i#GB"q]AnIflHP/:_<Z5AGK)X`a_%U9"C5!%WZGDD^!J&
Q0iF@n;;rHL"#cnXI,YEj]AWY!m.mR13(1(rhdQsRKK8Ad=3eP49i$"M8eH3t)M#"Vh5h6&7g
JsD7/I'n2<f#Ir%>NdZRBr@h-LWep(3f4kJ/f67tsej"R(K_/-r=G<WK:)5Ujp]A>]Adt?#g-Y
W^iX=H$I=+M`*%R&8='"ZJ+lAmd54+fqtI8h#B:"er'[b]AFed\$\:ZB2l2GMqO69/VpkJ0:b
eh(XPmFV/IA>5NYT:rfPQ.PB$6cXI9TFcVJtuA,?b92tlRgK<b2[tKip"D]ARM[n+Nq<XaDb*
I3@3C!shH%6IA^/tsC)W&6%/sK!Lo>\_oah1uedHk2qSGm<@\T1e"Qhc%"Cl;'$-4#'SP<mJ
2>?6k;H?u6MPeo4W)n:K<n&)&EjW,>`$Ug/FjBRHBi)^)/KJ8(P-k'r5K;N+c0Qkm<h<:I>C
N4UkEXK*]Ai2-bc$H(>_&-u7N,`_T%8N]AalBAo*df.f-4QO6UOrl/G;Y<tM3B=GtX^MN=YGO^
u]AVG&Ep%Fe\)R^EpA(]A9JTt#g*_u=dn&S<6h!D1EW6jA*N_Y%o-EH=$$0WKpNI?UDaqZ@H&'
B"\-ID(/!UV%j4Zg2M>$1!$K-q6clNQl3YIth/R<14S_f*^/m<8)P7*%as.md1?ARH&'_NbX
l<0>*sPi(Pn!D)GhWI#)9$drsT8k'K*56[DbrJMVpg!@c194ol.3&Cu/H-Y+SS+_NZt[rT&O
Xr5gB(4eikHD0[eD0=i6eO+[rjO4]A-/1CKi6qd-qCMKKT%pUF3AdLHl-Cd<*]AFmZ5#f"=+FU
n9CaZ;\ed*Y+[)NGhj'QRs3M`NoIA7e<Kh\5hY0+s1PFKMAkLpk)s6YiTV]AYQf-am'?X8O2b
cimg?0j88*(L"WY[+?OD$<T*?lW)"qr""Z5)3B5Djns+P]Ah2Ck_S=%;p$b^I;OB+"UCjT=[o
gBEnCRG-T4gNjNp=&tA8)J]A/?J(ZP$14F2FP#4U#9D,\,)^@Aj%B8o%qW'(r[uDU0bVdWOQ`
tWVm+%["4\_1_9r+(0NESDV)dK8l1>V7irR-rT54%*;8):G.@6m#O3A6>6!d"bX-6d('8h,!
J^SC>$'E8Y!$35pg,"q/\M222L36YW@#5F(,VX8(icNa+8TncBKN/ga=L/Y*>d'#e)-).]AP=
J?uNbQ,E0=@a[!3L14aboW*p3?]A6-W"=Yi^$uF(VV1s<lN+Dn,ioC)GiRInrBOU?eQdO2`^]A
s:>)i1Dk&R-;X[j<1W,oWq-gbK4jQr[_&7K$4NFnJP5-0q[8o3$.@Y`U8d&6pHPO9T8$)[Z>
6s*`AQ0B7AIN'ON77edTr`9bGT'BlR8[jE:L?:1I_YdEj]A$P8g!9q`^Wq/$@5WaBnW!]Al8qB
6h-3q,+Z\qB0%&NlY$o,m47&&ta34U`TIbs_o:se0UjphO/`@Dg7.nnf1=rQL"40]A?\_9tFp
YV6BA.@t-1IQ7%"E+3c[ZYrlh=`0ct-J4tN<R']A!h_N?52%,3pZLV1tR>iUhI=l3lT"c58d4
sNN8m-;^-C7"CQP]ASHqRsViJOEX/gD&:fY):[>2Gp4dF4eV\27jff<$0&tS,Yu3E)/L:H(VO
nl78Dd&V,*-54s2C+(1D'0lIBmi$/)1gegD7]At3Dd[&(TfD)H!BXG?Tkl-SeM=<G^.97;j_G
g;=4Yl#\UEI2JkEJ20?%*j<2;J4fEX7l@9X9@\>5!311\-!-)429J=\E[b+44T*%?IgDS1^P
9_9ks1pU?d1_hDH*^nT-iMLYJn<N,Sr<C2[`dZio.%\nF6/M/3kDNS]AUXZ_k4m1J\+/57UWd
pO)T3ZAf1=q>=sZ*6cm"7F$*<A&\RK'*:4K<@G;#?kM')I61%I1\d;]Aer\kH="^(q*eKXn]AX
l_,??W#_%:HEhE^h9@^j91:N@86?Q)iDXk]A-<U(_\'`?oBC\4IF49r0?k-^XRW)p8k3h`>eZ
@G>Yrp&X/N-M<dh6R(i4?!=$"0bOp&!";0W`XH(=M8qLcgDeVK=5)gQ[<7hkE-/nAX]Adf@8S
gcOM&-c"W62`D&!`e2kYb20;E0\q/U^%]A:Hg3H^4F]Aq?2mkgg^[q28PP(,<^Gu7^IeQ:,.JS
Y?RI+sUC*lI]A>[KYkN+^DgUL,tYLreA<h$mZF<*A8#)MuPMM7e[)2r9jiK-0fVq?0R99h9]AN
W\C"`TVGdZ,F!P<-QbPD9'?f>]Ak[g^io2k*r6Y_m)0[bH=rc?Rp2+(BKXbe@ia;;*Iek`YX8
p+2_i3,*#b9;d-GQlgDR"C$GG*UL#/cS>dsuJU"=QZZq-fLkpIl(SF=EqCK9M[uEH:,0ofg[
;ib987@S&1e=T?kVhV0O$kP44s77u$pm;W$tjHu?l80@7HH]AVN^K1?kE.I-[F,A2&/)WSC+7
$@&Td#+BY9Mo+LjI_"GLgXOCpJ-V]A[7IPooa2MQLXJIh'4%"6>M74.TmCJq7Ut;gm92uM&fs
5NH*d6L/Bt0K+A:`rC7q_rp6lP/G>+::CHlM0Mri?2CY8O<YTlNJ[f^%n"4-bc2[6A#F05gl
(Sa0b>ZuQ_8QSUcN$05LL>HJS\-iru+dsmW=(bjkb!I."2cPQKo=RUUHth("DipU0//QP@ru
'#G[YL=..epaBPNb8sCONMT`#OGWQhR@<6`=YJ7O\E'k="Yuer#^\ZYH"J\)9IhZop4'jR9`
\SCLJb+I!/3R-+C6%#n^,C4!mJ9?TQ=\o'n@2B72tR+nS\Y["r`8p&sMSm!VPTQC.(RPK^j?
TM6Z_B[FtN]Ac6#I`)uEfGICd64mQWC9PHdO!Pps\T\7b(XtN:kbn:F37JTJ8&"e,-9L94mAD
d^C?Jou0+KDg!hq<&Vg@#c)q\_2I1'rh)p+hX,>bI?1BC=dp"2@XE3+o8FUCOK%pEuM&t?,U
$nX\#(j.cl@EpntEDi^K[I$s8ctUr,&@WhKQ;HhlE+Xk(XY5-Y-_?T"c)R\rZ%Ti(AH+QpL,
\]A(/+e`LI*\b_e.nSrnV"]A$VV*spcd7?[3jqL&0oX:X./e@i#.d.;Lo*QBEP:"Xfs166qi<!
s[hqrE?27FK-("-Ngk*M1XN(<u+md)(9CCcr_p1C7YrXA<<6Ym*AB6U4+#+fA6.KUmjQp)lK
M'<'^3U(?!);?^8!AW;Y-c>M2:H@G$!rn7M1tVOP`A%^[Jk^a^AXjeY\j3!*"l5u";>?'A#J
`J`?3o..(i;b);YqA&&jU1aqJ!6*rBmcZO[3e6:r8UF'`s%.Q5JB#pQ.]APY!)/_GkLH+4Hsr
^M^+;hA5HT!(=4N-i5BMYC$;6)+m(gIM=ZiH:9TEV$PrY'B1R<hiB'nM^?IWV#MVRZbs)aUQ
[J`BQRhkl6]A+t!_S1R;5B.Q>2*+saiOd"fH^''np9tpLqnUL[0OY#8B.!50U!s]A`C03,jpb?
"n'>17obKl?be,8l(g*UH?]AaLH[BQoEJ[F?KW<b!;MB^5,=J3E%^jITU\&aQo\53'2hPpGl,
oZ&QbWsK_MBL*-5odJ<YG4Zcc=1Wu.W8$g92eRB7OM7DR"+Hp6/TPci7KC4Zln)Q8o>r!0UA
"HTH`2FnYei.kZ;OLo5jHDXZM:\qt!.$ZO$%la#K]A.n-'H&Cp<P@qV)9O%qu8-DS4TOar1QR
Z1jiCh@''a(gQb"k328o8f;I3bIq$@LC&+`I#eREMTa#f@_`iJTF:rTalY)G\H9$iBj(?`L>
5#`]ACs@T/5!#.6cJA%:rmG)FR\?U1:a&Q+)fBa_]AJm0RMg#US.fYmn%g0:S>?^odhO6%IFZ+
F`.JS\:]A0ll7\1o[[@N]A=%%SuG/8Fk>\fnb6pF(]A]A#f)XCjL[1HWPbQa5lg\H$);h?.p!EDe
R,cKlFedpqYP&E0e?^Ml1f]A;pkJruM+Q&8<Ofr>ADRW*4(B+2FBU7DlI.^M3hWb2?@:&@d'K
;^86D?VfuaqdJ>p.V<VUG]ApOaOt'goEep#sh2;pMIbGuPk(f]Aso9c:*^DJ5]Ai/q,a20YEto'
It=RFaV1p@a7@>i+osfdpO#&-rq#BF9$OR%7-ak^#;g_<"IR7."$6AUS7;)(fpRNrN<b)Sf2
8H1puPh&W1,.s+"P[n^9J[gS_sqNJfU)(Z4pYeZW=LMJp:E5A$,@k!93IP=r`FA<'1i9=FEf
E.$C[gZW7H@3nh1L;H\&dG^YBtKBsREjSn^&D@Ce634n%E\LR;(QuYIu>#3ROjMq4]A4fI"h)
K,(8Ih26#8'+*42$_)r/mRC2<2oN:-=t:ZiH.BiFRenm-q_@FO-_IVCUK&W%Z`RgH9Xt?=Cj
fLob&TF=;q('H7]A:aP+-^Z01lAN]AV67KmjkpjMru>ej!0`-*25QH=5C^(A)!7%:'^aNK7EJB
1?e\j!P/UQ$8rBGV,A9IR;#Rg'F[0Nc)ZSD<K?=BiGgop1fSY\^j>3PAj)4+1BT7Y)ge^iKU
A9.e"G@F!LM/2C7=\D=K4l?2(MY4s7,l9fqY"`/chFuIp>m1M8%XYY]AT6:[WX@qZFPB?UmUQ
de70J_d9/glWWVPH?m+)BZOt4CqmF/3G5orq-$<^D;HFa73sS<kIuTGpqu_sUs%io28TcrW>
TU^'3Z&\EeK/=eqP1mdE>dnb`*m)-Zn"/gq!4Sa.-?e\`3?L4rp#1]A3^p4k+8Y:o35KgLd3P
\R,nDj)de-LDr7?nQ[]A+/r(et5YV]A1N[c#r+lN[O3uKK\L<4"8Q-h8`1ZU4O>2AoQ4(V&[D2
4h(l9'2C$crs7QI$q@]AcCBlsJK3.LEGe5u%#.p,i"58IiBK1"gp;E2dQ)<:AhA:Ekak[3M(o
*98?T$Er+fIH";MrI6ckM\`i-'X916.RqJkIQ5)M@\9Bmi$MSbV+/QmS$ZV^Kr$ql7UKb;p_
3noamLq*&-Ir&>,TkJ"BX`lGX2UK*02jcCcpiljT`S"0k8ka'$iR(9nPMV1D-NA&Hh3p8qkq
97mC(HIF&jS<-L^><4:<HOnTGNm)snc.[Dc<*O_kK/]A\"C1fW_2B:b!<60QgBSskkQE9%eJi
!6=#!+`=;jq^R=oquUVVK<J>p1U.UNg:VBI*;>6l!ZW7:o<,6(Inp'cIc(3&u2hl3rBg^1_c
)CO3E>X[!2Tre.'LEK@!W7[p+?q(Q>.1t@6:Wbg99KQj?_[7S@<sC#FBPP<.Riki<,Nl,+H4
s=/H\nP%d6Jgu3]Aa8A"dLBm>@220p2p8SA"/1"L<uCl\'Z3$<pC2u?o"A_e^Y!$_52MUF,c-
7]AH:WHp:Y0/gA9XnY\(>BI-"d,JeLK?M88uj`_m9f-:(#9O'XcDRM,d[&9J$jd0sgPh\7Oij
Sk`EPHGKSSIj*&qWI!sNXuC#Eq`EP-G4/e:JPW!X7%F9m:31cI9dMqY>obln(R@8nT7V(rRq
R1HbV-m5!1`\F6<%N'XLfq/?V._oDm7jQE17Noi"r,_R8Jd!K$`aTDYPmVP/'SKU4\M.i:@Y
c*V(a]AU#IHiR]AGOYW.s]AhT%UqX]A>F]A*5/MYC*E@Q#B\0,;L%YXe-Ne_)0IQkV>RSD(#pS!kV
&Tpi1K7)`s+Wm?s3lnXquMpcfB5m.6q$jD4nKE4(OL[_)YoOPl,&=A+7]Ao5c!VO;A6@$&=AZ
!f^,/UOaD+`XGB&n1gi+#Vm8,Mr)3Y]A/5Cpf>RI:0Snp.c=UWK]AeMKopL_-?nHs;1;(;U<d&
"Q-)`-gKeR*n`,)J4h:Bt&oWiuR99RIefY.U,T\B8,e5@>EXG)Ha&'Z[ahAlVSg,2#?-lIP=
J$6^qVk=!f5i7qU$q!ZQXG!UlS<aqiJlbm?uO(#P>/<,8*#dMI1To_^=\EU1h8e_i?qJ`7EU
3$p5$)=?`ap&lp.\SqoC&Wejt/la$/Q2>%u@SFA*#$arY+2I[`aTAX4=&Gb3lgqJ)?t^$4?;
6>A3td!7qm%/hT6>U2HSU`rS[ENO`6\r_8AIr3<JlrU`A]Ah6<8YZoj$n\]A!Oo_Q5F-J/%k*!
@2_e<aQ;rj9/Y@J+=BB/P[E>M.aYmoLkb">!$([C8/S/so,bm>^M;>cQr`uGgM5=MXR`C)lZ
a!-OfE8c(mt$?0TV:.G$=t=CVkT>rM@u$?BEu131;KEA@>k!l9UMOAd@Nl\8fre9Nd8(ah5]A
eh]AD*+nnm3h/MUg)%[i36gQGS^!M-k]AaDO9mOCo!u6pgrUol?eAhc@uTd,-TjIV::Nb8*34a
SbDp01"ng![FiE486n;8,-L.G<1f9ufe,@QHNFF7'%A*h/%`Q1@YJpMqIC\#-#Xq4?]AV3jG?
76DjTW8F*cTriF,!R7W3-X^(OL<EpdB94\CU0raA[><)l1nBrgs5Mf"_RU"l@V!GA#nW;9Lh
?pf3Q+rH3W:UMCFkY6qK12L7\Neo`rU(3@?(.6)<hHCF,mi,1gD;V_?ib18DFRM+0IYHB730
;m[9bMPW/U9j]AHDVVkSETkq2Q&ZR:06%XbBV/K'kO#m9/u=eLQQ;76\1Lt?F'HE"7Q<2PpRk
BDb2/7R5C$LM'<6a>3YZ/mp_G_"cCPb&#G)5[YlfBFIjr^r>3n6^:>_'DT+"]A^Hts$uC!#9;
8ra-aem;ff3Me5@S]AMh2:&p(,A[-Rbp%7`DTu=13U@r-e/-jKocs'Y$5-P#IIZ4AF;15[&ci
OEQcaFfjjCqbhOt1*bqS@5<%2CU\M>1>-S1mlpF=Zs?LS<>&N%!?UZ\0D/.$@l1%[[;))&"o
PNKH*j>pBetUZg$W"+!35P8-'Nki)8B,e[h=D*5#h-\ISOpCs-;<*G]ANhUrJE;RFWh&Nln?7
P;k7B/t;F7r9aNe46##B^CW$r4-m>D2'+umF^s:T&Z,5988N'f-#2:'tLLm8oe5lW(L)*[8F
cN;K.g\7\VB[cD]AZj"X@JK;/6>U^^%29Sd<c]Ai(BoALr8t?rPr@3ThN'HTY?'J5q^a"c_K-K
5Oup/%aBk3=<XuDp$U;X'(>:\"MBFb<,;4GA2G;GZ>G)<o9,C_/Z3gHB]A4ja3GRM='+C@DL#
S,&V7MeM6c:&BQUgn<W]AZT#h*eX@CYXItGlCQhjZ>DE_f?^)ag_3sYL7pIB'BZ0\+=j]AIAe?
9d2)>>CA)tBqkW)PGL)8ZMd41*Qm6fJ!=GW;ZQ3@iGOLas?mMWUK)>I-m$rgZK@.6oeni\SS
"*\'&J[+Lo**WT)5R,O9L&N,YE9W-Hr5g?)&,p<G6B,Cd?BPKSM4LEB0!>IRJad-De=r=00:
POQdB-sC=ZRJ\lNCL2C\gQMd,AWJOEB<.JbiIj9O&>#TE`&,^EA"n2UJm7:LQ&&`oSO[CK(5
.7AS]ArN11(3*XW<.kduZ\J$A:biN\eI]AoJH/':scReXf`'q]A#a+]A\Fir'F9/1"I"cB<#sBCg
;AQO<LW&'2)UdKMsPs5aKP*foOmGl18TnpD1ac@<>rDgUuBm<[tL2)K2qlZ_%(3?RCFeP=X.
X1.6Vj&EF<<oZGUi[AI@IBmqkddj4jhmlBHlIIFg:K$?L4P14@`75U`=>(clES_(+]A$KiDIo
<HUdeT3lfphA*:/<(R.,orR=8,"Gm+>3\;,pI?)\>F4=G'G,lrO.u@7X[jKhq;U/@.4DN0H@
@0Tlc`MF'</C<NpsVX3C:?@.^&"@^B9E:f[Kjp`tj"6Cl-Y4"U<%a9#bf?kf@ADFM5SC$3cH
0*4Ir&<]A!Mfk?/1=ETU[\0B!s`:^1<n;dq.PWf=?ZJ2g2f@j(cmUV8Zp_;$6#oF2@!TU+r*O
]A82"S2]A8c1HA)80GE0(#C"[Vc$9p-j]AE)gZ%,\G'Z6oer([;@PD5AV(9PLJWbJ-lL]AU*G'@U
YY<=sB1m(HfPVIOTJU'BWrSq;'[.-tWXBD<Jl+kWT@S+F_+R:tkc<GBfMq,_208Qt4!m&FO=
gTu!,om8ROhHmH`Qs!c[YO."D`'D%+iS(mRPQg]AR]A(:.?'ek*^/kq<Q]AfrGWn46n9G."b=@V
-?Y,Ti.\DG69Ri?$V*0epSN&=*.pWekZqnri^K+h>?(QlKe_`cmMOXas0-OQfLm9tn'$HY0m
g5C.bhd$YXBAQ<E&NXLA'[?pndon=jRS1Pa:dkYLiE"3`"dJ4gmB5TLV!_DA7[tFILJ,a3M5
FT,kM1O51B(;HM"K%3Y!!*V:3_<rl3aY9Y_p/DCHX)'KCBl#k*Y$>Zq,>OTY?0f2H]A<018XV
KPM&b-Y$P%UkY*/I>jh(UhO6F`pCpJc_g:g*eZkoVZ\:K24k/#Er8..#o>k.Q'/qqdTb?I@C
<ZZQHPCr>c5O1ap?B?K^\->Vj/e@^K/GJmen2#5d#=^LCm)e5c#%%rp`LDbE,OPXNO,)%VJi
>CghPP:aW1[o,mlSB#rjb'?",pR-X<>+f$81+;@1V=m&Gio!FAAE?'1?jJ?LSrGmo)N=_ARi
;o2ql:hiuWXFlOTeeQfHAmP1`:\\l[eq!./0.1<(3n]A^p<Z!K:Nf7+Co2'Uq.JnUZfeZSqO%
p4;4t\>:m5@TmX7?>pP+qfUWS7H"NYdO<C'auCpls09=X3e4NX;UgMQak&^ffHDnAtT:S:1I
gD;4&=W'Sc&eLW(QBJf$MO_\s7VJ5tZUaO>6ot#&VCFZ+Ua1m[g8saE=E,UMI.Di/QWKPQVJ
ga.\E'U,u8.V)U:o%4jCed3D)AtQdTZsb<@.X_FdD4+Qhj<]AQg^3^F$(pc#ZIlsWRqjB:G@r
W)qs!%$A+;1aEQ5Lc&ZL6<gFbfje81R.rRe`$RLtA,H6>67P/G2q[kF8Yj6tLDPb]A/kb,>?C
2<0,X5!AQ>jpVVp-cSjI;qRH>*@o&qTA^'LI3$<<Z<f=hKHSq_%TmSIXh3?tYb4j0Mr.\+f"
*8oIflHW,9,#OkFji8s.Pf1'`e=^V7sgDZkYSd$Tlh>\/9iQ1Mh[d[%Ak\lV4`0c]A8rs#$Vg
,e"\t$s.+@S"0O5UP",jGn>5Rp2=3<RgY%kC*r,HD/9Yq.=0WpQb@&ZrkaL]A7)9.PVNDqtFp
U0sl<H"MG8[tnN1RC,kP6_(2FC<.\BYB4\\/>Cc">RIMbKhPIP"s3A(m'^a^;nT<a#Ci9=Ru
C-25B[kZ?C(c_?!9s;uL/^V"tRBiB=ZrK.RX2=M@dpkVoa)aNsToV&*i1<mIZS'h4I')AU3b
4Ag97rOZIkqKteA3ZdM>GgHO7"<F^IQrJd$-8M9(UZD!R#Dc#M0uC(1nY?WHgcU@a1O$IXlN
0otNt$$?,s0^q8c;rjP'+QC)(b4uL!`qo#-K3.C7p1//L1m3FN&s4R,[M1@]ALrS/9pQHrX;e
9ROCLM_8`Z1%\q51A[%8GIXASdlPMnp*9d(rjS;4R))ZB<6#,Ys`pkkrnJ`/]A;E<AX2&u,Jg
"MWsomE0Voq/8TM/H/Af&7kfqQ^G$L*Shi,[O#UoSk5*Kaq);?l$\2^WNH*V[,i\^&r\3g0V
gpCVt.@bp2!BHQ@<5)I?_'k;G$!W\OEt.X'+V"3)ALX0?kt_d^'U=j-P_]Aa(U.$rhU#ZBD:c
,ZdG,pe&'^B\ZipJAhuT^9L`]A<<'.WA69?fL[(qajkfI,O=In/<f7e]Aq?<54)O=V")h)39#=
c;m3Nhd,'fT3(8.I/+GEH6"4O5!GmeY3_pPBec!0h#m@g"rK0%+XM[0[EbC$W!2fDj2%=amQ
QTZ1eK(C+$N.!Su<Yi\\6[cr.(Ku<1XGjXSW[>EBHKL'Y8C91//oV31Ab5QPI2UMIGC\r%O4
F+MP;t8%\NP`BHM)!ofqIn?TZCgl>GE/.NrOVg.nuPQTp<.<?2G>'e%F^&G_mEk+0Q!3VT:8
k-ZND-3[QAt0E,+6bbI61dnb6[b%M&I1Xl&3tIpF&KC%Bl0d_3DHddLcH]A%AaKj0+BNmjIW_
h?IQoZ[pfFCg]A.T)jh)Y#%dtDJ_@hT?oB\XQjq7DAcoAjq>W':%N0`hYQ#suJ_FA^<O+>m)j
h)Y#%dtDJ_@hT?oB\XQjq7DAcoAjft_E32D?);%Eo%=rrsg#,.hXhEgm?Zg8Q+i_krDZgV@W
52D?);%Eo%=^CdgDn_JURRif9_=o[mEC&:i*-\&c*IfK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="374" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="70" width="374" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="url"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
<Widget widgetName="BACK"/>
<Widget widgetName="tnm2"/>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="108"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_khzb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_zb_dy" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_num" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="10e3db5b-ac61-4293-a845-2238affb15aa"/>
</TemplateIdAttMark>
</Form>
