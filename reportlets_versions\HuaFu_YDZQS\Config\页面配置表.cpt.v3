<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="name"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_PAGE
WHERE PAGENAME='${name}'
ORDER BY TAB_ID]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_sx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT
PAGENAME
FROM (
SELECT DISTINCT PAGENAME 
FROM DIM_FILL_ZQFXS_PAGE 
UNION ALL
SELECT '点此新增' AS PAGENAME FROM DUAL
) M 
ORDER BY DECODE(PAGENAME,'点此新增',0,'总部首页',1,'分公司首页',2,'营业部首页',3,'总部考核督导',4,'分公司考核督导',5,'营业部考核督导',6) ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebWriteContent>
<Listener event="afterload">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[
_g().curLGP.hideSelectFrame();]]></Content>
</JavaScript>
</Listener>
<Listener event="writesuccess">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().parameterCommit();]]></Content>
</JavaScript>
</Listener>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.write.Submit">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Utils_Submit')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[submit]]></IconName>
<Verify failVerifySubmit="false" value="true"/>
<Sheet onlySubmitSelect="false"/>
</Widget>
<Widget class="com.fr.report.web.button.write.Verify">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Verify_Data')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[verify]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.NewPrint">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Print')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[print]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[export]]></IconName>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
<Widget class="com.fr.report.web.button.Email">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_Email')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[email]]></IconName>
<EmailButton customConsignee="true" consigneeByDepartment="false" consigneeByRole="false"/>
</Widget>
<Widget class="com.fr.report.web.button.write.AppendColumnRow">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Add_Record')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[appendrow]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.write.ShowCellValue">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue/>
<LabelAttr verticalcenter="true" textalign="0" autoline="false"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<EditRowColor setColor="false"/>
<WebWrite SheetPosition="3"/>
<RptLocation isShowAtLeft="false"/>
<UnloadCheck/>
<ShowWidgets/>
<OtherAttr autoStash="false"/>
</WebWriteContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1152000,1152000,1152000,1152000,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1181100,6858000,2743200,6858000,6858000,2743200,6858000,1600200,1181100,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="7" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF($name='点此新增','',$name)]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$name='点此新增']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.WidgetHighlightAction">
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="2">
<O>
<![CDATA[子页面名称]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<O>
<![CDATA[子ID]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<O>
<![CDATA[区域名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="2">
<O>
<![CDATA[模块名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="2">
<O>
<![CDATA[区域ID]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().appendReportRC(1);//点击按钮新增一行]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ImageBackground" layout="1">
<FineImage fm="png" imageId="__ImageCache__7EE27ADEF3A959D32E83AF92E160CBE4">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n%,mbeXK/jZic@"B#mr>oZ3]Ajs2<*0Y=U)IQrTQ\\(?sK)1-]A`[
15E5,p'EA,.DH+cB'1e(sEepS/NEqJYmi>f#koC,(nh04-u*'&P$rfZd)k8jliG5&!+d#k(Z
qhCC^8EhObrdCUdYEg3^^"=PZkIh6mkc8>&^K8>tFLIoBO%;(T<9W=!mKdKQh'B`/YYfE^qa
K:9ikKH[="pWoX3t.CEP[/j#A+4U5F'7[jcia=~
]]></IM>
</FineImage>
</Background>
</initial>
<isCustomType isCustomType="true"/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="TABNAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<watermark>
<![CDATA[非必填]]></watermark>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="TAB_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.NumberEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr allowDecimals="false" allowNegative="false" maxValue="3.0">
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</Widget>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.WidgetHighlightAction">
<Widget class="com.fr.form.ui.NumberEditor">
<WidgetAttr disabled="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr allowDecimals="false" allowNegative="false" maxValue="3.0">
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</Widget>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="AREANAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[区域名称不能为空！]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<TextAttr allowBlank="false"/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="MODNAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="AREA_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(F4) > 0,F4,CONCATENATE(StringShortPinyin(B2),'_',StringShortPinyin(B4),'_',StringShortPinyin(D4),IF(LEN(E4) = 0,'',CONCATENATE('_',StringShortPinyin(E4)))))]]></Attributes>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Widget class="com.fr.form.ui.TextEditor">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue/>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</Widget>
<CellInsertPolicy>
<InsertPolicy>
<![CDATA[copy]]></InsertPolicy>
</CellInsertPolicy>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G4"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="3" s="7">
<PrivilegeControl/>
<Widget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var cell = this.options.location;
_g().deleteReportRC(cell);]]></Content>
</JavaScript>
</Listener>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ImageBackground" layout="1">
<FineImage fm="png" imageId="__ImageCache__CC78C69AD79D23B7C2F568E7663ED7C7">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n%,mbeXK/jZic@"B&/\>oZ3]Ajs2;o@/fm!rd';D7R+%$>ZP8s,2
D\*8Do!p,NI\=hS*Tflj3XHaNX'/d3S7$I"MU_oWtGU1YS!.\8Kc_`@kjKiP$X5J?A+ImK>8
bD1=Q&fc1@D2_iVP0P,LV4#c-"_t+VcSdG<%=#S:^$iSoB_(>+Rg0pcO$D@qAGNGTiS89*MV
`aScm>VbgHP4KR(`Kp!ZnJSL[2nHp##A;bQZZ%09`P~
]]></IM>
</FineImage>
</Background>
</initial>
<isCustomType isCustomType="true"/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" cs="7" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657410" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<ReportWriteAttr>
<SubmitVisitor class="com.fr.report.write.BuiltInSQLSubmiter">
<Name>
<![CDATA[内置SQL1]]></Name>
<Attributes dsName="hfzj"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="" name="DIM_FILL_ZQFXS_PAGE"/>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<ColumnRow column="1" row="1"/>
</ColumnConfig>
<ColumnConfig name="TABNAME" isKey="true" skipUnmodified="false">
<ColumnRow column="1" row="3"/>
</ColumnConfig>
<ColumnConfig name="AREANAME" isKey="true" skipUnmodified="false">
<ColumnRow column="3" row="3"/>
</ColumnConfig>
<ColumnConfig name="AREA_ID" isKey="false" skipUnmodified="false">
<ColumnRow column="6" row="3"/>
</ColumnConfig>
<ColumnConfig name="MODNAME" isKey="true" skipUnmodified="false">
<ColumnRow column="4" row="3"/>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="false" skipUnmodified="false">
<ColumnRow column="2" row="3"/>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B2)>0]]></Formula>
</Condition>
<UpdateAttr updateStatus="true"/>
</DMLConfig>
</SubmitVisitor>
</ReportWriteAttr>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="false" windowPosition="1" align="1" useParamsTemplate="false" currentIndex="4"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[setTimeout(function() {
	document.getElementsByClassName('fr-horizontalboxlayout ui-state-enabled')[0]A.remove();
}, 10)]]></Content>
</JavaScript>
</Listener>
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[setTimeout(function() {
	document.getElementsByClassName("x-text fr-widget-click fr-radio-radiooff")[0]A.style.color = '#419bf9';
}, 20)]]></Content>
</JavaScript>
</Listener>
<Listener event="statechange" name="状态改变1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().parameterCommit();]]></Content>
</JavaScript>
</Listener>
<WidgetName name="name"/>
<WidgetID widgetID="e0c74257-7718-4e50-b21b-e06c83658d1e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="radioGroup0"/>
<PrivilegeControl/>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[13]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="PAGENAME" viName="PAGENAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[总部首页]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="1196" height="67"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="name"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="false"/>
<UseParamsTemplate use="false"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="1"/>
<Design_Width design_width="1331"/>
<NameTagModified/>
<WidgetNameTagMap/>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="true"/>
</Layout>
<DesignAttr width="1331" height="71"/>
</ParameterUI>
<Background name="NullBackground"/>
</ReportParameterAttr>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-14462827" hor="7" ver="4"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="80">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-14462827" hor="7" ver="4"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="12">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="8">
<FRFont name="思源黑体 CN Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-2500135" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="1"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典浅灰" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="para_sx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1691568349571"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="822cb6ad-1160-4a85-a87e-277f476ee96e"/>
</TemplateIdAttMark>
</WorkBook>
