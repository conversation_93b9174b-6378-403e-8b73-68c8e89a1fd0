<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="Embedded1" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[指标,,.,,金额,,.,,同比,,.,,单位]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String,java.lang.String">
<![CDATA[HeR?GZt&4(D9Fmj/^;L!%J1&\/1`cQltCn&g<B5aU&!An*I-YS/:+nAkK/`e:V.B+S$MEqlV
cN<E6CK!Z0KH6?%h:2Y$:P"@lp^gY5RSTN`Ztq!!~
]]></RowData>
</TableData>
<TableData name="para_tab" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HT#Hie(1AAHJ4uCB]Aj]AQDUGeNmE_YIi-O,6n?c^!1U&-+7"ccr3ruTB!!~
]]></RowData>
</TableData>
<TableData name="para_tab2" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HeQ'jC&\[GQb+WZS@"anLnE#Nbk6AGhO!UC!<~
]]></RowData>
</TableData>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID, MODNAME FROM DIM_FILL_ZQFXS_PAGE
WHERE AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cfzysrgc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm}' 
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT DISTINCT MODNAME FROM DIM_FILL_ZQFXS_PAGE WHERE AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群')
ORDER BY CASE MODNAME WHEN '全司' then 1 when '钻石' then 2 else 3 end]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${zbsx}'
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   WCZ,
	   BRANCH_NAME,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
DATA.BRANCH_NAME,
DATA.WCZ,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
			A.ZBID,
			A.ZBBM ZBMC
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${zbsx}'
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="SX01"/>
<O>
<![CDATA[分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where 
1=1 ${if(SX01='分公司',"and tree_level='2'","and tree_level='3'")}
and branch_no not in ('2097','2098','2099')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.obj11 = '';
window.obj22 = '';
window.tbclick = function(obj) {
	if (obj11.length > 0) {
		const ment1 = document.getElementById(obj11);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj22).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07';
	_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
	window.obj11 = obj;
	window.obj22 = ftname;
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$company + "画像明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$company + "明细数据查询"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%fmP$,E;SPr8.R'h:GK;he_PE!k$N`.B[m,$t\9ncLa.&Ks)3f7B9i";ohFNhXeA#>rA'T
J<3!!Nt`+k&Pa.]AA/J-k@G^#=(]A<`G4kPp8<?+CObFJf<p(n^Ne>9epX`Kh9UD=hg^1uZE`7
sD.R9iF82[3-`jC5F'eio^;=-mCjl!)b%g\?iViZO'1c'M?d")Z89NAl<@W*7<m*/$Aap0&)
[<P:]AX3A#o'`goMR`K5^D*kF4)m8tc)Jm5&mc%_Y5)>MDK8$C@="KX[s/i4oSX;F\OCTS*HQ
;8X@C^#)/Chf;Y&=[p"u'*NI>X2oI'_"!o4P/^6G4)VL&8ZcqU*%%['"56cl48=18:H+8MhF
]AX&tKpK?h,[E]Ajs=^<R?8GXM-<i4KKjo=cfH0A*?p=E_oq=N^Tc<1<XDhBZLfWU9FJ,-*7pp
HXlV-TBgAF$]ADj8Qr$^)`KE-2[.3O`?Z\5MJ3qaOFnkeS<"inS>$c<4)+ClQ4#a;[\ur%5h-
0ge]AjJ;+mug(h,Fm)\!*_?B%&u(ap=L[!RiuOmo4tO2Se]A\h`+Wc8_ZOJZ&NOa8>PK/\39+*
0M:OQn<5]AX`1=dI1n3V!(c_>+ald]A;;a>fZ/lqIL%QSq_nan7##>EhmARf;2<+E/OJKuAocY
^\"]A!fiad;@]AP3dCfC]A0%e5G?fkkN=pecOS\BWsqNU;=L[Ic=BqloSDf.[[cRAl6fk6qZ0V"
]A_6gFdk=$onm?n6?M"&?_7WU%+WCVbHNa2R+h]AO8mWYS/_hZ9J!inAu1H0?OfRla&3u95>`R
U-MbW9XI,"Mocqn$dhID[Vh1a&@"`AuJ'1bFNU)X8XZ\BBdfZj9]ANJ7O`)eY=?TTAnT[`EgJ
T?_SO2/<Sk$93MhO0JMo>>qVgN"#\M=B6$VIOYAKS-oOg?K#hpe]AWR#q(B8F4K2c]All?kdA]A
I;W6hhsf>qGhqa"RT8BFZR6?WClMJ`\hVQZsLj/>hJu2JD]A_]AD/$GmaZhu$U_=$:;M,0Cm%j
&;rJOs7Z(o8upo.)hq5+s.a)dFAF`"AdbW2CbY.8"H)G;nXmZd.dGEJ5`JDSk,,/KoPB,t;\
B*$!mBDtGKP+F)a%C2-EO!&u[*E37;0bosNVi6I4KG`h?p`.0+".m;=<(g`JQofJ0J!Mi^"#
1J`db*W@>=%I:AOVfXOql-34;Jmd3Lf<@GhQPua5Rif_Mh`#XD^GC]A)EIR`Z7c*Tm5]AqaqO]A
IH(6)JiioPRYW1oYgPlkN2]A%&ML0(iEW"YUlHq&)qSodL4plVMK,$EsnA;C4*U.u[YLgW/%C
<P(.GU6[M+U="HOgEsB\Yfu^TI0u*]AT2KL/PtPl(W/4@br79VQ+5-:#JSq2o;(uN1t?U6Y)8
n4aYlF=gpWJ3D_J)[+o:=O`<L_qYqGi@d,6FY1=?uH[_)b.b6V[Uk\'8%>UjS!k/iop%\k7^
qY;JZ@.q)X=l65"Vk^LrD6<I_UQVhJV]AKMB8g36&OZ09YL=m3HUtAH`.*"p<*HXQLg2'C"79
\#S9n,CF0W;%1d>WTdqXtQqRh-SVSfHe_S`()4>-T4[Z?T0tKX_h3JEb#CBBDVo4f%0Fn=2*
ZLkV`:"]A0Cc[?^Pi>PuF#P#,N&9J2AR1TI7lV3u_Chb^E3+KSs%:k)!D32W^s_+3g[+A6]AA]A
9nGqF>N`EP^u]A(i'Zsb:O!(%/(^A!3F2%T(&94n[P.=$PkhTb8Q`kVgQ^/ogr9EAI9':6JM^
^sT'*I?Zr3nG-U[#$\@==mA1(<k(fHi9&r0#>$HA!fEG")CpOC*Z_X?.b_Xr!3PFefdZZRS+
eelP+3s\:Z;SDGG2M2(NYlLd:2\=T:3eCTr_(dMZ3/74b1MQ3uEcQX9cmBpUmul+*2ZF1OLA
OPmZ%KGD(MlsuW@m<n>'b]A(RS%3_;'NuA0C<7bis`8LI?RFiY.COd]A/kaE#'<,kV%:'d+nJL
+p4I:effAKb$d3*C!,PKm"g:0mq]Aek]A$=(upOCc$41Q-n,a;\.+:.KLVCqiicWU%)7`ignI]A
<5(?=Y3X,E7X.8!DJ9sXa91d]A+(t_"UH\`*FaOrG8O'[Et!Ln(&\<g:!(98r;u9dJ=E9;h9W
I*YeJR3g*Y=!d]A`@'[OV'PP]A77)ODc1&?4*m*a^Z]AhYPk(o\DnW,Iero/`dJ@8fRSCt!CUmT
gE'MbB+KPEpAL1l)^@nE>J&/'G#%.]APB^&nIVA1uhH1p.Z%e^6N'/NK180I?(ZB@UfW\>3N^
lq^?p@KdDRht"8JXOn7`<(kCW$hET,Wt"#C&fp;PYE-F+mpV^jUB,=GOtL%p+/f(NR#LB>EO
<F.o(F^"p;$/,6%cf2e$^R\Bd?\o)\^n'Pr>RA+6W$PX/qO0$uQ17Hp\%@E@F2;F.3RXpDJl
6J*fhh,PO&2K%9J[eZ"r@>BJ7J8p=(_PE`.qNrOlJLZDN6Lnof.RjNr_37JCp>tSf7B86bK'
il;Y8th6*eQAr>YtB:@H+>RhobU>q]A"sq07uHYbR/)\EI*%e_SatQC7<`("pX1Eb(>CN?R,O
/>T-IbB'KYADi@@%++"g`h4]A$_m71":6Jbq.KWe;5_$:(44c:ZrN95Mq\9:8o:rsMmFVhgh2
KJa]AD;R`%'M(3G1"JGr*<%f$L9gi:=gM\Damh:FPBJ`+ej7-G%EfSJrH1LQ&9JD!m*J?>L\I
r$o%Gi@BXD5QcaE5$<EMAIL>+$_)3o`O-/N,58AD#H7ILuQuibOWi`K.2U^X6P]A;m%M
rcZ=eI9d[:m#lV!\f,M(1.um/^=_RlAk)"<@"*9V;[gDYn,&.A^k&KQ/7<C.b4?H`Ab<GKq/
kIpE:MXZd17l\!)9.1V,nd(\9$f$kkk%h4?Pq<]A9r9YHSlfS^38Iq]AH_Tamdk?XKO,ZF'sLI
a\O[eW:l+ZV*ZIu<_'o#CJl!H+IB\a>W8'B1b'bJ0<?sRZ!,eaW^1^%Eho\KeKhG0G8WEUlb
k';Ok/'`Ld62a&h$V<*dN%W9[=B'k'=Ao07,Ha#1@`JodA_S$<b7q=,i`>C<S?#gHrRq=u7H
uCU$XbI-h96aHmHBi-rS/p\jW_IMT4#"n7Dirt:qKdIONR!1O]AmSKq7/IH8BtA/*@YK-+HX4
@<rk&2KS+L7J?odra5#d:>1ZPCA"+0N@#@X"gdJnsL=o;>Vf!Pg&!rg"Ftmpkj5,BOgak^jT
5(^]A<Y7f0YUL\q?'J%X&XXUB:i'?:X2s1p=j3?h7DJ*rra4kPoR7O+IQ\V4'?7aub#jE!SlX
Ib_4_2Wa*0B(;"2?^$HPqf@.DDUccbO'Bt_#,efpetN<<EJ3[]A0S^l=o:A_'O/s/i16)?5Jm
/e+5noKaYELal3K!0L(B,%)*C5AbRr@@ERC\^UXU.%G1_?3jEaZ5g3mgCO![VI/>Gnb6/!3H
Vg9QI;X)u:o^`*[s'6$8/flqrRH/TK;7]Au8#+[FtKd"?I!.(5!i4&C2G?jbDLknWuX*Ze%M5
nk4H(YFq]AePEjX<;!f\/&e0[CSS)"2i2sT:+=F,e$dXXqB*)k5g#_g272p>"&_Sq(6bV;F;P
ZlC4(KOk>h3nIK[S+.G_Bqpqh#I(;GmUK<A3!KC1mRr?A7<+bMgk#fTo/Mj8O8HT/dUPX^B8
gP7-[FPbTI+p.<p9DbC:\s6YFjT<\0.!`Ei)P.oX;\Ff1(Q-@]A*jTfg2c#SplOt$W%d%K83(
R6L1]A6mt&Q@C2bD@n^l0YTV-aNVXqmM/O?1DIupZm2LHs8g_E_5;uTZ6%kdT*QBc%i3B/*qc
\X*IU<T>]AWQ90=6#"Wqd(Ok2+0)3]A.An+o)VIJAd^U=Z\t2_N#u*95G=DgHSKXAq7rr@0=6q
d,M<j76R!po5&eU"_:,RgrLch^9[TD+VTV^CDn(Ddqc2pP#)EAp[9l9(O!99:hN-P<I'TcIX
ORN%%5%P("(_cu;n-0Bl\.p=SS!:)<!.AMeB46$UhJ1eVW'>0iEd%ncGEDQ)<6Si%TB/6E%c
rnWEQm(4]A.#NF("f2Ia/;BujBcs*b&ECn>P6)oS3ND5*&L5\T`3HOrB9D]A4<MigEf0h9>=j9
dii_G,tL?"X?kB&.4a1=5)XZE")LEDrnd3(d&BW\#Y(db&ZQ)dbL,H*+k)6[3k=&gN(u_C2n
O(Xh0ll(^Pl$4Z[g%PL8QlLK'V-%h]A:2l*@5E4G\p*i9Jp=K[F7YbG9mqb`h"2;plC9#k)E>
8>t\F\a`4`*KJS4r4tf`O\)\4V,!G]AQ$An9i5:u7MPYgDH2&.?*HlH<3!8035]AI\Cu/En]Ae$
TM1hl4)Fc!bV=BR-!%\DI.1HYcA=Qf(+9n(QSVo1:b.W/$?SBkNq>XqIIk`AR_VK!5aNR_[]A
ckALSmJhM5I<Uh<R#qtVmO/bd__T;_k]AahuOe$@$-(4>'(4=oJoL4f#U@F:]Acj;+2G/3P4P0
(s6f/`uYBa</LFt^<T4Kj`'S7G7=4elB:K%UcZrIYgh[RQaFSV?ZH)iqA$F?M8,>qeC$04TW
6SdEDCYJe7Yf6KeEaBa++O;O(GX<]A,<.uOj3U(F-X7i4g[2NSZ#aG(_im;[IscMQuNU"gh*l
RlkNFIg>mlF*htY/$JG%?@X;L7#=+*bt@cGr7$rl%!e\HfeS8EC4//)<to4T<c1*n"1BBg]A-
6&!t:7f,(OV!rh'B@4;hG"mC.lF.G<_jm$kA$Ki5C*!4'5QJ"e$g:(8+fr;#co-D+WUa1"fB
NliT\oF5,6^,;:,_1#3s*RdO_TH9Z?j64gfGtH$"XB`OXY)$"/e`Ac(dCNh/[md7nOG(M6IL
j`Ra$Er;TPR\^9lF$,+;J"d4J8tiA;WKGM0#\`s5ULEq2ePQGsB&:1mh>8%-3/fFQX/.?T5r
aj!EFGrg\<=T_%4'cu=h&3pZ_Xm[eP=\RMle']A"r)A&3No,D9?L[<1X=3(hZc4W`D5d!EmBJ
Ts'1)#]A^&4*ZTcF?[7Ii]A9k+E@^8@fDku/>hD3joeEs_e2a7b>CfI-6']A"I>tt6qh/&JTIFl
$$*C,2*kA2LXW[sV.TGqT,:Zgl$X"U?Qa#9&5,T0blO%)c0GBT"<QtA&C!kQHc:JiuZb2hI>
-Z1n.EK:Opaoo<W7M\fc=9>lXomo$n[kYnp-g53Fm(Z9#HQl@&m/FOB!s;rAeP'PIRu$I:ii
d(\@c,LH)fpBN=lQ.`1BgbIGkF+)?M"$kk_Y</]A3&8fMuBT;CIf]As!l!\Li4>'C<o#'ilj-$
HC2[-2F@1/c;Eh84L,ujc#^EYndQOFAeJItnII)12F_eTWr=YY3?G4ugQgM1i*S?A%;@`Yj=
k:PWaZ'/N;6u)l'lZ%1YbLBE9\.N:gc;0HHJ'318c=+'Vj&$LqWl%^s3o.QW!)BE3)#nQqNa
N"/)A[Gs88h.S$gtX>ZB:u48)Z$4[rE8KFo.eq/C]A^)L7lq1cR[:hYsd$W*E5f``1:-88lGR
!(GRJl\\eC9`m:0>H1RbFKsE-c2Vt3Us8:,++e0l=(Flil`o+=UDlBji7QGJ^L`!R0obl_L$
@ock7cn#nR*Z*l#++QC&:m$D6,q-6F*Mqbe:Q!k-7&pFG`H,=Wf<p0[X7:'N@aDacphM7:dG
'`T'"$p[eY!_1#hJj[1^Jbq3ic<`W52n*]A%Is1;2KX8fhA5mplCJek_d"YFCR$X1o/(:9_</
SIEXrh$FO`!T4CIKaP1GtEOuG.%=?@!@(qN6<^/q=86ee`e[/T*&5%]A__&%Ysl$U(===07#P
^j/YY\@L_eA^>""9^&M9_G[#"1h:N'B#cM0s"e*&P-4ojEbpt+cRrrr~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="46"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="46"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="2"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="626"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="71" width="375" height="626"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="2.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[370389,304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2<>'总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="0" cs="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" cs="9" s="2">
<O>
<![CDATA[客户数-总客户数明细数据查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" cs="7" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" cs="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m90HrdV%0UlY14iEk@m11Nd'm4*F\?Au0*+]A;tU)=u0#SZo#0Y+hn!aU,?#<2I/++$`-m<"G
gCt'EftbA>YTjQjAqD-somU'EW/gch@"dJ&(ZnoAU#5ofhj1F.IAXk?R2?MpI+G_7MpTr7o]A
9_;XFfHZoDHi33p"q"aC]ARtUK[i/**(B%Yt2h]AC&d-q-]AD@qEu3`t26Nh^3afOWG!n39QfrI
)U)Bi?RXHn#HrSrp3-C_jPQuFiUd0XoPQU3q\CYY<OrRe8la8d&[cf9"Bm3dD0UVCcNYs.<5
WHVY'nue7iL&8_NIC4\Kn4VU?XT6`7>"Pg^Clof7QdPP#ajV>`9D7A_<-&dVgKD(f<r11cHA
kTcR1Q#ijFi\KFYH2#:OhlRjSZ\bKu<'e>6HcL(#-Z8qL\^c5Agg>\/5)otsj6U4gD=E-a/.
_N:W<BjTjF;uUi#e6g!<9OP2KQ2%:M&Dr2K^+?T(cQ$"%Thk0CQ%h\QCrnbD%W%$iJi\*U`G
unGCWec+Dk.ipDusnhiP-s'Bc[@9&mZO>HL7]A;0XNZT't=[*0E%CiN/'cX27`0[mL"quVdWI
K&Q\b*f?\HFA8:?K?s3.Lf7ah@S,h-El\\SL[#TXGL<Y1F'sPA7TMr0(a+)$<>9Pl^rUUCBD
JMSgTA\]A#AQKC'06fQ!6o:r6u9M<+E\E&*XN5IW\fl^r_Y1[4(K,",hbBKRiEVlFO)SG-GuO
-Hh0.nf&C-gg*o/Pnj]ACfDHZ%YcB/>1;sUb^kfBU!o-+\4Ie4bjbI<PG53gjJD)J/mnrY`oI
Y,f01.6#M9(m,#V^OP=#Z&+CNL8u?bsU.Zm%uV1o\l-Th#c.OBZ>>$F3rB>XU^Ti\kMjnpZT
j/JMo7%*C*dUB`>HboofaZB;%4L@b=I\JM'Do"/EZK54NHLrR7l-J+ueM>@eZg:\8[JSU(nr
ohp^*SVT2o/&aZZEBXF-)pWrkZ68$-#["iaeifH`Dk?RlUeDJKe#@-GcA%86BJYSPWh[:jn?
M[(FWlnY14e[Ls0V@VDNP'nch=_r*@p%bfjmLE-u)s%O;rZ6\2,tI/".CIfA8"Y9C>oLo+cU
b\@/_>HLa<FA"02L,tF.&>liEB<rs1\]A+nj@l\cakq(;o@(nF&k_$tD<@8$WU7G2q9d@:q+&
[hiXqUA2`Dr7WgDYddbJT]A&OEJBPKKWO*9*5K2Ph<FAcoT5q[:RcVlY(![/2'EhPV%rhah8H
2mRC@:a?ZgDOW<BPU;E:um0c;n$`7eUd1*tZB43>'8k<NgmP<L'q#jI5!iPc69]A`tUNOL`Q+
7AV:0M_[6dk%!M@urjk@ql\["tqY:"*O$j[_?jY[=4B<.'_,dd4ZT39ujPN<@KfOl(R_CS`.
6/n3\GDX"OSXJZYN%&O/#*.q6Xb#CmS9%ap.'FRpiaJ[:NL2N;PdC#D821FO/BU_oqd&-%7G
=Q9"L-?.,iRli6^$E!G2CGO&:pP);m:PMO%$fqE,Rr(78i]A;`a4WD_U]A^Bl2n4>"-J>L4Ll*
Op$7V$u4>]AA]Aa.5I0[8i,@Kc@7Ujf0tBK+:!WQLJPAe7I&hk<qc09g1CFJL-6_;Zme(nQFT%
U^-)+.2MDB'8epks,=a05do@@[<ujq<MJ)tJQb1jgI9Gd)FR6LFEC?V'8#O\s:G5j*eIOJc/
\tuhh;030EL;;,bFcDgZMB<;F$1O^*5L0A$i'_iDT7tU"NiQ#$mdufk&*0E,!C[E3Y=BC*hu
tE'I.9PpVfr?hJFWo9?p#SSnIcn#dO32&BW$iW=\*OZt4dM]A>J+/'M-Hj'G)hUoiN!EH6t">
S\kh$Z'7tr[s2qhY,5%T@b[)+!_$-uq]Aa&GS+)ni=eUqD[$hj3"u4g*BoOdU5KR-T$?G_u1A
KcE21a2&hcnu7e0:^p6H;;B3rq,3/d9M7-tKl.?D$odc3lXWaQGOYQ*R$omMmL.MBhZ5'm0_
`e]AdSB:[M2fjTTlRHD+gdn-RCALe!O-I8A_E'n=QN*4c"j]AT2#1ZNAso_c6Kj88+eE1?p0B'
9Fq(q!+!eI'F7OLj(tTZa`Wgad[Bl&mM#f-5(/'2%el@2Un,6h70TRGT?PsPTG,u:3,aCHVs
PHmQr!^9od$QiX.!qb.!;[>c.#AJVTFWaFu0u>_\@p8UPIB4E'>Z&j;eZ92oHPBF1VO2"a;L
#,`&n:W2O:0YC[i'XLC?R!L:OBh@)&,T^u]AZ>*e`cYZa[UiUmVl@JKcl/_YR@LPdm#Aeb_OU
)_@)Q!J7V&pJQ\"3,:Wf]A&9[G*5+\1hgV5J)6s;r5b4SBRj6F[ocIHcj%Y1[?`gYKo2+q3/R
N^dYuUg=E5U!kgVV'WnDghQ4U0[A9^L^Rj8."1CiBDTchlG34,%GX=I%[lAi63:`YVXq$6Eb
gLI7fY6[jELY(41c<=>D<TTCk=l-l:HkVLZ`Y>5'<kqfh5=;[K;.nh"Y*V:/>FZ/T!rn#Q+/
is[7>L`2nd\^/d97-7$PW)0pj5sd3eGd^ORQ?F(]AJ_NX=/Pl/Zbj=E]A7Q=&+-!62/r%R.o6p
(s#P]AWAR/bJ-QP'Lna"b[IE-8obt*7^Lq>3W*ucq3_!6e/Rq,-'UK'ss1[4AHYgZ1WQi'i'R
f=#_i^7(=EIqK^pP9o<!844qKjg4$aQ?$9iO+Oj2fihI$701<'(#s[8diO=,@m6UNS=_WoHY
,9EkjuFYJMQ;I.NqjF^kS(`bVZT+k/2kZT-U8%q$B#W)F0NtE5AL6K2SoinRi>E*YYJGMF:U
8l2tN18NElpL,Lf8`YZlCectDW`]A7(:&^gDGmqmG<cD+QX2V:iR1hR-E%+^=`PQDNNC,=lhc
[Pf@&p4hO7!\CkWd@B]AJ!ugs35T'D_o['5ZW>-=)[td5\A-Y>V?ccN#DIGE2Q%Gbj7p-^>ie
;ZB3XO.Up[[YD_)]A/*n0RH0gs]A!,40Kunh8F?HaZ?9iu3)UFh`9paIm+$&*:R'!m`R+HAf!>
7MB\&7_a1)^TrNl;5kIR2O-``A?sB7'Rg`+GSWp)XT=0_)RF8MXZk_YB'Y@86`%i[\B;QYNe
9`mA%)*Ar_"8%su&f,N^9"Bj&R0kb6[q/Y<"gXsnd28dtB*deYkm80g+KMhJglENPb"+k>TF
/(VOpb)Q^]AQ"b<.`kq4i.^_%r]AADA/hQP5fjd2;^(#5V@Nj[X1fKdV#[8g-A:5T$.m(aJ53p
M#+QQSMA8gmF%qu>T?Q"%W'7eVD^3Yl3"J/h23p<8CE!+`*`9E!CKX'pAbsWZ[[p5CjgbPl%
"#!EO4VQ=g7LoU\nr9[sH\(FBAatGmY%huK7ck>K+OBgpcMoU?2d4>d"tb!tO-ci$5<eS;jl
Y=KX/p/sMB.ZlTFrinBks]AoaRPo'HrX_ob!qu:'XQb%`=M"<p@9kK$p7"^8T+Ip%)%G#9m8=
M=lLAJp>IKZ>NjGHSg;_6blTRg3UeL0LsXg9R#"<EMAIL>%FS:e]AkV]AX>Gt1dTYaJf_VIOR
^0YM[['Vu/<h.q8L/"/VUbZNrcP(;4KeNVh81g#11_YBj\!jNI>g0t;RmUG^cQ2)&JeOmXO\
o'Z%j[(&eER;R/&J86]ABXKDmEb-2N!s+:]AsI0XRcg2`&,c^O[.0pqu&?tZ1\%&3HnfSWDrM'
PtZQuTML$MH9$.31@AE)PG$jkg^>E[$di[<Ua"XX!9`;1q3=8TLAX5"8C=U>Z,B^Dhk%F&]A"
*iO6D5e)!FQ<J@r[L0YP]AppFm8Lk8=J>V)Me+0.8^t>;p>&g?7*s\Fe"$1?+kpWDMW2RN$u6
X-Fp!X`MR"lPUt]A*6B&okBg1/!'1&dnp4_X%9jq]Ah59?(%',oZkXW&RKc*_:X[d<2ng$S^Ik
2\5F(^ra3al^ZKFHZO6ALSk7e"3!E^k:k$Yc]A&'#3ic,ds9kk6HEUTs)8ePI.R9<!2fAq@EW
a;o.l2'm7'=o4M?oSpSR>g-TSNf@)Y;,'TcTjb_;DY>7ED/JA$:G+]AKOU=aKNJY5:Y(N^uHS
K:C.#GmRL'N^Mqs@@f0;F]AQ;D\F8Uub(fVt5-$Uk0jH26_Qk(O)7/^%DWhWXZQLS=:uGN.<M
OW>.PI"KE*RecHWalT4\%Y5.b@30Z8MurR2oZ*Z9p/iOQb>7+1NXES3/H34Xam@ZM$7d>1d;
)h>>%nD5MWqX[O`k.=C9g]AC2%;b[gs]A%*52n`SiM0YYAG,I\]A>+RbsKu)A0Rq&$'WlW"AW3=
-#VhLXJ.30/Kg">br[0V+.%sMKQW4JaE.#.k'9>Jk(/QM5TF9pXTc^V)%Hd;P.c>4jjW3HoO
PkgjXo+\44VSH8#P8H&m1V1Kj(l`9,E-h.VsUgd&S5m@'c.$Me0+<=dYn>IuT<`3'PTef<tQ
>6%W4+Jq'+qso^[Nl9c1kfiPc9,*1JG5rX2?QGcpC%-b]AV82_T,27FXJMT7$\[3X`r["S`bM
:^.39e?m\c*lRQ%$7MbSJ7-,U>Aj@XDJf*LjA2=UeY:@15JO78t+H5^:_]AA.96,*Gbj$!S,E
\>=<u%>"V1$g%BG\)C*3^;iD,T@")5P@dUT=@Y^1I_O1#m/l>V@[3Qi]An5P0%o]Alh$)%aYj(
&JKYHG>+^`=p2*B#[*U88tCTQZP8dJrb5tHJAj)W]A9nqU)U^[T"kRi5!'>@k:4GA'N$Yca<r
K)9(c\drn`iI_Kf2]AFa+rSM:eV]A9NTp$<+saM=h"dLP-4Tcjh0N?@nr.FqtP^$$6a?YQ>AEg
ra#spY)*6Q*#U;VB^7-CH0!qW2:0?)OS/IlT3MjMn=YCno8A$XMSiT>h!_MJh`N$6IAjQQAh
:'G(;_eqSs"MmlHcr.3(57]AW9Uaa%/KCpq%`(Ss29XX;:'8RH)mt&e7,T4DHJ`e/Z%G@.W'h
1Nr(F161NSM&Y/YIfp-[RA5k8ojK`S;7g7]Aph$MT5g$"P@2m$36@.SLH+'9Eso$"F2d\tpcd
QI(jOrp=kT[G7V\fYt&/4!Ur=")J'I[TbALn'D5T%TRLPeIX$a/(&_r1h45'/jP<hNUnAJ*D
%UoX1m^-7]A!B\#qhU=7pW7ns5Vde3WIeY05&aIU>OY/MC3Pa<d2@LE%g;R%/:02^%>CJN[l1
O$#[aE[\C&#M*^]A)WcjWCBD,K0POZi5Pp'bJ_[?PriQHX8!gWl3=,ZJ*C&g*nY/%PS+_R,`S
^&I]Ae'X&43uZT*SR2-g7]AP@TcMj\*SR2-g7]AP@TcMj\*WDZsX/+8:Di<O@UNP(7jnjS;s&jc
\pbKeHo\$_B3#kt2^)[gYE3Gh>PBPALg.=A?-gd_#[$,[]A:>/9$Bd`;DSc&LWmL!e(J1gK/a
B0/Eh$)Y<s0&1^rYk~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="348" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1193800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,7950200,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb_right" columnName="ZBID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOfCopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report0" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0"/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1" paddingLeft="12">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%fe;eI#!;f+gs>f9[B>U%p3g*/i*$428O6+9b&\@#Wm2j"8C"AD]Ae6@a:aPlb]A$&;ahB&T
tWP&-t!L.nF3eKUVuK,UU@'#U+jd63p.$0!hn!p?0`fbaCJ(k+)0+chDnOI.)IOT6#.)eCXE
[2Qm465ka/`GCKu%I/mD4f@X1M['&8'&BLb$I.sf4n<AbA@ZB"+Y,@_uNU&WtXV+oaoRYA)e
]A+BX2FI;&$-m=&/3?pYp@JlEe*Rj\QX"n#g@;EGD-tFdhtrV^Sr^OcLZeDcCY/$Sh48Yaft$
V,au8&Cl2cA]A[R3Y5$H+Z3e[Y42*=sNnSDhdGIp@N`Pk4J5>m%OsbkG,k334K3!`iIe`*.ZE
75fI,j03<0!%X%0Oi]A>jeR,\mMtX+c_dVHQ"9`!T>9A4Wg/2-BDZ/]Aqq!EKopT9#$9iWm%Mc
YMma`^b,ptZ<d7Bj2NnFj5UWDO/;0Kku`laJ3-dE^=q1#[IJjB;K2T>"G_Fi:4,EHFJOq`g\
jP)Xd*5DJoPL:h04:J#bfI.Kgc=M6,-76M$I>/"gA6t;\.DU=tNN\F<8:ZE9p[$5EGd4q25%
LPG[0C+s92H2S<m#Ib82D^,\*;2%VDQ-@UTj]AE[.Z'>t0PnDaZ$H2bV^2^-4(ug,kcK9'P9"
"]A=fQ(2*dWp/i?O<q!h\JJ1NUtGMkD\_M=`Gc.qPEJ;!uH>.UO[:r=`(-WdotWoLTs/Ud;sE
e\t[6,$aipieOPU,&Q<-\oqf:?USlO,=_EU3Q$V=3!9h/lC"1Hc^/1P.jUKW+7>a-(B2hn&<
9Ldk\Fcp9TC$%@$O=i9sh;f:?EI^0g%s@Psc<4Br;"k"B)An-6Y/oEb/]ANf_(bc&H/j,ERO<
lK\-u9Me[lsOF-Y,>IZQh\aO6DYjr3-M!cE;6e`$q'W7[aY=4X,o&@fF'X?Yk*h5;VG+XF@:
QC%+)Z._cB`XTbFueA!@8KT27ELMb&5Cm`<49cA-Xejc(0*r^5lF3rTClSp(<LYf-dqM"(;W
6UDBq">aj"93Gj`(MDCeZ8`.]A.NhL-h`BM<0("h\[RE?GoH2TQi0-ZbH13`Uc)KK&uR6(7'6
RWMe\X3=T2&LsTTdKp:"*bhAK5]Ago'\?OPIh;M?L]AYhFjoMW@u8Vh8g`)21)qittmS2MK(He
:M:bmVN\kuS33f/OtQ;7*uh6PJ\YPI!c)oNjc6p40:_2eB285ub\CHc<"GbBe;m)a[r)(n4r
/4rIGan#M5Bktl1!SRYq;3c_)Z%+R29kOpBP7.%NT(34_/q,rHEXtEa<kj_@ar;HI%UE!1u&
Lo4:'[NV$G=`Q>ajFR>X*nAp"#m8_9k,+iRHcbhq9;>uo??F"W2e>i^A@(pA.'I/.b-umNm)
a3#PoP_Jm2m<Xl^Wg.ck']AS?MAae'7BT/3\F)Gj$'V[=KsU*a`4*adJ>-dVGX#^9A8dWVV=?
dFl:LEhhMAmSqR[H'V<+q$)L+L]Ach2Sh)7=m=oL'7E<k%I$lA/6S;fp8#PT_7@g\ZIR%aD(H
&7b?hTXlDkSnVYe'"LKE-X.IFo+O3jhkan%(R@<E8H8MC2oTJ/,q(.#%?5bfn8@qTZih>CuC
%'"72)13cFXjGR%D`!#>=&H0i;o7G"em[&AU4OZJTRUti(%#h!Y"oVOKW^JU"0+PF7@,(Xhi
]APN.0gk8OVmu:)>aIdrF7Bgs\J#OtB+sD`g\9#oNLA_%R6F8e&CgH^ctRD!EHg7>;.:67@S^
#SIT3kU;@*\HT1!em#\!bU9)K>lkjNHV/,4K^VV.e98*@5/@)D,eb1K"Mmr)oJr1!<%ZC$ks
!AEW\C+t*#$#CTW5pPf4kS1chSE*U9p%$\$'HmP#9L&3]A[e^=k!L_h2VnHqEWEXDW=IJ=$(5
aqr7,)NU"EFs:)g*<rVlH"M2-Wl,&?1>r07/OQ).Jm4V,Slu8Zj,dZsEZ_L$nq0E`B9?NAN3
%Tq>XoW>f&EY[p*NiGM0J)AEpSGuG7B;s/'1[!Jh6MgKpAWWd>fL"eB3]A8qW72IH6h[+rkda
U4UU6,\6B2JcOe#SX*opZLg3o_s#So">*/HHer0jReFpi*`$\ksT;bJS@rR5nXCei[W,SS?u
I+qT$80Qh$#.VIJWan!52N\?hqc[O4"22Q\\SFsp!33KL^K'6]A4dT3(nF-Q4*=RbALA&B&3n
%pSe9k]A)[1GEIYjdEaabFkD^<[Y1<dM\G-p#,PntR<P^[f^;hiknptDKc^`&2<IR'8G=R#In
>M<Isp/B2tLkOK%*r"H2&$<gD'\+1$j(bl5l:jRp]AX%kO0J=,(kXYd<qKEgGD!"R6Mp\o'[c
!-pu9Qb!5OI%J$a`FlLE)I@C]AgeQXP>4)+%*q@IiF&2r*ClmBjaI,EF.W^\#e^bbsp#b<n;1
h;1c57X\:im2_mjUT1O=I>Aa]A%:=KYp0cBcagS^rE%!D*r^`Ke_pXSl;oHRSUSL#hfbR(gm.
<")ns[Z*f@=i8S1`"XHtUdWp]AZ-Zk:$E4RCgIl<2$Z1(4+"a5:sRGRj#qWUS>V;de.:c+T,r
2!eaZZ+IeqK(r<,Xk%:Kc-`f'oP;Y1`/gZJ/J7gr)FJL7Z5Md!ds.p`m@pBt`&hgWmqh`qYq
WBqR2)sTZ=2"'K]AE,IXIsnM3HI&=8%(u[jRUaj:5%!/Lg'/h?.k@d4$9Pf&ape='#,=NJXtR
k_V`l%1Ol`h=2@KbdQSq$6b0`sSSpnfndhp1VcK6'dKYDkO&-4cV>EN'`AcI\MlE]AT"Dh;Rg
mlEg(Qt]A%5?f2c)3X3to(HqZ7r26b"oFqD]A&i"Lr]AC$b08#g3L$)P6IO<&$_O<8.*a%h`U.=
_SrJM+(";9TuC.t9PSkEc`CR=l\B^^!,SY8[["`dpA&%_29e7m36=rJ+I)5%$\TE6U9!\0iE
.\HhmTE6U9!\0iE.\HhmT`1RaF-a-Gf9b!)b5hR)'C7m/e'MZD#@FdFN@p4`kk\0]A!!3^^!!
FDE!=1pj!YBk^"<[[F#f-HD>SH\ahOW/M*D8UYL$slP>o.hR_B/~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="286" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="89" y="0" width="286" height="347"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d6b23a5c-88b6-4adf-b325-7e28e43e0a14"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="5" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[266700,1562100,266700,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4038600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="1" size="96">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r<'daoS]AD7*W`B(i<a.lFcC@V4cFj]AuT.es_:M;t?+6babPniY;Tr<S"h59[#cWC.76<]AF
rA`VTBO+dM*.E'TifcKE)1,T.jQc5CUrMqu?D<s581Jlb%L,kJK=Co02@7jmjSY5+@&(s-K"
9Q-K<s^A5Ufl@#o4o>/kc*cb?.;_+p9]AD$gHooUeWC`$@kOr;N5@OmaoE8`T-l^,WSkAE6WE
IPm9nJ?edr:RVF\b5_9q*c+/hU)/JJT"JfO4DkCQ*h&0rXlKMm(`X_T!lK'pXp/?^[S;jlgY
O)8n.oKA.#cS>*^u8eCPL"8XB^+6V:L)*R7nCT`g&eP)PJY3SDcp^d.<fCJ`<AhHamklFFj7
ddi29JU>4o?af>\@rm36p5P?q7<?^Tm\=:^]A*V-mR_SAG'pFr[Ho0G;PIqK"h#(Nn]AMEWY=]A
BjqKP@Ji3k[oJ:_;m3/po79U,&[N]ATa(`4^^YUGOt@]AK"u4#T4=3OKt]ALd:Ql.?4%+jhkBS#
W"iV.oL="3:rBtsHh3&6[&RGE2#EcODp%]AXsIoWGI/7$tj^-C/a4<s)dm^g+Q'(;_Y`'rRSc
`=[DP[8INPFBF.U-Dce`O*$qr(b``\m0(I`V;F?M9pL:idJ\6Z_F]AX/TT4SKu?/[95j.Sr#I
0nF7CJ/8MO=#=^6H^)gRW6d7u+WoD/Y@Qeq4frpr6#10\!]A;;L+/Q$4*`i*joi105rsEC$\>
<kH@3,_qLm,k."%jK1]AnW.@>*683.Og.Bfm>,PJ_7a>(78ue$Y\ZWGG#(tlI"1cVC'S[Q`Z"
tEVpTQ'^#-`R0D-0d`?+4Tgo%/D/1DCGE&c-nJr-T<:`cPrEaSYVQ7ro7MEc")ZhXm&R[`Hi
d_s5]An)TRJQ@H5D`?ZcoDSkQk"8nmM37epTu87rl;nVhhm2!pb6E"6)^XDKoC.Kq1[AT>YVk
I4)/Ffp/8JK:0q4GH<;i*23$-G9/=T%<MRIURqCb,F]AUEDhYcqpURSaL?%O[rnW1h:3D&Tc3
+#5?K9%9[(ITnX'hocpH!4&(ZSIIdTOV95,2F>GUrY^<;q^l+@?nX@XccS#"nTS7Fa*%iZ16
X1bam%Wa^4#()p.&^oR_-Gu+,L,4NsC]Ap(rNQk$]ASOe]A[Kba8RiUF\*SYMgH(WW6$mH>@a0#
tTo"o.S1dQtN8^0,S+YejMU'TC]ACOPcQ\h'_0Gld\OJ.b1^_5Q*Y(<\21398og_5^unY+Lb$
&1VNFdH;IRN6D#Mj2DB:H%d<-j]A;XEOd.sM4q10#[U$ffU:@n@spBrCpq-tM8#Bf3Vmc>Jl[
4aM&!k%G_M2b\QDg`YsFLh]A9Fl=J(\('mUHM@6AL!LTuam[.5NWoBPn*lmc"G`NScN>]A:qi_
`dRl&:Xa61P3A\8G!3g5ie;fPrG;?e4\a]A&\LQ#2H![6>FnA#:f_iMU=bh^8n2&JAkAAs^$6
)]A;WtefM;0/XZKX[Wc1-c?#C64R5@Se"fKDTgHgWd:76N%(&gu%e<.BdEssN^ejc\Rmq*ANh
3:m)Ht?^`6G0[F]A)]ABGSpTDo6N4VcqHG=BhW^_nSHg/a`Pdc,+b$3g[Hii'LEQ1b0]A+`/<`B
CAEJ*r4VY:CPAEhVNXMkQJpX&CWX1ar4#<^3,rXeJ+tg\BV=@rsC6GSMM]ALFr"b"NUqjd,rg
[n\OBDkb?B>']AN,8]A^*XQ(bPkmTF38T5rE4h"huRk/^^B_ricWq,<-+BV:]AX`HSgRk6)8`8"
Ye6Y`I6BHoG)!SDY=Dm3c8J*Q6hkbZ!:L9Dq*[0I\E]Af`coiVSG2p(?e='N-2'Qqb*0N6:91
;:#hDX;f\W#d/`[gdP18H&s9$;*@\$$r8I(S6KE:</+_c<Z[C7\uOJB5hN0M;:d?t]A8Nu_/*
MlSIBCbd4a9j?X&j9aVIu?sNaa]A.N0FQTO1'@L1F+"EXi('ISc$.&6PNWE$#>u6"[+n<@=hC
,X@8GA)BW3XR1'Gcfn0-7CNdL=293'Se41=P=E7k(q-ls26q]AM0Y'-r.8_MYl>Pj:3GJYQA\
EJ%>UDW`?:[SS$IPp6iXqtg>ioG(0?W!#b-hl$/2S%+4]AN.t>i'GFc^I,FP31clB4afBb7:b
EUp/RZQZWMm)B^LjV,aD^0@1o+'i:H0^/gn?_;GYa$-C6u%f;Stl;#%,(oLU+qB[,mEAoB)u
CO%OA+U"NPFNj!mfHl4%9A@cI_<.g``a*]Aea,cnMZ^<,6_0Y`q_S".IV^T;`YkOi?oSkeYM_
&^Y2BbpVG&$8n`NNos=Du9,M7/=%rO=[CkDTNHAqe`2VqUN4#o_Kid(prPW9@dDor54-=>o1
?>f\=;*#CN'imj]ALnkD,\C4tbKl<:H*St2qPa.5[okB5g0UHKt.^[dpoT&(knjVQO\-P!fRW
&'f6r3'<nr]A5Jb!8LZS4GYa/eA.FaYs?EUAt&R=TCmWhA\#NX5M9L\&4brMHL^@2^g0k',-h
"f.`'Kl.n0C-o6V^2Fr+R'_#Z&=JROu[p!]AWZ@ER6<RVQr^aeg!OMhZSZBs>hIg/3Dd5[k\Q
]ACFU+?T/.iE;/JMN]A#V9gSWFh)\3\u^5KH=Cu(&I7]A^rM[Ke4sF35CUhDNbWf*_0.^?Ghr;C
/bQ.iVHY%BVkZGPKt_NLd!FK*pfKB1,5M?qnS;K5ZXG1dN*QMlHHsXj152Ls3e8Nb2TIUrT4
S2@_b[M-]AR@=ZF]ARhfa20FSRunT_oN3(55csK?;?Ackc6#)9WW=;2Snf8'g8pj=&L+Z\tph5
m9io3R_nM3aDr"=G;Ko7Rg\PG$]A=$p7<n[;$bdfBsffeX./@UW0m%%bPfgLb'gO#0W)j>&s6
E\\^JmnRTdofL%tX9K<>IXP6@Ti0(gT7GtA8I@nUTl+q]Al&X:rm]A2p5DXc$/2m*eA*FDO+*3
qX/EWae]AkXMUG8DlS"B71(>=C;F)a%.>EF\MhN7eorXo29J@/6pr'Tu5%eQfY0u-GWE;:B-J
$8$`(feCEGo2Ij/*82TB'W-f[C<"]Ao:p,/9,;MZ=R0!kE[9hdm`%>Q"^Y`_p.INl,Rj3g!cM
S@2/QFS85!)#s#n('ml@WXm8%*D)AC#[)QR&'!n'XM-6V-qs&fS<N#Eap)$J!F@@o'AN>-/,
+"+@Dk,@F8:a8"CukYOJ:raRj$_Pf#qMB%nT8*K['s1(Pn9!",THolO`kdkDS&U'^/.Qm(kF
$7@iMC"T0Oc)K;Hj8J/dUM2.F/&-,7)Hdpg]A+DW,16U-CK[!*:C4UkGABD$@bdcWj*/_e?Lm
q3\g:]Ag%oGIY6lt4p&j9\.:Hj^(p+VQn(fL^FJ/p"0B1W<AP_,Q[4VO)+nMt9?6>*$7pRqE3
<q8R)3[U=@Y(ZG(e-6-;6(&h#iA]Art3Fg.s$V)$L\tQDiciDPkHDDb78&(l3Cn64KGk]API^/
P:L:HeI&-PHhAc;LA<1#Mgo\Zl9Ba*pZbe.\]AS2VY:ts<]A^G*fXqJ:k6Sa;7@*i!;I2Yd2XJ
tC&tTtP,Lmm!0o`&gf(6eim*aif)f0b;.*iF8>Q:*jQ/><M31_A=2$RL1dh42N9*PNJMpRc_
R<80fGrFZ;<acLdPC+)uO,%\D%/D=gsWgD'\aq)tDb`J#+<G:^E\\7/>g7lOqp1o#tD#dg4'
j4tGW+QCVR<1YP+q)q#5&?0c<Mk<g)q/-RUGSRGto7?.g[hHI!`jCRDlnBHVp$4u-'?`.`V4
(!ng&DU31E(l,hUI3[l)C:(9B:Y>'FN"GH2j"bgGMd1d1U)q8m_"?b&^;KKZ.QLgoMJY&\FD
+CHYS`\4DeZ6i*I!M<+rGiDS$pFrR2uOQC("*%7cL5DYf<Q&-i:EskaJm%5eU:>e>h]AjTa[9
>5A1AP(SlVa)&aBDa-oerdVGY(hi.bm_9QHu$94=@)G+f@D=!5:'E["sAEr!XKp!!!3EKJ,f
WV+FjFn#U"Zt=*V^fB<ADN$sR2Vb!0-[G*S?CE\C)+ehPA&ZNjP_s+WYiD0`5jLk#beJ-^s]A
#`+!:Lk#beJ-^s]A#`+!:M#Thin(TWMn?+#+"uGZ($iQr7p[N>)"o~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="88" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="1" y="0" width="88" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,2133600,6134100,2133600,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,2286000,3467100,3238500,3352800,3314700,3009900,2743200,254000,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[一级分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[分公司类型]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[完成值]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[指标占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[排名变动]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=seq()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="WCZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="4" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="4" cs="4" s="5">
<O t="Image">
<IM>
<![CDATA[m<Ieq;dU5MB[T;KoHF>#]AdXt9L5tndJ:Nd=,]Arr-8FO6E+`n8$]Aa75n*bl=fLd3(*?"jemqu
a9;c'hFL5.pRF\bDW#n%S,UFmb18f6p4[_FO_.!6?u^WWiG'%f#JjL?QJ01\</s*WTU#dSP@
#"Ne\"XJDM]A019f0,jo/lNplmWno]AE=o8&p^8oH:IdfAS]A.dq`dhU,'Lc1#B4.m"8jjLSYP&
I*bIJsT*GK*eL)`R\.W.U@8?4b!qIqb$qa^M*$=o9c9'Z*SM:EKb-R*l8L2ct7a2J6'n:gFd
du9B<Q4L'q*!LmA$grV-GO"5nJ2,QikT;1J]A+`"=+SJ2Y(j,N*_E'*LS=jkuPGYMeD>;VB\r
<dW&iQd=a7`B<9r&`JNZhJ\(Z&rlN.3QkLkT\\kp]Asr'n_CKoYB#jhCa1e?'%]A&ck,t]A7tRH
MPE*eTWm#garH`k122('KsLOd@K(%\7(OGMu3tq*^.Re#$AkMZO1(&+9<61+59#!s=huBo\>
tP8%<FS]AE$(gCY.?4\D1<9JTWMQOH\EY#\E'"/UAQcke&\l/>630?/:#j$JN@T`jC3!WmcsP
XENB#tfGL?e6/sPRAXj,QR@%OsF]A)C5a5UK[7"uPn[8M32Q_u#qf0ohZ&uWfhPaLR!\;o'Wl
^Z;9jG/A<gLnH=it!+(VQl?9soZIc4'dQ/mYe)7+d2="3k7@uMK%P+4IUgjJBr%nhB?6TgoT
jdFC.._.pS%fH%j16V]A,7K)Xl817M`Np)_[[c.cWc[V#5iEnA3ceXk[EBO+2@bC4tE0*W]A%a
+g3SN&O&HJ7!3%_n[q>JL.ulm`PI"9i[E6]AHErB_+Dt-]AT\!Y1NdMB#+KKME!%l8^Ju==3eE
@6i`b$iE^Z2Pcq@UeW8OT(G>e'^>432SYAE3:k9@!SDp5\:U2*!g!9TXB`G@g/c]Ars)!(-up
lj=Zn?\;d6jWJ^0@>N@fPjD[bp>C%L)J%,FWER_ofV\OVXEj1S%pP>RIa1l(4M`5gud,ck-W
srgb/Apa*!ACL!A-]ALSrJ1.:aK/!1>hbr7F7J&l'bVaL[ERM@0-+#2:F,coG*@[1C4Ki1-rB
K\S<W[X71P@a(T`N9'HO0[/*;/*l`ko=;A%52cuYo*oW0DQpr3_#*^qgOF;gQ5/4`WA_OfQ#
85ViCbf8#Z;s130WUq;@i%>d'sF(0a2*bp]AX\G/X9.##SkJ<[=a4U:jUr>R@ZUVMi3X\g2cX
4V-k(D&R1Oq8moq'($kLDkTP1NK#nAhT]AEa"=TQ=?I5^VG/f]AYqeS9`W^_?>ZQLb.?<:+;]An
Z"AgcJ5FQ$Nje''=tiMVNOX5&_IX+B\Uo<0TCIp8R`nFDci;50iHZuL]A4r]A+tcL[Pqui@.Uf
SSk8dr"M7;Id1[dlgiNcD9:lA^DHNf&G!jEl3Oj'>$n8n"Z&3s"39G]A9plDm]A:TM`AbglD,3
:Y?!ES0CMALYnYE+Tc<EW*1HPPu&Y`0K=:u9,!Tg6`6oETNM/a'Lm3km/7_cQr-E#^btkF]AZ
onQ&^:NJnH?bqOVWq)"R\;7$2fl&&M@PO>0.ODCR]A:95R+DVA0s<Gn8c"[U]AS)eqeZ&i&q(%
Rd8*L:XK=2qOU&N$,+KC9^LQ"TpjfN,S]A]Al@Sj)si(Dr6uSh?K<N7>F+5ogen9`(OG'2<'2+
/_U]AR?3626LDO<3'&f^:0sn]A<jVUYd9]A>>)l9'UZWDr7Je@*Hd+!*,fO7T<Z:DD!JBP)(NVe
AcqUCS]A)g0ea-826Fn9;7blNgaikj3P3dNJm0$jRO7c\D+7$a-^7Edi(Pp@qu<n1T!K\9e^k
b=KnJ_)(>;s.;*fR1B]Afj:1O<M/C9YlqJ?9ms_ElBuWV7,OmC'NGfBd%8P&mD!p@_F%fJ$2_
k=>/&5BrC>G;Gr:9_%KDV<R/$T^=`NFGuXJtuRqmK(O%\W@XCW4lI,.s.M`.DPF!fNOATITR
,MRPM\X<A_X[L>lL=2ahP=((apc+k1Ni1<lH8Vc%`TN9Pr!m@8L-p9h-Z$VKeJdYh/GGVbRG
($G.5e'g0Yq(NTmfRbXNkf@HND#kb#$DhH*9"%r&:FC\F%#Lt9fT]A3HO=^Jp0Ol,>a^hE_='
luE8'NNI<0K'A$t6)pH-EsP3\A*Q7b9P!d)i"EmspSWh(+*.UWUd+Zq[#$5e.VnR/?W?q;Vi
[&^aQ&0$>!`r\Z).<]A(/3qlQSmKSEf]A<9E3OeH+AO%H>R@fKAaGNDF*Xt]ALi/X48-b1,!6EK
'g!bKA"3%&1^jfns_k^0@jq'HOr9k5@%UHlE"!li4b#IAZ4JGc.B]A9?id7%JYJRB@c-gVdkT
G=11#fCEA3/6aqV>4hEu`pW^Ldk.cukjLmqt/Zc(sr!VU(n34C#C:lWGCW@[VGQ#E_6I$KJ@
(3)UeecBZOC-IZ7Fb>>Kr2qrVVC^CUK/<QT783#g57't'?epAgI%h*_R'7@dWJ!'QGQBSJd]A
Re6$$Im/;I!t(4trR0#IU)rR9T;+Ra/sk4d)5m+Y\&jpJj1V9(*J>1l2*'4U:oTn^uA;s8mi
YS<N8k4:4ro&+IKl.k1gL]A:".YZH&3g%j.)o6A4JCU`N5LVY>=QEV[urGYt#;8@+7D9Lmhe.
eB\O3cS+qF*"[;/Nb5TVW\Gea;8[U89ZDPuY8(o9G`V0lVk(R#lKp`"-M)^hK]AT!V9,Fj\'Z
'DATD69>]Atofl#q>2i#tq]ATqrlg%1P!XmJJA4+T)TW'ik:!o9Tnr%dp%8c6TD.QQXP?%]ANGM
.a.-2N-<>N[`*lN4LC:<]A0fYrj=D%?%XVV)ArQCW%)^AobSOr;"0Zqaj;LR4B&DZ"EW?NW9h
)LEdnN>HH+>e<3o!]Ad8m)!bO*_*:&SRq-#ba.EpCuNYBu-gb04\.I6jm#r:&+YqY^-"[Uo-*
nQTSVI?K]ATe@S_haJ6N!2kAO6Z1.'0,suDFq9r6qc=Hl!o4$NW8@aiS,&nA^Mh;EF>s3&Lkg
KtOrp:6qV]AtR;\E$RZj%aHNd:eZYG+ueTacG`6DF-#8g#uA2''X-t+$KWIL<BL^B_#E5@'N_
t:JFf-6f^[,ce;;"4S3FPbLG6*LG`V@a@/UlG#>'$=*#uO@qWeoZULu'4W/65i=',dV^fhN^
H\?tH)TD^[BQm*9jURh<aj5u;/`sB974Za\bhF6U^m)0G%.%TmibVZSL60I]ABYMbG8qF/llm
,#7D>Yip2^k&afG!j7\BP]A]A6M*VK?FlH"2Y+^W"*Jdh(]Ah,0m\^jrf`8a5.Xb^(Om*m7P_)H
]AD3X%^,9Y-j`.&?8R2Jib/:3)Du8EpD4Hi.aQ]AjgEDJ?GNMZF;>,m4%]A\([M?Tmg1*^#7jVE
[@48kr[H_SB.(W1o5GIKWc9"UZpb@c\U^l1UP2e]Ahg9@R5:'9F37me^K=kKmnRm")V!ggjE&
]AiopHDb(1rA2>=amn2K`g*-95Oh6T!ln[kJYjS&CR(*`E$YCgARc3_5;<**@;<mFH5^5:u4#
G`uB'c4\P9]A+D9j)2SbH!7%86'T"1*q)C,W:+mhf\V:=8R``+&KrnH-cHr2kPgKJFGNh4Rq3
PNpf#G!N?ULF<0NaKZ'ns.;P)o&&*mAj,=aY>>9*JcalP@"Q%!C1)o_Y06C]AR]ANI5`kCX"gh
N=Hp?F7cA0FK"*`h1tc=HB439A^BcbCX3]AV&>s<;6Bo@R0B3'&fJb*ZU@>J>^l[kW-H*%jVb
R:`@F+YB5>n[!3XHbl=Y64YnP5L7]A*g4m&SJmZ)hmUI8/:3rRu7EEGE))0jT+E:RHM)k:+rN
XT>oJ!f\UfFDI+NW,1L.&M,PJ#%gko@0O7#5p^g;M/OFK<GrDP:F-;7n$-ilId&X_&i+[AQV
SOIbJLsZJOumPKY&iN*XJ7?oY>b.+8nM=m;uC!L1Vr[:[2<Uj("GR4g^;u_2NEi4@S9[#oE#
;r,?4bORCF`L"7CXH57"D)_Krm6hbH'Jf[KRD>1PegM-oQ*J!+h;<1Ybe>3,>>G&>=E9@'aJ
\,q)1_SJoBOYtM,KbZR=HXA#tef%aXKPrK,H;Og6/e_i'$bA5S2cE7)SKL4qe>$/B_%sK<>P
I$m=a@#R4:sN<4RS'!U8Fs-7!-,3kV<l+6kiaO*TZ@YES?PE%?KY*mJfjmOE)j:hpfqXIQ7)
rAoEns=#juZ>Zfob]Akq\66Ed`'5Eb_Rr:iFY$R+OL93>K>C"_d-8Yg;(<OGre&'&5Np^t8I=
KJ'j2G0q.PI<NAI8N\fPFJ1n@&_1#^3FR/STc>;)#"djfFrom#>=C(d/,cAIOkC`%<lG*rk6
A^]AB]A7Tp\3rr>@A\2PaOK&F*!*-S/aL3TW)G[qIIAZrDBaIB2F?/Gu9I@&88Un(4`\,@+s7/
gFLA5'K"US_P%Rn1\@XF_aWlf_N:+d;e8KVD"2m*IYJ+#-qcC396Y/Vfg>"Or,n!('@9=Pk-
\IO_A*#\mW:#XnZQJb!!W]A-OEBP+]ACYkYn%H!V/C#MW6lr20WA3,8hKRfYU7hdhNJ`Pu><9R
$[gof<dP`u%K.Q-o_^6#D'kW-A+**)S-@s;d"nN3qA^B<Qm;04]A4nHJB:Kp%5Mma\Ic5.LFk
tRJ]ACs_',.9#N-"<\'`I04-a2p7hAIETF@"4j8s]AL>p4<U7!eVhbb!MW8N\7uc-P8(uAL=W.
'@DM2Wn9P18fe0:24l@.-c'>b8GX90ETZH`qh-F03m7_@?O%?jBN]A>iR67hF!_Y#!C+a8Oe\
d-lhPbm]A#hLP)?HjhQTUOdk&UCmobc!hhAlPWhAJWB:+Gm-]AkY<)/3_'Y>%c(Fa3dp(%U3;'
brY3Y;BDY/h(4(#FRD/LZN`_em4OJ=gWVQO"D2dk4qfEh:r)Q==_tS&R^5,<lqs7?p<`O!&)
re++,fE6P?hl50]A>m@k&0hem,_-JgE$04LJ`3e4$'3=#4./&F+Ik(=6*atLXuRV$KB<-GBqK
DkNB5C,5%^s>RL@c6gA0%")+b,T,Tb#h0=;tK1eM.:qBLm_fe63?b'_pEPs$3&45QI[P8p-`
t#]A7A3H?4]AF"n*RD>*o\\c?uajCRbd4s0+t`+_3NH,ALfP,dU^Y5"mgL:jcsguIrROc[&dbu
3![^R=ghV"5:SeXMicQXlTLnr/\m$;=>rD7e'$":5gO$gl`V>gI%MmF)3rSHf203<(lFGC)<
P<>-&3sug`.am/S*TpVig#hD<c&Qppgr6P=;H<rg<#pkAT.X;HS3Z3!A7JFo.+#L&?W=pmqF
phl&hW@)bEB%rh=4(!>o(r6$9sQmkh%4S>Kb*Y_^J[+9kIOrHMnSj!'f>hBjM"gXZV%oM]A]AU
n(0mTdmf1!O9r<,@\_Zf$>!FA,8JI_nf[YQ>NME'9&sgAu!J@MpX"-=PKj*e0KLZ8l\R^RN_
?AZ;Qiqa;&7U!FJMehm9`Q\A]APl=)ZsEo9FAaD-]A3uNO$3LO0X2cXEJo[]A4&n%g,LIWOJ"1b
L!fJrD6ij_S@sgtCS%[;.<<tUf5qd)\G:bHD;AdZ1e+8@F!7[S%mODN'&g*<\Da4bBVCq7B5
9cW7ei=i1N@f_X(E,tX[+e&'62cW5Og-#kOCX)r9Qj(W8ioH+5?=1P1'#1M,b5tYOq,0O@f0
giU5&r.<`XL;p$mU]An%(oepfL.e,>'\NYS-p[`9Flh=$9FjrEd%f[X_&_\HBeT\H&(?-Vj@D
i*QiqW^bDBd<d`)C8K*O,;_$LDSo>ij&[RmSb`Dft_\1q&t(:1[7-?o!P\pJZBTaEZL>9$Mq
H7c`c>;iC>6>gii;VIHG8[;"=\`%qnr\.7_/+^.58)9*Fjsgae*P$iBI_em!U[G'i4;N^E.k
Ep]AkB2N*X5QZsZrXjgd_<4(Zp<ELWn+0DF?Q2&i!Dc%9Slb%m*5dH2Hjo:j)V8*cb]A.]At(-X
$.RoE'5Sj32=RrONJ96:0U'pFXMD/fD9MV42[Fl=t&Soii-gEiT_1XYK=15?-M\1.rhDdJ\Y
Hp5""+^T0@n\X0)X#i3k-MSlu;:&/?8c&hH%'sh!@DWLIBTqu=U!Bc43eui]AJMdd%PB)QCf;
-Fd\-Q0rNs(Od;C)l2Qm5fQ5>(0/`*$#mA<]A==%J8A+\hNZpLkS.'XG5pR^9\:0W(TIq>8Z>
p:.a`B:oLli4+Dn<=\;jafiSE72WYYp`$d]A_O*3WdRP'U/%"h#\p3J?W^\(3TRpJF.IdjTE3
LS<NTag(r/T!YC.T!P[j"=7ff^&epRCnn&O7r0q(PH<\W>OHRu]AqJuRQ.II-n&=H7m,*YOh*
R[5\_W-H=C*IP^49Nmr,1'4KlG%OE?Q7R5'H%UUqE<F9N'Bt!g"BMFM:AXSJ-OagYN>[LmJ&
L_5J`!/hJ6@+[5JB)f9A&0%FaZKqh+p'8\@/BLjlarOiM=lKMT/c!"9)I@147d&NpSLL(aPm
3K@Y<`u$]A]ABCdpgCkEPWp@Y;VnqfKU'9phh-+Poiou'0Q-$6C+H_FuF!(TIk9PM]A(0&=i]A[X
@M(IXMAimVaPZt]AG!s)&(38l%>a6h-UUN]Aa=VdX*N!dI?'(o;$:U^P1u@FQXP;&B!AlT,+c`
_<=^3&K*WXO&NG/k.hl`Ip)$QiC5knZSHT,d$0@Ndc'onYQbHcZ+8E:&bc%##-'HGA7S,E=Q
1CmXc1OH,SJ\KR#_%u2c2-NRholTk0C,M+`skXb7BYb=Fr.?ioL^lI`*gACOaiUU/1>B0NQ/
sj2!)j;at02Bu.nY-MG:\F0."?=A=C8U;d"jl'elT(glroMM2F(_9>lO>D6q:Kk,5O[Is4-a
]A\K.hqO@YAAk>8:6UU)Wl88%jDJN2/ZYi)Z;aXHZ+3.(-%KjHA'tSr:XRZ/#K5M`Mks?<e[&
UXBe14@;)H0D9\,)-BQp8)3a#u\&$:Cbpl"g)rR7a.fMIQl6GB`+(>@$2@l,iU5G9.c<NYeE
C?ek/7'>)mpQ_qC.aT@Co1Yi:gc<7:G8pbE>0E*iE\h#'-[VF-1tK<MW_T<d2P.lY37'\LG4
"JO\#qd0[D2cY-OXufU,WFYipk!'JiIf3:n$3B<2&"l<r4>3nr["rUYb&@+:oLjq8.UY"l6;
L0]AKP%BnKW591(h4l[t\kMf%PANJ/R38UNN0+nh&X9)G8Q2ZFWn;7>b;n$gQmbr=.&DVUIU5
=Bgn@?',q%afjudUt^CEoG>:C<CPoC@;j7n5CL[[1b>JG&_-o8Lgulmd#mQZh3`YYtE-jf/R
1VH-GmhY1k!>SOO]AD5qj<.a'1Ecij+i+]A`;j0W5!4Mo[ajH-MOKb\I;/^J;tf?&UDqpb2"O-
A'Cb4+u4(-N=Gsk+r"?)%U0.dp+cdL2Ykis8=S+KRJ:E?9/Z4ng@olj-n<X#"k,11&#@CGGX
J%W]A8LIIPN8MY)p(o]A(TWBKSM1@nrB,?8M6Wai1i8Y"-FYi?+nG'$q09A;AR0?(rPV7O!IJO
Tfk/m>pH$?)cg3!eBH]A9?6l#QDM`BPh8$m18Wa'Wie#/!K73PV!H@"JHlg;eNk4aI6Dd&MSj
O)AfL#tuh\t?4I9;=X_CEJ%NjFa5En55S.4p[pSVe#UE/41^O!l:2C&;&4"cf)%7-qF/00a=
IN*E;fh?u'eS-2GRA'aTB2*6:W4C^:QrcU`;h,R!`)RR>BOel*O4(j^%F`=m0MZXp?dh:h4#
/*?rHTf;rR0`<d(pWF'+J'=Lrnkc3k&:-au#\KV33B`$&'-/Lq1169X]A1bjQ<7j_d?%lKFQQ
dj`'g0\sDA'#dHdjZPb_lt59>_g:8Ws?..<\0dC"nVSj]AJ[ue`]A3(XQ9Y>=9&,Po8-R[RJSl
?_#9<)%5hGA3p"unr=8~
]]></IM>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="4" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="5" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = '总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCW[+dq?9H:2?u6>&YTT=`oHn!<p?A7DqV+!SO,>PuRl/X:SU:-t0&_'0nfJAcuX3U9[GoM9
%$*-kHF_&kN?0!otHiH/D\]A4m0K@47E2shmqgZ]AE5X9kP<q\4h&2<(I2TiD?#'sLks,rB25)
3-mGr3Yjq1OYD(l,4(#`8&b0-hF7P!,-u*qtgUb;ZcKa^sidAL(ETX<mHS"J4YM&;2I!O_g0
$.Ho^,l20D,\o9qKG]A7rH9j]A/$1Y1T:VT9f(inBIa\sc^Y!21FmO;5oFmGGG$2P3fCS9C"`'
'2]Acnu-^<sfL#&8FnEEEuJM]AU*30'A??9$UD]AV7q1WrK(+g[iG6uaKsGD/42hdRF:FBE$[;h
`$fb+gVmJ-ELoY.Xdo9^;781XHJ*AaEgT^87]A*8C8EgSfB$WS'gs/9DML"-hD+JZ>-8T(2U/
P#ZjO-i>[-:C_6P[kA!*<FMc.PK,1=7X`LY"Er&`neHcJYQl3JfS?/_Yi)+p;9?7mGM5[;(W
@NI;U.?@!\U<1c'R_mr#.gQ2u[m0WhF=`eAbB`WQUGpq`[-J4Blr6kW@XGFsZXbk-+Gh`4`K
3$0R8\UcF#N^`@8O>NXC@\72>kOFJ+!ggZSTm"jUIq.p+U,^E+(BLE7$2oTR4g\jTar2g^-e
<.jaL&Ps(kEk\bc]A>_qm`0SZP(D0?H4$dUU'`?USESIE._q-NV,G:pFIi+sOWO_LL8hnq4T(
]A$ZN23S--S+>[o2r?%)>oJ/1gO%KhZ_JKss@?^*b@d\0sI8^:!9s.!`&3dmld-8eMoBIaH/%
4X&Y@"FE9gf=Wn[aI*ld_=SU#=3R4..)^8R%.3oPBK\cnWhcH)IRH"8cL6J1j=Wb5@Ea7F5P
2RZT&XBt)F^CX,L)j$R?sr_Z-Ud]Aqi>nP]A>id$GqP*44)kcBRQLc,bd1bbe]AuQ:GNW2<mih)
TM"Q%OWj6TL_r:HScYR8*!.3_dh3lk*pk%dgZ6AF[DfL&(^_c@EN2Gdk#g>j_874p(np>P7j
#RRAmlQHTUR#:BMr.acOE"bKIq699tsJU[ga23Wl`2^3c`&BDoO/oDDOS8nYKQN9pRO&%H6&
ZgH:683$AH?1LegE.qa<0Pk'!/66V!Np;SU3:'YW1f%/LH-LpB>[HBgo38fld<b*$RUl^A&/
j=VaYi(BZ%*u?YHDer^Pg[_`fm+L=H+%PH50`A[;^8r,6b-WK$01]A0$/ij]A$'E@bgO\rS3u1
+T+\4.AF#8XS&bt`V\C`"L6KaFn&,@&:DkS81>bV(Sd1A?>#dB(%<\a>I#]AP,[N8&.3DQ+u[
#L()g60mN;g5/lPo]AF6>i5o@0>uKuSHus^V0miQiCoqM1T`"#Bd,c;RDPe9<N/GC3Ni5g=5O
q^Y,p%T0LrK,G/0J,Q0mHk65?SYSP+-d?'^/iVppYVm_HP@kh]AqD:/#FG1So@tVe.2bA_KHT
`jX%`>>:="]AJE"Q"Y\IY)$UM]AXEKV.AGbec/Fq\fcG$aX/h\utgh9'CcOt30Ac_4rr]AO';ol
4K$#&[QDXE>!n`SHtk/?.Mf?[%`pib+W9q^N46[>:OOWGJ?+\)#'i4jhlT0=$m:l+QCXZhLD
G$;\fim?Z'qC.CC5Md5K8,gST+W<Z_m:4Pd=\]A+]A&R@-JXgWE$X=6aW2^%cq%V_1&Iok]Ao:5
bM9*?I+p(A'QiiB6o)Vf-8@QiQX8?248gX/A<6jBe2MW^L&3R)eh7T'WZlIU_uf7AQ[.)egY
u%o9\YsD_GM\]A]A"e@8L%GlUYG+mq?sfNdb4uTN!1"o"6tl@"Ca-KKF-LD3GH%C-d`7E#V+^b
8q)13P<cWo^RT\SC5bH9>V,;,P2`4<a%=B:^@G<RrI-[\/%@b<Ff?&<e/?lcOP<EB"TqLd(W
3?bGB=#j9+i7-CWrkFV^R7%iN-q9_NEtLJNA1d"0TbFQ="n`T5%al$F:_\V.N8-QNjg"n<YI
=A,DA`D83AZhQ*Akd#fre"QQn.1WgY;c6<!n!fMruebeoGX]A%^%HIgTQ"h]A8@[D%)'R@QV)L
\aKCXUM^'V&^YQqhOoVJ@9Z\!F>N=!sr+g:L3^UEjBr-CkllWKa;4t@K=TrnU!ah-sOC$g%S
uZf2Q=mGoMDR0pmd@_AU^B0pE^9hC+aKbbJ\*fUd+hSf'NAAj8AH!o9J-GWf>pb7SbZ:M5q@
>rs6-orWGdXnbL/r7RCiO[U+PpSp=/BGFh(EP^3n7>h2/K;t;=+J$I^C:LT+;m;7>5lB3*_@
5K!9VEp5=sSEe!YtU=fDU`*F#h6f%^o@0Fmd>ANhc[SS;pOs&\?<b\?]AUF/P?JeP7QbK1h5_
H!q_h%[^WL+"^=_>YGHE\B<Cf<B."U"?Kc?Pce,'_U9Gk?5hi82BhM9#H\l6R85k!iS=b(T#
;>h2ZVX]AB"XE&J4;lf:hJ?$"T?$M1a'.Yj,DP?*Pl,h6L5<ceoIYes!:I/8gGuYYY$Zo<^R!
CU%N]A;Zposq2@IB7S/Doq2>Tb@I=>lo/CuFTg!%LVi)]AlpK-jA0m-E:&ir2u7g-*0md`%li,
cj+"=H&MD1<kGu-CtH]AT1gJX(Jh1:%4K#K*rOt+!gS3]A=XuONM.W[i)bUEC(,u]AEnXB+Pl!!
ros@5aq]A>eD(hCtVMi'_.%c>(<lfmHWFODq6VS0'jh_QnR-m\G_^1I"GPNRJM.SP<)l?UYg4
p789ZfdT%K'7G</3\-f_p(*QV$g;H#'Q-/M'.N#YLR,9=6b+?:-?l1S5Yg1hBG)gP/oIZ3pK
QCni;oKoGpEXM:aF]A#N.N=RH%n&p5--E23&H4<:pnFP$aJ<ur;00(mSbC]A@oi43R4GIPRPQI
kjlVcp5U0'%<oE!Y@eC6D2f]Aj>Aq!nF5rmEp)ZUo"\N.Mh2hh^sfn4dC6k#K?q+jFp/,R2U*
C<!XBKlsA?JPHAtY@X3tbV@h\<^0,2#!GLk^VZNXA>JY%b6jFZhc;]ApSmZ/jQ7>"=(XfP)4W
$%.Kf)sJ%"AXMe`Ftb5u'cOL6B28Vo%_l/0>m56sfrCB^F8qC5uPmbFKF638H1Gql1CLaLKk
]AQ?skj&q4%pXY[;EhL^bYk!tBO'nR09_CAdnh=AfE!i'm8NL<%mHZ>!r(rO]A0"h0SbWcGMF5
>3$A?3Ehr4+M!,YJ[hET=V+i#ArV(f8Rf8q\cTu)..CuhktTG5%Nkd,#X$n3-ta8nCH@<ptZ
^jCmc/TqO!Eo(\(\J$_9h/hk,8YY5KZ_HCML+&Mu4*nFY7BRAc!1qt8\d#mhq]ALt@*8QY\pF
Sg,.>R\CD!CgZr,j(>.["5E:*h0U"h(hWuQo`o$1o%DMhg2IDi!:bjT"8,<[*I)kdSE;!4F:
f=uFA!n7H!.'73qj4I6ZrE0CN3"!$pt>^UP?2d+^'K^@cR9r(?sgWD/XbP^NO%>%qJ)N1AXU
Eo1g)lFID-.]A&l5XS9qk_(nj=4eMbaq,#'7"'ok336Q0Y[SVaVkhs@+]AjH/OL,fq92cn);RE
P[fcFd8]AC.cqGld2(6Wnb%'cbsa;MH0od3C<J5.Bq2^h>(I8u&FT[p`]A^ss',(IR=$c3TT=i
6INjT#7c.CP&H+Ai#b[)$KKSnaQpZ$m"g)'_bjc@2k[S[U]AHpohXApktU@QYpf)YW39oA-d!
%7]AGoS`rZ'%55^tbD*%;`!jAnHVcG-.;B8&"g2\+jYG=0!:!9sDQ0P[5#GCEPLg>qi+T)Q^+
_Y!JaJPS,L4O%U,*_=joUKslslk%"i=,UARb%)OTjO4WG.eJ8QUsA]A,*GMFr!_]AHBM[#*Cj8
Y6e&SH`.0AW`n(OLPmgoNPlAlh9O,fI_=Y:Jem\;rMp=U7_>2BI8f4rMA]Aq:3c9GYCG:)81@
b=2-F1.W(`3BjK-]AI0Pg,aHO/(O!p>_jZq7c<H[;UJq?=4TM&>>PVcp?\`+Y"TLO%25G'n[(
U,[%L.`9.s\!)Nr]A+!&\CfQn3Vr<?,*sR8HPK1k^uYeF)[GbLd82]Ajq!;n]AZ&XgNd:k0?pm)
Gu'YnD<!_Sf.>)ZN!PTK@GM2*K,"Nm#s(6!`cC!$@Hs$R8)6hZ=h1YS:%"!=%*R)=_!c4-me
NQr4)hlKs#C0+iZW$BYJmPQ1D2:00/e)E9&,heU8ZdPXW8S*]AWS`mn'kJ?l^cl[I$Yg<m6n]A
W2&\K4omeM&CXF"#?gP7aQe*<H32Jh8jd>E;EuS_l8g`<b/%9B2O;BXd.C\0f*[@KspeB]A5C
7"pODYYq\X-&sN@;i<*%:=3l/a^u/HJ2a<Gh"\@Mr3Gc*Umj,lYP%&QRkGaS@Z@sZ!SP6HWO
8P]ABUU!g7d3R+KsmXGVNVm$!a&'P$mU>mce%%K#*,JV;ZGGGrMdVi<"Eh.Ek#"CZQct$Wj+W
@Z+.UF^!7UQ;apEHaOVC]A]AWa<M2m/KXmAs@c\X%3[58FEGh,MfU7[':R;P_ogWJ@r;_M=rM[
iZa4Q@4g)6Ma.7S)2!!%VIUbH:<$1AuSHh>4=K:*!Gd__'5Te`<BbZnW"^FD@=V<?DU=dKk`
1R>0d[,N^-?0W/2HC\H"?:aqY6aCAjC$GRcej7anRMa*`5GKKtZOagl-^@h-('dqH_r^\TGG
NRkn?;=1`n+\KBDh!A=jRZTG&=+#H!1KmrCgnIMWcj<ZFhV55/F\+`_AbJ02Ca<BE9oh!CL0
8.\p55'Y+O>jX@CC)U12dUY>[>ggYtn)geV'+f*j:47B*8C>BNt-,`C,:M,q$DUN8r+%_A/.
-lRZp"LdDS$affMo#K<Fk7[`^>H(%-1Y]AjCZ#-Aq`3A$\cRbhH"'qo2EN%n85ISSL7X(0B!8
h(6SNoK:TsL(I8onUah7Y@dHs00u'TU2?*&9fs;&@bT5eGpfgA*'B:kI9Q:l8hd0mHt.W4HG
Dlf1i)!Ze9*'mHZa>k5A9F!f(CgZORMEop%l5cM25M0Ch=:[=lQM]A_7(\sK0'8qckk+Y*p7^
i#JY`#IIr=(B%72/#XnIE(j1]AsJeO]Am"aZ=m-(iPWn99#h_i&1;Qu.^:#B0%dLFUBK^L)m8U
_uL.oO%+Hcil?gCaql"MBMai@)XrV@pI2q7Ta=Ft(Q.4L!6rp&=&M=iu)`Bl\fjJ?#oFc9BW
mH/K5Id?)mq69Mjd0=n*UjUO&TF806U+;foo`tjr<s`%Ggr/5d&&^k(8tNa$CO0hhL8OFdE.
?AJ_@&-]A:#M<FKM9&>)5qm.;h=mc_'DfR9kfsTBuQ1c=_fFs3BgY5FprNDnl]A1d;gR"<05$a
S6s@\0LbO"n^0XDNg\u:Y!2Ak%-'Nk3Hk.e\+kQ_o$<4q-Xu8:6[#qEl18(O.MRg.=juZOG!
J9*1>*TH,'IiKr@2V$OP=_h)[O0pQ3`.A`KT%UYY88dU3*T7W_H%>i"PK!uj>QaCHVh>eaI(
Cl;:R'.#XAg7/[^H1UL$`[]Ak\nL^((`nUQO@fp=.iY1o.0%o+D^]AC0kHaSdQM2QHX>_5<h-9
I?$QDJ#Uqi^E;;m>Sc9t02fB!=lJhZD_0j/#Qs@&hJAk/+_0ZU?HL8R-7/><'Z7@9,B,>7j%
ra.Dq2o$N;B*Y<N):N2gDtH>nog()/Y;W;anjJ2*T<[LcG9%D6^U^dm)+&[dCD/a@TI(FfW'
s>tiMPZ3C9"RV>2N&AO[G>,qMEXTQIg8#eWiJDY>0nX20?A='O>Ekf,il'O()nS+DC"!6*o"
E:QTCaj,-:8;iO'AQElj`Te:]A+1ikEF)u-cd2c#QUULCYm;qN%bjk)5#Y;;jZne1@uG.G@!l
s1[8^6l[.F)&"4"K8[qG3?<#[p_fm"HSa$c_2<F(&Q"d^ja:m2=;R+/!Ypu^ZuWJbXc0Ks54
^YdLe3NtiP@m%aa]AJE$jk!3;iR=1Ya=KmqO\ss'5o^>t8qq$bH)RF;!)Z>phh!58c-O'-:F4
s$<2om;(Adhjm\=415W';2$$0i)g719o[W/2[41MWs,e,\n:s8.U'EOUB%8c"9bQH[qOl+4.
b?5%+lkVWu*kH6Kb&Jc!:FYgihm1)U]A.gOAV<.Numj%?G%0SYL3aOBL07p>G*L?2:<ZjWFW.
TEtB=3\(C=:M3fjo."]AL/G&O=]ACr<o'qfcj,l('+;*W!>-K@Q-@"6Aa?uB2[UlLZT!;l/:E>
MQTtZ\HS$V`GXf)l`h<()X0=As;O`AVGL/!nAF'gDSHE6kBV[+"?4!sr\+!.=6Q?O^bL$6,=
3lTn?FF.5(Yu]A*DnM+hL%7T[&rCdE2[^)Rc*eA?!$8^cQ1C,dhUVpNWG`>\sM[&RG'k>i#!`
Qq9^ZIVB\++:nDq"<k--<]A?]A0O+.NA<8>:Nmnkmp.Eo`ufpl2O3M76a2P>ag!5p@OV9F]AW;h
<EmJuM#)$X!Q9mu"^%8a0^Um!!UcA)tiZKB)p=PGnH-R8#A!OU3^FF>A=3KNip_$6+[(Wq2;
:6@hb,4mEWsWnam^`FneE$>g\'R+IICZ:<#^u#hKJ+-ZlSQ;GOR]AL#P,nG**1/8ISH_AC`/_
(P'KPli277egO]A0F-^CiTF1HT,mCX`.0j"!*q[GHbq,IH6Y?C(!Ao0^pcpk:t'EB-S^8SqV,
+mXK9*8okf#;pglqRbDN1:^+Ofm2kWc3\hs6*@g4b)@kak:L8hhX77db4NkH5F6&Y]Ab9HQIH
c"fmrG%T><XH+6WP$MOWY-K3pqn9QJ\/<^%-kS`NQ`QZ6S%dXIfpF8u[Aoi-LYJX7NGN]A]Ac;
nBE3@Ni)ICLDotL%Bu2JRPX^s8"h;mI([epDY$#J'A4/G;-k^I@</$8am$\aOkQ9NJ[.2r,L
PUjL23'NYI9ijc;]AuT9D-`pi59Y-KXUjh5OQ*2b$n&XC%P57:/oIVGf0jP[8K>?RBC!RH_t`
%@H'C'<]Ao+UdOBre`1l%I^=a4_jXITqP,%?n2P_@PB:_E(lU.Rt.mfYpNh[\(^Wpd,R@6t,b
&P))<0das46H`W6O,)N9916FAH\0Cie!8&1[2%3EMobht+$]AJ\@8@?sj8K>iFYMp:2pOG^5*
\SE-TZK0'?B-E(DS48JAmJo"5@`&js5J<H+B90'_n)>mq9',H8>O\L9V--HP]Ab/+=?.1&MLg
U(7Zn`bH_p?AZAL.2(J?e3T=rW%:nI=;6^[BYP(oH>ZFq;*q;P"0e7[8V1![dk>2aoaj!PNV
!u>G+8,Wi@mQCK@^WLmMAbY9H%'(mo%2]A(ddS3"Eh>BP5n/H'CUu*_:8LOg*4`[nQ]A041QNR
eh44RS_b>^m=WdB/0[Mtg_++[brXke3:`^:A&V'HC=Yc!k[OJVd1nZt\nFe[gS@R(ASmIb[M
4>/cjPVcsBm$=rRa&C(e:WEW><W/\X\cZ=NGrSF4b;S'tD3B1FrQ=1(pjF=I0R[MsU%1G#X8
J#_NW8iuW7.odVgFQb;W%X![Z_*;N:F;CmkZag^c?h&U6GbC+81(HLjKY9)ZG?mi6))+-bOn
]AqF]AhgcSK$k778KX$!RLgHAp9)o_?[H1\P9H??V/k%jOEXP;A*+d/)jVAB9>A`T<HJYRhD/=
4Lr6p$N3P]A*cSgcmFSm1_ucc/.V"UR9`Y:Z!srQ*7GhGcQP@sX2]AK<qb$Z#eTlI:r4`egEY\
ta<&d2U88&$U'TOAjImAkpTUXi>o",r%Bq,QHp5F4aVUtJ41l!g8c*@F`4SL?4AN.<XS![$k
Ch_NOo'Eq0&J]AXHD\oc>$!FX1eYX![3dG02oj5fj*A#*r[ISJ^gX-buh@SQ!5^0plj^?"Gkn
ok\VDN9kr7OED:(#CSZ8*U^LbU0.rk/(D?s826bQVDQP]ATpbJ6!4@2%`uCP8l(04/A<[@D8h
d0Ih2ZEiX[K*MpVP83n"7>&9@a3TYmZTFd\kb8TRmA-kQ3@E4uuGQI1NY#q<>hHuoYOT$hdD
kYME[1^,hf>5\cb&\Yo3?,*(4.bOONql$]AajJ)?Y0_2YPL(\1pFK/Z.EoeSAXE30SRa`USKi
9R5F^OOYhEF3mOAJ_=_C(+I%:a5ehPfl)249nCV&@4VCneOKFpg-FPm)#rqeh(>6=2n\49%1
8g[IPHcB3kYH3d2%j\eiW;$"kp*qVP0MbX4CCKfIbd(T)G^6'V*n#m96e:!T6H+BkYfcWSpT
Fjq@KqeRmoi+?##Znp&aI>pkiqpF=(D9`h>)H/j,5S21fBi2L>&Q^KcM;d[E(,\"nZnq6oN*
pKQZKW.O'n42r,"#+HqZOG-.ikr\ur(g6bs(!1VdLTm7J/V_-gWT&XL#rCS*7>\>dr+s8\\&
i+dAGtb;T4!%Hq9"LVY/GJ,RPFp4=0@2hA4Drj+5+bW22dRJG(BdTC>$8ubUWFhm0u>2tT4J
iO'bO^F:0B2_C]APdJ/1d^NYkdq<G<nRa8!OiX@-n8_-'"`VS@fWd.tQc4^*I5b#L$pDYX@Nh
i/#FT]At5fgoVk$11,m5JQkr;Q='nus393&`s3`H/DWAU9>h0O`=5%n,!tFjC[A3`[h^im?]Aa
S`?f+8rrh2q!k\+Y"g#t7^1hXl@?BR4g!%X,BUi4d@^8b?r6jpVAJ!l+=kY30mjC,B!A8Z8L
h_SV;npEs^f3cTV*aM:dK&n>jl5NcaSiLCa:A!ehDgh7*p5Z`QR96=[]A_s]A.rI4o6@^@'N]A;
MauV='4$-8+3QXFqq(e#l:HJR1F_.TAn9q>E4SGXNH:V(@(jp&eHWhhW!6cfnc%^81jCN9Lj
Y@f3_3Z3u.E!oq<=3$@V*Pg&5pE`1e&]Ah+_.V[]AW=>'0P/!l2"mNEKA991aGV1=t>[a9mLr0
+KgIoK1_ngohT978G1#3c%e"*<AoCR/Sc'f0bT372WbOHFP/CpS-VPqN(bYO@b9b$KuOZcc*
:$S_?oQ8E\ptI)lP+k8g8rI<M)j5R$*nIM7V`P`_'pb,2:Y<1`MOJmEFu:=Lg;!G%SPCUUKc
HgI)c<&FnL?I:L!:D%fWDcl>W+IWM!Zeo8,>[o0YDk<<Pp`,;E`ZDX_]AI&8[nRkiuXJupmQK
RrD1GEq:O."ea,#rHJD7m4<BEFGuK-2Vm-L=]A*PTDB&bGl'orS1"^!A0TL+B.+Fm8@VR]A%7q
3W,uI.cHsDaRD[um#d#ssE:+U45*&HYQ10Kb3K[m&Tor1D:7rQ0-n?n":dLml0*O4`ZPO.XV
qgitWq5:fZ,P74gdLn7V>c#e!g=%)Hjb%+?l7\H'Q+00LO(r_7B"0c7!<P0*h(-!@b!O6iqM
]AVLKp)RVILU`[GFpsl#q>=[M-o$hdcqL&aFVkOgJ`eS0X'6N()Am,O6%YHK_[P#0SB8.\,Ki
/L3dFZkfVt[`l4W)7"-'Lg(LbV)+IES\1@-0&eITrM\FgOM=:c*E/tm7,!qWlctC8qNnCn^+
$cS+&p*=@>Vu.h0H16fn.3g.Naoq.Hsblm87GE(Z/5*tj=-Y^Trq<n*_]A6C[WG'5?-es:GL?
P=n/9l$ku5"_?%/^$@FGAHCt;(%ekHoRA7gp6?/_lSo!p.rX%J]A]ATemq0:usZ9(@X9;;'"q;
82+;Mlcp9Q\]A/(1I:tjfie"4;ciQ94laDmEE]ALihQEhPGr-U$gprYW*4ue,@c(8U2?b_H!A-
ZZR?;*q9)[068KSj<F+#\kt*fA06J<0BamBp&5(T0t1#2[+m:RR7[KQh0QXJ/ijnZ8ROGs;\
D_'Cf:`c1"0Don#f]A/"nB(Y#dE'LTp_9ZM,HUV+u5?gB7?`PPrMXG9</U&@\":,uot`YD41,
O)<$jGjIu5[jb8_po/DG$W/jK]ARO/fKOE@G\dVi$)Tr!&8^n")gPG1i:8nV'!45KC8O''^@E
US-NN\06>q7_5c8mR!j`]A=)n8]AF?Yc3ScYHFpj.o3%I%;We@b^1P&EnYk(?tAo<0P"?Cgd7s
q#T@HZ:;OJ]AYY)X(K0)NQC_s2"K\mYHJTeL<Z<Lge7fJ9jm$rX,*fXQ+([mo??0J.>H33Y!s
hI)j5,KQ>hu[(f/u<!,TE)0Ed9bZn#dnUcaqr+D.odt%%;]A2cPe(T5%21SJb+BY(0c,3QB3E
fc#lS=#@hqd8Xu*H0mB)I(Bs9nEug:Zkk6o(;?=emTekNtGVIl%H_m&o&uI9?KB.9jTh^KGC
`eO,\bJ]ALFU9QH2Aeu@2%0kq>E'&gRE&kt1Bu8*,=]Aj,I_pWBgp87(=P<]A=0Eo5lEmsNoDVE
#1?W%@p&pQ#B-l!a:NJ15nnhO`#Ag_=#NOX'dZ-YG]Ao!%Et]AR[A-V<$e-R)R6g#!:h'f$[))
U?)*8-3T\V_%b\gemDd?l0;pe!a($ngWH._@SQ.4\Buo0iN)#0!l-d#rW5h*A*!+:OC]AM%29
.=MUXt54/do3N8_H;d=75>$WN%*$[>!=cVW`:Xk>Zn',D!h)$\7g^&d6I4fR8&!@N4J(kBVs
u<<..d3LG)?:>P_,n9lH"/C`,>CAq3JoBBO-e`4X0^_=S*I6I"29d[X+,[89-+ijAu#+W6=B
#Ei&^Wu^l.!lMH"N&r2bD7NQZ3Reh+#&UB]A8\A<em7ZFU@-"T9?M6P4'&8GC?/#:d+.q%=&+
bmCapm;-2ObiM<h&\0go.!8&\4C;$[*1c6LcC1S\>:Rj3/1a3s&tc;A8dC-YRmXcNU>^k*T[
Na+j!(nc7@*7g(('uh.MK#nRL?<p'Bh?%M2q*IP8/eqh\nX>[/6hs\O070+98?u#P0W`YX4/
d1T.<(^:]A#pToLGk?oMG#Y7]A3W\<lV$PMMl,uD<c)kRBl[>%cgPa!Q&[Yr:JM7V1P2DP>-0\
4=f#Q#N:#M7o^5fD]ASlN\>V/LPp+XWN=a0OZMHmka=I@F2fO9D#,idU\1k3M9Z0U,&XfJ<s-
K3VScoT+e(9H(jBCQlJ'&q9p=^oqKUfa""'4m.,([r'%kRM\-R%aCgZPP"gk#0[u3!QPl[F;
R@6$e$]A%Z$.\1.L4;F*,*(Yq_6<''jRR%Or+bi`e\/'k.*g4/rDAU`R&\6rf:lL9>LUo'Au*
1'jl?]A]Ao#@e*4+"K8,%_m(6KeOdm-P.STnbDgU8N!j'_ZP]AR?H<'p+r-jZK]A(PfY/%5MXk-"
Ag]A%D"gT(JUEi9<J)Y3f%[;B[O6]AX$2ie-':KK5KnE8*_2'ciX7J48@+JK@UiAS6XTeZBB4;
7=&6OK9f!piN_e!8$EV6MPV>nD#.Ft\:"2)P>[+`hbYB0aS7-3^IMToG6U(XAp^I>M9S)JB8
BS?1jVJX4hofJX>AEstH0u/dr9Bq/B.qj.$hEc8&dJ>gZTP6AQh*pBMmlOT=!p-t6&g14+Pe
<UBJnPG8^E/"s+D]A-!B#5O9YM6WW+N`P8j366b_W(f>$7pqf;4p3Y8>oeqjm2c"d-)!Z%eXR
A/[+6=*.sX[O9-GbDn@e0O!^9M"2T5JRJ,(-Z$450=!N4%^I'%$s\DWaaj%UMn"@P3%7gk2N
dE8;rC.s>KIZcVu42n6HM:J2WLXs<G'V\<9_u)ph=r%8c"V_U/1,hB_W^7]A8O4D,;-dTdE@`
O5La!;>a1*I+D%W;&;(QAN\NNYWqG_qJkh@brK=657YHT]A.#5OM-&KIfX]A6"`aSQ,QSF3kBB
p?Mk&M*GB'aHBanK^Rb?GLc(j`K>Yd('pbSG[n8be<Z*F)1/s*ef=#Zl3?>p!g'0p93n<.K0
W:h\^SufLaGl1F%j).pTi-oPT61U51&#Sf$C<%i$t9mK_)F+:bg*gU_]AgKPuZCmL1Gk07=Z#
s"XbaOO*B7Q+(U4]AA4;Z[k]AFT![d-ZVY3n_::naZr-CHZn->'"X7=92O-40F5S?s4^n>p2PQ
d>&7Lsd8]ATg\!U>]AkgEIqeIQVVOcfQ!7Xo)7i2mQDj-j(A.[Cd,qC((@'c(bVAgb&l\S:,=;
"h^j]A*Hq_:4W#Sf@oeMn(^Ed>R[D@3TeZ=IWrog'2./o:op<glGW]AcaHB]A"C<ZbuFM;4WNU9
>E98RC#5hcS.Q4I>5+OV5XTW=W*<eMb(BATUW^]A6jm0PI.s[DV!CRmFFFS'bji,S7F.[PLH4
`'"9PEc>pG'VF"Gn@+?Y6V0n<(9Q:1DA[H8A$NBeI`<rR3=V1DlJVhW!bSTEOkkW/E?lq.r-
XjF-LG0"g)r/d#KJ$<nM+\G3(*N3p/X)D;bPa@88%pF+s3=B=T<PX)9'[>t>J_J/FS[)`]AD1
R:@H=j/bD5jIEIoWB$hms$p7Xnq",tF%/Iu&cG(j&P-GM2Fr%;Ht-8LK@\Q"/>,ZXO)m;6pm
hfhj81T<.RA%\a:fC</3M:\]A?8\/hrQdbs(kiG"=m/a[S/[-b3eV1<;j<iO81BY<-CX6=e:"
pO\%2foZf-#]A:3e;4TKG0'0\4G6nUe/`l!XLl,oK<U"/\-hQ"!'.sI[pm?eolsn6:6!+28W!
*N6oZ57D%Se1cJ<(a,/Vph4CRsOW'+?,pSkF;Je(@"PoK>ZS(\Pu:G=ha)nd?]AHOk2mpL"pB
MN`tEO+#OUb5.b[OSn(ekJ%Q)LX#eJGin(*\t93)M84ThGVmA9dhA,Km%aUO$'MF#:BP8O"l
i20-SMSVYTk5ors*VEs,uWpJ!RPCl)E:L6s-<No3]A]ASTkjLCdY%QSE=^)+LcqhprRg6+YJj0
%%e?5Z362U+04mf&$.Q`B2#^,&eRsD1niqUIo#%6q)r!f11@!Xs-c1+/A,@X\G<?K1NUX#TL
^ZT6'gMK&dIc+m.jF6=D*9-2Ig6e=*mI<=r]AU_i7K9Q?kRDHuT:.a>?2=4tpD]A4;j:)?3V0L
Lr2X^%?m#m]AH#8]A>Q`mukWLAVMf8[\HT]AjpnSPWnT::RIWd/GSrGq@=@Uf9JR6pmB*O+SmIJ
ruWgd\H6;!_$X%>'nU?e)klg,5VDiB5u$sC[Ga#\erWZIY-*:Z95O9kU&>b8R>d[Bn,lAGgo
$-I1[r(9Br^+;XPc%BM=<n60We#lq30O5'.SB#lT;2:R!OV1^p\H,EJOaU27ZfR+!LPgYKt`
37jHkBJ2QV("tZ91V_mbX8!U3\ndEj7H/NUnG]A#lpGhsJEMSU2$d>HQpWC3#4YnCUZK:@JWG
MG$In&/GZ4Mof"FtXU2>;N$(2A,UC;rJ,K*=_nW*hKhDolm74b`3+KC$fbD(7NpKG^@)mJup
hbR''<D!sB$\r<]ALmcg5,o,U:R7NWA;-`uH2rIE]Ae,OWjNshbLgY'%6+Dlf4TsU3]A9YYs\Vs
&#jSj1C>rDmS:E-$;rIUV/ip!fZ2Ze4`p5r?>Dc!n0%ba4*hs_FSmK<T(Y\^WKOO^kWX@B'4
F99/7G=q._+TnkS!OMBjMWIl_sd5bug:]A(*ghOVbobuPc@^m4UB&Eer1IcmgE9no6jD5Y.0O
RN>&b_9-Jr"5$I]AA[-@\52$R-]AT^Z[$K0rmnGrO,DGiGfEoC^k#:Fu<;g!T[Fpgr]AUc1F-IH
h*;l=rKTpjbCeQVfmQ)@Jp]AZTa:E:/>H$":ms$Q.)%+-X2X28%,\.PQ%jApr8%<@mDT4an07
/.I`,iabaiaFq@6;(P:VMF[[5n<#:+%7!pA`d4.:6-\pj%WLddYJgPe,O0'Bn@5$r6Z&6,B=
DIa;nBbaW9FmT-]A#hil">jFbK&r3IiB:$572iHGXq\YhpUUk=kGfGqu]AIecMCGKV#/q%SMP?
?78W44^CX*Q1BDC)cme7<sU1;EQ"^$l!%Q'[Q[;;(^V@_(GU&/"urm*%NfXWM+4o%m%VV3I\
&2%@7T"oF0Tids+m4I-#Y`U"oCH^Be\bALo]A=8MWgFh9n*ll-=]AHX.0?G`k!3^rM%%o@U0np
!34JR[[+2\qPD(Ke90?.Em"8aVh2R(*#7LqI!*CmcHc&\F5XqEI;Sh5L=/Onphgi#FRA<iE'
VRV:1.>,W0Vt2qrCF-=+[VN.XUu0f=TIT/`W_Zr&Ai`&_lJoB$Lb-mdgApE\>^4(d_IL=3Ie
:fCDC+C4"S8[#uUE'Mj?))qm:id?,0".`&A;Q]ADQrP05l_?X)s5VDGffkbdu#,oP\=)Mjg1T
C'33h*[Ea$;L_#b:JQ_TeiC,37FD2c8/WQTK/QGL1Jr<`;ICX1SU-?Au1C9PM5pol+DXD)EO
_0"]A!Y'5N/p\)B]AYk7aoLjt'bc;+^n,Z`-[ENl:0!jqW<>%qRomYu5ctGJ!7i_r!i-5MmH[n
o4pB3<g/]AR_*jiJ%Vhc<&p=WIH-.X.A*Q`I&apu<E<>CS(/mNp,?i#:f'?u.0+)1aIC-a9]AJ
XnijCt$]AhDY_Nf4f[h%/[\B]ADnTkufd$8%Iu(e]AgjcX1K&S*e!8VX1V?F)&M5MB?/-e6DbK?
1mJS7EV_WpDksOApVE,ZYR"RtKCC3WqKE)2:%Hq=E?Kr[Au*NX*B5[B9\O%U)0n\CaS-jqL8
Gj=CkCt$6$4Mmnfg,,0I6^`))bfA.I#V]AKbApr[FrQM1KM<fi]A[8C=[G35U[(XgR/$jqHY^H
daj?`g7rin54e%Ze+F`Db#dbW0]AT),s8a_X]ABdjYiWH1,9RO@P666u2G(1#D+Sa^9@q\7baJ
.>G4r<7[XVo[EWA'Y-p^OK^XTc*nR9P3.Gk&jrNaBBO-,Spo=T_U%t>X0FblWT4G@DI6]A#oD
!$_1YUg1ab$!oLp+.4X3V;Voa91!etlIY>PFNE9\YkP.MOE3*%P1:`lTF[RD'1Pn\,'lhCd?
;?TB0i.dl*rKN=m6qq:'GPjDslC^WqRG5E6h4pGQR=G4SXt-7dCr558S8OYiP:HUKQpbK.^&
KGOh`J:HeeA'HL:m7,Vh)XS70A;`4?mCCi;J-Kk<lV5+!8$=9RY&g509h8^R%t#r8.GlS)60
#FNiriUJUkX+O6a2R[3tZ-.dWli<SVj+,0(%b-9E6gZEnN5I9j-^%@rVdeb6P^Wb_6MK2&f5
04CTTJm6gVRB;K$kR5`Af"1sa>XY7;*<uO7iRE41^mZXjGhVYKJi#!W-*V_dtfh&r=.`IiT)
b+4d66l052ee0eEi[6;L\A1]AH`3&XE*b]AtLP#29_]A"qRM:FNU"n[RZod+g@a4RL<L+CYOo?b
)6TgDoYb:"G,MLdNJuJX"5!]AURnQ`WmR>+$c^/C;->/)Q"YY`hUX)*3'_d1J;R2u4ARqPa4u
4ffHIeO%qcXrb&!*4B"GFlarF[&kf>4cZd?)<uR(#e5KO230Lpm(Ie*"chR)4sE)pXu)PUhc
%ReE_#FB`(9&'3t7[dFBF?>VG:kKT]AJW2r@-7<%lE0HJ@Y&T0"WSU8oO3APY!Z)t#jT`B*96
ZH-FK$Hs8D)bAnco&l%d1ZG?dq7Ib(%DfT+3&oUY=Aq[bbo&B:mQtXp^*Y4Aj/`t:F[2HY^P
>0(bJ#?gaN*fU`V?DE#U4#[Sr)eU4.u1lu\Y`V6M?!"XpeO9s@U!pbi![!]AG#3h`f8c.EVcq
+1*eTGT9"`^DK4>pW;5?-Q"jQ3GAth`Tle<PO*R#^'!Bjmcc7ScWiiBqt#kP.91uOg5L`Bk2
DuE-5EmCh1+"c2B?[3ILS2'>cGCtD3m6G#C#9RDUKk[$d3^%3LII_l@>[=WP5mn620+YPCAL
#e=(a/?.!_.(I7tIb]AUJV;C@BSboo>;F9G1o+s@tfbGuBoqhXCF-4=l=\WN00rZG=(IiM71N
K$]A"PLfleEe`>c0,gt0oaaN)p.1IWn\mYp:;C0+;6!sl98M1QEZ%4$CUD1iZ>s_kAqmqT(29
:+LJfa>KO#m/H.aDWY0UVC=%,SD,i"8PB%hT^Lj>\:V2YWcN[0&(E#K'M+&\5\U$/rJ[\b/0
]APD903b?n+;Tk>^pZ"7,!l(eA:-!is]ARH*BTseElfnh@VbVD>3\RFJg6#WT19/0a"c_h2ae\
gD_#i0)Z8UofK[2Y>1b+u8^rn8U-FHk6Q'WS.I3km$Ia6SkFJj_h^2C78g>)IH-CN[V6+4<:
6jg#;'BKXPWku^gJM'9$.NA)$AB5Qn<g>93,Qtq4m=h8@=q.mlJi"27*F`4U4280]ALk[K8@Y
p[EuA4]A2i5sd;*M131\X4mcJ+j-HCQC9S98EP%YTJO1(Jld:Ifd8jD,r&1\RFf?PX"GOC*^F
E<UK[ij8&4-?<TPGpb'E^QXj4&e\EP!E<CLVoVrCiKR5&f_kn2TOT^D"X:?d-\Gq31"(>:=N
nEXFlf4@uX;Y:ir$kD;>CJ2)G!QV\DEE)L*##6AJJPe'p1edfr5rqTYSfNNi`C#),Sdc(d&5
n1>Vm\NP`Cu3K05Xpc<H`0d"J!fLbs.,H3!f`IMSZZR(>J;^gAD.dN'*t^.G,3j<\^]Ag<%d-
ej5+dUECu,rG:_i%BcJdM1hX>l7.`T!U:o?XaGtdk'k[\A$RF_T,1Ef@#tU#u7dD]AaKQa1\H
]Aio[28qF.`JO>'48M^tf*=>"Fm2+"\WeF9ZEbeQ-eD@lIrnQ*QRcNNCb5%SL-l_7B(NpoIG3
tG1Xm\W8Bg=o/jTEtCI]Ab@&@9+C)-WZS*$QduJhEaS*AH;$-NK:Qop!8Rj^q[rQCo/*9bAVV
d/VNb'-]AQD.tUp>0;ea^3u#OT:=jcg+^SF64\+WpV5'!A'ZcM"hGtIbO(ecqM'i;:T6iWsOs
Xet[e%$99e$32GVXDQ!?lnt<[Ar+R6K+B[L_nL`AHl=%X%5*"o*2GT0oo*:C0AY#d('nV[VG
%,).4bV?>TUS'*=VPqC(lcm+N/@a6e(_C)nDRKMq=J+V9<$DhG0.`OTkD0<Z:l$`E\kp.'I8
0d:k^El@ir*6foW4Pm`YgX:7Kc[Yq8ESRLkZ,XMWPie0Da@L/S>L)?ITT6rMlJC^33O:P[F#
=\,?e<&M#+MXNsu@\"M_\+`F5b.Ub@#>NY'(4rCNIt5t%gf'T*uCAdN4-d4phj#n!&9>@Gq+
gp6Ti%N&l]AQ?W:4C&7.Y\#X^)#&Rlt0nFE=Heag!PA<<KYdeA&%dCCn50:!_X2FM+>3JaY11
uTu)F>[L;8,YW)&,KLpUk5Ib(/$l`VCfD7OeFYa"?<M_$Ko.^n`,1XO),RX,3+;N0e'O-X]AQ
77R)E6IgkNTARIEoEmUmUg-e/<(JtVAoof[i6l("Wqb,H@[o?X,[#sDH?BZ^BRmb%dcf/mGD
9EQJ%U/ue/342FN,f9=2#\f]APk^4H;l[:Y3HWiDLHc]A#d/%JRNM2Hq@N['LJ0aMse!XIlGVO
<i2#=e\Sm-P*R-h,g9$HqA"K]A?O7WO5Q3-X,W!Xc!:U/_?Z"/e+%?>NVbh&41[d3.&^V<]Aso
EDHVX^jmCM3,XVIJ\h;\^81Whi--oQ0""A?5FDe@DHksI_W;a*I_HTECoY0P'!%D8]A=Od+AR
iVQ#iQEg:?;=NK09jh04^ICXQ[`go&i"iP?"QfIKXaJ7f_?"lZb'UaK::;R`D_X9c@/JX,#c
;S2$l]A[@%5_8P$-@keoT,4%e1FOR9e4QOkdJ!k`657"9R;6qr0YUh@#(\0-R5aOJNpTU;I1c
:.or+XpJ@H8[fJ?qQM+IFi-qmo^PqU5Wn!Ljnln!9^U??Pn@9W$?p&WN&oh>H@>VqA.dQp"%
M;c16IiHr4___60P!,\<Y`3D+]AVqeugX@\N%Hj4T;uE95hUoW\c;lt-,0)\>lg9+o>;b#(,b
pS<J<'rLd/>JiJ%aktYG8joG4YVH&R0%t2+'MXWE"f=]Ah=&+OpEB_AhnE%(S@me2De`J.Akf
`Ndk80abr-$l&?D<<r4XJG.ae(OJ&i@)JXI.o[@5k+?B*_4gL=l2G+d(GdUDZ7K#-]AdC+T8=
Z^<EMAIL>>cg)q:8Z/\QaRA8-8b:en=EuRKp@VDde3GTk:<;O!HCjii,+CfIA`
\O4#.'-oGML)Hl>b3Gc-BRkdNQNa\$GSolaKn?hg"A"th.K]A(-R(`>5HM4)V%Q^-i2Mfie>T
Bh?qM!3%5$?>BG6XB_]A[4TjT>Y*[BMLq]A5IJ\:tsQJ')&a.KV?JsCY)0U^0[<-U1`#uOXfe%
_)[?^NrI[mY:"@V0ZTEG(W=!*(GX*i%6GX'?4.k&,iH[kAB`9jjn^fKB`^iN3^K#7-WQ=N_J
Q/i3NRUIjh0R%AVmZs8Z[>YBr;GLYY@gFfWAb#2rjK$ff/Y\Nq'_2%Jn8f?5V%b)LD+1K(#c
Cc)W&7e_=!2cp*MW_dU+8X=[5*I_)!a<TOBV<Y)HeMRGN5/@t87`25PO'F$)_F-cpT1]AB.P*
Z';4E<<NQR@Q=_LqHAW!pi0E73^EC_t,d>Tp[foA:'D&%iAXbbF!Rl`6F=doBi9B!cQ=(`8]A
q!@s(<ilr0jI6S)dm^u!`q&W,/10\:!&r03M;TFj$TTKu_,5ZY(N7L)m%MkVh8#q[=RM,paM
k#;B"kF^!:p?4D+5%f3Uo>lD/6'(FgV]A"548Y8F/G1'NQO-t_0gZqPf%3Mu7'LlGp<taoGb&
D4^I!MFWTl2nQL)ZdWW",-*+EXA#+n<UBTme8LWUn'?]An?_(-#AtbUm7645"Ou,j%H-DqWdn
fO<KXJ22"Qb%Qg4*j+h!!g_805ceZ"#,C+JIjXCY<VJ^]A[fDD8$C/j3VfUqRa5FMmMEk</TI
_"]AK?5k7.?9F;CQeqX8Y!:`DrmaI1qd/,DF,g+3S+85G]A74b.s*)_,r6r$tI6rto!k^^Xm!K
l-q-Q@lK*5s.rViY7:T)gYfI(\aEqXU]AaMuP%=W;;6'7TAcT@s#;iEP$pNR)sdKD020T)KW&
mDe>J*7S%/T+<hdVC2:%ZgmTL&Wn#f5B$S7rVrSY^C%8Xp"qFZ[32F!;Y^[,nb\Msn'?@So,
m:Ar39O=ip^U=BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\s#OP+]A^5N
-ni&(N@4@$d!Q&1s+8DW0I-Zj%ZG[j@B"ODuS'[dYT7L?T7-F6Qrq&V;rG@09k[aM8Q[eo4!
!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="311"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="386" width="375" height="311"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[tbclick("TA0");]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[分支机构查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TA0' onclick=tbclick('TA0')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[单指标查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TA1' onclick=tbclick('TA1')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tbclick('TA0')><div id='Ft0' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tbclick('TA1')><div id='Ft1' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?q`h'5&56UUJkk2,tE5\.mN&>M:8F,0DK//R#P[OGY68Mi*$M!$U,B_3S(uDL,-^J`'Cub1
/ld(Ep**4X:^q>d?qc5XI4O;[?eS+itAjL[L6&hm6tXS2kY^pR02d1Z.qUhjM$'gAUg?XX76
a#fQG#-]A\1b5^[CI?U*5V(9(pV%j;KBVC.EGnOb"OnPU>(i_t3dENaa?ri1CsnK\M<r9SuCj
5Y37&)Dd-5D7i#Hbo?#^;'6sQ]AXj`>W*B7If>hgY&ol$'=LD)2M>?ds).#u$mY\`XdK&)G<!
3qqbjN$:.pnG01$ti<$KC**$eD%:j2L^4LB@9RE0?r-F4_LQ"u9kNZN<fq9^[&Ya-#5:/r0,
Stinm(Z@cBANn(VCU$siT3Cs$G3Q7n7F5[RBJq_R==?oO$"N4\a:%9/F?^mmXRk5O7f1<Ioj
kB#=sGUf.<YA4<a_79gTm:t1O)Ho)^:6UF\bR&4oDYg]AD1K=%n4khpOl8T30gPbk6`ur-\mR
a$X`WM(bIDJS,JJS\>@DBj9X/S15u44$.c7b-[/&R'T)k/Nbqb]A6fTi.)-Mu%`+&H?f4f*X%
if0o>\F@0p#kYI'ooX8RK!#q702P*$8_D46/si8&3G)fHqblP-bo'@0[m;#!,kU5;On]AX3d\
Vt`SfE!Q)=@g.jO"?C,I'ERa2/,8V^Q/,dc&=*4FsC:&Wj86GTq+(+@@:Y^Pc$8)7JoH>.sp
rO]A[d=Fq[tTL8Nqh@pJ[jHlD#;ZJi$R;X9k6$Hhkq"m7!o)A6&V]AAfka.5L8i&7$nTDH"+"F
Gm@]A=HA_W4T6h0:6QW:I9<npHF.5E1Gc;U_:3uf]A8H*^em-1SYk<]AOB(-c;JT!Th(MiZV]A$^
-=1_N:Oj48]A`jthrEm-nn7a<s;5;eE%P;T+6]A[Fk<Yb4mr#1s\Cne%As#,p)JM#YiWE[p<\1
;&5XRp_+qa"E(AB<3JYkkm#E!GS<XX/&i"k%JYc66Zmqmh+p4,N9SsZYp3<Y6Y2PK1&2hJi1
@5b^G*9<r.77]A'i=Qo/rH3S?8j7oMAJb?uRS[mHAr,aNWoZ+Z%JmJ%BEPORG1g!Z'e2-IAJo
-/Bf7n4'=,%l.^+]AZ<!_]AJP3EQ;=2n@Xc!k=%tr<?RDYucsiWh0K[aYjH&qOdc-&C^Ti5Ffe
K.'`Mgu*#'j:,oA:S!g#NMZd@+RE!=ojOPG]A6^dgpd-)S)Z4j)UT`iJQACNuZ2bF)IY]AiKsM
h]Ar[0Z@p?*NMuoi#K0"1<ltX4![?=ae7"uD5L[:glBp+u6(E7.+,2:&nFPS[j`fF[A!hCXaS
G`GW4q`/i_r39F:4ZstElTl&>N?;aaGU]A[^ZUb8$^]Ah/o-@4X\aX3#*\.%:%_=X0e-<@6*=N
nF@983m,l82975jta@tT<Ib$D$HW?mL+XmWfAM;M<;]Ao1ms:7SliL.@lHac-X;@:RE($6>!/
6'FM[4DGoq5g'9rqn$V)3KVdICB\OTV8PF^GV/#]A/2joab.boYG3&<fI?.L@GV4`>lnEDI<'
e4kin<sr+`Z>jB2EeJ^MV;7TEU1%Eq--ARXPIonj#?r`G$3'7RXEWls35i!Z4RDo`'W*,<.=
_HPSF/Pg2b6P,Q,sCsO+h<N"mj'fQKphOI<.M7ZPTM,GQj3]A!pSWr0IUM:=@#^`,BO2T8S+3
QIBSg[D^gF&["%&]AKFNC4?bO"Ni((De3;)itH"reN&9U&\b]A:m_>EjRe+MA<G`Y(Mre'5^nF
T@0I>Q^"HkVo2?I/'d_8'e/6HM,>3lDHQDH=i[.mm_+X8+dlOTiB9PC>u1!@6SWgrsiOEM.g
M@MbT@eiC!<"(q(:;$OUgD(hGiKbne4",mooa*`7m6\94!Qd2-8AoA+F&f"UJM]A,4CRd>DZ\
<6CGY+??dk,]ACX#8R_H/hF%/\6gns"n>S^4)0`ml@I#jK8i$'RY"E=mrB^-GJL(TLJb%8?!o
DI&9u57N>t"/%o/sr8r3!>FfkMR0c\'l6&4qX)7Zk;7AU0_%!1[NDNDG4hnq,3]A<AhBmuWdi
6TfHYiB;\nttK[T(O&mQf\]AoOi7V5Mp@V7PdErg7&IPuBdp8nQ*nY[?Q:0,;"5_VeHnmmbr[
NMF]A!8HT("LD@-OI\QKdA;+p^Xc'glQ34A52*P8a+3X0M@p"fbp0_m'r;^+a<S*?T,8/`X8t
)^V%TUQr;YGu\PeBH``2q8c(4Y?tffIj=Y[br1t@hQl7LrCiP^[]AcC7.&]A/]A$P,4OpeV[eBB
jmi,6_IRYh3MdKBosqhtfe_P"uqO>De:Pcu-HM0S@<^CAN46f'09rSB.Q23NM[NeKVTpn-+0
7pPKNWh@Ybc8-"W&5U)pS1n2Y5&5\f:WsNO$Udam9&cQMa'gp&5NiE^WYoJ>NXL#6:UN,t]A3
C.C1G9$MC;N+N@16D(PiI0>%g1;/COBSC^JK-t#^_lfK`%3W*-Gco/+hFdjF0E%b+RQfc9m"
XXTZ5iK]A=\RO.HYZ9;+8XIZ$4>"Nbr74BR.P5)UNX2"u)8K$W'^OQm/_#eN4>,/STfs%[g(G
`@=6-d4a/1l3gW;[hjmZm4TW.EHq=('(SW-o#DpDkU`(1\kV+mnlS1UX)n5&3K,JJ(?<?EW<
ud.))t^)rH%^mC\]A`JB@8H<Y02>.X'*l/elqb2I-H'SBOV(8p_-1DrGa#<8?k.P)P)69`!uX
aB9R9o`BJNn!><XkY%b!sD(+OY;<"U>;QmFX#Hk7JRST#-\GGbTe@*`n1;Jsb]Ak(u<'9D[Zk
*Ao)?8#;.YBa.'Jihr-gOfk("3K&(%>H0,e<WXkMqSE7XDn-J;F!8?>i<hPT-c-O:GZ0k$lI
eIAhRsC"_<8lM^f]A*XZ<k\k)?ri*Ig,B&C,3^)D0jB32Wu8dK_gmB>SmrY<LTgm1JFOg<i,%
eb?T:geQ-$bnk6G^Ji2)IC?TNH0-e`hq#R-na#UabnSgkI(dGm.[,s^Y/4\<`bAm./MhOF;\
J]AX1L1JN-spt5?t\[^lfl%C3"gY5Kp_u)9U(Ol/9ULWdK`h0;=l\RGBW3["&qT3IY,^Xs4C_
'gciaCJn9I$r2=6WNTCjOjjLV9=V<$_nY(Z,"!'ga5u*`1CCB06LEc>\^XD3.-\L%.-2BkLh
Q\Tam)(ggF!?e!:jk\)8>"$.LGKSA9F-2O]As_^imp?$)]As]A%=5(sS<(h&(07MoQe&OmUGKFp
Zj@>#4^)#rL_\h9^iL]A6$ciZh0dCn&6oL'Y_un(5<Y+q,IC`STi4op^]AJlT\3R#tnC;&sfbT
,qNI293B%DQEZ#g/H86%7dDJoLO0dd1A,b[R/>:Bmk=V8DZ~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
