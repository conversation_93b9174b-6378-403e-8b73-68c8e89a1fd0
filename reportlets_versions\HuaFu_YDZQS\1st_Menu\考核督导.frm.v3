<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-12-14]]></O>
</Parameter>
<Parameter>
<Attributes name="sxtype"/>
<O>
<![CDATA[3]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[8019]]></O>
</Parameter>
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[tzhAgscfe_20231016150910]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		-- select * from ADS_HFBI_ZQFXS_JGZBMX WHERE ZBID='bbyxzhyrjzc_20231016150910' AND TREE_LEVEL=3 order by  oc_date desc
		-- select * from ADS_HFBI_ZQFXS_DDFX_YYBKHMX WHERE ZBID='bbyxzhyrjzc_20231016150910' AND  OC_DATE='20231123'
		-- select * from ADS_HFBI_ZQFXS_DDFX_YYBKHMX where simple_name='上海东方'
		-- select * from branch_simple where simple_name like '%华中%'
		-- select distinct zbid from ADS_HFBI_ZQFXS_DDFX_YYBKHMX
		-- select * from ADS_HFBI_ZQFXS_DDFX_YYBKHMX where zbid='${zbid}' and fgs='厦门分公司' order by oc_date desc
WITH TAB AS ( 
		SELECT 
			A.AREA_ID,A.ZBID,
			A.ZBBM ZBMC,A.CJ,
			${IF(sxtype='1',"B.ZBDW",IF(sxtype='2',"B.FGSDW","B.YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE 1=1
		${if(sxtype=1,"AND AREA_ID='zbkhdd_jzkh_jzkh'",if(sxtype=2,"AND AREA_ID='fgskhdd_jzkh_jzkh'","AND AREA_ID='yybkhdd_jzkh_jzkh'"))} 
		AND YEAR=SUBSTR('${date}',1,4)
		AND B.STATUS=1
		AND A.ZBID='${zbid}'
		ORDER BY A.XH
)
, RQ AS (
   	     SELECT TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR FROM TXTJYR WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, GZ AS (
	--   SELECT * FROM DIM_FILL_ZQFXS_ATTENTION
		SELECT
		BRANCH_NO,STATUS,LV
		FROM DIM_FILL_ZQFXS_ATTENTION
		WHERE EMP_NO='${user}' AND ZBID='${zbid}' AND LV='${sxtype}'
)
, DATA AS (
		SELECT
		A.BRANCH_NO, 
		${IF(sxtype='3','A.SIMPLE_NAME','A.BRANCH_NAME')} BRANCH_NAME,
		${IF(sxtype='3','A.ZBZ',IF(sxtype='2','A.DNZ','A.SCORE'))} SCORE,
		${IF(sxtype='3','NVL(A.ZBZ,0)','NVL(A.DRZ,0)')} DRZ,
		${IF(sxtype='3','NVL(A.ZBZ-A.ZBZ_SY,0)','NVL(A.DRZ-A.QRZ,0)')} JSR,
		${IF(sxtype='3','NVL(A.ZBZ-A.ZBZ_SY,0)','NVL(A.DYZ-A.QYZ,0)')} JSY,
		${IF(sxtype='3','0','NVL(A.DNZ-A.QNZ,0)')} JSN,
		${IF(sxtype='3','0','CASE WHEN (A.GOAL=0 OR A.WCZ=0) THEN 0 ELSE A.GOAL/A.WCZ END')}  NDJD,
		${IF(sxtype='3','0','NVL(A.GOAL,0)')} GOAL,
		${IF(sxtype='3','0','NVL(A.WCZ,0)')} WCZ,
		${IF(sxtype='3','A.ZBZ_BN','NVL(A.JQZ,0)')} JQZ
		FROM ${IF(sxtype='3',"ADS_HFBI_ZQFXS_DDFX_YYBKHMX","ADS_HFBI_ZQFXS_JGZBMX")} A 
		${IF(sxtype<>1,"LEFT JOIN BRANCH_SIMPLE B ON A.BRANCH_NO=B.BRANCH_NO ","")}
		INNER JOIN RQ ON  A.DS = RQ.JYR
		AND A.ZBID='${zbid}'
	 	${if(level==1,"AND A.TREE_LEVEL=2",IF(AND(level==2,sxtype=2),"AND A.TREE_LEVEL=3 AND B.UP_BRANCH_NO='"+pany+"'",IF(AND(level=2,sxtype=3),"AND B.UP_BRANCH_NO ='"+pany+"'","AND A.UP_BRANCH_NO=(SELECT distinct up_branch_no from ADS_HFBI_ZQFXS_DDFX_YYBKHMX where branch_no='"+pany+"')")))}
	--	${IF(sxtype='1',"AND A.TREE_LEVEL=2",IF(sxtype=2,"AND A.TREE_LEVEL=3 AND B.UP_BRANCH_NO='"+pany+"'","AND A.UP_BRANCH_NO=(SELECT distinct up_branch_no from ADS_HFBI_ZQFXS_DDFX_YYBKHMX where branch_no='"+pany+"')"))}	
)
-- SELECT * FROM ADS_HFBI_ZQFXS_DDFX_YYBKHMX where zbid='${zbid}' and up_branch_no='${pany}' and oc_date='20231206' AND TREE_LEVEL=3
-- SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX where zbid='${zbid}' and oc_date='20231206' AND TREE_LEVEL=3
-- SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX WHERE BRANCH_NO='${pany}' AND OC
		SELECT
		DATA.BRANCH_NO,
		DATA.BRANCH_NAME,
		(SELECT ZBMC FROM TAB) ZBMC,
		DATA.SCORE,
		DATA.DRZ,
		DATA.JSR,
		DATA.JSY,
		DATA.JSN,
		DATA.NDJD,
		DATA.GOAL,
		DATA.WCZ,
		DATA.JQZ,
		NVL(GZ.STATUS,0) STATUS
		FROM DATA
		LEFT JOIN GZ ON GZ.BRANCH_NO=DATA.BRANCH_NO  
		ORDER BY DATA.SCORE DESC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_sx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-26]]></O>
</Parameter>
<Parameter>
<Attributes name="sxtype"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		SELECT 
			A.AREA_ID,A.ZBID,  
			NVL(A.ZBBM,B.ZBMC) ZBMC,
			${IF(sxtype='1',"B.ZBDW",IF(sxtype='2',"B.FGSDW","B.YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE CJ='${sxtype}'  
		${if(sxtype=1,"AND AREA_ID='zbkhdd_jzkh_jzkh'",if(sxtype=2,"AND AREA_ID='fgskhdd_jzkh_jzkh'","AND AREA_ID='yybkhdd_jzkh_jzkh'"))} 
		AND YEAR=SUBSTR('${date}',1,4)
		AND B.STATUS=1
		ORDER BY A.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="date_fgspm" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-12-12]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[8005]]></O>
</Parameter>
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[xzgjzkhs_20230820170229]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (
   	     SELECT 
   	     	JYR  
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)   
		SELECT    
		     AHZJ.BRANCH_NO,
		     AHZJ.BRANCH_NAME, 
		     NVL(AHZJ.SCORE,0) SCORE,
			NVL(AHZJ.DRZ,0) DRZ,
			NVL(AHZJ.DRZ-AHZJ.QRZ,0) JSR,
			NVL(AHZJ.DYZ-AHZJ.QYZ,0) JSY,
			NVL(AHZJ.DNZ-AHZJ.QNZ,0) JSN,
			CASE WHEN (AHZJ.GOAL=0 OR AHZJ.WCZ=0) THEN 0 ELSE AHZJ.GOAL/AHZJ.WCZ END NDJD,
			AHZJ.GOAL,
			AHZJ.WCZ,
			AHZJ.JQZ,
			AHZJ.RANK 
		FROM ADS_HFBI_ZQFXS_JGZBMX AHZJ 
		WHERE TO_CHAR(TO_DATE(AHZJ.DS,'yyyy-MM-dd'),'yyyyMMdd')=(SELECT JYR FROM RQ) AND AHZJ.TREE_LEVEL=2 
		AND AHZJ.BRANCH_NO='${pany}'
		AND AHZJ.ZBID='${zbid}'
		ORDER BY NVL(AHZJ.SCORE,0) DESC  


]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_phone" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select * from ggzb.ads_hfbi_zqfxs_employee     ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[dxcprjbygm_20230820170229]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="sxtype"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.tabnm='tabpane1';
window.obj1 = "";
window.obj2 = "";
window.ygzarray=new Array();
window.wgzarray=new Array();
window.yattenarray=new Array();
window.wattenarray=new Array();
window.user=user;
window.level=level;
window.pany=pany;
window.parent.document.getElementById('KHDD').style.margin = '0px';
const elements = window.parent.document.querySelectorAll('#KHDD *');
for (let i = 0; i < elements.length; i++) {
	elements[i]A.style.width = '100%';
} 

window.tabck3 = function(obj) {
	if (obj1.length > 0) {
		const ment1 = document.getElementById(obj1);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj2).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07'; 
	_g().options.form.getWidgetByName("sxtype").setValue(n);  
	window.obj1 = obj;
	window.obj2 = ftname;
}

setInterval(function() { 
	for(var i=0;i<ygzarray.length;i++){
		const element=document.getElementById(ygzarray[i]A);
		if(element){
			var bnm=ygzarray[i]A.substring(ygzarray[i]A.length-1); 
			element.innerHTML="已关注"; 
			document.getElementById("attention"+bnm).style.background="#AFBBCC"; 
			document.getElementById("STA"+bnm).innerHTML=1;
		}
			
	}
	for(var i=0;i<wgzarray.length;i++){
		const element=document.getElementById(wgzarray[i]A);
		if(element){ 
			var bnm=wgzarray[i]A.substring(wgzarray[i]A.length-1); 
			element.innerHTML="关注"; 
			document.getElementById("attention"+bnm).style.background="#417BF6"; 
			document.getElementById("STA"+bnm).innerHTML=0;
		} 
	}
}, 100);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout1"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="4" bottom="0" right="0"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="4ca91476-dcf5-4cd0-aaf6-ba8491300490"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout1"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="680f6f3f-1472-46fe-bf03-b979e965e7e0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout1" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout1"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout1"/>
<WidgetID widgetID="2ddc1775-07a8-4a0e-a250-46f470baf36e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="d126d6a1-7422-49ee-9d87-45f784368fb4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,0,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="11" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF($sxtype=1,'zbkhdd_jzkh_jzkh',if($sxtype=2,'fgskhdd_jzkh_jzkh','yybkhdd_jzkh_jzkh'))]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=B5 + "得分排名"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="4" cs="6" s="3">
<O t="DSColumn">
<Attributes dsName="para_sx" columnName="ZBMC"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[ZBID]]></CNAME>
<Compare op="0">
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[xzjgywshs_20230820170229]]></O>
</Parameter>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%fmdWaTaZ'3F5cFTOP17N-%GM]A=De4<$;%^'O"AkJbKjcV_K-6be\X]AE%ce!aL42:j;mKf
gJ,E[XYk.H3Hu+HS?E6"GY!Lqo#$FmXLHkN2<$(L5S\&-'uMI/(Ats)_B4<h#X<!!$EmaY/^
'!s!/E>5`9oAH'S5.l[\4prr-dZ-UaD=#nT&/;Q_6qNR#)%li,J+bu58HCVW4]AU]A#F_1Tp<R
WO5\kX.G^<Z`sTk@ag8fla4h9(M'-.GNnWh</;:cQh`L^AmH^nLq[bC"&&r4i$Kc3LUarp>$
>YJuk^4q*7G7:cn0n<hQM9md'9-/Q]A1ZSNj?a:=sElj+Dl,H`Pm+Fo0al!s"^pgT>?ThR0[F
gDQ??T@[<:gOJe<06q^^$]A71=mBN<a+P,ZR_0qrdlntCCh!Khgm2YNM1S7I-Cq_aqT7Y=Wc0
#>Ro1%'9Jp3abmk5h'%OE)VZ!d_7OS@qYlZRTb]AN+n+`SQ*OEAsN'%/>f;A9qg9"DioR9Ckh
MYd$[`caNhI*[BXkduk(B^mqlS]ATIAOmVj<iLK`!fh0m]AZXN5gbQrA9hl#'H>0uO3YdD5;dl
3>5A$luCH4WOL5R,sI2lNd"WNWs$0(D&c$Jc@.D%D^$MjfCcr5I%A/+=2SPE``/8@2Abol!r
qSj)5!LC>]A$O3=J+$2/Q.#"Bqn74geb3NQcZEci.Tah1W]Am&'*-.EholDDD%aE5]Ah>l,Bl?.
3OuW&YMt=fq=?-q?IP?FK^5^heWMBEqR$%tKG*:OhMQ-C^)YTJE]AV@h(%pFL@tBBR&U5+tM@
F/KO'*]A$Hj-,%YKRM9)oKrYWl0qams<a@=;B#GU1["9#WK8d9@]AmS8\!#*KD9P^W`Gc`0tQt
gbVgX..l/6lD2)9!Pm_n#I]A;():jKV'YrK.c?d!\`ZX0GMor/BNP7eESB6g:"FdrRBXM*`RD
/ab8n#W,mGGtcsdRI"-SslH"GjXM67G0Do(\ZcF"pmofh!,L<KKc0"LBg_^Af)e3%egD]AEC\
M(:Y9/]AbI6j0#sDbBa4:9WL_q%]AYBXONA5@joesaB(co.kF>Rk%\?TrM#Gbf#+<J;$to[Rb8
HNQ%ffY7=Y%TI+,5GVB7Wpt$CAkl#7)H'G\qT!%"G&uenN/#)P!u$C-1^T$f1qb>3il`"Z#M
T`4JfUqUpP4LC^=8IREtY?3QB*ilb(g_NiLg[R\2=0u4=TJ5[]AU2`5hK72ISRBaas(o"4:nU
YUW1u,-U!3eWq'k>N2@/#nmr[F)0TQY=7M<>V.9?=R-kK(3G`9\6#LX$!#]A'>(?JRl#dnb+:
#+1cSp\id-<ODu*/MB`5&g32RZ(G%leTrZ)Ypf`\]A,uQ)a.RG4;thG`E\HE=?TFcb1P&H>^.
aP`eDn062A*^0`lI@BI&d\9cYu5Pq/cgPtg0,84A\jcEZi:<`Ma%4g)q\W#g9ClHckcqg/i%
Wq#GJ3E'-iQXCZkr2qs5,G!M`a[QOrM(?aAm-D0VY?Jr1-/qXFTeFY".?e;1`itG+d:hU)<i
jEol^ttL8j$MV01^]A+YA9#jC'e:e9Y8+eY3B]A=d+<@[$Q6t'>US:g/p.u>46<Un&Y$&]A]A*(L
r:SM!Q:0!OjqG4fBC@9iolOnQ.&u"Ff;0XjUCS:*>(5H<tM\4%sB^/[;_IiQ)SGm3<0IXSJ5
]A87oSq$inbKVBsZmKjMi]Al3h.7hGM"[gKKGm>:TW]Aq[Ro.tXt<cRflY8XM/8!X8Un*>!gfjK
#2TM^"&33tfCj<-f&TL^("lApW@`tf0&9ZnC_i<AlHO!SZ>"W).,+i;PAm5HE]A:EMC6]AP2Gb
O@afKFi!.mPaOabQ4"cEIXV-NqeFdnpra>gPI8,P-!PHC<asETE+fVm7$.>o<>=Q$[_Y?ZYi
8s%d5ggc5X_FNdsTESOa->SalZg\ePBn`%6<R`#;kmF<_gCXeekPMYM@_aX[/Q$m&$igVIfk
HTGg/O8f0'7EXh)ep:"F+MkQ9A,OBBOY1qXnW@X#7"$YKI[`8uZ$_@*6K=a!H;Ou(_=(0'Zn
em`VU/TQDq@q5XcFau15VtsfIC/)l;U14?Y=:Fo1'aZL">b2.c$bJR"7b1A\O+i]A_QdJ811h
9/\q$hrCgtK?Rk&BrW-.@c,:ubs`sjfC\XK_0?'51#/Y"l'1/@UNdX.#@&oDYEV%foW"@u#8
:9.p)!R-R)o[S<5lZ4H1mKo3*\in_?P=BlQ"e?K[r@*t3_AUk!^Ue]AUh`riM)5LYn-e:E8G[
8JHaHTpR>=eN!>k3-PTcR)Nj[SK&,%rL1q^[9sVsWOes$V3g*.k@5=5qHo6PP-<BCl#8QQ:h
ZIFY#C&H#j%?YnH&NUd^3Is$0!iW3lU"3BqnqMI<5J`@[f4asNn7,Dd_3&8#1;G_70c+b7kQ
dd&.Zo;dc\jO*<5+[#`CkUlk6:=QJ;LLO-iHDaj?SW>+g+9(34%7?mG`(.X>@Rr/J%L^RG$1
4YH(lij_SsoeI;ucDYiD"11@WBXa)I-?$o(?[MBG%F%)L#toLWSL+f7Yr.\tOtJ)5d;G*raG
,ZN5j6?]AEqX:I<`7IDD+Z`-k^G&cX;=R#<3`9o0m@)rY7&_c7E^*]A3$,GeULP9-<D_<_SISc
2*@eGZqEpUX=Q-^d2ZZg%jVJ=ZJA$0%lLfb'3-R=V>*ql:mGammOm]AU0cLH@8j.1Rdt&AdfT
MS@&ldHU$(do_jh]ALT1;(SNK>SDP+JicO3J'Kl?.(,IPS[T;DE-qOPNRDtCPh9t,Dk[;&\.^
(\hP%a3_@(]A$rM<LU#^VS9Y[7!&A!3kR97VLg`$[qHUGUoC`:l?uS]A[oNUO9ZbUJV8WQJs22
P'bG>I1d^Bn5^>t`s@,0s(Hrp*Vb%()[!,2=Uh;GW_:+,_==\d[LBD1.<j4_Y8CX;PB/@]AcW
BPhU83r8rMZJN#fhgOUZ$7K@X*'=BtQn=d`7D<(('6g=@V7K9oh9\F8>)1,R3I3L-G;:DSp%
$1KJui6qc#6BW[@?UfqG.[8(A/EUSB#$MCl[\cl03L@.bkij>bin3.KXi3s15pDW^MrAVqE[
Zo,"I2iHalb]A0a34K:1B_^U/u9Z$SG.9=m%B3<sI(M,$:%/%]Aea-e$pmO%'Qr_?3WLpTX$N;
,&(1>b&%oI!f_LMZtnA^Yh'*hSJSP&bi:S^4$oE[1Zh4R2i3%bhocS(O:'KLDm-QNO='V22h
Dn"0i"=I8m*A+_bHAlg1S#CCag:j8O2JoG;"n;/$5C1:;2#:e\MB?(R%DYC((_b>Jqj/nn^5
on*F&5u:R/+h6b@Q^5na;:sdn9PZpcepj8NEY$"7ooi91EaBkd5O1tePUFI<IE=uicB?i=a`
Ljj*5P,sB%-9nC$]ARQlX#dW^[OE:960'W4GbKueb+X8G5/#&2H=$E58[knau(%m);/2=70[&
OX;20(Zk5`.#)T'+[AF]A(iAnjLY<Jf#hdl?hcu@r=/r<W4j,diE+:.oe%L8Z^'6f."SRMiMX
lnR^Qc==c,r(`^TgUF=jdF1HpbZk*FllVqrb=[lZo:ZC>=Gsm?he=u"dUlE0#"n#*7iX]A=Hs
WP]A#[k@gPgiSd7mLE-;LMbENR@toS=Q\@/B\p,T2rV(ZdR"08]A/n_ge;@a0VK(kO_F/2V24D
lE9e*`(Z4im:Y6\?*_GEUB,%Im]AYi81/`+DEld&bao-CHC*eO[.>ku.U%_njqfu"k*R+VDfX
tLJ8$WbJC-h/KB*+gZm^.L[n(LusU#bpLa/a$")]AhFV,=a,[g+/<U\(L>a=N<t_is9i$"NW[
rbir\eLBHYUq?mmm(!3bM'a]ATuP0=Cg:U]A.C:JN9Na\bFR=<,sfOsD;s2F^]AAbqgLMi2:>&`
C8jZb=JHR-;]AdJI[X<=OmEA*.?Y#2B_4l!7u.DuR*P)$c[PRUnkd6Ngkb+D:l!>\&>S5oe0G
pMg]A!P8LCj4l[N<tq1pVK,FGhW?8?OiId\XGgpq7nTRr\C#M19\\_^#\ObQbp+$RJg:ep%MK
B.3qKHC@["8eirQjK3)XY(?Pl7N5OEfUZ`o&'NcgNJMSj'0K6@&A::\EjWY(OUKDNm*XV$&A
_JKNC$(JOr7NmeD$h5]AK*u@;'oWgF'_C3d-X?cJPDYU<ctC!l'F!2^qO2YG!)%\3Wird4Qj!
B?UiRN"HUKe6O<%p,Gno>YT#$Rd<M@k/.9&h4*41%p^/1E>Fk-qi!#9^gXB*:c#,c9VIRb<q
QE`]AItlW@gV3t:a9kFWL%j;:G(H,%.r6i(O?,1Q77>j]ApnUs2@;q_P2f;"1MXV3q.oHor6I7
40kE$Y++BLNRDc9.^/L==Fap)dA!\EqnDVRE"4Vp9O,[tN>=TZinV*7_\3<]A;a`#"uHmSDSf
U)F93QE1+ieeQ+B>S=smSa#cI4(^["LCWdh<;eM;+N;VlO\X;k!<Y0G?,2d*3mbQe:@WAfg/
.\)>H#`G]A$OPFk:ABApSfes6CG\A8K<hO_Z,SmXYBt<?u'sNU"g76\+HAG\[Jr65q]A?)%)ob
E'u&.LIA<$E%GiKm$D_8`b*:XNcbj[?%Y@sa*0k;44R_lO6)_['94'X`4b#ifFh&%<D(]AMV^
$":ud,s'FcYbXJOn)7=?@0eA&;'GoNq4JY$MDFt1gu`CZW@;_1k:&S1&^"eRYU!P]AThuiQ5>
S/V)Z;u9eASc6?.h4WpYLpSH$qLKt:E(%a'=,J"s[NrT2S#"3Ar>]A[f)/LY'0C4O7BB"n5jN
>5Ig<euks%mrtj2JA%4`KaF,MZOa:neUgt$$YhsZW!0T%(=[q>:^_#(/Z8i\TGH%/>#5V's$
rDLRId8Us'Z3pD&V$%mJ[Dg>.c3*drc+[$X7tI=ntNBn=;F6W9nA8`?25H)*#/RfE,5A$YTP
mTEV]AQ(=3+d5RV6+/YE9SJH$:)E(2kq&0L_c=.`5/%m*8Y,4=`G!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="103"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="56" width="375" height="103"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[const ment = document.getElementById('ABS01');
ment.style.background = 'white';
	ment.style.borderRadius = '12px 12px 0px 0px';
if (level!=2) {
	ment.style.marginTop = '15px';
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="ABS01"/>
<WidgetID widgetID="9652d9c8-6f58-4304-94a8-a5f888bb9cd9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ABS01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=FR.remoteEvaluate('=value("para_sx",2,1)'); 
ObjGet("set","zbid",val);]]></Content>
</JavaScript>
</Listener>
<Listener event="afteredit" name="编辑后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[
	var val=FR.remoteEvaluate('=value("para_sx",2,1)'); 
     ObjGet("set","zbid",val); ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="sxtype"/>
<WidgetID widgetID="195e8edf-f7b6-4533-9710-f76a73c9365e"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="1" value="1"/>
<Dict key="2" value="2"/>
<Dict key="3" value="3"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbid"/>
<WidgetID widgetID="ef34d585-ccc4-4f39-af66-2866dd24756b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="zbid_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="true" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="Arial" style="0" size="120"/>
</LabelFont>
<ValueFont>
<FRFont name="Arial" style="0" size="120">
<foreground>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</ValueFont>
<controlStyle borderType="1" borderRadius="2.0" isCustomWidth="false" isFloatWidthFollow="false"/>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<borderColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_sx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($sxtype==2,value("para_sx",2,1),value("para_sx",2,1))]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="148" y="17" width="212" height="37"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0"/>
<WidgetID widgetID="f65f5669-1627-49ae-ac91-43da3f3480ba"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="label0" frozen="false" index="-1" oldWidgetName="label0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[财富矩阵考核：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="96"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="6" y="17" width="142" height="37"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="sxtype"/>
<Widget widgetName="label0"/>
<Widget widgetName="zbid"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="56"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="f28589ec-dbf2-43e6-bf72-d30fd4a8b455"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[381000,2857500,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,4572000,4572000,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" rs="7" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$sxtype<>2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" cs="2" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:98%;height:73px;display:flex;align-items:center;justify-content:center;'><div style='width:95%;height:85%;'><div style='width:15%;height:70%;float:left;background:url(../../help/HuaFu/cup.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:83%;height:100%;float:left;'><div style='width:98%;height:30%;float:left;margin-top:6px;'>&nbsp;&nbsp;<font style='font-weight:500;'>当前分公司指标全司排名</font><font style='color:#E55C17;font-weight:500;font-size:14px;'>&nbsp;"+FORMAT(C4,'#0')+"</font></div><div style='width:98%;height:30%;float:left;'><font style='color:#8D96A8;font-size:10px;'>&nbsp;&nbsp;得分</font>&nbsp;"+FORMAT(C5,'#0')+"&nbsp;&nbsp;&nbsp;&nbsp;<font style='color:#8D96A8;font-size:10px;'>年度任务进度</font>&nbsp;"+FORMAT(C6,'#0')+"<font style='color:#8D96A8;'>/"+FORMAT(C7,'#0')+"</font></div><div onclick=Fgstc() style='width:98%;height:30%;float:left;'><div style='float:left;height:9px;line-height:9px;'><font style='color:#586170;font-size:10px;'>&nbsp;&nbsp;分公司排名明细</font></div><img src='../../help/HuaFu/sj.png' style='width:9px;height:9px;float:left;/'></div></div></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="6" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="1">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="3" s="1">
<O t="DSColumn">
<Attributes dsName="date_fgspm" columnName="RANK"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="4" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="4" s="1">
<O>
<![CDATA[得分]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="4" s="1">
<O t="DSColumn">
<Attributes dsName="date_fgspm" columnName="SCORE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="5" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="5" s="1">
<O>
<![CDATA[完成]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="5" s="1">
<O t="DSColumn">
<Attributes dsName="date_fgspm" columnName="WCZ"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="6" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="6" s="1">
<O>
<![CDATA[目标]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="6" s="1">
<O t="DSColumn">
<Attributes dsName="date_fgspm" columnName="GOAL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="64"/>
<Background name="GradientBackground" direction="0" useCell="true" begin="0.0" finish="0.0" cyclic="false">
<color1>
<FineColor color="-68630" hor="-1" ver="-1"/>
</color1>
<color2>
<FineColor color="-1" hor="-1" ver="-1"/>
</color2>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[UqCn.S6m-%Rnb/Z(qh5Y0Z"AaH9r1j##V,7Lu(Jj1/a>R6)&QmmkThcLbM6c]Ad6'Ze1hfX*T
VFi\TaUNf"LCPG9qaOA^?r2HU$E6pY!kC(p'T*IY711KaS^<Q(C4h]APugbqXblo2<#JgJJWJ
G/m)4)4a[%R,AD#[]A&?<L':H<Pd:_T:b(B%JmYt;?o$b:cW57oNl(c+T?#4JGH_RM`=GGsXm
pI.:aCh/=L"Mp:E5H;ZX&l'D[1Gm4qNH-ZP73i#C;G*iYF%o5)fLZP/PNq6k[a28g>]A(gXEL
2E3]ASNl\8VbXb=_-=Kc`?QF)\gQ&,TthG&i6QqfHLAF[6]A/o[3n)H<L5(W;ZTJn<]A,mV^FqU
qa6qd`\.c!GN-Km%3Lgb/a#nlGlkW:=@3#N#($<NoaCD9!1-bVXq;VQ-TO8^5RG&Y[CqM=ju
Yftg:$rU\G6e)N:4L5fa2U/ph$Z8e,.X=BD9$D""leUbF,:*3';D.U)iA9E+!kIk<9GtVlA2
#3BlA:(9`RpgYKVpWY4]A.[t[>?T.D#'?Y^*AEJ%0Hml+'PkWGrX2T?%.MK^A]AA,*'V!d8YU)
S-Cc.$&X4ASdEK(hEG<>CKp(X,7;cIlTT.T(ODl%cH3s_*ghOh;`j$G$Do._PnO$+#/0)JIj
2nl%'RseFAAh!F3r)LD6nXV#)$7-o!:V#U.hPMgfTSHXRZ`l38/8WV4k([el&Z8bAi(&e-dh
#h%kLL:,7=p$mk!A/<e)hNV71'DHr:^3juj]A22;`pSuP5?:I:MU93DuhEC^PkKf<kE*7\3eC
K3k_SbTR:&MgZ0KK!NUH0b;WkTn@[g-l#\+)dHs!?IFI\M<qW]A!6P&E4]A>,%H4u4jfLuN:ca
IkP99H"F\09SeQT2QJnj"^fe[NJe3")bn(9`$d$K<U[qkl#8ZWIea)J;YSZt7rQG-_4V!k8o
a6e=$tlm#ahC_&)a9>th(r<1Y"5gh6fMFl[ID)dAq0@lqh<Rtn']AEA^7Y&]A$.:_g@kk;S"!P
)qoRa/C,BO1?<1q-m5r"qQ2s`Oc;[$_U5l&:QHuS.E4"=12['5X39g_:XhB3J7Mr/Qq1Z7LA
\_pm$'*aQRg(VtCOkWJLAD031QQL#os"kW0/u?^*<;TZJPS%jg`#'@J>>\q(Z'NuE+gb=s]Ar
]Aa]Ap%fR5!PgJC7:;Tm\Eqb2hI7&>rk)\EXTE:r7(K^?Cc<lPWMgM\(o*lm.dKD:PH<5VH-"l
oS643Cj!cE"k(kMjmdY_f$,UR0s+-<n42XCnnQP%Q8k5BiPul(i)pDpX%5'Bde;>cX@@,*Bb
'g4+VMe#7?OdJ<+S\Ok`./7P+4ViH0qDLM!?7?fZUhcDN%I%]A8Sjpb.:WU=3Y"Z6&Sb3HV`W
hd5nl$MEc>[dnp"32&j>X'f77#*XUa/tCBZ.Rk+G.fH[-gV=(g_uJYG6-en$m$lg\9)mHQIs
,(lCi+Ho^:nIi-^eIcJ!]A.KQ-&[+!Gj"8$bI<c46A!pDf6UJR<ena30A[XN]A^R[d6OWZp;pc
@kN@q5$d/f9k9@cQ5<:W>=KIt=QNO1+41@tTA=SNpG1<g25RXA_"65(1!nmk=q0f4;m1pHEK
YiV-S`q]AYuIIJ^\%7o2)D=?(BS?&ETV2i`p`fTi-=ll+CF[s`%?:+U;k%(hFFC"&Pp;9EN-a
<cNV^uR#_ZbI.-eu&(`DQX7p</1PG@e**+T0@Z1?sqn25&fB9qu\ZoBDo:jL#m6GgVK2>7Q!
D^5C$I-g6QC^["MWU-dF<SF@oKJ#`!Zo4u+c@2WBRm(.Xnp/8h^S*G<c=DjV$ReD3i\M2[!V
e34R-S7u/-Vff/Lf2q<2']AHO`14I0.[*Y5ZEG9JPJ?:c:ZA#7t5Z7>9Nd^[J#\0:Q]AARE:iP
%VPN*aQVYOnrmqs[=N4_=gIX^j,\oZ6ojSFmCFD&u^W&_cO(;OGTM>hdNVA2'"#NTgjE[B%A
pm*^?t>tO__#<h/%6[b=i[9KMb)jF,?((386*^'')g06cII]A/fe^\p+jf%O*N\?FKt)7K&;h
^C<GH<,>kC*@YQp4JsqVfdk952Y#Yp!n%DV,fZ0Y(*?i.Q)aXZSJ.)e0m(!^V%2k0/*S5"K<
]AjMV6*J1oF!0K@&!AbLShqWF,A]Abq6c*N5E+h#MVeu.=F57Xm6boQJ$\uhn`'h1T>7)nbSM/
noR\7mMZ2Nb\7Z)Y(bHB*j;5RB)sV+U#u6&*R%E9@bhDVl2BZMH&&JTY!`J(MKhT*YugH^^A
@KWXWp(rV%$JRIYLjZij_UO1Q3nQ^YC128+#V3]AT?^6*MVD#b>QE-p=W7N`(8+5.:>%c/JcF
QrXgs8PLGdF)Hl<B6KL,Phk05G3pcG#k>g6)#n7&lC\k7kNOeQaTnYsZYY*0/f>P[2%@W@0g
M"&t]A,DDfEYo*i8#%-<69Zs<ng5;.""4@]A@+=()MRKdui0Met?d.`i6bGdp&l>^Q,t;6R7e=
dgatT-Xk)qn3G[)^?7D2K*ED1<TDmC'qbMG-XVl);0XZlMD*G_2%"h40]A'Xd'\d2QcKACBBl
7#E?UE"&8$R[h-gB$UlaB,,c*Y@!^31/uVU&hA%>FiZV_Fuuq3*N-h*c'82KCDpl4is_A/8u
U62$kj<u7it\d"?Hr2@KsHVgO%YPJUecpcD4_G+pEBD<t_`>G,K"J`1@?UijOq_'e_,l2lt1
ZX?ehahLlPpnd/m&hIPuGDaGbHY5(b:8YpgK>O"Q@_OIV<bWu)H*XCK\3a"10fOHL.FD5c3?
l,/UFfJ9B?W3P:Ti'-4M\[Y<pM+?7:AG!!rH$Kl+VEcMc=Ra$M_QNce^^K\Hjnm5(N?8+#Ke
,t.k*i)#M>.bC%=%hHqmpq!S1MW&mgu,>U`1\-[kMZG^7HkQK,?D(GP.)A^QZ#In6iRW;@aQ
%<oAC7*Achm$'oL'rH%44kaBo69a98k3_,n!FcrO(W:i'>)6+Wdos+(i<r`+Y\'f6h+ZJXg6
%i=4fb'Mm^co)4?-39-W(&(Y-kW)4R*n(Ln#+gjjG;>44Eku!%u7r6&_X!`4MHrV@&%RIcfH
SUWhP6h&G'"$jNe:KemUb?`.XWX-QW;Mi!A@b;pXZh9Q';MStHg!`NdOUSn&1;oZU:S=EbBe
gtc<o!aClAYW@'j\e[ccM)YV*KD:^Vb+qq@OX:9Qsq"h5hER\T"S@pUb-=6cmaEs8kG&Xqsg
eK64G98'KheWkcWeY2H=?<C/jTLQKp-`B2;Q4U/Rj0c_=E1$hL1K&7TM--@TGYch(ZTkb%8I
m-5J(Y\#Rbd:I0+C"&!:FecG0s!locS5(/cjT!tAQtHLXhHO$6i$0ZdncBu`QVai+N*^L\$6
dhhlGCZbnj)D2?4QH<M$"Qe6kAs5A[#9ZB<b\!3r6Ac1=33&=aLZO[3,\MJe_c@,SLdV*]AY4
b`;:5.rQZV^/"mKgUE]A$r^4@k$-05FW-TmdIh"Lk%cAVQKE=sfhRm)_,\p?5[^U*>\A7,Z#T
Q!Yl0?4+Jn+u-5pf"pWZ[VUi[jf.h=E)LVe%u@n+"k/?5^Q:.VKPIG"A(s'%tMN`l`3HUSAG
Q8aW:%n#9>"nCiHfD_lK"<eGrm@YqbQcAe>`EE*>&"V1!%2709&H(:!DiU5Rqa@mGPONEIs$
86D/#+`u'>SK8Z_MJ2eFf=tYL0\t[@3tcUXZ?E_e1T5DW2gG#PGJD[0J%&-1s&E4q+4lnkQ^
9:7;+kdX]AD]Amj,p)\jg0S$/Si5).SgFPuJtS\+[;p/-V4JOAAJ#A!GA`M)AK;e:,Ai/VDJsD
VE_0Q(b2tKL!C)Ic.%K<fDt,VdKP#`+bcd$^-=P[.369rr-6m?B_%?TBqk;T!UBa.oT>t9'E
N_aZ5MlERgoNaEo>T`S?dIJ\5E:bAC/*_B'Xt^a4/eM.!H6;0L27!+.1-7'<fuLbMoGB8;`<
W9%L-OST!jXJ-2Pr"g;Z$81[[jHJ]AAA8DdFE+Ij-[KVb`3,IZ&4g#uA;r/TB4FUq7U!&^Lq@
3F%UhrJ;^#R2@$K,;k"=Tn/<=mGK'>f`p;hpVl=u(T=I@bLhkUfDIB/!m5PtaRZ6X%Pc)QBF
4kr+I#>>D!qF]Ab/;i6R:m=5l'4F)6=ASSB]A89?AlgQEDJ(K3fmbjjN&hBpITbjq\rE*`Ed09
4M%@0upM@jIcH(L4Lak.F]A:EK):4Ll\m?PC]A$,X,(p`!=hY+pWTC`2Q`:m0#S+VUK[7V-sG0
4"q?5^1.s*cQ.?HL6\p>5+koJ!"lZBt'_OnK6BTrRE!UFh1!7g\5Z2f_=%7JZ&[B<,P"2=C0
5Hm"FZKe@6:4H&tnXT1OX"mR+M@_=R"diQ'8)k3(`[&H*='dpcW!rjn_HB'0Bd3QHHDHReEN
%;;HD$h^*4PlloB:X$e_:3L.W($K=OMX!s)`P5r#IFEI`0>>mm2.duHE'=+.USu.u0gu-//@
)GFn2Hebb`Q-(iqMLK,AXu86c.^d4Mh1\<_mXT@/e1of?C&l4)N(8):f=+]A=S&cMWDZ^n9o+
[8OR*"7blN^G<]Ag!UEsXZfYPh+/E6^/Y@d]A-VT^I8"$,Y(s(5D;B+T`mkej?T*5Y3W/#*0M3
m"iK-\q1rJWE!mP!c2@aWb!;e1gl<qD4b_j:=781/`>mO0c!Z7I#\PRY>_Ps/qmbXn2sjrp1
J->!sP4\B]Ag^lMghWBhB5mfb"SY<lW2:55Q=rLVpqCbo&EX3Y]A]A`l-'mS9YYegDkNej%qRSK
Zl?EK7.9a$:MuB9r;!A42l`4pcK=tZh6PcLVZ6d.L(Sf(UkccKm=,%B]A5=Tbp-=F'Qtc%SG8
7ro9I.@iM!sBa*.NcC6WUB;&&h=W)K$Q\j*h<ko)U?M/OdPf=RGhT+_mZE#V2uA]A0N8*:(KC
HUCTtg:Z[859usXZ8OWCbjhKT,Y<a4bbiaW*f+C#[Fmh<]A%<=j%-bK,Sr;uZ';["B`7=E\L7
bNP1mC?]Agn.B`S?e&)7?Zo/ATHm<Y?\p9%@+D&T:Nkq:p_C[+Y4#d@r[!]A]AjmC*=5C#u3D7+
H$+2j;Mas>=gY.YD0X=$R>3b:Vu4sJ[9`b(s"L?1g*hgbLkC@&,D7n;9oUVaEM[`4@oHQb@t
d3M1YRp9ki"&Xdj5-dZ;H=oi]AnfI416nQYh*?=CnmLqPW?>Mmms-hm>o'aFc>^nmEb@kb5IS
K)(_TA3TH`!7f#mRDj"--<Jf6X6X.5p<%L]APE.?uVu#d?iF$YX6l@*4l08'G\9WRti5PC.n(
[p@ZG]AT($IHk?7"(:R/[RAVmWEfIDAn6*9!"fk5E3UFB,#*!:.EpiuP33u-r*=?UYbB(<jZ=
#WrJU9f,o12P._j1SY7r"-X1Xt-[cLh"s65]A/g^A6SQS_8MZM9DE#%/ImUL"#l0!'i\M^\+H
`2qoYDUiLnlDj($/4>?@c18N@4NG<Rs)<l21hpL(6K=,U"Ab%Ilqj;u]A&r;HTkagZ0mWqrrV
$@?=e*Ui1Kh>pQ4lf7*MNLs`0cO#^+'77Nr%`k;e:XsXakoNCNkq0kNrc0/,/81Y</KV%u`4
8GDW-EFb!Bhm5Z3SQFm$'SGO?>W<Q1Z!hqN60a8Hd%R3kK0--^)RB2rXWq=fMAOC#9cW#j&-
8N\Gm/7"LM+$9Q7Imk,sE*3ZT4&p>69b`)8jFiWQ4b!,3WP&g9"P2Y?NSP>-X+`]Ah='4XaOn
r(GKP9A@*i(25]AfS7-F<%"<FGuc"&!uZEOoqU*FM_;$)fXr&^X9Nl,rEf>DDlMoUH(5YDJMR
KA*APs3i-;lnZ!jGf+`sAN--(bQED=Pe5K^%l#uB>LqgegTgf.WCpW;[Oe2L@JZA>$C^acM3
S3qi[Zg:FKRT-!WN4+CDESWZu#9rkUGkY,%*So8S>":K]AhoT.@/f?j*.hJW<J=,$%CTJU<e^
Gh_iW7TlAY^nAI_ElG$BIMZ:)nMhrA'7Y\;157*4`cQ(\WN[>o*eQGphmENlPRS(K=*?n*l:
ARt93lTW#d/&a]AdCeG>Z<4).3)KN_03o"3-6o+P2"KVPLEGSGpJd]AY?]A#<tjZ9DAR7'')3V`
;m9(2Rd,5M!+*3;29M,C%VAs73[4ZrEI.t9&F0M>.V4tpjC#`hV-02#\?^!2^Ljk/hB]AIn.J
#[1J%]A$XfOq?k2?;MHhcf,>?buecZFU7?,04h;5MGJRi#6'.^K&)7iNfpls4E*hSWl\@r@-@
Uk,a[='&,gl[E2hCk="=&S6N)&J,+,J+:J_;*I;Td:OuODV_1t5^RkS$7/>iqB$ePWP9[("\
.St""?FemtnK1if+02[:KsI4IVD42<kee&fPZJp5&.h>IT9(_B8@UG4k^s)er6d%]AW`2"7#:
q/XU/s\g30[_)uQTFM)!RZ.>5,Q35lERu+7f7ApM<4.m^M4(&hh`1oqA5?42OQso?^IA=*&^
QiK<g@o4S=*V><]A%af7YJr@LF2/k>+1eU3)e?_;>jou_98:6Mi+E,\HOHbY`;KG+`_FL?.4+
Se0=ji^.hJFG66ZY6CN)TB!?J:m<B3H]A!E($'QE0PT?gguVU:J:FoZbk*`-(XReo*clTf[g,
?Uko#(q^B@&/3YpCKHJuo+]A6o>7/=4d6Z[["@Auc7u4h7;6+M[*Bf^dUbG[3GEfBEfT>(:f0
O%r$Z/G.:,0IGFNk$UUF^UZjDaK$WLG[h<W:?/'"kZ[oU6Km.^.93c6`qC_fc7&n'u0JXTYs
&(na:u&0_Lhs1)f6"nQj9Mk"J@0S<g&&i#r#0mcgVHGe:4XKE\e[&0Ylm$0b/8[hGA"VBB8Y
nE_$+r&h',RsTQ6Agb8J4N7_L@,0#:"?i<Yk]A;f)m=>lkPmKO1rPE,Wa[U,_lFKU/*R^"2Fm
=sr&ha\J?4O4Y7kS_<CXPun#PJnM1f%Q1RjTf"V\CO!d"%(3<k;,?^eHo9\3=BrC4Z+Wsj+6
]A(C>/e99Np=<)Fml'oH#Ap52+_CYN9,#FQ%W51`pOP_@,&'+7ZZnW1A_]A>BP_S:1o-%kMX3]A
a&.p`>>_-fZ'i.LR$-Ko("i"jNt2\QU&Ub4.PG:XrQ?8YKTn6_od8i/I=uBh$<NouYS90tc3
!jgG7]AmYc),<ibGrT<&iP+16<kA:Y@^k\J]A<p]AD@F!2]A(Vs84sl]A,_\1`;>6$mDZS7VO$YYS
rRo0m[.pMU@'1D4a.;_&psiX6(&j--9c?cfc/CO_E_pf@bY^*4mPX*$u%NdX901`hAXpc=df
a^45!9[hN6CDD$63P2$:G'F#@;UbX>1Z%Ip2jM7`T;ghibDCmKQ=?iQk5&MG17iCi1+USAb0
U[5u'&;2see2r3(Va#^-!ms,Rd/!h:[8VuW_J+E?p"0;ql49;egFI$4N7*U@Qps8$e]AD/gf+
[GrRBM"6rld.!m!Oa4F^#k\iD#Idj8uP[j$HN*>4'/PCnZclW!?d9.p#eA.`7Tbf>ccIajOL
's(VLn#)uSlW@V8_1pWW*rL:b!nB73O,#1L>7j#s\=[$)o^;shQ4Vo]AV3Zg]A/+M7=0Z8_W;5
p5T.fUV2^q\)3'jEqt.-QC<'d<m8i[u66"QseEb-"*.#qXf@Pmb3YOf'q!M&h2Xh]As,,Xk7Y
F5E1%ceJH=[d$#g>$/W@9n-sO:.44o#9(&tUDMcT8qch(;J:tn4m0[,+=Hs+E&h+O(-<*</'
EmrU=k;M%$01:H[J>iRUri79=eTL#4.9bfrWtY&QC/15(WnZ8Y:V.6m1ZD++[)p_HH$M'L?I
6>]AWX_1IVbMIU/c_Hr25ssUWWfYV$H\#8JE:]A!.k-[>!jCHk]A6[.+AY:7t08'J+_)@p,'^`#
`a^^9EU41pVkE-'dgV+K-!nb%"ItP-dPOM;cB=<oCG;4T]AWLS%[1*U[\7'W=h?]A82Ns-]A!oF
oQKP0.GUAG&-h&(PG)iP?hMO_5e+gVs]A-/*>J%9AbVg&*r,P,YM6hK[cXS4U>a-nC7+;48$[
pSYatHYofjerA,Un<T<^_9ZU<eMN,RP5`HgqC$r)fU'`MAuj(G%UrrHHBCEkX;2Sb$GnrVT[
ZtB@fHIa;Z>W@kG8jq>,[5%a+9V(PlN]A4Lc]A)L[=ICt;=4#oXi\6mIZSsc<=_a$&'Oo1bGnq
Mh`h`KAkCJXj@YIDePaqAD''`]AlYSoSP4q/J,2rn+9LB<^76TX:dO,E-2]A!:G2#oAT5E:#WD
PRm3W*'/:SK^P39a[9@K;SgF$HI>5O"h4R"W)1Man4TOCNDBTl^U0V2lkFJ]AB;jB_/p;B4io
e>;'P4e'cEX:MrknL@1fC]A)+4)_Jg@@t%<FaLWIr.g,+AjI9m[nauW1do<LLa;q+;Y%l/72?
)YA5'5%E^1fd\qZ:Uh]AFqu/h`LJQQ,7X@:J,()5V=EUhJM;UEj=NNgg.CoC,U#7B=bY*SO!m
-O[@o+S64bq%&)eR6g''&!d$t#`f13Y@\p]AqXD>3+*t3rm!iW?6+&1<RLb-[EjZel]AX9Y73X
PZuAj);O@jLKCG@bBQ8N4,`%(g>U#.k9$1m(\&p@Sdl^,eIo3np5%SK:7P-`?;Jm]A"D=aU.u
p_.T`5-?u$Do'TPC,Y:*M[i*I:WQkm1RGACYr7+b:-8q7goUAF7T`%oUm+*>PY,67+6;:YF4
?"#![Aa(3l5^UbGM')hM.S!2,lA5JG'l*RV^QN8/1<[-G#=7me+Y=!*M![DhO:!^U*6"_b^K
RVLO0dQ6[X$RUpt[DOu/DoILbW8_2Kko=S1qZC<=lPc5.MBl59D:H!<J_A,ViECAX_?EiZ\6
aAC[Vn(5qj\B!M\.\u=??QnrLpdt%dCFRJIet/q3^Rmhgd5Y=`UcJhR`\R;Blh]A+cmdRcD/Z
770N'muWa,e&dA%FWbB_r)]AI!!CAI2K([Qg;(D^AIe0.YEnpgt/22=5iHsrnXbgCU*E-6Z%P
UZKBk@6qW><,,:5i%-t1Ibbg^_0oBf7d@-(0-rRiqS^@ukle2R#:P[KZU1JM^fHN4n%kt.=1
lQTEQe*Kg^YG^TP71D)$"u21:h+'JXhc_H=<H*_'8I:6RV1Ih;cE9*G5J!TM!*W?FO0(""BL
a%aCZip<;[N;HRh^C_)p>M.f(h[PXNRoMYc;.c.R'IhFu<tL2m:9eU=R^RNWIb_0D(Tr'uuq
_s8TYqqDqDr:.RE=r4?TV>Ai/XcaRS,FnK)\g)%ii13<o1FUEsSFs7h\;qGF53i5cY,`NRUm
CR>N(`fr*SR5%=NmjM1!=Sg>6><?XXlidZQXG,(!'<0;PYT"?`t!,LFcRB%JtIb%i8M9Ei;F
u2pY*/X/%*PhPYdm7;G0srFC:3m5%ViTk`T\e)+Y3pg'"$BXo@>'o.!Xol;@FIki)L)W"SP6
5m(,L2?H(!?2\`+fAIij2G'M;1&Wu3g<Je1W<J]A)<KShF>Td@=_D#D.P6opI<)A:Rie2rcC>
X9fV#%_.%9O62P!=>8.i/\&aNA2*Q]AI8rGuGMap]AK#1!;,]A_pWF7.`V',?O7#24-oMZS2Ndb
s!HZmX4q,XVa3MY"XX,H4F\@uC'##9*A.+F;83DB6C)LhH6u-l3C6,'b(uH[MnR%4g#8n"gY
fK3hPKb\X`Ii9XPe1gZ99n*@`[/\G_d&p:_[DRO,ujn!$UHJln.;jo1B4!!*W_?@$M5ThKJ&
@i>K"$Fol]A*F)ia:JS6lBELk/&.!F3Aj3kCQ`?`Cu7f2U*:\J)N`?bm\Js.UrooUcZ(Y4<Q;
TY!o9esMT4g?9.dQ4r[<t&QMR3*"#lSDFoU!fup9e<F<+&IRF7/>rum^"HlCe#-l772IW86=
a9;<L=iAtWbsMqh?;'0]A4"?not8CEqoMV_U\Sk86Eg2&8cLEK-u<E>Gns%NE<JH%(6R%>]A><
UeQS-i,N2*A=q7/.=;5Y,B7>gnbh596:p)>n\TA8=tO3n1D4ne_"X3+$#6i]AGc]A1_hc,e>H;
OdO6$]A^_$.-h+&8HCS=\+M1>T.?jjd?.p\]AegXQ_/:q>>7e@?V)(757_MbgKTlIa:@W4N-Cc
$J%@ciRli^alkS9Gr,;2cc'IJK[5A]A*)37SLa^Xgi3tB.^E7.qlA%BM,Mo+35Rf;5*`DB]AdR
@,P]A2.9L\lS=-k\npDYM;qDr%:QU-QEubgeO4C3bF!&^;ZL>9qQ<eH^T4^4&JJtnB8=->7+9
uS>XpHWb-PiVnG%,LGDeG]A#KGkDi%&fRXK3873?a\/os"*?c$hfV[09rGbI&I!iMc)P4jrl0
5MuC/.K.X@=5]ASY@\k`!5_o8.9VKe;<^<EMAIL>\F:RYoM([_ZAn@i3fO(3hihqaO0T_?*
044emj?;0TTC,"!$.F6&LP4Nl+kW$EDfP:E8JkZ<<qB:X]A*XA.F$YkVDh%f/t/(fE"(OX&Cd
,f;),4*oXL&0MYUYAL&DRGKr&j##a>i!97&Q8aK1j/-kP*X;(((W4W>A*9-o?`M\'L<HAd21
W@IG:<g1qa2&/>nZ#lO_AV(_Ym"Q@R(bTfqr#lIT;t%Aqr$_Q+QVZHmr>r%CtKZG@b$ZOngi
5g]A&gm,G^5Tb"!MX]AqaLn^95d,>"Q]A;We^Xhk@.TA"kGB_G'*`u4P&1\mS+JWc9d#cdH+k78
;d/cSc*bkLcnmb&9B5&5#)hSe(-@5QUQ7(g[7:1&O%TB%/;rVC9U6*rno&BWb]Ahs!n(3O*b5
XN23]A;p"1]AQ;k:$lSS%OebsGm_]A"TZ&4eW1Kp@J+B,,g/2<GQ4$$8L&g=Zia-R!#bIb6h5oR
Skd9fF93WL6A0U7XSVFubH"DPR32Yk,K>Eh!F\',s2FPU6PA"P(6\K7jk`'=_;C$%Y%EQr'F
YddNma[^V.?S0!^efeO/,m6<[r`_a:ou&Y]ArAN$hH.(QGYak!NFl.!\SaC(e>["]AN^"9H2$>
^rMN'5(c]A6a^/D6HGGPgpan&G#7p(m4&+ie%]A==hOLVaBOrr\p<.%TE0[p?&6^58sjKV9"1R
AJV:F0_FA6(^T@:mMNYHES$K4,MW@j7m)&("jf$I*k6-9#M2C8f!l204m/25eW9!On\Il_85
kJPq<l+bJ@F_Gr+0HG2=8ttp8&R'1sHpHp/@M,,u!PB&_WLKM9,,VPF^t`qeui"e2$g64Ijl
jj3p-CSsS':0aB>h%oL(`KIqf@l-tab`;m$^10j4s5'p;*BO*h/-$:)Wd">BkC#5qKN'0eOG
2+1&m`oq=]A;$J&=e6YN'eii>ZU&<>3BS^i&=t?24OAK2KZAZ6(&#tppeu*Oo4-3E;se-A&ic
$TV\W"$U/M]Al5':kOAu[TCo2KRiR^\&P)F-0fWh77O%pFu.*2[dos--I!HqAK5c*`8cX]AG<0
"PH@j7eS&HGJHE>YE9kZW?(Hq/PcN*Sh"4XJ3_`P)NG..\igD[>9D6XpQdV'7cZKKL65_SB(
<7Wbm."Gk_Im-#@\Ug!4HBR>28G(brUGX+t:,&1<G\9"kSHu;WhZIhP"m6\.aX'Sha^AWRWl
Z?Pi*]A5K9eO9]A>^AjUo5ZG4R/Q#Zoo3!9XHELtGfj@GB`o&-u7f+(@"sTuNSSkFnOGrtWR6+
&Q-k88LEW8+mU9jds*Vn]ANMtlu^@*[[2EDD/YRJM9_i*8?GZm&eithb;&U-nm4HEA68J$5pe
sn2CnXc&*,3\o1-`]Am%Y7FgW?r;2DG1j)tCa-7=f$#9Bn>S_8LV/I&PjP!@\bW`!U"D-i+^V
<7pm8ebK<tap:)=U+t[<EMAIL>?3CMVD_AFAGn'_g]Al#ekj2BTbO0+F95f/AZahrL
?kWh\2W*jgd^"*S`SC1^f;s![/R5-u^@*5AGW>ra4cJh<mAbnV4!K&GtIq/H=CSkj.cG51.#
b-A5!ekjq/-Ahf4:I/PU>gqWa$.i%XTh_hE:rUU<26W"dBgX@h>\k9^Y&hYI`q5.Zf2\QWEj
e5EmE7;4DA$Dct02YOP>s6ond:]A'2^Y5;1Gk.[N7Tf<&gi?.>mD]A$2+/B+A1uu*(a,+dVmre
d1kqG)uH?J7Pd>i-?)"X4+`+;8UWgO,X;;NJ04dp]Atp)nJ]AK/h35%rH?rQedC-YhsL>0r^"f
(47<")<+>cndsuKmpI21hIj?iHSI1Mj.X^\Jp0"b_.cY:fOci3)]AHre8#X8Ns0HjM`QsCX4(
4i*W!olB""5>fE7Gu;2\9[7CT>DjG#84'k6oLs51,gWf+(n;XD,]A_8Ap><EuC=JBc[2U'8-)
Z\mqJL2^[:^XIOb/Y0$P;bS)J8s7,FpkQt9;"GEA3V#><Hn^0]Ak(/_bJn,T[rbkBP).^o:)O
OjZgh>TNLTds!X9dalcN,i-8AAkVbd)p3bQeT*[rg/gfPJ;[_luNG=CGShQh9)R/Z>*R:d?3
0M&/r>/C(;4^3V-&,8kLYZb?_2pP8g*rWJB"f+o2i]A@;kW%mG5.L*R7K0(c?7&.G*WA,`a12
b/29Z\l$!5p5drCG!Z[e,$3XFJUOsfSQ7FoeGQb(FB+j.5Ff-[d9c46gk2*HfCIntUN!9!n`
iXk@Kb?uY,Op7`<MCmC2D7jIM'7AG(!!9DMQ['mqX)m.X)g/Wmo>=,*SPt-O/4o65+LLWYa*
tG]A:4<Rc#Ybabd/Jj@Q$c\S.3Fl-HF"rS=T&K>mOXDC/9)/`e#>8$$1@<6:To1-?`rZ`&hEi
VLHJ`GKTc84_B,A.,`bC_pabNu(ih1Wh\)d8R5h$^e#<K@dW9p24MsA=5aJ3onCnZFc11Io]A
C\MQcWS]A2LgkWU<mICZKEnK]A9PQ>']A%Rm(JVKPbCoP1KL]A*jj1ZCm[0794VAA?fHd4@dEcFp
DRPVM:X#\,^\WZ+Mpqa(i%VRNh-ZH]AC=IIAGC2_fJI<bT5l]AEpFeCP9>)`R&,]A*3CWn.(=O7
CSt>-(:_`-]A=CJi3dGIPbfA\'7dNHej>>Zhu<I"<68r*;ct4&l-en@)97DXVOF(!;H8Rp-D6
2-gp7*'0osY52um3\EB^abXoqfH;Q]AlX>os;l*;<9\H8\1(XYm9:hG?D*J\I@[bdCTf*/G[M
1u<A<u$D(r>kM(-[33_m2jPc:Sf`%D%p`V;>6^5+e$l&KDK[&pX5o#5G)6t=K&6Ocqcl2"<a
4:VL#i%\sS3s7*2/6@j"_JV65CmoNa$Y%0qTBFb69b9@rYVS(nk(<=>PVa/XRfD,?n#MXVjm
4o)3J]Ar\(U0(1K;s!'iKY\9WYp\>?W^o&DKK]AD5"RB@h7:hRTFi-gYa%H-l&LX=G?aogUjq!
KglF#=<1XLh!(L&SDgAJU`TabIa-O[=TM(+$]Ao[&fVc*/b'KMJJPkNG9)uKX/gg^70n=QCB=
\HS6MJ_4A")41p2#Z3X.ZlG&:qKAYQG`\diAFUd)ob=>cWK3=<?D9_qThL=hM`C;>Z$jHX:7
pk+@@KsHpe.dcQ:dZm=@@jo.C+hb'M'T^9en>&q/YD<hfoKnMO4XE#Y4NJGok!D1Goc1;&T<
'"F)<8L\)B-F;7TV`^+@$+p_ZU_iTd&C_>DMrF70SH3_ZAq^9c2]ArO$W!e*.oTY<E-dZui1O
^)-cN@_I@-jg2V,FGG)_B-4.bN9el,db<3:5_n<lI-lm0_28)h*)74AA8.'UNdboim%*"s6D
Gqf#Rpnf+taRILj.+bM9(?U26QreWZ.8cVl2g2m]ARd11jeu(WR.ce067<P6@4U',0#6:SY?o
gHI3s-&%>H/BKo+_s0iR\oss`R%,3b1&oa,W*h_6@L)C6dma:_f#baR;PVL<t%Q5)-_>Hp/5
A55L\\Or=,R,f6^[fD1A84d=?M/&`,Kc5)\1i]Ae.-"LWYip6%['*OQL94dO`Nnt6?jE#fJUY
QQbeCH:APHT%3\O\]A(NW\`k)U@ji,M%5aM1f2psJqY>KMRc<$#V>NZ(=.D>"=WU`c5KDNC.;
EZ,*S=d#N0eA@q(;t:f7MuGl-eo33]A]AZ6(A@YZ;a']A=;c.<0/a`/imAgjW4E'7Js,$&f1')g
2YcdCg,qPZ*<hJ,kn$fgIJk:0_q4GT93QIh'@\DLMeo`@]AP^-2.!DC6s5n!TMsSA?`*J*C*6
*;8Qm?:H,CT+([5P]AWS<)C2nct/Kk#_2cR`*`_SGGQ\0SlXgOJ8"18&f'bq..Ns<#A26N'Ag
;'bR3CRB*fM<DNW4<rqLp1[GhX^g*6OS<_&S51d6Yq-?d*ia(8E(a=#/%Af$?f#.<c7-tGQ:
k!(sqrJAJEE#E>Zb)*jKVN3"bY8Cp?%F#Vj.X4\^3N0.piHjNf?;=tUAceogt@INV$>k4.<(
F^M*!B^Zrk.%7r!&?Qi7f_bJ?dnTW:H2?>X.Q1VL5f?N57Ck"8H3P:e`Z.UY.VEcHo@8RfNi
apP7#n!T)16P'8"J;!NpbM<EW8D]A%b2Y,;<,\/!WrppiU/$\"arUHn9blLPO]A);1e]A;'KP2o
q9Q3E)=L'qJ(;NGPL8&sQX'Q/9%+s'jF;mCBgW[F7;$c'/]AA#,DDf=AX]A/Hm<F_d5Z'8W%u9
&@H*kn*Vi+&^kV"jHgu=Q]A==)8@`K:\MjL#`&lr4Y:L#F%SX@N;P.C52E:MJWEhiKEIM,dm;
HgM)"Xg7-F/hTr<h1iCh<a]A<1)lD=D(e-,himLVeX[i''TiJumGIBX2_lBaktNQEURcVuF$b
WP5ZQ@:?ckKL7#)Q/.+cQE0W]AN(cDk\?9V5Vd;^AbjSBPZMZLB1B7!8T90tqK?7Rt;>M#GWM
nXMC-7LRB?ZX;Bo7Ab6><,oV!-_gVnqj&V.o>%E\e?uY3tMH=`p,7]Adm^f.6`>4?AK\%\YaD
OajO6=bAqPLkMOtDi1A.r:3;me/(F196>B)&,W3Z=U=faL<qG^8Z'6'7/6eBT@H;GOR"k_h8
P'^u4_Oq&TXOUnrq_6O-#ErU;.:C*n4%4#;[`95ZrY[h`>e%22gQI#6qgl<dJ(9k8OQOW?`I
6rgpC7T,Id6]A+Tag=hc,?nD+'2]Ab>Eq!YkGdQ))e&ugKC)B2CpKFd('LYSQ#0m+";*7jSRI\
8B7nt6G0ljB'EY"%D_Km'sICDlbQJ?ZC8j:1Y6`ql7qc)IsLui.1Nf-iLj(.1X*e`e_`uAT)
/+\E4m1EfZCIMg#]AhLa&I!gEAj?'V:-?HN+]AS1\L$(R9=r#P-WT*M,*U.d@hu?(s)n_%(5NS
#+U21"7;:]A$-*m9s+0tm>b!cf&f/fU.fLF*o]A1O.D7eU2cFm5Isf*M9RQ]A#3_Jie=h<.Mk;N
^T)^RZ^C8RKG)EUd&\b8:Ln%-)-n8QWm$]AiLFA-'CYlij![k'Ggb'8'(*m"Esj+dkk$,q*^G
PU_e26gepfoS,d"Wbckq6T^aM<,&"u5n&E+7_^>[)_Pe$um8ksW^Ppi/&(tJ!9"4SK4+@Z<s
fXa.oA#H_6&l<TM&L0'')@a/NB's:_`,C!9k#Y:gfm/!:Xi@/HCT&f!#mPn;mHiu*ITg_PIL
mA&m_$b>d6^gX/R-IGo8\0BkZnN8mqJWuMNh0.Y[;N;"K56bhbct8TT,#C0^Mm]A7bQ$\44ep
]Aa5q!QMX!>C;>R)-U3iiAE_7M`\"GDVkt/AdEU)U82<.E)rY)<EMAIL>@pG.pZ,hr?O%5
"3d[LLbD#cPHCWH0/naHS,i@&d:Ab"'4.imW!<kY,S1hnQl[<>!<;-hDiT0mRoQh)8EnX'R>
;<1A/aQpVM:fpY[qsq;9I)5aG9Cos8dVA+JI3CNV7sB-Y&9Pk[d>6aKf5k]A+b"jWE9]A&s?T>
M%[5h;F:UI,)YBdSB0uP/f$Oa)5^88@@u^h6FYHr/RVpq%2R+i>)&'7]A%fq%#!Z%f\`.<EHV
T]AT.:C`3ITgTk23`F[qT/$,3a:EfK37$jNgQXse#0c*S2tL9DHb/BF8G^u&^5l+b%*jRaf)H
K@OGd2;GUDK*@@E=`Y@h)c:l=Qk*!T`>IOS@eO+Mq0\E(JP3uJ@5Q!:[j6ne7=aFSYJ+@VXD
LW34'8uVTnSq.&Nho+<d<Ep7Mq-"c?tSq?:j]AUNl4qB[OQ6fhi3tbtC4."l5mced^A)oP2Fe
'uMe^WfB=Tbf=%PdS?,-qg#E#Tf5M"6u?DZIDMtGiqo(j:#JlC!j8@pG-#%nBslV3P((!'ZB
d+iH?>Z1/+OE]A(7r.lIjBRs+YjQPM"b4SgmXhb3d.phcc94XoQ`G7[u?j`T3W4b]A\k(B7$MV
ST8T1jhR.*?hN+aE0'I(p=\n%:j\<#EuG,*mU[Lj=d,*'q+0M53pV5Sbb?&5fk5i:Zp,:)Vj
I10J$C!uF?,puq\VULsEV+@mgXRFj8r\n&^6bK\(A5Np+qQq"R)f(Q@u]A.OCgTSl_G;C]AiYS
mPD23Q#%97Jaki'nDr-lIhAl6=,saoX0-s<)D1l4#dOaUHdB^aE@JD6#LeP0p8jr3D&<h/71
.'"VPr2PH*]A@jl.OCINK_HWKK'JG4S293"`eCobt]AuHN4]AqjfT%`bZ:\g8:sm)/4"fX>H./u
36^P54OW)n0S5?=Qd7?]AHUenMHS;mfkUI,?1)0R_.G4JIlmHg:G8;`Eebp!?A!()")_-nE+$
Z>uP;fEfME5c;3]Ad75OG:W>@64S@W\msAZsZ\Xne1npqZ+f2'`5,t,MJ<@(@&igL\=-I\_kh
jg2mRK^"U76b/BtQR;>3R(jlZb-(W1qp7be<)D_b_=l?)ob^@>6(I4fTnAfT2+%]A=moH@h7i
1sIg8$BTi*H\2^l>J">#\@Z9M[h`pW*9>C-Q8N)HF_*LOj>i%6%gnea>.CsSKs$=6I*^*+s/
ul,h%sX[_>L3CsO\"Dm<0C_$''i$Ph0MYb%F+"40:'+4;k'\nQ*k4gpjc8`jMk@<o<=ot4%B
B0'2`"5bt$<7B]Aa:-XdYS';i:Eqg^K"U4`DN@&eZo\#.Dh]A!cFmee#YRcd)>VcE7("4u:f0t
)d#jqF:Flgb?)or4Ba$u(,P_m5!O<lBV%ZuBdtKA9p[8lih11`D=Y)1%Ymj&^_SF8MMcoXfS
TX%FAaq)RH/]AHq@rf)PQ=bR>iUX&RD">S]A&`X+4JQQjY79L6XcnR:Mj:_B"``>F`dR<la=qC
?g<gkV8\-Q-'[e@e*C=BNsB'X8Rm^E_^N>j;GlQmAs<$3E?)Yf]AK"_3Ut*Z4$OA*@5P+$AA8
KP,YBMl.F?7Tf+m6@:ph4i/fQ"J6M28.jKbQ^QAG;2'"AW'jVe,8hCR=.6VY,J<5:d1'oST4
8;.3!D&JDc@.p3]AntW$r]A_gIHQ*#CjP?m<`*>;tmP1ORq;PIZ3bABaeS*EJ.5oA1n#_GOr/r
c+"jJ4'7?<&RtoK.u(!G!ipPI.'(V']A$`'$!XrhKEWpHL-!IG+^bDB7Q6R$@\++q!]AE!#$9U
8Qmq<eXn)UN_E"LKk^gWF$+XOSqd+A&Pm@Bb9^7g$<"l?dZK$P@8>OV!Io>pdq1ulg6t5apE
k,)>HTV6.=^-W_AL)M;@OBL==M,k0nP5'07k:tJ)7jF\K*`B?eL";-mO$<oXAT;"Es_lW&P=
]AC`#RKV]A<UPhD'NTYha0=>L^`&5a_Kf,7@BNU"sV%a/uc49%Kr07.j/_T@KHhNmqjJ1QF(>j
D;N%djn.41\B".b/@bCT=F?f1M_'sTEM'opJQ6,Hn+5u$Jcc"mQQi9E10uuV$mQ=dSX-"sGj
7MCKZ.eb9PEunTT>f>!"mg%o8=C_%r#kj4H+]AF%JHY[(cSZ<02?(#5c;%/fCjLFNWc5fG`IN
"2<8!TePtk"mKGN$OeR74m1e8?6f?8@gTAM6$8*3t,!sWSNAS3Y9i56P8hRqJ"%.8mn2#!T-
dQkSZ(ktAS998.rgpqEl5mmqW`N>:24;9lIDpS\GOlC[N'MCp9^EqR6XL@[h&'!rURV'U;Q4
6d`H^>"D*X,q-l30g24+X<rZbQO<>B/mjY0[ZCd1o5bpr,N,_QqtT&<!9Sd3&DN/DBW5EV'Q
/!\sR`8e8to]AYHS(IRVl2bm<O99b=<"*S5Q0;dD3Cm."fFZN_\k42tOLmtq2V%b\CB.$a*?\
@-]AGO#[#DSbSR%3cVf%i:(7F-IBg5`'#7Im[+HM7)_^[o)_MP9Y`h7GDRY$tgl,K7qd.5hl/
(o)]AE;Ko[\]AG[g&3dH4.&H7"XU(HSIRZZXJ17bc6VD&8fscX@20RbH$TC@eUg0!/LjP$61[/
PS-N[.=Cifu/38-fm&VMra/l)Io@(r:RpnH2X?r@eop1a&>@Fla.&m[dK_X51D/J,+6623-Q
kPq=A5#S!o^D=%B2^9=o7]A.T>ojdXRUW+aps`>g$6pR*B*Z,5l1Fm[Q>SQHV#oZP=:Lj((`*
679`Gc.\5mU9I8@a_C5c7C!rO01dDJ-cs(L]A)%sKn$rb=O^*mV>6]AEd/7p+aY,dU6$69q+Eg
*8mFrPpY\4k;$;bn*1B6%\]A_P#<51XOje(fr@nhXbKR0iFo?^H;[aW]A=<>I/a!-q]A[.3>3Ar
9$,V_3;r<nA!Y@j.;iY^URiQDSU6o*^rVLBrE%>lRLP`k_2,s;Ae$[?'4lAFt<kS;sOJJU[l
XXpPYN(uh\Le;oWDE_a$c3VAQ5DhCn5FL>9-:0q%CkB[jlMb:4s;dJs4skm^\mZ!9=INu26[
\Q!j*HMCTYK?O"TmJJ%Otckq0D$rl?;"cFYkufQR$uE>D>99lffhn!KTKV<+FWL!O^B)cmSa
SRrJc?R]A+h>u9)B[rM\1-`JN$.Ya6V.F);RI4A9.\uS]A/DX0mn,.nU>l7fcp<oK8+oQt^b4J
Lsu$,9[lPNI(_]AT0M334.M/YrIK8BF6qAr_-YT[l``uQarBTJrqsT8D07rQ)b\2K<hpN1Gk&
FC#?6Qb&qj:kZ^En]A>\ZV9X86?3YY2gm(l9Yqj%%[m;6k*h+N/6mU]AOd!UoaB<nemd3q`if8
,HL'#D[<sB$^]AdVPN*;KtYV&q%0%kp>MP?K*1aiF)>,t!S7.Ln)3/<mjOTkOFCR8F]AAAqjUW
UnMtT$+F_tH8=8n]AKU!d"R(8A?RkYg)o(f6'+24Z)s3Ko8V7>JA^No+G&f>JobX?Q^O*A(Zd
\Xn=W*DtERANPba"78Tbf%*p_q]A33dRY]AieeZqR"VY)Mji:5jJq\"8PEWlB4`L`LkR(9<kPK
ML:+$gE->h5ceES('iI*09gnht\jd^/qn&@^7>hEhGu9A<r%J\LN,o0s<a,F:G@\[bd.K'M5
g$eNITcu(G*^2*7jQ1X#'.t*A(Q>fiM4?f+#T&/LUCe(UKfm5SN?rt]ASpM8m\M\<!?2lZB@T
52$%,tGi#dYB/=2(i?$1#8ZflhS+<:u,V9V7EPtQc01AK+PZL0KP:Z1:ST0_Au!LZ0"mT^\!
FI!:gWLW8@Y1cl-Qo>bh\&aJ@A\%C)/hS@Zfl\PKb@_h8gSl3r4(I<cE$7iX_C7$0O,WuuLt
Q]AIl]AHffp/`lTUg`C$%cOjD1ZbO&?j)78`7Vt:ks7pc<iV^#Ae=">rmO4k]AF2[,A"E(iF'N8
1]A):-&qEW))0.?Tkri"rjA+Y_)BD[mBtCpet3Tlral9<G3g<Mnd^492c[cKMMDZTXqCUNQs%
LBcZ$2XI&plerjqdZYp*fMp7O,Cc$UN9G6rF?3SK+MFRF^CnD+m/1`@!.b=e>HZ[o:PmVks'
H;0CM)mc50.u[XVgk@H@3/FGc<]A*8c6S;+XC-L@=%II0iCMS(HPRXM!9o<9C&Oc-j0Ga/\2^
crVI:$K'k2MKe6YO@OR_*S5;<LG7@I_RMb4oNZ8<N;I/5kpYZP=TdHS@;$'Q5)O.YQ-[^Ha9
i4frB#Q(^%V5c(n;K>7_QpA1R[J[2W<Gp=j@JL%HK9HG$<7_7sXA6b$$ZhU/OdMG/nMm\>fn
stIrll^E+K3ga\5R]AG-)%P!:V/l)j>j@4"1S8X>B>"d\s"o4nAtm9;,Ps'If\h+]AZrl(rDZO
ljN79hc*aG-/7QnS=[B0pZgM\dB_6of2ej=CW;`(<%FS59>r5^b1@;N>*mnoi+*RLs2DJ]A3%
.udc+D[RSOC?nBfu4%WCRm?-P*)BSI-7dhR\7a#FjljkV:)NG8*jJ/c<p0-8ieWsOZRohs2V
m422V]A@AF6i1*QJ6-;\j(^r^?MQkX(NI54l"Ce@B6-p6Geihs#/RS;GVd@BtPJmn1ikVuHpK
B`Rn0B!r2D2EC`^G,,*Abdi)&V+9(\qC,n/19H`a;7DK'%NcE(dV@)%6i#/h)pk2#kPKD!N3
Fqsg`s.,X;4/F6*a]AM+oe?1lMm&IV7u[>cC:4:s4?X.\'VL[Jl#!R=,mF9P.-(Q@GCiD7AK8
,[[7Sr"'L:li"_uJ#b0>"ee=Q@)20g&c2#BSh22Uj_ot.OeUJ,C"s'7:r[B_?+2UaH^IenM_
R=]A\oq7*LJaBDMbs+o^n]A-^j,-^u!nJ.1q^OJ*-mHrjLKrj,Xg5#E`AB`1YA%(T[URGOnMIU
++49S@mS$#E!CsNi>=mG\!XpcbKY<$]A4jH/C_\_o^;lKUNDG-s=Yjul62#.GsUIQX_df%h7R
F^5l$q`T6rBlUtMYOm[(Z_N4/;i=8DI5isEG)c54X".ZhR/^+_H.ZP@Bdk*+*WrZaUhd&PiE
CArCC*-YmAOP<WTPjAb6r<`1i#P9]ArE(:od)Tm<G0O%espa;3/bMU_TS9uMfoNbO,K"`]ATS>
/\MEL*?G6ns!C?ttkS3;*5,>We]AW-(@@Q^k@`\#*+2.*0b4$$Dr%(8s7kGi7i<k+PKHsr<2&
iK09Fn=-:Qr3d")?D^H'ggee<oaWgI[TAm).!G`Hf$9e+JCWc$5ulln8>BR;:sIe'8ZP)"H@
kaSYo=Th6.<hdI3q6#/_@ei>NR@"9JC>UuQMWflc]AU\)/ZOLJ(<OjL5cekH!$n7U#,5$Q.u[
U8(f##*f\X#/^u'&!8m^iElO2R0EeI?Kft3#`QhR1mre(mfN@R;d5\3k-7=_BTuqU[%_U%"m
cBTrgb3pp5Fjuf!$JS[T\c!\T7'UlgOZ:i^pc9ZLjUhPu4!b71K4(l?Pkap9ftN1cTmSPCH4
kQNo^CfdlA(XT7E.l:J@s'c*$#549u^Z2%TVF4F^$""?J?b7]A%jZY03f>f_AAQ\,JK&/EkQq
D2.&RZG*.qR+1@fXh*s7Ze3p_TSYW^uSZ:dipB=23,'sK!Jf,T>(\uSi$A`L)47%Egr.`rUs
rp`c-d]AK#eb+;1r*<$W!(j[Y!Ze%p,6)=7VJN0JG2F"S:rUC!07U20(GTKt9ruMoW1Lr^`LA
_"Zn#[c^G,r49JN5VXn@7U%TW:X!Z7MiDI]A+rr#f5'M#"eNDBC%\RC5N^G?:&OP"7`,_!@"8
W/bQ%+IE^)$p?Y8OMl+!IXOl3hoNAjM@#*mN58>8T)lqr7?ZZXSBu5-R28k(d%"l\u%(C0aK
.PF;*KZ:1pR.><dC@''m[eZ8#@*'4Jn?%GFn?(ZG]A]A5#$h%@j^,ZF=[p]AO.l!Z=pGC.b^I,P
0=eDl.FGM4FaDWZU_oN.]A;K&0KX8KmSK"fTpYOC^YMi,oJuef6$(Tc%1R7=VQK8`?i3\B&E-
m3j7`<ire=hD(BQO0Z>69p;V'INP.EUX,$G+aXm-V1TID6#(2.$U(W_VU??bnQLs$YQ=F(]At
;"82gHqpr:rQUQ7d-cBLgG9iQ1]ATK4:J*h^^Ahp2s3==A'PCDleV]AEYrRI4TOc@KJG8_^g2u
dZ$#Hm63k?*dhJeHWXnm=I/E4u&p[XL>\o@;auYBI'3T'<Y6:EB,"*-'7IF<T1+b]A5coa]A9B
Z41U[#(332%772lO4\)2ubs<nYX`E+W[ZN6iA7W*eYY%V_mS\,\`k*t%p[*%Yo:E_B+8bg0f
q.C$q*!'<[lp?r2L)m%9lD(n<(fKaRMHt5)3]AM1<4OB!JaR^^Il8h%>02WPINMfl(3J+;SsX
\Zkb]A9=D<jIc5TgW&0#*9I!BaEXiT13?<@r9ts,W7,7A*$/6V&[A"e<=tjqV`\D\j0I4ddG)
@=("YG!\I_T2#c5'3HN`s.P-opPQ6tS$MA0e#IHW<cgjIh9n(/N0r7"8rcd<)^bSREQ9GV^-
K_/'0MH<2rq]AfXhJ*V_d:gEBUcO#>h[Ji-D,lU_</hM[Kb[\<'ZAlK<M+MaPko?jJ>M9<_qJ
(n2F-(#Gu^N6kg2^0)s4CL<&&hq7Nl'.p9LdO?hhl3Bpb>(3c^q^k,@Jg>(oJ_%P8s2NpG7e
l2!DX&$4cs!NY)It>OVYp$`o9]AD&:m^;"Y]AC&gH&l0QE<n'/kc!W6^CU6JfFV4isRY[Hojj.
2N1QU'[_K,App%OQaE@UXpQaO$Z4NkiU-Km1s=Iu)&cK,Ni%(-Q#M*\<NN:8?Zj7Bf/lG.s(
C+T$hlTp7LlVHO=#"q)R[LtMu\E`/'<oBos;E=J%VRIs=\hgi]Al-`"P#8SHTIlW9[$adQ:a'
*b7GcWFiNYn^8X9_BcB7?n[a>JPs(o5["+.LGSYj:u1dP\qEK:/\=*K%JoBq@8AK`mjg-OGK
uYh?unD32Y$AqO^X^H/BgGnPR--eKVC[5J7hMaLSW5ZU)$4)_g4%NNhqEfrQ?cFe9FB2TKd5
*"-_Ahom(kL$0'OVld`O\uF<$;LmCmh3c.VCEk5+Os>MYNRiIYOrYN#1Zr*6gSBop+=<Z%/i
8C=2>n4aM<[`mYP\#`,D(/afY=W+_iqeP%W%kT_?@t'P4r2Jm`3+Ho]A+GG`#<QG'+>t8*`@C
W:O@20(OK1\`dnslj0XiWJ!'WLtA`5D-Q'g-EAmU3Xa]AjK%5"O*U-_E2*Ud.bc`.=i$8t1#U
D".i8;X7;NYs8NPr-$o9ip,;Z4N>2/2ba^<X.5jO6p(0X&##rq^urQIr=,IF-R!gD-b/Igf@
F:+NrdmMquc+r^W`O(iSd#@>UUCF:oa\514%j_cOKX1qLq'*n)`ih+H;_r5B%]A**Mqa+n9re
?e(*bM?PR="o>o4`R1!3OQ="/Yt=9f5_k@qr>4==Mrl4m9kUE_H-J+Yd\Z,QNY<jAuC5a';7
4UT5BAi:L=2sg5n*X&@tBXA$FSpQ7Pr-O+(fP*U:uW'*HRVjD$[(jZ;Nq+D/r&[f$Tuj5'L3
cuN3]A_err=ZIY_1n_W+nh(m6]AalmRVC4l]AB`*mU*@C"Wu.qou`GJj0X>a-LcA\O?[^q%L:7J
,GY)#]AdF6Cr<]ANoTu)ZY@aR'48Z3?Vcq2=C`FeVTq:li,@fNqQJ#g[[a\W1RUa?$mh1P\2l'
o)@90"WJ0!"#.>Y2SJ[Yk3%`*^PW&PTG;%kIZQ8i-6?Pl6'B3XffG1)p$216p(qfCh`7EeXS
WarRPeN4n*ap2=71'AX+\1\/H2`"&.sFRi\q_0LK'?pMP5;??">n@p\RAo55@eSXQWN4^Q^)
bX3dJ+`e3kB?<-P\@dnZu(R!56Z%Yh^/rsW\''3sfPN8Cb^IU:HmZI('K(#9+CGLrmfbtd"H
m8=7e#1J(m6$9[Y>d>VWRfH8J/DD+u@.cpAnV);PV\2X9^?]AQND!_&5V#jlT[M)-;9u?O$Yg
0)liGA?"9T1GMC@SZCI:l%$0Orm2FJ%46X^4p:^IK:-^*k8CV6lu2E&3mV:mO(N/9rEK;n[H
4AH:9MZdoMMe9\#@IFBh\FK=AG6$oc>'%3UjB`J7C,fS=#<)/itIT=0]A[rb8#l.B2,'F=Qk7
^e$adHuOk_bQVO+?taH%C)85DoqUo2YsL<3`k$)/%=G]AGRa0,F1MBT$YdJn0bdpds2553oBh
(Td&Gm=UBM;C7+YVCZ4X<Ao9HA/Y7s8nKA\\*`#03NA*%\38Gs89KE'*3$D[[RmQ/!*5m)Ek
T$>":WOhned[Rn?,;aK7P#EBT1\BCF,FSO,DkB`,7C8kTqEM/QDM36!4>9)10o;:pBX?S$Y6
6JV8m*]ApQ>uL%0-Nj3ditQD<YLa.L`fAZQnBtnHkLngoT"<1d!]A[`9!=AA.&U+d]AQ[.7'q]A7
<'OE3shl>b[8db1dJQPr<6q1<#Y&@h&`Sbh#g14c+KhKQ>#uT1BI_(!s\[PtkD0<?<\L/ocq
53Zn',)$_fgssf:`%*Ah]AjEn[CSHTV9tpf;.-R>]A@B>qe!DnMXP9q,]A]AtFV]AWQ5")gj,[G)%
O"j\^62;%<ka$jr`?6R*Lp[Xrq;#aJM3:*^ggSTft6(hLW)XZi9EH-'(bgS^48cu\CDm65i8
Eb7ps\UWhbo.%2W[[;+(4ag<S;_Z&+f$A0A^SISmHG#j7<9hl@.SA7,>PMh/?P>q_Sk==EFO
ioOLWBa7RFerN8RHV-"0;5GA2:u%U)kdi]A)oS#ErOV^hDs`j]A=uVuY+B8>^MOD9[k#la>/57
m+/TrUeJ'X@e%9Aefd<2-OmM@kX"E\BicV`GcJi_EEq'Wmj44$o%\\,1.<0:q+r?11.m?pB]A
?@r[Ng^F>4TnKT24APa/mf2LP'r4u44J*__3q;erOdG!^q!"Fhm2\X:lF8qXP2N,_ba1&Agh
FYn6V&1]A_FK!CIlkJBEK^skX%4NM?i72!>s"<XC"1BZK*2V_Y_f1rU))-3aX60N8Cd"ET3hE
kCTP1Mdto&h9G6lEM!Ao3\lZ\R,b614P.'\e@5eEl516;ofHKGMt=F-HM;D,:'p1BJ'.5-V-
boP4p@B(jF;/V-2=*\Q$(_#;.!<SQb?XmqI2nA,W1j0s-eM[#AK`nm`a?HdQ\ul)-P)E\i1n
%Vk;PC+CT;=_hoR0$MiKh)#qf]AF,*nVrF_P^q=rjl7-P22,!p"e16HJS36cK*!</NumIS\T[
P,<#I@AK!]AW;h'Lt2TeH9\(_TY"WVgtER?khDpf;@r"'HpB^,lGN![\W&Cb<XZ13qY[YO'KK
Eo0KU<GkiL!S@q!U<4HkR`%i.M,fO[Jp2E$Vkr:Gi7oRO)Ik.^(h7SQ`kRCRs9V\[75ZC2ha
Ugkq`LHpi-?Y)+i^b.KCI5.:$CRL"j9NQSQcn8U6f?rc[GSmF66JCNBG8RC4K*$%b?6/.o>2
!1?I9m*AK$D6(Pl!,-%B@E6Y904K;KNht:q;oG6W[fpI(N=<EMAIL>-0Vbh&Z),)cs'p
I^"#g$J+lugqgn1U#N?-H(+1TbAY+*DL:V8*)(#847Js%$@I)76'qf<p"Y-ImKm5#oqK:eY!
D8m$5@M?aEj9iil@$U+#QPY_M758@O;#D($ikgObi8Q;oWW4QD?->&>;G,$IDGAu(=?&;fYm
r6SL^6Ab"Dd#d@r?gILsa>E(KUY\=IS(#qUWRYQU9-&m,3.?jSB8-*RN<^_0`N9Ak:@/@?\b
FgVD3N1Hp73+Qq"'>a</beF/~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="144"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="159" width="375" height="144"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA1_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[381000,1143000,199505,1333500,199505,381000,952500,952500,952500,952500,1143000,381000,199505,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,762000,762000,381000,1752600,1181100,571500,3810000,1524000,1181100,1181100,190500,190500,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=MAX(I4)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$sxtype = 3]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 3]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 2]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="11" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="2" s="4">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" cs="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($sxtype = 1,"分公司","营业部")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" cs="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($sxtype = 1,"当前指标考核得分","当前指标实际值")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=Details()>" + $$$ + "</div>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" cs="3" s="4">
<O>
<![CDATA[操作]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="11" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="0" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__413A4477FA5DC879A7062C04A560B7BD">
<IM>
<![CDATA[!>5b,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$/%m<5u`*!m@.sO>ZDZ^ca
GpuSCsI$h(A.#Ta;fW;1gSq6pi\[@*oMs:lA5@(srK*01KI$-JEQYU*!3I-u'$]AgF^#nF7-^
t]A:i,#"CHs%D.B;l,_?%R[.:*2d[@Z7rh"7Ep1;CnfpQ"2b^*(Ucd'hk\bD]AQr6ICLF3_=T,
pb!38P2mDP*Daf,q2DWb*j+'&?YR?hr$_M6G/=E\?=g!?BO./(ATZ&RM@J8PIJtN4MY"?1\g
R5ipVJ$ZhbEldC>l%3mZ_c6*+5ZJns1AIi^h]A7=Fs=m]Ag-8h:i1M)S.[)Q[h`E[F0!)X`0r!
WOm4Wh1sp!.t.<]A'r"f'LC?t)8t9i0E?E&N=jN0op.VaK[J6P7a_sN'E%JQ?1W1:n2NM>ifA
AOAM@lJ8[LNq[*!"cG)gnpD7AtG?J;h:dh!-D8%:Hih<FUl=T\38CXGcPZVI4R-`@>Gu<@pc
HE#Wb4c7Op3!<$ZJrM$?g_Kl/#Qq_J;O+QC6JhCrk.f'l",639@\"Q_"W&s'CV^-K(=dK096
s(@?!672X`;ko!*!4S'COiJ1!Sj3pWg5M%Ln]A@7!!_>uGEa)f\Nf;Io!=pN^\2g!k+P]Ade$^
^OH_%=8cb[FH@9HpqR;srcEbVXLmh:4[]AFSsM*-4*W]AKu`HMWuEO*Wg$W80V^M(F^&>$3Ud&
O2+1,9M(jp%SXSe/8)\)c]A(]APAnu(4!H*p&-$?S$SJN1a_4o6^;L!/NH$MBO&:sEZMN7,H=c
qo2%1[o**+p(^o@5[6f`284F'NXBflJ%>DSVBer0aX0=ZA8K\"k3e;e%Dq/ZJuaC>Gj7H7o>
d>:8@klekT:EgHuSBFNhoElVGdE*i[VmfQH*:``$d*iY(oY/:7K&)_=*\@4>JF#6R\X&O;ur
0cJp-H0.T<1mAXqB8YlC]Al:c,b^EOEn?K73(?m;;nn_!1VfN4(/R<sF[?HPa1M]A1:I)r$q-*
j.\H1!lh[nCYMJ6\'k_IU3V%FWtX;+bc?>t18LAX@O3,1e!3k0e\go(+FGh?eb.cslDYu[k+
i6rNOO25\f1cU-R-;3duQ[pAXebi#K%QK)j*5I"rEEB=k3,uXpED?TIhn#Dp8V$\@"DhrH;Y
47m!:DZ/!Sdp!o9?_1CCP,PHHLW-YP?0]AJf=d$O.dan]AlChga8n+*3e,Ml?&P3PB2$Yj$34r
bi6E8QE8hl<pQq8@/]Aj6$!aMV[_.=1ap!\5>SghK`nTf^dRnYQ95JYP/5^-^'!Gua)>X9$M'
M[uP4b8CHg/Y-H#D[=\;^(fE\>#dJm9`!XpP`mD*M,m\hCIaigeiUVe8O87&?Gc8B9fhlTf+
iegbZFa;lOM-c>0;oi,JB7iSAnb%KjR"a;oV+5m4HQ`OFS)c5P$>9&A+!9Z&_ca+C;G7!SGo
]A\iiZkWrM`_/2\gjYkEto9HU$ZHFIDSD1u6;N>@J/%opCL[-_MrBo83=M^[t)km#B&Pr*+>g
4kjS[?\H#gO@3TWd[PlP,t7o5ZZgE8bVZ!cFLo@YSBhgIuTL>+cH>\FKd\9f-*Vlbt_8nt-=
$:7N7R3'b(Y6bV#1\0Vq7BKk[F`2BW=$#XPnXukPZggBuNlGA,HA?Le)S87\";;jaHH,Hh1-
N=T+NDtj$$?nWIQm7p#`-uAQc?6g,$>]AoJ*nPmoEqWak'OM$Vmff$$qc<P!c:@JPJ(<^>q('
tEgUS+j%GqpS3$kGAj"IkF:*C(9*SKE4`MK.E^<i9DD$]A=EUGW6Z;_Yb:ee24:KBoB+&n4tc
17tGab8WtErjS;Xm6MnD)k,_P>Q%em=L!JLUG[UGqj*Scl0M,EUcWa^1Ri!0!JRE$K@PK.;K
,tIb3@M$c28X*N!?V`LYH(iVe5q+Gekj!m&Kmt<6QeIaU6s+B5'XZUXokFR]A6O1[.p^=T`qL
AVtHu\I@M?ab?=3jRXII8#I"UEB65,=@A*Dag4gBRIj&-;L,e#*W!7Ke?Nl"%AQH$-IC@"Il
`,\uU]A@fa@@dcb"t@^s@g$crfBHQd;26LtfYO6A`=8uP5,AFqai^o'SdJk)la#s[,c!o[4HB
J"ZuRL*CS52Ccm$F:cK$X#HK,T"Ynun-g3q.DRi,Z6e/55$c@FHl7@cH@?bdQWWGZUFi7S:=
bH<KHCXE,9)hG'<U5N?Y?(pk`?Z^?poPrE+_("n)T,*;GYCu?Zq,_5c%EYO'GD8/-\HpKSk7
Wa'Ec"8!oN7I<Sqn+$ij_(`VEJn!e>ij05"&h6>D1Jrd9_uD))ur0,Ci+4)JRhWMEAC@o.Z'
Z+"t.`n8:Ybh>I*a1sL*?H.q=+]Arr^5">1%Cac4B$&V?hCo<39=c7ABIZ%<@bd>J)_D0DL0o
#Cc[8FM>_Rg.>7i!tdR8faH$$ufoNJGr6!?/7>;gD&8X$4amd3j^YLmh?biV0`5]AfVcNN2@g
E[g<jI#S,G\MIZ1j&7!eK;gA^3b+!mY1%NmE[3ntUg:EqrHH`Z.1;\$DDjduBL#F4f+J,KB$
TJHYGG?E^PSFZc73=Cg?^dHb*o7%#'"G*?8@?)M>i4:9slJIi9b;!P?b?TKF462`K4L!8V2c
?]AYKS,D/LYfVB;O8U\.q5/sTge5\i3O's[mrN@[_nQHe-WXR-*Mr1j]A8!,gOtHC>_gJ*G''j
\.GbI=5Jl`0F'dRQEa4ARMGLDoSB2kRQ/uVH8;J/=GVm<8)Wqq_XAujWLYGRIe5%MZCCmOQF
1"[3F+?0u2rJ?R__XfdKaYJKl./u7JQbXZWA/PG5N%LJ'Lp^Q>s:HZ2UX<%^C9%&T7BQfM)I
tmA2^[1Z.M>oPMSB!&mkjMTa%kf!%1i_:-Wf=$2Ug>`sli^`Un_MQ'TUeod`"JB]ABc9ktY^"
*fD*D\BP#o"pNcdhiOK&j4di&"54?GneV9G)^KC0#C$M?l?a+)4m_LjO`-W1:4&9dJ-"UKJ7
F3;:+,3j=_'3od,U7lXu3R_M<>GNm=aDBg;#l(i#'8Ws#6W,J+ME"1gZG@nJn^467"8b1e(Y
Ei$_hJ6hB,n9$nuVQb!<3!uRkLq4Nt!B4,:RJ38"gK%!L-ibo=IHk`PY&X=`"1]Aj:dWKE_I1
=#KMYN-pgid8@:G"Z4S=JYs-E-0;nJBQktn&B]A9>pXAYr:_>9#=O*LOH4F4'#/W"533PYh[Q
ZL.g%;gFc+'<i7L;H91i*FP*Daf,q2FM^AJMT(Xr0kOK$_0!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__C6BB7A0212D9D7AF5B063C7AE49C5CB6">
<IM>
<![CDATA[!@eH,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$1UST5u`*!m@8N>*5rr=a0
?(oi`[U]AU@dFDJS5]AhOe>P7,=7[jVj1Em/V19,X&R5KD:M%N84AB(R)J^Smd0;rmDEfSgDBa
1AfY@oPR+9QH%]A(q+B>EiU4e[dB`c3Z+Ur.)*;Z5:?2OQ:kIYO'cJ=Vsg;iUKb^Ah;^\uF;N
fZ6>.8Fl?;k2h]AV`;XE:;nOEpp6r^gAV<?&9YpZb:_P!P?O\NbODPU16\^Qg`<?/T:u!(P:]A
6(kj)&nomsIJg%;=9T`rjrnLF0>LeLgei5BFm*&Y))I2jG0'eQp5$5D^3.KC7LA$lI-eLCP7
K8[Rbi8t/d]AFpJf!gWSO:K/DqS'NEBB8(.Di[8l8s&sXA&->l?gW`n<s,lI=5>4-;RSK4,"5
TMiq=eu44K-qblAg)(Yf/#"`OV[FSe!\GA?)XE>`4?Z_HQpr*>j9g]A&#eja0*_jf(B%;&HH$
ir?^)S!ZV5#P(5Su:>2BE"E98!XpO$P"5HN)&RX'ncS?J(/bCdeV',di!46PhB[mNOT\tL6H
kkQ[<>BPn0TNo"c^%Kgf>)_/:]AU]Ao&rOTuqq->t3!+'/`Zp5lin3pi!*h%GE>S'!(H-3mZPM
`,S./XRJ<RPHql9a8R64NB]A/a=%>V_ipO/0&aPpa01.2^24U%]A`)-V</"BSj)dZo!(VXoas)
2\1BeF2SmIcKoDL4'+)]AeN,[t0.X'r!N=8e.R4:jL]A+q)_+f(7+h5g^#N0l#m7?3Mb_4LJ*0
HNqE+D7bPEjKk0kjp^`Q8nHVr/8>Wrok#!*9\%Eb)"DD(!g5E;7YXRfq$\K1c3;HnKIV+uo?
O$)mo0"2HeQge*+O!-!1`MC/Dg'mq@GR8\Zs5mICm+5hSVE45r);EV:*'"Emm\%%td!3UtEB
hG&?;:emtiSZe/jeO,n!-_unbHB?3Ik/ed"I+,(pRncO_M_('Fm#GW@)3&_I1bD?$GdUa\5$
U`?81#^F$U\@QEumm_7HqE6"O<bC]AL3"^fK;VMRVe8^q(KC=qir;@=e;(.Zo;=WC2eVQ@O9G
+PqqP(+fZ.NG(e*!o-HK1LPofY:IVShcbPGWb$/5$2>%)nGg3BcmC;>]AT41<_tMef"P4'eb6
i'3e(k8:ee5K=5VjU<<WEZgY-!.l"t*!3rr@be(cetMmlA+?Z7V7$CM.*]A<<KB[V#o'!W9'Y
Q5_[Aj<@7N(P\Z8c\cg39X"(om)mUQB33#XLXYNp$hP->2*L5@d4:b^P3QHHZaa-LVWW9Mf,
$)&J%Wpb^dR4-(.@Li-_0UGXAU1l++(YGl=KbV&J3u,+eSR$=Tr']Ae*<)V!)'Xeqr!hK@s/?
DhIY!s@S#ZX]A&R$.BX-c\g`!_=[/N9ffKX0PPh%G&aoka8aLR8+C1Ep_:M6n07CP&r7W`8'k
rO9$aO"b@2R/5j]A8.U,IGJ3S_HN(N5[Hf0.m]AVNaS6XG##W\%<G(hDh']A<jG#=nN_\N\^UX4
pjPk?lYST%#OuNKJg.Vu.IXnt3]AjTeb3]A#[<;I"lPcQ-P%jnoIY0[jr/Cm/e=HO`M>YT,6aF
TLbGNkrj?9UEf/6&iUHqNgS^p"K4&gpOA@O4*EC]ApK.9&8/Pggj:CjOa'HCSkT;"%>/>e7ha
*#uuN47k65hM.[D&^50fL.=*:3XX#!G+\Z@U'2!Z,[-&e^;UfEqm4m%Ei,!\oXA50$i&75nD
:g`c1n:kTQH]A*Z'_(F%M.*NYEH_m*km2m%!"\7/"MQYJ:P-3`sR'X\05VgD`foapFJmU%W3Y
YbdcD7$k5`o)-cadC8:pjpX+\):?%Gh"Bp-`E`HpVC\Llp7cu;inJoj*kp`lOW%=RHPsqim*
g:!m-@YmUiH[@-ueai4\/Ja=e^'5,C]A7s&dbko*4RljGV9,'YtarF8AW,L]AK&1uj,YP9@O#2
/W3ceS"!iF]AA2;&ZdD^q[.+R^IPeA%8D(iIjHdn?a6OJ"RPS;M+oSmp&RkFTZr&t+-bF"?_[
DbA)"m:]A)"t>`RnZ]AC5Ft-HU<t.YC-;6a0>*+d:!91lsU9WN"aTh?/#rN_?hBrEo6sA@V?uO
@!>$`FNK0*;cS';OTJG8XH=u]A?ONm[LQN./(Okf]AT2?=$R59r,]A0,smeV)_F$R]Au`kq\C<r(
gP9@H%A=MgCL^Mqai("$o%'\2I0VLakn9[]ASVn1%_MW@M&=uA[j"@tV8LQSpd%6O=QR0bhs1
p`(of:h%=rG\_,5-#bG`p?8Oo.L?qLia3GNrpd?2?,I[S2]A,E4hTNV.Ce\DsAg'>B"M+I:Ja
2*@WsMS;i,b4BIDZk%2f[>n*c%2qq6BaOX=?!P&&:)C&@g';uMfo^O]AP]Ana8/7g]Aa`gj0[q8
/^=q-LG%YKFe7b$Pk,K'nVY>>o4]AR!Bbl_B7TRo4l`Z('HAoa=!3ZUT";S18g*ojj$,#5i7h
-Ci-mtublH?@JSB-2L!A-fWOtuMr(UaQ)_i=]AF0slXT#HFIBU/ql+tU0$qCOj\,/HEOAm5^W
E//%`6^l!#poII'()P+.R1^Vnomp"H"MUu#@'qSKK$uK(K4]AtDTg3O\:l-uYn9kU0;bg]A,i2
t.D9,n&P9>P@aq;ODhcSGQFNV%Gcc&4]A&JUdWF$$i,_(UXaQNte*m%MOIXeqee9Drt=3>9&@
#q(/W3$@pfcF)hlbkcEA"L_CCDoH?5iL>tL`!:D\Fm5jJZ,>DSO>gN1\?k'$r=c(`fbPMSsl
&W3]AiLk1@BYGM'IIkR!N4NI[T[d*K\3.EMC(:/chA(#uWA@YOFT/<EUp[<5_jY<@-g+Lu0>Z
p+Qc<]AnD[`a"H><EMAIL>/%L&$68IW*[55J++Gc$p>X:XW/Oj+=WMi*WJnFn8;6CX4
b?Ac@A8gI#[!I2bHVSdtHY+a?21h%'Q\I^nMFt<:LZ\CgJ?,k"E$;kq+p)s\6A*d+^M44Jlr
2aHT/YX]AV-KYd1@ea'U"(nXH*.Hl[4Ee-RFOte2G5p>FWUAF6[kYl!s8o3S/_`ra7j$>4>":
6:`M1D^L4SL3U1kTANm+&$OiQ\Y=F\e!QT\_:5T!FU0oG556>4gA%S?*oFdp75g@\/<,hZ*8
*7!Z"V;.hQ`RoSb0Dk<,l]AgfLsb>_Sp#B;:&Eh65Bnuq!Gk:"rsE!5c<k.mi`:0RfBKHiMf[
327fl4o%*(R=(pDuT1Uq?b3qo,rNpQ/QpASYe4a7-/^iHgf/l$DD;k2h]AV`;Ypr:F>pY&*d4
+$MI\!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__941F354D1F9462B64D43B5EFBB8169AC">
<IM>
<![CDATA[!UU.*pPD^A7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^#dd/k5u`*!m@7s.*7YeEf5
#3#+b;l[#XQ<B,!^emPa7Z[UHFePHltO%5bb!<.$YopLuW`(B@dGUJ6M`"J0Me6aj]AeJll(a
H#m%"?%>h*<d#-6Ud\]Au/5iIhBa:n/,pWhE$a0'W!2jmfmY4U>kbg,!,pG_8!cJ1K#Ma]A'e;
2S89U(jFP74Ci+.PBg623M]Aq.<UPcWHZbA!U8/&PWBK.T\*(p(#3m>mm2@&l6(UhUP@-HZdW
Y74V9A)/T+rLqXJM`2ltqD%2t[*f;$tp"Uf2bK,hQ;!ar16XX8!?LB*^BCEd^0\-Uk\=r.6J
Y%ECu>`KkI"PnuU#=g9YKB2LfPklWi@>ODH/u]Ao;VptZj$Y@gnBGS/kh2tL5\X`C"f;tb!rW
\>hNBGgfWM(8kr"!c,!$Ju.]A>G?H?N>B\(fpasCg`clVGNWd\V+_U\es@7q)49h.&gr@YCUI
a!npeIF!\jJ-,72:!g.oFDeJssOTt&Y"W+LD]A"lJ6W:a%*$F16L2mYXV5X,ku3B8HucIE(jR
/IW4RIa<gXJjH:Wc8)qm>JKWdfmcL)3,`dT5PHrMrbbT)&2(*$DmrXm/l:Z/23C82>^Mmap@
C3Hme*\?@_f%U`GhgJ]AR:Sj<jhihe;(0a#h>A)OqMC7XL:#m_u^@!`_/0fi/m"0#2TiT':qW
^Lq1!%pdfuZg,BG$3KQ[!c/5".Gthk^srE;%\-]AFeIA:Kj?(0-GenM_i$?Y!NYda:E3_-Y4Q
'rT2\,;?2Xa^tHNH/bOPWA)7:/%N'Q)d?0#0=HlS?8H!4[=kS3ZCTXWh^71#/fE$V^6@%<pM
?A6#\0@T)#%E-6E^<5?bP@-[g@bBNj1JPh.<hkU/647oA?<:,55);q@l0!<$eNAh2@nRf6TO
^gaqXX8"0ia%)@f`&CJEF)KbdqTh0mKVHd@hIKA$IS2?4"LsFq/>9!&P2S)!ZX\%!P#Om$^b
#nAfk#3K+5k%1UoK)rjgM)+1^#<GX,KPbrWNV_:@L]A%oAA$7hj\8Wk0/IUtWW?LW9Q&bl_kl
i6DWl\E,;mCK4FibkN`&BV'Bc%6a,)f1,#+7%jYE(c57\Ek_GQIpYK\I>mb5aZ+57quMKQgk
lk1)Qtf)e4UN+n2KG&-`U<<3Ym,gdqWJbPH&oe!>TCB*?oIY8Tdg8*\^WhD6hZH=\E$md%74
E[G:no.0=A.S</7ga4("e2.+:#fcn6q>?Iiac?%\<W_ui_.XE&\C'"Vf]Ar8:SGZu\Q<MWn%e
Fm-N>p/(l2Ut&-H`U+O`t1hTDY!Dn#7QV6<8tbShY)8l]AT%;>%sn*,VU!@V:gYBL>LBNi6Ld
=WFgnC)>XD_Y:K8qMh1Jd6k66Xme`1eeXd%EP/=#CQ:CIpWj0fWbl6!3o\"Pa9GageXE'@U;
X!1k?S(TtqSJ!>17c]AnI;lZ`^i;Q#+f*hnEhGrRlN-Jn5N)n([\A"7"eZ;$P@Jk`po1kE;Ip
P[HO"bqh624WrkW/8H"(msR]Auu_0Wo:U-_!<?<HT)#o(P#2bs#[Adb-<nHpHS$5Y_sM3V::]A
[Jce=UL4!4dSc7C7H)d"jicI8U:+@eMO1;k:1o#RW'/GlAP`b9Yg/O'&<$s>9<bIP5$BHd]A(
92dYT\q6>`Y,;mDQ67.$st:XOgtO;ek;Uj'amd^eO,U\a^Z&QU8@IGU;:2@6E'X-T\si:#4k
fn5)([16lJQeZloU>-PnOhirX5NZY8iWU!5^MFeR!$bN<SZQZMQF6>>h'Diibm96s6KS.3+I
=15&2JL?*6Pq?UF9$^U";"VOa2/pe4/q$HPq-K`AY&3p\3?8MHghpI^Rt8$O!4C[.f@@0='8
R/^mQFW=Z[>'f$5QlMc[#@Bgk"&eKF(sE)B@U%BVZ_M%K0%nq/Q>tEhERGNo'"&^P:L?_rr>
jrH3<^a1GqdKOq1EOsX+=ZcPQ[Es_0U*'<Qq'O93eYN<or=4g"a^W\aFldCe*8*\C:"fE0f,
R.s$S]A':Egb?OY(FV7t#>a$=s6=>lkE8Z.#`u^kXp[o<m0D6(0Qtp_&b@J;*'IU<322skT\s
j&)8i=,$F(OXa5gNF$+#e/3,fem6:VnN]A1d1gJV'WsB63f/I2=*0U"?k/QE9e1,7kq%\>*nb
iF11)3!8\o^Tje>7U6f]A\2H497f#&-$QosDF1*XY8e-C;Cf8;pCb_Kr<fYqA7i1fM9:<20Ho
@]A>5&]A""fKCf_E&fASV9[,SJ,1S3!MDuF+4i1t<C^Be!4ImS]Aq4_-#EY5PDRp4;o1hYkq#SN
YO.q@o_)ZLG/[#,#+,/??>B;;Kl2c)>qjVH/KPMk%r1%hp'@`Z>]A[iMD!tK[]A(V@Qb:'0k,.
EoP"/ArUB-OG_rfL7u4E<,D0g=+=k`6t;]AC*!#>(`I?A%_QG&GebA-m?CaX\$+;YJ^3TdfB0
;f'<c2ilaHTlES/g4;B=CL.?*1qmB8VahB+SM6^1V5!B6d'<I1%JGlYPi32j[?HEh8J:clQ"
0!,fls#G%+7f2G\kA`<<eGr@ElpZ*fP;n=j#CuMAG>c1qF,6CG"D;Fn(Lm==J=q;uZmTS>`c
lde\>^*[ns[s:[QEjHQf]A*fN;<f!R^/O"b,@dKg1[j\6>\pgi&4u\-"979(]Ac#]A(H[,InO:E
JV:AqoIkchm2N/r$mQBe%SF[Ykn!=>s(\t^<^2;pq62uZs@)H(LVk<*lc8f\ok57*Fl%+&=!
bsZ6XXn=;Meh%Wrbh9ig<KZt$(SY;3It]A'k#:OT=jOpu]AG.j**r'4oWRffa[Xlbb>XCTlV_$
&[aKYq&[&O0DV'nb]A8l:IDPbM-+q>g^?_C*<C!@e'L!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF($$$<=3,'',$$$)]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="3" cs="2" s="6">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[data_body]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_phone" columnName="MOBILE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[UP_BRANCH_NO]]></CNAME>
<Compare op="0">
<ColumnRow column="4" row="3"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount count="=1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="7" r="3" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF($sxtype==1,"<div style='width:98%;height:16px;line-height:40px;border-radius:8px;background:#e6ebf8;'><div style='background:#FDAB07;width:" + if(I4 = 0,"0%",IF((I4 / 120) > 1,"98%",FORMAT(I4 / 120,"0%"))) + ";height:14px;line-height:40px;box-shadow:2px 2px 6px #444444;border-radius:7px;'></div></div>","<div style='width:98%;height:16px;line-height:40px;border-radius:8px;background:#e6ebf8;'><div style='background:#FDAB07;width:" + if(I4 = 0,"0%",IF((I4 / I1) > 1,"98%",FORMAT(I4 / I1,"0%"))) + ";height:14px;line-height:40px;box-shadow:2px 2px 6px #444444;border-radius:7px;'></div></div>")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="7">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="SCORE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if($sxtype=3,FORMAT($$$,'#0.00'),FORMAT($$$,'#0'))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" s="8">
<O>
<![CDATA[管理]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="J4"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="3" s="9">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__CEEAE8897010892364CE393976732D1A">
<IM>
<![CDATA[!<WK&qh\-E7h#eD$31&+%7s)Y;?-[s1&q:S1'e'a!!"')Lp6p["3O2'5u`*!m?]AnJ'L<SV2\
3,P'VZe-M@meP`^_8JZb[i>-l1BK@hBtU9eO!4]AiQ,g`WlO>&B)T.ap82N8>.1</2S!6TH]A4
G:ihu8S_YO$bP@d`gXeSt,e&qG559-.S_;9cG38[6:6J#$&0XAn#SA7KL_(_=N<W5h),,Ps<
De]APU7..!U0j6%HCQC*K,i!*Ub@FK1Eak#L;%TA)#=aCa[%dEZ=6FC$Mu[RUYf#>'$m!nd9J
oH34F2G=Z&s,i]AloN,"K.602Qc1H)Fq&`dqZ!-?9*7k,lA)-u6@JdU*EFcJNa[;<W3XmR*fa
Z=_=RIF0k'T9tV?Hq#ZLZ_"U<2+7-u&djsB-Ag_COt94J(Eo52,cc5)rfQ97;Mc[C&-dJZNZ
,6)?P@j%'J(bD97U'MI<>=!jp34X:-^#tLmDm%WpUZpZGGI&e2ZV$fZ`j$ee%@Bm)J]A?ma@m
i8N\(%>3%k8RK9SWY"/s'dS,t>S'(<`D^)f4D:\*XhdXnXnUgDj6r6`c+rF9<Y!qQ?Q&(YMG
2iBml$>YHoGaG@5bMKk0`$D6\\jc"=+[.-,ZZ+G8irQW&cFMa-8GXZc1q1:HS$0;Oq!7#@[7
B;nKk16j]AA2f1%:`A]A8(5?fH/J)ks:qKXu,OEOmI#6s-7fj8723)l(#(dL-s+XbG)-`D5p0#
CYB/#*[t&,]A0s!A:83oZJ4^D\R4d<jRQ:af%[&MX,E.f15'nDk,p8>u)m'1Lg[hBDO-0&r8/
r8I.QQM4=LK$&rP\YP9Og$=aHnC"X[pTt#t.1T;f^)%7%(aU!T+]ATW:U^"Z:B2:;>Aul<Qu-
+=Fc&3)G)./lE+7"D\Detq#0PiclJ2Rc:e<W?mA8*XM'6rnERm6-;.<B7!8'q4@dD9eRKlfd
@]AjG9?;J67reS*?q'DkOhGT:K+G[_`1;mFWo`U6aY[C*O.Phm44P/nI<0ss\GO?f,6$8oIFa
;'P).M92O&+./Hh4&2:?>CY<&"WT*QZZ=Nel<_DX,lN*hDI3t3Ig^;5gg5biOKnZXKG?4LVZ
aQ7LG#Zuc':fi;^?*Ju_,/RqEN]A3PKJ_Y=EghUL39=16iZ$M<jW"@?QpCg2r).C(<.@4FIn;
'au,4&tVS+hbIL0N^eok^"[<O4LS&8;BA^cR?WjiQp*@D=gseG:In.:@3(pO<54+Y`!U4i3E
>07rBPLXGhuQic0\nEQo>KEr/67#2Bt6A,UVCFoGbPe]A\)dC6Q5z8OZBBY!QNJ~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=E4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[//     var branch=ObjGet("get","branch");
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date");
//     var pm=ObjGet("get","fgspm");
window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/考核督导详情_营业部.frm&pany="+pany+"&user="+user+"&level="+level+"&fgs="+fgs+"&zbid="+zbid+"&date="+date, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius: "20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 80.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 80.0,
			widthPercent: 90.0
		} //设置弹窗大小格式
	}
});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="11" r="3" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="4" rs="8" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" cs="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="5" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" cs="9" s="11">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" s="12">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="6" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="6" cs="3" s="14">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="DRZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>当日值&nbsp;</font>"+FORMAT($$$,'#,##0.0')]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" cs="2" s="15">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="JSR"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较上日&nbsp;</font>"+if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#0")+"</font>","<font style='color:#DE554F;'>+"+FORMAT($$$,"#0")+"</font>")]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="6" cs="2" s="16">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="STATUS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[null]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id=STA" + J10 + ">" + $$$ + "</div>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="6" s="11">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="7" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="17">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="7" cs="3" s="14">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="JSY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较月初&nbsp;</font>"+FORMAT($$$,'#,##0.0')]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" cs="2" s="15">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="JSN"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较年初&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="7" cs="2" s="16">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id=FNAME" + J10 + ">" + $$$ + "</div>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="7" s="18">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="8" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="8" s="17">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="8" cs="3" s="14">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="GOAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>年度目标&nbsp;</font>"+FORMAT($$$,'#,##0.0')]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="8" cs="2" s="15">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="NDJD"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>年度进度&nbsp;</font>"+FORMAT($$$,'#0.0%')]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="8" cs="2" s="16">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NO"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id=FNO" + J10 + ">" + $$$ + "</div>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="8" s="18">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="10">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="9" s="19">
<O t="DSColumn">
<Attributes dsName="data_phone" columnName="MOBILE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[UP_BRANCH_NO]]></CNAME>
<Compare op="0">
<ColumnRow column="4" row="3"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true" customTooltip="false">
<ToolTipText>
<![CDATA[=$$$]]></ToolTipText>
</CellGUIAttr>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="9" cs="3" s="14">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="JQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>基期值&nbsp;</font>"+FORMAT($$$,'#,##0.0')]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="9" cs="2" s="15">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>月提醒次数&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="9" cs="2" s="20">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="11" r="9" s="18">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="10" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="10" s="17">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="10" cs="7" s="21">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:28px;float:left;'><div onclick=KHDD_XQ(" + E4 + "," + B4 + ","+$level+") style='width:22%;float:left;height:100%;background:#FDAB07;border-radius:5px;'><div style='width:10%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/info.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:50%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>详情</div></div><div id='phone' style='width:22%;float:left;height:100%;margin-left:2%;background:#51A579;border-radius:5px;'>"+if(LEN(G4)==0,'',"<a href='tel:" + G4 + "' style='text-decoration:none;color:white;'>")+"<van-button type='primary'><div style='width:10%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/phone.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:50%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;color:#"+if(LEN(G4)==0,'67696a','ffffff')+"'>拨号</div></van-button></a></div><div onclick=Details(" + E4 + "," + B4 + ") style='margin-left:2%;width:25%;float:left;height:100%;background:#09A3FE;border-radius:5px;'><div style='width:5%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/bubble.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>弹一下</div></div><div id='attention" + J10 + "' onclick=attenClick(" + J10 + ") style='margin-left:2%;width:25%;float:left;height:100%;background:#" + IF(J7 = 0,'417BF6','AFBBCC') + ";border-radius:5px;'><div style='width:5%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/follow_add.png) no-repeat;background-size:contain;background-position: center;'></div><div id='FontWZ" + J10 + "' style='width:60%;height:100%;float:left;line-height:30px;text-align:left;margin-left:3px;'>" + IF(J7 = 0,'关注','已关注') + "</div></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="11" r="10" s="18">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="11" cs="9" s="11">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="12" cs="13" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="E4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A13"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="72">
<foreground>
<FineColor color="-12485642" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Left style="5">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Left>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border>
<Left style="5">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Left>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="黑体" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="黑体" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-657670" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-657670" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-657670" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[[(LTBgMQgPhSJ$BW`HMAD=&h,g7-Zp)1/Ea)5F,B&R)P2OfDE.'fBL%'icJLNS6+]A70Gn+6k
JD!rB0iQoXR2Yn)qHXj5A#;GWW+p*7oJMcI[3Q7Hs-<do*MMT/gtI<`;B)i63'2b1#g;J"6G
6i_721cRoT"'kO.jN3u?J).URZA&d+Vf-e56?hu"km[E0erh]APi1&'S\08q7d/P[>CIa--A_
FkscWM6*g&/L)Q?sqPB[Ea(JI!do?9jBcM*":\$E6@n-OToH:[1)&N_=C@*\t(9Xq4YH"2A$
&s6J05HLV<m4&gttr/:R3`,`MH@l.:=A/!]Ag`G;PE.Z,a$4(Ae:W7e<799(\:Br:J7+jPO/9
r^0'B*E-gfJj[F4^L-@(q3+$@W`!<S[Z_^Q:@ZVB5gTS8jKQ4P4d3LQ,$2@VJ4\k(]A6#N+8`
;"=@3rP/MH1rEC>fX%7=A>Ged!2=\$M$GZ3fE]A(8o/3F-H2&V;Yrim<6=iVbXEc9GF,u@&(C
FV5>?I"4mW,W=,7LGig#PA>k%5?i.H(IEj5lp.^MBlUWDc\K\Sdr8^eYcLb@<BkOI/&qUB[h
XR&F6@$!b$Dl<S"o>qYOj<X(n&E\m[cKog;BS^1S^RIg[e,ADn?@EkdZMr.L+AfIiun=:f%*
j.INZES1;IE6dO2'YHMph2bFj@MS+a[HPg%9^YXE3[=P75igNP(BETXQ[<p#$h4i3HUMH5K+
OUk`g3H4<Om9'$CT:h:$9GFCf\6\`nU&DA(B&t-54r/r^e9L!*nnoq6X"mL\Vou)FerUPF.\
C/?%NVU$hg7A5*^FU+6FFor*!*P$ptRmYM7WS_Ze1kYcO9W"glD7baiaY=j6`elNf5m74osq
)bdj!0>G?!;3Nb,]AY:8?Q.@O3oA0^o.i6\c0LJF3F6LT&+gMlVd"$M6oGncrk@cC<&^Ak)=F
lHEThe'5eOpTC]A.k0Dj5K8)6&kJ-K#Od%PmW?tSf:"?=ZHXInYC7`Aqm;s0lE@l1rP*Q%8V3
5Kr*hp"@'1;p+J?>rp(k*I"gaRVJ)e%E3pV3FiplWS_neL?Y"%H7DQnVH%8X2Qc9D-D]A;O&O
Gu<r,f7-teBLDd:[25GtY\>ksq>Os5c&U[8L5=[4SEuBhK=+`uYOE7NI(n[GiR=-UeIdlk7'
+gp%/i!-\GmFZL*&,jJbVG^9(tt#ebC+$.@H*)Ao+)nD]Ae3J+tcU6p$R0(<?;#]A`-uhSo7<V
A+lHkorZn+nnu4GTL1Ilis#(t4Wj;Z[(N@@qHsfnNX@$dMg.08e!_K"m`I[1`aiPSQXJaa`p
b(AI'3,O@OZk-QSH%TJ.*YTf&-(#*8muRh9aW>?Kt22N-`7Ae7h5]Ai.=h`;#Y\:F7+73.&0Z
/nA55<j]AHW;#Z0!tceP:P/YFYt0*UnY%+6#Z9(Q&Z[db9`U#"'FUUL2KSW&6/\;UW'e8qI>,
Bu'^I;8gN7(n8RCn:lRWA'8nYrK5k[^`$k<WB>UJe!S\m/pkC-03?8>k$fb<:#F2ZaJ-qoE^
ceNON(C$Lo!<&_HF(t_3?1$9(Z]A_Q(>6RhS7296N2#d(!=!u:*,5CAC>@95P+<G*3bLq0/KW
8hu47]AA/-.#WnBXYk'_oHc<f/\Ftoc%@jcI7*"73O]A`&45EVbWEE97%b?U'6+ks-j$Y+.]AMS
cV`61%D5J'6292ef/W($H1!n#L8@4>fO/o4fXu`VBfUIoPVRMCmj^#lDj:D9>!_92j<?q[;M
Z_Eqa;_dliQS^GKcXq*2G,<!0I10m%\":Bbf<jZT=#mg?9+Dueq4R]AruEKQ>/BCMO?e[?UOu
^9ejR?2#m->j?5m90.=)!VOXFPNIhKU%74'9i^n>XR0cT[R9\r:.-[]ASs`IaipKijF:XF3*J
]A;:=CSD(]A.a#"GHW!oMYjFHPAe18R/;GjM7d8mcF^(C9,kOu)rlD7?mrkPm9@oZ6HCh)bSF<
M\YHS$,>$\<"gr"/=pfO>^;<c!#%PKU(U4?++Ai4H+=`I*?mf+I57t)f_mHYY\HWu8o7<5S"
Cm8=r?&`I^S-t;DT>;&lpZhn@j_,R#+E#HJaK=<Y/;eW2BHu^'-.ru8Am?H\&^UL>9*U;CCQ
\qb>hm6fu13g:0+\&gnUmNl>o)al5BJM#HAm=Wfc"#e>ZdpitNb>[+'E/O49C<,@405lbs"h
lInoE\J_d*fOPIiA),>oT@WgU)RGdFU/M']A_=1g]AbMHFt<[Q]AM$Nk8;,"#!t?k!IT^C5[a$2
3HnRGcQs+-@oB._<!Hpmj(mW>ptm1;(k&jS6,0oQts7]AjLQ6]A3u%&kPUuD=RkB6DHG/e8?-V
7GWZRUMQhC!j*U\H93qR'/m:G5^qEBYAIk@W(./8'/I18/DIN#&#GELP%q\)8@kA@83RnFig
'nfuhI5Ei-9oV^s%:DG[pmU);1[*i@7Z"3Z$raZ$XP486/HT"Qnl>u^2BATY(e-b1="\d?-)
=uQ>s;2nZ3^RmIr3<1@<gP!\/m]Aj4"I3%]A;oY@o?3(1-o,]A`CU^GK#Efl#dfA^@LRP[I;*&T
<E8j*K?JgVaS;'WcD;:O^cQ@0*tCblR-bWL-p79n2GlLPdY^k>IH'BG=sJ&r4T92M[=hkE>@
&rXk^BG3\NK[cI2;%f(q>bQ&fV4MhHlZ51imT(\W6=]A@J(:FH$&ZH,&m4?5tj[;olZ%FG@2u
grYo0iRJXDk)$IJ"B;s$]A`AFoC-,pYkZVCu[AIk8H18b7U2]A*J\K6?^sO)`i6em<R@Bicf+D
86%$".GPhVL@(LV0GF4+/#'CmIdt13ZH9`^S2g"?QaS7(Po&+kkA+M)H-q6hdM!A3U4cQ>h@
mLTRLWOm-+G[i@b5@X;a^#Yf2g6Yq<9IO)XLOC1BtlP."a*h%*t;gkY6,_FN2)bP3Q=Y?i5#
f9YD"TZO>+;+#Dm*EQ8^YATY9_#+Ot%V2>lhh:TNSqN`h?\!c<57<qm?\S%kT(gW7PcR:1S[
*B*&WEnlL=5D1Vq=b>!Y/igQN8t@)<l!(H`f6n+f/ur'm4R8&ke,&d=-dbd7M,Ddq.&RXYO0
(M4B6L-YtSCh/,5#RJ-_@h^58N<I*N`H^\pCh!m!qJ$G8qL:5Za,QG,hK(u]ApO8F3Ba_+]A"^
;fSCBh]AlaF._ZfjC3$tC,VOUerU2O+]APd04Uqer[VGmZHBUuSiTV&sb*/PWD;j>4h[r[4ci6
UuhUeH=N!#>BJJB\\e*Nn&Q5g?9oI-`]Ah*1oU9:cS$D0[Jp/J#UBp[WXQ\]AYlKT>'?NLf+k^
U&5,M.Q^a!&X^jBVBH]AsnY)0&1TMgnqqtE8>e,i.&I2n,YBorfpWVX#KX,J8)X'$6:;3KE41
9\blKmPq/&pJ^f3V+L.(`bo6Xj/):KbQj#J5XnD$"P9R4,\O[R'?'(G8elLD*t5aeFZ8-Eol
4\?u0'fi8<tY,%=sM`m$V*a-s2T_r`lE,/!s^;iO"qkcu4LlF`7XIM_R,Tcd!i@T]A9in-b\8
e61T<\00(L!BA^`W"BLS+^p20cSds[[s170s"U0dKo.qQ@f*+S<0_2Je(L"B<=0pUQplAd/g
aE).+DGe!o2+[&ljP%[CJV3M$4]AE:\th9iHr&Vs"Wic<DYKQo&<1k+Qs@gZ&$PoRM0:AV&`_
l)X('jRL;fl<5gOi;t9pAp.?"6QN#qmC-p_1Yk2qE"*^R_$3dP&940Q:#UqUEiV)hQdO6R;s
G-i(R#^<Z35=hg;g'-l-!.9P>rHu,[9f#HUVn&rRVeZfb#6RV6>cn/HZd'`nl1O'VcjHeEp!
eoBL$=b_l5j3aHN!)m2sf`,n\A3D>heP1-#!J"=1-eKI%\eLZk>bd1h#W5H)`aF]AOOSk/c&+
1P@oX+QA04+JaP&@O*M0]ArYfKYQdt[kBLgB(MN>NRp`7Wp2=Ar,/0]Afh6%&-ooTORGVqpIK@
(AK#51,e%8AY"qhQ'[g.!YUo66FKP<HZ=SIKt`bD=cpGYVd!OJ/`ZnGURnJ=mkjm?q1U[d@k
%1@G@>RY**kF`^KFMcnZp[n^HN0I=BKUOB>qo!.3Tu6(:gnBli4_6G'cVm?QNGE585<TgI'(
p8sRF\\57dW!Qnh\nS`pdW(lU8iT;84k!-\Ti;FcUO4"*M8I>3!MhD'0)-q>==Z)AND=,JS0
$?ZW,t4Zj#lDkbBd2N,(f&+7_%Ud,fQd.oENp$sDk)Wsh5i,0&`eg[kBk4hTFN5Gkt31!XK1
,t4+),QnL[uN&%PEZD=B=;U\n-E7sIM/gG[X<DZ0S-cHWtBNR2L1g<MaK(?Ar!@l]A5Oo80*W
OQQ1`_^Z,pP+*YdSb=QeA`5d77r1suOQaniI9D-Nu_m!rdPOgI(@_bg`7.93WW-qir@44EE:
'hXAI>3TjcM_RM#Ndk&aeQDh,bp(mQ<;YEl$3L+O"0:kf6uo>Y74A9:iE.I+/u6spJHOuC!p
^VNiT;t!.dWqaCpC:.on;fu&A5$VFYl!2GM1eBs%AS9YcXUn"^YBf*%5Zf@KCEX>E)^F;tM'
3)'em.;H!End>3jcgO4u8QnZ)0H$BIB^HqkVINm<JM68=S@RP]Aq<Kq8nVKLqqHY/EUC?VJ!;
3gBoO((;Jrmbf4QW`#tX,8]A8:6I<a]Asf?,q\kI(aZZgu:g',djFk`Mf7_>M*%d`Y2i,mu]Ag=
0.C79&RV?@Vc50R,bGId%am'b!Z\9-<R^.>[`[76k>E:=$<@**rf<c-%d:4Pif5H/XB<EIt0
0Y'8B+DKfk4C`8BJm;]A0ns/gcc\HncCBq'+/4rV`LgLSY;.5PCg*)9<?&S.J.fihD5ZIBCp^
4p:RF^%XOG_jrIVl()Rc?EIcRTEB4IUgs!-h_C552a;&gkF-=#"Z#<+/P(<&1"j@pF8@gLmU
>U;ta^C/>2SJi=O5l&0InbE/LiWHrn(HdNs2iq:L(e(J#bNuZ;R-`2Qhe8]A=n:eEqfr2\*W;
i*6uIKS[Zrh8qaROU!C&rA,"50m4uPu-\s)9]ADeV*g;h1e"F6CGe@WLF)?*S\\H+jP/g8<PH
p%j45L0nmZF,$2ZXk(E5E;Jqg.pNhpU`YEC*C=2Zn;dc:bLKtChGE)V2pC%=tk+LuA:X+ts&
-SSLV5t:EaVtlD'/^NNMdH<#/]AYT8kn'sI/NBpia1%sHfK>G2r?7%&m!KhN6r&mJNW&19%9'
(`-><RI!+5s:'/A0_dp-&!gYO;ci%A#[R2#B[4Uir&7[<M-T'9CLMS&,7X=9>e5>#Qj1pd^*
G9pVH=3E^i=FoC1WbqdNI8C5eVBtqn_F=gJo=pUsL1f*XCee-7*JRg=E[V\5:J\?k#Erl[&Y
AdE!JS\p.?rr%MkJ,*1J!5cS5c2=L\(("eKQ.2,?Tb4k]Al*]ATH>m>l5&N[8p8.=h;-++YOK@
lj@%aCFfE95"Ncd#!gd*V[QLb4G*qOr'BO"j_Hukf/^,I@=KnU0kAfWWb\e!/ilkmm^f$E,_
)D+pkc%<DDa`%Jc7Z^nqL<;BJ.Yk&noX,KTSX_aX(q\OGO2!olTXar:I(?L)Ds!k.Po[Bjm=
Sp^FGUQbh#SIUVdZ<e%lEn%I:\LoKS&ZWH?U-IX)8*LDi)#3k*(f\G,GXnQ4Y=q=/WH/(=c8
!9$nKO.P'#=P4F66&7.&5m`5<%6[FRBDMWU`r:]A4/\a6(0dl4OTC*_6KDd\=JOTD";EJA79o
>s(l\@M5)4&0#YDmo)T6I\-W3BJF5jQ9GXDW5qa173!::.$iN,[[UVeF=hid$^?6EBuBAFRN
eB1%.[U;EkoYm8/)ETmlT.G`GHJ@\_Y*Jl1m;h@'6`)m>jXU4p(8chNG&IK#N?\5;2TQQ8Lf
mYe6-20'KDYlhV`EFVGXPo:%pj%cjt,p1]A/5-7E>X:LILbfoNnZ#]A\qJBRVg%]As[5ZY><r(&
lDYe:%&Ui7Qm<`2o[k36FZNO11_ME&_*0M\.34i)`"4lT1RCs%*6[\`A7PoJ/Bs]AA)(o6.&<
#^p7-T8_YqIiRI;!@Z<CB#uc^?#sK!O<"Ign$q6>&lQcj@!?,SkYD+msR:Mk(l/CQ?/Q!(Bp
"u4s=o$dA>sORX\Kr)&eT0pD2C9(M_=YTfpn4]AiN5I9o<MJ:dh<i&G/ZGL&/^dPQ\k1:&ETH
\7LaM6FaMifZn#A-AeqKTE_0]AFl,rY2'+sO\cSqTFpfk(8Nq&@]A,k"TWs$bZ^@@jf?J(S,\e
k3rU7g;[bnBITuHCWjP)>+hhg#u_r(UeKR;UN2AYA%dc3l;P^//:TW(W-7p`IZ_>Z186WQAA
*B@>rXA0HcmPX3Su>>_;c67&]ApNeLaa7;>ubIXfkeA?84`&=_p%kcV@HmpX[mV$YNF#Xj1b8
K!W(kbJ]AO0t-crfZ2,m#t'7R49FM<r30BPGR*$=`>"jj65,)++QVO_>'5!-EbTcDUuEs^p^p
U3/LQ-B>a&C]AI30"@U$@ct'kfsQ^Qn>t4>D`q8.m6QS%/EamsA8$.B9:duQH1"PcE1LjnEAh
*p8AlYTj^1%(<CL&HVMR;%C'i:.S&Q2_<r`B>["cHPe+%K/8'T&`Dq/328=/bnZdYSW8qqUs
?.@Q9Zu1,I1o:iYe+'Fn71>FFhTV5*ZYl-+1/E'i4.&`;NE;`VY%#lj<'Wpgi%%m<oNE8sV@
JBJP"2%0rf^&sncEVs$Ggr&e0dDqfYpp+^B>c+qcj>`5M`,aBVE@4nabQ+;-?#re<?JX=+_G
m.<JArEXC9mV.AWpl[R:l0:g)W[h4X$Gra"$Na$-?mnh%."_MR7rigHs!9mb!6?u5fjCB`G_
[stb#ho6E`jbuRr=unQHHQ/<&(!Df=(RV]AR)3W7SRRqAXPo^^phA9_'mZ:;_3s:Ynl*!l.V6
l`#:T']A^_rs3'mF*9o;(1!N:@+3#@nXCFN`cOQA/-VEYS!;oT!LKq%l?@MR3cc^K.;rd/UYs
[>u(s5u1!bl7Ohg*]Ag6!0Le5oP,!EcEe;4.>')?=^HEdq@\U$-5ODk:6K,CL::BgNk[DEDhb
%8!lJ&[^hS11f!M_Gp+*?s[17]A)0p",$'Qa8%drQ:bl'RQ@]A5F,C/aSGG7:-uC9Z#tJ8@R7D
!&+2d=57oWb$fRd#+FD-BTes!_#EKR%!0oqB1#BAh2I@)7J+K>9.f-*tcL6dKi5<QGnZVV7S
B[2mG\GA[#`/m$&cp_,.;$[4Y"J-=a4[>_SPNFecW%bSai*+o(^T@(f_g(Sm4X_6F6VgU2ZI
r!J4;c^B<l6R\De\@ruA\m\PL$kOPpMTp)l77s0Qb8-aBLrf/b"R=it[66d3jVX(Sn'Si]AD[
S&_^m%Y'&%kD4^0+NKXZ%cNBpZ$]AglL!&h=MP7`7&?JWDhCHWo5&flkL$1f+>A)s$Gg8,BpH
)-T44J;HYILkD%Q='k#_i4\heDEJkOBuED@4GU5XUBE+m"4,j%NcA/)XJD+2S)ZYaLW";<9J
jH^s]AkF^0S>0:kmU7Kse=jN?Td-nXNF9[Ak%/IdV\[p!#teDu.NW&eG-`L.`-Ka`=bQ(r18P
2$_Am/+)u]AtFF'2kY[I=^KU.?u9^i^rPMk9SoWd/UdieLAe1b=6o#Z?sHZnD9Q\E"chN(jSg
c*ULg54$dtii4oO7"C^.cCs(7=,N'o[;HR3iKg2($U,r^3kag)iph5ph#O8JN1iu=R8SI)6K
_O?0[OIuC1?r_l,3mo1+VQ*9=X0kSH]A/_9tMb*MZo'ZHiA8%-`_SZ85T+-ebZfOr>>^n<Hgc
#O@9?Q,hSrlK?#-bK>m;?+X`+UlW4nj^Pk01*=hZm9FGK)DZ_.;l91@PGN$U3m[WY<j+ADkj
kI5a&BUkNbNJKLop,)%H`$)bFl0,JA^%L#\K?8uZfp-$E+E,]AA:X2qBA%9U%S*jeO@A%U%.4
)Ih6!%O>o\bh+&j.lY=e0%fI)(r^DX-Thj2hS#L-_8@8[uP4cddjlO<G1$Y?VrR$a9@9qetX
O"Fe'Kar:L@_mUN<9V(@T.f98>IX%:CNQ#F%t:TVs@'Z7%rr;%e,W_p<HZhPBU3nXo.+Gh8e
15`c-I'Mh>K,"5#gGjKU[r1MrQ!>$bf]A^iJ0Kg_MS,g=45don[+"Y?Tb9j$[:W0]AH]A4o%,ZH
NdcV>:t51+(q8RV3a>Ln8meVjQmDTd+*ZNJ02QWM3J=]A4eZ4cO$pEHlUjt<j'O@dFgUeR^5@
&l#,6IVS=KGqM_pDXB^6,]AS5)9=V8PF<q&SA`JTX*C*m->"BR6`G$bB$FY=<!E5?><>JSKl3
,unlQ2&WWs1q\1LOXAL.^KIf\)+16T:$E-U.O>8s-q[C;7+G]AZ?&SD>I9V(<26PQD%D(NDpI
f4]AcmWM15VOq]AkRum7cu[V0(%P@"1`/@W"Slb7+EhK(McFP(DG3%S97_/k]AL6CPk+DV$7;=l
4I$&j5$a.:_n&?2f@N4LhM&Y-L@U&)9]A=*U^8o(He2;rc(qD2+\$=^JaV:l4q)9HKhWtPL"+
kId)-(:1&Z.7':\gPY1L,j(B6@7:UXbE6KaC*u[',F!%p8/GNI;Ob3]AW;a*f7rD*"VaZd:f,
>C)@NVNCEZY0C_`*FkIqMC7.L!M]Ag?_`F5XsD'9%OK[1"+W0Hn`SuuedF?UWMnr#M4DdU4VV
sO+sdD7`mNMp=sptj:"PB:75X)<13)X/q5q[\bbn\n#PGs?V4&<qa;94;Vo;a/a#0,sr6at<
H3[*AM+W"n2DK8Pdc81Qm*mkN:<CuW_pd\I/hc95:3mD/>ORRm<%[\H_[DJIJ%%QDpS19-`T
,lam`4rOB/0edD<CGLV?/J#W[@JkQ"At+maefd9QG2G).L:n1jR$c$=A9RJ#909O3aj0g:S]A
r*iXFYGdqcAta;M8.A9FgiIonGaCQA(Xpfu[0=+2l*Q+X0T6e"Wh6,XULu:PW^mG?u6VVpMY
MZ8^#hm!-6X7G"trS.cTsgMqobc[&'G:n'$N3^m7`pL_sYR6S()>otC)Df4G3Iqeea(&7HI$
87W2p6G_M7uc\Fq8U!TO[J?[",j-u6FB5Zm>O+^7@gn96S=[h%c%p@PMZE4?X4\aVIftX^MU
d0SYr1;=K(9+(%#rF2dVHbFY-5*#/Z0XUT*:YR2_b2K[\iYBF\8pq+_Y>[!Lf/HMGg2`e&X`
GWPR#NN[LN-f!d!mWW>)-.c%\2eElE@XoLo"lfWdR\4ljSZd2fN/T;2:hQ-@_mM21M?P0Vf%
nk9a6_)IIb,hLX95qAjT]A%d_'3o-jR5#D<tGC+O-gRKji`b9,?7cE[1kV'h.em\^D@qnG[m(
FL.1Th+=<2_s+=RJ$m?3YHYY9MQC<a;'h:h"9\["QYKg+fl]AJ0K(!7Z4-`q)XA%dbO5:B?;Z
*tQK0;E1OA.Y3t4Nt-QYVhK"rQK2X&)Pbl"2(C5(\o0bpj%ZL@Yqj.k&fNU.qWbB;Rep!o4R
A]A\]A>'32cYF*0f&AEaTrb"<[$nZ-aqrbAI/Od8:MN=M`1_G/BRnM!FbKggcu;QH6'4MdQN3?
24"23nVK;"hBFblf"UheX<Kg]A?j9,Q6*_r:@,f/e)qZ9*r25&8mPbm]AQN6k%:G%0$lC%(F0l
2^>AtP`pV1:1J?pBF5.lPf4EV,:XHC/"V=nJFV$'#"%rnt[3YZq#/Ng5IQ2.2<@a[c_9<>HK
EHG[<EMAIL>%.QJ]AR@tR&Sme6p2<=>3"VWV2>s^&q6[0Y>`cl.qlfZ=p_>!C!nugL$s-b^
!WT'mjc,ZboeK*b5<^[gq5!Ap"8KTBQ'U5Aq6D3*=8K12u6)-(?\KE'.9eB3;;I/5PX0`mf<
s$<cQH'aIYGAOC%!Gn'st]AY7O4@%^A&(D&5TInR)3R5JA#Eao@+qRSa>(#9>;BQAcA7V9Nt;
^8kq]AI")'Y[cD3;-B=%s-s@g@2pHbaE>iZ25\e"7Kt/elTm$*F7?\FUPGs\'*adbIr[^Ac[J
'kI*5aoG?\bJQ?^=;t"W4tcVOBlZe]AUU[:kgF\@hr3`;!<gU4f2EVjuu2am0+Ch0Xap3@Dj"
.WR]AWoh%1OpQ]AG'/nGV^GFV;eCkI4@7hA!.G5f2+.f']A@S]AtOYM!HHn?>"RZs.F4uk]A\21mN
($\f[E!WW\aPbb"1fh2a;booW*5bd3P)@kc=/o80AaX6=(8fJTi>5<;\a]AFUCL<rj25"`%+4
PhhDY2oC8,N\hNMAYKIu93]Ai55PZc("jaMGUB;YP"hnNscsnAD)lpOg$B24+k9LeU5L0XJdh
;'D'KSiM;EqOh:kY8hhs5HVIMj%taLY)6YDCkpYX>O%+"jo%aECus.Xc?h<d;MDLOB(9]A>Sf
q=hiq(.,H',c]AR?"4/k&kMf)bHU,^c.TPGQ(-n-u:p=3lNs)%tQ=TW(iEBc#"@^F05<lTDW7
9QZ]A?W]Aqu6Gqbd?4CY-;s/!bZinjV9U712sO"j)>%W#2Z_LHIn6M5TgA.td.nTZ[6f=:dJc0
A%E9ao//"e-b444Je)WRUalh,Rn"G!+.bT>@\W:.>S>Gd,l4oI*u7^_QJ(?28ZGh2P2"]A.@q
j#oSeeRrC*Ru*[$DEW5hI%FtEti5>UY?H*CJ;X:M*e,LkUHPYoFS))iIs^^g[0n(hlNaD&^d
/eYgkP@9O6Hkb#ASU+Rm!89a]A*ka54fAA[\Rg*f;?3/<<.pPCirAK.uUXJ"@ikGO!8rRl.)(
)sYe(LFD$H>Vpm(C%rU`1e0XDSjc5L.fW9@&0!'=,OOY=3h1LjF;0p=\$#iPVp.:UCY]AV+tg
^'mG$CC9,R!A(e2"PC(((TV@*@%rEj#EsAWu[UGL?o53n=dgh4?npXoohAK#\HL7=?'%`rgR
t_`u;eSa';-bm3+MLGO<B^qs?<Z75mf>tQK>:r;l@Y]A7+5lWi3CW6@c]AI;K7iW\OIBSK3Ymk
BgVhO?CHUkH>Ve"G[3:8nLcuun2.\uH;(/bKT_9;U<dr455VGVq/-+SdmZ)9t92>i<VZE[,i
jH5rDcm\f@-p_tr>VWF3ik!'[N]Al?*_F@5VhPatp*W!t\5ntu"/L:;j-l#1mS>aer!-r2(0s
!''Ef,8hVB[to$GmhJEa-0BXo7,J7,0(hR0S<_NR7srC:AC68A?@>E%5"Wn*oR)<;e]At+j?%
m/3e9UX8aK:?OLUb!iB-16G_IT^:f5sb3RX75^PO4mC8fmG1OEf6_>DFI%gG0B.fH.Au\-%j
PD_Wl@51E"o.#Y#TMVc:F)C&'Z^5Y-#p\C0B<8#d0euBllqB%KV`C&i'J`mgWTJ@g6FM]A81R
dlmg6uJ\XEl9\E+3a;lgoaHo5?aepe2IKY)@V`tla]AjGia?hPD^WM_gp'\%6A.RrY]A)fTlt\
bS66_4#L*!EH".oqYCaBI/fRnOl'lJ"nl=:I=q'Z#55<CihrN&"dt@DFlA$AQE-u#%N]AC=,#
AmJ4!dMseT>:/F81*S+P!sd4OGTr+!fV0h,??2&,fIlUO[b;"q<l!ao"R;3@n2Z\Dt5`-]AD"
ap]A,D^mgHV'rH<L*VH+RG:m@L.MOM_%Uaa1eNaL8ITdl@=RMnfYI-rWC%3_n8GI9_`X$]AU7<
.;NlkmC*uA!ZAb=`Ptk-h?o\L9`%0K@P&m\>8dFGKKQIki<.K&jP^!H+j$e^/R<:$WpsR?ZN
s4UVg+sWm_3e4TU,a8O:%iAKsseGF.3/)eQc"&r,TK^b#akm+bkS,X^S]A?8+''jTaK5Va8?;
ekMNYMJU$a_0_.!l=it#)FLFq9cTV;>;QiA%6Ao:&LR!nR<K8=p%%RIXqa4%_*Y#c=(.9(Nt
]AhFTmPo?^]A*9YMgm*>mKCM[0R<-[M+_LPM6Q;.j$?nqrS4XXK*@d_rAE'A?Ot4roIcI`gs-X
;23;Ei+6QlI98WmJ]Ao]Ao*/e?LAV*G!#MJWr8=B>*h[r+qs16aXXa04[3!nAQ#s-t:42;D0p]A
2@:N.b[Q6gG;amgW\'9^=qcSM\sLkVFRnVS+T>AlD/`:L4Cb//FQaL^F<Nsk<c_%B,<VE4No
76W/us4&K^-?/^lNbS1m!="+"6'Dofq>>;$oqG(EL]A$Mc&Va??-*cIm\351gXXmU"W0c;r1K
`]A'h-oeuZqqCH=lHMa]A"PGj&PmBhH7OhoWChuDV3$KScb1T"hECrE7sr<]AcEW#r=lKE1rFm>
h#]A5[RBGL1c3]Aga-lZi>/NU8:d@fHE^-1Hb`8>0>bad8=Pl9R?cn1G:1(l2QKHLL%&g?3CAd
%c1uufC,dU4p#MWfIn"@CBXY<n1L2q*XtL\0!]A<2g8Wj:A:+t"=DN%+HpL^m&X3$2kjHJqBl
rWb(K;M157bi/0#u.\#Rie\/YZ?Tf7*n"&p2bs2]A@3I5nZ>7$fYYGWm\R+f>E%>qqd!R$:4(
lB.sQ9%/+m.`!7Vd#?Xdnn]ASc;SDr#41S_f4(9I,$9hA9,[aJ'7=0<;F-2WS'OC2W,%6qo8Z
ZNu@Vm1CtYXEJrL\";caZcTd[ODO4)o^"o^S9W@``:^8tI"g9fp[`[[pW=,J:MQ9Rka\('Z.
Yb#eAkjUl-M9:>h@?.m>HC?UVVI_b^d3NLW6H^JB1gEU!ig"EW5\T?+ZP5Ltq9F`if=(csHO
ak5R0u`VS0fIo1=\bJ3L5;FUDXCZY:S:?ZW6;u_@9TeeHG^RaZ^isOBFkM&46R8Q+J"X`Pc'
;;HQ'_Ar+"!9k;MVVTQnpP&(ZZI>\%8Oi3%q?^=@/4l%(H/$TC#`sCWUq:k$=4>CDJK$WQur
a4]A4Qj6&foP@8m=IsVK.O/GQ$1l`8/k]AG>;))D#sY1**-'MBZ7C@A"^%iPX=&1DZ3R)k4\d/
n#R_J:dHrbDrs-r?(3TP%aB`5B1U,Zja2FU2+=H2$ITfrC/On[9=VOV%fM?b<JY)HG,!BM\[
DF;opQjjP0,0O&#&[?\A+s;R+.P4D/qV76ID;#9-52rjr2d"q8bOCH1#PH!^Mqn3`NoJoKJl
:YGU),mn&#:h(e#+r(Su?gs]A%>ef6+lZ>W?"G+c[mXPA4i50qQcCNG=7qD^.rA0hOoo%ts?h
P'r/c^!e,\_bEg`FXeETBhH6Fgjn`%AJ"JZGINtqWjY,]AXg2d8M`QQ$f/'2PJQ>QPg$qn@aD
"gGPCrPhF@_uepaR>cYi.IRn5(-beUO;D=,;h$5"q1j6KRnrj0?/"4KM;lOrj$kYa+cL'\al
G[FWQ0S*#<,6?uW$P&l`]A=YFa*]A[Ef?2+1M;fq\@(;K^\USkkj"nGg=m?u9t,;tH"*M+6$0t
")BmG9NiGUqRVX=Dt>Y2i!DBAZ:EnQiP'k5[)_/$GRc*TKk<Wt(_RO>8,Sot7o@hU6DUG#P3
4fbZS2fIbCr3"G/G0Kg+&cnIW!Vq7h0Rb+=B,oX2rA4>dGll=kP\`rPT-jqe:AA4KFLG/nA$
Vs_2(c#B+c-@\CVj0:AEh4o$hLWX`.rR7++`TK?K3%QYF#`X1NPlEY7tGP1U#0H<5FOCQ=J/
AnZTGpDcm^$sO6F`hdt/tOb3_7H*7HIZUb6G_>e9ld[<rQdi57//>j_RY5!jVA)_#;IkT32D
Yo@.gCcqTfedEqA1MLco^oLD>7T+UAP^=rHSS[i$oHrK4;hL)+7tlC^p%7['3DhrMbte;W[M
,bcm[Tm7Z>K,Im^p5_L+UpS8!aAhME!$^]A[7k.WRYSgjM7[,i4PZ2cZ"ACn9C@HlVmRd'["*
b9(e^d_En_RTd)Sp,*RuBK7@[4FeP]AQGtO:=cSY>;*BP3lU`@qEDFj(L_NKU(iQjKE7oPGs*
W+<-b"#?BG_%EBDY\%P8+(5cp,.hWH=$p2lHt/BI)c.b`9;F,4]A07<T1&uCQ'eoh`>asnj;9
p8?c'$Z6Zu:b);adI+B+\n[[<2XAXp3/FcBE-)(>9;PX7f2cJ]AJKI%j\C#u;rAj5o4JQGK`X
ghOI)iD^A_hDNc+rOt5#-WdX.2Sej/Z6pZq,_o%\Doo+60.3/WQ4YJDYYV?F,]Au,@NZHS.T.
&%YW0*!n[En>hE*IsNB1FHY:"BA#:>ncA5&q0'WsY#$n8RrQD/X`ck8_-EX]AFZErh1U&!JA>
C"d#-iL=@P?&d`=LL*skYf>B`7>JE;T9ZjhdX6N;=3@G_&.QY<n$"XaJpH=T8%a$5)A9"]A\@
(+R\]AW"PJ:(4_E=)bOoG'rAW:80m90P@se?>l4KRdYS<X&fdi@r4eQW3,r-I($[\D!gbR.Kk
NCq%_)q7c]A_?qNsZ@#'h9X3ieiI,r(2'TZHg``$lC81L4J9cLdT0Yq4%D1_WtIlC3+ll6c:3
H"IV]Af+[8_+pDFk6tZp5!tEC:=u!F=&3mfHeC:U-PkJ@Tk>JoKCdL/$k]AO,A8[@i&e`c:aaQ
2mZgE9\_&&)Lr,H7]AGF3)!r^-[Q""B!qZJQHVFi;WD04cSg$.;)V9;n!`:ET)CO*MMRTd?>H
E@(0J)9l9I(]A^4F]Ah:Aq!_6E;=4[k1CbQ>J/HN$fl]A&'<UL1Qo+d7UC?R6nsV[AZ?E\BQVF)
%GIl\*%DUrOc;Jft`$!$<uCUD<Y:eUp1eA--!(>B=\HYj%aI&<6fREIH:45+s)B3&QaZ;@f0
<(6jLDq/%/-pbeD9T>#qLa*J;FC6+/tIkPH0+C#j%_1+UK"RS6SI-RRl<*-t=&:>R;PosMoc
m@TJ[\3(G/&DQ?^lI49cDFNG%gh(8dm8LtkCAFI):*NF%fF9bTD)BZr>Oa7'<Qg`O%&qsBpp
7f>TH2@-Ff(P8Ec=[fH!/P_9b$pUk'W)T.:LI?:#FF,V9d:U5eV;1WCiX5%0U>#a@-p%!H3H
hDo:l\s,>n.0PO^h]A8?t%+15l%=VS[$Jd'WX4DO^=":l%A_JCT.JsfVnju&prR/@eB@Gf%#Y
WV,($K5MZp@)9@a5@M,*n/GCJ16KrH(ucIWNCjM[,@.,Q6i.@GFbUAZ+;k:i3-%Wn<n)I"g]A
O\&+`ugT#G;p"N'Js(q7$g`uGJC$V6uRYL40"S=-d!A@"@JH]AQk)]A8NCEKCFj&rgbZ3eH@2U
e\.]A+8!CN=ETY:Jf]A&X2.NBR<NHSuVZHAaiNO;M2Y`3kqQ(OgaASs?p$V<]A2M\%]A,$s[o&,p
7iV+Wj3tClug-!amZ@M-gd4%sKEi#mtE$l1B1Y=bmUd%9Kbhag<::m-@tm/dGJAF2QYP]A+<>
4nuMsGe,<pl&3RO`lWi26I*)ai-[?a$:_]Ah;oRLF)Y4u7Qe3rn)MEdaifJE!4d2=*A?,Ckl&
*4q=@M=qte^=Mg&Hg_bEKU!25bu11*KGgc%3)p1C-4m*PNLXIPAS;R.>Q."Qai!\j^jo@EZN
dPgo@)ZU[4m^.jZeR/jNb8NQrY1k0nmDC,`H]A[deFZ[li=M'fC#7KbhO@UHX3=5KR=9#6aK0
%T`2gF.@R-EQb8D_q%iu]A53Nr.qrs)m\gpA2'.Yo\FsVlPpPG)II>CqnJc!OB]A=4o^@3uMiD
:bd^[I@;Pu$tRDga#_lRd>,`HC2"7rdh6MA3H[3"#(_bm>=n?$;_73R2n1l9-s%.1KI!1eV!
bL#]AE_[j#]A$2"_e@1<u,T6jr]ARrdZgO!Y:Oe4Fj*,="KqT%IP:q6]AQ.4p-Pdt6up/Kj'WWj]A
j?7SXjSn-nVf(ZgGX`cl40_'V>4'M[ajcMrCuDS:A909d(DnATff0tFj?_L`i8)dOX=6iM7'
[cVDu]A..Z"GSp1(/Z,]A`43TQ.R,iM0?5kLq2>X2sXVPBOq091P`3QU!+X?Ej6k[Xt!GCut\T
$"!41]A7;k(O+V>BJ%doiC.BAp%p_[<Rl2(9YUJ/ql&-3g2$`[Fm6Yd_i?@s8k/EJnY%RWrZ/
'%+#-;sD0o<RDMW%3ej#t,7cHeHCJjl,NOu=_9n%3BU?=)LJ%F15(D!i$>/4;rh\hlG?Y/[7
1Ir%;/0T:WT+I`4e!&C*JU\HK]A$UqSk7Xk@n_#ri89^T#Q'g%7jB,;X<ABE2XJ[:9)(b&C=h
HP>Yg!=R2+#V;&oWD!?YpDOWF.qH#K9#%^rErE=H_^[6G5R5=i5@qWIZd%,:;pI`&g?lC6pT
c-h\$+T2KdK;$tOi,S^hLS*gmDoZ^p(q&t$3+<?)?"%oJ5!pG:,H08FIgQ/h*--E6f<`+?Us
ZhV>[1BL\.)IC4D;+TL0Rs-S'M]A<MIN1:q$JN&-I:p=)Y6V-/D-Wjpa6N+mgk_^^Z::rC;@r
6kfM9&'IEZ[tVRXK@/Q(W[gI53?_11?f'dM.I-8`*,3OW,iV;@)V&^XT?#OS;.<EMAIL>&f;%
b\Idlg7)VgXm3ZN7hlPZsbHR4e]Af6XujrVBM8]A\]ArANX0aDh^loA;+-eh'b7C$uIrgo67QF!
*oL43AFYmh0m,TqXqE.G3BOoU"$@OC*6_#BHiTr6MTj.^o.->BVN#67T06?FQKLYc]AnZNd<e
C"i#RG%@F)j^U7">IpY?N$n+IFi#a]A$P2[51!\P.hi$/jqSLF@%40>r\;/r;&_mS@(k)t]A%U
?VpQ;H%D#!iNp,hY$`jV#^<r?ha[ShNE/.Jn`rsM%WP0N\Q9bi<aDn+8(=R&*LTVe5#Q`9JN
bd_k;[@A]AmqqYPb5NJ#JehuZh_ab&`ZTg:aNhJrR`4F"c)7.]AfnrNE$(Y?B9Xb",BE4Apt$r
m(iF)Ps&K)&_OTRJ_p4nB@iN'ABh!G)N4rYg)HpHY3!:?Dq#/*7+s^c-N;+_1f'&cF%b/$e5
2q('rA.tAE$,N./QR0WT%9F.LH_uan:$8"4MAC!Q<'<\G_4$[_e_id"L9/qlM7*f>^i+uBho
]A%1Gs!)^;p[hmQa:.UO.V02nc$=.Q\V-cZmWWQFU&PFgXr]A=_7H%J/5gjkcomtplu4%rj"r5
%5I#hr<A>U8'>&@:HKed#D`J1'3E@X:!;KoGO'm6)BEE#0*"dC8P5;X@fQ&D(;/hop<<iIH?
aN^VfC;TaCtc[$53gd"fV/+O\^n75E[79pEum2T'<&?Mt-ERnZhG.,aZCA./5YKRt=9rm`':
e7'@0^NjMlkUR&qT4+;Qk5[Lm/JX1,P[qG9aVd=GRFCZV;2T/JXX>)Tfe-eEPn1GpYJ#OZRD
c&01*KflL'.#_+KXYCSY(e*Bj:%X]Ad+Cad'lTMPYmHlTUlH/PJ%ZKNn4S-!0[#``8$JnX%/C
R84JL>O:0u6+M*D?Ej&?;aCs-30qkdNm_-2'2n:gXPV"!06TmGK9:'2d73+a>WqOh/H8l2)%
9FGN,5^C5]AKn9aYWk`&j#Qep$@a>?E!SLdRQg(?3@<dtP\$IiAV/K]A3bM(B%b\GP_W]AMXd"2
EPLG$=g+QS7r?+dpG:AO(<X2\=m>$9.\tfj8hhh,#Cak[VH5_lWOL]AbM2RpU\'gLucbJ&i+n
tHa6"e^SEgGI8?GsS"LYIqSMbLFp?9e)#)#Dq$kcC!HL4:1(Ybp<dPc<C<m7SSksBW%$u+Xi
0$gXrRVip6i5p`HNJ%(Ne/J?q?ZC9QIL*Y#QN)k@L3g#ThGo^Vs%<N&JFiGbSS0cT$:HH)%N
A$$^lrIcCLJCS[kRHD#VsOB3,l_oR`2n$_QiRH[RYsGQ-uOk(*[kqCOpSZqg0toAN2b*KDLQ
R#-frhj!/(`m+'5Q`_!BP!;03!jn6'dW]A$A[4oE(JGYQaY\t>%UZd[D`EZNNCKm76G=A`jfg
pW^3t`p'/aMl!=/Y(r2!QnJ.4>qr2S/.,6M29I7GhCK`?hlVL#;@]AZn=tD!uCCc2H)I8.jH8
$##H6+URt\\dR_HkE@[=+h1sJ`#D&aD3O,9nZHD/rW,AAsGsqf6n>*hg\Xj9#p#PB5U^J(eY
#"qE0_TE`n;:-]AC#UB(Ne(cn76KMkY">f1^cYa'0-FB'Z#bsRZ/"f%5l8T\6HmP#2-Wo@Nlc
@=,i6u[5S]ACAWrl5jBI/\f-Z^0pWhr7>0)6?#bb]AX)6l`XVhQ.@#6lqh'R_7sZ>:ZiNkU8?3
JE4Ct*!a78?J1=U5s;,tB:F$SbJ7BOr,=R"'o,LYl1L?MdO'<tmc!fLi2Xp%`S@RE7OYFfj%
]AQ[jpLY]ACOG;Vis<bLIA6I,*"H84$m%%Ug@t**rJTZ\s2XH#ah./k0ma<WQfhqK'!MtfCT72
bNf)A-@R^/)Ok<<c@psL\!6,gPJ'@ouUWX9r0bCP6+[%#Sj7EKhi_A+t)%f?7e@QZNlZ_&EV
j7$tM`k?@_Q_DfooRm5G^,QHlXaF*9YHR3eQiD=Tg@bgdEIA^3f/t,\NR6o`htVmF;D;TNCA
!eFHX\?#UF'5;UZngabKRV>hq\i`QgGa(1C',$Gcgu2A;pb*_5bhP'(aHd=FUrFLl?!92_AJ
HnWX2C3ePDRi9"Apc^5YiC`_?A79:[hrd&7):9CT9CW'2AF0?h)W,m3^c`J?i+s(3-ETcgVA
>W7:LHO.p<oQB^5<@RqXpPD;@#'?4mV.u4\rJ:GN]AJfo=$h\QqbWB',lGU7ZE+TF1FX+KuUV
&^Bu:BVrXphRLGbYUDUZ)#(0dqn9;:]AmMgV9l%$+0lk@EsV=jh*I*g4=j"uAL:>8;97HNYYl
fE5r03W"([*"s#0c9F)a=BpQnDskbXGEFqPl=qO5YZHh#djtLU^T"jBp2R.$Z%LepsQ5a*!_
/+(%D#6:N_pUm)B&nR&$U=A*KEd1c%dVXgp(8_lD2`p4Kc8\Z]AgM6$'fGGi.;P]A]AM;l@]A>ro
)OFUH1$F7QOqNi6><22O:fN?$=+DgiPTsPm=uTG%`meJMfjU&EK&^]A]AHoIZjhNYj@_YL@<Ri
1FiCVE64C,FJ?&)j6Hf:^T1JJO+^4t'm<nF7+_OCXIp;@=@<';ufTSo_#QT_7\n$gE'rcGF:
XS;d!6f>eeN[!RsCM?u_rFo.C(h=%&;3M;[3EYTTn\#c(>B=S$+TG(DL[$hg,dZbq+)Ua"+8
l-I3l21Pd#U4L#PWb/=o(f47BLD>to[q4M.d^Rj:!fTFQ1&AK=eX]AR,Ml+YUe,psCCAu.'ee
:?Ll+U$AI:6<dAmIh51^BrBnOfhFr01cI$]A.(A)Y822OHNZV9=^P;QDP9ZV*^BUG.u,Zten5
gL'ac5PE,[!dOg]A3ofAlp-g/q<W,2\T&mnref16H^I^K<o<F/E:9KqNj_e+851+8Gr7C8eYg
#rEJ7"r/J=Q+A:S(O+F;.rK*d:B+-#i?O+2MqBYW*19aHG]A4@@lAuir'goCB"Osb3<1)^P#:
T\D@dD(M))'oT\Yj/ZX>R)fs:^/%FUh[:BXl\amWepAF,N5(Dm.;0'IQKHnHJm;-WHK\,)_b
nP&O7YY7.4+Wi1:\#rE&Q+bn(HE7gZ(j;iYdCh-C5-H?kP\bpOV)o_B"ecR"q,mq[ML1(W)3
3X[`O/AnS(R&gBLJp4oB?bFNXN"0.(mMb.H=uFo!TSUpKUnJ;`,n4,6q2d\q#*'IhmX"]A1VC
N.Bp4L`b^gFB9tU,ZC#1-e8C5&U4q-gl\PqQg,^mck]Ah03%O#-'tQR@10N?t_Ln]AfSi2Y/ru
VVojf'mk90M'G$]Ab]A":K_+Zche<OV_UA[Gc:SbC`R]AEC`B`Z"?=oLiFX6Gb87&!ZhGZ4:#'a
KJ.k@a6fcPSioE>mls;:R2I-.HmG;XYAL2ILA;#*h%b'Xs,P`9<JIuEbKC(5r+Za`3U!4<^O
/!77IeV3e%Gqldn&_^4W3gfT<nl\!EZ&QfakUCGAX5!Fd?j(KeabZ9VdPLqY+coXpb/4%[%"
*%dp]ATF'>ghhqi60:BHShTMccLn57)NkW>WKW&6l>l\%RcRiXbMcHnR&KBCW8B0!%MSH;"R>
c-5\o2l)5,IXSSV:QM_G5.`Y<Uk[f4cP80Crh!5L`t<d3;+-,`jp,qLJ?LNKo"k)KCCen8ge
4$6Lab$#7J^3&CbB0H/D[dOhbto4fbYuqD[JfpSbXk$Hr+D^QW!gHl5+K,,M4E/j`$b:JlLK
KjQA#?q_QbBg+#d3Q%%#4b8kc<q/d:0C=j.fnFTEAiVqN5cbI0SI6W\jU0$QNs-(6--t';+B
K&Dh4R#o'%pe+!gsO<r$R%cF]A]A/MO9S,K.7-*2tFbRIM?L/=46+omeJ;OM//LEDTEHLnZ?0U
"Fn2OoV$!cR(i[2/3"IbqSG:QgD&b(2?'B"7-E:)[4Te7\70m1HL\!m<1[_m5`(i#+-9jZYX
DTnnUpQQe+It2t013ceH9rrSAH6:4R@AA3i*7uTcP)Tlqq:j+b)mc&U:>$3Rj=,`9i%_i^0"
]ACXi@Cg*4j)EU*U^8AN^P\ol*gNCdfe1m93=(ca"9[V:O/P?Vd7[hIV97'XHdu_O2tjf8<bo
i`LViJIG))*ICt#L_B*UYSRiH*-uee"R-6'!GZk!f5.U7rM,6iaFt0XK?c-so_CF*WWpS$Pi
\qr.^!DB,-9fmoIi9%Ogb3/(qr!`?M*1GcR9X*&On@_g`:$/4.bn8Xb#WIA-*q$87s(3m"f$
?'=rhNpO.B1+CN9LbIH2"f$m3t7]AnWX<MUo=.Yksj!EdFT!`uLjafk'Jdc)*-Em/B9GV9Z&h
ip3YW%8a@t9T!BrhtK&;q6Nl-AKUE)X,2J:rMlX3(sE9I/kk9!W`n]AHJnCDELu\(%Y=U]ACLA
'>Yk$gBQB=CAnC;sI?c(S&g+b?:gnN.D8jAsUX/C3\'!n>VX-^eU2=WY;]AO,&&8Nlb<!I7t(
Qno?(:o:ic2h3*_-:[iejn=jY"E[^ZZ"TRFac!]A+fT/TY[S,CiK$p\f$G#'ZCS!NYJN0hO94
kXo2im]AQNV`R+i/Sr(j4JHub:E)Y6")=O6#1Q1P<55_$GUH$$FNWaOP':'mpR)BNb^19s,Zp
L:%ekh<ac`^!)"YbYri4Wu?h!H9cSDC7&5\eF:Y1dBZ4B""cBsp!eXM]A2K\ZAD3$hDI&r:N\
1R>5X-=-r4gB0&7;VUP+')10!b8Y3/*'JkQ0DTgMPW?us;^$cNW,F`MDWXbZ*bl#:kFJUTQ$
..eo'JN4?A6oQT7LMFrA)Bp,^XQGftoSirArq&cK,6m&*)&0hUW4J.V/&NB]A.0C`P5[63a?;
C*oYQcf/M\PqsJu?@`g_2=DDc#'c*.SiKkF*=_?=u@DDL'U:[c:7i[00)QE,0l_>Z[bcFZUL
=+a;!oBnofVQ]AS9^[28b4\&[#D5B+hX&.','.;h$(&>+>9:Elq@)CjpS1=38saMS[h6&]AF\:
P+KkY.2di`Q'I+piu+OCFR\O&:XjDtX_Yo#R*Q0))]A4OBaVn?3Z=I_Fk*.(NZr;]Ag*6<Xe_J
itD.fk,.'#%X6I%-_)GE8;ssK8'PW*R^KT5BmsZW]AQ&GA^i7R1B=\)RW[TCp5,,q,a$'+`*W
P<H@K/=n8VWhM7U+d-!:;`4hB9jeHj<#"D6qkdVc6r!Ka_'=Y[U9(:k%`"-naH.mPLVq`HJ,
qI_@>P:2WQ6S)L]A@3m@1PqfYT]AF,rcb-]A$!dF9B8*NG.X5Ej3m^JSeh$nEm>>;aC+$*"XPed
JDLl+TcsWg,l3$nkjT2ZIlAec"!.Y\JBh-HG-ZSr&3g35)Jh0A*6(oRbG>:?J)A(L]ASbK+AG
9RdUZ"SJdBe%Uj%qJ]Ab;NF,"`(TUe""5+@UT(+sfhmV"Xuu+[?=,-9qhsM7_R2o"=F>c?ZSY
S.Fcj-s9\gbVmK@2o_B2^[2l5m+dT[R7$SFJ95p*CEj@D]AnI_"os!)[m6E4:s0oSTAd&&HT=
ZZ^f73k_7*bR-<TuNqebC86<*%`ZE8-WtQGBJoB/<6O)2akb!dIPAZ@.5ai\0n(`CaN#]A'du
0MOXZAHk0Q]A6=L<'&Ik]A&#tqZ9h!<XOe.o):DcEo(HFlO$KWmpl2r/tHKn$^P\Gi:?EV*EE?
@%hm6g6gW\OG=HapV[ia(`<Rb'S,s;^MIcbEYUrVAc>+`O#9X,mB!99WAurLq6rulI-CT`qr
FZ#MsnAIM(PGX".X,4X[:-3i_43+6G@6CN@%ogbN4*0^U(.SWDWHZBC@3!X"=<2\icjCrdMR
_hN;>k/q-2IK=7oSqS.TH8e(Jk=b3s(lnkMRjX7P=t^c70,HHQCBI9nW[_>dGD]AaA3bk0Sqk
d!u9r\U@-cHU-\N)htO/a6q0gaL.0mb`d-ejnH$,5r!=);P_rA'sGI)H`VN]AfW"f)6@$>f6L
,H&ogG,P!D:a;%%%QDrBWO>ambId`5+_IqE3<=Hu!B>$Pl_S*$uS!dk(9,$I?*&=(,ipO9i)
)`m99iY#XFm(mW^u5qRrk.Q2^T)eUPjI4EMe[RG<k/!Q8b[:o!FFn.QZo$&D=he!]Ai#U+k<_
Lh_*i*lXV4:Wa7IH;:[&7&kPVm<]ABI\96m]AL6$NQg,)1*pHq<1aB"LG@=oI\Y(.CPGlq`"#g
'ihNP-NJ]ArR<bnR815[WKAq'SYk:/rL,MM;<o+k&$<Z1HQ%EhC;>cKdd!L\5&U63Yo\QlkIP
**"7,NLac$kd@%X_I$%o?49gO]A_D%+%+??*.kES%J;EcA^%Co-k6?ST,gWqWC#hJ5q^oje4#
KV:("pmR&q?6fk2"he"B7Es@TL@X/,d4O&JC05TT]Ar$;=`R^3X(_'le^_P$7X7*l_B`?s/.Y
#UZ\k;hS3btVpTB&2<>4[$:cXJD7$H_$.329PLCX&1E.euQXCq&+)jZZ/LoVkiNl6$f,[k`j
Y:Y8T_$noIr%$5+919Ph!lqpfm//n((RI,A\W?^T=&<=87$4("SF=H[$lC4X#T?.qjBH+Rp%
igJ1_RA;#F[MM8kRtK\B2FP6*XT$.GAA!l,""u:'4Mbr$M/^c9[+I:Z3r-5]APRXp#M+\.,R=
#4F.`5-<=*#QVW2:aF"q5IMV9F<A?[VXmGS480]A`6-^OF?+;gFO)gODiZRM@uV/qhk=A'E0L
.`U1jVhs\W'k^:RR[<6X%WZVSXLk'JK1=PTqGAA46oMUaI1>*j[/L01A7<j1EI]Aq""63*&Kq
dd=I"HR?gJ*L_OWpQFQFm-YQL\H?el6=UmJIc1$frh;lQe.sRToO/,!F"?aFa&CaS@Q=gm.R
nm.Y7P9M_MUaJ!>"Qs5M^^9PH'Pr>t%oF58EW_f_I#Ouf)VM=V[fERh$:KDX)&7c&$>"Om25
E'B7@p4X'(q9,7f-;hamd&gaHMefNgg`KBJ\\F#C"=;53KS,IHgc]AnYh'\5.!qePeNMeq8UE
?^>6r#C_8Kn?LOUGnOCRc!\JaF;8o,>s0jS]AiC/=*\W(!=_Uo!/ZcY,0pYDo^*1=Eo+dF3=>
8OT#`!-jfn3`IN*>oS&j)ejsd6VF#uD^q*J;e"/.X+'Ji:mSa0Ga]AdAKIInVPF7scIr2;k;]A
9\^ShG(^[_2MO2]Ag>;<R=<JU7cjIWXYW"fNTSNPB7fqkq'GWV#eZ(::.P,!oXp/ADA2O^LdI
\IdS2tgXi>,,\mD$hTCl#%J4kHf5d0'AIVrsi%W"VF(\?bL_Vj'Yn/"@U<LJgt>A)]AV`tF0r
'=)Wr3TI&b*X!U9#e\C>=\G41or6r>@Q<fH7$iH=4CP4dBrEDN1FU9Ls7sR`\TIXUFD%tG2.
hRqVrWL$Rp;6Xp:Lh$GK+8jUTdJVlr+3i)%iV&ckE==(#hgJ(A*8&<4RF#_PM]AhZ_tK:Y:\o
ANc^^Q<A/W7'pH)FY#4p(Vpg@bjNk$.))qCiR?K-\Taj%5%(/SaEmE(\[!4f^k2,i%lFgmp-
I\lJK-b:pM<%cB>2$W*((,1p:TQ;%JB'H50DVJL%E&dRg9cPf%%)Pi)7d(4:W`)/)J3Jc-_J
*L9dcNo$U27pm[$1@c`1tH?KLGP(mEF*P'J?-k1P@*f:DeHi,msNA*.I$/L3(W%R>6)g8[0<
Ah8q+$n(:P?Tp,!;QYHGD#Zl2B$L=7rjs5J8+([R7.9CW9FVpG/l?^q1A\qCC>7)gSKl@+6&
`SQK<1^hlc`aO6h-85ZfdenhH\LYX=<$%crjT>J!_.`V"Q^cEeLWfN!V.(XAoe[Tau/HE(ns
>%!?-V=t5+3(:6C6o>>2;'F`UTQYQ=S2q*gnOETM]AU!`g.I-`5UEj4E"inM5ljhl4$oa>e0(
H;_233Y1s39XV0#u-b5_A#Q@=lne;l\*[Y;c<+_So4bEeV%ce\)'U=<<VE(RdhQXPqabQ1--
A%&sTh#kuJ)Wj/=en7]A*A<Wl`?`gG\0>dHaU:7OY<lB1W4.DE"(4a]ANsW(FuqrN(W*'V25LK
mr.JD+K..me77M/O2Ao0$I'nfkIZ68Q.bV%VRBN&I-&cR>fDj/Qa*j<qku/cJtM+q$Wf11"&
O=n>eH?00+,n!QFMh2a;SjWeVPd(Vq/M7J0IR9q-s:!Ur]A@l`@S(M/\T&kG@3;*[c.%*4mch
fEru4A*;Kal/D6EfV9+8pO*gSd7>)C"=JP[.)`;iiCqC8HoCk'p#J/%%-eB2/gQo$2Q$p'CC
nl>2lihKE!@MAD,*9ZHdk@D9^ZYLm@=XT`'+*.]AOkeAG3cF>-*ZJ+HSB642h(Vf.'/X?kKio
'tR_%]A-G!c4HgY&:%^k34N`"s<;3]AoQX9@*a&<S@bu:G3gD-geB$R*Hp?OHR:k)VQ5oZd`+d
aCbjVq=sdCVT-9[.Lj-O^D0qfLK'A]AFNPi]A9CBpp"*B22`\-eVGQ,r_/qZ`j$d(E?Q[r3ubW
>mdNb7LGK=T6^bT?NSHmpi;_-SWt&h?]A$p2-j8a)$*c:[@+H9!FmT?>u_QNq^Q3V_AO$>fJt
#gYP_DaRG&W!>$?D`rS&M7D<)dXj)Y,5DsG&a\RgQN8'#7%2pS`e1m98KP<o$VKIf&BS*@MA
025'prm\4EGEbR,IQG)b'E/X4,imlW=NY9Pb5paMri\rWS(Y$cW(JVK.ZEFpKjPFjV9%(iN7
IK\]A!UfP9G=YRQQf`neu"=Ln3V&rFqQOctAUYY!O58!$9<q5]AJI,3OM<?HSOe:6-pPlL_"s,
.T#sP$l#qR193sZ.o*D'^E,.HPMEA?nqgL<3r4/KS&d/k]Am@j`G([rOo^(I(0J/nhQ'`4TMV
gGBbK:[TG-.<E'5PQ>]AhJX_inkK"U-DuWnB3>R'EL;_1M9'j&S\2BF-l1*A)c#ld0ur-^2%e
kDGm!$-RCEC6pa@'QF`P4E6@G5D:ORJrI'p#%=sF,je\PlE59l6K5MFM$at^-/;DN#c^*8j\
U81M+D&O['SeY7)W2bU3X*sP[%<`X(AGtJ)m]Apjem;d`oqoZa[6"L7pV'rsee-h::tC)0k^n
U36?4^M/%Rr\g'DA^o4*^+C2J[H3l-85,4;.A;hB"-G$EKmDh#C^R\.dI]A&<,T5%ffc]AiqmP
jqaV+@n>[gW1DgeYfd&>aUOsg8V?tql($SaW[Ul83*K_6`..J0^:lqK-HL9ri"60s<HmftRE
\(]AJa>e8-OPo4c<[;CM86^_!t"m+[4[*n67sh$Je"<F0u2B[_;C-\LfVpEUH^Y.k2'&*ck*U
`A]AFu/AS+06Uqi8X"N^%0kdRUfrO6O&hZYBUGdd)/#:EPY70HBYBJl;b\Q9qk+[Wh"^6*3X6
teUhV4la[8G..*\>QnUEE%CtrZZbfU@/tgXB\r;LoF2!h&_ltqBkK^Zon74s*Pa?)un9CBte
_B%jk#?&nlj0D$-tQ`I?#Y`bT=j\h)@<=e0lZAbr;<>Qro4gZ!MVmWGE`1mdi9U;Ci@4]A*ML
7q'7<Q)G!oAbjZgNkUGDC73+P_Jc(B[gTR7We&tb*Ma1E\^dbT=l[`Z]A,!&/mG>QXiVCZ4"+
OjG?JT1P]ADJcJB`&VnrEjChhPU;6SM'SP?V.p!WW-[A6-6>tC8h0V2*:EiD"ttVrf`MAr]AY\
ipEi>%3iTki55m?\8H&MHaF>c:s7u3T$e2fE3V#ZD`_='5d]AdTu+a%Ai4Oq6e48[CJe;s%hT
0ir;SYJbsO&Y%gj/IO^NS`a/-Mk?FRl=L13.BX/lKDS9P;69YZMl_R9K`UYpb/UAl8a-Y^Ei
M$NIEGRa):;e-grWPAKfue!gATC4&7jThf\e9/Ufa%E;d8TlWg7cUO,CR'E31ooucW7I).Js
7!ECDC1$m[J+4DLXOGCJi(3(Po!`Ms("&_ihF-nA"<\]A^NhCh=Lu(Ua4;hPdW<i);QNJkgpZ
Q5OgE"1VgmB(i9e?p<2,%q!0m.-Q%@`Q.$2S-f/+kZur*4q/>DIMJ^;jR#Y+Es!-7HU_=7jO
1(lESO/B,T=r4uiG#4B%P4ouH4>W;C@qHYHHhW\rXIgkP%*O<@Pfdt=]A+$CK&:$D]ADd@C=>.
Dri)!ip4FdZG@$J%MA3)p"VoqCn]A7kfs'u/c0$'pB@q3Sd<c<kjgC`h!=_gT1W7SEmu?dBKb
t4+)bk7qQ9L=RFP:ZDa$Ujn,Ii+6Ln1F958q.QWh!\pfloX#!SH9EW["-V2KgfG3\oDeOa&R
s+<l`qd3lC;_9i:G:YJ>M,(&)c#9q64@gl?B/@"OOKG2h`UUV7D:m5Gs2g"nP^-*H8ct(9oS
VhA+haR\E98_3$-'31D[>1Xq'3=rm?KRen-^2I8LEYMlgg(6]AX92_[]Af!,I.T2<S[1jQDB>Q
H@g=j!)==\S9M,iL'LC]A#@GNFLk[7LlA0aMEJ"&iM-;IO[J*DRhb).Q_h*-[SD1Io/@bc;NT
SLA1YP:iJ<cqKdWol+a`rc3`O`a#%hqra_pora-EQ:PY(O\m!ZLZ.cnCUI-^DUm]A64u3pG(L
&L0;"a^&QEL\D$uEF58^XWCTfR1lB0+G(7B8.B..[C^o+)8hStm&A!Hq(d2Hi8,Su,PTACZl
A?9RU1j2Qs%4*_;'B3T]A'9>W>]A`^U78l&Y&&1Sb;\\-?>s6cA^Hcj^EnV$UTkfl;]A."r"uFC
\%]A3Hd1!e4Lt&[bgqN1Q^S'@[a]A[=2<s*XO#J,A@dJc"VPG'&d/UCd/@MXn=tk%</P8``Y=e
fXNW3&m/#RE'dfQ0OK6UPRM.ERri"1@QFsA*<-Z\idZA?n%YmrE$VR)[3UNelN*Sh&Rr-e'(
PL9[C4bf/0+YuNS4Kp@rd*-);r1=ScTDK_HR%mjh#`$VQb>E11?Jc2/(!;2>8VhKGibEhoRh
`BnORO[jr7XVXRUA]AA^SBqS+>snICM'lBU9T%BXpVRKhPWnV:-!li,>X&Q7BhpJNhMk.RcEJ
-gTn4pB85o7FjtrfBQTC(Bh^+RbU>:l5an*5^WJjKD:NM(:.c-hY2lf%]An3I?XBs=0$`5dE6
A7p(_iN*54/]ADqT^E,dFJ*$@H(58;gZP;3O8!rJ[g!sYNm?Bq8u$9)WMeTlYObNmR7=?l)"_
pA(IffVpV`RcLmRoC<-)Ma&MB:n)M"5<QKOd>KWoI\+8g$[(80<T$2ui^/.R$r$4^SJE4egD
VoIf4b%&OA.^q2B$o1ZJM8A9A5H\aTNbGE0YV:pda/*LHo$%@Qd?L?Yl%[9f"B`-Gt5P83*A
1=EcT-;TP\Me3TnoVZal6AW^PVu^a7#9,+G^%,aq96PsO1595<@#WI$XJck0nsoQ=oL0*A"Q
$.!tffsR2(\c"DG4MUX&YFBr-As':Ya9SWW%XlH9S)nRRrj"3sMBG(PGY,,Tn=5L:eONSR+p
9S3_JXGH&1PPV98+e.l^r,Cp-et,kc8m60./_8Yd45g)N@/QXr!th8nC(ekViq9.p+:&2(!1
iDWoD,!u7N1$q3ePqWTj<VdFu_0/Z7\2`5(7$@P#;ntlQJZq&"Ckkn@+jVTKM<i`k7^m7i)H
V!c"KLE@>ZPpr(U;a'eQ,q2^'p@!7QcdD!\;2F4,uLmh,sQY.Hd(8*j(dr:gH;2C`_,n&BBj
O$_rel[r[1iZ4Wb.-(I2N8:I"h>'r5L6(im.X4eUg%&LNET5Sl6;,^)G[@Jb[Zj'7&IY*I;S
H5kslT',%F&,>,EM\WYhTUc]AZ`l3@V@+UcbZu!18U".K*6:+sX1mPR0!;>I`>JE!Q*nr=/]A"
'ZM[RG\R3'62\q:XTc9YfZUCrpYoSO!NAf-8#`nfC;,[VhjDFSMi>K.kOJHGjA<4dM1abiA'
;]AMh*#:^:Z#bo97+#:-C;1MS0`g+apdKu^;En*#&seHn7n%$XRc8>O[GRU"![)ID4fjB8N68
$j&?MOm7lY;?[$`hg>DID,42@NfbME;F?T"'Jq:f/QH:\gC'k.<!<./70dG14J[`H"q)>jEE
MNB<;eW*'$;oHko-2IWR??$1pH@/G8*"NFi9M&%nN6WC>#@>r1(Eq53LKIj14e$Z)<Zh*q/7
,P=1S7^M^iUFcoLXn'te/E-RUq)i"(Bs9aIWR\8JD=%J3Wj"2A/eLj/K6gY,JAYAW3Sl&!3"
<gP3:.QB%h7cEgO_N[q!Y=!AU$BWWp:T)pU:g@V?1BPV$RJ=C+Rgec)&Kuog(PuE8POfh$Cd
ebpGQMZ+2s8dN[3WjOf)41iJ6$crs'2<tl^`'Gn5hik*+n5PT"JJ=J7>;XRdRTUYjDXj'N2f
BA.EL+t681?1eMY`"L$a9t:P)q``nLK9a'%Z.iAMD$J`%XTbRN\&o[@nA%B"ZJ8h8q\23ZCH
Q&Tj.#-r!S2dU.>amXR2V?cTneb`]AZMP`b]A^IS-*Hedi4qXFN0ec#d\Y@36\nTIDM8s0ttSZ
GH]A?ae(;26SnOsl6&BM#Cl,oH&?XWo]Ap8QC6=\!gksC_9H0($2rBFMNRT?_>\8fi"e9NTeZF
o@Ab<rU4?>]AD(LIlf?6n;iGB2a;(i"T`NoNZ?Z_.XtYjCXj6KA5^Y!j@8Q\DXA9K[;.l3n"!
=+_GUVF>\$rEq7(1lI^7HW;@)$lA79U&=oMP]AG$5OcY8kTNt;EuaUrN+p:Hc.cVbf7n'6'u`
A\f;]AYhJP*"*h<`_Fud^,*E\;k[&l0;=:5T40NCY%Rqn>^Q@YCQj4j#5sM*VN_=Sf3)BTAg?
<JH>;.5R#GpmgTMbnq)XQDh8hXO:WGdr=1NL_QRj,SJsIGf?1$)`.<aDEh9T$=rs_g?',!rH
J`(UcGGquL#&Bi+M-#'r4d.7-.e:j(p<A"4p?R]A,ENN$8jYQ_-O^1X.O/f4ZM=!G'FAA;D$J
plYe1)BQm*=jTefq"h:1*Q,o4IETT7(fME4'T=B!G>I8>nHc^Ya0PP`rLBL3(gjkg5lL.G#^
]Ai9;8)d76o2\@-<pp6bh3%:b^>!'b@Bn052bI^I>e;oNsg%u4B)<pUU*8q9k!;s_l6]A)-XfP
U#;k`:i+&?-17D"QQ"$&LLe$YU!g_rb+U:eLJ?.jn7'^0q3\AfY@6PWdL[u7fdbs(Lb7Y't4
OL%VA;_T^JT-[R.S"RpeZ,([rK)_s(*2i<t`,O3]A2DJY_=JXbYM#+<P"=_LT>:0OJ!Fm"Ji0
?tCdO;re^TpXq>1k6abT$`&BkQ]Ar>Y3"s5R;Y[?/rA!9:`cn(,9KX=b1]A7'+abUPe[al2FR7
8:@&D-3-S@]AX^m/>U.>O?"3c5kKJ.L1t=3cX)<EMAIL>]A.]AQ.Q<\8`;8!erN<4'Kr>^
OO*23%(kY,poH67>>n_m?"e>Hhe)"P'#$p\:dFWS/I\XpCBd$mD1r>b,/4t(n[QG)tQRMt(,
t41prnG(\&JSci8Hb1d#m))"O\]AY_^\Nb*$,G11Bk/0!p=tW]Ad+^\W/RKk_UKk=G`E;'C]A5V
0pK*mB%[n$@coJS?2PjCVEZuigNP^j^W>aT9*a\)^,?b^<"jY#IF1B<K_j#G.o,B;r[_Kr3l
a5/59nNo$KW^*dDI7e=++%n4MHp@*Y#M:[R&b,._aqUIVk[>.2S'$:TI8e#N3'aiU5HEJ]A#S
@C]APOmk6]AQg:(b&$m*!*07d`Y-[/L;*7UDWMmJ/3*Bo_n:]AsLRl\iJ5fGdi9WY7M(FL20B)m
'HK\a`N,r#b4gY-$;#G18SB+1o[T?<so4-T55Egd2CIDN:g0U$Y@;AXg[/BUr^6_#s=p0+X5
oX3+"8]AGd]AQ^A]Als7&1FSH4RpU9B%q&6>An0cg-^CZsMIutcnK#)JtN+KKFXc9hlhn%4QBka
6GjaJ)8T6n;V]Ah#EL.^;A.-L9n0Q:s`8*c8+KD##)'rE00iW3VGjgSVa41anbEE/_:$o^6"a
<h]AVK5M/c`1V3.,*B<l_`#j0?+ruQ*UVa!XFu5Ug;.,4EVigpB"X,7B0PDLD`=tV'^U<BWLP
qc'?rOsrs4aFAT>,`D@2Jk2fT@C#0k81OMf5,JiAA!//->[2Y.S2p3t$QJO$&^.3C%sjh8A?
A2h#$9>"N;#DI\lA%<,_%,JF%8NZ3:06s*07^66,\JE".W"E<!9\-1_(AA:YW?B%:=;d[@?_
,7+8jZf`8UuJXTP]AEmG!oCP/@W1"p2d*)XXJ,[NJ!)5G'N>H<Uu)dU`[jj7\]AnbC)X`%IS0_
"a(-lHc@#/CW-4CW2r^TS5LOl$rQ(Gkir\43*]Ae1ISkmqat,ULmaX6hFEf9h0P00JmUC1'"6
rr$3OR]AI;<IdD4LWq>Jn=.X4u,!,a]AN9RW?1f>?CpA))O?!3.1*b*=@^YEr-Gt<G6RFde#)=
C/t"%I5"8JoDqd3N:agdE:t7b=!s7rAP1mFs0$Jn`sO-WXn.q(%K<;#^8h&`/1_g"R31^L7D
^q=hGl>B]A:]AJ\C:ncVeYh:7c91\o(h(n+H2kh2-pC&SX,uq%[chV7qjn(0cV]AcnDO[2d_KJs
'!)aV:kTf;@4`t9WrkO<V;]A"0+0Dh3NN-6"'7ZO:7(Qn$Roq<mp'&o^).@&-8Z0mNfF=.pA=
0<ekQ_4&YpA##3:sl.*o%j2uI'eVHWW?n4Z\'7\+de1,pmFo'AC^_KG\F0'`"f8)m`#NV_e5
f(&Y`9tH(JC79/H$W_e`#'.IJKJTjZ#7f4eg"]AeKh)Z<]AHa$Z=8=HL\,;$O66^9/?2bTuhV@
j;E1`*Q"<*oCrH^=VblP-lVbc&2O+r[_75C.2Wa\0A:KD-;BL>eigGi8u[l`arfXXJSq3@Vr
J[/"r11%c*^[*LReZL>4.<QT#]A&U,I#YRu_=iaDh3Ff\\dmI#-PPBK^:a'Wl8$0IB*+SC^,"
7AXj%#>6Cgts-"\7Jl05\t!FY2KCgg3'Ro:#AhX!'6//$kM]ANa?PH#&Wm(K?3dfK0l"U2Dfk
/b6*qR5o'4OZ=!c/C9VCaof/GIjY;)O&SrL;F^bsAP3[mfDIbfF1Vch&QkIk_:,=SZf\i6<\
\ufrq%J-2eOXCE,37%X[-?L$`'al6.Uct2A+LR2pHV+k[,$!^Y)u%'mALkAeH]A\'6/%L:9h0
]AF=;[^Vh4'8d(XF7R^Z>[;rAolW%J:c0XrJHudm=V^5!?he4S`j\:jJc;QD,=]AT5AhBQ9qE8
YUo2FSkQee0P?+R-Yo\iDem5lC)Vs3J9%'Wm^@fg7+$N&amB_V\pMTl/(Umj:B:^W+[a!+?G
gY<Z!b0J&">mG.^hW+2B=tK)hHJl2;R,fdnKD>R9Y#tMaQK-H14k^m`M'@B9j^&<p7fp@4M-
gVIIQ&6Ei(-$6tcU>1k7r5J=N8jNs=!!/AgP\f/2sa>\#C*#2L=N7aPb?);R<C\j,U1a?GYK
BSRO5(TF0''Kms*G4@\P2ta-<^.4c6L[i)sPL3/%Pp+K!Ih^A?k;ai6ip*2j1;D%Dqh.JW/6
WbHOJl<,?Ip%'2ZZo:%oqjorR.6_+N0fQpVt#ULFr*XjLm%X!=3Y8>dT\ZC8V=a@1[$O(lRo
CDHG;)_oh6!622@no>^RW([+LHUJiKRRtBt_>Di\&oOI;R$eHiJ#]A$]A(?g,n2pm%<d-YYQ:.
C10'SS9!#c]AY('^oFC+_aVmf-IUWBDJ*P?Z[&:&lm!IuemXZ4ab=3/ShmXAfS3Fka"j<-+Ye
*ZY0W*M?$Q)ZCt;3B`iIp(14&&K.M;UQO&)%]A)pSoS<g3&>&-Eo4Rn5HL`pBoH<`rTb+HUa8
^32bNd2]ABgJCqt]A\,Yg1ZSbS=*`-duW')6(n\L"-JubH3+>f9R6"2sg+))+r;(#lmm5GO%jA
8g3!H;d5<i0_:e=k")g2eR9@3pShL6o7M/H@/]A^I5[8N!1.jr\6p)oJ@+g)iedh$5ZSa]AV+H
<I&i-XQdHK>k`o;+aR<q<8l%uXpr(KCQYF/1."LF$:gpBs9,fQ^!H(*-l6N-WMs&%,V9K*QQ
O@WM-k2LV">'$S"=jIZh22E2cqL,*>=3!tJ@6bPG#Zt9Ib4k-PP6a<J:,6W**UN,6b".Z%j'
DYT2GqK0B:heB,dGhf6'#gaHY=R>m&45HDU=u#isp/Fq,@:$Vhfukk_l5cpa7(S94<'D-=k>
.q.M;%G"km+]A'-%-PDIc//0U,M;m1O`<uW#h=t./-Y1agQGeA!'k["TTh9+_RH2HATk]Ao[()
[r=pPQqcHKn!g-7CS/0Ic0]Aj;BJEqZIZt6HrDTS#89LcsDuNqH*erjqB'7RR1>XF>Eo6`/ge
CWIVF>3<eV;/mfEe/<!SKn,\\kTk'G8L*J8S.)_MVN45nm`);%<e.Lam@_)YALeZktkeTb54
lW]AoUT&@eojp&h/>dCqg1n$MU#2I4A1A7=AD4eOIjbfMI#nL]A5CTYuE&TL^gW\+r27=D%[5n
+b7p;0oJpEjkH]A9$('M[l+ERS"e'iK%NL1cN/QG,(uVmD/H^8EX--X3"Gr,g=50`sWLa4G6X
$MOXd,:,?f*e_B(fSVX!?$s(j9\UZ*lQ^,H"uP,F_l0-`D#s\Wk6&ACCfNcFW>F%KO%:idD`
JY2m5_/OS!R6=Ep%=g0FnO]AQS\t-Ptl$lDn;capJr2)`_!cO?WUsdTc9[Z]A@UEQXB&*"?-UR
Sj1c0/%a%@e^uNNPlhd@]A_NXAdQ^Z^Y5t"r`+Z*4bH2.hj&B.[:A2#0IXDqIgQ^i1CL;[5s[
&n2QhDC6u[o&n'jXFKOM%;c^iO/tYVUpjiBe%ECUbmmf-MoLB\L:s?mUY8"7RbGLTm#tQmT'
D,.<EMAIL>\q!cArh96^Z@#ln[m(OT%o6,8CpI5?IZ'/JNBC!(>RtdkaMVd^_[!bcX!!b
jm9&\>.rj`9]A1W6^o+A@Wcjg8^om0A]A&3f=3:\%YK5G!tXuWXTi`a^`\;e&B,10klg)N.lVm
/uI/l(mKB$!<-C+oFV*Znq!e]A<d6fT*-XHR6&Wb$^N%K)Oo,aBtiV&^d(/=J-\D)_uiK]AO@F
:"7iYP@m'Hm[Fu7u5??cU+/]AcC%2UGT^pp_mERe3l^4rgB009YIhe9aR1B8h=Kp/6O0;h&Wd
KT8T@n@767>:m&0hY#(.%uf63=U;)M>=LkWl-$T+Ug\hO2qSrMe,F1WrgN*1taQ4BK<B2f:K
^n/7/BEI#d*Hm2`Aa2n45]A!/h`OKU^TuW-,JK2EJnq+WLFu+tpdj??&f-`N@*/[SVR^:]AU!b
lb#Z!n=Kc7d[oe*]AU\<LLaDH<eD"W'@D6>d*.ZjP\"Ln=022KC;VtA'4e"(L7BQ/%I8YC=4r
J_De^IU+J^Zm1Yj/'$!%"N+YrEJ[)OH1J*$m/c;!AaCn"O`L%r>pa%^F9Y:*n[lhF\jq$_6e
+SLtFL5?5AGbg%Ei[7*q.8htGP`eJ,i9Y1'm!N-l<qh(>ZkYq*52AGK1Lg9%/o6]AKTSICrLh
(Y&Q2o/]ARj0n:9SJ8j.M?@!sN-/NjWIWg5`]A72^(hdg+ENm>I#gOarCnVP_rKQ^;]AY>OiH(n
%!JBmWcS^5dOcL<[bMb[mS.0^DI;\YKB2S6,+qAF1Fpmj>L(i2K7Z.XD$McEG;^RJOFTY4No
aK2Ha"A(sPeL(Z*cj^&+4W6VV`>uOEe[^cDXne?K[QdGG%U:oMp:`X98h4`GXoc)VB!+'UI\
@+,X`%'bFRIp1/_uM,idrN_]A-V,DqOuu:r.O8\m3B/s5h'uiP`-)B$Y1bGoZ,\8)GI7Rj&ma
OqrJAnkLY:s6>^BKn*[[S!HqLiNK7_qX$`QKh)9T]Aj@V,4mkMh-G.$g>#+CKprs4W"L=arsZ
fBi\Xb1^J6&V!1ln5f3ot3V!1\-0f65IYFdlBT`@&gm/$:BEkM[+Hr@dE'hn8o20B#OmT&mN
p`jqfuB$f$e$0c996ps>EWQq_6_ZS@0Y4WWfK,15D)CED8QY&):,0c'^BO,JKU1_`_#.C]AYd
_94$I+X[86/T.e=R:@Alj;L"'cOt5b1X[k#1qIeLTfND4!*KfpE+:(6^?eV;5"PAX6aAA@]A>
>VR2,PUC1Te96g%33D)dkF0[MDAmE\s&j5?Ddr1SqWFO\XgS7hnu+<E>3*)fpm-nQ"a[\EF`
l.HonsotPaH5'9$n:/f!TFoj\<O*h_fd=(P-oeSVd<Mi0T=]AkupPV#+Tq8ajY8:M:Vgf;1CS
q]AVag=Cjr%kE2[K0MD<m$ZhDLK>nr-4rc)E'tBnWGr%@W%tT*j/\N_6=X*5Go.uYnA-u?;UG
IJ[>ma-+a'q&PTe/IAEc2ILO'K@`-UqKOI&nm@U"MYI(Q(bknAD<WW)lL7HiJt"_:3:D$>T6
r96Vg,/VM;s/G^ANK4@[%fG!@Ib'8j:=@e`2sT#Wr!4l*o[:=&T-_h3J(jV<E`)WWYbi;PE4
s+,Z&Fnh"T<S&HYhYLeRXPp8Z5]AGs6]AjK)\foek?\!=!/;e+nfK3;h%?/:d70sKc1;K,;P1^
Z,%5(6p2,EZ2,T(hJu%@?MG,ot&@-\0be(JcXeZ9VhD7@B63?DUp!dGC2[6"8h6&mp8@9HIC
@bb*G:l?"B5Y_dmj+j=qmNUoFi;6L=s*#)C_(9d$"S?HYeW+]A'S=nZ*"q=i)WuI,:\FQXXgV
/(.JpsRQ1pFB3Q#06[:K/Q53WY_j=7_c++%f':qJ,CmoRGoM9pU8C^!C">c+P2#ghr@9BkOP
VD?-&?:`fN`hH!rCJhSreOfo\[AZe;XH)K6$ETZM%#i'@\VAs.`"VnDId0W`UHH0^0>_7RjU
=nJMK]AL[>e4a5RfA>K>BQXqba`0@H^28h9W!F41"2cakJZ]A^DhISr-Vd3U3TG]A_4'[A,TM=O
CpWo&Bm8o0"?Vf!?XS/0*q(G#(s7rX*-Y)QBrG$PkJkGHK\%"`DDYs/,^.c0/C(hL',@"q;*
5nCl0gIDaDsO_n1N+m!P/i5+D#FtSA@^c;7gE#TY[i1tOfa^s9rqJT[;'I5*D2#f[!lX1T$'
,rpnaH]AS@%s+LUS/P):O<FWVMM,N[<1eam/I1-Nmo!+c#UDW]A>E75*IuAAI!38#M[P[_IV_@
pTjBVrMD+[6c.mSA&Ujf!!AqXLoh1"m?h',rCrcB^K&jP9U*F^\&O4g5dm\"%\9-m@\i,=DO
M)q,ipq%<sq70g*RKBCj1=%S,*4g7fEJ#\roE&:oA"UL+(#EoAH%8/_]A"&+m2?QT6o!2>H^q
%XXQec&\3)2MR6hBmKk48ZV^e3UX+/m]AY&7_P6r?jd"9nbT=6!1:X<sZO<"_5#HQ.hX;(55F
Nq+]AR6SG+4H&k@&dO!H1oHS##VtZ<nkN"C7d2dPNuB3+qIWTh6$M8ZCp*.&B8:92?D2u>.N3
%O^fV"3f+EVY>*BXb"3/sRA>G=]A@Z3tV]A*Bn#g&cZ9]A%Pj]AE<Z-!8Lhm^]APhF$RRbPV"t5qJ
llR]Aa1*YL6F0ER?Jb1nlIBi;AT6rNSrBOU9K[lsCD+!i,9!H/%CMc(\'P+_l4TM),q>M9>0G
@tu?LM92,;]Ai0Kl&D7noC5Y[O_GOOl;WlrNQ$2*JpiXWpQcRcp?)BQ'<liY:DmKF35iU_?9(
A]A_dK^/%WY;-O+IPg!Iq+TjW]ASliR2D>77m2fSU#Ah0:l![WFrPE2OjMA]A5ZqO9A0O(_F./.
g9<]ARk^Mjp!\\BoL>>#ln+rAnNJaqkF*h..PBIOd;:MBkOS7\%9J*%L!RXcEMJ>I8<naKmgD
:j!IMA3f/&7O'ilg]A$&roTjucZ0:n?aYrtgom/4t-/$Gc>X<UP#:-tcB_NQT,1rl2gSm8Okp
RJ<pf+ON']A\1d+NYRKLT<?;'h*P'@lSKPZhfhGFKc^Uh%lJ@^p*gj.fMIRNnT!-M5";=uN`<
8LoA0<1Q(8/If#Gi3SYb<]A]AD'BeerTdG-e6,UShet@LeeA#PQqGBNhBj*+!LK/BZkH<i9/]A,
7ZsdQdU(gFH@muL;KOHfDda[8d[q*DBME@sFPu\>8PVH(uV@HHIB\Qr$![TLAeV6p*1>cj9?
54V7RGehhX[fY`5C)9l3&BCXfJFD<o>khk&<^@9."[7ZSGbub)&XKeY[bC,]ADc&)]A0'a99[5
ts5lQ_^)&UYe\`_.dW"c^k"sZi3D4\Ge8jg5!)]ATe%*DE-PX^<@F6_0G:Cgo?8+\^g"^;Y=U
l*AjU$\E?+9A0APr0/.<?Flh%&,S\d3NX[u_<%T>W&Q@5?NTS3rDn>6)diEF_O>c*ABN:D*0
5,mL]Arusc9">:bXAf^g6R^+h>A:^JN+L92]Ai=is%LhJ>1P^e<-Y4&HTP^;l89L:Pg?[Gr+J@
Sch8?"!S5eU/C;.gGn&4lG,$o96DLm5+g77k/H</p$sb?:NQPD*O+R?(-^Oj^G;q6uCT?_k"
/^@`$EMb[:OH%<ns':@o-:\('Nf7[XV1.DgBqBgW4pH6I+l8VMc$XcYs4nu8F^XD2#]A\14f<
<)\1t1.Gi+D*4>(A9BU0NeUFG"_9j0upVe'YXY@>fhGu&@$R;SFDDoH"tM)-<j\#G)g(eh)4
[!:'5j[/MkGI&'^iS?I9P9AfU;d-l]A-;QpNqk:J]A4%h6?W:6m)9q;A!\%Ip$HEP2SVdUddX$
F\VD+sr2nJ%hYcBU?/"SN@A?7'M*1VF)pnso7XSY#76Fm;7pH.qM@+D'?EWF6!MXm?C=K;Y-
r]Ac`$,$^<O8:78TC?O7_JN/BWZhqG8$!!43AS[0*IUl_2=mb_/E@s<l#%k>]ANDd\=sbM!0gp
<TfGAOBKqQ+BM\20!@nqikn&_QR&B!Dm@Bl`;6'3GPZ%7;+%KH1/QH@`u9MeROLDXTVPD.H`
&<O`u(orjP2b8_JujnJL/qj2LI;;K$^!-8=c[*9X5E!]AgVm5dj=jG*_(l7,\&/P+S85Js;W.
]AK3[L!uS#k)?Y*On]A6QF6lF9%%g4sO7jksRK6$#/=BriYX%iIX&Kc$$ILNFrY,lZJD-]AN+fk
4ZPQ=Wn)UfF&-$b,0s]A&0g9AehC90BiipRggPX^D(XI9!R/*++2YdHI0dGkCT@M&o_^2p&[K
F_Y)LD`g:$hCX$>9"-gRV42O$o>8'[[pAtt3O]AF6=1[(mXU*D98'PX.G@:3h%*Z;\3J2kKqP
]A-gR"3gsmj.$j<72,'aeHU5e#`VYnNJ=:cIn%31>0>-bdfkijS\):3c6E,5Qi2k1lKnLPK>.
"-#HR,lZ$$`,`"*@"\IX1"d[@F.Z"n7^)'hJ&p^XT@'/./$/a[YOra\`-c?^;mMYb@Lei0"-
kAKd/Dl(]Aa$I:6"GatlM>QZ0[s27q"q<j%P'W75Y!*0f+J`VZ5qfh\;BQZ0aM)s7/Ks'=mOi
PS(1XP'[OU93jM'5)=#0Wj1[_+UDaDM!+eQ,j18ZdV3[93gOlt+ikn$mmSUuV'J^h1h+I;Y>
?8c>`?-M?S9#3:W/P0@\??XU+]A;,N?o,#N+Z5%8GE/8PG?Nd3IT;GJ6,TMrCKjrOD'NQM0XE
e9M)9P]A\Slrs8\FPsO%V9;hIXI,UPq6sGoB9u/C^S*aS):UEI8/N'K"fNP^jQuP"(M!"6h^g
U"F^r?r8EAT)9`0(UJFW'<jK=)U'TEdikO<J5<^3"6da7:C"''kEe/cI1J2UfQ6_T:Y3VYZ<
#)X?-]AcY4_cYB%[A[rn,QMthUl>8("@[I*Pb!h(0$7H#TK>E[dYl/4n<?`1t4+YB<>LmSnN_
[`cCae:sH[/$@OFUL#l1b3geB(89h+qk+)$h&:p,p^a%DQKhk;LrfY(t%%lQe'rFp5L2Y3e+
CGOepE$FN8i:d_k\/3.t)D5_nV,jVRFE=Q&jQa4X!)&\qIbg!h'Yt;ht-]AXC\AhDJA)EVOb.
(/m<(=RVG]AL_g5\oSBoI6YC0`Zs57*;6)8cS//44^0lR9^X[N;MVZ2<4/GM?HG(14":[<.al
M(B3</?4>0kB$McM4Muc&5?a?nd#Hl&458-7,bkNG[^=KV2_r6;K@bt+GI1K[<=JMt_HfN)a
'rK<o#?Rq/Z,Z1;Fh$_fT2kPTY%u"f\8oB:-L;uG_`d94q+8`oZ94E4Zi)!F/`4`q4DLWSE&
OA?eMh<`LplNh2GCJ.+(aM/)b*ObH>YdfO_!0)<+=NakFS5b@Yr_5j[![-UnW@=s"U)j2A%;
?P5>(8V>4LkY\+s^Kl=rY.^,naINpA3fU19n9Y00c$3Di,J%kPPcuHjUoOcl<!Hp[,[b7ZXh
kn$o!ke7K;;fomIrKTdmA+Q>R`:"h*@T4<SqNH8^@'pAKjCX3b2:1eV/<g&Ft81P[pE163"@
uAp+iu0><pIN[.<.LX%il8pO5,&DbTqgX;Ei6#D^P@%&::-N&-N:Dnbf]A4=WK(/QO'8\1V7$
_?f]A;7*Y<-+e(qgJ/F&`B0XCc<K2uQW[AK/@NtNcJ@r4$=WQes^]A>Ek[F2rYPZW)G']AtI8)N
-ppL4Ts;;loW+VGT/lFm]A.O04h%0Ib9YW+[r07Y7b!?"lG2_[I&oqbI$_HUUa:O7NMu3MmR9
KH::U@RH[G)`o$+X;&%&M=I.e7+8;.O:]AbgD7jX243=E6\eHFQdOdOF[#Gd%8jBF\?/&dnq$
NS'm4T+/4Z[PC"Sp\TC57*EG:"=p[^T6Yu%E?h%$N@Xm@5F++mf/(5++"A&m5d0bU.>r(&CU
C-k1jX2S(;es"PT5,\qF#aCBm&]A`oOtFl;0I&,=a.l@b:G<1eiU1K$JHP(rd([lKh79Z3nR`
fft$.%E6$brn0M"MSAEj7%E1rT*8QXB'C#EQNS,NKtuMhZ$_$0+:QDjf'6Qf'8l@RF($^SV5
=?TT-!mQXXFQ_!9he_42sjq*s-ohSSD6cT8K5`JR3i@kkXO:0$:*#O$ip<l!Gd1:h7)]A9DsO
acQ++cmF.="Z<YV:o&^:lS<b#l?SpmDLA7'E"/MOq^SV!(pU>g.]AA0NK>>.Y#Z4o=7%iR@;W
`GYr4tu8nKI@"\9_1;@<]A9dYC_VbZl[h2Yn\WFq?]AN+(f^,C)0Im($Olo'9UknhE8/\kTAeg
)UXJ9I%b7;2'LE$-NO=Jq[)`cne`8T<P2Ce#7#E"TVR)d73PAI2PhI7p_e#$>PHnn79d:UXB
I0`jsW-n(in[s+fTQ8/*cfrr]A/c2S$J!H`B>G92j!tS9S8Y'-n+f,DKBS^'1kNuZF"Dm)p<T
[VaKtJ1%;onrOlnm]A5hjn6@Bg/k%&L8pY\t00qCon:Q.iZO`%dP&E#/D4<fEm5mm#$YW!!Tf
j@PZm@Ok]A"-NSU<r)pQj<CGlFIjYYOFq:3rI.`esC(WICOWi0.LbPfF-dB(e<5u6[=p_(jMR
o41/.[$4Pc36LiqV?:Z5jZ-=Tuj?MeASY3X(`o2[E/3JZ'cbRNl1[s@NkqR*pX!HbZ2p9l+%
,gQC:.9TD9`*q?]AMna97c$cV,fS?SU0Padk8)CfF!R?)=:Z\PsYsX+L[*J;!c.#GTSR`-n%^
BBh[#CeHmu\@%l5XGgHA8cd7$jjjBBq>m[W_QUj;)h%<$e"2j1$cOSV"$^OlVCb`ApN4-BVJ
+4Lrg!:al"CK)O:=Mu@a@0BP#301Vq2bUAB[.]A5%p;mb\7>U<8oX(g!C6Z%5[JYQFH@X%+kL
ob;[eb-H;WJ<s,Z2-15f1LaSY00^EL^3kLT,9kk\0LVWue!o(tne?>0#:rV:70&dK%g]A)=83
^;3Gq"f:5Tfe!+lY`=N`%[-DX8/"o\Q4X02!,/#AThp-Q,S"L@+.JIBC,;'WT['?pc%i2TV-
Ml'3\dt=)<ep,8lCAjhMH8GXT,a<`09MH1UtZm$<q;q&7g;m]A)]A"@=M*XZ.D#[%=6rVbq]AZ"
JI6SZlcL%]AR8aF,Zu2nrA>S@?op2,"PFqClk_lSlDgs,@2t3-^,.<CJi_Vd\6BP[HkPOW\g@
L)<:>k?3;B:#L$=3X'R-3ZjfO,a86#@j[f6["b6,\`R'\#Er4nTj1Yd3W<e]AR<h7]Aer)^S"K
uDuo"=r\S)Z?W+eQ<MlD0i9B(.Xf)`1CS!0;UB#bq!`-:)-/e+_:REa#pD;'Q/jmHkGqh;5'
DnCVqrWlM,:E`%'<.A$lt_Kcn'.>kL>rrUc.e=MH?LNdF@g9*[o/ma>RFU5`">tD/HC7(fPl
9^AB_6t(HIaC!*Y:4$)>G@<<16//_*NtS,6oRgHTq/r0tRIGK+Ns24jn@X]Am!S@r=JGPXiFK
-)HT_UhI^mS1K`ur_\+kI&E:&,K"(8HXC9B^d*l$q'8/Of_'$FD"\@ZJL^O9#")TIWe!C"T:
&Wh:Oh0SUMc=CleQ4qDp-$KCVKg2iHa4#6damgNu?="R%;JCI8V3b^)IYqGb'H8"21Z4#9%P
;T(6YN(`ANke%aspgNR1g;-pf&a9.?E1V2on^PVMSmk3q,;KO\"8K^0FSbiq\m,[9R3LXIYh
Tp$WkK@_^H?^lpZcDnn5+%Deh.tgZfDTSb>CiL-ZCi'MQOU-&#c+82d\R7l;/l>@q<th``BG
724"e8si9NNjn2AeF*B@GrAKI<#-X=F9__kh[h_Zq=+?`8`0sd#-7qA`UTr2W8;f6a)BfKW3
f#^:(Ss]A?2:-apJ?pp;,:X(>%(Y8Z^&Ag8FfE*G:kG'K#d*0rgj"+B.f/26r.;ZDP2.HSm$d
SM5Hl)3;q^p4>M89S%Fs9F@UeOI8(XmO*-p!F7r_[d7_,-'8o_<lRMQ9UUCE/<-bQRU>)V0?
QY"XFB/*s2(Nf"E_fPFlBC71di+j?*T(S^BZ^O?`+ir<:`I%[!+Wn--l[JP<q\b5]AcF3rpL*
5A-XDiQAQ'nkRT0uKJOYj_Ju0n8hhJ%A`Y8O`<&%UMqR;3oY#9aI/SFh\r'`J7->*kqo[@et
'459)A&?AIT<,76'@_Zf^ZA^rgk6.cU>4*&(JEVZ'lqDmX1%jITfEB)eR)\LhM,o_//M>1,l
b_fM=`KrlToZoq<qt:OE[aPduS'Ce^M#4?RIJ\kWT8*q,amng,MT:a&<ZV$JDcK[N;qg7t#'
l=$iWO1>!1`W8@PfZUEGHF'n_DQfDKMaKI.4spDWiei"b6(R:/4GI;Z+\q;5n/-Jpu4H3-fR
h*_/DA!iu;n4+BcZ[mZSC63A@C+5/(3RqN/O:8Fd?G$WH=(DW$mRf60=mYf=)s&<.tc2G*Ur
[:-lZnKC;c.Y0/cIMKk=heZ?CV5EN$N!6(@a:QN$Z+2+]Af3fHJpNrdASD$VCfYpnK$.4R#Nh
V3WuHro]A:W6smQV)G:t=fW1Td;NcbfLTZ5+u`Cd&-,)AkS_,?kgHn^EfeK3FDm7`%tfbUfpQ
BKoE%++2s4^e$1fm0frZ/291Kg+*T"EIWsOPh(cG`.$o\'T2YmjU_C20&tE6s2?8]AYF/Rp)C
EQ%1NE@@GACt]A2jD;S=IuIt^OB@OB#a#PP$e?S^a#dHl"?<7fS5,uMd<8=DtY"sdBnnj6Xdb
s@3omm9:jt>6\NEdSWo=K!]AF0]A(3SK'3Frs2+rE;0eBiatSHrHBM]A<H>4l9[fgo&:n:;-)sE
<Eqc$>mDF)rmW+3p,u@q\-U%-7$!Z1Y2AN'/HF":R+kra6=]AW<#R*S1*iK0r[:>'f7q=GCEC
"d<T/.\*f/Z\7d_\mBrp1>]Af7;(jI*5n\M<N"<1Cc7B`N1R_"M_9VBhl4p^N'ar`hEc>RsKG
i'5&$4f/S`")O<S<3V@rg,jZ[m;#pU+HQr=8'<?M+tjeT8ZiBf3+^QP[lC_D_grf#MPbuDf4
aVNOl,7^gFRkHi?2kV0G"TFpdhG:W#*a,K7d":=[%J1,+`o$U)WRg0V>Ijrh3&Z(JbS09AkM
o,^F-EbSEfANWEuriic27O+UU+T,L=G*$1o[DcHuF/p9Rs%lXgu<3C6dA5<3N0:#X+SSu:$p
uOTu&-F@$Io+]AZJH3s$6URdYYaDL7cpoTFR*SI$1q'Tb?OJ@\Bo]A=?-"k05=&+JDk@+CqfB4
.PY7k9#1Z*j%.9=[gJjHu9bgn3Q.OinNBi\<))9/dYpuS5'DIiSQfFFUoJct=+Z@5j1c"H7p
YoYlJQf#?]AHo:r-h"oF'8`b[1mGdr>3gV7pH[$-Dr5NY\^XgMW;%q`58Q([/XL5<<"3-/)%U
.$mfQ=W""oj(np,>%H.%L'^BeucljNtXSV&pE)-p"[a+nbTpU'W&=lIjY]AP`n76J,`8JhV<a
]A*UmDtAH,tb/A&o:V0dX-P+Q%<mhY;\U=!L2&ILU[r(!;W2/c6o_p0dg0(U_c8=cT\=2j]A)-
B3kgQ>rLnG;[iViRHtC1gj#]A=NbkuDP&ts4+>*X2>IJsD,RO-8`W[oZqM#lnfbNL!c:5KqK4
C4do"2dOYIb;pL?_A\7Qucm'n9!chs]A]AIX->27p#@4cVV4<aI/[!qoD4;+2'fVaZ5YrkWQNJ
U##(*9'%!)?t[=A`K=Ea5Hj`>8Ma[-I:cZ%XN]AJ.bZnT":ZVHa*)ZfW*A5LPGYBr[i2"E,-H
q9M-K0QdjrMasSK$VNZ!6s*higD?1.7f(clRp6m(;Yj:(F+JDU1hedsZ[Lk&^$+?Q!.s+6!B
hn[&QcrEui$p*rot;25=<MO]A7Mk>u<u@>VTjV2*F2`:84EpB(iB#<oPbF1F]A$dg:M6qgE#'/
r1aX#^2Q=/*/gDZi6.T/_)nlNbb0MBEU_=An@dgn]Ae.Z]Ag_.Qd2P^0DXdrINBZu5S^I\_'P7
cK6o5cI)5e4pqn`"sT@uZoPheCR=70&GW^nXE&@#la!6EpGATTi7UrF`jXf?$]AY6qYf(0aO'
b)=4g6$-]A^73KNNfkd&#n=CN9B+4h`m^7k#qA<%]An^,:#4>OXr:?3Vi"Gl!+=8d:fC_(Nboj
ED,-'2H5_p9Z*X**dO7:,6NN199]AP^NJS`Y'5=%pER#]ANiVb3ItBCXl;Q<T@31<oS9+?Y4&7
1r7&n>1)Q2FbPYc%U2HF^in<O&HdJ\kj:<2*dq%gRE<VH]AliU\XLV<DmLY9NR<b;0ZI8:np=
GW`c9D\D2\d1@G&-31MU^/<P6E'\4JQ-/i:FL[0bs8g]A8598:D0X)b;LPOI85%HTL%`H>=i-
V]ASf@7dg,l[c#^0(ePI/cs)]A:8P?m@I5>%oLSB#/)lC?d+S;T>#$B.q7cPME?ad$j;?ItQ;M
dhPK-D.3^lkT:?&$"=?9O"#KIPd\$W7[RXA>^Z<LXU[6;Z;J()GCt>IZFmaHSIDG27_k`sA?
fb'WMP/8s#$,XMK::mS!VR[/q1U`jCb]A:d?4pe<Y`:22@)Or,n,7iF`qO.5%)ob38-3'rAr?
?qbVD)@.E<qI9uK)dQh><N]AbM3ku-6E>\N$7qoV3#Pnap2+T]A<SHiAo@M!umB`R7P-^;5OiD
5A]A?KAe+4c/'\ZE?S@17c/E&,(bA@QA&dBKc]A9<OLm$CZcJh4Z(eASgqpTCRd#(\e.=m$2`C
#1jZmW]A/u<X.p%*![BdF#*l4FTUQec$[XXq;:l-=)n"Are8;?WGHS%\Nn)l`SpXUqq&H`"r+
\Y*\:q^E-"pOBPomdX,UmfM*07'oT'N6@N9EfZG>'r%FCM/8MjKM:Gb1N&E3$W/YFI^tO*Y:
)1Z\W1*c&BYib0LLdlN7lIo`#*MZPP39ckPWX$s.#/Men;,XIGX^(lhN[3o/uKRSeY3KK/@N
3[j*L>!89'Vr:(QtO?;o$DhdGXatWm%7a&;jD'HtB1@'Z/G'g4HIJ[(-?Klu<9Dt3N=kqc9o
^$Mr>3YL*dHX_X"ppUXS$7Eu<Ad:H#-<EMAIL>:*dO6H1e(*2%638hV=
W3b$<U:A!:[EK#:`&IIMTmI#uaT!(1-CGYYu?NlH/.ZQVVdA(iItqi'XQ)^KU4iqbJbFDa)f
gR@@(0,[t>NYUhDFenQ[iR$Q5-2m-;h_a\$Z0-U+[MnroiceOV4,1+/D.J0)r2D3+8PYfm'E
P5gd7+J6=omZEa^&)<9#2"UY9o6JdM!;pa;Z9@gaD3Wl!_t^&b'om7CFS<jCtW/)DNco,hKd
,U"#WA<c>?&=JVur\h;W\p?mo?<hHo@@B@/0YG@6_gBXBZj2]A_MZZ\95jm:h!e/.)7-.tpi,
C0:%qO6qCp<d.Or%dh"Z!_1c:;_5Q:sV#coV)Np@2g37_QGB;f6B6T(hD3??;pgWYf!G__jt
s^e*D6ha-3<r\lc2XAB=58IK#-M?4a3:YrDu#$Q)QNDmjF%#9bY82Vrao,kC-J=30!A>@NK5
fau6V&ukiIHUQq-6V0OUcl;5Lj/WjWGDk3Fc!T/rXnqZ_g3>F:&M;8.m%,01h.1b;Ij!<XVl
6#kptCMWTE;.Tia%fKn0+:7X9=g\6uM/V/)M3R'4r&_gWF^K'fk*Oh\!^BQWPn_ET^'mH>5f
k:k+E_i]A+Bk",ij9bJF!#=JXK?UY\.IOdi;mJ`r>m@4Db&M/R\7+J2hb?MOt]A[!EJ0b)<,o%
f(4D[#LBA.(cGgT+Oa`&f1sP;1AtKTt#bSMu'(jD*cXqOO.<:&d[5N]Ar*(8SERkH=OaTZG7.
`l=*37V-@fKGhk%`'TunGtd49kbkO.3@9/cc+nBjpZG@b=Wp7)Cr4l4bYBKDY_83n(@Q.,Uj
1@E-7iPS&K+>;B`>lUXR#^kiHc7bbFoJdtnb)'%a,?'Fd-WK/u,sK#fXR.UE7oBm.OS;V'2Q
_4,*W`+)>f?I=0gLQ#5;*3'pZ.#:EkX8+4`,9+,qWT#<]AeZE:X4?<B_PXSbD>'P\;WS&LM_&
Rn7TgDJ_tZ[W?"ON;h,\P]A)DlpPnRE')VC`\LhY%lDg+A%KUFGIqg>5(jNcsun)G5K_/nHe4
/S0Jpug9@.r756+WK"]Af[sD67]AYoCA47^nRC`"do*VeLL^*E4JjuH(qORF:^Xss]A*Z"8EgXr
$6O8b5qgl,b+hD2n/%Aa)c/1S)gM/u>lP,V#?K%17&2g`*b5inmqH'm"pD`+a9"Y3G1!ob`8
D1$KVe,ZDf!c>5%6mr5=P!>C\C,PQA0J\[d`ERZEs3<Y>!d\_:mf+G[oE_L\Dg;La/\gJ_4r
ZI*UM]A*,I@A5b6r;\t$3/;E'es_(%ZU%UC'"V*AN*f+,:]A/^NYpJ3QNbXE,Y3/kIIPi79HNP
iHF;E>aNc]A_`2iC)e>-Ye-;9nqLWXfYN^/8-1L[]A?+i1u!E)GYmII520!67HgZ<]A<mZ:a1D^
N=?<?_]AlXk'(ua*tq7;_rmZ!o,s(B&/C7\5%n4J<:ip]AjZg'A[9E@8;K%(Q\_cZX]AtF-Z5;k
"*M"9AV3n.l>0"&;Yef6NtTBO$o9rD0/'kbE:IgFkmHL.<1&-D>!OoL.AoWW=STBhE`Z[S6n
'?^0gS'bb)%BWMg]Ad>).VA@[V-?LqFl*A?p7aE]A^95C(ua2>`k`D.YL;85oFgA.M7(Ug`5%$
V9EhY16ul9aGM[,\&jnNN1!&OGU,;D!@cl_0QilqM7N]AJfQkO9n69Mckrr!93?U:S.+(N9%(
&;;n0o_V+[MqMLa]Aeh_b>-I9iKkGHKp?S7@U4##`']Af2a`#Lfep[J<D7).VT6CMJq5aJ=K@9
H)=Eckb<ilP(/3!%3_(Mo0?:KtT07p`HgDlSB#PPg^5G:Z]A^PFgEJECL1rK@SMsMrF!+9)hs
Ts/;U:X&5e!2-q+)5Wd=>S\R3ZZ6E\V_UC"Ema(G96:[LC<Z4ED;gQf0$l<`?lB=1Xf(3%Wr
Djb)dk4dWP]AT.0#,R!>--oZ)=@?sSjCG47pM3F&sP6WNuIL4"H0C,dj:<YHtP(7;W+VNTo!'
!$Olp#6Kj)GceY._rB$uKs1CG\M*'Xs=bWHUcQVL%/nM%II]ATo:sPS5Z/japPLa_TEaZG.E!
E`@i*T?o'mV=7H.7+*L0q'!TRJHqM!sRjNmVp1Bt)%pAZB/^"QG<.8PfTJ%`b"AI$7!*V+G3
I#J?m*aY)@ARb_^]A4[_]A21gH)ZJgO5:eXpI4Q&4bkECRij*RTm&%F866eo<!^$S`&d>E1;G*
#Xbc3dndJQ)P]ArmT_Q"3:dN@R$Up-fb\l^mU323#Mb;p:&Y]AM1Y[c`$b3i8kFK0KYl;@kG,D
='d%r\[ob#^&#Z\V6Qag>9L'2CkNJC5rD4[^"ss9b)%m+R^J>b^bCjUSB5_?kN2QGq#upW0U
?Iim]A:CSlfd\82<@%,CW9$5>Z4H%P%;f_TW\4JR^h7bWK[P0O)>nq.X63a<CMLf;M3!,FR''
,Ep!jQ4hE?qWm.W8^iPc!K;d0X'(fH[FF:jO\bcuNWi9.C6T5'MI+\(b$<9)D%8$@ljo/jEk
uJUHF&Ssml8AGF,[dp<Ae>[?K"hlS<*`)12a66QQ)O/^:Y:T\?G$*<!98E);4FMg^s:1^RpQ
;Os0b,d=,?Fg7/FYb4l0*]A3O$DDWpUa"%3[*@/4;Fl7BLB@abYSq7t/NfLN#:b=9`ZqN+V5N
$G;\sc60\@"0l>!a44uPM/T,<9/F&>X`ki'daT5:cW<P`S>2eO1u/,`^V7m,2&7K8O3`b^7`
8P";B[U+8$M!YRHqp%i<.tNgtL32\ZWbO,@UXYEQ@*fdDXJ)HDe6BUkN9DB=LZ4_\+3-.&io
R?t@hUlp.]Af05S9TK>ro`nQoG"hj6/^;Ps1tk$b8]AE-=tE]A(0\?C-ugp^apNB?'l<+Tg0A0T
Nodsc:*]A:eSO%<#u:-K2a!X#B\F>l6[sf6W6,/X&`25>F6:G,^B=PF0&Q?gdK>uX2D5UCUSQ
=C;!TRf3;arA9RM%r5=\iTdsJm6Df3<eUd[eX)&9%D%X'A3^\t<i@0^`Y8HW46/34iZ%S:Gm
rkL6`<f-`fI^d2cNjGn@T'`/MqoP4*;U^>1[2N\14&8fL:;t@hVYp.<E*_6J'B<rgZ)!08&U
8('53ZNCls@DPc9WC3kA(IWO/D5e+WDloUH`DjTS9XLhRDHp-;r%P61Mpf!=+f4nm.!MWlHG
Rn@`:_BKaU[EBtKhf%X6AM/N!Di$:)?*aN<P!::/N="sd\U8D=*5^!-O`6rnWdGq%-Z+SS:)
).tcK6^'!0/MYHK5Ige[L2^P<U9$=K%K@"8V)`H0>/s#Tec$1&n]A5kBCOrslI8-&Rt,NOr79
66U!Vh5p+&H]A[6M@/<2b)+9LUVK#XXIepCU,+!ZK=.?i]AOC/3lT^hbq!CU]As0c2;#2+m_i01
g^+aT>aoQ+21bbh%idgY7]ADDi[a-bq,ZC1haoVG0U#GD\&^%$:#_hBa\b[5j;P+/l\0f+Up,
UZr(7lqppJu&!ID8@4?'AYe[emno_19(-C>%<T7(euQ>1^[I!+j97o.rCcPpD?]A7c*%^A46d
-PMD?,W'<AsrsE$XF-6PP\@]Ae^qu\-"ptiK>8;MJH?q?S&@%*;mY0LuggXu,e19X4]A.lIaFl
]A(g)hA3U6Y_,;oLT^J]A.7QhWe`-;^>:jY]A?hIT:dF,+1J-PQ'>\k<L7YN8R2JnK\^bnP'M'L
m;-QcK*iA)'Ai%uE0%ulJQ!foG#[EQLqVC9UIKNB^"DANlJX)VOhULB)-l4qeDUC0oFD0a<<
.KZ\1@/P3'<_T=JLe#Mab7PDVp<O@OdNMr*%EAXcisAN&^cRHM^mF2a=BEKEaHgnu&O+/T$5
k*s=m$:i+geW$X"NQ4nCOs>Ym@#4P]AsAgrLrJB:cV^IWj5)3:r34(HG46Yc.bn3J<f:@#]A8Y
InB[8oT61i#nR>HI).g[=[,.mg(S9[m;6<]A#]A7[;T?ZdP#I6C@[9)*u.9kOl/Ig"=[pkbfKA
*5":k3"B`(B19M3X7]As"oouQl-0"`YJJuR1'VV#cq:<#X<g]A6SP,l;IC>,(Z>P$GW!c&QX!'
kfF3#);(@Km(H,FfeFPiP^%[&7laGtd(`^X_B;Z-M]AAY#$G>dV'cVIerl4%9/%K.+0P)(p?u
&Rc#h:r,_.&.;iR.]AF0i?\GR;dVEdLI72h<\2/5Y:[d]Ag>r%,We.3_QQ+oR>>[%R5P+Rjm2*
#q,3"4s_'B[D]A>?Si(<4"^+V/FQi/,>t&<iJ0$4es':hr>,u(2]A,^f*JEq.3fBa[K^VQRVID
KBKL<?S>7-TLr[e!gdJBiLCZQ9)ao<&<l(NAqa@B`6X_RbhK)j+6c7RbdPHB/`D8tRTQJ1V.
B)3@^4^kMI'jmpPe(44*me56H"MSr1hGO:H5s`$S`'3p%TBtPeM:h%AQa.XAGOj_]AoMjT(25
bZ!fUG5(9l&T&bLZZ1Z:RWU(o;a)%^_nWV!Gm=PB:X]Al3r,..<8/!]APSD&9j=e/(@_9AhC4'
G03r$En5^_jF":[8+)CBUHu\tUARmM>kA)8Lf(I^Eq-4&T_'h38f!7L@>9f=5;8;1H$.]AoRE
dT[Bc]ADM'^)8N%u*`.)ojA]AN8Q/:%j!Ke>Sq6[cCon8*&%BDXcC4go-Pa7pb&m`_#I-U3DeA
-pVHt5SQbJ8[A9fCke%K[KR0)7bjD6s),.LVF#W[V+b7WZ<C8-8HdLYo+4);VhlV#Qjn?I1b
Itr+=CSBZY^]AXE>59LDaSFG/A1LeqT21dTFN'E.:)"#Xqg2s,<mL$&HN1GC#o-hUs+dD>g/Z
#Yc=NoE4pT,eULUL!r:$SilBt#=Q1+Mmb7p>Dqt?@G-+D=,1-N+P.ech;iHY)$0(g4JP9c>*
4o;'e`h@0;nA/J24jlNV6E8#DFhb+#mU/t9+0#"T"2PFh+i1bT#LnqOoQ8eV58iYC#Md"noW
SrPie'uoc0&\JJu4Gn6W[TJ9n+Wg\'qQ%45Zt8L4;+g*F;u8I')E3c0H*#X.UX_d9/R\PiQN
4`K9]Ab_&G<>)h(CdkSPsgV3jiB_$a_r8@HEYFETRF+AG]ASq[F5UKg!)9HeqG)A$QB)L\KDlD
lO:+DD\*5n(nlMLX!237fB-[mX`YL19f=<5>s*b<2Sr:OV!p&FFq9&Q'ki%W&Q=2@-_u16:%
\np0iT14S:ZMZe^4<]AJ;(RNfA'b?$ZM,5+*>6Y^78G"p#?b(iHdgl1+9i0B1k6;Fdj9G7-$M
@K9_^9?cFnEmL>g0cWJI//el6Am]A,87"@=kO!^u8YsP-OS*LK0"E/o7X-YR]A!i8^7(n&<OPm
=5bHE,+`4r_7Kg_:M581I0'-Qa:RfH,r;8%/qomq;7bfH-/r"*Cs<K[#rS3'nY!@28XWeh\D
7ca)WskCQ.W1a.S]A"4:)2)b3S`)bMkZU\O?o<R\<47L-,bSAq4h1E5Km>E&%DJpfG8rT\oQ,
g:F@h4g/M4B3E<QJTZnH'Os17!W[GBe7M`=jf:]A!'+?&n:'Ob*V[%MRS-Dt*>K%.,KZQdq[S
dLF?TmC#]AGJNcQJ%majEk"3j$q;X25[IUAu%ndJZcFkaAK?g:eQT;H;8_"#+L#QgL'P$GV]A2
9+#ZLKu&T[0J6i4/KG<AoNT/_Ut$ornZ"CfKpKpKf7jRK9*9Wc<8Qe-LS0C5:CY.cb:1b<c<
)<t`jNS&9PWmj<=6IT<hNRq=9?\UlcG:QDBVHTDm&`RMNjh[F5/!7=ZN3MfGf^3IR(EPX%Za
`rkI53%Ha"Cf=m26[_&]A7!W8t.n6E47k@_+Re`U(Y3>WL:SmNf82r?B&m)VhM]A?1T*@IO0*Q
cT7hB=>34?\n]A/#,.fSHAl(3Z+3T.fiL7Y9Ii<%82ANNh^$SK;q;s('*[]A.2P#>X=uP%di.3
t\miqXN%D#?n?HQicVV#Y'>lfMU_?.G3H[>>]A(L$I@D>-pZpnml[d>cJEFpbI&fgtU"e9OXH
R[%p_RUYJFQPDD3r0c/$rOjT@s8:9.4Rr>6-%>Td?_6/:pL?;8^VBL<a*N9dL)+%Ns2-;SrS
EFT5<$Tp>la\KFQl<q*1cG?+'n-e"i^N3g>sFUqk#(9jL_KMJYkcbhr[mD*qA/!g26r]Apogm
X-;sZ1:Y<2+D]A`=m<V$0137'4;k497VP6(f&(]A"$r^:%Xa6bp\2q\.=ar3t,s/h#!N*TIeoT
+)<J'6)$k8Y6[r??AVdAG2cdN3)$"Jj,%gIT16"(Z))eP6[*#.*g8CD7ia5jU]AK2NVYhi$!&
"a>Y^c)iEQ'm^4gLa>PK!5rrR\`NbD5Dpm8k#7EF=iZBtB_cF"+$ma!hfG@'"548m8*XJNEM
bJe!Zo]Aq/P/P2pX1qX!0JQq%J\-6c$>Mp:,"`Alqi"ig+DLl$M(AN@gPUHr`g=*FB"haA@^5
J9n8r[,NrW<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="212"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="303" width="375" height="212"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="ABS01"/>
<Widget widgetName="report0"/>
<Widget widgetName="report2"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="515"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab11"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA1_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList/>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList/>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320@UNS$#%8!"TSV[>c0#r]Ae87?S*()4<]AX`BXPM&c_e^<sf&i8SJ8f
n]A#QMTlQM7'lE,rW7-guhsh@XIgT"19j#4K_8%G.7F*H]Af;'HUc)$HH/^`K_LlOV)0MAl[Y"
(ET[a*OPo7GB2nCL>bestm07EpoF4Y//$3lipDY`Pjt]As[UENNq+UZ]Ab5AC"TXFlq.'FnTs:
efEnLi-TH6O&[uaZoBp`VeEFg79@C&,tb[K"]ACtUD9M/"ero.Bp:^%*T`<%8%GDM;=5iVPWR
#.jc4@O\YIqWmO3:D43AT*NcIY#_k$ZJlZf$LLr8pDJ$08:^GcCZX`M>a,a@3m*`cn22A-3k
&JMsk%Q'_b!RY@*i.:)$$HA$t+`>l!@&$6"NbTTb3i[=%l_rB3XeT&i[>nuBeN0^Q<9pL6;6
>IE.nekkgOQIF"^ipRY'r"hS._7j~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="429"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="86" width="375" height="429"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="kjsm03"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="kjsm03"/>
<WidgetID widgetID="d126d6a1-7422-49ee-9d87-45f784368fb4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,0,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="11" s="2">
<O>
<![CDATA[tgyw_khfx_zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=B5 + "得分排名"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="4" cs="6" s="3">
<O t="DSColumn">
<Attributes dsName="para_sx" columnName="ZBMC"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[ZBID]]></CNAME>
<Compare op="0">
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[xzjgywshs_20230820170229]]></O>
</Parameter>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?VNeP3L]A@C"s_QC5q84LWZ&QXVio^6o2l^l/9!JVqbuXJg6!20iM%Z!%6OSA.D=t-$e3#P<
,RML4Y"BVVtZU'J^;!;\VVh*ruP-NB"9<rpmehrUR,!5JUI(F^G[RmlK5_fDg02s#S6YH@sZ
N&-ueen@'X?6(lbijmTc<VC.Be1d23+Ys70fTtdKA9F\gV(65Y(B.2a4F_<TSbq:G*HKp,0.
nli2+(E=sa4V,>OgFh`RHMkJ2m7'O@c+^R_5UR-?Y<`TNR!GBrG->&k:XC7p;=@f*,NGGmfu
>8m$ns38sm9HUoO-P*i&2:JVE6fY"Gc$B1mM/dKr_T"9Lk[Bjgq/gK+*tO%]Ajs580PSiT8V*
U3<!,4$d/Dk^qQ7lV6\8G*!]Aa?([eI1T`$/:83"#2^tWc_>Rb-?BWA/^)b8IY_ADK54F&Gl2
nQD[EfG$B/9H5C?]Ad0af%e42)_Wng&!]A'+]An\/V,`.CP9_=3SdEW_;GOVMWiHH3i]Aj+_WY@S
penKj3h@6@8K>kioPU\ZQI_;&>U\Vn11@drZ)t=`lep.?7;+0&bG9eA4-1Q>_Y`ZUB)%C)$W
#le"UKRsfEM:L?pem(d+n5"04_^Dt)kV=uB'0<5jL1MWF0sAEm:R#,0*I*j'A.'Y<=)]Ale$b
K^3G50TZ)imn$Ul^C#*WLcL3P`(CVLl.0).&6gm0eSqu_GQ#3Bb.<cRc\*m*GJX&,SKFYPV.
2>.UUqR>'7%s#KdRj"Z/1K"_G:s,Z!Jln^PPlOHlTUmM`N!2,FApDUl8>Aqu&Y5tZ3nc92+5
T<1%Y[V9aGN*aZgU#jH%(28D5OE^5.PfOg:[YTp:sb[8D>.kqqIi#OlXYLTT>jm?9X?spS0r
o)[bJ&i1$%ZTVRJfgAbhYHGUct7IYk^$Oo`i7H\MXLp!Y')q(npf8.f^7i>2KhJRgF<VX@r#
Vo'.BO&u0HRZiUd6.Y7]A:ksGgr2!l!gk=c5g`$ql%X)X\+JG9L&"U:g-o@&RWPXs0;e@*ZSm
NrT5\GhEL!^@6]A=:8d8X6rdVEH`_)9.iki<KSXU>$kU6NBBZ&cEd7t!4t:n<Wo"hiMLjEdef
3639$Z0HnEI$kqD?F`$1%?#We95<9$[YHY'10]A;d4?efHbDdXrfL$>l3`Q:t5&aG3#RX;iFt
q:ON]AmECqsLPfWFtT^Lcnkj)Fb&[C[[kIF.&&!V8iB`c&_!#6j2Ja>umt'cBR=9$%rFTs!`H
j:`nlCn8n4e1dU@?!adBS(b\tfW;AqC.FCTI;Q&FI]ATm)]A%r_F<DoeS*Jurr/&g>d?c3b7>h
X/Hk;HJ(B84NRQ9R"4[pSl)nQLDHAj=oTq5JOV;IcM95jTGmE)<S)FHsUtLbCs'%!"8D&'0g
,Tm/VUWhBU'"o"$(Z$/1376i#OLc3QojUnZ93i`6jnj8'kJob+!*\Z.\EG+,D'?8^QC=2f/J
^NO.m4dalgB$a,s&sD)c@GgSISWUci\YcCZ&T[lsc>\@PPKCfIn%?:;U-X!GIQ5O*Kq@)*g&
Kpl+f077.5GTLeM-['Zbo:<EMAIL>!=o$6MG9Tn'KVN<&W!H&fj#KE`N8(($5-_uInJN^
1!`8Mr$"m*.@q#E>,Hi8rl.YKcMT@4@G.'OI-fp[0i>rMMNkH/#T6V,V9Lo?:,;6@1[jZt&C
+rHg1L(,<f"YYcPCbPiZBVig@`ul)bo6Es2,7$r*G$gRBp.Kg8/fO)+r#7^\qYLT4J`fmI]A"
sog0s>QYJ6Z8SMW.m.ledFX+]A*4jk._e]A>3G6l5-a8&huCNG>PMC6r@1Em+JS@7-C:b.(bAb
UO`0]AW:Bb&n;b\'KqWkGF+*(F3Bk@#o>H8E9";:sj^DAl=59j[\)F6"G!6RsV\L-RTj:T=K#
.n7:8n2<0\Yt]A,%,PsCYn"\3>4*s:(I,1HIs!sR:4Vb:)Uu\&m!ocES[N[Lu_`=f@a6F9?#.
M`%T`#80fG=dtlZ<NBoNCc]A"?XF*\X6o"J2d?,O8?AZ^?GrYaFe3U&=GWKoag#qNTiE&V.^B
PsEf`n&pKA++uM9LJ[R<3BAbDMLZ?n(eW-\)aaM@\XeMW*_<<k$GuXB<m0i^Q[)k!`\(d#`S
biN%qg*5M<IWVbnh5=m9)^7ZRr_aBa?-qVdGLRCeG]AqZ.t$!usAB*N%'kQF]AZN#B/\aG>&Mj
Vq%unM@&Vml4C!p==%;N7_9%h_#MmR#K#koIRA_J.>D'F\dZY4G09I22o8a/Ae6smLiJC73(
!NrnRbdds,O>,c^V>382uLW)TWh%,8#Z;l:['l[^8",OJ30;V+IrB[(_qZHpC:&0\;l-39pu
oV@kdn%-=eYEFKEX=<,)CP"a8_\E`D$-&Io!.tj0TeM;HM_'oO(=%ho`$2!afD-K'3[$>,5>
(;+k_2kR>p=.WIDWLk]AXKbWFnU3SG5p7ShGc^;63$ERLJBBb="&aGID0^gXF_^mlQ/8K#I+9
Mt]At4$iNF;51bO4`d<97o1./egsQpL"#dOsl.\GB-,O/n0-HP/RW`hq\H6!thi4nX7X9WqAN
,6SjMpe.=.F;KEnJhd#2JQsd]A3cnfQOX^[,25oes?tku6Fb?-dBbcs@0UE.\p#%Z6Ecl)"rS
d;=`BnqCe>Pqu$Nj+FO/_Q(mT7)&eDT8blN*+FH9ds"4Gdu]A;rU@F854W6EkOQqd+J7H!%Bm
0Wdr+e:@TG<mG8h@EDZf)75@)f9L_An_Fs^`YL4&Bkp$QWaa=72R*Otd@<.,Eqrt*1g52Fgo
L2XpRQ02OA!S3rL^TcZ`k3,n+<Pq#$U%<S?u\CfrIBo8Nphi)7QQ01Y/t5CToH[4*ZR^%mm>
<p!@C:MTa>?`+6<ahMd!VO5Srhmj^FO'@RX:Re3qna;gld[afSRs[OnXe)TLS1Od44AmlFg*
!Gg:(*qS-^-Y=JF#ND4BAD^#G]A/SAj]APbJ=4mKP!eF77H22^;$Qa&^]AqQPtjGEo\/5TmnXkV
!\:3&dN$J_Us9/A1ek5,O<b[q;'tePs&]AJdYt"i>qBTL;@aAq.0?JfXN<'g&p&9K>"pZYdo$
3^P[?6P&[#(2u+$Y?2I,c6tR+l!Rj$j.ZNO8XJXrOUf`so,Ohd5AS^Ru=.b_51$,-O6^VWS_
Q+6a_bk!f[Wm@Jk+-?7:TTFX)&*S(ZTmj3)HoL9I>L5(MN1`aQ%AT_KUVuSKpnm?@a?&M'fc
Hn?"b,K]An6&=Q0a0(M#%hP3R7!<jro4aq$nmXJbf6<iUqT#+(>pmZqW3W:rN8IB7*am"9r3-
A9U5e$DC#Yds3C[a]AA"0#ZU5j=DG3MKe-W^2fdn31qrI(8!#.iM5Z*Jd36SuE5n6=JIHuM^7
fODmbFA`\'`Z$IDS%I9)GRXB.>cPC:Oc1[l,/SJ!P(Am0*dmF46iC"7,/,[-k2B;KnWVXQC@
$f`.6Lg&%">`0aFhnaXE\r!L`n+h)&A3@H0>]AP38G)`0=4pdOQ&a0$N%!?Slt?X0@]AE=!q)l
Z5N80aN$q!a-gkT'?Q+/c;@EhkPM63>e30MV:/@0^8S0XLM*/8lf9'?A"<#J&0%A&diX(l%Q
]AcjPZ44HaffUD6*euMSB$e>[/"Yg78&8Cle=>>4RCES)oXMh#'<HoV+HS@(-98I@pHE'`)/$
_TRPUKfVU3ZqUfZfk(-P,A!r'BcDD9UXdN"6l"fl5p9.pVZsb&s!W0G3<g9X`paS0Y*'=A?g
FcM8!\NYfY5\nO5jq7RCr9ilB9>sZO[#q'AON'KB%#%B4*aPI8W@/1GA7WOMA,^;seV?D^f)
h'CasHJ)dKl*,J]AkSm;ol-]AI.#Kc1!@NO94p#^Kn&Rj9:"0$FI]A\[8=[aqVOQl5P'"R6PF;l
9,,Nra[j&>*]ALl`]ACHcJMnN(DZG3%IM2r,r/3QQd*:t?1-qDS>mK)4bq.#1,8So5>@7hTJM"
)0[_N[2!aB%?Ck41B"h)2^fE,:8s"F/L;-\a"J!i<-J@i9SCOY15e*]A-#f>p<0qu5kh`m3Eq
H[0dO`T`-obV>]A*?O=93@a;c@>#5TA5qT#=[@e;bJm(tYC-aGM"gt!=e:R[dTplN>fi1@Z#2
^t9!Cqd&:qqn#p+H~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="56" width="375" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[const ment = document.getElementById('ABS02');
ment.style.background = 'white';
	ment.style.borderRadius = '12px 12px 0px 0px';
if (level == 1) {
	ment.style.marginTop = '15px';
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="ABS02"/>
<WidgetID widgetID="9652d9c8-6f58-4304-94a8-a5f888bb9cd9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ABS01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbid_yyb"/>
<WidgetID widgetID="ef34d585-ccc4-4f39-af66-2866dd24756b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="zbid_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="true" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="Arial" style="0" size="120"/>
</LabelFont>
<ValueFont>
<FRFont name="Arial" style="0" size="120">
<foreground>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</ValueFont>
<controlStyle borderType="1" borderRadius="2.0" isCustomWidth="false" isFloatWidthFollow="false"/>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<borderColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr/>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="148" y="17" width="212" height="37"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0_c"/>
<WidgetID widgetID="f65f5669-1627-49ae-ac91-43da3f3480ba"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="label0" frozen="false" index="-1" oldWidgetName="label0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[财富矩阵考核：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="96"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="6" y="17" width="142" height="37"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="label0_c"/>
<Widget widgetName="zbid_yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="56"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="ABS02"/>
<Widget widgetName="kjsm03"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="515"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="112" width="375" height="551"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[if(level==2){
	_g().options.form.getWidgetByName("REPORT1").setVisible(true);	
}
tabck('TAB2');]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report1"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="17508347-8b91-4c26-9d40-87c32e83f80c"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[304800,685800,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4572000,4572000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[分公司矩阵考核]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB2' style='width:100%;text-align:center;height:18px;' onclick=tabck3('TAB2')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[营业部矩阵考核]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB3' style='width:100%;text-align:center;height:18px;' onclick=tabck3('TAB3')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;onclick=tabck3('TAB2')'><div id='Font2' style='width:10px;height:2px;background:none;margin-left:40%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;onclick=tabck3('TAB3')'><div id='Font3' style='width:10px;height:2px;background:none;margin-left:40%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFHPb;X#)"E%3`giF^Q5M-G7<>2Ab<)3VP+FW6ZfN6DIS>S>Ws:f3V0bVEb2X:3LB>F#Ya-E
*l=Albd0Ar)81NfkS1.>TNq`L6VYq;XZbI@g93r5!I2o5T6JH^=,jn%I`Jkmh$8YSI?CJWCj
l4TPUu.+CIuXecUm=Kqu3ZIj6Je`l7mU+$Rqjq5b86\IGFgA&@q:+N9Wi2X,sQKCj=NPh3`:
O<bXT=l/IFeb#@ZqaB3fp9OY(??JBmY>mcR/I@5$&OVjh,SIbJ<b?Qk(<P4p0M!4Z1-a(etF
m9*lCmib:-s_nEbGQmUR<@F36n\E3RC&g=lCUGu1:8.?=NFDT1$1"\1c'@^$>W5:2cSlr'oi
)G*Rlm!%/L>gQ`5AK$O&FROXa3_T"u)Vc-gqpFGM9C^UCMUjZ1XETt'nb__%9,fXe$P?&%4g
,`omFku,N2?:\f+kC[@M274X]AH<"BthDas/-6l4Z7:INMXYaR^DhsLKU@lbURb++#OpK2O9'
??ed@Q[j)Bd<nB.Z_6n!,Z=C*+G\b3)BfN-aa0@b"'qijV98J!JldR:Kgo/<,kFb48]A-JRNn
LTp\Yquff3*k=$`Jle'mPPBl+fa*3[d?-L8"jlPrMegU5pho>?h'EtCd^nH1Pu@IJmac6Rjd
GOqM'.Vd.,VcrUKSKd(L'2]A:YL]A\;<q)_Q?!#bYp>^/oD>BlfRY?('p!J@m]AuYZpU>2,KJBK
@kaJ`@ho?-np/_sJ\Of42PK%s`8fE;1(r%MQeTjY=J=t(0.#$cmDj,!`:>3Bn3[>X`8")W?t
%O4quH]Ad-@Cl=PV?CYh8(uf=EMm:m"EGm5*h;?i0D>BPl*88odG,Ql[pl<<71]A=A3]AY0l[CM
#bF"5++*6]A=\jcV=C)GM#^/PY!X'&VXpL5"Mpi['.b"`n9DcF9.>8f)T;Z\)n;C,NfAYL,iW
M7$(S+.+[\a!n_oSSe6o':9!!oZ#s6u6[NXR:LnV1_$mj=96@,lcA@ONd-`8ks(U%RZj?2@8
qY21;ir1f*'6aY:*9%R!4^FIZ0*r=pgud=g/_D[4ATbp7b.Y6f7kQ'&FL-8T>-BQ#16-\$1D
_kPh/G>cP+HL(kf=7rZUQ4dWN82j\_"'2Lq1`>uP%a&*a16X)'/l!)-3mf8)Tes]AA(3oE^XJ
q/.ao(0Vo!`XkW\LJC?as_jD".]A_Q'Rmd;J>2dQ^:W%Pi'&Oh@d#_GnX?fV(.L:e)Dr?@fX.
n<*A9a.9@tEc7_je'P*S)CR)BPKPCG<Zrm]AoS_H^.:&)AIEI:aY!BedHe3:g9IgNf:;]At7aa
oZ&WD_=XuoJ7:6+KkZOM>c@LbccED(n8Sf'j/tF]AY[_]Ar;Y'Flh7U,522As8[N.&;]AulCcn\
.GDW%L-^K!^P4Q-q,e757[`=Hcp<9k=&,o\"X1"rb=PLArDff!\22U!b%p9lCOC=DG+j>1Vb
?\5amp<GMbcU/e/Bb=GeGJlpHT5:YY@MoXkF6a;[i0=Hdr6<;,$k#b(L-!mp'GQ24b/lF("u
"X,OBEW(H-nD*4@^Ns-cRq(#@c_;-0G'OVm%h_&oJ)gA+5i4hhaRW2l$`G*fnU5N18,+PsLf
AWL^^MBDQqlG_R#@Y9D[6Hkrcc=LSR\Gl\]Aa7V3r@?gc6$MDJeCQ[X0q+"?=mR:@^HqV#N$N
Ph)iZ'<:*BhX!^pC05]A-&,6b7GSNa!hNKS<hXg9Bae'#gA+e?<AN(nEUq`ZT"FbPGb"'kr1&
![l<c+/21tL<)2LQ8=s(@TjsRj,'c3LP]Ae[*LC[-C;6MkoD%W:SnapGLf26'OraIQJYA>N.m
O..-8)e_H'E;3+Y;u,Q<=q[G[WQ[NOH:kK=PiaXu(;[7?LAseRdlf]AMmuEl$pE8%+.n\,=c-
:K_0@"%g^?-OFAS>.C^^RS*#m$H\_ZMT/O&C&P80FoO.7KOu:1Emp"c7)Dc+Da;UA@4nD#98
&]A+rgEXn![+?oV9ors;A2Sm9h+hVe%CmpRhq\hnB?T#q=ok$gB,,5NAI%G4f"lG$Zro+Z;H^
Xm^&X&P[g`CTNlL@&k"kh6[B9,SBH.K3@t0'A<uA>L=,%8T6#7*ZO-]ANf*611J6X4_3nuhE`
^*lOUbY([k!fY1luS\N!ENld'LWjBOYKL<#tb^dY\YRcHDE//>Xpdge`dP=\OdQ)43.IJ%15
(AumS]AQ9#AD[mg+VoXedNGt-%oTB#aMm*$FRsN6=FEGMLSA,YbP/m'c188cWlL/S/FeL_=L%
r=+B$deVg,X>YP0&=b[FYV.`=.&XK6g>ML5?.B7m1(FTKi79`"Q*?MUif#LtLkCB$,1*>p&O
@;rm%_-I'G8o&nV%bkB5EF;i*`;rl_V[,'o0Inj*f8^'M$%Jq4a+i(S*YPs8@0B63VH.<l<=
m1rnfPZ8ql4]AbSmbP7H]AeFP'R-G3cn)K</T6,%\qVB"n=EPCeiC$]A-b"B:>r`Y*^,E.5r+>W
A(-VAi;8:,9^#d;4e$YmH7^s6qS2Uam,a,d^Xl1o`nK"Nk?pf.um]AG<i:.iZ@<5>FITCs#]Ae
e!pJDqE3AkGLoR'D,%6%l"f*6d%@Ki/I8(Dao$2@h]A,,TP=Y:tID0,#9-]Apu5W%??=LQLE#g
1#uD^g6QW05b`!BeDjf;!Wq!]AQ?^./d8Tb-TYH6nA5L:/h2rUMSd<I`3a(&+473>,,gmfR_E
PE)c_=qF)%l\HK0rU]A,B0Odk1+aJuCaqjoPfG-g.KYK-tcmm'*EGZZ'/B-*IT0A@%H54N.)p
[tjB'INr4jAnU)ZKfo3@l4L*BRc2Kg%ubbG'&"f7%e]A&Bj'[-AjIau[qO/QC2e#i`/2jt2bG
-\FG2@hSpX^16'`i:#;nCgdth=>U#Q?T-Ur,k>"n\P(.s[Td"=,:O=[S`6H<Ph;"e^1o]AW)-
K,q0D\cm^2EX`^cba#1's&mSf_k_^oZ2@jt#,ghYSk?H_&_4nhZE3@@/sfIHUI'&,ni7OahB
S7%2U?!1@<Gplq^(E/,*OK`V"bKT0GZ,cCS5k$r6?G,n3aC0rSoe0I`XJQc_dHi@)&(!9VdV
6UN_,M;L=IG,\6<iZ[R"pVE-j#&pL`.>/A!bCeDG3r!?=';;K.4A1=(+-QqmgVY<XhX7#I56
pcDp2Eg#_Equ8FY$9`"<8B.=Z3c<QOdNAgSi$PMSia95Y:<M+*L4rOEZ*0]A41&M!>ju&lr40
UGkid%K<8mO\PSNC-U..`$<bI4<eGZP]AOSdr7k^UVOq*_S68<c!kdI,\uV']A>%C#-\&h]AgfS
1]A6ST4<&'F3^[fOPSV[=pb7;u7fo7!e!b?]AhBlW?1t-q[kUKWZCX^,#)@U2;bO%HAk<i@rfQ
V_c>ps==2N46#WeF%@8+Wb0`o:!jrSmrT;<=C_-?.r(#5N(Ucc8Pr#j1JRE!`)T$7F!\]A_.]A
3!mF_Wm-#Y(ac`@mB%,.\q/`*ndG-Ms:YGdJCF%aKoWGr2@)H#VX8B*k>?=o3eH%(C;B)Ojl
FJT$%_nA`0D-HIp$,Z]A1<j-Cofu9qh&T%5RoJ2n*n"?^m6Kn9"XlV:$;c6S'VGF0-pRe?:k/
T]ATE#-D5QU`5kp""NqH/0ImO(IUb]A*4c9E23qZCK+>FW%f&/g#L9HTAG3qW1[WgQp"e]A.V]A\
Oo9W9!!3^^!!FDE!=1pj!YBk^"<[^E3n1Ca.2K_C"u`ITNZRn9,s)@0ru1~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="74" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 12px 12px';
ment.style.marginTop = '10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[485368,1714500,266700,1143000,485368,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[472440,2743200,472440,472440,2743200,472440,472440,2743200,472440,472440,2743200,472440,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="11" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__126312B0E8F5EFA2A2124D595C7C93FD">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n%f+beXK//Q9kG0efg_nT8aZWL3:#Y+bl4#Ee%nTBNsLEpV9fo,
$g$ko.O\+*__Kp%Z]AEpm]AYS<l7^KO]AUg@#2+f:N"W`\Zn1kcf3^V5U\:8GjcB]A_!qY;NI/E_
hao!Yt-hcneJ+p:J]AWCpZdurP@g)oXHS,ZJr0@\pL*8ibi@E50t4pNIKYj63,1um0m[5VIDI
G^T4FsghK.b/_D6#YmLem<#g?$+P?UVoKKh<6duik-4'*Ss2?c[P!5GNihjr^$eOqs2l-5-)
1kgDLjqmNNk4ERq1(Eaj=kI`2PD(Xl?[Me9SGSi$96F#$tbK'8Oe7@?oQeTo#`(P@D6Vlk.l
<9NsXODi*fl=fSYT0E0*ru3V^RBNihX!;+iU#1TM&T9n6Zf`0mG8XH3.[tWFhJi:q5M(lBpG
B&OD^\=A+5gDWoBD6Gj@m&*DrOeY!^=nq&,]AOQ2"pl\Ypaq"?`7tg;<X8<4-p._-VM`(P8X,
u"^.l$0Tb1)<r'/H?X:DfEn=mag'+Z[eYW<,B(&H]Ao_\H`=Z:)E.."V!FS2VUHBI?eM)!7dg
VPn*4g<X[S)^.AcU!+D!=BH6MDM=O3SG+=-r1<uEFpg*MmEEirRUo:>^^s%^="sHD:'H>k:W
sMqS]AgG3a1-Ensc`(p:EF6T7?h%D>)fk5+bRk>FT'u8?D%P)6prA#L?a.^%,(&HFC[#Q'Z\R
8+0f7j)ZHhP%T4P9o9Q[P^R<CoBk^$6D*%S@cuNIE)t$Wh7q/k%mGc(c'KgjmBY3O^[:4QeU
^HfHQIVDZ[0H8nm6dK5g>U9DboV.A/FYoFh`C+Gg3D>rJqb;LV+=[Sba_9H#!&ZH+m^a3`ic
tY:4c9)-Opuql#P*IX,8(Y;&kVW/l:Al14EZD^R*gJ,V+IgrQuWPFYePY[0Q[rp]Am#Rck[I^
UlF1=.T$N<!CTihh%of!b2%1p%G$"'KGooqe+.(-!gh+E:D)#E%3OamISf=.UArmlH\6c;:q
#k@)+OeKuJ=)TL6Q?L'.uB:8%=t2a#a2GYfMQ#\^Ubj2"^W-H-oR^kh2[<84e6Y$>N*H1#Mk
mcfCBQlQfB+C.2,b0TG6P?Zm6D)lJK\+\Rch6-b]A`GF^LB[&)#XiXdMVJaAg_T]AhCE(<IX7P
P$'V5PfU;eb_b\8]Ab=5:-c7.?bMAT$FPTI),=HS'$\uU'#Hs$4_Q"Q?aWuk@L>Je"$R!Z>.F
[*LdOi`4mXblRi17c80Ne2Y:0lV]A"4&q"@XRamA)fcT7PSHJ]Ag)qmbN1=etnI`uDIZ#;j0&n
%@9npdTL*]ALKG6Q!<X\q'lS?.dHr0p?GttCHCPlgS26i3bNqcHki&dRi'NFj2&uQ5t1#OGm`
s1q"jHD/+,MKEGDYWNR7#4lIQ3rWr/nghn$tr_@c7'P#Bhg*S>*[7ohX(WWd4-Gu?/6H[b=N
]A*g76n/mpr:;2.pIcH_u01+O?anTEnHE-`ZhZjaoU<cVuj<saEYMZ^_@)@4-';s:S02Ga;H[
FE6r3S!OSNTRl1ec0%";Y2MT!!Mpl^STV!!~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__5AD7B9A1AA7FF4BB867A654125D83B1D">
<IM>
<![CDATA[!Mfp(q2%pC7h#eD$31&+%7s)Y;?-[sGQ7^DGR+KR!!##i2UVR*#&?du5u`*!m@6=X)b1Hoq%
NB6oM^dM'0rBn`J)bkSTJCM!cAu'"bEm7+;X49+DM=a0kA(>.8c?K#F6mnQ!&p`Z`T;3^8*A
iW4Tah]ApW0N!3Z_r,j4m3R@TmS?E7(YH!B;DDgV%GWUKZ35MQ20]A\Nn6mERO$jcgZ>Y;8>1O
!2M`]ANPQ0C/Fle$(N1]A**;5t7ubEH_+KA=.lHJ$`^'u7H@\B\@+Cg#F?t=pU,uetXt,sS:AC
AJ/OotSD^SYj"F&*$T?pr$8>jlb>N"51_N5B+e4to/6<7$!5.E#>ABqSZC/3&QGg3iseBWqD
8Zd*AE+Rn6?_2c#e"gT%#=VXh;YAUcjZ_UnacP@>;'!4a`=fkeBdnqE@mTRJW)!:EMU`*_B6
Hu$`g-2_\7TGYd^-Oe>6+(i4!HE_G^Ik?o5AkAm>/iFr*[:9rk'V&6t?SmoXmW\KZKg<YlJr
P!n_K!@)jRD*L?bL>QWPt$"1e"J/ssVG$G+$EB+>MlhT^WnD[)_E-rD6]Asrn_N,PJ&_eZ#kf
?rojX4dO44k+ogi!cOaH5YRpRFgq7I"k:uXTn^JdkKL(q6^jb2#He,2L;h+L&;i2/Dsl8=u?
?32:0nThY8Cec(.QJ5]A?Q?P$buQJ=DGM1rFO4+JTt_-NR_[j^R_5H9@]A!HuRM]A9$X_pj(n4*
E<7ga\?X]AeR5JAIBfXGJVe`_k;,GqTS-q!9J?]Af_XHnb_I-%qm@L5#nGKdl'o(:b_/7Oro*3
%Pha3=t,ci2jfRfC+3NV;4676`p7*.@_V^F2f3NiAHf@-ZtSW4r<$paZ,3h.V^3(PU+i,X[6
_]Agi'HBVH;>r]A7Z_7Gh_&Gn^j]AAj^012(QI:75+M[_jBu%>K5!u([6D.L7oYYQ@RH4Sgc2%6
\Ge2b;el:ZN3bIYpojdqcibNJiT?I4P9`f]AC*Z8Whf?_ORq7`SC05%^fAhSi5O;]A;l.a9+Qr
\E/npaXi'GJn,RUCC1TE]A0g<J[@j$EY`Ois2\"lS!F`TLT-ZHJk=#4gbJP,0AG3%jeV$c#2l
i*_t:[Uq)m)=(X.2^4:J;NE=I^dY"NC]A4*SOM+ej]A1LFMU_(K-;i`FJ^dTem9%?5IlY1;smP
;RuiDl`dU`NV'!#M3XdNG=U:4(;rQYI-'Yf,K&l>a3=0N)5F7fG/R>mC'eTuS-?/ot!YH<dQ
QS<ZtaFT@g53N;S0V)o`OLbE8fj2qRF<fs4o7)UMh!-OC.ekNbJo!BKsRM8m2OHN;(;O=.2$
Z_D(*=k2BEl*G4i5:c>rDd,q9fnS@"S,%$(8oH8Y-FN%SCFRgX]A)R#0(0rC?08)#d]A-PKdMh
`SPUO`pF]AgpE9<+BF['GGnS-08^?-3jq(T'JVI@soT$PO6#_n'GL))06qRlEFFAYXek?js]AN
R8F,]A%,#/g[:ign1Fp7;<e%W:!HJim\ZC9`!_sF%=m7-Mm;W['!Lp68lSNM,b9!l^^G(=\Md
Ftbo:/&-A/qA22.2VRVRfL2gI3^&J@YX$:hR?A"Oplg(d^`N]A+m>k.=`)Mq&q;E-TlR2X9K+
#^#94L#.W4aL,c#FmV1?H^^pi+qF;Fo/Z>`U$uO927XENC>TCT=\eK/lE%FCu'0U(LUXfaeV
Ek;8k#uX5<I&P6@pol`Q#.8gJ;VTt:A_R'3!D2M^#<KHk0B"od.&$GJH__A?^IEH%p1*-+,g
?,itB.f9)q%+!UR<!Yi?A3N_9@6/ck=K>4$2]AVKP::'!NIjVfkC;8H/7ojnZ_V\K`:Co)-T[
"aJInG!ojHnFiXg^lPE7&K(YOfXAHLG_Oi`OWhNRE4#jbGJYS>B1-km(q!gWo7J*t6_ZV!9@
'lfd^6*2S^P:;=eaP3X`_M:f!7j]A]A;'`#.oGYn]A^B>a&T2FMT17=*MG!2>$[82B_c9:Y(.p;
S:!NDT96\Q?Grrig0"A0gF_*bF7@EbM5djajiPt#&K6<TG)%(HQNsjdD8runpW="X_U-u7U\
I5D]A-7PEd.VcemG-Hl_O60h@I^'DTG@8*jZsja;1?eOS.QA^r!*A8X?Sr<o#M\jJI5iMXq$I
HtltjmtI>m-$/8Kk6%j0:egi='('`\46!(fUS7'8jaJc~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="2">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__537871B7715536139F674C85238FFF74">
<IM>
<![CDATA[!Ds$'qMA$D7h#eD$31&+%7s)Y;?-[sGQ7^DGR+KR!!##i2UVR*"W0g!5u`*!m@Ck0gI;m3nH
`s@@&_.k!^D8-!cf(bi!>T@:X[8.=G:%[0O))mBC*Kp*E@8D-&&C1L7*n=/S#Ki@Ohp7q0kn
s4U@0@,Y6q#ciF[V1\7U(ONY!*E#&gh$!ERFn5gQgT\]A8:CX(/?<Sn>@[Qe`uOX:2[dG$3km
2ao8]AsXa#J(7^WiMM]A9/b:G<`P->F'"FrglEXp)"'"LKii$(XMh]Ak6TWT1L]A;PBC]Aa&]AU3sQ
rLgG`0XJ^[jGTP?!5)YI@C>"J(+8?P3j^`:btL2=q:-8J3]A>u7U"8V8Ps:2<"Xgh9$.A*su(
7]A@_@6RTGP<Iofh%a!P;EcKl?Iep5$Qo?k.-O>U(LMF$?QkCAul8F-4^]A[S9N9Z\J6korB9o
hLf@$21'XC&bcAml*RHO%HpE2$bZ_m<T]AZ%*S!G)&-k:_%o_fs.dB7P9*ZD:8W'QTGdU&ru?
`lq)^?;'Q>^%Km.EJqX<C_LZ+R5d52M=?*]A#r;5)Y_bVq3&]A9N>f^jZ%_]A1km%-'=Y^WE&X"
</VhhDkL\F&8>UY(bfoeQ]AL4(,'q$iYNuB[r,1s?LXRkQ_ripWJX+NVIT#bDpLjM?Tm*Mhu<
1C=6ea<_,SDG*d[:kSaXGoCZiZ:[0XSoB:d2G6#DVL>[d(;"HRj&o0[#.j7r-90mgYXGg+>M
SoMS"Dlg;>$\FH0iprGphZegLC?(B@kLo_7_'MXLcVb_;rIEf=,YO@P;D=6rR(P(c=;EQWA7
Z5(.Ceu(i055HoAZik+QU2E(4u.J(Vfe1*dc`TS<EVB+(lAen_"TL]AHK6\8[E)VmGRDtE3sa
:r4JYLGDSD"#YkEVE0q>doO)b*-:3l/qCHji&A/"Ss%D?`M*8+"Huh2l%g@B94*LtUJ@+'[&
.B\kN^0gf+BUT7/0"p2%Km.EJqYFEnDX>j>YAbp1TqkUABu?Lk<OWVL9t;LL_=N5<Md(78tl
HX$LW@.0Ea`s"BRj-Sf.!Ph;?*4Qd7s:eHHbWn]Aq"$2?[)PE7#PAdP=m&_(A*/i1c?U^1o.-
V*Ju*_%9io\>;H0Gl!$VQ\S]A[e5tW*2?YDGA@o3T]A#ZZm33R6fE'd7,S*.h.a+O![2?YDGA@
qJ>VV?d^"TcFNS3%i1Jd]A$o_$^<r+ZO[20E`20'7q#'"@I.rJ7&X8ga3.W#RH1Cg]A>4q-tC@
o=UX,,ZQTQD$309;D3.;76B9/Gj.QG00\J'A\YmNoPLQIS_8PX]ADJ%aEoVh_6O8[>P-:RjbV
-336GCJ7o@^Jl"JcPuV#R1iF@j"l5&1ee,((LWlE@1e0'th_\"=OSsp8u&=DmLem8g/0@+"d
Gh_jq&]ALVDO0=@'R`E@1h1U"=1-QLCq4\.\o)i-VR@Mdko(45@pXe#9Da8)I;cr!<Cf7)O'c
j'`kh_h.Z%GX`DUUoeN;PCY3L_c6<'MC$[(8<b'KIT)Rt8W\k=mE_%jZ)>clJ>%;bB\T;M6h
=gkHA]At=0ZZZ.<NE+[r]AaAkE'.GF(*%NAH'uc'2\%>9!KP:P6Q^B%[:<:=M?HYdL3eTohC1-
JF]A2)7Z35nuB%f))(*&JR"]A]AX%WnZm2XV53L^=R%_.X+Ll5+N$cqcEJ#+;A""F6[5taeg@.s
!EGRI$od?<,XS5hN#%<hNk%7rWB%QahJt1rXSu/!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="1" s="2">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__3C0809FB23A47CB6C8A9E2E4A138D6FD">
<IM>
<![CDATA[Fd.54dgI#5VsK@6<+n-0QX!=%FQs:j,Vql0^TYkX>[eQ(0Qi<f&m^7R17-ckj>&KW1RF`2/0
^=[Kc(nFbAGd%PI&R74jW;>hg`B/qgAEh^DTdZD3^g,Tcan;q=H,CMenQ*Peo)ZD@qO?"bFJ
EdqC6j;UG_1"4+m#T![[pQoi)prSdOAA^unSqC5nW?4ir[84BpVV)7#qp;PAtg9)>V\^=#0X
N@e14W"9V)5k_[8f<GE*3Q06@6kB[27p.;B;qGq-R;i$eQU,(I4Y(`VaksTVMWoE9dH]Abc;B
iVc375HBT-6LSPT&bqM+I?5?:s:BCW,>I]A1%mQE4Qd(H%$b9A%EK.,;5Rf<p^$?e`(SM!(*O
0kOpP3Oqal&6le6ZT-6s.rq/hPTTOKB@r6+,EK3UcfoL8-JsELOPChIeo#iYqa14S:a:lC\`
3d1_G@*`(\);F5+>8a4@b&I\=g[jRV(:OjOC9+7[eZMQaE/:cM!.2DED$P./%t)5.YI#pRio
eS"MK3>8oE2rpZ.lB)@Z\cO_h$9r3R+"V[)J<IE2,bYgfM@t`53=cZH@%,1R%;j)8a&tfuC?
rZB,23I^\kYro_[GjoEMGeUgj2MltY?',g?,E:fi[;cSJF1dU7F;hHOl`6:X"1#.5//;O=YF
RN[-,o2O(F0`a>X15/#aiMqTBUlk;*as#5Rg[g.[2en6W$LLqI[fs&F-h#p-VMI&iu<IUE9[
oEU>WO8Atk^Oi$)G8*Z-iqLEId_i&5kD5D%BALER0`c/qKBu:,R-g+FB&=sg*r!?m)o"KHlZ
iJnpI%l<obT4q>NN*3Ii/GL-J8LF1H+99pRITA.%+_/OojsqR4hh^a$B1ON>)-M-@tVU_-cK
f!fXfjE:=R!n_G;:.7:.Tcma($1pWdTGbL'`0EPTZh',pAJ^*Oa'1\#KKoR*tFnM&uFRW1eA
=Ki0MbKZ><\SpuNLn\+q@?:1.#E#?!Pjp)]A:/`7(U!JIb#H9QYn#f>j9g1J.aJtLJZR=KL_)
r,kr4%V-]A9*&9k&qaocKRF$strNlGLN&>sq,_o>M@&[QpKqP@r1E?1-24fb#c+\)Gq,<OFc4
!>p_^r7rr-EkJ.]A-NodZ$]A?W!2d-5>help%QRT=`!`X&_G@!miX\^+*3\qf52FaBF9!q%3d@
]AABQ*b.BmC8KkY[S=`@dVb']APge>?J>aXnS@@nL&*8?NP?dN.qpIUUA3A^/Rtj?(['U@)_"s
g>]Ame:[O<8MqHXP!F)9Yh4/VXX"KmmX[e^_OVagAZm_S;fHieJ"ik)tdfTdGD^8b456dt7WB
u"mpcSUoLe<RW<!Xs<j5o-AK)K@&s^pcg/_Yc2_W.&?B19lV_[1`u>CbJ4U0YdN03NdqfUCF
B6]A5K;n>%'r8U0fCO1e7Af$oP[<*SMQsJP28OhIBX\Y6t..F4'BkUVV##>Mu^j\9PWMq_pB4
s"7F6:V/(?Ib@`oD)_P=(b@p(XP[o*^l5/fcJEAWI,3()8ZYi8GL-%8=6ZY_r>EU'a%mtfNu
S?hoTqH?"5[5h6to0m5E!h#</2D;?hIPoY"kDcT*3ZmiBjsbC2SUX$N_!fph^`5JR6q*)%,%
,As@N)j9m!_co_.3$Xi8T`;`&(S^6a4S5n-3k^Se8jmF<T;IP6Q2'hFNj\]AE@%[tPD/g29'J
#;TMbL5'ncs`kO<uM\l_Qp1\?X:XV*V04V:[!0b)'03J/6?I>$mEk/HT[a1hP..6*.a;nCV"
R^;'B8,HRk?=D/;*]AW*[:6\4kT`=t8QL#UhU;'W@*D&*`Yl?_k4p'1_r-I:ClNeh707qrnP_
2AK.8oT42:&Nd`#m":XM7lfi,IRRBr*%MKQNo3+HQmn#)"DAT0S&>O3^t&oF_?8Zc1tQ>?dh
GE^h#@jpVFV^HlLWpB[;H+Y6/-99(e7@*q\*ieB-ReP(#L0M4>ZZEi7:k1eq6`PS42&%`)6L
9~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="10" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="11" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="3" cs="3" s="3">
<O>
<![CDATA[ 分支机构\\n管理]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/分支机构管理.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="3" cs="3" s="3">
<O>
<![CDATA[管理指标]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/管理指标.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" cs="3" s="3">
<O>
<![CDATA[综合排名]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/综合排名.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" cs="3" s="3">
<O>
<![CDATA[营业部督\\n导跟踪]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date'); 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/2nt_Menu/营业部督导跟踪.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
window.parent.FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="48"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WJGPKDB.[b[[d67XJ*+EX[:8->S1@"='Aa$p284b1Zd"q_Fj6@d.=+OUaUJ?;(uf`=pHa;
>eXL]ATjD"ITnE"9]A>[+b9V"OG&""Yg6I#^NIk+kPi(Xa<GE)hpeuVeZN2)5?7A'rpFlSlXm9
D8kM]A[m=5$+]ATU^P'#f(T#2mVA=KR:?P"g]A0YBKrYQ,BA8Wjran(nSu#eBWC=meHWsClVHfC
hsS\D;!pQ\*NW_mi4R:j3=cUIL=V?SS]AGXs+^%=LS%]A'Y@_OHWGK%B\(BEC5DAmQT=Z.em+q
@$6/_Ia6W^I-WdF6LZsJd2A4;D^#"lpAAPa8>\9f[fZCnQm0#p`'n-CFjLQQB*28mT1&1-dX
7SY5%CYj3q^K%"DI6HP\4"7ZQQ7[lJTAHj`GAOET(If&D3h6[":&-PqOSOm!;G.NkZuE8Mg3
NY1G"45t[dq(JZW6/WXfR1&R(&<\Vrn9ns+ceI$994mc`D4/ZtRoO>s4s&[@DnE.E,?T+QYN
Zb,e_X@+O\2'SO@W4m#hjLQde+^1I*T`nl7eCn<ea*6!3<8-[hWW@KF^hn*)!^&?d4f!5`ZM
$CR9A'<;jDlOXVk-=R_J*YWY3)BE`r-&L#"bH^$?mc!Lj3G+]A/d`@I)HnP-912aKYH/X6g#6
,Rh03Bl5XhUu\Jc_f2H3R8Y2]AoSqP-U8Bnu=lhFPYUIA`n5i$>Mu=\i58<P(%?]A6r0rTf)HC
_H\ZcGZ]A7UDu,'N-[mk/;YgQ#0=M&[k8g&?k$IFB#Y5%ea"E`9Yk_M,as_CV0Rn.-eiI9c,W
i1Qb,'2aaQ4gGh=\g5MiSA0+]A,e($[L^'#4FW2N1DR[Y[$D*YF<##dm"AKi@u@h9\Yr;PBU<
_BQ;iqbA?W/q*kreQ0GfB>cYl+]AHHgs(AW]ADXS%+_X-Wu(Z<r!sM*@A<.5&MM`cs;F[-eO)T
_;]AS:VL0t3=EcL4Ku=1A@mWHS/Jf-#n!&+=3k?g]A#/n!G+2[dM&%\-Y7DjcmD$VAg:@;'1BZ
ljOSnRE2EH;4ak5IYX(UWahcl7H99AeKK,ulZ1hBo?Z?j1eO/CjA9;)&NZt'&-01[Tp4%@1s
Z2Qk:o/j\eP;\MnY8*n^[d:7/n5tgX,!$3g>jK`O)X'esDfYbh96cCRK=>X*[t9r"#K%%k1F
IV3+XL-AVKe[I6g*eEbb%@NmYT5e4oqo/_nQ.pkN8!V1k#=tb'Lr%W`@erB/cV//9p`UEuVu
A4\'b$/PF8eVje*5cn,5^>:Vp&a_[t^ocIt%J@5ou;)MKgEKI(#W&iRtQ\&=XEmP-VeP&hO"
S:XE5RL$1NdogCOd[^Sr"WN/Zu$QP/aVEk#qEE@[LT=:RY0]Aq!+ASo&%0(m8X5<)D1-@rNSX
XUau9AY*[/Jm[!gu\\S]AQ/MF,RA&K9Q6iARNO?GgXf$c1DknFtUQK*]AN1"PR\5ATtChZqB)M
$hZ(Ig[dEcD1Z5S19fIrWr"MD8=UARd70?>rlHgcUap,p8S=T=7(o"8@5;#3hkEdb84?.bR?
r1e?%*1LbKsNR?;QMFGB;;%7.u%@\f209N/P1]AgIh,$(L;4^S,5X2Lo6[X!)V6<Ssi'jGXuI
@"n%jUYt.iqKEA3T-DIYN/AARMT#W^>^Jg=5BLmI:H.lmG=C;=Dg)CdW54S9Wm4R0EMJrsu6
dhA5rZ&c'6elY?EW*>8nIl(H\l9dk]A\eTNpILc^4-NoVJX=`S2!Dr[$9T'SdSsp2MoT6^FCf
>6nh[nNH_VFI9W6c!l\rH#O?^8ap'A?%+VT=-SF1Cb<lTh`c!`N+,-hC%h:t'EQ:b.'V;(]AY
OqsS6#AVTa9'Gl$DeQ-ZGR-*)hCS3N03U@!H\369F]A@E>9'RWVjjtIdNpn(n*!,_\N^bkp@G
l!c1MW0*aNp%_</rUDBA8`.i3$<foQQUd.K'3p^6?"nfESBE"?fnGegm=`NnH$n473\@%U3X
M.+e>_N[6W3D0b1C_A=up^46r2&2]A>3;t6Ls?1AUC[!S`')=]AJc`>3E:OUjO:jiCb2?=,m`_
<&bXSk"bne:ZiT'_9;X-^c]AtZ\qV88VdNOY.d$kQ\TRp&E$?]A8@uoNSQC((5E+,IR+Gn*/"`
WMD2a"C-SK^FU<+Q\qAfj^SlZ773M'41keh[F$-U/,me?8G"29)3ffjgl_G@W"TZ@*YS9?lP
;gCuo(kXjKE`$A"q5$b$/mVBHKrcB@V-"jcN>)6d6(`NG3dSC^X>2_63qK]AhLa!\qH`FNcdd
AGjAapg4rET]Ak.0Tle,U+F8jY:D!3PCaLbjma0ZFJcX`:2*)l;,_.Bb\dIOKaqo<'a9#/XT0
_F^2i'#Z)_>pAV[&`M^PVEXMWLDP]AT#%$D\.g=)W^$>+Uua/6Kjhp>=P>'DNd!Qf,nc2'ad-
4Q1;dh<2c?@O`*]A8nLU)cf9i!7SG`&m`(=Uq?Q(&7lr@T%gjq?X"%ZPH]A$_<I_kg_>,[mc[J
Q)n06u5,0Gb!R$ep@1L?`BP961_8rDBF*]A8e9J1G`a`n+Db4@<%KVfOC$iQHUHM+0$ROCYP"
ChaWNqSh4[_>QE!e@r0QCjRCKoGFiO.-+eD>crB620u\1XR/84re\N[e"0Gg4F#Y;AV,8_\j
T1a1?:VPlN?S_-::ho2UqZ'B]A7T/"J0)mXq6p&IGC_<Dq;+5p9;W8C8n4"Cf@t(;[GTaM4U2
j/2O(0=KjAh!r4_7[fJD\@6#5'dt>6u]AqV[rX#u/:/f[^\KnASZb6L2?0,cj;@h>mgc*Gl;4
oGZm!&]A).3!F/7`J<9m>$l!dbc,_I/1N3;R,El-L:F3i"4XN^`jMf]A\(l/ZNMh2H0fNk&0>\
Xug?Jp"Yj2qpk=Xc5NHS&!QA[o-\Irlp!e?eh8D,X7^pshZVKUK2orpk[W(@m-QVS65Fr5\'
1iJ5$P8P\2akK(_$53WhJ#/[Mr3u`6*9feHYi&cWJsVjG$1:PaGKW>[5cMHA^35,FFQ_FoTI
VeD<XhXOMaWrHaIE@a!e.m&aBNj>b<&GrAZR6g6j%$0dVT_p8:mmsm/O9"XU([RE%L)QGr,W
>CV7WOe[';2#4`Cbrd.'toh[Em=lF&mHZ*)-qa2u+7.>,'BR\/n%Je3^D)Lnd.<rb>9g5iRD
b>q#-&.;5CH5Ki6&i#/l$Yf,^KH<p/B;dUk),+GY<$&%a1INZ?K/)>DX>jMfD%,)o3t0lNAl
BFZU'hC:/2SmcIU*`,$bGUEaS&,TJNHklRBLMWWbI$OpSMk3\:<saqNB_]A4VT0\M/MF*M-"B
4uHn.(rBM4q3?R,C,%:L1^XY'%n#53DZ5V?@-s>5\D4fe)9n'OZK^15?"&J,b`QH'f.SJspS
<eFgj6O8;12!GILcP%6:]AZ3.0n<f@uf/6E?=m=73VQ\mfsGb))p^V$(;^HSmoEG4Uim*Nko9
?O(`_#;?r.tN<aft)M!kE);`9%atZ]A;:MBOM?]AiUdh4Zpfn0N>I.E<#Ijm"7/Nc8[0V"]Ah79
)V75Q\s/`E=;S]Ap(GR7Ic""jT?-A%*Ia.M]Ao[bE;3anqIj&&7$G2?NZ!c)KUL"pE;;MEaACg
cRQ"ecu?sE[Bgqdjc<kT=:q!_e5-)Z+$q<-J3"c'O$-I@;Cs5^h**gZu1QLg41HsKr]Aqs:cA
i]A3\_M>fsDaF[XC2(arUf$$ap2e@/6P_sSKI"&pZAAt:'QFHM#No&C'i)cXEhjh1f=*k`+<A
2r1Y_N0U*R$R7N6`Q[;F]AB6?t2P7:Do-R!/_"H[P&_B?MJ<\i3KQaV7HW)D;Sg_-)Z7!U.:%
4s)*,fBUY99ofk8B:.7jrC;0VB3bJiA[d>acQ3"J_Z<OSKQ`4k(U`jrSmn?GYq#Hg/l&5eEX
H!j3`2g/fYuS0k#0^9t_[Q5uJ!FH4":4%)%?/S[ZHh$*#\(FJdRb0Bji"+!&hKe$-MPT+3Z#
tA&RjZk&2NJi$'2Is7h'7d]A\a^??oGQ#i_kZXRD4\*:32ip72E1<qik$Tp2bhr1>9?:<j>k_
a3q*:-1V'k(.Xp]A#k2lsrkPk\9_FL*,N=+R6u6(=(c2.DR2/EAeVjCei"bAo]A(@-k2$aVq3,
G_pM1cs0L=V\i&K2]ALS+STtVHUk<3nWYMWUeW:5@qIKe*n[e<@JEZ;8blU5DiRh0&S?G>(5$
QQrUPcH&m/#+nBRb^+(HrngknB@PH\IJ'Q:Lj@F#>7PBGcG(fMKK!W]Ag`VbnpXHh4@m.A/sh
B4XUe1`A[3#3,_7tdrL5tQ./)koRo<Fi+E8g6DP+a!O2mPt4oI\4m&qP?5jSe2'N'pkrR&qX
G[PalL#499kQXXA`U(U(Ju&YlED4-S&d7;61lX@l[3!#iD@d5tDl/+'lc49'S*o_iAgOkK0'
:'"3A:Fqr([cUB28]A!\B`<YSp8[T>.PNFR`WNnHhWOdFE\^_d%5h7PJG`s9kSV[8dijqI_#r
pqLg%<[V>k#r'N^FeIYYY/e-ftoV9d%AkisWusFZ6$c/KA/?\"D1k'KL4#j5)o(#=[#)?peA
_Di4.@qNiTg+ppsan^R4t$lr>l`*oGE7]A[)SL'%V4/D0'9C^Dq(e#1cSfKX&`46\-0'`rCU.
qV>N(e-@6I!jn"U,_kU&`*#B`PLK=`tEi*#iiimF?;uI8a<I]A85[rcb[WQ/+@2h)IgjbLUsN
k]A^8BI=ieI$!';("9n;FDgkAp-!]AYHK&MB"3kE(H1t*YCu7iU4lII+jK^Qk?kR_\_V1gsd1[
IPk,$6LYXPD'@SpRG8?/iDNM_MKLHQ,ft!9=V@Y0M_&LFeF;XB^gKqdZjRTi6?f25<miT[/`
jGoR?g50/&^#8_u%[f'XdK+(2!\ddX#HBYbdZ13kp3oGZE*H7B*EO,^Dhf9)F&%GRgG-G>2R
;jFIH`BO+j`'.H4(=u%hUe\iL"W.&f?mscPJh;</Z9$i:@+=@(NU"\HnS9ic(\VP?J2/FP)P
8D,t1I7d1;9pl'[p(@%&`4gNNhhM*GVWg0Ogs0\'5LMoI&E+hrb6S!Mt$TTDA^ijIZ'IbcM1
75CZEsSBtT%fPS`s&Fog*&HH0T['dlN.M^0I-Usu\7BJ2H%0uaqH*-m[W*/$gE1I=#,mu9pA
5T,pHXZP=6k6QOMS71\pd]Ap@._dGEXW;FJoI<+W)mDVF[^2;A<o`KiDU8Z?PJe"k\Nal]A*fq
.qG]AHefn-!A%5R'&8`Me'rt$:P!j\QhF!Tr#F$"3D9bNM\R<Ik5QhDQe&.X.7@In:Q[%SH&6
dZkfJOg9Q]A`[S^"jQf9>O6Yr335qi+i7R_:So+B&oiE%uIF>N8qO0+'o7oul@H<sI7+ZhZ*U
h!Zt$o@OHQk:!e7ZE.sCkV2&:rNQWAqK3p1H]ADh9sB>E;1OkGh8`g)U-'[8FL<_)N`QC5:W'
!Se]AM7,?.4[0IOX+<&LUeua)Q.Y?M*0P'Q/(!WQYjt6A4]<EMAIL>,oq>IO5i,'>DC&i;M+
c1a6Ac_CZ"S@bg.5ZkBldZON?CG+[!F6V2+rN6f?frc'<B-K!u9Q/594#E*g6P8<`Ug%[W"P
@f\isaXfU3YsTauCJ!D1S7^.2G$c^nBKY\rT2uSekSM\T(^lK!V,UMU6#&c&pCne#!b"8W#Z
cDPq;rr:Il.:*J5.@d@JjUia`g+72B34:=U<f_2;_)YPihai2nYd9Wtol=\%;0ifF/(lIp1T
-&:Q@:V!+E5hP,<3uQY\L:WbG+h94EYbiAT#9%I;&L#)k9Pd)4MH]AoEb<*X2Y-$sO=s(t_G&
8qHu'>6`"\EN1j'JqbsTB)<YNBKBC$$pmU2L\?K678Ss-00f2*HIaFXM._,"BH$*GEq36[j6
\&p4(bP9uM%FPF8(-&OpZe3B66lGNl/NaW@dm`Bt*Rn'K4iB?)r?9r1Sc<qYbklP*;U<]A)k:
*O/^sa_r!c>A:rCsKOcQ*W5.(B=ZMDL*]Al26W+jIVq1I`OK0:\^T@i9]Ac,F@98N!7#[c*jE;
+LBLh)B.a_+'15()e;GZ[(ATFU?pd=8;3h&!s%WZ!]ArbsPS?`U.op[#&<E84T-alTE*98>-Q
_h6,$.e*1j*%mf@Y<9V;U9Ng;\7rd7\X.%(:f9]AGD.g29Eb>]AN]A]A5MheT[<%4B*Gm]A?12o[]A
f?A9hQ^H>/i%]A.LhS=n%*%Ucn`=ahp$USud]Ag-of,&5BuLa&*?5W7sVY&_s):'DK<Mf:.KUF
RXELB,%^j''K3Qs'@N-<."6(o;)?J>kh!H4__]AsAGBrJ9]An7Bdr/RSaWD[93#=iiaW2,`Us)
TfaW3Kn_(L_f0Pe/T13+/'p1rq;"l.i%5$$I1U(R5pr[c45/NRQlnnqMW\"Ud5te+5*-JFsI
Rf"g<4jNF3N<Ec0nCBhb;s$Dl'2,/s>+4^fXQM)qt,S<a4SXIK[Qg.j;^H[f`s!lSQ=28YcK
f#]A#ndn0>`A4]A6I;&uM:uF;S&^GdhFDR+)s.+8;;;+=M8<)Mrp%D?l!HDFTK(U>=_"X0kh<N
4%CoW,1#a2_mLp!_.;G8)MhcMcRqWS6cL(IiM'!6!_+q62:rS6V`jc4g`Np<Nd&,/T5/5tFX
:R,*sLp>oQS?1%;RVe[>h9/JKPD&df$,F@#6cPmUkQq(kFGASnXCq's@`,J4Vpda>cMAnS[1
PmdMTa*(=7]AAm;+'S;DRNkBjo?L>TSUN='p%Hb-jd1-ei5*gdYAs(q$p^DA=Z`^%mcIRH7M#
X?;\DS%:mCSKW6\OG1o9I&@.3,.[pa^[%mW"LVgUH!bm!1[t3Ie#52.FA<q#0<LhF2NcL%^r
2@)fd)^.`,!=V7+`J]A]ABj$:i#.)#q]ATe;hekN\^kMef2Ls0lm&A]A,jH>V?77Erqp,cFth?$A
sqJ>clGD._62pu%5-FbCO491_NhTU;b9mCL1<adC6b-]AH%X3Y!$&@"6V,fe`#BEHe-2fl4GT
iV9GV:^pXZpT8YdRhTioDMnl[No(9Y8=tp^1-_Fi*tS-2/"*Sadr7(^j%J^^!dfJ5/Oh5thW
2[3"UT,XPs+uZ=>)8,Bk7aQqgnAn/T?jB@aqoY_2<`,`$Z@+qR.S/I-f:f"SF-i+Oc3lU/IQ
'(FG(R(_),GF.oU=b6OTgN7eniC_c<VZ>MFPkY_%CVrha#VWj,0P1/?HOO$saZ[lblPlSoq+
Q4=Y(@N3B;X@U<*K<LAH`0BQeIRM")JB[*i<hiTQLV)nkU@%8k#<o12RL$9q>e:[4UEq,d*.
j-e3Z(6)]A")c<<`h'BtE9J(!VrOG6a"0&"/*`2RuYr>)?hC^t&[I5qA/5'Z=PZF6pQXF$ep\
:p9Yl,-s'hO`/H+&*1AY@n)PCj2+s@(1*ZUcqh9THJuNR"Mu\fq*e>aS?;6=+BY%eVtieOH"
U-Q\p(cEp;uTEgfTVkCEj=Q(B=0^@XFOIC`g?5N"2P:$d5GpGJ\),b)EXK41UH^<Yl!YPMae
Jh?M<W!s@'^,N/JkMjsb^`Q@Z%7kM6r7r_).4rBsKMB_Z-N^rI65FW>f"T:TRp]A$!HY:!H^g
4G>qD$snHGXm(["tHc<N6,q4j5_^'3TA/Z5A0nh.ThRT'j\RL&=S##7:,)V+sbkj:ECQ[ARD
C8J:BE6Hp6Bh3t-rj,eJ)RKuC0rGT\*br1/R!1j%iu\2?M]APknbd=XbaJX]AYR9*]A+]Aa=*SKU
o4^?jng\"LX*G=>*3["1h5m^0<&c7oL;'et_tK/?Z7K(5a]A@f+@\$jAT(NqY>YO+>>pj,@G\
4*(pfW^nl$)+?'d!,:'I-'iRs'Qr(UA_;GK8#8J%Q.jO7\gSGJL0WZu/8rep\cY4bJ'&X.J+
]A=_A-XUGDKR0jT7C1&)G7kh1?tQA_$qbHS_qhV^C22?(S1*iI=,;6Z^m7FV3OI67s^E&aXBa
9aDe2m(V?1@A?,@\?s<$tP6HO#V0a)3aQW0u]AXslqXuL:+/@hn36,Zib5R*?agltii(/j]A1L
gd\WkgW&/#N@U#.=ojjs5`_f?G]AmSf`,'K=gZBuh<N$\CRH5.h('^TGpu2eVQ\_ME$]AFum'8
Ap(gGEdC9%&.Ob_O+n:a:#(o,,eRk)go^MF!m=5>o\-*Zn((0d]A'DM;'FPu!EQC-##M6G+^r
2^4W@WPN(lfS1glF!se^;?X3)cKq.4l^SU*`;e#V-<J8@Wu!rN8k7kecA+>g9(]AWpf!Mq`e#
>%M.L^9^[?tA@Rs^0q>fcK37C=8E+\`E:mFu-_.p?[?a3"pDJhjddX;G<nE50Gs_N'AQ)mDJ
"BofJYGI69mIB<\um`94f?SKiJk0`".%+*>uK9+)+&mCQ)V0DAXE7RNBKIN)$N;qOo-_['KO
eW_;.\@'7SYr@E;Ng!SRi:\\@'N%egs\X&e$,4?A_II]AmgGrp"Fr65qE%=&3'tXHlIl>oRkX
(?e99^tEKD^i">1W,$?LqZ>,99@o)o.T$/aQ&<EMAIL>\Y3I]A;_6^,:rVDVkR
WjB#62XgPFjpTrU#+1?q3@J8b)U^<H5+5Uibc)7F>/ahmb#9M/2+>\0eA8CZ:em-E`2@igRA
(Y)-rRReZoNG[&Hed8dh-U9not3OCjo@Cq\c>1Mp,#$2N-+"<at<ucN:ANj-7i%#iGEE.eg$
,qfY9(2dV]AU#I;Hd`eMU#R&1%CcUI"0M[VJ8;qXi#6PD\-?1?Xo[.)CVVgcs"j0!\uUZHGl,
3<YE8*H3W3-$_Y_XY!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="74"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="74"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="report1"/>
<Widget widgetName="tablayout1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="663"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="699"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.Fgstc = function() {  
   _g().getWidgetByName("FGSTC").fireEvent("click");
}]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date"); 
	window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/分支机构管理弹窗1.frm&user="+user+"&pany="+pany+"&serch03="+zbid+"&level="+level+"&date="+date, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 68.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 10.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FGSTC"/>
<WidgetID widgetID="f5792a67-061b-4fd8-b2f9-4f2d62ad6899"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="TCTEST_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="url"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($level==1,"/HuaFu_YDZQS/3nt_Menu/考核督导详情_总部.frm","/HuaFu_YDZQS/3nt_Menu/考核督导详情_分公司.frm")]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[     var branch=ObjGet("get","branch");
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date");
     var pm=ObjGet("get","fgspm");
     var sxtype=ObjGet("get","sxtype");
	window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: url+"&branch="+branch+"&user="+user+"&pany="+pany+"&zbid="+zbid+"&level="+level+"&date="+date+"&xh="+pm+"&sxtype="+sxtype, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 90.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 90.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</Listener>
<WidgetName name="XQTC"/>
<WidgetID widgetID="f5792a67-061b-4fd8-b2f9-4f2d62ad6899"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="TCTEST_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="fgspm"/>
<WidgetID widgetID="932e86b8-ce4c-4db6-a93b-127c3099fe71"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="branch"/>
<WidgetID widgetID="b2f4176f-54b5-4f0d-9301-4d7126615b5d"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[     var branch=ObjGet("get","branch");
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date");
     var pm=ObjGet("get","fgspm");
	window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/考核督导_弹一下.frm&branch="+branch+"&user="+user+"&pany="+pany+"&zbid="+zbid+"&level="+level+"&date="+date+"&xh="+pm, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 68.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 10.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</Listener>
<WidgetName name="TCTEST"/>
<WidgetID widgetID="f5792a67-061b-4fd8-b2f9-4f2d62ad6899"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="button0"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="feac1808-4327-442e-afaf-252c3c262889"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_ATTENTION"/>
<ColumnConfig name="EMP_NO" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="BRANCH_NO" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="BRNO"/>
</O>
</ColumnConfig>
<ColumnConfig name="BRANCH_NAME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="BRNAME"/>
</O>
</ColumnConfig>
<ColumnConfig name="ZBID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="zbid"/>
</O>
</ColumnConfig>
<ColumnConfig name="STATUS" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="STATUS"/>
</O>
</ColumnConfig>
<ColumnConfig name="LV" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="UPDATE_TIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NOW()]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="ID" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=uuid()]]></Attributes>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.ListCondition"/>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("STATUS").getValue();
if (fr_submitinfo.success) {
	if (statu == 1) {
		FR.Msg.toast("关注成功！"); 
	} else {
		FR.Msg.toast("取消关注！"); 
	}
} else {
	FR.Msg.toast("关注失败！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="GZTJ"/>
<WidgetID widgetID="1eef2297-05ed-49c4-bebf-860623e8fabf"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="button0"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="BRNAME"/>
<WidgetID widgetID="96e73000-04dc-4b64-b290-35296141a7d5"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="branch_name"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="BRNO"/>
<WidgetID widgetID="b4db2896-4c98-4c7d-ad45-e8fb57b59272"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="branch_no"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="STATUS"/>
<WidgetID widgetID="5feff78a-77c3-43a5-b418-8ad0dde2dfd0"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="status"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);
var val=ObjGet("get","sxtype");
ObjGet("set","sxtype","");
ObjGet("set","sxtype",val);
window.parent.ObjGet("set","rqsx",rq);
var url1 = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/预警追踪.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("YJZZ").querySelector("iframe").src = url1;
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);
var val=ObjGet("get","sxtype");
ObjGet("set","sxtype","");
ObjGet("set","sxtype",val);
window.parent.ObjGet("set","rqsx",rq);
var url1 = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/预警追踪.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("YJZZ").querySelector("iframe").src = url1;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var rq=this.getValue();
var val=ObjGet("get","sxtype");
ObjGet("set","sxtype","");
ObjGet("set","sxtype",val);
window.parent.ObjGet("set","rqsx",rq);
var url1 = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/预警追踪.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("YJZZ").querySelector("iframe").src = url1;
]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr enddatefm="=DATEDELTA(TODAY(),-1)"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[考核督导]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?mcP;b%nEir`3p@<K1^#YB,d^o9r%X;`8n<,A?'/BYPm7X::(C+4fI_[Qg+LaH+VTsY&h=4
O^%[ho/R8K^,@;Uu&oXtBafGr1%C1$!nb\O^&M6e_MAqqM).So\O.o=8pT^@RmUB?hTV-`pP
a\,q4HXP1F-mp?Y4+(4efA(aOMi6aq2!YLa-J+)g?Z)T0.=nUN*PIgCA5>Kk>SrC8-N=>FS6
e8!&lOqJjN%I&UYQe,#oqDt6HhP1#kEsS5hrWbaOf]A-un_VNDN?Jh`#9LVYdW>B,+s5V))^1
i%[XPo)ZD_A[4QA'=3-<TJ345S*cZ*f7Ga9T-p]AO+Y0J$rf6ZEF9h)@/)\p3@E&GZZI!2,k=
,0:i3SLmWRU#Y1`4u7,T24H/ST5U4$'NI<C7*sQ2[D+7Y"^>ThY+-o2K`0+Q[F-?Fpd8-k]Ae
9d"'LiA6P2D,A@/Z#Ch4VD'=ZJ"?N>IUY(u3EhO`j$d?L8.Y3NCR("[+c)^>u`(/tgG3R(C&
NRJCp9dpYDU&_E_`%#gLO9$qN[kR,rU<5%rlCfK@(#-ND:W+;DSBDU1Nd"Kf=)Z68lO^l1HQ
'omH-,U2`$a^)?2-,Yoda_hPbHlGhmCb>33j5FKWRc@Rn<MPQ3gp[?Z=dT^nJBn=U2!g>^f!
2ZmoS9igQJ&Q$r'7>6*T7KdK`aJJ<2q,P'P6n,UG4KD>fic`YusIA36NARb"t<]AR`H\CWHKp
nJHQ]AWl7PSKXLS'>#.G4S\&4SfouAVLo:SA?u<Tg_1<9OiK@.H/&NsmW']A\45mb^@V5e=$`W
J7sU+4!OpPMI8]A':PlO[+F.K-:q()KacG@;,Lc0k>C<9f=gVXi-Xai8)E$le$<XlC_J-K5+^
nidWa,A5[(=W4sS1gXW[t8JXP=fH]ASS@m<e$@q*R[oo,,o)P1`KaaB$rpHK.)og;(7%U$CA^
-3$+HN3&(fSiQ59fUQ".A\uQV.k6O-Ps4p"a'A=Zre!%[K]AhjdnM;5mF]Ar/m&S'H!#5"."fd
a+dUKnNRrTF%+]A$HF&O%GaHAYC;-;2]AlPT3kldVcaMoX-$F)OEfe*ALNNE>k$*b4)Y?J[S$:
Ph.X_BRL+6p=)?@4F(4FJV<<Y#S+077as<lZ8(A8XVFn5d?LgN<g6/7SC#$8@r\A09rTLm+p
QCIZrfOfPjCiL5C5FuHiK?FMg'G__/bOFaM'`+S23_2P\WFlc1G6WGpWu5l2SPqf2EF;,lk]A
I2=?R[e$:*J5qmT(/E,-m&E%%g2hR7R\q.74^T@0!hn`-QY<%j*n/,N/iO><U9V*N$!hjU&+
OfP#g48J0UQD3Ug*YdGHRqp*NrtfXXmO(0[c9o:o`tFf?<&<#pON<WP&OcB$ho3O?6XL9g`$
1#Eo:Bq-0c$kHnnVd'#)m.pId&7J<=tt-Jjcbnr;Gnr2/cPpIWolT:WH$'+ro&&@kCE.H1mP
c#V1\&7B2ok1l(?E!/.aPmMna.m!sI(`HFC>m,i!iis9fMkWslZuP0[9e1iuDQ]AuV`I?jWXG
H.:_a&^$Aiiu5>Q$Uk2/`Q$bc,S3\g7$=R2[L^,<'CL=$_D@Q(glQ;7S:E9l1WafrURL2&g6
=`c'0qT/p$L8M+Mu#=0N_D)Ss7*OKLB2&S6F>Q-U)cgX`tTXm%XgO&A?p^rIGM$5G);\&2^l
LX,rAc3;tK5%37-F]AZK8KNmnWeL-iZd.&u`:1%fEq69ZhoaSU4IgbfMS?ELoQFU;WM-"`;YF
L`]An@/D+4ZPBJ$CjN_,8icJMG[snGrqrB.D8c\!uFsM[amooqlo\<q;)QT0Z+LR@=pg9)r4F
5P!6t"QU[8Mp@*0b!_2,/A<]ApT^L&I1K3-/Ua&%_a.etI;21MbB*C=h]A'tWF:Kji?0`qOM\L
43K"NP(<Btp]A01(MH-9`Ma#EaRjA.p2-ml0l\"4EbSfYNogAE*Ja/$>JPaOt(XPf&"jm]A*cD
FqF(NbaFp4'D0A;/adUOO0)I@ZgR4I5)br>t\@lR1SQn6Sm$CY-A[IM*#H\tg(bHkuhAlg%>
M*91kYOeW0tNb#]A-4-Bb1!Xc)IZ%b4s34Q>,mPbEaQkD2M2pp4_kq$1"b26a=]A5T$/T:*TH9
)i&ffdMl!d#`%!nAp)=dXt2BTahrZ2W(P>F$dflY,QSMWo3e!3sa##"tr)%B6O=7:Xgq9hBZ
4\j3OPLm6me`L_M:LD#KQ^EOt7k?l'6$d0P?eG:EP&&,N8D$("3BCb2*<>n1cOqT`9U<_rEM
"X:%OL[=\#%&:ns=*PJF&NGqQ1`g]A`r<FO6Ek&+6uqhBr%nh_:0'%/kYX?k<`d6DR'gL:rk6
e?aVeb[%6mK$uB6o!L0Z@90q#m<6h`.d6K3lWVdO;?`Io$ZWRALX>+u'kb\21_(9m26tuRpZ
`#tSGcK=Y7R[iF`/CPmOm29g0;Lk]A5.19B^/TQoP"@N<j=2C$$5`lBmj`O1BecqAF6--p4Yb
8o8H6*M-Ek+7fiH<8=^Kj>B(u(FY%&YdCs#4oF/Ji#H/Z,H/XN\s>i=EY^!V\]ADYE*[HeJeA
brKJQUDDi+8JaOAOE:6&?[^&a?8O%4!CdGWo4.LP/W]AL'hXe(MdfcfFDT?Q,"JWYK5-98mIp
O=bc;,'^pNusaQ,=HY0SV<@@16W__\gBIL,"Wq$tC-l(re7anLX,!hFm=n1g;H#(+8]AHGak$
D&?5Qh)u)TjR_*t&+J&+ICmqsf7<$<051`duGKZ"KpVhM5EHuUaS$3=EYRJ^Z%*Pj`bHMDuY
RJ^Z%*Pj`bHMDuYRJ_TBA!MmSJdEUqImocC&bg8H\HfEchn:~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="15" y="15" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="STATUS"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="para_sx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_yyb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_phone" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_fgspm" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="2524344b-a058-43ec-b648-9a2b926d2b94"/>
</TemplateIdAttMark>
</Form>
