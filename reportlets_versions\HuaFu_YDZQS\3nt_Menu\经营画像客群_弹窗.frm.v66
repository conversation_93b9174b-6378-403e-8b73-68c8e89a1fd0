<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT DISTINCT ZBMC FROM (
		SELECT 
		     A.AREA_ID,
			A.ZBID,A.ZBBM,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBMC,
			A.C<PERSON>,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群')
		ORDER BY A.XH
) M
WHERE ZBMC IS NOT NULL]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		 select
		 branch_no,branch_name,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2," and up_branch_no='"+pany+"' and branch_no!='9999'","and tree_level in ('3') and branch_no='"+pany+"'"))} 	
		 and branch_no not in ('2097')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="company"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)  
		AND A.YEAR=substr('${date}',1,4) 
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')    
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	   SELECT 
	   CASE WHEN A.TREE_LEVEL=1 THEN '华福证券总部' ELSE A.BRANCH_NAME END BRANCH_NAME,
	   CASE WHEN NVL(DNZ,0)=0 THEN DRZ ELSE DNZ END ZBZ,
	   A.ZBID
	  /** CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN ( NVL(DNZ,0) = 0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ**/ 
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID  
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+company+"'"))} 
)  
SELECT * FROM (
	SELECT 
	DATA.BRANCH_NAME,
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	TAB.ZBMC 指标名称,
	NVL(DATA.ZBZ,0) 指标值,
	TAB.XH
	/**DATA.DNZ 当年值,
	DATA.TQZZ 较同期增长**/
	FROM TAB
	INNER JOIN DATA ON DATA.ZBID=TAB.ZBID 
) M
ORDER BY CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END,XH

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="单指标查询" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="ZB"/>
<O>
<![CDATA[股基交易量（亿元）]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')  
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level,BRANCH_NAME
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') and branch_no not in ('2097')
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 			 
) 
, DATA AS (
	   SELECT
	   A.DS,GS.branch_no,A.ZBID,A.TREE_LEVEL,GS.branch_name,CASE WHEN NVL(A.DRZ,0)=0 THEN NVL(A.DNZ,0) ELSE NVL(A.DRZ,0) END ZBZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   INNER JOIN GS ON A.BRANCH_NO=GS.BRANCH_NO AND A.TREE_LEVEL=GS.TREE_LEVEL 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
SELECT * FROM (
	select 
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	DATA.branch_name ,
	DATA.TREE_LEVEL ,
	DATA.branch_no,
	TAB.ZBMC 指标名称,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	NVL(DATA.ZBZ,0) 指标值
	FROM TAB
	LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
      where TAB.ZBNM='${ZB}'
) M
ORDER BY m.branch_no desc,CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END 

 




 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("company").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="11ab8167-9496-4e2f-ad1d-f6ca2475e26d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="yyb_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="company_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WDEPA.CAAk'g!USB5n;FV6)dKHA!((3Pl!U1mT<OrJ_5TgqePR]A1:W.??f0+j9UM+WU-+Y
"f1-lAZ]A&rIht#e@d>F7-`5F)F'(m^h`TXd(!Mo6t'?hXB=IIeK6Vg/sd@]A(ai`!!)Mhl"*4
d!+;_[enb$+_GBZ>hS03o/9dI"?FUW%SX\:)r2H;MGbO_@d`rs_8C6J/SbH[c:G2XW2o"GCn
I**h#!Lg_6'-'5cu?J\YI6B]A>Ln2LIq3`UqY*.MYC5dpgO`n9,Lltq[Lkol0C)@#"/Oh5(42
Rg`DTASB?*@8-:7;@E=S6dF_)0N?]A'=7SFq#13X5XI-JChgOqJB??:P`<G6%ViM9._+]AL3Cj
LB)jsmCkk%^QV@mJ>3l&Gp/o*""<mGi/b5:Vcu+IaT%mf'JqcP`>TU4)LA;HXF:;bpk*C/nD
M-0HON2e!.\;EEa-oThrps<J7"2<LP=*-o9]Apdi,$\6R,NMtq+po1R&HApo=o5Bs$`7p1,B\
me,b*a9Ra8]A!:Zl=hA$K`1@JC&O(r]AJ7oPLTor20($(PY]A>.*\;K>KM-%ohU0lhW*Qh5c!j>
6\&F)?*an[i&CYhS"TsZM<!0o6k\o^srqNpAVKMkO2DVbq9HE>6cqh]AZ3Fa;R]Ao5eg1PK<XY
BN"=$KM[u$_NrT<IaX"uo9rCoIC977(4m1HSR==CHAVJT0GP24^)8LCSe=5qiTn,\[a"-AX8
12>!:5rc(I-5Il1<]A(pfdB@p38nb6rmk9hmG=`t@+@e1N4sGB"jMDn:4?b?sXH/a)E+I7[j/
^2I!l!JoT8?P>dtm6f/I)<b_-;M3!VGXE,o:@XBRa/->FZ=;bZr-o@TQ;P^'W[(j4dqXm&UI
7G,efr?[b8>^kVN5VMB5P]AuP^DK%\pBEnJ!@+E`@%B'A=NqEd;h4GFT*)hXY(XS%=&"HPLYS
l2"Agt_(%NF)',VlVf:H-J]A$I^f[u9+JW\2o]Ao/%b9\NY-ZcMX<A>XH^21`E>U._P:L+UN=D
UM>;6Lcir#3/8["^u$:?2k(ebY5LpT_tH7^*7L"@<e*srHH;kO1%<$c:#&<D_,X_A``_<7gi
%_O<*BZ_]A/.W%%cG#bdgbsp-+U3_DG,(Jr,@ONSB95q1BJc\'h6?1&m+q/]A@PXms?5&FGPSh
[ddd\%3f$4\&Un0bNTUj5_V(4u%6M$sjHPL`3-M:[)TMscT#SHBla3:%o_kb#m]AiPOKAN]A^4
KA"6*TVRFc`bg1cAfjK>e&WBu5IZAK\_>:RT8&=lt7./f&#NhciP4DI,45LG2eJgmA*2X%"<
f?"a%6==I%O/p`U8ARHY#dpn?JlR%3%-En\s>?P20q:J*c(-/";[*G)l.nGJk7SgJK8G56Om
I_k=cH7]AEYb/68W\qSCmb-S,/5lJpIr'Mc6afWnN@O,%D=1lZ/UC-&tbia75T:>3SXD;3:B&
IQB0l3f./r7L+G_-6gKp3&ZV3ir4)l]A+Wgk1HcnB@nDJY'D`YjM?MC(7EYoMh'MLP*SP-sBe
P;Yn7XZ";J`sGlm8MZJaTN_Kq(>%diS#EgtfB@03C1VGkPS5W`dr*3d=f`hHso]A7p(++CcYT
h<@H.P*5+ptUA5dWW,!1.KQ1Ui]AZDghfa--N`n8Y&S<S@rk[)+hUcDRu=EWhVAN>q\U/&<7`
In.=hVd^`Q"BTn(0^YXHte]Ag*@ejJSmsGJl%XfL/\FFXriXL9n>/kpe7+29[Bs==3Y]A9U/Vh
doU3M?QiCh#plCGj?k/B[tot,R="gof<A*F?'#P>dbeG\(4Q#2>p6!I4.`uX,F8bqplEBWLD
EEn!2c!Os^j!@t@kUi=&h`2`uNR+L/i_2hY2\nt7l\%_Zhf7t9#X!,OhoIZ6$As(+2\siYM%
)pBF'BOYGV9=43#@d88Cq3g,1Un&oJ]A!-Al_Lu4([4gTmqC.F7^KE*\#-I/;",L/'BEih<&q
,aE\YX`3k"V,QhcfHY4U)`s.G<>VP`_qQDug\W.K--`P2_?+lA0.eIDRe2jb6i6PHLOR!b&B
Z)Ojn\uM4KA+Y#SZhA1l_O?K9ooM8l9i_6AmG_Hf%1$D'S42=SEe(n=9C>3@kA;%R^c:>Utg
T$QA;)6AiJbb#hb%/fm-INqU7T!2Ft'WXD>W-MWkt30>)SJL*u[;*!D_bbS4E@=udf4?%"#"
O(1ftNo@5l!eR-tOk2pAD%e</VA*:B-GoZK)dMsZqg4["1s1Z)WRSP\*h#f$W%&C%.lD&$c\
-2]AfG$B+lRk"MWrdp/T052e^=*N=G8J9IJFYkZg8@qXc/1H]A/hMt6?[LBU(HCT7Td_jX^K6t
eW`EZo=m1N.[3$J2>O3MNo^'a7e3SA&/'I9-P--4J!kag"0(feAF<l1sPl+O!ifU*:F4RF=I
Ea4R8/Ki9bOE*[E("ae)j6gNXu+mE+WpfV?o4@2_Xf^"r!=93O\N:';tZ1eI&Ou[B0M<I+IZ
nR'[/ja+_cCM\6=^LDd]AihYQ=`J@o%;7RSF`-]AK3#L+l)i3J%TWO_^YH;\OMIg7<"-6W_1LI
A5T0ujjo,eA_Q7+Y5<pM6=!M!Rb)2qFZkcRoKKpcY5)!&c-Cj/BH<SgLeM$1lS3OjlFJD3ZU
\!WB..\_l<n-plprN0k)oV3BUeV#adB?<8I%m'<]A6cBP2_G&1`dK+5*F'Ih3aK<--hmN^RE(
k?fi*7jV1Y:HKc53_VW2Va)PY*9/;,ne427C'6^%I_FMq!R$sqJ#[1J\%.pMeV\n$5Y"qU/P
Gh1c*Z_-j:q[atUhu]A-du*Kkn9@I-R_=KT<f=aH<Gd\IH*q&3Kr/V>fVWc9>*#DJaTtbA5XJ
Sdh?a8RK?8;&c.L[&Qb17^Y#]ARPpPSubVoGC;/1%YcqYFXPalP@?0hrIXXkZ_MD.O)5-3(70
SsI[*4b<C:HN&5cgkW3mOo-`R^(OTO(.7g#6[n'c!NIu*\65+Oa!F8pIZt=^&*qcu2lSYRST
)oeg,=O)<T,s0$U6MN93^`ROj8,"D;&g^Wlog9l((#0+1%OJn*WCIdBWK*9%J,=`suphoNW[
ApLJLP8m0ZXR8)VY&6.'ZA"i`jSBB3g5gnZ3LL&iX%nJMZJL]Ag&Nb?kOr?Y\/JgaZ1DmfU>/
qV06(gNs0[<n4(c04JO;3m%9'&AP$p9C"/$p(JsF"uhb#XmNKU.8eB]Aaq#^Lsf^M<%E[Xkl3
Ds:[[r*B0,KS1S7/F\7i,R>-iB5cSasmhqt9f&!WhPn'rDYORC?P[MC&knirItrOKO$AoXk9
etEk&Ts)4`X-qeIq*"9H=^u>^5_ICrC*BM*d6n<hH9>5Cl>*+p7r\Bg1e*r-<'o<O)k!`P2a
R15l&/j5JU/<H5bH&k39@`8@\%$4MLgS"4^H"]AI!^B^6h4-/WtZkZE:l9O7%'ib!lcT0W49#
gJiTXFbj"o6#1oD/Jfuc%<XD!<XmRTc<2DY.-[Jmk]AqSZXBVA?5Wjfp9jbA^Q<_p3@4NN*=`
[.]AiMSVF-"?)XZF=a8128W^pi/`K-'114pL-[An5/&*K,5Kl?@nFFdC$2RVlFf_P>9K`ark+
j,_6fKc8qnAhPan#$S1*T!b\Hd\cl,ecTOK_[g(KY%mas1MNPU#1pG[S;9RO+1eK0shV>B''
_!1Mc:VXPU?W%_7V+YHfA@N(e&ZE;/K0`\;:%O<tj^rloAnAoa!Jf`qd3_`5HTU2A57oiB`-
PZDDmo6U?:WP[D*1!pf#=hWEP`m)b.JO8Z6t.9+1E-5dr/_:!!RW4`&(%Xo7fhJ,f)$\1Z4J
!U?MJP-oGt!DsgWs`gdn8/2&*<9F8pscX)t%O-`<n2Xe5c2T]AB*&Zrp'RPH:B4<6g/#\@5na
Tc']A#FWu*`]A2_T-mp:567GM92?h\R".b`-jA:5%G,&)QEVe4)\59D0HADmo/f<mO9t8e+5gF
tPi!h;BJ09P32l=N<=e;WG/3Y#4p7]A&B.a;^Y;kBn#'`L'\Qmd+b4<FtO/j[rA'=LWYckniB
(mftHPJ5<^h<?%`'LY[Y52:7f$<7`1F;>X\iB(7_"*YC2+J"OW+%g1t;*B4j')#6FZ1Q*HRX
bq1XP/iLF)JQ&Q-#4<H1T%NKu`n0F<l96[1*kNUbAbOGUIhe%T3nj99$>)l!*,""_qu-MNgj
#>E;n1F*^^9HJf\X@74F2R7#SCm@SMF\o?$hpY#5%,<jdB8VON?71l\DXAJYNM98rj,dZA9Q
0h%MAk"k?At/39Us=YH7Rk/t1*s]APd4]Ao\e<F<F21,^%j<DPgZ:@FN!eloJc4CZtV,;EkSDf
FThs"XPQlo<+.;S)M&/fC$D;E@?<D:!m[!TB<^hd.LZ\b)Ia,M3t`pT,5VJj;NOne,c<tmor
Qs;WE-#]AX1\+eiVG4L]A*MUj/%oFLHR)kahj8&A0d4JdWV6LXm;JDRUmY=%mCAa?:!/bAY<p3
1F.@J?DYS=Sj!!jCH)i15KaAoF[D1[26)*_:/&Ugk;nq-\"XM^P*@qpr>?['#PM6_$>N'Q\#
CL^K$+N;O[_(*cCJ,=s*78DLA$rX0uS[I0S>it:;:&lPj+gbbcm,1S7_nsUfur@%E;p:YUUB
ZHA'anOe!_pc.h1?<>=IplQ0PJkKl=e:`PR,i#+e7Qk1Z5dR5Y2H:Rg]A?dQIi@;8@Lj+_B$q
oFiFMb4P@?:;R1-8@E3J5KQVJ9<&!c#t?)n4%'M=tU'rKRnG@?2O]A=([Oo7o2J1]A+GZU;N<S
W]AmnK1=(;l6.ScV6)k*U^h[FEM3f]A%ffOa4"&Iu<g%a=WF+B"]A`gI\?"GhW:dhh+,<(JNq>s
=Hi*j,"iUHF-&'Pu&N]AEUQG$>%_8qBmDh!OG3.YZUHogI`,*Y"J^jjLC<,8h.[+35HLc)`D(
`U]A1#MqI;:^BiE&H^4(dFGh+Ta_R11dr1m!4@Nh8CDC_,V$`=5=0Na3]A^4O_Ka?r"-DmmrWQ
$7NO_[csM%mNWI/]A>(<2$#aU.MX2]AeKh,BPE&fFW_/p&m'7(q2\["H>AN$U\&M85e+RO60?A
j6730[TT(aC@522@eWOfP[2=Har5.%D2bH*Eg#7?bD4e'Y_Z'4:93R]A\J[lKoFV]A?E?\Fb/#
Ea9)dZ#PSIZU3ZH9-1@q9"0s*`Vn2#JlLAp]Ar$T(aFt_(b:1o4K0A`'YO,m'lONBLh+*QH\>
^Z;gA"7d]Apj?&N)c&l^A^:,(.##-&NL(j$^K;i'4^`9GE*W15(I.9#^H[&A>.cl]ANZZ2fJlq
\.M?l^n6(VIh(\%C)![S9F)oiSfS:!l>[nm%Z:1kfWqgPm-8d3bQ-fLSbB\i?[gu#1IrC)Ia
V2!s,A18Nai5"UN:S73&pd6@?<\^@Jg3A_B,\+ST+qhp1%N=\_;bt^Na!^g7c)Q3I.=p)&7N
?@q`$hI-Lt4`ET6po9Lc?53#s5R;K*C)P\KbN$+ib`"ZEn)HZ>h&b/..)h65gqrYnt671E;7
8!!o+l[c)O3LYJk<B>^k)E2OF3,FJo=:Feoopi)*J;72OkT0Jbq!DXX4s-<O1U7gqKNWZ7:>
hEXl!b5N>1mn3-;`4`8YVF.8^W^"0(ll$%>U-S<M;;j;09jaGL=qAU!CZmSCn-QIRnks(#pd
>pue]AL;.0&:V=ueRk>.dL)2<'o*E"+@2*-'TUFc=@&!ckE1Q`SY,WOJ\IP+0pWiqWU&QK+2E
`oPnd6KeTS$pF.=4ZWF<FW.$:qCJK;NbV9T"kM9kM.QP-'M7g?30k6AZ4gZq`J]AK1s*^3X2V
Y6HargV>G%K9U'VR!^"o?J9C,h%4_Z*#'<rt2#ucg\'6Qu(4D:?&47A:64QPPGO,Zp'WG,@?
CE0[M@i>Yf,9-E_VfY85XiV]A(9;CbX6T]AC_(4AuWFoR.dL\A8iEhcNV_TA4p]AZc>R>&d"fmc
]ALigr=.Prar'A^`hn/^Q0rt$F"FBL%4Ken)mF;<4b1=AbI?lNASMb."%Sb"IZ!,+e>uJkDH5
@o>&)4gJmm4^p*?DWr1FMGDi$YL6E3F4b>t@chjgs8O!imdXL1uaq`]AlnUs4*eiAih=[o)\'
gIS5oV7+,FsW3o<+^KG?$@n=/C<bBL#:"/*7&3P9653-)0)C@-Tg?`p0\e6I?&9ia/H3aNV5
'"Joq.:B66dN1jkK&O:Yn4Ts1\d7Zqj@h_ia3O,]Al:N`Q#fmGhTEM5WC?F+We#%rq>nBrXou
cn?[c:CD<^PR<u\qPd*p/"*oi4k#g;OUSeJ[eX7@5b_njN/!b1XE[q)@ZOP)qm`L6aLMZ<Sp
V)8D`ce(EJ4S'ler]Akjo6Z,YD#=+)ogk!-AdNFhMCjWp2jY*gdD,<f`h!Z2Ja<XC_)(bL2b@
Y-_:-\JaSNZ!inYV^D*MA,d'gQ.\$b8^KBO@`bU<mi+6"G<%b.N>FNJ%`.U>)A&2+Ke6q4k:
3-*jPT=IFaDEGcl'n<o7?rI_h6$C[T[!0$HpH]AL9D=JGi!'rKLt)hN5!&mKgWP8"=u>0^e]Ar
H2<l=sNX&jtEZ8@#4osQboii([IY8Gj\ab;eW=8ib+2TUa0[gj6C^#U'4^-(=mgNM@U\]A@jN
L=oQ2J\ljM6r`CVB:rq$G4Jtr:71ZBclsElPoDM`.^DORC)CfKFr4Rm$k>_;16:@gN&Y[9U<
_*8(N9WH?;,9seA:j6NVuA88!UW2F03W6o4XCu2c@'T>g$)Q2TN#%m3`Qq@u*i\8^hWG2K=@
53-A!K%\8uX.O^[#;:0r+&F;Z/9hr"l=4K3J?8=s3&u]A>A!j(lR*Y[_K7`[SZj2\`&di%boq
6NsSEqfs5OeX]AMMX7$Q$>45RCpe5b,R/g$\uej!GMJ4i;.c2^@/u$\/V(+'F_F)[c[(VSk2.
A9*]A>+n;J1u/(Sa?p1a&M[kVUmi*>^Rm\7nFc#5]A%)=dZo!+H>-\S$Hpnn^0i13Du%nFuFZ"
q"Y01Mi)NgX#,-Lf:)MMlc)@;QCjB.p%C&B2Hu0%3>tt[5C0Z'X)Fsh\(mF0b5MG2k8.>QWu
QAD3%:^Yi0A'8.o"7ne%3VUf<&YZO7SWd!W`')Z.QC)DtPrIg#-4D6[o7EWK@`Bk<<A4/8ME
#rsSlAR=db+akaZDbfm@UUl%<F_qo+o3WE0R#E7TE/\_8c(=Y*P^20kUlA;:$k-Pg$MP[bbj
`&i9YZO,*OhP_HciTXH!:WK7-:=R7)q7mT;>TeYRto<f/SA,]AnkN[NU*kqW4TN9UE-XH($d_
GeJ&3"o$eRUcs#D;F%-deQ^!2;L?XY^9[>,I_7FI\plDqNCBI!J*KrIqh4sRuHJ/i.sVn_<S
*l0<Gb>(^[Sdt>6!"it(pFs8O)W%aL!._EDp;4B?`N"tAIhr)]A)r`C!n\ZGA)[PLh;,7Rp4h
QdY!.K-7[n;j,7ILgMGjgZ$[+[a9P1Eef7++I'!:R&-nVRiBCYsV(gc@h=m[!dmR[XJ,&,qF
@QC&8QoJGP"hR"TtJ%jpdf!0Snek*K*2EBe>lV"[29,Q]A=kFmI)ak]A&pqH:b8ZWMi8iG7*t7
1Ak:Hk==fIsCMe4T3Yq.bME`V#:Hs^Y7S*'.6b&;&"acnDni<`hJu]A[DCRp\$QI,C_)aV-b?
3WOl`#T6BY"-(U4H*>MCuKL_XT"C#dHr1V1!tU>sSf\co3'&OR;j>;<Vi[7;-gTHV_,9.a$D
3\-i>b.uGfDo;m#d\t]A?:F;oLB+/rT:j"rTe5c3+&lUO7*05.%N/5GjP4L*3kW[\9A]A/gMjd
PVLb]AkSddlI\joR&l:p3BetF+>p&p+S.&\Z&885;frJQHiu!EGs(lW"Ss<19%nb,Eme30p.?
o32>>#plIUo9*`qdL\d*tij3KXM(g>6"(tFGYmU*WYAkuO@41m%!WDcY?Y;f4Gg\=T)XFVY2
bO9HSa+_k=BHR7`\(sSStBA5#/l)2-%5dNghio6-Pr>S+oC(24\QQIQ:0a<Y\lM.*/oe]Ah`5
rKjb5&,8rH?c>auK!00@>HR(ZiJHdZ9@93(@RL0:[Ub.!\7CDW#j+@W,1=)"QS/1VD)8$e83
&E;hk\aEL(CD-<PYmt'b<m?%'O3kN=anRAj5I6$@[UED9I4_VKk*Yl6'/FnG1iC1360pa,0#
Cc/ja+>nKr&P_rP?%lI:C3k>nF=^Qn4XTP7B30[Ssis`3)F*Sn@5t-Wmk23iN^@mO_LRldD_
-S3U;l%pi`QYrZC^$i+^.3k'%-j3$:pcfX\"'6(s>,,:*lp"Wq(nQ,JQYb11K9Vb&3dF4A`"
ErMG9^e`^U'cu\?-&C4LD809'2%DPkDd*PjM*a;eiN_gJ90Qr8_E%4j0G[>?gDi;/5W@DW,6
#5DEf,p\P<=f6qu"gZ;Pq@qaW#.GX6-gh)i[gMg#4:l1QqCBhcUpnk&->D3HfLQIkXgimFpW
Zpr%O:N?603$UaHOai+dT*>[,Q:]A]A/s/\dZdV`'ne4VXj\r&XA$Blh4$o$WWAq_igG$:4[8L
r##BU"t68S$j*0lm:*!bCU"Y[:lDY%C]A7Pl',o^iL9B5rcGEn6]A"O?ZP%,9>">$>ZlLHDVd0
7T%emf'3:;Rs3lhIT8'SP+.@`TWArFdn7ldXADpq<CLGXc0MSq;cD-KlUAF5n$,?G3l3L:S8
n_"oUKd,nDT%=2JReK)=,cj=8rmqeQVJP"(H\ZMeKg#-&^6D[8W1jl<$t,J&1%%&j2KU.31:
jPR^XS;BG5Vg_pP_q4Cm`-UsO='K]Aks?:NZG]AK=ur*`U_Do5IY8I3/E[I##=EbZr#b@#Yq9E
U2'mXD8);*:mR`F#-"WQWE+Rep/!j:&Rsu.Gmuh)dR`9Cb+HEZKa8JFTule[MRK-ufBn*;n0
/*Spn50;ks=_lCDrHXlK"3T4&g8G6(MU&5Ud$lN:p3<=QDR?i3["X>2Ft1eb9[L+%6N30nO*
oZ=XW%)Zl,r`hG9X4^/77-^Cts]A3BeUVKH5t##ZoWl=oC.Vj)0)O`GYDTP#9HNT'kZi6"Fu1
#/<T07LkiUm3qXJ4**#(1uA`93r++BbMU<WEq[#4>uOi65($.>h&.F4%M]AiO0X9`[":e,kjr
XR6CH;u&&-kR!KHd"aOH\a^8Jmb%*VK4)B^_bI^W3g9SPAUbd,N.i.O[n6SA%V.&"HTHkogX
<Cb<-N>@J:.=8fi)]A0JM+8;LUOBL\rn3fbS!r&Xk2^_>#?VY=#i92H(cW.sI7RGp0ZVm3&.&
\)I%O=F<>T[X7P-Eo--I1PlmtduKQ0s&;!8/M-oC^;>Ir;P&KqIL9F16e218<5;<8'p(cl.,
U8,(hoc:-tD`<40KN79rNlKuhWRWtWY4la9lbf\TmQ-UE0$<25<cJnG,f)ss^KtNZDnU5&p)
P\k($_CR1g`WLTO7IBcYSAM.7TUU*Z[Om":7CRnYemn&\iWqH-&c#TRIusOJHZF\OS_5<@\C
\kjYCRO!RJ8)noR:&&P/G/7_CXEUgCkG/[!7nGmp>[q_%)$DItVq=<5CTd(j69BAX]A+U@H4*
^o8.S46o$Z-HJ%=V!n%/GmjulkIMt?H/H"d#<#As5qnk%.m&a\S]Aq#fT;=A4SQV>2UNEaB7_
W)\#_$+5IU1Ma\F]A"pn79jCOOQlKPU5DjJ>C!5OHa]ApcopIdTQnkp,fhiCQa:;MF]A?ITP@C)
#6q%?]AY&e[K6'?#G)tJqYmFBg3jkSX(S6EJe8g0,-mU[:8+?juI]Ag%,g$Om`m,KAR&]Ai(S&W
*OcWR0#>'N7P^G\/P1NRd)CtWH9ff@c`fllQ_&RUtJ+D3P:gZjMsq_eo>+pl=d3pnrb0,0R%
[r-(E>$96'Y'[kn/90-nl,p-q&$M`pZf!t@N!.GZG*O9oRlKOrIILQaE$9<fA;7;&rLpnH?Q
eMk:.1u1$*?GUQ@>!m5E(CM8N$cJIVdRNk,>[&H-o0dq*Id>.GIC`FtP`Q4"UX4:.S<ANT:n
cW'jfItk%Xb3(gB#&X)R>AP(%YJYpt6*EV([.*kZnAt_p$MmjMi]AJkWin?.=<^DY$ceEn`I0
^.66-bFd7mqB2^'b4X3:jr(bec:D/F-oR_O8pT$K7e<?2\7MZnaE<n!JPJ/EYhcS`0D$2F!>
rUR145[kTl83$Aa5PmF]AbC7.lA)maUmug+]Aa;o;7SM=uCXm8eTeLZaL=:0TS"2Z'5*3;.cWu
'-e`U>I4-jqt^M6JfADT'J)'K$%ngikgba+k-b9V1K@'h$Md,hd`]A1JT9b@dWLJm?,FaUHM\
]AF,7aq$O]A9B;H]ASn6#lf79(2_@0cXLX<b<:;]AsCoPkH#s"F]ApYV[*NmK`53e]A(_^d*2Uit9o
De4%fFc12^LanDA2\6RVS``$p`,/MZ/7h"]AZIu?[r*0iRf7$@T$aD7o\XD<"nSgZYCf!BrB!
#Ek0Xt)00^25IsdCZ!##cb2S4V]AoE&=_QOiX=Q#2To4CU6Ri`>o44'02U;:OdQ-DuJ+t\aJY
DAaQrk6WV4Ijh/o_*U!chbM0>j3eR@j0[0:3'[33FFN@]A",+Dl/QeD4i.lmB-EbabK``8ks1
!@K`lRk3[3pR.H@C,A*7>*Qb9K`Bci`!b#4Jfo[/[3)Z8WjbO-h4STGu!fHL:0Q_@Db=W%hI
+-KCq:M=E1l:K**As'Ad"V7Z&2J7F,kM1r[)a.as*X1('J,KL4SR(hk<s<s;=fEW!NM9CaP#
>QGD%]A!tlVsdKQg?pTO[#GSF,VYNcC(5qf9.HP4bhQ:htIQRdk=X4*H4UM^mn(WIHocZ0+mF
VT[g&&aPtl.cM1NK"urU5WNp)uX_l#`hdFR0D3\ONT<Z&!QXLH65:1HS&`Mhp>kJgu6@;6M3
O<j3!sTfaguWT,O+q&f:=1MLB8fGgf^YHK%BLYhO$W?nr%gWTSJ53d:('S`MEHaMh4<5!JRD
>"91@0Z\'JQk<0($NdEeZfW)=mK4m1'ZqS12WL?/pkZIA041&Pb*Q>J\1YoZ1:7E&HqVKFRr
n]A-2Gqg!#m9[F_iWF:kSiUKeT;D/e9^Y-pA$[n^lMql"go!1^g$s\c$Yn4aXZc-k[E-HMpSh
d@p2HC7m'fO="^>Qn]Ae\=FUZ4gMfG"b8>*(^&laD%P<C3q:+o/Td:>c1oN%)Tg1WcH^R/Y*L
(YQqK+ZW-,=RDq;-F*tXLTCR@+=-lG4(#Gh*-cmA8j.03+VT/WsNFZr*_8\VH@V+n?:BUSe^
?t7N&sD7?@Kc<cm$.'!=oO9C?3OBbY_&mD@\35&FlbuE2AejTq9+TlZcSVLfe%D"]ASBO>jV&
foW_5rQ4%p-ApeYM-n^+U+NRR`b1`_Q<kFe3Ll;^L:_(\VUf?lQPT[mYT8V@5Ig$b]AMISWI*
oC1fjlf_![r8H]A<@+,f>*G.1re9QbG*<K.HQQt+^\@4VXEGn!WCtWs5m:`11:Ror,ArDhPC6
*f]A%?(UoR$(pf^aHE^`(@R&JEgHS<YPAM/47.4`_^YtPUlemB0WtbYLOCfgEJWgeWkb6i+$%
Sn`'2lbeu0@m;<dZ$/\HX1I]Ar\!BY']Ag$/#hkHP6Sb:/E"dt^&(IhHee@E[cEc(A7P'7$#V1
Ad3oe*=sJY2,YLdXYNFkrc*:[!/]A:87"SJFf2+Sq=MtuGFWjnS,.JTCF")%-HH^rQDkYQOSJ
"P7TYKgEAj-5Ba5%F]Ana\P'il]AJes]A-K7fj_97pG^V^OBXTlX@?jrZuRNLCR*aeM^BFf!7]A]A
YlrpC0)ZI:/N3R*hKLLeI94/V,-/"eL5*_YrL;sh)*2%&XrA*b%j,nBq-@uN_efjrGFEoG5,
'&&s&YO&73j6_a?S)]Am@4#hkLeh`$nTZG3$-LO)8XkdVU+j/B?K1BC[)\9OKR)il[d9R0m5f
9b7:J[1-AjQ29p5'6lm)NhThdaiW]AohlR(mSq[g6HBIHl=p5Z0s**7KXbaugI7mE'-2ufn=9
;\A9j(t.8M<Wm'$uHmJT[A<fUqk0=$XS'n&U`oV,Ud]A;nu1<U1DCq1?P5(-SXh+7k["Xt$,7
dLnb%$e68acKXQ5jPQ/rTFg6M"Oak_ES4AGBUY2P]A?R+8$*9^l<H5(>D/6*_%;be"$ZjlH-P
dpu_=IdO.k[iY]AX+).]A&r_CjNg^hbC2(n(6U>s#^4V#AAL8d#=7L\YRlQ+UT?3m-bPFMSO=1
gCf%GG1&ro+(&@Y:$/`Jkg_*]AY,T_0k"/*\&$dB3M9C,!d.Wjh0&t+r@DFm!7OlWcCNobXK(
"b514t):me05O/='*IU8oBS2k`IcVt!`AiWi>o=KZ6;"fl1Ipi6,hI.?[/GWFdn#>iUW_rmN
\i4aK-q[T;(#M+';tQg*M8IuG3.1ZLr_0tN]AmpO1D@hCdhhF_7!Xb-@-_iZYk?0t!-!DU<`/
ntG;!h[(BEsS7[SJ<97@i:L:?,YGT5Lo\)D1K)r?=YiGEfYrdPH-+UBC]As7X8=5'm3pI5aQc
h>Oe]A`PoT1htQkc`D>CZh?LI*D/^HL#.MEcE#[R:\%up#)r(Y,_/4-1g]Ak7(D/^Hlrt\94.-
9U4s)G`cC?qFiOd0A*`BS8>(B~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="104"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="104"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="1"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,2224585,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBNM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&;JG%/H_.+Z8uJZDd4&k:SCGR6:iRgTYsf<X.g:!q/GC/"OdRZ+1bmadgr;119!QTG@F
CB4Kbf['+<a0>6mQr/pGf!Jchm_9rqBZ:?!\?&kB$'4TATGrhr!h3E6uL>[>)E8U.'u@]A0ge
[(+i^ES,6F('GtP(\(S9);%V'%pFe^e"d?hq2m/^!,2qR2?M2RSPJV.BZbA*\`,+YgW9lIue
%U;;\pK:#Q0h>;Fk/V>L/qEeDpGtC&GR!PeSC`-.j*ETDtdpl74357`M?WA0sa,AFOT*hE9S
5#XPbEqk"ePq-]Ar>QLqIuDk98cIp`\8VGibk:Z>@qGT9ko4l/MDf9X36CcAPnj1foIDO48#9
qgLU&-\")PLYg3kiVR.cT149A>3k/Nd7p@Y1QT_R"tpY650oF;+63oHO$h^-83jrcqMYfq#O
ZJ))nc.^ria(,Id7^7s'O5]A>E:jEE;cOt>o?Gue(@F6+`t(mS3LccKPd)<IJp(kduLt5.r*8
kec32$eFkOJYSNRTE1^sSheV;9:QRbhRoj;VkZ\T5nb'C\<ppOA[XicIHWGR//EW%uUSC,o<
(R2Kmc$ajSa*qO);sb=U$eb$IpT7MIK0^^J#^h1iqk(>h-#BUK-7NZ`[+^ddrA<L^)MDPl(u
a4Z!%^k<4=e/+1h<t+eL[k4m]Aj"080YX-9/"2c&@'c2ESPfrT3+Sld#dq(=t/K]AjifM]A.:/Z
cRZ-]Ab3jCErWne*c-TLL5Ckru7rt/+5=D7CI#*?5\_tm,U&QYd`5F]A9-\<ggJk2d',9OQ]A<l
:EdHMfM(X5b(L^ODNAo-gtK4kpaTL"tR5s4b.geo=^]A9.\g'ZP>aQ,AB1V">k,s(dnNhU)4g
X*.Jj9faWh$]AY_$5nFJCX!OEr#!Pi"-.l;m,?IIEr]AfhC[aSlOm)cFeC1p670:)9\l3mlkfD
+H6d1A4O,s3!JPL(9i&gC`*mP&?A1NL+rF-m1Ps(&c'YjP3p79aC)VDO<VK&QENYn&!66rG3
O7NU^%0^%meDUr8(gX>I(5!e/M74H_\)3MI'e/GFg"Dom=9H4N*.]AH6h#*S0UiT/+9o?.@D/
\k3T`UcKtd0j`OFH2;*P2'YSn$#3[\AYcRaO0FoY61)u.$;`Eug?C)9ifSucT"^qKm`q_+7q
W?ZC^lDbB\GF;<)M@S\UtcUf&X2(V1bZe/^6,iCP!NM%$O,DHK?tSA`ps4V3L/!qS'jrG`i%
(dt)CU07t7:r]AO!..Q0=C;]As%t^\;^@9(AokpT'd_#ZS$"3qZu7hg-.g"^5u@Qa:_kZ+=4q_
@=ZlSB1(f[ARmfptG8]A]A\=S"8HZ&(9Od^$Im[IOC\V5lPkVr25Ge''+%Q[D1@D`b_$G>=2_j
:N@I_YMZkc?9G/UMII1>5G+-&03Z!&Ej=T:G4XVGX+YPH%0?s3)U?@2N+FI(uM]A@kQnj)eDo
6ph"TIJX:b(:dnBEQi-&qjDYoB3o53N$\jG@d-Bd2@Us0L\l;hAf-+YTapjE8:##H@VCr?`N
+R5$cA"IhlTimkg\!Y1G$Mn]ALQ4-5bbqYC4SKE,,_,Z>/k60HQ@hDP+O5+%UI(%SKc7g?.jC
+jOT=AT,(C3Ff7\%./RZGlnTg/pE\]Aq0&ui^>($)gOW6$$O(hgmj+$`PR5-!OEi".UI\f*%"
-pmulq..A+hmB2m\YTcO@(H,4R1"AUGXnq]A3j[U<c1n6i-3]Au.sK4gki/O6riu&''[;-:YD-
hE[Cc1Hl[("aiMGm]A/F08O,s]AnUo119$RBX<Z"L/uQZo>3Z&'&&)-sfjBOYG)5pZU/.4DaVM
+/Nd\cG_:"%YrO:3e=GRFt$Lj'Ch9sijdW4,MKtZ'NkEU`$utJ)Cjbe[HAQQCt"%P:>d.EpK
5s;Q]AdIgXPG/*f,Z,+miW01bl_D]A.EM+6G8@BRm^\M<9F1%..5NnL_\N%r[@Cegd-^G-b5(j
j.pMaCEKS&ok-@,c7c9Wr\+``7jdi:8(:tH2_H<kk=_!js>a4pc*p.n3aEVFEEtLR#<+rsBp
i40.rKJa&HGdX&:-Kq;[G)+EjZCF>Q#O&(9doO2s7sd]AOVSWC#btL1]AeM_4P]A5R9i*K=.]A_P
<LgtE&ZjhBNT:!BU2:;ldedNj:?\@ATZiFqJ%h__o:=h\\#hf57RB"ZR1c,e367LF=6cT)QQ
5'AJ?TI:0@/u"d";F"o8ah[Vjig?ZNd9d\G1l@KI]AG+&I'8k*-?_smSC":[S<h.dfQ2+Gph`
P:_^>Q_T%I=0-,OKQgO;ceT/JUCHM-GkfpUY@t_&0)515M!1/PJKcOV4MhJ46OqSb)A^]AF":
!dE[ToYh<3[:7=h1_Qa)4_q0^LV:?@+MANlWOYNga'F#p?'+/K@2.8SQP:/u5h"%0Og1,N?)
0u'Qq13aN4Gtd9'Vj7?Up6DjIEKi\pH=>GVe`[NFSZ0j.7NeWnEKaRl_bDGAHc>\GoCAt?^&
hY?f._k"N*d:=nIs@W#r9=k-fdTWK\Ir_f'NgK&(uGARg3[DMi0EUCAL1f>1i3$24Wq7oQ;>
NKpB$b,=)dX,Fo@l6Fk_(?GV&Vk27JnRF=qqndEbR^noYmL#^uHZe7.D%oo>U4jWuhitc(4-
pNT*DSlqPO-<'RH(C3!D`27UuX4E*a1D5^J0P8_\,`H?@5OF_#(NgQ;8)B$3/f3VHbg@]AH'X
]A:C$,mm21'?f`pkXTf)ANX71Gtcs3TNC6!gU6X)XZrD&^on%1jL">80?aR+u$`E+Hl70BJ\U
A3;WFrm\SC[n^0[5J,b94,-%`)2^/M9C/Tr3(Zl-j%K3hD6Hq3C\QW4lD:I*(Y?fjEGkEQ\@
"#$7bHm1^4h?#UOr5KQOh3ZE!&d1Xf_`h3?sRp\'8fQ4"3(n"c_tkbGm)6j*)SU5jH<%*o92
72I0&/o8X;@Zl2J'+YC/ZRsaSmdP'I[iNRNHEda4&9"g\*uFQ^&Wd2!V4$=$+Lh%!N\7j.BT
tl4K?8bt>QPM*74?FB'ueRU7t`pZ`+U)!Y_ThfKsNKnm`5T7%WO8QE`T_e,tf]A*Mpj>\`SZ_
^boN`?Zd2*)[(o5EqU`EQ\*M3^m]ANd?+5jC,4%\J<d@QRJo9>)[!AE:2&I9kt27nA!>n)MW/
Rdc[IU-7XjI7as<K9J/90<>HH8XghZG%>:3Sg:5gl.Oe,[6%(5?Z\n+D0TA=qL5_K6:n3L)2
J<$P_(u]AcGraDZ%MjBK!&F0OKk-[&H`N,([q%MS&DSM*C'BDH[gXgU/T9.:'DW`IG@?1VU/J
Z=Z:K=`8iD?@d\Xf]AG#]AMG[QW*!Bfp!\P&3g/o^g\"LYUO%A=)f[:c\D8dN4+u?n-h)0Jj`o
(Bsm%f4k7r4Z?r9EJWI^-0&j!W44RUF8phA;kA5J8S?lJ0Qr8M&^YTKKk8B+S%W'2ML[K[I9
S-V5#afXQ-1\aqV3+;AbaU<"?Sls5F84ho"4rO78:CTc/Ff>NjTFI0-O^q9GQ9-4E;L8?H=o
CQ`GTk?bPK+$]AQ6t,FMOI1d>A;d8tSmnf^:LC?SBg<F$aT[Hd'd^'cg(4(7K9"JP'Qu1u<tu
a_;psaHBn1$@/(p7j<>%o`keG:RqkF&VN/BR'Wg(dZ%;I,GZ`C>M)XYc).V@?M/YuYDDr9d^
+o)E\h_-dm[#:DiLd"NdE>bZD<^^qCM.VLBp7D+rL>`LhIIoU+r\%h()mE?)RY0;k&NA5ILW
Sg(;&BO-YC18'om**c3ON_-8FQ+dq7GPb\VU_i,$(4Sp5[g/40S1W\s,YMl5?jt"L==]AWKj+
[KN[2fYN@jp&Zh0$IVJRqNi[!eD#-K2CZ[N\BFhk1h_9qhr'E6,b9Ij$IM8DOl$iuOV^$>fW
L%4c&'I^"rA8iDq>[&GBN_n<!_(#dN_>,hB$)!+'mtD554XG<;G9!TQB$?9Bu:3$bG+8FYL=
V7Q*]AgWc6Hc8`PLo\Z*1O@bGj!amMe79KXr9lkm=5-Db_t@G^[RSK.a0TQ@3)VMqgJ^H^88Z
>s_G[^;K>Ha'>!54nar0KfO'4\q%-emW)"\+WaU9c*Z&iBur)@%<V`K6S)OtA#\R$E9E^V`:
'7POaQ7s2nJJYAE^-NgfL.kP\VA`V]A[I6$VrHuD]A+/40qM;(7/,$ELdIVg%s_@p*q*C-.8p)
8;F\r>mp/C!H3G%o[l.N,Yph[o^Z]AO"hr6t^9sMC,RCk_U-V-3/l&>tf6/aiWjW9,tiHaST_
n-kuCU;`?GJ-VH`ePH9%Y2Ll]A06KTPm=D2nQM5.qt\gf64T8.XU?4EF\J<ST+NjrJ:l455cU
%ha>%_0dGj"&n3o`hXN*H`EdXa!(PK9r4gkCH$q>Xb2rQI]AoSUZr^T`K?rhJO'/l]A2PO1=;&
E#"t`WBDc?'LW*pZj)^U"Y>P9+%+#6(+?"l#R7l@iQRTuAcIMWYp%efI3DGQlT".0%;_Pnnp
`^r,*I%FcY@Mabp9g=rQ5YWWJm_kCp0T`^F5p6`in72fg\`Y&+Xb)qe)#XFb?YgP<_GS1!Xc
BEugldd5&W=,AQi^FWEK!2k/9^0Gn\TICnCaKX*%1X8MY_)+u`'YhE'=:i%(BgPk.C;S2>iJ
r.G%A`cR?k87&"a_'j(]AmGJ\#]AcNc='FFm*lRSmqR*:FFJ5g]AZmiUGFR:Y94e*m1['uKql5V
unT_j8-e0.&X'4FO,A;Xm/H87qJ=1*k"r=)L3c1p#AT1pF0b,T\<QW96@3T3/_H!Sj'lAd:D
Fa,5kIU\t91D<\uN?S/r'.u[S2n%Noaa"Q$7XdbSd8Q6uR3CbW^FpA+-j5=kK99*i7C5aIku
1t%j"9N)B)qbYp=:m;m6FseJW0=O1m*:.=2Ugf#9:$rI%]A'Zh2*X',Cc97e-%7Bh*<B%k@rr
JP73_%JIY]A'mW_\S"m_^G8Y2>PMTOhVNf0Z3M_T[W;"VPkMIE9;50`(^\iK9(o5A:S5n45#f
DJ%$M&TKGi==[.XKncHqLM)b#NJ$.4!9%Fi.E0*YO[Z_]A6>AH"nmPl`6!+RL`8ES'pAJs%Qk
NnH,FZuS/ppRWg(ZpI$&`V_6=p8*C!9L;J@?Vp_G(mcu,NgZ.rF,kIQ>bj..f:B*]AK0<e#CQ
Ch*CC*^P>6eKs8C>0\'7$1ahiLOnjFDHAMh,eI>6l)AU9<,AY<Bcm(#"<[ZTbb2YZI/imA>9
EsE4"D[0iZZS:S@=%i%Qmr%lKL+AG]As$i;cj8$i(t0$3@@tNY-k?TC,&^*eNEjT^W!Ys_c%t
Eesq?@J,m`SMn&=`0!&iG?qg-e:36cfUCc4<lIoRJ0NZVnZ*?B<86ZCsc`_-k-m#1MFFW715
V$R9X+P2JS%,kZW=UrYcpuBr`*4LU'LX[I#?DNO$F;CLH$Bm\j+&LB><d'se$ohi0=`LukEb
-QN4+MMg(r"?pZ&030k8D6^lh?-b-P:F3rroL@AeoVji;.?-t3eATfAs0Np2`m/SZ`\Q!'B$
Z"]ApC_E#PohoIun?3G='%^-*UZRDYLkM2B_>?B7m!P>(/I6'?"n;$jj,$Uf'%!_"N_lGlQ.=
F%M9),'6X9f0]A*KH"ic>A>a4\_dOd2LI7KrLuALVCQf9G%WFq-a.=Sm=1,.Ro>Bb58Do2Qe8
O3JN@]AF^0^3BB;6De&T<R;1F[^goiVHI1ai/b]AW&@.JGE%JE<07*F>ob03-XBU*_H5"Z\Z"S
Y#.a"%R7`ep-jWmuDIj0*og_G4\V)5JA>4S!,BZ28trFdBLZc7TNCV!Bh[:?V*A`PZBi#\F0
WpgNkcpW2WCbMt_.7Z[]A7e&76l`&TdWB^F$JL<85l-O'<JTm^nRb#/Tj;TT?9Wbr*XoDHcED
?5nB;c7l&EKK\$l5`f?XN#Ss<HcFWR%(_?K]Aij'\DIbSjB\q=OmPma@"BQkBVX4?L'[;KBV4
'bU<c\!!3a-QQPY<`T4Z:=\P@Q&SlCG\fXQnRW)d&eRA]Ac+fCaPd*^#SWBjP_SJ\Lq&M]A&i7
;[*Vmbaai\hd$5eXfK!qM,#R@G=_$GPT<oC@n)!(NF^3,V]Ai.lbjAXjcN9!\Do27/h9L:%2B
u*n'YHKJ^1A([A/A]A%W"@P0X()Me&kUTk"#nS<>pldqJR%2P#]AN!Z&Fh5U5L:P%iQNjT57]AC
WgQ@2H,4HOKVP'5Olpb<A&IP&*;=bN6TH*ERbA."@(4o+(eII'!DA`ie(Q><8B.OOhgTMWJH
1(iC>G!7$XcM)?Ab9D`DZ@arR0%Ug5nI-P5-*qe&EqcdZpW$4k`T6Cr+"$)7Vi#.u@NC]A<A)
0(l5L^:9_?V,hBKSjL`bQ&.T<Va.0in`adA(Ui.L0ke(OmTR&=shpN/fFsIB-<&=8N>@Ik(#
;%!TA>`-b_I_S_>!A5m-S!oM]ADeCbWY0_N_8L:'YBV\?4!<LsT(@0=a*%L,m()5ZlarS-PY:
+QYcaqZV]Ad2PSm%?g8KRLYOr&&mT1LT"(UWKLhc\<liT/gL7*<_u,-A'hN^9`a"_fYQn+(p=
d.f`5?S:R[2l\mZ1`Ua/<]A8.6TG(%A^!,6[ULDdnXKV#?n@\);GhB-nLA.<qIiE%mOCR9@"X
[H\eO_0K7aZE4Y4^-i"$/Gs0Ls..U>K.`Vd+.`nc9GS>]AZH+.q5=sWn?K.3p=AM-Oq40W9En
;IA\@$_lpfaC)c%s':'f'+%clsA$KPV8.WUt+o1+FdG)qJ8Xl]A:IjgkA8*L3td`if]AENVQ'"
3TWM"U)P$E!:u;)bH>5GKP_Cf5s.a?Sq=L@112?F"hXGlg;0>WbD<LJ"\h&.NpeY^*=)8[iO
/cX?1CETbB"rmmUl,+p<Ci6GULH6`CMt,Tf>V<Z$gjZ+fmTCa=Abnl;X53]AiWA.^:U`\B'-Z
&cY8@"7I6=nQa+R3TiL@tc6m:h0_emRWMMM/%c3CF3O9unJ[ZQ/"01[.O(oc<G'iJEcQKGTc
Dom9UAViLY%#Tf`Xu*Sh_FlXtn!448ipeZJT4D3nb:l+(\S)IkHLPa_XVhu73/hAW"!U)(a-
)_Mk(fL2OcDj87c89cq-Ko#_mt5q&4JjP''@_K(&QP8k*4!rkj46)/).LuHC3q3jI)dP'VrC
W"Ti8*D<RWjX:3e>#=gZAP#TPgSXeH2=T)J]AK&7u"T!"\L4"S*mU21/l(MJ?J2Lt+Y*Q_h@O
$1lWGrBZK-BN&mOXgf_QM7jINf#V)hBjrSWD2pEisMj>"?aP1^Xri=Rd>pr!pq#:\43tg^X%
RGjN7h5h/@Y+9=eHVj:Fm'-5b'G=F+4?#bS'@\S-S&M;_\f-nkE#:CEm^ej39CHUb#.18sn;
+T&`HX+n+JiLG<^G:BCG?`-7^ISrF3BtcsOAA^QTF+Wr>'%7L<r@i>_8[:Ug88d>I>0m9@;%
MWJ;fnb6@";-e"R&XJNaaXQc4>E+QE@Y[MooGj^9o&Cnb=j<.jJZ;Z.F;R2n'@TB7o;K/g%\
.@i7N=J?1Ls3)TI;ie)GkgX4qGG[stBS,bEIIEW(gPlqfc(>Z34-9(Mb1-8H]Ar+6W$eZI]A-"
sG%9_n+etTT<X&FD9I(Hnor]ApQ<8Uhg&S5"lqeXrl"EO/,r?g&SOHk`,?go]Aj="P'+J5a5ut
hR*"O"NQIb5!/I1R.C!u'=/;6Ekn0u8<m9)f?rOn,U9mP&U%'oHLOUIY,[__7@lr2b)31>UU
`N\[kV""JoJbqYF+&_H<rd"Qt@t#E+4[?ME@s4qFHICA!,%5dM#O5[6M))B7Hr^T]A\fWoE(=
(F:TKPHoTmZX^9Oc-TnLAcM-+&Vsbc)$7O!N!hoi+Dt-Lq8VcS$^@D;$u3h/NYB6`8,p6]A9b
.l$F5k*`pCpbhcD3[M3LXqUM)$Q'<oPeFen#`d-Qr=5]A19bKTggbc9_3_6?YET=rj[0'qZ2K
L'ti:i*n%iR>NB7JtqOa`M7/8IjhK-Jn7Ds5'`?5_lOlQTk2_?G;G4i1-h2fc%D8@D=Z%3id
J=2n18M/-'Y>0lit?GpK&M;7K"!A;faCR5[?P$>qTjG;&$T6]AK$:m,+J<[,#OdhjUE;A[3fp
3BlV-6]AZP^#b#,EgfidYB'VKX-d1C#=fLe,a9>2HWZ07sna`6-af#aM6)V<!Gu)bn]A#9pQ!T
%'1[4E#9>>[!g6dRUS4o##?j"F'DGY[J>Sk%"]A>jtYC\'?VjX&`16V^=?]Ahp-PdFV;A<-/4?
5OTE$T:EL"0@i_G4(P:[7/p"`XUkc)b5*tM&`tfH\gPPa\>]Ab8j.Z9t&o?Qp?Sf1R\C/>A>5
rGSFeY!g[K5+3lrr59@C9i?,gqhnI<8l5U)lruJI")Bl5L\ccr+t'X-QY47T1k6D0f>iJe<h
.Q@qn\sWA-9-`mg@o;e'Q9NN"Wip>:Tf/HU>ZpLUm/25'QY^WeCU^NUO'l1<YpM[\B*^OZ'F
lhnB_e[]AEgNN"WiVT.,Q)H1-\9T?#+25\CCQl8n5C.sVdZ2[/W+=[CQpZq]Ar<G91-jU]AkqWb
P4eWA-9-`mg@o;e'Q9NN"WiVT.,Q)H1-\m(WW!VEE1[1HS4)c2D\?j0?42fC;*)#6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="164" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="1aaf1d28-933f-418c-9e4f-386608efbcd3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="gs"/>
<WidgetID widgetID="2b117ac0-f2fb-484b-9670-4a26489de6ae"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="ZB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="company_c" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="ZB"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ZB_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBMC" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_tab,Key:ZBMC}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="gs"/>
<Widget widgetName="ZB"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$ZB + "-明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" cs="2" rs="2" s="4">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(GS)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue("");  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gs]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?q`pdc]AN.h-.ABVJXR\0lgLuEbWdgk#qJg1=n!]A#Jid%AVl7WAtg\Ec'?I-1KTm:<-C#_8j
a9qVP^L1c[_NKP9?NlTHJ8bg]A-./i-oc4]Am7DOlcf[;lhA#on*g/P>Si`V%M&?RAqQ).Q.78
ZIb]A"sb%dd%o'=1nk"j'hI6CV3Sk&PBfl]A+R5:'(>%SL<=cg:Jsa_uC<Z2*DNQ1k$Al[^u*b
mHL"LK7q6]Aq)k.d[uL/!o$#d^:qnE4pg!OQ*2b*=n/P6B\pE5n#]ABAs(EpS/rf7EH]AnCBb37
/iA@e!#E'e/^b9aA\j!X0*m2!;mFl]AjWqhjt]AmX/QZL*"sVUNhj."/\kbhQsLKT=l[u6g_:^
hTO=u+JnOMg<#]AYbjE$jhhp-DRl1GbD[,6.]ALNr\IEH3bp)C2t#B#D`Ik*:`s3:Di.6fq.0E
)&0kh0$)S!qs>Z\/7oI.,OT^>G"LCO+.bQht7&L&]An`%PQZ;ko#=*`kQ]At2ea2==r'c&4,7,
7Na8&+iShd^U=[^GY.>cARuF1@rO@mLDjZ\W`]Ae$"/;=J>L"Dj]AP\lIn2B'tH52;&G7RblTn
nTSPl)KDOL/r8`J8;QJH?`"9RU<&"5;s<&<jS+a`uXDZVP-DFbed-@1u2Q*cd;J&X/PAfhRi
[^j.+lTPaX*]AG2mD^lu;PJCffCYa!%Zj\o[N\gZ#gHF<XL"=;iQo!i.?m;Os,t<Mt>@T5Os-
(:@*WOrKh5i%-MF=H46,$Z%/.U=D+_"TV@9F!G#0+0%Icr]A2YJLl0O0PY+(=8bL+]Am,[M:5"
<FB2/7j+4$KW`W=.b*i0_UV3?ijKe?l#%XXF0\;NPpH[XPM.4#[H;[ZLH%d$JWKZ,u3iaHGQ
&Z->o.!+onuA,Jg@m<0!F=N9h7V3h))^Qu.>o`5@pr//6Q#phu;4mN3i^:$+oXm[Xel&;Wa(
.+$7AiS[J;L#Dc(0Hi"J7';6l>*bS]A/Ynd\%pHMMr.D&(i5^Qo2nY,LBq$DEB7+pf)/0%]A"_
3"O@WI(YoT1.F5q:)nVgfUWtZK%/=^.R[`c/&=Dgs_P>aR!gAQg)DGaP+-$"16-PnVD"@@'a
g7@q4#[t8`$;P_`E(T&(BP<7"S>TgVRc`AX7gSsB$#B$Xe(oYJr!-)+LMNc._2X]A9]A`<rFIA
=M(f6.J_2@Ba/_@7]ArAqEprbCZRrG:I)id'&K9e:NB'37r0%)##HH?"D"&enGc8CG-J9Q=_5
*L1!L%ZC<MM[cRuW3ug>g$c&e&a-BD)%`+[^QrfH-A`nD3#4Z>p59`P?;.nS_eD-$[4D(6q,
=c3Hkf,?^b#X:2IlWBJ`AFOGb>I7gJ8-"6Z.3NUT9Pth12;qu^-k#3`q:=u/rhoqi`h.]A=T\
nbk3t`aDbm./Wasnt=#uYhLcg`dX`+t1la23Z\Tq_sMMA(8m?JsJZ3<?iDp@Rj%!_qJcQN\Y
_d#</I(;]AR/`gsiC;]ADEg]A4(picL(i=ZmI"okh(5K]A`r(UkBWj;[EX?M?WeqUjUQsU8Xq::&
Ap-SDc2F7_<"6*2L-_-KC&B?-24O/EJUoPdQ\8\:hVgk=8T+\%P,2=puTJ^,eBH&g(^&Erts
6[h4<;)9lq#kJMAsG\C<-V8`ZqnnCBPeAOA)-XMS*0$^h&aeV3uma8WN!KZJ*Th,M"QlCi[M
&MWQ#1K0VNULe6'u:EUH&#=nFYk..NY:qSX%;Qn>QPXUP_C>lI1Q(=-=]ALGaQl58:Qob_"=&
bReXkWW+N6/I?NQIV-IF=7)sqJSp9?m;V&,0p85Xs9Mc,u\BTq8\*pu>6N4TAITd=@ijm1X_
;h7;]Ap[!MEO`F0ek\Q$?`RZbd$n]ABm1S/nTa)Nh'1[^-9L^NY9r?!2'4!$''$lfQce"Om!_O
mG+"MDuQPIZNJ]A*!`8Tt)71',>(il&pdO@h#[+,:PJtU08LQdTeNEX0&5fqT@<;1#E,F-:#%
,^*3l[93Q(l&QZ^R+ZgV""QcH/r,Sn3AR85;=?:CmFAQ60NG\uu^Q<Gk^P04GXIsVHdJCT:]A
%h+n:3>L<G(FkI3iZosCQ=WQl!>3O%,/6E9sIeW(J`8IX0Mq2e;^X"cIeVOJI6W13R!_COqX
M9O=.th$[NkP.IP?soH3]AlNh-Z0(hu@d,[P:=]AK8]AZk>FHSpse4QgCBZU"u`1>nlc<hMr(P&
M:fiR3$$6BcU>EZTcrK4L*@N?'n`ap8E`6.#"C#*E;.rY2V+5CT']An\Vf-3rBjQq8`7[cU]AR
?PqPk*#N#F.5&\A_<8iU&>9+f_9A?u;;=Ri2&f0ldoZB7/p#XirG%%fL=`jf$a3Gu#i)MJnB
@ThAj0b1&'ON93quc0AXGcoCXP,`\HA-"36C_g9K(C:\d.hA3F9Yt62kC^WMV;k6<m)+3'QN
*Ss,T*g+JUP1L^1,)@K:%&2Q_P8NW<jn8]AOS%\rq]A%a6lf_DGWu[:UMMRJH>9mdZ2,%.F4-6
@<k7r_^p([mOhASD2bC<T`%QLQ70/E@A'Z1d"*dklmW="6s&2fqS%MpJL58H\^fcmFt<t45u
8gXXroFoWJZ)X;.`j5?k.a%c*AZ6PI^o&lAQ^'2%AB#T*q8%63F3VeY8F%"!bC,^?+IQ%"l.
mRKk,.ZPiUUJiF7W6"PDoo4($/qMm6XR4OsQ1RbpZt,PTekQ<Y9N'Ciu%C\G)ig/F>iA<'EI
e&Mr7-,;`_K`S:i/_:@bOM"(W+=c$K"SuAagGN=)[5GSJn6l<B;C]AD#4om/RhT/TL4KWM/Cr
9h&n1>t$FL6M+1dW3A3FN_=Q,:CZTC<Um5Ipig/dHealn?J=&'>P9M/\^H^1n=OO`TBTHdo]A
_&Gn:]A%Ic5ROlC"Xt19'5@_c0?cTIQM/`i9%VA=;jGjCdo'3#W=U82;6PPn?6>FMLu3?aY*$
nV'Y2Q)PD7Bc3kJh]ALOG=^sDa7o8'G'j`B[Q-.B;D91OsA`!Z4^Rh^fjH@VJ5!K9?CjtZoO8
cbC"Tk3!,-:X9of2DO"2LSc/D:4f:;#n)h\>rQGG+osjRg<b\jn>oY==i&glDg^`nm/^0"\B
$>N1I:He(1HWk1W;\_5F%'I7/.JSTDaIg%lR;fH\3MKFqF=L'e>"%EILR+ksl$('ODSR=)ls
/RoL'Eg;qp"6?($E7`pNrZQ\SY)V:Hi&19Uj-@TomX4Om8ZPbcLJ4oYZosi0EM5S>l#l*2#K
Y_am`0(os2hF/*>31n=dHtDHGid\<liS%XVT]A$QerPVBeB&Y#@k)=GbsIEs6LRcFN"Z`2BI!
eK_]A&$a]AE`HDeODpkI@$/n?<8@@Z)p*q<0?rXhegTRtCR!a?sG2iJRZe.^lG-d&c5?.8DYll
($:mct0sp.#BkZ7):D':pI<->#cEKBmf]AI_+>]A\go(QmiC_\bJU-'b]A->`q7?XnbUX$0QV!7
/RB^B`/&!."E(5q'lH7-2`SK/DdF5&F)N?6V1XOB?l.sX&m-d\MRI-6H^LoOBX`0uI),RVa^
Da%:;oRGZeE3kmm*C?LhT\kmBN/_>:+"DI@>LmA*&a>`rJo3X*BnEp0t]Al&mK6l1?$UJpMfn
$Z/>OQgEn3;W\KgC>95s\.mtu(G3sf$r_^b92#H8o(#hsd&8J[l68Rj\=/7C!=-SfD)mKVkQ
E)K0#:3%h>"Vo.o)8!.VQWkJubc:0#)+Z?eGC]Angi?*F60E)Hd.FsF*2som`k9%l&*`U.:ZT
crO\$Wk1eT&cjlH1q$pBht2Nh&Pa1MbG;jHSKIa0oGhTscC;]A3bt^3.&fpciGGI+`SKT!j4F
:gdTn[b[e+o_/'+)h)SD^S%-@K3jn9fl9gh,Q?K:'/X(U'nt1&P!*^]A6>7'u>dANRV<)pP5F
o_n'^cGc:d$lA791$2(BsC7`]Ar9T-#$3b=bK&W7X<L29J^)=1noH3EcQfuIVJP5n&*Q5@^KC
A=6EmZ!Yt856qRV*QAsGb9^Z\[qj1Ju*XTHlrlFSXalS-="DtN8laO6@Ud)2fI^:j5!gWM:e
#!m&2LlTFq>hPNT$U-aB0Cu3a[O?3/<5nfANR)uA>T\=0$0e>sbafGONHUX*f6J6?pE"/r=X
QEps()g91u00$0FBK($CO39SR8DcM5lkLei;FIr9riJB:HYl9KWHVBtl9IiUS!B)J:&aRsMM
Iq#]A3O5r8/&<L<a2:Zatl\[Yg^Sl6Nh/K/k;T*OU/QEjhtT#AdM<K7>:G\jV+OLqO4-7[(K[
hhm0r6&?3PT2T+/7Z^!LjhMILoc@+,IGTVMhSZC-fO,`qOW9%OR(\or_0oQp<"^>W2iTA8KY
Oq:=\I('=\;Q0d\[!:ge-<coF!;IITu%BUOYK7b$F9L6tHJ6P-Eb-EiG%O,qLr6g+DBai2a4
k@X/2NijH5&%+4JX1B;CX<@FKP5:-E!hO-&Wc\#@5r+,r=`'_#I@PonBURQcSA)ab<H`Q.+-
-h/[GBoFBPKm"GgIoa32Te\-VW7UhLsWqr(Qi,B*uU:VXr\[T8C/)aNdRUP]A,D3>f2CZo+9m
l_i:sa#=Q-f$DD4[J2:]A!XLq;IHpebCibnOZn`HsnX7qVo-_6&==nrU$N36'B_n9Tr$(u\G]A
K'$+nr3DDNs)fW2G=BqF]A;:O'?`;T;%4_K_qc^[eb:@oiQ$8)@JF4rpCtQtQ>5#@O*2#q/d*
;1/`sU6'Wpatkjm9[d(\sQ_<s$!SuMIj3uurm1#je4]At&30YrAZ5D!rB3EE#3_ZX(BFVCpD?
-h,!RcnMZe3<PV1Eg@N,*Y:hRBRM[kZrP\6*_kIIF8JCU-.'aIL[>eU8!)>rpo5]AU_@aQ2Q6
h$$JEua@`['`@ZIG^L<=Dl%#<?&RK;6aV[6Bl^Y5u\p=5"IZb7o9LT_J,WO2R0tVC$2b9GIS
dkVnAMk[DO5UF,(WQ->'Cf_!Aj#;TCginFCYq466e`uPrJIjus5#LVJ`nQN6P[hH,DSR!+j:
]A!'9+:o916oO3mp7t6XX>]AJXHn9FUlUXQ8*P,S5lY\Xi7tp1[c'e<LJa(U\SAJHcOZ@g&L#t
gCLNi-lp[Ag6=8:("YMQ"go?X`!^>[mEI/s$Jo7eB_4?rH0d=',l%Y]A-'.Q+2e(fJOf_1kYt
fALN;)]A?0O'cX]ABa[`1h5]AL7]AC.ON@4$^K$A3B#ILU)]A&_=QYt%h#n#:-AAKpZA1c,B(63J3
GF.X)p@u/!nOPIJ0_:4QXKG[<LDnmId=nYkE>CcuWCN?F?"C,=ZM4K2@!nS;_*8A2<JhZU5P
1QS9)DGB@O&8FqQ>#FJk[Z<7`0[?``B:YE;BI9M,JbM+-A&XY2>_<^@FN7!$,ZbM?l)3"/c2
ejt3-8QGJY+UB7YI9[5;k^*IVqk>",'6W*g&2_=bI_'jI%ShGgZ!]A%/F%pdS'Hr:)a.ISG)&
-:`]Aq#JEuho+?&JQXSg&:GbK+4?`IdH*Uc"1F=u$K_.*FX=)hQg)6^qFd+eGiGRbTR4k-F9a
-Z*q)Q&=i/OokA\qS@7DjM^L+BC_7CSDW#<nCeM(/_nOBUe2^_FFk/oUM.*XFDfbupq$2#QJ
\g#V=1;1lDl#`<BmO&/8.<W>0qj:[%IRSBL*#0d"3%?TEQo\_>bH,Z%.;H-XBbgAka!6D'&m
6[o`GXs644)D=!ham/$:H\CDZc]AK7sVL_eA^>""9^&M9_G["oLF,$IBmBFuhj7C7mNcc%D8-
1^a,5+hSDQYBf7n=Jne@Xe,+~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="224"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="224"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="0"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3467100,2641600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="BRANCH_NO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',$$$)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-12999178" hor="0" ver="0"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[


_g().getWidgetByName("gs").setValue(a);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[单指标查询]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',format($$$,"#,##0.00"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="A3" upParentDefault="false" up="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<NDJdTIN5oCU6`S(_cA2WchqDW=XSJs;Od-;Ktl.oU.oVM6$5!mN]A9ASOYK)Tn;Tj@4_n.S
/Yn/u5AAW0E0o-mXTSLqI+RF$8.PmJZ[+n:T?cLR\0cro]AgK\(p0/D\*_Emrs/oB$D6R@n.'
%>dNR.JGS`hDcGU*gUl@E=lZ<%m-qhi=7F\V8?,`FD#sY4If!&5V6ut+.Qb*+GY7^[:Z5TK2
Z.r5rItkhL<i0[VFR--*@KR$qZHG,n*JnWCM2h%e/N3'[*hXG@rp=j%$\U+8-n1TFqL[=9aq
g/#GO%O6o&_E^uQ[9VRl>'dUHDP&-j7udlkECc/MGar&AXWH=M`ZNG*Odk;<c4nC(--posds
SmNb%#&.uqp"uPT9Pad[h,ut1Xa^87!$&NB(U3qSWopG27Y]AUBNA\JL"76(ZS=sBS55!:;fB
\LsedjDApbt?Pk21:\Y^b-BIHs(p:]A:A'3;R25`#C\jo`sr8"KU^kk3Q>'r$)Z_"Kjm1Y0mQ
oa"0bkerB[1MB!1E"OmGk`CE)i`XcU"3$4oZWP<O`eYB<^>d"W_r>A$#^VAU6_fe+&YcIe:=
do31X4s)o_B5uS+r@*8E`2>Y$`IsrNf$?[Z/f0VSf(F1\VA&\f?<^n:<.f!on0"EfW&IKOl7
*"IY=i;VYCb;;)QANaIB\i,!/?Nr.C*dSFl*_MnD&68jFh:_*:5q)1I]AVc`sa!8knO#W/0/a
6!hmc3o0\`miGt_3$oWj[[O;/IV+nq7W_p/0mN$Lpe)n+dn?%!4-/_W6C:XZe*(FSPmtq*]AL
Lp.mOL9=8j4GqeEpqPfDRn^01)*N6.2aT/^U>l=6SJC``n7gPjp//$A't`VAFFr2-g6j;_J'
r1M2nf2NM1U2ScHV73:8a?GD!Pg"?2L#E/,#"-Y((R+C&<qrH530a!9uTd3>k/Vd-(s+0.Cm
)'9;J1eXO:2iohi+,\-'^T.mLYb6L5,eRk:MQM*?Sb^+%X:e-0FH,Bd,kQ6.CqC@p_bfll!k
YHL+:lBPL\Q1AMaec56G4O3#3cY7#*e7HTN>>*hSj7@Vff$oqsPkKr*Pt?b/";+j^*f(Cj#U
dEfA8eL0SK=XhCW\CG7O/*c6Jg4akb_mnuopkP-7XXOO@JDLP663rI[$r2rddNN1+"_rZ]Aj?
m]A)]AR'2L2M@99=r:FD;'_QYqTO+^pR]Aa%=A6a4:pe#A0SAc)eE5FF=YFmbW5UgA#?gC.5/DN
/h96V3JsRD=A3,kO6S),<fLCRQHL_;iKTgjMhhPFE[)rcl&%na0M!f0qqb?nmn;.1*L(E/BE
_kqW-N!uM1[>aND''f94<r&FW3&0krBtK\&1Qnse71.6WZ-tIg-8Di8B.V6m;Q"Si"%mLfQh
LR=Fh:Yq<4r/!n"Y2^>6jQqY5.c&&9ba$EF+&9%J-_pWP1b0ZB$S<#dVMS/o[[(>Ae_Ug:M<
M8_P-IPON%p[Js+q79>>&AOJR[eU2ZW7UrA\c**YgHX6j#EHCVD;e'r]A-.idPP'f.qF,%$IP
!4)bCA5eMrT[jQo"6a/BRIt'5\>X52TF?<LZuVm^E;qs-!"0hRN3G;hK<-+!'!QiPF5(QsVp
!l=Nd.H,q3gD\T=-bN8k^_.YP/+HgFD-I`1ql.c%_0'C59J"FU7#T\GNXdHtR>"C?m0L.3Rh
-%C/;"449FkZ=?UI<%\ND!<WPe/n0>klpJjoSDX*8LV%jkhEB;W?+oZO\QFW#'5tSmO:kLIo
6&0B)bi,P"b+n&aBVHU\hpZCO41U!Q7uMTn\'YcURAR>1$l>([q^,CjHEbfCMr::<.\\[6^p
L-$*oo[*<H%.?HNgIHBDA_pVI#4&aq"uiW4J+4_;on9T2a3TU[DV-Q3Htpk]AUQ;:+ZGJ&!d+
l@/+5oQ:qX/cS::s`f:i+DM,ipbQd_Xr\\\SA\IN<Tee\jPURX_!n@iS`U8nK-.o+;3G_NDS
%?2Mm(4Iko!5Ap\'@5AipamR>lEJbknQo_5<-,>3r%Gc"TNMFl7eER,=;b3G!$L1@QLC6Vgr
Y`"^$Dq\-C!n.Vqjdb,%`*A/*j\td<T2$U$j4`Km/Fj=G"b]A"C!-t@0FmlKHKhE:mWcB2BZq
4GN'nen8qg;SD#_^5J+r9nH2FFs<4e&C-">Ve'733t-)3bhT9hL\RM`:9:r<#WMm-FUVBKf7
5fChQ8Vqgi"A3<Vq=4p8!PA_`rM40s,EW,GRiK:ld"eSS^R9jej-"]Ap]ArW?28=R(E8-V]A`W0
6THbQ&`j)4ft>nZZ7!d+CB%o#$U6-Qa73.XdS74AiR.DpcLo2HcY(`ldD\gk@JDktf\T$!1b
r>dZ47lMZ+i/V:Yr>rLm_R\0#]ApHN);0YpH`+ad^F,ZP2V'=R&r;!Q#2Doj0H3H.RS8V^_h4
MeiAe+tE3EsWgHrW?uqCg2<@0/G:GCFA$0dgMq!/0=BbFIg8+hOiFYMO2mq$4WK!0KG.#_rX
HrFe[[.+ns9S9fC8+Y+ZMR\C/3[KN4K7D"X8R?^qVbD=*ch<?bng#C5hHjom#35M,"M5'lY5
-N^O]AR$ALVW98$nC4FZ^Y.r6jir)1>hZ'*Ea:KR'`!.f_OXiRLrPOf#O.r(%0qVDFEk4g`qW
MKE<Y<a.%j>[u1"FuaS<db^?&74m8='hpWcS6\i0K.\1`"2,WUsAgk<9TPKkj<Z)ips/?*+p
WID_oL)+9%6EqXGUhT/LO8/[<ZgVA6]A'SEjsN;o99$7e'\rGA@Vd7?#d^LDstaP\6:oKHVg9
2eAmlnesY=Rg6V8i:;@I9pS*BYN+(W^P+3CF:".Gd7fsg#`kHSpq!?,_X,8e021>(\dpPX2B
9-R-oC3E,)$_4O`l,WMmpIcmgWV>u3:m+VciaU\H^&pk7e$1.S)rBBkXRLS`pY[?a.lq<&N-
DC4q$kn%s4a9O&g[8,RSr0Wq/E#i^;Yg21]A0hW_Ck!,ZqoQMW&-ok]AX9?>=,/GtI$2fLEMdu
m,,9>]AE;M"KB1%6kf<KVp*!'tY'qZ';eB1>,`ihRLt8dPdb9Pcg8"PFmkkH!7XtT/%10b%/&
mA/!Bg]AD!sR*%<?X)<jMuf*VEnXj=:]A$i@V?;>pSe:3p%<YrMo/eZQ/RadBO>5j:rfem9pDX
sKGk<.OB3(mY^?.2`emm9mK!M[e!-qabIhkjiTT5%suOS`'9'$Ou7IW(ZtN=b>igQ\27eZ76
h7DkrHTWjuY1lJ$JR^4-i,=4?_02DY'^!3@3L=bZ#dT0oji(ah^Xd`59Y2J/OS0QjH13juCK
^3DKbFXLG_@7LD!>%P;l+YjsY,e'2mjo"Vi,j[flhop&5fD5u.2Cr8pq]A?lkXZlo6EfIg;5T
,G/\ZS@?5g\,+6OmD*f%DCUjoKK9W]A=bkFD4oOmNmu[DeuM1g$[R+Y3at?Ja+G6B"?`7jX(j
5l:!(YE,%Kbf/K%a0Dc!K&#o=tYDE9Yb5Rinm.!b==aKC@n#>ndB)i2#apa7dVC,(@g6Dj^/
K,YG+_*L9V'n:s"%25nLAM_\&?ngZ\5X&pc*-Y478%<WAGP2?l@Fdg'p=7(5UJ[c`hrT<lCq
Vd69&Z!Ur>4fb>l,C`Uf&tLL'Uf^Z_[g5$\,]AbOGnLL\JmNe"0k]A+lVF+$[6q.>2Vhe^*bWH
]AI3HgJHJP:DJ#$c"0s=OFsa0r['uhQ/M;UbbGWIE*Lg]A3>Jb+6rCo7T$.G9e1ibDgLi!#+PM
-qim*0iA,^":I>"):M,2.u6fF2%,bMI4<#G[TFI=8#q5W69PB*<2-e7)K_?Vsk4fk8Mcf4sA
-FVufjKM$'Z2Si4ELCPK!M(\[tUU=58:hR.eOBI"L9nm78h--A!25fm+A*0Pt%A+L`'0,Kr5
M-%p\F[cX\t&NtW,_p-#0/"0]A_+aJ<N/VbJ`cMR>b1A!AA$0>55Ot8LK!K$4nr7*\GZ//-\%
\94.3P9XVL96"n_UC\<gl5;"ZtH=_jk=m5[5!dq]AWjR.]A]Ar;7B;2]AdK44K31a,@tghOQ)^':
pM:5r1`:s`<\Z=X67&bfC(1[s,((A1[FI)a*rI&eop6H>'n4bQ4Z'??=G:Pp?[8o;6Q#-^N6
DQY"!;qsPT(-_iQ&B-L+[!)?Al+l)[r$?_9YkAAQ,J$He^&S%4`D;eNdMG@F;qgnU4gV"Djk
Ms),_3MfYlcrIL,'24&*;"ejF^4r?aW?bB@]A2=EHD<;;]A64U!]A2i;(?GkaiuchFSA_SCEI$a
&W=$/4a":XEItun[?A`KbeVdH;'*B'SMo>XsCd=bJ!A$ocl6p&F>1t\)5ROmc&QCbOng-b&Z
2"!L/M+FR.[P@j3dF0XUMt=fG#;mnU!S8qRn2,X_9:[8VnB!tQ$jlN]At99!uR^%^QP(]A;oaO
K#K-#1Eb!8:Da<7rXTf,cjksjN.^Jl7FTXb'6D=1S\f0r0XK%\&&YI("]AW%JS(3Z(pWee]AkE
PX6G3,rn93U.$:@_6QAlB9T5fh6&(a.+J+A;$3C#)-6`+7X6#F8B[KtoIXXcHdaZ-..ck]AZa
8Y#"fILq=$i6:WB??1dh>(T-W[ODd@^V!YIIBY2YSN*qiF9@S2?&40;EbF&1o"=uJK=timWF
6=@JD18]AB6M&UYDgjN_7X08r-ar.<7%4?Y0B/=og07^sUTM*3]AJSA=J&Cj#-mJo5,/tj#6%;
r^58sc_98$TaYTe*?hSc7\obBDR7c>.DReG9k:%%nT8;m+DTjqW#0dNp4Aho^G&t7r*d<+Vd
i-fSUfm)a:K@A?(Y1A"3cI*s_7QmkeM>?@jC/tJe,YdAjA0[6`b3@-_@*NdtV[4tG=pIoHK^
RS[MTQ+\J)*=//XZ.$bb,bUJuGQZa>`8!;7uCgA6b#?C+ECIN\`VjRJG3q<6%K>(9b5)>Yh;
\:-<'UQkKQRM^)%k[kYMM]A_V[BAt,#]AT3U.7M4;qIpmK+AQb5j[^W?IR[('/XlloZ]AYHGP!h
U/!Xd1:`(iY,9\l)>es=(mi\?jE,@-Q=%CYX>uG7Vk1:7O"+5P=#Qf0f=_;Jnp(Kde`jpJMG
2+C/4$FP8dC5=Uo7N3:^%Qj5g,OOFhbhioB([!k%,.k)lQ5Y[_G@0ErM!_R=d#>sCc1JWh"e
Odl2bd=K`DMG#YMpgK:O_D]A3+B4r.n<!MHs[ciF/eq+uG#/:Z8SKC-(^]AFi^e)*eTT92&</a
B:Of>_ngo13noD]A#G-H#_59+h@F%^Qc7(*m.'jWElUeQM:Xn)`),J(%-gS"-M%IF>Noi)DrB
Ik$lP"b#d&)hk'H:C:9\gXIWu@rH#hl^+K(jdaHrlqGQq<c,%"kf&WLar.=+G(<MdX2gWIeV
6lsWH`!4)G#Z*ABo[ifX`+CZ'j>cW#;bVo9-&jd;/aU71`o,/TH_^@NB=&.*@WTiQd'r\P5#
mKl0sl]A']Ah:Vk5gM/fOnU'mM-n_"_n7,A;+UYqNI_)ICH3)-bQH\T1R7nPaK-<5H:osE`i?'
++4UZiJXYPj-_)qh<9"iBL\Y<kVKU,?59Yn`O;'(^e'*7<^qIT:MhppNUdmoX;>%2W?@8YEr
D=8%*Zf@$,7hn2N"Y`X02?Z4@<\@b)Lf8cooST)m^K:G7J=U<n5qj0O-Q(RJq9u*OtU`.3/k
TS&+XmE[m9<=rInR77CcN3g;I1);>#lT"Z@Z'*.$"6o5DJX!'4T^J"d/VVM)o(J>j^31H<RL
ru(Xe7.h)VVee(YKO8u/-_G&`(*PQG25f-A%?-7H=:`A_5T!dVX5XOFO+?u&@>FiAek!]A&d5
>^E"Yn,_<tX$qfqOp*&>?u$6<1=@<PU7aAbp9l:YVaD)b_OkOB!HU`+mEQJdW]Ab:0`.PU6)r
aF"r3dZ1<tiRA+_T_%Oc9q0*3AW-4dXhI7o/4sr!P;6J4eA<>Pg@65rqQ"E7.'ciaYlpfd[r
M3-gQ)u^dASXdP?$GVW%,ET+B3,8QS.IQZ(Jef>N0$*48"p,3;;Q3iGF")XEZ;HSpuZeTN"G
fK-a?'P,NQl@&<;5e2ntEoJ".]A/VYRBU;-0i:,1R+0[F."Q-mpl@R!3pR@1';r$rWl1=,E_W
9!LUdJH;"@6QSaoA]A,mWKL01o1qKX:*k^2-N9#IjG$XbG[R[@lBT2:SY"X`'FsEH6l4_)PSW
;+p?&M^lhKM6n'bpFa4NEB^5+N*NN`"kV)<R=mlh<Sqt"(+?["X@Pl7a7CrG/s<p0#I$\QoM
D"sd]AEZu2,WAj#<>oUII^YT=(\F9SnQ65rS>U6\IjA[cKpA,LF/2Xk&ropcbF>fZQ\g:@Tq+
2meHb"??"69*/:D05kr,4MAaKc#Bp1XaWKXMW'Un2mT*ck@kTT!kDM/MEDhetI-,GJ=U:\Y,
c^H/UC(b/iG5D=:m^\'F5HMj5GA&<gZEpbI`4d%PI_Dif/mrl7TD)S_)G\^AbL'sq5iOmbCh
%SUdh='R*8?Rn`DW6NMl+ejdf@#n_0q7-hSiU;tPoTtA-Yi.ojRq1kjBuYpimGbU9X&Q"4Xo
Np':8Q*F'"H"G'#Qs>cUDlD`JYE3#Y2KWK(;)@B=b183G537-b%)F9m`G/IhdZ=YX'$,-QA)
%:e"PL4^'I1o(8&gFS]A4!j+[?FG-^q;:qY\=Ru@*DrD-lccQl[m?Zd\.TJ1h:Tpr9]AFe?6K9
Hr:iiX"H[*6)-^kf'U;#"EEqgMq&d58P[=iW[q2fbOFP2lg>d-8=a%)YqgF#a+$+oO#gC'_u
N2h+IK>Uc76`e(di@Kb&T0+-I$VWR"Oc^dp`!:)IaJPThfU@A.:,?/33G8]A-\h8MIn"@c9M/
aWsB:V=c.AQI.G!?VTX9D-WE(Kd^97Dh]A0PArW$/qssh)#A4V?S&CdMR)=16d8Df[H1OVMsT
KbY>tJq7!^3V-tB;IBC+$qHcVh36`#!s_.ipC.h.5?4M7BrqjkOgPL?NUq_a[DYC@ekh8UT?
k-LE]A^S$j4n53EnS_RFt6p&2qXCf=.F:':mZ<MX!%\)L[R(D>&'2?'in8b_=To(.=F70X*Q7
He1Q$1JF2tNPXIbaX7U0b#3p-oT:=;9:Ygp,0h9X6sh^)\A'jdt#..I]Ae,lhtl$f:'4!/-pk
LlTfQ1&'<(qQZbdb+,YFDPjT#fG^4:99>--W:O`#MBrdsum:`DlP;Mt//u+&q2*U.+>G1s21
DMKmb7kPE63h*UoWu=fpuur),%`:W[/+m^Ke*Z8a.;5S!V3I._l3%eDK7X7APsVm?gDZ""Js
R7<eearAY13i`d3]AYEBk%GEF1#]AKo0Ylpo'hWOQ0X*(j8='b[$LG+,L9oIg6tW;@cQb]Ae/Rt
C-#PIXAKQMZ6XjWM;I@g7;(q:28:egafeJ37c<f;P>s=e=7:I\__/^5)B+F?;G5B`P-X@ngG
i%8jX;"$aoD'lc5]AKb7MDIY]Aq2&HbVT(ZhA;rbk)rV]AH>#]A;-WXgYhtj6Vl46,s(Hh-[i+_&
`&b^a9fRHp>np@t_Xr53T-(%o"p+._T94LbQH3kA%p4BU2D`p6s6"cQK.k!>4&oGak)j,igl
KtbrXB%>J&,?Y-pC2J.=o+NUfui9`[D!lJjGrM%fj'^/f6,/*V3SV8S=uD)j;gOjF=JiI8nU
5d3t"#%Jj@O5MVdnQCal`#ER'HsXmSs-8t@?1f,I1i'bW4jdsF_[Ku]AUl+=1mo@L4I0f7ju*
0GqMZQ4f@PWEjZtV$%RglU<b0nYJm!5oRYK/LgX]A0+#KuQi[b6:GT^:&6t'#%Ves-V:)>ABj
PoQDuk9^W]AuatO.AXe&Vc,3+AHAbo8;?4:"#L#ZnFRIH4Xc-G,UdQIb.+gnm*$ZQ>KF_]A'r=
golF&3el$XB%pdaV`\b<oO!;sSr+RTA<^Pj'102]A6[#jlf%R1`#^b=P!!bD/%AuZ7Z(>^DOb
tNuYNn_J[id7?XH0tYYOtCl(\n0/!*0<L)=F&.ZSnGlZgTnAd6I(/Frkjr]A5#IXa':XG2%Kr
kC0)ET:0*42F%lk#qan7*,EGcEg0IkqaA_tgUAT.3?H\4Y.Q8ElMH'B<%_k-9W!=`Wr\ZUG[
fkKs/?dkU3hYsQT3Ma):=Ft,7H1d.a\u+N_1Na_]Apg8Z<H"m,H`tb..06GPmWl&=ArUfA:Q,
0LS:/oHHb.VIVIG:4a2ieG;Am'n45M%b>hqS@U^"]A/c?04?_o'-+r:%ft@kM$oN<+.Dbra`=
;lZH/T>5<K$,=,=%ZG,rLmuI@$15q`Z2W&?C^!4@fA6Cdf$Rbnq&h1diGIl=!PJTqZYBc\!E
eHnk5k$@F+]AuS*Lm;Fg[bmtIUQ<GkE@9;T;A_lfMm*\Q5-3hk>\._9qbGoV9C%,l)6q"4I(O
$N)\IHHP$iOuqN,XnN+Z-Yf@Td!:s>ED%oUgXl/(77N>B2!C5q%5!"Uu@"12(OR_8qtUT/qL
Y2\cFUL.5)G!pg'aG4`_fn!Q-4\SR;.T`WuhWAtanhgKt+Xos<kY!OL1a_d>&@DCm!Le84,H
d@\]A[8inQ5)5j_]A6gj^&$U8o9g3r-S<k_:FrYIiu93$*=@tWa]A#&AAmU9=E&(jh#d2h4Ms!W
Fd'R/]ARYsDLE:&qg;-I`hrfVobcE]AF]AHji82&E]A$2Vpkap^BRW'AU@l@4\X*))1b@TU@=Mpm
?O3#Kh'Z)d5,4n'T(A"'fC7@5FtsHP@6ig-NfVu-S*A:()=USnUYF=85<e:W%U@(i3SD%kk?
m+-9fri;\]Atn/SPFA1J$Tc@nP_-2Y6RhP9d[)GI</X%hPTPO&=\]AfH1i.d7ot3jL.dT/cRFo
>RrN(N/\Is&B@G.c\d;%FCG<L\!r^e;@W<W"ies.I1O"#f!k<7(X5$de]AC.55qXTJ@bnTSSk
oiE1#AtEh01[]AhuYY)diTWu@.%1^"^fkrPQFJ`OPq0QrRX9Nfk*g_#$:9,dRt"FG7.oXR`1o
fk1+dc]APV1m3^^=kc\&2m@*"dE?V+gB&&pCh+HMC?kQ710%_V`=aZKd5d`at;NWpZVB[c:/%
F5C,aJ(M/FJH.kJrbmk.P/57/q'BJe81U7I;qe+,V9ZhQH>5Z5+RR(c4K@4nY,jH9e/*kW=M
l6L&X`\Rt/[Y5q$^/$>CREnOML_,D)O+"\`TA!Mg6c2`EIPEitoZWJS-]AnfK%[*.MoF&rO?q
%3'U,[uLPUoHu@22uoXY-"e7d3*s/Ng!,BIUXBDQ=6b3U]AYtq4*aJEcFkCeL)%m%Vh-HCtmP
2ONE8e?fjnhuA4Db2S-7mrVk*2WI0b<4Z5HSm`\0Bn0:3A9//6s%;:&#$t'9[pp&'pU2:cip
b[KFTd20IBu<dt-q")!_,I%1"fl<"K3B)"g+WJK`h7>F?e<m/WM3eD;Mj5\c[G$+Ppf]A&iQ"
#Eftr(HVso[lP0YiZ#Jo>Ta9Vr^u_j7N/7*r,GMZgCu7j]Au5'pph:9dpL:V\`U*A[<QimC@Z
-1C@Z-1C@Z-1C@Z-1C@Z-1C@Z-1C@Z-1s&8K_12cs6En21@J*402^ou>e@(Z=uPO[bNTA<Wk
oS(cW_,c2h_:@IYE&G-D>Rdh?=+dQie_k1]A27OQ0%B]An:K<)i=i+hc]AVr1=9rq#T\%?P]A)Vd
i4>LWc6;s7?6j~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="284" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&dmk=sIk(j-i=Z\h3>>?VC(1Q&7F6Qi_#k6qB]AVe='!8r93TQ!I$?f>T_.DSf>8Cd%XI
>e5E_(_2.k2*<#m>p3Jk\',!KdMD8K'nq0APA)s7D^A:S)q]Ao9l$@IHR/h?fjq;O-bWfL9Up
c6!Bj.='l52/":sj<0_r3k@nQ06,&JQY0J-.(@[]A"G7(*>>:),u5Ib[Zda5*h"m<53c!WKf=
`;olFVepafY5E[WsIh6mAoSafc091NEfi!"nPF4(,rNHce$W/$R778@r8H\/1n>mKSsTE5mZ
2&_T+\?ILVK&a-TXu8n`JL45%O=;o/U1qo,^?_%6cb9R@b0:lu\!IIdbb[DgYhTfXmSV6-sb
rnVkF'sQ?u-a)clj(I:_C0aWZ(8:KSqmrkI>>*l3X(^Z!cS.8sW"s<p0NM01qi9ODdUm'4$=
-O$EA.-GH]AE13^(OGb.+T0BgRI9[>M(F*Zf#i"g7_Qh51pR$=.8l1s'`A2pU8O!/Z:9"IY3K
O\oFqD!A8!\OZ90:HI*`;l<-$e5)d1f*u(!2(Cl=]A]AgmoWR1DK=(JERol(EBA[1rX;N@9$Ie
5IH5r9h.+jV<EP4I7%oH#r`7qBF^ueZ0fiCQ]A.)I#iE=1e#[DNdVrs@`f,3,<DlCUYR>$nEg
?/r4M,]A1/O4O@D\<elFhEl9IdP'1fC6<?_<BWKlp6a^mLSe(ZcI)(R9s$C7MZ>/-M[D/D\EE
lCh;5R\6@iKA;m2Nl2r6I7uZPnmKOe<dadK7UE:"=:_IU=]AM=-[!^ko-d)`n#'7NSJqZg$QE
'qV*#Reg.gE[n[-SM<`1(,=BrPil1jNOb6^Ma;%AcM#q?8V/]A!JghR_bp=G=Uh[@R"d\_FEN
3d^eL!`C#3o?)G\YP>C/fmb1X-h2_e[@tQM1QJKsQZ:cY_$U*X#>aXg<(ak0iFQT8gP_]A&DO
(l*>QleX(URE8Gb>,D.%E$/7/]A9ioHf%`S.Q<!qRH3O'7[]AFI'Oc]AX<-![P:rG.#r35p1>VJ
AZ)i"mk9Rg:YeXq7&fiN8/?>ZSp^IHXJ]A7tYCgb.KnD0Q7"_b8QVPg[VZ>T.KY7t4cZ9L>p3
1.7C,BP-iZW[6q"5l&oXU^?K<%/[hbo9[N5b2eq9Z'1AOJOmTUktGO1fA]A0ACK4R`LFsf"&W
C>EEFB'a3[)6)_75?`?@bB[iU0$5E+URQ)E#ochPr:`FGtLth.%*NH'UgGQs4Mi29&F83/0@
DS[d!2ftT_-;jdQ6nQQq6e'Pp;86Psp+313#nJ7X![>PYJD*_(>ON_M2Bi$m(S_RIYjZPl/I
o8C0jNTQeO7OkV1L$BRaX>;+4^O$Pq,CgFFL"\L<`a!'gMO,<U+VdZ/3#[kNS:di5$95,LF9
k-7jNe+4'YWf`QEg7lYOe=T)=W*r)+q<h=!p([hrG>)1Rm+f5Z15Gq3H(b89/k6An$tAhPGi
UA\R+?Lhg,+Sd*rBEqTKAhf/&[J_=^BHH\i4Q6_9OAuauR2'O-`g^lU;B8m&X-5QYoqg1o@Z
A8H'gE3^Lnt$;pr';IB*J#CDj#Mso3=Y$]AoG2fI,-Q0kJDhB1AOV6X"!@,7=2,MiXc^LT!F3
C8.sq*Zf!![Q^lVW@s=pXGMqeHW7kGc=.P.\cg.Hb5NRgNRr/#/1ZEHk(Ipu8cu)Y$pIWcn!
l%:WD8(5?gW*k38eMV1CDgHSRdit(A;NU!0E6;Y'TkeoMIm?7@LSjfRYotY?S0ksB2BXk!TL
?IoNg2]AT"0BY(ncSZbaF<NG?=QB3NI!FO(]A2E^>J5+Y1Dq2YlEaOW>?b:4k6*LouQd#-q&Wc
K-D*GSh=nfkIntI(k,0a_S'2)T,^qqE.aq/2OE!jP$UhBbcjHIR`o]A!6"k2T>>\ff>Y<3u/o
FR'qKW;XPGsCQ0cbNo)UAmQ."35HmYq5Kc1>>1\\.=i"OV0O6%O/74&Xacn[&a^giQJ!'o)J
9\Y'6>btW]At-XcSI3r*+/4P>ZdQq>$(k2b`dH<KC_1i=P_SolZ%\<:[X7O-l[><CuKH>+Dh9
D=PRVoHL$4W<0-T/k=beA(Z&UGB0^@0NQ]A=_m3KEabF_McoFIb_2"MKYO+.Q?(',-_LpqO2u
0QFacL(D)GCU9MQ]ADa1ZJ9K'@Uu'as_>mmD[m$<oPLI=p*^'2GW5q0E%tH%'/cPhY$!o(acl
p9W)H?aeL_3IXB#H\[;+-b0so%t:1[U5[qo]AJO1WW'#IaQnY0!GDmLiRPT&BLR`WSQ?i`b`=
[TeE&[ZlMuTu*cM_*GBcf=OeGa57PX>X,k)H+!?Stf$4qIA:6cp4'TSs'8PQhgu5QM$&:d>$
b'Mq$q"VacurFW&]AZ+n.EEUW-C$PYP1U0%80m_T#$L,aCd!(#iAF#OpIe;$)Yr98)(0Rs04?
iU9G^]A4QmJ,p'd!!=(R!!Y--!UTQ:IGU["5$M2C8`X[PT/N4ims-B9'H>lkrYG]A'jV`/OZOf
c6V#hLL!!Y--!=W?9!uSfR"u(Q.$t'&;(r-.F42WP/&XLal-:o/0hki5FJ88/8!=W?9!uSfR
"u(Q.$t'&;(r5!c/Z8$cE'9ub(,B+bFRsN!m6N,u"9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="单指标查询" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="eb964ad9-cfe6-4899-8446-421fe3892538"/>
</TemplateIdAttMark>
</Form>
