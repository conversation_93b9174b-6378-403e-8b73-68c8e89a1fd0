<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="股票质押项目预警状态" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_stock a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="债券投资风控指标体系监控-市场风险指标" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_bond_market a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="总体规模及授权情况" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as (
select 1 seq_num,'1、定向资产管理业务' business_type 
union all 
select 2 seq_num,'2、集合资产管理业务' business_type 
union all 
select 3 seq_num,'3、专项资产管理业务' business_type 
union all 
select 4 seq_num,'合计：' business_type 
union all 
select 5 seq_num,'自有资金参与集合资管计划总规模' business_type 
union all 
select 6 seq_num,'自有资金参与集合资管计划风险限额' business_type 
union all 
select 7 seq_num,'非标业务集中度' business_type 
)
select a.* 
from ads.ads_hfbi_risk_zg_asset_all a
left join tmp b 
on a.business_type=b.business_type
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/
order by seq_num asc]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="债券投资风控指标体系监控-流动性风险指标" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_bond_liqd a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
--limit 50
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="自有资金投资占产品集中度" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-05-15]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_own a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="债券投资风控指标体系监控-信用风险指标" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_bond_credit  a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
--limit 50
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="债券投资风控指标体系监控-信用风险指标2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select a.* 
from ads.ads_hfbi_risk_zg_asset_bond_credit2  a
where 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
--limit 50
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportFitAttr fitStateInPC="1" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="0"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2362200,1143000,1295400,1295400,304800,1143000,1752600,1104900,266700,1143000,1371600,1104900,304800,1143000,1143000,1333500,1219200,304800,1143000,1409700,1143000,304800,1409700,1143000,304800,1143000,1409700,1143000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[432000,6096000,7162800,5791200,7581900,6972300,6667500,6667500,6438900,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" cs="4" s="0">
<O>
<![CDATA[资产管理业务风险日报]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="2">
<O>
<![CDATA[ ]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="3">
<O>
<![CDATA[ ]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="2" s="4">
<O>
<![CDATA[一、总体规模及授权情况]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="7">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="8">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="9">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="10">
<O>
<![CDATA[业务类型]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="10">
<O>
<![CDATA[资产净值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="2" s="10">
<O>
<![CDATA[资产总值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="2" s="11">
<O>
<![CDATA[ 限额额度（元）]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="11">
<O>
<![CDATA[占授权额度比例]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="11">
<O>
<![CDATA[是否超限]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="12">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="business_type"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="13">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="net_asset_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O>
<![CDATA[非标业务规模]]></O>
</Compare>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="13">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="total_asset_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="13">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="limit_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="13">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="authorized_quota_pct"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="12">
<O t="DSColumn">
<Attributes dsName="总体规模及授权情况" columnName="is_over_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="14">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="4" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="4" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="4" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="4" s="14">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="5" cs="2" s="4">
<O>
<![CDATA[二、自有资金投资占产品集中度]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="5" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="5" s="7">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="6" s="10">
<O>
<![CDATA[产品名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="6" s="10">
<O>
<![CDATA[自有资金参与集合资管计划规模授权占比]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="6" s="10">
<O>
<![CDATA[自有资金投资份额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="6" s="11">
<O>
<![CDATA[自有资金投资持有市值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="6" s="11">
<O>
<![CDATA[风险限额（本年盈亏）]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="6" s="11">
<O>
<![CDATA[产品集中度]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="6" s="10">
<O>
<![CDATA[是否超限]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="6" s="10">
<O>
<![CDATA[产品集中度限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="product_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="total_mktval"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="mktval"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="total_net_asset"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="limit_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="ratio"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="is_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="自有资金投资占产品集中度" columnName="cpjz_limit_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="9" cs="2" s="4">
<O>
<![CDATA[三、股票质押项目预警状态]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="9" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="9" s="7">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="10" s="10">
<O>
<![CDATA[项目编号]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="10" s="10">
<O>
<![CDATA[融资人]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="10" s="10">
<O>
<![CDATA[证券名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="10" s="11">
<O>
<![CDATA[融资余额]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="10" s="11">
<O>
<![CDATA[履约金比例]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="10" s="11">
<O>
<![CDATA[预警线]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="10" s="10">
<O>
<![CDATA[平仓线]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="10" s="10">
<O>
<![CDATA[监控结果]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="prod_code"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="comp_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="stock_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="market_value"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="maintain_guar_ratio"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="alert_ratio"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="treat_ratio"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="11" s="12">
<O t="DSColumn">
<Attributes dsName="股票质押项目预警状态" columnName="monit_result"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="13" cs="2" s="8">
<O>
<![CDATA[四、债券投资风控指标体系监控]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="14" cs="2" s="4">
<O>
<![CDATA[（一）市场风险指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="14" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="14" s="7">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="15" s="10">
<O>
<![CDATA[预警指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="15" s="10">
<O>
<![CDATA[产品名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="15" s="10">
<O>
<![CDATA[证券名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="15" s="11">
<O>
<![CDATA[债券面值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="15" s="11">
<O>
<![CDATA[总规模]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="15" s="11">
<O>
<![CDATA[集中度]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="15" s="10">
<O>
<![CDATA[集中度限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="alert_index"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="prod_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="stock_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="market_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="net_asset_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="ratio"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="16" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-市场风险指标" columnName="limit_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="18" cs="2" s="4">
<O>
<![CDATA[（二）信用风险指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="18" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="18" s="7">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="19" s="10">
<O>
<![CDATA[预警指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="19" s="10">
<O>
<![CDATA[产品名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="19" s="10">
<O>
<![CDATA[证券名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="19" s="11">
<O>
<![CDATA[持仓成本]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="19" s="11">
<O>
<![CDATA[净价市值]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="19" s="11">
<O>
<![CDATA[债项评级]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="19" s="10">
<O>
<![CDATA[主体评级]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="alert_index"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="prod_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="stock_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="cost"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="market_value"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="bond_rating_level"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="20" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标" columnName="comp_rating_level"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="22" s="10">
<O>
<![CDATA[预警指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="22" s="10">
<O>
<![CDATA[证券名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="22" s="10">
<O>
<![CDATA[T日中债估值]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="22" s="11">
<O>
<![CDATA[T-1日中债估值]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="22" s="11">
<O>
<![CDATA[变动比例比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="22" s="11">
<O>
<![CDATA[限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="alert_index"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="stock_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="net_valuation"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="net_valuation_last"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="ratio"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="23" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-信用风险指标2" columnName="limit_amount"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="25" cs="2" s="4">
<O>
<![CDATA[（三）流动性风险指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="25" s="6">
<O>
<![CDATA[采集时间:]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="25" s="7">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.MaxFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="26" s="10">
<O>
<![CDATA[预警指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="26" s="10">
<O>
<![CDATA[产品名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="26" s="11">
<O>
<![CDATA[正回购余额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="26" s="11">
<O>
<![CDATA[产品净值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="26" s="11">
<O>
<![CDATA[正回购占净值比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="26" s="10">
<O>
<![CDATA[限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="alert_index"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="prod_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="repo_balance"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="net_asset_value"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="ratio"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="27" s="12">
<O t="DSColumn">
<Attributes dsName="债券投资风控指标体系监控-流动性风险指标" columnName="limit_amount"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="144000000" height="216000000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="8"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="rq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ksrq"/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-7]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="184" y="25" width="146" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="Labelksrq"/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[日期：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="104" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="816" y="25" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="rq"/>
<Widget widgetName="Search"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="960"/>
<NameTagModified/>
<WidgetNameTagMap/>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="true"/>
</Layout>
<DesignAttr width="960" height="80"/>
</ParameterUI>
<Background name="NullBackground"/>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="10">
<FRFont name="微软雅黑" style="1" size="120"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0000]]></Format>
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6709E3A17B2712D6B23823F2C27AC440">
<IM>
<![CDATA[!CI4,oncL?7h#eD$31&+%7s)Y;?-[sJ,fQLJ-Z>Z!!'o#@ESWN$49?m5u`*!m9L*Y>t#XNpk
Th6-o&H1pkfAe<.%)2S#hING@l#bh6o+\Qe'RfXbf@5&Q0\oO=KdSc[]A%.!J(B2&VV\uUHp2
^YuFrD2740f2R^#-.sCAAF#Q,Ys4M1_AaeH%B',V5cFNNm?acG?c^Fh1s*:2cAkk?4+sJ3T+
sJ3T+sJ3T+sJ5*`n2g'1XaCuZP+L['!=D$]AG>a1b2i^S=$(qZi:fV.Euqi!\2p'4[1E7"#Cj
))E-Yg5okg[)RlXfWmtg[pj&>=]A;VMp@!/BaIJce6'13GhZbXs$Ws"n!<QnkYIn[1$C[X"Zg
R&r@XS2(KM2V.*?S3DJMMYs3deQ'ZcW]A*f@0bVR'HnmP'(P)#,K*1gG35$E-)u3LNjlNJgB2
S!E3+-fc2%>5q7IB:^H1qBl'@aVD*[_p\nc9Z:;e$qLKktA`$kVmM)EU+eJh;pX(P,s)#N.H
-`3DtPoIHjt%MCaaL)POh[?E)uI[E`67]Agl2kRI#B!B?u4_Q;^/h$J?Z[J$E[9ZWQZ8?M=s"
NR/mGRq2(8-"&]A9,lU:(M1JFQZ8";g*`./?SDdOplL;;67VVCW-qT*^sK8]AM]AFKqG?V&fW-q
V`^q`#5&34jX2_kXIPJ7MkgtJu05lc?4:T+H!-(?S88=BAi\WA]A2r.KG0[1BA?F56Le$ePII
b,2(4VtO\1UdD0m5eMhJhNMg/n5O.p%ZN*ikL5@?38R!hf:d_C(A.Jc!t_PRljC._g:tfTjn
a.8Tn_JX,NR1EC&L5;<OX;[D,-J0#!Y]AtTl.qQjg^a8!Ig\66I%)PRC[uRTI%*r0rmjS%;VT
"9hmmYLeWWIf-a7A&>!qZn43QQb]AV3t0.Y5$f"`:'!6"-P&p4Ipni)bui]A_4,SB9q@q94S:>
m"92n+j?6$SX)sg@7>h?f0fmcRdo/8c<7Ghh+mOG*lT;[Z*Yl#lOd=`rl>E<)q[J\B;?FS(p
i=4"CKEAI:j#4/qg[+<EMAIL>?tLm"R52Q-CZVII/nVcA*bHldQgq&gf92g3/+$;C5aF
B!c!Ic67<o;kG3hIL5Xe)lp+AS&l's9RW0W!1:?Kbj[g;`l=TJlK6TmFYL<=\oM:h3qOmM^q
XRb5X#hGmTc+hMlQG(>^rGn6_X=PX8R']AjTZM$%SA7S&4\3U*5_At(k<SKEP;^^ks$%7dq]A5
2fi4Aj;#<ID<VsBp^!jCe>qXI@P=+oY*\]AIbGOU4so&=Ij50*?lp^DE8iJP%r]Ak;0[dp:-\'
VP1"L+!&iohbAJQDmIQYQh_R-XHXn:piKnY2rt*R&ihYq`@-HFX_Z@i-^*/bEh2#5$>Ec6J^
m(k+P&*f+1UL6+:mp_,&&,Bu]AfbCu:Qd]AokXY"e;-okVNj+VP]Apg0A/u1!16f)>Aif^/VD>p
Pqg1p(]Ap797ds:01+s#kC.Vo7mYpIJaunREk;nFp>?e58`C$`,Y:)`nDd2@8!D`D6<<-Frfp
"K]An'>=aa)/6$20paSZDlO?fp6t-0Y43AZ7[q7a)'Yt1@@\d)CMR:'6;+$gjO0]A9V+jg&5m1
81u6SQmul?-m2%EWB)$`7p.k8c>J4jn*F?h:kAADu(C=:d?E/3&-rM3Q1!eA(:G-^[o]AblCK
^!F-lBU?P=(L%;O'MfEW)uk]AZbIBA5iMG@QY/T9I\XS_qJVTZq"0!\+SB#\k>k-?g><Y1?.A
!1^9J@nqA41IWY9;`-KV58>+*@)CoQgk[k.Gi.A%B+6'X0oP-;7C$8[k()%!\[0S$jj>6*TE
.(MVYYp3;QE7)g>h8(V0qLL="j&u<Ga:0VkHh#`L(S-^CRf8gRQJK:QNu9R>W!t5EG9:s$.g
M't18OcngX1)0F%1"?Bk6KU=.pD>>"*>L;?=SaSE;7#?sXk(6/F'Ge&iIRj:&R>I<^-eV4Y\
/#_6-XNU]A`ucK;,7oWsl^gG+N+\G^7+A4%foS'#e9Dm+B8`)(n#*S/LBrAkJWcNE+'eAh9Y^
<K4WW)`>WXn6cfc`Urr/I'#j&8LUK-XQ*1UnMqHlf+kppo+=f&R>A>^nnIX_#OEcFnlF"l`1
D,_B:O@nc7f/g,WM%I9%]A$g".@N$.H\ClYkJ0So5Yn0>@rZ:'9gs&7fkWEa"eLLd(I>&Cc6,
i42^+INALgQ@e51WJtoYefshIZ)eDEZZ)6Ik9JF+ltZ\WaFV=mrk3@R"":V\I30b*DNFr6Tt
aXHeg=X>J_AMD"$ljJg*h=5:QJZZGi'G?,(>=&-qb>X%8@*.moS#i\R->m!'&ugq2:uS8WSO
GjHUr*!-`]A!!(7aFC"\2s%P:<<1:n*%l`pSb&Kc^qc;%$HURV<llJpC674ub.=]ApMK`OgY85
bWX/MY$iU_t>p.,:!*6M"\fa#!ej5_Xo-L!=(>br92UjjQ/&3KWBSBo5N"6huK.74e(0P631
J>#;='<hl\d>Qb9HZHLO6A![+UV$R!dem-:EEVnI\WdK18gIcoD/rIKe0Fnn!R.KPV:rI3(1
*XnDF>XiRPR/rE#Wsa0^[(1ZN**t`5ETj`QlP9JCC&t!r^YH/BLI3(_#"P<iGVknX3Q&1=LI
9)Co:Z/F"X(\'#(ZHlT:uTS:S?YIYTpL0m,AaLW*]AcP`lJ=6iGuV=:KerZKmQ?WEs"\bj4TE
0W;`Zqd1--uG:.Z)DstJjC10RV!;,G:]A?_"\E3o2r1n-2^rt2Z2#6I]A"U_]ArGI.W7#>QfjDI
4_N8"2?S`"E79@oM^5c(T]AnJ?goM%hN]ANbkOh=tdVs=b[6Le?BrR#h`N)(?OcA(U:4Q2W/Nt
*Y8YPiTqp,YuT]Aagu;Kf&<jqXR3Wu;U:WrnV7GZ4^]Aj<`=8##Cp7a_',OI,D+/d'%:3(HG`<
cQ"U?W!-R:pgGkb'7f!aE@Y?S9C84kJEPS9I2fUR13Ec.5=gm6Qa?1a>,dSoG,s?@&MbI7VE
p/r!cU\A1dZW4gI[u25W1cR3f68kQjWU"#!B<M_4o&R6!ik#d&Al4*[jp)cTtL4X1`"/83s@
nm=;6GL8jp%S96rEs"[hb43q!AKj)MdTU0r:,lksqVU.EpgO18H^78B57KOB!;Z+$-6BaM.s
1&<U,cUGS.A5UD'*qGh*.r7RO7Q)AUU5Uh.QX^*92dGr9VC9sT>(U9V4J_,(uE=Cgt35+(#C
QQLaWe$6(;6S-IgR;Ib13>!$&NO\-III"@)(#$SY[ER]Au^ALcP7"D2-72%(%R1b8BXta;ck!
NeiLILkpkCLkpkCLkpkC#Qar+1WVn[%&g;-z8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0000]]></Format>
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1535195" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0000]]></Format>
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1535195" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典浅灰" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="债券投资风控指标体系监控-信用风险指标2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="债券投资风控指标体系监控-流动性风险指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="债券投资风控指标体系监控-信用风险指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="总体规模及授权情况" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="债券投资风控指标体系监控-市场风险指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="自有资金投资占产品集中度" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="股票质押项目预警状态" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1698737469371"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="a2708900-9290-4398-9b53-8c79ce3c386a"/>
</TemplateIdAttMark>
</WorkBook>
