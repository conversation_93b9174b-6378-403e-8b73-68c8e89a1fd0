<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in('cfgwfx_gwfxsy_sjyl') AND A.YEAR=substr('${date}',1,4) AND A.ZBID='${zbid}'
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, RQ2 AS (
		SELECT  
		TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR,MJYR 
		FROM (
			SELECT 
			MAX(JYR) JYR,
			${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} MJYR 
			FROM TXTJYR
			WHERE JYR<=(SELECT JYR FROM RQ)
			GROUP BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))}
			ORDER BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} DESC  
		) M
		WHERE ROWNUM<=6
)  
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ2 ON A.DS=RQ2.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   AND A.TREE_LEVEL='${level}' AND A.BRANCH_NO='${pany}'
)  
SELECT 
SUBSTR(JYR,1,4) JYR,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,TAB.DW
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
--ORDER BY case when DATA.JYR is null then TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') else DATA.JYR end
order by cast(TAB.XH as int)

-- SELECT DISTINCT SUBSTR(DS,1,4) A FROM ADS_HFBI_ZQFXS_JGZBMX
-- WHERE ZBID='cfgwyjywyscszb_20240622171051']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d4100abc-37e9-4c25-b085-ccba13704c88"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.14"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1837898,1837898,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,5334000,5334000,3086100,254000,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>"+IF(ISNULL(D2),"","("+D2+")")+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=RANGE(LEFT($DATE,4),LEFT($DATE,4)-3,-1)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标值"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[JYR]]></CNAME>
<Compare op="0">
<ColumnRow column="1" row="2"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(len($$$)=0,'--',IF(FIND('%',C2)>0,FORMAT($$$,'#0.00%'),FORMAT($$$,'#,##0.00')))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[jLfb#eF!,<8t1UQ'-T*+81Be/Dad!Gm(6fQ^5Z]A?X]A3H+/aF\Z3n]Ap0o^J)V0SC>09G1eg_T
X]AD31m2=p\FJEofD:546M";5KE*O&$t&`qnDLaIUDR0]A&'nD7L%T5Crb1qR$cNKFflW=0sQr
uIXaEZ]A[EN)GCkZ#P>Cr<(":OrV('u3KA#):Y8:Hi+8$mP3q&Z5)*:J8?f)EC@erF`:23CPQ
MXD:*fDh;irpmOe-:-bRqHtfCYZQpIIVP!Stlje&_`dX<hi'c"@<E0(B[lAK[?9iJc3hn[Jd
D%@;'UT?!Z84&i"]A6!-gQR7rEp2#h-8TX6HNMlET_+&^7^p`\0+_.=KhtX>d[MHd@&rMpu'B
fZN1AT=]ANWhuH]A-qd/EO'!=8U/.J,MHUVJ[]AlR9Bi@ra$_qrG%9GCuk_er1&[Jb!UpY/nZ1j
.H=KE_tR3H$gGW5Rr>pJG25j84(`^QgRh4o+K%V[E:p:@-__Sp9VV4-tYPc`B<rqu(DH4Bq(
H>;DDjcf=OH$gV.(ra>Z:1-ES@_":ZX1"^:<"4k_1Y('2_E9E:1gZkm?*8c2=et*oG^@.h9!
p^0+.e)1se%4c7be&UV^:cWOhFB/8iLQH%aHZVOKE(q9^[,rro#!$9mO!EjnV5-"/bkRSH0X
Fmme<'I^uFcsfA0rbf:oNhYO7;Idlr7gdN+;CD#JWe]Ae?#VHNcX'PjoLHO'b$CGent"bDHJ)
Xn%p/h^s\)op9lP2Z<[!"%+^J+dqc@s2W=\?gX@l-HWJro>nCZWo)]A*&GlB?He6nYqh(i-FN
s#d5,Ms_E!He,0:c6OB"i''7Qn^P+IVaLj'VC>hGEAdN9o!Z\O9W2E'ML+N=#T_nK+%e&?q#
&"7judI=MWPh_'-(dK`Y;CeU\G]Al3]ApJ9L]AHJse8bm#0pgpuU5Er??e@!Zp:LB0L2K0QEMg-
ua\%%AlOM/,2@'^^pJ"Q"N4%5T:``9KHXuU.u_>9^)<P=Gm/bZW9mOi1OC:_c1%B=h4/*:5D
K>n<NHZk,H*Ri2[]A&FDci.=oEc/J%69^`f5&C"F`D1anu)rh7qatq<q8[la6UT)rArQL=>+1
e7CP8.sZYPeGO_,IsKGP03OU-jaZj3J!f@O([/SoI'Q!#jM0O=q_:8,l8st'LL5q07fKS4ns
=:pLMQZ]A&`)L3\E=^XWVleM_Z.)dqVU",E\(8fMYclAG@(gWf,QBqQ#:&\OF)NB4TBp1V%,`
fJ(:%',;&MH*E$hC6*hOWn?B8o"6^rq7TMmEG(N;]AKA1MFUQNmY-@>4r)rK3cZ9FIR/%7a:/
n:\=aYbIqc>A9q@<M<FT?=j3CHDIL5HV&e9)UMd]Aq920'rC,_Rsrt7W_pIJ%2piS-Y'sI*=l
m8D%;6aN)$%o`*;^F*9D&g?9TF!\8agRDq2(@@:!to4gf[uKp^^f9A')sGN2Y&%(eb>4D;8R
?@=18g^+;gREZ27O`.JPVur1KN2\p2noW5r7H%sIc)PU!<p=r-]A[(s%?2=GkHP^a/meE)t#M
K:#D-3ec`LjVqWV",t[=nC3_bKoams[_t[l'hs(OIg0YId0Xe.hP",YW@G4[uOMROC6.FaiA
YE^6I$`fCk*d!Ak.%l,uuHf0u7eir-qKMI\+OLVfG?_m$\7ndk8f7rYJc[uHt]AdG.)58&1@2
0H?rf?8RX`m.l[5j&oDM%nQeMWptf[B0kujW=lp"+(HB_qnD=2lb:qSqo<BV)q(Z%<'eqYuT
)@[Q[l''hE+#n?k1-67cqkfo'GXYU=^1bg>2"O9[Y+J*f7le\2c]A<<6scbfIlXXgG6<,ZiYB
Wb`W&VCm/^kd"[7i*HPkFPA\)3[^L<&-s"hARuT>>]AS;M0]AiS9,O;hldl[gNe!CSrimOV@$:
CG)M++8elD7+'hboRij(/"V\#:RhDMkaok3B=F-=J?qAkVoM/_&s'Z0DIRKhR1F2QJXaB%1@
0I1.V6?60UuitAt1@KI&qj$';XlJ"q7)Fk>f0+!Cr9#g@joYRV?1;m!W?]A)8T"<l<0D(a"U6
5+G=NN?p!Ab`-NnabfdJ[jc_I(Hp6<nMm[T*KZbWq%aVpT<3)LJ$$lls+%AJ/'`#-\sg&T0)
$_\Oq8iq64AO#*P)O:qbnJCVB2>8:>$H&'aM(pfi@j_s.Jo:m.t7Y%e<H*0^+Mqjq]A-NTEEq
IKP;4SF..00RYq9k[A1m!UNVNMp(./ouc\)Y<j^M^p_-="hMgRe1Iq4G-)eGT*/4h^@3)*OI
$=TXj?ZO#q5=QiM3qFdpVLtI_V@n*`g*=gKOuh2`rCh5_b/6[&$8&7j'%aIl,oXrLtAdqK>$
M1`Iq0>P_Vq,6L7VP[BjAC^@65IR*4&FW"+)QH$N0RF=.)%ter+lk;ej'@#_tW$eDkffo+$$
6;gu5SVsShgW%Vq&F:sW=#fhHMKt,e0%C+(4TY-eT65Tqd/\.D&p`!s+pqunQ(qf/%;$@9Z>
S^"aFJ5$`G67h8X<AlH3;@75Rd_EW[\'duY"G:mTTF&9P/NHr-1?cc1.e`_Pd#*ADWbZ&8V/
+lV.mMQ#M'Lg&CobWP\:E]AQ\*^7g@rI^h,/qM89j\0]A*4*jhU9Tu/@^d^ZN:_p`p$!M#88_K
X:Geae0e`/nu"1&=90T8sN[5*V#Wqm2$\CEr)>[a^L1Qq<EhQpFoP=ZM4\X(?L1#(:7FgACG
-S0gO%4bB-)(c_8gaJ!Dn_Gg%24'p#<hpEWbgn4=5[gR)N1RUM"c5NA._aTV6Z$bXpe8-sad
5tqih[!k;%(7D=e']Ai/@ik4rQ08eU2)QK=+HcEX+\sn#r0X>EFcC3T",/sYL's%+3`]A\Q43=
CDh`OH;i0=^/4(WkdD276>;W:#O3aZqGTd)k*\hSTQ"MHI'lFU!o<[o6TiCjijhka\t0-W>=
TU)I]A$TicP-H*_]ADpDVppoH6"MrM&1+`OT,Cj)<5?[61S0(/-O>H)%o:j5gEhabrB0u3dsio
#-T.B3B!<[?4*9)MMAi\N1oVlW.#n88Tr/1b;BdVr\5CjgtgGt4QOE/N"mR>\KMk">*ig"Mn
K>0`KV8fCmQV.[mq<\q*bL;-3m7"=oM(g'6sAk2dIThn8QH!>hA+t*6oc&VOV(XaWk%/+Z1@
kJ3g&R_=X`NPVG&#$bEFr5V/<PMMGE?\V!ec8!UN8p0CBqFL4e%qJ-=1$ciNMuE`<8:&hfPF
&X@0oF:F^BrIC>k<l)lbOa/!rSt^TK,f`64^d&G:!%$7i)>,G#QJj/_U)QB(/EP#8LXFWLLl
NB_q[G&Q2U7^1F!qHg%?PU`F>fc@<#4*93E"H6;gLBrVF"/dOT2DT,6jF]A3@1WjM-ooJ?;e!
r4AH%&pi3+556bfN*-C^4IUjF)7=8ugl(o]A/W=l?r,!PnEdhPN%]AeR7O6?42MFtVmuq=opnD
o&3W?kIf+sll<Rkug"1j1nf08R13,V4H;)QHBK/TWk'T#!O.*7le.--K0Y%fFI]Ak9P_VV+@h
eWhX_O31_@9&,4YE0iV!YtdAQ8o1/9H>+cUW=q$gI`L$frq]A1cE'#K28<gMZFP$&9RL:if;>
]AK(>ma+<NhhEQ1pqgHd*5O3AVX]A`"J>V>]A0K,T:bE_XiKnFJ"H<\#cW3]A3-Q?"JsQ!$gK<4Q
@o!J(7tkBg"P;ZJ-r7M3d(/e()]Ai^R0Z$A+U[EPW:TAo9J5PjWqBd"_4>Kh57;h]A%KOkQANG
1L2W]AK&GZ_3B\[_ce8!Z^L>&-UNcA:+'8LLpV@CZ?PPDR=J%/;E>hq4t7h%N&tO\aD(XQr*7
p>#kH3hdUYB?=5LF<i7t='.fjD4^PVtE5s39F2IWr:JC<_N14p+`7A)ra#^=M@LOcEf"1iF6
muoO,Q3mH@XFCeF15\TIa'uh+V5,dWfA^nF+Cnrg,PDXkj=5/ntu%)a3k/H+kS6$4/\)LEf2
jr`3n#[*Z+&,'ik`6;0BZ',CF_,o&mZ`)MW:/edEpP6W'Jr)^<bS.2b8b?Jq"B3'b=VTUPXI
QhFl`!BJn6$+"U]AMu-%thZ^YWmYVYaXWC+Bar?LE2$G+jl]AtsH&"e*nihsjQeb;9qj`enJRQ
b"ILfYSmC5^m@%jkI:7TW9A'h!Vf&f0ef+LL&o"e_[LeTAoi:JuK?g!LJB'\`[=!b%\BMNbo
i\2TEgVT",+blP.2(mtV\pTX`Aj^;;168j+/KYS&R"SHNi#HZOtlTH\r,a(f(`TdN?9UlILX
.;;6Mq&DsW$`SNf6*L7Nnm4h$tk-J1r&3&[#H4rik[1)6P94#94.$pA_oH%BFn"&$,Ma.8?]A
:enC3Id]AOoA2.pe(J1sK('9'A@LMaJel2H`KDb`>#WGC.*'@Q<P\lGk[/danI=I#;?Qkt*T[
%U(9u6"kG4,k0ftU;u@/XgA(UkG"B*Y@\WYr8I22G.WGlERj1E]AnuX6._`iA0Dn-k"\`+9\i
Y+am)jC?SU/2MTQRIoHOgpnB7^!J!UO`so%@$/]A!i/%61]AW*3RMS8q(2:f3kjNAil>f+.JP*
=,Tum?Z`GNt9_0eV6La[KLuP@W@`7q6M%2D]AfE#]AKqM1\&HR$*?B9!Y%=,+2E=J.htQm^$Zp
=Co?qP!$*a2_D^o/'rQU&<Rc<FN!mauZC_Gu.^:`&J6@K\pm`[Ysg+Iu]A3t5/\X'FsJY`a@q
ccH@eXV)rPGQY:Gi#ITt7O;=tbY_.nR=?VS/@U^sO&9K$V(b7eiZ]AUT\QGWhb`c(3O?4;&U\
H#K-&;e3rO1O0ZViJ_#Fk?^iOg<H36agea4KnX1ZLNN$1mU3bP)fOER-2,WR#CjZnT!QJP:<
4*1(;S<QIWU[T@IK+S&MLDE!CHodBfUtL>MlqX(UF7g,EMs!M\4Ri:Ptfl=kqj?`lOu>=8-T
gNI=Q&1DMiVAmOF:J(m6tnel\9V`Is>,fA<%=ZP>0r-20T2:0%FcKC0k_3:A/3<8*I86_'0\
&H+/H,42TGK)#4OM)b)]At0Wne"Fr?&@o(N5>?tX\V.+&0obp$mo4)tN!f-bQrdDdLU^RGLog
]Ag7+&!0k^^a%!e?2P(0R*a9>c2pUHD1MZ7[_CL7PRt2m8I9*\p\u.$3"*J`G#Zma<QL@d?*;
1nsEsoi"!m5d9[9X3%KTftQa#.L$QhQT_s(kXVtTp>#V:km;-a[8+D^2.kHeW2Pl@)c6/(_n
gfoZIRO<oWm]A:^u`b.MsA"Y>@aLhmU8P&C+EH?;!`GZr6uO*^9_Q9qL'4tYu06:&]A4I&gT*-
.PW-0o&mEX$E^l-sF-upUaqXs70n<+)+*nO>-X@HE"[?/TrKYRYU<M-0HOGf%mEG<@>e,C2(
Q=\ZLi3,8+;]Ai^H1]A3]AE"_7EQWMK:]A='-!WrIs4mo/)/*l!9=4*D3l6'=ciP]AbfKBFV3(`SS
t1C[m#$U8AY(@q618Sjtm1+1jF#at:ts$]A9h!-7@6;-&Bd8Pm$'`lc4<0!bQdk9--0<#DT!N
kDB0IoB31`+@WY.6*SH1,c;M2+[mrK[]AH!MUNg$l/#\P))M]AK\ip7m9*="G3/F9jge'JFlmM
"\kdXe(lVN$,Mf0Gb9"ajM]AW?gR8%N,aOQa(>.>B:IhfX%TiT0[$2j]AlH@JR0e3ld(X6;I9d
edu:[bkXdoVrRn=tM7L58)X/*Pqb`Q@,qAI;BP?CGR)=YO]A%r7a(e*8$)Xsq11k'Z!`-C:*=
@$,H"&XJ(f!f__+h*"7i!83:F5D@-4qH?ulegG54(&MlGetFiCYd?N%8,pVbbSi<4DtU_ONJ
muj&FlMKiMu->nO\gCb4+Em[F&?U,=2SG4@:eD9e[`aOT'dW#V%Y]AA0+BXOmC7?(CE5QVh^M
iY7!mH$G.rZb\/+*?e79Pk,rW!0,%ZVMAjGAQf_hYsk<I^t;Iqrab:)[0!n'6Z$K!GHq=MjY
I5.\/jl.++0jL<GdaJ`?Gf=n==buQpBAG8BEShO&=p95*1O[Ug07ba*K"U)(,uoSBS,\7rQd
ad)Rp>X$;c5QTio3=k>X"qK<dd$SSm0c]AsbKUH-l(Ir\fo4??"#8iFmQ7<IgYjRY"/)Nu]A%N
431;iPejkPhZa$'t=traRDG*S6=g)`F,*\e`hq[X&$5BiMUEFrePd)ET-Qsil0hZ++Oqt*C,
<Mftj<VdLmS-E-u^H1R/qsH:f#SB<0[Gp!6hh'\::Q.$c8b=lks=c#qF\r2_"]A4!7a5c;Q3.
[Y%=#N`3&,Gr+i+G,Pkb#V['>,C:A-qWunAh8:)r=S'lui\6&h.:!k8;[[eV$NoT#HWHT286
""DXdoHA*sk@h_d'=YU.7_LJ-LZ\[JT[6idDEQEQ9T'L(378G+Rt&GTcjk<pULMMW4lp2'/2
I-@%_1g63jdI-O1u=#<l!YmUr!6s`ZSNm;5^b0.`?cE'Ce!rS`q#=K$u*F>k`4["+XPAh$me
[J1:YI]Au#Ki_NMSOZ*]A>9:lBPgQZOU)GF33/7kEmcDS6Ihd`6gj?[gR7s,fD676=mMNHW:rh
FmDD[\B4G5m0eTY8XiOrq)(##E35IM-.du5PVogjMe8UHua'&^I(juD]A?(+s[u+t3/!&\F:(
[pMAf6*OdNH?ARYIM]A-bNDLueE8iP-h?Ve`WSmg@i*a1;9d$4)@q91fnR&60)fl(#Gfg:+WI
jkCp52$*%[:\2hk@K@&cc;dm<jFIbe@*Ea_:3[(KB+Q5UN?e%s^dE:#cgiS!AV19G-*V-mt0
?7RW3thQHDF]AK-_Gs'qH@1"UR8Q;NT=.aDU"c"KaeS7Y0%dtt>0(gab:2IUVljnN9GOBKCOC
5*\b3*:ksg(n%=;W`5qjsN;JF*#QZ#t^j[X8hn-OW'SWPLG7"%EkW6f(CL<SS(HS;q&8eRkG
s96rR2JFWMS*+he`dOg06D*3'CuMi%*Igr#D.Irk5B8=:@S/WB^*2i,964KfF#:oF9J`omk\
b*Zctb&<Xtk#@>9]AuK&h.V.>hnF^TU7a*r@kj'aR/`1*NYti]AG<^!Z#`.K'A_=ZoAS6##\X-
kRJ>?H[E.65/(DeG:)l:FB%$[F_9?AT,Ojo#F0<T^`!-6/9Yj9+3]ArbW@ih&Ug:?8It!\Ik1
aaGAKo3.Nk$`tjXp"u=Wk">_u9RO%T(_`OQ3Rmi?.p$R$EA+Ok*01EJ(P@T+jK*g=&_'YI:M
umM85E?BL:f-'@U/3K``9hX=JEV*+]AOP<GC]A\D9#stA7Jh\1"Q3jD`OYi(Rs-ZgH22CC!>V7
A<ZaB'g<^UakejBTCjsM*$;hU=&<CnQg.'DWIl)7]AA4d<]AgoJWQk@<;bcThW4S$2j#p&[8?V
f!QXCnoF+SZbDftpR$;(]A/3f&W*Dp)H,\e`@(n#D^1N=%:HQIESXSs+md'9O>4%islIjWM[K
1*\4C-t=2_3/Y`s6XF%;;n:W'9)J>:_@dP*gCkbmuMdcGO'n0msNE;fG,q?2F,+PG2CE^db1
>;0*:&f.NCX83k=`Wj@,Y6&ms_Z9F>0[RHq&]A/-t>mC2ZTXhM436)`#:$Ur`8]AYq_;N+7;fj
h3aSj*YsiL"J0<1R!>i54u%((@9ePWlPgV+3:=3Mcf^CCq!;ArH_\ZmTr%u1c-#ET_!kA9f9
ej3TtuAeh0!J1)jrVp;[[2bjctT$RJK#?-$t]An_L7UWO!V_jFok?7kEtC!$<^Bg5r=@F]Ag?f
]A/?m>a0"'85#^DdLJM6/>N>?t>05m?bIN%Wk_XC+`F?&(Cq9um-slYgb3'?sbFY:W1>H<Q<_
@TACOj,$A>)ise"ij(Y@K'Y6*oCbq-Ns#Y!L8Dn3N4^JQ/1]ApBrK]A6NHH+9YTMU6"br?-tg\
K2Q0nr/O\(5g\6*=(H]A&U?>IOILo[Q(I0kS)1(Qb-6'@jp-VW"3/ZV,3&.$mIE?:WINC^$E\
$N/Y`u$N[CM?FskC6+b0>dYB+IA:cJ.5]AmFWOI$XuOpSCo>hEnqTP-H.l68i1>1;^m0qV9KW
,0I4]A3.Q(C.Q'f#"d?@k<JCK^#(A%X=VRMuFKm3)?V"(kg\]A:&l*fD?dChe"Ea^M<#c>U^]A!
Psg#DR-3UV;2Flo]AUijc8J!E1NMM\2jDV\YBuF?ecA;f<-`rqn_93n@NTRU,+)'D24#'WJie
F=u)-q*FYHVaJrMk^R-auZP&e$fF(@Y_5/)5rgd:KuYc4KH:9tO'riXsL@#"`8-%4N>sE0@k
R\lTh"OtV5!5"CosDZH?@p+5js-+^fEAQj^DU!e"'f9pN,apDEh-L/#@PUjmS9gIQJ4$SWAL
j&9%T'hE""uk;IFg%@cNjnu:Z),A7,:+lL0,\h\]AjDIscIse^eSY4eM$rg>7LX?AY0Y^e4W'
bY*LK]Akn"!0&E8k9-j;9uUp^lVfl9/DM&f,h#TVN,KN1-*In(pApR-ja*oh1YP(64qISDlQs
M;&.AW%@T,VlDT%1#lPi/=HSghBE8a&$gP3a)5`7ZJO3coP[)CZn8f1#(/VZ0U0GMYe<T*),
BuI;7-E2F6fu%C"uepdY$ke7\KY2(p0]A+[e,FiJ9<1f$1>,R!gl]AIGI't3-W*R[2i,a[W`?h
1ek3j:F8RHO8_VY#f4efSX/k9?5te!P2caZ1.B:Q#!C<d^#4ID6PUunrYEt1H_Tk0JkPEKo0
lk"-2m]A'G@o++W`NB&$>E^os-p+&GbnWWH+NeH..`B71Lc^j(2.Kn]AH62:j=OM8Pq8kgY[@I
g._^W]AFA]A[a>B@Gh)[_Iek&?8>U?qUSpXB5_]AgCe`E2cce:UO3lXVSX"l#bO9ZXm.$>*&9gg
]AB:=83*pKFFTANUQ(-Zt*c`;]AAM.P;#]Afk8$=>gUC5s5&8b!F8YPc>GC]AQ2U;7`Pt^CO]A1]AQ
]A'fMn<=N\1`X2&BJY^/-lFG_;_\R5<d"gpGZO^FLs\]AdQUglehoA6$AeRiBWKOP(!HLWN636
?W*__SU;5]A3Da0LVC_I&4[!4Oq0ZRfe.Z]AFd&5/kn"9>#g5E0Glk(1b.B)TI@Gr5TNAhK?6G
]AJ&[)K>scC^B.ERgg`]A)F!?Z0d'"Xmh;f<MY^8/dWI<2O^XB-)m98\RlT?8S*1QP_lR)#cJ8
AG7_AjH3!/$?;!.0VKrEr8S>q0l]A>'s?;R!fFp"_?>7Vb8sD,j84+iQ7Ij*fhE[rb]ABhnpoj
2jc1m[\dXC$8ke\[<(QV7eSNV2%ONRo$L[q!G+/+4>J\#BbnI1lOf]A*D(L7H!kkoZd1]AjRik
V4WX^Bu?/=';^QnkDbZ.hn(92;Xm78kU[*7b#(<-OkAhhh0k=)if<blf/]A<+DG!@N<]AsEcF`
Bkf/rT=R!WdOMAeW&R(1&PR[,dmGl.?Zk.4nV%%"-s.ZPdjWnK@?.V.DQbAX_):[phP9Lb3%
L4pO2rU!*p>jpH=`P#9/:$,&g"ef;h$TsB`0lfGpW+>9kJZ[3qh5q1r\ESD>!ttk"Y0*8$(&
Na"74(@oKP;-1a`>"9e*.]A.D>@W^L>7K$[ot9I[?G_nMg?g+&?&T$cq-O]A#GFA=:T,4=oZAh
GL)2_rWV2fZbrUj7J[<Q#!/GI5QIqd1kQR^Smd*=n@Ao\A#29K9ZSZS`'7T/X:fO5!E/,+]AD
'?O$GcSZeU;"\JcY:S$BUTcq[,]AUXc4T2\&"?;(Lh^6e+mIR.1O8J;^FP,pM2C8(aJD$9W#H
M)?P2Cj6/$U7LA?pkU=@F%4NatMO1?,9b1mmEki-WCu;4HMlr]A`ajbcN@QZsa+Qa=BnG\u%<
T1W=6!*Q*nfH`sMI#J2k())hJmTg2fSD\0`Ac*6a5o&p'Q8[%M,P,BKltf'\j>bqd`bK"Kou
mA0RJ,G*Gd.//3JHX7'b&bU"OrrVe&Fq'Ek(YD[IJ&[hJEH]A=0)IrG'07TrJdLjO\16?a9>m
!La0r*%@MI=8_&,.K(4Y<!1Ud.I?F/Og]ADNRlmK%9<7-;+1)F9`u2!;pmq,Zi-.:!AW/`=F5
r^KPaQ.@2)kmu0g&VL]A.#r^RLGA.+_iSkD[r9R6IQ&rN,#81@2-ApVa#$aF708<H&RuuE(Zb
7@Q8m$^mLAM6KG]A.j?c&FcRRrss.$3XgT3^QT8_/E0/6-F.cVNF-+CjC@,ghoVm46[;3IbA9
]AS_(3</e6W@U.Tf4/4S_2.B<X+K%uW4Xc]AM7++3M^;J1Q3eJ[+t(8%*loq!Jq(>5XRd:0-_9
"4"Gd/[A(kAVAc'CY<6<Rq8E:Kj^=@J=hZpcXj7UisntS,ALMdUXH,,uB5(6-:]A%4E!]A&b=\
KK7!U)!`fh#aUnmK%oi/)Wc*d`9nC`B40`<ko[cdj<iKu?=TkifY!8X+$+`)#Y`$JK!sq/pO
!Lc&>-/W_toJ4fHE.g04+J255=[dVU?gFUr4["[he4lb0nWc?0MCo%cr$8P*=7+m_aN(IG1;
58s8Pf5cp&P!QGKb)>,@ql+;CI^OQ)Qf'&]Ag`XN(p)<\.@PrFlI`(3qWlP6"NL<qING?_leO
((;US%cO^i,8;hU2qACql_pKF_c3H^`KblIolRGJ(sr%Iqa-U6X@$ZIeO,sI?"8ubTrB_(ac
53H1+u#s+MYj,P&T!Pprrk9u4:,]A>ULpmD$+!s51tC3e^VKb8I"WG4SkN]Ap>$lpdk@(`hbaM
Xr"`Bl!(lAdcJ<fi*N;/'R^lMdhY0STRL?'\0(\(pQ<.@F`NAhl?0^Fd-;]AW.**4CS"&jK*G
<*%TIk8r\A!.Gns50QZ-gU4K/X?X9O#`VfDfUK-\.0=&+iUaIFsc_`EjRpFam<W]ALi3[Y<$G
*o+BH"IQn)>mpOjE:'X<I4ph]AWN_O/6pus'IJAJNNZg?NF:RO3A7m5Hq8Q>OAn/IS]A4)Z-9O
<u>ReB,<e8F@2&N5]A@S6`R^aN*/D9R4h3`edWD,%]A%oDG*uKkM1tg[p^@ig+<**>A4l%Ygbk
X2^@Y]A&pQlDL1.7bZ^]A\".Af:BHann0lIUtS6.hSH$jh8Ej5lS=Gr?'`Y>s7bchLN[UVL=O)
46(eq`DYibci2D`NrmV$o'%MpHohceG7XUD#l1)&rE\U*d.,BYCAc:n[,o(+PA2KZq`g=>%u
9Vb4riG*a8,"5($36;7/4fpkPH-^B\f=p4Pg"Xlk(1t)%dZF>TW&n0dUiZWd/*j`(3X#e#J5
+RGjOr=fTZM'/%[B21<!7>ZF.T:>tCP$2^#8$*0Kr3]ANE-1t(GfA\tC2k;i&b:^,^":<)569
,?NtUDe;Q><KL?E=NYg7:)J"c$!jQ@oq\4=k:k)aEijBRFQYO+``.`IiRd?-0IF#k0tgqU&R
-iU>5Z9]AeBABSa)(iW?9M^?P7ujdeoH?RgsD\L9,@OCt.aZWHIT4?@SqMs2jKO#Q\H&KC!&;
>J_10JLCDAQB"TK6GbYqbZp[/9/)VEU+NB*8ml]A_:X.OJ\k8e030;[4O%N0nI;$Q!n,4.rj"
+J:%PCO2O!lMF2]A72`,G%MpQ5CJQSn`%KX/=h/;eTeQ%s?M,dh-JLAT*mIK3=W,R$t3G,X'2
N/\CVb]A!o5DFJo4b@9bR+3N4?u$6)Q`@(k6RnlQgHQ9=7h3u4C#3]A:?JZ<W=\<6]AAS?PJr?o
Ej(0#;KlSDtJ5^3:_pVF8Y+<LA+eC4*2S!jBlD0W6+D1B3Yf)ac)5nQ-n(#\dT#W/a(^+a$W
'(U6a;1@ugf\ggZ(l4A`8=),)9.A<Df35ndXCV*/OUo:o'E"c5j=lHPA1$"j"bShFFlpS@\^
V)+77&4L.q$9%_cp]AO/NYYmT&aV\R4kSfa)j=%t\kl)T9E`gK\!Z:R9p%Rqp+'e#nBT8LuK[
H)Q7QZ3YPKhg\^&@(qg[J61%@=>.Y0+AILmdFc&JGIsd]AL2LBpOhk7A!m9-B[B&a7]A=u-rG3
jg/eF4i\Xbr?<BnOMG##Z^RF9b&'Btf.B9VTpS]A@YFP@G>]Af[St(O\$#Yj3a9*E`TH0k^*-n
(QKg?@;cg-n3Un^=j_F()n)qC0X3s)L.Hi9JIZD:>2m#<JTH@?`!*IiIq!V2@GZWbYuhkgf4
%(O^4V-ii7tY?>D_?&+%PA!V4S+;<NY<jUh9SA3kUa\H97Ucf+EPR,LiOG:jALMh_GB7`\s\
1)$'eM:Q4>W8N,UTVtF[%q)RLGM`ka?W^";(e@[=Wr!^@W&Pnc[4@:$TmTZp6\[!2nlm7ng2
(A$[UCD_g4gBC(N[Zd4`W`MU3!A69(<e@-AK]At7C\8!H@JX:i:Hc*E(uBIoe7LE9<`96((IG
]AYH9?e/"O*F\/'<M]A&]A&jCJ@I1Kf%b$>W=L-#jKP)2/S&/Ce)E*4KuO%+1iq+>RUpkNa_D,L
A=>e%b5E5b7,,b`(g:f/;eL&b-RZ%1+O`(`uUq'50a7b3E$J/GYT^O91dNj2Yke_N>`%VC_+
6H'pt+U?`MH>D(%GBO9VO]A<1L#hQn`LaZfj'f`WCoj]ALD3(?I)sCg[t0'ls*[RnF]Au-/%Paf
2u/]ABM*:8:,1+3"IGuG]Aj(N1*;B@GaT)@,g4Q1*VPHjEKot2!t>5(%"d^/M+n>tN,&W46LXY
!gFXujfZ-ro0aFSZBfXcUM1`As>7L,5*u,XMq:R7Aa.!l<=fNi^$t#X\i<og3_KhQokVIP&5
2jWYsG=K>r$>O]A52bm]A=0jMb[KO4[&rG[D0rl8T2TO'VdKT-3!b.Pi'%8ujcp/WPe*CdLNoK
I*bd9VF/pS.AA9cFkm3(Q2D+lURUE%?JNSgM<G-Pj'Ha?kalPE6Y5oQ-N8XDrtEmFkLZL,`e
?hM3,+>lm`c*Q0#j*\?&\$-U+Ie'$4]A4r_ie.(DEdOk_Qk)?3O5eWXY;8M"k.U(:MLO&jh[u
)OKJCT-t0=RV5f(Tq'c/As%ZY`ml7B$l/'M9hT9Af*K2&HW=ItN`_pQAW^<g^S[pVo(B[*=n
oQ<g8,M@VqdIu#0Tl@PQ5FtHe\9$!1nIsn/J-DBFe+6.8LW,fB_c<@8h-&:2&rHTNZUrj:[N
,WG*?EoM(Y8h1Tf>eG)M'KPf0WaQKAEfDXqXNJ6tJ>2E#EfbY^6ZR6_O**sn.C&jZ.;.%kf*
cKT'cDE;oOF+L]AbgUG4g9Z1?Td&;S,lCmI]Atpf[5+ADg9Wtg;]ALe'2duI^G'lTqm_Yl9I=G@
2]A_8m:TgRn,TS(;%SqoCA8*@e/^(Iql[<I!.l+a-M]ATV$,8B)quO?d<lY6mf>`8Gh)6JXj0S
VP[tI^1;3.GH,(b-0NL;I-`A9R,_n]AWuVL]ArcSKmjS;T2l[>1F&TBd@<`U"caJC;&O*6XN5t
X5*Wumh?D6t6!)Hni1;9<b,&5U^&psP@0Y0]Arf=<"j*J2[gY_U`.,Y'l^s;T]AA_fM_@Y#8]A"
>oq+/^+$2UP".YTp>W\R+YaOIoq]A!FsRJ/a6"_+Rg-</f*@sgLOR:Ao%_;=-3!V3RhVqS[bB
OKT:"5lIX=4cunTOb+CpjhchP&\?le$SB-7C8<D/5!F>8l@k;l5M<L,;$DcP]A.Rf9e5.1N1D
:E(Z:GY0@tP[mHZo)`cmY&897G(7r-*&gqPhhCo(V/AbN6F%noFEUlq2>f/jnh:AZ%O9<RE&
rY+eJYFcT:"/uP4/:_ib!uln?;=<=gINS3=&X!qZ#S'(CH_bLi6?l'-4leL]A:P`I:7m0%[h7
Pb_'uiMKW7^))>ij.@!.44l^(^9mOLh$OPa&O)\,%o2&q0Z-=on>1=9H$ZDB0h(^AJVFo*Y@
u9@&4J3F#Gkhu'::bk?fB3nHr,If0d-J(0JEcuSpTg=Za3.JpgGJ?oUmm@?XDf1g@Qad.FB8
,$XTP@\CiUN3(O%]A&eXf[ZMfq/9RD1)B%fknN&rLqu\rjQ-5R2TB"A2#^GB%O+<EVO[\_lsb
I+mBmca.>t2AHEOsZKQLOC$d9UM+jQ'X58ot@@:&Je)DSl!EY]A<WWbdZ*Zt]AHnk<gk+QCCOu
]A.Uu/0U;IB2(l#K1Q7_^i.OCfmL*)FH4?_EFR%-'h^PpNNn':T%O4DZc!ne!2sPd>Z!@K<a_
T4_hVmhUmRYD#Nac>N5otja9Q]A%I#ZYtR$<W:p;60T8=#jff)S]Ak4G%GU*-O'6bQ:bI9p;oE
]AfJrIop:/8rB7n0+$[\&hD]A?)K9PS53Q.K+<EX@*]A6$+![WQN*e<Hu;Mr3sJ0Y.9p;DdT.??
Hi,4qBa&+KB]AVc<?ZPBQ`TXnU".:0BcO4>[/dG!_<cC=Pp0n2a_8#sf30CF2tP;<c-p8+\DF
%]AX!&O`aOieC'Z,#C@U2kJb2%dbZbbk)qBTa^:rRlZofPIIS<Q*7F+`pXo70&T)iWk5J&S"S
P_$+rb,nFfba8%u)$#%Uc4\9@@Qk%&=:4.$<f8BF'@,8hXmT@[B1uL%gs4:^kh#fJb47j8-I
SF;!r2qJMmH]AGej0G/VHJc'f9_M;dCAT7Sus]A@Td"m8J<FgiSHCTL"DnhV+`BXEU"9__)rFd
;m."3IVZGbP*7%4>$+*mW.6gssX%ZqBfh.AnAok$QCDl[24s#Y3a[$%8'%.*?38/O?U0h3ZV
a3E,@Ci@R9Oc)J9Ueem2&18r,3lk]AY31Jp53P.J!P+'@\e,(lBnJ-S$+nJp-QlGImVe(sKqA
iQ.D+<KIaf3=SIP/63%>!DKDM-;WH4eKXtM:dUg#C`A`5P,[</UlO%GAR/^6_=g<b<1CZdfN
QG-6X[,`#aA!k5P4l;'n]A48fml+c0de6?AC!<>5taL[EN0e3*"+hRT,oVRq9E;Y`.nDhdf!k
tMmn)I,Q4Y`fLp0jpgk.H*N*P)IeEg,%lUXJc7aG3Fr*b;WmAf_&mTKLaJU6PRYn-rfm&^r:
JX)EoYhY+s0NT\_mG2?WQ&7qS;o4#tuU>/403g%rSTi\t]A@hjS`>#AJjK'+3)en*l/k@HBt0
W(?Y#Lb*[q=ZX3glXC.km:`\o\:Qj"U=+n>KkI;o^qmYST^YW:JgV#AB@PkpCWa!kC1QN%^E
5;Y0TdR0.jZtGGoR)>^HET@.uLf]A-4%B>E\koJWK"G\#a*:6bXEkVt'kr>iBZZc6+5PT]Aff&
8B=_,:Tr7pStIU*ZV`.7#)/V9)>FG8Z]A<G31+A7XR"G(g*!-bO+sjJI]A-56`h&Xg`b/oGG\9
DrGc2<rL(U8RJmB9sZn?PCZlJ(bq9oT.g2J3`Z+mp1#">[?jpURn/+_?oNc3ecc)pXJ"e7Ud
A7s=_W:Sq#@dX1lAC?Q'm4O8ks*(f"MYaF?,LoHOAaEX*V/6?OLHcLcOUe%BUc&C`0=<#PFL
8eW$Vh,Kn2d\d#O!N=>&"3(!kS7:%`h<'T8YNn*>?;)5`jO7W)-86OOiY@@plOQqkVIS*,mj
]A>.a94*7%-df-LJZtDYQg`#/.JBY2&c4qth&Zq.6'P8DojFR3rN#hjs@^TaqmoYSh\.jhZ"J
$p6IPplbLS!jeSiAn%2\;K&9j8a/f099cf^ZAg5_dp*O7O/mYAnt4lh["poG'"Y*eB[NlJd7
fMuR3!G#Q/+$7>Oo+Spp.8!?I@N3r(Ja$J0<DrZD+,uQ1XaH_C>+,@/2u_J4\'C#PCL@h"&e
BAggH=2`<SJ`!s"2munqQKbsV(6M^u=]A9TUR_!6^nL%XplBnrVi-,BRm8a_K?Bt2!pq0onDB
06I#o=OW:Ta[VZ.bGYN>KRO>Qc'jnNr!T$l;#_#f<MAbkYL`r]A6"fm.ABHKEA;BDntHoEo_@
=@?Q_gB(&PjiO:"n</2"iT\sE^hDnKp$F174f02`nOM2?0aDPQRFo?sR*3),2Ed1]A!LM%[C)
r.Qeh;Zt;UHB`TO`d2Nj;&Cq2_2cZ@l%gh;HW.bsd[dc%?^#mr:!/K2jIR[RQW']A"&+OV+$e
_.ld4L\Q[feWmKIhchE,F"7bBY:K#@iZ_-B?$_Zs1p@.;."gDC@TjC^KV_qVqclfBi%=Wi(G
PkR&Kbrc'ADk3Q866]A,4p*&W"PZXKKeT[OXHjf(Fhr9Ik`5-G?B'Vc&>Vj^8fCd+C;XYVD?[
?YPuMq8>hU&K*NDGdUC@qQ7*1M!#FSpnbdMOBNl9rVbM"f)03VT'`l8i)bXdKYjh+!n#rF/j
o(&&-m@$rZt1;s4.&)$h,R#r4n]AE7=a]AboJ872PJNDbIqdDD2s7qEsP1H(h@9c+7tWZP-)Oq
>H\esXg)snnJ,GF_73qJd?:;!3P%?pi#rql$:3cQm<9M=i=;.`9eb99:Xp[.4W,jM%Vj2^]A:
/MT8//1Z0O.ds-,uF6IGsr+(?Q1!d':jOrkP`+5R[k`ppgP<-L>8m7[3!*o-pfW.HQorBfSs
Y7#nr-'Mah)kGpP7^q2I4k0uCLDqOM(6X"%>k8V6<g;r0u^b78k4TLG>1f.b=JF2^MI/,Q1c
ff?]A_25UEhEBtoaEn?-CGOsbAX:l=<fCFqjR9phM/[94d`M.+cpKsW+Q=/ceJ1h?hAN6ChoC
@_AP//o9tlD<XK/u<?N'DC!%spH6-MI^BUBll7_2c/eWlqQZ%?62E&.0OiA7L4R>8[B)Dq>,
lUW(f^X;F1h&Ag2$P(sT&\9O-gi?*>_lI*<]AVM=q7IBSdaD%R]A6Oj'#5^m@-9&2V`<d\_ngq
l"<'NSET?YK7HrAq!\h%#eNVVq-r5*>Oh1]ABm3=.8<7o)W?e,2d29a)GZQ0:b3c'E*b5"0a?
2IK2NMWAD_?aD^ep6jhGHd!%ipL\'G9BJ849UJ,okq:Irs^dsa_pTK%W]A,\tXPB-1L]A`[:qG
nfr<b>9p^XVbu>Q^=\RPTi-$Q@>l_rf(6qP_19hDd/(-*5Kb8Mi'$f>F$K+pdOdP@i+Osp.)
_1+HFk[c.TG*LY>"#V3[I,ls'O,5ur;@X3R/aN)@`_L#UKrnLjPmIgiX?D_H.F;E\fM#[V\Q
5f\7Hb@,oqnaU4A:<-D.f6$7^)o!=r<*FndUp+Zj:8b6_&QAop8h/ZD8uL2CQ%f:5\@dNM#Q
K36%0piN0k+T]ABs=cfN`RtLF=`fF!ncj4!gcgV8<!uar)LX%ETPs>Ti@Y2r2[tUGVJALTq63
gZ^5_&E@V=O)Ch4M(&K2lOKmM)1SR,@N;iUS83-_4#\P/>e;"\Vi(-"<:'iL&_^>ko:>+V^f
(4#ffYSduL^B>j$>kJJj@-kh$r!ckgROJ@rFjh)iedH4FEat+Wg4?aUR++4Mcq\-e8Se/!]A:
pp]AYBpd8C,X=p$/Y*Yl^noYQ#kWh"^!IgAISA04t;^W^Tk4bm2Sr2V"?LY3sXP7>H===Ok!_
Slo\oj&!R">/b2BF1";o;H>*Th6A1;RjbYdclNZni<=3WMl-o0,Wd2Zq]A1"sXZQLN!L!M8,-
V35PK^51_76Z;pe!AI<V0!SQ>RKg6Vi&"X-/+3Ee/A"Yue3t'C_sCZPP9t[pe'0`Wgg0JdaF
ZLo`7I)kleXN=HsM@YfHo]ALL5.A7Dg`k%UfO8sd1/-G2JWIlC,hF?j^O&H_Dk#kJt0>36[r9
=uLkLE`EiReOuIX"-?q15<@t@iUa9_Hg:S)tP'q4Yn'&3>h_>L]Aq3qQ_ADUa6n,+1+hBCSH>
CW.J=G$Pe'">7$m8M=l/-$I\L[$[p!T<%!sskWZjVD?pJ+Ho[]AZk^G2RTEEhWo3.q>-MQK.8
OU,*XCu/_J8P*n\7*]AnMF`Yp.fg[+/r$=[-,.0,\*[5q(/0ViX!d)RbVfEqn3.miqIlI0ro0
-d&hOghC^5Q8t&q!)M%c*5KnEZ/L#q==Lg@9d>=$`L2pOKTp$K>e;jQfB@TXDb*d$3J;+k_R
q$iRfd\jD@0VbM91A7]AYtEu>@DW5?M+Ri`CP:WXU]A(ZmJPjH$?AYI#Rmoh18P3C92F4Oa9uj
aK<"8B35"eZoapaP6IDMQ_)QPW"gF<u;%JoEJ-L("0+4UMr@f!?U,*+j!kQfkiH/Uuu-p>Ae
-PqWOfriaSM+JlVEhm>&SX)RJ/?98&eQJm:>)nbWEqQA!'$`oAd1T-HriB+R-0g&BquRb_DB
&:qIJnUN,Q`&Xjic`YYq[r]A`/(i!T\BNBpe/ClFal7B^`-j<\!'IjD^[HK#$F_R^"pSCE/3p
ut+BS`nsgFhnfJImG9j\b$m[//g-NGUFtPj9bdHo.Q"\c8lgPS(g._j_2#s2,-!V_pSsLB0o
H_W6%boPWer\k%Topnk&/P(>^'4'W^9%StO\9E.K0fT8%A0j)j6^*,gF2btfL&R(kKjU`T7C
V"W%+2rCL_@mHnZ`gZO(?C<O+K)Rm;djN)E::%M(J[NEJS3S`IeNV9\)OHRk`8e7Wb6FsS@`
-!S3<o#lPoL8*klluIeTu64`B[*7El*gHIhK.cp.68':lnJ3C`.)^X8[&(WWF;aab/)m9FCB
a2Y^Q]AqR'5qq]ApG\[/5V]AMkW>ZYi+3f9;t_@knJ_X;!RF:Lio46NM+,Clm,$_7mN5Liq/85i
'Ni_d:MW?OJTKL"tb=rH.Bgn)#p7NmcJ\eNQ;3%ac;jBU=`K'M:0WG^7=K*<lH76umN1h;L&
4-W:dfin+iA?=m5%:-'S5D$o?W2s]Ao@RG5gfgU;;A!;7tMY4l>b\;Q:EfWl(DN>5"JVs`PU#
dKZ9N:/]A>ED#\:-@;gfMSJkAbAIH1F(5,\-X%2)i#"CYLDaZnM3'$)7.`le>3gM&ieRB3`PI
u$'UB>#bmciMO"^aSI(Z0b4Q2D),G&m8]AmjWOfc!UO=2N=,.?-it9l%)S2.\3>/5_Tr\8KYo
>Z4S-@gN:/+9I+@AGT"@gu[gR"71%@o1[:lbSu;Kcm,M/<n"84K@`*F#GQ#Tml6p?NMlNHXU
m1O=f4Xn%'0]A#j;Y_Bc:(g/T1WP?!Aj%UcGjrU<XidpC4,5Y2QsKrdHE\5@:'dC1\faIEmNW
G=+.sP$csqQ<NaLgb8<<klcrjF>RcEZR$Dqo1?jbPbg^>!NV)bc+]Al[3eYQ>&8]As[_>X;@H1
/%T2U\#.Q%Yo$q#c&/F$B7II%&rJ/AL8_b=Z?1&fDN66;`9i4Y-nV(D<VNlWjHk7Os$P;?oi
\j)ABj9S[7C&d]ACo;iI_NBI2Ig!(JUd$BV8*dcDf*4C#FNS]A?=#YVWQDaP"1GE9\F*@.V-G<
dEa4.LoN<OPTAVg3li%^IFJlh8<nJ@COPo`mE<OD!AqX>2J2a&:dA>[Yr.2qn#Z>J+hs(Tqq
;^$ETF5c!QR!DjmbfpMZhdlZgn9"Jr5:$Mah'n-OQ((2&7'<%,V]A+:.ttYhL&qbg=aoRoXT]A
BpFpLHNu(DK8n!IX=HlSir1#ShT&rbu=N!6gS"r[1(jM*"D7p?fgb\[A/u]A'>KNI11f!sSr^
fXMkGS%W1P<hLafLl%9KB3o>hN&>KCFGQA^os4r)*Q0o/YPt;!j*j^=#C/pHLD/m_W'SGg4#
lTPpp1(d![4s-Qea079hm'.3dE4pdmt>k5OH-Uik&dZ:t/526t'Xie<ss*$^,0c[sVXRnaKb
eFZjNpLRK<&3<8CY!eVJ81)!or?-P<2)"9S2di4L3s8\I?ul3_1HPIV<$h*?\sF.jU]AY]A"Ii
nBuQ(jh%s0:+=DI:;kW5lD`@s8A;PU7EA4l.0G+H_db=k-3CV?eA>@tsLg2#GeO2qp@ZS16n
)cY,u;BBEdM-5rC8IEBWC?s'[Dr\]Ac1LJL%r'EPh9f]A2L*-UO6RKj>`d`#V)q\1_^245h9Qn
sitdo&sC;r_oiJTJM0MargHc=BM%36]Ad@4-%'rF$i/7`85Cf/:cqtH4;tfcPWkadm3l;Zb7=
F4!dhSsY?1G";>&\7iXe"2oNYH'D=LHe44PM+;,&'a*V)fm%,#QDm<d^u/4hsZM-Mgu0h*=m
K0FB1[To6h["Dq*.mfkiY8P>rgeT;RRLoV=L$gI=h]AYZ<,7CVA+.F<D5A);&3VpbC[gujpaD
]Ab(GX1*="7[=uFm&4U(f]Aqb\W0A$/33/`C=-e=SXI;)T&NG.KK?#nOli,";1r*X+bf_DhHud
h6@J::\m&,4a:8)m_#q+h3j$-.,)j5jBntg:-`OF#*I9O,\tR^kNc6$[G+g%<U7-P^_Ob)oP
#Y9B=UVGmWV4o!Z.SGCcReXEI$,Lc.NEYTQ0DCMQ4eH4,(ms3:uQj*'?6Aa^4jFSm.IgsT0<
4GN9&[Xjg&kFdhq1(S&T#<XZ0WeY,H,'H(*HmhUGS3n)AFMS.$2N#%R0n6^m\D>P1M#p!%%4
!Z?U<%4+[bL[ee_<-Z!tl/hkW'dBG25]A*sK#@D=8,<le(1c/\>pAcqboaR/fWhnp-Q@XTg.>
K`+45AueH(XWE!9os5V;@<cRRt8I5XJoIQD&'-&m.kE:.Dh&7E6]A[U6'A=H9E*#-6:)D%"p2
[WKSA!-sAX80bM;BN*H*TdHgaSMUq!<^M\B-_';0-QcIYCT"['lQrOYa&]A/WZWca@"/2@3KP
2UK1UhBTAc)30ShO^6#AXNAkl&dd*X,\Y'Y9[[C9nWLZ_!LkOg"TEjs([^VTjeENBHrLI-/l
oSs2t/\+Zba<>(#EsH<Y!:TU]Ac5I?gGqJkEL0lJ`H?":W@]A_1l&Ih'$&E?6,"6W5m+-/qjab
cb4St;m08uJ6`&TeiZoM'>c0;2d)V;HA/*&^BUH=8f_A10[1(mNdMK_CsSPsmhCt;QWS5:hM
A&[5Ei"hF$rQ_c%_u6VQe=iMhXLf5$f,Pl-QAe&MVkt@>3Rk)ssfP&XCTBl>5Y]AQ/[GbMZ"i
t*lo1dgHa^iV0+qUK`%eeV(kh1M'.Nq-k/Pu:c\Af2En)ITs2kn-0rE$[h>T8<!5dc/gmHsh
kD7D+lX!0`Qg`f=Dm0N3&=@Z<[p\K;Or#IB&D1r4j?0nWnWRZ5rt<p'2'&7[ZVRl?E<3i&eV
P?L!o:d&sWni'+:X6$]Af=KF/p/X'*S7',8<^CIlIENWIK*`Hqm4X7E%:Ko?ZM<aEip]AH6lPo
_(T/;l0EC#b98@*aBL^FKN,Oi*W2=(=PJ6XCF-6"7`qh/[M\$Hb2cT[9gts"-<T\_nrrPhq:
'L1[f,1S:)_7,O<LiO)Y9c"<=hWYbA)r;iZXcH]A;QH\0f1Z35c+>)bkCqs=XD:=&%o*5WMo3
&#`g2YYscP@1Xs%QbMrR`>>I57fAa)?=e*Q7.?'jg;8.4*mMM3^;5dHRfB66+TRZcPQf"/n:
0c.p([`7@-="M$l48Tr^q4qRbE>V8CZS@sT,QFgfB7BrKaYLCZM_;A1ef+->JmkJRt;H3s.b
@HS?jtW*rTjFJV;=YBHc[r4Goscd5R/FO8']Ad-lKH,GU9KNA_ITH:N=f-!?Bi6V;//^)>5G[
Oe"[J1<L9_jZ&V&WpI3VZ^o7/rtj!"/i1tWZpI;8:hT$FeU)UkBE4l)GGA'^qR.]A2hlu,Va*
G$<=N3ajBnB\Y`TnNMF<HAmY@j,GO4,J<DfIU"@OM4K:&cT]A/.g`*_^JV[Ae:NV-r&-e5Fp)
5AW?bg:R:dOpiV'IjVFJAWD;Pm<.6i-F>]A&R:>2X866EQ?)ZT,Tb^><B&;tL,Ki69aQf8>*A
I4E6(h]Amc[hD8]ABPib%(K>lh/$iG=E%'pFO^mOEUM6-4RJ.tR0<?%+$J\2n.6Dk+`(c>kI#s
9Pih:AT-8+U/qn92b<qPhD/=:aN*5-Ep*Wek#I<oBQ,<U:$>Kq#=NoXk.%tr6/7CsaF[rHut
(NrG(J&AWHg'.<EMAIL>=8<)C^abrTnF5mFo89"Lj3_NDP$2]A?%]AAontoB[J^XD4J
"<E@ON@QgjJVts58I&EV*N'KE^1^.+I0L)dV9_?;`ioe#$;MV!`GV-*a&WG*T=:GGD;nme;h
k2Fu8bM+AK\I!.J%.XJ7q]AIF`^)d6$=VtUN7'-h_t6S-raSr1'TqKDW*p-C.D#5bUQB'Pek%
+XQO*3'q[dq%PT>:cafTua*Eq7W!JB@W?-4sX,gG/te2]A9/tL$k-ueMqb%E,b9W0,HjKrNi9
EaE%hd2D#_%G$D54&!-6mZ6-DGeX+lJee7Ih*]Al*p`+@2pumIK%3K8[=i``.p*lHMF=\r(fR
VDAp:ljFj#ne"'Z)kH7:qooG\Tq1DNJ&7'p,C2$<*^_j5<koi"S2<BjRZjC29qJLS[&M\E(0
HIYcY29AWR3CZe0\]Au9e`55Uk9N7k%9C>l#omg"a)ZL:Cs]A@k`AU)D&Ed]AOk=dN,ee^?92\k
COZ/rWcXhgomq1A/[U400:+3/ZY;XCpV"%(1OlZ!Ur"pSd^00\"=SG"amM&-i'rV$^AsruTQ
5@c[\Yi[3FIrq&=+'_]A'lWn9DMs@crFWWZEn0]AokjZ?ZTE"q4?1V@ZrV4-EL\^8nrogiiG4r
4a:ApLmafkG5HX-!hs6t)fYb-U*mPiFHD84\umXK__p8[9G4gc$n/YANO:,Y:CF+)5=V=h*s
p[7p'Cj+QA53j3GkBDf0[bod1\`*L?q#9IJrh&%Dr-Y'4Rsa=]A^rs>C*R=`:G44PMmJl)=ZM
MZ:m=4jm[m'gLilfmY(%)G>U!Mdg42;<s^H&J[@ZN:bs$'QRkMu+,h\XUrIt+jg8H8Doo1at
gi]AgeoNW!J$n+=pHO8EP(HbJ[VET')q?pf8^H:md'W"FLmm:T@BU\)!2p-#l2H/F*6p@l,s^
A@hYa6A*2n+jQ^AY%V7]A4Yb@XYIStou;(^"!/K,[l(/Pq4'[aWliq"5A9r]AI=H`0HntNs[FG
)7s&qYP:i=Ugqf1R-e-U%g1RSA>rTVV-"KI(olo,l<D9LB7P)n:^[l;FgIhR$QrLQ7>Qcbpq
qfZh^T2a\Ha6-X`Qg`%G^A%SuQZd\iAV?]AE(0l?2T8j"pkfil7"`PLQ76)c'WVZKUK3FI7V=
X_7\^]AO5p%OTsq6Sr6YE5A_X)iSki4;rgQe1"IG>La<;BXMJDCi+#HJ7PqE!"8h;m0Q\[F'U
c,Rl&Ba\S.)0G,nDZ5VN:(,W0TA$70Y/hMPZY%3cSCg^IYKN-$sAY`/?TQIO7O%sUk(-JfMJ
ijFlT04iWDUrlg")]ARn@,=,?o@.ji0Y3XLrXE3Y[]A7`3`<+]ArJ,MZb8dN7oL%cI2s5el$R8B
0OQ!2k0PqZA9!OB>Gh"T/)@@fW7!?Sbe+N]A;RBn$qLQ=(LGb[.oZm96^]A5)-$m)o6N<k4A=/
qa&9D2Z+9o)@I9KMU=3ikT3Aq5`S#S;[6lD!VG<3j?ScJ*Z"B5A7Iqu.`Rs-0O6XB;08I5#i
4_9T'-Zi;.kP.0dC:L<.:4PJdCD,O/skW&?tcrj*bkg=84MD.=,CDM=-qt.d2F;%t&imYF4U
j9)elZ$4(<#R2K/FR#t(RB*;;1LJ%EO:Jt'PgO:i1U_0gES6uZB&<_6V8+i:S39So6`8V_2d
45C3^+'h-"^5n&i`e5&PhH3EF1/c51pMUdnkFf072Qpg7TlLf;:[.2PVpT%AW<ub=ei&qegP
<u29HhKNO"9Vl[f#(Ru3l4S;%4If@M?!D#fP%R8&8^l@mMc1F+A9Vm%"UGX1H:7[8r_)c\er
CfK)dZ;G2.b!qYU.N;AGWbbYfW3T93UWQ&kN@GlI2Z/eBI=!aN]AQ)uKs6KOj"='R3(o0;<>A
P,d\3W&,0Pm*^h'pC+ULH9aCfK)dZ;G2.b!qYU.N;AGWbbYfW3T93UWQ&kN@GlI2QCRmfr24
8V_"LPbQGd^Gk"![IKmi]Ad:(R&AY[C;Q&R74<Aqp_q9GCPkF?tsQcgMCVlQH53i@WX!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="732"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="48" width="375" height="732"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="1d53af8b-f2f9-402f-bb13-bfa82d2c81e2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="数据一览"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9".'P%hO?B"mQ"S8=Y6EQZbS1m:Xo>%rj!TG]A7q.\4RQ/HHg8'.<EMAIL><lb2\_OWMl4ZAh
'a8.#MQ,nc3>Q,cIN9Ukj7$:f,duH[gh4Hu&-BmJU\d[9tS8>jq@"c[4RDF!%RJ^%mkIqkfA
-2Z`u09h16ar##K:B;te.FhRJU\bfqK!8oVVo\fJAie]AJ/kd$k(_j]AckLK^lGWVlV4H@s+Bh
6:eOrbh>LbB`m4LJ8"WrmDlrs2hO\>^7j5jaM">&LlbP@t&F,^3]A$&oqU->F]A7JLD2E=c1qJ
U\Pid"FFI6!^L'unoc-(9M&Tq`mL1X\Jb:;8T,O@i&GVC8+9;1_%RR,nK2mQ-3gNK3R%f?k<
fRp`&g%K]Aa1MH,j!8uPS2]Ag]AU;lAL.aOK%6"Un&[qm^R"a85j%2f.iCp*#KUs6_&eG0R%"_;
;WHq`;/f^'=W@gIAsU4RdU#47Os(^jS\qdD48FH03MH!!!&j:NV?0-FOlt!%n_(Xc#sQ#<G>
]Af.JfN?V:r,-NCe_':4:M"4*pn<U0)`!9d_]AGqFL=cF3pekp(s/CK<0QQ'i1SmbGdg]Am7OB@
m?T(*hLKr)H8P@R.g9`hs^&:8LZ0dRr7$8or671c`9.JK5?#o<'-LFP*Qn2BBuCV!toaI-m-
pHlKeg[NMXQg]AppW&cHhpP-JkoSW)^A4[LEso94KCRF$W=t2TKYjW4G_YK"6I=5+Qs4IgbA/
j`^'YCn4d\/)7VNgunsS6'.qn&c$G"WN.PUBVo0l-H*/Qf^.=5.`kjKN.t8s-Z^<oV2^k]Af2
jjlLri=1Aa49<6S\+?kC%BA+L7S2T"em,<6/I%p-,":/gZtt4MPK)]AY]AbM*!@)S/lZu[Pc0C
]A+Wg;9EoK@KF^/G*HB[V&lp>=#L%2'fqge:H<M\l;]A8hC(V;(-_T%)gpK'C6!'JZTD_;.3sL
sg1$WMYMS\[:NSCli*S\=C\NpPtk[mE"p=/m5#5*$/nGqF_#1[c[otB@2kZVK.2lUHjkcL^K
Q3gG82"j:B[pLQmXBB5>)K?=3OOB:ZkOeMKdFBTYE1k9%V,8R.[*A;6q5Q#BGgd&q0hf4ID>
lAam'q3(78?!B0nM4`K5T""'%eYX-jrLpJ=<p.^+\q/=CWD/P^6KRIN2TNg0#"'>ZLaW9KAY
<$;>,/1Sm8bKdj^=7A&0kXS+IT!oOJ2IVPM(;h[*_k'E.i4_#_<>WT$JOp..Vm+Rat:R"Oi]A
!@R$l3]AX9k6b^<tHH;]AtKrW-J1rG2SG7m5lV>\62<Xl`OND=u8_D<Jps6-t1>TC2S?etpd##
JNV+lt*:PK@_R+85U"q(nk>j`)s2M.F*hZLFqZXWZdb/@rN`6O.n+^;3i=TKb'7&<!)%"9N@
ER\]A*i<GKK&a#-Tk*=Wg\qUh.GDHlcXJIog.m;)I'?JfG[P]A<uH'Ag@6sJuIg+Q82Qh%ZFKo
qFWUdA<"o&EPnSCg2NRX^d:1SaL,,q]An*"n<%o:A\>neFq$TB;LU2.!O)k)=kIkeV27.!t*-
F/prp2U$1t[6lj"O\!T'l;8l%a>tNUq!654L1\2Bf]Ag8F@05$pu=fM8Ds*FU0`IDXhmN6dRm
6*ad,-*hrXF>D*Z)=.HB_UD,498\Si2llkFp0m%l]AJ.cUB,PbE"fB'Z$gE1fUD&#0l?Ef#7G
M[gf:+ee8,cWkLoqMAXXiqZc?H<`b:qof,'4r`Kq96R;$`ge6Ad^u4Qd@&lRi9$u8tZdC]AKk
b:$#^pL5blB-#3U1\5"qgIipX>.YY\)QD@$X9(;lfC?\79'd@o;kbl08LI80t2GK#/jJT]A@V
(MSM,Xtq1[`O[B1[e/A-+Ua<'TNi>81,>A$pD8JV?[,u$q,`kH?Ds5[6c=mO:<RQ2IH'joG`
><n3)ESPXdh9Rm+B#?_W6=m"bk3CiF9O\,>9LhA(6$Z.Himo6E6%93B.JmmNCOk`KEOtK/[;
[*Nn;5FMt\gKE4B#>IJhX$_:i,?Fb5!=%_2A^_5&1+`'2-]Ad3UYZKaja_b\u$KY_@^3M4o*P
L/_Qd]A8t-Dmt7XetKM`2r6pTGQ^50;hBIfP.<"#BSPb\P%lT`[jm,Z`qsVt:6)(>i7FP^HbQ
c(c)28)SFGe`!G(H29e[4SbjX`j;\GN-?p1i)^nGs^5p3I&8nP)gP*%gS+DR:fH_Ss!/m/%>
>HiV4l!Vq`W;H/;Vs^$HKm6r&aSp4r8/ZVg'sjJKN2;8^gG[OD]A!VDcQc>+T7,5gr%2*D;_L
C(rgdqDMI^ohMU@ogbo#=Q"1u:5e^\J>gHULBd1<]A&#R6@sbbe`?;^TW_(QXuA2I[JUTLMte
6>j/F9ZVc0.9Ho&/]A3CbjrV4]A!G>VQh#dCrs,OHod%(]AHu<nG%tMq;PW*s.kFl"RoK4?`ata
c37h)b#l+Edlu,5qgWDZ4[anQ![M0Q"kYLKTnFl#)/ZkCR##`%``mp<h.?4%o[;]A8IZ&uEYP
g&oXat+?!s4,.2Ki)^b0eX'62BXkY/(m=5U8=[p"pQ10Z8qhmCT4`dV%r71W>%*bO^_]ABrCq
R6pU92E1!nQaJuZpHmp2,g-NQT3U$=/KRMQ<SjXk$&LjRV(&TRV$S,0+:NpMp>-!T&J_t.[+
PJJcuo'Y05kS.OTMbqc/BTYE)C]ApBY-6T!W$&[>CPN@hH)+XAT(5d7%MMt_gOEt[A-CAhK"j
dEMrXEf,JILmH44Th:VZSV\</[dr(C>(hs>&f4+q6[Q3>)+)R'"o'W/a1)CgrY?;Ib:6KFBo
>5n,q"Bed3#@=3"SY)B,`0?OB?`s4HBa%Z5/iMfF>:NEY@'Hf79=Jea5G\L&Kg;VaaBfU+nT
79`&ru<'O$`EIDkFichU>LZ))>uKY]A5gr3bS<rbhAIG-e\[UT(r<Tt(oXmHh9\T6WHO$EQ)p
51JNgL-DU0h'=_S[(L"_9VreZA)u\-4$ngEM:?F'7&"kb?pn./#)gM:VU6u"gn\B_;hN%sFB
oT^)W]ADp`:"dY!:,C""T,NuEIRQmdn_@GNfUV&?dPEg(%/%`Y1#_(^@,p9(WV2AZkoQ#597I
9$2LQ?WRWnIP*P>MHVIDW4bMC?nO4\?V-R9VU>*oq!gf=4k+@H@$?+>Zmhc6bA]A[2%&ZNu>h
XB9R_'I?)Gq^"n\LN`bo.dS(f3]Ad>Dhb>]Ae`Y-8bna]ASo'LUUelG^p/CE@Y%s0AG-A(V9/dk
]AOBGU4F-d63$#mZQ7'DL99`nUV0\3mq8_p-18+D_S\jVle$4oc0AF>H@VUhtRQ/*gb#-$]Apk
KH9Gj(=QhhZe#\h1V3*pIhp-k;+;2;e?6.3ilor\:J"5"5HIsna`7rW%:U'iB2S8SBVoNH1+
l96\J1YXm[eUE>QtGSWqU5]AEXZ$@lbpcLD!]ADHoug+<HfZtV'Cc5h2N6T@#hXF0JNQK4Ak97
B=F*@/i*?3cg:3Grj2%m[GskoZIaf)Q>iQ&(U9eO)=]AnKH34.9$4k,dXD)EOa&UQ9CS_2a.V
./!AJp_<A?V%NQ:s2)cDRWe#BH3PK3$/#rPr$rJF*U$*745$C'=''^kRH6M]AT*S\kJVZDol`
j3`k*m#k`&IZA:]A_WN5Nf>Q4=28R;$m(%g/^^'!ifXo+]A(UP.leEm-?to/UuP:qVq0gFLXp:
f?eV$$>Whu=PrVo%VTG?s6BC\F7(@To\SKdCuBS]AT*UU-?ln%>cn.VRTeE1mlf>*!XMO)Ml$
J&ns/;(ubfJ>PJ!]ATZ6'c:YH5cX"a:CA>g02%qfQH1;1N/7J#ef!@EAit_RP6\cT"Df_Z+<[
W584CS'Y9bYFLpa)1@LlLl?lZn]A*0PG3?IEj/tCeMFjCt?Se-6J<LeL':J#mP=N@]A9N$L-Uj
HJ$u%<lB*Y0S+M$]Auoe(>F%hW[ku&lXnlSf<K%X,6&_a`.:Wec]AIHMZ30c2.eEH1+af))$(a
#"Vp<r2^kNnZ#,W;9R>7h1$3L%nTOGI!,!.D)6n*.erJNT^oP=i=7p@q#l:*.,ElL89Hc?iO
3'eVO9D=%SfkfTie5%l:kjQTo'6m7K<Gq&2j'B[B=Q+\RM)+%hDAI)Cie2#,T:)')Q't9E9I
&Yl)Sa9;V;a1N&EqUK@><mI%H#LZ<H@#TkTPCRW?uA[aMX9$Cdg+DJe@;*Ri<`SN^J3Ck`nH
bmImW'qFp#'bKf/0JgRTS6h62e*FR&O>\9Rmn]A6f+gl.m![<HP$Nisqp>IJi=?o7J9Ng:(56
dC_t?$ncCQohj%hX*/*6m/N34e7\K<#RDr<)jhSRdP2;[j]A3_p<R[hjK:+U@qD2^rQpS5UAE
2h4P.-8*EK>@hn-)taPEa'$^B3)BmY#F^AXX=YA)dR+"Ie-?^*!_jS(=>,3i!CP;K%uMIB5D
l:_J==Ucs[6PIdGa8om]AbFXFt]A\]AodFTQ+V1-SGe&3%Laf3nKB>mN%idFMe2(O-T[:XJS^$8
X;6pOXE9RmAR=.^dp.RDJSKi,mstZ>D@9.A(B@B)[fbCKeBiN?ct_gEG"dHa`F+'a`J<(F-n
S^5<b\"Cc?Z^P/MdP+!")<;D8_fruAi_TK.#]A!io2H!se+L1SS!XR7S]ALO%rhH>aY4e\/B08
#XhZEINl#/?(j<Ec.P\[:gcnh`/M41+s+0(@=b@h5!X<OlroG9O;K-0T#BWMG&.cJAhr42;A
\08`]A:aCAF^r>VC7fKp0)oSODHmgJGZM\#qJHR:'o&)\bNk.M,9UTSNEo.rY[UQ&S8h(_b"s
&=XFRodW<M!WD-mdOQBt*mWf;OtTg7@WI^b?7Oo]A\aW6%8^hIs6>U6@7B\KNZ<7sU!1SN?f<
6At>]AVrI=L/]A(%oR4M9e,1VlmmTnH[UXZB\Q'b*^:O[B3\IcRm1FD@0lt'EAf.6#ZA>C$\A"
1cDh3Hr,.VGQ=\<tUn-J[U.kFnEf.(\c(Gt\e)`X!'Z3EI'sG<RH^rlT0B)$(A`O0qj#`ji?
+RrRds9M']A*IO?>r?g'>.L(F;&53Yo2=_RCM$_`^K)0qc&f)(96PShk0d`/;;iBM6AtVG9_.
GU\19;i*MWLj[LF&1iZnr)PGS&l0&P3J1EY9K33YY=q5q38_m1ELkqJLY4jm]AHV0Mjs2_b:.
2p#OC$K]A[GnH%/WJb4Jk`[6l]AOk]A-i/JVr(>:$eP",C@drQ`Z%cf^E+V%G*L<]AO@Gj)??QLu
%Ok(,6XE/9,-_>>t/i)K1JEElB$u9:-&XV.%9>)*Fb_fu>:e!AaIH1?]AFWm*a4T6,7)(hYs5
JEu!19?aC_R+R6YpamNG,q,mU>;\R>M27l(/Mf6GpCZ]Ad[43Z=tg\Sci&Hr'D3(<0nFQu:4a
NPDIVhu:_"9=!AXW:"u8VnPJ/&a/Sbrf6^>'KdC'snMh"'<?dg)eQ(l;4P<[Si<a(aYhn&CM
VB8L]AsLpU,gZ3bGZ=!Ca@EPBNslAMlYsa&#WY(ba)R_mYRtp/u%d%P/+_3LTutHj^4h*D.l`
OiGk'T,q@?UIqoAq5nk[G4n3[gui4%@>Y1s8pgJ'Ve-]A/Zn]AJqC<hs,;I:O:8HN0\OXq9Xi<
d%6_[*4`Ip$-0.lq+YGEH4?ia;7'm,&6C:9<#]AR4l^O+,YOd4[u:b1>[&Gm/2]AO';np'U<!R
GOadrHBs@'Gc614\3R5]A#4DBPt!'P/<L4*DlK""4Tl`r]AhmJ,H0%;M!@HT\f5`t9f3Tbra!C
@FTZK?buP\+A2[NG0.8gj')OII:,M_>KV/p0aBRMq7O0*Y'R,Wgisg!]AJ;H3/WG5T"EJSrIr
HaFmL917h^KM;T7R%(u'/^2m[FeBb%)g-r'\(e."j^Ml44n3Tla,J>J1]AE"b-3Yr=[XpEo^1
9Wd=PlMGuI$p")s$Q8)6$F7]AsM9n$j6MbUJ<Z;H-ZR7"EY>R+XZ/EjVQ,Ml9buY-OUT4f]A82
78Po5m)^br$DR58a<S6iJRX]A!THd3GnW6aN_jfK=^n=C@-jpBjJ*OQn=/%M@Le"))gsQ>iJu
jLYGij]A@P%f,)37b\l#P8otuA_II'KNg9A19m==&8@Gh2_[+6QrkVmsD#6"N`W0OJ;=UaDLH
04WrT8<VRB&p?$h?%/JC-6g<b@9`lOD5X5IIm?R@*),M.Dq7;T01XrjWg_"=f<04pmP9gTAa
s#"HW;;=_5u#/]A%,2e(FRHV.N_=.UL?e(-.s5QBa#4E5_pR;[(2cRZirEB(pnFoNF/55^XHK
dI*;0Ytqdo.j3m*:7<XEYr)1Q[::)^gOnh'/1p#]A^(Qf4(h-#H]ASsa$55$2uIeq,D@WL`KJt
:o+4J9&<WW:jsf?+h_\W[iS0!MG&&jGu$c6$tAToZmF/nQ>RL$oDD7!W?]A'io_Ne5D$7I2;b
4d[J`3?C4PVA+K81@iGN0eg&CTR*)jU#8'ssNHr/-\VXH`+;ZR)J9NA-[5u\a(.@p2iY3XNQ
n,Jck)e70^:cBB@_)Ut2qO7c.iUV$5BBQ7GG%F":_LtF/^::,m>fcUThtF44uSQ4*3$.'`7*
YcQB%s$J(d`-.5t_.QRHE6"P&0hPtC@gk[L0GCYORPU*$#KHiF,9YcOD\L7m!>lgbms%Fhp$
']A]ApTH;pKAVVeh($m]AOWf/_'XR(UBfKO*J[&2\S$o!-L\2OGd1`e5gjFXQn.(A_J5Ocm1U0)
eegJ#sH64'gEbq"kL;QC9tCp2#pos+OKR[]AM]AWrYmA4R9O:6WXhu[/2)ClS35?80Yt$?YjS`
MJGNgA/F0VeXp]A/db;nj@`5/IqhKJLB3AKaRMa3RKa48JIe8[V3Oi.DA,C50fD,h!1mb2P_P
;Ri;olW*#]A8D@j*ED6Mh&sYW*QDW;?IZhg0P<7+iX\<\iA`8)N,Z;YUR$r.VASE43-d]A#7[Y
T%>8DcMM8L$fhu%q#2i'AjHF>]A//gNfj#fa0@,f0\m.-=M7CTf&]A-Jn70*>-/fML_SI_&>7n
$Z$X?pFFnYK9,MN\pab@*[(W-^P]As*F?ot5qPu4M!5OSTohZ1l:_34q&+ra0ohZ1l:_34q&+
ra0s)17^hBhTGmckU,22Dh$^Cmrm2;K&j!8t2AJ)'Jh6MDo5&Fee25,BY"Un6)"H;[Q(8Nj%
#oq\2/PBp,&l:F.<-hZR#"*.%2FZ)>Nf0lGF*BiJ'ORtoUjo5;~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="23"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" cs="2" s="2">
<O>
<![CDATA[数据明细表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="112">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&;WeGK:@Fh9*!jJm$iW)SX"`.U`c.bo'f&gKuRN4#o!1D;7IKI#6;,"n4FbLhPGA<4Q7Qn
_a`@#Qfr#%Pm/kdl.kT6#J)?b^ZrBAVgqEF*REfCjU*?I.g+omFJ\r+W7IH`+eW5!H^:L'C^
snaUJBk5KPnYJ-tk<0QG(F3$;QEp577`9AFrie-rU'3)ftmjCJHKtd3@gU>p$=lJJ,f2l]A0-
-Kir"7aPr%!Mh<IJS'SDZ[?o3s*/=f_TW7Mtjs4]A,_M#q#^d5Sm8q<=7;0_BJ)(Z8HYF:7%(
lHOE6]A*A`L'>S(gd*+_DO1V=N)V61M9O/BZ`Yo6iWG1k<9)O8Dat)p3^n'c8)PWT#Tf^:&k&
b0Cq#g:nd,l26Xn@]AHr>.^sf1r9s'3*=LG&cKV3I-c1(41f@dL]ALLl%_nWh8C5g9kg8[$iZg
ENUU]Ae/sC3@KEeR:J10E1IC[I2n+)XO?9_XG7<ElEl>Ic;gU;lJh&K%jtPs8HB/KX1cEWKk4
]AHHnYK^U-VD^%S2rlMA;83GU;)[_/2k:5<J!j"l0T?SW8S^AIJHOqLnP&UfF%n/3WtI"maA1
\Z>LG<GNL:f/]A4)J!7>5>U1ib"(<X]Ak+>%O3Wk!a4U)W``@OPIaRgK*&QHq7ortL<CgbPF6#
t/=dOu<J;rNVr:j0NDode(U5d5YNNNbWD^jON2n/:Si[XL$ZT`Znln)#5O)9\b;`CaJ.cSF%
=S\UZrs1dMnB1)M5.\l`@AN%Mb%Re49em@#XK'LQXKP>Jpb&%BV;Se;j!mum-8^`hm9"E<[<
$bXDqcc>NFSr?&KOknonX!7Adnf72]A*T>#SM=JVcG\W0=[-X(E<Ym)s]Ad6&?SXYZ!6E*leU8
6/6mN.bgoqb1(Xo_6%5ER-7::QMN:6>=IFN.o\<unRGl,V]AA)BP4bK[s\ls)`AhWE!$3tMk7
LMn3=@9&iXr8M3Ej<]AaQ1-%2/0cl9%<_\?WHTZ!-gR6F'R'3`8gEO24@:DMhKW<K59Jq[g!d
5o;J$@`;W[unZ1<%]A.?#POEe,OKTUuFL$""ql5k-r+*<LG*<*,5M;RCNcL2=^6Ke1-s$\M\n
JjF!B>8cgJ<P;I;$f<;IW44'51CVp#%st,sNT5fhD1/g``PQuhOa"6HdGo]Afk+eKbgsjQ'C.
=h4lRu3Z%%ur?Bo(,Umu?V;$OKIto.5NC'4[lI7V_X_/s2AQ"`O[a>?mT=E6%OUkK5:<Z+E!
o'8uprJ;mQdrt'-OX/fLsZITY:aoN`<Io%8M,3AFXeO[n_XH'jIZ<fa11K_<ZadeW2'C6o:4
hK9'dJO.5nDCR2Zre)1185aURk,c'C2[;2I?)=e`s7ja>XU2)@U81WkF]A1':U0217I9ae;c4
f<<2;q2kE3u@R`nO3JJWchFgg/M\gCF<?-AnTniI"bm64sC+4Ea)l9J;*#2qpt9N8=NFCWM`
k^"'2)]As"Uqe<YVlt:amqaZW5NA]A^u*'J5,i7-aTC"<a%(P?#NE5V._i(?!4CD-Q,EAlP(>^
aA+kRY_cA;oF@BloqnX:!1O1BQ567K"4V*P_*hU@Dhnl[,*3+9Z[KMgG*p`\B/!"r.kJkfc;
Aiep8Q[sd$I1UtdeaZ?Dn$d5UD2A7U7d"sb;!#FFnm)6E)d\6.$@i(Y2d?N@Fhb0YM:WF29(
*c=):jZL:<n;%##OM%^Bc0&cInQVV;\s#1d_8@Ba!1]AEd<*U%5VuFh71+J,Gb,6i\&C4CB=E
lTrCWPk$_k)8%.ih:CccYOm-efqZHj%mP6^PSrik-uQ-A<-amTD4Nha^,]AgY0=4W@V?Je1W@
r'A-9F&b#&4=Qg06Ya/9/GLJ16Y[_e:r'HlN#SHik\Iu,/D+dq;!5us5Yk$BXla$U[9"8J??
UPHGbL:SJg31\M:PDq!__;4;7Vqb?+*h@<@P5@SdlN04M9F:XC9lA'kbk:;Os_(1K1mIWGR+
Z()+$cq_n'Wks\EfO*[XIf%!?=c!)4a5hB<C#Fq%Eh=hA,p$]Acd4snk$4Jf"&4Ph<`._,4[N
Kt_H'OK\j2=I#r[TB-<d#(nCT)F1.IYTIAhhSi$><VMKPikp:NAoMA)1BagH_f@JV*EkCTcY
AS$ct04HV8#c&3\629H)3/8Tds1E0KWi^8d.pc;Xia]A@7adVf(1l>6'J,i-9Xd!LqgRbH6UD
QQaenQNIq$GTC!_L0nOu[a,cTQ'*JGZQKJ>Cb0U-lqW>1cMd!2V-`\\gSZoB=fS;.XF*]AME>
TG`Z5\jd:4_et*Ls$"fO6$_+`duR,;ScYYbI\n\o`Ru.Eq`qN(S'-L&jV2Sd9%c(@P%g:Ke&
)RQK1qh8\/6&HXH(D(km_\.Y$3hH7\8#?>@U*Gcci4CDksmb`L7.u"Ql5kQaGNEEH4\3-lR@
ZiARE1t%+W<^O=]AC1#'_t6EuMh.3;mr^O&*+^OO]A:=ak:6BD[X<gLH_NjIt9MB,Pm/`Zf5Nh
QX]AQ\!*%#)E%*,K=5p"2.ES=aOgTAM8ret26.*FK!gp\6X;+hP=VE?[H^X:R<I_Um+$"j3]A-
VnMPF"lD=e6?=PD*+@-04)=a=>G08c7?65o<]A?bY\&8A1/Q(#uO*T]AK9A]AiU;hkIr@_%!Lm`
uVj&IUSK2]A'b3Xt"rq+''P"M7M>Na#3;ZW^0[[C6/+4#^cJ2&`#LS>P)]AI6HlK*^l7tZ)o&L
bS"9'QIk&<p_u<3*_78>tddOW[Hp9;K?^b"4Ppg:7d-:<DjMP]AqF1k^Ygd<[C*D^nOP,>aab
%4mD;U7=!K"l*h?/h<LS<E?(dguC</GlX81fDY(?Ckme.IqHSg)"SSMY!G8[`cp1$,=1H4Nu
0"h@&+C8]A[6^8)9<b-f%^Fd%ncmANT\hY4dom4'0%GX-7Y]A5KNZ-p[URDA2JKs5#Yj#F(5*P
gj0cM./J$PCRi_0FoRkC@$Kdrdj-IMY:+Ur>.PlYkq;Q05c-R_pGl2"?p*Wrd;P.ujN,IkKD
O?iJF@$bSK;A0G&^D44YI9VPq/"9ULCL7iog(5U03;*%T(DM!\?Y$^[8Md]A!UsNacH[WLo2d
]A_oC*]AF/!?7Hf[ptT#P>Z4eKNS==G!Q<m%Br)=]A<L)PX%]A-"d7l6\ZN3V['7c!>0P)B<J)kE
[8=0.P#Q<Kn.u.ZP`NO;?*<h!'=,Y%+8i5$=r9E1<??1#;oqmXOB8!f`,iVAFn$B*]AMJ-.]A.
RK3D7sCH["8E,\oG\b_hd)g5FE"5pkf4GT;7?%/<6,g8/>tpE1p9d(9k_PWO':W0O@4&!B!>
KaT5C_]AgU#$rE;F([g?7iTPoWVe@qRGu9,ZJ*"\DFSp?gR[sBnK8;`Q]As)B5bOFQb_]AtZ;Qu
2X^N0\jlYG.1$.fN!$Eu+a5l:02b7^aIdr'E/2a4dH8/`C$ZA6Nt9AkVMGN@A\`$U;.9V%m`
[Vn24HT7bhlBGHo<qDbX$WVRqk3JlEZ$$4-.(lAh^fY)EU^+kBL$7?r<=(Wt$/@M!h<6gJ)m
3nD!DMWPWdO1X+*&>iuo!p[0/DN)q"G31g_'?kDcc@o7EIc3oMHV)YMlLp[@p/&QIQ&>DfUV
QHOWje[QY>e,T_!t9#T@r1TRQO/`^kp[P/QT@YOf0BI[!?e;Dk@=M%qqeSfGtbi6VpmSeRXo
XOg8]A]AUcg\R^N<HluL]AN398#P7=,n$R`,u@/q1H</uhlN#T:9i2A?-c>JtkEm6o!83==i8Ld
Wrhq"[9cV!Do`KQ2K0egn6o@`2I/&n5Mio@).t^kOBKPbTsnJHSak6ShtgL@-/6.fPoKS?C-
@,V%JHaTa>q\4KEPcZIie/]A)oH;C5!)+Ml4$s1ma!i_R^';m-g-;:W*hR8jJtrUj#+YEArVd
60)1BCGkj>'6%56Sa*A=b!N_W"j*JH/Z3H'ZErM6@N6^+GTQ%qZ>mLP32#h<j"/6:!R<qYDE
g".E6.DbW3E2lG#99GiPTOF8YBYf'@:;C<g=SQ4gG]AhXHr<;>Pq(!DH'PJ<E84a3%id>sEIu
@Q?:M1_VG4]A>(@:CDZiIffKQFIiF1jc$NehE\j[Ve@0b>HH&]AiUh5_L>l;ORgi\)3DSfA@1%
C#q+h-d/Ob^9[)qN85_:j,"K4J7^>0tpO/(S768:)(Z)5+S189DL`WSN&mW<Z1OW#?T;WPCK
MZ)W)qaO!kj(Y0Q/E&0)45(R!\E:"IXT.#lliD-ljZ`rt<:U4ci[k[7a'c?<?&bf5Y;B+?h.
4.X>H"AL7rq=XiqE&-)75o1W?tl'noj#&3SIhmldKO@?2mSjg1,3`V_ed)Lh;-s%34WW8l"Z
*SR8W2fUp:Z*e02#a0:jSoRO$<.O]A8HVF`"[>DY^\7qHFs>FRt?(k+:El92%lOUQig+,880K
]A=op;fd:#-lh#5hO>6$NrCD?f0spU7mSosbrP:FP]A"K,)[ZVX7o-BEEn7MK?AXn_`4ZE)Tf@
dFIHeuK2V(.?4!.:7q&i^EdN0]ABkOqLk/P%uoFJ)>b4\po1*2T.VB@bP=IQMZVp%N^&O>rhW
$-@;I,H%n]AcN/3p&c"XIR$gG:qOq.';VW3D0lV.H+4((Fa;'a[52IDN_,0B9PP0*2^,8/%&>
D>Q5PhWQ:LV@1$+kGT(:KB%(?ptmp.$oRO7"RcrLNnbkObm$,$J4]AjKLT##QDuOD8ghpoT2r
IiSikB46G=Z'Kte]A1bujX6j5e'[:!b5'g7i1r9TW;:KfX,NM`j*fHgEn$'ipYQ\m3qs478Jr
F6P1*Li>Vb<m;pW67$9D]A",.OTsu(2;.:%T5h]Ag_g!>AKjg<Tu;p/MB_T.u-fW[HY_333<Xr
o%jCBSF$A&t[A"u9Z2BcT'Oo##:I1pBjmA#6$#L!2\J9I2pX94dUG")5Zm4a(dV?RW14't_%
^-Bh:2W7l@6,0oMK<e9!!,d6Fsp<USULZMc6f))A-9.Kof(T'q'6&HC6_Y#i-l1Z$7Y(IDN(
Zsdu2hsWh!E.H`7\'8gMj]AqO8_;jqnAE#TI!PR&WN\k8_7O)MDa-lCemnMMH>Ri5JhaLh#/k
h.Q<de^mn=5QT9;_V#?5:I%lM]A<_o=n+%l,NKh9DsZOtJ!Y%%-S`)5a/-Uss%/7Pmq:HP#Gp
^<kandT)V*(]A0ljm%WpcG5m/#D9DYIHP<;9m]AdXM:pFiC(26)tJ$D(nKkAFlIX^q[lIEskC'
:VKMaj\I]A&Ne`6.+N%@&o(3$QkOa`B(Z]AhmEH%5?7jNpSUjhI1:N&FH9>d'(S4'LWfkEps7J
R(p4_JCI-)>G;F\c:FT)A/aWU4,q&#PH<9YN#ASQbLW)bB97h&NDO_DF1tYt.;!248j_bUW/
3eabhK:jn@TSf)8'$E0@96Tfm&]AfS["p*FI.-/6QV]AQ1(5k\m5&Z?fV@?fX6AO-"@Cm?r9F`
+b,)?)9APrg/\>Kd'";-!@BW*rr,a#/ka-"uk$>hVLp7Z+1-'X3"+'atGX4^6M!+#LeDVBtu
%.89*6=oolVoRo=^m`MLeM_tLbY4&moRR5:nVNV8I_sTcX$/F]A.!B,J:a7b(g3IOBWugiJTr
de^9URs:;Zh2L@U[DI"0LnTZ;44%U;9rYN^QgG3PjPf"&+8BL*[9tA3;4V0W"BMi:)b,+#g)
Obb?\]A3&fs=\N91uDQqj/-^OS?I&7D&&,RgG=?oBfa55%t2r,sT)\ke.k*m>M%UG/8&sfKcj
qCN(/E]AYVALb$(-K\U-SA(J+C39;EXj<HTLb#:^QR(<-\^cfm*E*F5@X!S>5JC6Q#7PLtf36
urJ7U(8pA8fBm=6n\cO#uenQQ:Q"<*CX6upRjf'@OgaHSCp5U&Uo9/'/\42sR0R08We90ZhQ
ae+OGiBnP:S8%NmnJ#qlSd'>cq/j><(d4pt<E"7CDD%W\&dFeP=8C.aC=LPrE$+^.A<;-S^#
Q!/(<[3,od\-4UWO]A[8,)k=q40M%V\c8#Eb1D?4=6t%#-\nTJ_.m,Ml-?\=%9)M*N^X4;`&5
rLC@p]Aqn3M^:$pGEfPK=q95Bae^Y:#>X-kX0@&R<fZG5t+^7D=d>1[2TR!kU)*67c8g*Z(of
F]A#H%6d^>_V'.rH;%c(<)=3Da8<[8(qp\$rIiNR&,ZfY<N",S[ga%N'a>^0DNcV'99.Mhl73
6H$om=O8ba"c*RG[$dsmG#%El\8kSG$]A]A64nIc>M+'*:[hFFlbJ1getVZ4[\5eIOO2YIr!&q
q'eR!`GC7Q(b!WbBsO_Ho"L+d?$+PX4\iOQkhZJ9#eT&?&%63bG;J4HRi!bT?E!C1:qL60g/
6HPOL1g!5>phEW;l3m!sm?cWR0p\[-SIL`o+@eA%o>%;G`B%QpuMW%>LJ)U=BU)OBHeuLhG@
uJg2/f.4<qeo)6*BOTu^Kk%fCN!#)@r:V4cC!k8,Lqg42kT86gRVjqmg^3:Y#*K/MS8i*5EF
g:9U-H<\qoJnim_`1;rJXJ.HLZeiQ.c=%5FucrN7EpA$BOe?l92W+"7?blSJqD(VrW_cjoid
Zb-[_N6Y/&1"RTQsiLWmr:1p4LV[]A/oe*&:GsH@9MOP)o[pXH?=Pj>uS$+E]A7i]AF8+Ucli]AQ
fPk@bI(Zg'U8(HK_KYIU##GZsP0=<Wdu1YelG6s;/Ppt.QFp&rc1`"3Oh0_JBu=[j1EPP<+N
in@&G-a[BjA8lJFYM$ZrbD<&KbQoA-",=&L!J)g"F0&HnClJ1jU]A$JfhU1h\sK]Af,-pFjh!L
2(m"\61LSPaCVADg"ER4/M5GLWCo99L5H!q5Rqj8kFIXlV$ZbF043).RCe!d4VSemc7tD)4;
D8+?ls!biG'rkP!,c&^!m:B-JHkOUJU_P^YG=^79/;WU"ONj?*@n^1*3-*IRnBfW5C$pC[4$
n%fKWM`]AQcI#]A'HYM4%(.6U3`Hpcj.:_m-rjpT'/tPP:XjUk\tNLY7@n`!87]A9!VT5!m?Rj4
Op14kO/TKOr::;Th<h$aTD@f=$H=]A\/4?&_:9aJ!'J$cNM%Ef^f^P;oe+KK!cZ-j&nH+\j%F
WGo`Amg28-X?C2:$@r$DFn0WJ%d]A*1i.TcHn`(KW!@(`+c'n?mWA)V(^;dNEdKk5+Ip-)omp
jUXRY5h?Y1P$5hnp`+E2r*<qGKZN>*m\>iP#Ot%ST(E&>F/@m&Sc1UH,R1[(!9\(e>JS,$&[
UE2Z,YsTB^9`!r#!0Z2h!d:1m+.Q!pDIU&U?;!N36<,k,N<c5;bgs(AqLA!\Aa89TaP<5Xb?
Uu/2Igs_;-VtkH#SDdtk>HT2^EogIY1;DPeCqDg"j\W.-s"HH:</7-njO1,lp_XOI..g8fU`
CpRBsO?TQM\;3;2.UsPbg3&g8jL=Cn,6jcK^1GeDq]A!LPVm3H@rd<Z=A8=&:Cs/VTIWP5Wb[
Xjg:**bSke]A8&4NB5&I>7j?SqI8M8n?/ubgclf=]A$lqpW$<?ZkdVZ1^ioQfG5&ZMJcH+b;jd
lHc%O98bK5RI<k:u]AEhFDC;&gt=%#cUd@@:$j=ek!Om--T0SRTEk6VjPLS5`Fc^FF%G1'*DX
gMjZk!:%&V]AVn;>5Xde'=5o&r[Q'p]AS>q->3:RAS;JbZ/e0n'9FgQfei,Rao]A_d/-c7Nj;=B
]A`05X6ZXEV^tJP"(YYuLk80$YhTN4JNiU2]A4+M/iL1lAVuLSCq*EA3.mHHX.-0[<+4M=`&rg
p9DFP5?u1UESpj/C+Yh>bp%l.We9/Vm$N<G5ai:kL:XeIVMcM)B.d![`7?O8Of"A]AcYGi$9^
S49J,59)JGBXh>s`u@;b]AZMVM\EAO,@"s`fNtL+IGbNR7r-3Og\I5",JW!^'PJdr!E`t+5Q"
e?AHI[OC;DGmUKj1rr+i(1TR<chb=]A>73aT5pfu2l`L<';Rg5dDQd91\^J+\h^o!oM-rg91d
s&tM]Acg%ELCpG=`bV_$Ptch%.br++TfpU6mb)X+bP@]AT[Y#<t6(S"P>,kMI*P3j9,nrj*p\'
ZjBNrkYZ<(fK2i7<<'J[a"?Z54S'52+g0:c<fKd`\hFB_+qe\Ci;G`YP3-,c3:`e-VL.i+HY
=plA[s#lVJe?;$_#*c4[oR?)&[lU:bs(4Tr+Z;E?0:eM81S`;Bm"K-9M_YNXn!#BheQ,\D]An
N8h(8K^UNLq,h=UeKtZ<ZGAJCGBnq1Z_),iKMVan"fG3c'54dd>L\5jXY)dhgOY[%%5%mXe2
5np!0j4EunsQ12oP\31=/di33PnC,+<Kh`l>8l68h+\Idgeu!eW=H=Ro2#9HN.7CBJrKdt(#
_^;b4Bi3XP"tqNPYmlmR@ImmiA1P]A\g>j*Z^EP]A=M:P!S&3itkLDH<8FKs+-d;ceYb8_O/!+
:s@N$LXKecb5pK8bDk]A$Wk&ZdKsNRnZ%g.,NfrP"kQ$8jN#f+rdW*DsF\V9Xp.SLe#>+0GkC
-LbZ-F;<XbFL:=Ah^'VKBgt`O,\HPeB8m(#3JO,#Bmqq%7qcT$lE^1b`APXumqQ1/M*i_+I5
mkdp!h_X5f,-qT>iJXkj(3sF=UCa<)QrHFtWSqmHeR*E-oc&(pZq-Thsq1?<_n>aeZmTbHY+
MqF=PF4n+"dgGn&CkYTn"daguD^8$]Af#qT@rPN`:R,r"'F510+QZ&K,$Ael29JV3>"i#nOE!
e0K1T_"7"@XmF[be=hDKF!?'i-cUI!J'g)p6t*%B1C3s,b_35S)kP^BI;$)6;#5"U!$S\#\o
E)l1b3l$bs\'T'-ARdeE_c90o1RqY8NulSP_E3]AdM>XHD(:D;=Z*_*Y8Vif4VH&\i<)T$0ro
VH3"L:/<gh#u3h]A5/5UOOUi^c'pZH.M;F6$*>Ef5-moH@!(&N!QIosbUW35Oog;GgV;IJ0/,
ruq>A0%X+lAq=^6R.hNNKe)*,*chf'?eQF57\`Gl9U)nE^S>nn/IkkFbZ]A3+30k^Ad+gPe0A
`JQ-M,&.<FZGS*m4qIT/\oFdkiMq4A=N;HG\i!\,m%jFadf@/>hSRl[ZpW,Wk,]At\[&U%O2)
GFWPWkgb%oH8(ooT,AXg0ENA#`l1kErm5G-QItD*nS`L=3OBj@@CBErg:bTi%>M^0hp]AqWEY
e#\E&pTPCLGH_-kVo+C8"4RI?<8R*V+g-6HfJ'2tJE(1#*><CEM-g6qfi9RN_n8IS3PSj'es
dZRW'H_`!@@r.bm6Qt0N^a*/fR6\&`ZdGYC$G5>gfQ0$"?N@mSU]Al[!-OD0'\Kh:n4)2dXB7
;,[GlW$3M^9Gb=%dE,;Gpu0B:Rj9(iaY5k0]A4$Gjjkeq5)'Pkl#0T6@B</?>F\rIILDaY"WO
,nr$5q3C1=]AZZ,N$^LUNW4]A/#WHYI7ebtXmL^)r_:kt8YrkPpY:2SPN`s,[!'?S24`Ugi9ph
'<l9/U[#IU0o0\FUN49]A#&R#QKj`a#]AeDJ^)KH<Tu,'7cYgtf='hB#iSXZ-NW5Ke(B:0+bm1
RHTpglhs4[[d7to^tWN#ngLsIeWk><EMAIL>,OBb`"&8Q>4\tE3ISFdhWH5hMYkiJ@PG.1"
]ACJ`cRu_K+(_>\Y4HKg=5T[uKGp!XYJNT#!rW6`krC+;5(SFD@)ckd[He\>0kcNGfLg4lCji
Da:8]A-#rj'b'/L[d&>`l!$<&*%(\N/-pW*lc*B(_>Y84H,'o&&^(qpE/OHC@NR7K7M[u(9Bq
8q<lB'qYl)-YKt#KT5&+Z,l[UG+mdg))a'(7nLu$(_g!"/j'b'/L[d&>`l!$<&*%(\N/-r-s
']A$pr:Fdmhhs]AYR-HKEk$*(=<?B/tGSgtO@6BL(nLu$(_g!"/j'b'/L[d&>pAY4Vp8!5dp\P
F:>AhRPWpuAPbP-Vf$N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="report0"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="b81f7a11-e12f-42e8-b9ad-156cd9d44c1a"/>
</TemplateIdAttMark>
</Form>
