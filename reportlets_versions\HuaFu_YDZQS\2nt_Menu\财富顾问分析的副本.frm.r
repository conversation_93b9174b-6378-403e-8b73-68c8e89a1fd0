<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="Embedded1" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[指标,,.,,金额,,.,,同比,,.,,单位]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String,java.lang.String">
<![CDATA[HeR?GZt&4(D9Fmj/^;L!%J1&\/1`cQltCn&g<B5aU&!An*I-YS/:+nAkK/`e:V.B+S$MEqlV
cN<E6CK!Z0KH6?%h:2Y$:P"@lp^gY5RSTN`Ztq!!~
]]></RowData>
</TableData>
<TableData name="data_信用收入" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[ColName1,,.,,ColName2]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String">
<![CDATA[1GI<90]AY&KiA(BgN&XOj_$jgK;[C9oe'nZI0]Ab,I!WW~
]]></RowData>
</TableData>
<TableData name="para_tab" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HbO:kduMsS45-:oB]AaV)Flq/\1Ou!.o-SE>m?migau1)Ll*R?o)kidX#U^a<"on~
]]></RowData>
</TableData>
<TableData name="Embedded2" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[ColName1,,.,,ColName2,,.,,ColName3,,.,,ColName4,,.,,ColName5,,.,,ColName6,,.,,ColName7]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.lang.String">
<![CDATA[NR>!G5VNn+iL4Hn"Kt4F`T3g<>Gqs<Tm*SH!ls3RlDs?Vqsg2e&/"LoV5C3GV>2/\*%@C+oF
qK:B*oF^<sgO5'nnN!%>BhZQGS[!SjL8K)_>tJq\/,r~
]]></RowData>
</TableData>
<TableData name="DDD" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[width,,.,,color,,.,,虚线width]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String">
<![CDATA[1GgfD<)-(B:GOWe1c4-r!:pheN=@lW9tAK`]A]A9cj<)+Y?/gE&'28phte(=X#~
]]></RowData>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财务分析]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[级别分布]]></O>
</Parameter>
<Parameter>
<Attributes name="istr1"/>
<O>
<![CDATA[0]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.url = location.href; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout1"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="d3eedc3d-affc-4e67-9edc-7b49fa908cf9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout1"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="3e950a28-c438-4468-94d8-b2bf9e9b9653"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout1" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout1"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout1"/>
<WidgetID widgetID="aad25741-65ed-4865-99b6-d579611c39ad"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DAT3');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DAT3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DAT3"/>
<WidgetID widgetID="e42192b2-2b43-443c-857b-543792f2da51"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report3"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,1419367,1419367,301450,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,2743200,2144616,1752600,1752600,1752600,1752600,1752600,254000,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[财富\\n顾问人数]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[试用期]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[初级]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[高级]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[资深]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[成熟占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:400;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName1"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$istr1 = 0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=‘’]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName1"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName2"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName3"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName4"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName5"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName6"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded2" columnName="ColName7"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B3 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-394500" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" cs="7" s="5">
<O>
<![CDATA[点击查看更多 ▼]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[A3 <> 2]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$istr1 = 1]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="istr1"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DAT3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" cs="7" s="5">
<O>
<![CDATA[点击收起 ▲]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$istr1 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="istr1"/>
<O>
<![CDATA[0]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DAT3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="5" s="6">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/.!P?YL[8eDZjLl.!d<>8C8<2t&)U=,k3+<mA,oGYMsL^L(/8.C.^W<%Q<!W`H^+t^/u!e
a;kKH_[mKTM\%H[koCkM4rSj5)S@IlMP3]AWA[+Re)rn1Y7"K\m]A?pa",BW`l?#BmAc`]A)]AKT
^ok2Tgk"i)5rLU@#]Af:F7:Y.'?B5*=:p(3)+LqC(5]A5.G5lN[U^$Qg";<\3)GfJG(f[NqrMC
o)@6-/e0c6b9k*(.HhV`:RAr8'@Mi$9A)^!AbYWQ3!63gp,YnL>_R3ETqb./nB4:7i5C8-DL
UQ[7%>Pcq?DZQY*,6c<U6$*s%_Nr1!*+X+S&uAGJ`XI;`=B*S`"l]AdR@=T0Di85TQI+n<,cs
/#+J6%)3"D4Q&UR;'-.r'u=/JJG6/d;n,sP_Y9^q#NeQPILIc@En(D\!0<LWD[rR=ZPN?bb7
Sc!FT/<*SXam)G34fK+pS"gG'1,'X9\2V@1t_[qYtrbMLto!cK[dBOur]AU#2_ITr]Abe9+CX`
XPi.b"Wi#_EjmQaGp`t8Ub"%%?=<E"=O&($^/]Atu%,!b.OnM(*R4rW*["r$*2IHZ/'?ZK?#Y
iY;[*iSc%L^!L"!UNaa/;fYe=hNcIr&<0/kBA7Pc#H_eY^Y]A3msnD%m;&d.-P!%;3q5F^P$R
I)gXpVQI"KHmk.bdnh57%*@Jt$5j.?;5SB;l]AWpFr^.>'D2Y.ouj$'BJ6de/!q.ZW[e:<Y(P
4NmWGOI.?ahrG(=BpuZ.:,b1;H5GcBeVKp]A?)jbr';7H<lRf>@5H*B#g!tNUcGd]AsT!W$ZHh
W.gC+Qo7Le>:\Wo^QrWs!)P8`tKG-MDL67sWH=oO=Cfb]AFIFbHn+Ba[_0C?u<A[\\)p;h)Kl
@cT&%l3fjd0HqdQphBW$WRjWHLA_sCl!rorZ^&F38-Wu?sR<V1o4?a/2B).R76o*+K2M$8$^
BNWss'Lm3?IQNeGOJ:aW;9]A:E8CFdM\9,&iX3H;SY=;3n[Q$JTlVMHKONSUU;P*i_%7IFbra
bt6N4ThLX0019@\-#5A/m'[1eJQBq>-%%<[D7hZQS#K[,RkD/[?-2BPp)oIqE>RAP[`Hs>\m
)SHPeYY"(uru%SPHfi'dpokc?b`Gt.V8,GGFgUV=^5Ldb.<Ri@FLdAngkJnQ-%\mYhHNuA?-
4q:YiQm&?srp1hS[^ojq[1BZ1Y%rN5p6jmT-pqg-@WcCYVYtAZB.t&GeQ&+[*AVOGqETGYmQ
K7=Oq$i-"D[1F&F#j3p&/gVY6bPV^=WYJtARCi?tVC_Rcg^_Yf_pR,c50)/gNfDI#c#T<[\F
:6IlE2,L_@[Ic96r8PT,qc&\5^Aso8&93hoJimoA`r!fkQVU5Jd7&VRKZTEjI+]AliE2+N/2o
cGTeZF)]Ag$<DqFU4^`dNooZEioSGotbK9QFddnD<h8AIp2#h4gXr"*[hp)e^[W(B"$)AYVc2
@qOoZ_K$O77NVNe0LHcL*f.scM-^-O33cjh=4M832kJ#BH3"u!'\Zo3,"1`"D9Um59YhA8kI
2f<[H-SWOg?-(2]A,R6>dKT#NB>$lEu!uVc.ZU\,1-j2(k3A)D!XG//b0bX::b'40!=%icCs!
K*J$(TKan6u$B"hle%6M3^Z1s6<6TSf9r:mr7mV>ESXT">B[9jOk74a?SPaZCe24HiaoT_@`
;_'(H7LHR,!Ffcm6^_L"Nsb"K``%62F!Rf07%noRT-nMI+ccYWEt2kPZ`r`5QIg"K/:I)")G
Yu318.^[(ko,S3?-?\C9,$5&L++Ja8eTMbW/X!i+&@?.jF<p^aW&D$+3p4D:&gCi>*hLR&s2
jE-rNn3C=3qMCiVfmiek=`Nu*EF/TN25nO='(]A.I&+k=e:CoYcP`;8MMDSXJD@^F(:2OQAB!
/"6+O&IM,8p@*\_<i`RPi5/g9hsc^kU\o^b.amfTlRS`&6*bS7S9'n%no]AXglTr2]Acjb"i.s
@m*r0mmLDncIZcG%]AtpB"hf^-AZSE.u[4k!"N.F@`O?H7SdAJKk.k4l5-%d.G2?)'5dG"Ice
^C44c$=;tn5E,"J$40ZRJ0#YJkqE3d>*kd9Sa303a_J,Qk8t*>3s7ABp`9ulK7]A]Ake08eR3A
ka)d-HWX[C[N&RpNs=Z+N1QSYG_1b_Q[KUL9]Am>HpD(!#gHL7N'mB<[P<-8Qq)Ou=+D`sX=E
o",GH$fPc(NP%lc[%p91jUTf2j0G8lrE+0O<srlRh0E.03VjhZqqK4Alr;L,dRkDo^=CT3g(
5f6F\KhPGLpBh%BH*Y@KAhc58UgVbsgFeoPrAdg'm<P.-Wh@9bf)E;XVPLH0pgJ3nm@C0%p'
O#<epE4lTW%*f_>]AC9291G]A_b_YD4>Ci!K*W&_8P,5mEK&F1'SYZ<as_YkE!L1W:4u8eAUZX
+1[0?ish91rE9o(NN95]A)`lp'cHI<rPUEV]AC911lj%Lqs(!N2rIG(FSJVBV6SFq2+*5Zr;uc
'W8$;M7OVo9MV+Rs)Fo0?if'<EUOSYpH$>_[;BMI;1$qr=`bD:%tqIAGL@4m/+P,`AWI@@:c
hLl;RVf_PQ%2SGG7T.6T%3$33qA2!Wfd1c+s!u]AsBG*P`&_Ye4<)dFFBDd^Km1B,r[Ieb^9t
VNRc\$*mKeE/&>ko=B'ff`c1k%!n'0)"K^k!&sUG<n(B]AnT-1\P7L?^HFS"h>DK#n)\]A:@?V
oog$:N->Y^1_cXrka;[0MDIQLOaipl?;BJp(bJL^Z!s#!$MAYAhLLY2Ec<Es`-ug\"#*$o:8
1REohnLA:C[Cr_deJFhhdNFZ"3&"!bu>1uUE_AN`sIH`EsmnR5G%j_Fu[61^'s.E"$g^rl8%
am-n@:S=,L:=_d$[9jX6l\*9pSq6uEp/aDXY&A`'t78@uNUoB67g%R;Yrb[DsW+]A/ud"0dr*
fsOIZN97USo=Wn9.64lP)Wu@7BpI!#_UcKfb\ZjlKWTd;ic)6+\m#Ap?VWc/lSWtVl/%_!"_
$SEj7Hgqc\1P+>@:Ge%S=bYgTS*7pJ$V]AQ+SlCV4hBW:=C%'_ut2+pmY/D)dYZ.oF5ThkZe'
M'F+Mk4;`'hX/g"jPV\dXr"Y5GU5.<_H:R!\AV51;XGh\8YDFhECI4!s@q9l"AN(+kltFB#]A
$%%bL:EHPi!(,pKG:/IHSm4$)Yc*BeI>r9=SG5_.u#BViOI9gG2e'lM:C-1=0sqR%dB[T(@_
71JAc\Hn3h]AK[_\6%+k`lFQcd6uZ#*YVQ&I*H`aZ(Tp$=0h9MA;,AMp=/n$%\*^pE-rL$^kc
>*UKiHf5V$Bq30r!i+)eJon3l7oFZ\FTA30r[4M%&(bQJ!l,._ooVnb%`QgGa&<6Ko$ncH_J
841k)TiLLn=FTjS@5q=,U<V+^jS9lsYU@+&($)dZ*obnWoUD=6.(,EShX)+!,9HiuR8V0R+:
*O\i^p>8\W4mr9j$]AM(>oS^MR@=Ugmq0BN'H[OgaoGh.5C\Dsu$QMiqP&dR:_;>9CkD$dc(&
-FRYXl",<4@31!.8cHS@5P_l=X/1!FFu90L["LIS[IR"-Y8kZ55HujbAGS`Y&p[(Z`k\?n:!
d<IXLJ\h5t81T5]A;?cqb,_Os6nEd\1*+]AIS1%S:,OHpaZW>/u?j3YVW^&Ho`H>+CBK]AQLfG&
hQ;Jc'hY4%cJYijIRZ]Ap<m8iXZW$rhZu,WKQSsQ`M63#+NT?3&85oLe.cKn_p5b1dWIbcY:]A
UEOQ<'08UMcm[470G_J]A/eW<4@CocgcP)d++!<)!j5P;5ID6ZLh0AcNpFQR"g_qT0';=I>MS
)aOQ&JIJ*C+Os.?R4`t+6:!C%p`fqJZ^E1!UnfTk4b(dkdfJ.1YCX4g$:5q*[p_<Pp]Arir%!
3BC<e)s#&[bDu$Oa2(c*H8[DG<#[hhZlF%6A!i!)0+LdlPK:WgUP+D^sUp0,9G_`,t/I7/GZ
I/qk0@Tg'8kC\XcfWHj3Pn_ONQ2<`8UXBDsD@?gA^n.eGJuO1gdHYMt6R(GC>I'f30U*gk3E
K5&*HoBVa,7-B0KiU7ru7RY*u!-#g<'HMq[[j1/M3BM'r./*c"6/\Ni`dZ/k`OHT&]A]Ae-tn)
muhrDGlS;q.TNfF`NnhBKPDkX$RlSc=o$Ff0Zqc3UCOEcX<Y+FUPU=s7Xj=(]ASNj(tjkhMcK
BMe?)+a4,Bd-SY2f.=lc2`_1Z.!(+u.e0V.ai'0WHIC7Vq"".GU;bD)Z1D!nlhV1_'CrpSAg
fIR&lCj'sdh#&`7kF`'>K2a2Bq1Oi?B9U1Htu,]AT[Ip#k-=WoY.jk97blLZb#5J4-N;4o6C,
_(Vjh?j@L.=:9*`??@3Qt?97GELr]AC`K5bdLp5H'=\kiH7=Er9o7/'k):#iTd;"NFu.gqCZ?
._frn(phl"E8CH;O4_sQ\Lh%E9VBR,>;5gn=.R,%r`=Om!O--"Gs[<[i'+O+VNU[=gHNq;ih
Zr'0m+MVSK#c.MhY'.RVWk21GJ_Ld&9bn/`CNlWk*/`N7Id@/FV>E6Q@7S\$F;_[Q>k5-H_l
Q5,'a8VWFoN8E*S0?uhjBieJm!k@T6$5ds5:VME(g=Oa$MFe'?(0H>Ku\CFG1d4@l,(+\f&e
r\A2]AkN5nWi_=V%2`rH`.\Y.Y#^p#Z!b&_@fQ580+E2;PBQBS3\-XXhaZ1Pe\%-k1,+(Bb"Y
9%;len3&?)=lAlpTKaT+@U5o^'[R?06Bq;#]AnD!r/RkP2cEPTb7;JGu_kN!Q=DpX&+f3"CCo
=;X[CRV<B/S%%0eU-2RBGZ:VO"i.pINJ-PS]AVkUQ:JkmsD"2C2T*ki"%1>r;)W9G#QkBff6P
=8`IAkbHb)X9Hk`A787OeHuC:(^,b69OXUhFp@O&9QUJb.'Too`$enD"1*c*Os9j\4EQT@0u
56has/o+-$F9\OB2iD/!1aq@q9eh8k!]AC1ic%`JE(Bl3UP3"7N(D!X/Ydl+LOaNAWQV.U;]Af
*H>Q?u?<Vn'gIZR\_Aqm0mm"P=,4JLQ:Y@%R>it&lN4iNIi*mS0>uG:u10:0@1:p.g>5dG"<
?[!ueYg.efgD+0JudgDnPo>;P,Dm.'<,"FtPUk&^NjcnhA:!;G!1_3O-%q-!CqDF>0R[3?g\
`'WiY>V&q;#F8UG_q&!af=4+[G8qkQ";UH\N(%IDRW6A`X_Ch!BM4ri4WC;_mlZS`ZDig(Gd
sVm9IG5%?8u)P:bk78gZ["/P'9OG_6u&!:tt_f/V(GOAWZ'@;sR@DH^2^!QhF%S@=,&-'lLj
JnZIWdGK7/cW^3QOht/)eNA?oUO_b(sfNEea\q-)m=K4ei1_7-V=F?%eoZ(:'[>/1^MGdVZJ
iiG9!?+KG&MJ&J]AcLqbi=L@\3;TIYJ(Qg%R@J5GqM\CQ2'H69r%:W"'F&Tkr$`WI0/;d@[%G
G`4LSUcM%.o2`o"f:'+<a``7-DkmQT=lrp/!@LDkkq;;Z^Ol>%p(Fb',&H>'=n=k.aA9C=7/
#9Opf;iY@K.M0Xi1,7q)/e:"PedpLQJmTFm?Kj?YAlB_&kbsH-lD8puH!<>(H2=nE#^:k*n&
-gB]AiJ.p2\`W"XgE!o8!):H%u8,*#'[JUna>IWK_WnR6&Y>.A[(eP4Uoiq(>^#=0.!K<3Kg*
A0+,lHA#IEZYbNm2ZX/l]A3^aIQ*;\H;pcDiY:YG?uXSt99`!,`!CtD9ei8=A:^H4,NT\f23Z
`B..-Y1,<7@d_@49&0*D8F,]A$_R+F<&,`N9:^;)i0\]A'jPpL3;=>%&RPG&/mN:Y<3@B=El6N
LQ2f2gN+qRqme1L:H]A?9R+CDq5)[n0_*@J>^<q9AKZ9*]AoN9fl=2r@k\O1>uc0;q8,giI";"
W)%cViT#aG<A=Rd@jhIX;3J"RFa=\+._hlkZ*OKV\Q64Tc0lWm_bnMNh'6106V*&j.9$1gj<
;-%Z&PFgqDC0pj\d)1CH4(fmQ^0_k>qNm>2ItEghn[3Y8XJ2_;9o08lmM:ak9*cVd&"*f@uu
KmD)g!GoP:f?:lI9h9rPo?Vban0QEWO"\7=47'e&07#ocg(ad$g,DWEBo43_J>i:UI9OhpEb
<@a3M-HS.*EjoZ1MJT/>Ga7n`TM[/<q<fNn]AIG;+6eCIBuEj,'<b=*D(>3T_EEdFaiH100l\
^]A@`"Wne]AMV[Zl5<Rd2tK/HGcHW$HFZfP=?%Ke<qrj*=PfBKk"Vi0190-*"N!]A4e*.4;I^rp
e5J.@rKIge3L-RVQ$.npW86Gd5NRIH`SYP'+aU):$uo*m`#b0l#?j18cnkZhpPrXJ_2<)YAX
7(:o:q'De*_"D$Dib.K,&=/R';2@8irYql0FM@o:['0oU+3,:8I0JNFXgoB7k@9Q3rg;VW`a
0eE?la3iWs+d;iX;4:7Iu`;KU4#)ka?))G3g5p>$0"qX%!]A`jVdZJI%LW^h2taehbDM2!^/Z
B+Z#rfoY$Q7W6%4l^?+"M:8/U4(R48Q2,u&AYpEo2kh(%Gu%$Rf-5%(L0HeO<$8X>B"':7*'
p[i>uEiI,A@"E,9/09m88E0H'5WNPhC4-[T9U(2"Dm.kl`2$,G]Au%h&_(:b%WUmAo&Wrj32N
-7;U\A_"V2q?3_J>/%79ZpN"A!>>jQrN6P`b8fEF-bu;g_3]Ao/Sp/-=)pp,=Kk(P5Y<+2hW&
FI1_VAoM+6@9;<S(,Vi;A'1Z@rk6'7R<X;F$ieDGO(qUTj*Z#?EfU/S=&)",bGsST$-X1D2H
N2X4"HZ40?+NpfTLUfk#/+Jp>3;pDF"Eq)eea&g@=+6ObjS:MKR*"sBQ!'[<L%cSMJgle.#2
hnV-"#h8@-"">W8ks)\q_Vk)(-hrKpcZ)2C!M_@f0\4u%KV83,@@X0:!13(6qA&[I6."$cq6
q,nJoFh0(PW&Z0It\DVU4Y"BY0\N;Cpm#/I'rRQ^:rSeh'kJlcCY[it!n9\_Bsan8b]A6?^kl
bp87doODXYeb!bX1jP&S-mhbrfY&i96j4X$!p`4k"s3&D(+EN;eC$KEh3UC@a:q3GNh\>O!L
]ACE9.VuY6i#KZ7Q6bo/YqFlOo\V@%OM.*^pY!sSW@;>@Ur%KAqKgZZs0Muk2,Q`pfQ\a2_&c
1K?,!WndH7J4$hldYIOE"(*F49[no8lUgnbZfXr$^r,f)hbog+3V#U/bCaD55/S*OOI%anZP
.lCTo(8LJas>h#Y(GV\B^9?7Z='B:>%#eHkNZL!.\YP'$?a4F\K[*+7(K'dY9&r@^?Rr6UeZ
J<[#a"dJh>i&H$A;]AG0joY!cY;^1j3^*6:GPgn5d=(>7VuAaH.P-;YNau>XW.7F`pF!-GJ_D
Z.-Wee^bBCG."nV)>j1Us&RdNpmgT]Ams?r(\n\?,X;/XZ1:XXE7%C7bIM4&l_s2KFI.A3HFV
Of/8K".Fbj(\h,E)Hc]A7&_/+pi;T)]A%@G.r6c#m2eD_'&c2.nIun;aP/TGmR-.U]AIIuPGhEo
:bn'Y5j;mr2Tf,[?aPBJ(>d^B"g\n/ZFXplg:MqR<E\NP19KV%_m`pX5q]A1'I<rpIm=0[BqY
7&`ubr6_0>V1ItY#+U<f_'-7pr`E,OlD!q.F]A4Uq;o3K=6F[ApD.U(D,3af%%_(89;DbNROS
&l]Ah&>IQW-V((TY4l+RLsC1VZ-NDfPO1=K^^W.)GMUl.Kp"&)KE76Wi4e%4i*;0/[;LHeOXd
GLub%PR4/*XI>\@^Rd1'_jAOY;Ko)&h67VkR(T5DHdJS8:I'*70&aq6Mt3JY0%]Ar8EbEQelj
F_kh9W><@B+[$j7OKa0D&r1(7M]A4\p1,DNQ@*n8qoiX?a\q(>J=:/QiWKcQ,OBRglto8SDPr
Het([C/HM;mM6r<@,'/C7FgRPRp-:%UJd\cJC0`a%DmBBd5h2GO4ffdcpj:Y`.Gd.&$$[%nO
SMJ?ltb,*WBO%=m9Sg(7Ml)Zca$<l!WcR3'#YPMjZH_Th9>H5Zs-0bH$0n?PdVY3Rg\oY1D/
dH-P?WIB);6nk'kg.PJUbJFgVH9dYu*C+f_V4nOu]Ae_C"9SUUl,Z&\#7!\l%(I8<X[bW&$F)
Z4qVq!Y8bgMadqrOlh"5Z>tYPaGU\RLW/7_RQYO"/>jh?'8>shgm-Ye2]A$b=-Nn*CR/\Ebqs
pZ>6FQ+nUGiF;ORP*OLR2qBAh^\%*QGec)@W&?9ge5pr560\Oh(g`Kd^8g4s'>09;(#g[/_@
spd3\Ke)kUB-79)GQ-Gh.aJ63o0P8hg,R:RXfFXN.oaapoAjH2Q7hP"lU*s,\phf3UT,/IB+
!d7G)gTQpct=ei:4(&>FYh-0,QmG)$Qb=]AZ@jm7P8&g\dX\OtIL&fNE2V%>1&\FIoNfp#)Rf
,>`P>jtC,b"i429tMZrH44B@#6^PFpijbJ54'PhU>sL,#qd2]A<&d4QT]AFGrM=a?bFB49kI2Y
3sUu$7iKOH?o]A.Dohk2Y`"?cW*oc0FZP[RdbaT<k[es6IAY8^chVN_.=j:XZ@7`Pj+*R>[rB
(n1oTd6QR2hFEj[OZdV'j=7k#6[BRBgD[($?Kih(N!r7e4l=$H!t%FTYJ6FVSF/2k$[$4Kp!
%>:#aZd5@s`\f:[G*`i6i,k%"SLS:)=9Zmo$+/-VPZGnn>lJ2TdB$1`k`Rodidg7h[?.YBm]A
OQ)T?4H.DiYjmf6i;<bX"d<.E):>Y]APla7hhXpsbIIuWj-N`58n!lY-N&F1DIu4kA[;mUGS$
:h/mbGW3&]A7RcKj[/N\Jf)[URGk$6^QIB!`^,A\\(V:[lihGi@H+q2,:=ea:'u1q8F+U]Aucp
UliX;AT6./)*OI-j`cYsA`bd<2WZgE;gg%bjTk`I\1LAB8i9]A['n*`7r2-3.IhY4U/I2@.B!
jF4Sr/PZZQnmd(rD#r?dZ!<o&$%o'X*\OWqF4Y_+f4(Hj8T[-crIqo?G'C\B6>V>6iEg&a5F
^HGBVkG]A/26QS[j=/i<HU>HpP>A>/-S>Ei`9bEnu3\Q\ggdi\FeD'"r^poL.M4OLmgkIXg:-
Taid%m4F9N8uYoXSQ]A0g=pF&p%*LFOO1q#]A$rFA1c6&&:QqX/6bO.hV&==QTA^:^H-uJOmg"
G:YEbMnEU6Y5Pb/o`_P9j[bW8ZX+'fNT`uQ/c[PnH3N\KD_h$AJuI]A2T!<Dmc-cntqf_?&bp
agD,aW/HRV7W"&On#C61]Aif_bPR7uK1,*P6M+ecSd?/c>[/YLpTF]At-0f$oDfb#qkW:63Lb<
#f+>7JQch4Md`c7eRP\bc+!"-^ApKp#^D^30Wt]AolJ:?etpTSt='"\%HCjD_a*t8@nqELsK3
&1[1GDO6+)ilo5$@Ii'Gd=?k5W?!M]A+*J>CQh72%t&RW(d<p8WlS__%uo:_+j9K3?)c$I1B&
7s:lM9*5TQ#o5Zi[tpg3=:'*dF5`fT-nQS/(Sj"o!]AmeRR=\&2oG-udb[H=+h&b?bS%Nu#ED
4V4rfuC%-DZ>gT53j\N!%Z;/kjX4Y_(Z4]AaoU"KHNcpD[$!BYl(KU&q%V6S5D:#hk-P4pG($
7n=.,?ZLO=<)-FX-McM9]AUAu&jG`]AbX30DtY,hTdLpMCh)?0/kk`\`&8B!_T[L!F.4:#5'6U
mcXiKod4)nRkjD..UCBCddDh_K]AI?rgBd/;G52#<EnPAfD#Tphp2%Lo5HNoa*TF]AqJ%]AeMeP
+7qWcc,E@duHOWEBYM)#f;O'a'*@*k37anlEc'^R<B7Ul,3*h!k).-G.\%]A0Q^lX"[JNMGTb
f,PK-J.IFK5[E@F_"q98:q9LdDjacRS$KYCf%@:PTqt*'FC*J/;iAg3.V#of/$upDP<A4^6o
?-Sk1@<:[5JXCO'mPQ!PNT`3EompnMV^_@AucrZF@CcAgGD-1`>ZCsKl>HJO1G!PMZF>n^UN
^00o'@I7*8F6I%F"M4)ZUCZe[h?Rm7lf_:>I6Be+pnA^rjEoRbnqo:dESkM)?gR`dN?^i<f@
gSmhC^oJ(E2Xn\n1l.VdSul61&ogTD*k#YM5%9FDT9]A9,oe,%DeCYP4&.?=;sj'pe&>W[a11
ZAh#hl@rW$@iud/PCK:_/!EBYaI5O/2>dR[BF!#9TaNm"Rmjr077Wo[A7@`i/F.U5Bd+m2bS
qI`K^[2&D#p17=l]A)m#>&!R8q&l$-(M<iOok/$FV&jA%b.(1)Gk'nMZ2u52V17iNF"[,nX5R
]AW#/c[Y,G'U(N'2QRYgm-D-=n=%eaGAb7t2B?'0,S[/gs27-#j&PQ#+6++Q"N9YtF9/IWe2I
%kT'qm,mk"@cs"BZ2#'UdeXi&H2<bX,NtjcHUJ`sN+aQB_!u^^j+*LFKK5G[L4346lr6OIli
Q+?e9Bjf\FF=G&b0e`Wm6a?H`a"ASE3MW*$M,Q.H:s\dOi`UdX.0]A0s4%G*#hr!)\&+Jb(5t
3Q_ZU6VXqeL&_]AI5,CEpAVBTT38Ch%g5H!j:%I(G=NB;7A9-j^k'-:8;dM_OS_p03hIp'>-)
EiU>%+qd/k!m`,n/B;ra?s9!,+m5F]A]A0VnSpa0/N#@Z3BE$5/Y4=^(SrR?diIt2kB)NQpJSk
,p3k!oqDX,Xn9%Q177$=\K"mcS;Q0O92mZ<6l4nq=*\dP=8-9#3"i=eCjq66&[bLH`&X'(ie
LC[8FOMs)MVF/qhQ#Bgs#Z#8fK)*iNHr7.R$)WIJJ"GU3huT20KtuOi):ecH):r0hW'9V54p
e>hM77<HP!<\[U+GGBdtdHWR\Y4Km9*))<El47Gdk"?WT93_YAZq^=TAl1*72+r3Y]ArgL"Mu
&pIh=KPC*>[.2\hn6CeA<*7dX@Mr]AIc?Mt]A$*Fo83[g8-G!7$QI!1YL)#,E7sT.(dK1>-h@+
t0_Q_2Oe?02lpNY3"?XAfV1qHRp>Jb8G:-WHhb_nPj83s'cM`Z'OT`n2,?,Om/Dhns5![>/L
a!#_ah&NcZtI0NBios,F*+4n[-)Xt`F%lY6u0r\.F:fMG5\NbEkmeE/rkA,EDQ($YT_LH\n]A
fX("4r6X1,Y8XdD=EAPcoPOIeAQE03Gu-/X&[b!SW4QdE?17s>lO,Vor1#"#Z?DIFVnSNEn@
F7!BE)hj`IS@8C$PI2*JsDu3`'Sr:r.59a!g`^_>H(.L\/F3Jb8MK6Q>>emIrYA5[tVS=Vp8
TEm5YIN!A'NBb.15^uI;QWJr-@D7eC9il3hG=Ed7fO3bi"rZ%m4;?gWK#Cj$EpMYu)>,CXNn
L/qT^c<`7-.)^sMpd&']AC?>@dIZ&\AOWI?!_Sm-3h#-;FDPlQ3m2lM&+cQ]A^,sG_.h6nY&Qq
'TJ-tY^g!^nGfPp)F%b^@gm_6MifQp^`CS$-fl9o_^O@5EH4&I>r[$WYGS'KVfdj^'G,7!g'
)>o%:.m=h+_.l+]Aj]Ark]A^mdU\MB@<]A/.k#YRKcj:@bhhu^?ijP)-bs'K?9<ir)#rRQ0G3OiC
f_raJ$9Po2@HkpApN/cM1Lq>jMbjo;o-3cZbrJ'M0>`l;f*m8Kr+i?-i?\,Vh-eZM:Ik-P93
^P9*7nrAT?k/i#ji[@`c357b*EP=<a&%rbr^@d:RL/i=plPqsk,Ko'99]AjLJ!H1n1p$bPq+U
*T8ZiQCM$3Y4eKCR>e2U0gBU,0K<3?XdN5RdaOk$Yifg1L,q34hSViX-^8)h9,;?h1ANWEP:
Sl<;7onXH[4i:Nd$`*W95NDJe5Xqj$V`BCdDC^DShdUuZ=JU42EZ'#A9epq;HVMB^3L%jpOt
:R,#r%7(SUTmk\B[:]A?3@><I/r9.94*F/BXUD!G=fla3'K.qF6Lc\7!_97.:+Pcm(B%5F8<c
q(r9"t7IOhEc*iCY=*#A7QbJkK]A7Et1!gGL-C.d.ij#s5.klN6^>tDpSgJR1D;R0`E%8o]AEX
N\$V4M%/hcI=Z=LrRYLdmooB>qb\sdIlW0!p7TC3FUFp(.@afp$i=TY[;)>gcX*J8uCl\Rh$
gM/\YJ+6A[H2_jc8/"(Q2&!Am6HcX#gcAPe4an=FJSDApG_m2>X_"MNP(-XY4`I2`(f2L>pg
#7;1BI]AXK45!0OcMcMbji/<?+D,l=3KmP)2jg4$UDePZ`*k-Dgae]A16c`0*O%==%:#[lj/V/
Al]A?I,^b<:1s`@Ic`19`c2F%V.p<7u!R1ZB`4A@>HCiY)HkZ]A?e4'"\6F1HCmQ^'0P7+K[Ss
$p#8s]Aul#V@F?LfZGc<^+6;/']AF%-m>)lZ)Z^ZL1)j%=:k*b0h]AFLe!J)W,".n./6SK@^ZXU
r^S98oY!F<jofQkr$@Mh`#ub:]AI%pbk+q6>S+QXQ$RnKBoel\Z)&XUs,s#2e#C#NOgWJ#sP@
D2U+V8spB@-$.RMHH;>Kq'ghOsP_&^2J)4Q[B<4L#gZ:+uE=H8Tne>j^.;i.L>1[44\mbINk
qc$+]A4dEfU%e"gKAmBB%c>XU*[N("[8U1X75s:O<6lT3Y=(q3':C,-O'PrL\m'W;m1W`4tZ_
<cWr:7jMjYE[;,7+<%(X.%)sK5d@oYq^0aI:]A:Nk+L%qt.mb;Q\>#+q"V,R6)bsD9N3TZ#=/
C$Gk2-MeU;gIB5E4A<*[0Q8ITdBaEdDf$4]Ado2PN*hp/'(PUImZ#'*NF?]A!7KCd5OuW5QiH9
'*q?h!SpB1QBm:6ZoUS91h!8R4Bmf"s*EfkhEWC<ngX]A&XrZ"krGB/<KkN!!=k^J+hjsFg%C
1l::I6o8-bkkMh,U)s,\"B7I9O)lb-_+Ct<C25uQ4gVc_@l!VeI!dr;afA[/KVic^p&['la_
cqj]A"J=rH%CCp=h:FPQUcr1d0%UZ)<6=%nUcEG&U$`&WR9nVG.9r"X\L8XD$GhQeF9b,:QlY
$!;U=$5V\N.sc5SG%c&/[u4iKb%s^j+ZgMtl6L?q2<Bpf1m@c950""gNR*USGC3p&ib1[UqR
Jr>%I?rUgP^iWU5O>pdNJ-#+PTDO@>Z2qA/HK7&*^:>NMU3N94be:M@KCnn]Ar'JcQe.Y7qI/
\5u%<UWC%H58,ZdMFSL@7=+1XQ2R;Y>rI=@ps+-)pfZ))3R]Ag#"$;>`\NI(ML5ZA7bLI,j5G
9fU[_hY6J8+2JiH<[9BE'`4d2^eWN$MUbRORC%2T/pa@'p?4m7"Ae[`&EQB91SIp0k0*XNTS
`^:MI2qrlL]A!A=cbWPFAAC13!GZGkta#0'AkS]A\eLKrmRCXdcrlm:!_SGXi&[1bX]A&^i9QhL
juB;RR+$1iQXS(EhQuGH&8,!n/#rU3g/LRYMr!j0qcZbnOHM[ul2%XL"P8]Ap?nuZ[a2=MZJh
.^*9TQ63V@)Oa1m@\u`X6bJ1pD@2_5JSIZeC:Q?d]A3rRSb9qC)4iis7lKJW%O+TK.K0-6Xt9
uhNZ)=eJasb)h0kWTC`o,EVG-X@I2mkY%8]A6:`K0-m&DT^ZAgH?/@ia1EQh[ZijWqsM@;KU:
@*&fFK$1a.O,bZ:C/nNpWSOHY.4JRfD*JHJBTO#WR6(?SDWlO<S2!IBR8FrogaDGn!t+",*q
Y6X.?t,a7kG$D^HFq'nK3mreuP29-/o38VHI%jrG)!VfbN`0:"ZsEAp?'$hi>7kL]A_'mYk^s
H&6D0RnT*WkJM7.eM)r"U_#M..$tdSEdl@]Amf%1Cb)O"Q)n5"b<[V)j"C$,dGB!"03/i:G/d
gc37i8/bdcbH^/kh=jaOV(`/ZeIAMB2(_J"QrpSpB^\JoNF=Buc\25taVI*j>+11d,MbcrV(
Il[5/G-eL91V.4<eZn6!+'l9=7lb6kP,5=pghfcR9dq8GnpZ*7+*4S5Z"X\e9hqA$F;Zc;_+
3Dr5=f3)0<HjTum&FuPTA>-sj!6TBQ6[P,F\"oZO%9b/1;#B&G3'N90\_:oa+B@litG@4c9l
W(Y5b+YiNbT2p,tcIAN"3<IYYJG..RC%AjOhkWoNF!=]AHP[VB>i7Xruh2pmtX]AYXQ<!`1g$]A
FTIZ==2#:&kYRIll-AQU&FT'=hbHpq%Bo6(e58r!LEc_TDCEu7#UD0o5O3**p'=]AO:gRYeAl
7%i&0%.Qcj,VI!=c6WqQEgd69F;Tg+%I3"?sId>H/k;.&_nSGaQL4Ei7GmQA41CRV]A3/Lah1
oh"?VsUj=4&QZZPbhom6$A5K-*AGd3UaP#g`1Sp45m_U$qnRKALEUBeLApq#\esFGBQjUmMY
6\UX2c'_6Ik5?/IQjsOKn9[QF:PIh/fRk:/^b(tMF6uU:-0[YHCC77=&Y+o2gqkm^P%;.B:7
Y0O/VGhaZ*r5@l$&kr),J*DVoo&#BQcq]A<<&-kS2h:5]Ak5_ke7QDa.Oq^?rp%GR<A`Y;e$Io
IG#TtXH[@L$M1>NE#SqThp/^o6i6RI%#dYLRs7c%g<WL+EV#PL%5!URD_O*k]AqV_`F:Q<IUF
jq5m-CC-__q&U>7pWn_;gZ0*D\K-CAn7),K[ng!sIX&&(usbK"anBP/9h$E#dVY+*a^@$4;m
57BOnX?d&L<Q-_`4AQ1-+mZW0.M;TTJ/kI`$"h$*h0jWP/<H10#mUS3JE+7Z%KehjeOnVstY
+'%,1\<fk_L[7[n(Zq<d\P)t2V:9302AX.Jgb-Fjkt[#*7t$5g.HG%8A=M?<t&XOD\?)aOSm
H+kMgOe\:s#WEiQuSUQ"W7(rKhAUXe8#%c%n;6!6W:ZZ!"]A3Xa/2#lnolK'LJ8AY>ge:j#K/
^Ir&m%sZ"<`oDhi4QR5D@g*#9:1EpYo%EPLItW#/WQsO1eD#^?#jC'mo8k3V%CM,/&e`d!6N
7E%CcFB!>$"b^?f.X[@^cJmf=hN!--K6'S''Q[Z%b6A*AT`\$r3L1DtiVAc4pj4^r3rVV0Nq
(Me>OmF3K4Rg[cI2+nP;D5n/ehV\HdEdA,>JijbmkrQIQ]AT`-17:rOiuS$8:;%XKaAf_jTMk
Be)RhEe3'SQZ%98+b-o@ZOc;r,B\6\kEtl=Z@$E)U>Q4/ImRff-7'_,&TOjdHc%qC[P=aaB(
XE;56%RT\8eejj<.*<F&+s9<IPD61j@NTXu1aUDJ_(e<[P42s0,Q8%KVAfhJe7ibEd*4`$$J
q&EOn"B8ktdeMgo;0=@$>R,1frBqUFL>G4!$/he8!M<OjOm/#5.:(oBLWPQ'$,#lpr[UZ09n
eq&E:mdHlen$dHG)O`GHpRaK56*0cej#T`=*o8MJ$4(jCCTsMonBXOgT*"0bSE/7'7VTVl5&
*]ANIZ0:pP%@,?u?uqLr"H^t'k!rn6JAQ,!n\;buHc?FKTaaLnL*25atnV6>2b4,[[s/:3`M@
_6o)\&Na7kLX+(cIWj>h&_>(hh`3EY/ohT.MeD`Zh-Ss6)/GoF&oFt[g21KPQ*#3D**7aFs_
t%`^,GS1aO6'*NeCX>$Eds@W6R.C\lf8&t5<^VoBh9bEgRP[pb#,,9&8[kPV:10%98klCO2[
2C)I"&Y:XG-.JiqI8'n&Q>>=el792`0gf@^`UW?tUFm@8ij]AV8VKH0Y%&orj;eO#t06dZH/g
]Au)cq/jd`CghFb[=]A_o1Tj3_KS4?D0,g9b3b[1rQ^*C\)X8u=_3g;i:t<M@TL2bmMW9K#p>f
8'0UR"(WKNC_#@+2-nilc%,T9D1PjE(%I(Q,:WRO)s-?ZUk3"OLRV.XQM0&9&#,(nO'IGg`7
;]Aj\)n!lJ#l,U`D:IM9/<><*HWIc5.OZRoU?K0j9iE*qZrPTU:nu]A,&f;4.i[+eV-KXspV\P
h+<m]A'>LPYq4*Ac.07D><LnB3]AjdkZaj)0E"6Q^19,8k?)HT5]A+4CF*k>)VjjNh?GU9)St$R
j^:7tC@n6IHb/>_]A)CH0'$47I"r+\qGb*<<dElDfA`P:[7<i\dGt@FAdpK!h(]A/PK_5`3N?-
KB,8Id=u-<Z3.UG;)?p"D$,_sSR>prOc0>5$)X?TRLD44'M/:hffqp?o`i@e_q"UnRM=qL1&
EP1GjWWM+Ta<Z\=af<eX`VJPDs-,7J,b1MZaJrgmXK&!7Q(WK"Z_l.2Y0AY.ej!sj8ONZGTb
&E>C02RS>g&/`iOkQMUSOkshKmu_jGb726mKba;(p@UEb[OF6h;F'Xe6N#SQn!>aKE#-Q:;h
K%);e?u:WcW?p:iD-DJ&7^M?2cB+;Vt"%YY\O/O/+q)2BG(a;<^ERbg_\f)8AMp"X18W(12C
P>;=a<lorXT6S%+(4DdKCslm%Ci$9dY]AGCO\Q[k'P]AV&P&j$cCR$5K%;+\9)<qXcQK"LS)B/
hicoHd9L4.Jsh9r,47WKm2k;Opemn5]A?9R;au?WZi&)I-+o2Lmc%lgg+oaX$iS$pU\qn$QV@
!eOC#J3$d^[.4/06L)a,.1M.h![cpEZKl#iNG]AX)<^7e'EDeNhAi-^mOlMr118^2SMhO]A!pG
5Pg;%2:ucMuY2^SQ,-uZoEV-o'7$ELSa1MED6fX,%hA`8M4/*J-f$f;\+RRbgX1o`E:1!lRS
[9KkDa;#5;AghaVUos#uWkIsROK^s*9#.C))[h,7:c7PX-.QaIZh@^l#?QJ]AuA?mh:GDYYaF
IaPZ^b#n&0CMoH"9?`hRJ^>r0Dh';,.QJK:1Oj?m%RGmj6(NLMl*tJqpHZa>0:WHDrfr3KfV
:&G>mAFb2@nj^#Tj*O3gn[fSiWT7lF0`MqVJa0gE&88it]AgsObfCO<KGO>!fRGDZd]AH<-o#/
Vqli1jq*Wcg[_@d,E*O;UQ+':.G/de'F8=Zs)OQr+eYsiDXp&^*XaDTq]A]Ac^f#H\0$*5JiW'
un7,E46/MlD67IS9*U)_/*u.p3O5?=8'(/Wbl#i!Q-?7AVZ^f/i(SrMP:^AnpmU)89I6tftm
5e6Y6Kf)+YV0lC_%nB_HD3N*?_QM3<>HVNVj02sFDZ4"b-cJpuNbCpmWr0pCnU'6<HE9$r.a
8uM@hj7j=h#YX.VqJFWf@`AB0X[Eiks5qSMm,%d)A]AOhnWh6JP#$c*FY?tr_J,/TKIKR"J%,
ZP`:Qj82]AuMI#ZMM>L+/o@9\k:l%>.]A*J\_JVUB7C7Ye;^`WRa>aA=R7]A_/[_QCbVA'iqmo"
TKIObL6dJ%\UVV&AB@AX^MF!n)fE13]A:kgajpni"CIu[:4%C4Brog"M1!"b?=CY`TC"q/WK<
bI"#9-@@gDSEetgT3M62L(h8!)+!H4p(dR7b#pF'[<N1ad5GrRH1cYfJrIc):ko"]AiVsFS8D
.Y'!NgT%q\ifH^LFJ2;]A[t37mi)D,]An,,hJKApNGVB[2UT=Q:^X+R0eE9VT2Z[qR&hjj0U`m
mOKhIY'3<D.&ZoN)D,N!l[[WlO0<@#p!k2n]AnR9<lOAmYAX58Z-$H;+N]A0FbQ.&d:_>(MCOj
TET"]A(<tiOK3"6O=PMrN'89ThOYY"YiT:G''S6IYJ<TRJVu5lL#YB]A(L)ZX#nU649k9.(\dH
3NBc5DG[eN0RoLia3H;^>US_6Gi-W%O`Z5XdDNJ5b1cN0OHSB;2!&qd/`FPl*Kp@%'C!gD\L
?j8]A8E-[*9I;i6ip4,ABlMO=4G#!R8gYKFitj1T"#1,3gn^XR&puO7$tS-CS58?:OQqc4B![
bRe?ouFFbc\K3sLp<il35+F*B;PMW7ipY,@bWZV;7k^4r.tC"g:J'"`u%_T-&VWDO2B8j'hk
"k)4[^)hX;3`D/1Asqp3j^*%Y4AXe@(*NCYWBXl,cRaV3%<\W++rO%akfn$.-nJrod`npfOI
.BQBoQ`I36a+F,(Ar+HK`REXbF5u*IK?3?H^TP]A=.q7ED0e,k2I+KeE`EG'QCa(O$osGm+/8
5Au4U8\td$R#H2+reo9N_;#ll,@+b8t&[$bRo?ueuE_ggRZ>,\G"HuuS&<-;iZ-8('*YDcTH
ra@K.3/):K<).2)m78c\UCjl/#)Cn\$J4>h2qhWP,UmNp?^J"Q,*k0]AglJE/q+c`._jTmAZF
$d)*?<M>I5&tJ+V-L6sDC@#=L"WH$AYk^0lu:V8lp>YH\G3m*&7Tr/'$$J%[.ri=qMeO7]Ar&
pTf5<Fm+h,m24*OE'JZl9DQ*?Xe>L&o#/j;=@HXgjIg/+p.I<>(DhPoXOXq__:eRSc;=s<fQ
*q!r'Y@-(pj&Oh`'p:SG0:5d$g^Sg2KA\mq68"XoB8VIq!'K_4K92d]AnVmA/S<_qF6"pRm'4
g@Dk-$j-&`oOiakm\P1=3R6.=s]AmhG"+,\o%RUdp!./>\\j9#K$[icu_n><HpF%\CHb)kD_X
KaNnqTscdQuBVWX+jI+E%nk#2`i4E^sniL236'*ouqDmfU?mJfN_'ri=Bp\FNp):<@VLYX/4
W<FWRgtgl9L*1St!fX8-CEQUPVnF]Ah5b__/EKYeL>u0lK'%Amgos#WSI2&7Ta\='LGCC5Ze*
?.3&E04lq.f!;eqOt#,EMF2+!_"h/(rh^\c6./nrf)*b*&E,?`C%T3g=15#lMun'H/*8M4a@
+q,Z-^;o=B?*a./dDH`l1`K2<eh9ql5YTd186@r\ok_P/pqQA(<tE[qsJ,1+1nGOr2]A(%;==
7I8,E/e%nOSr,:EQgn=3EB4aF7]A#&1"Au)5<e^?g;,i@@`p$Ut3>.L(MIt^4\lc`_u.]AhXue
"oX`M#LfhAZ,73\R[c+d$(qp@a,6RL-0lR#APtDgp`fccXV?,hh.(^pK13=(7S^LQ]A=Kfen1
deHh;NE:4Tb-dZFsp*GYgV!kA&<obhDBoNW&Ha5<`q^uXL6ho]A-PY`02Bc\0YY&1LgFAFm^+
,N*IW4UR'4dQ>=NiM&!?b+r7W94Ja)TTu,pK^-qIG3mM1[B4N+-YcUip+/1D/i/>5g&s>YnY
DluKiO&q1TEm(h7c^_/4kj7Q\%IQ0J`(hl]Ahq&=kFY=DSVTmTi:FZ@]AOteB)ES?"!?<XLQ=q
;/CFZAk3H)lBId[bMY#hN[]AULZ2M@f*@KT`,FX8R>k6NGAp!%Y6A)DV)]AMLjg+aj3.FJb3SM
gOZ;JmVh(oKb+%d4ou`6WE;X17d)/@L:5j?.1=O>I1aY?\D\^;T2R/IiKIodLt^EiYke-JZN
a>+jMWcGr4>22))?"\d*Q@NUAuP]A:RTfSbZP*E65@\=,u,,5t6tX630VF"Vt)!e'pmI!pG6j
$WFI_3iQ!8Xoi^:!jBN5ggt:P)r%=@ls59Lo:kQS68=,;65a\42h"7nZ:d7/,JWKP;Ku?9d#
KrJfiT5UEID+"QnV3j[@Ubnp'TNfepikA*^UP.fpR.A?n"%%Y:I0/PRWcPd/$-ldn@$&'P=3
i\b;_Nj;`Q]A^H=mY+9j!thb,Y,U?0N<oGdW\d):)Mg2S[$DeVDi:V2,1)16>JSbR3.Y>:4:=
e6!rCtG*%5VfU5aggDG2fgcop+Bs_pu!4CJk=RhO-`M0mnLR(d;5[4Q?cjBMpGdET!pX`X&[
(2rT7D<:%sg$h1$knV?26c.V#"?qLcKoP<:IcGIDMD"4[G,(Dk,JMD(K^o`HjBk-@!_Iar%2
Ts=t0p/j1q7'5A9pQoNp8!^=YT!:$G\tM%Tq=M$nBsg;]A4r.U&7BGAAd3_"`:]A)gII?0)Q`2
8u6_m>!-c:AAKd8SP]Am;@Q<>$lk(%=<9cCpptB"<g@4$W,Od7*%]A'&jX/L=q\*kSeJeB:G^[
@0NUIX_FepuTM'@p9^Vh[Yjl)CFt.0>MXXi^XL4PX3r8jbaqn=X'H'$*@DR6;A!cQul`7R/j
/p]A3`Ejo;,1iKt+cqP;=j4G`EAA77oGJ.t%+ui70K^hSW#>8B;8j$Mm21*a=WtXmitsUij+5
Xp9^qc1Vt2=#d(!r>@a4IuJ^X<Nl.eWnCe1T>$WK.9ZU(uhJCrK)&4VVA\[X=K]A<HBgT[\;7
c6\L\QB2[i=2)Dt/B''K4rU5/\q%%Z)m%Z4BTe]ALaYj#43?l_BUe]Ar7EW&QS4&qtY_fIr;-L
rd%Lrm?40.G1Nrc&n--mF^E*tR(!Ri[/rRUg/7@EmG5pP_d5ER;7M+3YX&/\PNq?A!c<0mO[
<7!lo<q8tE1mjXf,^9,-46oOm79@L"B&kPqk)UYIJ9:nd31X/kh`C(NKI3Pp-f"m%O*A]A^6"
^cBO/dcAdQS0Gk,R+PuPak6@USL_,JY6@j;;%,39BRf(`)V"M*Um+!?gqTX:tCoM<2%e2"'=
)M);34jJ2U^=P##!Z>X7nInCCYk`$\4+q^seAF/ik>Iu$/S'TTC8gG1:HkM'A$\"3_D>>rnR
r<?SW[$/64s&2$aUcsk`gbEB=?KAB[r*p&`q4+TKf"+r>-+T0B=I;CB]Ak(WLW&%p\JC"=4Of
,,uU-="hbM[:oW^+]AU#oN\h07Nr<DYt#io9aHrT);TsBL5-DP63rWG61U*r($X:dBk"_d+>_
?R-XE?\HpWpCkh11=c(cSCu/b0feZ_=S,]Af._CTE":r="+l0.obBe/R(-OY%ld9%5^r?ITmn
9@tr:_ckD^-#R@:eWJE+JW*d,mg?f<De8cANY8BJf/+?r9;r2*BF?/a[0(_M)B*IIlGW0o+X
`dN\8(!#(KMinu8^\+!m#]AY^q^>cGY>b0Ebt.XGYo6:R<saVQ\d(YF/p^MZ8LQpC-H*V\7]Ar
-8.,C\=;9/$'(IqG4O*fpmu-J+78VKXNl/+NCc7q<KlRcOkcKXYmbLiB8C/%k!aK'q?9qa[&
f/X]A,RnL&dcF]Agmi:[FPUr:Hf<^r+-kT)['0@1^if,7-<2p;9?utmSlC:r<?]A#RasU[c[E.(
GBU6aq`G1kBmF=[L^[\Z%>B"R\jBkWAU\s>mis:8=gNRk1]ASEMea%#\I/!M[E0gPE.O0I1>Y
teoID=t(lf\C!;G%$P`*dDbYQrJ0Ya[/cHKO-J*25WPsF\GLOBW5[=)2h0$J@oLR97Tff`'?
h)_oH=X`O!(^WEJdl^P)D>LiKr__$3([^VhGM=AkQF\fA(i3@`>fdTK4i;ms");-*hRm5t.f
WJMR$ct4O_"8>tl\$(\k/RR`?ZHN%qqY!!oIWWsWZOA_b;5jT`ohl?<?[\]A\?bHmbn.TYpA6
R*U%B<^""u.cWGt[02]Ah%DdEkrF"&U+fSF&:+tcF4gpo9XPr7f6GZ'2QqXQ^.m20c9m!7+eI
AC\UJ#>Hk!#\."RSZP.`*g5@S*4+8qOZ?jZUJSJUUs6.k;rlI-r>rX`;F3S?S>(YIfda,LoC
2Md-01Sc$'JUa_.P9mm]AarZN,6lD9<So1h@51V%)W1?M3WZ]AHiE0N^\FiUen0V#l!g4<To-)
GQ5XktsnjSXf194eo?qR%hc^-Js)ulKY=(m`]ARYe+89CoU(?nfUWF'*I;Z.2S7Csb3,MfjWW
,<Bl(BT:Q'pW6\<@Zk23^<d3/T103BKaW6F`63j=0laSU@)dZ0-(L;h/So.")aRPX"?U9U*+
5_"1O7a=ah'V4SZRbH.lMN6"%oYtK$V5%1[h0oji5Aa2*2.T4Qo4LP\2&<*`PCA"V!LE7&A.
!:X+C^+s,bh$!XoA)Y$CR]AZ[fig\TS>.(O-.0oB,@X8X[(<[[8$Q9Q>-"83*EW4W-q0>9C:9
6Tbn$)F<W=\H]ADPdG%%$C*("o3))fc`OrKoe%>4i.o&-GK]A&lK@SN3>+MfP9SVhI\Yg!ea`L
$XSU!%nm]Al1q^]A+IkQXG$?'j#C.Ce`[i'OQ@O]A<j@Q]AJ!\k4Q-(mV'O(]AA7u:+'V[1A3'FTG
?.dZ1XS;>K\=q"W@S1\;ZUMYQQJ^PfWo9-X?0QmMiqe5*l0hbiO/u'N_TD*[<.?RWqLGu\"_
\'2!He@p!g$Pr=)1D*T=$9=k3<tUr:5>;5D1m,nNUgebT=lmhE/N*IXZ]A0mU[+'L6^u*"]A`A
ldfM1P@I3sQVs@k8s/"ZaRKabdK-&bc'JR`$"G=&eZic3F\<h^mecct>/.DE-"`,*QI`pl.M
0jTeE5CA!i0C#$6L1hP+c4$k?W<]AooJbltNnM4IbtZs-oLFKbQ359M7"3&\*VoL-M(+j*XXc
&DmbZPbD;(9)Y#;'4F6$*NWJh[4&8p]AeYE=6[ApX>7L**:?pt=,$4HGePi(oKaUocfbHslu!
qX`DZoWS8X?7>0=j0'tV]Am,'f;0r'6g$cIhs%e"!*S"-?/KTe%TR\&&.#nJ4TG6WZV2Whj<0
S.&Uluq7#-_P;&^er3#Ql9a,uTAg+9i*/PRo#]A,S3:U+XA5lQS:2?qn7:@S*sSMT=5U6ZgZ#
Mqj`*.g%F480$?%c0;oFJ+Yg1D&]A<CJ?k*b+]A.7,m);4PWN&>L!cbrq4$""2?q)@N:A)f'i2
u^s?FO:<9kXYa=O9$Z2&I"1kp;i6+3gXKNe\%MB]A(@]A7NU/22R(*(UUprCr4Q_Tb:TCGgg5i
/#UOD=28DF;-QCb%Q`!;R6FZXS;'ec9JF,7ci`IE9r<B;i*_S\E.^X'g%2mO?8G+Nk;VVes#
I(4$[K^A<_Ld/4ICgM*LAc'UlI0dC8GcJ/)%hkF^p9O5N\>HXY\(Mfk<AGg@d#L4C`jatkjn
7'b?]A:D_h*5WWXgMip9cMA5N'2rdJ6G<IF5N1mjeaV^-GI,nM%Nh5H.G$aQfQ1m5GB?\^(:i
7;GK1i>^@[6/hh*a^'2RIT"nh.nS#mfkk[[G>6srU:BBp]A.fC5`\IfUR%\G"<QpM<R*sQVo*
bC<2CX!=cU2e.GC2!q[XqBRg/JZ--Y.Vc_d]A/r5iT.3`jkpEgOo9]Ab?WFeW'`>fn2Ve3m2Rd
#jm=m\?4Dcl4e_^#m_gN1nA^\Rq3Yr![ig"#Bk$F_?R,^?d9S^'t:1cse.@>+,!/>Zaq!LVF
q[gUP[`h:k#X$/\THXg]A#b/[FVBi-e^Yo=kVc-\cEA3/d'@*81;u*;9Br1L0JlB$"eTk5GIk
#M.;:2rA:7,ZHkd+3>Xg<BCgYY>`BAAqcV7dq,8qZ#((5#==a@\U@(:#N&Nr/#"4pOoW+PHF
1Cd%l^$8d\t.;kqkcijU\B4CBu#`k5!eo2o_'XSJgZ+=2FqG>D]A[2<6Ze6uCXs459K;e2rf4
7V#[\V="S1*6I0r8L2ee"E-L`C;sls)^ubpQ,2kY0Y.2i,_r,[]AMb:?K,2CV/jA5-g3$]Ac(q
dsc_AU.63-73\@Nm\4cT#(ePf(d_#8O?pLgajm)h:c.T2S!gD(@o]Ao!Aaaq4>ol+?rpi?7Q*
+n>d+E`]A3s=Fml/pLf3Ko;piKlah/46\9Z:(S6&IELg]AseW8<V5AqS$Ia.<=Z1#"^=hc[is3
K7$mZpZKrRC@u3K+rZpj:/@4$<P-%6Z=8AEW\=b0')+nfLc4Zh\"dGGXCpn@:jdC(?44)[^d
UZ(Nd_#oRYmFYhg#X2"M/X'H)77`n2O7Qg_XjP0IYKr68B9B6+,G5RW<S)Pg`q8b*!d?I;lm
oh?&Lg;GQ&&j+P^Vop1-`7#H:#^@U+tEQ6Q7B>H@1p7FaHc"1Z[JEGb4ZnblGth,FI:QL.(C
8>b%cOOQeJsLoj?6-&^uAn0@Tk@pPsf4BJI<-*C&W^_Th11OJE)ZFWV#%4M>NBK[9kJ2h\M8
PV?8D<r-68bYP"^@7k;;/WMl:[:%3?iTe>iXA0Df\&q;^0pFQ&>/oYCrP"5l&e]A9Y'(uW\\J
L5SGUA=8aX"V3P<8.3ALC0q+6h(_I`R4*^d]AY6l^tG/<E.SuIP's$G$6;Ep[NH!?Oh\WIJ?q
JE'-"!Y0mDq(+;bp)#LskiS2gYlsbsk>]AOALpDqbKL-(U1e:cUF_]A<;l-j^)T*nYjn6kZI#0
4:5ngmY\6!j,/>'SNe-itX5N[Wj#*0=%M30M@`l.:[7Smphkc)(SX)eoL([mDP2OYnl0OGgJ
bSn,5YXJr/L<3P#CbTU9+QK`(51#QVWX?d;,46atrq7PF)DhJ6_[V.&nZT&on&rL"s!htS-n
NlQo0b6Tke,!/T4ookY!4H*BnP8?q+hlUt#e3"LlLKa>LM\TLP;SmTQ\_,>*^BueS]A2a1uaP
<BTF"@;_3/oT)QM$XmU"ltiL)(1<JV'T&;j8qH<2Q$h,;-,,48-,/82PeiX'CS%Pf$V,CQ[W
XFU"g9/;2)fJH4*TQ?:A$Ad%*s6CX_c$%@tt?de(Q`!d$PAdcAi!;u`olUG@Z^U\n8B=m9!_
G[J7fq#_]A&SKq7]AE+9Q7+)-YZ#&$W2TS#*/\!L$WhjE2!IE<f,8-?FlP5sR'=>*-n9SVl'0Q
15U$r6H_p^H;2]AkA]A^sWh6>skJu]AD$^ELrbRS#8^Eq0-7rE?PaAN-'l:@&Fmc>,5`M8mN%u,
Y/k8.1#rUKb8M[TQK=G?\63a5D,:McN*;]AKf'M#GiDFfN8!V?*8)^Xt\j`FDin1Hh&qWMI"J
sZ)=hIF;G+Zb)%ph-,>7m$/NRt]A^g%,,I=Z^J)2,\*=<&d$Wi*5iE=QKpqR;oS!Rn:+qP@40
umnCCRi*11"gC\I2(K"-RXFX8/ZXBQh0[_%%,/ho]AKBEt2"a8OJ2u/M)h>i5KK]A@4/.^>[UW
qKrb;9UsD`u5qg&*[8P^>*r+4St=$[U*fZnRrH,#ag>5<Q#o?MQps^*r0D!18G&\lI(:qWiX
aO=s:2#cZOQhMDo&`*r(t;J#MtTBq(!aCs1bBOB:<-agKtg$)#tJdDqIQMFTk\`&KTI;&/Wh
`(JtUFOGlA<l(&A<X@X&Q63j)+=R&C"ckGk[TScfG,IFig7EL0oZqi_;qg1$HqPC#@lDu/Q1
Rhrp`>*#3*)kepdU%15nIcs=CBQ\#O+nmDGrOMX);mjLmR`l[O&TCK&jKjFG=V31hM0FP#7B
\'&u\K?hEF1@9VdQ*fV08Y]AOJG(bL-j2#C=&9,?5"jA5i_[ed:J9F]AH-6!VnO)q3s&]A)VZ$]A
m7+>Z@]A.]Ac8"6o,M<k.Q9o)@G8uEh)=0WBdh='O`qMl/0@;sF_Mu^l%`a^8Q+r4M6mg*5`gG
V^G%@qMXUmHN\ns8d%YWUN=At?\&5$LZG/>8l&jgu3GjeQ[Co93:6Cl7c2OQk/Cf<F9p%Xhk
>)bL2LB%67<,Rbgm#B0W+u$]AJqHR-4XX&Y0=(k2m6_6%1nc;$[XLfOXp"3WY$XUL.d[[p2,J
p3M22^'Q;tnR,FgZBET0b`F3,JRO,fp$!3bXuLs-++UG8.Mf48m8;@RbCM>tnm=j_uk8;hRb
d=59c9]AO4*A`k*A]AZmKWkmL#%.WTZN=G9o_bL7ZTTZ"8EjjCD(Jc9=#3XE]A@M_iR!V45GbFK
m>&Zcd^9c:Q[LDpm%L!EcC,1LsmAJF(llqnbSC;a.eruB@X:q-`uGZ;IR.jT?j?@/rRHjB6<
FHM(([k&j75dj*k^o<.ph5hU<r,XmcWW=Or2fk?T_lZUP4@/;IF:LLiO!LDEdaN8Wd[dbEH[
+`SEhpm+8mQ"Zs2b"tjmaO%#17F?hN_t`:c1:]A*9r`NESms%TM6cL*`ATn>0BDJ'jh"06<:#
e??`W%-rF"p^mZF]AC_`6p5'_md$7qc$VjW*jP/"lL?[iqI^BlIeWSXMCU[[a/bQBsQ+0+f$X
9e*!;M!^3r7"I4;@+`;udF#:luCEiU<[YV3X+t:ujYJ%=Rqh&Xb3[MUb.J,!Rnetepd4ZGR$
@"&qI;+IBjo@=JH/FBK64(eYSPF,+5t\RN8:ef)ZJ$P2iIj"u3qM\`_)Q'%CNk/l<^"a.esZ
5D&%:&/VW+P^"2foF:HQ@Um`oTS)19QHr8;.kTBtJNdDeSc:[`+`F3&3aMPF8>piPQ0<cJne
,?3t@9;T6o^M]AhaSiU(2Ze%)\B$DG^2d&2;SJb>tA@]ACBCo&@.WZ1SC^SlJ>4^q8nKAA,B^J
QHJj@eMR8Y5j=(S;kSYEEZ01<a,`iA";&d^9FOQi#j@+)2m<c418B=&c+VqrV?#0VjrO5+pQ
0+"=%f<9C_^"<BJ92B)D0d,]AhqD0u@`1YM482Kh[DdVKdJ.\Yu:bJ9t(rY"3kRJZ']ATSci]A<
Ihh+^*!KaD0-igJH&+FppGSqa8R^e;Cc!Mn?RSaE5F&0Bo+=^_R3K[NktQ#jW+)E,)>3q5qg
>`(mhd7(M-i(F:^`D!1$HoH?SiB0=j1_cIm=E7.pq@X'rS%Z%Rm!?P'OpoI1EZ`W9miSSEAW
NhlTYDKS*kJb.,*>,Yp^F,PI>o(r"EO(/>(If51^X.*E!cLDlt-7c,_9)PsbVa+b=)Z?b=_V
tkln4AZiYhR@Ub^'<L':Bj#,7:3KAK^28AsM$Ma;5g\n]A_dqH.b)j<SmQ*ZEguTOje2>0-`3
RkDd]A.Ntr,?/N`k3$RFM4]AD;:&?[)35i]AHbj0!*K(f-?njaM`o;qI%_P?gP]A'N/+BD)"-@(+
Y]A7!\Y,XsU)"hc2fgpS<S(+*0Z-877QmnBgRj&>F3i_iN:6)K<Kkb'#.5a*(8oheJ0A)mNl_
oqN&anR=7IAqTCYI?/"YV*U:oHn7e.S?gUp>#G$p@+a1UKOe.\%ha]A8EoC[?6)\c(.YO>?d'
$\_0!N$&CUQqV]A\;VkQm1foRuY*-3!9dgTdqGuD<E%+s^m;Sni;5kr6F8[tLApRrdS.g8!6+
V8KJkk)>6374J:tc_,UjCG1]Ao-5tSBLi4q,#Lpct[9JOPddS"OO]AaIOK^c6WDRB7Z>84ks#2
PJ*8PYGkPn3JLNl6/0Cph8[]A)?F/jpoSr!'d^Gdoo(6Q&Z=t?+m(ni`I\)6ade2[4J5Ol;f4
Lha?HK+]AriTa;=Zq&fGm/iI?`D@dLUP+EuJZ!EO*2hJG`/.g*eQ]AHk`,:p<r_dRiBCF"%,fX
5t5=P\$$t?8&%A<TkG1^@AD7='!!*+=:SNks5$<q)fi.=VgS:57Ahu</c3H=^Hofi.L,KWPH
g\mC#V,i9o3">YK:[]AdpY3.VN33Z&t'ksNsk-s.+5'OU[piWX1T#6*$cGp/#6M3*/?-p3s1\
79UGuJg4-oa2RKhJ,!e4)1#X'fdQPBF69JefXLk.C>YU>h?=a?u&,Lu6>]A^:H"I5Pq/elO3^
9TO3=`M9bNVYJl/j@<8'k70scbc4Mb0&O>JNqUo$<dN1E"7t`PIH]AIMd\/D/6RT!^l@.=07C
]AXeU=<a@VaLk\UMiSD\TjT"/"!&Gn(B^7V,u\>)7GOGrG=b^"]Af)W!^?N%(eV3tKPgk^LZ-7
7</+js@q;aOCk+p!p->$IT:`,kYPZs;).W-%'%u-&FP4SCFn]Al_$$Xh>7IYu8nCFC8dfe']Au
$h#Q*D#A!:ZB4kdN"\S)Xa7h4<?=Ghh56B9PV"H0OjuZT4d=BNN"c-':B9>/;6DN:G&cm2/X
0K>/7%8:Q!qYT;<gnZ'i+;+EQo^7LiiZLd!1ckTNfMacS4B'qmb^Lg0E\rUNKUuE+Ze(RR7-
!`i!b_]A8^UO$NHXc?f&F=&:j`0XoJB\ElgFd@8Al%aX;j;p/]AIddj76@CEn(,7%cOTBi]A8-0
$qXfEOX.n4rus\clWWQMfm0kD#s]A0hL?X9k)AbM`[cOtiHRLM3VepfOkMqG]AUT]AhrP>Zj%tn
#R@sZH6eerO5j)/sGid^"Z0=(SD>KT+obMjPu`gK'j_ES`CeWXKm-dLI5@fK&JIpm.qDpQ11
d_-&[MT.bQLU.dN==`I#7j,ns7KUTt+ppeVJ5=HAcgql<-;/?$7EC<C'Cr[/mQ6kfIAoHP.C
-4eI0I.TDB<D[_IYEk8U5=,dfY7Oe9V7_[Xo91OA6!D+;g=]A#.(U/(*<m`8gstRo$?fjr48R
6))@o4TWh"6s6rk2Z;.;]A77X'YINnhS"EC86KBWGj.NGUg+/Z2d0#\4hor6Qa,4Q=@CH0kUa
@@.I[-1rliG?T5Be"'RJ@Q<+7GBRqs%b@"%u+#^0$oDOk4'I$JNCKDJW"6I2FBpWY$b&64@-
UnfO\kS\FAU_'<26^>_HMJRV>*mGqO7@0$M9U64#0H@P*M,LQU,a-M>=ZmsL"t*eNVbWos07
'MjiTf:GAO+1`%HDIFiL\7+PpQ3<A:-\NYk=Wiq!bg>@*[)g3<2u,C-pI[P<TCn$9Lgb&U\$
;-'p#4pAqf0MB9J]A+58)($O2%Z5u7e-9\U"M2ecX,=qDu6qU3D0Wo#<Xt:/;EP0InPS7@1BU
RDc\C]AaI,.8ID]AKiZnO[H(0D=N:%0DhjN^0$dB&jL?iiln-B*(lK!<ioXqh$EVi!<p*HCb[7
>13M7gn$Q)C:lQiu/O"JNm/>612M!Y'lkaOX"'GJjENt0m2ocK\7$Q04<YCQ:Nm-Y=Ai'fJD
O9d'HmFn=fuWSY_l6>XOF,_?rP^"dXU]A6DF=r-Zf>Q8Ho^gl,]A!_Hrl*!?P@2N[6Qb942#4J
Z-!jE&\Vj0a\H6iGj/VpW^*n_/nLig32MXZ<G02jH>i!;0gs^sKb=FhMaO`YAYYB'/iZJHG3
%@j96-!Y*a&A_=Rk=*f*+?-k5G,0@r1[13VMbjWb.CiH5d)25Q)GZIIXlGDfB3Q?0Nd=+&-N
VJ34Cs1DO-uSVU$JG"%LDqnjOVn*3fh+PAU<PK5VEQ=R^qW=JM-2Ms/`PWI5:+!Q@R\)4`)L
(/IZjB63OX1U6"H[h>ZTDqqE4V5*'&&jq*f9iasCg!L#:ok6bT0Mtk"!Jn??LM$b.-H/3Wd?
U?aSK(i#LW\rVng*]AiCU:*SWpe*[5CWk1"W@m0]A:oI8j^\\E2??gGOoXf,;RZPpAKJCX3Y[)
qa3?o'jj$W@"eR#r`.9KaI;_=3FA]A-Y=.=oKBh.hF5*Df'`+"t(q"0rLO^,g%gtIi5lja;!7
jpV1U/X$`<E]Ab$BFiV/i=YV(c\rE7V[-NU\`,5XlXA_Wu:k]Aipq-fNO6[9L#d)>n%DRu$/k[
.[]AY`:g3cZ?V5]A.-H7EXGi,)D5Gg;jfB=_dK+>4rV39#!V&X>Bbd($S/oA*?h=;>5DR4]APUo
Q-c\3plQELLi.XbUhYWUejaGZAJuSR@R:h#"c`/=Q*<m,>jY;[$qj&c'Tb1h&=l.khl9iOZN
>84#bSGIbhiImZD;3$`S9dTOfnjm#J43fD2Y,;JIFgd2JSL_6=hW)[c76mghESROEh`_f`\\
kiK+%&Z[H&`@q:KF:]A<km"((L5369:A$[6OlUW#?.)+q->"<0Y[Ioql:*&'XR>J[R>[F)p06
'^TF-bqTGO$cBJe,Mi$$04P7#<GZc*F]A"@oHM)nFG0;1:d\^ri6:@cQ]Ad!g<7HYQ-,uMMC/3
1_-gQ;4$2+5<8#.(MX[>cXUKLGW+qcIbK4<Rka.=)pAHc(?1?Ytf-V6n]A;Y2Pnb)27">.s=6
;a5Y-jem]A457jpYdE(AFSl&4l0s;@!A,4)e3>&:CDkF_n@6+u?A1$%\CjU1F=Nj71-88</3u
'*W*a<&E"pg@<4VgDGiW$LL6.Rbo()VOr]A93dM\1fmDMGN]A7:sIC[(Lhe0,lHIWgmL"cf13@
ae\+P%fsHhrCMnohf@J`p!obB7k^TMg=/%1lm]A=ADtO9,l%UXNgWK>Y<(-@+#\89!&.e?A:1
GpD,s!&;!kM#7KK(XMl0>b_rQM2!"DL_]ANc#BWp6j>S_uS>4#l@6&mG%t`B:aphk()dJnGSS
m=Ep(J;?fX`hT]A*k2>VWM=l,&Y;i.-*(LN":,nt,nC2f(_Z#d`*ng#[FfJ>G:)#)l?#@p;4+
)gUBSJL]A6nXE[S`_*H>H_`ne(RQ4@0V)`HN$YKbhG3E?!6i+.#ZNSHi*7+0Jpal]AegHQiGd>
=\h?+DQ>1Xik>+9>5p1E3)dcn$R0VWI$nV4Vec;04Vc:[Cn^8f;qoMh?kO*SEJg\gFN<k>jb
Gg(SIP\PCPCSHNBOGt;ed3K5AWSgeHO`nX44C0WhEcLu,rh#$^GAX/tk%%?F\47(p9?d2M^a
bY2"=^Q]A::fbEQFCs/8F\cSdA7"U+5OB?P6S$^Gm7!R#M3DM$]A`:DBTbP1'CNI@FGum"Oe\Z
ZeJQ^N7ZVHgHAS5`k@QTGp!pKIjd\fuF`rHe`E6(81#4FMB@?QNMk&-*4\#dj9/RB5HGrWhP
F\0]Ab3DfM#7XI50A]AZ-[V]A;R+SV[Wf$WWP3=!N,^*ZqtZFasB4Ztj%r8q@e!-KEASYLik%)o
Zeg@9N0N1MXkL*@NrM('qlSuZ8(e8Pfj>B#@pMgWb!RR3Ck5_ic*H.@qp[(^oV*[MP%TpF#6
OUK,>d-'kKBJ<Qu4^Lm/=t,2)Hld!'jPZ<+3?d0SqkpN%f)GZRDqu_I`tq*5+QR,JlWDEb?>
5'&HaR9jl@!N"rEqS9LK@W6]A*?5$LpYBqDs(K5N*3]A&3e^uYmV3N)A8ArT/nOk]AKA0C<X5)7
e]AW/fD8oe(*F_EbESPC]Ar-6u2q,2\D-_JV1l16e?$-H)As^M,[P'Fh1-o23BV&R6QioVs+s-
eS6i,)QWock0W<N@YI;k!kXe6e`DY?BqaMc\;kuVu#[RCkVO=Qi=-5+`g;>4+_/:G\H\qY9&
Vr&uoFMO.,udd@cb&B?)Jk/W4LE?3"5GqO2Ra#SAR?H#nsrfr8(83J_b(S?.cDnMN?S?cpO*
:'`AYq;(IFE)K3O7r"GSDCmDipUObN^^j.d(#7GOK<&]A>$+I4H6fJbn*l['%/CW2XY%ZuU9*
jFCD>u3N1n%rkBb]AU[Ls3bdR,`2gdbgma**<)WPR.:VIf"'6(B;:ss):JI!RBL[NV>0;s4o+
k"FTFVJ=>Hh=&j7QQ+45sojL@M>E0tHF$nN]A:2p`[YEBO(j45Ru4ZklFHcgYZ8r@Y(ODEb*$
M^QC<M612"nQ<`!;:q&[e5/.[fp4?lc>+d?rC<R7tO.crUJWN*_r:]ATU9E`m?g4X^I'h*]A_r
(7QZZSG;@iZ^8WEEnN%QY5N%7UQ..u3QPZ?jt`99&8Cm!oNo7aA@Q/Tjmn9AM([mB#t$(eM%
hrhZp]AG9]A^(#[>UKB@]AiAdl-p_r,3_@R*GR\uKI#i^P_9!:tKO'JlC7"TUBljnEQYppW\#Fg
YPB6gRY#\c0V!s(5E3#%7B_rn$fZI=OlE9t1Kgi?m?5RrB$Y_G.NH2rQ+=KUZoorh"pPfgDT
rgn'VKo4s`Lq_&fSiXF-%YIA<J[bhg&LDSS>+TMs5epHjDd@9>ooHnkt]AntT6Dk]A2<S*3T=-
@-?=M;CX=U,UJhkY:^L!]Ah523tpd,b+fTl*F7)PeLS4=J+*C<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="115"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="387" width="375" height="115"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM03');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM03"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM03"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O>
<![CDATA[cwfx__zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分公司明细"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r/te(M1MRB_a:;$<.FU'q0X8<KPN)&5Gp+FsNTVB@#0$ltL]A1YtDY(Ce&,&s/DM$T6iJ!@
:!q+p`i]A_9bcLL(KN\+?D<fXLIV%muOs/>@B*!3@CHVq<blA52W]Am+m,6(dJc:lHUL7ip)@?
nJ;B$J`,,F%[o:Y+2h1X&7)\Z+Z,$V0d-Nbb=IDMf"oJZ-N<[/rTm]AE:]ACi,TR>Y8,ECnZRi
s0A/pE.*;-$qNGpq`?2G>;d7ZeUS*rPgGNnnm%PGBgJu2&cKngoh9-^lI^np5c8L.?Cfno!W
h>O,^\)7bM)HP:PcjT$.U;9klph?tM<TS#^i2o]A^-sH!iXp.sT&1!eWnti-+7X%imd"4K=(f
CeobP!!Flj"2GC4l_bLKPF1RtlJj4AbqI[i+=79s"oBlG^=dmuM2'*pbPO[p2P%Mt;7&?Dk9
Vrq'4'2.TVJ9c8EHV]A'so#\pG^oVBP(X[E9QPQ%KJuF)ZCe!Ga%9Y!u0hXH_^SR%8oQS/dc"
dJ,a0mSb!qaoT.j*?K<.C3:Kc0J,\Y]A)[P1t#X!U/aK'l+rpo4GO!">!AT^LLEQBTY2rg%r\
`.>rkic]A^XEAu^AClV(eC&@9_R@^U'^aA$?c8hqTHW'G%kH&4?kpM^o39+i$l;(G=@3N&p]A,
l-fI\<iUX&+..E=W1'nfb7.m3Q,/HqGHTc/Bg>@_(3\`2^#:V,EGR)s/i>&DYq=rcnF@Shkn
-aTqN"1m!=a[WQi,bIF-8uQ(b:Th2bnM`1DV*nTY>p8';"Re\gB^JJj&MmCNn!2SsqBo%r`Z
f03`rjP20-=C7r]Ai([e>W=S`\u69cU[Mo4Z]Aa@ACTUS75'\u6s%CrVd]An/'>I_a?@pJP6Nia
&ZeIdjQ5`al?tFc[CC:FnBt1uMa2=F`(>aou!&N#n27WWi9QZrt<`6WY7mTjiOGg<uhYP!tY
\UrWa^&9DOY[Ff>K,4F'VMWgKu.1l)&P3#fUA\lH!"'?nD',%pLmi$kj!MU/6V8-N>^Idjg\
8d$bKUXdHt^3=#<`gHULhOq&4TOQce_f770U[5TnpMEkUgc:!&-T_(CYi?\A/;nP[nQ-&`;a
!Tmq<F9<^pYh1:k3)6H@[t6]A)"QkF7jBO`b[Qs#n]Akt$"r/F;-lMO2C/R\iU-S+<^67N,CUo
!"2DX8[:7h0VtKSlL&>Bq3cF7[>*%l\lDd>KK>_=PRO)B?ntrY.^18l)kh/LF.jFNBInoBp:
7aFoL5ND#O-qP=>eS1P)EbW=FW4Od_fS!6MbGloL@4'3!21k5?:fY9H[_3qc$cIu3^=V]A>fD
Z,\e".4Oq0eO[R-UV(/mNWfb(jthPV:_T8J0'J"hcpdobmg/`n0a_s3a_+_]A_p@E8-gN_egK
Wpijk4N2'mN^WqJ?G#UZ7Qfnj)^LT8pcZEhiX_';%h!'6jB7:<,b7W:hqA#\oM;?^tnP$o,4
c+5^#)p)SS`S*DCS69c4_=ZW-iQ"BV7=@mdl]A*;?\0+N@\uC"^>h33"DlJ+*.h-"<Kl$PVEc
9_NYBPc<)o%;W\\/ocF`t^/AtA`,)slai#?7pM[+O>W-A'Bs/]ABW4BX]AJ[_:4`IU@q6J-t_A
Ma<(1pQ^i]AA6ni]A+;5uL(XCmH]AZ-#ud7#Xc9mBT)n0m'GeikmUTJAoZ?"+W8=fm/3nUrMR[D
f+9FQ<6X_/d/#4`ap<jBkj[:b3a&.;jZPjn`W'X6*mH-l,U2;gZ/7C9&('U?AVUi8^6-7Hl`
5EcsCHd>OYRA8Wg<tq#uD)(^jmR@EgWScrd5^rE-$TGD7G8_Q.M(r<)5SS]AWnCB7F0V9L@"7
EoLll,#$.M>FK[u^"C?IPd,BQY1B]AF+CcER5:m2Ib%\n=m\pOIGM8dG?;$:94NCPg`<ekX).
9B#NeW]A@=0(m+]AZ[(R+MMU-F=(NF9$6mr\?)PtO7Wc/079YLIf`NfW@+IF)k%1@-pO@T<\i.
N[.mXU7kRJmX?3kPkgGSAj/QHC!3<X4/XMKMh6[0'Q^s;\R:f."gP6uHJjb<QJ\2=4GKMhm:
9Af$7Z^&\HT>6.,p>rlU6id4Y$4CH'1uZieJe7IC9MLGr$"-G`8;ZX3Gg=E[?RX7aG;Q$Cnn
cXEPKk$WRsBB0O]A+g0(,BF2*0<1%CP&ZLJ=/S!)]A=&GCP+Q0UK=SOhhHRFo29/a\T$U_M#)j
rdY1D@#WZX*/762_GICK@O/i>l]A@OF.e;5.7='OgWC;o927H:5)k9"\<<GlgMa\Kkh)dP"hh
_6$"nGS:jW,Z%[]ABuJ0:(AE^5Ms%CQ]A_[^ELr,o5^B=dhQQMEp5bl]A[h3=mdHm4cNr[.V8_b
hSXI;Ah;(nCX`@N9hm>X&Ns68Pg@&P[(93!_fP$FLh3URp1'(#k`Z5pjc1CB,W"*0!R,%:5<
r.+cGDNN`OjAPTL5Yff2K#T((-p=$M=fbJH0d/Ec4!@VN=WY68:h"SAIih]AA5.CHGkh*Z9Cn
@Tbq%nXM7(He47#BsEDo@G:Yp&X@F0Rf@I0i"7WOaTAk@pCeJd3CSfW;(;(4gSnP"Y^-d$K-
[1:Ulbo]AC1QiN6[5=$%(^<4L>,;!\!RNe,9I.i5.6m>]Ak@rG0tpbe=sh5:.q6+&TI"t($,h1
*cE!37":^-2l!nO0D\X@"U\_eBnP:g^bJG?>a^R]Au10cAhhBgLUq-7enetKMWTRfr/YH8!Cq
oN#JuB%8.I":0,G$D/`71d`>5!SbSojQV<=TiBUXJcT)`)S7(7Q6dR@21I%m4c#eJ]An?^lH2
V#r[H"/!HEn#_<Qe-TUJW.j@rCh@+\OmbP<CE;jZT&I;)W]A0?7G@LK=RWV6esICpiD>o4*LB
e^#7r:Aa]AU2i/u[Zj3;:/0MY11Wo.jo[-VA3nZ@uBIg"-PB-F`\X=!r[3\Jo^^%FgY2;.*Dl
"0();,-Yh1KqMAO0uk8?]AZ>,M3PS+,e&EB0e&dYgjb3Nr`d8gWc)Nd6i("SKhL;-m-R2!9KG
IG'1fs5&4**WO9fasA;R0e[G1C5OR?t1^i?k;hjuuDLr\PEKABMi-KfnL"1hZ\'c7P!_T=8M
HMgXj8rfN?AY#F&T`_:!F;g@A(7[qBs#=H>\bnBQVO]AkF#a$X>dC48?=4]A%#7^gc+&&mD_sl
UO=X^b6']A*.2-RheX\%3Iu33b1ALhRAW9)@G-+dqd6]AZ@BQ5?p8+XT1\4p;QGV6G[D27"HSe
3?5Zm/cbuOU;N:l'$_>05MBs-U'^t.&oetWZ\W*TtsH.nYW<p#<gY"gNa9NKmI[r4nUe4X7g
,!TN9R"tZu%#B]AV>rOf(F)&"-;LGN'97^?Es!m._0;$^96i@Z_mUf/#rr$__b<O1_.l`SILr
i6-J*Ed:H@c+EHgS=p:Y1RU$gTIRi6EDfVZ[`L_-W+6It+EarqsI(ns=sV7hm8B#.&CO+lI1
&Qb09'KIXg=C\%DO>SV8=Eld4%O?%$"cAJRX.9Sb3PWjrM?,k@5JjdQs6^Y033[a:aqMq=Fb
<9euYd[^t,TrFD`k0UjAn,m#F,_Da,E02X,VI`E^ifG&\OS/m[=J/0CKK.NUq-sf6u-I2/7k
k;DoAP?1ac$!$I/2/H/)a,RW2aYTNi%07Q3`jWfRY1X$s5o:1-Fi]A!C<df\B[$@cSBh#(1DO
9gUd`:/YJUAT"!('8VhZchT9h0tm+YW:-0l]Aq^974(KAjW"W^:*l-uFAf]A&g:@pY'C\DXnXD
hNc\B'%SP<>k:FQ.&r)#@$$5\bEE)[Qb!H31?L]A[Y#R"WkK%3F\9V)\"'DgQ@_2o5"3'CT36
"/)qaaf'B?UO>P3LfXfU">)#Oo7"XmT!#;c="6Nid^C0.PP1a$`0[,1e_]AdUjbTcYMA6]A/*J
L)JF1kqmBWBS]A<FuF]A_QT;qmZhc^cWs,u?iP50[NE\rX^h^-JC"l!Q0%Z+LA(us!"($VSS-J
b1;1Mh+^1j5<$."3O\$a>(/;Gp=25Y5Wm#"#\U,?@H095*0SU/@BI/&,6b<#fiiWWl)]AWDja
WksHR]AK7RD8RTNQ<DP[2cYBA\hECUQc4W4C04!j8?QSFSdDP]A)`d5OW;]Aj]APCV[fISAl&Y;Z
lj%4)Bt2n<\A)#@j^YOuccJkB2d0?`<_X,gr8$af8I]A^JcsbCgVS<6BG-YpV7j::\%A>CN2K
"ReLYb<lb%%ZsGgCfeI7i`gs**Vs%eQpfr8k>nC5$:0qAONG&fWk0!J6`Yt9[)t?rqcfQ-so
IC+^(F%Q:M7%YnkpYtg:dJ'*.>r."Kp+<:O]A^%<r::k+Y-$?j=6_eEn>*M3d%!pNmNj<FL(!
5dMWGb+<n=gDnRRan4TBksqM8cici_`1*<_RNL"$V2NPWJLKV!?>=$"@?r>1caPkSod:*PnG
OBGKP>[JuYEk8;S=[]AeMR.L)p5MLk\pWo,un0Z=L,Z7lB(_;KX"0+6&7DUFu-d<*F$h43lp3
+;fP^RAkeI6R=U15gV$J9kNC8)O-*a?2pZhSmUh?\M]A)5ttN=DOR0[lE$!4X^dfO73t,r`Ll
,lD`]A(KH!!\B]AJ.$#d8*>SF1^&6]AC^4Z^cEHeM-PuPN!a5*3eO]Ac:V.mSYo!H!YG6%-h&)"V
_-3#2q!U(>97]A6=We?:2dfBUAIO(OJ[0qPC/LR?\V$W'GX"q7-QaNp.3FWYXQM>SHeg1rC"m
n5`$uI<!R6*m5?/#bF,1)S4'O"K$!?)dj-QS6/$/e#bt;l#]Al'3n314IGD,dqH%cZ97lu=!e
-:;'.ILPo9prtrt-_kmWHMJ/D;jI>ko'.#_Ra$L6]A3?3k0>:OI132#0iOQoI;Of9q]Ag+tOBd
Z-"qLqGFcRd%tE"VRKp!T%6O_#d_$@FOk(;Eh9eri^,EL"eMhj]AqkB%j'ph0/(%IH2qA=b58
_&9!'n9UauK9tMu6s.16OUmVr?=cUeW5ae6E.EK/L:fGc<EIuVlF;R#J;c7nhgtSc0U3MrgN
sQ\Kf6F4ljl4[J<SR&26W7+*]A+cs?hon8tZkT7IVpR[;VjsX)R:FSH;FKM*]A=k@`,fuCC/*t
/6gIWYUZ$D!`A57._o&baUJi%9,JWq,u,0F)87)oW59B:@nI4*m,bdWp$fB=`59TW/5'F9VW
>0!)JEP%?_#RqQ"OTtpQX6MlK_VC)-*41-L*HLU0e?&[D8F>oA-F(#.ot4h#+p7Jf<[;,YOh
=7"&-u8iJ)5aLeB]At$:*t+Y0WeoGYY1ECB.IYS)EPp)IPi2ANk4*)a!oJk!5tpeFC@NoIFTn
8Cg@Bp_]Ai;/^')?%qgbD4l4OX3`uAQ]A`iV^BJ!WQt'2Kn_q'?t,HBom%TI-#a[X%4!L/Lgb-
fQjV:fh.$[Z?F&F=#G]A(oj6@+b;`HE'6C0ZufXFq@,<oN?nNXZqCI1;SrD)pgo+.L$,B<qaZ
usVa8<YIXZX?o:inOJosW_F(I!/K'*9J`3]AfCf74JngYEjRVpolWF**R=j*j]Ad`0,s#R?\H\
:Je@'n'mQJ8dAUqXMWUI<B):4b)m]AmD`B'5:_TS[I@%eYBC:*T#ORMF'Y_/1Z0lr59J,K7)/
%^[j>l-qVJI14V!Qp/Xagu7Yq5.b%.SqO\#ijMZj!M`2<?'PP$Ni9`gn$1eB183F]Am=*+GSj
.MEI@coCMsWe@a@S@r]AcUbh'<co_(+pH;C7(,o8FJHOO$XHSs7gni&LiIhI_=F2i.3C"r$eP
[bc:SVT!`R/hifFNn_DcoW(6jbfoa8j@9('K=!ll<%:IFRO4^55%5r_A(454j4g+V?V7l;S0
$DCk)X7eL.h+`)S:t;tbdBg]ASAX+iT#1eM6kLC@Kq=h:l,<+Mf%=<)Q.6M+Xos*7:2m9q)iN
92q2%X?/<u%[+WQ>TJ5P0PdQ*'a?NUo-M:I,(bemh$/]A(\:ug[5MEP`<CAH`490:\2IAu9?]A
<B8Y+UPX!GP"<DdGm-_KT`U<Qcq4hrhMlrcOhIbU!\;s4"b"[H]AV?=Nam!B41!oHfs%h"0'5
pTBtEWmI!r:Te3n^;/CQ=`_m3*13MIF#5;TWA0YARamieN#0GComg.-_Z*ZHpVGdSSk0[mW4
d&TYa'-+Jm9fE_jb9?DiiPp-0-%.c.!0:9JYT81;IO5KFBZ9oO89T8p>e&R_JQ>'Bf_tK:Ca
o^M%)`O!s3TaJGTW_Be:@^,t%Uu_oGd\DD=YJm,i9O`K9Mn;?gC=b->J`g'_/dF8m(rQo(AV
b^[=GKpdAaA,_(-!93I6f,i=`GHZ&l]A,JT>HW`R<\*Nj3G._PCXn]A$6[jsa!pc=1m2f0uCSs
GH'_g7B\Dg!FS`9T/Y*rTBKk@JfKP;A-OkO5^"a#ZV1D_,pWS\dPTAQBKe4@o+ClR:G.c%^0
RQY)+lWj&P9FZI[Ug^`C_;69kQ*THR#1-fPf1@Z7iC5=+9`Ya?g99TB9A40JP.*P!+b;,#`?
AEWo.oK-(m^&$f#nG'EWHQ)N@n\W.a4Pd:F-\__#%smX(a+LBi(_jQ++GS.)&BuTMSPAY2%<
,_1^$pk;c?MD.0c=&$5RC"5AHB#7NS.Nc@*MI<0T3X`^19V#J5L%fc>a]AHop!W92rce=P>F5
YR=7;g;rk.F0$e+]A>+;Z<h*l$2ZV/DHEZQ_CQ\WG#,\\/]AuAgZaj>1'I:Tu9WcW:*M8gY&=T
*0lb"@.rE$0GjJ:f*+C#+qeE)57DO;acZ/2Ari)!>e%U/3SZ+:E[EhL/n+9M#(![3]AhE)">$
9qr%fZD*lL@Gq`L]AI#KU,4lJ)ooOX/A1en9&(Gj`V2m1WlejHLb.@)3HKDgPt=aReV]A#q7;1
4p/*id=,I8)AKPS(1G4]Ah)<'Z2(E/5OHZ]Ad;C6bamC,3'=Ifj`>Y2m%iO1:WYR$7eImVZ^jL
.b_^@MsE/K"2L9R]AOXP@083ukpph]A#pFatK4o]AE&eecR'LIrY\74$sG0ZY.u)1>/YKo;o/7R
kaYtf\gGY.R*lglA@(8!C*(n^F,B/m965&qg?CCIPK_gI.ZQfa/cN^!1o*&AmmFMLl-4J32E
74pY($0:leAMBC&6UuI;)X[%I$CI`-93#hLOhOE']A=.10saC!WP7n#`?tZcBr<det4n`a\Ln
97D""6V%eO[rc5IfY'3*#p>.P]APLfs4o/_2DA[@Eb[Jab.6UTolEp&0Ybh>VngmanKqX-"%$
H>,2aU81QFTH_!mqYlI.IJ2p/"O"dVSEs`H:2rn8i@2;SkY-6er2a20H#Np9=R>bmhc7qCX?
R(44Qc&]AH\2F_W&CJ>sB_3>I7J5e7=n>A`O!SI4C3.'Li?^)t>IuI=#AM8W-F.gHLtF]AH8l\
_^$)aq^'1-<,ONJQ6Y!GhZ=[6bE3OmqK\=7Fk.8;(V&S7Le.!#UEUm`ME[L^,4\*]A&/%;r8-
fMF+XD_oO:W$k6:_@g+X-LX*o\L1l(6]A.nGP,W7`B_dbmX-h2[U`QcQ>H]AOL]At\>9@srrgtY
a7"g@-&L'P0L^4S9,".*?&.p*Q7#2*\+X0=-L_(.Brs))EY0<Ta^Yc0bLW-[9<=ofLrd+=,o
Aqn&L>nQ#cQV=%Q_?`6L^4S9,".*?&.p*Q7#2*\+X0=-L_(.B6:?V8OoKXH2rAgs/6<#V)`C
p?Qd#uD(N96B?N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="115"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="272" width="375" height="115"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('CHART1').children[0]A.style.borderRadius = '0px 0px 12px 12px';
document.getElementById('CHART1').children[0]A.style.marginBottom = '15px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="CHART1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="chart0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ChartEditor">
<WidgetName name="CHART1"/>
<WidgetID widgetID="a4fa5d50-e297-4123-b870-4f4985d6bc19"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="chart1_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="1" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.radar.VanChartRadarPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.VanChartAttrLine">
<VanAttrLine>
<Attr lineType="solid" lineWidth="2.0" lineStyle="0" nullValueBreak="true"/>
</VanAttrLine>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrMarker">
<VanAttrMarker>
<Attr isCommon="true" anchorSize="22.0" markerType="NullMarker" radius="3.5" width="30.0" height="30.0"/>
<Background name="NullBackground"/>
</VanAttrMarker>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrAreaSeriesFillColorBackground">
<AttrAreaSeriesFillColorBackground>
<Attr alpha="0.15000000596046448"/>
</AttrAreaSeriesFillColorBackground>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="false"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="条件属性1">
<AttrList>
<Attr class="com.fr.chart.base.AttrBackground">
<AttrBackground>
<Background name="ColorBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
</Attr>
</AttrList>
<Condition class="com.fr.chart.chartattr.ChartCommonCondition">
<CNUMBER>
<![CDATA[2]]></CNUMBER>
<CNAME>
<![CDATA[SERIES_INDEX]]></CNAME>
<Compare op="0">
<O>
<![CDATA[2]]></O>
</Compare>
</Condition>
</ConditionAttr>
</List>
<List index="1">
<ConditionAttr name="条件属性2">
<AttrList>
<Attr class="com.fr.plugin.chart.base.VanChartAttrMarker">
<VanAttrMarker>
<Attr isCommon="true" anchorSize="22.0" markerType="RoundFilledMarker" radius="3.5" width="30.0" height="30.0"/>
<Background name="NullBackground"/>
</VanAttrMarker>
</Attr>
</AttrList>
<Condition class="com.fr.chart.chartattr.ChartCommonCondition">
<CNUMBER>
<![CDATA[1]]></CNUMBER>
<CNAME>
<![CDATA[CATEGORY_NAME]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$name]]></Attributes>
</O>
</Compare>
</Condition>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="3" visible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="flow" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<NameJavaScriptGroup>
<NameJavaScript name="当前表单对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="name"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CATEGORY]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前表单对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="name"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CATEGORY]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="chart1" animateType="none"/>
<linkType type="0"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="normal">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="false" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return &quot;&lt;div style=&apos;white-space:nowrap;text-align:center;&apos;&gt;&quot;+this.slice(0,4)+&apos;&lt;br&gt;&apos;+this.slice(4,20)+&apos;&lt;/div&gt;&lt;br&gt;&apos;; }" useHtml="true" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="false"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="solid"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartRadarPlotAttr radarType="circle"/>
</Plot>
<ChartDefinition>
<MoreNameCDDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[data_jzzb]]></Name>
</TableData>
<CategoryName value="指标名称"/>
<ChartSummaryColumn name="得分" function="com.fr.data.util.function.SumFunction" customName="得分"/>
<ChartSummaryColumn name="中位数" function="com.fr.data.util.function.SumFunction" customName=""/>
</MoreNameCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="f0012592-0f25-4b65-bfbf-200eda1a32f8"/>
<tools hidden="true" sort="true" export="true" fullScreen="true"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
<ChartMobileAttrProvider zoomOut="0" zoomIn="2" allowFullScreen="false" functionalWhenUnactivated="true"/>
<MobileChartCollapsedStyle class="com.fr.form.ui.mobile.MobileChartCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
</MobileChartCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="230"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="42" width="375" height="230"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="1f447c8b-fd18-4374-ba2c-7a6673c76356"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[228600,342900,1368795,897774,342900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3448050,3448050,3448050,533400,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="3" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="2">
<O t="I">
<![CDATA[6666]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="3">
<O t="BigDecimal">
<![CDATA[88.88]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="4">
<O t="I">
<![CDATA[88]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="3" s="5">
<O>
<![CDATA[财富顾问人数(人)]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="5">
<O>
<![CDATA[成熟占比(%)]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="6">
<O>
<![CDATA[试用期(人)]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" cs="3" s="7">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" imageLayout="1" spacingBefore="5">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="1" size="120">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="simhei" style="1" size="120">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="120">
<foreground>
<FineColor color="-878336" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<T(8;q]A_S2RYLn?qWH$,^([tTd[5i6q'P!!!7br+qQ$D1.20h+sQ`G6"D`ZLbo;E6_[%^6<
(*1,QS'8KE+l;KE,F7";6m1K2[!O^ZutJh>1Kl.S^]A^L]A*dOf3WgkDlpGArls$J9R;?N"pkS
BC%F,.m3Et.@97o,=9nc%b5-CGKZ(hZo9738Y2i:eQmXC?qUA%2@QjKRdUTItgYX?tGX!M!r
'#)m>p="Vq93o'<7>P-O%%AEd`:LP2r:*?MSg?\JGuS7QL`M(ZhGX-5H12RhNe*+?AkH"^d"
6Zh,7/ISl@XnSLo87d;Qs(P%Y<pRG%I\:"eQ6D,-]AmTMEI\jN3)X&&"R`\5D8M]A(:=KIR#WE
FYpL"oRaK(6q"]ATMXD:&$@`,GJ$`;[]AHjOjIOFYta,.LFhcF/l#KEQ]AEmOKqb98\B^6%gdG"
'>`3<1&/)>Hrnioq`hHr^#Ei&_7%D*cbECF(TX0-YoFUZoJegr`,U;S]Abf2oSOAl:9q28TnH
jVc2/-5ae>$"m#:_\?Aj9X1i]A+^Sd^]A-$BpJ$&H[-(l>:te.mFqqQ*#$DT)I1C0#OBNT-O%n
J2L<0*a3L:`91&j]A=52\U1Z+PTHEJr%.B\rUZDN2?TuBX+/Rk;Go*4!50WS!Gs4SFOt09I9r
bcY`APZ%=11anEU%fiU4H1$s<7+ad6=p!t>k+^M30E*\RohOarF6AgQFrP(C5e_$mBUiY!ES
BLQZ9W@?-Jj8rMOL8'e*F@%]Ak5ppqBXaN#q(VKElJ"=0REN=?.8cXIF8.3kr+!:I+p)j(]A:J
1c#Fkh1i0a$jT*B8["p:Pap5H<5JmNf;8pT)_j<]AD*%V644HB*N$^2(0A]AgD,kA:!Z2ld]Amq
mrrdQ-QNGWjhg:5X,Ed%".NV[hj<Vd)XE68%*W-K@eoX?]AdpM8#`85>KlV.9dqZ2&[8oZR0"
^N+Pid;@O;`cb<*VP!;V$hDDfgVMYfPYB:+)f'l*st($[poZ?.+qi<K0T7*%;W;hZb&7Sj,/
BSJ,!MWd)2"cYTFSZ/*O=M\Go_R0`U"#s.Ym5=&Kq@B<jMg:G#8VEa+Nd:9YB(U83#d98P_\
T#o>1PuL1uOA5X64NiBSCZoVD.S6c+([0e1(TQj4[lf^t*nW(J=,&>R3o7`@3"5<>VC$Ue0J
5km8LM:*/_nd<^.WUg@ft9b,Qr/pU7(,Dg1+[\l#k*AH"-MHnb^FC]A"M^.mRQ\>f$T4Epdg)
a4\IZ.lCqL@QQZK5be,Xah[Y)/!pN/V*S'U3-@6f$jSR=T<69\EC3[LBRu@fYaZ#CjY!h1%/
"FC&1_3h#A,8/7/ojpPRV6Doo9a-]Ajf->@Wm`Y,R*'_\j,NW864A:Q$)q]AR0_+Ac''k+]ANJT
PVN-"EV"b3+Z/s'ABp_nEhEb(mtjj[,re!_D]ANRH!aW<Fp5(HNAC$*L*FeoA]A4?R2KTK>E8F
[F1B4TsGhfE60i'EaSdEoP1n6qt@bigL630@8,I@oJ-YSF*9f2Z]AQgArfu,#\eS*3TdI23@j
Pi#`(<AnE\Y+.1p]A]AZa>m<RIm\A"DJ5.1c!gK%p`Bi/N)mrZcM+7G)2AW#3AHd76`-2&]AoBZ
+B/AeNE7R&J?kElGJS9QI%;0:nkX&G:)uEW8NJq3HZT'!;"0R+HHr(e#W^$n(hlhqPV+gO.b
/i1/S]AEd9))2:EH;-;D3;%`D]At3/Zank<Y^e%VD`/io9_ssbc,;$Hum/-i#NgZgm]AU(c+!,J
09K_-eG9bm*ggscn:8`GY'[R;S]A';fjVenqc+Ro(F=>Jq7A?`H%O"KcYMa1Ku5iQ2iJiu:co
`ZK<_j6As;>2;kCZ;H9H2@#LIl-BVtP8I.Hb-aM_RRC'kpq)F@;V;7TT$.0o?XuY//NZgumM
Upm5;%KGFLQ*7/Z.nn3;@>CK5SGC7H/>7'mi:%3QXaC2*jC#lO_aKCug-q1<aBU_)W?n'_k[
(U#4@qRMn>g/U$gXnd=c$L_F.n\=cWZ\REJl;d*+;I9sa)UZqqOa.[jcS<!tjUOql]AG&]A7CA
tL'=B47?eUJH=oah9J+iq4s;n-Ro[5&%/Iri<VQd\E2KlC=[1R_l0qeKAl'Ke#sO(iVlm9`?
ts:tf?'Gm&!paBl%d6L^0sXrX31N%fM>bWs0C*T`'Rq9;4uJ5%`A7_cFD99\3.P0AJ>EO^!c
"d5:><q%JD6F7kp%Ec[ThF_1aZc_C1KOY>4m=KhmpmN]A'K'6<6FQRmLel``_Ye_7O[d'j+_5
H9')>94F@XZpkMC>d_4E:L*XhC0lkE=9aESHW)h"W=On87ke9:>cs6i><)\0/YuhYEoSbkP\
7&7$i=r.:"tIp_M@qLYs0!)UUZ>0)k0!n2CS6*D\0Gima!`ejUO7k*J^3EmBDs8:N8>Y*+$1
u1(>ILR-(8:SlEj-_h6p$fJi`_,=h+[_YM`WA1\;$h>^hIbFhiRo_MNCgja3b[7"qCerA0VG
ZIiCHZRPar.3"6TXQoEIRVo;/q;YQa0B.*;'CK0;.U+6(AWCKgVH[2]AEq>^Jan.U<*#6fQt9
mLbf;71/2tRRoNs/>[\<_llq22\bcoQd=3lV\0VXCHU@T['Np9Y[t,f`I@!f!'pU&gLL&R@^
f,$a6rnJ/;6s&Kcdm-m.]Ajl]A!rZ<*9NK@k]AIKV@?Kn:5C3R*a4F]AoiMF26OjB0@6#4*Hs%kJ
ZY"Hcb/k[;Zo2#e$!X\l=AsR8?+.*d>piLppU71S>H$2dIn<^+tICH;b6>2K8b,!ar]A%J^.(
/i=7`?TGUqj4p$NRVZf?gNfj04,mnVi!<BqT$`F#hu@HqOK]ALf#+KdjiIlpCd[m@08:.1>b`
Vr9V7l(esad=eTh;%7b"8RE:rf09\Ij]A39/i,=$)Q4.hbO)^0_;OS='L/X6c2dk5MVXY1pe4
.3X\NQO\Ut$%sL7a+!/McW:AuGt&C`)i#2,N%Fl&T7Wg5beiBng\#he:4r^#cFg&&IQMS$)^
!"T\O:^HKl)iLmOP(6r,Ko`8lkZ^TW!NM_;:Uo,+up%Qa=XCT`R#ijg1V>?i*!jddMtK_G-;
%QORo*[U<YC6Q#Jf.89\S%KCB=EE9L6-KQs0(dKEfO@JKiV>8\FU7;90lr&uf,=4t3;P^lfT
kH^Hog-s^?l6/Oqu(G@3JFT-6]AEVh5hro/fVeN1G"j?i?p%Fb/cpj'$8no8H<GqTQ4uJM*Qc
.0H-VkcQl(C_D/4h#r!N5de>lFhG'[k8aqO>NAkd/O5Y&7#9F4hd>$mYsC9tkZf$qoI7OO[\
#S!R;<0Xl)T:GU>UBKTjVVm`In`!VjR[Q^j4OD.3V7]Aj2n?VM$r*c4C)keD"E`>#;Nao7qbu
!4kRKqO@\:"i+6,-s]A=mg04K\q-`gf_XrJ<MjfkXb8JSS!)0:Nf$IFV+Vc!"6bkemrtpXEH!
]AT=&e*&&f4\`:&_'NaVThS/OHFpZAPq.O$%De&/n19o6:l#Mg=P`rYaPc)MaLm^]A@FR&*2E)
8K[\2(U,@a5LE,%IlVVHF]Ai_OYf4cGP/]A&6m*.UR/YXFSis8DC=Q,c'.*CZr&CcPK4@X8J7"
'qVuE#@(3XWN*X</!rb%MF@%"ZT;Fflm\Hj?WN9IR3eD?g6j9g,F1?`<?PPgMVeQQDS<b/c*
$]AuS##]AJY^2^<0u&M:\e]AO*63ruc8Bl:)-nX\U(rCF:5'dm0Rq3%>+ZYM<$,Leuf35X'Z_6f
]A$"'?]AT9">k%+7WgO'26.^g0#%O6di6',V!XZZUVVg-O6)YgRWo3^DRU)'2(>fp]A8S"/s5D4
XZ5'EQWOM.c=/$t_`Wd%.!FuT@egh67GqFG=B:1`Xnd<mPEcIakU'GI4qEgZSp**90]A??[TJ
o%[$)ehClZJ9T.q:'&+P<XU[lrN"W&>;qP9Mk,c'/$rSC*jQVCOnK"63aipDhe(Yb#MVehDZ
sS7QNU)L@F31gmGZZnBPA\`![Dn:s>j*m5AjX05$*=?V1#oaFdr207UC9<+5OW7KYqQ,g4]AF
BAdBsptaM!+FVY,hY3G#D=["0f-eApmPZW)IGb:e_Rte#ER)QZcoR>;#MFe8RUa-^<^+m7^c
IQt3&\ngpBSmFPUQ8.$6=NlnSWCH'e(5,_0q@>L4Y::L-[MWG!ZgPaf<^-%D"X`d79_Z<+S"
Qr<!fjC[b^ob'!N.0D+'FA2lj!WT)@;aSD.:UfbusjdE8Rm\l7_`LFL,<kmgQ+D_(&*2m9r=
48;EGg)825Ab.-#jC-k0JJG&*_$g[*A7'?'m>qI=9EQdak@'jN>s?U*d%C;%\+%ZQT&(,U'W
peBR`_,+0g*3oJreMC[@_3H<Er;![=uk5*f#5O&#iiJ/">nT(m(Vdt?<EZm5-b4#3t)^\DDg
,!jf"+blmu[X_V)<oM*9`_.;8"Z.(;_%SOVe8c`99njB.%NRSqFR6#PpsA^7@c!mNb-ESs]Ap
T^<)*0!k+:f).94->M<Wp%(oJD;B%f\bq)u`2j1Rd-%C2S(\hc9RO\,-$k'iCMY\\auoMuMn
YS8FEpL>LGD:P$-lFFkGj(P2M\`3kaY$C-#lllS3V19BUr`SOn4;DldW[u2r'TCs3D:>l%)L
S^8>bI@CP`DH'=X!%A2in3ZZ`OPR/W*TGtMr8<>mDLb)Vi2ll4*R`/oQX?n7ZK)W'_O*;oXa
OnRWH:(-,!I:H;N[aD[7kpMt3IY:EtFZK:_3^[QYPg\K("n!d>6,n9CKAq_<WP>`hQph[(no
EIWMt!jFYLq=TD1P$Pe@Y1&qO1Dk`XVSEcNS5(-#$j]AKPLiOhk^TMRG5"fRi,+,0"1t+^3OS
B/!1>>.gNa&7J-mg6r7K:BI".60K=AbE@R^__.\>b%+\6Y@m'15278VZa/rN?@/&K`%'m.Jo
jm'+<4fFtt4nk,ZcoOtB[b>4Y8]A2TQK@X+1AH[B<HQ?n7[)(.U#f!AUX=agE+d4J^D[I38Q[
JH`,NX!OTRXofDP4L>>M37MeXpiX@-TsDC#%^`t*FT[u:2EGk";lorA?qtT\U.i-3DFN=6"I
:`<,-Y&B_DU^Y.?cn<^r5qi/_2A'h0a[%GF6AfJk^*=oWJ[Dq6sHNs`o^D)og9-rd2OPp>Eu
p@*4\`_BW%il<AR/9.W_>?K*9S6Z\rT2e+3@@?U<)DAn(kM'7nQg40OF[!Z]AP[rVb$oH$_W@
0d*U^"e&EQ4'[-(1^AUljdRHR_Bq>PLSqN4kHF;G_7$eQ,d^otdJFNp:LU68\B3EE\G(Zs;I
4i15M6B9Pk>('3N[6,\h_,LV(ti)0@(@`4/%6f3p6CfWQVF.0g`@=H$@j\r=96d3mK`&#$n;
E'keH*AJ7+S&\Cio5^cK[MMTC,+cF+K*A-R]Ao]A/&u]ARWg_[XIll3\&]ANSf!%ffA48(@sE84i
+`1fKhGgYmj7O1dn1+"l]Ak(X=Su/Nhn&3)IQ;DOjsg97;dh8$5X!0Kg@a,Jd&<<=_&jBGgTO
=5;2";[+>3P,-P>5u+^4U:GZ9[ab1gjC4jG=!-4MH'I5-OB5Oa+=(;sEo@@=RcdW^F"ceor7
-W%:H;R*jta?'bL7OH]A-TI.Kuo$$"R#:N9l1g(a;o>AWGuVNOsn&@M?+Irk4"g>WuE6#JrE2
Y8=iS#Y'sWu@4C^3f@KXNFF#qYj:/\=C=g?j7J`JaSdi`gTO>MRSeH1KZ/kAE#\aP^n6Im2i
,40UPGP&I;/q@tG-E`QU]Atb4Gc@B6O@.kqQngMn\.IC>gYJ!j]A'e:c=NAf=Lo7\:M0:LS._T
?kH3Lp4Q=VAh%TiS>_MO!&YT/=j+o@T?,0bjYpsI"tXcZL7@VRF\nl@go*Q0Ie2")%dH'dTf
jJZERmR5o;HUc(T;'T+`#'s/o9;qEa2Y07D6;((&"^,03#V[d.c>0E:ZCVMnkagVRp;WV_X,
7B&'MKdPY[^5B-SFV[kf*U##Fl%qje>8molR%:[toH.3B>?gCBL>A%B2guja2-T^.[Q*p3u(
[p,iB4O@LD_m0r@.\..LOV)]A-mm//^6mJUgl5%7\fW_8jpMfsMF>X5g.G.FpJ9h[4G;:PJSc
'1Yop9GklV5A2!DUX9in`N>!;ruKL%uEcrh1i+PP:0&kJ<;L;G@.YcL[)[FA]A@26Y`/u1&D"
^-;=;L\gj!fn'aj#WP[MW8H*,WJUqm,().uRACo(D!_j9qU,Bb*("<m[*+@=^%ipmikm\'QJ
;!ZX&2F2RY*sU9'j3+#Cb.U[$mtuR&GG6C>%OUB;k)j3+dHIOD<Nc<"Ns-cOS[[ABp`.t#>W
i.hok\O34JT-QB26sN4jusG*/u-)Xhd]AmC;84=9_\8(SoT6cI,-F<TVlQ>bK[Yp=$;6HfBZ%
V9gj+FU';Ct(c&s\MUaKt/!\GCoA3oQ"AMJP6`NWD*G[7=oT/`eoLTPhf(6atbpcn874lG?'
b"L[^X9eYl/\SHO)i[XM?5Hh!0d"6q;Oa9:2$#iiZbOlqLaCR)I(8qG;,M[X*k>WVP"WoE[)
mDpIki_=G&<YWn5bGJ,I\!4\DSLCT9jXi&t4pj?=?Eg.#or]AWd#B^/7g0((9kPdA^.cVG4ca
g9K5$NMA>O??S1tfOC]A#..-%i^FSB,;T`c:+kU6[o0"1B43&O?A5[?p*_2>HVG]AY4Dd3ec22
I9S44m%[,0Y\^oeS1[Z!q05)QZ7c/nYYoi0"M5K&n-i!W,$g/Fp637V3SIcg;d-((:.19CH[
EqXSJrCE:[Hdo+i=h&c@)GI6\tmLGR:NUh5?0lkDdN@O4.4r0;O[8"']A]A.Hsd9A(;9>&nCd1
3H5o>]AiD$[EN,?mm%f6Frr"8]A,)]A.%?_((`fP"hi1kB=8cdK\K@ssaP+.mpQ5q4a]A=_+08qW
VmFC$c?/+S7=Z^V"7#o'HdbUe23CJ!&97o/Y-0E\&\Ab5,K)8\FcRkL6\UB:%U*TJ)H+4V0d
eIN#9)LA.$lY?aOOe)I.l>(n$FkaL+Od2qbo"\$X:`D5D9P;S`9$`THk<GXVM$g@VBBZ/tQ'
iPsGPNDDZ<.5okA<*A<4.+\NV]AH]A]A6"7(G.?+?!]ABPE^ij)DWHf0B*4k>:mVF/+4guqn'Gbb
C(-CgA:HN>p&`c=&dbCru6?e31$rPmdRNu2*RSp@TiA*-.H0QKM$UX;j&MC3?Z:O8`*O7,]A,
?sm@q,B"=C1LXmZ_@4NPKU>WKA:E1JK*]A6ZJ2-Yp?/7qNU8hMN/"(d1HFMcA,C]A4B$jRZBST
pIJb9C<oVK.Q#':X;kt\S^G?8oDL`6!''SRE(HMV<VU=dr$k-7p0:n-8E9#8m(gPX^$ZRUhu
e,@iVr?7t'#J=m(P[&P=GHo((XX;[U'8.agiZ;/]AVXs4To-C<X$$(OjYa5$;m^k<V!cH?UJO
uYuH+=`$`W^OYrJW!-4J.>b3Suk4E00B/WM)J",:Jn`QSQeP\M36)RSE8,MMf($]A*GeZLk-\
fRr6^PBle8lfRbX&.DFO->;CVn*iWe\"ef8cE9%N)eNV/k<^)k&1=#+)3m-@gUIO1,i[257l
ddPV"'EVGb\L_GI]A.tBH"?[CcA:IBH=U)>]ApTXd;2AQ,#T;KAbqR"u%sp+?5LHW*VpI_)SdU
3Og4\@^cI_t2@Z#6]A=tG@r/"EWQdA^:$*C-S*n$mujf#mZd</71EHI\j)cY<d[1,+TsV]AfAg
58s+87HZh!;fplLXa6[.Y[u50B68$/o`S/X8>NEu!S24ne'\gP;9NsWT>8eCHoRO@_sW=#Lq
.l%CY'eOCdl2!D"%!.]AN0Ugd56^h]A.^M(4\q"iG^!faNQ,;9NfRFj8tb0eK?!YNp3KQ?dZFU
dDd`YsN'c"9Wnm*cYt?(f[0Z&S%$14DZo/dlVqtsbZ<sGD:@P0&Xr8:(po\83#u.X^Eo/g5%
U\,tS)V)r_R4$lHg&sNUM)hi2s!Tp6#CrXia]A*fIGQ%<NTWAETMBrVeq*daHp+4u4T,1mZ_1
R)4:quUB2CfMdtK^s\,X%qOi+s##+a6j6@5hR=CDJ%[C2J%^_Z2@dOmQZf<*)C!n2H6]At(Y]A
fRZu_^YKE#]A,Z3kclj@glCXOf1@9?He&(uRj,KD7D>3j+kncBG0)L`mFaBWNG"3s?YWS%bWK
acUI2T+McQ+g]A=DfOk/O^\orH*g]A$V(<\[H4,Ylf5B.rlr&l.?Dbrl+K0L4\CR<]A=\GFpa&j
)$9X"*IRBJXGr"n\'det2<@HD>CT2OZ0Ym,B$M&+W._DY/!(`Og;31/nngQaak\[Xkh>H#MX
OK9m3WH%_`f:U@M9!J^:813=OMd0I!+Yg=\#MJ$%P0iQZ"*n3.^I_7i=>:NMT:%b)E^t,<=i
I38AB8r+u]AnC?&`4uGr['^:re]AA(EeVC:?D,";/>S\(G,6k#F24.?uNR0QV(\5@_as9iLc:u
comMRDp`b--G,n19>`e</Mf^S)JpfDI$YL+Sc(8G:o@nt_q@A3CT'fta@9INC6?5VB-CpZ-D
[mn!jt^[=2\q0IITH2!2D=A6%n'^D3pf>oibrGP<@.FBBV&[qa24<)FE]At'`W5$1H]Ar@ouJK
MBUsKg#sCAgnkEY.5,4l*<uBrGD!OC_lu]AWCcDfthDN-+aTZ/e?C`NR]A=InT>I3Jm:8)Yl5Z
_=<ir'5;JiQ$N+*pc1o#gMr[%$CC(:1Td@Y%`8e*/X(,;q9WJR$3Y[(ts@7/D/>`%`VM[i"!
oc/85T4\6Qc0q'`6#2Y;uHWSU_R7HS[t@PPPNS8")*H<_AE#EPo.2X$iH[3ta@,eF&U>Auf$
?,db><^frkkMFE@PP`ouYF1>!kR$$e)n$Fr_Ym-Ak0WePEXuH2femJlFoI"VZJOlZnZ<>*\$
?_W"/N/)Y4onSS#/m\2pK4;mSA/O=c<J9ke-.pR*OS.kqbL-a_jI5/Fj&'f^A>lO#NPtX;^#
?pqI_QoGR&$i)qDj;0^aH!hml!oH!\S'fJH%\=AV4*$J40\:iJ`Bk,/5n-%2f4c2*j@7]A5eT
"B!X`I=/dXE=fZC[)?p_9A_s&\Q/,5$X5Nff0d1:h,\"d<fH`XaUoj6/E\WMd%%?B,FJu'e`
u:(/$[/[6]A*iY$HZ*X(th!P`Ngj5"F??LJ"J.6#%cdQ/*Z8l(\;0>"5OISk%^TGB40B>,bA<
j(EpFH5]A[BWd'*GZ./9Z;_,0NHkH@)h):0X#7e-j[Yf:H/fHRfH^4!!]Aie)jA]A'!O+s,<Xh<
n5;Z8o('3$<RKL%h8Tm\nZ@ed:,8PM58=37frr\=T,:Enqe0_9Hn&b/dQS#?%foS-k9j.G[q
d^s[r,9lYG]A(ef0o9S);#9(;lm!;pL-l6ktQ=)nRgkuW.MjA)D-.`aefZ]Ale?HYVE>b<Um^-
"aeq1qLqUh/S8!E28DAs%rh<hb*fDJ/g@A0lgVC0\#^.>\>BG-!tb-%-tiPQ'X.QQ@Do(PAr
Kl-85OR#U,WJ$Thu^W&XIN'-rAsqH[sM"2pBu0gm.^g,\?s/2ugK,+ZfRn95n9jS_;H<+$6R
)J[@t#Io[[!oo[fZqPt4q#S/na:Sd*XLrF@)`,[OU9&,>=n'\LrWHhD^Mik-]A?7*-#KRUqJ_
g2u6?cH,P-sSM.&eIE/&#H$@PfJJY1`e-d7:*)S,I-F>Fj?j:obb727DD-l$Ok&I2K+YZO7#
T(/6_^.q'fmh42B?ojedsX-9RVj3MIUhbR=gGjTB"$XaO';fK3RFpUYCEe5A7dIC+3Qe02qc
3jZ5.d2TA*@*BW>:cuc..>chV`&I12.rZeE/jguiKVIf1hJo]A&u42j7!YZ<K3F["QP[BX;,K
.+@f_@DY-o!O%WS'YjtnR=BkhiUm3r!iLu4naJ`lrKFmn`p:2^EUbUl)nclT4+ajM#7`(W=!
.5W;3=&(2[4L6tEm!P0Ac6hECLcpk6rJ>o19Qac&1_PMuOP@AXKgh\UV97l1ZV!H[Y5TefH-
3>GG#j,KU"4cJft,PC>Li(dA1lKYINL=T,uDJW=KeEgodphYfr8'^nVJopIP_0iW%.HOhW!\
4eG'h4%GX<`?D:[_oD`\ZGuirjc8t^_l)^OA8,'U*C?Z7Za(e^g2YRT0lIHK@5NpBW;GDTsf
&U&;cPSZP8qQ0hLbQ*.S/hT(>@Zr80iJ7pZ2!>sl9<mAe4(O$nkpZd/`_TWn<3,>.UDa9Eqq
(^0)G9DE9r2*:F"9I;X&SNC(L>klEN"O*LZ&g*q^Z!#^QEKp(43D?WK-8MiKDfH6gMl?;G=Q
$]A*8hH0OW_EZO5F&j7Nil'NH1>;E(l6Y]A83kU_'*W;E.!pki%pQCFVrG89`ajr2;,H-7Alq$
T4skArOh[lbYh>E^q?CQL2cpM$6[IA""!`P$8jh$d?B$IrDQ`luY]A0!P4FY*]AQI9Unb2><qn
]ATG+is\4[K*\/;XL\[[J#:k.e534'M??-g3%QU8q$X,M^^&_8Eq`=Ci3Wml6t`=2h%\l@lB"
UY0=l\I]A,`R4)OmCLY)+eG%S%4:>peW)*N([3tu^g76&mFN_WmZ[ts*eUq2SHdoF@_0!fUp4
,6qC4VNitc%>eRQ((dMa:V":3J9ZBKP,EJ/sRM,YpMo36"@LWB+!294Xc)*81NIHj.r&*ks.
'-$eD+C6_*^:te4+/@XV4,ia-f$Ua<P%o3Del_g.,F#BM4WlP%g3q&?BE@1:$DJD_;Pj>F,j
:RfWFk%!;Ra.#qTbdi.pFYS+$#<r%@lVA4ZthK]AbI/T_7#e%#O(6!Y_Cnlb^N?5*%0j>b8MV
eoHIui[)Z>K%6TZOS;!Ce7HiCSh>ImU%5nnR%D'CLJQ/3!/AE5:3=G7@Q#0D<0+5Xb]A^koOo
:i5^T<l53,?sFb\Wp[lA&j$#=@=2;=6"iqSo>3[f$Z%T0e(fH6p7m,mkSn8h#oVXma9HG?]A=
*;$umXXr91Gj^,B7?5&r@ZiNO]AXs/cUqf(BF/#8uU)[<eDkATOe$Z>T,ukgcM%>H%IlE\VDW
$`nh9,SZmC>,Or0ir3k?RpGM(YE9[2Xq.bD?mS/ND>Sa1RCp?;p@Vt6GnMOSJp:DPXKcd'MN
.@-6"bWG=o?r7^=-b=qJ[1WoOX8.RjL.kMM<%)a]A\E4fM08Lg?*5G&Kb`+BRnub[?NX"o"^U
??YL4CMY2u<Rb?bejd970kusNc$W4SKe;r;:MlKh[A)`(I"#Fftin%0&M>S>F;r,FSM(*j^S
_q'%Vkp4n<fW8e+WZIAkRkZt&o>aT;<)a)I;QYX%u\dC(A[!D+;.q\2daq?kr50boCY0*H,K
OA")Dnaii7Ue:k]AOMa2.)&7?^7;rlt`j,$AS`.#H:,#t-81<3_`J:B(Fk:c:s@UQ(U,/b"6r
bO+gWd>')K`?KK9^%AYobU%\kj#@'7]A7[t_f+&H2n_Or&3GNn(nU:qdHFX[#f@WELLfh8!WN
4Lj#ttc<.D%.HqVigefU#Xb\m<4_R$riie]A8TQ42X4G4#s,%Z.\rb0afJ,E4_;RN]AhZ%'+I7
+egmcq;ajc'Y&C*0CSjPJ6/%Q)atWLD<i^]AY&ntW1D%a3(PS*1N0CMcP"0?8W.-gd%j"P_5@
u'KccCY.Mf(Ok!o/a5!rH=(i[3XVt>lR&OOSEs%8e#%Fkd\PZh5!p<<9;i\nIjZB*!KWI[`d
aJJ4Ddda4p(:B^9teR:5[Ok3LWR8!b=BTf2d8^)sMk^GkWK:.p&T`u5Z[p:>e@Mu=`N2THR9
1ekRR=:-mbMA2,&KL<t-),,kBT9ljD5-C.'']ATE;IY0dGclGj9jT/5u7UrYp0fcRWaZ,j2>[
-#k*2#C$M<LTmO-f$LR)-:*_)D,$]A:HcDM]AFMtn^@A>%-gS$"Ff6d7D1XrUZd^_4D#%5as`O
%cA#gZ8]A=GK6FCq4nh/^HGSELXC7eL(<Ak79Ob*Ds;PREGa%3CqQt=]A=^HaoTVa%eg$%hI_C
hZ93+D"XY`si-5E^th.N\Qk)"*I+W;+9p57lpMG'T1F8EA)ZO!_DXm,Us1XfQ2imHrBh*ki_
%gK9'_!_il1UZlbfU/MGT"2jc:5!?%"%V4MA]AAFCo\f:=E1%MT+VG2,G;4]Ajm=_/V3lA9ika
rO:.-rPVA,Q?pK^A"7QP8^I4arRqj]A%qsGddp<P[!QCW.7NnTh,sYdV&Z)3:38U=G[Vm<$iG
_R"CdFo7cU.B9SO^]ARH5q>eaBm0&q%9^\lU9nY]A\TT(nBl?1ad?\$i3qA*SiJ_kf3T+c8c97
shR%1L,1N&2H!t6cr28B8#NI8$*SG37Utsc!ZaDT@/%g;k*?^FEg(;7LVGIK=&$\,/n_n%pH
FsX%RK&TZqL)0W6$Hqrj?G_oqJsJp:Zh*Hs5bC>L%fLHl2%\WIU[6Ol$pb<>*<<kKr"1.idn
aX.:8*_+J%L%eCUb,d!aX#@c@8C=(@CiT@;a,9-t;G]A"X^UI4&IuogWbB7W8CTd8fcihi<gE
53RAU/s<_Z/b_15T.a1a"5XNUs4oP6$L`Il)>:+f,VImJ4$A.L^E5`%Sud'O]A>4)0^6e%B;A
Y5^r5E/$/&n\./LIS>mK$)dk**K0IboPi+X-C)mS?FT3.k":!hL4#Dr4)Vm.l#^#oI@.-gcH
I+.oYX+^pkL.V_u5Wi/opX.iW`Xbp9s[4:JAJLI4:ZM0]AN0<X.F'AU9E55]A<eB9>ul^Bf.Qn
`BAaqd2NB>(s%E<["N*<QHY#'s\0LKrdUA+fg]A)a=:^8Z3j`gX?or2X-!@0a<SHq[4B"qM9&
PN.8Mln]AJ9Gl0cFWtANMaKb9lG,h-Y>&=!=W+<QHY#'s\0LKrdUA+fg]A)a=:^8Z3j`gX?or2
X,p?&.cc("Ej^sAq?_FPlrjAdoS)a8GBs3QLX\uLX8Rk2>JF^`nn*?nqBZ'YKkp\n~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM02');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM02"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O>
<![CDATA[cwfx__zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="财富顾问人员级别分布"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&B$;Y_*KBt?H.B$TK0:jbrtCH>NaF3o:i'kM)oig?^EQ)U`322Z&SbhR)2(1p:,MQbIT+G
IrC.=m\B3i,BD5_]A>O8`G@:!<Ic;ZShK[qoA[$rc-OMCRqlIqfb6ir:^-WZ[]A4QqILc>o.jQ
^\dAQA)'7E@7t<kJM6ls!jYJ+jIOu9W&A7`;I+JmC`?Y=iq#9@:p2Q;RQ`]A)N'u#1ZVQW`3V
f#7MEaJXOV/*<[_g1jch="OeG@/"DI$?2:RPsuWgnNsth50df*\ImA@rbC?.3"b-`J&M'UKA
t1-2p$5#iYZ'NX=K;rV=<dBq\-<lp#P+\g1M50:VTfJ7c!L4oRHt[Xr.rnuq6CriP$(NdLN^
I5,Nm.Y?DC<W6tM0ooYMNpci!,[`MMcqd5KM>4s4>J-<H=tb9Ho,Ig-_JSGNV?;**@P6WAQd
$H+43Pki*D46d^Ai-(Cdd$oCL7\:*dt+@A_R(8[2q<lg]AQ#WkpEO[R^nQr@.qnNS3:/e\"D`
H.J"I!<`itjf+1+'kbrIe&WU,Ffe&bA/]A-)n,\kB:r:XLZ*6=%V]ArTLe8PL4N*KW>5k9AlQ3
-#dd5.9.Op'(R*YK*Q$dA>&/4!!&HU2iC8*fmd]A/$R`CSJ'42+77IEf#o"k<5i/Zl$&/ZD-c
=IQARU]A[h+d(H)^&MXJ1rl2iJ0PECD<W4gZG$0lh["cj=SaQufeq9$RaOCh;shq7$uO_8Uh1
iqAEgbL+Z:ZC`9aJQluTl_-;$r[rO:5nk_r[]ARd)@)I.l[\r80]A!<F60fdb>;&@hY:gujXY9
6rF[%!i^]AIYd[\>!5q0$Up&E:o?[m`\AT`,T@*p[khObt?pGa,K[KILp$\mF^j4K=F$BM$iM
gN`CL8.?-G:&#i$4_L2h`)kOBtn:U4f*!@09nSF\d:XSU%+pZqNDGsR5W_YD$@K<YJQ_5R^D
<EMAIL>=V;(`sHC,%*$SgjeSP%\REYeAbD\#e1PeM4V2(VIE35)"Foh?DV6'=
A./J4aT(t-T"@YS2$=W*"(Q):*kO``]A#[;W94ntN^Rb]Aaj>-`\bD%^Du6A5=p'JRYtEka>_9
;su>_#T+Fc$s4I_os;,)5AHS@Sfn'c1/+_<ebE?C1XeNhbD//Od"7@T7rP`8]AYdZSmeniG)A
!0ql@b,S>N>p8;C-"N@NoEM_9._CZgMr(_rJo06aE:Kru8t,4)]A<Y3E&pOC_ipG>ft)uSjs-
YVe26sA1"4g(Wj3-0b#)Nq9i`G.^Dk#"\>*4e,FHC_-:JXZ@Coq/"\"8/ZCrkWEH:+;Rk$]AI
[D%pKfBIgU5[K$>QtQWrqPIJ@/?J.,aD&n5B+T$;N0UiY`dV2,B-i^+=@)9]APfbAeJk%iNJ)
u9SbbNG1-'2DP`VlId&S-@RfFJCYC'BT`T1B2d.I"VV@7D&k`%tSCJU%YP>'.GQUpE66RXm,
BK1k_oE"F5ob$@kAR1qLD\uI3B`4o=NUMAiH#?.<gG.]Au#3p9Yen/,X$DLDf-DA<Wo(^2i7q
\UFLPYa/qnk)sR3IOG9=eCYCr-Ic8tg4"A*,#\K\u4qP:Lj`&b6T3HPK._Bf;<R8da1>[&NE
9?%_!MKDn#m=;lkZT/DMWCi%Sa%fShMH\A^^*]Ac>`[e?^mZ_7c)"6**%2Y5\j"j5KB`ONlGn
'<dQY9iPu9,.q]AV7(?>3-)9Mf+Qhu2-[alcnVR+0K?*KeV;$UK9I9]A;mYes8X<0$KOrhLFF-
6=/(<Y<rNu,RFC)6N,ku\ji0CC&\Ae!/99524Gu)>-g>1]AVCrhRfH$=8cE-EhAQ4K'%T1d#G
q5ge)m:\''3RL<ke@UMX^mlr3"\c%qj,,i[X'_".<*CQTSH*;dq#*pDmDg*D*Sf%U^>`ir@_
s)XY,_1B<('bqZPVlF_*MMN6I=a;(X%.PQ%O@/Ksm'GC]AmEgY1=AdqreaC_1Lp`qVcLbY]A##
NI[[(kad_mKP;h9Ml)/)`)#.D]A53=OF,V4*)%H\1@U,jbiok=D8PQ:DcC*-Cs@4:P6n-P!5(
IJ^r9(J$E.SnT"-$2&s0'p1P]Aee,p,I-(oRl@n8h?njK6a7PHmR'ZpQVIsG*)BVSNuKYc<-[
EtMNXgjR68c,3a+S?BC<t%?C3?b1Ko5R#C"nA&Z-b5n7f+]A*5C21?E)KKqFX@J]A(d_d_nUJc
4'aGI-kT8XoA8!:,#qLG-VCV=QkJ!J+N0;X[$917%p#bI1uI_IZG4RQHVUcnZ;QrtHtQ;'LW
@q(^]A^5T>s?/?HtE01JXuLn..]ANcn"eM*g%m:[g[-!ZaFk;oNM30OWRRAZ9.fSZHnnO`Oho:
G=@=KA5An'fP>HU5Y@0@c2cLl11c0gV3+SrO-OY3X<`;'cIClmN8(%3#fp&\VHAXBU24@`\T
["Q0UNThmC'7';+gS%P6I&qniDZOE.aN/=V\N@XLXeC,KLf?ToduBT<Fcb9(,A4Oo9']A14OR
G*UgA7PqKi+91pSUarfq;/8:7sX=IP'`[%!ec$XHW3INC$#j60UH!+)amW?^c&j:kZd!`(<p
(*<J'<3l*i,qZ37'/Th241qd&ia]AH&65ZeZ,e!(4@>G&+P&C`R?,K2:kO6Ak2T)O]A>S,mMl=
6N:8p"2C4nDeQ,hD__h=1g\p'A"Q<Ypsk!Zhf(.BD/0RiW3M7hUj$7@*i_]AUk>.j#Eg.r=YE
CO\AOX16I$kf6+p6;\n:7QPsU`oC5)6g]A4'=SOU!k0UDC8W"nL9;).e5>rE1hPPC:*!Iklgk
#26m)LS..6Ht9XHL)6['6U?Q&J>nNp:&\h0dbGA0fV+50E,L^qp^o5P/8fpCU'nT!Acs8Z,g
HMrCt&4r,.+s)]AH;`>emK3$b)VhTRmIS'D_d.a\,]A@;*eJV;N.Z4)fK]AEf$4MEe@<7f7+t)b
GI-P)\2?6h&f.a+Q*p5b&W3U_1%_O*5NS9"&NreOGH5k+iA,]AXAR3agrdK$\8!iI@#s6?s4M
Y9Q"3HMg4^D:lOOQE0cc\rIH:/(Kk.O/\XGjt"0W-2oLIh@WrX0G!M3tfcJ/dgm&Ku`8:p*k
<L<NS_ro+JKMJfr0panO,d\I2`i?j*j#BNRrS<.nn#*c:U]A5<E6%=H<e^*#7)lW86L2G3;p$
#;`eJk:eJKVgEDSlM#B3#mZ]A(hra]Ae-/5A;gUVf\VU6<MMuW`3raPoj.I&C4QVsCF+71**pk
sf/Y2GP1GY;<O\cF?oC1ju2L:c$<Gaou5]A!(^THB7=8#c\(<'Td842I2lSe8>bd8Q?%p<$C^
KWN"KV3iQP]A90;#/]AcNA.2A3.,gm0JC'p44V.\+fLFRjmoT[,A[_e7ZZ27XkqoqR9pbc^/(g
`8i/H4CuA0\bRjiKG,2/duH:$.Ee.2/S+(X!chpuQL;hgY*^m4[:s^kM4mn^;5AN;Gmd%=N$
tIW<K2]AF2dI)?#/ZT@kl88KmGcOHW-d)9O[Q2r";UUH"kW)E(3ke0q,S0\Ya1>,Cl'MV^7R(
-H]AM[u#*nO$*RL4!f0d:CX/JmqHH4m?@UHlkE),"WkXlInJVPN%XAcW`"G(p!h</ps[T2$M;
e's7mr@IH(P<5QCisJ,f]Ap!!*?j!!3^^!!FDE!Jpah+k$,9m[5$HV2qF]AloR,[NH2@nlUcYV
o:,2MJaQm;n%=6Am``/"!!GOe"@)qi5QCpP!!GOe"@)qi5QCpP!!GOe"@2t<aS2ICRqAdpDX
".Za1'irlYO:!0Rj-BP_rR#J'tH0Ta,m[g\e$b!!*W2!>#53&-)_B!!*W2!>#53&-)_B!!/2
]AN%?^;?l4(*aOOqS3j@3H:>('^n,EL~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM02"/>
<Widget widgetName="report2"/>
<Widget widgetName="CHART1"/>
<Widget widgetName="KJSM03"/>
<Widget widgetName="DAT3"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="502"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab11"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="7bef9393-3afd-4ec8-9bdb-1a9ee0e1f7ff"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2683994,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,2743200,2743200,2743200,2743200,2743200,2743200,11820120,381000,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="DDD" columnName="width"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="DDD" columnName="color"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="DDD" columnName="虚线width"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="4" r="0" s="2">
<O>
<![CDATA[三年留存]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="2">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='background-image: linear-gradient(to right, gray 50%, transparent 50%);background-size: 4px 100%;background-position: 0 0;position: fixed;width: " + D1 + "%;height: 2px;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:100%;line-height:40px;display: flex;justify-content: center;align-items: center;'><div style='background:" + C1 + ";width:" + B1 + "%;height:40px;box-shadow:2px 2px 10px #444444;line-height:40px;box-shadow:2px 2px 6px #444444;text-align: center;color:white;'>" + B1 + "%</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCR"1PA/W,%,udPm%@5r]A@$kY#8og,RK<O7+Xd[#"Jg\lU,!3NF]A<Yn;A+iuOO-=NW5&fnO8
o;JP8GDK+g_AC8Fd@_H_1+[fD=>gmpB4UFg5*$:9Np<jZN7na8,"dQ.b#"a**;qM!Vfph8Dc
NA@oJCGDGtUJ"Sa#Q:44.=)OBBgP'7aNU91;=[:O,h`C>U"pZodD#<o"f*/(%gO=+S\)?))*
T)5<a^;9bE(KBIn%ZL$HA=IgDt)3C-DNQH;+`jAT_5=/I6[kqjbTZ/]A2KMUA*m9+@q0!m<69
LL.sghr&NkhA<G@#=CcXJ-m?+c#e@Vf20ZUe<,)`UaV$dF9Z!`_jm?Dc.=^+;TBibNT/7P=.
@&dg#(]A=*:rd:7UQhBZ1=4+hDS>UN;XJ7_IB0QQ%f[96j1pMr%<NW1<`O]AdJ,.k8WCPkb*F<
*QWNCD(s*-B^oH]A`GTJL*A,_EIUl?h7)1>,itRP9mm3X$cud#^;j52WErR9E0,J\uF4HetSJ
i;Y6tsm%arphd_J-0%-_J]A@`Rsa`>gF5U1j^^:6b76:'EX$KmoL"1T*@L9mJuZ`J->',,-Lm
5,decPJBNG*(1J%oRK=Dsf.#1`)>J&5?_%^/Q0'@R>Pq-c[%D>`AR^m"nnoqlL*hf8h1Hl.D
b/s/lBCV??S)H6)3Q3>$QF^7XVo9rZsE&26cgoV(!=2QboX.$PRPGu+s;J2l0.WLLTT,iCCc
Ldm"0-s(n-]Aa%0':_'3#D]A]Ak"?Hh"!9@1a$nV/Qp_E83F9gM`@p:U/1p[Eb.q7[Ddos`(eGq
N)R239h/7BIpLK!@rhMD3V'VB;F.a%Z6K=o[6C!",JH2`,GL</hlULSs6R\lfa#LITe:!oqf
J=V_aIOSFh7k`Sa,ZSgq5Bc4bWlF,_KDXm'bmC,!?or.=D[aIu\h6k_4\]ARWX8>TWfnQ!dS?
\Ftc280kBG%E4&?S!gj[@97-P[:3]A@HKq+U)pG`R.'Rp'labAa:9'u_\u:!$nf/,2F\YXSeR
qMQIek09eUFPGL1[X;agt.A^r9frV0[+Za8MOi7GKkf&#*EL(At!U=&AOP):G=Y/1Em`f@<3
;6(EE#O%6<O&M%iKb*P"d^@p$orARr6R6Y&Q8kZ:,eWPAcn%Q`b_HI9dqU=c6IJB>1\_$!`i
#";,:1hfTPR/CH[Q&YF:VY%I5$aT7q)4kKO:3C@@\U8=aa'&5)DjJUb6Wk+1.e&*rTBK]AA2<
tU5P"VLOU7aRG.`L+7QekDK$JM0p+rNnuHiZn'0rLBS0t7L=h0FC?*0D6OG,5DAeYIP*61>"
&:Lp/M&_5U!,oHcIH*1"1&m<0%E#s0`bQ#_`GAnLX4SubU]AaFp-6jhR:4TU5p5dmE)T^lLR+
0fl@<h'VdC&Z0U?BI&4qaSgW,l:rDM^-r#6(ZT+ZJ)8@QO&&7IQ[")L1%]A^`ud,X$g4U#1)>
<`,.7C-7^o0u*/5f+a!e73=(=ci[#o=+$.kP4IJe\V]A6lGJZYDHa:2_VY!IBeTSKbdG82jHJ
rahTUGL(11=b0Romrd$\B^2PqlT9?4$suLshI]A)JE&R&GJGV^hT#\_u6k7GVd^Cr8gW%pHQp
`c_Eb,NFeWq,ZRW]ATFBUt=in3l!C[Q*N0rj%?^<HIg]A6A6-%A]A?]A3CKt4N<.U"+B(:iQ,TF[
0E_e;CH,pLt(sV`E$]AJjDpKjPFId_F/dQmF1AeOMj5@VFhcUaA_;<%_`W\QB8h65lq@YT3WI
FrBJO$J+YlJHIB>?=\LNY?Z=<=SV]AhYQ4XTanSL[#nQmlpjDn*Rf9M:RDn'm66Ut:W^e(lF:
N^RV6G&8_SBY6?0dN+P(oJ(B7a[k)63i8E+k@):4[FK-5j@tIbm6bHXq9lO+1sGX'*)*r#pW
GhVg/>p?;0Lhb`T,p<7S.&pC2RnY0P+e'RO<2FGt4HMB/S(j>D=2c%PGJ!M\ueL<"`/t*mA+
+@9&<4"7<o<(ICLB_]AD8W0"tX*<mSLI=qVO9LLoqDm)grQ?&IEbHb1-uB.g"3=J@7>Ap(oC@
.C9S\*2!K9NQTI1kY`1Tsr&K\&2s'<XjAj2mmo]A1q%0:DMH)jW,TJ)Q:reH-]Ahcs3LI2b@-b
=:Y%]A'X9C-(UIbh*aq0Ac]AD*c,CbG6"Y0_?n>>i#\&ma5Zb'NXnjd6h@J>MLh.oHrTHlP;>O
O#>mhDUCluI^1NmND<K?I`uYR[CXZDhj+.Y@A8":oB\r"hFc0Y+18S@S/>UDV5=.Um=n>6j4
i[()iL`HI`Nah'epIq,k\U+1WQnO8*sTV@+An-,p4Us9*,Ds+3e>/;,5ZK<@bl]AGGFcG6"Y6
9R/IX9jBHR+YG,.i9?E,b`W'K&>bU3,1ed/W2>p8<a2YiQ##-.(Aj?N50S/+</*GPr`0njd4
C4OmL#$IEmd)e[`eC.!5'O"`bRlotGrM-o>2Fm$WMpM$0)K7_e9/Wa`4ME1B<HP9U\T5g%'c
eLI1L?]AZhY2661*i8PN8nJh2r8b'A52^gdj]A(n['FFp9bag#l)M,L?mNW0UM2-Lq&\(D=&:1
R$gG9Hp^H9]AO6!sT2U.4CW&X%Ejrp`^E0KbJD>se2tQ4:dg`3&Hsg[?=l9mk9Ua[jnA2KPUd
@:rZmapM["(:gAcqEZK-EXU%po!2&Qne/N-Hid8Y?,3<kscQ[9q0jPOlG6FL),XA\[15cA"^
@!Y)Qr@FOJnj0?!PM/0#NftenaeE3eVKRD7LeYT`Y2krX]A!]A"6M?]Ag@od%;oIW*5^-Od/'#g
l!5S,(-:0<]AGnm@.T7[]ADWTmn<8tUeh6Vl3[,/pCst*g*b\oD-_-'qdfCAXX57t8c\M8(c=D
#6@$Efhog/jGmUp6>YSuZMjsT[NFtG$rm_g5mpU:SS8PtKX1#E29#S0KVd**.sY"62pMhMl'
`.49F(!e'VfYF1>[1VN?bT)bjWmWl3eTs0^M'QWB\plC,dYhIp<_llOk?3:!qWsDl@B!E-M?
GUrr0G3\n9tP$mJ"ETb=q/IeETsS?'JcsK\Sh^]ANT^FHNBQ_o.<*L\:sPQd^P[j6X_+Bh!"e
!"U.qQ>//#&5U]A1i8*o0:Y]APdcpEhq#K2MkEU.\#9-eDe;.uC!V<IHh`/t;_baWA%dX\Z-['
1EIF2rB1`E=7*'.pILg8RACeTqG1i2?YIt\&Kq=o_6XbfO5*A/DPL4AY8HSnqSH+pu3uDo)h
AmRS7W3gUAc=L]ApZ3bmu4I[`m\:G"Qt,*Bq'F94C0`]A,psT4#`d7iO.;<nj6N.M'p5g`(=kr
6$p8IaqLL'EWB7<Uh[hPl;SKXhU\Mcm%[8[h4q:Gmus_A7.5>IU'LhEa^'@<"H^4LrSsE173
^oqp\00!O...pm,ki?^?n]A(B9&50mS&3tobt!TgP6HOQZ.!f4R(J-koH8LJ4OK`;EE\eP96[
hPDWa2,5.`Y&dRYe?dI#<!?uCsC4p9"^RiiQmVd!YI*cbFPn!SR&MVX0RU@]AUl-P,"$!>]AD5
P@j^1ki99E/(Eg9ps^ar+f^S.<hiJH%?p(DYL(cKbr=<-h_abTm8-F1b@uOFX[U=`:kiZGB4
^h!lhlM:$gh5e`H53_aU#[$:+XrCL>]Af@@3"(1O!J-BFR)2qd$4a-@WrAeZQX8ID2+2&$c1A
+LC5O>f%nfn]AaVCb)RJT`X#hX1F'DAaIIN&2(>Ias3@XLiF^@hWe/S&;220$gK!LD:CfonB$
<O\]A^!+."kaZDA#i9+"-7=phm:.]A-*LOG=iVKg;>O$96JpigR;#PM+ID]Apj[*D9kc(>'Aa\;
1.$QY@0@!diL"QSL]Ar5-320G'65WRFL-$=<mc*^9K0DAe<i]A3p9]A<u'G#Zg-/peu14r(ugUR
B6G?M*ki`\Hlql&$ee+H+F#XM@>*o23W[bRn%DT6mTLe`=<Z1Ph$+VT]A9H:4LWJLba\:e)6E
F)pE$C42k4<P2eUA1*uhQYmBt*82g(TPX$=f"70`ac&U-X2iu=kr6!0"7#EQ-X,@T;;IV6X(
JG)gT6(]ALo_+.V<A0TQFTmhccekYu//LU4+\5_\WDG8a>a*+"[T7IAVZ,rJ^`J99XWY;h2r`
&4g73ZX=):6bXHq"^u(uWb2H_\-WG$*.@_`o]ArNj)tT@sch@`?L'R;d#!q'1"=DR>poQ+oYY
edTQ>j-7JM>BJ^W0-J<<)3q-38j$0geIu%ZF<2-e8dYfa?<%SuYRa8=FKjRP2A.KP`:f[1Oc
iD7TP"81;b[tb!ND$gfe#s1:S(`QIGddN9T\SWqqI1gZ\M1rcLE)f(!o/?e,Kg&a'k=AiFGn
LF[]A[TJ-;_O!dOS>1@k$%OE2>+01V(rM\pb's0H*%L>CVoY%H1$CiS_2H%VXaNV$.Ubit6*>
NB)R-0(I"h0>nUhFeL@FPUr#YFjV1+s$B#M&e(-JUB*;ca8")jS2puTXL0?9<2cJX6P9P;!=
U1JA\'#E>bJ/!"_C\uP*9nh&TS\1S,C#@krSOKC=*UdSm#.XiY;*\'BkX.:Q?S7*]Ap<hXCtO
j=Ml)L_jDdKR\f^[0#pFU1?"RJ7^1>@j#,LA(23[tGG:HI_d.4#^@N*[3qX>+9$a()0AL2=g
H:-`Z6hQ(J.:jK4NgRLE2D/0p?)$PJZa\*7o)V=#D">/K>n$tYZfR(H4u$c\'aj*kMR;u91u
/iPr1[M,0N+1K#X7/?msq:,RT2.\BDJB+%&%fUs[+iU]A62/a-9t@q4Cj+dht#TKI+kDkb,26
gkqFsW]A-LhiJYsRpjG9@Na$4`'eGEpG[b_,d=ZuZM<,MnjWin;iP#sKj90=M$bB'oG`]A^b7)
b=<miHB?J@(Gq?"&!0]AaE@,_&`12U@,bb;U8]Af`tSrZ'AiiiFG`"sB\p?\r,19<eXglB)$Tq
.9kB]A/[R<#+Q[=u/ShR[MfplS.F>S<cBOg"ZdCjN"[XC`Z8L9!`g>'?X7pH`H2[^Gt=8'$9<
etpe6RqR(f?KOrYrB:kbLi%#B6C,\KfMRqpuRb#diRNO&XLWP!&l4oGeu@0jHUAD<DK=SoCS
2r&Q:-2Khn\=S[sNOk0i]Ar$<&QK\)q-9IlV:HUSg"[]A(egOa4Y^pZ1?4!T;&bT!>A^T9a!1+
&5mgn0Ne)?*FEDj$Ie1f#:\B]AHpC).FNj8L:\['A['JK"A\P[j)BGWKh<&$+%f0Tn8=p^fec
8Hend.Gj\Ej,.B/=L^4lAHcgL5iXp9-%]A.;&-Zhk./Tq?+m6Y<tZg-^"BMg!iVs@)KB<f``t
",i;@$LXf4r=A]A.iX7=[HPoro3UgnZ/OJ2P+4J+f4f+=;o_JRD+NL&U0h0XDVi!.F]A5?l&'_
2+bNo4i659`+2.]AYgnI:4!B3)jbn:(KHHTdI0oFZl'iE9&Lak?dcK\4V62V^8D+hFNlo6fIA
umQg*,7rWT=]A6(mClYs'B>Ci`J>3GSkp`NKM4O-q_LklYUo4jMpR3"]AA!&%/EEUV^J?d6=H"
cKg-HB1JqXjq6u)8T+K^9q`DuKFf9]AL$RNo^FUgOS(_>-?U9LuONc[eN.^-V6f'Lt#@rVdK9
%KBrAl;0$,nn7GRThl_1m?oH#DZVZTN?+%sA)##*0[&LtGgGa7`"`F3[1@p5LD+o#89N.nGd
-8sP0!&g$KQU",O\;Mn,X95]ATte#dltb1#nZ,D[]A)n_/iTbo/cu*W:q?G<e/_QQ33FO6G?[r
^R\sl9NrB4'[oYfunf>L#,t944uX/e^5HqPrs+Oo>Jh/O3lW<rA2\Ricu8S11+NH,f^]A9BDa
mJE%6gj4'c.N'*=o[\E&.23VfWrI1M@fpV$4dH-GWsK9e.W(Xm9>b?;j3IZLH/O)L(WJS]A8R
;TOMT+h^t*C6aqJX7.$)/QEpY,_c_25at%tMnI6!5k2WU&'1N"M=b8?8c6C)(T5*W_ia(V=I
FoC=hC'q`ZXgW!t%I(EFl!o0ruDE>rk#2U@iO&.1.&gWFC9ubjW-]AMa"[cg.ETbgcC">EaN`
EFs7AX.OG(&-<RFI.q9H0,g_RXYG_LfNnt^*bh:d#N2+jM=`Zl8]A>Pd.UotSUXgda^=#dCqq
duac6Z/r=TQr/Ai`EFU]A6lF@k37QaF384W+koA)H72HF7cngH-D\?tQP,!7U@/^R2X(*JJg`
DHJp"`Lq$n;SqB390']ADu(T:pV0\S>&>8D#tGp5KMBWnL*R9fV*I)1D@e&4uLWJ+Q%^mga_o
CW.>'H7THY.1O%XLQ&D83W%aZ4Gb_@QjFX,d6#QKGr5R;Y116I$p*rh!4RFk1`0hc@nC-l^9
9"^9t:VffikL.:`*ApFp)KMb\8[^rQJRgm+7tC,gOaA[4YmBZ3ZA((e,V$^_-^(&hLG9`.#V
W`-Zr-XNEXrmO3`mW3BL0(6m\e6'cS1A%=0eBY()HQAc/A&Q+I+TFICr*6K$:V.TjG8\b=_E
U<36F##+&bclTqQI8H]AQ?T\&>U5'7gWdi5[iYTTiPl?k`Ec3LTj1Fr9VA:kbBpt!s-j&dS!+
2Z[!PXD&/dSUSXrn>iC@P$]ANDg]A*uP_N@nlqL7qrQ18AgiZ/Z(#,FH&dEH.]A+Gf[kYI5C,qf
-_mO5O]Af<5=L8)K@!+;FeT\ZGrAXt,&!)lTGN)b'M2?#/o"b-;&]AP&2Kt3-_*e.6C*rr*,F,
Fqo;:`&A`mZX!)tcSl[T*=Aq"2[!4E9u50_SRLAAsa/k(\=7^`96rV8!onhj3BVFuqV()s(I
l9P(4V]Atba(g\&"4hsgeS-?Zf[AEk4_IR<d57,7:c`d*gc@%Y,AhU_Q+ElAO"^9ER!e(X\28
/'^hN[D>[4[7)`3[k>M&/b&86)*a"nFUYlm8\[XLVM3.X"Bj/FDE^X)pF!Mc^<\a5%KLlC(J
d8#N?!Pl59K.d,3@?/L+-5#G-&r/./(Kdq7NGb4WcU6:TKFmk_Db/,6&oJ*smR8sj\;V./4-
J[EHj]At4%%KZu8SdE-Eg6q;_4V&q;341_fai7jj7k3:;bo_^uDlra0SO[k=M;b0L\*R4$mMq
RA>*@_/m//)'+%mrUE*luq6D%B9Z&+]AcX0ahQ$7=qJoY!GL+g3o+.4CV+u<SIb9Us'9L$&Un
-7^_qb%;K4!+N.[kQ_^4p?_(rHU4<=K:NiGO<iS\dRBA(Yo&or$,8fPhlHb6[)^G=$2BKrRe
7f7j!aUjF<V^%\Nmh\r$`cc:15_2S(i3^BjTOBCWb@9__CcqHNQ=jLY_"eur5@8E<TiVmqYo
'7DjT;;'n3Vt+Nm=bBd=(N<H3mkA>qZ5fBEJi6aN<j^j1>(jZNLhXP$;">\-"s7_Jc\0J86B
h%\oRTYbL3LWXE@g/(/ET[7U1"NitorV&&Tde!HRgQ%+Rcaehfeec;;,2/mcTQY+sb5n73Vk
ABB]AkDI-/'U0:900l:Xj7(F30Zm"c]AO:e#JTBca<]AK3VH,HL?):r:^]A*8Xs),&iP@mb0WX_l
XJSO8m8d28a0-ACDSF=a_B"6Z.XcOhC+r?3lmRicVnABIODBpOu^\<.1&1>GR9n=[:o-"1E%
):]AG.k#HIcM/">lAk(b>:'jFA6TP!kilG3@NE/TqZU]Au1?0U$k;V`3L>ZH^go&UMMI0r=2Id
!d`S[o2*7MhFlK9QZIs98J:.s(*(QYWIQM2Ihn?pM=ldLXimbIB@/elXigUA"'rice:I%#sT
+5md,&E!'EWJ@!H1F!BGcgkPPQ%E%XDQ/k(oi?dI(O]A8$X2O]A2qop#2h#^eb7#LPcMAa6B2:
Y>FBEVb3R<.5^2"@6H_;]AdtiQmKScg1cn(Hl=?[$>NnE)Ujtl"LbgS?"gImTZmE0fMDA`od9
sih6E2E1Mq?9dl4UH_,[&I%l8Ueu"dW["$eIR8F_[;H?SWLC)^^GL0*OJT1.G0%)#a164RGT
b!2U(GE[*c0nGQ16(iBVhu"KH5UB\H+eHr2>[V_\4IYPgGn01i5TA_ak://cQi`=P"DX.og@
RtTnQl2W/LS6jP.JCpYjG-H;2WD4aiX.rM#U;P#m&a3%=0Q7*brGQUO^'AbT%5NR3FbB<tU(
25odQms7s(gkNOpD'rT8gLB$ZfjC_+^GOX\i#1+[e=Z+L$&p;9:-Om"S+l0<iD?J<$s32kh5
ZH1XSZ[]AGM[rDCob^7)bt7b#0ul1\GbB.p5b*.HYTfO+aiuHWo1fh#<HLp@%Ym=b%:.8LSf1
*\UbW(,7#p.8ViQDE^(u8PSTtm3nhU*(<EMAIL>^q]AsTAK/_`a\a9L:l:c6(skWaEQ0La-)0
@o0I?%>!mBnXpEPL6R7EBM3D2\GI`kQWH6P1L,@Tpq-KbRW_VX9]Am6V?b0*2"V6HH5kAK2-X
F3_qkKiUOlhC:s87*d(%h\-C-#/Q'qTGXh^Z?!B>_n0'bam##7p;($PV8Uu<oeQ5pLN\+imL
h@`rMWsZ5qXSK?KXo;@hJ(uqV^;[C=oB&$#olp`ETbEJ840=J/Gc]A=IpCUqRgjQpSdM'5;&0
Iq5E(@dISD)@4Pd)WXO7GC=oRX5;>s/,%)=@KVZHPAobj<Ts_<?t#W6l"upkn;K2V6Rt7S&^
=Etqeg-FS1<2MeT>DEmDqFjk27RCSW)[b"p:j2bm'-]AfR-['lV]A@gqt4%^K'nH==A=^l6+r<
X2Rd;.SR;-n2O&Gs$=4h1P/8?hpi6c^@d30Uc9E%ti_q*s<tObf@0$Qgdf0q]A:tV3LW7C5L<
p+'P3S5:7*.Rk`ENsSiD,9_M3r@<iPri/4(1e-C?Ma(t;*c4f[_(VGBPt^/PnP2m)#_6_sBB
>3=-h4'42YrqFKb(VL<DJSh4i_2;_sDHPSGmh.__o*LP^*6I!kVXu,:L!`eJn`^;#E<a&j!a
HJZj;"[]AWR<]A]AnQrhkhqNm>FHMOi7&*;\jO=]Ak2cEXSP_/!eKAiC\hR6]A!Z_jJg[)T^a:u`Q
%Gjo@O!*T*]AH\Y9#A]A,P,ZfbR#^+\j(D]Ac=6E$6N7JF8Z>5e">MT"LQH)-gsa81`u98'jm_G
L.JT11#pd4BA=m$bj;)1[bo>ALMf(A]AQ!FpLMY@DFlK]A)q=[!H;@j]ABZo(>*$hLMpEr@1XKf
:g?\W!+T;L#%m\u&/b0WQP/XJVY6LBpA*M_@%9e5C0%DpZ.Gs?+W7<^K,WP<UUk<ac8n.dp^
g,g%a=H4&/Z,k?s(m60#)sQf-qQ*Z]AjWe>,dZLTAbkF(,5eaZFh\s(`k2i6"7-e+Q![.VH'u
e*Krd-N3CDa3;Pq<%KOfJ&S[03&L"0V(DN]AG\S-$^Q6d]A.b`T&/61s0JV4AMK67WT?9PkA'Y
QOKn2lf`W?Fo5Ct\N4"_%bM6P3T*8>INk=tP$ltGnV%Z]At0=$F^G`OAL*SfctS_ek3@lAT/(
":Sm2\UHmNK,H'pnQB2nG3OPb_5d^EZP6f;SP)[J,h;?:!D(Dr4'S53u]A%f6YdClk"up4#o.
S^Oj@P"NlMYOkLRa*Vo8S.iQJ58`0UTY"Pi,DB72:u]Au7ZS&2%Aq^%Y?)_21/R(%UEHV7EfC
MAMh2hQaA(>1t[HBMD0_*"ZoXj=;W[N*qoM]Ad,@-kus&rQ:XJcFK?,]ANO`.I"*;Zt.(:PWg[
6`:Ymp"SV1ss@kSTFJa;J,;+<,PR'6E`*:np)5JO8QP3Ndn@13@7TPanBgB>5AbSoZLVP:#=
n`0mL8ftb_K5IM+QlW?ss`WthaBp=*S:9om;GY^WQkugEmaB^J^4JLZ6_2$[\3;b=ZQ$ZXh<
dM0p@k8I%Jig!?4M_iGE`LB8!I&q06gCp:`2A9!#9B"5_T]AUWZueG5Mf@?B-JI!5<Ao\dbA;
Q^gm1h;38S3jp_I`/-Mkc:k0kh+FHFTdl_:t7kSh[C@ZkMG;gLQVBa'GDC$@tM6s/;PT/s"=
h9d*RA"ICoi*)V&DP:qdIkFA(q^Nh/NfOn(CAP`t+I=c#Kd4!']AA/OW046ieiNidUJ12@+1c
?STfcfZcOXG(6/EsIhM`K$>^M2<+c$%_DcYTLMj*U;A>I7qf#[p>2![5)Ng?Fr)e]A?e3gp>N
8a"VPK>$%:f"#j3C:&9,2Q/3R6>pK.ZB0,LDKg+)2Ng+\cMH]AF`\t9*:VOkqLBXQ.qmXmuKh
j\*X;\4(tDR<r![HEIK`31ptW/D']A1tgl.-@pVt4i%mSJ$'*fbnZ'@+&N3_cr6K?>GF#U]AL1
kXrPctOZr1]ALpodcML)^2CDX3bigpb/jUht/u-*(IuY0kEb:<Mh&%OW&/.o'[KO.DRn:phXQ
-E]AP'9p#YZl@<p64[sA!(_^,e5sF2[1*:#$h+7ujIn$K_m^14D8+`iaVhXY/]A)ZUnE]AZQ<2B
$82<;*?$0pFlGbJ:d'm"7>i/A&A0KPC.Qe_DHjc@SPc/6R2'[r'VPS&JIdRG4u?J"GL40'RY
fak*U(j?nmcR>kA8I/Da@![Y&\?AFkek8Te?K:U'<"p_Fd<1mLPAT/pd$-9h5&IV6&MhN_Y_
bHR7]AJ79]AeVcdh22S['>1]A<V(R0g+UJ)gQ?<>i4Ms=oY^mFOB&qPZkrA+6mjL,1(?M[<fr]AD
UPl+pG8&eptl]AnVjUI93-1EK;,6du5[h!37H@\@`.5M$o,_BtW#+@<#ePEZ=bcWchhHm65!4
PLl4Th$hiefh7*uqFNJ/(A-OD`?Z*%pkln^pC'!h,>GYW[G*0W=5r</63"o`gL#>FRZ98`$E
BWXqU"`KkVkSlI1nRDm8@Fpeb#VBNU,&]A7aC4rpMpR&%o(&0\!=-Kk'EH6(rAm^+_r'O'>b_
8&Epj==11S[I^ZDRU`PqJT&$C-^RtnpXME$dJE4-e0*+bt6qV@&<V/jZb>#,mQd<Q6HdG:do
"H0PNi[ncA1%a&5[_l#gBtV:rO1`&S]AD46q+7\2dDrV`1@tp[(oeki/5oPFhFcMfs(!rY\N<
NV%-dtf.sM\S@g$Fd>OMXUb.H/Z_W\_\#tL=F=KWs$>m/AK@3+rU_E>j;WMAuMSbp\QEC<`&
?_1(0kK.,hr:Y785I)J/oJqg!8]A>Z2EdDbe0]AQaVp),,&C8ZC_m3m2CKcEQ7e@M*in79$`Lb
Q1\VQQk[J,HPt,dA?s)B&1ORqL<\G3.`U]As_^4fFmU8:L>WJ#\V983hc-aTAP#2*D5742%ju
*j=FU#i`'gu3Ie`W!'VKJpTOU9JRd)*dj<T7_'<Ncm]A$^P3>(g3W^ck2CA)TG._R[7HLgU,I
A[[2-b`6l%8-BA<0O-db-`+[4W`u7T;^1j=ir2FSDa8qB?4/YbAf53Gs`HT]Aj5gW_4OS.AW-
.1Krd9tC0^OmaHtqq)XR2DrtGA>/J8+Doe*0u;7a5$q#m%fhSjbF/<22T-:HYU,d7r1GC"@B
kq6$j"XJd?;0C1)[9m-oE7'$7?Pm`KG*TT[eIlVRGl8aO0c)Wag@q=,:XJ!mgidjob0t:7GE
CYh'T!)V1Y2U<gTCR<'rg_VV!_AJogtcD^3"!ML=B\;@4cg0F<$QR\(Y?\?jIX9YO3[gTo=i
)?uEiZ[\*d8n(ADM=K=T;NHQIFhiE:sK$8F%)I07@Z6U0:j3o#iE2-W"">h5MGQcCI"34TN)
G_39dgUdVIIuHQIh7dZ::hcemH8Hkai/_TO$5-Jek"7iQRgdZ]Aqi&[SAQ:r?2),KHXF!CdPi
`[3o\]A(i47&0V?Fd0C_HN2*oM]AS"/,K/otrt!RYE@PBgG#A'3V%)N36TaIeg!\'mD6tS#.Ah
XunlgC_7J,7q+=pZmYJs@]A'Z_J((fKF'H&]Ail;\,dE1<006Ba;H`=.a>\kFBje7Sb&21_N36
!;&:3nA?7<:S]A=oeCejt%:7R`WQ^3SF(Q+*ccPZ_<@[k+\-@T?$JL/UnitV$FRuBi>``:VLQ
74bqA.F@)'P,,>(4U.r^u=cVo1LoESX>cgUMiVaq]AZLRR$LV9S\P``s,8oYs,cmoiFhB_NB;
Bbl/;P\%8(7i-(pXI1NKWS;[2:-u(L0U%db?Bjm-C</c)KR%n!U&2]AoFuCZLq9*8]Aj+d0,F2
2Y+rR^[I3/kj;E#aC5mkt_OsYA2X'TLj+Lf;k]A9W9A6mdqPr0==F<j+$fX),0=_pGtsE*nS8
WVKSC;nZh\b^O5X[>L0b_LT4sScigf,l4Rm?9F@"ns9!r-sBQ^/PZ6=ON8r8:2i(,Oi$ibhq
k5;pU408aJu8\_umVh$(DoOblDJ,34R3'M<sn%_mJO9*H$Q&lM9aU2ieYn!Jcbn#tH&)Q<$(
M`7YpB&;Cjej0h7UN\\U$HgHan\)CQ*hi^au2g(,F^''['[MkVP;-$eY?gS7BGKf<dGlbg6d
'jIL.Gi+NMWoCAH4;5J#;/TZ'0Q,;WBLZ_PL!4)b;A405dZ`S;COY?86,__Y,pOQIbrLo5cr
A"0]AU6\BG!ttO0RV,AQ5TeYuOTu84PPe5_\>BX?`cqN[R"&_96OEno)nApMal+.KM`D3'r3@
M9\@R$-b9!>-V\)CL8.=SeuLY?pL3.E""LqRFnJn9CJB2q<:_`XkH'I\h/;Um00VmJ.jk5b?
J`'Ef0\^;X.3\=&t0Em8sSVF,'H?mpcMLE(0a&otk&0dr(F$G.a`BfZA5\&o&:I^R#22<$SK
Hq*sZsL'Yi9_e3Z+M^qZq&eIJuDCT\<]AQJQ-Y5I:f2a'Y4)Y-!DKY#P4#lWS1QSLBpU^JJB@
]AHZ*"kV0JY/Irf`M6$$;(lee+XP:6&!W5JUfnE%;*='dP[46AQc%k3[:dseQ`iLJ3$;1pj_M
Ah3$iQWWc<F&^Q10O^=3%J_M!tRqdR)M/K`>I!mTbWiu@]AY/O*>[h1sm:,[H-TpTb*7*ri(_
npK1uW[u!\A*3f?Q:.^MoM_pN#qJZ<dZuQV6#H)DN9[O=l*[2emr*GB2De#$Gc250?It1H,!
'dT#WC<]AFlhKG1kK<f.+NRM7KC$5diDrN9M,s]A9%38u;KJ'H>AUa&j\]AGNq@Dh!'!b:s$'^s
A3`.%W4hJDuLr-1@9%RiAOO(oIZG8:9LMTFX)-EHfo=>EAbe`WTYG3sKnE+/boPFmL!6aS+X
h>Qgc(n(dQB9$Id.P'X=-no&ppfgO\WB<(_P(dgIeL:$/s^Yer<=Lsq3,U&3('tE4)R!nr3J
"bY'YiZ.!q>EU2h:==UqD#]AnQ-=(($;F'/bP7T2in#.XjNQfTmYep5(%coR->I?@X8Em13^`
`rV5j.tZa9[HE61reUo!WEEJ#$^-]AY%-gJhO3XpCq/U."1s4>ka#d#S[;A'W*0;oTpLe3:/\
9s.O+j`Pl$E(u/7=#Bh[[YrgW`IP=QFKr;EI2n0$&8Q[@4kf*Lks.P=SI%;]A"n@T;D-m,m41
VRpCuN.0f1bBl<<(%`hj@k_I>TaECo.rb^,bC$e]A0_mj)Wl:/ZAqkC2iIeh2"I4M7)`e2:n?
ej#@[ZA-s/nSH?6.EQ[JN%?Q[u)4c6bs<4*t<;Up3T%]AdDJ6EDF:XWO1]Aq;HPbt%heUMpP9=
iW_nJ)odMdd<a)Q_1Mpf]A"JEmUu,PJCB]Anq.6p=tZ)9ZtZ'L49]Aa:HXg;*]AEQ9V/JU(F'`%b
c&=LE-NBoejoj;NG+IhQOg?p2Dd4<D^h3?sjj+gpD5&;CY1K&UG["MY-";Hf/sgL,Mq[4IJ;
3:fg7m/ofLCc1&d\7i,[W0Z2Ga`^G4N`!:g-oPC.\G=%Cs7jFY$T9hA;24mGL%34lN%4X9S`
^=Rsq%Co(cOFBj)e^i%X?4;H#JcK\O3$rG:u$X;6MReq>GqcF)9s44Z-!WJ]Arh-\Jn42VG1e
rsubEN\)gM"N0(c.1!WZX@9Z#3Ch<./#o-<]A3%QNaHkI4D!PMQu7]AgSRA0]A35?PH^\tQ*0?N
%_W%pm4.8j`HL#qoJq0nD)_qlPVP[orpiNfI\+(`"U[c;h3GNWKl]Atdck.WR9o72rbL+!$M>
`ONaZeA4Id+60.BhG89A`+_CN+49d'd.\KI!D^,0l3&#O]A?G5'Ci)Q<_3Chu:c*Z;P^[4PH>
-rH/P=>##"$V`;PUf:c?S(_o=VtQa6&gMULmj8P4#A&+/tL$:eEWCe,8u=J9eVn*h?,n1WDm
n(ll"sbM?8S2jfgH,9&@=$+"Vkl'HM4H^!-!2n[W@p#aYmVM]A)T$f/&F8"#EV'(]A=SPYLC`D
_5r<B<&.V<]AX6c)fDPFhfgcaH29o>]AR`.;=.j19K(2qM2c..cg`[D*k&(GS+$l<)kqUh:iU'
k@i'(>Sk,;UB`o&U.mXNIH%/8BPa1STli&rP;cG:`l'MOg<H]AMNoeB_O2MmB4l1U4F0[2%f/
+q'"6QRJC+9ce36<2.[&?Uq$D3.qQ2.]AC3C2/%;4nV+4%kVgaJ"5lSbd\soraIFLFggGT+o6
t)#I+\0X,s(6"L4YCrUHUNL0Ror]AF#AV;_<;;e#3$%=Paj#U[D,YN^1nRL8g]AY-\(H_L+q*H
02]AoGPQ?`VHW*dB1_[L8Z$kZL%Y`C%qjOVi#<&&jCBt9YmTVdi/j0R)PVqSdaN1M`6ZJVe9Y
2L:0&6YM*>X`6YL<r/Ns0?S?lpj-ZO'4\9Y3RgAL9[47BcfnR>Pe4:ls^hk-!eE`3MiU:6"O
&A.!b$A5)?sN`M+R8r5b\%YtNr++(-QnmoqA*;j=i9RddA&Nf,L0`,!$n7bHAKC.^UhpC2Jn
O4M36k\Y76j?c?7<EHWq@Yr5L&_a(_'R&s>lcINC3f(!%VFa[>-M`!$qkNUH%Mkp$IQl[t+^
hWo$`cKDM<O01IV0CrU1W]AhiQjAPPbi2^,oj^i^nWiHQFfG\l8c&njfCVd+R\pkQ'(if^FfG
`^^2%C,>[Bnmoc$RAdDO'pV>/0>*3:M>V*'Y)RQcjAd%K?Y#_n]AUj&JcjPA$SVnMI+0CfW/M
bQ2\NMMWC71L8Ge6=rb'O,e:dJKiA+E^HJ\:?ME4UdQ&g>U2b[b\c\7oUFT:r\kq^sq8ilC&
12_R=-FF.TskP$TLf7c"P?4ZQ^laeAZ\=aAE0^rU?#Eu5TDO)(E\R47XRfo0H1#QF*!>hOY]A
jAs0<Bdk,`4Ybf@V\6Reo6oe.e'_T>)Bp.[dDWP:L5T2Wk:do3dTU)r*DCt7=0`h"NVu06gS
2JTPl01O.Ki1"P@EjYC/lX1d9,0EY+Kl=55P8@ZEkO3!`24BjQtAp+(5gHPok$*K8@J/U>k%
N_!g#hZ<-M3X8>*[.X?R2HAP^4L]A[2=p!%DRA)Bg8@`1,.`2Y[,*Ahn/<)gaY3<eO)Z1aaWW
eBF7@O_7^I%UT$FIQe=6XfrKdE,pFmd)J'iI\3<0QsUrM'M9aTQu$3ldj7+o_ig8\mp/ES7K
PkA<UlSh]A4=K*af$4+0)&s6[`,Zf=F^317LJ&7b1:+'=B23QX`PhJt@mDr-HQsVt,Mte(q5R
'hBbfj;BQT\_C&C,)F:m`kA8ZWTN*+=\KK6q+o:p]AMap2.DhA-Jr;fg5E9Z%$qdN-+$kM_0'
J5t"be\1fYNl0/<tP!:TL`d[fSq5<kEN&Q5bK:1Ehrca&qTZ.BG3%<t:-g#N$3Ql\ia\Bp;V
n?nBGAd@YC38Kd2TIf:m&gpCmqq_`:ZBkdOAF853\??*7K+2:AqY"B`#>2_B"72iCod7VV<q
(uR\Ig'F<aTY#2Ker!JQe%O0r@cHQ)Z`p'"4;G4Dgm>^fMZ<_%'FcJKhSf'b;Z'dprG&57uf
+?8ZVDTo<d4t3ej[t"L]A2X)oWE-kP1*O\E^^G5Pk^qEUnZHEX78+:hTcgk?)m;ZQN#O0d4\7
kh-0h;Y.Wc&)mE_lNrS2R-Xd^OH;oq+^Jn)FB8\RjZ\li?OI.g70Z&VS"IF#HZ:[e(IQ.cgc
6OQiUjFO_dt:/GFtm^-$nV$^?ZC<N62e`Z-hY3?QL<r^EK6JVCIeJ.0*A0+((/=]A;O%`"q0W
/VA&C[Nl1$8@7?01fRO:74E6FPY\E6^Z;4Cq;stC5n%#mhccBs@L@g(<%?ERVi/%CF\BO1KJ
to?6l0ej8,BTfu?A!@g6p\?qd8#.b/19C@dHHM2@$#?DnL@!1M]Al-:>2M&$EOA6RPpp;;_/:
FB//+EdclkC;LB8E#Y213mk9:\S\=_K^=S:jrGmRnuiGjM:)@p`WDW6G`bLpP[Hl0>4r,Eq(
>0@RChH9-VREifWbTK+c;]Atau?$=U%8G_AlbV:7Lq@@^p9mB]A-GQ_JiZ!Zp#a`%JQFJ%qc%)
=^o;?l]AoDl+nKo#*T9d%3+B-\4_b)OOeob!#I_d$Z_*bE&t87C^s@qtGH!?E'QF:berP9>I/
PQPsZ1M4`Af@s>YHrK^ii.Fd3YgN2ir5G8Z8qHoi.#n/@u@WPlUQiAtFMp,?2ENX7cI?l/tX
S"4Q2?*n+>.n%<U3'6<96[uI6]AW6GV$3=CL,fj\ggkRbQ0149M6,9JZ4C1F!]AU/+29904F@$
<Ik`BJJr%5;N_<gcHF.`:-[r1MDZUHjhS8URCbb";l8sC(umCHq6Ih9JFL=a0UQHiM6g7bY>
H#.WDR8('[DrgnB^u-$!s.!UK$KQjs05#>f3]AdF@o^`/+ruf9i6_@@I<9'-o?h=+N.$:"*7E
^X=FI1m'CH\O[,bdPUj4UJ;B-os59rh-Q4:CojDYkOCne#q6#p*,("\IE-F^759G@nR&`f]AL
^DacMX^qd;PX&q[adSeJ0kF<*o^b0b0^tN>+&WGdL$\\pj$:i5qr%1juV7M>mR>2.`s-7F4@
SH^BalR/bGn/':.[GiB@Hch.j@%Va\Y'9Ii^D&/^Y`Fa<+ciA!_a<c.R[0t*Tf#T:,KM*B.s
j]Afh4$/Wp)u,I]A&LqNH4=#7fjQ8O^@0J:o<0k)&Z:rD,lU#'54;Ue)_gr]ALr?8ReM8(?u8G.
s3(;Gq.KAPF;GR<ANCQA;j2P\P_FO4+[(<@'j!FG4f7GD39DtTb'4'W16T8P;Ik>qe1=$I#7
^"kT>Qb]A?1tki09(rM6T5ek>1i%d:[e8)1V@:-=lF=?Qk6/YC/lkp\ZG]AtfNY9c&e"@J^E/%
UJOi[CF#W/J`k9^n+h&`RgSLaZ`2BMi.o/j<L?(g+5?L5t\d')ipA&'ClIK2G!D[,*^S/WB$
l2,'?u0<cVm.qrXTAV+0N1kT_Q!-"98$"f\<PKha_j:[hALcP3Gi/9K<DR=R/4Nc@r@6OW'D
i.6DD-0]A7D4(]AD@e*VXk"BkA0qn"p9m@pL>(]A&op:OV5C*]A^7rWGp+ZI^-qMR)=)W-L5A**/
mf?l`GD7W>I/Al1P/-uO=tGS$lC+WNnDa#GNdRNs3TGg;WNO^J$GBA;6$sq&XOYr0m2EG,/I
#8)#`Z-G;c+)i1!3oiRCIlMAZL!Yj[RmK-(?%\Rn3HIhfR\iBl?q]A/"_eBNCZ>XkHCoks##U
c7raia;GJ*]Aift%7\^f1PboBXQ%R9_b/q1AQEGjpC(3qZaJD^)L64*hH;Z8m7WPM3q<?h_b=
@h_gJh=ub]AJ0h\>!lP2U=hTce;$kHn;tp+GH:N3ShH+;/)RCq_\"(=DpT^[W<\>1354K0dmi
iQE?<(4lb-M4g*?l:7F+MC+94BF2.F:K-?Bj!(B]A#;:q<:I\MBO,1(jN#%dp!7)C@-)_L.M9
kTPr7'<4B6h:mG*cYTb$ZtuZC`m^Z5c_4k;\s-PAgF!Tn5C)%t.r>l08p?rLLhK5"K+:KV&s
1\]AJ]AmkrYiTb>=<1P2gh`p%U!UrM44IiWSZ@>m#&el1"6:duo9H;IT)8h/q7hpsPqC,b*Wb;
aM6#,uflMspF6PUC"X-,XS'iYfQkef'1p8EJ]AYf)@q7[G=H>j!u=S3L#^*Y>s>YaK^S7.;0Y
7s12\0cu9!V-P$kqdr8TFmEKAD"NY5]A<bfFiurEYi<Y3cd[+)!>t)5B(QkmksTY]AN7L8IVWU
C,bf%T?5H!UUUf<>%Ui$rZOW.L\eWChcG,:^WiA'sfAA!>6]A/TbSd.a?D%s<_.Ga%Fjh>G%S
eS]AcQiNVNcpO)9KE>c:-BoU^?R@FI&qZ8OrAi+$"g3Ud?GUG#G8%8/LP``e+7F:nU`U@`IO%
9<&>h/em)lIAG-l'O44mk"J^;4`Mi<P:.Kloj\j;HIh'3Xje<tMPArqCJ'G<Z'HK"6i:V^8%
#cQluajUL<oCK#5\ef[WtG]A=`/MjibW2RCQ_6_'Si_\P0e.0SAkaP8<[NbfI5(QKn(hT?2''
t8@%:H0l81tJ4aZADFuF5\lh-[OSM/902o9uDf81PC(,8lTTU&UM?,5YXg?I?V\C(mW"[a!]A
/NqAP43n$g27Nh\A,&!Ek54&nfO_njKm1%BA<)Ma,kGs)#0d'5$ie[ps9@L;Q7cN]A)%$X5'Z
TP!%iN'c#tYBMd)QarMeVq/p\W[d]A!hb7miKtPkW$HhiBVCn3eG[CBUmpdU\]A:3IP=aKV>NL
3YF/YXG"^Y7)k,+Cf3GBUP*YF"s,#qlfbVMT1sr\@=agUt1JjXNAHHfAGVT$>HDZd4\M?Ck>
:f+?f-^>l-N:W8r`58AYtr`&fppf$^\i_ROcME_jUrtV.5Y]ABT<Km2rh&QENoD7:.D+T&W3l
(GW?:Z85@;tqg>'RgBRKqH4XiMZ0DpaQ+TIL8;ns**G(#j@\3aV(i\,a<26>+(WTk5,#U!r
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="456"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="46" width="375" height="456"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RQSX').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="rqsx_c"/>
<WidgetID widgetID="8f84bf28-639f-456d-b954-83ac1f6d0375"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="radioGroup0" frozen="false" index="-1" oldWidgetName="rq"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.radiogroup.UnitedMobileStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" leftPadding="15.0" rightPadding="15.0" topPadding="3.0" bottomPadding="3.0" buttonAlign="1">
<ExtraBackground>
<initialBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</initialBackgroundColor>
<selectedBackgroundColor>
<FineColor color="-657670" hor="-1" ver="-1"/>
</selectedBackgroundColor>
</ExtraBackground>
<ExtraBorder borderType="1" borderRadius="3.0">
<borderColor>
<FineColor color="-1577998" hor="-1" ver="-1"/>
</borderColor>
</ExtraBorder>
<InitialFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-6577229" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</InitialFont>
<SelectedFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-13947856" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</SelectedFont>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="2021年入职" value="2021年入职"/>
<Dict key="2022年入职" value="2022年入职"/>
<Dict key="2023年入职" value="2023年入职"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[当日]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM04');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM04"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM04"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O>
<![CDATA[cwfx__zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="财富顾问人员级别分布"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8nX6P$,PsBt@MD1Lk0[>2RQb<0eGtj9;M6XY2=bQtBYAZd:ONP)og#/u)0U11^XM/Zgjj(J
7V*DP[e;.:S..&6Pon+J9pO,''pPoR6&,Dk4;]Aq;Y]AFX<hYubO[oEn+YMMrS,Zt]ApB65qQ4)
7IO+so=FFTWhOaducf+KQj\&<ldLXj,!<=LV"+,4sT.:`nS]AWstUoEbKWI6rBa*DmqF4bj.D
@4((k3A?]Ahj8[CXll1[j%>egYHj@>>rHQ7be=bEG^e0/[gel=JS'gkB)55GIdjFEq/1InY4I
i):$SJHO`3bi.PsE2MP!JbF6JXC9U>o)JPJsUgri'bN]Anq8QPL\E5<j#CHk$-92d0>!F'R@n
N*PPb!'ojOo>'_5(,Gs+/;WB([K:V(#F_*,7C0+*3d,r+!<>5"qpd_Sa(=eks02[^Jie<!@H
&9eI^K@[XGWNBIGf>#R_nRc\X*%=?LT6m!h=bP!b`Ktl2#'lA29uKXFC5*%]Abs:l]A8`-aT"n
=a9gM6(.hX<e?lgh*.qnna%ZXaQ\o81]A0R)N:+F6LHU#UJ+mjY]An#m-W?$[%N^gD[(Y(qf18
:2C_2Q&NKR`===dK,8O@=K?#Fm`R&*\jgYaU+F^^IKrFQbMM2=T\DVMsXr:[TfXgbs^ukT4u
TuH/3TjJ1jnQkEVR9Z5OpIB8HSX7!:Qh&V1^k3+As]A1cnhM5m2Lmgk3)ng3,0<QL,:f0q>@=
*e)MK-WVhX.!10b0/e1:rlB'sggO*k_)1gjm-@g8qkPI%+g/3"g4N5g@T4ua`qk_<3!sbIH"
g<;mQO4$m)9EY%SV*741[;HK@I/m[<4Zb^H]A:G%Bkl';GmU5^=BqeEoAR%2F6(66\)XXY!1L
N`Y4O[6Ld]AP3qQl%o=t%ec6/Gi@H*JNf4Q#n4hcb'OR@2,]AK?l;f87kY?gDp?F.W8)dOrfuK
(VFR`'D)5#cg(A1WWuIP7mM)QpDM1.7B*MLZOtNL/q.(A=V*V),[]AU<DN1A9mS*[820d_In,
Ej%cFSfBd_\\p@D=Z9`7nU)[`RCk;Ns!&7C]A*`Q@sRAM[>FO`)ALd3T=kk2)sO7PWtOc4\<V
Xu(G!5Wj+bLZ7GmS<g7Z,r/6mP/uF8AnUco6J=Ts2s2"7G/@%aF*((r6R'WJML)?gQ"/YBBg
G[L8^f>ZH&l6EQVKY#a8OGU&TRO+50cIHbcB":MlCd_idl]A'D1<Tk[67dm.LJ*n/Hlkb4`_/
G[Y>Kl#Yli&m,uBoK^p2Y',Y2fgrFl@CM6'.h34YA5oX5oIdE6r@q*"u.S116(Fo]A]AFNV&r-
Xh_GR2Qq*_D8`o:&L[:#%U4oChb5PbbSGua"fX3'@4'6a5$M]AJ3@J!b)EMM/BL:dna$SUB3T
h_^U'TOIJ_M)1@nE7r[oAA>MmbD-k>&n;O5#<cS"mDHA9aT)Q"@OoV=ON\,NYGE@_K<RM(Jm
(LUbg$M!kQBb.`@PCPI,2acE2kt5L#e4]A)P.ESk/Gfdk5nPNd4/KJJu^9\q?ULsA534Gb/^M
hC[)a_NE$Dh-1Wa^3\M@I.&T>5YD8sb;p:hX.PW9CND;nS9>X2Fa*0XNgHcf>\kT-A\g8oc#
t!=Euk_KL1K]AYCL,RI>ldpRZgTjAnB%=mhr@W7]AJ!&H0!+IV4)20TkKY0W1`Sg\FiX^lqJYh
U=hlCL^sgB;L^Lg1AU&0!k<KF3qIUB1V?fN9V!*FY':r1TZaC?dfLSRim="WJLm.g>o;e<,?
OVQ+jqa=1"UX26e5BAm<`jNH4QT$bX.I1+r2,P7)4%Fr`\/kiU%,.L_qM((oY+qX3h9>-Ue2
A+TmnUEVH@mqW0T/A4Tjap04-9c2B\JLH89^.kpsn=ZgNM6+9dScVcQ\K*ajO)N8=Ak/l+3H
aV(QBWG$\Acc:KBR5T6ap"n4@4^tCO/+``7?ZWB0Vf>XTa))-Jt=T$t;qPEg@F;d]Arp_F`)F
Sbl.E5]AVZh)7(*d#gdE`V54j80FK(il+lG.TfZ@#13,Z;J(>']AR)/D*b]AoRI6VF!N'`(dFD0
[TGXgH=gp9jhBS)WTI42^=Ap"KB;5/E'I4mjs*U(Q^,$/o\EPf2LAs<;4NBGk$u?NW,M^XF[
Wq"/6JQ66.5@/-BmP5"X@_pi]ANj[Q(&o6$:_Y=Esdd%8A;@RdBUej&[J;Gf(rkc";:q8sniD
Z&WMdBZZOA2/ulf]A(YNu`tul"F2FcLVK'&hZ;a=#(oqJ1V+%),kWKOsfjj3g)gN7Df\08*C`
(]A3oEm&Vfc:'5`K!,sX4!R0DO6"1r,mkq0YMuii6M4WPK_t-cKY,24^M%@URI?A@c*#3<*Os
<;FR1UM)W\$ECiTekG^25?Ki=p2RN5q`npI&r!WAWRj=Lb?Qi.\l\5-E7H]A[q2.rgY9F4^lN
kG8`_F\LV$E'XBUQS7'jf<q@fboUOEH'_fF+'!ciho4dlejen2qNiaWMHN3bcdZJNQ,N!?o]A
]A/f?AGm9sn(1\<+7@!k$ZEV(W`h6T[*FQ5ESGHpWH^5>K#/`[PB`oge>B>;N[pR.A[;7<9B$
TfmQd+%CZ]Abs*eD1\_YR\8:NgHrb2QR1hNegE'g(h.]A9!LJ-C^@fD7S&5>Si4TUrlV2E$U-G
E=?oC:UO#/_aAeX'nJk.QpHkFugAUEbUOhLnPrkt[i+K+_e!,f\pQe[:.+QuDpRH#3]Aa5*J)
'o@KWKkb[YkbhQ;":-QU9dSbP60H<Zi::f2iOTmodW'fCFb<AV'5*M"/"uq+PTd/_po6*j>h
6R&"cd/s&=RH&(S6J?JX=&!$"2Me0\95-ao>O/)F'TU8C+)<ejfU-.oMH1_$C]AH.Qqs:_4$'
3bB(RmIfAPmX2R%3EC(?`o;QRsHY,hCqig<0@8LM35-9V-#\B]AaJrB@%!^=>IMA8X&`h2ae&
`4pRJWJ/._&a2N6O>.ObDO4>OXHg<%E;VJOU:-"bL_bic]AmD"n*:k8O@N>Cl1NkQ=l%Z"#AZ
VBPh'Hla;6+H[?')^tS87nn]AuV-!L)?<79(%3FmRsj74-%uK28b:LX*Wad)8.o?D@Qi*4K\$
d2'rN^Tm+sj>kpK]Am]Agk@2)SW1;L>bc@lg[EVi#nC'0sUimBB]Abl`KM]Ag7TrQ_Sb[5GJe@rf
aE#nG-(5$(g<)E[M,3&T?`-$X8(FVe<Y/#\3VYQ6*I.@aBuf4e*'549Usi5K@d:_7$=t7EC\
NBh*I_)UrF'7hf_FoPa&/VO2pLk:`N\L,F6o042GQ3AXqcK_XFX)Kr)QJk7l6N##cqg@k:h#
3Oc!1>u':GH&R/6P:Wpe\"he-m;D<)K]A%R[Mfbc]AX8KUpoVl3D)gKRGBB7P=GQ_\UR:e@S$!
K,R9Lt+$1(:\oSAjXI4(<Br/"%+!r[+`#OIl)h+B,V?%OHmN.UjY!,,X'Di<B_1OBEDo,-/4
j$lrmA1pFD!(e6X6RCiS<'m'h$BE3Ib8$d-lZId8nK:t*k#iQUlobo20PFsG:EHZ&9P,0=J%
Di=XPn5:4eLeYY"WkS@OJA.P3R&kR'&UGn)KSkcacm"@h,E#/!Em9;1c[#mlUfN"eHEOmM+G
$%#\H'!.,:pGLLSXZ^2F1QKG=t:G1'J^@?)K-)VVUWaXK1W+WG5D\0E?]AW9*nE30g1@#gnoT
NIBV#&RDiBmD7>U`?@[_m<Y0_OZY.=#dMFVe1ejLQopanJ+fh2RK_iHN964ia4=/aHekckH<
-hgihM;iQ??[q;+"B/'JsRhP&Z8O!d^NuF`9_g.n,QB)2X?#,plCaZ_Wg@c[4bs`k*Ap?q7^
c!QKESX#7k)RfCpYV*@r1,jD*R:RMR6!:6/TrFO_4='l7TShp1f5%JQ,=ZcMW)D#aI$`>o84
;PX/9I*'SO9#^Z+m]A_"ZC-5PO7GNfC._aTY&18dFtL7HgS#cG7(TA7lB$XJ2'DuoX74f@F[(
YU4ZWqhNsqs.%LRWibIFIFiGrUnX5>H<@?]AJc3]An\J]ALU#U20F=_O_TP(cCTC,W4,Q<"Rl<n
nLu5$Uo^L3A`JGG]AWZX3@NNpLMs+29>AL,e2%SdGe31*'O1%83q@s>]AE5S,,IYALN!nBtE\Z
8@LO#<4(Fr->G1YMR.F%CE8p=GM\3LTO=ScIMHd)1J;S&>H335q8h31]Ajg2!!lQYDlXglGrB
F>m%08'TlDT+"@^DEP:WLX\?2hq%oItOr9QVS=(g<mGpcS>ifiT+Ud,c\6`'bU(-a@;Q&&<D
r^$AiY9N]ANXi_n\f<!VM66'tp+jAT=(*F;[;.P7BQ)e=S`]AI\?S=3MDhJ^RiM;>m:n(\t\BJ
LlMNLHNO2cNbE=@k#/WDX&J\bc:J-STbL[r<9N%K+7I%,j]AUNp0;`IJjdW]A!j/6Hl$_ID=Ao
9m$t<;*PL:3o,J):bd*uHWTfHQ,;h!r]AlQR]AN@?l)nG'uT2)Ku7@Zk8+[Hm1,l4L3SA19-1"
YsR[3'T?PkLVSU[C%Q]AiV]A2nXX0@Lot(A;lYXnU(*V6*jcBgd:S<6Tsjq4'T;+=/@+?,&SDE
!X(DJl1Ud*'%$KAc"pD\;1_3[tI,D/FQ08EFpm0s$D0/(#LrJ-q`"(qQS`-@k8S5J/<W/eqE
n.Y$F(maInsd#s!DQRj5.5X<32r4JIQKHp]AkTng@5kT25>)pL-`*mGCALb'Cr9d1fn6oo2WZ
=Q94#_hc'76k$"beRXP4`B4F&5=.'+\8#=F&HU=o)>F.0ca]AY1MeT)l@)Dkjh;?bgai6'mP0
S(@1UR2m+A$KuE,"Rm\J]AKp:6dHY:eDX=J_N08CqGA3Dcoo"T>C,q*rKD!A9UEYbje?)r($?
dI>'Xqt7kBkFAm+p;UMX6KhDnRs!ee3<S]Ac?r[RI1JXb8(H#mO?J,CrA5tY.@RrCL^cp0C,V
LpdhRH+Tbm_f-cD3@bT<-=]A72<`jGS\UUD-je<RYN@m*u^C%*#i7E_b$H.d._g5)%qW!t\'i
s7?ch(R_96g)6n;t:#4hQ*cf/q*,WD`SON5Sd_MS;'it\[MaS.Q/908fEJm1]Ad.]A02o.]A'HB
ojR$geTof9&dLf$<V-_&JIXM`(\32lIS"aG[DlUt*s2QHeW+6p%`N8O*2Kk>o6n_)pgU*`6$
:k;q'opAK@SF:kFN68?]AIB;LB5=TdQjgo[a_NeE*jnlNlKW@_0IMpb+nb%[T$*\NVaGAks+*
(d@B4t<TcSl_]ASc[<iV@\UtC0:.f[#I>b3]A+E;WW%r@:]Ao[4_3+2HCX-_C@V;2-X]At`uk=#T
Ukel_gbDtel1,kVkmNCL03%1\oeR(CS"0Ol;YO;UOGYt(V$aWKE*EMQ_@Zck*LrNcMG4YbT`
#'ab:!-D!IC"tn*%N"C``p5h?!!&+Kh;t)GsK_^[e^rIlG8PGZEEPCZ#0<l48dkUpf#-#7AY
1`[]A]A:'/&+LNjp1Ug35<FX1,-CFM.PJ7#b[(PQ'&7`D21e&8Wlt8o0[j<'B<(<<g4N+1-=KG
X@Cb8jMli6mdC6rd=:U$X$[2<gWh$3^.4nK2-6NDl5?^]A/n<(s[sTSjEiuMI_G?H#af<tn`C
HuE<QDj,>IA4mUE4ifE4-hRE$*!$o_>T+dp7EmRqBtrFRL>`r3b`?*BT-jg@a(OMS.!,K@V2
WhmH_c-J)f:<4]A_VBHfa8*to]AX$c(Ei,]AXjoL[uMApfDG7^WsfgB@$*L?-$g]A$SMaj*="g&S
N!.0lCsT7OajBor\ZGGerCUdS'LfESZ/@LYC"U8,.qRt7?9eF+qPC9RR8N!(6B7ehtJ>-1F$
$^*T!ffhW%F4Qoa'9R^5#Jo4V4->=&34GNCW$qlr2!LSP7X3s[QJJHRdR&m^IK@]Am:4V/9.%
_$5]A'!+:N;qJ%OroQ_:Y!]AY\c33O.&laQ\7!5A54a8EVXQS2/Ce,m$C/Pli)!jt9F:^bbS[6
Q<?$e,6c5V5e?eGfX9M6S3NlgoC@EK6<IRIOgAkqD9[CX_bJbG7Q7?i_g/"g@tB^]AIU<s(:c
rTX,&QVt^&\q#]AZn^;.E>H[`^(T)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM04"/>
<Widget widgetName="rqsx_c"/>
<Widget widgetName="report3"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="502"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="100" width="375" height="538"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="558e69b5-51c5-441c-bb3d-dbee6a059329"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[571500,1143000,571500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[426346,3981691,426346,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="para_tab" columnName="name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="1" value="0" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report0" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B3]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[ 
_g().options.form.getWidgetByName("tabpane1").showCardByIndex(a);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tnm=$$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80">
<foreground>
<FineColor color="-759737" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=&B2-1]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=‘’]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&;s;qE)25m<T)/fe2JW74ue^rN/^-.",D7%mL4Z@o^g![;34/-/'P`MGn*Ks6255n"nDL+
O^NMP_"WJd;rB6,c)[66]ARRT"N70pAOMnhTVI71[gBoREYE^o/ZQ8ht`i#q45aO^&";gJL-6
*/i#.9=lTDIIX`"E1Zm22JN6O>83?^,M9/MHp\q>u;G)XZoM8"l"a+9@)U^:j"=:88DiZ5kR
^"@r6m/_#rA+\*g[Y2*p[*/mrqPVlB,"jkF!1kQIFj8S.g1R)D1/Z\F?XZs2uFCtn0e+PXq;
8.Hl$K*ooYGUEl-Et'h0j>\dbY&8;=/pI1I#!mBFjiq_\/`98q!Bk@[bEBng9+BFQ/kX-i\3
SSH6h^\V@KFGb>\bcR8e!r&mEQ"+6;4]APDE]A$6[8\tbFUCMp5WS3'a3r6g`E]AAI,teFf57(Z
sY@/;pKCAgoLW%(bp(m%'DUgPlBLkq9TfQ6?HR(b92:S<LC!,&#8D9*[T=CgfM+QWYO@Gd6X
u$YLMLDB//7Qh4AT?_)$@*OoQp#3?FGGCG09TC-,p&@Eb[2*JiiCir9:=)m0Fj@OEWq55>C2
.ZY"s5Q2-G?Gp/DUQ8M26u=;e#BY4L0$W)Q<i-6F,[B1g*@1qUhUjTlfZg<n^"W$4]AO5grH1
c$_d;,-Cr5FFf"&sGNPf#H*'EgGAin="20#53Hpg5mfL%E)h#omjVdTB;GO;;V'n^,)Q'2ue
Dlp(,EmWhDA56n#^!%'<"B51KM5q4ench#p/e5*Wj5%FCY8=[,<L%[I`d0I[m:U&V14La6<a
`E(qF\BXgDP\,&(_d77lp78qN!:Jd[K3O4-UDrZjf#rP(rL4S<Qr(=pYY`0;P,@3a?^T"tb5
PI!F5SoW8\%]AB\ENa)pDjbCSaD\`s^OYL=PaGh18!PSrIuj-jpJp&1^eC#]AZ(0_FsR4W&QCC
Jn-ZmeDua^RV^.qNic3lgOfC2'N[,ZeUNSRe$FjG<!oS`0o]AH#R+-&*eFR=%I;W?)CZ7H1Gn
,dfT:/+>X)(SQ+B2BmTP&4i=g:Ci$_SRhAth=0(!bTH*0I:cs[idjN2e*%'?AZPA73rYE,pZ
E74Au,-)GFOnXQKJ%Osgcd[,S,MYVDgR?tG1>KCF*^>Ej2@<1s9mjB[f<$#`Ai!*]AN.f\`pl
E%6'lOcINR9=Hf,_rWTg(Xoj6@=G@T;!-S!;q_&P8L<Snsq0[Ajtt>7V;oHF5lobJ6p?BH3V
)CW(L[-XNmlL;^J36\/S);M<MEi>^uC8FkM[/q&ej[R(O<E->oYq$0&FrcS7oL+[q"BR-J$/
2QblZI-k]Ac5dkg.Y1OkbAFqUOGNH>^h`^1GTiSLOD4$K>;P6fZ!r0'8K9b@'eiYGd_1R0e,q
:!TO<ClPRQ0+_Gbj;)k\j!_E_(Cr^@4YrH./uS8ap&drst(K4*@&*SW8G'%`!\D?&Be)\bR,
R[n$I;l2V;E1T%1DjlUiNI\c^C$d%NDBrNRLoG0H:aBi]Ar,0kRdFQh^SYl1%[L\O)6^MD7m`
XY/%E:;ckDGuT<+\[-DIuVAH$AlFYWpWR9P"CC3H2kG??((CSD5%Q1j^Ab#qMlS0B0tL52hb
=XtFikm'u9b$^q4":Zr7@pVn0/SBAb0RlG-Q(f]A,X"'.p"3a#6I)8V\5]AF'+dGq&n?SHKPVe
*OX$#9\1"AECoj0$s\OZ<'5sMb@L!OS:_&fsd9dB_reL_\eBMAP8j$<D',I&6dS2*6iT>'iV
%.dl(!8a`Q,?)]AYVZr?e942O5;rh5u'K;1O"qJQ?ThKZ!8r9m+E0F^l1LChSEE&JPu9[f=^f
CI2Qd4AZWn?mcJ]Ar#H<?nH.J2:/94W.%q,0r=Kt[#]AGCZ8&2PpTbs=_r.[id0?eF"R7ogoN[
_1?3D/7h<a`ca#l^/[aLT+IY7*F<_uV+IFCO%:Z#eheN3P5A:2A2XPL>bT_!`M+=p5'T0q%[
G?7h9=]A%q4@RS/\6q.#<AOl"+AeGQn_7D+R75GY9]A5I(>dn3GQsp22DR+`9Li8)pZ"Ms4&(X
cg&-2F7!Uo;L<2R]ApDU\^:<h3S++T#9VHW%jcX;^D[T`KCHV/+>.`m;EWoe>ugA[/hkXl<%2
4"g]AQPW>Xa/-BUReM^DcB1X@eUrDZac*\NdNZh#T&BUd9o-Ad5gCd[opX.u2%6ga9c;>jU'C
HI/r&9a;k$kX?Nqkoc8h0mBTJq"$E:Kc\#?A;7J41&R^;T=Fc,G]A$X'_C1_HUpic\eqiDTCX
8-_)q?P7I'A$orjmjW2iPTB@ZJ%+aD&/hpV]A]A$csrg4iEW/g<S',h+9@e>]AEM`&D-Q)@?op;
s]A0Jq]Af9Y]A"M3Ck+Vm8640$r=>^2M4gEcO(@L"n5>QgJVS!k+i8`iO&^g!otmF8F1i`^[)n?
E&jmP&%E?>1o"G<p1$_IcttBWUOi#=_-jfG]A.Xt=09PK1:-fTm';d7s6iZ>C-?U4\fhIT-LL
r5_QQHc?)KS>1Yr3Xgr7JVC)!rnl`#^XG'tX\`br"X>=ED)S5nYb\3JP.?UT>D[DOuKj2;KG
eRKKGUH!RMEb6Kg#u%2a/D2=M44/6_kFQpA\SX#p$SQ!D<kG3M8q;mfeMK!S26^t8q-LGQrR
0bd`kc^AegS3?h#g?"ZDJTn;B6[^B&.R4fGQd/k-oQ/4o_6/$u8Bek,b$:juCNZ*FSPP:=O_
r+ntZ)F<IXjNTXERNG4"9B1MVi/%6Vt`$ePl)LsqE@CNPs\:59%KM^(p,Brf+e=HmZ<]ALl5L
\O0UIO!)uHIB>Z$^P)`D^I"GB';"+psrf[5lNBXM>tq?3:!G[T^0teZ6d-Z:Z(dPD@1ASmMq
+$*NOd".ghHZ`5EXiea0A$Xq#QS<((ldnRVT/jXS(L.kj?.W\oC^#!2H\c;J3Oj?O,CVj0"S
62FHao#(.H89^<6Y".-HrU^N8X-#::\'5+DClAU>,;`WWmhk0?lY''_,eVnXSK,K`2MW]AHSg
irKX:'NO<CmYoZq6kS`\JX1O?K+c(-_0VFheouM^[V(S#Tc@6AJ^]AC&l-[[t2+>IMd06:o*N
0i;?/MT<6^@.?Pp]ANN"lOL@ttf&F36g]A;,O]A0,LGQX]AJlnQ>GuA+cV%RF]A=rDPG'%u?H#[+F
sVkk0Q8f[YqJ'`8>'$"XD*K$kS97%2-5F16]Aj)[YJ;r6C`?EgB/L;#N]A?@iS\53(D-Y%mX6d
7ELh*sl;G6!t-Qr$7BWoARJ7%8qpCk@!_L'em]A4[Yj2f&k%%)qaDf3j7ho!`Ng;p$LC76B[P
`!8"N*-:8_gQ&HA>Ug$`s5qq&Y;HtpfYNVrBM_HNoQ#BJ'A.E*R]AO4]A;r;*dW1\_L.]AB"l$7
`!IJdO!+TRmM#W;dC.a'.q73IPl<1!]A1,%7snX2q^-2Yk?&BRH(Rno;9i*9c!bB1%PVjLd;G
Y&;(h<+9jQV5n%36K,DNL"u1p"$t9eJc`tOcRn(q=lS4!FMf7?VSU--4_9DH3l8dEOYGTRWZ
Bhn,k(QVCK,DNL#4[1q9l1Y/bN7`SQV?4\Hi!06qr[[-If]A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="72" width="375" height="28"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="85d99569-04cc-4dac-a5e4-41f72127d470"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList/>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList/>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="51" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DATA01');
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA01"/>
<WidgetID widgetID="d47a610e-975a-4c84-8e0d-fb4a89bd56e9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[524786,723900,723900,162045,492980,798653,798653,1111169,952500,162045,524786,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,38100,2590800,571500,38100,38100,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="1">
<O t="DSColumn">
<Attributes dsName="Embedded1" columnName="指标"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="9" s="2">
<O t="DSColumn">
<Attributes dsName="Embedded1" columnName="指标"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="Embedded1" columnName="单位"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="5" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<O t="Image">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" cs="2" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗2">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/CMPY_SLY/演示/YDZQS/经营画像_弹窗1.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="DSColumn">
<Attributes dsName="Embedded1" columnName="金额"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=FORMAT($$$,"#,##0") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" cs="2" s="7">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.area.VanChartAreaPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrLine">
<VanAttrLine>
<Attr lineType="solid" lineWidth="2.0" lineStyle="0" nullValueBreak="true"/>
</VanAttrLine>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrMarker">
<VanAttrMarker>
<Attr isCommon="true" anchorSize="22.0" markerType="NullMarker" radius="3.5" width="30.0" height="30.0"/>
<Background name="NullBackground"/>
</VanAttrMarker>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrAreaSeriesFillColorBackground">
<AttrAreaSeriesFillColorBackground>
<Attr alpha="0.15"/>
</AttrAreaSeriesFillColorBackground>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="false"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="false"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
</Plot>
<ChartDefinition>
<MoreNameCDDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[data_信用收入]]></Name>
</TableData>
<CategoryName value="ColName1"/>
<ChartSummaryColumn name="ColName2" function="com.fr.data.util.function.SumFunction" customName="ColName2"/>
</MoreNameCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="a8a02903-64c9-45f1-9fa0-522eb9468dc9"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="Embedded1" columnName="同比"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3) = 0,"","较上年&nbsp;<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'-','')) + "" + if(ISNULL($$$) = 'true','--',format($$$,"#0.0%")) + "</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="9" cs="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border>
<Left style="17">
<color>
<FineColor color="-1183497" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="17">
<color>
<FineColor color="-1183497" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m(?n/P\\bQ4=C7T$;-50$%dhp1a7`".3[-4KZ!l=3@G/(X-?/6FE`]A9!Mm>;#jE=t).4<Vjr
$QMk<3adn'AJopVXCDcTKMoqqc>[C[W3,-&"kPM(]ANp5bR^(O/8(,T[Q*?59^0qP<@UXo,pl
F@i6^%<Mu4L1"$mg[J6NK6UjS"kK]AVJ?TuG8`5G&us1nKrH_tr6"Gp2X9\1nGqC#\G7(5M[>
%f%&$ff,j7NF#D;s&g17i)g7P%EZVDO)S5oZQh"96bIDa$GI]AeCVaV30%XPD6#6<Wj><A/TP
.8Ua&@5Zd<nlLhFHCZo9:EUZm(1#kXC2UR);jRaIt,-)n4u8%7i?a%Qn&GVZT%dIa*8;V]AQT
&HmFYJ.)AZN=;@;&U#,f.;R#m^UO;.@%oXb!ib-OK)@eJD0Y:YJO$4O8apUgpG@tX?3PnE*r
b=EJaJJFn!PnOK=8\t\"Cg"SS#J1M'#&RJd6V>l?W'IpW"Mj&mY2S:r]At`MZ:T269@Jo"-\<
RZ1!=ZqWegnB;8to^3&1QR]AqMT"A69qF6\8Td>PK:rs)^*ru:reFi-jq>[^.7Va+Uao8Z#oK
:$O`Z`gjpgRQfI;1]AFV:d:+Kd3-g5cr^!br7="o9Wm.+&+^Y9l4,":.jO*pK+ik)a%0L28ZD
Te2?Z5BL8@u$BlRAd"IcoAWMU'f+&DD#LWVIrmLt.d]A)R#j[8MSs@tC?5fX,t#;'&K'plaf1
:4Z_Yo%VO_EWCYSi-,k1>V8&HRZ^-WW1f;t+>RQql6!?k26%b%JW/[O'/<oO8r%)lB@!23EG
$:)gJ5g@^)ZE\GO)GS#D'V4nD8b?J#.WEagLN2ht6cra49Fu<Q&S_PJT1H'#^a[H3T>jIkb,
;KlCMqqY\K``RNDeoOZMTgA>KZHLBa:P"_g,j4ZI^aiJ.]AY!B1eV.G$g+<*LiLd?sj33J8_N
T@ij]A_iHm'Q8"6?.C^A"rhW6)t0&of#Smcpg\GkR"!aKVZBK2"Ma@ZUIf4r=qt52%oL"^&J6
2QZEpp5?9R-9iMpGDV4H!q+h]A4EAn?.DRkQqu=CF_@_l?oS!W0L)+HWKH=5T;e2k#t\Y*#1)
e)3\diVd%20GkC@)*-XaLKPWh5CM6;/.n[;4$22A;"RSN^1b-LD\86WG'Fqtjp)dGN_gG3W`
RR(`gIrtRaUGQpD*<M]Ab@`SZf$(b@!R_^K*#8uQOSM`k!]ADG]Ak`\a%gYIo)F#H!$LuU5`#,E
$B`/r[NIf81>aH@IT5O*Z97H3bg#,`6m7c!LE(e+sf]AsOEdS7T3PtR=i[D(MR[5Vmi9H*@cW
Zdb>o09r$2[%O%dYuP%F50P/O75F/X[a-LQ^2l0I)]A7Z\iIgQ8;bAI,R?sZ%R*>,Da+f3o%[
@hS653;MrHHo,r99qkKU5A+l]A^]AjPS>2E*=g2r(H;Mh@t6WEcL"j@H5sf:=-&O9!dRs@u4jS
T:n.t3*BR!5+#9/(qEh:ea9*N'tk^YDL!"[Y$2MjAA=@Ua.9hJ;u/o@aZU*oE(ejr<@D3)d%
6H"E3/Cp4,]A@03e/=<`%PKs!>0?]A&KVsh.OFU>YUroT[kDUm*"X@"cXE,L/fX\7%Vrda*bui
*]Aer[Hf]AWDH5l'"`/VE".m_(cB7k`@,ragnGD9CRsPS>X_C/fbbGh-=!7KX[t3Js@fq!LGN+
O.N6b?8NhQs5Irk4=*rFZE#'rLjn#;e`_nF)+S>WJjM,I$:tH.p`jr2I;4uT;l]AM6%=EuGB_
`-WY^_H7tue#WbH59UO7H:k&[QrQ]AA#q`ietHN#JTANimfA.1<]A&r9a"m%'0:I+$5nh:mTTN
,\`5!B*ZJYlct\PT./+1bg["jIqfZUC'i@!'Q`"<R)1u)6cd:+:U&o?OB`B"-`%Wb'@>7[G,
oj%_U-F#4RYf*k%AVl/UfgS67ruNTNL""0]A>XD(L1k.p=4[]Ae18I^("&&2(N#p[$5aJ9lq)?
C@&0GS!`s(klp&>*Hq5,CG"p/j9fLsDKP?jR^g\EV"O<pr9&01>rE*<)mXm*Z8+.kOnK??A(
<50-@n3Dg/]AY<JPWMubf@ct0b7pCeNcUH3Y1W"SA$lB%@ptjYl0IZ9\C%#e2pVHCRVJuT[&5
WB#g_I4XtE1c)5<_qI,hpX&n*n"jfWeD[b-qLdMglGoV^VcUq4`t;BJq>BLp.9;%lD:0W@%2
%*RiIXQ]AL<8kP@k]A/RkZb0%l*^R'2%+NHa;Z6W#d@XJs28o=D8)M@;I[L/[UrNXO%8.D0s/l
ZYSfEHVb]AVegSj$eP%LX&8kbjND>ig,K'GQ3b\hsr3qNPd-&(e7b!L5RJ"dJ9Kd:nE5I9;ij
^oCeE*J!OsK(I")'6mumB4JUO0WjoR+ZRjZO!%R;@;Yk.@_ASr1l\HD20cEN=\aLUK,;N4r'
k2fbVOt!+8=Slp&528u\Q2S[hl\JR`efk=KR^Z2T^R%&<#%nO9MVNgbD4[%4?4agqsqt4iN3
p[DN/k\Maq[LFRBS-p6R5V2JqSNa9d9/K_?F;XCLafQ_q_f+JK6$9$*bBaK*\H:IjBJ\,P0k
hNrQaa(D/]A*T05BifF^!O1;l4UXf_?V;.WY'j,@4H[!bh4h4*3IqsHuY=D"DI0NQ8m;iRLN3
+Cq)EArmDp6]AM^\a.?]AIX[o=Y)QG>+VGU)9RHNaJ2>?a,q1Xpq$95j@V^+,i3L(aI%`h\]A.V
oU1(iWD]AGM?bfY5:oKo61:<Q:$eD+Z+83GbJ%[pFuddg%QFLB(iWZ1gX>)d<*%b73?f%9*d!
g-dYN.8\qW."8IkCOe5:^HCnGX2RZPa-nUjC#I.'EM"E:cF<n9'&iKCn$\=A*NqGq(5u#a".
DYma^U\c"#0VC+sgh&gFAT_NnP8Da3+kX64/f.'c]AdfpaN3=m_"(BX)_B1\Z$'g6D`;Q;o!c
?>5$XA)WH2QmHe`G(,fmS^7,nd?`[mHFp=Ym!C*%m>0UVX%p8=1&Gb5FODNmJaij#=i9QeOZ
Omk.+b,k6UrGIL4?nQoAK#7]AGAfMX!u'/p;4$,YY5.6+R?`7`-8OA)uU(DH^V_&406n?T7l$
.<VX"Ler;qC((W_>jPm>Phe'PVVREf^4QJI_p"j.gIQH9'8(/+Dq#%e4SPLmb\[-RO;mg^&W
?G-$-"lZ;h*pUg/O]Ak`054dW\d!D(,`MUnCNF*TO0=N<9XZ$-HN0IpL9jp@r%MB?5qcU"Z")
JW>+7TpEep1Y72!P@ah&6.:tt-<S:o_HT'V?l?=+=KUNCGd0QgGL%t7W7?:?r=@^t^H.P(E5
MVFn@PK6BWin?>O1?F9=#Ac;pqPOia8d=kBd7C=30NVq-9FOKk?<1n-!^Xn\+7AWj;+Yh_OY
70GoY1;mbmEepc70rUhVbC3A(&aP9B%c>V%&k>q@ZbYl@#^EA30+1aqEmqcH]A!UF.KH/cD)f
`;Cr9PF*OI,Cp2]ALF*8T,R[0?>*RqeoeN"3IF7#p$o)A5/l'f8OA@OW\m^#2a^7@fZ]AN+u3;
9/NBpn56iMMMf6!o+C<77s0U:Q5@VO3HjGM>Pj4PcYcq(Io!-*V?n&W^dd/pXt!P#qsdhh6Y
Djelis_`j^%ABfhs@!+4/U91JOZEe^/2]A/B.B[)6oBnJE'ikG'2Nb952);%%MS=4m$8lk*mX
%M7'1TO'EPS$b^a6-O=g?Ktj1h[GDL_Yj<.Mq*N5@M>FV:)YcGEMl`,FId#3PfM9-/m0bH0o
RcrI//uung70%aV882C-oC6h=8)r/A)D8DTPki.iDs^VPPlkS!W:$PZPg,FBtYJ.9Xmu_?>8
`&Tr6V_Teg!^sZRkGuRjm&TpN'7(_d1-ZTV&doJ.jgLib-:g&#iG3i!=U>9:SN4ed!EeE7o)
'g#"p"d-/'nYa$/'EIT7W)U3Mc8EIolf"iB)us2XaOl>?Xn@>(l-#A`^9r;Z>ji>=l*J.M1"
oM:),9AET%Nb.JT]ACl%80O<#?-p`==\?rl!Hj_,G#((:f&Nd*J']ACG3ra21</#J^^(ue64,3
D`0XMrHSs2qLX^=`IJ8)]Aje74o9`V?1$g1V0oHLqf&+G&G>FG5EiISg:sa^>gTY7q;43`Amb
DW[B.p]A`\_0o@Zac9UO)+<X*%DB#TO(6l2D41ep\.bgR,.$fJ^X#Zdfs>HP5:tm6C=GbL=sa
1jf;r+R[0`+?AFK@W&RT,EtY"Xgh0KO9=gbGfitERHWUCf2HDS<2M(OR@T5-m'b'RoK=\Re0
R)_FQ:hgh5;^cu_5UFD&Lssdc&Vj2'T0>M[a_&nN:4M2bk4599\A\!htsA_Cc''`l&h54C$n
`j-)J'ALQT\/ad08_Mrr19Ss/_S_:9co&VB+\(C-SAic)m'6M]Aq@HTapf5hkJ6dfps43h5LL
7#qrQ<&ef]ABV8hLj?'Bi1ch^<U(5.RLV<^JmN#(*8]Aa"t+5\+(A.toR6[1><ZZ)Z"hH"fnEC
to&\p\U[20?\(SKq0PrEBQl8:P<c7ibuo![cH>GiaEX,+k-4]Am$d1->IXY/mZ<YkGeVgY3UB
E/tY+-UQceqiZ^s7[ME\,g7Xm<YYED'W0a.D=dINDRZQ6IB<1hr#aF@$%sBm:7p?$47/(%A=
7)d6NWPblT=ecp]AhCpd\pO.K5@pD5B,Mc*'jYFXX)U9FlA>BQGk,+Uk*,-r:>kpYh;lp!'I:
eu#qq7oDP8a(RU>7).6D2rb9eD+6#/MX&$sH"GoRkjrD5g3$u=VP;bOJLmDZqB[oH<91b/cK
f9I')(otIq4\gP(=(!/m@81Po"aVfB($E6jjOmq(M&i1QS?14P))nOni=,VEVC5rTc.<_@m7
+td96T3ppN0/Xd\-RNkID#.d[HlBVZDkm/W)'u#]A/9h(c?%VjU'Q;B$qQ`42RR'Vn)Ui5Gb0
0goMItjA-l/?-^r(&n?N/R8`4BOg:c"<A*lo/A32pAk<="iPsiap]A.)Q/am?Q<UrFEd(=igp
F'ELXk/2"[akci6MI0):Z0+X\)o$;^!BP[nnBk_8!t-PIhJ'AiGa/AR;MaUJEUYBV5k%^a13
d?M[uXg*!besk6,oCBrcA+biY6_l8tE?El0+G%c)[A(tLd+:#kpq;^!5/Sm$Al^MjXtDP;)%
)`ioH%_X/QiXT@O:EChF'"kokR449sM,dPUoqp^P(S#\C!,i\m0-_6A;A^&)%["aOCJ<dlV[
S-:\YtM5rK*SY]A18Zh[@@!uU<gQb_AqlkP0Bm4/@H\9o=QGPcaEcg<o%X'%?$0U,_sS3%*a2
-B2_%mCMfZbC7_>AYg#$ADJPqRaMP,sE%dlJ"_VDTLZk6+=QF4ac"sT7B<G0I=g^3/\<#$DC
!uRCh7T2\RSSl+qW82&[%08qr?V_uM:e1uigMJ_HJ4\1'l544&:XDjSAp%We/\VD*)=W2hcW
E[R2n;j$uPM$<CCrt#DPa[a25&oXE\e8X9CA3#kM2p._d3W%$N^ff-5-8`KpuI>"quX)KqH@
@SaBg2^)U%?Mrp*MTme]An=3+_((@5-NL1Cb6d%qGqK&rkH!.ALOC0T8B7.)u715'CMB/$,+N
=*967^>0&-UuJ^GU1,%\a-]AYY\!'nOcm4i67b<S$X`l;llsFOYEYuiXNdIA4ATWIE[SP2g$L
I"R-OLC2]AiLL</\P03]Al=4\RFZh.rRu>V+\?HnZ%D'Q^\=D'NK0eUoW>$dRoNZ%K(*c&6j!B
Bi5Ami?d=E2O@h>4:;&P80V5,lqq-<k@YaB3%j',.&nlf<M9;9kl>72S7FSIq4"L[e66Sr\Q
pOc#6eHn<l&q0>S.COp?acGh=*)E_i4SWVZo79l-L8R6K*QZZ_:Rp*Zs$)lG>i(OXYp\N<n,
dCU8\Zb`]A8mj`e[b1AOrHF//8+<R:p"J?hE`3roZTr4Y4##C&8ZSc9d(NMMh?^7&dn6k7)d2
YP<+>PIpfhXG_qCIcXA@_CUf+u?*Q@9#K,1+?C<k!OS**X2l^C>>MaaZpni%`4ND=R(@IXQH
Yo^r(eIiJB'nt-l4&bbD!4t-@BNkm(0Im-G@G1*#O!eU=l:@]A8QX8cWVnaq15]A]AV%$Si;fmB
RuQNR,NTM^*Jp;+5;2+^jFYq9PrM=g`.[j%G-Z&WQr'W#0A%4=b6")m5JNDI'dn5cJZ@97!Q
<e)Mr.SE?$ctM\YMakY(BpfX\dG2N#)X.laGt[q`N8dqhqHZ.4<LpMfp"1P;IgB0l9W@ub7_
kpj7TIQQHe3cLmMT=_9SF7-?qZjF^cjRG-+]AaX+2U.i+),p1t5*YW3V8u8o0mo:L3l[p\\I]A
Wb:jKHTtiCK/f;t&JDK8D_%#\1njTblRR@+;k^:3C:DGS8rU8KiDC?3;hR($R,4S\'<hIsuc
=@S.URMm6oCb/PKMM:eJd4?l0NFSQRb090*"O(pSP[<td,W1PC;#>*%/N9`]AS$5LG[>%]Ap*6
-U%iiY>7C5N7+9rEg5:=QT<2-r-1LC\]A@2"GdN\h__HbW6R*Un7=ei5&u;CcL&SW`k/J@/"L
K"QeNE/*=sH2dTM?"gRSL;Xo)$rHII6Cr"CG>U9+0`:SWYOG>NC'Wtf2P6-qUc93Tq\/s.FU
l[MAK4'>]Ag"<MReEr903&Ukb9*0rZ/b2@)342lW[`UP6;%>%)4S?^BSSf\QYQf&4Hq#,@i]Al
4]A$nuf"rf+"1ZYF*-<mg#EggW-%]AqSl2R9j,BAMkn?=\s>$13rHS0=+BeSSd&:?G_&#tg7ig
35Jq/[?R]Ai#h=fPs)JSJ$QV!s5No\P)1$%8o<hJ+)^h3>0$GTYi"#n2+a9ed/+>(LZ&hptu*
+1=pMQcCReSpM<Yf:3<LGIkpGkK3NiF)-Ngu)>-DX!eV1jPtd>i7m23o5LumH@:EG5]AN!#1&
Xek5.KKdZdS+&48*S?CW7WS/1Y*2#/8VmuEo3E?XZ"1*`?lGKI8E$uF-M(/]AeK/PC0'bqN?t
j*UKLcOKb.a$CC:d6<sVRsBF'LG\'IhpU100kRuBlWP5[M5G7#I3\m=C`BcC<mGq==3kn<7$
?kr1NM,9>tn%qddg`'%"N"?N)GU@>GtA<l5iXrqto9o[\p!uXQ4Z[&Es!FWn"54mIXuo2@(;
QkD\Yi41Gg9dc"/cfHMEFdIHEfcX4MlHZf9hn9(L=l8Id0B$Zc&JKY4%P;;6nqfo"F%_?"I:
),r.Rl=63p*&9PAD2W;GT+fO1l!+]A>.S=G5%d/bbLpt2V=h+"CO`\9*(ieM0rDB:f+(SR?>I
;7E?P)LY`cgq0kWMbj_3irNATJ;6e(`b^p/-=6g(%!p@Iak;3K.I`1lo=!dmlXfK^^S_G/'h
n_@Y;"IjNal(8Ko\$tOrTVc=SAL_joV[k5JSlLt[7WL-j$kC/@!0C-n5MQ2p+7/>m-6#m!@;
<A5@>)pT45pl?lZkV+^u)#MB=+)<&FImO&#U4e=Euo,WDVDNj$b@+<klUt[OKb]A'VNo++]A_^
Dmb3]AH-`H(%V,Be+Lc[e1Oo*@<g1+j"Z!f0I,:8YET>0p93Vr,W_F'0;_q0`,Jdln">M^F[#
5.>"R`uaUfjN(!=5Abk?*DGtc>6Ve`*.oJT+f*l`=ce:8"U!Eo+r;8988tk4p$0_>SW_[Cl\
LIZLUIo0A*Hq)9Hq)UK[N.=Ks)s;`g]A*[@Hh[f,q/bF:+\Po6ai_G#'8^M#Lkd]A"h)8;L`;d
M=$RR3Rnr$p"I!gm5Ci!MTbrY,SI!C/&hc>f:2.^:Y-H6s#>4b1Ne,j*h9N56KU>WbBFRmqU
JPE[Q`L]ALi`"FT^dfOV@>0.!l/aXQqAb8Tr"Pqc)RP3OS0lQ?5oCs=>,T-KSIBuS^r9Y>'Rb
>p">t`+Fn2;1@?,?^H:,?^]A(:?4of*8Wgei=X-BeE3E)hQBT/obOXi&LK3=4?T]A;A=s(i>*7
R8fe(:@]Am5!<4a^FVu>=b?EnrR%r)XbbBQX_!!@:hp6[2%52f$/3UZaf^-B8;7CbKKM9O)H;
a\\+r(7$+K5Z'@eVIXce+s63Y*aL2HVloW;e/Ap1V0GKsmSD&!>D"87&P_(&hh4As2MFt42Q
$HBRR9[bJU6JPj``++.fS!+$UkAK/VhTb5?=k5.!%UpL`FbC#dh-5@s-D+<!#Vr9+0Uk2XYj
PM&R9>P?hB$9MF7Z5(4/E:X$KY&@1mHu,jT"JB!qj'&Ff9B]A3j?shK?$E"LJ)m4WFFHLqntQ
++;7cC7l0ZV^9%>i5>AQbO@!pu5C&UhX4o+)k;bJ8bHM17L@@p\*DeplSh'CYANY&\`PBsDg
i19YI,38Eki-S=1-"cn'SL`<jZ>i8CQqc1g==hgpF4peZlP2;1!knB"Ie=7/@PNlC?(fp2+u
&[8@4Os#<;,0_hW$R>Fn?F2DpTLNhr@rXpPh"B><p4,BS2qEEo%-,"Z7jc_*0j9d1WC<)o;1
SP3$5DXntca8)SS,r4iEkLd/k*Cq1(KVSZU3dNP+3RikpL3[b$;5FNl*u<UMLLqMsU=5#73M
6T:Q8P7H`/E:K$GV)9ApGM'.sHXX)c*!D0<sA[hF[igl*=0W=MFumWLQ,[[a;@qgi!XgEK*j
$0<=NgI@l[)k$0Z9KjF4o,p[*Jk2spX<chs9Ms`qI3j2n/]A\_!\1`$kU#Uk\[r]ADNXC5M5Bg
Z#\)=lInp'q0g`%A0]A6MT(&)T(.4r6VdA+#4(8k*X5G^*p/ei`5M@PCWsJZXS(.ZT:Juo;hr
aKj7"6PDoQ]Aqkd%P%-5_J^=fGo[)Sp<oZNPjMobeGAF:kT]Aoq*2C\@6&rPqfrLqE'ja>p>]A5
A9?s?n]AbfK[<biJP`_R*/5tL>]A+OV#qGfQPH^:F`!sMUMVESI$$U)V6'3TQHp2ZNCO-ge5Eu
Ui<F&9'r-o3)8pR\g+:I>\!2&P*f,A7Ii//m-ipqf(jp2.>+]A?a9b@_@JON=Q+^Rd<VKm>W'
c-UA1,`q^&coCUKpiJIXKe1:_Q)k1VYH@#W,qn?FM_iA_jb//p7>p0(rFQKk?Qmj5aNsq]Au7
c$l$+"<fVGOLJRCO^$C!1Y6Ve@&<Jk\<bddmE3O:"jsmLC;"Ro+H2c%k%bKkLu\>o#\JM/lI
%Ort]A&^3,)P#iRlWh6`6TKG705.:Zj>r0fPN>1^ml<HEsccm_c8iNQ\r/$)a>,D3.#RZ63(o
4==n.d9`<>=3+0;FqA/d[Pa>Ao*\_F,E[BCe%9.k[q1]A:gf6/T(_iF'NbYaYGK4Ab\1$5b4j
\ZGag.em*3-uIoUN%mG:fZ;<Y@((NgbTJmVK\NW*4+R0(OOi-rH2(V-%oM/cf&QNX)\ieR(H
Am.@QQ)#5JJan7UFL!.R:hf.]AW#HIUi?N,.@^_BRh"ULpls.2_q-tQdoTShjX2&V:eik#*43
)N`]A$BN*+O#hcincQhQqkFW.@^gpVQe6D3Y21CIi9M!Nbs)d4ma^*qTXL#nr_.GF7EA?S"aZ
jmPdu0:=c)G5'(CSP@!AmO?t?tHi;E!S3Yce`X=oS/qa;]Al]Ag!M$Zk2\QBVhCWXC["gb(nm!
BCTB?atstnN`*04#XGl/*)Qe#NslDs?p>@9fii"``:o;V8<IUDDhX4\QK>X/N_\,a`l@<4AX
u,/pE3YCiF9pNaWC2X>aP<8BCSU"Q$"CYF)iC<#L7St'9n;;`8jkt_<ef-`_5XcEVJQa%,_%
lcuj%K7qJb/X3D^@<omrl)/Jj@]A=:DB-&0Al]A3h'X6[aY>mo_rClJa(H^gl$\ft0&ehd<?i,
j%D)mjQHYkMf"VcFuJd3j$a2!:WS'/(ie&2HANu%%$.2,o783T&1C<',3O`C'05u\0>=Og*l
THJ<]A#cW&2#&b=A9T[n<hBd[@B\NkUm]AB"D=%rJ-sI7W9aC$s/.Q<BnYt;)W2N&UKJ)=<ZtG
jLf,[/)]A==,trl9:hNXPXnOiCk(BKOOmO(a3VSh,Xof\4q</gK&F#Us##pcmFmmU8JDa"Z6>
+"_r%Wnd%skX2D.6Q5Y5-Z[PD8thMZkR6mc0l/Yn`lTa6/"E6>MA0N-_G]A#[geJfq_1Dj8CX
TZ&9:G7m,3.&Qt_0D<EP(:>kEG@`ti0gkJNQF(L)9GU<'ESXoNCW[q-)*-<R+r%0`O&'!D31
oK3R7#7^_Q58,`ZL`TbJ*k+OY33OZ&MWG;3SoQ[^oR'dgV9Dr6X<Dgfh@Jrg>MN`Ueo0$_8$
b\WuBNKcLp;>m-]A(9&jkR3G/>`iB6H<^),3,@g[,X5Z8kXc+TgsMc[S"e/J:(4G:\P*`FWD*
(%-JUGFkr9]ASqf.6([RmDE/eG3NW7ZiJ^*=X:>RsVYeclW[@.uFHI3dH!e8#_6"%2[W@j=_U
K2EmN;sPhf1RIblQAhA@V-pWUCMh#Dr8"'ms-nNi-N8q:=*p4=;Y[9B-cO)/ahFG':q#-k7`
kF'FVIN7Eo/cGbYDI6RWq;Sq6CghW`b0W'3>nq6KY=#>RV)rUM!;mj<M2&/0:^?!#hmYfi(Y
_ih$p".nb_jdh>njLOIJl7*LGbqq2^0mWn9r0@4N/ad>^RnPS[t`D,<?Y+J0<=$bf;<giTk3
9#hH"9]ATZ'`6el!B+`*Dl7G7QUl/Moi<h]A&n#Lh0N2&P:Nm6BUeGduC@'-fF9l%cL0@hJB<@
0@<<Y]ALOh#BC4Y@,1)gT=$VoYbahQ\'Xr0NZ.uioPLiRjHFX=Yg_4X#0h!jlXqWLO0`hK@k=
5eC:N?A?+b>r4mhI]ANl9$19!Pe0kUru2>5md""T1H67Qut-6hQm:[BmhF25>=HP`7+i:c<i?
>T()phd$lj3i+DEVlmD,]A!4q'CK46f4#9PlMNqjjq5<nf;IL67)@n;QM?ikmC,m:=kX_YdP?
!Zt9fM&1bV/3*'%_I0J%=&j[?Skn&f*1RA=$;(K]AXOQdR>C;2/HSIN[/`@eQO.fggtN)H[9m
;.rLsJkL0Bfe7#KYNo_6@^)9,&a\o,JGU;Y&0Xii1`q<&]A.!>,qlfX%F+pmhI,H.%Kfd^U;K
;FefsG`*YL86U`Y*k[U4dC@C0=V?YiGq_QB>mHDQ8'Wg!>bWI:&(X*bW#el*h*aFH*eYZDiF
#,D&c6_N2mm##*lgZ]A:'Zb<rF3q+Hs[\E`0<H176N#pFO8+hj.#X;*AaJan91Z*rgG@#naa(
'SW=a3qp&*e6G;T(qbE"G-So*>mJM,m%oV5Y'u+c=T#?YN!Sd_/r6-_b=`2T>R`nFc[h`I"o
U#ahX;b,fOaB$7"uiFX0WC4S%0Lkc)1tO=?EM1pj]A7q>b3IIa\ek6%Uqn7iaN463dXi>q"XT
UC-l!%rg9?PRlQN_@[;(Q&?SU2GC"IlJ\8>)9e>qTNK%+P+Mtu@*1atq*J`qTi&F>28NC/^a
]AM"Nj7W(!uIr'akVHDYlTDQ=TG5sHrD$XR1j#p>ffDcs0SM-/i`.W-Sh.bl\e.1=H3lh(9)o
QebP,RC.FV;=%"u/\MQo=$3/dqH,?V&%,T-jHrR"/k2I=K5LCt?Cf#!Ku)RGp?+W!)u3<u=&
ri`fl*>OdqQdJgW;4fMR[QOJ_R)^=a`Er"->=-^lKe5l&nJ8$nBh=,^+-":1"l#u7SjGdW\H
B!G0PCF'nc"g)X1AOT]A4$PK>;?LNFHtR;6,?=4.U!m%mCdIpjB]AO*5d\P/'(>A8G3S8U'I9r
Fh00MRm"sOHW4[+SsFYGmKJmH&:43?Sk`QY_[o&dP^7JQQq_8&2C&$@bfD^J!E=!R?]ASbO/g
MZ4h-&S,62j-R?NQY:&X__T/ofEX)Rs$GpT\&1dqi-T(bl+[Mb99SWu$l.tWI?qcNHZ;0"\u
_%o;U-I2#7%jV\(j/V7idtp2hDhOr^/ca2l=(G^=jDSpT>c]AIrISe1e]A%&/G).)N>!!'+1@q
40:l1]A6/SOXRH'O)JR:V(JR@8YZs0R_@g)846qQ%.IZDfUZY83;:&W5f?\jf&,a%O`Xj4$-o
^?PNE0Sq;p<[P\fKiU)iJ\5V0(`Bc>/8\7?]Ao@tdiU?<EoRroS?tKlE29ZTYl9"n=JD0W#:e
':X.%5$EQ+sHDe]Aa;O;tG]A4`?@g#Y'jeO&rWi;pE"0?fUL>e+T_dQpUd)a$4-hK;o0mN/Pr,
(8n#F5Hmu)7E.>>rLd+p&a0,\P%hMBL.52R0h')HNeE8>I7>(E7/c.JY/<3$oCX^AWZt;FpI
l]Ad//A"5At7.Pr4[O.KTJu"U.t!/5M,'<cDIr+Q/M[i9fQ)lYT]AF0i\VHOP7$u-NaLJ<-nbE
)!a01.&79q$(!i8qg'[Pbi+;]ApQ21Z]AmfI8!5=boZ5NoF)/'Sj5VStHE]A%$`pgoLCP;**'aI
@FDp:bW$'bS%i:5+%EN+HXf;XD,=.NDQjX?0)7'pQV.B>s6jT2E\hYo26l^:VG^H*[R9>E3f
K^2D.IM^67MgTa\cW`CHi_=<>5]Am1iD.9*>m=L-,G'$U0pEQp;&Y\GaY*dn>SQ$Y(Ec&1P]Ag
R*PdWn:CY=M]AT@0QMTqn*c7ep@A([=lac$^g.hQo"Zu4%\Y"i$<R>psgd>P[c)@gf1r\O_TW
U*NGV)9%?P94Z,MFSjq2aE;QA']A/8BQG^ikD;ka$YLo!Qa\qkfHe!E\>_pLU`,;1k(6L-9,6
l%4XG*pe3@`!P)B?Xrj#JB'j'#>Gj'1B7A]AcIs]AKW!6;RR&1f[hCF+=WIm4!J@ONt0o<D(.A
bIH7^J>ts_Jn>GJ>:('C2g$;2orW)LBi1cCb.+=ZUPEhJ@%hrqSusJ"F,+q:8!)8lmH%lS`=
cIRu>3WSLfbkBqtFaD+^9V-`Zf)\<eRY;,@Pd51GY<Qf*n1i2"g),eG^:^79W[E;O\Y_*Ddo
"keTrIQta"gFF0BaZ4Z^(]A`Gj#dGFtqrIjsHBgS'lC)TIMs<hMip*'b!6fTde++?bl#&bX%_
;K#+!6U+bqh873I,o98V1gu?8(C$gL[3+%'jj6o8,76nu)9K;%k!@N`U8n+j++&aZ$uC';X.
o0C'jj0bVcM@.>KD^%gW!FR@(ij'$oR6NZrPqrdLPS]AI<T$YH(D/$u'8[6n26pMOo)c[9,b>
+%"PKV/fGokIRsi<Pn>k=8?G.JGI^hKV[QRtj2'1*^;NI`'-m#b2&KaoD'OpD\[q0BdZN\"I
uD#5\2)fkl5+%H`V?h/Ir<TiJl=VVB0]A1gY\Sg"Yt+)PH:W+.f!+?;#IBA6\[hX6<!rMrbPC
He9ksJo"u-s"_]Aki%(X]A?AH9S82QDS=6ON^>[WfPc5tI'CcRPp0$NOb&:]AonEHLmQM6>gP6E
Ua7gC7[.pg;%*kPV!0??ag0KAU:DbVuY>b!VBDr"=X4-nZ[D)t9b9A&u9ahGs!R<rKS:4\!f
l&Hgh!P`LD`UL<63j_`"9^\#b(&lqVPY<Bom<jn9BcA#QFJ$?9N2>W0=er6<<S3(J&ieVcH0
m&jUqIJD(?_#`5BMAM;p30aH[+0a$bP-Xp='G6&'s8R>lg$E<*2j.!H=9hfLI##gVcjj/Q&F
?Q^Q*V5EV6+E,j!o[ZgK\I`fJ+=*T6.ZM:/+&*9*9Zh]A?Y-hnt_^LZVk$FbZQuD1$?dQN*<8
[>0D2h^lgjG51HW3qLA0i>0-$,2Ifq`;L$Z]AbBtTAS2j!B"U]A$!`o*M3<AWnRnI'Bm]AV0_;u
]A.glP*\ukcQ_.Uh+/L`[<(W+96;/&u^h??`%@&#EJSAfU)b(ZctG&Ll<l-.6Kk>-U%m*%k@2
o?*U_fQQXEq-8K3WkQY0d7-MPr2XBGFf9-!E:a9unoWW#fNGQ;n_soqiJ?2)o'HQAiE8gEFn
0+m6n<3WWJq9^]A:i??'[!gfPF"WgK,2bh@I4H`Aj>if+Uae^Me^`3NQ`.u;O0SoUQbl9+cWo
>@rq"(`#UQ%>S9Oh[O'k`N0jHM/+0W#YX,DJrPR%%IJ5ksaJTCnEoB0_*(S;a^LRLQ%$@e;m
P`+Zo7.S7P>u2J''T*+lSf*e/S6kt#&Bd+";*a;n"i,E17HI]A[HZADkkr:cTU(hMGXmJR565
WB1$KsISO+o:pI;/YM\d0&F3:,]Aqn6k)EituM%VC*5(1eU"*glX#;"BOuWY*?^OmF)/q]AEh(
P11#`6a*JiAVU#=c.DF'UVEAM7D_qrXk`,Ih3F68_'AG(QqLKqb\WImj]A(n3oT"M;;BuJGf:
Tn5t`W:B[i&K4TRug=2X/&]AA\"2J:b33!'VP[tl/F(/C5,P6rFI+Z$D/h35Y\n9rCVr8bNo:
'p3knb:rco>f'(0&NDY+(e6Lh_MgF@+6@5=ZIhE;_*`)3U,d8Am'VPTZCDa>EDh=6&^CHq0!
8HN?eF$>VU@ARsGl<AX#\Lt%/9%@Z-2l<0j"nL%:Q[O8(A4kO!k=t,IB?.Ld_n"G>0cD^b;F
OLS`*_LGCJ"&N%C[=De.=:<&%#m"TqlHT"</iGlSU$idqsEu1%6/?4>c-;[*hNEo@mq<C94>
Iqc2O]A)D&:e=9$!>YAm!hd9I'hhf1)HI4e;X<>-[7%5j2]A%g+Ik3MG8lRb5hWPD4\Z#4<90_
.K&8N[p3QX,8PGa?S`UCB4q%!pOA["MO+Mq9q.4H.:^-Y#fXE<T4L$I[B182IH3G;haS@:KC
C9+dR!<#+q[-2&l-\L+iWm:/LZqq/W->]APV*jYl*gZcR`\N5'\niEn4J#dF*mR882h"Wd^dZ
(DaOP97Xi5k7Rdsg#[(r;c4u!hi#uDh7l!O?;78RBoV2DG\,'2PHU`i)`9B+6EO-qRAFGm;O
*]AKPb[dVART,(WO4b$:]A3'f`-E9J!+tAh<G#k^.W8%*nV7rOnULkIQ^VcZpMFk<$VToGi%'C
;'43&u^<D$B:b\&n?a%O<;q:dsSOq\iM+gH2E,:HY!8&uWMNDW!4.R*05QG?D[E=E#(NnE&[
X?7iY-A3#lg.8;?R7&%&KY_4lC$0`1+]AC!5'"dCRuklg;p/6A+g/XT48ba<Ib:>Z^=!a=(_C
tb"j3fM@+ZG32um$Y>?.(73rpN(.rh-@ihpYbE`!aRcnB>7dbWc_:BEInN'$&LFZ>WYFEB2S
,]A@L8^&[+$\>/W;9I=HAJbtP@heu9`rTt70pu_1U'1V^!]ADM\CZt/Zn;GP^XoYUVR62ciTYo
)I'DuD0lCS;o>7@P4bEr?$qrg<[lfC\rKq!5Dlf`+L)s)S83R_6\u+?`:O\Eb*e=MG8Lha)`
NDgdD:FVoF]A=rl(ej*db7$a@6XC21;h3lK$%iW3IJpeQ:7lXf!ig7*]AY3VX4Uig(_MkZa@jb
/l(r<s2hKY<9DIU9-Z"n]A0?r9fJi1AL%#?XdL?YhP6m'^"t3l!uJ#\g42f+HGr.)Ep#"A&kL
OU&1?`j`!qS0Rbheq/1[,=&O1$CTN&J\;6V0ZBE9^CGUQR)@\6Ajil9&,rn6gJ^l[[-WD]AM3
QTmiXE<Ik.HiO3&i-8`==,`RRq")\On81=ZQLQUJ0T4lkq34/qI]Au=S>tJLd&Pt8t5LAf8,5
_8e\79A1kH_>(%2[X<aj8kS^V@,-Rjt"kkFQE)Keq,a5LF>8=1k#EQN1c&U2\RVA+.Qur>bk
CY4,0j`o8OX8,@.B)dlCLR3XmWWAPkNX<JEXM2`e)(&3s84<Y2:l%/W$15a[(5.UlCPGAoU-
@M.k!'YQ,\9'VWpNFe5?F)!$"e0B$Gj&2=Q(#V\R$pMa'[*PDV2mF*39-KU=/b*nUV:^&F%*
5S('bobdVK3`+sec7]A2kb+K(@=%Zm<=+&[eZ[TWYWA1&j"#XpqFgL<W]AP9&c@(0e&mM5s<>8
f#"HI(8A>aT<XHMYB7h*HCG,L3elde._Zm>6_-i?VjM<ce@n)Ke11+)NZQlge&%D4C?e!kkT
<U[ASU.J%CGX(jQOV+m"IqceT7ei#BRunLp(T.C9FiBXk73R:0toU3!sg1i01Vc\eFbXEqO<
dF(oqp+b4oZof;BkUKR?iFdS?I6c8:M=EA0nKH2VV,t2_RI>FHP><RBjr35@gp?b+t+aP>/f
sRI+)t0)@UuOFH?QJ1.WdB)h40`cUo3(d9Q'6Psm02VfPD`O_3@e6IBkZij!*pkF=S3T6G7A
SC$_43>'>=IPH\=IHDM#I45L4giiK2RS&)I%+a8+?*#A@!3O+%0(l%.D*+"ql3(EY,:#li+o
neq51B.fFfmFK`f0<7i778a$:.S.F&PZ0crDuH2_20Qu#JMnqfXmp'dr9lk!G*C2]A-1\VC20
YPF]ACQ4]A\;g?_,>ha\iFtQa^kRu?jRIR^\)mR0=oC[9qD>,DT85_fPtU=UE'"B$`<t41I,7i
q-rPkTZ4u-F4,,gj20mh"`NY"u:VhLrqV&;^67HjUcg)4,>2iE'O(Dp4W!pYdY:ZR[aMba^G
lnr^gF/:OT=$pC#p(GV+5;e,A:C(7HYQpNn#L_\a'+T5*tq08O#;YB9M+bNCmY/l!Ia\uAm'
*Nqd._XlAcfcj5VL[5CNA2>@(L]AI9u$D-bZdgZrcpPCKo'X!]A0R>Z0m6D!+m&K`N1bPNi(sQ
@lcM'&Q3]A8*k^8f]AG+`*-tMc%3U-SL7h]A4=ZKhnZKEG=P5RqJ@#SsKt>F!K'&D!VMQ^/m5:r
ES837n@EAKSai==`=.[c6?-j-5T<g%0/f)9S9U.t.CH1:?_Sk%D,;1tG#\.TB:\*Z98\G=n*
e!as$tb\TLqAa=6H#Us6QVRYZXi-AMoRIollFfZ3K@hVeR?I[K@)mRb.,u7s?rDZ#YnuQVbZ
G(ed&N8bf+YlWsVO'_@i]A\2dk823:dj6H5dM/^I\nSf+K`nQmeaja-+t#(S4#YBRREARGPuF
c\lt(:NS;LUD#'5Mi`5Q7&aY*Z!oqMAXo]AiCl(a--M4F;gA0T45liYHO,%H`!D]Akpj+7r0N5
aT^$VAe,Vn)K97CA10s2_1OeB96s9*?2YV<Wc1ss]A3Q<l*Bc+1(h#/ob,'>BJ]A=PMDV(iSk_
3K7Uf_</=u.T_R'VanDoqBY%om`h9J?OU_g]AQs*(UpHEkHpG2["#nme$(nkWYfHY@.@W\[2m
ST0V=_fU!isJk6:G,)E9IcDE*Y`B:*c[lZP"*`.fk_<3V70l6+,P7`!49/e,k]A3Wdg`H%*V7
GqmLc#A#;'f5/R4A?;"\;D.l;=gY@4RVklA_"[,FmGEjS;s-uM/'`Sfp8:-!7N7^Gc2OTBu@
9!/,*>)9-/LZ2h't.>@=.\pjDf^dMPa@/=VihW>H'>.1;p2=N!r:&Td-LY3<(6\sM"%X?HV$
=f<-^qd8i?+K(EZq:9"A.$'D!-l&eq<1:j\`cu>5gF4:V]Aa%Urgr/JtD$MXmL7d#A_DdTh:Y
0KA_0[8]AS\D1O6ipU-9^!mLF!?+Q@j(K..gDpUp7CZ$Us5?G,IBt2E.M?3n<@%!gI#C(K>V`
=.Se.TSE0RcI+$3&q&6@Im-q6u)79RUa%j0".s:aL=W3!UX32;>.*BhEAg#_.8'70:7_#6,f
N%G"]A'f:dhd=#+@3,sHPRUZhLKR;):"nsE)5`d_bu1QC6eb%,UK`Za4"ZkA\CPM_HNUm>>Lj
<,Gida_:n9)YNo=hY(R%\<YBW$WGjYpoWYW8EO[XM>;a]ALg3#l4?Go\q?ZL2t&(u0nNh$M"?
VBA/FJ:lsO<Q@R3?[iZtoj5^7i+'5E9oB3]AY6T]AAh#cXdd[bQ\luAPdE51FTcooQ#*g;W0p=
s6nP+VhZWj*jIO+oo';F8N`R&,l/N9q[K4CbHpm>kCdad?>nYC<FSdgd*W17?!5d$AX6M9YV
RG@mdb<im[+D@5[+,#qWC.nkj^(,S4q`uuCSl*Up0/&RD4Y,4p<ZP3F09)>m;@I[X0>BF-E^
./VqgM:8FM*A^?=)5=]AlT]A+u")N'-QBuMV>?b'*DrfpTDdK>D#)`QuqirIqYm9RTI2bs?1BL
]Aaiu4sJL==,3D^niP/]AHnGR[@E]A7r%YIXn+2!g-r/YMUr4J7!h>1boEQs-n123N68t,n*7Q8
5\H//G!G]A!_)5LBkd]AGj]AC_nr`>LS(`;CZqKs`DCk71s'Y'>-=bmt`PP3(mVWrjZ9E_6P'\V
Ni/fuN+Z$U"rVB`j&IZRq,'r?'2q_A<(T"`_IL:)/jUB,=s+4$LPic<OCd!e!GG<-8iLEd%I
b(TU>RE<QhVK?>pLXuW@,6$o*OaT)]A>]AG+Fd;+gV1-T>Pq1DZ/l]A?EQYg7SLt&qK^H\es=-S
sJ!-?O<j,f<dYnI)-C$Pfko[eC$.BCd(A\NFg";(S]A'hn+DFE?0.FZ;)Hr=?=,/JbTUT'HPd
i(k+5LoN1_-_SJp`5/KR80MMd9\-WT'82)Lm1!`0oG6D)R"_V#f1g6&r-B/&,+@.fAAiBeAi
pY+-p!([M>;Vnl`PVPr@>h\qO/%K<Fb<alJ^I4,2M2_Nn=dHgoD@e\[rRPHM'F]Ai^?ZQps.;
es%&$cY$:T^Y`HCi\MpiD.5pZr=Co$=:pT+g*)DmZqT3"<p2SX.s8"et#W_9(k0?kC]A6&M%*
L[2u1Cd/`U=8c6]AE]AFXPK8>E!2Tne$52R/PI$kR:Z/PmB[&5iaL^q&",ArUk[?RUu/*Ylf-2
BKDpoYZ"HHY4Y^`RiB;=XZspQ4`;3(Do^.47+o.9R(XF*('*Fd2T-UR,-TA$:^#eY$jF<ge3
q<F/oNt)1f6.(I$bIE%j*?k..ZVeAM7O\UTlKnFYP$MBqQ;M_s._&'!j=iGl4Goo)tc:*&C<
eGZ3ALO_p$ZhcK9aN<?)$eu#T?=$N9,:lB<Fl[Z:.H:ppXMo_.-PYMF8VI\I%*CYcMngqfYg
>e`.M,fk&'ktO^pBWq:V?q*>=ruSWp61i9!j8?5<B'&.JbU?n5N&,J:2Q@jobbLD8C+nO.+H
gP;B>$^chp2?SMggs6*iGKO:[Y]A0tc]A%?dihKbiBc?hK]A:ou'USd/j23f('cI\Lp\k^3'4:8
u^)Q<Y%;29^,I.TMtFo+EYV_kYC!p@m,ji2VLB;WNi3;dTLL3"_S_E[6:W[f[,]AcT,Gcd73K
2C!YRS(A;dj`?;=Bn%_T_jW@ts=Ag,W!!^[/h"%[Nu1epo'YPl=%4lS:-"hNgRg"M.Z8l$#0
+5(+><cG;U1%SdamfFA9Uf_lIO!-CV?JnGo[_O-eGd]AigFH%WbmSXKqQj]AW6S/Hs7b!^olX+
K\.EraeINK=EH'h\I"+Dd%[d)1[P$tlDh(-`Jcpa=9%8L2dR.G!R>=Gkk'V1^!mW:#l?g`L!
dMdp='Qs(JYQ\4mLHs/bHHYMsfcXERUOj@QFFK5=:YGHJiZJ$%\/[=6rZgZU+Ag%nsMd:ro-
'7ElELlgUFhVqMU*(V4,>>\_9!hg>91Ps<_UmAODg5hb\s<qUK@Y?pHE'6KHiKTNS$B!pkk7
TE".Y7;RZ[63?>]AeV1Q[oX_#_iT8"Q<\"EY>rFiA24m)^RQgD1$Rd$nD:\(<"noSVRf<)e0P
UF"=ci"8`\&UoasM/tF4(RM_mA5A80H4RWa*,Ad#$-5i\,4YHBjT:CAk>JMTef<=cW::$"Z!
)Qt\0bU7lJ+JNI4Q=WBCDUklQP.m)"$O1PKVh\9?:#k',LT1ni>WSPY+aWLT&s3m^+:-^bLU
ce/Y5jf%(nN+<1s&YiRajLl7uGOHdNEHo0HS?pOD+8F"V.:!prBK91Fe`@t^]A(r.BkTnp62K
UXOOIGE<b$7Q9n"ZieB;KnufpH_H_3S<\WVtEHAM>r#CYpG:pE\<FZ"<1`%N%7O\)O]A-IQ^:
cahV,)S$St7!,fiaU)QUqQ,IX#Ro95A#6%9e:E;NUlM^2UF(4#uY23;Jr[+Kb%Pl[ra'bR?d
=U-q%n!`5(?:e%P=701dXTG`^'ZT;GE[%i_?o*W'JblVV49)buB;[;(-?X%k?"Zaj`,2J3/@
P*AFh'V4D!kDp'.:t#C)H27c17a<;PIYRrAdb9[H\-lq#KT%FM/MhcXGHS&ee/Y'!J9i)g[A
/n!iHr>tDB9NVVAnY`k&^Tru7P?/pm-a^V4fGEVid=R"Y'HL^=c5^S[X[RPYJk1^s4<SV^HZ
^0:VLAP%?+R6ujDrNb;;:Tb/N.0Oo`>V7*!ZC5;!Gdl0#:8!P)lXi-=ddYq;kJ#+`_c#9A8&
=(#f<9q?)o5dkB2^nJ<g%4@Y#k0.O,FfP+JmD"#cQZ7."^0]AjB!L$,)%c1*X6aa(#PIqO4=S
hOb'<"s1O#_PBH0Rm[h_a3<dhjgqFI<(6$0qjh3O^$RJcF-kme]AAscqh0&huHn7cjL0L;4&@
tr?XUDS\e0FJL_e2%d/5h:$q(ttn\%pSg!Kik>l$=*$q#^?_'W&bZa)@7.f6BX\$b"nd8hUE
u(%qQ.4%7>&/7KdF+:(bcb.X7Pp>f?*KRXfG:<?Zun<Xd5oWO7>4,Osmq#jn)UMf1fmD$U2]A
]Ai)qX[o*a4\E$(a1V97A6V1+D'df+6=J?pd-o\ng.Rl85);_R;0N?Z>7f#m.,0oC'ikZ#FW\
7BG0X?L'6UE\8n;;HFi?qRkk3W@#?S1hlmJ"m,_bWJ_2BNggOKNEe*@J1YZc96Fa&$?QoNSI
Yo(4Jo[3T+C/2bp!4@(>MDa'c"V#-B?qQTKEC@%kd3c!be&H@C_]A1+qqel5s)bp^dW_UFp98
0D:QISI*6=Y7=J?kW3G\Y0^h8^>3H"LhV!IkPK\,,`/6keKBGVkiaDj,Q[\*8b"!:#]A'Aoog
E\ao=^':7YUZ]A!q3E6a+/,Wl"n9gSubSFiLbpr'hVafj]A/*iD_TT-1X-JGSJT7CDD>Xq:*#\
+@t7R2\'9.#3EU1_EKS)&=9"GnUP!![-Q%LDORBC3tNci!XLAh,P+n7]A`b,o^*<:YqNfbj(+
_L&Hql-4Och\jCRegBodmGcHk2Mkn.uJ,J[.5@Li=O/ACr*'am/kr!':kEH>#uBm2ZVlKVQ6
&a9eefk/j:E_CLp:NM.t?/JG43MU2W($6n+Jm-,i5Fjmt$]A]A%8]A;GZ#nj@ERU,d@GrRcX(ZB
Ird2K:bAe/FRQm+gjYII5#NpTTKfC=#4cGoL!)$IKMKI?J$)AofD]AD_ai2F6-:<O*UXhk?q6
9'c%HVkhGG;Jrq!rop]AD^bfD)3CS19r=\sPIG\QP6CsKQQXZm*<nCoe[(;ptBTeP2>NmKHQS
='9NhV;?Yq;Xa/Z=$\eo9`D'p6rp*=?BG*m;!Bi@l0Cf]Af7tjk`u8kM]AffT\Nb;G4MueESQ[
mnJ;XV'0V$%S5aI]A+GrD.BR%00M7R!7VqpGh3R@*MqMBC`!DqgZ>s#HJJn/#b_cBN-jS"3-)
nSq`<Ks9iIIel2h#t-+\%/_`Ai)i>]AICcE=W@E,a@<3"^Xj_m7rd-6Z>eITs::ohUSAo7n=a
VK1hkl%P07j3sc['+:W!`[nf<=_Witq`A"u$+jBPUroS_T+IQIq#0PS@4Z/GsO3^k`bbmMWg
>@,K!83A)?bqgbHVI&RIf!2cQf[)1XWS(\0n`uo6%<)I?khiU"MVnrUBlKX)`MFpX">DYERI
6USDUoZInWUs:<X=q+4Sd]A.DfL"P;8N+nd5$b"B#+PJi[m<n(1H%&rD:ZHL0'0=G]A)0(qT9=
;CAU*OA[XS>i=UaSC6q`)6"&P>,8jYTW`eBB+5$)Xu-h806I7T?=7h2RkS%22k*ln)X:]A1)S
)!KjnTA#7e,[2m5lpEBHBXE&[7jq,9IcuRf&+kF)272s\MouKVd;D(n:uODV996o^htMGXai
"U\Oms%jhHu6AUE[Oh(G>\+QL_e9aP(`J(#=F`Q*+C*U"C.kEElcRPNV".TRT6*q/[opT*O#
6/A/shIIFgE:ot&1r0JU9H@g<9$RVrA*ZY'Q.aN?*Hn%b2b3QWB$J9&BO(ghld*=!-'.h;V/
U'H>ek\t-6\(X^qo?Y]ALrmnDB"NFjT^EP:4SN4&2fE0U;a>oCR.4iIjccklR.Y?$0)8),74M
,q82I:==4u\8#<Pf*'p8Fdh2]AkG(/@&o7jiZ]A8it3QU^HW"04d+D?cPmpEe!Ed59q28^?*\*
P1'S6gY(6IeOP$o^3LptVEuU_?s=b$@*=-N'_mV<<RW`QS%"0`f-5]AYJGp2gCR#?TAqmD5=*
SS9IoV=4P%a?cSR0t"5I+uF4h7kT%go"\?IX.ITi.ND`WHUfNT,AK7$ot)*51VMG&F&<%Z(_
3A'YZ6I4$"6\\eN60nE\;ee>MplXcC[#"&1pDT9R"hm4RaIFH!8+_83meDPN3paFb>NN0%Z<
nqRmIJ0`l?CA,hWBIrnk(L-mT6oCUWo0uo,bC*TS+QP;jtEfF=i+CHO1Tch#jYGf("a87CQ2
G?/s5Yib4qEBS]Aqcq`G@@9;&7_MNsnHM78)*Be-k%I^]AY/lmLi[.mU[#VZ7/mdHm[mCRm(_1
H[DVbq^RR:3HId!*-2.e,(I,Jl[nb]A982HtafXH+)o2l!ePm%B[r+tpG`n@4.J6,`7/rK`i[
`^3<>0$fQ,hV&gh$PIi6M91O.fY_kc"3h?WY<OU(>CWUX'IP$FE;fUQ6/[%0)o(%N.k$/LsH
-fMMkCde!WQ"91lRe`HV]AKD`rqKL(91e2]A>p?M3:54_5SYP7sddU_Dm=*_3@"3*#"r4=jH8H
;LJ*-`^GapDEelN,M)$IR.$@SW_rWcVu>K?N+ER8:_J[@gsN.EZ'r6*n=-Ui]AC*tGr2#C-h:
sN[A)pujaBl+']A+n*"13X#d%Qeun^7^LlPlGM]AK5f,iIu,ijAte\A9mg20,>(q->(dH]A$P(2
]A5D+"VO2o,mX?#[OV^kT3ap00^AG$-ViZrMenX'hlEG=aZYa"N5F@n*mWC-?5`oDWGr2_us0
VOV@81=h]A/g;_!&gWD[8@V]A.+4Ro&8=b%)AFk>l+Mb/TJ/onJi"'9&<Ig!+=2jV+bK>m'18^
l"Jc/5P2$1K^6s>_q`K*%^!Dc>Elq^8^MBrqQ#lZX2$+,0@gh;uPuuhp[J7>+ZC_@e2PO\.%
B9deQ+[B>L%io>oN*)o`ZBkE?X2$Ol$n\C5Y`F:kYFtmC[Aame,>87-Z"+gohg^W&u4hGqP)
UlYcsJ?esA/7n]AN1>8m#EI*$mMH[9Mea3qmMAkAj:#a_R[B.CVVhr-!5=o4T>c.VeWpLCQCO
'G:&ZSp_l&)QtFPZS^J_oI`a*.#kl@pY6H%4JM+E,0)]A_I<C!ZH:*>n^"guk_`KoeCA36(Qb
DTj4M1m':6VP=3>A#of@U>^gD?5RC#GQ*2@:/g04Qr16iMpD%U1F)b6@lO:HYeG0:sF7%u):
HZC7@*0LkKt#!DN8HEekOE=?pkGYA9hMm&gqg8pY,MI7.KiYj(L4t-ol7`m7N/a,*T4!C^2h
BS((LK_Os5(2kARm7Guf]A;=i\^6J:?U:r:6Mt2(Tl3A#`p\[VP8ru!I^Abd53-*Ad+Hln>L\
2c-`l<BgN_QXr#HnbW:$SL<;>NJjOYG6('FS(a,#*&\!1MC,fGUkCG."%r-;K*hFb;r_cnM7
4eIF85M:J/jPfK9S.?-)(Y3JQk5A6Oi,OO"cm+JGFtCZKr]A'f<oA@mR$4[0JY?N(gcE&h(ms
V:pgM\n)X4FI4;hHAnr9G1SZo:4F@fnLD=5\(iC:Y:WKbMq3k)/'7<M4*@d[ggbJL)-;c%p.
%B%!3VeZ-ci\M"Jt,PV)56oY)VGK#Zi0%FTE24g/tog/GrG87/@E"@h(hNRC__"m485r#SL8
7%<_#9@D,]As-<TW+P3u9`MtZB'#l,p%0oihX!5di.7#>+?Bj6^/S26`8`@m2.oH*CBOd3\L#
?BFePL7lH`?(.UteP5>)Jq2P+e*JM/]Aie:+!-kp5@0MG5@<CLbirJduF%(J`G0d'D`QI=&4,
E"Zd0-4ooF*)3+Wb6,+g6uI3N1I#1`dn^:abh!8F148,Sm.hL]Ab)WR.J43Bh#p$W;&A%rIn;
fg#c,Sa==U6QK!_n@PPZ9:Vpf-lOS:c^`*?u^*d0Xl=7@2uc5fb'%6DY*4<IX?%lW43VL:)L
]A9!\?DHX'dZr9Rk\TS?TD!8/QJ]ATJoG@Cu4.^sRle[%JB?<K3Im`M:;1D"35!r=%HXpkT';;
tq&@-H?\3Y"@gT4Tq'1Ip-+#s1YT4RXu>Eh*cLZij'R/*:Een@0"_9l%J/7R42)UJHXTu"Y_
A'8DKcU&G,R'iDYmfZSt$eUa/*2<5#p*A/DlLoo'V1L+4[U$?-t"i8`S!(L?(^j=E$qnZ[Rj
Y+5.WLgc\I`fN>E5rL47gQ+$,)C&8\$.=tZMN!*oo$#Nu%nGp?:1F:s0JMf#&L>]AR"K"jq4q
LMr*UX5*dAr+pcZkuu[%4]AKejhC!3#)-.O3Rf]A854O2p-`T.EWnluGP[\BL!V*167b$OL'.s
Lm$2cQBhsu`\I9R(E.50W7PYF^oo::U$[?KW1q4h]A:U*&."uaY:Y$`h$&O2IeTmhYHB\UM_F
d1)^SiOSW,5sjQ1G&CN+a.(8$iWrT"'$op)<(`,Xt3VQ;DV[kAS%L6_%gj*+bV;cWKh\jZFa
7%/g3=_VhScl@NS#J4>C[,-nHm9!?)2lGg0!>9k!hW$W.G&:t$)ZM%tuL%Z5uB#JVTlC)`ma
F35$HGt/2:J!-NTXi<=LYViS/P?j<!afHEogVT!m"<2^ZPW!fMS:[d)dk+$kd?RI7F@8&C#o
uT9[o?[4YZ\,-/lbm]A1tf#V_Ld$4YBj):P3c1,<N[s;5`_I\8)ACl1+h5>Vk3+^7'2$!SCC`
1^T&+/0oeRkRW#SA6oC$oJ>6h&E4UI_AqCEUFn.b[=Q?+";&HI38>(r\H8,1O-IEmYlPMMVa
`)?-V96InF(`-6a0?6G<e#%9L,,cI:]A-g:Y;Nob[;dKc6SAOXD%1hs_p[1H"<kgeik%.!k[n
J*j@:6d1ZF2A<&rsGYu`-'6]A79u".>(lnVaqm8g+":^"aarAcc^,ag(l,9:QS)VV3_P_f2De
0,KR'4\?u9ZRN'-?XH:LB!&QSJBj;b[0TNmCP8Wh&Em`N_d>8nD6A_>'"^*AXRbB:c]An]AjNt
h&cT:`O=`d`5Jg-d:;>pXQ/`"baI0EI(o2Q7Gta7Yu<.rb3d.6F_VdV86FapRZ.akP=MPfru
*Rr@l,V=e=7b/.J^,6S3+^"u\3Q^iu'ODQFTIY`4`"'T&P]APf$%$c;RR^]AifA]A'L-mX1hX9i
:rd`IW"r!C#AQS/cd\4n=[Z<rD3`IkLm-BX#Q;^]AE8)OAr?'E4ZDAVD"7Cti"(ORq:T7+chb
WGq@FH8FWCF0':Dt`B:=LG5<e"3[j:,GW!%2EU28dj2*LlFS>MIuo?Hu3<.=5l(0a[h+_R6k
*\B2c50ur1Blq.e'eGr7j&_a@anS$)08W.aUdL?Kceb-@ho]A]A=SZ3m.]AUbBfSK9k+O*p`K0S
9h`E`cV5#<EMAIL>)?UUgJ\4([rVGIk_LnS]AMF6,fT>a[/1@ZkDNmKZ.,lQ
I8TA#if=d'Ep=XB"]A@tQ:IU6r,q;0CRhF-O10C3r'&it9h:&-b:6C1O\fYIc8ktaNMM-]AK+L
l@.GJ/YqS\=7S(51h:HMKI!8Se)J?2X^hc^.W1o^\h[a1sVuDc\=[h4n&1dKr<@_Nh7Rh5rj
XP)=!BQM1[]AKC`chE1Y)(7S`jc>]AL_R%0PpQ:Crq"<!.a1n^=0ZQG'p(1&Y/EkHEEo;Ur+,K
3hFMa4E(^PafJDVikPYTO;4JS<LjU'oEt,XX[>>@FjG6Yi]A%NVhGo[(NiHEQp'gegcXYI>SS
_0Cb0,TAqaQ_2UYt:K0<4YaQ`cG5jYViSR^Fu$?6+"G,opre;s86.@/_g?)>irgbM^+ri?69
P%9Dn']AI\:j*63F\5$kH1,%$^UN:LfBr/mRH"]A2ocdk?At+.Dk;EK_5qqXUdDd2?-)+SO0_V
>/8::LD91DHsuZ*;3'k&1q+Ep]AL9"a0M?M`96SLh:R@$&Su]A'aGG5"YjGV4kKQK"]A4!ll?uq
B9?</$Ei,Rk;lASVk=ZAl1A7;;hE4",u<<I+h,:Fh5k3#'V)4+LPKp&/.>d*VVXj#e$3q?(%
.h7Q(X.ML@GIsk]A"Us?T"F4(r-^8TRh<km<2"p30[ntTKJ_6_Af$^(?)AWI,3#aC,7E?kG!u
QHZV4@6"h\GW/..kP-`Ub8u#b'3`:%;_BW)!FL.:Y0$bTieq,*Hh)jh8&>Lon.IC6"@K^EQT
),"9-ap)\t=+s@?Up2QKX)5o;jq+ZMuk\qa%"q]A"1neVk2@HU:'K^e8:Z&0mX@-TEPF^Us8L
T=%Ml2ZZ#Dg&PLpbscs_J!/PdLjNo&S5om*sCofr.F#%65?@Y5bIp!"MqMT.\?6Ien%>-*^(
CSa4n-)nD")C_O.5bSAJcZaeE6"TOgGbN\KfbgYRP;EORt]AQ^U%"Cpt:ugAs_QW>TP!-s.$/
@5pjPlLEt9r87cj/S4^P_?jlb033P5@^EA/gi?In\I,j;(=Kp)&L,!\S0uWM>nD<ITc%-^?N
KN"*7<U6ZDPlp<9='26E`Bo\NAi*!kPP@JLP<(U!p4K9kQ5L/;t$DD&4&kAUtFrHJEKcO^n?
F_:g*k[>KRYBOW^<>*^sbKbjd3A;&^a:"+IF7E;D[[TNZ`qK]A@A)!fNQim[8AQE`h]AZ)2po`
2;h_I<tNb2Zf;@HN:19MlYeWcb.YLlBgZJTur%)P7KSB_Ik_`@N_'fVPR47T9_+g\-a]AOju`
pm#O*NN30?BLVnR7kV.ap-;G?`B2f.(A$N-mEpuY+OT^V(a\G*uFJr_g)08'laA;+>>]A*@+N
9/?s*YcYJ8Q(cT#i+Bj1%B?M76ND0NbQaO5"`s+'o4EJ4gf5J1IWBT`h%%Gr5a<>=B3PW`]AO
e%*Q-hl:]AD(K+#&rNN?ZTgri6"Ge_M&LNSQ:.THQUBE>O*bJqX-IEZZEYn\,K.=ih67faeU'
!6H/iY\6'AC&@7!mJGUhSWh6[b<GA#@Q$_.JIriYN-'&qNIs0X1C3,9hn6hg,'7kd[=1mSDZ
=hLH21MGA+d.^2dfWpd(q1X.-!.+MVcr6?6BMaopJ$RcAILm6=Mn*a4%4g]AG/N,10b,gQEKI
PQM<0%8!A%elY?87rm*iW#<GDlp>_6^)gLACrk<(e'?%V8)$6j;tNh2cuITE)Ac5TSIQ+]AoF
dn7m$7Qi2d7`Q?LW:YLJr_HrI<Sc%^3`cs?6FY6>:Y3`ZF#bCT`%:<CBfs#p`oN(a0sTC3_O
C19(Tk&dP\4[=5J1)uH2HEfAFp(.cEcknA#5Z=Q,g+Krp(f/a4U4m:QB5",t-e/RB"8a!t`N
5=Z9^V7;P\KHLG-@@i\m`,4Q);e"H<#a2N/lmb.(DT$jV\S6d@3_6#&t7Wd&R4F[=rf^:F_\
j'jVZC!8Lm;lK[f@R/R7,TP`7Q4Y_FOR>^oEBnuAWWsKA^U$1COYhk_>@AdX_D.cha)P-UT+
a3_e<").=gQ^$!D;[Yo?3fg?fRdRq_A.eIb]A$Tg0D^R1[LfCh8i#'Pr1)k:1H5]A]A66)+0r;g
5;q-"cmZeq<2CS*^PSRW!;XJp/e,X&=LuooP!hW_B"q.iY(_Dc9_AFp+W6U%@"%ba8g'O,q7
0A`ic_0\lE1j`]A!F<#b@C+kPL)d3>`.dn:;bb(Hb^To<d^Z5WD^KLkj1<(P01@b1>2SRhA'S
KD!),(6<W(#,PPJ:Y!'aT<l!2k,\aS&^/fILS]A,3-2q>C*-KZM2RSA+e0k#T3GC9Zg)+K)>q
6jWDih*fKl5o*gh/mgjc/ChtURjc'hO(Utl_^(H5Q;RaEKWpU-liMb\PgnjBYlCM<klT@g<B
:GNl^Yr_hD-pF:XHAe)apQo7%*a0mImSasiKN?8u#hM)7G<&UJQR)_AfkWDirdfikSr-c9m.
ZddtQQt]A.3jY;PZfhG8.(Zo\08S\"O"Jc.T]A>2<EXrre$NB[Z3+B-ssP;T/A*FjVDf4LVq([
S="C]AlB$]AJ3`\n:<`/SNQ4O0bl7aB5f;=Hu1LpJGDd*B)-*[+P:e[]AOm76rS8^Ema[lBeLp#
RVr:hIhI!#pL@jpej=tF[;#)@s"huc#as).rTi_tlI]AcGT9;4pl$N7&3CPmjo@k;A\/5)^r:
lkP*igT.NjTs49[ZXOKd#H'I$o;->q.ZF5M"?5%QE]A%E&*:KdUjS&Pndl?;?N,*Xg>IgK;[=
nSd-)Mj,]A-k#'3\TKUpE5NC^W]A@P[5DX]AK1GjJriu,Sph^)6B%0>-Ej;DGo%"qjRQXs`&<9L
A+?r;8(L=MQA'\[mtI(8$/)!@#:DE4g"8^bfhdmO5JGli</B3%[*+:b3<DVPdZiQha8JLEo2
i0[^;A#t42pcm8!OV=LVBT$k,I)VMj"X8G*?"2!4<X(j\U4qD/M?\@u.Q#Mi[hKM&.q+s-$2
.O7aIHa=H'Tr,sF0H=OMU.-)"q1bpm"3tl:B'UE4g?X#TW)otZk06btOK,:T"d0!Ka)'.nY8
92"aGh&QM/jbWGDDL9,;B<V$"]AI6cA$!1X+ggLc9fsY2[O3Lg_7b"AR?VJ*%U@n9^TbDZ9#&
Ms,*DjZ1F7O#OZ?,6N7)gLCklK]A!%g2NVeP8DF!3:4M+?t7SZD%Fbtc!_attfI7.A=X5K:,7
JAsHk[!rNd\:p"6?^q84'9<<M4`]ALg>1>rlg;<T!]Am7.ZQ).:O&n;8<8AP2lT\[K*,hJf9%?
FE9dZ4S#feYANY'1@DkoW%t#2R-<)!tptjr8p`R.QCrPDP]AZMo[qZH$TQ[=c$)=mms:Q6q0$
."\*OCZ>t_3.iQOT*FK$lRegT\^%B&B-0&oq.EH^f"gtrdGXYSnAf2okU,JXV-lf7M8(d"rV
;jM=!ap@:X#f9X@CX=>la\V'#\&u%]Al9ufkfrr#[bP]AVIBkhqF1fWuKu1!JBZ0-+$&!U:/DH
&RLB1=2pC(TS/p26b5$mL$filcMgQ9DILeAmZ36=s#d-L#h:YQe$*f'_\,8off-QSK3;cWtJ
",6jpI"@;ZPrC_#;gWsE\Mgd'NJ%I7$a!dbdhM8k)oT3M/CWppT0-P[KF)T..OK0j/(oJU::
U.95"og2Vn98KG5Y^77hC^WI:82.W_;f>*8UuU]AUcNHYAVbq!DDF@p^!piOX\8>a]AoGMqna6
C3MtgE8g@?u;;U>R.9VC`.OFHBaPf;Bn<)gjQaZ*NnDSLg=C`d>d5/?K,-/WAq#s)d?8H>O'
Li(f!+aFa$LEAugVTjP,C*sF&mDos?7![D7\c*>TD,Z,O$'R]A&$pi)/ClUc\TWNUc0X\l_Me
*4N3MJi'+in<<W0;3k4(3cYqAFqW\E1O,A]A3S"GiB_oM=W<Q,NtIl/5FJ)CkRaUC<cY++:f%
Yo[d=jTTj>moALY[Sfl<IL^eW0']AAB4g42akjq=,hs+,qk*\#e1,<MC*>hSDfICQ_rVFo4\X
d3ch!LReE=7Z^&LT+d48NOc^0o.a'R=MREbB,7l'F4[h0ulI3PPL3HLP=;LV(.*+eqZ/me\2
=G\[L;UgE5;'&0;ggR[\%h56bOeih+K&ta#@s';V'mO*a8._;7"QbRrOP%Er?]AOrrPmI"mfa
S*C?I-d*WHeMY(R3&Y92gFJRq&]A!*1XbWN`)!Cl'uIughjutD:=;q)Y7#J&1,G,'RJgoo!]Ar
huh*oM5S#/dbr`C;:Wq%T?KSS<WO%0/#_<2/4h>uE-'4%rqliKiF?H^5`R]ATG65l0f)nme2^
hs=Vg`07U:HJa(^<NCLDFGa/RHBSur>%bU5jqq$l%g3n6(9@@7;-5X>?=.%R0c#@Dc.'FIdA
e5hK5ZjT$YRtaW:AeofD"VedTFOdFlfY7YE\/&E`Z<YTqm!fJ?am0q-Uu]Ao\3U9*%j;L:>$!
@5?P.V6R/$_X$<3:a>oo4n<!1[YCk;!1'b!;)iH('p.mK:VLF`q/:@JVn]AULYY:ep#+6N;A-
MnHR1;g3F^`UH9^b1_RVtV4[lssg8_rIF/Ln`9AGM`Yc5pbab]A[9TbI6K\5XH!&F^lja,:Vt
>r]A!q0Wj30G\IM,iNmN6o393Vb,rlpdFVn4;tnN`>Vm,Y\-+7A=l+`P+eQ+ef(/,uhlGaZ<D
HM5qX"mbS3$fN*2Afibt*fO[[Y!C[lc?:o2b;lgYorW(fP1bi&T<=)HLkR2O?JF;:E!@eM^:
KRGd6%;!j)pghJ.H@I2pM;SOj#P?)6J!n^s:pE)>X&s4*B@@;(cO)3XMEI.PcDtO8*;gH.#P
HWrO([2"_:ahYObP&6N5p%]AJ21UjH@6oqP_B]AGMOqgPu:9CUP_rQ;lJnE'm!Z,jc&uKIu:,K
`\q(@ICOfQ`_!$&/lcYM;cRpgD$S:(YO[fjS>u6T?U0#qD&D\8r&]A8X;bVaqWLuiO*bi=b[^
#;`B#mO[Uhui@'j\fe;PL\"/*d&J`?_<[8StnXi)[;5qK->/9?P74KEWEg>Z[]AmIS")O$:$J
^+8fpAfRS[03-WC5h(eS(To$Nf!i%cdh0CS%k+mKh2?>QK-N*YV$M>jO37B8IN6&ZO$W'b:k
cmWVQ36c%ZmQ4JY$JDCsWRo>/D,m`BHYJDnn_M?^Hp\"E'!`T4\:7[Ut\5;DFR'a>PT%<SU+
3+ug59+I.<epMl355N&eHM/CPY5Q/)HH+)WDpeZisoi=ob;6aFfp?g-A2?<<`;<!A8%c`R1c
ZPA$>:Eo.d!3(M7cBHp6]A^F3S#k!!J5Z/g*m[B=?K(U333m0S[aModS7rR6(892_(3MN0+g)
]A'QR)?NB[))dj6KCOd[bfn%W/[]A67f)^mRn!>S`Ke`(ueiSUT$'lke;R+7a#)=?TBuB')LR?
.NY.`5Y)b&lQ!IEUQ'^?^9DIe#eg$H9;<oAKmAoH;\[lJg!HEEZW:._gT^a-NYH1Q3AYX($F
Jfj;1G>'@OV?pTCm>khU"YcSs3?2(rPM";fdF1f4cN?jmXDC6tT,ig6;"dNsH_o<Gji6YP>?
$DaV@JqJ`74\O+2amDATf&\)#IU*V3U?0i%9h0,c+M6eTP-3h^De?g0?r*uFu-7i#::a\JQi
%)13V)PE;Re\[.IE/PZab3iC91Ifa;?%l2h-cT^3thX41%XH$AObrQ.Yi#V:43#[LoZ9\YS^
`Oc1pi>>)ubJ+0I@-Mcf7ErNfFJ9EAT;KP6LPbcEg.8U`<\:rDQ=@ZZYQ?c3%b.-eS[76=Gq
=j]AOH-j3-0LEY^d=#J()WPN]AXhIB@6KEE58X^E]A>b'ojTQlp3TG>1C09N4nQ'=k52N2qeNhY
?dF:b+^jKCBb&e'K\++A\O(0!$#<3/,@#2@cni*#^Ba_uSTCf3K\:0<A*8[KSfB7TJlRX_,"
`T8.+V_?LrQc""8FOt[a*Z'f]AUb]A=NUnk_..q-r6@$lqF+q=LC8s*<cFlYXfA'$8SHQ,T#'T
:A))!44<D?UqG.8;uGLmunnAG[LWU9W:n>>=t:sd1URWl_,Be'QPMqQH[2bG7TgaD5-@T38O
KAn7h;Jne&:;Y,%@YD'?YX(;GcFEd[<>K$8^O2Jt<%Y8rWbdU+7X+@1So"A^tU-R0bb;Gcau
9s7`jfP#jXMfaGGJBJq72h@b[1N.5b30T^N5?O]AmUuk\3CO\U+0Li+?#H\_U!C,XhCi\&'2,
BZfhssS#ELkjh2/[ik1EY]AH>>0npSm0#i"T]Ac!PW>GL`%=V:VDgK?2DuUR=m4OBSIl+@WW)C
6n@9r5mr+4:=BK>-Fq=1E`lLk`5lS`c,EZdMogAp@NfL*R*l2n=c2<\$bLUF)@iR@@Y&dO_&
lj'$M`J!aSAon(3bm[M2<(:,'u+I"cRam3>R@4kPK%-*QBW2.ggu.3l:\03Y*cUZ'k+E8H?i
.eEO7?]Ark5_QZ.C!V;biC-cGPTR8m<i1>.PIBo0P=HT8R+s%0j'li1:["*'Z$QE/AA[)Bo@^
TSf!Ad.#VQKgBrQ`;&DaQrIRHnt\?D/u&S]A0:PS8EnEDGpZ+TiK.RFWS>W$[5JOHNU)D&V0F
.)D5]AfVIbd1/!aUKlH!SL^h/'UsU5Y;t60V1muon(#JKWUH42S]A2r34=PO/ejagWm4KX47!Y
:(-(c6!9V&thmVeYFD7/cZE"p>eDZ)&9C8,mN>"4Ypsc);6#^d-W%;cHHb[,bHsUlL/pmY%q
kHJgbg_lE02!-\9Tr5<:=hkPeqaTm?L?nZ`9[0rIYSU.CT1A\OTPJS_*=ZN-N-nqs$.>PU0r
`H39OrLKCV&_Gb7Z%+%KiSMo12Ph6laTR7q;#Q@R`Df04kV;3G&hjLY(8\\O1[i7rJ0kReu+
gU2dPY,e[*k#F6$]A8u=c8Z-.[[r+$0AkrX-2eE3kVHFP%#f(ZY[8tA>7nj`CrG4LE,XG<%(+
F8ipO$ZR:!C+N.=Z1HB\mKsO"^A)mG"U#AO&C%&,::S\p0Je2!An7i(Jb7M:Y'./.9bf3]A@5
hlO0$p7k>dFYl%2SMat5J93(gDO1W:&^%HARD*/a5j1tE-B-<s^N$"ocAmjk3FZFm-Lu9^L7
GQAIJnM5$r)J28O60)M&G[%eK+SXRk?LDIeK5B4+E6LVoN*h@L%?1)6E!#l&Qt<=J\q/%`l)
thMI5Vd1STE13GcgOO?cMhUfr=hCXHfe9&eXLHd0H6<>rj(dOhB`P\hOn%38rS5Y9kVgu(83
gS!kqDD'B`=f%5P&L9oU;_G>dl:P/Y%[:*]A_hnW;La&Tu/N5j_i20`,i!##Q_kJX\/YRh*fu
Bf&o^91;k(ZKdlZRYtV,+$iU*W!g,d03+9^XiIB!k30r$@J7AU!=I/+bHLF=rh@B[CS+P@Hh
pPR2s/_\hto%[i8R,VS8-!B/5^nBD5Tj7(3$J*&1L3?\^0=8<+>I?`tGk'!8)[$QVV76#2PL
0Q]A-DU@+XW5=NN*^KJ"U5]AkorJ]AlJ,^X%&KD`T#$R.o(C*+ruhR[_1I(s'&e*40N*TJ>G$')
6V2b5ZF[sqO0"\GaOb[]Ane7BRuc$`+Mps0OU/aa3]A\\E1.ZS4P?/EgcbI6pW!/di:,0,tMmq
qdF;G2&.>sW+4Bh2e;t9(7='7BoOmBUg.35<n^BS#+N]AHZ(UI`UVg5p:V@$jUmDn-Yt[gjbC
*X"P!u1;Xu)mZ[2g<UO6O<bT)Vc+\mI1q4"YHQ4VRb<_P:gSbmTb-Do\<D'\FntEjrQkSl9e
QlWrd\h4QG(fMr"FQ\2(i*2Ks%[ZP.P:]A9hg4g5BDXm2cjMUu))#ZOChB2_-B5$DLOVju)3I
`B%I8I=UB`K00<M+^-SXnb:lRlF_o8tLCV98nHFIB0VKlABu*j/`?]AZ82:u9&bc3YOe_UWqi
Kol"4C[ARmg)pFdH_k1#H:g#1Gt'u"0@/WXW6-G!<WYeh\akBd>!3(`9J>iKY5ZILXPNm*!C
OjZ0"`darAHr-D?%/B@Gc1VhlDA`,S;.nO4]A-ioWOo"na3O3UdGWT5cn0(<u[KeI@Q,Y%LPc
#":BMq"%hm%r"qg=!\8(C`]A:n2;+J!_&=gY[\Yh$*+?ZcEm^%'=hc%VQ=g/IiIr-;cQk@E3R
YHbWjdBPkm_87I:<HgMMLs/H1@]AmasrG>7AF`Ij-Pd'W*oTjd"*O&GOf8\_$Y[7KtgBX6rp-
2`2?))3G9:i!8U0<5f0W]AV#0)qR)SBiW"]Aed8-`On(Cm`q;)Z(5T]A_\=H_T"]A/!rhO73k\9l
'ej]AH,<FC[bd_!m\gG!k?6EikOq(PrP:.'@@G<5;BU$EqP@igmKie)Lh$JXNQ+)67Q,q=DNO
F_C?j@kel*2*5QaSqV<lBh)j?A>NNg#"_]AlCk1]A9=h;.D3,TG:;([(@Od22s(:]A<4!t%h,]At
@UQH/]AHXKBQSSqlN+_ft+l?X'pP;G`d5G9MrGpTiNb\,Y=u"PLpR[l5+VT`lkU[?-)\]Ad_i/
p&Y`=e\>q+UV9n8u5Sq"3HGSKXPVe_@]A3t1$;54tMGZfh>LfVSMeIM7$\J"mL*c7(7e'ToGJ
^TdZj>c<VQ0Q/Erik@)E'o+/6a&SkgU^;.;:1t5hrC&;6QY`aE(cPti0lX)*j7s\leN4+"F?
\OmrE'$9r2cYbD;n?Xd9Wa=a@#8'j,aC8O!-TNHlh9*sk-NTX3"*Ms'l8_t9Br\tT'+!T,7\
T:&Ft?f1IqE"A!->kK+,B00fcJ3R9\c(\jp]A2L8L&NGBAU.Eqi\uVrhp?#rU;/L,",r^L4^d
*7LO4^O6Ia_W9kt&0SZ0e#%#Xo1p"g.Yu2k\t6GVGe=/%!m`r:h\GJr[Z^RU9>HYHC1YL2;@
)$Jn\G`p]Aad.&q:ORJCR/^_%l!]A)%^bXo>^4#g%pL$57&J1]AXnr]AO/-B#g"F6A2O4c":@f`6
dpKcCcMf/9<4GeNJB[CYd(]A7BokV3E[Q97TFnP42?pSP)t0As_Q7=YHGD0BB,/0&\L$es_l4
-0cO[-(HidQ<5do;b<F*_NgeXm#BUE]AJUk>uO8.&b6.eb<Ek%MtYQ-7(t>K8GdHa*<ar,@<"
f:>%,I$8>2JWoZ1?kuF79!RFR8:j\(Q2$dk7rE)Z\3G=KMpRQki09M(:C+N\>uY%33dXtUjq
WJ/mr6>(n*Zat_#f-bAQ>!uH32qgK@CRbgR0g?p8g+LV\o5r7]AY)jdr(5Z;I>;e2,f!Ys"nF
:-pBnpIq5ld,(b*NY$+3[bV@GOmA)HL1._hO#J9cIn5j%ho/?M4"Z8Y0lf3/40Pi@6"7h*BB
l#YT[^"sWLj+!dgfmEL)@;9D-TM,TDAnf/o%hMJ+Mc^%aKm7?OWdp,jrAiJ]AH$j\.[C6F/L3
4ah)8/4Rcc1u;5q<s6)L8W%n:)mej6JH6<HCiW8mDYVO7H]A1;?KRM'[g3JM/'.hm1gCN3&U1
RM&5,*h@8>BuZ\07g`VOXU&a^2C2!>(EuilPnR`RB4?ErT1pRh+',oc*+]A#(-WMdp4\pFb[?
IB;1Xd@)^W#/nL[:\*W62_Giel8W2th1BlA.d(lO@_j!R-*]A)I^)@SQV;2?g3DD?+eX]Ac``1
V;_S+F8hWcNj*.aoA$,Q)qs@5e\sfiil1W?g*lrZ?CVX6-+*6BOW#H!8AXqfXm>S=jpZF%JP
#l*hSs;\*(H_SEVL>?Q`<Wa9/FD)`DV9]AcB0(fic+n<CJ-+3JG*F@c27]ALm&;6G#F7J*`FER
-GNiWkF)?3USPIoa'ggkQV,/*USS5rlD;.!,aelnYpK,0XaFCbs=OBZKZeYrLMiMjR43(UQb
R!C'HRHVBCAkeML2BL@8<8533hSm(9`_$SA)rWsQ2S\3u>#3l$as)mBGk$+m'`JR@^!T@O"d
S$hs6CgQ>5r9B0[?NWm-#goF'm\8?WMME7f+m'F7U#nqqhp<TD:#dqMti;j3">cPj\HV#"9Q
=Ma,#d<!R@@FjW8kAq/l/p2'CZ[9nRDIduX?d_VEhb[(.1U3n?6UL"qp+anQ1_\[G.I$kk+o
/<$_G+Y/X$f4PV*dMY$]A9$--T^K-4Jk+Fb**6eeg)KMT6]AhB&Nq2FRq`&OXAGT;DU(/#5`Ru
Xr6Rg?J7&F+:c1Lr$8jPq;_(=K&5V*gtF4sNNrFidueAa6*gW;_sTol8-+3I]A@p93)pWsG\j
.*kn*GJLD6?KED+")eDkAlJ]AIM#Y?QTQ>-5mS!CSi,"56P_N'72WT9:\7/K%S$WReM>%d@,2
%0$0SQW;g"487htXcBlPj[TkGHVf_c6+%C35O9K'pTl5=IbM`$.Sd?J';mdt#>-.79fZNGM3
D7Vl.05m5>OIRi.iNq(EJZl7Eg&Cosl([JA=h=dN*5tSmDd:W@Mpoh51\t^JcKN,@<YCYlG<
(kWISad97qX?M'mil`q8%H[bf;+h=I%QK`=E"8Jm.W#3WYUq[D'$[gh&5St'PiHU>5/02lh;
`ogGj)#YNd[g'&<taDMj4/]AiDfef('FK]AOk#$@O'o5+[&/U`!t>sV>S5#oqe#J8e:-"+BA]AN
Tc2@Er>_R5[gV#(GkQeHFgCdhV!7`F;]AfXs\,`bIXVbFpU2Ca/*O'=3f3LWsWm#M]AR"r]AY\A
cj1,>ptYi4.%hkOh]A805P7=b(DF0U$3"FlN.NRY.T7CJHp6aWH9:sGGmbqnAIpWjZ,g&045M
YGslKqOYo[$jl4UQ,_ML<I8<VCOq&7TO2+KFU#p"_X:Sn8:@-Wod7>d/!g":YT)\b6GW7=gW
"_hJ?bt/dN0YC3VO.sW\EORGh7laPq]An[tNRA<;S)fW5)6ZDQs.PEc:B_4-AHpT9cg6SJ`SP
,'$=bTCb#@RY$gIBb!'&gkALqkRqAiCnlZh;FSEiJcMmff'1f7Fcp(WD(i5h;6g\pcc^Xelt
kKXIPe,&cnB^tCD2Udkq.J1gIni4I-je^*uNi[t-o3_?:dQ59tWWY15<I]Ae.nG&/rCrYdnT@
XW]AVp)E>\dcS/JmeH5W<,1/l/)&YdM:j*'VQpe8N\t\(QN=Yqo3V*oN$ip'WOFVHcj5;;j"-
b?'^3X+8$L'?L*T,H6Y8s<3+TjaH9NK:4=h.C[=i0D>LH&:$hW]AL;#F-jX:8m?IkQ@MK:j>W
pXdeQ0KT32D.m:eX'@H+I4:BCi!pZKjb!t*p$iDcmA@SSd?PFbh0Yre3')uLFW&d1bHI$Dn%
I&r^kuWd=R;?,UpQB_fgGcr61Q@>k'9d7)i")jj<i0aG'=Y[LO.#.&#69j)1%/d?Gok;uGV%
U]A/k&]A(*D$]AF-r..s5Ws,VLI6ou9=ZkCo(t^VRe"h]A!Vodt4M?600l2Yi.o.mD4op^PG_g,F
ud4-G?f!=/3<k=*`ig0&nk[PF%:BlRp@Do-HsiEVV`qL#@s]A%R'bHe9Yi,UHX2,3qRp+[(GM
Ks!qIVFh,'A-1qAl;P,a0*e'lR$WT)8^TWWn+7EG:L&FWZ1'\TZbGB+/?<_1qhQ!>hc'bAtN
8]A`&l&u(aQFS0X,$:/Ws*FXX@-2VBLH2m#&<#:]AZ^NrdknJBJNrQinOWO,1bF4"+6?r1;[4=
=72?Ziflm)D>k*_#[./hdAk#'-;#FXED$AOWi9P?&4T=K.A+.)U*."!4nqsR`frpMeE0l!FM
`nbafrRW<Hh,d>P:]A=er9Kqem^IC@kj&]AIF:ujL%)Zgp>M/U*M7ABmtTuf?2q^tWS(VtaeW`
F$*`b;u^nmR<&Tm/,sX81t^1]AB651[o0?iM1R)73-F'hUUk)^Amp>)3,YbHt8GO]Aca6hV=n8
W+--9&65d'&^Il)#FRn<_XC@CRham^494\t#^Oa0EPkDd`dM$3f2\T]ARp:lad0:+.BG2V7Ci
6%Oc6X+!p_qfQ3,@TK7kP<onq\Xh/GP2srA8[XcGN]AKWB>cMnk0Ak5bRJCcY5MaGH2-DUoCL
H.ShRW;mJBWUIr>%R)uf#Ts0$;_dJ%kBoKJ4[&XgH\"W^<i>GX:F_Z(D~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="30" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop = '10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[cwfx__zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="数据一览"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"^;dWaHD[WV5hk"O%LX4:tR7NNa2:Ujut>,A9cB2N^k;6^NA&@$La#Be5P$'h$WH,LoQ+t
9?G74]A$M">^]A*"B5M%:+pMP\_cLtHJZR>rkSciL[X^2h;A+D5POErpE&b6gNIE9!!'SX^HH`
M!;I[XmB^Af(26I`d00Na<1Q/Fbn!Al68mh8bXZKoU/UYfifd0bErFA6o_U;+2f2Vld@ZY_?
IWCC(E8-9]AZ,soHo=ra1k4lkr=98Xmq>A-dX!n<jkVT7ro:>'NTT5!jQKZ1$AV>"&j&>j1<J
g16@``uI\6s7&KrM8\-&/1VKRa$/5hK.)LLjb!5N?F>=Q.tYAks80msQ4ZX5*7VRLO1:$W>Y
d*gNbd++15!R!p_LUBYG>PIJT"WLueIh$jN_Kb*;7*gs7kfojB>MK4ZVi>nShR#!k%b^D15.
6_ho\6!)j8Y0,':)>!:PWlDX0Z4M'G>]A?VY>3%g8!Hlcb1.pG9ZWl`rFCpX5![?dJopMPgYr
Y]A)_ncNg#eg"P\;]A*nkojkd!;gjqOU.BD+YMKq;?1<0@5LB8Z!a[cUeU9Q4Ug*2[%8Vi\>Mn
SSX6VO>kg53(";S$XTEa$?(gk1Cn5G+OOpHY_`GbeP6FR]A)]A6r?^9\N.UXG2Gmr'jf\'JS=^
5_Yo2*WBG588r2=S3W7r4<;rt#U#4L%]A=T.RI+QMtM0$T"kfUC*qe`ROVO%/%!SU01c-t<dP
W)GGghIhS!Bc[Z&ER2Esk9\_BPCIdYSZSi:#_Y7s<V$e^c]Ak9=:mYd!>^Nl9F1KmJU9=Bi$*
a\6.,-_c1_<o:7\T6C+`5aj4GKJNb6,^\Ep`6'.27SMQ0B1KbIDB&pp9jmgt3*FZbK0D5ms.
p"PhK;oR,*TJ\j'.r9K&aIkWcV?Kb/JQ<_s9^9]AT2"#E$L[Y-'S6Dj_GA7#P`>i+19s.-E?i
b1k73PMstoUbs9fL?H_22^%3BE$T>G1qmP;"EV0K!9Q-PUCdi-@P/FT6\JS;\9*hK>HU&Xnt
Cu+a-2+KgN@X)]AM1?k1%55M3e70Sf1MO&/-t-,q9t%4Dh*`[dr2ed_qUCVkY4Ho0h@_/(,Z-
JljA%]A<IQ`k6Z$A;m-OI`'MFIeSq5XHY&Nj=*c-`b=VoPg7TNZH*^!_q5m%:-^3<[27;3DaZ
ourLn&Uo(f-b!.OZdLCPBs&d`3[`jGk6XV+5,ao[,i0q3kfl.m3a*)M&l#1T,<(_:F;\F[)Z
W8e;sf2:Pl/.!pcU\nL!14(2M@2FTWC@=cF/42P>?9[2K$rgYtIO,gc<q,#p:OWbn4\1(QIM
5R]A(oI)::@;LN\W3q#WUg,!_5Hq>83HES&,h9BJ7#)U:W>R#7Y>M8_d5Y^[JVG.4q@bMVpBl
,IL*cS14n.\#+jCtlR1-.WO9C9AV1i&F]AZ(3j-hEjI_k+"JOeXn8cY:%3?CgZXj8iIk)-ZZ2
MuEQO^D5Tidm>qXSkpPI(GlCP?nmfKeYnh8>VVkEWSl;C\X%*'h@Sbl"<9-;<V8AgBm4.m7h
flXH'^h*E@H>IBkkmJ@BQe35>X_@G!TS=Xb+]AfN&pFkk)Or\<Y2'@GMn`3'[r5UTS.5)auSZ
$VLiSZ*`K,">G9=TqomFi(tAs&RmLXPcr=FQh>LM9TN0a9>@*\=2iM^VB2[&DfKF3"n$ij)H
S&,$%:VWA>4Pp_gp=jsX2G@R35pE5>&A*`kouooCD1_)L[jZk$#+K#IeiD_V?/)j>HY%'/bc
11QB@9ZQN`\pIpIk-p/33/,a2+]A&;ZTYh9?shKng(QGn8Zpm'CToM8s?I"C-C-+V(7+HacE/
6`>@;G/&]AdJ'C^e;,RqB5Xjpm%+Ej5_]A^O[GfMu2XQ\qboJeRaR@kOj3rHW/<1TC6dNu#NOZ
]A9"M%?VXH_qcLXB[h`ef&@pKBf+_pk3V3#^qDmR\^(+kGDKRiIgm#+CA6s_OWNs>q8<7>jG@
$U-9i!ILq5rV2-8Y?[=5?6H#pU]AJgrKN3&IMEh51_7=&Qtk,Jm%Y$CWe]A>HZGHPOZ&`ujN3q
"OVnfb94D=J=^^U[=:l[alRqL]A*P0Q152uGfZ1IKQ8#uUA&o%[$@pUkK$*8'E!QXWKi/gf85
_D.OJ:MD<iiR9^i6ZGK_W#@ZuYcB%EU^;pMsm@]AL`0J&rsmi!K>g)T<uTJUbEYT,?tW/579L
55/H#cBg-=k%\8KHL6NpM]AI:abpqSiHR^hs\ZE23.n.cuJd6/`DRS&Rp48^JbG?!ndb_n9&N
r`PN3)nd-$b2lPL.<$[X0!Rj=!"Y_*H'Bo\@D-hql"UZ1?,0qtcnUWDLo5#lN2_,'-B5,q$+
*k.phQlFPp5-ZmZ*!n)HuJp>[Wk4\VASIh.E3![:E$8u/j1>52X$UYo]AJa1;.98bRY1TnkLI
IF]A_]A83Xb:89b$,K$Vg.P[Ag12!`A2:mL,nZ/]Ag@+@[&7'O&P1H&XL@*suL"%&7+G:dD7c$K
dYhgD"l@"t+%:LMbcpp+N@YJDmL(5Y1/r:<AYS?kA<i"gng.FZ\T8Zs@a+jFI_9cErOnJZFG
_6K@(n11$?[,K?q>K@^.o.LZL$/SfF.GA8dP6Rd"b=_*VdR5/U[q-]AkMjA$476)=JO!R?C$W
r*o"7NRh3`VjsN`DTOBZq$qkZpnhkn!%d5!A,@d_F.QmKYe$$I<l+"R_6ChbHHLfL0RsCngt
:>$f@;g/UrAl2tqJD2b`^*bhh/D^#bY//-]A8CH<02[U)J?+u%remnY(2`j1dp6`#t/3[rcd9
=(^@nTTE9Xl%n@+d!^+9$W91Q.h=gJ9Zq,lGuZne%dK88[YXRjmoH1H(fF3<tS`$;OQHL#?B
9m;2Gi9"\a,%\qpgdZ<=(J:gE@ZP<kg!mS[hai0[%oH1=AN6mfQ'W@C+u4`)`E'\p*+LBT,6
)mOX3d$B&Wa`'j1@%\i=o5\p;dhn_?<]A*_knNL@!b,X0nP*EgS"$:S>5ds=W"%9G$`15Pn]Al
#g+%aaub8F*^5VS)AZ:eEQOSm%A4R&p!B(9-4^:<BV[2YbYQo4U(Ze=LnHoDV6R)JU\$O-LG
<CF]A*V`gd+9ajqO<]A,5)02C;q<^gW&"i#<&FQplHaX=eEB.#W==UTLuQJb$GQo7>B1%6[?^e
Uu=a<S_7\;CBI3Zg30437_p8V/!U]AJ-#N(PY@_G&f<H>nS^9Y==Uo?e(*QK+%<t<n*&rCkk>
V@1S2o+.),*$O=cMjqfTP=rgg#Dp&G60gA\1')o0LD=FEseE2.+%gb&3.-.'scrT45^JRL$1
ckBd<`[`IbG)ZftW0nP>n6+]AU^:SOYMg+Q!Ojs,]A::;h$jJF-u?!CRtMa'`M.W!!#dkR&YO+
OtC5/h9Le[fqPI,O<dMmj0#:V]A0EB%4(qP_K"`*jIJe\"b^aggOWW4$2giad5f!oU/RSh!m$
FP5FQ`5^9L0)^6lgh_#qVe1e5rD0_6<W!$EU_@l,6D<OgjEc_-Ic1BrK,/tcd1/98_6T)]Agh
.`'2Z^AlfO!?G]A6Hg=s::,e'gE6j@3H-r0rJP6DKN#7REDO275.a8d"'pdVhkaa4G.LR@`\n
,]A:d_J4aQJ/sbCWi2K;j;N=Zkmnp01"[/Je3"jWo9*lQpS>U4oXUWLMXSnlbc_-+8QZE1Fd-
H/PCPTuseE9Goj0.9C[A&BuOXP[,^`elCu>>Y^AK;<3&@V5b^^Jcli.r5m&VVKogo9?EmsGF
e=(?("j-Ofp,j.5Yf40AO2&BoqG":"LAOlePu-[/88O^'3&,,Bf\W;Bm%!2DnmGRR`0'423e
Z3CN;AaGGj#@g"A:ZGIa6C+MjG_@lsXI*7uN:A3#_To:@MAPYQ"AI=dt>u1A_WB^O0BQAIW[
CK'Di7jQ4TXaC_YcfW/m1+7uXEmpT7.ZBPA%]AV&=$fBt[n`Zo/D2\;c\C?.?+RsikR^2;5>*
0nFe:E2VcSR<lJ&1f1i]A35jUFl_`Rt$83X([P[4<`0mYg\D,I"*ESJ/^bNFbsn&gVp>Xgt"J
_;c-!pj?V`ah+?^Qkc>.LhKnbq"I)&X'9$!E7GE&7N!f?%Zhj9ZZ(@i^-FBi`PDVZm%(/p_4
iJ\$<L8OhGgoNmIk'Jq',FKkYW\CJX[-!%T4Zh%XrSff&'&sL\MSe,_i"*8!IJ7p&0>g>OC$
\LS&(O1uMo(1b%=cb_^*>[en?PXg7G6l;(M:oUb+qc#iVn@uIq3X5H9m;guEO[t6QU[)G)q?
p[OlX=>?4`RUo.n-dt*GBeCQHY6S`=5r4&mml9RZ_dX1I=Bu%<MWL-3/8B0@t#YeqTlTgVg^
p!gG]A/laEe1O!(k.O24$i9c<knI,KK-CWd:F\nT\%Vp[c.p!X8=E3=aE[=ipW.Fu59WBP;Wj
K*shC;(<NG#pi23Rh#j*qjVC;dY*JDMf*J*2k(7,0/HTt,k:['-H[GhZCt!W=ldcNW8&6F8'
RUmdh'n>8YAc33Y/Q"nenqWq&6FS#?Ua2qW2MB.q0:)!89\94:&f#V*ZN\b*,d>R8k^YHOVU
[7R>"8-q/?imOU)f@F??[$mi2%3%l$/W-8eVIRr6Pq5Y(q3i4I%j+ZT7DHUOiZrJpeVNA<l"
Ea7j.Da9*P(m9LjFMN+"laZe0I3nP-$o#p(63\!UL8Gd$Vo]AG?o/W?U`iHjEM[CQR;7Jf:cO
V`a[LTP>*jTE9O<\Cia8FteHq@M*0_SShiGdSL\af#oP-sGalU_lG%Z4(*`(Srq/]ATbQ9.Q=
`jXdm%J"oWf\HM'>5+#TnLb/u-?]AbnIdBEWMmpJjTI2gc9o+-?/2ZZhl/bnMC*IqF<8G;o#F
$XWY0]Ata0$q_29KK^#VRYU5E^Y0jRe<Wr:tW`4jd,(2IJ.F*TK*lOGqR%u!:E$b0:S.hT([R
b'RG;o,J)<OcEtBkfp9<d%]A2oe'G_qO53^g^RHbefW\RCbNn,*W#q$tY\-27$XsM?U3/M*,_
<1r3gpDh]A0tUZN^i#]A*)`s*':3Q@h[h&qFLoi[1SP7)Lf6DuD5O*L<AVBSdO?o+e>DX#mYu,
6A2f(aKB`kG%?b,$$:h[6*O8^&5[,ch=P:!$&"GCQcmV`uulX(6uY]AG'!m's^1;f.>*1Yo'j
lec8>^Bb`,`U2.b:"N(L#Fg/dNm3N?.!5^dG7N"2GsA1;rg9@=3OluE7FEV`GGNg6e-^f3P\
g@"_uo<ddUr7]A7%nnANh!#:G6BA4a^He2ok0569bB*O?X?</'p?qTd1oCTRO/ugX>gFI\f]A/
)_RZ[Ig5-+*=`ehA4)48b<K7H,Z^ImEOT;fX_dDph^!<LA?\\I80IaV8-5Ts^C>@;4k,K'@a
Gd\,!i06mj%aqkkcc%V>VO'R+TD!LGJ&*3p5^*Dr:0";pK=B)B'g#,")h)VWEKm\fApKF&UV
^o8o#l$mFb1RJU3u['R\[*c!9FT/,!a,-BS,3dZ+G&$poI68e:kK/(X"q'oStFH5\e]A?CKIc
iUcSmah:.rH_+INnNh)3GI&#4ZJaq.?34?N8,Z$eK26tiq=m8^qA"4QAt_]AP4[>W_?5buQ,3
8afF>2^4\W[UFhND7FPdRi.IQNI0h'hbJ;W1a@EkR<r:u]AYg'#&3*:pH3mo%]A5Q>ht8AhM%,
1^PUWl]Ael23;Y%reS!^Cp/#S)Pe[fft-gZ/gINCXH[/5didj`+.KaFk\.p;"/=bh&6MnItqH
)Z%Ba[ZO1PDl0re_,5.^pjVp^FW_AE\Bb9^)W+J%j+4fST&NTM]AMIOEZp/lPoiklnDYgm5>V
TB)4?Uh\:8U$T(6OtPn!1GV!7aH:-0m8?5W[([u7;O;^lGs4LEUX7s]ACfOQ)l,,cajCV01U[
>%XSRB"2UK=\a^lZHXIVBkL8*k;/Q=r-;<,0Ebi7YGgJe./WmN.@(UM^N6$uIo'@ETZ#mS>?
@^2@d&/DiNooZ/A*jW2`@3=2BB,bSr(Fb?L@EAPa<@pp@W@hp-53`jmrZ7iC,>AL'd2pECVm
_N?:AE<Ls(dB&.G(,,'%(=.5`C]AHl\TOJ[0VU,VpMriscukL#%gR&jJH^dbn%gg4ho8\^3L*
tTR<aAnN$,eMA`XEVuM"P3/uFR(t^"6k,q]A'n@\WAQ'b:!csW>jU,*>`hZs'=/$OipT[DF\Z
^eIGnD]AV$-YV]A"6l049$9t(bG!alJYV&M5L`=,(BAP"SmT;I\N`al*EH7(JD'_Mad0lN`Y5j
F@@L*+GYrJ%1gk_`c:@Dd+<:5=e6Y8[?M]A@n)/6<K4;k42g,`Znqp)cPjY<3Ic:pFh3\-G'Z
$)Sn>epNMB&N@Qd4<>N`"Z.Ca\VPg'PZ5k+5&[_sf8:Lg.?ls6aP=Td\--W`Q$S:p/bhht[S
YEG*IL8!5k"\eGhi:@]A??a.m[mT?<D/[uB;A-r,O[H3Ah`BmrM:2:_"Bc8$[Ro]AO8hMi:Idr
m)]As\LCHt=qU9+V-4b;HhCGpLLc+/M$5XoFe\L;[T00F'V]Aqg8/r3F]AC;&r..<tN-Li&DN]AT
,IakN2'`[6hrXcS$V1RTg4qV:ae)m$f\UjBJ(A.Lnk2kEFaXi[NKCWm[R]AgiEY1;9Marn/EC
>gYU.8Bn6!):PM^k4]Ab/$cgb"]A:"tVW]A#/o`3b:3:c^'+CD2^ZR2R@A@lV3%bJ0sXb8sgb2W
K`X>7Rac-D.Iq*%2sQOE943?CD:`LohIpLXl"=p"F-e,FS$dY6\C+-mF0R,=E/VfEfU\&^B1
0X-f%<(UDf=3iZJGSKcaX.+C/lXdhQnm6q'(jDSdro\G6LX$P>TMU?UXM%%iS>\)ed"<5(:[
W8Xf?qTYuZBs7ceqm7#L=EbqDIt[%c>_K\]A!ongZZ5"Gn/ih(qY*GWF\M*^)QFR;_Uq4hWk'
STPd-Xqgk1i98mJQJS/GX*Hag`Cl3<F!9am`"O_oC`8nR=!`9Y1oj'WDlT0JE:9n<QiW_%'F
`S/bfI96'BnF,YHeLbm9DJdH*rr!r*;f7]Ak)M;YQ-LK:SS,[m_]A0H-te$ugp&'&K)L&_Wuhb
E*GCM+hN/]AoR9>4K5,jkA.0i!Hl\'c1roB3+a`HPc5*4.1TD-dfA78OMT==JCOJ1F[)+J&i8
GTdbj/$nbbh]AC@=K+8f_!4?$JQT,$N+_s_fN)%#6PfAf@B$Hs70g<-3(5@f=4g"]A2W*]A/u]Aa
)c`MZ`!Z3ip9qA5JU.gE2C2t[s"JtpJ:=L?njFFk#F8^m7NtUc2J5>m-PZt.nu,'U&YT%L/T
-2!<@Z"\PFug?W5\5!5:W0Z3:6\iF0A2!(]A%NLY(L-GTC+GfAUjs*F=NQiH9Ac?AI.kFa+p5
LY(L7KghF@,RL+>k^?19X8N9Bo!l`FL82RLr?h~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA01"/>
<Widget widgetName="report1"/>
<Widget widgetName="report0"/>
<Widget widgetName="tablayout1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="638"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="674"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('REPORT4').style.top='-10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?hTi;qJV$ku!?[>L<PT!s1X+$<1!).&.qhTdV<''p(.R%^Q]Ah5R=S-LC/VuWg--u;(,<KO>
7KjTLU:`,n,M.!MVsf&0ME*KZlBkg3V%cTBG).m=*-34=bbClaQRkHb]A$!rSNn9S\*(CJ)0d
!*?C\H:S2YgVk^T7=nCDCZ2+rTns:C<*P8AMSPEE\/t!ddILQRpb`4c5Z0e]A82!GBtDoMB"n
uB9*4)oK=`ZE22f]AeRrM>-j"pZ6FEGr\k<RVZu.@<EMAIL>%LZ?#H;B$CUl^)`Q/oQ
\/F0BH8o>a(`HY*"kggY&G6`6c.%'ujrGE=aYZ5>7Qc6s\fP+Ifc,W^O3Bim-!gU5M2I2bLo
OFl5Kos`3qC^eiMI!Gj]A>IIW%Fn2?WliYQJ\UJ0X+FAY7oCAp7r;eNgGu`\`\8U'E&L5plc[
44[quCB#qGp.!fjI.m@jfUMA?m^h:OVE_hCSE;6^<.BNo3J%JK33a^<8IE5>*SJ25nUlA"\P
t8%gSM.DQMfeUnM5-$N/Zh,G3kR"gb/R'#g=\PfVN40VFNb6Yl8l\.Rp;KoHnFo_o/"U"\H_
q<^48(NFh:+`(M6;fnB$6[J`;>bZCU<F)$g-!e#1$IY4o'D2;gK8(L';_L<VX,!7B!@pF70Q
;l)g,Mb%Q?h_ROo3Bm']A;gJLadXOmZ\;G`2+/Gq34&?TH'>lZQ+QrAh,JaV<JfDfZ8d*Km1>
e\g0g75c5f2*kmROpiFn%0\99;\_$.Dk:P0c%.0.__&mK<hF8-2Y/=se^7/kB2JXif^M;GFC
_Q^Y,8Co=ss`=pn%&#l1P$rpl>[CKVk"YOKiZhlWR'L<bM?:HOr)JptMpI"BUr8hd:Mi=dCe
@YWjOaR,5Q\>F3u6"+"]AS]AFYDP;WVcL0jtT72tc;C1#W%7hDcrAk#l&`l1)''br7OXe)iV$"
:I[cERjZ'aug(#lIG-Q(,cfe@lrhR6ABL7Ea*D"c)d;^fYYA`RD+I:Q'ddr/%:+_$<jTpK-R
^kc*4j:5tYcQ%eqp,[#6mm,G8u&LFht/fMkon"E-sW0'e8Ej.WgO0@/[FNui]ACl+M(J1N=(t
7]A`n1"5?mrN-P@6!"U@)pktcGe!'Y8PIkJ9DMoU9%q4)P)t"ACCtBUA(F%"-.$G3]A/Sa+Iqu
l?.=DJlZR*T7VgH_%M2O.[n1sqWLp+G&9<T`usd:H5ObEjmT>gVp"='"LIn16=;HcCjXT%!i
oj%e_L6Ko>KG[ZORZsmun+"t!n";(j)bV7/&ls0=jc(FH3<F/oo2KeK$P5O2h@d$KtRE3L0D
n&U\r`D19BM.lbatgA!U<sqcWr;h[@T`4H%B>1.TnS9k4$b<)0)>R$/GCfopD-nOA"1mL1]A?
_/%P>ChTtU3j?JQrU5KX'ibfsPP:lTr4`@bN/"2AV,g$c:/62l]A=Reu%UfB:l&JauiQ*E'-e
O0,K9&#!V8f%rkk+ldsdia+$2Lt\+^jP_O[4dmrMRpmajqifk<S"_C:a0J78@^FAk#-=!M":
f9#m@kbR']A'@AA4<R+d@"J]A.=[fU.F&0Z/<^n[PZE=Z#8+`U>g\>-c71W^0&4L.GT,0^Q)4Y
\O1`EsK:e9i:;JLVa0sO<bmHX47rl:AJ&F>XH,;l]A;cte%kc@$N/26$/d7NW9>1d)rB'L55'
C&?XI4TJ1W1n9,.A#Jf3MfNGfOTLFDuaq2$[]AMW6;!\9$2/`&)[^,r2'_%coQa[,h8b\.]A)g
'V@nXQTghBD%N+/?>boW>K9KWetj#s9Lh]AL-j(Yim4'o>UD0fiUfP9eJFDrea@IhM7Gs1sa7
(?bh5A>\B2JVug@F[qkiG'PqCTj<Y9;YP5:UmW8S]A):='@aIiqjh_<G$D&_B5n5uH:*jK4=n
j+2IMg,<s.#l0M\uI<lSCFrZ,5MljZSWu=6D$7=%;JKF=^Oj$YKQNW!s7i\t&m*JR@hV#/GQ
.2fCo/T.!2k8hH!Hb^eb52c5E-2f+hUV/bK?l;hOj4=[Ee1X"2lClHT!qD%1CXRFNYBIH_o%
@nVriMMEljT0!8]AiTi7=cU??/'>&GCaHt-OkoBOA**TmqqHinhN2<?/bl6iS9^N&<E%.JdTZ
K0CL=Q)V2=HTV^[.-n(WcI>2jH6WI.-/SZ(1%>3ag0Hs#9Z30nlk%blTrFQCO*`#!em#iRUb
2g"D>ba;_G^Ae=bTkV?'>9\IlMhA@u_s9Oj*f,.CP>dH>k81nEeTh)W+aSQ<NdpX88c7rBi>
o@NkaGU(HnFm%+Q7p2o`-+e&QO5Ac5dSemQ8iVVL\T+gq?\EI8JMap[W8]A;d=h:l#j^9pJ8)
04?<m"9O?Bj%#+hZKo9$aOqUEGdp5NCEUenAKHTemL)h=!T#fu"kXJ_4M)n]A&qM-2h]AY41*M
bEK0#/CV$%n,AS_/CmL(>HU3```(mP1.]A1`Q9\DMcTq%(/H;6LNj""I?tS8Lt2#e%8$]Ab^/Z
$;9r)=*/&*_,7M*3/rm<."pAn5trNAliFX!G#`"^?"A7*rD/#JW<%YV$I@Yl:O-F;>mr.\,7
!-AXKN]ANcL4ED,eiX,3-^lhG'V(=EQ1]A']A?,]A'hrV[6.jDrO84j'`^3?9ot`s)MNq4Inmk+.
ho!E%=1oq]A;>F0<g*XC[-'QM\jEFGG]A/fi/>Kq*#K?@;OCub2l)<I4^LuHI6gEhE8([]A5?PT
*4V[F9oL?;8<O60_&6so[_Sr6u9hB[d/=#@Xd9oJ-11DXU&^hU?Uj-5.I%0I5R]Aq.q$=Ye%[
#?SFAUuE\Qmm_!IMnprXE(k7?^&f!bP3m>E&F61#B"Ih<45:0E#_3+5&3*n^L<>,9`S'HKc1
[s1&AcR(!LW1+7`q.P?\*h<[8>_nJFq7-<aFA$<43`U[Bo?4fQtuYCCJdEX.G-L!=JJ88XE_
SYU<Nk,[03Zq*LS!4QJXPD\[:JjY+Ua.qm;QJ7-R229[Hk8Gl;Y),e0IOLD@m2egod[!U$Rq
mVB;Fc/d.gh>&(^Wpt]A]A@2[p'c\(?VHdQS6V[sX9(m?OY=R[Q&l42XDC3s<df2--c_Tb(d"3
<MdG1"p0A8`OBa!rj!RK89tlYZ"@HgUX+dtZ$di#0oEk@E:@U`Q+ci)pT(Ti_5!#T8`'O%0[
<EM:]A0H4,,h/C>L=@"l(Cr`>,L^M5.<89M$Weq8C>7mN^pWtAT+Lcf;ci)f:bJja]A5qDgVR,
mnl`_b_SO"jT:RU"4'<Vnlqtsd]A$-c'U##mJU"l;P%ZP<X9aftC^I9'.n?cGl]AO]A(l#T\gP1
ANQm*^"1r!_7r8d"&2`lp>g=[h\9TL(qEmhU<&1Uh#7*WE#(EsB7`LAK\[8ia0U%`5G\j34A
kGF?Iq""i*:r:^qU\,^[9;naON4qN:`:l:`!a.D>no<5gMiGS*G"4.NGNDokSmk<G^aU*\LW
uUDgVb)@,)0X?"!*]A#L@o<+*&7qr8(=p[^:KrJrP0_bTZTl-]Ah^Yp>\l36IR@n&[U1n\K5VA
mo"AgUn-Rqg>lB7EH@;NOc6$DHP\\'rpeUpEEu0e2!0'j"ie:,n#-beA73j:)O&&lFe61==1
PLPHoc6X0CjgRoT2ISbpB:0hF#aq8mWWO1`!UF16G]AfGYFqMqQLk"T_7"$lJs[81M(_Y)M=#
Y%VjW:16L%[FHm&>Wr*,c,nEF%\uQ%=#jsnj8@0@FIp,XMj2Ye$&0eBE^(hdQkH_fo7L[03;
@:3dV)Pn'A8%(?3*=C(dkk]ABsCM"%_T)#_f<5:J8=++M\9)f2%Hobm?,FX$OsdL.3elsl%(M
`P/b,nVWr;`P@$+-K%P[`%72[QFb2'5l<Pm_<EMQnnP`+?/Df=&\"_RPqQ*8KGRm<d>@$^_M
h9eG6#g+j:$#:LdAWbG)Aka4=`b3ZSPt?:rT=S=?`Hg+jV=*naD-a$rTca+(0DHjZKjlh$f!
-JkEt7ejl'-LTo-H=-6llm7W\L+[V!TK0Y"FeI,k!o-Ce'o;mK/Bb?.\fIi0>\QnDk:.,IFH
9pX-a`HN_G+Y<H]A>@JTa#jXX2c39muiflukl#Ul![##[,Bh!:&h<X%)$lD"%Xl_[m8Z>*[&>
ap+HI)nd397-'Kik44E#9RgDJ;Sl=cG"UDito2o<H8qGReYdV54?RU6EN_i"1DG.i-Hl^t9G
qF\S-S_UH8Tf7>JaO-`Y8Lrrhgn#C,r-l,d)^0p7?['?bR,DqWE?_NA3Nq,,TWp,9d1Qp\f4
At&0$_M&NIsS(n2LDg'7X+$c<o&<1A\8c]A=^e2d7pEWY?rS@q>'qEdos]AEf<`SrE#=Cj[#'h
p"ZV5T]A.m&iu+ctkrDsBJUVNI>;9ZkSs$tC`]ANpEVXTM_h?6$@h3CC(a;s*Popl4(@R(N,+=
7#/#+*f,&,Y<XJjW<,VBi5Kl'RGjsOL:"9c5M%[0pVXo6bJK6JkZ(%3PLKG>h!GMp>H[J08Y
!$:#5j>m1I4FU]At84FD<U(OXap%iUUWh+Yu/&f/j/$j`Kuf5dMF&J(Yfj:-Za;NeZ.>T;EU#
a=^?(OdfcBC]A!'E>'CP8fh$:[KjeYg@`mR#/mV(8G&t3`o>/4[I[UpM/2_h+)Z"DK:JQXp3W
kO%DGUJ83J[R3glgct1A@+mAN*PLm5Z8p1Z@(t%^01rde>3CS0NLj,b`4hQaOAI^J4)gTeH/
pYJg""C6e>fl^(4]A]AqpKMLF!9B85cR:+*u?W`d!7@oN$ib?ZIWC&FM0eghH=o#`E'`;SYa>S
=k91KbOfqM?$#gGDO'V:fQnf!ai5iEPTqB?NZt(>oRA$"F0d,De#@MK.JZ<-=-,Z><MUn@$S
Affh(r"L-t>XXC?gq8Cs&"d#.tc4TGD=mNl_9Ac'D^)?_.^[B<BhP62cV]A!d=3Lj/b\:3a5Q
`8oj;7_Kc-[N&Pk9s+-D`k$6,J,?Ak2%(agpgAOOBG,fX?ZeAL57,q$5LK]Ae%C88@ApIle_V
BOl5PYuC^%3'AX@lr251?u?uq*4qB8WL&bS/0&rIH^1ZV.]AbU1!bJjPlY!N97n`&//)OmYp1
Yh*<DLW7(9dr.5W]ATjPgE=\h@itU"AEOIUgt;,HFl;fcR=E;6l\)dZF4KBi'tIFtf2l2`=oi
QEIMg=a'8S#-@&KI*?1b@?Ag2>k[3!c=<^9>r;e2AJ30D>p$WP>Z1Yt':OO1QT/t.D@MLN8+
AZRLRFX2G)rFWT%j&ZY>VMiGtX7SLF]A-*G>4X$o7]AWS0(jr;o.76-K-J^mYA4)>4.PapLJNN
#kD$^$;K5S78g^VAR3=,4d1GtKrjqYcrJq&E7RBp='(OMrUe%Fk>J_r(!srg8/MX+HUSc6d_
#5IVM57LSaq%M5Lm33Tp5Q&7V()+C8K:WFU9GG<A)E*CW;N&?#[ArIr1Q"_[#sV6-A((kK%E
\p>s$#F1m&Y)7YJr<1o,-ZmWHFc9[Fl;*Hj3s?-R;ZT9J^>?$LA:SrJ$[75No"76P+<XFZD1
J9V!qg)C#G74oZ_F(OJkm:8_8/'.)S:2<9$"Io3Q_3Js,b,`M1TD/-YnVh?52g's$4k#VgDd
i=:7\dl:r[fj^JRj/U9-rnH;&DcCF+MjC51V/%?TZn"6PYl07GHDL,Bs'gq2ntfJ6Y>Sn)b"
$CW'8UB6P'Lf^Z+,$]A;O0Y693'-4VoJWr`c"]A&iBNU`?6fK$g5:YsA@2mP,H"r8.k3XKp]ADc
HZb\)-'sHVn9Gh$,#D;)=c!Nl%AC6$W%(eCucS$r<QM>-_G>Re1CXG"!/8dMJX?(]ASP\WYq#
R.6HfX,Q!t*c)jtH.l=d=!\cWdqNGl]AkJeuo4-@JZK*P'TpY4eL/(e+=TW7JQpHVX9%Zc2f_
UnR=^m(HNq*2bFu-3A(HT*@:-E*)E+jetcrG/n`_8,gP8<l`hJqbE"mCXuqGi2ZTV3]A^%Lrh
cZRFKV(!R;8t73ZDn^Zt6_h1sL"B:BO=<lQ98ZdBoK?Ssnf"/9B(8ToRS[/ZaZ6YSPg,>$)/
J@4eR7[BLFt_HL%LCLJgmruGE7/I^75*<gkBia&VAPH"BHrUX4e?iTYVrp6\n_5^;5NIAqEh
4lOHXZcL>S[G_oAA&pcMeZ*@W^X=o(=f-_</YKg/ZM7IW#!pX>#^Dp;(_XZ75sbZ05rSbmU9
e3:-6'LeUDT(qL8M~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png)'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground">
<color>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</color>
</Background>
</initial>
<over>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</over>
<click>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</click>
<FRFont name="Default" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(0);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TITLE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="移动战情室">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="rqsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_self]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HuaFu_YDZQS/1st_Menu/Home_page.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8s`p;qDqID%SB:O^8.+#=9rlTKL./8fB)da#'Uqd9<dt'J3.H)CU.J+G!.C+X"q7W#.kdag
Kc%!Z!CA%H(&KT(0T2'GD[0JH1B8'*9<`ga$iJIm\Q<PF8bk^"<8W\+Y$sRFsSj8;),ORk.D
be2lTE!Cp!81ViLh=K@MX%c?M=Tj1*E!IgZ:'`T?MA<tG"1s)YER4>8H,UMjU6GQqanYFD).
M:\rhW*efeop\VWiNUh"mjP*LFg7dZX:$:O'@$ErZA#n:&eFa[lYn6V$;/2.OWDk#1FjX1]AM
4J2T=6ReOU;lj!"_p'e]Aaq"u`G[WZK]A2]A"(!hlH<h0GZ%?)O43`!'kac5^21;&ZE'"[OX63X
frZ_dA]ApQ$^`*0i+%NT/1eX%&9dU-Onj8Mf-J'q;_[935,bab<FPY9[T<k=(Ls:.JL/Lld`r
LpW?!cCG(i@7^C?[)U_ZT8COBfhC[Iq:`8C3(u#Vg5W\MMEADfN>tVljJ5Nh,fOD12'Em@`^
UHVkr"6g1jkl]A#LX'S9Of"8GW9g7aST%[bK\b&$k%p:qV$G<[Um5X$1fl<`Ii9X_';=2+r)R
\=ol*ie-C&(V]Ara9N_IF0C_=j"=$fRju)_U5TZNaU"`#9TqG`Td3`P,@JPu>PA0P9HVHj(*0
,KmZ.-04.3_V"@&&-3H3)mRrQ/*EW&9=XNb[d/<\htAbctnUJ]A]AL()<%R4'%MQ&\MZs>`glO
I*qkoT<d?5oqei97+lsYV!J8g\6?dQZL$@+%e!P**BC9'gNt:tHKY+"KVb0[(iuV*]Aq%W6S=
*"`YDT9G1X]AXNX3$lV)pTUs0:ZL1-["i!>Ajh6j41KMNS8hQ$$TMKO78X!RB*@OoDU]AJ/O'S
R]Absd;?4/EVNYZ.!c/A%@T[QpCQ"\)j/p6;n>5SDGi</m`,X#S34U'1$A2_fbS[bX3RT4A:/
7eAY2YDlc"+?k5?.1aa2V"1h-u[=kHVO>.>*dqtl$*h8:C5k>c<,=fY-GD<#t#>CA4?VJp-D
`r]AcU9ukS[F=H[2K6;j;[7Ya"Tq!_jaOWQO;^AW#K_]A\M6lLX%FPTO,O+Yu4^-Sr`QIlqVZs
>j;NuL=+m?60V'39_aocUII9N%Z'a[m]A2E9r^00n+3Edhe3*1HYY,,)M$2<8V+OO/'U@3Jl^
C=9&C]AN4bMs2o<=?<Aj5J_Y%BJ47<iCsh91oemM*B(,CRhdnE#,Q31^-8<]Ao697micC9+60k
Q2]A<t>pd%3a9&2Ftc`)boZu`GakNJ3@q>/<Jc$SEKH2J,RT<8HIqE8#]Ae8L2<m^u=d-tLC@Y
djgM;HK^lAQ,Wn#,N'[Go,c=UOq5<1u3<F?.q.Yd(#'`\`!GK[f`:sbh_+a5oVCMa@P'Y>8n
pNDDh)^*7Qc!_uuo7Kg[UV:n)a=C?"</_gQR*SM>[oN#`JBNaNB^<n2Z:a9lN6W*A2WWPmBJ
7;k@`ZmBK(TZ1@c$+eVqnq:7e"LDV(q^4KH"![7mo^c@FIl_P.2f$F+q0q:rrL;d-k)Z95L&
XUsoJ`DnVi!:$!<M0gD7/;FBGV!@[BVp>qu[18Cf;NJ^emK_>AO4m'Wgi_dUGA-q&)%#M^2>
BZEir'$]AfLe4F[6tVMsdi<$c#H_IYHQm$dkU3Zm^`'m_FWeVnjO<0R.6='u]A`"*rn)MqH#B]A
'1niTI]A4VqZ*>elSfKA3cj]AX/>q"^\lR^$ZZQ^u/l9B!3T^;E[E.^u8Jd"^q&P/^Z\m=FX]Ac
L&nl>/R7V`BN)uO_cgq%J"3;[p@N5_3_8J2hXL1Ef"A85RlChW`>P82dP"JJiaiO82(;9qtQ
A3+3oe(eQl'f7L'$gT*ZA*&oeci(u3U'n7!\Dm-niVgGhPMCI/KPKgpHA=%*399cLA!ic$8\
Zk]A!cqQeVtB2TF-i;oTM%9f:G]A@VGH/;S\7)fB.RGOn&c0dkF]A3CtNctNk+:aXhdpp<C'-s=
FG#TH!F\47""jbp*TH,X4]AK/"r>o7rUprP_kok1%UB?e&[ST@u'#KI)]AD5<C-4Iu1gW:.]AQ$
-rK?lKfOT:%`c5%kjpOeM?7Qgsc'hgOh3@Zpe!T`\Wf1Z@AJBDKV(B><m@P:?#pekE[g;'PY
tH:^--W:Y6-tb4o`u)NPYVF@BUc!.bDD+9E.25Qi8BJI"Xd!YL5R"<nD.#XmTX"HGB85J(QQ
DK<meqW@4S*5hC2JFSp;[Hjd]A^,E@6G^\8`=s8ClDr[[0`Oj+td7da\j'C^$%mpmX6:IgY'q
lgNKSiS<.RHS&#Sj$W<.p-*s+]A%j*WI'\,lVngdX[,R.=gmeq8N@9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="13" y="16" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,5753100,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[财富顾问分析]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?[]AP&m-'mBsQ18b-;QmF_<7rdLsOK<+)TYS@3I<RF+]Ae0gft]A?GncjV:.D;NP_2*2%sPIh;
(Clblgl]A`M>o<VNOW,Ll`TU6:*qD>hkbGEW:SQr,@.2cKC^EmrQ8<qg:%;O,J-`^O86F!,ss
rA^!^G?o;M,3NfbimLuNO!.ah?__GMG;a?tEb%Kk4J+9T+p6/9r1UjlCEOkmS\\lYZDdt\D4
auFeH*sj5q9R<dbL^QO\E,\hEUBomDEC&=&9`4iI-Nc=onsV=DEY9#poZrpR-6ac'_Y'L]Ar%
[V"d0t4%>#A`-_r>'g:26Q\+8O5]Ar^\T++?9qq^%gLYR1@Y_tQ1^WgN7r'(udP`(,=c]AT4Ka
3d2)7&.t<#\Bjrb8s(jDP.i)t0SP_<1hde\^W^u7XAL<["3'&hTbg`OF.E9.cDprKn&Mt9I%
rF7M@?]A>Q@tRl\\Lo]AmKE;-LmCG&5/3OBcVb6I`FCs7V:DBp6o7P!Pe8uV,pcY$'h_+"bl*5
5/OLL8poP+ZXZs]AeVhijf?6i(Q(2fk+4idk%+%?`/f*m@KpBPMd$:GKYdSBiCd+B7d$Y'4;R
QA4@Wk%T1W*?3T^MGQ\)+pc@p]AN441`Rs9)HTDI>7$1YB;V-Qms7ifr6a/ACR2h(OSZU`<4e
6jg48EMDHF7TZ$7'ohH%t+U`,8IQ'<\A!UeT'UcrjD&j9eM$P(asF3HFlaC-hrJPEA3]AZ%.`
\JMp9<\V?".ucRoUdEMjR^mrD8N)?@mYdX\TNd_ZTE=fc:6Qa\FWN.75.5,Cac+e28C\?)Bn
H>ubTp_'GtM'NF-<Ua;PU93,g@-!+dSJiZe>l28<GaVR]A\MJDMC@Hi)dM`W2J@o%0>OML"Y`
n;6"i4&aqr`hKf1s&`D9*#!ZaLDOt+.e*7ok"="\j'9&u-/Y?^[</6Br$'Nb0+b::qkIIq>-
Prm^!e0-W26:`DpWB2(ADPek6'An?<UOt5<#\A.YL+8egWr8b'YWaf%(>sl<"2X>/3eZ8Ut&
*)'R6p^Ko6d8LMsJaFWUGKhQ^uLppLjo#4Q>pDA4@CiD'"9kqV&ofRh7hP#%VK(+LX?9NpUr
VrNW%Ao4%3]ARf?Pd>hI\YeUqAqf2H>R86a<9(AZ?F."F)4SATr)VmA'?`<VXR(>-:$A-a?A&
+6dY%hL'Pl.#ORWdRfaVK'JAIqrl=fsL+:f,)c%RL7;b*R/[3$#B*R/-(s%,r)c6*i_6UPJV
Q<^.)a@lPd+qRH%^:cPdCUK>34NC=\^;,F1LQR0q"70@D?EOZ$PEK+i9S@\)jZ.P'$FEVWpW
,$t]A?;jKN8/q[U-C2cN:\d6m[$2=YQWY)BI84pu`U.M\;2rejM*N8Aqb(hVc!<Y62t//G<2r
sr>Zq2V>2&/6Co-^WCBUBQ80J@a:3!Hqf0FI(O!=..CnW('`=W)8%&WdZ@Y=oQrOAV@67?!(
1>1ZM]A1Cn-9%"/P-6h34U9MXgAD4Q7n[_FHJ[a^VP]ATJ<CmeEX4_*hicS'm"_c,\I@uI/NZe
_kC25s.\hHMG.+kQ4-ZK]ADU?=]AP1+Vf-,8]A12t.#mhiH),U_E=G[SO$[G5bGs]A!?Mk:&lIfq
s=<F-+d(^FufVf-?GW9%5lpWEW#uG270J1H_.'EVXQuEF%^7CfJ[!8;/[u9)IRljsMq-@U5U
@6<W6.[>1BMAGQFA;19<2HcS3?=+?U_LHq/tiCTs*>24!86'\f*_eR0VA_AesaF*>$[HO);J
D@+V/j@_BN?#9hNQe-M<+[W9T"7):3?1`RkAn)=Gf>Q!IW#=)2ck&;SfS+#_l$(3`BMh9Nq8
]AuLLWreti/Pi#c@IRn6QcbHJPA\cd5#mdc8"6<4n1ADBm.%LcD/<D?n;%EjOr)+HU@-hiM#t
;^f+sf*V2mQ.ZL]AkS8R_oYD@m4k"AkUmu/e.<L^3L5L]AU$4cen@f/e]A]AD$CXd9bb/.+;]ATjp
,,WXHaV`_E%*a>Y2[F-7s_n5gGe6f3!5=ib;jPbH_Xr^A#>1)KNCO(&+cPA(V8m'U;4&QEW%
)M2'-hKnoK+;>(KjcEb$`N([bihDUXKSqM+V\esK+[Cmc\Ln2mM&1irNdC6m.fQm_GpJ]A+BO
,u)H(q4&7oBe8:rFH&Ok18douAg(@7F#XY7F%fef*Amjj-K5N#lZ2KuDPX?=_rcUPKcM$5f(
U:g!<2m8rLgCHV\[m4rQ;aBU]Ag`\?cgY<;;3N)NB4f-_p_X27(hP=D]AM(!&CYkUuQiQfRS2g
u:9V7YQ2L>WaB_g>Efp4-IL/!)T[SQ.1UFJCKGL9[VW'n=6ZI=_sp_u`jEUrK.f&&cU;KbuQ
,elZqjVg=NBbFQM#FF*IP8i`l,:An+i5RQs"D^\=)']AktD)te7^kt9de'D,ZGW%jZ*"*`q$a
da&t7]ATJVWF\1]AJWtbA&#&5Q.M0ON8m0<.1/[RPZ%sYi2)mXJYJ2')7$S!>'<uAd9e_tjZi"
Z-lGD.;a;gVNVgTT\\QJYb(4L-FJlL*ZmP%\'M;42R!(r,:9:ij.8hrdBSDgP3oXeui!qm3i
auT/3$*bA"8I4b)d4HKJ]A2(sL9eP[%IY7`"h\B@IHW)^IW[P1;X*PTV@WRYMU@/RFd0<CZCS
+X%Gtr7?Fth;E4m+WdCnh%C\!>s@)F#2mHZJfT\YHW#r(_KmH-j2lGU'-BMGG\"@ZQ>6UWIm
QTIOH"IP!Hpb\V/FY,m7ITaYa%DGl-,G$'&[0_2)4^0RrSY+Y\N2TaV(@%$$`^:tEW0h(M)O
Y:U=B?o`5h94Vjg'4"jgiiB"(A.kl91-e53Rl4'NX/63/>/Y>"`'CflE6Z*&_>8,E[lN+2IH
9C[XgC.?A)+GI$U*j\M_-g4>T"r0Zh7>6KgM<\lqu!?j@F94E`DB>U;D")#Lsn3&l&8eZ3j;
;<KPnoC.[LI=qg=GH7rq4J*cAKJdc]A@Eq&E]Ak+]A>pP1N>+>L&T@pr2?s0)]A6Ya<;O2EH7R_2
%!F5E3hR&1.Z(Ik0S:TmTZ,a'7.)Ma\)TD@AUj5)!'GCNN^+#06UHcbi,h?IoRbRrX.a#t-k
Gak7NZ#ZP(9/sa,<]A[7[N)S,cge(DMf,#:$r$>7^rmIIDMpr_i`]AJ5Gj4;l?#j[]A:f,]A(1Vl
&1O=jJ"6/B5-C_MpG8>hs3*&)HR,/P<D_%bg*6&Y+ZjDKet.n4WRH)Rq?>b.bRNIK>Hf?X&?
eq&'h:'eMKB;+!5bITcPRjkq@o8[Il`%KD8H)RH2X?Fa+hGdcORcDnu-=QM.21d*_,H/E)so
/pn>_OT263VA,t+?CiM&qKW,O<U;L6Er(OZR"sG0MNg3%CP>?=;+n%Z>YsU45nI:g8$.8pe<
fK2+%t$^\d6S*[JCu(8*K1.--FrTWlGdlAEOHG#RXc.mXtnf'pm7tdiU$<&6?tb+V[D/K#d@
<+"d^>F)&3``1?qME'jOf$?!?G+^cVB&\]AV]A"IZSsg#foq_2UeG2Hueg^i*T[;6jS\rR\`Ki
&3`J`d'_YcB`QR2uql'`6'@[]A^jq[DcW>(3IO?gj_ctL!oR._q3O'FBE/In@68:\mB@V>_fj
ZBgL30[L[D;d[_m:@%bkDRr;R#JP6$,"s&Ab5r]AmsC!/LRm(Xmci]A8Q[n~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="161" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="38" y="15" width="161" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SCTYPE"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
<Widget widgetName="BACK"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="4"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds4" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_cwmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_zdzb_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="0e301ae4-8a73-421f-a564-99d15cfd2d05"/>
</TemplateIdAttMark>
</Form>
