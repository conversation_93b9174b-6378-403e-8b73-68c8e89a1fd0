<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="Embedded1" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[指标,,.,,金额,,.,,同比,,.,,单位]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String,java.lang.String">
<![CDATA[HeR?GZt&4(D9Fmj/^;L!%J1&\/1`cQltCn&g<B5aU&!An*I-YS/:+nAkK/`e:V.B+S$MEqlV
cN<E6CK!Z0KH6?%h:2Y$:P"@lp^gY5RSTN`Ztq!!~
]]></RowData>
</TableData>
<TableData name="para_tab" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HT#Hie(1AAHJ4uCB]Aj]AQDUGeNmE_YIi-O,6n?c^!1U&-+7"ccr3ruTB!!~
]]></RowData>
</TableData>
<TableData name="para_tab2" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HeQ'jC&\[GQb+WZS@"anLnE#Nbk6AGhO!UC!<~
]]></RowData>
</TableData>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID, MODNAME FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '分支机构查询' AND AREA_ID LIKE '%jyhx%']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cfzysrgc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm}' 
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID ,AREANAME  FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '单指标查询' AND AREA_ID LIKE '%jyhx%']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${zbsx}'
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   WCZ,
	   BRANCH_NAME,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
DATA.BRANCH_NAME,
DATA.WCZ,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
			A.ZBID,
			A.ZBBM ZBMC
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${zbsx}'
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="SX01"/>
<O>
<![CDATA[分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where 
1=1 ${if(SX01='分公司',"and tree_level='2'","and tree_level='3'")}
and branch_no not in ('2097','2098','2099')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="typen"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE('TAB',SUM($type))]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth;
window.objTab = typen;
window.obj1 = "";
window.obj2 = "";
window.tabnm = "tabpane0";
window.url = location.href;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.tabck = function(obj) {
	if (obj1.length > 0) {
		const ment1 = document.getElementById(obj1);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj2).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07';
	_g().options.form.getWidgetByName(tabnm).showCardByIndex(n); 
	window.obj1 = obj;
	window.obj2 = ftname;
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$company + "画像明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$company + "明细数据查询"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<X+YdTP>5F6KlsSoWt]AVT(H`e1rg;2%LtT9Vplse0ZtIQ)Jt'n;P4X9j)a6nLVONT#OZ!9a
st@bQB7kR\HO<#=LpI76s5t`L;)(o6u1:hm_J8IICOK((9iMIZ(o6ICS_2hM_7q\_?NNo$E^
q8kOFN/tDZ4;VItZrSIJ``=/o!n?nFBIBpcd+"nt8(+6kA#Yskf?LEuH=S[@+#PXKnO06*E8
i^qPrhE!XN]At%NV=j&urPpe`4h.>9A8:iTs4FIF3CJ(W).<fueX?C8=&eDp(X=WrHtWZtUe[
=H_[%Ef+X'2KUgWdll3W(8Uc#o8WQYMh6:?br]Aljn^ndEE+p.TZ1s/Df9<?pK)5Yk$G/,a7l
Nfqg>[lgHqY\4"9UOd^5b.3IXp[4UM+V1QJoE+7EqEQe1l2NT=5E^0X?LOf#J1m.OqS-H?r7
bVXeb8q:"32i#MS5dGli(6#7_c5nIieOR,LkmK8os_M*ritZjLZl85#F@K^Fo7\pt.U.o-!6
sXX->^jENTK22[eQjT^5niY:P=LK-oSc!l-`P0u"rd,7*CYJ8Af]Aa+A"@^Bpja)q\0%<>KIT
1m*48DWYN,ab>j=Tp-G`d'iR<!sZLHN2jDle@.lRY_a6[<uEf0t@Ar0n?q37/=2ob4IVVFME
LN\(MnO"LN8+JX&8h[h72(S)L\EbBld`(E7'He@HFXR:.LX.1R.HRrsop,a"Yq#:C('WM_*'
q:6fal3AQF,nD2h`T)N<P0l::`0u*']Aku&R\kWbY:%R\+>cl4P)k!d)33LS`PiB$qCW'*S\G
V!;9fDp;psf*O^Q0>8.tp6-mD?(jYDV<i=R#g#O63UE^b-#I!AZR@3!>3_VT[THjKZlADFkP
g&t^S"csr*jY=1Tn:'-r,TS54DR"*#'rNTF9hS3OiD9I-^+u#`?'u\+C(5-o<US/b-TJ,bs5
+1$>@KKVPZ6M%CIF>-EBmI-]AZ7Se?Tpp^dYBqGPTVL,eIb&nng\Th]A.i41=ef!KW]A"hZ2TA'
UL(jd;;#]AnQKM]AFpq>eY"DcS+f@V<3p+lbI#snJllbr%JiTmPE,VJAR[iQnO]A]A0%rgSk;CZ3
!sE3:6ZAW:G*903:J6[n+,G11M&EdRV]ACd>SJG7YU^O[-<f975Yd"D$A@7NKf$5r#Pg149+o
6SR-AcD32s\!J1VD94?DQXn+>%0MZue'p;)]A7ikZ?2!P4GOR<r<7`7P88o,@qLNS<,S&`Yp/
W#kAl=c9)-Os*hR*0FL]A'ri$bH%2Y6ak^#a-rmep"]A+4\OQAkq?D1YFIc"=<S[:Jk$%k2UXl
(!NVP#IibM<un&ZhlS;Js3mgm9:4]AjC6n]AL?X>:JlEWe::VWXX"-H+>8NjQmH"c'=#Vm`?fV
p<(CJ2#Us2dMIWO7tQRJ+46AcQk1)?H3M<u1XI=kl*9dksd$_sJbRf`t"9c(oJMI,s3D$Sm?
XPQ[Hf(hg%fgu@J"HnG2NFT@:ou*&konea2$hI[facGC:*hT6IO/=#SVN2OM'_3&gjP&lC^%
q_;:r#WV]A3RlbLl*$o2H3Fj7j9la'g(Xk"J),na`o@X>UhD>PV43N_auUJf")q<[+,J/f"uo
#P=<G^a)Og*nh$;XlWoJ,OWsPlo%XDGC=e1YBgn#]A,($69/<Yl*-4l&JM;>&RhsG/I3JV;1p
DFTY2m`8u=[F(Ynm9m_TO`use]A52>&k91N'<rDiW0VmnZ$`^16'="%)#5MuD>eaL)e(E&?=f
\P'%aCk$jV*g5&`^TnO.^d?)rEqppGqCB@:lKLog\^1d9dFJ.<951#a+qTtFa5bMo_@=MVI:
N&1UYcM,JEC?WcU2T]Ak6i0`:(fRQEfJdW;&>69%o%9D=i>b0CK-'>C'l>iY!Cn4G``@b!.&O
2YSOQnRC/"SEHREX(%A'7U.mr>Y;-QE[&"oS\^UaNW`Od7g(D(Z]AV0@omab;/CP#6ku@YUi5
17;&r8,S@+=Gp3^)2'n2!HZkq*+7<2i4.s+lC^r&-dm(o:3gfk#O7mI_+rPX'6VPuj1(N-L3
6>1FEZDpf+%!!5MZf1tN-$UQOXqFJ@gi7!(CmD&_.(6QLYG(G@bNWG"ne,A%dG-M)WR(CI.F
J`)O?4JN,/M.<W,8jPu=2rk-8::l>idpFM`p9a)O"BC1cU2!Bra&LgI:P&>X8Li8Hl+6*l+Q
0H?1%PK.`sN=Yt4gBWZ%O(k*_(3d[q;Z+qXW]AM3Rj980Egi8:#Mdp\S6Vj8K2i0Gn&`ROo'%
.*1i=m=R*[te)MH&Z[.Oo*V.msAjI$?"40`@#r]A)OK!ottMBil[#F9(jeM,FK+_GU$\TQoa1
507^Lsb:bB^%B-BuOJMDqe`#ekWihSH-noo3Q.sl8WtLV%JI+M`Nko-8;Jfb^0CMuU_qX]A0`
-\Rs*_K%p\oZ5g[H>>P[^ZsJ9kf<_DL8&<$o87W;t9#p-]A&'!LVYAQ_nLMoT*_;7dBBH:&>[
%gep-6Oofrg]A0.+8YIDeC'na/^>MPf2(3R8?`^*qXt!Ps8+%ee-@V$G)n"#Nq1H+tjnjhk5R
gaZjO/m]AB-!_SBhKfG+fVFNHfh_`X>SistWdWMqC((q0*/n8KU!FkmEbRM.WF9X"[RG6j.L.
$`&!Z-lN["P'\9Ag,4go0imf[d8q;6PfET:VT/X6l]A+ImuBQI&@Ake``7%jKb9Ug>(peZ%OP
M$8n+I`Iim,d4bc7*/*f&3_c?"c;,kRe*S%0JA-F*6aJ9DX1<r1qMqL%9Z/o)3&ME.kS4TBZ
6Ek*m\?8'Ht_Lbf=tn*qaV,#+aYT&.s;[6IJ(!r6l[Nf`t;JZ'VG<eO-.J]APaAlXJbRtX?`n
PP2;]A/n?Q)"Rn^&;ZV^5bY*BD`D-BniT\0Ph!^ZffTbseQAdJWORP`8F4VJjULf53rB]Ak[)6
l)k$7gI4%Y3>o.l^18$>f1amh-f)k(Y(;s^fo"9ZVi%l_$[e[88]A#<gipd_:LLfs\?GQhM*,
SWt.1SlAClN%"0DkYWI.*XI7Olb?nSF@J\<l3,r;,@naV>WgeqA"1!$m>kEbn-uc@e-16aEk
388RZ70p=S.5*L#?N.^n\g<6msgOI#)n0*^aib+/q_G*hHXE!F`6_\7W<7iAe!#g+\U5@N+$
-^uh$;)EQpdEc/[&P/6=5X0<fp,+88Vn-CSNJQ`30o5RCWUB]Al\uUKGWYJF^#[i,NTlW5YGH
dD.DoJk5Aun1aRu92A#o@`OP#P9&_$#*lPU]AM6fJ#VY]A(-)-t_5294.[TZJiu'P</k;oNK5]A
oLsGpSa21Z\Vqo7qJO$i-=J=HoRuI*H_\%XV0"p'DnYIl_V1YlL5/lDL-j1qm/61;P`r*f`N
b*+OKbZM#K>Jp7EShD[EX"lUhWXaF0ZoE_'I7/LQsmYFFPu%13[nQ4$,]Aa!u\efKaQnGjMbc
mh5s>5fiAuS04c3?V:oL_2tDfgIDc2R'M,%aY''kb]A/"KGE=N\!-jHL79#+H@TT5@78h/q9#
F!3QN66<lUnDD#q/Y,%VKYSaYg&[KOmR4HQRjs&"fSH(N#?V:<Knh_4n/F\,ZI6&RhUpGi!j
37'i,>V2C5e5\KGX)pMqQ5%h`e5F3Na3YMJQ.'1C@,R?LRQ,9MB8n:fr>IuV_\<6A"s%sshM
2jqE)#A`Vt2DE]AH;-#ohZ!=.MShX<2fX#lq*cp8T;Ki;60;bBlHMaj-d+]A(^fgQ(&QKI3Hi5
8@gLRAI_J7:=`T)?aj';^>m,Zjo5TNm;0p2[WbZs?Y$[Y]ASeA&4ldk?Vu-&<!6d6:Dna%3@J
UeN/-!+j3,Fn5"d*A/$/H'KXmC`j;8$5"<r-["geNa-Qa!f\4E4DuBT]Ahso"j/P0LV\k`(OS
tOknn.H3_-D$j/*o;MCYg\TpGK#4s@_l%7O;k,]Ae_+gcZgSdF&JJpB)QX#Q8_O4S'a;>S^n*
g.!QB%rJg)3m^WJe<H(_QdT[Ucr99Sru%Uq=5EN[<L4qO]A>Uc<JG:SA@2mZ$4e7.B:!?0MAo
diGF<m:aL58l3EFh+s9"\p^/8+P37DO18Ap)BEtkqJ'er^Z?rtQDiM'Xg&0Xlu(@j.7f(i9,
6d&;a/X!hOsnY2<R7`Cgs8p03dFOUgUE&.F2\^XK&b%^h:3*lZ-hrF*T9+F#0_H=,\bIdNPh
]A*[KEEMU/._'2A;BFFD=OO&Q"qI&X6']AGVH%ZE#!&GV\&=fUB2)W7)D$h6t`PPc_u7eet)GY
s3nFG^b$587srq!IaXaM&>X+]AKBGOrVEbsL\tiqIAc=+94S'De,FLo!M'[,.qc(G[!0h>O@8
E>V0_/[]AD'LJQadCWZpIH<Xs,oBA=u_.qoj16->oD"a<LROq`@b5-R19l$`Gs'nu)"eLaLt#
oemNn^`04HlUW01&&$'db&^*@J<$t6C[c6c$)Cb2Z$`49?@tK;_4@ON!tgb,&,%PW(BZ3TUU
<jB`X95`(<$iU;J6665>q1/(+tp"X>V!O'dP;PM/&TTFK7Da?pm]AL%YhVj>'eo5O-d<gdoo3
`GQdgJ6jU=oRm,_r-]AqiJWl::bE6PHZMZrs1W`5B;<\+VCj)@RuJhln[2"s_uN6/rJqLF/G^
Ee=gOpunaSLY>:X"5X_%GcqledAr^\ZtL(;tN\:5no;or>=XD9fh!<'>IYAEC*b$^B?nQ)$1
]A5)frJCAu<1-RI<QW%gQjT>adE'r3)WP6;3,-)Ng,t3f/,!PoTI3jsHaGU"O@\kdj)',^0,M
MjMU4$jjEh3$DbT]A;<i+%F3B@$Lg1&51I$0O,MpPl@'G,du!U)O/C6;MI4,nO5YY#@r$c*A5
:%=e?f:s7e$#-@g0TGlbgA^0=8':%:c*^A`GBa@a@l-k;u-$1Z#scG>#c-&9`90c02m=X"XX
ZGSeJfGA/9&iT6PjUhC-=`+J&<+5IkMq7H-H5=Nh5FbX(t2&<F4P\%?kSB@ue2jHk?L<b[(G
kCL#$Wt/pG"`<kp4M.<hC:bU5?!l``;AM^,keuAlKPbLCf8(:Y+I0mA[G^c[/%B5h;7A6cEq
*S=2J6saNX7X.dSl(4%E/S0,,dp9cEDnH7Vnl=R7Z@_<EC+$j<slYM4J4ja/)-=r]Ai-)=!Y3
18#d.%5r9Sr0M?P3ML*,H)fBAaoGLO6MqH#Af#R5%ks<Ql90e03&P>_qXR2XY\O2bG^JXN<J
OtbaHQp?=jZ'`g1,@f+(A[um?i)X/7dVacQV0dm?^b_d%ealG[*WS^Q5kd)?ufD.r;hU\$fR
_lm"#9M4YXdc=NA$1s&s=U^M(fd54EhJ:H@+Ij'mZRS:_6VR8%h._Z8t$PRAu:X_m@5tIdMO
_=g"\RV4N&pDFeM);b<B.+9nloLP[R@!k$D81IT[b71+e-E3QEaMJ':/AaX5IBq;7FtDX?9C
Ym@o0A3j<;4-@dma^l>1EhV6CJu\D:!VXo\Z8X+`.YLX#+c_PqNUVBL%>S;d)e8^MfGpA,@j
_/s>Ao\>TL"_^b;#e6ItO-I3mR9Yfu[MFleH?d7f<s#N15rVuh9PHcUiqhat#_ZeA;?tYL1c
%fm]A#;!K6;iXX):U.<aZ+>%bS''c);CWeTA*eNc^d5-,H(;D/!]AjOG2dcRn\(KISR]A2l3EEk
)j/m6s<XG"mA4J]Af.s#XfoZs"q5\ambZcU)O8.lN6j!\UO9"o$c2\@Bs9WseM.u^4s.H,6Pk
qp16##W("\-0cQ-"c9%+c"FlG-Bp.e<hnZ/nt5`q1!R1NA.#epIB-c[*FUZNJLS'O)4KEfJM
UF5CQLL;O:3Y1\UHeNJ_7NM3$\s.icMq3/*[a0j%np.(bV"17_SpR3h;pp6r0C/RGk?<&H(W
]A"."l"`>j.I;#QeNZ+if2q%B#"n1i:BoP49<K?@Zd&.9*,\uEQi[kC=94tK":D^K:oUES8oX
;:V_gaDqQ.ItPST.da5NB>=.U]A<59d*RlFS)*;!iN0<00uZG[6]A<tegEk]AhgAqbh"05PY$#:
8fgo75gtV[-`.7%@GXSliT3Ykn$G#F#a2[8%So/'4o`n9`$`.P9-9D.ZoP'BTD74"\!c&+O;
?Z,U8*P8N@so><pVZV=mZo06YMl<9P]A&h7!`[2KF/.J6$Rj5LpX;k.c%EeI_knD`TN20T0Q>
+"0M+sEpPN<35B+\??VEXY7>Yp%]A[;Z@S59Z3]AkD6D3'p[g:^o_(d0KbgQTO8_p39*2"W>.h
@@7s0)lLg/Z"bi%n%LE#PR5e+C<3u(rYYlXPWtSOC:D3#.n5"sOhrs#`ADQM/U=.3r%L3`&_
YT[h"QKY6dO4>nkE;0hb(o5`K=GT-h.+aGkAj\BD<5.D"9=2qo)h$S*'9H."tE@PLW5`eP6f
LU'.<MW9.T%*3N^ND-6WSd`%@!oodc(]A)"Dj&S2e30fbe\WpKe)IQC:94s0=VbB#/aYrCe!1
:qs=5p#-PmS.#AlJajl0Rc*KISSq>h]A:cZ,q:660<Y=(_sC7a,DRW>Cbr;j)8<_Y:rRiRK@>
RLfD=KC:%^Lhr>su_ht!rD.RC@$Qd,P5nbD<DdN^^KD9hJ3*YcQgXU-]AW1IXU^2q.an8_.&1
X.g8"8j=<IV9`@dfPSIkA:kM5JD;**jZRh/@*,L/5',B_JQ'qVD\,HO(=[gVr">=pW&XglrA
/e17fK<OqOdYQB5FeHYEDf[+(Bns6=_&Tc:%KJ&3#VS3"Io(h,%s[\mKnNJePt34o'?[)KE;
cj[UmSB^sJ6LR_T'Fo%Hto)Lp*m[6>Z`1qgScrNiQr:^MiI&'fpLW[SY+Q'U!8enh\/IDo]AQ
LH)-Yb2mRM+D3SMVQcPIA.#7GkME<Y`8@E$Lm^VWi2J7OLT\9-<nkH,XM/ZEkO'2PFSj_;AF
[,YIHCr?\b7h<GUQN'Z,M&)8@ji-liBbA;8![5kUZHCIJIndU4KDb_c\F1uC8;Rt[/m,qu&8
WVf4u,s!o(23]A[L$1Dnqne2eQos0BcZHlDB>"l@7doVTtYp@eH2J280.d391'g+^7ha+ZJ8S
9,YJFkkAD@W?N_s8\oI!FquCO/qa/+l?D']A&$P55!EWFG(eD.QJrLNs8%.bQ+\-&C=uIa/V,
CHG/'I!^H+-KKE]Ah?C>T\"C<29nP,kJV-5UI=.pYCKgmasV%&f[bW&:-,_1?ac`XjiV+sQ/;
Ri6:BWn^(e0LP_IgIfoQYfb?8PC0piuXtG6L><lbu)F'LFm%'+RSOs<FY)3IogWQPnh>!oYV
u&ZA42c)Ck'I16pCFAE&':c'q/cEkN,b^8_\^C7CVjG4kNP^+pK:^9k.F7[8c0O2hmBMSI7q
?SX1BSDL\,(Im/H"Q_bZpm=Cn$Wbi;)>nhE"XEOF6Q>1@'pt<,3"=sEm<crYh'\'`P=KpkgP
&\M[@QIDeulg=.=Qo`LA43"?Gbi+!5jHgD>5i/]A5j,/eRMS;f#&bq[A[5,#YKA8VFj;1!@ff
K[(n$;FS>&WrfGEe-&o.a&.)Hsb^q7S_<C:JdcH'Rs1'%QCeD1p"N51aeh?p+d&]A;M`]ABi5W
1ik;8m?JSaPu?oZrLaqe_G)&j!n%b$8\l%&k[%S"%-_M7V_T(E9n9#lXQb$)3e)4MkpoO,u`
c:lE$p?`]A-`,Y76`*2f^2lF?l##Yp!osX1_N?E<?$"&/\,eTB;GUCCfZOh5XR/b'S9KY)[;#
#ecY\oS*+/JK)JrkWn@DCV!@nf#V8ZL)ELjk,hT7!Z'>i)Qe`D6tI=jI=41(#pJ,;\b;N>=!
R5bpZnkNU<4db1jAZhl:,J%Ymt`$.$SAq%m,C_^?KVnaC$-cgM6.VA'#B8&M$RE,>+KFn:b,
`RFT\.`i"Q5rZ`fW`&)[oW8T[:LKYA0Y992[53-8Yp`.EWZF[iVi/^HqkF-+Kos7OtCV_U]AB
0hc:+WBmMJTF3Qk\0,8W:kE!\?8\UfFqN:j*(X'I&bIeKA@D65r/5]ATo1Y3okJB1N<ZOYD1X
(HF`n0rR1D-ho>AXJX-L0@dP=j%!9%%Qg]Aik2fS>`ZCBADa]Ab>\e`_kX0<EJ`,$M9pHabDGL
eVas?'hoHh5Bq!NRr70K-+G_"k]AmL3ZV?%)_[*&WoB;L30;&DlhQ5rACRXGjIm5PZ>a>!Mmc
_k!e4HL[dUT8F[<%M+jPI&cp0#qDqINe]AT]A/c&r"AD;K`2*SLb\ZkP(I#oq(b[6W=Eg'4,dl
b]AlAbT*k/MWYZXBNrG3oXZ]A`,(i"tYibW+o@3I*6+X#BpG&SG.eoolMs`@VXWJOq\sqLFf/p
G&:m)c1C@"Z3Q!L@gh&nnPEsB^W"AF399?0&Jjl]Aub)ajldY<2GBZ1$^f.bW!ob[C87t&<lN
!DI!ur=n6LKLc6cf(kq2e/>Ef?Na"GG0XCkX86n?)(-)e\Y`LM4[5[Tl.:;=g'[!o,H:7?,-
Buq]AC)\+DqT'd,\N#U[j^=3X%E;4;/2d*lF^(T,1eQ.e[j390jD/45bNoI^-9[0Gt!)E.bgO
?;+g_l]A>a=!hob]AmST,@DAAr6tV@PIl1t4lBIPfaQ>#eZWL`Qi7i_JVBp5AkM7;k<mr2OTI6
o/EcKK!WPh:X$m]Af%!Hi5F)Nq+l)[PFXfJO,M=9`KCQ`/U)1lXDQ's%sF_1Yl.jK)6mD/e<W
m&ebdW1\Lh7!K<U^SWSMhg=><-cMk`7VX9.f@T2P45G=!U4lU<;[/ihc%RFIM#ECTbZMXA+9
W*/-;B[0P[6Z,8C#CC;Zf/oc.YWRa-F]ATs0g`eoG09O7l\B#Pj9M'1%8FN[nrIX3#_aNp+i9
'gIjYRJhZ(M!%n&gXuFPS%(?.Vr=bR;PKlMc#>JR5ited:Zf-#OPB>gE2K=fk-HXtT;$*A!Q
C/h4f>nN?iO5SBl0Su)/jG9--S=?:#si'D;A90I_`+W$dN#o$:/HWW2(.M/"3V;dka3cN%Ue
Flq[MZXAElh:bU('UHOF)3mKD%q6?,f)cmH^N<FR85]A9\K#LBt+FFld4<uHG+!EQP?a:cr&r
!iVHETkFf:7X6mh^u+@9uuhq(*d3*j&,D.:jsQc_$D:7dTh+nOM+A(=(Dqi$=Zp/;cQKsYGZ
QM-jL-q_!Gj5(+od4,[XuHi,I.rcmidXhe15djL,#Q/kl@h"AGq8_:O/0lHo&'T(0md$\s5G
*-@Y'SC.f+CcRIcC9&AT89FX\e54KEA,"*2KoT>1cY7Dk%UdTXG5jZrG*-WtbX5,H6@*Dc#+
`_2T:c&724d"&BA0G)>^63om2C!>?sR`P8OT:qB>f^5L(S4e27AiqW)SIah]ATOcOQ('ENuI9
JIL+Pu[p68[.-HW->Vk,`.i.Iuo_"G\W'*$gI/>a`Z-r01mkg'QrT$g%)\olSRgN'bmHYS;C
=ZJdo1/nba&M[%6X.7+YU"aIPZpl-f-u$\jOQAJQ;lA'>r5:ND;:u_bgsbhKXZ6EcC[,s#b>
50TWTY&kLP)d"KB:L33'Ge"]Aene&krCNS^$0/drrDKgaMZCXt!':,D4=]Ag.%(AXr>AX<-SoH
T7(L-Z9r==%>_2flR=lNI*<ik1gXGNfXO.+M8.V3G$lbSYJ?'gU&'DLa#:;[`.XT$-TRGcUJ
2\GHP7CV07d^2>WP):Mo`o;[9G5%NRc^g"i\@r:[Z*W/2R#+j_Q,QhfGc7.W6)hdi(.3Cj&m
R-j0DT*H>5U@+$6RdeYKdq`SCE*:;H1:Q_j9(Sk^eO)`G-P!%=2i%%e2F.^RGmu;8q9t8?IT
$.ueI5;E]AS.Ns.E8IBSFBa$6;*d%9O_9!%i<=5@SlNYm/Zc(kBlrZh;#r6\L[t)+n@Z6Sf!M
^68,;u+5.(//EK[%^WLs'JE(d*R/bQ*;pcJ,1UY^c]A<)3iGS]AWGB44-*;m/^<u'eE?2$?sat
M_3nP,S[GaJ#B7#h#;;If7\X#%I32hO3"o7l&h9\f9u%hKf%O>BsOuAg$[QT-mP=2&!mocGL
8;ZLi`*#gDg)H#<^\2Vd3!'TQ,o8+[EGO:ugO3Z&7OU?B%OI)E#u!]Al'M*$6G7gn"S)Y[;sZ
;98-2l/C;e_er%@Q!Ko$7s!TpeJkA1hK?s&)ZBj!F&Xf28:qa?=m=J=78W,Z'6tK*hJLYSbA
*l'KfAbp!=p=s"E!iT(.s:k8@iDX#749d"Yc?m.--8<K`(Q9e<@K>NS:A.eU8MnFdc":@WT&
7sCCu(s3sKBaTUs0VK.,fGi]A@L^Y5IZgo@h$fmrN5a[IhV&1_0<5W<?"[PFT;R)L0(-*3l<)
!b"^TSeC2tlCZnXKXuWQ!#8<XGiZc5`lL+*MEDnr7Vb_RWk2iNG;L7TB+b&"UoGW4r1Jr/ii
>'\hra[NmXV874Q-Bua+:!-<4oWNX.t<&NaJksVc;h>j5Mc"f#aUZ\u/-"%JM7M`0In.Yf/j
$k+$*%Uj(6^'fZ*'MML(8f@p5cYEgD9pNj`gs#6!<`MN=;Amqj1KR]A6FgOH4?M;.kSdQ@Efl
1:!?%G7)1UAcYbV<M@gE]A=Golj6t!mjZcQ3F8.cN<N#UksFbIbW60MW(UTGBQG\AJHc4-\_N
L6XRYI2J?@\DDRf=]AVa]A*s5Z@M38\I8VK'H!HR,E-@enj7rW"Q<rg^n]A"mlt`\>Q7**qo)!A
mm0P0p`J81<)Rc7E:9atbJ1KfUnmY[D`dFu]AE@W<47E`m"7O#en.*)5n%_$S%_MoD"j9GlX`
UuV?`hG>9M^-*]A)DTHg$Nri8SQNE21Uk<ak72#4FN%@!I)="E!P/!GJG5<#M]A?(^`@A!g]A;W
7*H28=!I)="E!Q=0K=gU>2Oq"0\+k,njC%;"*ddVGXoAA~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="46"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="46"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="263"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="434" width="375" height="263"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="2"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="95"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="276" width="375" height="95"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="2"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="78"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="198" width="375" height="78"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c_c_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="63"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="371" width="375" height="63"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="3"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="71" width="375" height="47"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="2.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="3"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="80"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="118" width="375" height="80"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="KJSM01_c_c"/>
<Widget widgetName="DATA1_c"/>
<Widget widgetName="KJSM01_c_c_c"/>
<Widget widgetName="DATA1_c_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[370389,304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2<>'总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="0" cs="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" cs="9" s="2">
<O>
<![CDATA[客户数-总客户数明细数据查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" cs="7" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" cs="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m90HrdV%0UlY14iEk@m11Nd'm4*F\?Au0*+]A;tU)=u0#SZo#0Y+hn!aU,?#<2I/++$`-m<"G
gCt'EftbA>YTjQjAqD-somU'EW/gch@"dJ&(ZnoAU#5ofhj1F.IAXk?R2?MpI+G_7MpTr7o]A
9_;XFfHZoDHi33p"q"aC]ARtUK[i/**(B%Yt2h]AC&d-q-]AD@qEu3`t26Nh^3afOWG!n39QfrI
)U)Bi?RXHn#HrSrp3-C_jPQuFiUd0XoPQU3q\CYY<OrRe8la8d&[cf9"Bm3dD0UVCcNYs.<5
WHVY'nue7iL&8_NIC4\Kn4VU?XT6`7>"Pg^Clof7QdPP#ajV>`9D7A_<-&dVgKD(f<r11cHA
kTcR1Q#ijFi\KFYH2#:OhlRjSZ\bKu<'e>6HcL(#-Z8qL\^c5Agg>\/5)otsj6U4gD=E-a/.
_N:W<BjTjF;uUi#e6g!<9OP2KQ2%:M&Dr2K^+?T(cQ$"%Thk0CQ%h\QCrnbD%W%$iJi\*U`G
unGCWec+Dk.ipDusnhiP-s'Bc[@9&mZO>HL7]A;0XNZT't=[*0E%CiN/'cX27`0[mL"quVdWI
K&Q\b*f?\HFA8:?K?s3.Lf7ah@S,h-El\\SL[#TXGL<Y1F'sPA7TMr0(a+)$<>9Pl^rUUCBD
JMSgTA\]A#AQKC'06fQ!6o:r6u9M<+E\E&*XN5IW\fl^r_Y1[4(K,",hbBKRiEVlFO)SG-GuO
-Hh0.nf&C-gg*o/Pnj]ACfDHZ%YcB/>1;sUb^kfBU!o-+\4Ie4bjbI<PG53gjJD)J/mnrY`oI
Y,f01.6#M9(m,#V^OP=#Z&+CNL8u?bsU.Zm%uV1o\l-Th#c.OBZ>>$F3rB>XU^Ti\kMjnpZT
j/JMo7%*C*dUB`>HboofaZB;%4L@b=I\JM'Do"/EZK54NHLrR7l-J+ueM>@eZg:\8[JSU(nr
ohp^*SVT2o/&aZZEBXF-)pWrkZ68$-#["iaeifH`Dk?RlUeDJKe#@-GcA%86BJYSPWh[:jn?
M[(FWlnY14e[Ls0V@VDNP'nch=_r*@p%bfjmLE-u)s%O;rZ6\2,tI/".CIfA8"Y9C>oLo+cU
b\@/_>HLa<FA"02L,tF.&>liEB<rs1\]A+nj@l\cakq(;o@(nF&k_$tD<@8$WU7G2q9d@:q+&
[hiXqUA2`Dr7WgDYddbJT]A&OEJBPKKWO*9*5K2Ph<FAcoT5q[:RcVlY(![/2'EhPV%rhah8H
2mRC@:a?ZgDOW<BPU;E:um0c;n$`7eUd1*tZB43>'8k<NgmP<L'q#jI5!iPc69]A`tUNOL`Q+
7AV:0M_[6dk%!M@urjk@ql\["tqY:"*O$j[_?jY[=4B<.'_,dd4ZT39ujPN<@KfOl(R_CS`.
6/n3\GDX"OSXJZYN%&O/#*.q6Xb#CmS9%ap.'FRpiaJ[:NL2N;PdC#D821FO/BU_oqd&-%7G
=Q9"L-?.,iRli6^$E!G2CGO&:pP);m:PMO%$fqE,Rr(78i]A;`a4WD_U]A^Bl2n4>"-J>L4Ll*
Op$7V$u4>]AA]Aa.5I0[8i,@Kc@7Ujf0tBK+:!WQLJPAe7I&hk<qc09g1CFJL-6_;Zme(nQFT%
U^-)+.2MDB'8epks,=a05do@@[<ujq<MJ)tJQb1jgI9Gd)FR6LFEC?V'8#O\s:G5j*eIOJc/
\tuhh;030EL;;,bFcDgZMB<;F$1O^*5L0A$i'_iDT7tU"NiQ#$mdufk&*0E,!C[E3Y=BC*hu
tE'I.9PpVfr?hJFWo9?p#SSnIcn#dO32&BW$iW=\*OZt4dM]A>J+/'M-Hj'G)hUoiN!EH6t">
S\kh$Z'7tr[s2qhY,5%T@b[)+!_$-uq]Aa&GS+)ni=eUqD[$hj3"u4g*BoOdU5KR-T$?G_u1A
KcE21a2&hcnu7e0:^p6H;;B3rq,3/d9M7-tKl.?D$odc3lXWaQGOYQ*R$omMmL.MBhZ5'm0_
`e]AdSB:[M2fjTTlRHD+gdn-RCALe!O-I8A_E'n=QN*4c"j]AT2#1ZNAso_c6Kj88+eE1?p0B'
9Fq(q!+!eI'F7OLj(tTZa`Wgad[Bl&mM#f-5(/'2%el@2Un,6h70TRGT?PsPTG,u:3,aCHVs
PHmQr!^9od$QiX.!qb.!;[>c.#AJVTFWaFu0u>_\@p8UPIB4E'>Z&j;eZ92oHPBF1VO2"a;L
#,`&n:W2O:0YC[i'XLC?R!L:OBh@)&,T^u]AZ>*e`cYZa[UiUmVl@JKcl/_YR@LPdm#Aeb_OU
)_@)Q!J7V&pJQ\"3,:Wf]A&9[G*5+\1hgV5J)6s;r5b4SBRj6F[ocIHcj%Y1[?`gYKo2+q3/R
N^dYuUg=E5U!kgVV'WnDghQ4U0[A9^L^Rj8."1CiBDTchlG34,%GX=I%[lAi63:`YVXq$6Eb
gLI7fY6[jELY(41c<=>D<TTCk=l-l:HkVLZ`Y>5'<kqfh5=;[K;.nh"Y*V:/>FZ/T!rn#Q+/
is[7>L`2nd\^/d97-7$PW)0pj5sd3eGd^ORQ?F(]AJ_NX=/Pl/Zbj=E]A7Q=&+-!62/r%R.o6p
(s#P]AWAR/bJ-QP'Lna"b[IE-8obt*7^Lq>3W*ucq3_!6e/Rq,-'UK'ss1[4AHYgZ1WQi'i'R
f=#_i^7(=EIqK^pP9o<!844qKjg4$aQ?$9iO+Oj2fihI$701<'(#s[8diO=,@m6UNS=_WoHY
,9EkjuFYJMQ;I.NqjF^kS(`bVZT+k/2kZT-U8%q$B#W)F0NtE5AL6K2SoinRi>E*YYJGMF:U
8l2tN18NElpL,Lf8`YZlCectDW`]A7(:&^gDGmqmG<cD+QX2V:iR1hR-E%+^=`PQDNNC,=lhc
[Pf@&p4hO7!\CkWd@B]AJ!ugs35T'D_o['5ZW>-=)[td5\A-Y>V?ccN#DIGE2Q%Gbj7p-^>ie
;ZB3XO.Up[[YD_)]A/*n0RH0gs]A!,40Kunh8F?HaZ?9iu3)UFh`9paIm+$&*:R'!m`R+HAf!>
7MB\&7_a1)^TrNl;5kIR2O-``A?sB7'Rg`+GSWp)XT=0_)RF8MXZk_YB'Y@86`%i[\B;QYNe
9`mA%)*Ar_"8%su&f,N^9"Bj&R0kb6[q/Y<"gXsnd28dtB*deYkm80g+KMhJglENPb"+k>TF
/(VOpb)Q^]AQ"b<.`kq4i.^_%r]AADA/hQP5fjd2;^(#5V@Nj[X1fKdV#[8g-A:5T$.m(aJ53p
M#+QQSMA8gmF%qu>T?Q"%W'7eVD^3Yl3"J/h23p<8CE!+`*`9E!CKX'pAbsWZ[[p5CjgbPl%
"#!EO4VQ=g7LoU\nr9[sH\(FBAatGmY%huK7ck>K+OBgpcMoU?2d4>d"tb!tO-ci$5<eS;jl
Y=KX/p/sMB.ZlTFrinBks]AoaRPo'HrX_ob!qu:'XQb%`=M"<p@9kK$p7"^8T+Ip%)%G#9m8=
M=lLAJp>IKZ>NjGHSg;_6blTRg3UeL0LsXg9R#"<EMAIL>%FS:e]AkV]AX>Gt1dTYaJf_VIOR
^0YM[['Vu/<h.q8L/"/VUbZNrcP(;4KeNVh81g#11_YBj\!jNI>g0t;RmUG^cQ2)&JeOmXO\
o'Z%j[(&eER;R/&J86]ABXKDmEb-2N!s+:]AsI0XRcg2`&,c^O[.0pqu&?tZ1\%&3HnfSWDrM'
PtZQuTML$MH9$.31@AE)PG$jkg^>E[$di[<Ua"XX!9`;1q3=8TLAX5"8C=U>Z,B^Dhk%F&]A"
*iO6D5e)!FQ<J@r[L0YP]AppFm8Lk8=J>V)Me+0.8^t>;p>&g?7*s\Fe"$1?+kpWDMW2RN$u6
X-Fp!X`MR"lPUt]A*6B&okBg1/!'1&dnp4_X%9jq]Ah59?(%',oZkXW&RKc*_:X[d<2ng$S^Ik
2\5F(^ra3al^ZKFHZO6ALSk7e"3!E^k:k$Yc]A&'#3ic,ds9kk6HEUTs)8ePI.R9<!2fAq@EW
a;o.l2'm7'=o4M?oSpSR>g-TSNf@)Y;,'TcTjb_;DY>7ED/JA$:G+]AKOU=aKNJY5:Y(N^uHS
K:C.#GmRL'N^Mqs@@f0;F]AQ;D\F8Uub(fVt5-$Uk0jH26_Qk(O)7/^%DWhWXZQLS=:uGN.<M
OW>.PI"KE*RecHWalT4\%Y5.b@30Z8MurR2oZ*Z9p/iOQb>7+1NXES3/H34Xam@ZM$7d>1d;
)h>>%nD5MWqX[O`k.=C9g]AC2%;b[gs]A%*52n`SiM0YYAG,I\]A>+RbsKu)A0Rq&$'WlW"AW3=
-#VhLXJ.30/Kg">br[0V+.%sMKQW4JaE.#.k'9>Jk(/QM5TF9pXTc^V)%Hd;P.c>4jjW3HoO
PkgjXo+\44VSH8#P8H&m1V1Kj(l`9,E-h.VsUgd&S5m@'c.$Me0+<=dYn>IuT<`3'PTef<tQ
>6%W4+Jq'+qso^[Nl9c1kfiPc9,*1JG5rX2?QGcpC%-b]AV82_T,27FXJMT7$\[3X`r["S`bM
:^.39e?m\c*lRQ%$7MbSJ7-,U>Aj@XDJf*LjA2=UeY:@15JO78t+H5^:_]AA.96,*Gbj$!S,E
\>=<u%>"V1$g%BG\)C*3^;iD,T@")5P@dUT=@Y^1I_O1#m/l>V@[3Qi]An5P0%o]Alh$)%aYj(
&JKYHG>+^`=p2*B#[*U88tCTQZP8dJrb5tHJAj)W]A9nqU)U^[T"kRi5!'>@k:4GA'N$Yca<r
K)9(c\drn`iI_Kf2]AFa+rSM:eV]A9NTp$<+saM=h"dLP-4Tcjh0N?@nr.FqtP^$$6a?YQ>AEg
ra#spY)*6Q*#U;VB^7-CH0!qW2:0?)OS/IlT3MjMn=YCno8A$XMSiT>h!_MJh`N$6IAjQQAh
:'G(;_eqSs"MmlHcr.3(57]AW9Uaa%/KCpq%`(Ss29XX;:'8RH)mt&e7,T4DHJ`e/Z%G@.W'h
1Nr(F161NSM&Y/YIfp-[RA5k8ojK`S;7g7]Aph$MT5g$"P@2m$36@.SLH+'9Eso$"F2d\tpcd
QI(jOrp=kT[G7V\fYt&/4!Ur=")J'I[TbALn'D5T%TRLPeIX$a/(&_r1h45'/jP<hNUnAJ*D
%UoX1m^-7]A!B\#qhU=7pW7ns5Vde3WIeY05&aIU>OY/MC3Pa<d2@LE%g;R%/:02^%>CJN[l1
O$#[aE[\C&#M*^]A)WcjWCBD,K0POZi5Pp'bJ_[?PriQHX8!gWl3=,ZJ*C&g*nY/%PS+_R,`S
^&I]Ae'X&43uZT*SR2-g7]AP@TcMj\*SR2-g7]AP@TcMj\*WDZsX/+8:Di<O@UNP(7jnjS;s&jc
\pbKeHo\$_B3#kt2^)[gYE3Gh>PBPALg.=A?-gd_#[$,[]A:>/9$Bd`;DSc&LWmL!e(J1gK/a
B0/Eh$)Y<s0&1^rYk~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="348" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1193800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,7950200,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb_right" columnName="ZBID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOfCopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report0" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0"/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1" paddingLeft="12">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%fe;eI#!;f+gs>f9[B>U%p3g*/i*$428O6+9b&\@#Wm2j"8C"AD]Ae6@a:aPlb]A$&;ahB&T
tWP&-t!L.nF3eKUVuK,UU@'#U+jd63p.$0!hn!p?0`fbaCJ(k+)0+chDnOI.)IOT6#.)eCXE
[2Qm465ka/`GCKu%I/mD4f@X1M['&8'&BLb$I.sf4n<AbA@ZB"+Y,@_uNU&WtXV+oaoRYA)e
]A+BX2FI;&$-m=&/3?pYp@JlEe*Rj\QX"n#g@;EGD-tFdhtrV^Sr^OcLZeDcCY/$Sh48Yaft$
V,au8&Cl2cA]A[R3Y5$H+Z3e[Y42*=sNnSDhdGIp@N`Pk4J5>m%OsbkG,k334K3!`iIe`*.ZE
75fI,j03<0!%X%0Oi]A>jeR,\mMtX+c_dVHQ"9`!T>9A4Wg/2-BDZ/]Aqq!EKopT9#$9iWm%Mc
YMma`^b,ptZ<d7Bj2NnFj5UWDO/;0Kku`laJ3-dE^=q1#[IJjB;K2T>"G_Fi:4,EHFJOq`g\
jP)Xd*5DJoPL:h04:J#bfI.Kgc=M6,-76M$I>/"gA6t;\.DU=tNN\F<8:ZE9p[$5EGd4q25%
LPG[0C+s92H2S<m#Ib82D^,\*;2%VDQ-@UTj]AE[.Z'>t0PnDaZ$H2bV^2^-4(ug,kcK9'P9"
"]A=fQ(2*dWp/i?O<q!h\JJ1NUtGMkD\_M=`Gc.qPEJ;!uH>.UO[:r=`(-WdotWoLTs/Ud;sE
e\t[6,$aipieOPU,&Q<-\oqf:?USlO,=_EU3Q$V=3!9h/lC"1Hc^/1P.jUKW+7>a-(B2hn&<
9Ldk\Fcp9TC$%@$O=i9sh;f:?EI^0g%s@Psc<4Br;"k"B)An-6Y/oEb/]ANf_(bc&H/j,ERO<
lK\-u9Me[lsOF-Y,>IZQh\aO6DYjr3-M!cE;6e`$q'W7[aY=4X,o&@fF'X?Yk*h5;VG+XF@:
QC%+)Z._cB`XTbFueA!@8KT27ELMb&5Cm`<49cA-Xejc(0*r^5lF3rTClSp(<LYf-dqM"(;W
6UDBq">aj"93Gj`(MDCeZ8`.]A.NhL-h`BM<0("h\[RE?GoH2TQi0-ZbH13`Uc)KK&uR6(7'6
RWMe\X3=T2&LsTTdKp:"*bhAK5]Ago'\?OPIh;M?L]AYhFjoMW@u8Vh8g`)21)qittmS2MK(He
:M:bmVN\kuS33f/OtQ;7*uh6PJ\YPI!c)oNjc6p40:_2eB285ub\CHc<"GbBe;m)a[r)(n4r
/4rIGan#M5Bktl1!SRYq;3c_)Z%+R29kOpBP7.%NT(34_/q,rHEXtEa<kj_@ar;HI%UE!1u&
Lo4:'[NV$G=`Q>ajFR>X*nAp"#m8_9k,+iRHcbhq9;>uo??F"W2e>i^A@(pA.'I/.b-umNm)
a3#PoP_Jm2m<Xl^Wg.ck']AS?MAae'7BT/3\F)Gj$'V[=KsU*a`4*adJ>-dVGX#^9A8dWVV=?
dFl:LEhhMAmSqR[H'V<+q$)L+L]Ach2Sh)7=m=oL'7E<k%I$lA/6S;fp8#PT_7@g\ZIR%aD(H
&7b?hTXlDkSnVYe'"LKE-X.IFo+O3jhkan%(R@<E8H8MC2oTJ/,q(.#%?5bfn8@qTZih>CuC
%'"72)13cFXjGR%D`!#>=&H0i;o7G"em[&AU4OZJTRUti(%#h!Y"oVOKW^JU"0+PF7@,(Xhi
]APN.0gk8OVmu:)>aIdrF7Bgs\J#OtB+sD`g\9#oNLA_%R6F8e&CgH^ctRD!EHg7>;.:67@S^
#SIT3kU;@*\HT1!em#\!bU9)K>lkjNHV/,4K^VV.e98*@5/@)D,eb1K"Mmr)oJr1!<%ZC$ks
!AEW\C+t*#$#CTW5pPf4kS1chSE*U9p%$\$'HmP#9L&3]A[e^=k!L_h2VnHqEWEXDW=IJ=$(5
aqr7,)NU"EFs:)g*<rVlH"M2-Wl,&?1>r07/OQ).Jm4V,Slu8Zj,dZsEZ_L$nq0E`B9?NAN3
%Tq>XoW>f&EY[p*NiGM0J)AEpSGuG7B;s/'1[!Jh6MgKpAWWd>fL"eB3]A8qW72IH6h[+rkda
U4UU6,\6B2JcOe#SX*opZLg3o_s#So">*/HHer0jReFpi*`$\ksT;bJS@rR5nXCei[W,SS?u
I+qT$80Qh$#.VIJWan!52N\?hqc[O4"22Q\\SFsp!33KL^K'6]A4dT3(nF-Q4*=RbALA&B&3n
%pSe9k]A)[1GEIYjdEaabFkD^<[Y1<dM\G-p#,PntR<P^[f^;hiknptDKc^`&2<IR'8G=R#In
>M<Isp/B2tLkOK%*r"H2&$<gD'\+1$j(bl5l:jRp]AX%kO0J=,(kXYd<qKEgGD!"R6Mp\o'[c
!-pu9Qb!5OI%J$a`FlLE)I@C]AgeQXP>4)+%*q@IiF&2r*ClmBjaI,EF.W^\#e^bbsp#b<n;1
h;1c57X\:im2_mjUT1O=I>Aa]A%:=KYp0cBcagS^rE%!D*r^`Ke_pXSl;oHRSUSL#hfbR(gm.
<")ns[Z*f@=i8S1`"XHtUdWp]AZ-Zk:$E4RCgIl<2$Z1(4+"a5:sRGRj#qWUS>V;de.:c+T,r
2!eaZZ+IeqK(r<,Xk%:Kc-`f'oP;Y1`/gZJ/J7gr)FJL7Z5Md!ds.p`m@pBt`&hgWmqh`qYq
WBqR2)sTZ=2"'K]AE,IXIsnM3HI&=8%(u[jRUaj:5%!/Lg'/h?.k@d4$9Pf&ape='#,=NJXtR
k_V`l%1Ol`h=2@KbdQSq$6b0`sSSpnfndhp1VcK6'dKYDkO&-4cV>EN'`AcI\MlE]AT"Dh;Rg
mlEg(Qt]A%5?f2c)3X3to(HqZ7r26b"oFqD]A&i"Lr]AC$b08#g3L$)P6IO<&$_O<8.*a%h`U.=
_SrJM+(";9TuC.t9PSkEc`CR=l\B^^!,SY8[["`dpA&%_29e7m36=rJ+I)5%$\TE6U9!\0iE
.\HhmTE6U9!\0iE.\HhmT`1RaF-a-Gf9b!)b5hR)'C7m/e'MZD#@FdFN@p4`kk\0]A!!3^^!!
FDE!=1pj!YBk^"<[[F#f-HD>SH\ahOW/M*D8UYL$slP>o.hR_B/~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="286" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="89" y="0" width="286" height="347"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d6b23a5c-88b6-4adf-b325-7e28e43e0a14"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="5" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[266700,1562100,266700,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4038600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="1" size="96">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r<'daoS]AD7*W`B(i<a.lFcC@V4cFj]AuT.es_:M;t?+6babPniY;Tr<S"h59[#cWC.76<]AF
rA`VTBO+dM*.E'TifcKE)1,T.jQc5CUrMqu?D<s581Jlb%L,kJK=Co02@7jmjSY5+@&(s-K"
9Q-K<s^A5Ufl@#o4o>/kc*cb?.;_+p9]AD$gHooUeWC`$@kOr;N5@OmaoE8`T-l^,WSkAE6WE
IPm9nJ?edr:RVF\b5_9q*c+/hU)/JJT"JfO4DkCQ*h&0rXlKMm(`X_T!lK'pXp/?^[S;jlgY
O)8n.oKA.#cS>*^u8eCPL"8XB^+6V:L)*R7nCT`g&eP)PJY3SDcp^d.<fCJ`<AhHamklFFj7
ddi29JU>4o?af>\@rm36p5P?q7<?^Tm\=:^]A*V-mR_SAG'pFr[Ho0G;PIqK"h#(Nn]AMEWY=]A
BjqKP@Ji3k[oJ:_;m3/po79U,&[N]ATa(`4^^YUGOt@]AK"u4#T4=3OKt]ALd:Ql.?4%+jhkBS#
W"iV.oL="3:rBtsHh3&6[&RGE2#EcODp%]AXsIoWGI/7$tj^-C/a4<s)dm^g+Q'(;_Y`'rRSc
`=[DP[8INPFBF.U-Dce`O*$qr(b``\m0(I`V;F?M9pL:idJ\6Z_F]AX/TT4SKu?/[95j.Sr#I
0nF7CJ/8MO=#=^6H^)gRW6d7u+WoD/Y@Qeq4frpr6#10\!]A;;L+/Q$4*`i*joi105rsEC$\>
<kH@3,_qLm,k."%jK1]AnW.@>*683.Og.Bfm>,PJ_7a>(78ue$Y\ZWGG#(tlI"1cVC'S[Q`Z"
tEVpTQ'^#-`R0D-0d`?+4Tgo%/D/1DCGE&c-nJr-T<:`cPrEaSYVQ7ro7MEc")ZhXm&R[`Hi
d_s5]An)TRJQ@H5D`?ZcoDSkQk"8nmM37epTu87rl;nVhhm2!pb6E"6)^XDKoC.Kq1[AT>YVk
I4)/Ffp/8JK:0q4GH<;i*23$-G9/=T%<MRIURqCb,F]AUEDhYcqpURSaL?%O[rnW1h:3D&Tc3
+#5?K9%9[(ITnX'hocpH!4&(ZSIIdTOV95,2F>GUrY^<;q^l+@?nX@XccS#"nTS7Fa*%iZ16
X1bam%Wa^4#()p.&^oR_-Gu+,L,4NsC]Ap(rNQk$]ASOe]A[Kba8RiUF\*SYMgH(WW6$mH>@a0#
tTo"o.S1dQtN8^0,S+YejMU'TC]ACOPcQ\h'_0Gld\OJ.b1^_5Q*Y(<\21398og_5^unY+Lb$
&1VNFdH;IRN6D#Mj2DB:H%d<-j]A;XEOd.sM4q10#[U$ffU:@n@spBrCpq-tM8#Bf3Vmc>Jl[
4aM&!k%G_M2b\QDg`YsFLh]A9Fl=J(\('mUHM@6AL!LTuam[.5NWoBPn*lmc"G`NScN>]A:qi_
`dRl&:Xa61P3A\8G!3g5ie;fPrG;?e4\a]A&\LQ#2H![6>FnA#:f_iMU=bh^8n2&JAkAAs^$6
)]A;WtefM;0/XZKX[Wc1-c?#C64R5@Se"fKDTgHgWd:76N%(&gu%e<.BdEssN^ejc\Rmq*ANh
3:m)Ht?^`6G0[F]A)]ABGSpTDo6N4VcqHG=BhW^_nSHg/a`Pdc,+b$3g[Hii'LEQ1b0]A+`/<`B
CAEJ*r4VY:CPAEhVNXMkQJpX&CWX1ar4#<^3,rXeJ+tg\BV=@rsC6GSMM]ALFr"b"NUqjd,rg
[n\OBDkb?B>']AN,8]A^*XQ(bPkmTF38T5rE4h"huRk/^^B_ricWq,<-+BV:]AX`HSgRk6)8`8"
Ye6Y`I6BHoG)!SDY=Dm3c8J*Q6hkbZ!:L9Dq*[0I\E]Af`coiVSG2p(?e='N-2'Qqb*0N6:91
;:#hDX;f\W#d/`[gdP18H&s9$;*@\$$r8I(S6KE:</+_c<Z[C7\uOJB5hN0M;:d?t]A8Nu_/*
MlSIBCbd4a9j?X&j9aVIu?sNaa]A.N0FQTO1'@L1F+"EXi('ISc$.&6PNWE$#>u6"[+n<@=hC
,X@8GA)BW3XR1'Gcfn0-7CNdL=293'Se41=P=E7k(q-ls26q]AM0Y'-r.8_MYl>Pj:3GJYQA\
EJ%>UDW`?:[SS$IPp6iXqtg>ioG(0?W!#b-hl$/2S%+4]AN.t>i'GFc^I,FP31clB4afBb7:b
EUp/RZQZWMm)B^LjV,aD^0@1o+'i:H0^/gn?_;GYa$-C6u%f;Stl;#%,(oLU+qB[,mEAoB)u
CO%OA+U"NPFNj!mfHl4%9A@cI_<.g``a*]Aea,cnMZ^<,6_0Y`q_S".IV^T;`YkOi?oSkeYM_
&^Y2BbpVG&$8n`NNos=Du9,M7/=%rO=[CkDTNHAqe`2VqUN4#o_Kid(prPW9@dDor54-=>o1
?>f\=;*#CN'imj]ALnkD,\C4tbKl<:H*St2qPa.5[okB5g0UHKt.^[dpoT&(knjVQO\-P!fRW
&'f6r3'<nr]A5Jb!8LZS4GYa/eA.FaYs?EUAt&R=TCmWhA\#NX5M9L\&4brMHL^@2^g0k',-h
"f.`'Kl.n0C-o6V^2Fr+R'_#Z&=JROu[p!]AWZ@ER6<RVQr^aeg!OMhZSZBs>hIg/3Dd5[k\Q
]ACFU+?T/.iE;/JMN]A#V9gSWFh)\3\u^5KH=Cu(&I7]A^rM[Ke4sF35CUhDNbWf*_0.^?Ghr;C
/bQ.iVHY%BVkZGPKt_NLd!FK*pfKB1,5M?qnS;K5ZXG1dN*QMlHHsXj152Ls3e8Nb2TIUrT4
S2@_b[M-]AR@=ZF]ARhfa20FSRunT_oN3(55csK?;?Ackc6#)9WW=;2Snf8'g8pj=&L+Z\tph5
m9io3R_nM3aDr"=G;Ko7Rg\PG$]A=$p7<n[;$bdfBsffeX./@UW0m%%bPfgLb'gO#0W)j>&s6
E\\^JmnRTdofL%tX9K<>IXP6@Ti0(gT7GtA8I@nUTl+q]Al&X:rm]A2p5DXc$/2m*eA*FDO+*3
qX/EWae]AkXMUG8DlS"B71(>=C;F)a%.>EF\MhN7eorXo29J@/6pr'Tu5%eQfY0u-GWE;:B-J
$8$`(feCEGo2Ij/*82TB'W-f[C<"]Ao:p,/9,;MZ=R0!kE[9hdm`%>Q"^Y`_p.INl,Rj3g!cM
S@2/QFS85!)#s#n('ml@WXm8%*D)AC#[)QR&'!n'XM-6V-qs&fS<N#Eap)$J!F@@o'AN>-/,
+"+@Dk,@F8:a8"CukYOJ:raRj$_Pf#qMB%nT8*K['s1(Pn9!",THolO`kdkDS&U'^/.Qm(kF
$7@iMC"T0Oc)K;Hj8J/dUM2.F/&-,7)Hdpg]A+DW,16U-CK[!*:C4UkGABD$@bdcWj*/_e?Lm
q3\g:]Ag%oGIY6lt4p&j9\.:Hj^(p+VQn(fL^FJ/p"0B1W<AP_,Q[4VO)+nMt9?6>*$7pRqE3
<q8R)3[U=@Y(ZG(e-6-;6(&h#iA]Art3Fg.s$V)$L\tQDiciDPkHDDb78&(l3Cn64KGk]API^/
P:L:HeI&-PHhAc;LA<1#Mgo\Zl9Ba*pZbe.\]AS2VY:ts<]A^G*fXqJ:k6Sa;7@*i!;I2Yd2XJ
tC&tTtP,Lmm!0o`&gf(6eim*aif)f0b;.*iF8>Q:*jQ/><M31_A=2$RL1dh42N9*PNJMpRc_
R<80fGrFZ;<acLdPC+)uO,%\D%/D=gsWgD'\aq)tDb`J#+<G:^E\\7/>g7lOqp1o#tD#dg4'
j4tGW+QCVR<1YP+q)q#5&?0c<Mk<g)q/-RUGSRGto7?.g[hHI!`jCRDlnBHVp$4u-'?`.`V4
(!ng&DU31E(l,hUI3[l)C:(9B:Y>'FN"GH2j"bgGMd1d1U)q8m_"?b&^;KKZ.QLgoMJY&\FD
+CHYS`\4DeZ6i*I!M<+rGiDS$pFrR2uOQC("*%7cL5DYf<Q&-i:EskaJm%5eU:>e>h]AjTa[9
>5A1AP(SlVa)&aBDa-oerdVGY(hi.bm_9QHu$94=@)G+f@D=!5:'E["sAEr!XKp!!!3EKJ,f
WV+FjFn#U"Zt=*V^fB<ADN$sR2Vb!0-[G*S?CE\C)+ehPA&ZNjP_s+WYiD0`5jLk#beJ-^s]A
#`+!:Lk#beJ-^s]A#`+!:M#Thin(TWMn?+#+"uGZ($iQr7p[N>)"o~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="88" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="1" y="0" width="88" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,2133600,6134100,2133600,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,2286000,3467100,3238500,3352800,3314700,3009900,2743200,254000,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[一级分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[分公司类型]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[完成值]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[指标占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[排名变动]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=seq()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="WCZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="2" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)<=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="4" s="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="4" cs="4" s="5">
<O t="Image">
<IM>
<![CDATA[m<Ieq;dU5MB[T;KoHF>#]AdXt9L5tndJ:Nd=,]Arr-8FO6E+`n8$]Aa75n*bl=fLd3(*?"jemqu
a9;c'hFL5.pRF\bDW#n%S,UFmb18f6p4[_FO_.!6?u^WWiG'%f#JjL?QJ01\</s*WTU#dSP@
#"Ne\"XJDM]A019f0,jo/lNplmWno]AE=o8&p^8oH:IdfAS]A.dq`dhU,'Lc1#B4.m"8jjLSYP&
I*bIJsT*GK*eL)`R\.W.U@8?4b!qIqb$qa^M*$=o9c9'Z*SM:EKb-R*l8L2ct7a2J6'n:gFd
du9B<Q4L'q*!LmA$grV-GO"5nJ2,QikT;1J]A+`"=+SJ2Y(j,N*_E'*LS=jkuPGYMeD>;VB\r
<dW&iQd=a7`B<9r&`JNZhJ\(Z&rlN.3QkLkT\\kp]Asr'n_CKoYB#jhCa1e?'%]A&ck,t]A7tRH
MPE*eTWm#garH`k122('KsLOd@K(%\7(OGMu3tq*^.Re#$AkMZO1(&+9<61+59#!s=huBo\>
tP8%<FS]AE$(gCY.?4\D1<9JTWMQOH\EY#\E'"/UAQcke&\l/>630?/:#j$JN@T`jC3!WmcsP
XENB#tfGL?e6/sPRAXj,QR@%OsF]A)C5a5UK[7"uPn[8M32Q_u#qf0ohZ&uWfhPaLR!\;o'Wl
^Z;9jG/A<gLnH=it!+(VQl?9soZIc4'dQ/mYe)7+d2="3k7@uMK%P+4IUgjJBr%nhB?6TgoT
jdFC.._.pS%fH%j16V]A,7K)Xl817M`Np)_[[c.cWc[V#5iEnA3ceXk[EBO+2@bC4tE0*W]A%a
+g3SN&O&HJ7!3%_n[q>JL.ulm`PI"9i[E6]AHErB_+Dt-]AT\!Y1NdMB#+KKME!%l8^Ju==3eE
@6i`b$iE^Z2Pcq@UeW8OT(G>e'^>432SYAE3:k9@!SDp5\:U2*!g!9TXB`G@g/c]Ars)!(-up
lj=Zn?\;d6jWJ^0@>N@fPjD[bp>C%L)J%,FWER_ofV\OVXEj1S%pP>RIa1l(4M`5gud,ck-W
srgb/Apa*!ACL!A-]ALSrJ1.:aK/!1>hbr7F7J&l'bVaL[ERM@0-+#2:F,coG*@[1C4Ki1-rB
K\S<W[X71P@a(T`N9'HO0[/*;/*l`ko=;A%52cuYo*oW0DQpr3_#*^qgOF;gQ5/4`WA_OfQ#
85ViCbf8#Z;s130WUq;@i%>d'sF(0a2*bp]AX\G/X9.##SkJ<[=a4U:jUr>R@ZUVMi3X\g2cX
4V-k(D&R1Oq8moq'($kLDkTP1NK#nAhT]AEa"=TQ=?I5^VG/f]AYqeS9`W^_?>ZQLb.?<:+;]An
Z"AgcJ5FQ$Nje''=tiMVNOX5&_IX+B\Uo<0TCIp8R`nFDci;50iHZuL]A4r]A+tcL[Pqui@.Uf
SSk8dr"M7;Id1[dlgiNcD9:lA^DHNf&G!jEl3Oj'>$n8n"Z&3s"39G]A9plDm]A:TM`AbglD,3
:Y?!ES0CMALYnYE+Tc<EW*1HPPu&Y`0K=:u9,!Tg6`6oETNM/a'Lm3km/7_cQr-E#^btkF]AZ
onQ&^:NJnH?bqOVWq)"R\;7$2fl&&M@PO>0.ODCR]A:95R+DVA0s<Gn8c"[U]AS)eqeZ&i&q(%
Rd8*L:XK=2qOU&N$,+KC9^LQ"TpjfN,S]A]Al@Sj)si(Dr6uSh?K<N7>F+5ogen9`(OG'2<'2+
/_U]AR?3626LDO<3'&f^:0sn]A<jVUYd9]A>>)l9'UZWDr7Je@*Hd+!*,fO7T<Z:DD!JBP)(NVe
AcqUCS]A)g0ea-826Fn9;7blNgaikj3P3dNJm0$jRO7c\D+7$a-^7Edi(Pp@qu<n1T!K\9e^k
b=KnJ_)(>;s.;*fR1B]Afj:1O<M/C9YlqJ?9ms_ElBuWV7,OmC'NGfBd%8P&mD!p@_F%fJ$2_
k=>/&5BrC>G;Gr:9_%KDV<R/$T^=`NFGuXJtuRqmK(O%\W@XCW4lI,.s.M`.DPF!fNOATITR
,MRPM\X<A_X[L>lL=2ahP=((apc+k1Ni1<lH8Vc%`TN9Pr!m@8L-p9h-Z$VKeJdYh/GGVbRG
($G.5e'g0Yq(NTmfRbXNkf@HND#kb#$DhH*9"%r&:FC\F%#Lt9fT]A3HO=^Jp0Ol,>a^hE_='
luE8'NNI<0K'A$t6)pH-EsP3\A*Q7b9P!d)i"EmspSWh(+*.UWUd+Zq[#$5e.VnR/?W?q;Vi
[&^aQ&0$>!`r\Z).<]A(/3qlQSmKSEf]A<9E3OeH+AO%H>R@fKAaGNDF*Xt]ALi/X48-b1,!6EK
'g!bKA"3%&1^jfns_k^0@jq'HOr9k5@%UHlE"!li4b#IAZ4JGc.B]A9?id7%JYJRB@c-gVdkT
G=11#fCEA3/6aqV>4hEu`pW^Ldk.cukjLmqt/Zc(sr!VU(n34C#C:lWGCW@[VGQ#E_6I$KJ@
(3)UeecBZOC-IZ7Fb>>Kr2qrVVC^CUK/<QT783#g57't'?epAgI%h*_R'7@dWJ!'QGQBSJd]A
Re6$$Im/;I!t(4trR0#IU)rR9T;+Ra/sk4d)5m+Y\&jpJj1V9(*J>1l2*'4U:oTn^uA;s8mi
YS<N8k4:4ro&+IKl.k1gL]A:".YZH&3g%j.)o6A4JCU`N5LVY>=QEV[urGYt#;8@+7D9Lmhe.
eB\O3cS+qF*"[;/Nb5TVW\Gea;8[U89ZDPuY8(o9G`V0lVk(R#lKp`"-M)^hK]AT!V9,Fj\'Z
'DATD69>]Atofl#q>2i#tq]ATqrlg%1P!XmJJA4+T)TW'ik:!o9Tnr%dp%8c6TD.QQXP?%]ANGM
.a.-2N-<>N[`*lN4LC:<]A0fYrj=D%?%XVV)ArQCW%)^AobSOr;"0Zqaj;LR4B&DZ"EW?NW9h
)LEdnN>HH+>e<3o!]Ad8m)!bO*_*:&SRq-#ba.EpCuNYBu-gb04\.I6jm#r:&+YqY^-"[Uo-*
nQTSVI?K]ATe@S_haJ6N!2kAO6Z1.'0,suDFq9r6qc=Hl!o4$NW8@aiS,&nA^Mh;EF>s3&Lkg
KtOrp:6qV]AtR;\E$RZj%aHNd:eZYG+ueTacG`6DF-#8g#uA2''X-t+$KWIL<BL^B_#E5@'N_
t:JFf-6f^[,ce;;"4S3FPbLG6*LG`V@a@/UlG#>'$=*#uO@qWeoZULu'4W/65i=',dV^fhN^
H\?tH)TD^[BQm*9jURh<aj5u;/`sB974Za\bhF6U^m)0G%.%TmibVZSL60I]ABYMbG8qF/llm
,#7D>Yip2^k&afG!j7\BP]A]A6M*VK?FlH"2Y+^W"*Jdh(]Ah,0m\^jrf`8a5.Xb^(Om*m7P_)H
]AD3X%^,9Y-j`.&?8R2Jib/:3)Du8EpD4Hi.aQ]AjgEDJ?GNMZF;>,m4%]A\([M?Tmg1*^#7jVE
[@48kr[H_SB.(W1o5GIKWc9"UZpb@c\U^l1UP2e]Ahg9@R5:'9F37me^K=kKmnRm")V!ggjE&
]AiopHDb(1rA2>=amn2K`g*-95Oh6T!ln[kJYjS&CR(*`E$YCgARc3_5;<**@;<mFH5^5:u4#
G`uB'c4\P9]A+D9j)2SbH!7%86'T"1*q)C,W:+mhf\V:=8R``+&KrnH-cHr2kPgKJFGNh4Rq3
PNpf#G!N?ULF<0NaKZ'ns.;P)o&&*mAj,=aY>>9*JcalP@"Q%!C1)o_Y06C]AR]ANI5`kCX"gh
N=Hp?F7cA0FK"*`h1tc=HB439A^BcbCX3]AV&>s<;6Bo@R0B3'&fJb*ZU@>J>^l[kW-H*%jVb
R:`@F+YB5>n[!3XHbl=Y64YnP5L7]A*g4m&SJmZ)hmUI8/:3rRu7EEGE))0jT+E:RHM)k:+rN
XT>oJ!f\UfFDI+NW,1L.&M,PJ#%gko@0O7#5p^g;M/OFK<GrDP:F-;7n$-ilId&X_&i+[AQV
SOIbJLsZJOumPKY&iN*XJ7?oY>b.+8nM=m;uC!L1Vr[:[2<Uj("GR4g^;u_2NEi4@S9[#oE#
;r,?4bORCF`L"7CXH57"D)_Krm6hbH'Jf[KRD>1PegM-oQ*J!+h;<1Ybe>3,>>G&>=E9@'aJ
\,q)1_SJoBOYtM,KbZR=HXA#tef%aXKPrK,H;Og6/e_i'$bA5S2cE7)SKL4qe>$/B_%sK<>P
I$m=a@#R4:sN<4RS'!U8Fs-7!-,3kV<l+6kiaO*TZ@YES?PE%?KY*mJfjmOE)j:hpfqXIQ7)
rAoEns=#juZ>Zfob]Akq\66Ed`'5Eb_Rr:iFY$R+OL93>K>C"_d-8Yg;(<OGre&'&5Np^t8I=
KJ'j2G0q.PI<NAI8N\fPFJ1n@&_1#^3FR/STc>;)#"djfFrom#>=C(d/,cAIOkC`%<lG*rk6
A^]AB]A7Tp\3rr>@A\2PaOK&F*!*-S/aL3TW)G[qIIAZrDBaIB2F?/Gu9I@&88Un(4`\,@+s7/
gFLA5'K"US_P%Rn1\@XF_aWlf_N:+d;e8KVD"2m*IYJ+#-qcC396Y/Vfg>"Or,n!('@9=Pk-
\IO_A*#\mW:#XnZQJb!!W]A-OEBP+]ACYkYn%H!V/C#MW6lr20WA3,8hKRfYU7hdhNJ`Pu><9R
$[gof<dP`u%K.Q-o_^6#D'kW-A+**)S-@s;d"nN3qA^B<Qm;04]A4nHJB:Kp%5Mma\Ic5.LFk
tRJ]ACs_',.9#N-"<\'`I04-a2p7hAIETF@"4j8s]AL>p4<U7!eVhbb!MW8N\7uc-P8(uAL=W.
'@DM2Wn9P18fe0:24l@.-c'>b8GX90ETZH`qh-F03m7_@?O%?jBN]A>iR67hF!_Y#!C+a8Oe\
d-lhPbm]A#hLP)?HjhQTUOdk&UCmobc!hhAlPWhAJWB:+Gm-]AkY<)/3_'Y>%c(Fa3dp(%U3;'
brY3Y;BDY/h(4(#FRD/LZN`_em4OJ=gWVQO"D2dk4qfEh:r)Q==_tS&R^5,<lqs7?p<`O!&)
re++,fE6P?hl50]A>m@k&0hem,_-JgE$04LJ`3e4$'3=#4./&F+Ik(=6*atLXuRV$KB<-GBqK
DkNB5C,5%^s>RL@c6gA0%")+b,T,Tb#h0=;tK1eM.:qBLm_fe63?b'_pEPs$3&45QI[P8p-`
t#]A7A3H?4]AF"n*RD>*o\\c?uajCRbd4s0+t`+_3NH,ALfP,dU^Y5"mgL:jcsguIrROc[&dbu
3![^R=ghV"5:SeXMicQXlTLnr/\m$;=>rD7e'$":5gO$gl`V>gI%MmF)3rSHf203<(lFGC)<
P<>-&3sug`.am/S*TpVig#hD<c&Qppgr6P=;H<rg<#pkAT.X;HS3Z3!A7JFo.+#L&?W=pmqF
phl&hW@)bEB%rh=4(!>o(r6$9sQmkh%4S>Kb*Y_^J[+9kIOrHMnSj!'f>hBjM"gXZV%oM]A]AU
n(0mTdmf1!O9r<,@\_Zf$>!FA,8JI_nf[YQ>NME'9&sgAu!J@MpX"-=PKj*e0KLZ8l\R^RN_
?AZ;Qiqa;&7U!FJMehm9`Q\A]APl=)ZsEo9FAaD-]A3uNO$3LO0X2cXEJo[]A4&n%g,LIWOJ"1b
L!fJrD6ij_S@sgtCS%[;.<<tUf5qd)\G:bHD;AdZ1e+8@F!7[S%mODN'&g*<\Da4bBVCq7B5
9cW7ei=i1N@f_X(E,tX[+e&'62cW5Og-#kOCX)r9Qj(W8ioH+5?=1P1'#1M,b5tYOq,0O@f0
giU5&r.<`XL;p$mU]An%(oepfL.e,>'\NYS-p[`9Flh=$9FjrEd%f[X_&_\HBeT\H&(?-Vj@D
i*QiqW^bDBd<d`)C8K*O,;_$LDSo>ij&[RmSb`Dft_\1q&t(:1[7-?o!P\pJZBTaEZL>9$Mq
H7c`c>;iC>6>gii;VIHG8[;"=\`%qnr\.7_/+^.58)9*Fjsgae*P$iBI_em!U[G'i4;N^E.k
Ep]AkB2N*X5QZsZrXjgd_<4(Zp<ELWn+0DF?Q2&i!Dc%9Slb%m*5dH2Hjo:j)V8*cb]A.]At(-X
$.RoE'5Sj32=RrONJ96:0U'pFXMD/fD9MV42[Fl=t&Soii-gEiT_1XYK=15?-M\1.rhDdJ\Y
Hp5""+^T0@n\X0)X#i3k-MSlu;:&/?8c&hH%'sh!@DWLIBTqu=U!Bc43eui]AJMdd%PB)QCf;
-Fd\-Q0rNs(Od;C)l2Qm5fQ5>(0/`*$#mA<]A==%J8A+\hNZpLkS.'XG5pR^9\:0W(TIq>8Z>
p:.a`B:oLli4+Dn<=\;jafiSE72WYYp`$d]A_O*3WdRP'U/%"h#\p3J?W^\(3TRpJF.IdjTE3
LS<NTag(r/T!YC.T!P[j"=7ff^&epRCnn&O7r0q(PH<\W>OHRu]AqJuRQ.II-n&=H7m,*YOh*
R[5\_W-H=C*IP^49Nmr,1'4KlG%OE?Q7R5'H%UUqE<F9N'Bt!g"BMFM:AXSJ-OagYN>[LmJ&
L_5J`!/hJ6@+[5JB)f9A&0%FaZKqh+p'8\@/BLjlarOiM=lKMT/c!"9)I@147d&NpSLL(aPm
3K@Y<`u$]A]ABCdpgCkEPWp@Y;VnqfKU'9phh-+Poiou'0Q-$6C+H_FuF!(TIk9PM]A(0&=i]A[X
@M(IXMAimVaPZt]AG!s)&(38l%>a6h-UUN]Aa=VdX*N!dI?'(o;$:U^P1u@FQXP;&B!AlT,+c`
_<=^3&K*WXO&NG/k.hl`Ip)$QiC5knZSHT,d$0@Ndc'onYQbHcZ+8E:&bc%##-'HGA7S,E=Q
1CmXc1OH,SJ\KR#_%u2c2-NRholTk0C,M+`skXb7BYb=Fr.?ioL^lI`*gACOaiUU/1>B0NQ/
sj2!)j;at02Bu.nY-MG:\F0."?=A=C8U;d"jl'elT(glroMM2F(_9>lO>D6q:Kk,5O[Is4-a
]A\K.hqO@YAAk>8:6UU)Wl88%jDJN2/ZYi)Z;aXHZ+3.(-%KjHA'tSr:XRZ/#K5M`Mks?<e[&
UXBe14@;)H0D9\,)-BQp8)3a#u\&$:Cbpl"g)rR7a.fMIQl6GB`+(>@$2@l,iU5G9.c<NYeE
C?ek/7'>)mpQ_qC.aT@Co1Yi:gc<7:G8pbE>0E*iE\h#'-[VF-1tK<MW_T<d2P.lY37'\LG4
"JO\#qd0[D2cY-OXufU,WFYipk!'JiIf3:n$3B<2&"l<r4>3nr["rUYb&@+:oLjq8.UY"l6;
L0]AKP%BnKW591(h4l[t\kMf%PANJ/R38UNN0+nh&X9)G8Q2ZFWn;7>b;n$gQmbr=.&DVUIU5
=Bgn@?',q%afjudUt^CEoG>:C<CPoC@;j7n5CL[[1b>JG&_-o8Lgulmd#mQZh3`YYtE-jf/R
1VH-GmhY1k!>SOO]AD5qj<.a'1Ecij+i+]A`;j0W5!4Mo[ajH-MOKb\I;/^J;tf?&UDqpb2"O-
A'Cb4+u4(-N=Gsk+r"?)%U0.dp+cdL2Ykis8=S+KRJ:E?9/Z4ng@olj-n<X#"k,11&#@CGGX
J%W]A8LIIPN8MY)p(o]A(TWBKSM1@nrB,?8M6Wai1i8Y"-FYi?+nG'$q09A;AR0?(rPV7O!IJO
Tfk/m>pH$?)cg3!eBH]A9?6l#QDM`BPh8$m18Wa'Wie#/!K73PV!H@"JHlg;eNk4aI6Dd&MSj
O)AfL#tuh\t?4I9;=X_CEJ%NjFa5En55S.4p[pSVe#UE/41^O!l:2C&;&4"cf)%7-qF/00a=
IN*E;fh?u'eS-2GRA'aTB2*6:W4C^:QrcU`;h,R!`)RR>BOel*O4(j^%F`=m0MZXp?dh:h4#
/*?rHTf;rR0`<d(pWF'+J'=Lrnkc3k&:-au#\KV33B`$&'-/Lq1169X]A1bjQ<7j_d?%lKFQQ
dj`'g0\sDA'#dHdjZPb_lt59>_g:8Ws?..<\0dC"nVSj]AJ[ue`]A3(XQ9Y>=9&,Po8-R[RJSl
?_#9<)%5hGA3p"unr=8~
]]></IM>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="4" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="5" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = '总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCW[+dq?9H:2?u6>&YTT=`oHn!<p?A7DqV+!SO,>PuRl/X:SU:-t0&_'0nfJAcuX3U9[GoM9
%$*-kHF_&kN?0!otHiH/D\]A4m0K@47E2shmqgZ]AE5X9kP<q\4h&2<(I2TiD?#'sLks,rB25)
3-mGr3Yjq1OYD(l,4(#`8&b0-hF7P!,-u*qtgUb;ZcKa^sidAL(ETX<mHS"J4YM&;2I!O_g0
$.Ho^,l20D,\o9qKG]A7rH9j]A/$1Y1T:VT9f(inBIa\sc^Y!21FmO;5oFmGGG$2P3fCS9C"`'
'2]Acnu-^<sfL#&8FnEEEuJM]AU*30'A??9$UD]AV7q1WrK(+g[iG6uaKsGD/42hdRF:FBE$[;h
`$fb+gVmJ-ELoY.Xdo9^;781XHJ*AaEgT^87]A*8C8EgSfB$WS'gs/9DML"-hD+JZ>-8T(2U/
P#ZjO-i>[-:C_6P[kA!*<FMc.PK,1=7X`LY"Er&`neHcJYQl3JfS?/_Yi)+p;9?7mGM5[;(W
@NI;U.?@!\U<1c'R_mr#.gQ2u[m0WhF=`eAbB`WQUGpq`[-J4Blr6kW@XGFsZXbk-+Gh`4`K
3$0R8\UcF#N^`@8O>NXC@\72>kOFJ+!ggZSTm"jUIq.p+U,^E+(BLE7$2oTR4g\jTar2g^-e
<.jaL&Ps(kEk\bc]A>_qm`0SZP(D0?H4$dUU'`?USESIE._q-NV,G:pFIi+sOWO_LL8hnq4T(
]A$ZN23S--S+>[o2r?%)>oJ/1gO%KhZ_JKss@?^*b@d\0sI8^:!9s.!`&3dmld-8eMoBIaH/%
4X&Y@"FE9gf=Wn[aI*ld_=SU#=3R4..)^8R%.3oPBK\cnWhcH)IRH"8cL6J1j=Wb5@Ea7F5P
2RZT&XBt)F^CX,L)j$R?sr_Z-Ud]Aqi>nP]A>id$GqP*44)kcBRQLc,bd1bbe]AuQ:GNW2<mih)
TM"Q%OWj6TL_r:HScYR8*!.3_dh3lk*pk%dgZ6AF[DfL&(^_c@EN2Gdk#g>j_874p(np>P7j
#RRAmlQHTUR#:BMr.acOE"bKIq699tsJU[ga23Wl`2^3c`&BDoO/oDDOS8nYKQN9pRO&%H6&
ZgH:683$AH?1LegE.qa<0Pk'!/66V!Np;SU3:'YW1f%/LH-LpB>[HBgo38fld<b*$RUl^A&/
j=VaYi(BZ%*u?YHDer^Pg[_`fm+L=H+%PH50`A[;^8r,6b-WK$01]A0$/ij]A$'E@bgO\rS3u1
+T+\4.AF#8XS&bt`V\C`"L6KaFn&,@&:DkS81>bV(Sd1A?>#dB(%<\a>I#]AP,[N8&.3DQ+u[
#L()g60mN;g5/lPo]AF6>i5o@0>uKuSHus^V0miQiCoqM1T`"#Bd,c;RDPe9<N/GC3Ni5g=5O
q^Y,p%T0LrK,G/0J,Q0mHk65?SYSP+-d?'^/iVppYVm_HP@kh]AqD:/#FG1So@tVe.2bA_KHT
`jX%`>>:="]AJE"Q"Y\IY)$UM]AXEKV.AGbec/Fq\fcG$aX/h\utgh9'CcOt30Ac_4rr]AO';ol
4K$#&[QDXE>!n`SHtk/?.Mf?[%`pib+W9q^N46[>:OOWGJ?+\)#'i4jhlT0=$m:l+QCXZhLD
G$;\fim?Z'qC.CC5Md5K8,gST+W<Z_m:4Pd=\]A+]A&R@-JXgWE$X=6aW2^%cq%V_1&Iok]Ao:5
bM9*?I+p(A'QiiB6o)Vf-8@QiQX8?248gX/A<6jBe2MW^L&3R)eh7T'WZlIU_uf7AQ[.)egY
u%o9\YsD_GM\]A]A"e@8L%GlUYG+mq?sfNdb4uTN!1"o"6tl@"Ca-KKF-LD3GH%C-d`7E#V+^b
8q)13P<cWo^RT\SC5bH9>V,;,P2`4<a%=B:^@G<RrI-[\/%@b<Ff?&<e/?lcOP<EB"TqLd(W
3?bGB=#j9+i7-CWrkFV^R7%iN-q9_NEtLJNA1d"0TbFQ="n`T5%al$F:_\V.N8-QNjg"n<YI
=A,DA`D83AZhQ*Akd#fre"QQn.1WgY;c6<!n!fMruebeoGX]A%^%HIgTQ"h]A8@[D%)'R@QV)L
\aKCXUM^'V&^YQqhOoVJ@9Z\!F>N=!sr+g:L3^UEjBr-CkllWKa;4t@K=TrnU!ah-sOC$g%S
uZf2Q=mGoMDR0pmd@_AU^B0pE^9hC+aKbbJ\*fUd+hSf'NAAj8AH!o9J-GWf>pb7SbZ:M5q@
>rs6-orWGdXnbL/r7RCiO[U+PpSp=/BGFh(EP^3n7>h2/K;t;=+J$I^C:LT+;m;7>5lB3*_@
5K!9VEp5=sSEe!YtU=fDU`*F#h6f%^o@0Fmd>ANhc[SS;pOs&\?<b\?]AUF/P?JeP7QbK1h5_
H!q_h%[^WL+"^=_>YGHE\B<Cf<B."U"?Kc?Pce,'_U9Gk?5hi82BhM9#H\l6R85k!iS=b(T#
;>h2ZVX]AB"XE&J4;lf:hJ?$"T?$M1a'.Yj,DP?*Pl,h6L5<ceoIYes!:I/8gGuYYY$Zo<^R!
CU%N]A;Zposq2@IB7S/Doq2>Tb@I=>lo/CuFTg!%LVi)]AlpK-jA0m-E:&ir2u7g-*0md`%li,
cj+"=H&MD1<kGu-CtH]AT1gJX(Jh1:%4K#K*rOt+!gS3]A=XuONM.W[i)bUEC(,u]AEnXB+Pl!!
ros@5aq]A>eD(hCtVMi'_.%c>(<lfmHWFODq6VS0'jh_QnR-m\G_^1I"GPNRJM.SP<)l?UYg4
p789ZfdT%K'7G</3\-f_p(*QV$g;H#'Q-/M'.N#YLR,9=6b+?:-?l1S5Yg1hBG)gP/oIZ3pK
QCni;oKoGpEXM:aF]A#N.N=RH%n&p5--E23&H4<:pnFP$aJ<ur;00(mSbC]A@oi43R4GIPRPQI
kjlVcp5U0'%<oE!Y@eC6D2f]Aj>Aq!nF5rmEp)ZUo"\N.Mh2hh^sfn4dC6k#K?q+jFp/,R2U*
C<!XBKlsA?JPHAtY@X3tbV@h\<^0,2#!GLk^VZNXA>JY%b6jFZhc;]ApSmZ/jQ7>"=(XfP)4W
$%.Kf)sJ%"AXMe`Ftb5u'cOL6B28Vo%_l/0>m56sfrCB^F8qC5uPmbFKF638H1Gql1CLaLKk
]AQ?skj&q4%pXY[;EhL^bYk!tBO'nR09_CAdnh=AfE!i'm8NL<%mHZ>!r(rO]A0"h0SbWcGMF5
>3$A?3Ehr4+M!,YJ[hET=V+i#ArV(f8Rf8q\cTu)..CuhktTG5%Nkd,#X$n3-ta8nCH@<ptZ
^jCmc/TqO!Eo(\(\J$_9h/hk,8YY5KZ_HCML+&Mu4*nFY7BRAc!1qt8\d#mhq]ALt@*8QY\pF
Sg,.>R\CD!CgZr,j(>.["5E:*h0U"h(hWuQo`o$1o%DMhg2IDi!:bjT"8,<[*I)kdSE;!4F:
f=uFA!n7H!.'73qj4I6ZrE0CN3"!$pt>^UP?2d+^'K^@cR9r(?sgWD/XbP^NO%>%qJ)N1AXU
Eo1g)lFID-.]A&l5XS9qk_(nj=4eMbaq,#'7"'ok336Q0Y[SVaVkhs@+]AjH/OL,fq92cn);RE
P[fcFd8]AC.cqGld2(6Wnb%'cbsa;MH0od3C<J5.Bq2^h>(I8u&FT[p`]A^ss',(IR=$c3TT=i
6INjT#7c.CP&H+Ai#b[)$KKSnaQpZ$m"g)'_bjc@2k[S[U]AHpohXApktU@QYpf)YW39oA-d!
%7]AGoS`rZ'%55^tbD*%;`!jAnHVcG-.;B8&"g2\+jYG=0!:!9sDQ0P[5#GCEPLg>qi+T)Q^+
_Y!JaJPS,L4O%U,*_=joUKslslk%"i=,UARb%)OTjO4WG.eJ8QUsA]A,*GMFr!_]AHBM[#*Cj8
Y6e&SH`.0AW`n(OLPmgoNPlAlh9O,fI_=Y:Jem\;rMp=U7_>2BI8f4rMA]Aq:3c9GYCG:)81@
b=2-F1.W(`3BjK-]AI0Pg,aHO/(O!p>_jZq7c<H[;UJq?=4TM&>>PVcp?\`+Y"TLO%25G'n[(
U,[%L.`9.s\!)Nr]A+!&\CfQn3Vr<?,*sR8HPK1k^uYeF)[GbLd82]Ajq!;n]AZ&XgNd:k0?pm)
Gu'YnD<!_Sf.>)ZN!PTK@GM2*K,"Nm#s(6!`cC!$@Hs$R8)6hZ=h1YS:%"!=%*R)=_!c4-me
NQr4)hlKs#C0+iZW$BYJmPQ1D2:00/e)E9&,heU8ZdPXW8S*]AWS`mn'kJ?l^cl[I$Yg<m6n]A
W2&\K4omeM&CXF"#?gP7aQe*<H32Jh8jd>E;EuS_l8g`<b/%9B2O;BXd.C\0f*[@KspeB]A5C
7"pODYYq\X-&sN@;i<*%:=3l/a^u/HJ2a<Gh"\@Mr3Gc*Umj,lYP%&QRkGaS@Z@sZ!SP6HWO
8P]ABUU!g7d3R+KsmXGVNVm$!a&'P$mU>mce%%K#*,JV;ZGGGrMdVi<"Eh.Ek#"CZQct$Wj+W
@Z+.UF^!7UQ;apEHaOVC]A]AWa<M2m/KXmAs@c\X%3[58FEGh,MfU7[':R;P_ogWJ@r;_M=rM[
iZa4Q@4g)6Ma.7S)2!!%VIUbH:<$1AuSHh>4=K:*!Gd__'5Te`<BbZnW"^FD@=V<?DU=dKk`
1R>0d[,N^-?0W/2HC\H"?:aqY6aCAjC$GRcej7anRMa*`5GKKtZOagl-^@h-('dqH_r^\TGG
NRkn?;=1`n+\KBDh!A=jRZTG&=+#H!1KmrCgnIMWcj<ZFhV55/F\+`_AbJ02Ca<BE9oh!CL0
8.\p55'Y+O>jX@CC)U12dUY>[>ggYtn)geV'+f*j:47B*8C>BNt-,`C,:M,q$DUN8r+%_A/.
-lRZp"LdDS$affMo#K<Fk7[`^>H(%-1Y]AjCZ#-Aq`3A$\cRbhH"'qo2EN%n85ISSL7X(0B!8
h(6SNoK:TsL(I8onUah7Y@dHs00u'TU2?*&9fs;&@bT5eGpfgA*'B:kI9Q:l8hd0mHt.W4HG
Dlf1i)!Ze9*'mHZa>k5A9F!f(CgZORMEop%l5cM25M0Ch=:[=lQM]A_7(\sK0'8qckk+Y*p7^
i#JY`#IIr=(B%72/#XnIE(j1]AsJeO]Am"aZ=m-(iPWn99#h_i&1;Qu.^:#B0%dLFUBK^L)m8U
_uL.oO%+Hcil?gCaql"MBMai@)XrV@pI2q7Ta=Ft(Q.4L!6rp&=&M=iu)`Bl\fjJ?#oFc9BW
mH/K5Id?)mq69Mjd0=n*UjUO&TF806U+;foo`tjr<s`%Ggr/5d&&^k(8tNa$CO0hhL8OFdE.
?AJ_@&-]A:#M<FKM9&>)5qm.;h=mc_'DfR9kfsTBuQ1c=_fFs3BgY5FprNDnl]A1d;gR"<05$a
S6s@\0LbO"n^0XDNg\u:Y!2Ak%-'Nk3Hk.e\+kQ_o$<4q-Xu8:6[#qEl18(O.MRg.=juZOG!
J9*1>*TH,'IiKr@2V$OP=_h)[O0pQ3`.A`KT%UYY88dU3*T7W_H%>i"PK!uj>QaCHVh>eaI(
Cl;:R'.#XAg7/[^H1UL$`[]Ak\nL^((`nUQO@fp=.iY1o.0%o+D^]AC0kHaSdQM2QHX>_5<h-9
I?$QDJ#Uqi^E;;m>Sc9t02fB!=lJhZD_0j/#Qs@&hJAk/+_0ZU?HL8R-7/><'Z7@9,B,>7j%
ra.Dq2o$N;B*Y<N):N2gDtH>nog()/Y;W;anjJ2*T<[LcG9%D6^U^dm)+&[dCD/a@TI(FfW'
s>tiMPZ3C9"RV>2N&AO[G>,qMEXTQIg8#eWiJDY>0nX20?A='O>Ekf,il'O()nS+DC"!6*o"
E:QTCaj,-:8;iO'AQElj`Te:]A+1ikEF)u-cd2c#QUULCYm;qN%bjk)5#Y;;jZne1@uG.G@!l
s1[8^6l[.F)&"4"K8[qG3?<#[p_fm"HSa$c_2<F(&Q"d^ja:m2=;R+/!Ypu^ZuWJbXc0Ks54
^YdLe3NtiP@m%aa]AJE$jk!3;iR=1Ya=KmqO\ss'5o^>t8qq$bH)RF;!)Z>phh!58c-O'-:F4
s$<2om;(Adhjm\=415W';2$$0i)g719o[W/2[41MWs,e,\n:s8.U'EOUB%8c"9bQH[qOl+4.
b?5%+lkVWu*kH6Kb&Jc!:FYgihm1)U]A.gOAV<.Numj%?G%0SYL3aOBL07p>G*L?2:<ZjWFW.
TEtB=3\(C=:M3fjo."]AL/G&O=]ACr<o'qfcj,l('+;*W!>-K@Q-@"6Aa?uB2[UlLZT!;l/:E>
MQTtZ\HS$V`GXf)l`h<()X0=As;O`AVGL/!nAF'gDSHE6kBV[+"?4!sr\+!.=6Q?O^bL$6,=
3lTn?FF.5(Yu]A*DnM+hL%7T[&rCdE2[^)Rc*eA?!$8^cQ1C,dhUVpNWG`>\sM[&RG'k>i#!`
Qq9^ZIVB\++:nDq"<k--<]A?]A0O+.NA<8>:Nmnkmp.Eo`ufpl2O3M76a2P>ag!5p@OV9F]AW;h
<EmJuM#)$X!Q9mu"^%8a0^Um!!UcA)tiZKB)p=PGnH-R8#A!OU3^FF>A=3KNip_$6+[(Wq2;
:6@hb,4mEWsWnam^`FneE$>g\'R+IICZ:<#^u#hKJ+-ZlSQ;GOR]AL#P,nG**1/8ISH_AC`/_
(P'KPli277egO]A0F-^CiTF1HT,mCX`.0j"!*q[GHbq,IH6Y?C(!Ao0^pcpk:t'EB-S^8SqV,
+mXK9*8okf#;pglqRbDN1:^+Ofm2kWc3\hs6*@g4b)@kak:L8hhX77db4NkH5F6&Y]Ab9HQIH
c"fmrG%T><XH+6WP$MOWY-K3pqn9QJ\/<^%-kS`NQ`QZ6S%dXIfpF8u[Aoi-LYJX7NGN]A]Ac;
nBE3@Ni)ICLDotL%Bu2JRPX^s8"h;mI([epDY$#J'A4/G;-k^I@</$8am$\aOkQ9NJ[.2r,L
PUjL23'NYI9ijc;]AuT9D-`pi59Y-KXUjh5OQ*2b$n&XC%P57:/oIVGf0jP[8K>?RBC!RH_t`
%@H'C'<]Ao+UdOBre`1l%I^=a4_jXITqP,%?n2P_@PB:_E(lU.Rt.mfYpNh[\(^Wpd,R@6t,b
&P))<0das46H`W6O,)N9916FAH\0Cie!8&1[2%3EMobht+$]AJ\@8@?sj8K>iFYMp:2pOG^5*
\SE-TZK0'?B-E(DS48JAmJo"5@`&js5J<H+B90'_n)>mq9',H8>O\L9V--HP]Ab/+=?.1&MLg
U(7Zn`bH_p?AZAL.2(J?e3T=rW%:nI=;6^[BYP(oH>ZFq;*q;P"0e7[8V1![dk>2aoaj!PNV
!u>G+8,Wi@mQCK@^WLmMAbY9H%'(mo%2]A(ddS3"Eh>BP5n/H'CUu*_:8LOg*4`[nQ]A041QNR
eh44RS_b>^m=WdB/0[Mtg_++[brXke3:`^:A&V'HC=Yc!k[OJVd1nZt\nFe[gS@R(ASmIb[M
4>/cjPVcsBm$=rRa&C(e:WEW><W/\X\cZ=NGrSF4b;S'tD3B1FrQ=1(pjF=I0R[MsU%1G#X8
J#_NW8iuW7.odVgFQb;W%X![Z_*;N:F;CmkZag^c?h&U6GbC+81(HLjKY9)ZG?mi6))+-bOn
]AqF]AhgcSK$k778KX$!RLgHAp9)o_?[H1\P9H??V/k%jOEXP;A*+d/)jVAB9>A`T<HJYRhD/=
4Lr6p$N3P]A*cSgcmFSm1_ucc/.V"UR9`Y:Z!srQ*7GhGcQP@sX2]AK<qb$Z#eTlI:r4`egEY\
ta<&d2U88&$U'TOAjImAkpTUXi>o",r%Bq,QHp5F4aVUtJ41l!g8c*@F`4SL?4AN.<XS![$k
Ch_NOo'Eq0&J]AXHD\oc>$!FX1eYX![3dG02oj5fj*A#*r[ISJ^gX-buh@SQ!5^0plj^?"Gkn
ok\VDN9kr7OED:(#CSZ8*U^LbU0.rk/(D?s826bQVDQP]ATpbJ6!4@2%`uCP8l(04/A<[@D8h
d0Ih2ZEiX[K*MpVP83n"7>&9@a3TYmZTFd\kb8TRmA-kQ3@E4uuGQI1NY#q<>hHuoYOT$hdD
kYME[1^,hf>5\cb&\Yo3?,*(4.bOONql$]AajJ)?Y0_2YPL(\1pFK/Z.EoeSAXE30SRa`USKi
9R5F^OOYhEF3mOAJ_=_C(+I%:a5ehPfl)249nCV&@4VCneOKFpg-FPm)#rqeh(>6=2n\49%1
8g[IPHcB3kYH3d2%j\eiW;$"kp*qVP0MbX4CCKfIbd(T)G^6'V*n#m96e:!T6H+BkYfcWSpT
Fjq@KqeRmoi+?##Znp&aI>pkiqpF=(D9`h>)H/j,5S21fBi2L>&Q^KcM;d[E(,\"nZnq6oN*
pKQZKW.O'n42r,"#+HqZOG-.ikr\ur(g6bs(!1VdLTm7J/V_-gWT&XL#rCS*7>\>dr+s8\\&
i+dAGtb;T4!%Hq9"LVY/GJ,RPFp4=0@2hA4Drj+5+bW22dRJG(BdTC>$8ubUWFhm0u>2tT4J
iO'bO^F:0B2_C]APdJ/1d^NYkdq<G<nRa8!OiX@-n8_-'"`VS@fWd.tQc4^*I5b#L$pDYX@Nh
i/#FT]At5fgoVk$11,m5JQkr;Q='nus393&`s3`H/DWAU9>h0O`=5%n,!tFjC[A3`[h^im?]Aa
S`?f+8rrh2q!k\+Y"g#t7^1hXl@?BR4g!%X,BUi4d@^8b?r6jpVAJ!l+=kY30mjC,B!A8Z8L
h_SV;npEs^f3cTV*aM:dK&n>jl5NcaSiLCa:A!ehDgh7*p5Z`QR96=[]A_s]A.rI4o6@^@'N]A;
MauV='4$-8+3QXFqq(e#l:HJR1F_.TAn9q>E4SGXNH:V(@(jp&eHWhhW!6cfnc%^81jCN9Lj
Y@f3_3Z3u.E!oq<=3$@V*Pg&5pE`1e&]Ah+_.V[]AW=>'0P/!l2"mNEKA991aGV1=t>[a9mLr0
+KgIoK1_ngohT978G1#3c%e"*<AoCR/Sc'f0bT372WbOHFP/CpS-VPqN(bYO@b9b$KuOZcc*
:$S_?oQ8E\ptI)lP+k8g8rI<M)j5R$*nIM7V`P`_'pb,2:Y<1`MOJmEFu:=Lg;!G%SPCUUKc
HgI)c<&FnL?I:L!:D%fWDcl>W+IWM!Zeo8,>[o0YDk<<Pp`,;E`ZDX_]AI&8[nRkiuXJupmQK
RrD1GEq:O."ea,#rHJD7m4<BEFGuK-2Vm-L=]A*PTDB&bGl'orS1"^!A0TL+B.+Fm8@VR]A%7q
3W,uI.cHsDaRD[um#d#ssE:+U45*&HYQ10Kb3K[m&Tor1D:7rQ0-n?n":dLml0*O4`ZPO.XV
qgitWq5:fZ,P74gdLn7V>c#e!g=%)Hjb%+?l7\H'Q+00LO(r_7B"0c7!<P0*h(-!@b!O6iqM
]AVLKp)RVILU`[GFpsl#q>=[M-o$hdcqL&aFVkOgJ`eS0X'6N()Am,O6%YHK_[P#0SB8.\,Ki
/L3dFZkfVt[`l4W)7"-'Lg(LbV)+IES\1@-0&eITrM\FgOM=:c*E/tm7,!qWlctC8qNnCn^+
$cS+&p*=@>Vu.h0H16fn.3g.Naoq.Hsblm87GE(Z/5*tj=-Y^Trq<n*_]A6C[WG'5?-es:GL?
P=n/9l$ku5"_?%/^$@FGAHCt;(%ekHoRA7gp6?/_lSo!p.rX%J]A]ATemq0:usZ9(@X9;;'"q;
82+;Mlcp9Q\]A/(1I:tjfie"4;ciQ94laDmEE]ALihQEhPGr-U$gprYW*4ue,@c(8U2?b_H!A-
ZZR?;*q9)[068KSj<F+#\kt*fA06J<0BamBp&5(T0t1#2[+m:RR7[KQh0QXJ/ijnZ8ROGs;\
D_'Cf:`c1"0Don#f]A/"nB(Y#dE'LTp_9ZM,HUV+u5?gB7?`PPrMXG9</U&@\":,uot`YD41,
O)<$jGjIu5[jb8_po/DG$W/jK]ARO/fKOE@G\dVi$)Tr!&8^n")gPG1i:8nV'!45KC8O''^@E
US-NN\06>q7_5c8mR!j`]A=)n8]AF?Yc3ScYHFpj.o3%I%;We@b^1P&EnYk(?tAo<0P"?Cgd7s
q#T@HZ:;OJ]AYY)X(K0)NQC_s2"K\mYHJTeL<Z<Lge7fJ9jm$rX,*fXQ+([mo??0J.>H33Y!s
hI)j5,KQ>hu[(f/u<!,TE)0Ed9bZn#dnUcaqr+D.odt%%;]A2cPe(T5%21SJb+BY(0c,3QB3E
fc#lS=#@hqd8Xu*H0mB)I(Bs9nEug:Zkk6o(;?=emTekNtGVIl%H_m&o&uI9?KB.9jTh^KGC
`eO,\bJ]ALFU9QH2Aeu@2%0kq>E'&gRE&kt1Bu8*,=]Aj,I_pWBgp87(=P<]A=0Eo5lEmsNoDVE
#1?W%@p&pQ#B-l!a:NJ15nnhO`#Ag_=#NOX'dZ-YG]Ao!%Et]AR[A-V<$e-R)R6g#!:h'f$[))
U?)*8-3T\V_%b\gemDd?l0;pe!a($ngWH._@SQ.4\Buo0iN)#0!l-d#rW5h*A*!+:OC]AM%29
.=MUXt54/do3N8_H;d=75>$WN%*$[>!=cVW`:Xk>Zn',D!h)$\7g^&d6I4fR8&!@N4J(kBVs
u<<..d3LG)?:>P_,n9lH"/C`,>CAq3JoBBO-e`4X0^_=S*I6I"29d[X+,[89-+ijAu#+W6=B
#Ei&^Wu^l.!lMH"N&r2bD7NQZ3Reh+#&UB]A8\A<em7ZFU@-"T9?M6P4'&8GC?/#:d+.q%=&+
bmCapm;-2ObiM<h&\0go.!8&\4C;$[*1c6LcC1S\>:Rj3/1a3s&tc;A8dC-YRmXcNU>^k*T[
Na+j!(nc7@*7g(('uh.MK#nRL?<p'Bh?%M2q*IP8/eqh\nX>[/6hs\O070+98?u#P0W`YX4/
d1T.<(^:]A#pToLGk?oMG#Y7]A3W\<lV$PMMl,uD<c)kRBl[>%cgPa!Q&[Yr:JM7V1P2DP>-0\
4=f#Q#N:#M7o^5fD]ASlN\>V/LPp+XWN=a0OZMHmka=I@F2fO9D#,idU\1k3M9Z0U,&XfJ<s-
K3VScoT+e(9H(jBCQlJ'&q9p=^oqKUfa""'4m.,([r'%kRM\-R%aCgZPP"gk#0[u3!QPl[F;
R@6$e$]A%Z$.\1.L4;F*,*(Yq_6<''jRR%Or+bi`e\/'k.*g4/rDAU`R&\6rf:lL9>LUo'Au*
1'jl?]A]Ao#@e*4+"K8,%_m(6KeOdm-P.STnbDgU8N!j'_ZP]AR?H<'p+r-jZK]A(PfY/%5MXk-"
Ag]A%D"gT(JUEi9<J)Y3f%[;B[O6]AX$2ie-':KK5KnE8*_2'ciX7J48@+JK@UiAS6XTeZBB4;
7=&6OK9f!piN_e!8$EV6MPV>nD#.Ft\:"2)P>[+`hbYB0aS7-3^IMToG6U(XAp^I>M9S)JB8
BS?1jVJX4hofJX>AEstH0u/dr9Bq/B.qj.$hEc8&dJ>gZTP6AQh*pBMmlOT=!p-t6&g14+Pe
<UBJnPG8^E/"s+D]A-!B#5O9YM6WW+N`P8j366b_W(f>$7pqf;4p3Y8>oeqjm2c"d-)!Z%eXR
A/[+6=*.sX[O9-GbDn@e0O!^9M"2T5JRJ,(-Z$450=!N4%^I'%$s\DWaaj%UMn"@P3%7gk2N
dE8;rC.s>KIZcVu42n6HM:J2WLXs<G'V\<9_u)ph=r%8c"V_U/1,hB_W^7]A8O4D,;-dTdE@`
O5La!;>a1*I+D%W;&;(QAN\NNYWqG_qJkh@brK=657YHT]A.#5OM-&KIfX]A6"`aSQ,QSF3kBB
p?Mk&M*GB'aHBanK^Rb?GLc(j`K>Yd('pbSG[n8be<Z*F)1/s*ef=#Zl3?>p!g'0p93n<.K0
W:h\^SufLaGl1F%j).pTi-oPT61U51&#Sf$C<%i$t9mK_)F+:bg*gU_]AgKPuZCmL1Gk07=Z#
s"XbaOO*B7Q+(U4]AA4;Z[k]AFT![d-ZVY3n_::naZr-CHZn->'"X7=92O-40F5S?s4^n>p2PQ
d>&7Lsd8]ATg\!U>]AkgEIqeIQVVOcfQ!7Xo)7i2mQDj-j(A.[Cd,qC((@'c(bVAgb&l\S:,=;
"h^j]A*Hq_:4W#Sf@oeMn(^Ed>R[D@3TeZ=IWrog'2./o:op<glGW]AcaHB]A"C<ZbuFM;4WNU9
>E98RC#5hcS.Q4I>5+OV5XTW=W*<eMb(BATUW^]A6jm0PI.s[DV!CRmFFFS'bji,S7F.[PLH4
`'"9PEc>pG'VF"Gn@+?Y6V0n<(9Q:1DA[H8A$NBeI`<rR3=V1DlJVhW!bSTEOkkW/E?lq.r-
XjF-LG0"g)r/d#KJ$<nM+\G3(*N3p/X)D;bPa@88%pF+s3=B=T<PX)9'[>t>J_J/FS[)`]AD1
R:@H=j/bD5jIEIoWB$hms$p7Xnq",tF%/Iu&cG(j&P-GM2Fr%;Ht-8LK@\Q"/>,ZXO)m;6pm
hfhj81T<.RA%\a:fC</3M:\]A?8\/hrQdbs(kiG"=m/a[S/[-b3eV1<;j<iO81BY<-CX6=e:"
pO\%2foZf-#]A:3e;4TKG0'0\4G6nUe/`l!XLl,oK<U"/\-hQ"!'.sI[pm?eolsn6:6!+28W!
*N6oZ57D%Se1cJ<(a,/Vph4CRsOW'+?,pSkF;Je(@"PoK>ZS(\Pu:G=ha)nd?]AHOk2mpL"pB
MN`tEO+#OUb5.b[OSn(ekJ%Q)LX#eJGin(*\t93)M84ThGVmA9dhA,Km%aUO$'MF#:BP8O"l
i20-SMSVYTk5ors*VEs,uWpJ!RPCl)E:L6s-<No3]A]ASTkjLCdY%QSE=^)+LcqhprRg6+YJj0
%%e?5Z362U+04mf&$.Q`B2#^,&eRsD1niqUIo#%6q)r!f11@!Xs-c1+/A,@X\G<?K1NUX#TL
^ZT6'gMK&dIc+m.jF6=D*9-2Ig6e=*mI<=r]AU_i7K9Q?kRDHuT:.a>?2=4tpD]A4;j:)?3V0L
Lr2X^%?m#m]AH#8]A>Q`mukWLAVMf8[\HT]AjpnSPWnT::RIWd/GSrGq@=@Uf9JR6pmB*O+SmIJ
ruWgd\H6;!_$X%>'nU?e)klg,5VDiB5u$sC[Ga#\erWZIY-*:Z95O9kU&>b8R>d[Bn,lAGgo
$-I1[r(9Br^+;XPc%BM=<n60We#lq30O5'.SB#lT;2:R!OV1^p\H,EJOaU27ZfR+!LPgYKt`
37jHkBJ2QV("tZ91V_mbX8!U3\ndEj7H/NUnG]A#lpGhsJEMSU2$d>HQpWC3#4YnCUZK:@JWG
MG$In&/GZ4Mof"FtXU2>;N$(2A,UC;rJ,K*=_nW*hKhDolm74b`3+KC$fbD(7NpKG^@)mJup
hbR''<D!sB$\r<]ALmcg5,o,U:R7NWA;-`uH2rIE]Ae,OWjNshbLgY'%6+Dlf4TsU3]A9YYs\Vs
&#jSj1C>rDmS:E-$;rIUV/ip!fZ2Ze4`p5r?>Dc!n0%ba4*hs_FSmK<T(Y\^WKOO^kWX@B'4
F99/7G=q._+TnkS!OMBjMWIl_sd5bug:]A(*ghOVbobuPc@^m4UB&Eer1IcmgE9no6jD5Y.0O
RN>&b_9-Jr"5$I]AA[-@\52$R-]AT^Z[$K0rmnGrO,DGiGfEoC^k#:Fu<;g!T[Fpgr]AUc1F-IH
h*;l=rKTpjbCeQVfmQ)@Jp]AZTa:E:/>H$":ms$Q.)%+-X2X28%,\.PQ%jApr8%<@mDT4an07
/.I`,iabaiaFq@6;(P:VMF[[5n<#:+%7!pA`d4.:6-\pj%WLddYJgPe,O0'Bn@5$r6Z&6,B=
DIa;nBbaW9FmT-]A#hil">jFbK&r3IiB:$572iHGXq\YhpUUk=kGfGqu]AIecMCGKV#/q%SMP?
?78W44^CX*Q1BDC)cme7<sU1;EQ"^$l!%Q'[Q[;;(^V@_(GU&/"urm*%NfXWM+4o%m%VV3I\
&2%@7T"oF0Tids+m4I-#Y`U"oCH^Be\bALo]A=8MWgFh9n*ll-=]AHX.0?G`k!3^rM%%o@U0np
!34JR[[+2\qPD(Ke90?.Em"8aVh2R(*#7LqI!*CmcHc&\F5XqEI;Sh5L=/Onphgi#FRA<iE'
VRV:1.>,W0Vt2qrCF-=+[VN.XUu0f=TIT/`W_Zr&Ai`&_lJoB$Lb-mdgApE\>^4(d_IL=3Ie
:fCDC+C4"S8[#uUE'Mj?))qm:id?,0".`&A;Q]ADQrP05l_?X)s5VDGffkbdu#,oP\=)Mjg1T
C'33h*[Ea$;L_#b:JQ_TeiC,37FD2c8/WQTK/QGL1Jr<`;ICX1SU-?Au1C9PM5pol+DXD)EO
_0"]A!Y'5N/p\)B]AYk7aoLjt'bc;+^n,Z`-[ENl:0!jqW<>%qRomYu5ctGJ!7i_r!i-5MmH[n
o4pB3<g/]AR_*jiJ%Vhc<&p=WIH-.X.A*Q`I&apu<E<>CS(/mNp,?i#:f'?u.0+)1aIC-a9]AJ
XnijCt$]AhDY_Nf4f[h%/[\B]ADnTkufd$8%Iu(e]AgjcX1K&S*e!8VX1V?F)&M5MB?/-e6DbK?
1mJS7EV_WpDksOApVE,ZYR"RtKCC3WqKE)2:%Hq=E?Kr[Au*NX*B5[B9\O%U)0n\CaS-jqL8
Gj=CkCt$6$4Mmnfg,,0I6^`))bfA.I#V]AKbApr[FrQM1KM<fi]A[8C=[G35U[(XgR/$jqHY^H
daj?`g7rin54e%Ze+F`Db#dbW0]AT),s8a_X]ABdjYiWH1,9RO@P666u2G(1#D+Sa^9@q\7baJ
.>G4r<7[XVo[EWA'Y-p^OK^XTc*nR9P3.Gk&jrNaBBO-,Spo=T_U%t>X0FblWT4G@DI6]A#oD
!$_1YUg1ab$!oLp+.4X3V;Voa91!etlIY>PFNE9\YkP.MOE3*%P1:`lTF[RD'1Pn\,'lhCd?
;?TB0i.dl*rKN=m6qq:'GPjDslC^WqRG5E6h4pGQR=G4SXt-7dCr558S8OYiP:HUKQpbK.^&
KGOh`J:HeeA'HL:m7,Vh)XS70A;`4?mCCi;J-Kk<lV5+!8$=9RY&g509h8^R%t#r8.GlS)60
#FNiriUJUkX+O6a2R[3tZ-.dWli<SVj+,0(%b-9E6gZEnN5I9j-^%@rVdeb6P^Wb_6MK2&f5
04CTTJm6gVRB;K$kR5`Af"1sa>XY7;*<uO7iRE41^mZXjGhVYKJi#!W-*V_dtfh&r=.`IiT)
b+4d66l052ee0eEi[6;L\A1]AH`3&XE*b]AtLP#29_]A"qRM:FNU"n[RZod+g@a4RL<L+CYOo?b
)6TgDoYb:"G,MLdNJuJX"5!]AURnQ`WmR>+$c^/C;->/)Q"YY`hUX)*3'_d1J;R2u4ARqPa4u
4ffHIeO%qcXrb&!*4B"GFlarF[&kf>4cZd?)<uR(#e5KO230Lpm(Ie*"chR)4sE)pXu)PUhc
%ReE_#FB`(9&'3t7[dFBF?>VG:kKT]AJW2r@-7<%lE0HJ@Y&T0"WSU8oO3APY!Z)t#jT`B*96
ZH-FK$Hs8D)bAnco&l%d1ZG?dq7Ib(%DfT+3&oUY=Aq[bbo&B:mQtXp^*Y4Aj/`t:F[2HY^P
>0(bJ#?gaN*fU`V?DE#U4#[Sr)eU4.u1lu\Y`V6M?!"XpeO9s@U!pbi![!]AG#3h`f8c.EVcq
+1*eTGT9"`^DK4>pW;5?-Q"jQ3GAth`Tle<PO*R#^'!Bjmcc7ScWiiBqt#kP.91uOg5L`Bk2
DuE-5EmCh1+"c2B?[3ILS2'>cGCtD3m6G#C#9RDUKk[$d3^%3LII_l@>[=WP5mn620+YPCAL
#e=(a/?.!_.(I7tIb]AUJV;C@BSboo>;F9G1o+s@tfbGuBoqhXCF-4=l=\WN00rZG=(IiM71N
K$]A"PLfleEe`>c0,gt0oaaN)p.1IWn\mYp:;C0+;6!sl98M1QEZ%4$CUD1iZ>s_kAqmqT(29
:+LJfa>KO#m/H.aDWY0UVC=%,SD,i"8PB%hT^Lj>\:V2YWcN[0&(E#K'M+&\5\U$/rJ[\b/0
]APD903b?n+;Tk>^pZ"7,!l(eA:-!is]ARH*BTseElfnh@VbVD>3\RFJg6#WT19/0a"c_h2ae\
gD_#i0)Z8UofK[2Y>1b+u8^rn8U-FHk6Q'WS.I3km$Ia6SkFJj_h^2C78g>)IH-CN[V6+4<:
6jg#;'BKXPWku^gJM'9$.NA)$AB5Qn<g>93,Qtq4m=h8@=q.mlJi"27*F`4U4280]ALk[K8@Y
p[EuA4]A2i5sd;*M131\X4mcJ+j-HCQC9S98EP%YTJO1(Jld:Ifd8jD,r&1\RFf?PX"GOC*^F
E<UK[ij8&4-?<TPGpb'E^QXj4&e\EP!E<CLVoVrCiKR5&f_kn2TOT^D"X:?d-\Gq31"(>:=N
nEXFlf4@uX;Y:ir$kD;>CJ2)G!QV\DEE)L*##6AJJPe'p1edfr5rqTYSfNNi`C#),Sdc(d&5
n1>Vm\NP`Cu3K05Xpc<H`0d"J!fLbs.,H3!f`IMSZZR(>J;^gAD.dN'*t^.G,3j<\^]Ag<%d-
ej5+dUECu,rG:_i%BcJdM1hX>l7.`T!U:o?XaGtdk'k[\A$RF_T,1Ef@#tU#u7dD]AaKQa1\H
]Aio[28qF.`JO>'48M^tf*=>"Fm2+"\WeF9ZEbeQ-eD@lIrnQ*QRcNNCb5%SL-l_7B(NpoIG3
tG1Xm\W8Bg=o/jTEtCI]Ab@&@9+C)-WZS*$QduJhEaS*AH;$-NK:Qop!8Rj^q[rQCo/*9bAVV
d/VNb'-]AQD.tUp>0;ea^3u#OT:=jcg+^SF64\+WpV5'!A'ZcM"hGtIbO(ecqM'i;:T6iWsOs
Xet[e%$99e$32GVXDQ!?lnt<[Ar+R6K+B[L_nL`AHl=%X%5*"o*2GT0oo*:C0AY#d('nV[VG
%,).4bV?>TUS'*=VPqC(lcm+N/@a6e(_C)nDRKMq=J+V9<$DhG0.`OTkD0<Z:l$`E\kp.'I8
0d:k^El@ir*6foW4Pm`YgX:7Kc[Yq8ESRLkZ,XMWPie0Da@L/S>L)?ITT6rMlJC^33O:P[F#
=\,?e<&M#+MXNsu@\"M_\+`F5b.Ub@#>NY'(4rCNIt5t%gf'T*uCAdN4-d4phj#n!&9>@Gq+
gp6Ti%N&l]AQ?W:4C&7.Y\#X^)#&Rlt0nFE=Heag!PA<<KYdeA&%dCCn50:!_X2FM+>3JaY11
uTu)F>[L;8,YW)&,KLpUk5Ib(/$l`VCfD7OeFYa"?<M_$Ko.^n`,1XO),RX,3+;N0e'O-X]AQ
77R)E6IgkNTARIEoEmUmUg-e/<(JtVAoof[i6l("Wqb,H@[o?X,[#sDH?BZ^BRmb%dcf/mGD
9EQJ%U/ue/342FN,f9=2#\f]APk^4H;l[:Y3HWiDLHc]A#d/%JRNM2Hq@N['LJ0aMse!XIlGVO
<i2#=e\Sm-P*R-h,g9$HqA"K]A?O7WO5Q3-X,W!Xc!:U/_?Z"/e+%?>NVbh&41[d3.&^V<]Aso
EDHVX^jmCM3,XVIJ\h;\^81Whi--oQ0""A?5FDe@DHksI_W;a*I_HTECoY0P'!%D8]A=Od+AR
iVQ#iQEg:?;=NK09jh04^ICXQ[`go&i"iP?"QfIKXaJ7f_?"lZb'UaK::;R`D_X9c@/JX,#c
;S2$l]A[@%5_8P$-@keoT,4%e1FOR9e4QOkdJ!k`657"9R;6qr0YUh@#(\0-R5aOJNpTU;I1c
:.or+XpJ@H8[fJ?qQM+IFi-qmo^PqU5Wn!Ljnln!9^U??Pn@9W$?p&WN&oh>H@>VqA.dQp"%
M;c16IiHr4___60P!,\<Y`3D+]AVqeugX@\N%Hj4T;uE95hUoW\c;lt-,0)\>lg9+o>;b#(,b
pS<J<'rLd/>JiJ%aktYG8joG4YVH&R0%t2+'MXWE"f=]Ah=&+OpEB_AhnE%(S@me2De`J.Akf
`Ndk80abr-$l&?D<<r4XJG.ae(OJ&i@)JXI.o[@5k+?B*_4gL=l2G+d(GdUDZ7K#-]AdC+T8=
Z^<EMAIL>>cg)q:8Z/\QaRA8-8b:en=EuRKp@VDde3GTk:<;O!HCjii,+CfIA`
\O4#.'-oGML)Hl>b3Gc-BRkdNQNa\$GSolaKn?hg"A"th.K]A(-R(`>5HM4)V%Q^-i2Mfie>T
Bh?qM!3%5$?>BG6XB_]A[4TjT>Y*[BMLq]A5IJ\:tsQJ')&a.KV?JsCY)0U^0[<-U1`#uOXfe%
_)[?^NrI[mY:"@V0ZTEG(W=!*(GX*i%6GX'?4.k&,iH[kAB`9jjn^fKB`^iN3^K#7-WQ=N_J
Q/i3NRUIjh0R%AVmZs8Z[>YBr;GLYY@gFfWAb#2rjK$ff/Y\Nq'_2%Jn8f?5V%b)LD+1K(#c
Cc)W&7e_=!2cp*MW_dU+8X=[5*I_)!a<TOBV<Y)HeMRGN5/@t87`25PO'F$)_F-cpT1]AB.P*
Z';4E<<NQR@Q=_LqHAW!pi0E73^EC_t,d>Tp[foA:'D&%iAXbbF!Rl`6F=doBi9B!cQ=(`8]A
q!@s(<ilr0jI6S)dm^u!`q&W,/10\:!&r03M;TFj$TTKu_,5ZY(N7L)m%MkVh8#q[=RM,paM
k#;B"kF^!:p?4D+5%f3Uo>lD/6'(FgV]A"548Y8F/G1'NQO-t_0gZqPf%3Mu7'LlGp<taoGb&
D4^I!MFWTl2nQL)ZdWW",-*+EXA#+n<UBTme8LWUn'?]An?_(-#AtbUm7645"Ou,j%H-DqWdn
fO<KXJ22"Qb%Qg4*j+h!!g_805ceZ"#,C+JIjXCY<VJ^]A[fDD8$C/j3VfUqRa5FMmMEk</TI
_"]AK?5k7.?9F;CQeqX8Y!:`DrmaI1qd/,DF,g+3S+85G]A74b.s*)_,r6r$tI6rto!k^^Xm!K
l-q-Q@lK*5s.rViY7:T)gYfI(\aEqXU]AaMuP%=W;;6'7TAcT@s#;iEP$pNR)sdKD020T)KW&
mDe>J*7S%/T+<hdVC2:%ZgmTL&Wn#f5B$S7rVrSY^C%8Xp"qFZ[32F!;Y^[,nb\Msn'?@So,
m:Ar39O=ip^U=BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\BAdF\s#OP+]A^5N
-ni&(N@4@$d!Q&1s+8DW0I-Zj%ZG[j@B"ODuS'[dYT7L?T7-F6Qrq&V;rG@09k[aM8Q[eo4!
!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="311"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="386" width="375" height="311"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 

setTimeout(function() {
	tabck(objTab);
}, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[分支机构查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB0' onclick=tabck('TAB0')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[单指标查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB1' onclick=tabck('TAB1')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB0')><div id='Font0' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB1')><div id='Font1' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
