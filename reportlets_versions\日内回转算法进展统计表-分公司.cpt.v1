<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[2941]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='3'
and branch_no not in ('2099','2098','9999','8103')
${if(len(s_fgs)==0,"","and up_branch_name='"+s_fgs+"'")}
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='2'
and branch_no <> '2097'
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="ksrq"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
<Parameter>
<Attributes name="jsrq"/>
<O>
<![CDATA[2024-07-12]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[宁德分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098')),

tmp_khmx_1 as (
select up_branch_no,up_branch_name,branch_no,simple_name,client_id,client_name,khbq 
from ads.ads_hfbi_rnhzsfkhmx 
where ds between '${ksrq}' and '${jsrq}' 
group by up_branch_no,up_branch_name,branch_no,simple_name,client_id,client_name,khbq 
),
tmp_client as ( select b.up_branch_no,b.up_branch_name,b.branch_no,b.simple_name,a.client_id,a.client_name,a.organ_flag from cdm.dim_client_df a left join cdm.dim_branch_simple b on a.branch_no = b.branch_no where ds = '${jsrq}' ),
tmp_khmx as ( 
select b.up_branch_no,b.up_branch_name,b.branch_no,b.simple_name,a.client_id,a.client_name,a.khbq 
from tmp_khmx_1 a 
join tmp_client b 
on a.client_id = b.client_id 
group by b.up_branch_no,b.up_branch_name,b.branch_no,b.simple_name,a.client_id,a.client_name,a.khbq 
),
tmp_khs as ( 
select up_branch_no,up_branch_name,
        sum(case when khbq = 'T0算法线上申请客户数' then 1 else 0 end ) t0sfxssqkhs,
        sum(case when khbq = '已开通T0算法权限-个人客户数' then 1 else 0 end ) yktt0sfqy_grkhs,
        sum(case when khbq = '已交易T0算法-个人客户数' then 1 else 0 end ) yjyt0sf_grkhs,
        sum(case when khbq = '已开通T0算法权限客户总数' then 1 else 0 end ) yktt0sfqy_khzs,
        sum(case when khbq = '已交易T0算法客户总数' then 1 else 0 end ) yjyt0sf_khzs
from tmp_khmx  
group by up_branch_no,up_branch_name 
),
tmp_jyl_gr as ( 
select b.up_branch_no,b.up_branch_name,sum(nvl(jyl,0)) jyl_gr,sum(nvl(jyj,0)) jyj_gr
from ads.ads_hfbi_znsfaijykh a 
join tmp_client b  on a.client_id = b.client_id 
where a.ds  between '${ksrq}' and '${jsrq}' 
and zhjjylx ='普通账户-T0算法'  
and b.organ_flag = '0'      -- 个人客户
and jyl>0 
group by b.up_branch_no,b.up_branch_name
),
tmp_yj_gr as (
select b.up_branch_no,b.up_branch_name,sum(nvl(yj,0)) yj_gr
from cdm.dws_evt_t0sfjyljyj_1d a 
join tmp_client b  on a.client_id = b.client_id
where a.ds between '${ksrq}' and '${jsrq}' 
and b.organ_flag = '0'      -- 个人客户
group by b.up_branch_no,b.up_branch_name
),
tmp_jyl as ( 
select b.up_branch_no,b.up_branch_name,sum(nvl(jyl,0)) jyl,sum(nvl(jyj,0)) jyj
from ads.ads_hfbi_znsfaijykh a 
join tmp_client b  on a.client_id = b.client_id 
where a.ds  between '${ksrq}' and '${jsrq}' 
and zhjjylx ='普通账户-T0算法'  
--and b.organ_flag = '0'      -- 个人客户
group by b.up_branch_no,b.up_branch_name
),
tmp_yj as (
select b.up_branch_no,b.up_branch_name,sum(nvl(yj,0)) yj
from cdm.dws_evt_t0sfjyljyj_1d a 
join tmp_client b  on a.client_id = b.client_id
where a.ds between '${ksrq}' and '${jsrq}' 
group by b.up_branch_no,b.up_branch_name
)
select a.up_branch_no,a.up_branch_name,
        a.t0sfxssqkhs,a.yktt0sfqy_grkhs,a.yjyt0sf_grkhs,nvl(b.jyl_gr,0),nvl(d.yj_gr,0),a.yktt0sfqy_khzs,a.yjyt0sf_khzs,nvl(c.jyl,0),nvl(e.yj,0)
from tmp_khs a 
left join tmp_jyl_gr b on a.up_branch_no = b.up_branch_no 
left join tmp_jyl c on a.up_branch_no = c.up_branch_no
left join tmp_yj_gr d on a.up_branch_no = d.up_branch_no 
left join tmp_yj e on  a.up_branch_no = e.up_branch_no 
where 1=1
${if(len(s_fgs)==0,"","and a.up_branch_name='"+s_fgs+"'")}
${if(fine_username=='admin',"","and a.up_branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="ksrq"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
<Parameter>
<Attributes name="jsrq"/>
<O>
<![CDATA[2024-07-12]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[湖南分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with
TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098')),
tmp_client as ( select b.up_branch_no,b.up_branch_name,b.branch_no,b.simple_name,a.client_id,a.client_name,a.organ_flag from cdm.dim_client_df a left join cdm.dim_branch_simple b on a.branch_no = b.branch_no where ds = '${jsrq}' ),
tmp_kh as ( 
select client_id 
from ( 
        select a.client_id,count(1) sl
        from ads.ads_hfbi_znsfaijykh a 
        join tmp_client b  on a.client_id = b.client_id 
        where a.ds  between '${ksrq}' and '${jsrq}' 
        and zhjjylx ='普通账户-T0算法'  
        and b.organ_flag = '0'      -- 个人客户
        and jyl>0 
        group by a.client_id 
) a 
where sl >= 5 
)
select b.up_branch_no,b.up_branch_name,count(distinct a.client_id) khs 
from tmp_kh a join tmp_client b on a.client_id = b.client_id  
where 1=1
${if(len(s_fgs)==0,"","and b.up_branch_name='"+s_fgs+"'")}
${if(fine_username=='admin',"","and b.up_branch_no in
(select * from TMP)")}
group by b.up_branch_no,b.up_branch_name]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds3" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="ksrq"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
<Parameter>
<Attributes name="jsrq"/>
<O>
<![CDATA[2024-07-12]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[宁德分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with
TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098')),
tmp_client as ( select b.up_branch_no,b.up_branch_name,b.branch_no,b.simple_name,a.client_id,a.client_name,a.organ_flag from cdm.dim_client_df a left join cdm.dim_branch_simple b on a.branch_no = b.branch_no where ds = '${jsrq}' ),
tmp_kh as ( 
select client_id 
from ( 
        select a.client_id,count(1) sl
        from ads.ads_hfbi_znsfaijykh a 
        join tmp_client b  on a.client_id = b.client_id 
        where a.ds  between '${ksrq}' and '${jsrq}' 
        and zhjjylx ='普通账户-T0算法'  
        --and b.organ_flag = '0'      -- 个人客户
        and jyl>0 
        group by a.client_id 
) a 
where sl >= 5 
)
select b.up_branch_no,b.up_branch_name,count(distinct a.client_id) khs 
from tmp_kh a join tmp_client b on a.client_id = b.client_id  
where 1=1
${if(len(s_fgs)==0,"","and b.up_branch_name='"+s_fgs+"'")}
${if(fine_username=='admin',"","and b.up_branch_no in
(select * from TMP)")}
group by b.up_branch_no,b.up_branch_name]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_First')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Previous')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_ReportServerP_Next')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Last')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[export]]></IconName>
<ExtraButton ButtonName="Word-plugin-export-pdf">
<Buttons Word-plugin-export-pdf="true"/>
</ExtraButton>
<ExtraButton ButtonName="Word-plugin-export">
<Buttons Word-plugin-export="true"/>
</ExtraButton>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="false" showAsImage="false" autoScale="false" tdHeavy="false" pageFixedRow="false" pageFixedRowCount="30"/>
</WebPageContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="2"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1981200,1943100,2095500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4838700,6286500,6477000,5791200,6667500,6400800,6096000,6553200,6210300,6248400,5676900,5372100,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="2" s="0">
<O>
<![CDATA[日内回转算法进展统计表-分公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" rs="2" s="2">
<O>
<![CDATA[一级分公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="6" s="2">
<O>
<![CDATA[T0算法业务-个人客户进展]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" cs="5" s="2">
<O>
<![CDATA[T0算法业务-整体进展]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="2">
<O>
<![CDATA[T0算法线上申请客户数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<O>
<![CDATA[已开通T0算法权限-个人客户数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<O>
<![CDATA[已交易T0算法-个人客户数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="2">
<O>
<![CDATA[T0算法有效交易≥5天-个人客户数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="2">
<O>
<![CDATA[T0算法交易量-个人客户]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="2">
<O>
<![CDATA[T0算法交易佣金-个人客户]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="2">
<O>
<![CDATA[已开通T0算法权限-客户总数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="2">
<O>
<![CDATA[已交易T0算法-客户总数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="2">
<O>
<![CDATA[T0算法有效交易≥5天客户总数]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="2">
<O>
<![CDATA[T0算法交易量]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="2">
<O>
<![CDATA[T0算法交易佣金]]></O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="up_branch_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="t0sfxssqkhs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="yktt0sfqy_grkhs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="yjyt0sf_grkhs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="ds2" columnName="khs"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[up_branch_no]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="ds1" columnName="up_branch_no"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=null]]></Attributes>
</O>
</Compare>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[0]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="nvl(b.jyl_gr, 0)"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="nvl(d.yj_gr, 0)"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="yktt0sfqy_khzs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="yjyt0sf_khzs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="ds3" columnName="khs"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[up_branch_no]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="ds1" columnName="up_branch_no"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NULL]]></Attributes>
</O>
</Compare>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[0]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="3" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="nvl(c.jyl, 0)"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="3" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="nvl(e.yj, 0)"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="5">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="11" s="7">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="12" s="7">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="158400000" height="288000000"/>
<Margin top="0" left="0" bottom="0" right="0"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="18"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="jsrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="142" y="71" width="98" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labeljsrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[结束日期:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="62" y="71" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="ksrq"/>
<LabelName name="ksrq:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="142" y="32" width="98" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelksrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[开始日期:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="62" y="32" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="formSubmit0"/>
<WidgetID widgetID="a8566181-2d83-4556-9431-90b8ed51e695"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="745" y="43" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_fgs"/>
<LabelName name="分公司:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="branch_name" viName="branch_name"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="410" y="35" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_fgs"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[分公司:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" noWrap="true" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="330" y="35" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="ksrq"/>
<Widget widgetName="s_fgs"/>
<Widget widgetName="formSubmit0"/>
<Widget widgetName="jsrq"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="1102"/>
<NameTagModified>
<TagModified tag="ksrq" modified="true"/>
<TagModified tag="s_fgs" modified="true"/>
<TagModified tag="jsrq" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="ksrq" tag="ksrq:"/>
<NameTag name="s_fgs" tag="分公司:"/>
<NameTag name="jsrq" tag="jsrq:"/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.report.mobile.EmptyMobileParamStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="1102" height="165"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="112"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="SimSun" style="0" size="88"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="true">
<color>
<FineColor color="-13421799" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[=sql("hfzj","select emp_no||emp_name from ggzb.hfqq_ygxx where emp_no="+$fine_username,1,1)]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds3" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="c7fdf75d-b811-4a56-b632-79462b528b40"/>
</TemplateIdAttMark>
</WorkBook>
