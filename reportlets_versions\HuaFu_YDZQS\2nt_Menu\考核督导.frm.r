<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.parent.document.getElementById('KHDD').style.margin = '0px';
const elements = window.parent.document.querySelectorAll('#KHDD *');
for (let i = 0; i < elements.length; i++) {
	elements[i]A.style.width = '100%';
}

window.bakdis = function() {
	var u = navigator.userAgent;
	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	var isiOS = !!u.match(/\(i[^;]A+;( U;)? CPU.+Mac OS X/); //ios终端
	if (isAndroid) {
		var parentDiv = document.getElementsByClassName('css-1dbjc4n r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';

	}
	if (isiOS) {
		var parentDiv = document.getElementsByClassName('css-mbp0r r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
	} 
}
 ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="d126d6a1-7422-49ee-9d87-45f784368fb4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="11" s="2">
<O>
<![CDATA[tgyw_khfx_zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="新增高净值客户数(户)得分排名"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($ds,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r/pPM,s)ZcahBf7HTt<tn\Y?&B>]A,!ELM:_8__&<NP!"4@TX>%1VL6<b024YB8Q6u(pgUa
[l>dS#f1!<NQN,U!Ich>hokgiqn/B7=E@J,Ep"9TnS/kBtQrcM@0ZiiZISn,))-%fcT;hrRM
.Fo]A<1nE0He]A>Zq+RA+!f_"@/.dgHZ[ZBhB%Ifg.>!3J6V%VaFI[T:>YRMfS:$YtMj^kFXuj
'bCUlH9YX1,Yh8YHTmA)G]ALIg>OJWWT'uUMM<JgYg4Efl+=E[IX5u4h:k<)C;0jg>n.K:,\>
p`/J4d,&na7mc>VZ\UL>K&U54f^M,AA4:d;EB>mk6RkK]AeJ!3[m]AM;J@7Wu9@]A7Bs,&QVs*b
hZ*X\f<8%p5QK^l$75*mq9O9Bf-_V$'"`E:p-1NtKR2GRCWKe!dskses.<HnbPV)/mPJPEK*
RmdK_c2\7D#*H<onEin(T$@2Fu\4@ZqG_4BP%R);;[7kdGm-1PKr\Z$+hfXRs>]A#Lb8&EWL^
^*baOn#jT!f2kQ`krn&T9c?,l::AYUT)gjtPgh!lN7=qM]AB&fZ(-M>uGeD)3#jsFJI[X!'Ml
b)=[bC$APYG.Jf'fb#+@uoS//0l+]Ajqi-(0[W0c*=`B)H4>`EDK%ZY8BO"G2:>\iKRC@8oWk
[TWF;;9BRqCpHK/o//hAN?MT&dQ1q2t/E2drb)XQCg08\Gq:N@(#@"cM(r\FEpd7+V1.>q`X
@Rhc?R=-g2\c-E_GQE-ma-$sE9]AtMBgopah`X%kN3h.s"Psp(_mH-g`Jh[o\-KAPg%GIPp$M
cI\'eoLH:F#`md0l7XG<CTA-gfI#No`4>3&#X=#35KMJMB5#@eBoEY6cV%O4edXABroY1a?`
#XPV]AGCV\+Fb!89K.,^^Kep3&R)g4St]AW?5-"saJ*P@?M,c&]A\8i?'[CZ35ul<"TWg]A$gePo
#3I.bqM8)d)Wr=<\+ar"8DFL.LhNA`>0"@0eh=6b(?P>r-H_CNS.(MJ^(rXIG(m1g;ifM!J!
DWJ^'FLW:&4A@E[[u))@8KVBRGAZTnH:Q;a<BlT8"I?Kb1"C#4YVcLjc<#[',tN-N&ao0omT
<:u94q3F2A;VGeQT&.IZS"(hXNSEif9'YCr=*e1"5&u^BICHX2d"Eo!Oc.24WK0Mt=5Z8@n9
O7$>.Y(:PTdg!AGpQ^%`E%Im]A#31N&7a"CT<k?I$'T271PSD&*E^75'pARiFS4_fuDL*-ZT7
E`^3>8RcWm3VBD`Oa;uL>U);o[C]A.Psb;CNP3R!GEVLn$2BBJbp)=2"Xd@QW"otDnGWQTud?
D.`256'fp>5;'</gDTq4]AZePE2^@91Yl.\(3`ieoqZ\0,S"E[KjZ@I,GA[cW`$iP<uF\nLl(
M>#I7?HNEU"h3+$0=Z=lDs"]AY7lT9?$ngVfbPkjfAPJsW0$&>N&9,Dn[.Q<TF/f?XR,k5l)j
F'imm8q;aQ57^9'9F8^Y!b_:q(Y[#E$uG4>\>;F+)+U$_HbSb:%HlUO27td#<:uAS`1kAr"0
!Td\4;^26/dWfb0aMD4]A$=`E-3RO3&(KDBqj7_Os$W]AB[+P.0NHR2.eAC;kR;2;.H2HPXl:F
W="ZJ*3ZCI%$N,e?[H*]A$%hGVP-CFZ)VJAenNA8V-WcNn?NQ&/,J'Yf3-I6<QL_qqT7_2&4E
qY`eO&`Kt62199f0]AGurX!9X,MNZNTSWP.:4.Lp>?_#K(^Zug.\<csEhNjZ@*k_2O-&jbHur
Jt86IS6pUt[EM!mZ(eljY.\2E(Vi;o34[hIo?RWl'@M:IrJlTf?0+b?;G2UGuF1(bsGG4;Ci
/ZY/LG:/t'q\Z=+6ZXN)Gn(^>-131j(M9CAi4p^t2Mc!lWc.WCb\UUsO@Z&iXtQHdja?Nq'1
[O)`YUs=:\=5/O?s9eV&B5CO&lJ&$=ano5h3]AP[jsI;[JPHojtJY]AX.%ZEgB2\f8AGNgjt$`
]AUEBlp0aD,f]A't<TW5GMorKmg89SSAZ)l3[DkntIbP%$0Z\MQcDKYd]Ab7P\P@&b!._;k?5BB
i<Rl9^M=8`qCsJqLJ?@+N&g.1'aGJDn2>oR[nUeffC(WAs^=@[Mc"OoOL[JefTIUXchS-B=-
Imo!Y&@.QH$bXCL&AHREj;M8S]A74dph#U6"LRLF2S3q@WUX%PeO1.db(6h9<0,bb$g!nHR9$
p')_Im52Z?]AcUJ\lLS/R>p(<Nbj=0&2=XW;p/E7l+7b`42d('aZ:;3E?-\=k_gb`u6GXnFGa
,a*9Ls(UP_!kVQ5B'\#h'XsEoOX*qNl]AfirGEYK>l0h'M<L6i-9D7H81.U4R>AU%YnAte5TB
]ACq8m_i@<m_!_I%S[s)o\65VL!;,+EGJbLcpQTcII<O#Kt6V6<$DKVdM$K!LT[.9PO:SjN4J
[gP=X/*3&L1TZQ$A*gSQjc&4;T5%K%:VkPF=,-J[M1u(SWHIpZ1(=E[`a>(C_I^-6]Ab+L=1E
i;.ojhWmM7Ff,7#V%M>DRddo#btb^4dU?;n79MZP!%R6PVIOY0,dg:eMgk%J2bj/_1Y7Ws+4
aDtY`.(2.23fSXLN;MVWdDW3[e7BI=gqlSjX>V6Jk9XmX2RBW=l@e5R<R":8rNQ[a\;+^$b5
:(6:^KIgfR1XNcaq<e[)C^5Rk>,MYjA@nlu0KMOO<-:n(1B$R"66kp_^[B+I3l1+!G$Ocl#u
@oVO*=(-Cfqfco9_r]A5qMWrFVJKssfP:6&-i%V.RO4V_0tg6Mn.geaOhM9&[CjN!]A^IKZ&49
>/=[/eNr5C<8tnI5Y?K6#si)pb!mRk;C.+<7G-=m#qF'@YU5m4s;gA_66oc+Gl*`'8b:>*#)
D2NXuGoc=#l$F*%J:\EObo`O!TBiAO&G+Ra+?\p1TH3h27k6H-YFAq,%un>'@_#Vkb2jaa-n
o\Ei7c!P5)]A!JC#DX#DZEV_\aLi]A^JU"HJYOu@2Vl\Br54+V.]AkL[(o-R!)S4k`t^r/_,oO+
=L`8DlFQbZ.)@fN")^/$XO#VS,#Z8)SNU*K_nnpe;RVlRQ#BIHMqQcW(Z]A%_+EOeasYd/_V#
?BuGTjkQ8Wa?(,JG-Bm.jR1Z3kREi`=IdHa;#B^<,/@*,oAXl:G(%^[o*<!DTP'Gl8`\Mf5!
sbI+Rf9F\]ARG8"ZG5Fg8*e&udYR49SaBcfSggt%<q)RMa%p!%r[ab94@d>O,&'hIf)?X!-EX
0);o"X;Bi]AN+f<mY6DgmWH[JoK594qMRQT]A4]A6Fjb./m&RZ+>[DqCr]ASJ>g/Z_Y\S4$iAIuT
liB^m'=TVK^e1s:!u]ADKBL,IcbMSO\3^9)K@M"Z$^9[)r)+:NS([`Q.iN0"$iaP:fDauu'AW
g5tL<Ek.EWgFHM@mKo1GHG-nd-Q++`-dqID[0"85?8D0^2q9N4JX85tpCd5Ksck_<CLQRebg
3oOa,rrU!Fl$df>(o4MR4;]A6K%,TDIXB!gXQ,).\m5PX-^Wk1T25^T]A`77);3&%BrGX"H@Qo
&2nDG0kQo3_I;lV("5R4R6rSfCA?Fq93<[OKhD2XNTIDG#?'6]A4-Os%98L/!^hoQ2Y!^/nl8
^H=rq_Z;Q2%!R<BkIP+]Ar7[j&DW3&%Q?q1FokAWh!m-.U8iZp<cGWagLkr!&s2a\.i_'Jpej
[C=`]AlHLE@MI+J)=C0V"Cj]A7J!og.'34aYMbI3b97ns>1#*od"MaL9/TpM;3Pf:G7Pk#t.jI
/+";hg-N^3<1.le=Iam[1(g&C!?ZJ<JpD/Oj:L`M1#j-CX_be,sW;&2ofdo-]A7lb1%L?$KG\
<i*YLDj=7UJWkY)kOcQ6&/';\*P$<^2CZ3`IQE#`fZR;_E=a,@jQ4`co[<<L_]AJ^Bol\RnXY
7p$CV8@%1]ApPlL\8[lhJ5TnJg0H2`_-C#))EJqmY9<b;OCSWKGM\i8CR7>V)mWuq6Zi7()FB
E@?1=^T*-"mI)TYdn_Sd=W=1sNs]AYn-\Ha^hbY2AI-3?<>]AN&B<4lScl3,(HCAl;,q+;qJ@k
nM2s^_;a6=Aca'Q^g)A<daC//>\/:C-Lu1N6t\2haH=ab+4Xb<O^'^UZ5GH_dJgMpHL-I*6q
en]A.J=o:=YdUV0--_tRA%EpM$XloL"#C"42!>\k?j#'V/fO5&cdIKj[l!I!,fM"d/k,%QrtH
ERD=0rm7BTm6:[l3q!5@GjZJBe2LEHl^Xq&Dml%_5lQQ?0GbaW*D`olOOP*^$2rJ;sAe^X,N
d%Shf(AA[q"+bk>\Rl(<Q<p$:mg^`4ZkEZ/%h."\+P^55CGT^d?n75E*-@^B&j$l7peiih:A
<HYDis^-;GsjRRdX[iEQ8dc67VT,HP]AY\pb_H,HK6[[<Y`D1l=L/JpDI@GN,K;IjRGd!4ZC/
%$oA5QY7H+2)m'nC%jB*;.[[H\N0]A=Eg/c9;/q>,5IYjrS'E43U:n52J\"iQAk:s1bbdcsC@
KB0#eAg;#UWk_qL/G,HaiC&H$(_Q@\-*b+5='tX8F@BrgW5XLu90PcN!`3.'ZiY:6b!I*C$:
m(Ea0=TKQ'r5W6s,+b)[en.T/6oEG's-F9,QIad&5UV]A^.ccG"_l[4p%4FHY=JHQc'WqYm]A,
V!bp:q+l^YSAN?<[2btSuGp;?b@I\`O!)_:?$7LgZ+O7?rJlKW0CQ+.\k8bj-@b6FKdFCcJb
)t>6Uc"DCDNtRG'Dp8'c?XCLkr?//3i:\.O]AhW#RaJ\AXb[rjW1jj@`9UWMe0K#^$1'P#\&%
H%M^(0hSqi&@'/UmXR!&%k%6Cq,u(P&peX9+]AX>VE.M$RDDkRV7WQ([DbT>'NV]Aj=iOjteJ_
t<"dO[,8UboJ>2LTqCH^fQ1YdTH1ki<0US;Hq+VMduX$>#VD9pLDWiX2PTO7McS.PP'G2nH9
bZa:%;]A4emZ0RRS`IGK[!j8-%-NNm))1nHd8?_tfb/O]Aj*gpO<Sko"s-h"?HB7$1AaO(4?(f
1c[)<mL/?Y22\iP/r$n^/@[4A)?.)opViskTV]A(&]AlD*CYs'L-Z-'5q=B[.n0sN<iD]A[G>ep
E^UsZ0qPP[EoC:1Q*IW,dC4mR"[Y)^/*j68js<QH]Ag9h&ed3^mO-AkqUH_i>=3o4-agi?X7R
?K@?Vg-j"eGQ'U*V<kE>eP2Q8Q32Mg8V_g_]Atld*+$EWlJ2500_-NW((WKWCoF0LP9/6OsX-
Y#2=<@mAp<]A#riDB=?5(!u-UC.U<bmG<nGGr=4S;e@mm>[7[6*iKCot6$s*OiVcVLuY8[HQ"
\9h-t1#=/]AGI\[!F(ggX4@WN0YV#9;o#<c&q/COD!IKd9tj)P9lcJ'eIfTC#Jo_PWj4Pj/NT
E:7M%<sf%rK%8Ko9CY",8\_TCm94NEg7[!d:kJG-a+$C*/^^_8<*$II21&o2N5["RFU^mMiF
8^e&iRB+eCrWJbMgeB6s9Lk&0_B8qf`4<aZYpL@i4X,*Gjt$NVVm1B[B[aNO>7lQ49M4A9pQ
:Q/XMFr'_\o^GLY6b#2*qgYPrF+X#7@P)d;otkWIctN>aNBY9F3%:V/?U=BF1YU6-G&s&[lf
&f*2f`)nG'D'co*ui#6CR(X<eXsFNZg"<YKE:IVK'.&)-"XU::4NSo^(p="aIV^0tc@%g00Q
g89rlu(Sfo;5?HC32B_TDJj-V`ZR]AK.+.*nQh@YM%-MCQ*"nHk4>qb\i9D2H&aPmiG@_%>C:
.KbuMaBfhff'^tWqt_8n3GQY%"u4?NCSgWK51a\qSeE;q"t`*)Bm3QiU*feD9YA:[DEoKb/H
@?K0<Y\&b4\NE6<E):X#0&Gap62%l$UC>fSsl$`q\'M\==ZX9EBQb!8>9nrKZOghsb?(O?(i
C@u@B4cmX$Vn]AIm$4Ks!e\LO#;oh7bIA?[kQ*KkirC7-%CW0Um%R.T4]AEKDFFkE7da*o&aUR
5t9CDJ.V.m.WXF@J5/B:F2UZ1R'ml\;$Vhf6E5Xjd/(>9_Zu]AM:HT4*$.&KCGFTGI^`k83TV
dSd^48cn?FjEZD+jG-"b,6X5Y`E,DJ_1%CHiXgn'NLYEAt2E'oYd&S(Z0-l1_p$m+SlB7JiB
^M'*YV()#f1>\%gC\>*:Y7@/B=#pUkh!`'$GJ0H?B#_c=`t1oV2_!U8bllo8H_,%@@T<p;T]A
<X"aSkJbl(,*gM>Ke=UI`LH]AKj!T)2#Ec[Bh%Kd[>>0[qiC3.9=FaWe?OH7kmjeW<!b<3.3&
@cYo(?,i3nN/uLY@I!X:U/TP=W;osUfe?MWL:ulM!A;m%q>q&g,Ujbh9Ro=&4AuIENHt7B5)
6htSaPBNaS<-?8"q04ocVt^Zb#qa]AQu_&Fd9plTJ9s9Dk"a%GRFbVbg)E/=^SNk;mH$PX$cV
PBjI`XZ^\`<fc$=$CI+%iTK=AkO',uoHisE7q!</$'aKuVAQa<n5<Bil!,arGdL!F!0Y?Dc*
dF(P9>f^[=6oXj[5iBsH3IU=0%]A0^)mF)R\U%h@Ql&p-]A;ie&mc/"b\C8RfZo!&1qDAPA.#:
cB1"*-,-PX)QO5D;)c.!Rh0>WBpBD.@a?@*IYPl[r5(o2jE%PcQ>%1R>Q:0=CS$=LGshEEjt
QX&H$EJQ]AgnfTfq:E<fF,/mm\\lNM@?jQY#5.cqd]Al7:Vj&+fl>?D+<lN9Z:e&!LB;+`d=Uu
@R\c+J?)?0m"!789oMeXHKjU,Pi">kZPXEYr)97[n'Le`s(DHX)]A.6_h^UEt-$$CA<_&g@1p
YH@t(ne'h1)Q@BZ^Phb1-?Y6U7gu'sE7PZ"H0:-O7+>d4j_kj5;qu99Ig-<f,Z)J+p`"G0!+
,H%8IeY`J!W1H*\:ir<XV5:b<SOIY5&*s.`-X:qf#@%sdkTudJW$Dp.<L<n2.!nOA)B`%"L_
DMXFqX!d5jdp^PMidbF/*h`%:,fPO%m2)heII>VX[=/4)(@Z.nlFm.ET]A\#8+lA<j7:>*b<H
,q4bOb#,]AunU0@N<Cue<YtiK>>1LYb7^THDPO^b@_+a1Dg5m.%SAaT6Y<MPf;7B2":jh;&&q
.+o:jNnWEYRYH*R]A6!g'=[@(?eEK@%pk(QNpcqO'Ul;1L0[#b%$SXGpZ&_7;5!@agoD1$SMb
i`S5*BE;aW#cW.#sIJI8.a"*-8$W?pF':+]ARVn9V*<h?i'[GbL<7-hbg-mKVB>uA*#SS,'_o
X9+;HGSTGr]A?@XhX-8Bi)n".=rZNAI#.gbfC?]AN#Le+>XMc@ZQa2"7BC2lKKNMj:bG$q18UR
UZ?CbHp0DeTk1^*?i'd_SJLf(8af=fNsQ0-S$#4EW*7d`"`rlttZbGV%r.U]AQrZr[@!L$5M8
X#;Hk&=ep6KL#8:*.n.;Be1r_RG5Ab3*;4BWuU5gptHM:C"jY7[(R"LnL-mcH[]AlR1_4i$[N
>$;Igh1e%R:?f`o)]ARLn:5O?R_QA2O*Z1@Jc+G#>WMK:o7kbfG=^"QZ)ji4)kE%CR<s-Y$O)
(]AQHpA=ugK'[gg<RLc0ZGA9-Q#`p3(aZgHX<'t.%#G,W^>5.K+XOlf,58NQG1L2CC!IC*r[M
+Agl.!s.96_Jmaok3/\HrUt2B:&$(c(.4Enq'"1EW3*dS%n%sOm$hi]A$Ob7bZM2t3RL$07G[
h"XSoL-2;K54lY;$YGhPMNf1.'#4]A;o5[m`$,[_;9nn*RIm8VSX)"^QX3:@=PSlt8?>J\-C/
rL3W+MrO^+[^/]AukS0CmB?Z1DI)AMh/W3L0rD-^=*oJQDej<$5>SZXtN*STq?(lJ+QEl<;A.
8qs`:ucDopFli9t9]A&A=uB5![j\E/l(8XX,e5s[WiY]Al)Ts[kY@4OZ.,^8@84f/C9gWhTX5_
I4F;oOas8kBU4[3\lag5$RCDQe&/G@J7(4*j'R8P$,;&%X;\igr'hb-artD_>M<UBJ@q7#q-
uK"S*eSQ+NFIBb[r[:J@uZXS5r'hR8kNun-P/#QMckK#[flY/NlFXIeHJ9;DR$4ujF3*MbF%
)e9"NcS]App+9+Z&dTP\Ob+CD'96M[uN?k'q-*<I-WuYp@)?HS5!^\Q9Fg@c*Ijm-ts<hN&06
>jZ@A5X%#$-fpdN.2^46=g%?0o[?P+]AsFSd?LCO*=gqLb.LgO"7C!EYLDNje3g+9C@+jLY+@
TGVSXXhr3-jpoYsr]A,(6G'5Y+.e45,uD]A8WfKOo`\2#JVc+G'lf8SO)ai3]A/+MO'$@@\VnX*
NE'J4]A#Gdq.[Lr_F,Od>*("o,k8mUt1eb+9g`#I^;BfkYSnhe82;tYOg:k*(PBIG/8"Qnt5D
rqn]A&nI]A^.7`_Rice&,+"lDp3/&q6<SD@]A1^LIB]A`)Hu>(29;'/.a;(BR\:Z-t5pb"N@J\U!
=6^K)26q05\M.QT4#mI[b0!LD:2-4<aU?=-K'cTS&+d?2)Q_!&Y2h1`FipXN/2?&kZj:\VuR
5-g*4h?X1N>tr`K_A'3(bFl2=6\uG^Q!L#3A&K'N`-t\P^QROGO7lteaeO_/1o[ch;IQ*E]A'
3`qM=-*:blP0AK4L0*7SC^"BpK7W(=LVPe2<P4`a*Q=QfaM1^#=L`-d0Rci_8grm&B;aOa8B
rk,aY]Aq.2@l[Q!)C)HUYf=&j]AF(pJ%DP"4APKNZPQqK`=$[(Y>d+=q:/.*8YR_$X'V(\_pN"
1&#*\`XX<Y*V-f1:;j44rOqscn&r3=hT(Y+q6Wg1%m!-(q*j44o7DRQp36H#aX$r97[aGofT
VY4l^C[a_XPYIcQJ.kS,cH81sCNHMYDHki\MA?h_XOJJS#Rq`Jon-Fg*lOGGnlZLGA`_j5kR
)@[W'1Gb(^;BPkm1#(KLHGTV$./]AmaFp]AiSS.pH:qHn0eBh.&FTr=mnr4e!qAeedPD>hJDC)
@pkY[ceg><FPpMZHEFnJdtj;?qR%7X#S&R'&e>#E2HLFLk?6%=q<W?L.)O%]A&]AbW.E5TD8GD
Bdeg_VT`T"Nh+9rPUf8If8P;\DC>'@Bj'?M'_4#g_fWqL]AUoqq@R@TP+N$IhGjij@`HV>gT?
%l61V/G^+=4gt:OP]A^=8`MWO3Eh*Ke_nX*$Q?Jod(?ftPncc<jhtYmNQ0klX;j*&6X9:=:DK
VJi1Ci?ZlAF&9fY5$fK<LrF36WBCg4-8<dM:m'dd8=n5Q>*2n^RM1<!W@2[iLL&b\fjX6BDX
:I8M!_j8gKlT_.V>a5([l%1fGF]A#H]AqRpKB/Xbk[E`.e:-L?*K2*Ajh7g->ZlM'$b#UYW9N>
V*RQPdp5C3Itb_ae^KKpID$5uAt'XFVL>9rBr#3pQZ6-7_Gh\gUIhcg1U3G_N*,J>hE9WQRW
MF"`pd@&AeE;-:+FX`pX2;O>?8g6f*2i8^"a-u&#D=))Xm#bp^TNnU"a?Y`2cSV>rJ%?4cE;
E<?+ZufOOfVf6tMSN"8flHCcD_!p9K,.kdF8EkQ]AYtLbh-;E0nC-@@4;._iA*[\Y!,uXFkQ&
EaXY^FdYhP$N!'p)*HXFR:LE1aIhnaO+*IJ+L_0>AhHjAQFGOQW##N*At8+?g.;UY(uRuP+^
GklOfci50&]AF!H/IqfpHHF>^.?`7i<qWk9*h^EA,qsa=7AXBhH]An>aG46R0cKC5BokRP1Vm[
uZ*%_@;\TKD?I\qBFD46R1N^DT0[hjTmZ;FC0RB'V5)g9gdeRh,N0~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="85" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('ABS01');
ment.style.background='white';
ment.style.marginTop='15px';
ment.style.borderRadius='12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="ABS01"/>
<WidgetID widgetID="9652d9c8-6f58-4304-94a8-a5f888bb9cd9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="comboBox0"/>
<WidgetID widgetID="ef34d585-ccc4-4f39-af66-2866dd24756b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="true" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="Arial" style="0" size="120"/>
</LabelFont>
<ValueFont>
<FRFont name="Arial" style="0" size="120">
<foreground>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</ValueFont>
<controlStyle borderType="1" borderRadius="2.0" isCustomWidth="false" isFloatWidthFollow="false"/>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<borderColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<widgetValue/>
</InnerWidget>
<BoundsAttr x="148" y="17" width="212" height="37"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0"/>
<WidgetID widgetID="f65f5669-1627-49ae-ac91-43da3f3480ba"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="label0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[财富矩阵考核：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="96"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="6" y="17" width="142" height="37"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="label0"/>
<Widget widgetName="comboBox0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="29" width="375" height="56"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[382385,1143000,199505,1333500,199505,381000,952500,952500,952500,952500,1143000,381000,199505,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,762000,762000,381000,1752600,1181100,571500,3810000,1524000,990600,381000,381000,1230283,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="2" s="3">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" cs="3" s="3">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" cs="3" s="3">
<O>
<![CDATA[当前指标考核得分]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" cs="2" s="3">
<O>
<![CDATA[操作]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="2" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__413A4477FA5DC879A7062C04A560B7BD">
<IM>
<![CDATA[!>5b,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$/%m<5u`*!m@.sO>ZDZ^ca
GpuSCsI$h(A.#Ta;fW;1gSq6pi\[@*oMs:lA5@(srK*01KI$-JEQYU*!3I-u'$]AgF^#nF7-^
t]A:i,#"CHs%D.B;l,_?%R[.:*2d[@Z7rh"7Ep1;CnfpQ"2b^*(Ucd'hk\bD]AQr6ICLF3_=T,
pb!38P2mDP*Daf,q2DWb*j+'&?YR?hr$_M6G/=E\?=g!?BO./(ATZ&RM@J8PIJtN4MY"?1\g
R5ipVJ$ZhbEldC>l%3mZ_c6*+5ZJns1AIi^h]A7=Fs=m]Ag-8h:i1M)S.[)Q[h`E[F0!)X`0r!
WOm4Wh1sp!.t.<]A'r"f'LC?t)8t9i0E?E&N=jN0op.VaK[J6P7a_sN'E%JQ?1W1:n2NM>ifA
AOAM@lJ8[LNq[*!"cG)gnpD7AtG?J;h:dh!-D8%:Hih<FUl=T\38CXGcPZVI4R-`@>Gu<@pc
HE#Wb4c7Op3!<$ZJrM$?g_Kl/#Qq_J;O+QC6JhCrk.f'l",639@\"Q_"W&s'CV^-K(=dK096
s(@?!672X`;ko!*!4S'COiJ1!Sj3pWg5M%Ln]A@7!!_>uGEa)f\Nf;Io!=pN^\2g!k+P]Ade$^
^OH_%=8cb[FH@9HpqR;srcEbVXLmh:4[]AFSsM*-4*W]AKu`HMWuEO*Wg$W80V^M(F^&>$3Ud&
O2+1,9M(jp%SXSe/8)\)c]A(]APAnu(4!H*p&-$?S$SJN1a_4o6^;L!/NH$MBO&:sEZMN7,H=c
qo2%1[o**+p(^o@5[6f`284F'NXBflJ%>DSVBer0aX0=ZA8K\"k3e;e%Dq/ZJuaC>Gj7H7o>
d>:8@klekT:EgHuSBFNhoElVGdE*i[VmfQH*:``$d*iY(oY/:7K&)_=*\@4>JF#6R\X&O;ur
0cJp-H0.T<1mAXqB8YlC]Al:c,b^EOEn?K73(?m;;nn_!1VfN4(/R<sF[?HPa1M]A1:I)r$q-*
j.\H1!lh[nCYMJ6\'k_IU3V%FWtX;+bc?>t18LAX@O3,1e!3k0e\go(+FGh?eb.cslDYu[k+
i6rNOO25\f1cU-R-;3duQ[pAXebi#K%QK)j*5I"rEEB=k3,uXpED?TIhn#Dp8V$\@"DhrH;Y
47m!:DZ/!Sdp!o9?_1CCP,PHHLW-YP?0]AJf=d$O.dan]AlChga8n+*3e,Ml?&P3PB2$Yj$34r
bi6E8QE8hl<pQq8@/]Aj6$!aMV[_.=1ap!\5>SghK`nTf^dRnYQ95JYP/5^-^'!Gua)>X9$M'
M[uP4b8CHg/Y-H#D[=\;^(fE\>#dJm9`!XpP`mD*M,m\hCIaigeiUVe8O87&?Gc8B9fhlTf+
iegbZFa;lOM-c>0;oi,JB7iSAnb%KjR"a;oV+5m4HQ`OFS)c5P$>9&A+!9Z&_ca+C;G7!SGo
]A\iiZkWrM`_/2\gjYkEto9HU$ZHFIDSD1u6;N>@J/%opCL[-_MrBo83=M^[t)km#B&Pr*+>g
4kjS[?\H#gO@3TWd[PlP,t7o5ZZgE8bVZ!cFLo@YSBhgIuTL>+cH>\FKd\9f-*Vlbt_8nt-=
$:7N7R3'b(Y6bV#1\0Vq7BKk[F`2BW=$#XPnXukPZggBuNlGA,HA?Le)S87\";;jaHH,Hh1-
N=T+NDtj$$?nWIQm7p#`-uAQc?6g,$>]AoJ*nPmoEqWak'OM$Vmff$$qc<P!c:@JPJ(<^>q('
tEgUS+j%GqpS3$kGAj"IkF:*C(9*SKE4`MK.E^<i9DD$]A=EUGW6Z;_Yb:ee24:KBoB+&n4tc
17tGab8WtErjS;Xm6MnD)k,_P>Q%em=L!JLUG[UGqj*Scl0M,EUcWa^1Ri!0!JRE$K@PK.;K
,tIb3@M$c28X*N!?V`LYH(iVe5q+Gekj!m&Kmt<6QeIaU6s+B5'XZUXokFR]A6O1[.p^=T`qL
AVtHu\I@M?ab?=3jRXII8#I"UEB65,=@A*Dag4gBRIj&-;L,e#*W!7Ke?Nl"%AQH$-IC@"Il
`,\uU]A@fa@@dcb"t@^s@g$crfBHQd;26LtfYO6A`=8uP5,AFqai^o'SdJk)la#s[,c!o[4HB
J"ZuRL*CS52Ccm$F:cK$X#HK,T"Ynun-g3q.DRi,Z6e/55$c@FHl7@cH@?bdQWWGZUFi7S:=
bH<KHCXE,9)hG'<U5N?Y?(pk`?Z^?poPrE+_("n)T,*;GYCu?Zq,_5c%EYO'GD8/-\HpKSk7
Wa'Ec"8!oN7I<Sqn+$ij_(`VEJn!e>ij05"&h6>D1Jrd9_uD))ur0,Ci+4)JRhWMEAC@o.Z'
Z+"t.`n8:Ybh>I*a1sL*?H.q=+]Arr^5">1%Cac4B$&V?hCo<39=c7ABIZ%<@bd>J)_D0DL0o
#Cc[8FM>_Rg.>7i!tdR8faH$$ufoNJGr6!?/7>;gD&8X$4amd3j^YLmh?biV0`5]AfVcNN2@g
E[g<jI#S,G\MIZ1j&7!eK;gA^3b+!mY1%NmE[3ntUg:EqrHH`Z.1;\$DDjduBL#F4f+J,KB$
TJHYGG?E^PSFZc73=Cg?^dHb*o7%#'"G*?8@?)M>i4:9slJIi9b;!P?b?TKF462`K4L!8V2c
?]AYKS,D/LYfVB;O8U\.q5/sTge5\i3O's[mrN@[_nQHe-WXR-*Mr1j]A8!,gOtHC>_gJ*G''j
\.GbI=5Jl`0F'dRQEa4ARMGLDoSB2kRQ/uVH8;J/=GVm<8)Wqq_XAujWLYGRIe5%MZCCmOQF
1"[3F+?0u2rJ?R__XfdKaYJKl./u7JQbXZWA/PG5N%LJ'Lp^Q>s:HZ2UX<%^C9%&T7BQfM)I
tmA2^[1Z.M>oPMSB!&mkjMTa%kf!%1i_:-Wf=$2Ug>`sli^`Un_MQ'TUeod`"JB]ABc9ktY^"
*fD*D\BP#o"pNcdhiOK&j4di&"54?GneV9G)^KC0#C$M?l?a+)4m_LjO`-W1:4&9dJ-"UKJ7
F3;:+,3j=_'3od,U7lXu3R_M<>GNm=aDBg;#l(i#'8Ws#6W,J+ME"1gZG@nJn^467"8b1e(Y
Ei$_hJ6hB,n9$nuVQb!<3!uRkLq4Nt!B4,:RJ38"gK%!L-ibo=IHk`PY&X=`"1]Aj:dWKE_I1
=#KMYN-pgid8@:G"Z4S=JYs-E-0;nJBQktn&B]A9>pXAYr:_>9#=O*LOH4F4'#/W"533PYh[Q
ZL.g%;gFc+'<i7L;H91i*FP*Daf,q2FM^AJMT(Xr0kOK$_0!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__C6BB7A0212D9D7AF5B063C7AE49C5CB6">
<IM>
<![CDATA[!@eH,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$1UST5u`*!m@8N>*5rr=a0
?(oi`[U]AU@dFDJS5]AhOe>P7,=7[jVj1Em/V19,X&R5KD:M%N84AB(R)J^Smd0;rmDEfSgDBa
1AfY@oPR+9QH%]A(q+B>EiU4e[dB`c3Z+Ur.)*;Z5:?2OQ:kIYO'cJ=Vsg;iUKb^Ah;^\uF;N
fZ6>.8Fl?;k2h]AV`;XE:;nOEpp6r^gAV<?&9YpZb:_P!P?O\NbODPU16\^Qg`<?/T:u!(P:]A
6(kj)&nomsIJg%;=9T`rjrnLF0>LeLgei5BFm*&Y))I2jG0'eQp5$5D^3.KC7LA$lI-eLCP7
K8[Rbi8t/d]AFpJf!gWSO:K/DqS'NEBB8(.Di[8l8s&sXA&->l?gW`n<s,lI=5>4-;RSK4,"5
TMiq=eu44K-qblAg)(Yf/#"`OV[FSe!\GA?)XE>`4?Z_HQpr*>j9g]A&#eja0*_jf(B%;&HH$
ir?^)S!ZV5#P(5Su:>2BE"E98!XpO$P"5HN)&RX'ncS?J(/bCdeV',di!46PhB[mNOT\tL6H
kkQ[<>BPn0TNo"c^%Kgf>)_/:]AU]Ao&rOTuqq->t3!+'/`Zp5lin3pi!*h%GE>S'!(H-3mZPM
`,S./XRJ<RPHql9a8R64NB]A/a=%>V_ipO/0&aPpa01.2^24U%]A`)-V</"BSj)dZo!(VXoas)
2\1BeF2SmIcKoDL4'+)]AeN,[t0.X'r!N=8e.R4:jL]A+q)_+f(7+h5g^#N0l#m7?3Mb_4LJ*0
HNqE+D7bPEjKk0kjp^`Q8nHVr/8>Wrok#!*9\%Eb)"DD(!g5E;7YXRfq$\K1c3;HnKIV+uo?
O$)mo0"2HeQge*+O!-!1`MC/Dg'mq@GR8\Zs5mICm+5hSVE45r);EV:*'"Emm\%%td!3UtEB
hG&?;:emtiSZe/jeO,n!-_unbHB?3Ik/ed"I+,(pRncO_M_('Fm#GW@)3&_I1bD?$GdUa\5$
U`?81#^F$U\@QEumm_7HqE6"O<bC]AL3"^fK;VMRVe8^q(KC=qir;@=e;(.Zo;=WC2eVQ@O9G
+PqqP(+fZ.NG(e*!o-HK1LPofY:IVShcbPGWb$/5$2>%)nGg3BcmC;>]AT41<_tMef"P4'eb6
i'3e(k8:ee5K=5VjU<<WEZgY-!.l"t*!3rr@be(cetMmlA+?Z7V7$CM.*]A<<KB[V#o'!W9'Y
Q5_[Aj<@7N(P\Z8c\cg39X"(om)mUQB33#XLXYNp$hP->2*L5@d4:b^P3QHHZaa-LVWW9Mf,
$)&J%Wpb^dR4-(.@Li-_0UGXAU1l++(YGl=KbV&J3u,+eSR$=Tr']Ae*<)V!)'Xeqr!hK@s/?
DhIY!s@S#ZX]A&R$.BX-c\g`!_=[/N9ffKX0PPh%G&aoka8aLR8+C1Ep_:M6n07CP&r7W`8'k
rO9$aO"b@2R/5j]A8.U,IGJ3S_HN(N5[Hf0.m]AVNaS6XG##W\%<G(hDh']A<jG#=nN_\N\^UX4
pjPk?lYST%#OuNKJg.Vu.IXnt3]AjTeb3]A#[<;I"lPcQ-P%jnoIY0[jr/Cm/e=HO`M>YT,6aF
TLbGNkrj?9UEf/6&iUHqNgS^p"K4&gpOA@O4*EC]ApK.9&8/Pggj:CjOa'HCSkT;"%>/>e7ha
*#uuN47k65hM.[D&^50fL.=*:3XX#!G+\Z@U'2!Z,[-&e^;UfEqm4m%Ei,!\oXA50$i&75nD
:g`c1n:kTQH]A*Z'_(F%M.*NYEH_m*km2m%!"\7/"MQYJ:P-3`sR'X\05VgD`foapFJmU%W3Y
YbdcD7$k5`o)-cadC8:pjpX+\):?%Gh"Bp-`E`HpVC\Llp7cu;inJoj*kp`lOW%=RHPsqim*
g:!m-@YmUiH[@-ueai4\/Ja=e^'5,C]A7s&dbko*4RljGV9,'YtarF8AW,L]AK&1uj,YP9@O#2
/W3ceS"!iF]AA2;&ZdD^q[.+R^IPeA%8D(iIjHdn?a6OJ"RPS;M+oSmp&RkFTZr&t+-bF"?_[
DbA)"m:]A)"t>`RnZ]AC5Ft-HU<t.YC-;6a0>*+d:!91lsU9WN"aTh?/#rN_?hBrEo6sA@V?uO
@!>$`FNK0*;cS';OTJG8XH=u]A?ONm[LQN./(Okf]AT2?=$R59r,]A0,smeV)_F$R]Au`kq\C<r(
gP9@H%A=MgCL^Mqai("$o%'\2I0VLakn9[]ASVn1%_MW@M&=uA[j"@tV8LQSpd%6O=QR0bhs1
p`(of:h%=rG\_,5-#bG`p?8Oo.L?qLia3GNrpd?2?,I[S2]A,E4hTNV.Ce\DsAg'>B"M+I:Ja
2*@WsMS;i,b4BIDZk%2f[>n*c%2qq6BaOX=?!P&&:)C&@g';uMfo^O]AP]Ana8/7g]Aa`gj0[q8
/^=q-LG%YKFe7b$Pk,K'nVY>>o4]AR!Bbl_B7TRo4l`Z('HAoa=!3ZUT";S18g*ojj$,#5i7h
-Ci-mtublH?@JSB-2L!A-fWOtuMr(UaQ)_i=]AF0slXT#HFIBU/ql+tU0$qCOj\,/HEOAm5^W
E//%`6^l!#poII'()P+.R1^Vnomp"H"MUu#@'qSKK$uK(K4]AtDTg3O\:l-uYn9kU0;bg]A,i2
t.D9,n&P9>P@aq;ODhcSGQFNV%Gcc&4]A&JUdWF$$i,_(UXaQNte*m%MOIXeqee9Drt=3>9&@
#q(/W3$@pfcF)hlbkcEA"L_CCDoH?5iL>tL`!:D\Fm5jJZ,>DSO>gN1\?k'$r=c(`fbPMSsl
&W3]AiLk1@BYGM'IIkR!N4NI[T[d*K\3.EMC(:/chA(#uWA@YOFT/<EUp[<5_jY<@-g+Lu0>Z
p+Qc<]AnD[`a"H><EMAIL>/%L&$68IW*[55J++Gc$p>X:XW/Oj+=WMi*WJnFn8;6CX4
b?Ac@A8gI#[!I2bHVSdtHY+a?21h%'Q\I^nMFt<:LZ\CgJ?,k"E$;kq+p)s\6A*d+^M44Jlr
2aHT/YX]AV-KYd1@ea'U"(nXH*.Hl[4Ee-RFOte2G5p>FWUAF6[kYl!s8o3S/_`ra7j$>4>":
6:`M1D^L4SL3U1kTANm+&$OiQ\Y=F\e!QT\_:5T!FU0oG556>4gA%S?*oFdp75g@\/<,hZ*8
*7!Z"V;.hQ`RoSb0Dk<,l]AgfLsb>_Sp#B;:&Eh65Bnuq!Gk:"rsE!5c<k.mi`:0RfBKHiMf[
327fl4o%*(R=(pDuT1Uq?b3qo,rNpQ/QpASYe4a7-/^iHgf/l$DD;k2h]AV`;Ypr:F>pY&*d4
+$MI\!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__941F354D1F9462B64D43B5EFBB8169AC">
<IM>
<![CDATA[!UU.*pPD^A7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^#dd/k5u`*!m@7s.*7YeEf5
#3#+b;l[#XQ<B,!^emPa7Z[UHFePHltO%5bb!<.$YopLuW`(B@dGUJ6M`"J0Me6aj]AeJll(a
H#m%"?%>h*<d#-6Ud\]Au/5iIhBa:n/,pWhE$a0'W!2jmfmY4U>kbg,!,pG_8!cJ1K#Ma]A'e;
2S89U(jFP74Ci+.PBg623M]Aq.<UPcWHZbA!U8/&PWBK.T\*(p(#3m>mm2@&l6(UhUP@-HZdW
Y74V9A)/T+rLqXJM`2ltqD%2t[*f;$tp"Uf2bK,hQ;!ar16XX8!?LB*^BCEd^0\-Uk\=r.6J
Y%ECu>`KkI"PnuU#=g9YKB2LfPklWi@>ODH/u]Ao;VptZj$Y@gnBGS/kh2tL5\X`C"f;tb!rW
\>hNBGgfWM(8kr"!c,!$Ju.]A>G?H?N>B\(fpasCg`clVGNWd\V+_U\es@7q)49h.&gr@YCUI
a!npeIF!\jJ-,72:!g.oFDeJssOTt&Y"W+LD]A"lJ6W:a%*$F16L2mYXV5X,ku3B8HucIE(jR
/IW4RIa<gXJjH:Wc8)qm>JKWdfmcL)3,`dT5PHrMrbbT)&2(*$DmrXm/l:Z/23C82>^Mmap@
C3Hme*\?@_f%U`GhgJ]AR:Sj<jhihe;(0a#h>A)OqMC7XL:#m_u^@!`_/0fi/m"0#2TiT':qW
^Lq1!%pdfuZg,BG$3KQ[!c/5".Gthk^srE;%\-]AFeIA:Kj?(0-GenM_i$?Y!NYda:E3_-Y4Q
'rT2\,;?2Xa^tHNH/bOPWA)7:/%N'Q)d?0#0=HlS?8H!4[=kS3ZCTXWh^71#/fE$V^6@%<pM
?A6#\0@T)#%E-6E^<5?bP@-[g@bBNj1JPh.<hkU/647oA?<:,55);q@l0!<$eNAh2@nRf6TO
^gaqXX8"0ia%)@f`&CJEF)KbdqTh0mKVHd@hIKA$IS2?4"LsFq/>9!&P2S)!ZX\%!P#Om$^b
#nAfk#3K+5k%1UoK)rjgM)+1^#<GX,KPbrWNV_:@L]A%oAA$7hj\8Wk0/IUtWW?LW9Q&bl_kl
i6DWl\E,;mCK4FibkN`&BV'Bc%6a,)f1,#+7%jYE(c57\Ek_GQIpYK\I>mb5aZ+57quMKQgk
lk1)Qtf)e4UN+n2KG&-`U<<3Ym,gdqWJbPH&oe!>TCB*?oIY8Tdg8*\^WhD6hZH=\E$md%74
E[G:no.0=A.S</7ga4("e2.+:#fcn6q>?Iiac?%\<W_ui_.XE&\C'"Vf]Ar8:SGZu\Q<MWn%e
Fm-N>p/(l2Ut&-H`U+O`t1hTDY!Dn#7QV6<8tbShY)8l]AT%;>%sn*,VU!@V:gYBL>LBNi6Ld
=WFgnC)>XD_Y:K8qMh1Jd6k66Xme`1eeXd%EP/=#CQ:CIpWj0fWbl6!3o\"Pa9GageXE'@U;
X!1k?S(TtqSJ!>17c]AnI;lZ`^i;Q#+f*hnEhGrRlN-Jn5N)n([\A"7"eZ;$P@Jk`po1kE;Ip
P[HO"bqh624WrkW/8H"(msR]Auu_0Wo:U-_!<?<HT)#o(P#2bs#[Adb-<nHpHS$5Y_sM3V::]A
[Jce=UL4!4dSc7C7H)d"jicI8U:+@eMO1;k:1o#RW'/GlAP`b9Yg/O'&<$s>9<bIP5$BHd]A(
92dYT\q6>`Y,;mDQ67.$st:XOgtO;ek;Uj'amd^eO,U\a^Z&QU8@IGU;:2@6E'X-T\si:#4k
fn5)([16lJQeZloU>-PnOhirX5NZY8iWU!5^MFeR!$bN<SZQZMQF6>>h'Diibm96s6KS.3+I
=15&2JL?*6Pq?UF9$^U";"VOa2/pe4/q$HPq-K`AY&3p\3?8MHghpI^Rt8$O!4C[.f@@0='8
R/^mQFW=Z[>'f$5QlMc[#@Bgk"&eKF(sE)B@U%BVZ_M%K0%nq/Q>tEhERGNo'"&^P:L?_rr>
jrH3<^a1GqdKOq1EOsX+=ZcPQ[Es_0U*'<Qq'O93eYN<or=4g"a^W\aFldCe*8*\C:"fE0f,
R.s$S]A':Egb?OY(FV7t#>a$=s6=>lkE8Z.#`u^kXp[o<m0D6(0Qtp_&b@J;*'IU<322skT\s
j&)8i=,$F(OXa5gNF$+#e/3,fem6:VnN]A1d1gJV'WsB63f/I2=*0U"?k/QE9e1,7kq%\>*nb
iF11)3!8\o^Tje>7U6f]A\2H497f#&-$QosDF1*XY8e-C;Cf8;pCb_Kr<fYqA7i1fM9:<20Ho
@]A>5&]A""fKCf_E&fASV9[,SJ,1S3!MDuF+4i1t<C^Be!4ImS]Aq4_-#EY5PDRp4;o1hYkq#SN
YO.q@o_)ZLG/[#,#+,/??>B;;Kl2c)>qjVH/KPMk%r1%hp'@`Z>]A[iMD!tK[]A(V@Qb:'0k,.
EoP"/ArUB-OG_rfL7u4E<,D0g=+=k`6t;]AC*!#>(`I?A%_QG&GebA-m?CaX\$+;YJ^3TdfB0
;f'<c2ilaHTlES/g4;B=CL.?*1qmB8VahB+SM6^1V5!B6d'<I1%JGlYPi32j[?HEh8J:clQ"
0!,fls#G%+7f2G\kA`<<eGr@ElpZ*fP;n=j#CuMAG>c1qF,6CG"D;Fn(Lm==J=q;uZmTS>`c
lde\>^*[ns[s:[QEjHQf]A*fN;<f!R^/O"b,@dKg1[j\6>\pgi&4u\-"979(]Ac#]A(H[,InO:E
JV:AqoIkchm2N/r$mQBe%SF[Ykn!=>s(\t^<^2;pq62uZs@)H(LVk<*lc8f\ok57*Fl%+&=!
bsZ6XXn=;Meh%Wrbh9ig<KZt$(SY;3It]A'k#:OT=jOpu]AG.j**r'4oWRffa[Xlbb>XCTlV_$
&[aKYq&[&O0DV'nb]A8l:IDPbM-+q>g^?_C*<C!@e'L!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" cs="2" s="0">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" cs="2" s="4">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.gauge.VanChartGaugePlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<GaugeValueTooltipContent>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<richTextTargetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="true"/>
</AttrTooltipTargetValueFormat>
</richTextTargetValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<targetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="true"/>
</AttrTooltipTargetValueFormat>
</targetValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</GaugeValueTooltipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="false"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="3" align="9" isCustom="true"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="false"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="true" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="false"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
<gaugeValueLabel class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="3" align="9" isCustom="true"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<GaugeValueTooltipContent>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<richTextTargetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="false"/>
</AttrTooltipTargetValueFormat>
</richTextTargetValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="true" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<targetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="false"/>
</AttrTooltipTargetValueFormat>
</targetValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</GaugeValueTooltipContent>
</gaugeValueLabel>
</AttrLabel>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartGaugePlotAttr gaugeStyle="thermometer"/>
<GaugeDetailStyle>
<GaugeDetailStyleAttr horizontalLayout="false" thermometerWidth="10.0" chutePercent="0.0" antiClockWise="true" slotBackgroundColorAuto="true" paneBackgroundColorAuto="false" hingeColorAuto="false" colorUseCategory="true">
<needleColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</needleColor>
<slotBackgroundColor>
<FineColor color="-1710619" hor="-1" ver="-1"/>
</slotBackgroundColor>
</GaugeDetailStyleAttr>
<MapHotAreaColor>
<MC_Attr minValue="0.0" maxValue="100.0" useType="0" areaNumber="5">
<mainColor>
<FineColor color="-14374913" hor="-1" ver="-1"/>
</mainColor>
</MC_Attr>
<ColorList>
<AreaColor>
<AC_Attr minValue="=80" maxValue="=100">
<color>
<FineColor color="-14374913" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=60" maxValue="=80">
<color>
<FineColor color="-11486721" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=40" maxValue="=60">
<color>
<FineColor color="-8598785" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=20" maxValue="=40">
<color>
<FineColor color="-5776129" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=0" maxValue="=20">
<color>
<FineColor color="-2888193" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
</ColorList>
</MapHotAreaColor>
</GaugeDetailStyle>
<gaugeAxis>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="false"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-3881788" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList/>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
<VanChartGaugeAxisAttr/>
</gaugeAxis>
<VanChartRadius radiusType="auto" radius="200"/>
</Plot>
<ChartDefinition>
<MeterReportDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<MeterDefinition201109 meterType="0"/>
<meterDefinitionName>
<O>
<![CDATA[]]></O>
</meterDefinitionName>
<meterDefinitionValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=100 - B4]]></Attributes>
</O>
</meterDefinitionValue>
<meterDefinitionTarget>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=100]]></Attributes>
</O>
</meterDefinitionTarget>
</MeterReportDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="fc774ec3-0f90-4932-8fe7-794673a8c39e"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=100-B4]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="3" cs="2" s="5">
<O>
<![CDATA[管理]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CollapseTreeProperty class="com.fr.plugin.cell.attr.CollapseTreeProperty" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" rowLevel="1" fold="true" foldIconName="" unfoldIconName="" expandAll="false" subLayerRetain="0"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" rs="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CollapseTreeProperty class="com.fr.plugin.cell.attr.CollapseTreeProperty" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" rowLevel="2" fold="true" foldIconName="fold" unfoldIconName="unfold" expandAll="false" subLayerRetain="0"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="5" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" cs="8" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="5" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" s="8">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="6" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="6" cs="3" s="10">
<O t="I">
<![CDATA[188]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>当日值&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" cs="2" s="11">
<O t="I">
<![CDATA[18]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较上日&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="7" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="12">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="7" cs="3" s="10">
<O t="I">
<![CDATA[8]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较月初&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" cs="2" s="11">
<O t="I">
<![CDATA[-8]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>较年初&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="7" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="7" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="8" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="8" s="12">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="8" cs="3" s="10">
<O t="I">
<![CDATA[1888]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>年度目标&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="8" cs="2" s="11">
<O t="BigDecimal">
<![CDATA[0.88]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>年度进度&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="8" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="8" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="12">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="9" cs="3" s="10">
<O t="I">
<![CDATA[168]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>基期值&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="9" cs="2" s="11">
<O t="I">
<![CDATA[2]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#8D96A8;'>月提醒次数&nbsp;</font>"+$$$]]></Content>
</Present>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="9" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="9" s="13">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="10" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="10" s="12">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="10" cs="6" s="14">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:28px;float:left;'><div style='width:22%;float:left;height:100%;background:#FDAB07;border-radius:5px;'><div style='width:10%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/info.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:50%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>详情</div></div><div id='phone' style='width:22%;float:left;height:100%;margin-left:3%;background:#51A579;border-radius:5px;'><a href='tel:13333333333' style='text-decoration:none;color:white;'><van-button type='primary'><div style='width:10%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/phone.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:50%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>拨号</div></van-button></a></div><div style='margin-left:3%;width:25%;float:left;height:100%;background:#09A3FE;border-radius:5px;'><div style='width:5%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/bubble.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>弹一下</div></div><div style='margin-left:3%;width:22%;float:left;height:100%;background:#417BF6;border-radius:5px;'><div style='width:10%;height:100%;float:left;'></div><div style='width:25%;height:100%;float:left;background:url(../../help/HuaFu/follow_add.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:50%;height:100%;float:left;line-height:30px;text-aligh:center;margin-left:3px;'>关注</div></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="10" s="13">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="11" cs="8" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="5" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="10" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="72">
<foreground>
<FineColor color="-12485642" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Left style="5">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Left>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border>
<Left style="5">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Left>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="黑体" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="黑体" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[e@]AEZPM<N(MIJpa`.iem//2'PJq.qH"VIPdJom.3Pq&KG!i",=9+)pf-m_Cl/OVn$'>R-H,U
e=2!X&QJKTP8AUMI&U>crM#g%V(d^N4noK?T`d`Z4#a`\ZcSk-:njriVeY4#o8[Fr=OIIt`M
b$\ePg@iOo/\(&n7#=i<n_>/.Rn)V]APF4K[T#\S^4O)T!fgKK\<bcdhjTu8ttQhta?1RUd?D
YS1t#usKqq^2'3Nc``lT@qc"rFU&(-U$q534<e\iRYQh$_(DNcSG!'`JBr+cf:W4QZHf_Z`'
RZTiT_6M"SP,PYk7FnUfq#\XiTf</Se0%ZW@9e1[Q3>&Km0kG!3]AS?n<(C8[@oRM5!s.g+8D
9o4#G%PuY-QV/m3Niu.j#(#]At0pR78QBW0S2EhnS=1it(m/K#h7<W5DF]AS0JK4iM0qTIFGIQ
OujE`[li4H<',X6m+`k6*hogJ)Wir3nPU1t13F-m=Sb.4ig\Dp$[<O>(DsC?6W*,HV#(+6Lh
Ws%f+m=P&&[g@"#(Nc8%h,/&[Lpp]A^GZIpTBGP]A-)1XWqo8C6Y?9kT`VS8?E+(\lW'3G`hqp
%>j!!Abh3UfaZsA&#==Y_e"mh9dWI@.dI=-kOFWe,T0Sc[3Od*CU\W7'DND3-Q/GU9gr_5s'
T6jI"SpUT%k]AISr\"_^t+jfk\`g(fNWVF#e5`G92V@DKj,;4TEPXakc!>oJX__U\4aCT@)Es
^okD,;7]A*9iO/A%9)NeEJA!,dHFD@"]A=>MGTLhl]AB9tk&dh44+[9,A'lbE7p#fnoAAG&aI"D
7OcCoUr02;>&I%KY_KM^sF>T=_99@BP!gNUlh(?fQJA'#S%[[.RX'?/)[?Jl:8X^Dra-E:a;
'P0u,B]AZfQE-K/_u.GVHM&a8"W)1l9+kGZ[3WX0NDF5=bmDr9f,$lHnKRfa1F\.ZCN>al,LP
_O"^#PZq%:Tn[)?7N7md7aq^rK/-jWsNL?-k)o"[)CaEeP*DrTlq`j\uTRAcYe?N39]A+4]AS$
Bl(n4F.Yb"Q]A(oL82`dLlfB=9AKCo9(iOD^Hi`WnYp_6*;]AM/T^o8L[3^XR0Jj"$$D\fHupK
`CfM=2-\s-Y!bu+]AsrfK2q\*"Lt,ZIOhW;;SO=!80j4O!3p_]AhIW_hSICV_NVH2phWn!_j89
J2jTIY4A2LQQuIt7'8&_&<f,QmE"/pGlbW0CW2f='Hf'bgFD)?[%8@#Q:3Fm;)Rk<YG=n+u5
fGjW$,=OcC0UuhJZH4L[\V/j)SDf+lN1*\-bTL/JJWtuR$7@X[A+W1'Xll\+Jd$0&DV59k-2
D3QOZ3luEI#t!ZDAt@!lSE2j1bLlg:;TEH@@H+]Ampre\d$"k'j)4?TK\[FQTDjXLL\#4fEl+
,c/-G%V(rn\*BV\lTIXC/LA/R6<MO-EXWfQ[T<Sqb%:';LT#<(1M_tu73Tr0(uc+SqC'RITG
CgYbJ:V#iVj7S"#'3E=n^7^_$9Mq5G46HetOCG^N2d89iS;DV]ARjaZs`0]A?K!D%a9m]Ab\WQh
*L*<6^\pmCbHBGmh[68i*H,Xrhi'ZDA\*4iA-#Eb_D&?L#^rrlul9%Qu<<H%?9R!GuV#PXOR
$:NI-:D?G@O@+'^]A@<5^Uf84E#5)Ft]AXn=ad"Vrq\2nFUjV0(YPT_6O\R'N66:Lc;`'[X<l4
T$`enFTW^r8A6F?@#+EOV8%AUK<H:8Qp2Wg_-`n-g7$[1A'GT'u?p<MHFP[QY*bj5cdaCp6a
\$RWb9c<)-tQM"FZS8b5C*@"gO1lFU)\4822gpm;6QTr,:1<VUHs#KEL@T3.&br<S>9GhYW^
'H/hiGbg:35b)KV>s>AGY$CCdg#SgEdOcDs'a<S6Yf@?pI#.LY$Z^n*"_J,S';tc:,jsTfC]A
p;=Y+$+K(#![oc1U/F;j_i4PoGmUJR4q6?h'[\WA)[,-l`IC,cqm7&bHB$d9Ad&,6<F@q",7
m,dk"Gh/^[F.oaL&i(l\K\!M%\BU&1sB2<d*)d_#GcEK+6%j1!6FME?FaT8F0:\"n<lbr)tC
Rl$,\*uOi>JE?*"u#\^h-l-^^A#a('TSVW7tOGZ7sMCsa]A\Au4L\.`o56XP12j7/:)sY61(p
?9l%H^h3+))EhM_T/-Jt(n+2)k1C([W%psI,=X:/6M)A7_6k/2SSZ0OCA(p<H3&2SW>C6]A25
6%qT=@OQGa)<cJ]APe8UQ->^ZJCTA[Re93=djX?Bu!ES1kE0`n3#:h0ga.cXVWEQ@:Zm:*SJU
C27>ZXl(Dkr1XO8`O@RrbRg)'H:s.)\SRh:h/'9m>XqPOR4c5#NSri7-htbtl\%C>P=fE8)4
(?gsWtSR)Y0X(0;j')9,F6#O^Nf:A-S,Mu^hZI4';Zt=[bf\3ho4s/X!d>%]AuQpTI^h%@GI!
C9A%Bs0T_<:uZ!_(:)gkk?Ir1^o`TIM".KD#\HA<LZGjp^Ja=aE1#i\</D10sRrDk,(*':ZL
3U21'Ek-0\aU/hmslkegXE<!+ORCSZ3^<mpS]A&OMD[MQVlQW&*<\6Sk2_NJ0ZlhhEt"<P<4L
fG9R\V1;0p)<_DuJ&usU=eG%9OKUs1Wp5G"fqk2?#odTC[1qFA%pS=459.7L0e-(%q@lYOl.
pOYc>WZ^"1MA1m*a^A3p%M"U759Rh-:Q'gSM1mjDh3ndX@/&^51`[_DO_mnVE3;r(ot*P+I6
5GtYa-FW'_BH:1<5!IioBVg=tNl!<la2<cI\qE*HfZD/DnGdN48CVJ=sW5i>Z0d>8Lj3Xm@a
L^@PIp1.Hh=cq8$W+@J=",ZgW36&E[NKAUM9W.8q#2bj4HnJX2.^#=/2N-Q8UFm^rUP60pG`
?USk]A&AK_Tc]A@:Igird[]AH5siN0C^-b"nVG98MMQH/3Zt@c]A5EItUDE<X!K#U75AJ9hRr*r(
"BH$Qah;hELnDk5bH>AtWNVD0DjVe<A,B.a?.?be9)NR3F%XmB4g-,$o#Bad2O,3:;RirqGs
)Kk;P;M`;hq;jl_[7LHm@hrc%k(_[3D*e?EMGfcc@GWJ2-5P*JZ*Qb:0JikFQB)<fALjOuB5
kWj=13:%Lk]AVVjt*L<;O^QA-o6Oudcn`e]A@mg;ohSb2ZF<E,Rs%SDlFN'"B]AT!CPS-.5)3[>
Esq7k(Rm[q/fXJ4B<RUd@@8=d6`XK>j*[b;Tsq\AhHKEFVt[/BqO6.QdI-iHWB$!<P"WTL0M
=B!Pg>V[V#),?@EBQ4,sj%`WB+<etP(mT(8?u_OmEnES\I?MIBGdHTgnp8P)4:UgT8C`]AErT
^Z.*G/SDShmDESE0h;K,(E+WTp*FU<YX3Q@J4&D\0\LBF6,RVT<j@BWnun<[%\pU'"g)U`MG
<1=INC5:Zt,uB2C^$%rTM(^9^;b9]AHm=lH1'/f@Z11+qeLP&@l,"G!gnZSE#/]ANM@F:P[Ea;
f.IH`POq(N5RkhnCOY^E*8r,@WTfIbrh?c_>6Jtmgh830MVTGHuiK\Sl54PTMTb3!kQYtGX,
7Bt%)74ei4O7;i&%Z"LG\FsJ#g9C4Q&S"kKr:?9-GJ-.UdP.[nc`bIrNcE2V=LHXr3l1jM7?
`'Y%5(Ts3qVJXMJDm5f)!909aKe&gu!C4H4]AT8h!a;m>5:*r&83:?H)&>:;-<VIQhLR/!7f=
.L`fL(NRbjERp1VG>LEtpCiQVpgqfB^iC!SN0=WFSU)+FYX`EmNaQp!);aGe\AWMRoi7c;q-
s"1C5A'ams7?7F$Df8Ks`Q_68T62CVSn[P3:R7.o1X(Z9$IHd!n@?G's%9brYNRC"c]A'aRL/
D'Uq*fYT_UVL5Wnf6W[#hBtUisXHHVU\##.F"L<Ke92?'eK.6#^7>qh..o=6"G<)S4eZZ_jU
=oI,R3i$e:eU?QZBQs1%O[#Rj!)5HO=jAngj7P%1ER</?25qq=\!R/?GblZAH#M644J<(X60
(s'gn>@G+ha7N2Y"^YsQciZI*!8I#-#T*B7/jI\lh&%U:"B0L*9s+m'mbDH&R*rHtH#_2W0W
kJeF_U3TZVa'+i$Q(=UMmtT"%[*.!H?Cb87T;s5-mOZ[gRrVP7l]A-\F+#.W.PfIlY-mH`Y?(
0"//ph`qB@LXNd]AJH80N%qrl^uP.\-6p=AT<kHG[E>FaEKNWs/\#Z\<%ai:!7M@=CkCTg.Hl
DC%Oe$ED_atm4)P@@3VPeCQ-R*VaSOY+g-OtJJb.h5kdnE'+EYG``O`h]A-pUO"q'\b`5]A!+k
he=Q%YHM%n%sgqW:djDLcu9U;^5l93Y9'$aPdiGe$Um).*t;co7q,'8[^M8&%@)<=hC<[[-;
'd%NiMbhWgM9W,Vjb\.-'oo</3mp:&Q;[ou=7mjL]A9N>:*<Q&m+M48U+BYu]Anfd6Nd0k[KT1
[((jO**/\kMf$$[i#oKFIIW=sroD)2APSbmgpuF(>YsK>0<j$KbJ@@R"'&""p%M.M,<d^.<5
.AoGeb"SR'7morfePn2l,CGCG:W=(N-H+H,7Gl>q5lb6:G2c-7lcCqik0G?5.(\\4r*Kr9u<
=h_1?'EBE78=E[pk,df&/1JDGp_$FALr19k#n)q)lhQNl!<C=uAq[j!7'p'lAMCaCI+LO<$9
aO!6"B4>h>)UUho6RMC]A3/KqYdNRABKmu.%/NOJ%J87$3$sV*Y\haIT2<8sHh'6*2,$L!4<<
o)IkXoM?ua1Zh=CREih3OO"H2pGZ9&Co(Mo#4lV/GBc*t4/2.Eb5+:T/j_W@Jn+>%.sl-676
(Q7hJ`RMn'o.U8YDgQt))NIhSI>"ON>oa2O_(s>KO#XT!VBgjGPk^L%,W@^mpnsg&>X$qJN)
91WlH!$[92.S:OM3BQF`J4X[?*$!8K!F#%cK$I`M`J2WTnnF=,C:Jjj_@kkT,MD0#BI"8ERs
d;/c'sk(dMc06i"iT/CnjX#YNQ>BJ4R6Wh%]Ae@h7ud$LORPlWCP-Aq\WD[CWnTPS&cPW6oq<
Fr#GX4n!M+As;"jSbR$f6nM/!aH6@M*JLBje@]A>nTY1:D5g-'C3f87k%S;oMB#O+<3m43S[2
M',,ckn'nThA%4%p-In>(XF>DCdMsh)M[,2UKs6auM;n(il4jI="&9plKR@/*>Ir@AEq#s#T
1I+?6<M`'L?K0(r8t%pYjTS'$I^?C4.8qs?(cq4dVL>7h`^K3Rj;di^hY,IV:c@4*4oN"4*>
6]AJ.%Pe_DC,.eh]A:]AuYK]Abep$e*_IXA^sLDFc(ZT_h18@"K5.;^.$'KY"6WZF[NO\Os<6O9S
]A^qO-4nR%gp9qoCj<m"$8&$EZjEk\gfnBn++/]At91_AJNr^7[X$45A!a"KP1/l=<EMAIL>
QOrlT*pd"->ufkT.7I=QI4b?h\?'Q)W#ijd0`f?*n*=0apkJjKX'*b&7es^\AcENlHP$=*+p
r_!Sj4JSo+LX(V'n6e\YZ_#?0*<Ma"b(O>'DGLO>&Ms"33RpU5Bl3Zl0k&M>.OVEh.F.C%&(
'DIK\6"8fNa+]AkCWc_#FMQE-JN@>8'Ka)kk[K2?Qo&8lXC@3I2V5&G*I-F4R$ck]AO/hbFjpR
n[k$h6'VNOJGN_6k5)%XcOB<,n>K$e3)M0m")m;&dr;HXa;00Y[+82='JOQX".@&+p!\mWMJ
3a]A$#VJsRdG.4)L_aTSsIQ*6nYOIjnhe&UN[,h=T!WG+`P-.c*VbM"KAeq=DJZsUCA14MjiS
qi'nm0Y\A29U]AbQ:QEeIU4muB.D#]An+@iD@sKEq)&(m"3Z+$:[c3W6nF\kYp/%QLbr,.O7?/
&@\IJo.9)dj<LdV.pW8ZnqX&$siV"",@kOc<f8g+s5^#@!`Tq896V@W>.$U)t!Xb]A5AEpsQ7
_;j\!KG_DLQT0M8aa+tr3Q.d9BeInW39>2Aq/L,`bX=&(=%u?PfgqY.`h$qW^lK1'^jBG`o!
nbe"4j2^@HsSIl\Y7e_C^`SRkHPG"5@*Fom-7+-J0)>K4mkLgmbTi;.u>&%?p^O_!Ln,Yb&7
5a.r!2&o5\E/>\E_&s?Fu,Md,oNVa[e=f4=#JqnGR]AutiYR4g9O,-U9RpYakcOQ-DC+e=rQM
OtHu=UHSdma:8SW`=:P]AIKMFe!0#XAY%W8I_RH;]AF]A'LENW5[Y`f)BMH$*=p4=NQNo6a<>1s
01OI!X#XhBU>,TQ5,GD>0*.!m-k_pQkMek>;`)fM4;!kIbG'Fq`K0$5Q#k'M!m,r]Ac_EOU;Q
mRgHncDfK0L)eN?DnkY3QpNAQ)a$pB)W,c!U0hs4/>)L>gdC;XU4B4(8"qq?iC7%7Jd$qn5p
.p`rXR@<jg!g-Z`/itJWb1R-+.;UjI2Ms0DLJcMA%*b6\W4,qC(pj"97a$O4!(=Y<+g+Xatu
JH[MiAonTW-1/1G%TFbbYG;e"%gWT2L)0Shggr3LtQZQm3Zm!&O7a_/n($f&K$i]APVhkEF98
N%mFngd^VHP?,.4k?r/H[Zp*]AG`@%FEi7CIMH-o+*bk!<<-<Kh)AY78t_*o2ud58UAhoB:)K
OSGm+%PWup6@LY,?&4AmOccRmIT`E9uG\@Cro<40AhHf2`.^j#Wu*993PVP;U;G4R/m.<OA<
'0!n&FeIccJb#C;?M>7V*2-(.a7r+Ufd;,jW>H]Aj>0)")iU?=7o;9^0XbRRhRrRWTloU,R?C
&O3jXg8".9`$8+*Y<q./g;f1&"gJdal\KX8@Gha"6dTjZ00c&#b?WSc=!#)H/&\(<r%a2/=Q
.m,"C0m?@%GTmb1%(ArDW(LCr&ao-"mMCTn)3n3fPU7&gQVJQS>jb'"pQ/XV.CCdJiq8h;@r
VD?\6!pe*L!5S^]Amk6ee"Kt]AD/u+PR]A8%:qZo83WVs/I,mg'5%c*tinG2[9]A&cA:qI,j2rZ8
p^eVOQe:gBHK>4>dfF5QS1Houuf;rtaR%1tL6\Q/NA9c>l'@Xd\1<<ukGebMHR3jF6Gl3eUr
&T]A=/I3g\':4&k"NsSse&63gp)TJ^6gXB:K)00m*%\uHD>?>0EkTB$glV]A9*e/%qcH)4G=dT
9FC*$,sT?D[*iHW#FRd,emf^Ljm8G.i'pUUh^[h&V$m\U-+>eG6_!]AN2nfj\Y6+EJb-EV<F]A
"%,T.th0U\f+qA]AdE<"eL+pJ`a<0F"(]A(bq$[b=<0>lU>GCis#+\8.,NH!H>2\nk2qG'g8eH
2#AY>j:DTMiOG[S6l+QG570&F3E:1Dmaa@C"K3m$jQ6)@I48O8/Oh)-Z%uLhpCfrp?&+u`#*
DWbs%gLdaf>fb[YOJ>2W(CF[`iYq`8YlEUE)MY`IjL46NGkrha\4-A)8"<<K!S!8ea-PGf6q
8if6@Xj3n48*:tE[67G/'dJunT:qL9Ep&^d/_cO@pCYP_#-C+2'*ifa&@J@u4-T3eZ=*P-@C
(_PCEmJA\7q3H8l<Kr0;3nZ#Numl1i.%9g!YD1.`L*PmLn&PC]A:4nOO5nOa3.Sa";+Ib^f`o
ZB]AKqlaOSjACY[Ni5=4F=5"+@RpT=q#qt-nNF.1`&P>,[^h8<s)]A6KUm:ZLb4#kQWtpg]A0#D
&=A=`YT0RDUlE`bOp.H]A]Ab''fkKu=BpP=]AoQ2@bHp6aHo'-_krkgREglr>)XlT)olLBt?[V,
^=1UUZ@"`3,Pn8/9bEPMI[:p876;q'<&`"u@s]Afgu!?*i_m<th^O2oeJDEuE:joD/D@l:^h.
QgJ3jL;$tqFX*=-,'o>HSZoZnQ66_"K.)?&is-`eee%:c7BC^6P1q;$\n1.!hc;2Hp^Hq-k`
2G4<3d.)fP"?F+4@l3XhpqsXEc@!2aPLmPN3C)d'\.HH)IB^1McR8FOrTc)kl_:E``WefL%H
GhUqoF]Al0Y:e6-j1g0maT@P\aWH3=P`+^S,u0G5*e]A\b%UrfeI,\-7GnghX+ILtcm)%Q:JQF
.ZiVSQ`<XPtlug%aTJ^ktOV#LW)6a^5&.:e;[1:QS@QKG=2Ubn#T(c+Mtr6&_"@l?:=/R='n
#6f6s*+9<FR/)(BcChf[&E\9ID'!dJX5iq@*cmu)Oq"t\&h'R2$RnNELpmrj"@@La&@Jd:>a
/:BT(&<?YP)Drm4EF@'EiuM/*0):qTWYN%V[YKJZEe>SdlK(",>/JS8B_3M7qb#HQe&9YE2<
V@o'74Db48>CM*qerY!)J;sMNdY7)L\fPalf>\C:$5B'rPi;fKEJ_T[JY\<"q+H7^JML&e*F
lSa/s<F>R9H&HW'&?hqKQ?csB)el)g*Rq7ZOKM<cW5Ko3t5r?#sB%R9BW_k!MG4l\p=aQD_7
F:4+H+&u4Q:QfYI_M5a?m7VdG!\('Lq/C;0OS_T[D?ee7&8g-3SuFhVTgLHcRhO&ad>4L`X?
*%min<E216'@Ja$62R6@Y,(8P!SVa9nCM4Qrhk.i*ARY+XQ:2*WO&5O_c>dOU1]A2`;6"&rV\
_I.al+g-jcIY9#TMoAX"$-`6N:"88-HtqOZq;SdK!,(#$ZU(3>+&]A'SW$Z3[#nK1(Wr@$XIV
8*Umh"(]AlY0`B*NO$@4T/q$rpB!-P#;HV?[VcoXNu.<:i>ZVCGn;5hJE0W=[-4hEV^j1?:e?
iO+EYGh_^^9PEoL^YPL;?TVQAdh?T<-0MTA`/Sa:i%Wgg;U\oa?(,`/0_k/CBY':O;orEHOi
jWlG[-dJGD\i$d@?`mEfpbdn8_m"lq=[]A#`,u4N#`;eA[\5lgi,?+ZP8+^>R^"*HB7Q"=V\3
X9&FH^P3UrZVaHj()qh=V.(Pkj^7']A]ARFl6as+o^f^V#&hYf:?F;A%uZ,`Omnk$*WqhPAT$t
,rF>fWe\1hg:Z9*R@rt`>DC.7fKoorG7:O[H,HNJDW8TFg`XG4Qk]AItTX\Ff63NJR9amO4E,
f-U;f8bT)WuqH.[[e'hMdm%!rXnLl=aME9-J.ULC**7e^>gRK[#OR-F8%r+R+;/Ti%I2:ku2
#<lH&X>ijZq5dn*JD9Y&+g(pb(f8!D-aNJG"*MKK)j<4M2A1pr7m;Ok4V=Nf(^O;&0MCLTpg
I7lC>4:(V7$lHrWY*-u6[!ZBYR7hG;&P^c8#EXDrT`cLW6;B_d5eW?qa3V-XG.gE6";/(o+K
hld8HFeD?<QKm%+S1`]AET^3gs(3V_a?gbM!C5c1O>f,2^$Lon`p-<^-_(a1,n2S'(Fc_OaUf
\L;S/[as]Al0.na1PabLR;g_\2J6+WKiKK!K4'Fo(kkDQn^&:eD2BIgS.%l!/`^m_K(>j,;j:
F[45HMN42H4?,h^_u54]A'feF\%tXKqeh_42r,eP?9,0#9/&[C"(FD5AXlo!o9j0I_-(P#OFA
nU!Y6=P!F$Y;:<;'Qtk?3/RbrKFK^ImrBjnd659!ph!P3*e/NL1ljcYKdstE%Y`C$(6^9;%"
$t'10)sAp=WCQq*VUo"/4;SmL(==X3l@\@4ARP>U$3DZ1m*tp?^Xo9lVSSSBX3<F44*HXDK4
+Im''%4VP)-bZSl@Zgns6-%:^(L]A6qP0CnrSM]ALWl!,+8J&AG?B<Fm=MM#kasC6[1Lje@h>B
9ksHAG7HVSal-Y08SN)n1&#LhI^,_8YlFro3;AnsE['MlG72$D7hWAM?9pIf%GndFV_!p,.b
sbhmB#G^FuKBeR''Gl]A#]A+`n9i+_C]AlViHRd>E[Bs4dm<tH^A(CcelEB&/SJgGEjdU2',&[W
@F/0[5[]A/'[n0m&E5AlG&Ep.0bi'tL5]A0iPHO3dU-R&,AoEfG/$l7Gfs8Q7;Kr))OX+%qP"1
6D]AUguV\?imsToXZQi\Gclg]A'MbKcZZnt[Xqt<Vku"5^fkiCnB0UFR"b;;1Lg';B>Es"Ch&>
ut?7.[JTg%PsZ"F&&,R5LRk+"u'Y\F&Eg2^5H(F0r\D$_g_[pAFgpI$]A9_li9e09cK!)UFW#
[CCkK08aaM@XI)JaD39R+aX3HKC\XYUU%f(l57\/=6^X13>JEeZ'PWqS]AHYN0TkOm^MB'/_T
;'a2X5Gt.>'O-G,]AT;^/P#0mJYF"MsZj=YnbAX\&.:a[V>%;1/o'2RU>%VCqk_Sb#h++,t>$
OK_VlSARKnR"rX:720N*i7n1r(5bGV4`h24#?"kt4D=hIfBQHd/VPFnhKRdp^oK5g`6a*&+5
9KK#JD$bbDa(#'kBhUhOBb5<XI"^Nmh/eaX/R_g\W,b^\E8]A<MR_GgrkJf0T,;5gJ+&56\JC
?lmp"&g'u#VTHtAU5&ZPW5EMJci33Rr!__"Z(WtP5i*+Y(E5jtkUKa+9VoG@_EXr[c#.Xu+_
aY_BZqSEqm%<''7.8h]AV'*7>+H[j1J/@(C_FLpOq.?kP7`&p;VK<n>#n0D25f_#fBNWt9f06
g2)Iln#<+/PcR*T!3)AHh?ue/AN/Ik%V5E5E)Cq^T52U<]ApmCF_Fai9SP/e:hlF5d6p7p#G2
<Cb:P+m?2PEP_NuV!ZZ\_kd-+^M\fFAEUE+L5[5.aS6E<\NWnp70';B1LR3qf!CF9Jd[R+,e
1bJrrpA\V5AGL-8b3F0DPS`djZ2.f.2>VGO>>#.g_D0.C;/PRT3"ET-B301bgM9[,\.ed`7N
'5*Y%]A!P.Y)t!Sls%Uf`'aeJ5?&0<CPELIdG-1h$^a=oNgGH/fTmE$-0_lT^O,.,p?+SMK'^
Ft03HW@i'4'6\m)&f!_#bL\&^P`"i9[T1uO2GiSY_3k2%5p54#SG7W6HZn=R5oIY09@p#e(H
L9lHU1L:?MN:eVWHGm/4K,M_mai[HlHM'7O5=Ra^R<"mmQ/\.D4@RM_S'I:X4h9*:@hW]A^;3
5kaZ_J:Wo_$N[e187>itD\O_us#DMEYYt>V5Cqk\05f?K_1SciqA3t;9NtOk&jVYdK8Hh0OU
_mkes4bKC[Q))'h$#>YVA*<>iB+EQQ<Oi7W`FR;?i_saQf2>?r>A_?o+il&Ck6d/I__AtUqZ
CHEi3`IKS/m6Yqd.#oO]AX[)TF3kQrl,=V^,*qD\q0<eO!$q:f0u29&i\^/=)q/l,@:r^gbJZ
>KKTMXe@14m0+ublBO[<ns(Z'c_d1lf63;#W1k*>)IG%M9stT*Hl,5tVZWn9-48575<uWE+A
1:dVC^PGna/2,]A:`Pa@>,XP;-ZKU0=h!R?:CEBcFdW]ATP1'Fb3HF"GLOtf;D8.5ESI`ERN[g
_p%^brmEN[Z\XbO;!Ob\gPU2d5C8D3_EolX+A5Xd4fm5)qP%V_bAu(Cq=+LJ7^16:AEhNqq2
lHSa9;DFMo8l'ZY0S+>Y)#]A)V`MK1[]Ab-M6k*&CZ7hB7nR9SBH[Mpeg&CuEbJ$0Y0-rtQ5a$
'7716$P10X-rFo:O&o&:5N+n)\6)?.TQTFS9+-E'"g`je-2KbE.4]A4;8f.%gKplo+bg7F?/9
mb@t^qI1m84#bN?f*C4W')?sgl>n=9ifs`""rsjcS_B+lWU"e.eg:UX+;?)9JXYT8V#qeDF/
(P2)NM^Wm6J"3YQZ0!=hK<I?nPf3H4"Q9^.Th@Q9GX`#GDV56%9q3O`D*eg/PSIgqBn7+lbl
!EP#0?NfJ7IZ-M/lIuG,`S'SG:D@2_"`^0qTD"'gMX.<6HU:F:D>NlRnXltJ7J@iiF_Z`A/q
A3h@P08VNr$o3gQBuU=[WEn/^.hhR$G-U.a1GTj5osJ%6F2)T6h=*"+AYCV7PsV;1@-bf?&<
&T6t<O7FN6nu)UUtsH48bt\q/ffX%-7(mV.,1R7aZ<+`&HATAm`MCPG$c>=E`aE%UlqT`Wi8
$,L9sWpTU!i_X4NXV/*I$BOnld#-'TI!L)OUh73(`Hn'.mrIb%&&ih;;Ro$GkH"="aF-^@70
8U#cps&\<h`VU^K3+l(MD+FWp_ad,n]A:cb`od]Ad-9C<LJ!O^S8U6WAJIDH4CQnt6$:JhXY#h
.XU.EHJj'NEcjIcNK.bs]AUOEZfdI"':Yj!*D=&G$UHZ6CVM($W@p6q5,;*0JDK_4sgHo#q/:
MHpNi&/,$EoJJKX*ISt+(jT6-+dr/ZWbR8_@PDfE'@q^8n15`%R-1p1TXBRReU1O)g\W\^S;
l2gsj5MMH3!61JZ%Re&oge#l@TqLR"+SNg>]A&^![_N<ls`UIV@3r\l,(\X:X]A$oXX'EZ1a2+
!JQ)GP.ep0&jA'=88b+:%U\b?#.4M+UG>%VJj,&\Y'o-kfM[Wd#Vr\$'lnRTI1EKV_P.W)?5
X]ArCq5as+=C[Z_6/a`A*S@S7FC#,dAW$#;)&o(2hgr^E6C2r.P5M-Y=4d&).QPZEe-aINoi]A
f'$Sp+]AiuP,BjEm%=E,Is9OLl^dahSdc/@Ii$h0V*%da^NN&`i3FsL9t=@PUW%2ug-Te5TV%
8$=<I&`-R/PhTDY>m1\e3!kRDW_IY]A=<e38!=G4b(D#-;Xtm\XFrF+b+W`9fVN`7)\Mo%Thk
7l2F#GW153`4VIY]AM5YF.aKfX;R-pl"&1%bW2N<oSof/rQ2?q_sir'cElbE:3?W_+sKRVU9!
lG?=u]A9L%qi5l;F(q!mi3q^_n9L2keZY)B'bNKdfXT&)_FTI".oK;!$eLm!*PPC-e`Yo`mF8
^%J0iE;Urlq:XrdqQfG%k?Q?UA\taLSsCP21eaEW7XoLL_3Th\P+6PRC&^U!"rJ:BX4n"k/6
=[I4]AAOUcg:8]AW%U5SMYR;bk%_6VqT(hSteE?Q/NVkU1^I0fM[,^3G>>bKnjqK,kZfA@/P`G
bn'b6WAbA8Z.7#mo?(5%md5+rl-Bl$<+d(osm]AEAO0=1kpXmdl=?W4,_`?KiT<SKYqFdQ;o%
ignIr30O+Nf$2A'>\J67T5YX31%>jE0W\m<3cWtJDFs']A)lSk/)['q^66=3utF=J`.#]A8Cj-
e"b=/H!JeF*u2T#%48&GB#K^+emaU37`?`ETQNM\P'0<ur;rO(NH=a5I)4!dXo_o(F,4F10A
8-M6kVF,QLd+$R5&OX7gK?5U50'2DHp6h/bc9/<ioMs@+\$Cdq@n(^K*-Tk@;6D`r^G9V0"p
q@gECP'@2c#7XWe=,Ba3+$G\t]A(NF'c>f3'A/=&U4fWa[n;h>eno\pg-&(uY)FFrV;[4@4u(
+PG_6$MP]AB!XEcgn+EX^Xrm3NhAn"o4]A@ZZ#B$G.6jh*)Aqnlm6;SpRpla'qJ3e9cOsYjfKJ
fs#du^B_G<-tn@,5r3J$mHgptXp^D(qoAJ_<=r,DhZe<9cONSb9(^3D^^Z?QSB(YKI%=-l`A
X%3W*bDGFeoR9c*9=HA6mJ'fZ^o"Ua3()S4<"JquSfUULT,7F#O)Vhs;gjds7ZGOkj\ZCmp1
/r#i&mb68[C(EBB$?UcIR@5Aj&@e;^feiYtIF;Vn`PXIcUH[33)<%fBi8VmQXu]AR]A/DnZX(P
nCSP`s9e)5(UP>p"6+NV+T@NWm6\"V9nA0MFW;H>D3'\FeYB5Ztb*7V4MIf^M7"Lo?&9LS)f
e_?R/M;o:jl<k_hWJ%WcJJeK'95_Y3'!RGrNdZm2-S2\Wq*o+)VP]AZ'D2V;#<n'T&8'\2m]AN
OG+M0[&aoA*U0)*O07[`QCa*9MZ]Ar(i,1DGAO!;kn4'smTobtc5:96Qd'(Hm-XfD^[LJGV`c
3&j_hs(iRMRE$S_mgCg3`4.feg0[k'2mKRFA@j'Z]A&ibIqfd915!$=R'U6:WMPE/VB0OH>4k
'!#QM:2<;j0o,ih9['A]AK@UBC@]A&MtJ;SkOSKTLP))q_t4XLb.p@/qo`UIp7lJ/%qk.[32)[
$s'5"/7?h5Um^A5@BTS#%hRdubI,]AS.9)^Z1"k6M#^A$QQKb0q36A)')Zb)R5]A)A7V<XG]A[O
,skcbr:M9<o7q&e12qo4+F0jcQfI@Kib<:]A(:)8EY%L9eB!)h]AVfgdjn&_0?DqrE1?Xc-`S6
1&C4gY$Hih&pZ/`7\LDMB=L>n_EZ;JsJU>0_RqdmP>O7O"hbe<k]A/@Wr(f^?d);Q%:Aab+iR
1[)`/`pP_SE^-/@&-CoR@!hA6s8=r)<c*BIX5M;?]Aci+OjXR=uO8aTpbc\ncc8tmEPcl;$YD
6ReTE@uQd*KWH)==`)P.RC??#k`%*^Ks/>eNdEk44ju2mqh701r!3C.((/N;dQE+b%<'X]ABW
PUe]AD*ZjjUR&Nk7"o33U@cNl57C;_tkgNU:RF-k$_Hhck$cVJ<%"W0f.(s2QNOc3309/67(3
*QIL[CQ0#"[Am0B:,<7^p'S_[9OPtSF85_*o:u'6%WOoXt16:-EN'%3-7!`U#2J+h8NF_JqS
A.6V`*?%QB3pD1,$qjA!h[XaMn<cik(g1TUXsq^?&\:Jk"R6r.mYfj_GP5>@fM$[7di=k]AB*
Rg0]ACH5O@YIoBZCQ.3`hS^I&q<7f9/eJm&B+5T5:SQkf,/hSnPgpEP)')oqf%R+nX>_j013F
&ftG>"ApYVo4RR[[+s]A!_"qDK6b7bI^fg/'C`pq0*b_<^+;l&WQ:/0g"6Z#d^`._d[ZS%!5`
NN0,+5eoT=k?SlHR0p>tY_aDgT>BK^Hi+A^nR"9^0>e\<_/"7D.W9U!9Mh*h85%DCJCf@1rB
G-=gNVdK.'abT?6GM=H49&I`Z^.\8jQ[$M)5*@h61L)jmb-.UOmYq!d3+\,@DV`m:UQBnrkp
W\79%FiXUTif"0V:"6ZJ$lXRK$AkKts'QZOT\NhZpo2KuZ4/(KMBIts-K^V=J=fJGC+/.iR9
q4ct"q@dIA(;0?@h^5*G)q!hSo4@D+R)NUj\)U)c^<"mdrkF3TBjGsq<O4HYnlT8Ol>V%/Jh
+0j'^<c5$rLg!PJ(COr#SP0'UgAKm6";9=%iXHbI%T7`N)%B;K4T.Y;=[*eQ+aQ**2b&J5+U
>0RA+?gGf$TqK`$4I_loe`-\XUIkq.]ANs>ZESj,a#N>F_WC;chEdeF.;oqF]Aq,J:^cW^l^bb
5^ti*L%KU3h#NEC2<NHq\4#LGZo">G>S:*k'3thqJW"d4#?dbk[_h)WB>R+57^"bNk=/7IXe
i*jeAVFA>lipH-W=e-oChO,+"pgr"JA,#MP2MG"<O2MC@&`nPP2BmV8=7'ga9Mr^js,N7a3R
.b#J'JMO%QELd&o!$21Ecot<5!>'t6k2!e*(1BG$E*Vl5q@Z$rM;-@2rkpMW"/kJc.-f4WM"
g6:2Z4Ou*\kG5/U`DG0b./.rmY&jYQ-X:,Bu:Gc\)5uMuHXM3GJ3VJ*PP_Fl$SkZoT[qL)]AD
ta`-`oa<TpEBhVs>S)tS[&5a@3Ns-(hjO#V(7cC$lLoFb`kD&k886Ds;mE"oM)jmgECV6s)f
Oann9!?THHAuWJ")mJ.hq^klmW!2Q?_)E$<M+UpkX'$YkKeT+JZ66Sh0pA"GQYqiZ_5'`fg#
&c/-1J:/q9B,8n*&/D'".BU2d;]AI#o$.&Nk6K#c6rh@D`:H[9jI@M_p;TNW+aE=Ld/@pcN48
:9_kh:TGaVYOS;+OP?qr/ca)]A[f;4r+>%Y!2HShsroAr8s58c$N7EQ#U&BWMAHk*8@e?%^>K
"/b0MLZe3HCj[ErX;!G[$r+fft:fHfpSDV%Y9LEai.q+SI(R?66mMJjfT)+`+m#cc9i<Mt@4
Ub^"#7q?nh5U,/859dK?E'ZM(Z<5>8NjEK);7>$m<<mK1-2S0EGiJ$B`5N=he,$Mg*i6kGqK
m8MupX,]ACkhLtoeQ9=:]A[OfFlmn%T"*ipbdB<`WJcYY@B'/@@L)G"q[DJ%h4%QCoOD`lDnCD
GbCb#6.;mG"cWt:osL-)XqFBK`eXEJ\"E#J'3*`C)QOC'F:n.95j^.lJY1RaWdJ5$`o,9bc@
b]ARCIf/Aa&9Zhj2>eQ9b?E:N1qO;0U7ek'L=P4bQ`d(q,>I>jc6?=#uVC&0-pW,Q"dC]A"%m#
5Hg-+V$[mqh4fk@"2e-uApT1]AFkZ^!HEa>g=q8ciu1jK4?2"pZK=!s,gW(jFtX<gKP_94+-r
`bX7?\DW`\Mo6:P0mrgTR0lJU&2AH<\WP0&E76Vgp;!/Q5gCKB2KIt4PO2(5"C3?FY2V/!*E
,*j/@*B@*bp!`:+Q;7GjZh-7$M-Npn\Hd4P5maOkP3S+!SnoHMD"e@aYh_3!G86L486Z_![c
L7Dnl).IqB^R`MS)_m:OhSj6!p?n-q-m]A!;]Ai>5'2$P*<)Bf6"M8_AE/g=$/.T'q+=[cp=qu
:_;pAoV`R*AU.Ine5I=JFB\2^8-asS&o_9EZ'1sM`8Js)YZe(=<o)1SVmZ_OK'?IhUT?6-&>
U`@>Fe.aY_ms[-P?rcdQlqgK<j(ccApJ?ctGrbV-LDNKuQ,.GX!V]A'_cB,#cL_c%nTWR*<F7
p9L-Md<9G_VX>AeX$Seh9TpY(WkLs8>El(R%T[4hf`oTUEVN$%jf)"n3ccm'o^8G^-.0gBYW
b=UJoPP`DLO+7B?+oI=s$j\L+7KC_>F"FF\N[f'EFsGN2Ven[+Ec;1IIlk,`jffASGpE3f^s
\NL4%aes0FUViUh>kZC_H>KF1]A%AY4!8TDe,"/YM4qK57]A<2?;PeSA(iPO6<,4Re-ZZ)Zq74
WIILc(ZgSW.6IG>5>'fg6"]AqSK?A8o,f,sl')8UZ%eU^"O7>5Uj)4#EP1t2%!_E2tkNbT%`:
CHf1(fE-[uoG2IUl]A6Eq""DcP4VA*_'ERj:8R7Gq>4rH[;u2;#-D@TLt(&NUKM6'WgqEdE5k
W?Y*Bt@RhJ<050WNr>\d`?&er[KKdeP#j9q3#EqkA".o08N4L]A['#q3a'!<`AbJt<dWItIQ;
bXR[XVVTt%+VL+Jp;a-Ep9aG+h2<in>(t-c;Et()fa&B;@B#*d0u_8N@K:.nE-&;_s$YG`_.
4QOONmj[%eN4$h;2j%:Xuh5E![cI#-11^-]A^GAMbLG2%%@=/%WIb8I<E*D?0$?0V3I]AZK.o(
EnCYtB(l<Fn'#-7&C<Q>6n,f8^QSg0r;ju_gt+!n+gq3]A"I/$0,3.JHBE>a+4(<[qe0"8'ol
&p?<ON\L'&^u0kZ9*Vf.LR&);ScGMB[3V2<%pR*t'M)RCb'Dm#.]AmKYJ#k?hZ%D"1P;IBBeu
eFrQ"0lhc:Th%`,XED0m1D:j*;$8k]A5+i@q:615u\)k:)ra]APC+cp#T&/*`#q).hP^6K+u<.
_HVk8]A65%hmnRT!*5j]A,(r\0\c4h>kpmk:*\4W]Alt*rkQ)]A=!:&F:Bs(-fJ7kO.YKV8A0*'Y
p)IksT7'Zb$5du4Su5TFF%$R<BkNTT[(&u'p'W''lbe&=r_T@#QWk6euV,qIkZ?#_.4T[@Hm
FY-sbZE#23Q`O:0A_:\1:#uGg676a(rL[!S!Iu>Q355>iMY-rtG)>82g'<0N&8:tPkkEaV_T
i*p;Vut.r5Q:!PAE;YCE;Fk&"qqMk(iC(+#j9DS,O57p(k@.hp1EBaY4XO:?[feR6lmiX<q,
]AWlO+=f)\<NNRcq;qG?J9(YRn-(a]Alb'@;(]A?m\)b>7l/E-AHZ\Rc)%;XS:$JJ5GlL'eD$Ff
"3Zg.V(Z?VfX1,2BAjcgGD"i[U9l/5`Bqhoj!7INd!]A%p2S[+>4j$!C:u/hb/[mmb\*7:k0F
2c,CuI-SsA%fAs"9)Pa9pTrJKremp>u4M%>Fn-_aq:O-l*]AM1-t_TelKM!SKRME;o4]AP&VXW
<`]A_qh&_@rZF"m_aL-"Odd%X8=U:M7=^k-8BtY\G9Y/kTO=H$+s53#k-U1!82qGI0Z8uge#D
f`\XU#<20NMkY1(b0(cD!5bc+ESeSbAspN4?r@0K2N*An)R1n5DHS15$J^oWH5;]A[#HJZ1kH
I=Q3@TSXVZXInWHYkF2rdP?!q]AU-=D\L#6QMo/H)Un8#J%g\LoB)fgs@.B)GK.R(Zp`G5n^*
#&*Fe*Q%\,SM999`EeclY2a0bFqi'BcRYr\a%kKoB7m0+.R@pRGd1.l`9549Z&8VVLmRHNA\
bo+II#9g[7UK$nSK9MZDFN#UTglBd+>'erT+M<:TaM,,,?o[[5;uA:,2*%Rt'f)ikJqek]ApQ
0+08HIK$0Znqhk8!mKNG;g2BAbg]A&LEG(LeJj.J+A\[W8cX5"!VT73)PqNRO,_<8PEl_0.3!
m:.dph9ODeJsJM?MPBI(E5[lkT(DZWc>uWHJK/0]AO)n,jcfL!>"N"c=`:N;teJn-?SkrBDkE
CTW4=PmO,r2Btq,c:78HLXL1AKS,H)L1_`NP,nInt)9,`rrPtW\W/WGo6a'T!)3h/Pjo[[ao
J[.*4U,]AHb@]AE%53Tkh%Vnkce*mi'>E@Mr"F=#j^Xl)Er;&&LGQ\e;&:Hm:V--oEf_WqR?K\
<FOT(_)CUU)Z,fFJi"@bMp;uo&nalXr8XhqWuQ<<s!+>4RO]As;o/\>63L(#kDu>f8SemTcjQ
fHPl,0=tL@gmruQ2>g+TIK4D)P404!]AXd:41+cFYp(>@KVL?<XAo2aJllS4G)8;q?&d22eaO
:s\"s(JkIbEuo9i;[8Z%7?H=(ePO)?H6lPM1ksb6dGAW(R\87dr';#NTB$kDN=?aW/Y[*g:;
ge.?;tfDE^06u$A>BR-o>`p30P^.h8'b<<)^fLQ6<5&@qKSLn&-4>9ip?C5?]AShqfHMJE8"N
Y^*S_(qhZ:rLNJ^\'i<!"Q%;Q0>Mt#pVZ#FWJBZh_'idoa@5g5umn;-SGu_'k_?2+3U`\]AHD
2rh"dIn)&ATU[J>`YYZKOc`&+[r8l=dq+^@p6_H)]A'\e_&ehf$'?^SCkIDW^.Us!qMtE;4\U
A_!9!mP_qbHZ/a6^WN\d=m3Z1EaOX13fK=g(61Ar0G`VlY57bZ/k8bs:!Ts*>%Od42\4"'K&
sW<0RS<5\?:dLESoGA>S>lIW^15')fRHcPaq0IMihkDhG/1\DAi$q%2DTQp:X0\]ALmV*9t73
Q+RO=;Sm,LnQY;H/*t?>7"HsecD(r@[cF!Hr'&X>VMl#Q6r)/tOO<#L6FVGed"WY[(a[P$,W
<_I8]A"W_Yn)16AK6)Kg,Pl/3G(oDJZ`rG`C`G@\Rk\S4CJKo<Ng8JkA4l^C?Gb:2L/fI'4c.
`<mM7RlEK$9[6IseVYYZl&SdpX+ZQ7Y3`>fjo6h)Ci]A9o]A1XJ>JNn7&UA-oIaZm?%ujSUcil
+2.LUef2@4Z9W,Y2,M&n[N\>>:%b'<6ipUm[`]AT5]A7=J;V4@06E,<I$K"D`kcZPcCH>=q7S7
nLeg=.^uU0U>Y)O/d@(&SAI?MT\%;Tp;SResnT;Wk$<E$1&`;jI!IH6aj@>fG<!^Q&BO)nKp
c"LYe?H-ER\Z3.\3O9>`k_*FM<3P`]A'^qgUu^k,SE">Y,;#8^99`hMs!:1H'-7.UWI4^p\Y4
OY7jqMFq?("=%/4'XpL.`AbdKk_+!KthNC^U-6Bp]A$"@qbG:b"24s/k,=D!rq"NN4pJdsm=M
B5rA'auku/dgZmRpaPqoW?p4Z?,78S.Mr5+d*H82)647JRahoM?R/aMXk+0qCG"UkSPF3rF?
Ssn2)Xi[iteCE@9&HsB1*s!n7E%K`k^0A/^o__7Dc%J3b&-!r$Vr$VY_L>BuoRDtOd3)RpBa
A_Y"hfMgbHR=5[VD9Xj2<cf,$qW(OU%sg4re$L&bX8-Im"`%:Q&%L2Htsi8F^jSWL#<$L#Au
48]A-*^qhYa%p8)QJlg&niT?dKVFs(c7Gt7SBq=*L^V,#Qn]ANZBplO^?S]A5-EB-2!Q8rd9O2K
S/;dj-(Q;2V8IGe.fR)s3Na0TrZab[jc7$L+ET"eGXND8en-HD3bBp(TlfCK)3B*8)mE(K<>
K#c'cP*/R9t!La/_4,;*$YOI`K'YL0VG)29f+)(e77nr7B9WKPWuT5g^UTC-bhU%@7Kbs#rG
mIc=>.qBVXlh[\,`S&'9JIf%<3@+AOjCrGID$)UuZCon1OFuNIdPZ>p8Hls3BsXd]Ao,He<f@
mKd@FNfLTVp=#$q*h_akOCb?</-hR3<?+#eo-(T0O(Yqt@DCa($;=ff(/EC$RrWR"E0.Se0;
V<5V&FRk[@S-#,p+#B:Mam"CU<o-M76-)f]AP4jrl2mD`]A-nquknJ6>?ET!%%X6n/:"+cC[(0
SQGW_sR'i$;Oo6`.j[?R'BDScf/GUoWO>EU?C1_I`+>"5dS]A8^e7;s;pj:^BPKajn/!,fcmc
7s$YR'>7YoakS/4$3h4.?O]A.dk/n%%>`DFO-+]A*Fe^6uB9R80tFR9n<8sLS+i@K"iIKWMV`4
"#]A]A*RYDWBaW&<:eJh0k)c&pd\>2X1\M7o=b:jZt?4a8.m^&`=`9sF;nCoo``bFW%*(>YBY3
\th(;CJ$'$$NA0:n,&:OJ#2EZ+j%/E-\(4q-^8&K8Udk"N+Z?MMp(L^.e6_-2P"X$TE386BJ
'QS84J.:Q>+dfE5,DEgYHEa6Fep(r]A&PVEhtN?@.jBgdN?n(/N)QoZ3HTNBUAKe&QE`iM,p>
5L;$<r2j48?S;;@>AR;1he=*cG>Btf(KB*Xs&J]A[]AGBo*IE-^8\O9H-@;k->`G36@oY/l^dP
F0[;>uS)sqI"eE9<6\bBq!XP'j1";?=A>S+Y9IPV^<nk/OFkUVI#DsX=OAqIa@D6pp60"c@+
'UUuO.!fZq5*L"O/R#_3h\-Wp;/Q3m[H27'=+sjWoG?L5#8fuV]A*Ac[OK"6/^?[Jkk`7LS.7
(M:`uc0B(Er&&AU=;Ih?+U3>*lXOlo@rQGqJ.96[YLgQl5N=#dL+PoK[Nni0WPuc^r!G5@*L
k'H+>iT&Fk@]A5mm1<<I[n[O"`C5_uX?o3NTFlY5JOY_Y[#m>J%Dae\X=ZjY!hDo-'s<L`=B9
"t*`T1F4>:/9<8;^br7=/F[6A,'QE[IpeNLR;gXc2!Nt5[EVA;Hr!3=4+PV5*cYRUXAE&`*:
@196kk??eQh;QFql;Y9-ZP*t[/oAMZud8A'(]A/q%tr3*6Qfh&$%f2F[Gd`^Wjf,8_aQP]A)VW
X]A)5%=j.mUi;3R,ku,UZn*^@;Y[<Quc\H>i3q,2]AgdhBc*_ff"1g+04=qc:oq9:NaM_AETc2
n_$]Ac@tki,6Jtp8k\rme&DaY'W^Ye*A7G;b2PcB%X#g2a]AuIkkjEV0BkC*[YZiI"[$18:VsZ
2.dWfPC:Q4"R-S:/HB]A2J<go/qgp2PMHHdFuP%lrV8mA"U)P"`7YCFO"=KU2Ec4XK@nBr;[&
!nlflKZV9q!tr6c$50qXLrfY*V))'P=Ftf0Ntqj$ug@^Z1WqLDi-=.;TuE`GFL^cbioV&iVN
Nd^GJ16GSP*2?,>j<-tq/dNpaY2RG&^V1YMV4iaQnIP)2JHc_\W;Y9rjI<XFIE[(gq\C3]A@%
gkG]AS8UT<aS]AG>WDX_pB\,gG@U4'p$qV;1C:SF)R%bB8Jf>buC\VMtcQu]AohFO_,*iu+H,CN
C<N3fHL&:$n.@Pb?>b0qDJKVkuhF$osH@E@2=:#%ANCOj"?q2&K3$dlQtZF;]A]A<,&/!h.hmB
WTD8E6hU@:q?Ak2crYV2JW:8YLQKcDK>LV6PCHf^^dLbZg_t[jKSU#E)f&B;rq7h)9RI:KfO
9St5c(5-?htl8>?-J&TP4!r'5s)U*j+AEMW"%@[q)Zt+Pk+40pD>lcVAs?='0G/1]A&*DbWR]A
NO++.6gf+SW3(/O89;F^806*Gr3rDdq&2gYc`93TcQ06I*pfDHo#>hf+^Du)?BL1)9j16NK6
HiI3A`O/1$J`HfpEsB/"^JR]AF6kld@_-Ou.cVJKKE1^T]AS8;@]ARo!Gb7)B]Aa&$p`gJ7B\uFA
u+C+d"ZW2roa$g[2&22c<:]AEY<=;l)$uaWmai]A=\MjD@sf=cgDCfc`An&J7*dYY^l3_p[r%G
[_2WTWeGBn$\@U*s]Ak9+:mGJ7O`3+4"=kU[H"hZ9R\!!d\;l0#N>4[ZsXu^aGQ`mdI:T-`OT
2+tnOHF#"&oPTHg+J^=^\j*+:qr2E]A-:tP*,;-)"&p5.rt5s4(D.9CdB:Wl2.nA!:69/Xc#:
;R+k>I+[+'%E?TGL`*:!MTkE4Nq[4<R]A3q*/HL!2-rpC[$M6fOrC\V$RFqf,]Aaf4.JRbUuCR
$!agU&'&&96Ga,$nCm._"QeLOT_H.=i,"1S2AnR//MsT]A`A$oRd>d66ohn<4`H>;MdJBt.@'
+2p9/uSjk46q7rCAQc3]A+fi97iLcpe'=To,,ll6h?Es55gUqc';p[X"Ok'[Ds9%NLO.<j?-I
S`uH@A&G\\H"7,.M:HR_\Q?8GmnS_p6NK-aio5c`Abl2FYD%uSj.CH;/VT]ArogUu9S6AC)Y]A
RZ`Bj-k[!3<P\Y`lk=,.%$sc,%*Ds)[>DkW$l1EN,hcD5XAe_G37IipY"A8s7eal5PPmukB"
rESF\b5F5GgCJ`k;jNOAPEnDknKpX\dG,K>3-m.Ifs?/G<`+XKU`>X3$mX\$cp5%.&2"P:n3
ZuF4=%CK!'&DIUHL$*pj?*$=017392KU*fZdI;dMFQ>;sr]ARIabm%n#b4<qF6$EMjj5&kDoe
kocm(JQeld<2[8Bu.n@!T`G50s`;p87cnkJt6K0`H/\76/X9PGNi!^Dj-DN\<__r_E'd^@8L
-8]A>*MOYkjW*To!2rtO=:.X!,`)fHhDdCT!7BD%gkaM1GST-SCU[IPIuk'sgYXgO)Ber&=l]A
D;F0lFh5pBorD7B%QisGi]ALYNW+]A'Vnh`1?`P4Jk\^:+f_W(rhH]A%8Y>!id_'5c%H';E0CLX
!Afd;'ofstos&Min)1Kq,39TQB3N7!_3\/s$2+5@'Egq/50M3#M&XXm`E^"Y!$gVrQji).@J
^@AL2G4R-6KAcTYU#G=+C_m6sHX!GA5M($.&+n?^2OG7U83dT#[kR<h,'Z`pEW/]AnB?'[_;<
537XP3C1g%Bg1Z,NPD>Y=.i%l1I=HhqkEfiN[14L/VuEbtK1AEm7L[oJd]A:Uj_T-[`5e)R5j
h/);/GN"pOXd&A&u:;j/MrqU.&atFt"ke5oGdoi%.h>'>YnZ`;D*T?0E1JRCc]A]ARU1Dtn8:I
_9hkOupgclCC.'/c_`Ajkh)'9+$_69Nbg:D`uT*m._ic355H*)eT@12@hA,WI4GfaP8-j]ATj
)9VEH(A5$HJSUW"CR*1M0oogQoh6ukBS$US.Vh@SapcPP1"AcKL"qq]AFrLO/5e,Tqtc)s@X0
Y[neSs!l'k$h]AKOZTkm\P4j&EPJ"opY%>'>+_2++1RWNmmDM)OS&sXf"m>['MrFX/nPs+7W`
SX(m_SM>JZUEr[#fH0RucueEFcdVqO.4VMT`q,dfFUWm*)JW5f*R/=b?sr!ds0lqt2KXg:pj
s;GG:5?7*1KUPrW8R\i.GCVUa!NQ/9+G$D"1cLJ?E?2H"jaDotpD.a]AC]AtJ4UOB]A`<PK(F%7
HblTdm1-J'4-E"egd)iM(DC:Z)WbZ0Z%GNK8/K_Nn]AMFi*ejD+uR,FMZ3Rt(lrU-/WI,<dj9
ITW^Lj0i[2A?faqX)#n@d;[GkGn7ZLQS.TPc7W[sHVL6=eBD#!W\AC@)(@r%HT/MYh6a#R'H
$=r>JrBO1=FZ>_h-52MTB2=A)^c,Vnd*r:B6W=E_i3]A:>)N5a<T-DXXSfcFIBO5bW.^]Aq6F`
Q]A*5/d)!W6+MoZR)("+Z.L4^/*B3NWXGhBI,ie^_qID3Rj@92U9m.lfpB1V[]AW@<a58DWR%,
5f*"r%VL>KEiX'c!(uJdn_F_[i,1ke@)QnbsbO,XL&9]Ao$\n[cqSW9">\qfbbMNWb7W!_M,n
9UXI1s_4A)fngmjC0rh4R2NVj88B7m-iPC<7+tC,9.dRL9K)B=RZ5K=eX38p$,BEF1Hr@!/W
>-Y>:n/"9J'266>bfK,(gH&L;lCTVqf0C1(^eC5O76!r0X":t'_/Z>&<jik2I(XI7<;G-RC.
Rtd2$>1HM`)kMF599-!2@lm@B^?9:M>hsYIQ@fKM1&@a!i"V3uFaDkC*rL0q6`ItgKWRpRfF
"X`a=O9tP@[S<7Tk$>Q@K\VfO."iqZ/.1)elpC_hoRhnJH@@67d_XD@$Y2L+(H.C/Q'ZY&3)
/6Bt^ZM0-,IK8gWTm$'##+p>EOYV%\,T&8WQkZ`!-bn(_d+"hD(YiBP(am7N)4O.:15bnree
mX\_D4D^j>188*]A7jN$P^:!Gg/@^DFu#oig#JN7";G0\A^Ph9T(+m&D`ZPg?\7;g"%18mP4G
Fjq[*PJeTDtbWit;cN);/B%e5^Bk+A&uNs,jb\-6Ih.j'qiR3k0#D=T.!Zc.P3=1f/30aMg2
-$Q/;lm*o5D_7o9r(D,mb/o7s*i5j68\B'V2e"OLR)qbHqR,jY90!]Ag+V-W-aaL#@O"m;jS`
k@KR:cf4TK[LgQGj-c"9AD4a(hHHAj5&iT3iZ[f]AaA/7pOA,,uPsocCnNaWa,2EWRP6[<heC
lRE"5H*>\g"lWcPpX<9-_4:"6b@mTupAG^2lSDsg"0T$e]ApaZfCW^Rg[/*+YQSQ]AeCHb+o/?
/_!UKM#3t@a7[l^4llX1isidR]A7B3=fr3TJfeeC@bN]A6&T'A%<2XfO\A4R>[)bSb"oG7_(fX
bFTRMpUJej(tLjZ9)@,u%3@l5/^[W>h.,5sl7B0g,@phG1.4_&e@r<M4^[PGk1m6qYkMH^h'
0IHWs'7Wk0b'ccEdn6@8YrU1J"B&p-mG7XLAUUYHX8Q(]A<`X*G%mQt9Q7WXoGqnS`a1RYCOQ
(V8W]AX<gVITn9J>QecU=LH&U7R[^Mt,XNL6>?,Z7hj^=&hKWck-u8QPKe-.[Y.MXa;acA!FE
c+)>I8+YkFD[!<+"dX!#.0;1JjWCLpHgJ?GK,U*jLrc6\47)%0=,Kb\'An<oDgSQYgl0p0%7
F"[anH9#$FElpe2=FrID$\j`rN.D.flh$nqud>dUK$i6@e"1U.FV]AgI+,T[]A$2VXW*X-QO=q
c6XJPl*PUtR<a!cU73Hj_oTU8:ZitTG^%P(UNZ:KNF.RqHte=!,e`&i4[@J+.F_@%?gdj>Q*
/P@S;>eYNqYORb3:c<uWrpS&nVlm=)#[(LhRi_S\+#`7LcX2%X(k,s=Cq;m!)+rXcb>)_!Gc
4oZYC(!j<_&b6lC&8-a,*8hRpcq41[e(S(@BTH/nueEngh@8)(!bQ(lKRm&&mQs%N&u7.c`s
n$P?_o*n)9r.D_@cBks0MSs4l/]A84<>R$JOQ+7s`T3]A?NG.R'@[Y%V@S=*4aS+m:iKn_40,r
Y3MApF<;0`B^]A<.+pC-bkVtqVrFts;O<,@Ik]AFW^JO$&m>`c7i]AGLU=f`ZK2o)!G$heTIpbG
ZG85'$24Tsli5=/D+36Mp6)lsGplPtYq1UCY<R;$ePRoEdZ(HmD&j-lChed66!i*#2_*gB-(
o*Wdq&CGgt721s[`%%+AF6YLmKIC@;a$c.l-_*k*UlEh;XrM#4C!VO$Z@n7j`4pb3I[]A\==W
jqq&J/S'P`=IOEI)-P@Bh*@7+nEXq,+0U&4s,l%Q.kOZU8W$NYt'9Q[#`n6IeoKLf^2'S)%/
O1ND1^qRm+S2D*(26$RheGL(T0>uJ8:"7,V;]AI/I_[40Dh>V,UsUg3*97E31r@D\:Ga9_37d
/VVT\4e3&?1i4f1>K>!`RI3o:1(dYe5'/!*GK??E,pd:ll[LDRQtG+*tqIm%:#&jal$`]AZ7g
@^8Cbm27'*da(e)A09ek16MCFT1e!ZlYd1ki"n(T]AtSAh6i\AK>8pn?p*I%/"\D$EV7nqRMU
auQ?jhMs@he3dI&WNPc=0=#XodEVcl>3lm+piJg#ik:LI1/*mLh&$4o-eCj3]AVD^X>El<K3\
6]Al;-5!IPg8S_$giGs8"*\6Q*e"0li;\,05V/Jh6+#f%=;*LmW%s]AGX,;GJHC1]A\k8`_g*N3
_,p-(B`[ORf]A]AN6dT.,trN%fMX8LV"O<oE"&LYe^ZMc/^1^=S3j8O*Gj2Ms&M,BeATL6,qI"
It-2A3Va?I3CiV-K`PkHd`qcP^ck4Ai&RV'HRmLX".&_U'"!c(]A_/!]AlA02UD*#&!9r\#"):
MBBWN!YoH""NI52nTAU_m#b/i(]A$Xh&)+H#]A]A(HtfTi]ALN%CP7#>'M8APeD;Z0]AGVV'KK[<o
c/-F^l'QiVTKg7a]A9L2!T*XTd(%+-&eA)u1BN*Et#.uXe+l]A1Y1NKQJKQk"H=$ru1g*m"%U>
=s!Aq.7l7!1kf19aH8e-RI,%fih^e?/8-Th)CpS9$r[SN=GeVC96R;_k4EZhVlqf9bdX6!).
@c5^EnRJoHrPH/frs.EeG\0J$u*[pF(rG;h!TnTP&?(_nDnb(q,:VpK)p/6[EeN\B]AP4u*X^
`L!nL1Y+RAnZp2BSL,4L1Y+RAnZp2BSL,4L1Y+RB))BH4uLJ0]AJckJm(6uq_P]ANpE^S)Oe10
A7p:AnunCQ:RL+mPGEpU#8rRU/qlP^ouX-^;GOj@B9I,/UP2DcBP)oE3NUFT(/5<cfnqWYfa
&+:b03I1V-~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="557"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="106" width="375" height="557"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 12px 12px';
ment.style.marginTop = '10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[485368,1714500,266700,1143000,485368,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[472440,2743200,472440,472440,2743200,472440,472440,2743200,472440,472440,2743200,472440,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__126312B0E8F5EFA2A2124D595C7C93FD">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n%f+beXK//Q9kG0efg_nT8aZWL3:#Y+bl4#Ee%nTBNsLEpV9fo,
$g$ko.O\+*__Kp%Z]AEpm]AYS<l7^KO]AUg@#2+f:N"W`\Zn1kcf3^V5U\:8GjcB]A_!qY;NI/E_
hao!Yt-hcneJ+p:J]AWCpZdurP@g)oXHS,ZJr0@\pL*8ibi@E50t4pNIKYj63,1um0m[5VIDI
G^T4FsghK.b/_D6#YmLem<#g?$+P?UVoKKh<6duik-4'*Ss2?c[P!5GNihjr^$eOqs2l-5-)
1kgDLjqmNNk4ERq1(Eaj=kI`2PD(Xl?[Me9SGSi$96F#$tbK'8Oe7@?oQeTo#`(P@D6Vlk.l
<9NsXODi*fl=fSYT0E0*ru3V^RBNihX!;+iU#1TM&T9n6Zf`0mG8XH3.[tWFhJi:q5M(lBpG
B&OD^\=A+5gDWoBD6Gj@m&*DrOeY!^=nq&,]AOQ2"pl\Ypaq"?`7tg;<X8<4-p._-VM`(P8X,
u"^.l$0Tb1)<r'/H?X:DfEn=mag'+Z[eYW<,B(&H]Ao_\H`=Z:)E.."V!FS2VUHBI?eM)!7dg
VPn*4g<X[S)^.AcU!+D!=BH6MDM=O3SG+=-r1<uEFpg*MmEEirRUo:>^^s%^="sHD:'H>k:W
sMqS]AgG3a1-Ensc`(p:EF6T7?h%D>)fk5+bRk>FT'u8?D%P)6prA#L?a.^%,(&HFC[#Q'Z\R
8+0f7j)ZHhP%T4P9o9Q[P^R<CoBk^$6D*%S@cuNIE)t$Wh7q/k%mGc(c'KgjmBY3O^[:4QeU
^HfHQIVDZ[0H8nm6dK5g>U9DboV.A/FYoFh`C+Gg3D>rJqb;LV+=[Sba_9H#!&ZH+m^a3`ic
tY:4c9)-Opuql#P*IX,8(Y;&kVW/l:Al14EZD^R*gJ,V+IgrQuWPFYePY[0Q[rp]Am#Rck[I^
UlF1=.T$N<!CTihh%of!b2%1p%G$"'KGooqe+.(-!gh+E:D)#E%3OamISf=.UArmlH\6c;:q
#k@)+OeKuJ=)TL6Q?L'.uB:8%=t2a#a2GYfMQ#\^Ubj2"^W-H-oR^kh2[<84e6Y$>N*H1#Mk
mcfCBQlQfB+C.2,b0TG6P?Zm6D)lJK\+\Rch6-b]A`GF^LB[&)#XiXdMVJaAg_T]AhCE(<IX7P
P$'V5PfU;eb_b\8]Ab=5:-c7.?bMAT$FPTI),=HS'$\uU'#Hs$4_Q"Q?aWuk@L>Je"$R!Z>.F
[*LdOi`4mXblRi17c80Ne2Y:0lV]A"4&q"@XRamA)fcT7PSHJ]Ag)qmbN1=etnI`uDIZ#;j0&n
%@9npdTL*]ALKG6Q!<X\q'lS?.dHr0p?GttCHCPlgS26i3bNqcHki&dRi'NFj2&uQ5t1#OGm`
s1q"jHD/+,MKEGDYWNR7#4lIQ3rWr/nghn$tr_@c7'P#Bhg*S>*[7ohX(WWd4-Gu?/6H[b=N
]A*g76n/mpr:;2.pIcH_u01+O?anTEnHE-`ZhZjaoU<cVuj<saEYMZ^_@)@4-';s:S02Ga;H[
FE6r3S!OSNTRl1ec0%";Y2MT!!Mpl^STV!!~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__5AD7B9A1AA7FF4BB867A654125D83B1D">
<IM>
<![CDATA[!Mfp(q2%pC7h#eD$31&+%7s)Y;?-[sGQ7^DGR+KR!!##i2UVR*#&?du5u`*!m@6=X)b1Hoq%
NB6oM^dM'0rBn`J)bkSTJCM!cAu'"bEm7+;X49+DM=a0kA(>.8c?K#F6mnQ!&p`Z`T;3^8*A
iW4Tah]ApW0N!3Z_r,j4m3R@TmS?E7(YH!B;DDgV%GWUKZ35MQ20]A\Nn6mERO$jcgZ>Y;8>1O
!2M`]ANPQ0C/Fle$(N1]A**;5t7ubEH_+KA=.lHJ$`^'u7H@\B\@+Cg#F?t=pU,uetXt,sS:AC
AJ/OotSD^SYj"F&*$T?pr$8>jlb>N"51_N5B+e4to/6<7$!5.E#>ABqSZC/3&QGg3iseBWqD
8Zd*AE+Rn6?_2c#e"gT%#=VXh;YAUcjZ_UnacP@>;'!4a`=fkeBdnqE@mTRJW)!:EMU`*_B6
Hu$`g-2_\7TGYd^-Oe>6+(i4!HE_G^Ik?o5AkAm>/iFr*[:9rk'V&6t?SmoXmW\KZKg<YlJr
P!n_K!@)jRD*L?bL>QWPt$"1e"J/ssVG$G+$EB+>MlhT^WnD[)_E-rD6]Asrn_N,PJ&_eZ#kf
?rojX4dO44k+ogi!cOaH5YRpRFgq7I"k:uXTn^JdkKL(q6^jb2#He,2L;h+L&;i2/Dsl8=u?
?32:0nThY8Cec(.QJ5]A?Q?P$buQJ=DGM1rFO4+JTt_-NR_[j^R_5H9@]A!HuRM]A9$X_pj(n4*
E<7ga\?X]AeR5JAIBfXGJVe`_k;,GqTS-q!9J?]Af_XHnb_I-%qm@L5#nGKdl'o(:b_/7Oro*3
%Pha3=t,ci2jfRfC+3NV;4676`p7*.@_V^F2f3NiAHf@-ZtSW4r<$paZ,3h.V^3(PU+i,X[6
_]Agi'HBVH;>r]A7Z_7Gh_&Gn^j]AAj^012(QI:75+M[_jBu%>K5!u([6D.L7oYYQ@RH4Sgc2%6
\Ge2b;el:ZN3bIYpojdqcibNJiT?I4P9`f]AC*Z8Whf?_ORq7`SC05%^fAhSi5O;]A;l.a9+Qr
\E/npaXi'GJn,RUCC1TE]A0g<J[@j$EY`Ois2\"lS!F`TLT-ZHJk=#4gbJP,0AG3%jeV$c#2l
i*_t:[Uq)m)=(X.2^4:J;NE=I^dY"NC]A4*SOM+ej]A1LFMU_(K-;i`FJ^dTem9%?5IlY1;smP
;RuiDl`dU`NV'!#M3XdNG=U:4(;rQYI-'Yf,K&l>a3=0N)5F7fG/R>mC'eTuS-?/ot!YH<dQ
QS<ZtaFT@g53N;S0V)o`OLbE8fj2qRF<fs4o7)UMh!-OC.ekNbJo!BKsRM8m2OHN;(;O=.2$
Z_D(*=k2BEl*G4i5:c>rDd,q9fnS@"S,%$(8oH8Y-FN%SCFRgX]A)R#0(0rC?08)#d]A-PKdMh
`SPUO`pF]AgpE9<+BF['GGnS-08^?-3jq(T'JVI@soT$PO6#_n'GL))06qRlEFFAYXek?js]AN
R8F,]A%,#/g[:ign1Fp7;<e%W:!HJim\ZC9`!_sF%=m7-Mm;W['!Lp68lSNM,b9!l^^G(=\Md
Ftbo:/&-A/qA22.2VRVRfL2gI3^&J@YX$:hR?A"Oplg(d^`N]A+m>k.=`)Mq&q;E-TlR2X9K+
#^#94L#.W4aL,c#FmV1?H^^pi+qF;Fo/Z>`U$uO927XENC>TCT=\eK/lE%FCu'0U(LUXfaeV
Ek;8k#uX5<I&P6@pol`Q#.8gJ;VTt:A_R'3!D2M^#<KHk0B"od.&$GJH__A?^IEH%p1*-+,g
?,itB.f9)q%+!UR<!Yi?A3N_9@6/ck=K>4$2]AVKP::'!NIjVfkC;8H/7ojnZ_V\K`:Co)-T[
"aJInG!ojHnFiXg^lPE7&K(YOfXAHLG_Oi`OWhNRE4#jbGJYS>B1-km(q!gWo7J*t6_ZV!9@
'lfd^6*2S^P:;=eaP3X`_M:f!7j]A]A;'`#.oGYn]A^B>a&T2FMT17=*MG!2>$[82B_c9:Y(.p;
S:!NDT96\Q?Grrig0"A0gF_*bF7@EbM5djajiPt#&K6<TG)%(HQNsjdD8runpW="X_U-u7U\
I5D]A-7PEd.VcemG-Hl_O60h@I^'DTG@8*jZsja;1?eOS.QA^r!*A8X?Sr<o#M\jJI5iMXq$I
HtltjmtI>m-$/8Kk6%j0:egi='('`\46!(fUS7'8jaJc~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="1">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__537871B7715536139F674C85238FFF74">
<IM>
<![CDATA[!Ds$'qMA$D7h#eD$31&+%7s)Y;?-[sGQ7^DGR+KR!!##i2UVR*"W0g!5u`*!m@Ck0gI;m3nH
`s@@&_.k!^D8-!cf(bi!>T@:X[8.=G:%[0O))mBC*Kp*E@8D-&&C1L7*n=/S#Ki@Ohp7q0kn
s4U@0@,Y6q#ciF[V1\7U(ONY!*E#&gh$!ERFn5gQgT\]A8:CX(/?<Sn>@[Qe`uOX:2[dG$3km
2ao8]AsXa#J(7^WiMM]A9/b:G<`P->F'"FrglEXp)"'"LKii$(XMh]Ak6TWT1L]A;PBC]Aa&]AU3sQ
rLgG`0XJ^[jGTP?!5)YI@C>"J(+8?P3j^`:btL2=q:-8J3]A>u7U"8V8Ps:2<"Xgh9$.A*su(
7]A@_@6RTGP<Iofh%a!P;EcKl?Iep5$Qo?k.-O>U(LMF$?QkCAul8F-4^]A[S9N9Z\J6korB9o
hLf@$21'XC&bcAml*RHO%HpE2$bZ_m<T]AZ%*S!G)&-k:_%o_fs.dB7P9*ZD:8W'QTGdU&ru?
`lq)^?;'Q>^%Km.EJqX<C_LZ+R5d52M=?*]A#r;5)Y_bVq3&]A9N>f^jZ%_]A1km%-'=Y^WE&X"
</VhhDkL\F&8>UY(bfoeQ]AL4(,'q$iYNuB[r,1s?LXRkQ_ripWJX+NVIT#bDpLjM?Tm*Mhu<
1C=6ea<_,SDG*d[:kSaXGoCZiZ:[0XSoB:d2G6#DVL>[d(;"HRj&o0[#.j7r-90mgYXGg+>M
SoMS"Dlg;>$\FH0iprGphZegLC?(B@kLo_7_'MXLcVb_;rIEf=,YO@P;D=6rR(P(c=;EQWA7
Z5(.Ceu(i055HoAZik+QU2E(4u.J(Vfe1*dc`TS<EVB+(lAen_"TL]AHK6\8[E)VmGRDtE3sa
:r4JYLGDSD"#YkEVE0q>doO)b*-:3l/qCHji&A/"Ss%D?`M*8+"Huh2l%g@B94*LtUJ@+'[&
.B\kN^0gf+BUT7/0"p2%Km.EJqYFEnDX>j>YAbp1TqkUABu?Lk<OWVL9t;LL_=N5<Md(78tl
HX$LW@.0Ea`s"BRj-Sf.!Ph;?*4Qd7s:eHHbWn]Aq"$2?[)PE7#PAdP=m&_(A*/i1c?U^1o.-
V*Ju*_%9io\>;H0Gl!$VQ\S]A[e5tW*2?YDGA@o3T]A#ZZm33R6fE'd7,S*.h.a+O![2?YDGA@
qJ>VV?d^"TcFNS3%i1Jd]A$o_$^<r+ZO[20E`20'7q#'"@I.rJ7&X8ga3.W#RH1Cg]A>4q-tC@
o=UX,,ZQTQD$309;D3.;76B9/Gj.QG00\J'A\YmNoPLQIS_8PX]ADJ%aEoVh_6O8[>P-:RjbV
-336GCJ7o@^Jl"JcPuV#R1iF@j"l5&1ee,((LWlE@1e0'th_\"=OSsp8u&=DmLem8g/0@+"d
Gh_jq&]ALVDO0=@'R`E@1h1U"=1-QLCq4\.\o)i-VR@Mdko(45@pXe#9Da8)I;cr!<Cf7)O'c
j'`kh_h.Z%GX`DUUoeN;PCY3L_c6<'MC$[(8<b'KIT)Rt8W\k=mE_%jZ)>clJ>%;bB\T;M6h
=gkHA]At=0ZZZ.<NE+[r]AaAkE'.GF(*%NAH'uc'2\%>9!KP:P6Q^B%[:<:=M?HYdL3eTohC1-
JF]A2)7Z35nuB%f))(*&JR"]A]AX%WnZm2XV53L^=R%_.X+Ll5+N$cqcEJ#+;A""F6[5taeg@.s
!EGRI$od?<,XS5hN#%<hNk%7rWB%QahJt1rXSu/!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="1" s="1">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__3C0809FB23A47CB6C8A9E2E4A138D6FD">
<IM>
<![CDATA[Fd.54dgI#5VsK@6<+n-0QX!=%FQs:j,Vql0^TYkX>[eQ(0Qi<f&m^7R17-ckj>&KW1RF`2/0
^=[Kc(nFbAGd%PI&R74jW;>hg`B/qgAEh^DTdZD3^g,Tcan;q=H,CMenQ*Peo)ZD@qO?"bFJ
EdqC6j;UG_1"4+m#T![[pQoi)prSdOAA^unSqC5nW?4ir[84BpVV)7#qp;PAtg9)>V\^=#0X
N@e14W"9V)5k_[8f<GE*3Q06@6kB[27p.;B;qGq-R;i$eQU,(I4Y(`VaksTVMWoE9dH]Abc;B
iVc375HBT-6LSPT&bqM+I?5?:s:BCW,>I]A1%mQE4Qd(H%$b9A%EK.,;5Rf<p^$?e`(SM!(*O
0kOpP3Oqal&6le6ZT-6s.rq/hPTTOKB@r6+,EK3UcfoL8-JsELOPChIeo#iYqa14S:a:lC\`
3d1_G@*`(\);F5+>8a4@b&I\=g[jRV(:OjOC9+7[eZMQaE/:cM!.2DED$P./%t)5.YI#pRio
eS"MK3>8oE2rpZ.lB)@Z\cO_h$9r3R+"V[)J<IE2,bYgfM@t`53=cZH@%,1R%;j)8a&tfuC?
rZB,23I^\kYro_[GjoEMGeUgj2MltY?',g?,E:fi[;cSJF1dU7F;hHOl`6:X"1#.5//;O=YF
RN[-,o2O(F0`a>X15/#aiMqTBUlk;*as#5Rg[g.[2en6W$LLqI[fs&F-h#p-VMI&iu<IUE9[
oEU>WO8Atk^Oi$)G8*Z-iqLEId_i&5kD5D%BALER0`c/qKBu:,R-g+FB&=sg*r!?m)o"KHlZ
iJnpI%l<obT4q>NN*3Ii/GL-J8LF1H+99pRITA.%+_/OojsqR4hh^a$B1ON>)-M-@tVU_-cK
f!fXfjE:=R!n_G;:.7:.Tcma($1pWdTGbL'`0EPTZh',pAJ^*Oa'1\#KKoR*tFnM&uFRW1eA
=Ki0MbKZ><\SpuNLn\+q@?:1.#E#?!Pjp)]A:/`7(U!JIb#H9QYn#f>j9g1J.aJtLJZR=KL_)
r,kr4%V-]A9*&9k&qaocKRF$strNlGLN&>sq,_o>M@&[QpKqP@r1E?1-24fb#c+\)Gq,<OFc4
!>p_^r7rr-EkJ.]A-NodZ$]A?W!2d-5>help%QRT=`!`X&_G@!miX\^+*3\qf52FaBF9!q%3d@
]AABQ*b.BmC8KkY[S=`@dVb']APge>?J>aXnS@@nL&*8?NP?dN.qpIUUA3A^/Rtj?(['U@)_"s
g>]Ame:[O<8MqHXP!F)9Yh4/VXX"KmmX[e^_OVagAZm_S;fHieJ"ik)tdfTdGD^8b456dt7WB
u"mpcSUoLe<RW<!Xs<j5o-AK)K@&s^pcg/_Yc2_W.&?B19lV_[1`u>CbJ4U0YdN03NdqfUCF
B6]A5K;n>%'r8U0fCO1e7Af$oP[<*SMQsJP28OhIBX\Y6t..F4'BkUVV##>Mu^j\9PWMq_pB4
s"7F6:V/(?Ib@`oD)_P=(b@p(XP[o*^l5/fcJEAWI,3()8ZYi8GL-%8=6ZY_r>EU'a%mtfNu
S?hoTqH?"5[5h6to0m5E!h#</2D;?hIPoY"kDcT*3ZmiBjsbC2SUX$N_!fph^`5JR6q*)%,%
,As@N)j9m!_co_.3$Xi8T`;`&(S^6a4S5n-3k^Se8jmF<T;IP6Q2'hFNj\]AE@%[tPD/g29'J
#;TMbL5'ncs`kO<uM\l_Qp1\?X:XV*V04V:[!0b)'03J/6?I>$mEk/HT[a1hP..6*.a;nCV"
R^;'B8,HRk?=D/;*]AW*[:6\4kT`=t8QL#UhU;'W@*D&*`Yl?_k4p'1_r-I:ClNeh707qrnP_
2AK.8oT42:&Nd`#m":XM7lfi,IRRBr*%MKQNo3+HQmn#)"DAT0S&>O3^t&oF_?8Zc1tQ>?dh
GE^h#@jpVFV^HlLWpB[;H+Y6/-99(e7@*q\*ieB-ReP(#L0M4>ZZEi7:k1eq6`PS42&%`)6L
9~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" cs="3" s="2">
<O>
<![CDATA[ 分支机构\\n管理]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" cs="3" s="2">
<O>
<![CDATA[管理指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" cs="3" s="2">
<O>
<![CDATA[综合排名]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" cs="3" s="2">
<O>
<![CDATA[营业部督\\n导跟踪]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<X+]Adb24H1X4uSmEmPNBjR6s8_f^lBdkHn-(.ud6$HLq@Tf[U9>S5*QRNYfQ':>6.4hU69r
0$X=uM9#"jn#QOUMh?5iNeP&(!N'g#r1U+6PZ1q;_2SJ&&sG^$fT^ht>jWqlWB1YHEmMCsq(
6TqV"/djYNG%?5:7b0P8N'dM($cT2ct\kWb/s,8OsiV`3(Hkb`'m-!_F^mgh-g!]A^ST6BhXi
G4LKj.9K?VgUXDf`&:srR'Of304'cs/u(k2:LI=cQV28+]A@dtrRTq6pA;EL(qFJFU4&$j]Ak9
m/aa<`6SE"<f,on5?8BnC;(3:M)%)N@\9l(tDllqO.(`hqh\4B1=-J'H7X.Xrp[]ARQSCX&2)
.jRd]AF2a@"Q$kLS>0(*]A[.NWJ]AN)Uk6%@LtPYQ`'[>)\e'VMph]AbqN,QO!_dj+Y*mV8/T(Pi
qJV+bEnR!/.X\X('%1%f>bo/pH%fqI@NifDamf*f;]A#<`nkj<doRbA38!pGW";F0ucm6*SEV
'Z-Z`I+_;ZsCB8*^>Rtpak'\>^P'^upJ_?oUo2k/FQmM%DjS8afO;-@C%:urb*_Vs3Phu2Y[
i-03o\`_NDP"n/mt&@'Y?JFcRFD1&jt$"ellPA]AYEBDK+\sNi3Fp]APLO=N$*jD7QUt&o2Ri\
buL6M6,<Qpe7]A6:k%<J5[`;1j9i[u(M*S.19?4ZA./gV'77WtM2m]AT5hu&@ofQoPH>C2[,\n
pW#nKDK#PD1Fcm_KO!p!aEdg@dpm4GH6$:r6B+)`"Fs=()CZRe0sU`[HK<$f[_c`W,\>lOZ_
iR,*H*M2%LsGuHt.,-(ZBRG*O!*q'C"-Yrs]AH>Ju.kh@'AgRTT8QoViT`#h*/V(CGgX.]AV[C
>p51l(0[]AHafBc\4X_-(_iHk[=&do\o:7#r&W,tIR3Wq[M<EQ%sPdP'R;Nl+\r+qLjPlT<K#
gn#7ln@k/iAGYu4\?q>)=Xk;rf1&nmhh&i@J17K6s2Fq>5GJm+k_Id9%n"Ha?b<lY*toT;Z3
jo_QD`Fr=1O\kaknY#tdT(c9Q(cNN>caE6FUTI<n+L_,!'730Z`-e>t1-)]Af+#8io"<SHe#J
fakXCUQJ_]AjoYHk%k7sAXq-&/b$d!6!^@8<!hXs-XasTnN27E/ghJ-2;5g:nLld8U0i4nOLg
F@-QrOhKC'Bk',3Z@R!)-t*1cTg"$g$"u!M<`]A#<1_s7,gcgqauBa:p_"Qs*6rSIrU2k`;9g
:3$Tp`?3SKXepL/[GX!,<=@Yr3"GQjh&6dU%WYMMtWq^;TXE:<u:U\,Jb`bE3j*R"e(_`L<o
OF6#PZ3f9.5Bn*bZ5Rgh7srKAAX_tXYA!NRqm83K&\Jm+JLl!mf^Bt#@9h&qt"m[54L0$f0A
Ab^oK6L%dc9dcV(khQ:cup0[2sPa&Zfih4iu))!ZCg+"H$9rX"W5SQK%7"Dd/U&jm7KqOHO7
7pTb/3aNPHRq!h>@@!Y07Aqjh&'1W[ON)/L\Qb*g/j/&1f]A"Co5P717X;u:WW\najE]A*`De\
Z-V=a><V>9.is9\P;qWtU,]A5]A)FdqpX]Ar:,7G2'11g#aH.+4nD.;>c[aqTHe8eFOpAp9daP<
Sm"RF4B"M9tdm0\[.AYJ$9?U1MdT9d$[>6O1ef3A%O1+=:Paeou4Hp2SHYZ?Ep^VY62nm<?E
&oAUidf1^:rst\TFd7F)mdIHh+sF9ro$^0BFAXZoA!/5d=dE<$)j6;P5_B+Np%\UUa+%.OMa
7p$'#))h&so'0W*f#?&)&J0KY)e".RS8=9(ANdsX>MSjHW%r0L^;8@rfLpUSh!RmTGdXMVGu
ON>&1#OmXf63C[u\ZZ*6=u9Z))5.f0aQ/.!@L7YcYkedY3]A_aiC)k5--\fADqcpG`Q$:qY2X
f`h'$(@pO48(,XQu7OP(#qS+AL&o:&M*[<imZB`"@)j@@DBAYQL2E-hYm!f)H/m0L%eHWo&)
rrSU8INrsIrSd1olQH8k7-<KoP<a"RoIk\Prl!bHjF_b*ao_YJd&4rrt>"pu#iFDb,Gf_,;E
%er_d+!qN^\fo1`jD4--8dFI7p0%rc3huUK!CDk$"ZUB=C*_1G"d)a]Afd)9V?4Fk8*kWg#cH
:el=Sh6[)D3J<XNQ5(.jl#A0VN@o5>I`NNW%HIApT)r6&,#HKk^&&\GEU#QkXt!47gAK9)ll
R?YbpOIM/]A#9@>=AnuF^<76odgu+JX!($6L6I"E?*Gi5k-&IH\*0+V5faZSW05Ss6IFYrd4Z
U)hX.<>"m\JW:=D.c+_5OGZ#D?Cc?mJNhl#%u![4(\_J50Y;Sp+S&Ood/gTc=IMaR08<7ukn
/f@r?\.B9oMZdme>9CS@,57]AFb7aj:6N#b[L69Wrrf",O(o7>%5ks/Mr,F,W3Tm?`7T)/UMV
U>EY(1!2&>+HB]A.>\[X3/jK&*A@K6Y5iGHWT<V@'7iT'hBPqDo7!MVUT+5oMuF1F==7=<B@\
.G,#Z^/h>l\lGHk@o@D2Ub051AciM,?K2f2J]Apil5-L=m&jjaS[.5rKKk&BXqY\?bL7O(&!l
*aL`;6+%Nu<1'q_'eWN=G#b>hkPb-J",n4-9T]Af)]Aa@8RQbt;X+n&7io'p0Yj%&u"??KKrc0
eD-riHPEa$*2]Ae7"PsaLKDuH>djsG'nbc_iCB#4QqlI/%k<"3rif@A$JpN=t>cAT`bWb%g^/
Cn%&O1(PjZ.YsE+R%"/'Q'aZ9qJ*]<EMAIL>%.T\,AuJOQ8@W4U1\7[a<,2eS-KS]AfF!7
assEARE5.0YVhT)!0t]AE.]AkFCsK<OnM%KLW]AjKqdHd]A6G/B=MW0)0*lT?kMG_\<BiIC!!p@W
<")P`60naF\)_tP_A!N.._]A10uJBZ1n/"Nnb_b!*YLkg2'_Nl-,.Ob=&>-L2FPc+a=X$pN"H
WU[N3K@ZJWTu(m*!YiK</4o,9LA_P[*d&9VcA4["N9GL*ZGBVlD5'W_^A^#aGJrJ@W2\Pqi3
44T6c]A!"bBTCf5So$]AM]A/cY&4oqUBL`$J<CRkcQ]A0Ti<>"/557N3_Lh&(E'H:_(*5B\421JL
8*.QR#P26B"aE5S<2>e*[]A00=>orI,BFotshl3DkT?PIqgV'C'J!L7JB0P7jH@5B\u_lY+@U
+$D,p(i&tm!DQ$9KfKA7\kF-%$/+TID+)BX#7:6G,W%rhCN8ak)+1d[-=$]A7&f8"<gH=G1jT
BFds=E<9Kut:\>%)^1U97'l9";i8A@!o-AaI,YBV[ap0h9?[ajp`HtKc@B51b]Ab&t-A`(>$q
4p\J%GkZ\pi8sAgq8)lV;Y;K)<EMAIL>$jB8>A>,umr:#)+p9U8@pCsf1%4
`r-#=,1[ns@ClXXg[XIDt+[V[AG,L-"5Pgt:FJ6VmEsnGM`61erMMA1p4@#ik'ejjoXGfGp:
h13W:J%fH=fY23`E:JJp'NhD:qXf5S62TR/JKPRrk&Xh$pbPck<RRW.+[lXm3I!Gh#!tD`+L
2lO"U:#*;aQPkmE`a/+,dKJG@G"68`40rX=L^<je+&j(2bOWuW,EQE'/5M3VV8n`qCq@(,%e
aX$H3p9CpDdA/%eluZfpM>a:$TaGdtjYHRN1&2T#8X"IWiE9=%_V"YC5Z)f`);+k$8<V)G'Q
0`g*O@WI;_ITWS3]A,oY@ppj-MY.H-c.$?*%"n@)o9]A]Ao*TM;$4R.aP-KP0SqK2Q6d`9S[^-Z
@W5m]AbDBD&q*3T5XO<JAr0m7bD:Xa8$ij@N62j,cAeO\@==&CQg@3gt6;P78ZaRm)Js.D_+q
P79jtl=GP_hf=O]A1U*Ro_ZY`,r>^u#f(AIg@iO;jD(1b.kd3RJfPUQ-r(##Gbj86.p@?cYUK
euEbgC$RWMm3Q$>?J2.DJpB\/1*Lgg^a`uOmn4VF*U-fMtqQCMm(EjHMZ:$Ter"1_S(Bm20W
>$9uSg^1jdtW:0lA#(C\UN*1o*aQ&5Kf]AP/eUiRc.!kEZ8roJ3Yr2"YM>MA-Xt>A.sKVtV2d
oJ^$9bCm#MLN?<$Lt)U5\J]AMWX5!$u%o*Rg]A"Al.2A[Icn7'\\DUIH%`LrC&#G^+]A3^`%=h4
hM`f',J(=L1ga$WOI+jn5HI"BX=7BUTt24'C>8ji(guZXfb*&/@*l0664GVKV\*Po&3)fHqe
$Ob.84`e9K#GH>WP>s@SLjh<HK8:K(c37*RSDJkD"`OLc2!BIe$)c;'*U]A=,c70U8D]AW5L/A
lFWrQ_CC+o7OdMbIa5gPMk[Sb%uFk-XI!KZc$dD]Ak%moPi/gg]A_oHG++u^?rtY"EKc7bkX]A(
eLfX^Yt`tt?dE%bA.#nS3-V3sa1iCj#2XG:N+X1\Nl;pU?PY(CS'$p?r8Mt_Ial9s/96T=*<
;-fi;8j0<MI))Sf`cLts#M8_GXZ*'^:H6YC$tF*hC5;7N>;HDhkJL6HjIgti7"\X>r.@h>-'
YPaGDHJY5T!,kZA;#"7!2]AS?eX!C5`"^G"l[Jg\8s$6DD#5)]AZXrY\><?FXZi1%5l(b^e!5p
SB@fI&'4@p5W(s*DKiqMpllVkcHJP7b"+3e&cH_BLM5k$,YHAuo>74"$HF;Fd0do]AOQa+cBi
h1Wk;Q$McEP34W*33MQ)@*sp&`=_^j65mmNN8e3/#_2jTl"clV0ZY_*NS(tf_Sk\XbMA_bNT
#F&E:_/,lPg8Ql`SfEn4Q7;@ckLc_<=BSgP^*G6/R%()MNP-?bCG2*r&`GVTqH%^fD#IWa:X
6t&*M$f-kr=lKu?T>ln]A0sCW4e2Oo>)>BmifnU+0o#AZ*<lS#GK(#,mjV-2-djuApjA`s]A1t
u*a$30Y,X8mibk/(d>rES.@\!l832-Bi%?4t`eLrsCYr3m.:)-QepOt:4Ii,HY=IfCGFX;Q-
=K1FtCFDY%/+_@Hn"8l(hI&JmbT5KG>+etsj2\mt[Is:@UK=AD\-Ge&s>/%qhN(lH;oC0_6=
nrU1PAOQMmd.`i@3JAEfP76mnncV4>,63uV?^UOC.Go0)5<I+n*Hg\QHKFrm_q"63[):9),=
_f+.CbSGC6`iGfV.-CUp,cIdEY9fWu-AQln\HiBhBbYa4,1W&Bt[Q^mD(CC5p\7'SF'B?bN_
0=\P^]AEX,tH"WiZ5En\@.d=d&ZZa_.d]AhV+Y<']A7BaP_K#W?L-\^%)af2P$N8Mch\_^^4_,h
u&IbM>Q.hs_m<;O!R$%4tK=(M.V+Z6f[r]AmgJ1X^T@0%:_WBV"-a<0t<t/I@7.b(ZYF6kh.3
.6_Whl,A"ro#<D5j;8\E;P']AQqI+QODJ[gOh7UdKEqP&GPp$bQ3Gu7Y<8"=C'IMfCN-kN<IT
r#T&/I&]AP?pYpW4?B!l\,Y:*VH."fj[o#'4uIV4o+8c$)po9J6"QP]A`$8XC?30O*(PA$'V$n
Z%5s6%XXb'0L+#b%eQKXN>f;8'jET=`L?*7;_&9eT[hPW)?pU^IL:CR+o);2Xqg8INJ_uN,m
J58M8Gip#L`7uYJ6K/K=K=F8eF%>2hUdsD(8]ALE)Lj5bZYU$(O$FpDITj3(69`5qs]AGb+Jc?
jc-9G<.m!npf-@cr9JiO#$UM6]A+cCatDQSNf[K$1E51\ih-$MHggc0$'Imn&q')rXf,hce^d
\QWbA@_@A1KVDc`5PXsC3Ckdf$hUh..W*h<081U9QiFH)+ROje=ACkL_C2a-Ic'(Pgf9^($=
-c'2@/Tj)2TK&unrVo8I^^N#qK`&P1q72r&Qn?eLe/Tqs"<NNE#<tO,XEuZTkcHqA-iGd<P0
R:@8pZ00UN"Uf?]A;l-NVB\;gKmH`35OCMsG@S#19\/X(U0,_*7A*jdurlT3ju-.(+r<%>\56
qgpdj]AI6rAa:uWdg5OXM4=R7d;$;C_45dLjYOd]AXNSU/hH3_"[TuPtpD;#kQX.i[CQ:U<P'"
;hO9+$uZikbd=d&Y>Z)ZA5f=%X(:1Wn$=f3V(XpE;JKi;,+9B4G8>!L9g^kPEH,HSQ'UImE\
KHAUmu_s8O"cOL@hOhBP3f")M,H[53NH4.jggqZt-dZPY310;ZYV:VYIqtcqrJVgC5i0ddR0
]A"*nUX[(K`,/%%LN3GU#C@t5:kC;AKOJJ;M4N$f+61S9<2<&?_"_2_3@cS.i.\:HZ9KhpdA$
mgHrV0mG'FZWg$kG=-Yo?]A/uQ+=\HCQ.:P8G#Du$,.`u$mZK;F9gF@-q>NdfXbFk>/lIbLTl
,V*F297WN&+A-Pu)9,Gdq6YP1H=fLZq*;Bm@IQ4]A;^R?%Z]A,M3bR>_**gT#@od=>o$8MhGB3
s$RfRMLjLLUZ%Wk,,(('>DkqW%5XCGf`DWp9u=$#V[GHq3^-%C>:qdqhEO=]AUa%VPc#DVh2q
7bqVHUfRhIY9R'H54aP4jG::IHja:h?USXsSYh,F9TdJG!40alc)pP8-s/:T-??;0#I*6DQi
e5*o)BnEVB<?K%l!EK.;5"T-W[?,JH;F7ZkR71b5/Crt$T$VJde6piUR;tq9^7$FVW/UW9'h
>#nK-Ng$l?p48t![p-^>+.,\N14i5+m-IFN^Cn@+ga*1uF\6)f@;G\566r1Z?fNIFif2q`kC
gAK>Ub7I_6i<;28hcHNG3OYYOjbU\8+onMC,2EgY*NZ)&/h=iH:#W75\5_/&)';.aI[ChoId
t9uo-tK;+/YOZ1u;0=&@e^l`:AlCJ<Y%CFGIjm>FGE<-&=[0\B/RXHqCM[JoC`BX3G;nDP#;
&M/[""Me@F6(`hl.=#K4#I`mp%IVr3*^#'T0NXpi,3LO<8o#5uHL`+;6=7F3pnFpQS"U).]Aj
IPur0=\Yd"rtCts1Y>Tc<QN,Eb[]AB.qL[b:g-<C>sE.\&MSqHa#b1,3fa*?UthtokJM%e,#l
m4>?=.pTFOtN-2pcAn+)YtXW74p;A9u&!`bQSb-G/;<QsXfrqFQ]A0E0+ZeN""3j[j3B_B1W&
V9*?3cs-39rd?>cPl(D[eY>9N>XR29jXE7K@:^uYLk[@pWRHk*ZNG1e&N<!L>Q<+8*]A[R+nW
l(VmpuC<F[iM%#s&cN\r.8R:,Qor*"$U@:7,94g^>o&pM,YL[HPs,/ZSEE_m\b5XIe`\M:_8
`1Q?M1aUX"DPRTq#acjr`WaFStP>@@omOeU!D0e?]A?+ttS6X0$.ril(9@VR&q3ut?!HK@Lkb
]A,D"*Ch%UrnFO!`2@l*qg/BUg22+a%pjBuKVp47LR*ZlN73/_CAYnaa$sDnH0-J<b4p@$i'J
'P&FCG9-3-n'atpU8C,KAHs0)S`KID]AqqE'Z?SqQ+=r+1Kc$-J:HY<["bbQD8OYHmIi4I1[.
Sm$<MW$Q/5BPnC721AMjqXLj+ToYl@'a@6AQ2?;o_8L.ns('@M\8(IojI-Qep,isL".Jmg)M
V+EE,2M&c&r^-D*WR!cd"O_1in[jkAa3+VI;*;;ZfS"rYI&T`6V4]A\?qk.k/JfA./("WF5-J
b'tlu'7^cfN:Gh^H6$e,PRr5idWANcnSlN":+\Mq:R:8Y"Q'b<fmJ]A@jc*Ht?/L<g2#aU,mF
.sFa*EP+$X>/(TcI#ri/MBJ7L#5-t<%(GQUHOVk*F7&CZmL@F4$t;d+$7J>5W(Ri<HVT&Q3@
(*PciRId\Ma>A/=_`>_Bba+@6pQM-PnUaVW#[g3Yb+fZn]A50N*!t$+NSCp^@>h\FRUgVi(VQ
oP<2Ca(T*fK5gqonq!`[Ue<mrr<c(lJFZ/^2sD<Y1_,?a04OeAC_fu.aJdd+HF=coaCa:885
'^B.ZD6jmO,hKO2eC&[bWeP1ZNE'IWQfGrOAT0]AcNjdmgNR<<Y:O?j`'2R)gtB)1VoPl(=Z*
%m;V?oXr[N$L*7(Ld=M#+]Als1O:7CA(r'.dqM'c:&YO>WnHcJk>.K`#tNOA%n=5t,oFVps["
g9u_dJ.R^nt\Z@j)MAnX<t!iC9j;0-EG7'ZC7jAX3#+^R]AIj:a_LDH"o7cdcqAlMZ/^!([Jg
Wu*4L?m;0GQWgofT@qndft?2<(cNO/@YW&Jt_Z*&h9k7cD$J*_lg^cP4hY>@U::c22]A<C>;5
2,k#b6e,!j<"2hpePJ,&%95dG:c7nFDs4W1o\BlqYNCti-RY:^6cF&[S,WK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="ABS01"/>
<Widget widgetName="report0"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="663"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="699"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[考核督导]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?mcP;b%nEir`3p@<K1^#YB,d^o9r%X;`8n<,A?'/BYPm7X::(C+4fI_[Qg+LaH+VTsY&h=4
O^%[ho/R8K^,@;Uu&oXtBafGr1%C1$!nb\O^&M6e_MAqqM).So\O.o=8pT^@RmUB?hTV-`pP
a\,q4HXP1F-mp?Y4+(4efA(aOMi6aq2!YLa-J+)g?Z)T0.=nUN*PIgCA5>Kk>SrC8-N=>FS6
e8!&lOqJjN%I&UYQe,#oqDt6HhP1#kEsS5hrWbaOf]A-un_VNDN?Jh`#9LVYdW>B,+s5V))^1
i%[XPo)ZD_A[4QA'=3-<TJ345S*cZ*f7Ga9T-p]AO+Y0J$rf6ZEF9h)@/)\p3@E&GZZI!2,k=
,0:i3SLmWRU#Y1`4u7,T24H/ST5U4$'NI<C7*sQ2[D+7Y"^>ThY+-o2K`0+Q[F-?Fpd8-k]Ae
9d"'LiA6P2D,A@/Z#Ch4VD'=ZJ"?N>IUY(u3EhO`j$d?L8.Y3NCR("[+c)^>u`(/tgG3R(C&
NRJCp9dpYDU&_E_`%#gLO9$qN[kR,rU<5%rlCfK@(#-ND:W+;DSBDU1Nd"Kf=)Z68lO^l1HQ
'omH-,U2`$a^)?2-,Yoda_hPbHlGhmCb>33j5FKWRc@Rn<MPQ3gp[?Z=dT^nJBn=U2!g>^f!
2ZmoS9igQJ&Q$r'7>6*T7KdK`aJJ<2q,P'P6n,UG4KD>fic`YusIA36NARb"t<]AR`H\CWHKp
nJHQ]AWl7PSKXLS'>#.G4S\&4SfouAVLo:SA?u<Tg_1<9OiK@.H/&NsmW']A\45mb^@V5e=$`W
J7sU+4!OpPMI8]A':PlO[+F.K-:q()KacG@;,Lc0k>C<9f=gVXi-Xai8)E$le$<XlC_J-K5+^
nidWa,A5[(=W4sS1gXW[t8JXP=fH]ASS@m<e$@q*R[oo,,o)P1`KaaB$rpHK.)og;(7%U$CA^
-3$+HN3&(fSiQ59fUQ".A\uQV.k6O-Ps4p"a'A=Zre!%[K]AhjdnM;5mF]Ar/m&S'H!#5"."fd
a+dUKnNRrTF%+]A$HF&O%GaHAYC;-;2]AlPT3kldVcaMoX-$F)OEfe*ALNNE>k$*b4)Y?J[S$:
Ph.X_BRL+6p=)?@4F(4FJV<<Y#S+077as<lZ8(A8XVFn5d?LgN<g6/7SC#$8@r\A09rTLm+p
QCIZrfOfPjCiL5C5FuHiK?FMg'G__/bOFaM'`+S23_2P\WFlc1G6WGpWu5l2SPqf2EF;,lk]A
I2=?R[e$:*J5qmT(/E,-m&E%%g2hR7R\q.74^T@0!hn`-QY<%j*n/,N/iO><U9V*N$!hjU&+
OfP#g48J0UQD3Ug*YdGHRqp*NrtfXXmO(0[c9o:o`tFf?<&<#pON<WP&OcB$ho3O?6XL9g`$
1#Eo:Bq-0c$kHnnVd'#)m.pId&7J<=tt-Jjcbnr;Gnr2/cPpIWolT:WH$'+ro&&@kCE.H1mP
c#V1\&7B2ok1l(?E!/.aPmMna.m!sI(`HFC>m,i!iis9fMkWslZuP0[9e1iuDQ]AuV`I?jWXG
H.:_a&^$Aiiu5>Q$Uk2/`Q$bc,S3\g7$=R2[L^,<'CL=$_D@Q(glQ;7S:E9l1WafrURL2&g6
=`c'0qT/p$L8M+Mu#=0N_D)Ss7*OKLB2&S6F>Q-U)cgX`tTXm%XgO&A?p^rIGM$5G);\&2^l
LX,rAc3;tK5%37-F]AZK8KNmnWeL-iZd.&u`:1%fEq69ZhoaSU4IgbfMS?ELoQFU;WM-"`;YF
L`]An@/D+4ZPBJ$CjN_,8icJMG[snGrqrB.D8c\!uFsM[amooqlo\<q;)QT0Z+LR@=pg9)r4F
5P!6t"QU[8Mp@*0b!_2,/A<]ApT^L&I1K3-/Ua&%_a.etI;21MbB*C=h]A'tWF:Kji?0`qOM\L
43K"NP(<Btp]A01(MH-9`Ma#EaRjA.p2-ml0l\"4EbSfYNogAE*Ja/$>JPaOt(XPf&"jm]A*cD
FqF(NbaFp4'D0A;/adUOO0)I@ZgR4I5)br>t\@lR1SQn6Sm$CY-A[IM*#H\tg(bHkuhAlg%>
M*91kYOeW0tNb#]A-4-Bb1!Xc)IZ%b4s34Q>,mPbEaQkD2M2pp4_kq$1"b26a=]A5T$/T:*TH9
)i&ffdMl!d#`%!nAp)=dXt2BTahrZ2W(P>F$dflY,QSMWo3e!3sa##"tr)%B6O=7:Xgq9hBZ
4\j3OPLm6me`L_M:LD#KQ^EOt7k?l'6$d0P?eG:EP&&,N8D$("3BCb2*<>n1cOqT`9U<_rEM
"X:%OL[=\#%&:ns=*PJF&NGqQ1`g]A`r<FO6Ek&+6uqhBr%nh_:0'%/kYX?k<`d6DR'gL:rk6
e?aVeb[%6mK$uB6o!L0Z@90q#m<6h`.d6K3lWVdO;?`Io$ZWRALX>+u'kb\21_(9m26tuRpZ
`#tSGcK=Y7R[iF`/CPmOm29g0;Lk]A5.19B^/TQoP"@N<j=2C$$5`lBmj`O1BecqAF6--p4Yb
8o8H6*M-Ek+7fiH<8=^Kj>B(u(FY%&YdCs#4oF/Ji#H/Z,H/XN\s>i=EY^!V\]ADYE*[HeJeA
brKJQUDDi+8JaOAOE:6&?[^&a?8O%4!CdGWo4.LP/W]AL'hXe(MdfcfFDT?Q,"JWYK5-98mIp
O=bc;,'^pNusaQ,=HY0SV<@@16W__\gBIL,"Wq$tC-l(re7anLX,!hFm=n1g;H#(+8]AHGak$
D&?5Qh)u)TjR_*t&+J&+ICmqsf7<$<051`duGKZ"KpVhM5EHuUaS$3=EYRJ^Z%*Pj`bHMDuY
RJ^Z%*Pj`bHMDuYRJ_TBA!MmSJdEUqImocC&bg8H\HfEchn:~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="15" y="15" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="4"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs/>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="35ca10e1-32d9-4d10-978e-91052c89e4b3"/>
</TemplateIdAttMark>
</Form>
