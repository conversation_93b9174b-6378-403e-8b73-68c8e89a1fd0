<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_sel_sc" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="serch03"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[8001]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[ with RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**  
总部指标维度数据：
分公司指标数据维度：公司得分(SCORE),实际值(GOAL),目标值(WCZ)
营业部指标数据维度：
		公司得分(SCORE),
		实际值(GOAL),
		中位数得分(MEDIAN)
		未明确：所属分公司营业部得分  select count(1) a from ADS_HFBI_ZQFXS_JGZBMX WHERE ZBID NOT LIKE '%_cw%'
**/
, fgs as (SELECT  
		     zbid,
			zbmc 指标名称,  
			rank 排名,
			'' 类别,
			branch_name 机构名称,
			NVL(DNZ,0) 当年值,
			NVL(DYZ-qYZ,0) 较上月
			
		FROM 
		 ADS_HFBI_ZQFXS_JGZBMX 
		WHERE TO_CHAR(TO_DATE(DS,'yyyy-MM-dd'),'yyyyMMdd')=(SELECT JYR FROM RQ) and zbid='${serch03}'  ${if(level='1',"and tree_level='2'","and branch_no in (select branch_no from ggzb.branch_simple where up_branch_no='"+pany+"')")})
, yyb as ( 
select 
      t1.zbid,
			t1.zbmc 指标名称,  
			t1.rank 排名,
			ddfl 类别,
			t1.branch_name 机构名称,
			NVL(t1.DNZ,0) 当年值,
			NVL(t1.DYZ-t1.qYZ,0) 较上月
     
from ads_hfbi_zqfxs_jgzbmx t1 left join ggzb.ads_hfbi_zqfxs_ddfx_yybkhmx t2 on t1.branch_no=t2.branch_no where t1.oc_Date=replace((SELECT JYR FROM RQ),'-','')  
and t1.zbid='${serch03}' and tree_level='3'  and t1.branch_no in (select branch_no from ggzb.branch_simple where up_branch_no=(select up_branch_no from ggzb.branch_simple where branch_no='"${pany}')
)
)
select 排名,类别,机构名称,指标名称,当年值,较上月 from ${if(level='3',"yyb","fgs")}
order by 排名 ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[1001]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[3]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with ab as (
select branch_name||'营业部排名明细' a,'2' as cj from ggzb.branch_simple where branch_no='${pany}'
union 
select distinct '分公司排名明细' a,'1' as cj FROM ggzb.branch_simple 
union 
select up_branch_name||'营业部排名明细' a,'3' as cj from GGZB.BRANCH_SIMPLE where branch_no='${pany}')
select a,cj from ab where cj='${level}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[//setTimeout(function() {
//	bakdis();
//}, 10)]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[144725,419100,228600,566057,365760,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="10" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbmc+'排名']]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?_Nd;d$PZe"o$s'YL_MJ4tttPop*;#Y53-*7-'"fcgt=&k/H)6DT,cKHq>!'+.;+A0.>E68
=1^0I7;GUI%M&O?`l(1.BDd&<`-.,TWPXHhDaaUYi9L-[2]A)f"L7<Dsr,JhIl:umsOHeD.:0
6(]AXQ'S2tr^0*$Nbj3#&LK73qM(n1^0^db5^*UuoS&Pg,[:F&`_k3lm<c?'V1meW^+faCR*P
6'n8`Gb*nJ*]A(G*_Y7UZ(jJKp/e7g);']A^9IISl+e6s$_C$KYG?WrhFjWER7%YoO<HB1'p,j
hk!VjL`:[Sahme6=HmK.?l)H=:-8XBtm15rYhH-d_-(?"gfA^l%>,s0+%N:U?jI'Dr46N:Z&
53&W#q3t*(qWDKQbT*rM:7c-$RC;(1I?:Z'a22-=MCG-S8ZKQd3>2U/Gl.-j%Tcj="q;QnBT
`-M*.5/=YuA-h=.7"T<Naf#F=o.LhrjlXa\m^.J$^r#hT?2c`:><e%8NhOd'KD#d9'.JSY_;
T.bi\UHY/4TM;-sBpg,*AdEu/nMrO,3f8RBn@^`/A4`B$h61^H:K(+al3Z8MEadn1b098&HH
RCBCqB90RpZIcqWe@76q+8,+In3cO<Ms;'is;#j+r/:bk1&kB[#:2G(4q(#/ktFm4d*dh[TH
sJ,`<KHNaicVAKa"u'lCX%P`TJ4Cm%l&&OmMuk2srYqdG,Rc/kBr2@?YOih<mRfuZGCOq-C+
QcAc(e!e)K42@Jf3M#=M9r+^Oc18PiWG68<CR)19Tl!\&8FTH$;-aIL'pR.QKh_H.C8;m]A&]A
sLdc)g`oq'3PG<r"2+asJ/F[O"SVOV=9WW7nE=T2$1+;H4r`]ApOLe'"hjC01sX/E6hUGo0$7
"e+R+6ah4WpQ#%GPiSobm*72S%":[tkcOqsMCb]Al"i[n1r+d-r]A#D\?]ADf8=?&;7="Q[1r;f
40.4?s*4j1enT[S:_K0Y<!g'2[:UqjQ2`LgYsRFWJa-A)9-oSnnhcs9Y_j7&@1T:f!EU%?Ni
R0:?Qp>8T5=/e?FEi\sgOsC:,6BTo7^`=lIk#4T1SGdn$.0o:8^$28F#6cs*0Ne,FYd1c%09
c-8h0Jn*Suc@?0@-ML-535n^9S3g6j6]A;M9+et5?KYkJ!.9`ujqYPF-M#:*3Z;s7[QRTNA&.
+Z#/lGL[A9otKGrCgU_>Q%AH)^^>*N)XaPIY<,(HP4hSD&W00(a_Af+CJ_Pp<D%^JQ+s\g:M
c,BY-&.'S%2.+/+?"W`b*#*s\OYdM')>D)\".2ceq&"_HZ2UX19!6'3*Wr-$4\#%Eq.1P?bb
9lG;UJcMl;)AY,N:^7,l54F.DV-X?=N^_3.Z;uo..t2mD5+VC[8`)ap.'Bd.mJ?,YrtZqnV\
IojmjDhia_e30Q,+j1Yc^@pIlFI(Cr1B8Q8Z63%9).]A]AO!b87of%bVB^"g)';e2+@,b`_2"_
?=!'_a>7Q^=hGQ8HHOB05[i,@q?<EMAIL>-9Q<:d49Y*B>5hn:eN4(@bj(dRLg9\."^'B--r
l3rh4L]A_f\aP+6r$HaIG$q`IJ:T_f/0B&dnbn%"/RL54u![VI\[&j@`t%=h^hfSBqhl%;kf;
u3TsCM[^@03&qim*==np_5C:+DmPWD&qp^TAIe\U%;gS+imtg@pRBEm+a_g.sEHPFgM<+.@d
iX\N$=#KFY<!?)08R1-:!lGbPlJa;h`c/?+Q<-?!ZRG\ngOgYF;-Q'IFlB>^-=/K1'eTb<\j
GTkSKi\Gp!5K>pDV-8]AgGH5Tt?dgj`mZA9(')</7t^n4Hs3R&WSnW`Rj+f[r`5JC5Nng0EmW
Z$iagD.r@mdllg/;u1[620+*0lfZ^ZI-Wn1)3)$K%pD1T;6J/e0Q;$[*`IGRW$VcpiPI:HQ<
*\n-K9(0L9P]AXg\HR3D6\&faY_Ab+b1HHBcUpi6cZAOD8%c'D5h3O82uldZV'J1c0"?*W7L4
"dLo0-f6@[f8a]A[t(Zr-TA'44dQdtWnm\%nu2SDBL3.dBd&fQDb0,]Atd5HpCt;7J`N0Qr]AEU
`+s18cEo3XS.n'H*G]Am,A]AJ"B.&_DiPY5P[?TA,Li#C^DpdM1NiKHjT.A/,rr<%(]A?!q]AQ%!
#Q,Ns37>`IK!VZq)!,??>jbsLLDFj9V-,)+`[8.fDk5[=b*ZYL,,Yg(G'>G^kqedl"9lqb="
\,QZ?IS6tFBoIKDBpP,@o3YDqC$/%5W$h)8dGV4EK#0NSopUEWHA;.c:\P9_]A_a:K$dLVg2i
532`!LPu:2O/]A)\p9?`=M>6ZF\Bq$CKca^Mm=_\Hem$R[8)QZFI9]A/7eidr5823$pgrJeG@>
N[YLe49SM3S#D8ZZoGpW@1mCsCc;NW9`A57h%gmoLe8j8)`jDoSiY="9%5ZR0T\P.e6jYTgM
d!6!$S\(jnbC28QISUZQcaldg-C[&Id0pUp?_+]AE3L^Y_-KS2BVQta\[mfbSrTe!ad/ftU,j
/TQPD8A[h!_mq3?aRB-#fQD,EUE`NS;:0ImW`2$euFM`uA%dum.ucYYcTaHu-7G1##5/T6OD
\;]AqC0=gTh.fp&p4QOtd&n(I->lO%on9g1HX.a0PkQX]A8ikOZb\Vs\/<0s_'4jMSsj"/;XWC
pPXUmr]AJGa"lS1!!9R4WgZtPls/-Nf3"W0b5KCpu*':ihhl^_R\+\@jUrA\:s0+QnFh7M+'2
]A;ld!&XVDWZ]A+_MBf^d0Pfp.[q=H*]ATQA^<0H/W"\FQY&T'e$Y[KQfs\l!0iC810Q!^jfR`4
+JIoJ'84tZVbMV=E0::qKLds;-F:DJO]A>s?M`0&eT)EoBcDa$Q/F$#Q$.u7[%kkR6nBooPd;
YX67U#S;&hZ^Uh8]Ab&3k7Q=2O[RasQ.%25DVj`NqM&j+G9f&(\Yr^,RuNc$i9bMaei7SX$L_
:AEL^aPgmtR!XB4[6l(&dBcC(A$m<aKLj?-r_MgQNCg]AB#BAN[">!5%Ejc5',]A/ub$l(3[0:
u<W5E7atOqg0.YlG7/I7"21/9#1>m%6en\jSGVN?eLdflg]AHgEj1%CY6EN#iS*sU/64"@`9T
#Of,8'$3S$-jR\7#:]AWDUN.jmr_[R`,F9V[L$Wd_sFTt1"R4m6\3d(rD='B:rkeN^%\U5IAK
A=]Ar.Hp=8Ak]ALC@jN!O,1FB^JuaLo+X!dS6AH,(4_Ie2<$al;(Tq5Z[e5)QR)s^iUJi5:WVA
!V%Nm=te330DPmp6oLF#`-@L2ZKfgO$#`V4amEt0ksp[\[GW%ZrP((H]AG'aUYY6O?Dh#@2bA
-uMf^$8q`IHZi6&%-1[lSsl[AFQi^aR.6S>7GR*G/@u3)7WO@Cf67-sc,5K*:p;I)@+9T0;>
liMl$Xp5KJrudOf:/?$XsHI%`56hP&t6TPW!t`G4VR%0^`OXnhM]A%H!\+FK>Ia2o>Q(uM%/5
6E?kn09Q]AhMT#[B3W/V239i9tqOVPiZpeN7jnA2^V?B+;unRa'k'benkqg7H)#`14/'0J%?f
kHBGG05L[^FVn$MaVsYJ5LmDEAFqn9GeS%$1AtT1R*9%>FB<i.l-kLj`:q*!r#`*U0<isS_X
*%G\j``XjnQ!F,M\San>&/6*2SG#S)cK]AeV>T>If["^JBV^/Vu(I,*UgBDtNI?#_Wg=18<&p
Y_S9Uj$+"*c&DE4P4Ss""ueX>]AGMZakSe]A.#bGDV/b6Jb`WI/uf"I5jV[@?F/lK`=D]A-tV*'
njMVb>8.;og;pV%B<"@d1'bWMM*'b=l@X>Y8qK*<E$UV7@T(RolaH`\>Y6l!;f:"R@\?-.]AD
SZF<aah$'7UL<6@^jmQ_f,2KPGb@7AnrH@PaOR_sB5qZL7bk^+!eCUf:e!&9lUrD'`qrSJ@8
,agr_VctdDr9oUm6k2L&j/H@\MoC=88Ni9-)NH-KV<@o>:?+`l<&0qbN69$C!0E2;fGu@iaT
ZGIjmUufmI!6b+aAT'%\CO),n_UW%h9-Xjof8K7SETccYdLQ@A1L't9U_'4K;.PD#RhPok"p
n^WflqZ@E.`2iK:r9JOtV]A\!_\fk7Zn[uE2Z2@H+=pqr<K<PT!1A#Ln+E1-*7Q'8fhqK5]Abr
Ln(&Z5,dqu<m5m>moiI@mX'[0&kZdI'rGY$]A]AtSR-#."oA[:VTg)>`r&V:C/+8jofhC"?K_h
&'YMQ'9)pKoiENA*!AD\u<W]AdCYppc%G')86^noRn"us8<eJ>sfSZBU-0'cT'!I!6T^]ArEk)
dibaJ.8^`2S]AOL!#o;JCk)u!!B.^tfDtpni+G)KpoW*")-X'<jq#cr"-?%)k#jiS`CA*QV*)
B#-Ih_/<?2N1f2S2D>dY\te#H(DC-W6,!B$MSe:8H6"):(1Vu\cK#1S/A:]ApRoNdg]A<ASEdK
`n4nbmF6.5A&N;I)1M_~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('CL_R2');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="CL_R2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="CL_R2"/>
<WidgetID widgetID="47eb05bd-2d0e-4647-a687-bec0a1f403d9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="CL_R2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[0,1333500,1333500,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,1984188,2819400,3677696,2819400,2438400,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[类别]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[level!='3']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[营业部]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[当年累计]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_sel_sc" columnName="排名"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_sel_sc" columnName="类别"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(LEN($$$)=0,'--',FORMAT($$$,"#,##0.00"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="data_sel_sc" columnName="机构名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_sel_sc" columnName="当年值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(LEN($$$)=0,'--',FORMAT($$$,"#,##0.00"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="data_sel_sc" columnName="较上月"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#,##0.00")+"</font>","<font style='color:#DE554F;'>+"+FORMAT($$$,"#,##0.00")+"</font>"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand upParentDefault="false" up="B3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="6">
<FRFont name="simhei" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WDE;d6U]A.Omqc5cN0G<!92KV*mU;;(9p[%7R(:B`SiDUdI6[5[":Y+@q]A&K-29o5_)*$6p
QQm&?156,VP\Zm^W"NqgQ1:hYTmaaI$K+jBHEe\^p]A@2qMC+`eO>&:0fiu_SU?Urk1CS=]Ag\
W++2a:0^i\W9`AL01"*abkI+N,f';nlAs^;IJ03Hc%Hbd9bJk\Ld[AE%hgP=CdYhHM='tcMg
0V;emb-K;pC&YnjPDDQ9R+UT(\#L5l;_)LAf?;"::c9]A*hTN#9npPMW)H\&c%KpfEX*Oi?F+
.&UhNYp#@1PrGi.)c`Q[o/=UliaS!l&p;j9;*!#6&\lLZPjoX?^r`Xl!P?BJL2I4^-qOg[h=
W(?f5>)d5:##?M",,%ZRs5/u!qS:LtOr1lbRLJkSLq:I9k8`CQ(cZ64O\6']ARDI^'pksj&%4
G8nD%SmBkf1\9-3p.F*bAcB:M#`=p"lk64;Qj9hic6Qb(M*Iq;4$\;pQUMCoEAk@uG=WUn.!
e4RsB]Arl;Kpi'#mW4S7HD[;'gIs))AtVHq`ni8ktO'2=-JQ4mlerdDg15.EP"#@f`1&$<aqe
*$tUR('ETQsC!qpF]A-)cNrK[qO2^C*C&3#(rbZCDl!u+b]A0Z3!TE@L!.*!&B4'^HVA&+H&t!
`09n]Ad'J*I,I`7FHF,<%f2:baT]A9(:;!J5&if#Ta>%/]A$(Br>5hAbfb.GTGr^s,1/uu)E!YO
!.*;MM+ZJT;\Un!F]AMid(:I3Li8\2JhlWR]AF[3,nh-Z%`,Vp4F!R`0#>HgZZGf(dI^Gdsq#o
A/ASVO9sJpVb3)3ap%S#_)g9-UN/&Od3MnqYZj9WPI"Hr9e:-)b4DC:"DB`A<dD((,Rjk:d4
`lMZOm5_^mhE,/V\9DaAeX[<pM7p&lb)rB,Fd%!&V4+$7;:?^I<-O*_ldb@<&_6d.$'Nm`ok
6_uVXL`W,i*2(+kDGBA,8YeT>I4he!pG"$F'>hfj/K^U]AG[,)SapPK":8p/-,lRlr(F'7CCN
PpK(DeJF0('DF:RoK2kO6V(N7HR<25+6n<R3]AZUUn\Mk<Cu4Cl,-BVM>G&s;j0p%*eYbK+$i
<I-M\jn2A,8\5S(_Psg#a`gHtLl$qd`G/J#'pb*3kS<1\;%"b2!3S]AJC?5*Zrb(pt^.&ND1R
qr/(=6A<#MmDVc?6:]A5K9CnZ:R7jg@asU++k",X@f-(MU]A)*#sm<)f/N"L;>RqEjKB<;:0QW
\EkWN.(O__q^dX]A_Nm3mih(hU<P-mu_,=[4^p!#n^#g[MX!4naJ2[2n>(5=Qt$S9PZPO:t"?
^TEmHtRh8[W8*h@@#I6:@SP"&$hZsHZ5.Y^2=9o?^c\L;=ipJiFc7kO^UJ0&Q29Jahj]Ab(go
\C5N:!?*qf;Qn@FOSjYsgMg\>O#&UeOn_a\,dX&'2#EuHKqU:DW-M20PbK4IY.L$e6'EJ'@2
0iiN]A$u2EBpH`]A$=Ysl5P0__5)nM)bbPBMOZH[S@fd@PF+NIECOfLt66UC%(AWj>.qjHS2Go
VgWoJq+n6A2JRA*"p.&0?f#^cOfqD`1quIKF#_M"W@HV?$4"nrL89T=`<M"s36nim`H`L\aI
%X./fAnmCCm97>+=XYfR-STJMKm28cM9Qj%>B%`R/mW/$1j%AbKY3q<R5hVXgWPB-"7_i[WE
[dH!nM/OWC=GiBB8c^VZ4YX3ru3&9D)#[a$KVR1I:hV+5>[-p-KfGXa;gm!s!fh2]AC;&YNB1
VFCfTJs:aB17&OGL'h5-uF*6&?K#TFMtL"r'[5P`J&T"Hj.oX:Ujq$-0^jUdk8DY">d8K5.G
4Vo_0LfnNGjnO@Xco8K$r[Sp5=Z_9Kj@JBg"L3mT#!GCa2n+u/&Q319*<a;s.8)(fqW<HD/+
'0t>+@s_eSO_u&A"[H!EmIGS;fV)?Tr[4Hp@&,;m/N]A7;fU58Ke`js/FW`l?]AC1\$dJ%PhEL
-Fh6,S)noX;1=%6IcV!N/qUDa+MLSDF(A0#\2!m_2)3#]A*=Ko/6XZBt1ppJ]Ak;M]AhhXhX73A
iefI]Apb4%Gt`DnYVhC!<o*d)@9s$KcERa_a!=o.L!Y_B^9,sQUSCC>[uMo'\"&`/s/OF0;=r
Lf:"5YRf"5ZbSq;sP#-UV9+12BLq,V3@q$9Yk-]ARN=$Ir[-dcVP$Cu?o\6`+l)n"pIJ5&`9;
N'p56PX,5,<gd;.]AX6mX=.VU<\h8<(j*9iCk&(09)?M-^e'9]Ahp[!W)P`(i\1]A(N5H.eR8mW
j:%]A/6L<1jci=YN"4<'jrrX/J'eA+9D:iGaK1ik6IVs.@#g6>HBskX:_5[9hkG/9dcRX5BD"
UT585&LR]A'd+nF_@CMu-'+UE@+]A"njXhi,hRC'7OU;XJ_u6m2BaYb0N^@o#[S^Y[jD.hgV<%
t?JdhCA^/XH#dfC$q+%cVD2q2O"mV5NsYtlc=*VK]A5XM;Z2a4Xth`8or6WQ42kUIODKVP_C[
e8QUNoGS[9Tl1/^%Y*KT4C5:7(G$X(kR[@"?Z$_1Z',U%K#6aK5="%9[`FTog.Mc)W%MdVOi
$<"Hi@Ol-F@p4!SGO:o+CrF@`;;002GL>b?p^,q#AE*NEiuaK"KEs0ZS<G=n+thA_j]A[S\fN
f3g5?Vm\%Nc@dJ+;=\*U.`(]A/29f/EDJpSY"_@(ApsH$.@sp>k4!3Ma#4=gF[C]AV.7;HITSK
5-T2M/cK*h;rYOM_0T_%oLGS8XiO]A3J?+LLI:*ST]ABnKsRiq@6t/o^"90Jm_D7W!TgSrPqPZ
Dm5G:.+(NNU@t_,#f5Li4RXdQJu-k3+\HjTOY\Y3#9H&Hp(\W*oE@3Hl",Lnl@'Da[h,e1`2
QUop+-.&T[^(EuF!3?j=AaEi-@QkNUc(9\3Hp$,?1:."&T*:ShSo@W745+QIB8T(aR]A$0i:=
DS"(\U0`!qJ/H-%PRi.RJFi)1SDW[9K+q>e0i,E6K,BH<lmokf1m&^W)jXdejS(iNHK;]A=#b
5L^Cd__YU^ZAdO7L4-K4Y=h=]A:)c>HK'Rk,nWFrf`Z0V+7eK&2#*"JsDb8Eg)j]A1q;PZ&)l0
Jk?o7nI.BIh;(epn-6KaCHqM#_?iMR@a>,asba#>A3OG[]AnA;t0=l.]A=#)ZM\Bo.eT@9]Agh5
l(TY1?J4'n.5P6oN!<W_VFr>bIK@n8n"WLQ$#i0(Ej2='4Q\8#!(Lt0ZtE-K*YEDr%s&SLJn
q\:1e9!VjRojj0JfZ$Tg6-IC=Bi=2L&`e;@@N6=>!@?-YWYS%0B6Hl,WqggqTgH0;22n\slS
^)tm,5e0=u*HlT!R:No[?L90]AD"#J)\F"Z^f=R&oTQ%R#V8pbp:i*RD_P*In[R6hI!ucu,;I
>RqeoM)D`7KLkjMb8M+ODj@CTVI:R(c\s)*Q\TPaB12q*ibMI`0^D;9+$W?Tpo=eY#QIdnTP
UII?p!4W0^makepFUO3p@l^Jf.`FYmYZhj#3jeQc7&[#>^$QQ0n$3B,[CMj9C[HXLqi(PEl#
T#Ip>gVdLd'('re3QN$A.O/Tqkmi%-#pPj%soM.da'V/hTP`^al_)!h?<Qif?0'A/T`US-,>
F91m)%:WInai`Pq;USWV4\fXV!pqa8=!4CaNfn5c7@G!A**"[sn..-JA?>5"\4i>b[j'BN+,
T[?L;`maN#+AGJS!N%?71rT'nlPTr4[4`&+rK#;6<Fmp^1lm61UW>G-Si8"R\2TnAO7`N6(M
9TiANDJ)0=e=Dj0Oeci;m"qKR?poC)gB#4"`?17u!&9,*`kq'/0=R`VUQ%X1H`0?W]A^.XZc\
hph%CGQV:qZ(0fVn+Yd)ZOQg?]AQu8YnC+2Hj/)a:k1kGWN+8VhI0f[=0?,)=/;lg3uI._LTO
'9aBkgG4Ke%\^SL0a^Q[5'E9%AG9<l3gjF%QH)>K^IMdgPm&Wri7EokpA/dW"T)JI]AQl1[%a
lU`FjQ:(Nu,1"[=e,J[H:eP8LLRkeE)#)"F+Q9hOoA/Wi)X!c.ura?cBj)>DXtEC[Yc8IELM
j1Dd2[m5XO6k4)VePmr@fud&SG;1G9,:(=m=<H;TW(3>p[5T]AG0feE7fY,Y+@t"7o6JlV7:8
CKMr`YSu(`+M^?eK[!Oo>H#H*$IUI1@eUm'^r*?*s#RY:`[+pQnHl_jXM8HI;J_kE<JU!mFZ
[R\K:Wm3phC/$:\f]ArnTZBV%[BA@!lFr152U[Wg0QI,h)9U+rd-cd)Lu?V82EiF_qjcoLL@l
;QA6da!s2U?E;6^%/iW%Rddn)pCSWOtseU^nXiq\lh5u=T"`na7I%WKl&W<:]A7Qnl_L@mF%o
>sM;qRZ8]A1W_8mmS)@?"oqNMmMn-)X4b;1D.np$5-1\BO#uFW1d4gHE)t?%+qp#2]AZ1iWuaA
DBPm4qgSrG4F2PhY9=rJs.*2E69[ucm4r$N$AXlh>3@%,q0]AM$dM!t'^_Q;Lb=dal&L\PROa
=J@/-NYF6"BK(.b(:PH&(BT9oBeRB`j_hb:HPk2F#sl8D6H=_&56684fnEk'^T126G`J@Mr$
`W^KN:;-aA;1t+I^)3Z.6X$Y8iOlXhdd@S3j1YbhQj4caQPb3C]ABdkh_=ZEKKPk<mUs*)sKe
dq)T_U<S4,!6lQnu#P=DJY/2Gb(cffbU%LoYU(i;%q7pBpCOC'Q'[#jPp]A(d^sG)qNePcJ9g
4ro3NhJ!.D8#`P.[F06H?.([32Pg);)T-73kmkm_!jNm8f6+oeV(Q`3M2^=`E>'!E-/KP9#0
H<r$_qq17/XTmScK3+7d6CA#LM]Aj#<8Z'O^=qWGF<(_j:o5cusD)SU!=s3u\`\aBSE=^;$oQ
V-`82h-YgfIg?o.%[[.)$N,Q,\]Ap^res"fETJ,<>_):[ao.6"DXCm=+Ih\[GTo]A`o#4o)4]A5
kA4t(3l_=:2<emorK:iNZr6A0ml!4dcB<>^$U9fklVsjU]AH,i^c-!02WO2r1ca+1QLNktk4U
I&,^LJtbWQ!^s#-U)GRjjEXURBXuLq^+JYEX;FNQ\5PAatQ'hfXqtEC(A]AgkDtk(kaLa[605
joV,g>")0ZWT/'ic`?)o&qWN`jbWG:h\"?JuMUA!a-RG_s7n]A(St]AoS`Lj6mVlIT'6'0fP0k
):?WN`0tU!IIAF5+.?ck1+&hR-ijS)arSl2c+>+5BJ/?JB9RHr6r>(u/3$kB:QSTc@c["8+V
"Q`ad"e5`no`Xpg#TW/d#u:c$MY$oe*t(^5PYbdfQ"Pq/>Q@P_9LG4'QgO[-Q#Pn'F3B8%P3
gEa=J^Cl#/'s4R$[r@:CG%@<qe\*F=l6IHmVdH9fQ^/\jA`rE,hT8^cZ':ceh_1F=NrjU>N8
ZP"&Le&:e3-pmh1uecdNY8fgW*1u&cqWZK6h,$VM!l%6jk5VFs4%K^^qB"QTDoG&n*bN?DM:
[Ms+a<!>Sd0cmd\aTkp-,#PlD%qEZ`SMC6c=^Oo9*lT4i1qDkr''6/G8=.9VMJI6Q<G:caoJ
*/OHjPq53M%UNJ7&4RfJ,;cD5"[;V>&m);S)^^\GcUYO%&Bj#RKg''E6fLD\Zr'a,X-<!ZqC
'q8rEWB/.,`_7LJb.j8_2Vj$FK/-N+u#o=5\"rbWrcDfX>o:\9+bcmgBFCL"=/uY7)@2^YOH
f'":C8=/`LWfZIUrD>Wo(BNjXeeACUal!U"a,r4)a]ATA=7fh'*9cFOLH;<1q=ZORXg@jmRNK
!G=2_TlM3:C%Ih\[faICf,,1[)3<h_XEb"!/3MYb2t]Ap3V$un*oYPV=PL'6pOo1d7[Ke:I\r
=qM-DXV$:>g=Ro0RKB%pfuDM;=-CG/bO$p#`;:7iLeAPg1;kXN-_g9?rqPk1.W+RY%LNN6g5
g1d&pEH,ZZ4!Eh\`rU^=oPYKd[rIeRp$c7R!O<(8AOkA;Gq<gab85tjNiY:9J'3O'E0p7kfT
\n"IF@57,Y0%p)Zu^eWN;)k*Db3+[VYLcr:OA2+0kWMJS6]A(XEA>K0rRW4iho$hYSUG-nS)$
@ql/nM-9aoNM&8+F-<b69/D:@pb9c5&YPeg$?:jS)>S@=tXE==ikkI@]A9<NhIR,$h#nD8cFG
;[i"!'dP<8kVorgP@Yp[+dKZ`(,u)L4:F/7%jGCVKF]AQ'b*?2^Mf'FVaMC.%rPf^NqdGUhEY
%E5Yc<7L(/.0i:rgrK6de="<s.C)[Ma2G$TW)o*n8:=8rh)e:_>F45X8rn#^NpA/C?@)OEW4
%B7(&_1=PK[a1aX]A[adGFfh0K`[n(6bs(Qo!EK&HcIOU7d&2?dfE0I_!O[I1Go,dfoDs$s?#
TEPgI3&=*i7'+<c/%!N1@e2<=K3]A[fn<OmS"7^oFM3233!Tb1p?lD]ARPi0TU'X.`M7+qkZ[]A
G6%J"KLp^--5t=;n+k+aJ)o*<&OHs@)+eVVP[n(`HFDi<3)N^SmdYM^o3m_]A40l.N&<_kpoV
V),K,!1FPnM16pNEo]AYmV^X&3b8J&2?TmafMaDMPdrTip&j\R_QRZSE<1^.[+PO_[(p`LR@]A
A=DFNu$4TQF&)6$f$/QSF&^.e'%fAR$MqA<++_c\EcN5NM`mV&lc]AD_d;Z',^*hbZ$Bd&1HX
?+[n,qf`XKhbZJiGCC+=fMB.FL3XP99PMq1nDU/YhmcmHAn^DjY%(-^8%<EHFd_P3$db:a]A(
/k=XPKb5huItR;s>F$9R&PN?qKm#jd'.;EUfM0B(_108%B^YpQjDpFI"(cc^$fIng/C[JH37
1T&o#UdKao5]Ac0&:JV?pW:mFSo<`LfXi.6mE+01g8ZlG/?(9ns%\"UcV8NBYVnMd*O8$k`7N
jO6%=Q.'B.3=nZfOFV]A2]AMImmU_3BIpk$+l1dgD]A36G(7sg>*c!So(39W4P5EUb]A*XPXng/*
EFR0]AA3j0O*pW)1<E%Rc')rB'%>%9u/\5futJ[%UKAlnNR]Aqol^^\jd5Jqf&:@hLe&j\ikmN
a8/]AF[h0#+/s#BS@fg%Tgr>]AS`#2^HTIpLTqcesKe34iqp>oC`W$n!mX9lN2Ktn"cdkY-X"l
dhC-G/:!k*#Pm.V=U$U^:[9b?'/$/SdU8OT:0b)17QUfUI)6VB,'=9b;8fR%2umon5d=Ysu8
TD7aH!ZaGN5Ti>fHNqu*Ahousn2[WlObU8F6,\\V8U;onj@>'%G_;k98B"8s,Y@Yldf'":0m
7XIGDm"K@`,,Y'F&gs3!=JjPf#?[#ilm7-`ESp!A:)>W3Cn.?Pf6d]A.@0U9EP3>S<oP@e9>5
Qn-Ilad8K8fF./regN.l]A[rCmuVnZ>Hr>W)f=',H"lqqZc0DKTR=\V\fST&o\<e\Xrhm8,L;
=VJ%UQd!u\OO/LSF9fEmXG^h;8B'%/^l5<jpnUPd2MbYmn3!]AsE2arhEj`n//KON=Mu><V[I
%'LBOjg_M"h]A;EZQmC%RDh)T9pI\0_O9OB0*^\Db=7)eW1H1PV?1`\rO*8qarHa+KqPo`Qb1
.5:_RU<s318`/#0gd_]AdR!f`NNVK`#L6[QuPb!*(EokPY.j6#fk.g]A']AS'-9[4[n`E6U)6eQ
gK*Q5s:P!;k<76*:]A2^ku/W%>4)h[Q.D)?UO/XEm0MeYk=8k=f^-PJ-Oc9,q$=R@XY1Hc=tc
(#D]AO<_"Ua',Z\4f)EroRLRboR#J@6)OG-bJ8"Zj(IB:Q"*K5@MFCIF1UQ$h7-3&uXURtBoK
e8/@fq_\eNC3)M4UKe-\U2'm!O>kn!4.K7/TW!$qra6E$Z@fPR!D?WM/FS?5Agf7-Ar"Us/I
##n/QERB\]AJ1)IAt!9+=D,T7^=^`j*^nbBK/MFKt.7:$_L)`p4``/a$*u9aUlu#VLpRbT:2[
9[o,_2[6t"eMC)LB1H`'>I=+bob3!;O=ZN.5;QA_k7^/3Jpa`>;AT$ZQVGt#\""f2SJ@;hYG
T`,f-a$5\mctrhe<0c1\s6'=R+#Z]A4V_"d^t2m2[L<XB'I7S0;&iQ)+(>$h#/'t"\4J[cTW(
l>o2Yp'j+CTt&bOb)VVq8bp-n)$+BG.G(:LN<)&#(8C%gK>d9c86PU;k>cuN^,#h?,(/aB.Q
/ocLr9,p'DqA@P`9t).EG.'O>+IinOH*r,*5C]A=>PY``+`K`55B[Uom=@)Y!-l#_p):V>LY>
_gR1.c?BElF7A)YCM&UALdtmnaEZo+kodM+[1Aoe$*f^YI4UGt:A(&Z_NP<4EeN0pl*<4hl9
Rd06,%hR:TKj?\]AD-bm^3&h&WD'$P7B?g37Q*PB.kW\sbGYgrWOE>-1Pk0g0n]Af=4t$Yf(`r
Ja"VLGdu0ejt8JC]Apa60f?JIXo7e_]A'u"9BsntE:(FJKPH>B7PBWXS0sfXN4/-'H`6gR.,94
T(X5WG]A#57DaP`n4<Olp5F\%[=o#,(E#]A8)`5]A\)l*:]AM1!=Gb8UlR+5'b=QhEdp`Y5:);np
4>Js\[m^8=bfj:r]Aspi'SWsBhfMd;9Yo53P$c:Ql17/$aZHSe(b8B#LTL8^R=tGn(/'%O0S.
Vl9`lB4g<GHG2?#c!o<*N634RmE;7>;[=cj/F-3"1WdE%p##aJ4/#GX/GXBSl3c27p.n0Sb1
[il/W=o*&9I=-?QrG)J;2M_//!c0NgCI!sQ0ktEBlliR4c`n\FG!O^ij_KUsZ8kqh[fu\$90
1B\dfS*";=$FR5gK-nHla0e[a$$n!;j=7KoTl&>R[K/b]A)@\`\Sk#U#?d]A?ba<$(_fLA7OWa
_4bSctLQ9D:t-PdZk>dX@i88!.r82tZR/\1T7[,+3FWG>0D3!#m>R$%\Xe80>Sc*qu3$Rt>`
-uD5+KWhQ-Sn8`n*iaYEqV28=juYIogiqqT>U$+/@M[[G&$GQpaQS*Q=4:EJSW_"-j1VsUH\
L`f=fS!UC9HBERofiRR9O9%R03ipo+//tXO8B_?n8]A7,OOn-En]A2e(d8f"%CMfWZNdTeAP/>
<=Hfk372B:XC<#G=NCNIH[_T6+OV,P9WK_?41o2tA@,QWo^iJ!u=jrgakH-WM5jdWje$?U,"
X;U%[BNTh@JV'R(c?'\MnugOZ^ad.+[tCe:ZuoIA6X9@:h*IsY<>t/4ND>8p=#P^!i*)V:Nd
`I*9NmGS1o%1`"13gOjad;79?nI&>p#dS:2QBHo\VH_fSu$UnM1_k@aB-cq%i'c;O9M7,jV<
pLrKX3[$5G9'(MI>o_<K)^D:E?'"ogR-=u5C>QL=^RbH%e/`AeRN2O9@G``fA[5RKh=.t2EN
ojTk@A.M"d<rig0^:-H=:MA;8kJU)_d^-;^9I)%`Gg[bW;VG>#+WNQ)G8Z=kO;3C0>GA6R^(
?o[YD5]ADo"R)[72UFMI6jY]A\NEZiiL#j5!2oH8S'`\=NEk[8%4(^4ne8[dD3b'BS6Q.NbN^&
Ib2)ggF((b>@-1)ZS3+fQ"r$^:drbVeDU9CZAF:OR`RCi*d,$7mF9gWF4QQWTa9shPeZlchF
n_CsA%,VoGYhA+6]AD+3df:;dO^G$[FX@T+)%=*-jnTd2Q:qc?mdJ+NU88eoH"La8<_LS$H(F
g?il!]AG5ec2rY5aLCSUJ9"_<6n>D%%gb;dDTERQ6d%%6YMB<qFqR]A6hkt^tBE8=(1;BSn"Vl
TX>Oa;"1rf8=A7092=D1#Qrhd3=N5'oL*87OE1e'2`5Ml!h.!V-`H69q.Ofdro`#3%i5!PCo
mnn4-8fQcgFO)@%cf&?@7\kl<=&^=9&/[;1b_d?TPiTNe3Zlo&r_f*:ZA@b\l<sYpFT8i^sr
h<5g3FIok(3OQMg4*pSd4?^0iIu@F2/]A7V"rAQ"$KO^WNDEYI=La\!VsW<aZTQ#h\C<jhl&_
bS_XmSl#L\mp"*)%G)G9AFUQ0R_!@N8UCJNMkg'+eRg$``o]ALEp@TPU^]AE$.c2I(JqA)"d4U
f43ai?$6YC<Y[jR6ZJd$>'h]AJXlq).&uT_?SR"%(.?[ao#51f7o\&\\"arj^?GSMfXO]A/p;1
=e@C\PDId[;rP;D30g<g*rLnZ=Bi&_5/CH6\t9hH#q&fd\)71XkBF>/4mS<cf1PK<It$MYD/
,9-&3+ke*(a`asBJY/;BG\+i\J1YYhE=(o!>0n2cYlK;)RL%)@m4OJbS?s&c(%I_L+&(9kZN
@f_AGT2(5N?@/SP.mqck09gW[kn#KN1#0f83MCAggRnp:Chr6NF`:<'Ce%jbb)\H?8U)rS\<
<!V]A=/$:lbnnb<Yqd\`[!&hHZJlF1LGLLJ`.+0<.7u3tY&UL8JIa@bjmt3[92+qXStt?ZP5[
K>RZN]Am*oY2P4oRl]A8InB6ZZnklWFdc>AgVd<5JRV'E>K1nbeO)9E\EZ&)g9oT?7[mOPl%cK
akDImG-JiMZ,:!RhYoH5"cc_;0p[8+RCfU)M6$@+<qjD]A#gbR5U[O/a3,?'fa@M=D.C#K1es
so16B>rBR%D=Zq$'Ni41Dbpl[dTJNkC\MN[*[Qd)8?$bCn,Y%%[e&>ZOR=*a`fX_XdS;J/oT
3TTh[>VhS5>l""d-.jIhm,*iMj$QAiCN2Rg^95[HJ`(gD3uK*$/9A./V[A*>2;uMp(b(FB6<
$Nq(l=c'1p)*)`sfMnakK`ZLBEUoj=k%'ecUYEi_"K;fV4t6751Wb(HcaEj9if)</;Nf3,Q8
)T@ood6+`fWUtP:SM2P)G2Q6#htuC(FAJApJL!VsfJ+s++!HDAG?XlMT?JL#"tFYgio+!unF
]A<J-/!Kl\eP#OAmN.`F1p>$c[.$dCE3W8Irf>..e"bl3XW2$SUC[.42IstSDqcu]AScUQm!N_
1nM)Ddd:rNRs$/H_mReM)rNt?DIXt$I31jj59_A&'ZtF"\Yj(/;:"?tOO+TnlIU*=4iCWJ)[
Pg5@q,IB2Dqt?QRf)8ZFn5*IZhk@/A>Vf+JNG*]A9l>YN4-Qgd'(<*\NPrjpY0lq(&)W^RhAV
8PdYsQ:]A'(i3RpNG[T?M_g9;kJNOa6(^mCqkH_!/2$A.81)6G'^@@>H=5Cot#D3kgbJ3:$A(
hmX1n>Iit.K$`u_A&eD+N'T8#)IN!&SW)U4%<pmm*k8:<BQ6,QUY$$F`t*"0:L!W;ggX\B_l
]AsY1)!-ubYO>4j1rk0Bq*+<BkGNd.22D.Ir%R>mTqk#JeJh&s3ATrX7O#sppU2Bn?_$$g2^O
8S@__70)tfR8a_P:N_4%PV`1fG->uI^3VFnN53=2;CC,tUE-1C.Wn5WlNJY[E`9<d`jm"=7-
7>h7[?O]A^J3b*r45$-6=YegIUD*@ZNF+i9Y)46;D.35s-N0Ata/q)V[I,Z]AK^*6nDWAX'g2t
A28@R/Qp'9?&/o.9bG=sI!IGmPfYk#'5;38`K#jlf"Md0e@?Z="F>nm.]AVIX^+3D'1k]A^+0G
a"*s;?tN7[b"!_!7_2-kE*mp3apWm[fEX&1\9T0Pm76qM5*Sod[ubEEjsu]A@(cCaVh%YiagC
:i;M/m^*dmkem_b'Nin-!_/F3$h2hqb!B$"8d0Y'q-<RQR&eB?9\_pKB"IiknQqSn&C5PRlD
jr-l\7n^l?`$lU#:l4QV6eiSSABul\^p?XUh0jg4bXWAB;/e@T[:;:$A8f_^R;NOC0Grdd,B
8HC*GT#i(_nV<G""H55gK@$/NKo1u!Y4LpcYKDKV`W&eI&9nZqT?GU.mpd$pBY^B6VlR_ZLW
(Ug?WL<ER[=p3b>frls6?XPSV44eCV@D;L>O%dcq1(1$5N,9A;f;[HJSL,+pCUCUSS1gPm#?
BqJ6g:>StQ.SY'`^r[pZoZi6*.a7hV5Kn3bF5&U)d8K<AFm?HH!.Vqu/,;8@-!B&]Aj+2Kp3a
=i^++fOf9d[Q@rN=u0(6PS-#E9l^$QPH^VPaQKV`i4->5:3sTgeJR4:mS[cZ:`!:C[4[QH8<
k]AOFP"")#_`)u\eTliKY:$iR917ZM5Ko&KpO36:ekA]ArnTb8t3^NB9!R0,3>fVn-X!eKJY^_
511TH@>A61%\F3AW'<)>3_,5jeQ,>oS@,M_#"n/WDZE'Lu/k[i7hFsn/,-`Z:=([9fG3/f%T
bjd+eI,=dd:S0((agCgPQNFWt"c,&*?^TI>joZ>!Z.1Ms%9r.2^4BXV'>_#&=rhU*X11@5/K
Pi;0'MEAoaSY;!4]A'TFco0g5=>Z6i1Y1RdFLPI8&4!pEV%HSs<=%bB4LZ]ALXI9#sc.i7H\o;
>*fVIBj/(X'r\@DlDWCMU[\"MT?rD"S`.W#Mj\Cle-hT'gXI?p5c$gZqOHE855s;B_aP+'rX
sPpq#+M_JY_JLX;c.1sk[[M`*9CYd8*%./5DRKRMB8;TLh;gF;e.Ftk8dDWIHFq.P3M[C"Fd
T)On&ldZ3r@""]A8#q3i)2f$&A;M/VM?nXK3W!9g07#jI]A`6g#@XAo3+5Rf@+>`TJQN(]AMM2B
U31TlS\Zd#B2Pr%UIgEg!YQ2ki]A,<gV'?rVr+.^6HR6g7f`aCdpE;k(B&KTc^>@r"!>E:(L^
l5q,`Z9/mp'04/cEP4!+k@k'&RmkkpDQ-(Vg-j@NZuiT82--dG3]AF$fC8nI=Nk&=W0u!e%X7
=9HEOrnZ_'6e[$Y?msR_RI7]AjK,YMmid#b9i4ZfAW8,CQX#[Q)Jf3%j?Q@HFN8e90CoE>';B
!0PY%RjF]A]A&2ZL%p%KYdBe,58&dt)g&(5;A",ihiE'QEo/E+8,*=/PNj.qjTNTR++CCh+Ts[
K`b+pYKmY9SKoUkksGtWqUOG><m=Obt:c#2K-GYMYla%TN/Kf7Rt8@.8jg5<5(;Kb<3(N/ts
N;Ds:A8`;h9lqZBjiSf*p?T'"WOcN-ITGjUC1bV[D+_Ju7Xa"Eo"F_U;q;@`\0G"A1P<*1VQ
3Tb#4QiB1Xa+%uGb,`n..J-b-<:(e=pJGq1n:N;WPU%$@AX++0D%u_L!MS.9/5FJ:Q&[6V5n
[.=hk4>T.?K!I=4d1\#bjlS3lA.kr3Q!tCd#EpJolsh,]Au9mO0;Ap6!EZo\)#Eq!>ZE=e#$r
p)#j=N7./<,ZHCQjIg`#3P<s\DeK\dmo?=/h4;r@]A\N%2ZYqsF(']A]AgJS6k%lq+*PbZaR$U)
m#]AUbPM'E#.qK=cL$W,kHa'+?ug43aSpKE<(?-nk!5+';)>7m&mD:Eec(0sWj-JM;*1RIm:u
8P0#):bPt.*\@EqBW<;]Ar5g6R;_9F!Q0)`'oK'$#Kl(@&pO74Xmb*7L1h>$-!P&N\M!d%1j7
HVg+`T&H6SI8J18(93/;k$,,%Wc_=J-l6F\[A3:Os5Uf2LN:$\bJEB%eUuZn(7).j%dfrQ["
nA%*niu.BFjL(4g^"<clUp@htAFmauT0flPF[(:/"XA?Ru\u#IQ`'MdZFm"#Ic=YRSn+(@s1
Q-2QOF"&$V0K<r!;cd+TMF`?TaG47-D&:+UFKH<GjTngu^kTTR0H3cZe*^UC2LX8EP+nQ=l#
X*h^64]AcFd/OS-'%XR?E65Z0W)7,=2>k5TO6b?dQtLOjIn:_eh:FHq;l8UEidUq~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="518"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="42" width="375" height="518"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('CL_TOP');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop ='12px']]></Content>
</JavaScript>
</Listener>
<WidgetName name="CL_TOP"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="true" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="CL_TOP"/>
<WidgetID widgetID="fdadcd6d-c749-45eb-937a-d5af1092c779"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="CL_TOP"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[884517,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[11468100,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="A"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@!3;P=HJ*?YHqQ<)>,;_#oYM+JQbR)PR9_>(N]A<=ATVqYae$`_]A@%0<fT3jM?Hc1C(X,@.#
/cc@$<l)=KnM&#W*me1-bmd5Y.=D*G>5Fs1l?)n'(cSkP+=!oBbn#h((Zq7h,.9ocXSroK;8
b$A,W]Al<dUseUsH<q[S5O%!oK,3YP;W*'Es_5aY$pLfNl.nrk^cIplf(H`G<;iSq;j]A1jg%6
ps-IZ892b>lX+sa?FqD(GB?<ce<]Ah5l1b/6]Ad(!Dq\Ka[Fs[qkntFgG25)ePoA;D5/1l7@Y1
[2VUd#PL`3;*>MG[c"A>G!67aan\Ze,@nC`#p\k:<Ro^I"!#n7/4Hi#(7?%JW15Ua"IOm526
IHF"!QD$`,]Ao"rjeuE=p9a=UEr&;c[jt#6h(%:02Wa"M(DYR5UI]Ak2YJXoX1h2]AX^VE\6'QB
OP?92&1WlmC+61Xk*?;fB*uLmsuM&RNgf)h,IC,Xo6T"J*36q9p^/<so1#0s6P![<qFL&a]A/
slq`H.d2n#]AO6j]A?C`4J7;cDb_)-_#9l.p.TR,p&K8p^bH2"]AjuhJE*fhG%&a$Iac69o!X^<
%^g8;h#jr>jf@a:d18>2Md<qqps5ZY7]A&-no>`ciPMa,#!-aIe.5-d32&cp@3l;F2RNYHC-8
K)Ptdbe>/&]AcEV07J\LO>7b0?V7HA"NQq002F1K:!B#;jr3US^dBFVminkQjDNk/bKDq=bjA
L<R*WmRT\8\BYlFbFBtS_Jm(]Ao)\a8?MhG:a[>uhW/OHp,re@)aJ<7@.J'=<#Xq;$cmGYpW#
Ztcs*"<?)%Z-H=]A^q4&MS5a`9kC<#";%?BYh;!-!`,K-U_@[E.*n0,rUQS.F$i*3N"?^n!2Q
`+G1d&(ip(f5[_)FF*tk#V<O+#0%ML_5;/[5'c^:R5M"@\8GJaBD.)L`Sg9!u"Kk8Fk-:%T'
Nks;,^oXD?^X$b&0'rW'TYC.<=FQ%CQ_*Y6Cja$fm1ONF(mpe&ir]Ac[n)T&QdJAJ@WN>A=46
*p=>sjS\Hu/4GiOAj<YL'g,#(m^[jAn/;J<jEVo]AcD4rZ\CdOg!UGW,OYndAA`9jGk(j)^'7
)#(_UZ$DsZ7IgN-6U&?l7F@$@X2B8rd`&bR4saXM8O0DFD%)RZ9t)H?,a(D$;)D*pe+Fm,G&
=5,Z=mpnFf5!p>['>ZNYYR[S!e2*PZM`lHT(kZ'[R[=Ri8Fh`Rrg:g970paK?Pk=:f:]A`^Nt
:MQ'eTKD(#;q!%S\)FGZd1WC\q1F;PE.*apohr&b$lg,5Kfp/B@lZao'%Ak@ZOq3X]APgQdG/
H#W%5*)<g_^hq^o[u$p_:'#QK*$N</^]AqqCa2]AO]Ar;EP:E;`IQcT2m/RF-RF>1iNQJU4CAN_
2a@GubkMp_sbi+Egmq)?-@+8_lQ/ZG%eX+N_^TVj%D;lXlhlD7PndC[>KO&'e2V`N9kddcmW
Hp>/'763uPgg,NOPfaC/Zc?G&HATQhEuAeJ+!&`SG^7VpCS(?>MjngGWuUb>"h7Ct-]A?`AN-
uK;l#Gb3h,D:PC_;lKJnl<GUtT,**M)m.X4uL;*PSl87<fk4ONtM9hPt^l)X;&MH9]A7ADEqp
;VUY0MqnGQpp[t2se-F)jXCh9h35*ku!W-W\C3;L`S?k,<p/fVE[M0Ic.(c,-<'Vt+ho[AJ*
@.gZ:^B=:!!*-(#S8+DJ,fTO":,P]A5_&h8!rROY1&<EV1tBi<b.sg]AK>XooolVhXB)Tks$X%
mj$UG8*l6dX/^.j$\$`+a#=+Wj*WZQ@+S/ZVt!Z\.("Y*?"$<3Z"'rXA$.St['<1j<c^ZlE@
.D<KF=&c&c4?^@4WJ>I2rr<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="CL_TOP"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="CL_R2"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="CL_TOP"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_sel_sc" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1692320229115"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="40d26e83-40cf-45fe-81af-c71a196adf8a"/>
</TemplateIdAttMark>
</Form>
