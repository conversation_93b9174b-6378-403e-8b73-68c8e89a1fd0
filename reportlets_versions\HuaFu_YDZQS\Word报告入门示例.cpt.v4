<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[FRDemo]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select * from 销量]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="area"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[FRDemo]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select * from 销量 WHERE 1=1  ${if(len(area) == 0,"","and 地区 = '" + area + "'")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds3" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="area"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[FRDemo]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 地区,产品,sum(销量) FROM 销量
WHERE 1=1  ${if(len(area) == 0,"","and 地区 = '" + area + "'")}
group by 地区,产品]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebWriteContent>
<Listener event="afterload">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[$(".fr-tabpane-controlbuttons.ui-state-enabled").hide()
$(".fr-tabpane-tabscontent-bg").hide();
]]></Content>
</JavaScript>
</Listener>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.plugin.word.ui.ExportButton" pluginID="com.fr.plugin.widget.word.v11" plugin-version="2.1.9">
<ExtraButton ButtonName="Word-plugin-export">
<Buttons Word-plugin-export="true"/>
</ExtraButton>
</Widget>
<Widget class="com.fr.report.web.button.NewPrint">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Print')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[print]]></IconName>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<EditRowColor setColor="false"/>
<WebWrite SheetPosition="3"/>
<RptLocation isShowAtLeft="false"/>
<UnloadCheck/>
<ShowWidgets/>
<OtherAttr autoStash="false"/>
</WebWriteContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="12" rs="61">
<PrivilegeControl/>
<Widget class="com.fr.plugin.word.WordIframeWidget" pluginID="com.fr.plugin.widget.word.v11" plugin-version="2.1.9">
<WidgetName name="iframe"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="预览所用的网页框控件">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<WordIframe overflowx="true" overflowy="true" showAsPdf="false"/>
<Parameters/>
</Widget>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet2">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,1866900,1143000,1143000,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1905000,3048000,3162300,3086100,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" rs="2" s="0">
<O>
<![CDATA[地区销售概况]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" cs="2" s="1">
<O t="BiasTextPainter">
<IsBackSlash value="false"/>
<![CDATA[        产品 |    地区]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="产品"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<O>
<![CDATA[销售总额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="3" cs="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="地区"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="销量"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(C4)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D4"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="4" cs="2" s="2">
<O>
<![CDATA[总计：]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(C4)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(D4)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="6" cs="4" rs="15" s="3">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Microsoft YaHei" style="0" size="128">
<foreground>
<FineColor color="-13421773" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.chart.base.AttrAlpha">
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="true"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="9" align="9" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
</AttrLabel>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="72"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="true"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8988015" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-472193" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-486008" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8595761" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-7236949" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8873759" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-1071514" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-3881788" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="20.0" categoryIntervalPercent="20.0" fixedWidth="false" columnWidth="15" filledWithImage="false" isBar="false"/>
</Plot>
<ChartDefinition>
<OneValueCDDefinition seriesName="产品" valueName="销量" function="com.fr.data.util.function.SumFunction">
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[ds1]]></Name>
</TableData>
<CategoryName value="地区"/>
</OneValueCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="88398089-3d25-4107-a512-1cb9e3c922e2"/>
<tools hidden="true" sort="true" export="true" fullScreen="true"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A7"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet3">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1143000,914400,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3200400,3505200,3352800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="4">
<O t="DSColumn">
<Attributes dsName="ds2" columnName="产品"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="0" s="4">
<O>
<![CDATA[销售总量]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="4">
<O t="DSColumn">
<Attributes dsName="ds2" columnName="销售员"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="4">
<O t="DSColumn">
<Attributes dsName="ds2" columnName="销量"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(B2)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" cs="3" rs="15" s="3">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Microsoft YaHei" style="0" size="128">
<foreground>
<FineColor color="-13421773" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.chart.base.AttrAlpha">
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="true"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="9" align="9" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
</AttrLabel>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="72"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="true"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8988015" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-472193" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-486008" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8595761" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-7236949" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8873759" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-1071514" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-3881788" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="Verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="20.0" categoryIntervalPercent="20.0" fixedWidth="false" columnWidth="15" filledWithImage="false" isBar="false"/>
</Plot>
<ChartDefinition>
<OneValueCDDefinition seriesName="产品" valueName="销量" function="com.fr.data.util.function.SumFunction">
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[ds2]]></Name>
</TableData>
<CategoryName value="销售员"/>
</OneValueCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="a4cc8f98-98de-450f-a6d0-022415f6388e"/>
<tools hidden="true" sort="true" export="true" fullScreen="true"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A7"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="false" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="4"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
<initial>
<Background name="ColorBackground">
<color>
<FineColor color="-13203982" hor="-1" ver="-1"/>
</color>
</Background>
</initial>
<over>
<Background name="ColorBackground">
<color>
<FineColor color="-868841998" hor="-1" ver="-1"/>
</color>
</Background>
</over>
<click>
<Background name="ColorBackground">
<color>
<FineColor color="-868841998" hor="-1" ver="-1"/>
</color>
</Background>
</click>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="560" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="area"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="地区" viName="地区"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[ds2]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="435" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelarea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[地区：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="355" y="25" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="area"/>
<Widget widgetName="Search"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="false"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="960"/>
<NameTagModified/>
<WidgetNameTagMap/>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="true"/>
</Layout>
<DesignAttr width="960" height="80"/>
</ParameterUI>
<Parameter>
<Attributes name="area"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="1" size="120"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="1"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<DocData class="com.fr.plugin.word.reader.EmbedWordDocument" pluginID="com.fr.plugin.widget.word.v11" plugin-version="2.1.9">
<Data>
<![CDATA[N-PMe.*[qCfX>YO[dBJ=[Vad-D=`^'gU>?_Giuj=2WESQ[h5egZD+,jbO2GbX0]A+;'Vp:GTE
+u%_W210!<2A;r%#d17LG0!p.k5eIpjaT4!=M+X)Y@5eHH'UE!-4B5koPjJ\=4;]A8^r._R/\
1"%)Koap4Hr9gm94>VmYkYtBpBnSpOJ)5.u>Ag;,">Ff_E]AWTju7`oD9le$iUH9Z("WYF7ha
bTs_@[5<m,15Qfq`G6[[8E]AJ8n'r:Sc0b2O"ZW=&ISeg*uLWsL8Y;XfdVTOY0aQKe`q`lm)7
>:LP;6$DHu+mf"Da`6354P@pg%UV?#U9*J66?.JiAuSBu]AJXR@n@FTCNJ_$[3+^b0dg'jYhp
jFp@($$eZA@6*bCDT73iMun3N[;i._l0VA0YNSaN'7KU9*8c!hP!MT>(9a[sYsq>"esZ'YcO
J%.K::_Ta*E7d)MBJmlt>));S*_A;t$>b;g;MmSbC%d2A,_`>^81Yog%Y<kd!G)FDnqC*$l6
Lg@32h!HW86`\.X=\r7B7E_Y]A`)5mmBRA>Xa-H(R_L,l-]A5b=1Vm9IFkXb/]A<=3m]A@Q><lmJ
H+(7.RtP.14O`2o[an8s6^7>4%GKaS.Ur5/N:b$&Gdl]A7JO[MO;&4*9p,S9,"bH?-u_W8[@p
#%CQ_]AO%Dfb3YnbcB'r^$YaHP6Qr[![B<r)B^?X9cP3oEd6O&?dgZ3eF,7f@u#Z,0&3Th.C*
LIe1_Eb\(SR_/uN,?rXC!P)Yc4/#Wsh!?;a*)4$q3,Ca5<,#iXWQ_l@C)Y';=D&)f6"Cs#D(
id\C/=j$Cb/J[F=14r/$f]Ap19)H"3h_B47eghXRm-Au?>qt`-Ikt(43KCBdg:o(M<n10#aA<
rRJAp^rWN6"n`-Ks^QMpR9cZ_I(MY>#BFDV4TM&!%bb8iBg"T*1=og:-i^NE'gd)Ln'2_=l=
38'nj=A:-I[TQCqrc2<iWcU-Gbo$$D,ULEFVO>YRqK6/m"rIt%5brW>r9Y_oo`jeF!'bFnW0
>5qLQ\u3e+?o2s>`hIkbs)61pRj`4seHrs/3L:<n#@s,^TN0Io<-J22Eip`hoP4WRVY&`$W$
UP8aZQea*%YuieP67Q+br4+n[Y0>oiIsCbOX_ZLG?F+8G&1=u]A>E+f!0N=]AMXK?iEjN^(0e-
Vk\8lZg:.fol-,\6K2K1ZfH([BAr+@)G@CS9GH&;Y;s.\te\bih#X%U5-$0,&Qgf.).*V.lY
8V[-dni3[K!CB%L/OP.pqhKPd'Mc4-7k-W$)LZS[aJ"U%:O6f&RSFhaP%_2-"KYP56#`W73@
'5V#[$DVi?FMUUDC-Z#AZG4jT2G);S$EW*Ac?:Y".kQ:[)"]A=S8jEg$NZ)?4,cSP@*QKY6',
G5h\O;JXD]AGgj6j6REDAN/2&']AYV]Ao-):DLoE3Fbg9kKS[-ObLrZEr7\?!#5Fa5Qppbh!?#?
s$mV]An^Wd:poAsV%KHM/h[h/J%r2iVJ!b0M2<fA\)g/cOlDO*$8osC9lH2H,WbeHu\!?W"`)
R#BL_*ahADmGpiudp5.DI>0WEk[Z"DIgEM[0pYZn?+\AG;L#+.PRt!8l+Ddb$1Ygg_K_ANrD
gfiNBn0a/28je)Eub>[re8a+Z3RRG60O#ik6^Q59kINP,NP@:L8-[:0[:DUIgmm`1g`GlOc<
$'N_=r@hn4Z[)[-6PKNq4r*G]Ah)l-G_ZSR7e?fj?#jm;Y>JY'i\KC"dPB;q3LW4$'1^#^A[u
;<66P*fMV58cI9&/[Z2A8G-1__j&`S+&Z?N-SNu0^Kq>?%?XJ>HRn5G?>^UB+uCXOPaC#cot
*()"AOgjt=idc1N%00%k2X&epoFlKaPjG!uZ6-.?L0h3`hi=74SY]A7RYTpF8=VNsV*[=A"V@
rm-gD7N7-R$n0)Z<cDbEa`N;NKQ8'iVQ82F$n#HJLSO?4QZNa-6mt=lSu)<F<[/aMt(k6-qd
k-7VM%7]Aq$:pf6l,ZRIYl>M;)s4:$ZXr8AKLn$."@IM:rH:?lDSZ]A[CWY\"sNSETgq5JB@cI
Qqaj+"m),JMMU:g#h06G*B2U%25A"e)h9`'$D.-Mum]A?8hsa>7MH3l"F?\q;QIu6FmqM(dYJ
E[BfmG8*Y9na?hP^6%^Z?.<?JWR]AY<d>_,L.OV;G_NbPg(\hT,ND!/;Y^fdOo<a3oCYWoO52
l8"V8PHBEqig%(\07c9\FR^Y6@,BZc5/tH3PoFEsQ).6Ym=^sGgDQJJ3ABA23,fAO3JQGs$"
Ll+ZXL`-*&r\ijMD'FBG9EQT1HC5qR.t8JF))3@fa#Y)MnLMa<1D)Sb)jJ>)3:5q?9:rJ;b[
<#TWZ5:_,<dTFWItglI+$)G!I$%4\K1!gT3MJjL\18>-XZaFHp.$a%=4B_XSJ0/PtZ!;[&q/
Hdbc"L-UW-j!<BB:'R;=(*b'dVmguMT)V#H$fE$-N1s+%1"<N=_O;Q_f;;l83*Gpg05TS)Za
&)f)rrC#S1+++Q%Ff*O#hAOM-0"1Rd/@m#]ATK4'(_cTi$JV?WUs[.8iZb*>t22?Dp=TGc$)@
]AXXYe71DuFV2gk9P1Gj`Ls=Nd/^]A1[&\Am2SV[2&$Dkm^FYo4#W3<^gpkE9d:Xoa8=dS5Nd=
%25+d0/QC[F)TUinF2:J)T!AOX%F^[h73$=(S&[6)pAqOL5Sh-R!TmtrG%e%l8TA9^;P';"9
k^7dK$H:1o?i/_aMZmeB^QLjF%k"%+'.NL2.$\-L,)3aeNT#=>:Q?a^`BT;!8*dU/YYg*aMc
4=0_%&Du,b#%Dp2Ld1.9JkqUrd<U'g9$_L\j%f3]A[,Q.bg`P`0h5.\.[blspJr-AU.E9gg@)
Tb0HClX7D.A;.*&K%,KVE'$0FJ+g^kLDG'b@EU$VKo7Sp^?p'T'_JS"d(kY7DEa^trTpF`'K
h2."7l9*>6d1q\9A6tl*`/Qf&kD@5Ipq$aobLm`AW?m<g]AAa725nm1<DYd%i7ksV@=6E:-_N
007.C26D4a+7.[WYr_%%^$r,:[=U`:Y'&:4`rj/9=K1^S.aQIq'Zf_Mh"i3Z,uq1&L''K/o3
QiG5UQ]AeU-uRXLrA7iIC-,Tis6DZ0VUoENSk6ipQ5hEapAH]AH@CqaH^VOjeQjGL$G8SCEc(H
>rl%J:k4oD)fj5UZt71hqd-TIb=R83dAGhGRPH*'9+4A]ARZC-QY?c]A&Gpb?O,5R2XVJgF2*H
)Mk<o\Oo:^f2-TK*lF,D+iI#9)fIYhT7FF\rWZ'M,3VaeVR%:J;EFT%KU^J^,X_c@)\>cG[k
bg90(,0/%lOaj@?Ec\r,`2,ccguuH*RWR=67R$&P,F<`R=(.G\9=)\(mfWQl]A/N*m0;!egpS
]A4'=)ApeeGS*h4-#D6^3-L*m;3+W#L%O8Z$Z@.LcHP4FA(%=),IWrkW+af]AoXVOftF0o,ac%
9B6P&MMIV`j^CbktCc)*9*\H9p9',.\i+has]A\%G_euW@chSU]A6!t.itVITW[Ki(Xo/6'<(r
YaiqE(cB7<#le;KY9(H4)MW$VV5BC25Q'sf9Gg,/t[d"PY[qhl_HY:.Dstpj^#k"%53"`pj0
PMqU]A/rX1M)PLITmr;>a`mbn"e=iY*7C2R$heeR5S)a"E*HgSXs^nN>K?<5/epk!69cg#%<K
##;ng*['PPJMd9,;k^R4?kV[l!bbd)1!js@JMjP52:Yb8I%:o$$?WYWXq_:Z7,99R="_"Hc)
i\iUmbNWi(5ifd_c^L<!j8lU^FsYf:*0AYlo^@X<6R9@L@d$%UuS,ecqQlG;R[RrRlq1-Olj
3o%]A5Y)%L,9*RX4e&t7U8,-m!/V2o;51l0BD`K8dufMdPtTjhVKrn^>7_SW<gG;Y2\H'jA**
5mqTq!Jmjg^r>n\)R$i9Gg+BQ+@oOrdVTpKQp">/rP]A8F5n?u/07i#X#k=>ehHg0cAH(4"&1
:r9dG)N0NntDrJ75b&GG2TIqardECR42NbSJMipUPO*0KW%+N:o1FP(ER=^#M=6F(i<6u;ku
8//\r7T>M^Oet"o#)XEq'J5Zt?]A,-*rsn)hC6SY/H'1[.3uUZ;IgrmDQnu5LpJEuGgDs9%M;
O@NXo\J;GKq:[/IFeNUIk>)KDV!\M.<hs%3ud?%KFWOr)2ifa?L/J):h:F+*kT_4^/]A6reQ,
<^p!6?buWu1Bj4P9:=^6UCt\Ju)=I@X8+23!k)upi&)QDVG./(Q3de>G/_-]Ame*>li29F%c#
kp*WN^8XaOcg$<4`5_.h@'0"clUu^!._DQRfhgG;q9CYf(jFp)5598(,8hd2p\T/okaMnZ+J
ZfhUL"SZNF#X"7mopj\YP-,=38nW8gbC4Lm:KO$e<!RguY:e+gZ.kmltT/FPSWW&O<fM'hDG
@ie\!Apj$CFdopI.c=9foguigi`!$`R1<B1"L&B4d1D`JC3`+7\Uhuo#SH&SDZ*2]A.?.Ud>d
I?[=C$=%G08\WSnfq.8uX>A_;_Y&Y?+^kI2k$IqfJ8U3_MX!TeqL=m>KtOC2us.h1oV)6.A7
^g&0j3WO(:nId,acq3[_$nlCJ#"U1!o`&?VM,&Y$A<ONFUrmo6HjYjaB%.C;C-W>D2g%GWF7
gMAP0!All?/_kpK+AmWfP;(t8Ip!AK<F,ofe)SO[k=PGZR]AX<^5tc[3+W1+b4ubVn'VK'!r:
-qCt3ad]A-kXjZ1PYp]A&<^%P5bpi5Ej?)^5FlC^PQVam!nE.r[ZYYEH\dp2>^Id=e`(`[)%IU
gWTN;ceqIacH]A7<>ORXl=q"W*ELp/@8s)@W7l!]Ajqi0b>aS!/.k69;mj`lpZ+\\K"Bu=^g#\
f#EeAVEGG7#c8WAOo*Y3Ir1Ml.1r.a\B2/qt0:N'.*cf;UH:WWjb9&hcWP&Vi3rLg5Wm&&su
V(c)K3HRpKG]A8j-a^Kq#FYs]ACc#QMMM/;bd[e<7"a<iI*tl.ZaQ#W=ct#Grb;RWhX#7(OBX8
cQ/F'qt)p>n,qEnJHn!b%O$^MW(/NN<Gf\eU!ff&;j[d_Wkp2qm8Ak]A%2^>U21&`qRPOXgV>
"4q]A98t&m"Qcf/=&3.t^^2=X[NHO1u=?jY!bHe.QU3(G9^]AP>#oBV@0uk\/'X))^^(uOso.Z
&65m36pXXn,7XU)N\3m\j?^%N.PnktiqSq`pm^I-DZMAe3uZi'2i'?[6_;Jc6_#Qd%G3j&=e
g@<%dcXHp,"0R7PcL$+]A]A4HFb0]AS*Em,Cp\k?]ABR_]Ao?,Y_3F#V1&lmh/sB#"jt^aYBg(`EK
1G/A\ZJ++l/Sc$8q<%WFG=`(Y%D%7F[We->>^uF0r"VO,aN$8giRe#K_(K8HZ:=2;UFCWtCU
#sWn3J4QJpaj.9G15>g/;JOJWTAgS%!`<YAV<]Aa8[q;*!Eq%1Ta2fgbq<"=>lZ(ZCDK^2Re^
+)KWI..p[6aPTSc*->p/D\ZS,/7?2er4Mo_()[7F1JMmeS9_p$5.&s5\mQ/\g]A1hT\h8h&;$
.LV=4S1r4.Z.;R`C(*""R`TrmBQfJilKQl;(R]AltB+Z(S!q/rM$M#CmPpEWlpnYI='r?`iCr
dIL4=DVYCK)RaVetS&d*G:UXO;XWar]AgJ7%hM7!r5BPUPFhRoa1bo9E8t92PDLLASV9\KOam
,btbHVm$cERm\*YlZ,CVTJ4'4!!7EH1F"!la8$MQQT"q?o%&)/rT;!I;T`,gN?bFp/bC$(D@
WS=`4FXh"?8GP96chjF5jEYg1%3h.6&n!CdYCr$rTke"p[/t.d=[IZB6.0\^&,G6kEF69jLa
\p`X1B^oQ9l3;.m=S^Ur.FB9Rc:88l3nDmmUCB4%^j*tQYrf=c69S29orL[]A"aRN8Hfi>F,Q
5X65_h$qs/"Iui/cUD;=dfk4Yj`)Pb5PUU_oLIiB_Hau]AXj"1XB5hmn2O)4QK>b@G2#MATj%
p,A`D'.4J?OOIl%As,=IP[b/AFq>onqQW@:lp8`.?sOT&(C/M`Wr<0f/LL]Ape*CfH4W&Z4Q3
"jj!_u/haNtaK>K]AD<Nf9#;9[3%[(V5flP;]AMWhc1`E3+uS#scY1g%B@FmaVm@Of[$MZGjpG
95Ur^JW/r<TM*eCCY,IIo.9&ru-(@]AbXGtf6l;0m-:h*kBNA$`8AmkeLlZA.uk2=8-%%Oq.2
Ddm]Aa0tX7IBO=k!uK7YZ=_^5Y=KqZLtOo)&fhfSTn(N:nt:SjnK@2`qs`T@uA)IrS7uVHL$<
g8i81*(gfQ@+H5*e[;PgpAeVle`7]A(oO?[CJ<6Pc0"lsKII7<#Q?JTUDIl=A?0BnpE^R0JF+
jl87;m>*W'RU]A$O5LCN5cNZUDLee_3%oU6g]A?'c@(4tX%/KTN1]A68]Al2%C(Z"eC^M@hA!OkD
U@MYb4^dFH\Cdm+$:ncX%[\e.B%k8*93U_74f%kI.a=gKj9/&[BXZ.g:XZVVO)(1`HXFsLlM
^R#3o2XdI/(:WqSJIMeIGu<n2(:c"]A+;t@0!f1Tq#RHgS^tV245kc^V<Tn$Zrqtm!9Br`b>]A
Ao?b0`T<5(E]A#Om3r)aOFIR!(AHYqc%r`In!?gS)*knXK^q!!)[VJcBI&s,$!lR^.+hlj2G2
B7:b#h$qAj`(WSWXaC@(Y"0a7OZ8T4/X,.a3q:>%gQ=uEq#^<EMAIL>\t0TT?kWHPjr[XWV%
UoRIKdSIQD.8,f,/lPU/t2Z50Vm:Qp$,flEr]An'lN!t">gl9a3pcqKB)Kp,`l0df=nFWP<It
P;EuFpJQ^kYcp&<s@JDm8=\JpV07A5(=lni+8u5Zk<e_0<&itc3EdR-2pDB'3'8%n97DsE&M
_37I's2/fM/TtVLA"qC/4@?Vh4N<&X1S9_D5KDM.&@Qq$GaLl?5)\-1+[5YBrF?RU/?r[#kO
n^aIlVrEOXB.71p7$F&14+Z[/)]ANM1/``)Q#;=K]AEQdPd0&qV?YGNXlIP>*LGc/@@hE?jRf/
eLKH/EO?59J`Z>FW+A"DqP^r5EF2qiZ3m[kLY]AObG:U,YL&T#Pc>9LEg[2=F4R-Vj\965S(P
]A$R_s%%;:oAVg<Z<*a;@'D"h7kMOFtKutM^g!gUb@g9h@R#.h#Ln>FC'[.;k`(In@\:O%CDq
0Pa5l;N6kG0@dFqS7gFR-80Ea30;HH9+A?s/<b0o'g*AU"=_9BY-9h'0@mQ4m[M4p\iKQ0%r
#lZSB0=Stm<Le.N\]AA"%lt_bpZ;'r]A6ikAcQJgd6mjRjGWlG/s7(?TG^XG?h;S1^Ld<EcT$?
<iHQQ7s-*T#_bYd9Zq7:r\<0-?fs#MD0pWb5qJ@nUnJA1.)Ha%A&5V"EAA0(pR[RL(F5[6/,
L'3d!cQ(g1,pnB?nVO&\giKU<mH;J$)$927CH"Vn&bRK.(d-;:dI%)X3J0D`Tt%+9O(NV_QT
WI9QC$:!%>OcAe>?Vugr:loa;ZjJ69TiWSV9Cf'Z/84TKKi@@5N:MK4Ht>ljq!jCe`@63OC4
pKUKo:WNj[a^-TndA9's=n_S$EA3[Y>Ygd^0`n-_W:Q=9FIqrR4EfZJ7J^UOhU*qhbInPaR0
(Dj)D198H,ZH6EPHbHtO$K%cNO*(_iWB]AHPk+t#'k9UoApj9u*f%S%@)HS,8g3qE%8W,mq+h
*IE!4Q?=VBO0@$=gi#FpRbpk85kiL89^\>;?3o3sE^4i4Pq!,d-bBSL8?2^L(bFU;1?[aJ/:
$2K5ngYb.=]A>S]AS>ond5T$$iRa\]AU;.seEIWi]Aa5Fu9!A6'W+lCgn.agQ5p0-iM9VnG:IKe7
7U,pNIgB\cIDn$cZ04k/SK[*KO8A6jYMZM7_Wt@@R9,ZEo,!qKWc8h2S*3#l_,7-mXet=@2=
:7oCB@jtfm*^jPkk20m$jYHS&l%"IOd4P(*$GHM3rNVHkNP9qp<;a\D)SG;`V;%CA*`aF6qc
.4=uGYPLrX_qVMRf&j(M&s,q_M1F(?I+C!m*QE:_]Aa;oQjF?Flp#0*jHj1BK$IFR#)*`2_l:
`LD#kj0#B:t%EQEW#0(DX6Pr&(moi.ooKrP]A?NpQj.`Or=GUJ<1D6V#t,CqN&EOIRUI,VJf5
J62ej\m-C*07<Q<Bfj"EWN]A&E@<<W!Z1h1XAgZ!,?@)u01L3Taq6p3KQ*!r88N<.*$W1P`h^
D^m)T&=UPU./P?[03iedp"nZ9@'!Ul]A1abF+2E4CW!)LrB#)nm)sQ]AT?.3M7P<q5Y&^(2EiE
Z$nfqn*&C@6SRT/JS![r!?!ZD`L,rPQjaFgl,#OY6]A:4E4!R,i)Nf^!OZF_"\!s.oBD*jEi(
oWGtRP8N"=3,'lAC[0^gkl/;S0^X57o>"\G#)lTRd3<[&bo27`tY6f0sM)u:2!d[3aZ[7q$[
We/EPKa0ek/U0Ztp6U(Z`HlSb$m1kQMsF9@SI'"c/j&BsgaeQmnc]Ap,WrW=/?4E1sX(?dp$-
`sj=qTV)a$L8ZYuDQ6"EqKr#r[ja.0^kP9iE5>%;`b*;1<8IXN>aX<(a.R:ejafF5f@m'S'n
4t\Ierni?A(;HY-ArM8u@-Lr#.f8s'.%dDtJO&rhfY%0)q76)qu7@fU1;Ps(e%<[BeO,]ACu[
-##A*/O[S9,Qqs60nod;BBA>!Q]A\;p2O4"iH-!<MO-`.lE]Ad0>+.ddLb%-uu45FDXJj7=AJL
I*`A[/+9$;taDR=2=_]AT0t`j=?>"Ylru\_EdN4]A4_&n%@ssp%cA)!a,jHW4R#=pCeg)-O"_P
/&>>)98c%/qp"\DMl-Y]A\`=F,Q.2RtEIkGs?kB%o>c1l0<T8:Vpmk9W/d$!p_?^'qMB%\#k<
Q"JAP\.,JQVCfWl8p)oh]A_+&GgfeO*Sp]A)u((3Mr08"lmM#p5g#U/e;&cAB/_`&uGE8oR9*:
07[9,B&7MklajeL"f?J3,2SIk,Nt$255D"XY`fe6/-rJU$H#\0kdkY3nYh+$^S$[5.Hq*gbg
Y6cV(i*Qsl00=D5=9&uF;g;at8<U=c&M['k>qe:'iN(CND5^VI7K*NRPX@'eN2XX1V691]Abr
4q5bJlH:3.sKca_3@X;&/@LT[<UF?BODeWLeX>4gKZ47r<`pkUX35RmW-TX<nq:e1ZlK7CnK
WbP6^#Qd'4_iri`Ah6Ucl-?27\Y6>[2e_'FT$[Va/FoFdOr#UI&eAL@k0("s:`/207>-boI,
f&k[i.nMZPKRbaF1kF4X*N![;T@\D"q7b_r:-1(\"Uf*HBut';P<nm_oasc*4KK'Zp6&s".,
ci!+)F,>d/`l@Oo8"jRqom:\[b;rhW[@B-!TVV4$L:4Z>ADnfKNTThd<eG?Lkfh=C1rTU<3'
d[YNO&\kK:%Dm(@V0t_,r?^L2FJa8YZ>U:9\9CqsK%nP.-nAZ*qC$8E>-k>P\baE":_53(N*
d0T3*p5O>6MJL&G^o!#kJ;5^oN_1HmiTR8??AEd+33VmhOjMpj7)&I46R*Pl"!Vb3T<BU=ua
CL10esjiJ7RQCBEs]A!Xo,I0"p/WGC')^!NYIq"ndPsFuqHe&3(20Sp(g:'DB0Z4-k\e?k,Rj
8<S'QT8Nb*5ERhk_Z>l/Sg4iBaGa7X(2Cc[ASF_Jd"VBH5[^4I]AMFI\&pJg`Mn[(#gqKC-]A2
*?p?CQ85)up,i"k!Y,ZK\8tR<J0.hKO<fT*hMkKo8mMfC)i0<>1H^+`aF/F2O3rOfc`.2J8b
4AsoJ.[bY24'V8#uHX#eR^PM5M2>i2g/)`8]A7('Tg+\-9hjT)rB=#dK9&iS\lnUkp?:*fsi'
Xur;K38`?m+U&7)(!7=HK<A?Kl=Ba[S/dFlsZmafN<Lr:I3M6Y_cV)YA]Arid)VVF.SN/9q$#
G,G*g-Z"._T#@k%&-VN_Kp>X*%H>>`#39k(loe<7JtG#Xga$AK1@eLo%tYV/c6L.t&uAQg6r
G7.mGG"=JZ[I]AJP(3S]A%[KL3`-PB.;9LikrU/Y<Z2X)%LP`5[+q0mc%eYI;mX4@4)h:*FbJC
O!&VN19IH.RJ4EE7WL8FlKp+t&?.'Vu0^fBioQ)eBZO=]Atf+R3>SOgta!dIINAj2F^IPL>E!
OC'<N^_T$Y>o`_j!%*Y8tNRugtWD/&eW:q!$L1[eZYUu$<Q)i<l?!8jSe[c]A-hb;!oEKR(MK
mKdFWD']As/oQ\r#4PNkQpn]A_V8+7kf\K$5KL(*&%X83A0jaQ#F3=7Z(Y+]ARD\Bi;2O5,&LMV
,6kbC2NmQ^8lB22[/_WDcoMnl;%?7''f"A0p5AkREQ.P:%/_SoDRg5@C&BGS126V=Q9XDQ$4
5:=eiE%on4P<j4rJd;@MXVXi;h^UF1C6drg^]ApF*@`mU?gO;VFJ8u*<YO+"V%cBP&=MYLRKM
R,%@dF41[/E2"0H"IRC4V*G;R)H:-4UJI<<6]AB>X6M)]A'M9]A"q4nprcX3]A+2=ME(`!,.H@MB
R)>+*Q@IW%AqE?rHp"TK;Tm:tQV%4?F)+GLemIppj??#jaZ9L@M2j@I<>La5iHC\H;'cG[@m
*SWc3;]A9MQ*o]A8:Q9']A1anp=TR4e=<<5p6R$#-Tmd')3$"XYuC8ak40RN\<1]AP-ZhrF6;E;B
k?gA>L\e#%=!3G?=<5?qsQ]AidJig(.d&[]AUf^PAWW=VW2d45g#C:5o?m!qW`.,^A/]A*7MZ>5
Khh>1RglDqVG`V=]A(B.X(LT-\;fK'/B&^bi0"eU,WLN0-3clt#YEID*85%B?>oHM6(7rZ8Kp
kS*`>J7X<!<t5M2p"1*XiD%JD4ok_KJ)JB2<XE4%989N&IU8SL1=pFk/,k8r5,H?Mt]A5>-aT
b^tcM5Bm-Y0iT%i<Ut)LaSM#Gge,D^j`0_$i\S`=n;h*=aQp.!bDQo@T1GQ:\ZTUPb(.Q_fD
XSH!4RU&nn2D=CEMB6EImf239ld!VkFW`Rg[.@q"B6KkY&<s?Qis"U\h[0*,GP95a,kDp*\i
k_Ghotr=BB`Zi='fVI^u1ZqOCmC%H\$t(*!'l>gh+f\Ig0^(cgOt7c'jrfU/N7Y-Bm=On\0-
ZX[sj[<Kj>$FK%pW5,P.j&^tOGtV(4P)24]A129H[%18&Tc&%nB,p?649=]AYe,,d[L^oGciGf
d58o)4[?KJ/SSj=S:hAPa,mE<0$]AV,34RH%C">CLp?$JXfI*_]AW1#I:6X%"Xb&ZjJ[f+@c]A%
>*1ns&oo&"+,Z(gmBHP6tn;PtPVo-bs=?Yo-f>GjA(uZ8&9GC?V.!9G^CZ\Z9<b64&.mEde#
2i^_`Ukpp"\c@3#<*'6%E)2,B4AoR88?^(b]Ae^)@<_CL2`^\1`lWMI\JV8pE$['1%KX!dP?E
Rl5Ir2IDa'Q;[7NQ0;/pjW^G8eb4HXm,@d5DAC1)<V+a$@"OEH2k8O:$mgAs_+jl+3O<I*$h
10p4bmAJ=lMXfGij(<$rBjj1.-B$R#W4u^-,6oM!o3ViO%I4XCg*4-&JMgY8.3?Zk2(+_om1
<'TO;ekCN"q()qUAZ!)9#/3c0'nrnWe2*T+D"6[M=eYX@.s!]AdH7U.f8Va>7+U9P_36Zddh^
MX;S"\J+IFWl^\TqZa.T1GO]ACiNm.gm1cCM+PbOKuMW2[pE=,L\(D:ci11g%nU9Y>KF+7dr\
`c46V.tq$<:&<Qic@DgcH1egOkJX58dX-;74'7pL]ArLtK+!a.(B4[2_8JT:Wcm[-eEIkXTbl
/[PJXHE&G_9^`ZFg-TDR\sNGfBJ!^7@nH;ZMOVVs&s5<.%("aV5`RBQ!HLG.\b#Mo%R:'olU
)ZkO`Ge&'8$c!bq)c%3%b4"F_%Tp5M,6Up/[+8n2H"dlYX]A\%K+c`Q7G8[EISStR"T]A?E,9B
VtgEQA%MQa5GspWJo[NQ[#'(BfK6(,QjI<%S+DUI_0!-TX<U@)nYUE<;/*>'j.@;t=K:)g&e
88tbF%N$a^bdWisa&!*Q.a?**]A2Ag(3@j-q#a&TXtN!!M'CGZP"K,=\@6V4MG,A!-,o0iu3_
LP:Ss4qZUdAm;Z<Yq7A?Ljhi#"XN8d9raM3]A8<lq&Xi<?)I/n#mL_S5aH"ifp9F>`i-id6cl
oY_:8=,:@V>B2L16rH8u+a;>B4:$-Pi(\h/"1]AI4Z.9^Sr&P8lIA$80[+RU,uX!7`5"1B9h`
Fr*\O8:X2FeZ'OB0s$EJeY>m_&$[UGJnAK%dD9)R4s[pq$uUc7!5qMi9H9%+m]Ah6(e:TpCZY
2@.pi>']Aa"cb<`lhp;hcO&KJ't=5+l@"<h[.Jc;l%'ddEU2h%@;!Nd,?F7b$3d",G`G'E^un
Eb#"k4Z,i+is7f-[==nDn;^u#bX,`CE[3ft?$V`*d6Zn'=ltu,qY[*I)9;>R72.S%MoIag.-
!^ota6u,5;64^5fgT1lXSqO]AaYdt-ghBO>rk-SM9FMF<X6=m,KP;5\R3ZTZm)iQf&X&eOJF>
)`q/:H[c)`"IUb^F2m-0p7H*q,P<r(N#/N@B<)QB;!eC62LcM*`Y7TIDa7n\E8&F7`qM2@G:
b>UZDK?qhH2*s9NW4D-)[k+aV7ce/Mn9_[QVT^(oQ-%/)[K0Eu4C,jKZ*T#6<DnD+Q!kMtp4
0G642^sY3Oj=Y=ksVJ3Bub[Z2ERE^DV.["U6mtmX1#iW;G$;mI^a>)%I@6No7Xm;"J,P1n;:
_!B%O;f@4N4:o;"$]AL4=DjQr/5fs6>)F\e`X-GrYM1cAa8<o$%Lg\DN#!uPX,,(?Q0pni4_=
G^np4QuMkp>?'rn)_+U&LdkVBE&S-!ZhMkg%ANoJ*TkdLW#&96E&Qilk^6-J-XW@4.'B6frW
"Z88,KO>\saeS't%'GN+]ARH<#'`02,!eNpVNof[a.,L)T0BIj4eQ%[_V]A#r4nb8L^>;MlWPj
@\(Qa#_YESNPnBk!750@Tg[IO`=s=KV0b7Yn5qJ=Y:FY:mOVeoIrm%]A;4)&c@4MBIS']AjXCi
S]AkK\QK2(@>9oN_[+-VEYB)lVS0R"H/%mh2JPFO9(u!qS(Dk8KaIVfUgX`U[)4-Lgt_*Ta-r
!7_i=67A?]A=MUM."@246bb)t8kSkXqr8lb'co#1-D.^MI73.$ARGe8_rn[?K73'6$TO%%$Vq
okWC3)li*KSI9]AmL-Pai-F78g'8m:N)E=GLB#TETRcgpT,e1;^^:JM!9q6WK=3BjL*hor[V#
P##ZBKch.2'+_6<TC!4cS*@F=G[FGVO[%##:*e6`HYBZ,I.L/#$moAcHVbUKEJeCCCX-@qd8
P^WQk&\KVI).=_C:*-9k/17DRG1=_0gO-`PNiX:<*7Bu!aqqOWT72#9]A;/)X=]A5H`7F[[FEm
$j"GEk7T1mF4.D/IWAU8XeQ7j^Z&<K3S#<\BZ2>G/O`D,q(rK'$aqBO*+kPfk\RPU/Jm9L`8
kf?!G)Wc,@nJsg+k[3"FKMY#0ANFf%&Le2h-0P<b#+S6daTeQo:<5+^P6"j=L7TAX11GYa+L
QgH54:d<u/IWKe/7.Tb.7EiX\o_iOQt2QUQ%`9AF.8'jhS\,X*i9L/h^a6Ckma]AT':i;t)hP
o>7mIPg<T'8,A3rs3LHP)UiM<6,*YRqPbnFU[/1asHSR[$9*I(,$#;\M%iV+0p\B4l?&UUpD
@EgegpY++F)hR)3fm!CKm^=ho!r(D-IqSZb+qmFr!h!`rU4h3BQsVY+QLN?aO*$$/eMO%NE.
`k3F2_`(Yco@PV&!/hga2HG+'!G&<Q-EK?#T_sW7OB4rC(^GIWUo0qdm-a)KK$ZO:<EMAIL>
rWP9qH4flIP^f1AQ[D]Ao&*pmjK<[[1F^,QsM.L7fDouWlSBDg]Ac'-5PPUIL#K`@lPF^DQo#%
_DK1gI9&D`f6Yd>0pIC,e+_tiKTY(=Z:U=K%^9j0Im[Ymh`]AaTH%<''pCb)&-2hRT7j'sT/C
g>T9_o23$'^]ANBqj-GPqKS\rH4:PL&=YgbUkfk8<c>Pdi7?8[L!&!<=Hd!<DWg+)#dY7k6qg
4"Ec]AGAAT#&5>u\'^7MSi.^P"B=BQ$.4Mn#A?Z>B$-7hf?UPOFgUIonS_3+5OQZ/)o1j;bhb
HDkZ]A@;:?8&lj#<AkdVE6(Ld(iL$/p]Ad]AC*LC+T?fj?&k;BtM7d=K!NM68Ttp,Iqdg`Bl#?W
P!2.7!Yb306nuTX\g1]A\N<UfU+\pa6@+?4:!qJ/AAl'21G:OIh6a)uEc<7EdOpI&P]ARU(P8U
]AJ4oEM+\dR%uo>Ik(di59(iJfFbRF;<VYf+RUlB<pROA1g^dT)7R=(TTJtQUW:tsot(U=>#5
hHHX<2]A^!9/Yd78)iB2;oPOi@$VB$J?"Z/lNUfr3!i(opsX(.-+X<:ns;ZNq<c'/X3%8#-`S
`Jge>BJ!qFe##hNhu/ro;mBa.F[VL:6Ji1l:Wm#6e,\J:q:O1T\,+qc(<]AiJM.mm^"?7@;;)
q!Z]A0:'L!TOWle'poXUpK,??S.fj5QtI7RBP\a0LhGEgjeF5Y$$p[=PDI[8G`c1ndBE.TW(i
fTHeetfr=/dO]AHNP:R^BKS6&+CEoXqZor9]AdZekcu]AGWFa7/o#?+S?&U""V`Eee[a$ISnYL8
1:c,JR8tZQ\+qk]AU;!/Pd^?X]ATAne>BI-6M/-P`mD0P`==bJJ_]AG/f,-TTF8!SPQn*p,X?k4
Zi=E\!$,U6s,CJo7G[5KO+QZF[B!R8:+#'P-VhgkZ"2?h"[=<DnoHqZdVNe!FA&\o%hQmjBt
h-o=rABS$eMjAbK_,M5&$2SGq#9EJ1Ft<Hm-P*(j^qrl(A6,l0!HepI?*eSsT5#'X]AaPlpI`
\V[C>DiBE8*dmRrDube4gS9ABmm>;L7MAS^f5<V@Lm@+ENbp?'[d(Fa/=\[H@rT[Q-teL$4@
2-7W0391PQ7rnTE%U>9-,T%HI1CU3.Sk9qDl-f,ms;nPU;nbnZ_q!BuIF-rdLla3a2l;']Ada
-e<[nTYPc5EbIp?>(0^W[nhLK\O]A]A@,LP*H(;JHpk&0XTfd+>qU]AA(@)cQ30k"D>hWkSsWEY
BdhQ,:K6O6$7&#*-BbQl:,6j(G_'*C,&@;*p#Goa%=C.Z%7Q&_Sah`>6Fc0AXN;=t6L64k0=
MOG]AtK`^@2^Y/jh*VfGOD'K*5pqF5o)]A_LJVnmmr7BOD5kb!.>0q>0IVQN[0FZ3!8TgmFGRg
Q:W8u</,5o@@bK/&&dYB_a`n\*?MO;4it1S`np#?`0m>#"%ekq.UWGjP!C0[<kRkP#S=.5mr
We#3jQR$7o=cnk3RqQXC+Ng+!G5\MD?*#;I#\;j!/H5pV$"i:BR;+!G'h:Cl03HKfn@pbLb=
Il/XEmd<q=3lps[D/rd(fV.cPcC!q/Ud3toEcp*21/e@IK'A]A:((SRgKEat)EO<.A3f/6DD;
05Duo!lTEtI`qm%(.jbM`;kf@-*W[lgn?SMfHj[0\5<U`Q?c_AsZdk#h<SX(@>.*48lgKa$a
H6D&JqK$tX8&+*%MDUZ2-QDJ>,F#=?=*@)d99#!`s2jB\hN.g0'GJY?R)@:t2d//aIFr+$JL
aMBXp9.UF^G.q)s[06601P!S+q+l:'?=J#=fH>e,/dlJLF5*'[5"XD0?/T#,^YVX%[5.AO4C
g$kj(!`>!.@BD&F*(EK!Y`<@2H9=jrEHkk?s't(TPc]A9h7@LHpcKd_8t<0`_f]AC(X1qe'8i3
=SDsqRa-HKgfX^?'.+rRF`X(S(uqmfP?-N99HmUM1B&2'?p(5G]AAXTG$1Q"7S*$K8*ch<U;h
@TaYSg"F21(?$Jp63:n!\RIHZd)RE=7U.PtHk&DLqBML^<V&,F#/l7^`ajp.FD1s`%G7U_Mq
,XYMB#<UHGWU]A=NmPC_%4k&2D&:t[I5S_0*rLhXR`kelHBZ^cfY*2s&fThsofh]AD<>2PA5#B
BbE>&8&b(i&*:flsfd>6nCWh!0''+.R(BnkDL>oo?@glh9?r-&=K^D^j:6e3YSj*O%R`IW02
Or3TXW`fP!r=JBS4W(E^`otJm/ilW2\Rn3@Wd0t32()/5VaM&q.<F),P%XopFDbie\L5gHpD
$;l<)FuUL7%!6sK\p^.Q2*rRgUQ(;Ci"aLN?(r#`&H8Mk_A1>$ugt!MMj;mX9tC4rjDqjXm>
D67X+=a?sC4hY\k0ijhKBX;TMWIFLSu$(@"ge%=Hb*GP?46M;../D^^g8-.8[Do3fQ26OnH(
Il8*]AgP!GeH5YBTfKAFr_/S%s+8H!@O[qZ5`P0n%<.$=p]AX"sO7sI#_BZLD`km6FY*%NtjJg
j3k"1)^7hB(f+^pl@Xg7(NiE7D"Kn-fY"1n5jcF,ULP2NU(^8A?M`rA^OA@e:;GS$SCZ-6J5
D0mZiqS0[qk_>$m181qafdi(V^EV8J/+SW*]A;BJi3:EMAF]Ap?gcLR?p0',Bnq2#i)V3?YPqO
U"N1rKp!M6WR#5"!>"a(EKnD]AcfEiqKN8OW-EeeYI[n&^;M"+p$QT/j%b3:D]A`FrME;N/*-m
he#U0m_6g*R1ZNZnC*St+QQ5UOAju[FXLE"!.\9gUl=80XB@Mpr[rHT'57'Q.`R1oE0d8A$X
)N*?hS?-k8mgmJT>Y4D6I1gao"fRI!FZR$GUn)=>Q[TmqLc)fR[i2Td?M4mCL8=6@'f>-soN
YH&D<mW#?n1elWaS-BR^OI(e(23Eda`$ZFi)>`Z_Vsp'X\JpoD)j;dU)Q[mRt@*WHq_:)M)K
p)PM6kL`5BB?o5A1r*>HlDBUKB*dhPN9+\<b+:@MpXdLAEM3U=bN;QQWC!@6[gm,LA0`G,?i
1baGO@h31E#6Kj'S;`1E*o7!G8kFu$G?\,dN^JPX5O\8mt(7CIa(:*/,NABSnT9Gp?26[^;o
uHT>tluVL42]A729WZ=I7rdq\\pH]A_tlMA)Q&ZjEhJGpj=3NrRZ`Crb+6jp;Ll_heM6?%W7:_
lbTbd&CZ$JUkV5U"3?9*.l>r'7PE,ZCo%3PGgO/q@SM@jRHO^3j)-Xld*c3<(Rjr.+d`(PHu
2LX?Du9Qm>Ln[HUhacNs3c[ofO#PbV;`L'm"C2__O3&VHSo=kr!Sp[5.6nXLm&4=GBESKW(%
TW7nr6oB]ABPLLG_?NY]A6`'[`jLU+%3%>VCV8HE8fJ"4WTUc,%Pd,.a7b.ELH3fK=O[O=C=)&
Z5ih^d3-GMD6UkTL@BAQ_M@5<SsKWi;R)`L%B)c&!D5Fg(#C>Z$t!@Z/TY(I(KUg&M"HXi\f
_7Rl=l2Y,*hDj5t$nhbR20^LXUo0[`SBW@0QEP^@rnQn>B"X6'f58q*.TX$rM70H8Z[=-cf2
%']AV-Ms`D?3JC5`SW@jA@La/q.`JDF]A>Y7$e/;9gl_;.O#)IR8p3F*H\7;9/]AN2GZeW1<N2e
V&9qAW)%Ops?a/,I=VN1a);NUZ<BI02\,ZSJ)lJUHRJ8/YR[%Cr_P/7[9XQrFk(4oJ!#;ZtB
bg:`o<Gu2cF(tqgjH*XIqEq%M"BZ`I?Q-sPUA$rd5<LL7,NPW]AgZ;Om!Z^b6nNPYl5.pVQ.,
O;"[*gb!YR;-+W;K`M*Wkq)pTE]A&##ZI^k!:(?FkG[8(j"/->[i>>qd;!(2Z9Dbm=qsP,<5-
ElKj)=V"+$,T_ML=UfY47eM@As_Uo2Kho*erP@4'mL`CB"/NkN/*huZdWHiHge/A$'*R=:!"
IM'23U98QSP6eng?IEoH7T6CR*,Oi8h-bM8h9X^8d[48(AAu(;3PLp@]A,9G<as@c?<orYqXP
LcV8"&N2be2<^k,ge49jSN_G,JAQhq9)f5Bej7/-A\';>5JaB2989:7fQQ0S1]Ak+E[K!Vq"o
e&LYh9GBhuB_G+4S7B):70#IdLVn\'H40#s-jP_5VZ?_53;nHq`hnlD1S=ko/H-0GVD(+Oj'
J<5?i-Pfq2<(Y*>4+4N/$9^jFOm:6f:Ei76%+2HT=k1V.aijj_40GhLG0iU_>O3D<@OQ;IE2
M//SLmmn/IQ"O@4VNH<(i$F[*5i3O&5k=^0FYf0gXe8C6oE;!@u=>o8nRl?hUXS,gnr841`3
&g/3VaNEZ<&FH8g6ce^^0ksGNUL"3YI)Mkgp>_U-]A4\5*;=IJ!'OZ1]Ao*Y7UX>2K\?ACTNrW
J7YC=e4oc&@"F3k^RCBY=TTL$h89StX$DBqCQ9JI'`EE\ro:Knh%tWbg4RP?iDg7_AG]AI`@D
MJLc#A=uo#FCKV!:KT$iu"1nUii817cI1c+el,'RS/E7tski-h;KSs`Rr4?(N/2jUsG[[c!<
Ao/P1!-C_2&8F&7(T3'GaOX(/Li-G\TLCt-s3scrC6tDQ)Xjmpt2J[ofN0tUR%H^A!Km;Ti$
iIJc7qbU:ti6QMK:A26tYJ@(]AN=Ksn5^Z!P4K)OkXD-OU3jk]ASDdiUP4:Dr0:5:ai=dY(aQp
(7\-C%!T'eE%Lh><Q$bP.8h'ik8sS$3/X?RD0FQk.gT9lf4RFH:[(fscVYK`(oO'9j5DFtoJ
0q3%L^VZ0nuN9#?hc9'[`#D?=tS4-%pWMcE*B!-leqaW4Bu<r]AAnsa#1.V8BQ,Ad9bn8'+3$
IW14edg05KZ(Njk6]APN[*6ZoaCqkC[H.fa+%FAK$V,%]A'l0M;Q)`(OJEQ35+WT3u\`F"n\l6
s33k?)b,S<2)"T:Of&:8+Xu//(&G[mV&$A0L"Y')kJs$kpmN=(>B;MLD%3:LUn.[o3cK<="V
gjO,*l:/bVP>`n>3b7iecM^Qa"E.gJ:\b&/`Kk%RqQ,MfAd@?iL4L]AB+Ld*=FA#4^SDT]Ad^S
".&#p:KLfs>SBHJK&8;tJ_H(ZKW;!d[8,?=3u;sd</t:^L:@C=5?$S[l94>_e4P[)$S#oY16
XRQ$C9DM1K1!SVlW.:Nnj-0A+=[ahk"/o7MpRrXr:eA;t&4q,?u'Q-I]ATrRJ!5ChLSCc^1;b
-^)WOT,1#\d2Wl2e2Ot"O?i0tp=H15cZn,9SRU8HO4-2q'iN)Ctg>U$B>YSL6&#l-P*'MpH4
5:m6WjG4:R+oKa#kNb++^9a@@#I?_`oc7Sj-[n^E;`NqL!^b>0<`)`QYnCC]AO3]AeGWqnBg"N
K_4Us`kEa3rh$gsA<2/jMnEgPW.8HS!ke2*[?rlo:ZBH%]AkK-V'iD:%!q4mj;\2R7Z[S:JRm
,!be"d$e\oSk0H)Fieu_\m4H0+bG;)W/4tP&LLQq_s&5@&5h!%01j.oZ2h>b3#</A`c*[Q]A"
Ld.iFN]As7%rC:K,)!r>EL754ChBkAC&qj;U?':O?C^%U]A+Ab2*%/q9iD^'B=Z([S1E"H.$0!
S<*='=NP\#.Oib*h8Ypr@+_asJXO)bJ9jUcKm7`bKP-;s80qES[fEh9=bm]Ai*>dUN\(JUWVl
$2!u0icY[Iok?GTa,kIT4s.P2YAYr*ke"$n-E#`i$_,GF2d?N<6V@>2a8S^l.n(/jB6Xld/h
<,rs_s(phhS*g$"EVQ[YBd!_[-S1]A.U)'Tb)SN?"'0b]A<3L9QL(4ce2<aDABP@4:4J/]A@$*0
X]Ae&,^ej^Ydm:XY9QFfp32kjV9R@M3,seA.hU('%_COmI4fFkGGj`lf;-*p#!C:1\9W\5>/6
2*G`[ccNj9-EZ&I%K6_:>5U!oRd^U0bMgHBW@*aO#MNI[>e^^n98?!_Lu@#e0*785.C"b[$!
4AQ87PX"AH<6U$eIR3PA&0kM.%n/rR2#Pl1X_UR*cX.8Ci,b(03=gbp8305,`IBfa5^^>7nf
-qjqF?%UJ$Or$`9bRCQlOB`ej_&EFIP;[o:D,kdN\ChQBE.Kd8oi#o"&YBW<("'g)Gd;&ArN
D^*@"TPTLk<q/i?r0i1TM.@8SnJT.d-GV7q:MBqEEiYNWeJUu\*;,C'aVZ/um3JSs5jk0sPh
`Uc:'a0NMP4$+Q!#E\7'X'o.H?'-.q6tXGcEq[gma7I>cI0m[*$ikY"oA>Y\i8^%7idf9jiB
J/[\>G!kLuV3u;A0k_p;Tj`>\2!(.gu2c:d(Ijr#H[C,n!s"%[&*iBa]ACJ660'/X\JLd_G6l
Hm&)KP0b&BgZ@j"&"b;UQ^n78fOnjI7Jnpqn:dFdG)ZiIpbR@c)#=uB+YuAQs3nU3I?H_r)q
gX5,VLgLW1NqcT_.JhkjYeQ;gc/Ru5;aE(Ra8[ah4=Cs4EJ=1+udZ:$UTMm?,/ETgG?(cZSV
HlO6d:6>b/;E@k:,gN._h8M]AJjs!T?[Bh3bI-^^57A_W"'5a^rh5JJ"7R%8X<l=ca<Y(D1H;
p"2=`I6#'S$o#n:.4.=8TRiiNdBWn18^tf`"Wo%e/@&iOJq,#s*mINq;O]ANiKA/J#/h#@$nR
$XN8Ye*t5hh]AaJ>>)aif,K)`2hZK9>o0-%R&_AaH"*tPFmQ0K@PVup<Iut6d"a!'1e!ji$iT
kkmK5N\m5L]A8!+b"A;ab*3]AScUqm<]AHi]Acm5X-16GSHD?c(/Nm\JRUh6*!uVKhKXVE+b2CuH
=Ws]A-j):DYUJKGaSUF[9HM%mI34$H#3l1rX(+n5f5=]A.62Vmlj[7$Y\1lbN@:FKY=:)_.^a8
D?oV=n`d<AS03Qgcgd$0>jY6q:_0N1WMa@L5#YGYHABsPS)JJX%TVQouHcu>O3-U_:J4jI0W
L;meWP#5X`F<`gZ/!)U'2Nip5:37Q+e;qkK@rQMXT3m;JeB>8?&nk^1[?n!ol-Xf5a(;>-fP
JY0h"Dl=W=?l+MW]Alai%G>B.DKa#gJp+7.K:Ep%MLp,Z&&WKnq%>Yr)qA^RHYl0LPBBYGMF"
/A6^7&9e=mYVEA'!K!nqMaTNMX0f0PN2_N+h^f#d^)Yq1Yn!PN&8%5X*6$O9Gn(Ke<@c(\I.
K>cdiq\Ep"7#Y%YgCDHi$hO?Z)T^a-hsea4@_EoG?M[lKf0:ES^\4d5J@o_T,CF$bGdMP[[(
'4b`\.$pqWUjO04b9_W^o""Y=A$a7eKK0A=r1KKGE>fPpADl-L6u2.@L.P9s\!C<*k-3fTY7
9/W`m`!4p5;O3;5#rf]A&Y'A<R^-;*<:,:_2269M0=_-JO63m`BR-WV`h:mZm=[m$gc98ARiC
(s`W=CQ2pp:;d'Lk#>Vf*G'Y5g=T=(i6tGOJ_Gq(dC&Sef8]AA`$s+b]A`Dp&ME;ZJVeh5#"D,
*PDcrl_@do`5_<Y?-[/2\f5`ZN1SW+6cG1tYa:sEgAu@<9TODFb$U%20Q&F=niNQ)Pp]A"[jX
b5VJ]A(JoeYQP=H8:Y*!i/lm"_%S*(]ADLM]Ad,5Z^\0SlpGFp"o2)p3k*GcSJ#5J^"hX>m-n!n
IbeuehiX]AoGiXobJHGHY?==jL9PHV\^`^NkanpPm`gnejn>p*6#F9)L/Cs.\iLn6=u&!LjUA
OVHufqjD=K7#&V<4(W%-9U0J9+0;Q`-^<,8b\eA=%L+2>85Ibq5L/_fl"Mb1J[[)_h6&e&;$
<W1M87[lE90_u4%`p1Gf_D?%o!+";Kl3;F7p4Afo*jN<jgrgMrVBal!cR-d@]Aa3JTYg'`j'u
.0N-O7Ki6m]A:7apRP1@A$#>%U*dNhZ>kJ10M+:apX"Y#1Y<Z7,?o%:>U6`L\ABO(?lh.7<*-
_FZ/h#6)nE!??/rqm4A/:WEfS)()<jE\:o8!Q<"-5'C1JjTpU%lZOsG&UjX!UraH1,lZDOnV
pE2\6<H5"e`s*ld-e"q-.=llb'%:GXS4)G[\F:8=oqCYt\1\`4T730F)nOQuqL)j7SB+['?5
p^M"&Zs;O&]AHN%M?V>Y+lQL_7UZ>2TK-L%;r6RT&2)WM%/C\Ou?tWY*X/_5W_G"e&h9\1Hih
rMB&X*Wt4@+X]Aad!'R^n"#.QWD!a&D%@9!Im#h0aP5n=RhmYOYi\fcVE=`TBp5?H3'!U*F<p
;j,W6jZ"%3P5/HU=oa81h>frC9:-#^HEk5W))>\bD;U'r9^<@s(2XZFK0ps*0g,KG]ARMh4e;
<9M16`XKS:XEUqi-P@$>T1/#]A[U%fFZR#&a)R)1Hi\pX;Y15^H1*QJo)6KC`NiD'^#:i0!Kb
.seiIZoGn/g>@?JKa*(<3UB?/.:KN!,fpdaS2h=&67VKT_=Tl8Pu/dl!4?D""'(LFLpOl[t=
bE(FC/p*8Em^T8Tq'fIH)gN`5>;MC!Ekgh?lG58m_;91KP&_JbJF8?8oIII)_2O/_[\EM:H7
9l90ler##`aFdrWP26PC`;?H[#c)T]A\\qdDAacBA=VmdTc2F"SonXH9oLgoW[*.OJ4I:"RQ*
/Y%d`UP+6*#a.XCS:rK4$qP8.?#CUQq$nAsPkr]As!1.qFHJ&+`%HGt:_@(!b&ju`.uQLW'A0
cW0X^RrT[Is-F"E`ruYRb)=e/Bhh+/^o6arTA%\Y"7DCV\_A(No0JMFKOA'[h#(!SXkn00Tp
Y&IZsYA[F_)G-]AjbkAcK+V2H4oeQlqBNIspd:@eJ0i?%83!p=+5/e<RNJoV]A_R7W=.,2FYqB
`3<4=2PIA=>D7U.fqU6U3,c#e[J:D'*Jf5LXt&"`<W.nNng!]Af%:lL*gmf>Us*7BGl`XS8'2
'R#bCApbV/!_"lU+$++sfTC,5R@d^OH+VcGPbB.ef\ri_=tjEI18e*N,q,b5RFrGiLMWSf-"
';2:UlZaTMpjF>*YdVY-g;gC*Dm2cl97M!OfA?f<d>9tKX*`#7;`Ckpf\lcs53M+Lmfq-#>o
]A8I'U&kj+To<,u(CcCU6'a)kn9aTTVSVh4X,aKgkI[3kLTLCr(V,TdoWiN3+&O+Y7X#W]A>0e
'.g\OJ5)V1DInB&II`XhMq\@,'UFG'8hh=L!h5pdbkqFDDnpJ?k?d`V;D$o[nA[q,'1"!=\s
."*ANO0QgBjQ_;Z54B`)[F8IC'*3C26:K7:Ks!$jqg<?)VR.i2"a_0T]A*AA%h$.5<W-D8`4A
(bk1?8LrXQ[hiI]AYQY%ojpMV<9SZ8H#PX9SbIYVqN:6W,W6*@OPQ2#sD^kr-@djra@QlOdWn
PW,YV9P1KU#_HUP'e./uE[cJ^.k']A[+V/k!qHNBdTksN&?UK=b_O4$U[,$]Ah2#f)0ZR;MkL-
omm*V(TBmk2YG`)W:W<cT7kt*#OsW]AKW:Y*#1(bMB7g9niVE96l!MtJ6^.A4tE!]A_YGY%gtk
n:Jj!G,gD16io5MCkG1kYl>Wt`ID,FjUq4^?hgbM@h@dO!4GN3Z>*=q(29b7Y-etWah5-/[b
gPjU'3:8MVOGAu>IJ1Fdg4:.:A!R8e+iRFLMJ2]Am,&>dc$@6^b4"I.;eZ:J5(hJ5SUVtV$bB
[Vu*8@f79d16*f8/rKnl#A!.9gDD:LmROOf$V0Oa*#A6Tqb@3<]A53NG!L@no"@E,Wpoenl'F
nq!(-RQ0(@"G@:sjO0S";FhHO)B$U+i]A(^3Zm@Oso@seStdL`aSV:IMb5#,UmY(54FKC(-`d
VV4mU,@9A;79B&)lq;g8-O)r@A=cV?R3]A/Kb&']Ai=b/1/ebLe>I)aF*Cq]Ah@CGc2+Y#>rD\%
Mrgj#i!c]A>od!CL0+Hf)dE2FNfO#nTTnj(_[PE3-%I(t'__0@&Toq&U='\(BPIbGML@8DmQ$
h$VRA]AQr7EJ^\77O<cj*=7i`c`]AT[:m_fP^XjN<nlbgHRTp7`1ha(8EXZ]A'bo,+cjZXaB7@r
_Cq[kEB5)W;`Xgr$PbLH=pKlh7C[*Ajm52-.eWp<>I?o7F/NbY&l=C]APHGOEZcGgfL$1fLn5
"DfQd6MiLg1Z[Y)^4<*TA9<[fkLAbGgHtn1pKk^^T+`3eB#?tV8P+TVOj>^6p?WM8EL42R;'
E(V:>jVUtJ)f&.VGq-Zk[Su;IMi;Ll%PKU?:HN_Zh<)H`T?e458<#=d^/38hIDC.$^Nnrog[
NJ>;\Ge<fWWP(MU"YdKRH")?HuiW`\&RhBE,dm9NMq4=[nQ]AjC)6l;dAOZn7CITOJs3WhqJZ
ncqkuob!.'_8T`"j-"dVEBTK.ah`(2=Ept,3o0701*![=.l\Vrg-J;G7^b#Yl7c'nG:NMcK8
#F>.[!MXC,B;AD!TkgO#`#dq8@A7Jj\ilc[2@%i@?Knd()=t-isdg6S?69R)Colq>iK$odRg
K]A:3(R$RdJt&-dkjYhuh>8u-(4r&VbM[ZNl55__\O:!U1@#JknSP9J=6>k;*-J8+TA'4%Knf
:Yf"aa4QXO_9Z>%Iii(lHFmaXdn;GZ>Rg[ID'gP2WOF"eM\>>#V0,r4iYd(Bo4NS+1rIt):a
2I-VN)3."G+4[]AAse=6p?^8^&2Zj[9#C\!JhOMk,#k#4ImnqWoO,5]A?Dq,Z)^mIZUhgY3a'F
D4Q;uUsM<VT[>jW0=&Xt6@s)GM]AUn_,=E9%,_4ifTjkRE>UG3kMn=M5+X6$[Bt/K4Tscq/0Q
`Gs[WV*H[lrsnp;5:X0_p6#kQMVWgKub;`?hHPbi^Lrq8*O@c6]A8P`kWK<fnrH1mE<V3)hN3
SIgVsG:>?,]A:%UL211Rg'L*b9e!k.k+cZbui(Ws=1@2`,&f!0rC<F1p;Ipm?L^O6uc#?ucjY
J/GjCXQmJ,$b<uWW=t-_KSdMc,*qI)Y5??$7ie?AUip51qZ\J]A]Abua$kZY.`hir$TisqnVP?
N!MldAf<\G[Ee20h#8!b:o'lp?$cim^`?[EWYn.uZe)na&q#WkT*a=V<f]A);DGdE0FTp_.FZ
/pYnV$%EHl4W'4D/r>hQL3:U7:4Rb)lQhDW@uP)OPF?<OltX$Oc9UZkg$,l=Ghqs4FTYkQ;8
!'5n*d4$meb\=4?&SJq0?>)X;I+8UJtCUV74<?RW#S#CjTS#lBuY]AP.jBapY\!Po&S=*$5-5
Am=,Ro^Wm8a*W3:\GJWbEnU$-`ZO3,5;M:9Q.n&h@OP1>n=D6mN\hUjmh/^agLW(QA.NbsdR
'#'pj]A+o4%B&c:OBln-GBuZ)]AocrSnj&h\]A01q<F-<.>1cM.ZVK87'F![O8QV2';/c$P2]A4p
-KA>oi!nBSCRdk(so2fl?O*)D)`/e^!BTh[/A:s?n=#';<[^f$"T:4%sd,jAWB+`o"oWDWT#
;DH=dMSWqn<r<i/[Yij=S#<Q+$(CO>76fm.U`/:enHE^p'@:(h;1+Ok!I\uqV+N[FUh6j]A\5
qg-)bUhnYVKrD))SWlC'Ho^eo)M[HUY?-@e$gE)T-]AR^YPHLmg<9MP\kBn=2^o1mre)C+V4V
3(uU[F2SJ:sf[71$BkaiN,E/g.2Wl%^mqoefd=8Jkrm92+qYFcl&$ibCQNE_1TApoFK@jK2-
't!+4rnb4,M`i<njc+*BB.^S=6GrWl/1]A<V;^fiNSG?JPi,(X`!LI;925:!UVXt<H,$Eue&o
'ISpn/?cl8iC5t@*P='7/!^"ejL'^pO_2Jus-,WrIHlnC`.HuS#^hNer9UA)?]Aiioh?*ol6*
U?4lN_8tB;@mb#*HprkciUs-lMu4r_ee`t8P`suha&<!(G'FOT!*J$1L[TDZBddn/-*%*[Sq
h9sZ7C3,L!+n0f%dQ=c=M3$7[!W]AIJefi2.]AG1#5tHT(NVI50JS;97AZ>";U(CL*6\p7c;9T
s5S&eOI7'+`.%5`'%0M^"DG>7l;3%4n1Bn69PEU%p&sE@"r:7hj>o=>C-pjlUUI7h(Ld&\3@
a3tThc-cp$\e/M9/c.bg,d(d`&U\m<'C1GHWu8#"[68@E&RGr_3Z;kd%K*U0X,]A9Fj+sZm,7
gLS7/3+X`ZkiEj),Tf?&9*/Ae*QTkhOLO19c>963'5cPPHA)1$MbCU\NEX%2"Gh!L-$#Mud+
+5i$W1EF#5Kr/q++&?7np#-$2.Al4<$)7g\]A<@\tiCZa8)C$74WX5[U]A3#^=;tAV+!8ect%>
0<)Q4_RkHq3ZR$,]AOX\#W[#I"q/u5sVK34&flWd.L(iH*d$\gF63&:8+&6LRY56T6.7p9IkG
s3[>Kr+%1!<.A*Ud+Uof8";9&fkh>08bfDD0dJGrm5$=Bu>@$+Ld-8Be=VT3Agj$GXp"Jn<T
Pt&E9IShp,5?U@Df10/fOU^tikfX<qakgSp,Cf(0k,CsXe55,R/f@(PF#cFRoIMh<T:C?\9H
&"26Cj,[M)`t_>&IC4"NSF:9_Nf`ij'&fplBYaaU&n]Ah(1$L$nWY]A?qtqAVluWIe(l9Znt^$
%tM)j`Us[l#,\+oAq0G`4RdK_^+V>JN7Q*(%NFW2DV?Ll/)sSA`d]AO]A#$HTWPUH4+4g#3B:q
L,Vg>is)r(&kgfN<.%/]AY=IT&.h3TBk(H)s9k65CCkH]AP$ioLV9qJ5]A^e#-biHI;jRQDd96H
.H4SO"7%^67Cep\915uS3<!i/LhM"br'UI&(6>hV.k3fajq4l+66T9AA&q^g44(SD>@MMdII
J&<^<%),(L(.oajo?*\G06.omhp4G)u61`CMJ6u_6FL]A`6ip_-nj=dh^(!>YgDfZKJ?/X3a&
8]A#u:EebXb3*:^BShAUbd.M>0<Y!XDp7JFbF/bY/_W%sn%BZ96J=$&^/)n\\L.4kDE?-o:tL
.\V*#rXdRK-9%`&k3'V>[(j@9%E4]Ah9\R-SK2&uhM4I6$F:%ZH[aSRE@l,9C6N0=jM$R$dfc
m9kme3k7@XV2$p.A:nYD;W8D\N`T6DEu_1>`%1!Mm@eEqs0:bEKgp+,+,^Zlc&KnX>AOJA[@
RHrBM1n/O%+.+eFL7M<W%+RR;k-rJ63TUY(W^Hs2Ol[+meKX4jgfYpKNQs?oKrQK,rSc7mL*
uZ;oqo=C/$l"C:D:$/?#HPPukLZEjSS08.5?#1&Tr/K@V'[7Dn/cWHfo@KF>o^7%)#V"[BHb
RtNQ=X]AK+ItO#,o(b]Ao/nQZ0e^s(L,G0m*f;SqI\@SMu#G55%($l/=e;RnXf-<Z5UQ)(!^Cf
4T4;$kp[uZjRiHHNOeeB_=!gAQueMcS^(koa2YIQ9?6"DXUATL&"Y_UAYt$lYGB5Q;mdVbhi
.9q*7IPl'c$"oMT[@0?up-jm@F<u^!`:TT&OX;9oWNr!u<[.ZoM)K-C>nX-HLM/19jrU`d2K
1(5nCfI!ua#T^FUpWGd!7Zq<#L2[4J'p)N3SPqo[[2dP>7*I>4updON,LWdQCDptL%Rp7DS3
S(>KiEHo!`YLXmN_HE;$gji`)Eeg26)hQjV)]AEJmW8uk_JLs^(m=HNO=Pje+,71Q[WjMB"oR
F**qXts6<c;Kj\h?"'k'7dF-_VM=[jkc5S,nX#?sIbUPh0u]A"GS2D/MJ-jc"uk[S;Hn^3Y6V
5hjEL%YYj>(&?\_Zn7a2HjYp^nB(=6n>rU'`*U.6[=3Q8CQ^\*$qRZHV5ph?fg(pdc]A([_>#
5QF^;LM$HZX87MC</(ciY\Y:sb;7geX%%ZB+I29da)IkjU?C.SNLd@S-pmDW6=I`i+tO]AYF4
%MB7\50YZ$**6"b/S[2<C$G=#qI7!lrM!fC,q!8oCFr)4k85FerC2_nU?X+C=0iFVTg#mns7
]A'jU9H]ATtS1rokTkGtT?0MknU&keTk?)d!SsV_L?TD6/Tm@</f26\:@>d1b/BBE&9(2'A!pZ
TU"t;<=1^"lIr\`*rIOBQpf.lDg%ugCWBR5:Eg<TMjAiPh#aSqfV\M^FmE*%XV0_<mrii%'L
<Y\.q6mHYB?YNcK4R(?.6UD--:+:mmW*EUL<ZkBpM3UYQlTChZbI3>LTtT?nEBd'-TlTJa?G
ig<r72q11@)Wk7FuM$-leQYh@CWqK<]A'(fC;N,5#qL[cRTA_If\BBf"p%m#iPM#CCC-[2gt;
lOH;F5.Fs^_de<,s))G:Hmp;#l`LRTddR70gS>R7L.g]AgrHr&&fV0t=T\`YinfV<SM$i!h8`
`!gf6315X'64FOJC>*T@SBGq\Y`4.')nRo9f$/X@LTH_ng2<I[)1>h4P@ekNR'CWDEt!FFe`
Xan`ediF&c71=?:M^XP01<R`T1XFEnZO0CB!PmN8QR6Z7"I^4\Y=p_eE$R@kKO)UrGg3J@<Y
0E<5jGtFT8U_WZ7q$Gn!LF;rP6T(@%PCfaUE@r2-q8]A"q<N3H>X8'L*[=)UKFHos1dg44Dki
6?5YMfq4q;bhW%o2"-lF0X\qp/o2Y`eb$H8C9koPJT/`P8'R^fak=h"+AXe9A&l+3#ec4tOl
UPDX7Ff:34E(/.@^4rg)9?"gLJ/@1<Lg=!]AGfbcT&q[h:RIR]AP^5:MtnkJ`N6R#5(;`+l10^
&@Y@2t1P'Gbrn"_`#-n_k/+#aUbU^:3W#P4sDMg*,*?%l!)$n[G$u(5EV*#'DFa^rSZ)u61J
6i1Na9O-COl+kA85[!]AQ,PdR4`f?WP`5%uj&$]Ac5dDF_9XZ-8nQia5q>LQE@r:I_g<Qqm]A7O
9a2t0_8=%jU-4X2,'S3ZqM:aX7n,kWKQ_C;B?o*.j1lr.)d:g?"AP&R+X*!G=%)uHjJ5IL4U
3\&*F7M??3jO`jA24b'jXkoVTtH@:Jh8-kG@oui7P=s%5Xl?R4(__+RX&.,@aHYY;$JQ)*+8
7b[2#7cSs$F%Y&:oPmLk48P)nR1k1gAi^CC).&)rD^aAF"Y\bVW)8qsB5.IK`gQBfW7Tj9B$
HqI9Kqs+o5Z0BP^e6:AWjl25YJ?s]AhTAZm%@llj>5B2\;F<sl^&2nh:cbG[C7VZe!Ja"$8Gp
AbDTim1[:hB.W&>NAmngm[U:7.P"9.lN7K^9*i@QqbN4Y6TE;`2l6Jm1I^)W&lrT:/Rii&*(
2%*RB:SVO_=.ULlbfbWK9!cHeOVMcW?N]Aqa3\="e%lLM7'Eg0lmb;-S07R_,U/SDjT%5CT&c
ukb:7khD9Y3`\0s@ZQ'H\KV5A(g!.FT\F7H5THS^j\4\[RB;F81%Ua#X@Z74FGbC>QO2hV$Y
<IVqI60FGu271lHgRl7j/KeH(]AdSPHn\M0eg1PT]A/1J@t]A>FYLV7^4s#9$`l)1T&NE@PnTY;
-YRCJ":6HMoRo*%J;ll-.9l@+5BNWH\2A/m1B+F/0HsLQPc)<MkTs<f75,K,mT@[8:>3"89A
>uenlW-l,MJ=UI4^HM*fZ;LPS1d5?%4b3gRYo(^Eag&tODj_0n[,ml&*k8`R'N1Eh,/ld0,9
b.b"URG]AT^,#QiRmZXUK^4A<=FQslnk5-X[M[Ff6Ju!+u6Z\nZa*:Y;`:jeklI$dm[5o4tBg
GP`nR*[*$Un)s;k\2-OM8M\%uIo\SHln<0&_4VGncbdqd^`^a$X#J)6=W3A(L0+A9S8.VUCk
ns8M#ukUPik`bgHgZu;Np%j/N^gm2eNl=tT>La>eua40=]A0+>mX/\bQdO0=+BdLP'.+jeJor
njFHT8*Mr<El[3^EdWjfR4M=HNppI1X27D@KCgiba]AkWQ#u+8`5I)r0Z*,3gmfq]AXs;en;1i
9]A[1:>qRCp(eLJE:]A%b#iSa9R:**VDuW/pR+)<`?ahqc7+NMkM*PLaCeMXE"M>-XQ.[r?rs?
7`_(QA<St03]Ag*glrfO96^Y*!s-pZ#K,YT"L,/)o`7nIiGSj?`Ics1MR:]Ai7,IsMAn0$fV$U
5X)K.sPX+.9(q&U_QL$*Q%2R4!hO)&%F/hloEBoS5::@$uWo2Pl%9'7#L]AfJ7pp9jjjhj\+3
i"R0+4E]Ahm5YENuZ8(;nYM0\gFPG[1aN26Z@@"2*J$a5Hub=a-Z@Bsq(+^gJ=dqA&EN\.`O@
'ssL<47k06euTd0U-nXpm(Q,;MKgJnMD9qq-537]A,CQ^F@@%u>U:V\4\)Ura$V>BFZ2a6kp"
Eg=gJ7KTV[a\W4nd6KN(VuH'hfS/89r\#:u3eAudh>%s9`-HC=m79DnC<Rs.nfkVAdXGXd_3
==92j/uc>:(c8=l/*X/*@nDGaio#<Y3)0s1:5<&GBe2<;g=V8^9j5P_;Th4+\n]A6ilU.C'e*
DTKGVE1,?$,)io)m%hkg9$koIe=pA_n49KR'Fp*&g)8,r:&'bRpkq^AMDc&)d?/c.U5'AEQV
#(8Y9GY)\_`h@#(ci*P\3LbeMdE`@e^`0`U\4jo!+3Es6dJjdIRp,KDQ$T$:7]A'8c;)\ll]A:
\#t>?TT"HmV6[HbO6c9$^bTDg6H$1nr-C"pu7"sI6FNd0Zg#!)5<i!X6o'd)rW\gZW$?PaNC
q1<[bTM=n&ul]AXW-)^0-0_.#+*G*=j$be\65m<R$'&O`H\`IliLXhkg+?dot3"K'<6ZDuVc2
rVpqfqld?Cm*&/*N;b'4[Cb6%UQ%dR7JoBq$N&Z+b0J)FlkQRr]A`f$H^o`*rP(GOa6^#&9Y[
qBP^I(=S=[PMBk/Ta!)j<2!'@@.Yp9g9fa5$+""%QAC[S8+bfD]AoAmHTa?=1Op0SVd/AJGmj
,<G/;+^%A2H_)Pn_JeT"S=giS/8'!+<1?,$G9'SQLb"g38b-+#4m(PuPTeoq>9iHN5g,)JmF
'&E!")6d:h`B':4$HS'@?\fV^+&8e.pq:g>a78.+T9&saY[7.?):%_XVq0>J:'Pd(m9JjcP`
Zcp9["sqnRj7R_msnH8aFhI]AQ#K/s&/^R[WNpb\cj7gE;W*>%:<oKf*EhfZ0:@<N?'uC!!#_
)"i*fYIb?o%$-?\83FlK+dBY/Xgk<\`qK9m)]AGog(g1lB?;,<LrS<^d34GC/[s-8'aQrWV1T
.LXWerp!BimJt)@UEF1Mm3$SgBX]Ahtu4HTs'.O>AeD07RfjY7,Cq=F=d7Tl%FqgYD0aDUi>(
EiQE_U*Zk'Cq2Sou-G/#L;.hD(CB<dII<3//Nn!4o._@7&28r$J<UWtj-0Yk#QnRiemdIkY`
BOfKjSET_A_K'..=Z.L+s2*C,cji^RuPB).p/qSp)C"jLoagt<AFADF<f9.q]AOf>C[H^:-OJ
oie?.EUZ_Gk>!5mCgg)sY=:Ah_0ge3=!ij!6iSt9>oIJp6!m.,KpLS8eSRs\sX"5r7:6LitU
`R+fa>.5!pf9`+=lK$R5H-'N%pj,Rh<K^aDp71p/Y*/]AG3%I'P]AD_/,.iamFWQuCt@>PMD^g
]ATTf$RQ?_ECs@e$4HkCr!-q;JRpt[123S:Dk\@^:TM(VlZGsV-o0?"J3VlTCP;'RiutB_ooN
@r:OH)m_9^I.`Di*%Jab(-1CbJ(q@3ZK]AlG2RPca(V>Mpb"u7YMkW;ofIDk)pBTK'!(6`*Rk
i-8;TD`2E4mg#\=qU3oiq=m]Ac@1Y)epJEm@p]AaJF`TX("d=FRoiktu=hqM[M6Y*M44Q<gU8*
oYkm!]A:cc):eaXBYDiXko=[m0m8D@UjNpVY@SlBt)r7r<+<obQ]APMdWf<n!>g\&,Q&Y]A[p'<
kilZACr]Ah*?HiF.I>AjXFC$-8`;s&Cb/iOA:q*[Sb#t*:c'$hV[]AIl_aVYu-UrIHXhG;f@WW
2't'ZZ@3[("Dp7'_/Jh:U?cgB'l<R>=j"%[nIWqAQ.?S_F*\e2QLcXZ&p0Nr^mHXTAn8[@c0
YhHKhBNA0b[rsL$3&?b-?QT<jdB*@;q-@BD70"X5_1k5=s#rPF`q'2G5o.D%^N]A"dR-KDk2+
o\`=WVT?p_AE(A>J70K[+)UQR!+4ZU7t1o5tec99/b]AE%D$*RIe>sSXs[>VW+h.R2H`[f/s*
Oq</R&-jp\e%?WhBH97$qV@9s9C"K]A7E/6\-"(8K_/SeWV3YP(rg53hIL72X75r^^WQnO^%Z
?P;9C19HCC4-@QMU;ea.a2]Ar=]AhU24#1iUJ`/aZbkkdR\rAD>[GY=l%MAp>HJkVQm]AU?YM!;
V6BA-BlIl*3uNk'!+'C/\jUiL96m1U%f>_[/,uL@s.,**VRl0>;,d@--<+3`XGE7/Gg,s,0s
+I(WC#T..B1F0`fg)dFk(^pR\FTZ2"0!=Z[+0X%(T"JaF9!9EC*efbU_EfAt>)8]AI]A&ee9R*
-p"JcAn(Qr$,FA0/U?OSst!AhG.ea*Cs3A1*4f>nRX27kL"p*/_`,0pn0gUee"PNFu]AO"mqC
-]AbkDq!.NLTKFl2?1pFfLCZ"_2eo_3]Ah%MQ%]Aq<cj6pDdR-hZ83=(F)2CGgb<Ta-)XB1#gRW
Jc4B7K":\HlWgmXS8I*BRs%7BShjLG#3Ld=7#WG1;_bmsog&8HNa1u)H/(MHPCJ'r_;*=t+3
[3*qHkd4Cg>Z!m&gF>BKqKBN(]Au(4Raa\he6OWH9(8G#X<-HV`&DDg,7K2:IZ6)/3a0_Dfa3
]Aq\/C[p6:cM*&S@"S1-9&RSHX<LV+oU3R[\YgS1?AmVldC?[B&#_TW55)1>ZM2-S%^9a"+[R
eLRU<a>&Vf^UF=j&7O=I!D71P7,\A/k9V>qs>kYELE6H"<aR-m6E(6,c/IcPi>&4kg0mdNsP
Ho_ULjUjp.l<]Acp"opQ?KDmda[Y2,BDU'A@_5_G2rbe/C7>]Al2bKjRR<bJ#Nmb:ThW3Ceq^\
f*$"$&W3(VN(RF%[(T!]AP$-5;DiKM8MK7-Yb%g?$k6(9Lg..L:;Qq'pg'Z/I$T-@rZ"H'lnn
e8obh!1tA!33[*_s[d_R/1Q=/j.sN4]A3R<L`4Hh9T8S*$X>gm/??oX57bR:hIgW/6Q$3H*m5
]Ag$`Vu.hSotHPHU,aTR(;X!%#?Cpl`t%C*$$;#>^ZP.+08SJZ%KA-T)gN&V:=rSpQRkd$tBF
8VU63DbBH\^u4ScRkeW:i5&(-_YV'_o;G&rkl/8Hg*1CBb!"MNA?Ai4Y*IiX2`3#QP]Ak]ATid
en5I9o+:D[hr$\s'Q&uBNt[DNt[YdE+C*#j$IY,LMm"//C^KpSWU?HcQtpC=.fe)Vhm(HG7I
dI[i@\h0U46AECj9BKUFeC[3'`45^/*qm8GA#["DqYhT?O_1gtA.B%Y[P5t]Agk`5\<6X':c-
<kqUe-AQ_ktGhJXMDP,5#p)Dq0&D,AmVm'2L#I"'V)<.=6kD.$"3DH8`o=kcJ&N8tE-5?BbE
:=.C[UIGlLF,ZdgS+`(Lr<!!HS-:u-m*MuJ<(c-+[r:K5P*]A/FagU$MsJ04u?ja4,ola*RPV
qd1$Gh]AF%XFW9>q`U8Zd5Tne=]AR'ZrUDSA8M4F<>,p5!IQe]Ar2EpL)G!g/A;REGgRfe8&3^A
pg<FjJAFZtLRfC*>/UtaCF0HV)lfBl)e5LFB)lektX>=%pN0@a"MX"([u`tM!>><cI!F(^JM
k\*r*N%'0,hG/6!;JAol]AcM.!;`F$R'Y;2S$p/tiFhuo7jpp/f</P\XccjsN:+ej@b"^3W(\
u^QPa(6DJ]AK@aLWl=,l`T:G5:3BZJVTT\gi1*b94IPEUONErkKoO]A0oXg4IiIYe8_\TdYK$=
rj7Tea2p8k,Q,V#,iB93/I2GZ-'JFikBq@CY-`u"]A]Ar>16lW+BR#W]AJEa*LJk>Dl[k_Scl+:
:m+BIkUaOpsSVoos,bFV9Vnr2=9bu*BqVb),Y$Q12O.Qrh6#Z!aoNE43S4[4$U22?+1rI`<.
8)$e0Yf3bF^cM(g:QF@$^`A%umL?BN4"o0_0Jod@55F;d:i%SuB%q#!'4QSYLG3W.)(7$_fd
W55<E6$EZK:CNmR.Wq9JiE3m#]AZ>3^,HY;p8O$.b28^E4R:bbL]AbqrQ[YALN>6JfD&&VR6g5
=\9-@KB9Wu^ne?r6KNam-f(96CJHG#_N-WP;-H*<AUS>Q'<VSqQI.2u2r:"UM0U)_D3-)uJW
.5pmW^JdLf/RhteNNhM9Qjim,RFAP&]AnV;6mBMiL.OCkg<Zr\\mQXUdEH-s9KKg-=[bOectQ
ub>,Q!PL_J*,R7'P%o)XJKr7_MkAIik=_>SX4;REH43_D*@.&#<N8s>ThjT_cj`#30uUJMD$
U8rAE%$6eu0=dZ_9D5cQU\D'>=4WJ^Te3:bG`DO+RqRTek7ir!';XI%iTDg1O7p^]AI7[Y/Du
_$Z*@nhoJd8?2`NQ*V[[n\p.@XG\#B^C?+YpPil&KoA,td./\`J1DEV>ld#I,bA^*E.MQSOd
@#i2gGbMg$n9W=Z>Mj$r<$nd&Lbm'SQ%HhDtu_$ncNc;Llr(G*tNd*E-<[fd++Ga+92C[I/"
WS268M&>7VH%*Nd7EQe2h>LKJ#+**Wn7KgnTbOnsuAh&]AJ]Auha94";)W!W\.i9(<%oocd(Je
7rj-5AP2fFlrb(j<$,&f%a)$Q+q4+XKB0Y%af-DM2Zb!ljR,.Nr6Hb"@_<Z?4H?HZ7/s+.t<
!@e2K*'R6Z[mXL%L%P"S6:gr@?')tE>pkG8Fs7!]A24dTJp+oG&=u+9h8Oli:&ORi(hR`,c0T
U1/Sk[#8.HUoB(&(-.kSZeJ+YKjF8W_:A'uk&T3nS*O^FHMqIp,5Dgp@ZOmTpX@c)0/Xc[cl
Bl$-Y(mLc!3E$JQ)`uHjQCMKmXI;"o,s5?4=8HLi10L`MV#0M:oZ9mT10J59YR1KbBT]AQa+%
poJEQ'nD$EN`/6_NZ<#/K(4(;QccF\=U9S<1"!,kic:@_#1:eC\'%GOuO%o/Q9d'RmF,b6*=
m8I$=J3(n]A+!BRGEI-kE*qAY=iE)2pI`imS`\2B^!^.i'>7_*=:YdD+X=_0L-n0jYqol'n)f
n(a4*iah^7C-Q:'A%J/\B6Ecmm_6fhmGnSKg5*,ERG=a.DoQN.dFUHk&fRH1noZVIj5ZQ_+8
4u5E!iUiu6dNlH2G`1-:QKgBcZG),mBZi!`8K9mtW#nE_#QnR:^+Z\JpNbW@br,#qNsB#uD>
3@`]AIGj[l>nX"D?gu>5qSE):J;o0D8+K98*18Bn-r+78Smd?lcQBY5dO"$D<tF*=.q/#9psV
TbYN7YeS:RF!6W[+HB;e6HoFPrL`-PW;kmo\"!M@Y$6g6W?^HlWP92<'c@?)E;d4=@UYjU_l
VeIQdNEs$eS4G6Ul2-JEY@LmRA_NU1?!\3aK/D]AEWL;i1hBJJcbjS2+J0L"Kui4ppN7OI[[o
*d0LLr$Bh.ee)pEbA9g,J"nj-)H**?IEY/71@%A($A2@!R\mlZpfXcG+[-#eu'eN,VB>dVq8
4F:QoXM_bm;rQ@%R?pIaeX'r_J.Z%5Ok.L#QGH'k)S/SH[^P!7[UYkUS8.QGO_a?=8N84rA>
f&n)Wkqt;!%eaq"8PVJ(@\WcTIi3=C^D#C6+ihoM/rNU6u<r:C.eb?k`MGo7Q=1*J8gNq)(s
4!T'DREG>C7]Ai;0ZfUTo)^fU:&Mlmd8Yf)GJ`@^UH4.Du$@Tub,A3V"b]A`8WI#.2PoJ^7*J+
abfbjLsgd]AMb)eDAE)P9:"'R\dXeHneM4WYh[?m<WEW0n\K8=4IImsgM;Sm:u."tVGZ@KZ;W
?k-$%0[`unY_f[II_KT,Bt]Aj$WY/t@a!+lt?LHe&P8X4#^?:T*!0kO9a^aks8cMUKk=L[`WP
$^a[N6\:s8G>&;,H!V5M\Q!Ek:9_)U!"EI.`Z+W37:7==0?L;Z]A2XI?aQ@-(R<)[anFf@gBV
/\ZoTYCGf,@sO[pJuS/bCRc)6#r-jLY*AC=OR$8fU)ahRq7Z-\@.0eX6iD7pV&D=*>(WQO0f
!6YjrI)nW1e6[aJfK;j]Aa*89WeeM,#>5IHh`R50BOleT0q72te=!%ub^p\(#ul,bE6k21G6k
jEOToZlX:S`@I="O1*5A%N$I'sb41SBnQ%XJHhC6?h.ZNF_?4`[Jg4di3V3j[,5UEVrp9^7N
Nok.Ad<N_\cdE0$-r<a^:\lDEn]A/%E=g]Ae)*%h0i06.L=q!Joj<AO%Uk\9HLC>*+]A#N4TcV&
IJ5D@,'g'Pm'(W:CJQQ0ft'ng9Lf.'61K?n#UdVURu+E%MOsgDL:c6RTC3Q\*QDBnYhIl>1>
K5P*Cj!&nr(mlk`t>Y>cY'SIZms2G4prB1\EAr#o++-eme1HaW-QkgZ#R,&Hg7`jH1mqZFar
eVrL?Kr?Z7!m2+U4(Vl3_S'=]ATG(Z?\eN+cM_/d<FK&sH3It^_;j\fo_[B/4aHJh_A/UrPI<
_%Qs:)MI00<cS%:"?MOd]A;A:JR8F\?`.'bMh&iM>%QVZk1cRkUMFUF+&<g+=,1+jr?8$;lRQ
U19)'ed/LIE$nFm1cW+(8-%:ppiao3hQLnaD;.>Qbi!<-($)/np:DZM99^%_@C3l7LnoAd1u
rq_F.S._A^jNmFD(h,H[IXeqBH5&mcGP.jM(Fg+_b)8J)D2Mg'6k>parXsN6jO%/f1b[Z:\i
11!5,R[<h;rNQ'u:H\PeM*bgXZ4Q)JsTf`X,(Bn['@VYYU<S.$nkQPNcl8X*Kp$S*#iR*&aZ
LiHkGkGKGEc=u5E/89BsiD)0o)@S;IRDc(V;=ZJ]AYVdN1MnlE/CHnXoK+pAALkp6?7RYhp-l
o[Qoi8q0"O%r`pq:muWp<I_Ed$;r`q=fe;ZaFBLghfWlR""$$pOB4pJCX::CO3jOXR_6BmTT
bAb,N-*BD#<&0F3%DH;`A^IRl2kTr7.@gNJip]A@<A2W*6HP5?_d^=^NI=R$Cm@dfut)`(G&]A
h3kDonj>4T^F.nZIW[?nDfikQ'0b+6Y+`kNZ`ir[ic"3ADEcNr<F\4,gU$C`%[n\Q(tVbk54
rpA#'N1h!Q\l$G$)3=DL(`XIa1+h\>N^@e#7?/ehLD[pi?!;XWQ&1$se/-f_1Tmk)pB#8E"f
J_a`@8'lN1<h_IHhCKh7ZGAR\rO:P>Sq"Nan':9nZNF-IF>mbplW-eE'EN6>-ogpaoh)oYmG
qZCE*"tGppE08*Z1A,Z\3J=pR/4=NEUr?O9&n=@'(]AlgNI=cu,mR-L9HG*e^OHa/O0D8gE2b
K;>0MV+mFc;B*KmV^r&$?'/=T9R14e'=q#0:K.K6V=0I<t0G`f*e61)q<A4h$mD)8gu.9?Fj
*;IIn.%Yhc<!,(Si;a"qnj$l*kR\BULru&ir^;,2Ejlo<:m[aDDZHq2A@9&&lWl2tCEp%f,8
dQl`rHg#/Y$@XYisCa16u6eCc%N!n\$qIXSA+ijiCBpIa?H]AG-@o-c9WnELdW>1E-D4!Ko\j
Zrp8oVOM\q+gVj&i(8J;1E>rVt;c]A(AbDft>s('ForuSq)c3/]Aq=_n!R%q";[f%nM#k+@mAG
'X[#*Zm/h3C77Uj"Sa:o>mP49,4))'8E?5(Z%0><5%1!\p,1WhM=se-$<CpVqT-$-=Vf@2ga
oGY`C?0L-33HkT]Ar6CU.uEkh#^g%SV&sbB7)/X3(4@7UFS;nS;7"o3Kl?MA+8AC7)u8s+cJ9
S*DfUZ8>RMUr?srP9b>:g5K3<dOt;TH8!oGpLb/<-k6OraSoblcO(o^j%`m8c5sr9'6'(rW5
sC)2?.VH5Zl0=2VhRr)#C\Te)KAl4j4t>E;dFqVa"d$k:2i'r?$'+H(eW7]Als)uM7%u,X11@
6E@BPTf(%cr5l[`eW]A1!il&U9X>&s3[Z_#SG483i'3kd=G]A6SR28jiMXGJ/\[eWVUr53q`Zs
0npT@#a>]AT_&1,^I=(9V#D;qme+OsUAlE7@W=eVqbOo&g4\Q(DXj<RDit"9CJ6G_1,T#-"Zm
&5%K8S`H5]A>/+9$JNO]AZ(o_D*5qZc=F`"QI:U.Mi*ica#D#II4XZ#_o?de7>B3%-#)AnB*+u
Tss+gJe#N$!SKZl\\"uo*8NeXO<bF#FJYJ8(@p)ib^UR$f=$?hZ,ph@6QWm8'l2.?_g'`[gc
.7"o%EV*4(=*bckhR>O^SEN,;U"Z0t%fErU9fCH'A<nAddUR:j%oHkjuq9_;AFnd?Tm[*dWt
$B3D45?O;IMlEO)KM!Z)+MjCiULL[1lWIM@OaJ&'8aW8+M;"@X`;#&bTD#%T$S5DZW,e9$O>
MCRVJ%+9m_9e\9U\;,d/5AB`P*k)e"M^G':+q7#9ac?7k!&f2K/E5=s,3*'c*R'AL`6lZ_d9
OreLp%2DAir^fTh'MELpc8cP%1[jYL^F#!lu_K)A'WAilU;cY>6bJ,@d.XI`G+`9ci22pMhc
!Y+7P#&]AsaFX*OAg;3tVm5U,:$2LJ545WEa$e3_B!G!_-HY]A,YE8#O#f[]A^j3md!//CK+826
b`F%pRHed&pqU"o(hjYG3a'GV0uO>$;`1.c]A>+B$J/$JRlOf/Rt(#Fgp1<MTEmT(3R$lZuCA
GWJ4^A:"^>gXa^j\/1eUun^>[0maHtekOaPB4/+W1FV:+URI*$df$$2P3V.mU%+P,69FYR9%
@3G:<d"@Xjp0rJi"r[I^\6=qW<5qfI3-igV!j)d2H['gg_K))rk_^dZg-`;_"meO:d%TH*E:
*]A,dt`rg&D!3L9HQLH]AlI]ARkjWS*7VLgAPO%5/;A1k1+7eN$ROTr)>dU..$uCXZ1,CBHuia*
DX=pTQLEibYaZ^XG"XdZk,DEPOj4FJ69K@F<u4Z,aE;;1QsU!.-8ns;1!97e-+.^<pSSuVdJ
dWu#"R&)4QoqGC+:QA0AbCnWnttd=*lO2p%^4)L#1G'(ETh\3V"ObP14Tu5UWr5.55N/oM_C
dq*NB!rn+BQATr>WqcqH\lA*5)j6.?^C'-ah&4^'mA)H"jb:ih/ArqTh[Pn2K6<(4-Z/k0qQ
LNo5IT!La?!60<,Q1DcHf>c68&p\SgZ/:u<DY2nP-%drUF(0*gj>L7r%VCOLai5]A/U0LeWed
3J2"#YX=,Amj=)ij$Y!'E&/%_MGb@V9HnJTY<[X]A&ehhBB<a_4liYDpkt+Pie;K!J]AIB^$^N
\c5N2\&"!Z?To+<Wec2ZAo@XSj"=f6;m4-J=M[PZ)'?5YALGjDh)8U?66>qVb(AX?PO,d&"u
D1g7>RmQloS$;^:&4X^@(]A\l,]A6.XmKBdD,7crjdkB-;MR0I]AaFZ6qGPEd.8g@T#MFL-l">C
D[YpH/o6]A?MFdeI\$-CE7?^43[/<FaSp`,%5J$<-jLVH@nR$kq,;;prL[?JlMHDS$cGDB^^'
DW)R2s'/c+PXke=b+8tX%u@`,J"H^#[b(k_P:tmbr>:OT8oNa/F7ZI<:JH/fh\4dnbk>#'M@
B$dg.\-<(QJE_s`kBYZ*+d@0^Vao8#QR)Ei]AZYn"q6Ql1=bM+p,:GKV?Mo"#HE@rl>/U\Bg0
K7"P-ppGQ"r,`hb/P)h,DR\n#G+c=RiRLUpQ/7ZnN-nN5J>6<!Dl20T',q^kn%&m`A"ARn:o
_[olNcj=beg=K<RD_[0S<I#4l?(q/V1K`.RXluG31Yr>WK;sq#/Uo>TBuO7WHBTUn_OQ#$sV
q_$A4ia.YK0NBA"#jiMuq<iD>m$[-^\bgDt=[aE/5.G/dqK&.1.;8dT.`8P0o",1AB4CnQR7
^63^rgocr*%$WGOrTpiaLYG1CQ!J/2$V4:>Gc\Q-6AM<jB?O!^h-R,_-NVOcef1Sb<Eg#W(-
m?J(B`QFT&=8#UKU1dqM+p0+rEe=g*jn[<QE?"NfYua5fC-8;phuSS`/L*R2V!4ZVU.oc\@P
K"4mqbHc?$Zu\HqHcKa;'^0?Soe%Ls3aW5->B2u]A1AX\6QF:&lIYccHphZ*kN-"TB_.Jb-\b
RGKXaqk#?H%,?"P9s.M5_rq'"qm9dmAmi>_l+p<KVf]A'3_0-aR'bbg<0*]A;u8NT_'5N,io1b
t;<-Ks`/@r+'S*4qOH?pggdA^#@*&sh#JV,WnZN/cH64\3T@uQUJ_XC<h3^QEj\Q1FUQ`g%g
CGYC]A$)9Pd-]A`FXWk(j\/MhN2YqTN#A!S,J[d<UPf1@-6jbS-;W!SNNSJZTT!cQ<5[Q^kMS2
8,!V5dN-0:<Yr*$cA56^(h9G@2tY]A>6\f7h3`Ye.KX>5t&-cEA+eCQoWRn`F+a("gGnSt$OW
GOUe]ALRqX$Z/+uol@CG[2lCpVNp;"1g!=7RFD19T;;@[Xh3S\Es$fPA?G2QOo%)f<B.VlZSo
H65lS4-bjVm`85i4tah@&`@m'V)]ApPYhVHg-E-$`aN&"!ZIgUP(g05d7SU^&=Ps-/FY/-P6]A
1g6N\%lE#0[(7*kXH_Km"0u/ZWX[/JBY:M;h]A/MNus"\Qr6Dr^chQ^*F16QbmdUY1FXes\0I
XH_VBmIH!Ef3L`fbg0SeQB).J@lB+>HmeobjBAN*#o:O74KLEUoPGm^DB2Z*(+g69u+``3M<
dQ1oD1+nc?Xu7B`b<>U0G=o<h@6]Aui%Mc_)6d^$.J'H1bq/o?E25,g`Gmp6dhJAma-sRH,"D
HcpHYPf-BhZpcWJr"$NR2![euT1t^Yk*3%>.USBESGY+^)?ukjAH(1qg.cCH7?<jj`@Z!pj4
]A2=+<BZBAqLk`X!7W7Go,dRKj<CGp<29*)116bo#uL`M7?p7>O^*!Fb9djP.$N8<DQqX1A]A$
r&X?]AXc="/7l6*9$U2ef:fBj4*`8c"B#1iUZh+ok5ch8+oU<SCCPPcXZ9TNOR?/cC_[QXo$c
)X![HWD)8epHe/(NKK7GeCFk:3:CD<s]AcL1,-srPfhs+)eA3pCGML7G2DIbjDlYb!0jls%2(
INEU@(4g`i`*mTk_D9Rt>>p<":NI<"`@<AgA5nOW#e6\!86h6+o@'%>3,&]A;j?`c\#a"ORM)
`\aP]A5H^5BO-i@I[)QP1;6#RnO.h"^@A!3%'#,;c[NI=PG79#NGs2FF[]AU[iX]A[@md"7W(eZ
4kaZTI7nEX+?&d3-;.^eS%,7r;E!RBOm[gSUHqe\RO04Vi1FZ=:Aajm'#^)ee0aRJpICBXWo
TnIROu<(cl0VCim`^=5.u^Cg$VIBWJY_",\):`;i"'9a)-;X.h[b-$/^Fh&&7,T92BDd31S\
`D,^lap54IZ8iT4JXXTNb+N=?/]A%ZmWFp-97cOrYLahdX@(9f.:-'2J?j+1]AB[$(/juko53G
'/9W$R%<&fo(Lr)!k4sYG2X9SL6#S.0/$eXe&'bP=NUe2^%HF'o`OSp8#R-35F7A"#-o"QFi
6YfY!=,M0(T8ZY!HtsXEACo&@_fOrIC*loeJH7V24))XUL3ReS'S\4.?L_tnRkqZQOXlm.,u
;*F5C&S6qKW$SE^Xo)d[3Tl\CRPc+>(=`!<A$]A=N+B3SXL_S]AdZd9l(hrMc-Z(A"RHd,U$M!
miC<$_?gL$(JG,C^*b@n#I/e-$>#!;Ip`u.'(uBEQ^3#3[&TtBB\H?"[;GQS"nJ;<YIaRIM\
9TjjBI?gbJSN>7j.Es&$(U!q4[V0t6ooSV1,`BCbeDf5iqcY>eRlJIpLY(<l7!koftoc_K^#
eS0cM?faCQ3EpH7aRL"]AbXAPDoX3<%*ao/RpO=4ZtRZ!m7hdX@DtT$#1tDkgulT14#tYOut\
p+/:Gd[7rb]ALXR\>Hg']AHnF8qi1!$[5755Lp%NfhKRs4Q8!GNL2un%]Ai*G#&F_B/nCLtcadp
XD_lm$!G!7rd3(#*pMc.;^E^OJ+ERD.m/3F-cU?>Pk_.1D[VC]Aj$-aG73_BFr8G;V&>$rfK`
[D_Nn;`jaeb99!mr@i:!H;lX\BrGitQoGffKA.?<(i(Ls2p-4X,q-HaG-mB0![aqe/cEI5k`
3(/9W;WGuF[)mq75eQZ9.r-!T'<q/q-&!U^#9G[%0<il+Y2u:9NQ0H7SLt'*E,^2kHlp>#!8
2?Gpa&*+'Mpf+'RVZI^8+2]AZ420^3(W&CLTf(Cs+/8AnEBS"2.E_4`&,+A7Lm9:HB%Uh]A4D"
Go&Ni""\s]A0k!`W?OS_Z'Bc;5_V`%7H",ks&:Q2O0@:UeYBkDaE%:p-He@rhA_<-;R89U0q/
@\".0V$%i@kT"*D<TmnYs6lEqGA=)46SiWk-o6gUjV2hG'J+/e4#'m.e:ERmfSSVjJrk')bn
e0;<QDQ@]A[!l5)e[T(hq!/Ie&P$*\)"E*^(g)q<FlnS!#PlR]AQZHI1;QGLV"g6b!AnN"r?Bn
`[ShZPh9.Q`6d^57Z0c[FUTi$d=F5kiF%I_9UT/R]Aa(9nPA_[Gkm$-jWUi,82M[3'/2L0$VI
k[kd.>%o8(/K"T_Pi%a'*IUTp7:MZsro14'7``^&a"6nT9>F=l$oFMm/$qO@-UJug.C!QL`1
4Buf5ZBs&Sc;Uhq/Zm2`"Vc>!N^'S$GuSpU7[<oL#2lVm6$J.0dKd/Z_M>iBCR,gte%gq2`i
6&t[8U@!Fo8GZ&R_9ZM?A0W4^fM-&XYq]AN+KF]A"_T&fY_.S/;0YQCZfNUa:&0=.ZhF[l'/(_
in'b:NOSP@_a/.'4e<%tk"]Ae;hXe9scEpa6p(s>2#]AZs=6<GTQ2O1H("DK'(f:4XCBo'>TK6
nC$':IPZR<W'l/o,+dA^:*at?S;1J)`XPuW-kfaZ"dXC%!>N'XD@]Ag?I_rfH^5bukc*FdjdD
D&$f/:?G9[pM`\amF+LnW5_A]AuKq<dIpi`5>9&V;-]AZfN)SVt%Wp4?Yh2Hq,[%US"Am@66US
/2aLX3u$b\OGI&oZ4C_DV*bJK90Uf^NOR<"k`h?AHX0=rr&B2RrAuU+nZMdpGeX'c1Gq:^_q
9*/P6*is<q_G`"ZtVqo-An6_.VcAP1:Es4/>e_A35R0IZQsg5p732M-+#h('<EMAIL>+
J6[i_s,EL\E=Q$2hk#m'GcJf*;;5@=r1DU_$6G$4a/o9*K)neetngbRO<"c@'[H0_?k*CB3U
a+6=`MZIqpOUfgDui&YO5VbmU;Zl-Nu*b+jA+AD:0.%3>@(_`UNtLBpai!sP=1^Ch`@*1?,"
#"'eos&Re$8'7_$-=!R(Q^XjR*/+p!!+V(g5CHSYXJK!@&`%k1dILb_c--))P)?M$i&5$`U/
Ki#([h3=IKh+]Ab,8)8g`$!(R50U'N'XDOM=>EYQf1XWuN3sQ#WiO[@*-Vg;_*1tT-J=A$i<c
c?T`pCaMk`M\4U'Ctp)O4)k1tQfV(Pq"31nL"eZ^[=\OZ)NnDF7*[h/_(')K9L1R6#_k-eUE
'E-cLf/-T+9VT8/ri$[EI.T-l;g9=(aDR]A3R5[5LPC!cd6;CJ]A/jU69Y.6e'XdA#'F%=;j.1
"5jM/3k6>qS(d)F)$gj>&)U'B+.h8)iq5khiT@L/8hh'?!fa_c5Ct`1\X&ofglN-S,S<e10r
2MY=b]Ac#@d\sIT[o;6u;IbO=b1QY7jiulGPYr[#s@6i%dC?ng+D!f)J+[QcDJ(^2ZZ=%Au68
lP*2&PqCZbVkf#)<!F(7QGM45ToqG_9F9B&73uIbA!s;q`ZASuL.R-A1,IK!N1"s`B<67$n,
9nkceBO*dkdTm^9?k:QLFL[m[N`lo>rXMhKsQfShOVBo8$M.C02&88M\c9qBrjJVm1ucdoaI
tcqDW6k,2XL""aC+.b:H'YJ2/@lsGcgU&+,BN<1<ak`Nkqn9>OqVeh0_<og,A\6M4J=DP%O3
iD5EX>YG![fHA8rDYd'JNkPU!VH2u-^"CBpT<Fs,s@E@`*lOX&n^m8;/,bJ=a0G3%jS);ZNn
PNFq\nC=UJ<@lW/!!b&rj?X5V]AIp?QjDJoUloltjaI<m]A<nZa9&#FTp60<h/$+8,@B=EIS`q
2a)+;D0.ALp>PKG_>[G(o@82[Jr53AJX*>fk$49j\s.]AiIOsKL_DMLPGbCY08m`Xq!Hi=F7Z
Z5)s&j.k8(\o0U7s+4SgchNCAVNBP%PQuHj]AsZj'lW,5"?-S48T:5&HG0ngR,rQBO#mIa@W;
O.BYHF:==PGJff>cXA@0S!-O4,$NUm[!R9M=_Rg`<65%1-47gU-*_A:J,OA]Ai1#O3Q=#A=%0
$a.H1P6,kj0aRT3#mAd#gNn1fomHs5?]Ad!`26,/eYj[//fQ@o"6>X>0hK"%'tkI):'1PSo!W
PM7(DRj,r:ebJ=fj<fj6hpn^CpEmeiVjj,dq#H2U9RQn]AFOl0k,BJ2&EhR0<ou*li$AmKF-4
r%l&!J8QF,jKR6Cc6HVc%.6)u^X@&VMZ7eifa`#k@,0C\rM>VBArN'4I%K$g?!$>&>\W)*h3
#^Zj_=QO%GksW;`/iJ[a[]A!b$ajJ;KV^3b-1]A"_(0A3P*Ecr94q.0Jhus*n[#dsO+FRuJum(
eLu,lW9CSpWji6q405NC6`isi2Y]AOK\eQi7d50LAKVc!GjNMl,T@t,?(?F\%]An9fc@1cO/e9
"D\9Et5tbm`k485Kf-p*92Pn^2hps>L';u*M%*qrX&WlU7r`P?l7"qg$15UXh!@E]AG.a%7h3
;hk%EsM#chE0fTcuVi[;ip@-<=!]ASeT4O&CS\nF]A)#YkmXNL[VqFr3SNX]A>9MGJXs3.hcU8+
l9F<afkH="36aJ;G`BLZWD(9k'L6Jb&7Ak^n$G8o4>p7W/Qp,OZMf/WH+@XYr9K^3H%P,r]Af
sJh/Y?ju4K!B:\k`ULM>c]AiqqH2]A7D`RpM/!RR(%*O.h1=b4,:d;bpf[m.+ht/`G]AHjohS!u
jMr1IFD!V_B+aY,dfC8@6/0I(t0Im)kS-,C4C?E22+]A6K"as&?b+D+`Up*p-tqu+4u2uVZU2
9e"92X.21beM3UN$^A=a<KhHO(po6A_[kZCGe+n[Afo?,N'MudbML,BA;lr+LQT3=E(hCI'Q
6_TCY-/53qm.p%),"+6l@qiNt;:e'n&mio5&=;Q`gG2)]AK5>6bSlJP^F[/4ri%$L%\XSMQVh
N^r;/\c-C:e(aAI2AL%dZA7=mDEru>7M8KFY2[6O:g;?$nWRB_acA)\O`HK%m3&^/j`P_mKb
<`N&(?d4:GqV,Yq@^P(Zl<Vole=,E/qWrc3&%7f"H4OKaj>\;0=`RM9=l1_P4LA\1Imt0RIB
ImsuM<c`\C3PLNh^Tq-Umh(<i988kY7<KlE]A6EX4]A-*4NEhU\?JeP^%'a\7PG>>1mAUKEJPg
,"Eis0T'0%]A%rYEo)([%*XgT%F"`r(G4l2'4N[>XOh]A+*0%>;rVe!)h?/LE+2bC6YqY\=cKY
"h^P+?XgE28PR.XPIpR-GL,W`cmcLQW^Dk*CoS-8!UH-_U%`^FTK&=>XGm_T'Ih-un0h'j,c
"%B8k,AN/>@,5aYJ]AHeDRfg1R2bK5PqVPe>%bXd&^t=G#VQa]A)d>ERhPJOpuF-?,B(W,*Gid
>dn7?*cK#9NRC7.!m%fGe<DO1@tcs1(>2U4LEV'iq9M$TUD.Q%iHf>;J]A8qbrOmGW,Vd*ISR
]ATgLq7DY)9O&'9JYX5.hT\(LB"Ncgl?s'TLlq/X56Ed06Sat:/!`9=P*Pj_J6rq't+LDuWJ#
!nRF7%f*`$3F4/?XM2,7DXk@Ga9C;<1YI\5N?_$(8emS6ZeB?"=L^%R(n>026I1YS^t]AX0),
%\#&^baObKFT.>^+ah]AA.X*(*r6YDoF'ROQ9k`9XZ:cg4RW3F:D8O/MZYS9)*0P.pIoH1iA(
)Z%Jg<HrEU)'rW4hZO2'gV3&`Xk2D.BZ!V0'tpG"W-V#-c!]AE#@9nZ(o'^#6(jSJ""2TOpf<
\1QE'Y-Dj/KfS,^p0GL(LrjCLJS/]AG`;Ai*Ps#/q;O:DjlkC+p&s9LAPb)"OqbYG)k9%f8@f
/,ObV7":=iH!FYKHkT/W46HD@P>qYsil:n,?>jqjKAP!@0>`\=(.P.tPLuY.tGaqbm"Q8=TY
@G.@*h>cXdu>kT?["s!hCKq5\Rd2K58iFXZVNfKO]AGa(p@XsKKL:'m7^D$M'E&s*Y=gHk<."
%6%@JErcd2H\pR07Mhqh!$eta:(2/Xq7HAur(Ti)I=E^#BWdQlNHY9`2-S';Fdofda.NS0'b
A*P@Y#c'bFB-\$0Is!Wn8u><S"uYTW1o;0Wo)jM1ls4"_?5e0:et6jQ#.N"@S#L^Dp?;2n#i
73>6>4,r^V-VdTK;C&l"`'jY"bpiVWgf4=SXE>ha6Ci4V_T!>81H]A_0rfJLeh^XP;D0nd=fY
>m)g>?O9>Q5S`+[8/'$3dre.6&P[1%2\6tHL4?m$2rXa0;UY@,Qr1l$DP#duP;UsghhQOZpW
*:scCjmOaH*e?)-MEn#e`L=cnP.I!%H2D)4#OePDa*WX[Pei(5<[Fk8?T@GoD22#?BS)\F[@
=^J$=$2=(oU;<aP>nA4!,G_c<;``p-Jg'+t7tVf]A`p'Us;\BR",DHRA3aLP&m8WDp=nKM*]AF
U#>(TXM8U7^!5,JZYAh:`o"Pl$*XH]A.?(NhMb_!L(9(I(YZA']A_Ed!9GrWT`%"CQ=R5Fn#6$
2D[DWPI4\%a,";oq+M:@S)j0H]Aj+9$>]Ac-5oDP96A]AMl^--ee@7t"WWQ$\nU.g\BlLL\+r2?
f\TpP#NPHV/H,T'7Nl$c*FNS?.O`&V`64<&BdM<qI'-N3s^OQO]A@@dW_]AcHPbD)?8_OnuJ!D
mp$Mju/.B9k]AFLgr0cVHc6<UG_MXY+GF"/k),5ZGn\#D'#l[`psJEr9pBK6Z69Oram::-F[r
A8=td;R?pP9IIBB,Cf&<c\:#eaKe>2"Mr>!'pLU%JUrk;GWGO'[ig]Ao#dco%8&$No5C(jBJ\
+K)C9XTr9s'Ns0o&WR`jheY+fV9-?spbAM)q$)^R=>R^h[L.ck=;XNa[U,J`b=0]A+#'J>ke&
D;'jmF;o:fBA>$[6ANg#o9o=R,2/I2N<bo_Jl\B\CY=1EY?>R<FBm&JhRHAJXX9MSJo"/)'"
U\W3*2dOgJR6>Nd?gISt2BBU'+e]A]A2lQ(2G^dQ\%:-Z2!4rIH=t>VY8N!ih?ZF<^Q7(u0-#A
Z%7QXV0nGK8?TZ>12b@/3Le8kV6T/=^\eo'GdNrYA=Qup'Xb3]A1I#Jj1)\WbnU!B#Gc_-"+[
o0HK%@]Af/#RV[sL_g.XnPb:s[G4h*jitilk8'VXtI@7H_d!]Aq_J_nXLhj;uY>MDiG&D_#5g`
7mF!L?GbnCn.#=nrgJ9pRYqlliu$'L[\Kad9EEoTn?X2@Jp*sfdJ@raYs0NGp=05Nd/kXr\]A
8@V;dO'XGJM2V&R;K_.:7iSl?Z#/"lf\!%,Z@fVpoT^f"s<>2'=%=rPKKlCVKFI:o^*YG<#7
d@F->C"3oZ_>W@.k<a8rNA#o7P]AP*hZH^0jSP?X`0O=U>IME&*UY43T<EssI;"ocf9(g5JHd
UWm2=Rg-P]AjCPs%ih0!)54CVV`FsOh-STRarsM!n4(SAS(uXQQD#]A)4Ecp=Oq!<IK<Yi>54!
OI$D*dg"obejS<Oc.0<U@p63stoqmM%B4s[56_r*,k#cq"]AJV_s#B3uE!rnJE:>_pO8'N=e3
7#e@K(2L\+6h$g`LprnA=Y,]AT4fY+Fka5PG,>H2N\\%WIjX7S71(63&q\RIn?LJjePPYUH<`
RJr0@Y4QCXH-^$BlNMea2R-bYLVT.I?cBFSVorb%EVon*YUY*+SbOT[io8phPiF;d$4gqnM5
/<4Xo_iR[[`s"))>)3;R.=c,Y#om@<G=:`01`WE"6\^o7sdL2lV810p(kP(+Xauc::CK3]At;
!KG+*ZXOZ6b"orn(H\<*bT7ID/oS$fL0DjY%FpCgP3X.V(NJ`o'Q=SqX'%@Kn"5&S+)c9rK!
ik4D+>Uh,M5JY^tYgR\q8#lN"-,.Mde,_0^_>e\0S%?*=e*d+;o3>@`_ciLG\4*%/R)60'rV
_7pW/,kmHL=kE@`/fRO@gaArud.V-tK1#g9[\3_9R;Smnr5eSFeD]A8:pR[MUJTm8hF<>d3`T
Z-+]AFOLjrIWgm3D+dNSLrVI;ju'sRT*\K(i\O-:gs+&5Pm:Xn'ENZY=?&ii/LW;0s`:ppMf#
F-i::bO2%&j]A9cl$4hVq%9t/%>82cD+J_[a45*M0j3Nu?t'[U*F@\<X//e,"O0DG&jM)YarO
aI#kZd3\oe5/XY'j%I0&o2j:3,%e]A(7+0VSs#7WBkD[CBL?kPqg<D0j1]Am;mfm1mG9]A_lk\%
+cPp''):Ofpf1q;B([QcU>5O$0P.EiH<k;F+JMhJ;*or[ajPD:>!N[EZ^:[dCDh\4M3Fm8B_
:a0rYWe2Uk!jP!_BXUb%f^MA1VJop8rP%=`A%nmBm"XrMFcI(&R;m'J'?D0Z+oTfQs%m@P;g
&r9:?XmR.-chQe6pbF*\XX0fKmDf4)1g60_:,X[g=<^^URl5Km^/8?g\9N"5ie/7LJI$A?#_
g`YdYk[lp5B$/f+<qp`1*$7&PPk'[!7)Lcl>gd_5:Bs@-8;@3:(ft%Z\%I%D"D8Q(JImsm#`
0tnM:+<\3*t>MO0sIojO#KGY,Y3A:OBIMK>ja2BQ-)BN)]A?NHmYq9E<E<Igqe:s=T>]Alq0=>
6kcbJp=M6+6Nj(/j6ns$f\<V6m9q.<1N7?i7W$3,+`*!6'eKQ6VE=Fj8D=8_t%O`JLOPQ>s:
iMp!R[85i<"XOUPc#J6gMEm`>jW1=3r:s)*8kA#\pTJ$CUNa!MP=Y0ao6Pn]A%_=:#Df/'(;0
%GUT.GN'Khd06$<-V1&=$`-(]ANTW+"S_)r7<u<&*at#kn3F!NsK6#))ltP;5>LOa<);]ACRV<
V<??[+"*SQS/a[B]Ac"O1F&,2.bZ+&0ear7YHs$MOrnSm^UmEWoW]A4?>BR(aOsQ5D<%'-#f"q
c^\n/8kMNZ$SoHf1mCQd<g*'A2Z__\"2+'=0L4NOmc'BoZo_3f^WCF#Hk09%GSY*3Y(:dT_n
Y$8Ns[g5nq!!(K2=WeB^?;;Js2<+%-?_\@Z2PD&j5i?;6ZbT=jqW@e4.o)hb8]ARq@#MSc6@,
>M45?V_<,+VAe3C6]A9B<^7t,Zd'/LoD=$L#<(JkkXP]A0pD8ne0[=LeALIcQl>fp)UcLGM-<X
d\bq)L-<N/!`PQP^P,HLEqWKV7Ba,YT>;1'a#tBm&\,IR=Vc<^L.0/Yinm="+r4rVF=40C/f
p6?>s\UHA6(rQqs^F9/4dpo]AUom3h^2W*bmQrV$j]AL"[*G5Pa@qnJ[6qAI#UBh\Nd-rW(%DI
tVBbmJ&#(`2W)3ba%fT]AAu)Zf=#uberOP,]A>=MqJ*F6f1!anhT;_?bdjFN/[I)4AS$q*N;L.
srfMYep"`.ba+.MV3/JQ(F@<b[+lY\PF+0)Cj]Af^DK]AiV$^k6FZOqF=sfRN\E;2o+hD5RGiY
a+q<fH!`5K\M35!\Mf65fghS`FgYSH]Ab]AY,nS2H]Al!Mi<'`O06i19@ks'2-:-eb>MY0pTXme
Uj.^Wrbd9"3^(nkM$b^$<V/9[+*HhFdZGF/-K#&/0.e<Yn5bcP*D@D&%3B1#Q;OC[,GOS,6.
7Wp:osAj]AXnhEkKHkFJ!j2Z+d:Sd#GMpAoF(aEfuTk7>_D=e)?#k+@e/R&M`hZH(9;<-[0/T
nr)e7W3PFBu$mg8cQjXX)B)\=KrdY!@05e%FS%Jl`6sp'`^Ge7b<9+75c>LI;1_2!M[eZpB6
M?[M>pR]A_B0GWQYDY)^,CArg@BmaAQ@l+jmU,+[_l+mi#l@\-Y#A0F@dZ]ASH'3ojGFc"^^j"
,C5<PqB_*epE"nrHO'gM_W8t+2eefg/ua.sqeco@+1W&"T1"TKm&W%\1u:>D!Ug)lqoepInH
")DnE5R([7;/[K,-)(5iLHrL!!3b5Q#$3-++3^iV;]Ali@2EZl91OYJ#7)!`TsG(r!o$QY6Q`
Ge=V'QhYd0;1asP:6"6iuASXe%iZk4D.NuCX##+XG_[p_^!jmHKm>+UDiK"ZfJe#<$^krKS4
V]Aec[6W$ArAVc/IpJ>'ID3ZH[&p@+^=f?n@Q//Hm0-?Gc+>FPa6$Ob]A'Bn/?,Q0Y.JfX\ldJ
BMOrf-ZZbb*.i8'FYU?-S\\g7RnZ$HC"4R=uo_6iP"5L6I8X!9fHd,"q4XD_?o$?h;O6_C@D
MffOIo6i-mZq]A/PU@jKJW=K,2V2SD!pH=+R*Zu]A;Cu0oqpQHUE]Aa!2cZ7$tnHp&_JjgZG*%o
bb8:3+gR8!BU^D"g:?`q)I<64D/1dGS8X>d#%/dEiNNN6CY"m;X<9,ZCDd\Kg5)<2jGVUt?7
hK[l@tU/\(L0SToCphD$bU@%S-[.Yt22(M6'Pd'KX`Qkb7M(RDm4HFt#&m-lT08>W`l%L,8`
R'7rMZKGX7t#1ZhC'hJOhP]Aipmfg0bSGWgE(76=ksr]AG`9;To!C/mBr#V0l50KDigDMqBLdl
q8p*Z9%P*4.f[l\57#GjPX5-Ytis7LCt<uYcc-pHdo)#U%K]A/r2^Qgp;C,&WHb4Z0%gj(^C)
WnERk:<!KCmHlaKWqHq:3S(l)aSWm*`cZ[[(+d`g^FgrH*;M`R<oSd'Y!(7&*&4&a8@@3O\@
#NAq<^S=0O[Ed9oG/QcR/7d&KQqsZlpc%5"?`Y?+$Vd#k@0DH*p`,_:I3)W;pa]A_#JHfKcMF
K?!UL&Il['g?/J?Sjo"-nKV3saMD(&q\uVeN*?A@H39JQL?7P`*9-b!Aq`H+(0&)fu$e[(/)
L\"*HPETQ-=.<C^s!e%P\R:XmSCq`dV.%H&*/R<"0hKnb::1e<R!V*PE'9n*V4/@1ZYs[d%4
nMlsnjZ&k^ki)?[bWGT`9\pCou("a1@1bi/0qXBSRgbo`1Tm+:kMk:YES#OLVqO?F3JE^bHu
D/j"spYr$((7XeGCZuM2aSq&A$qN]AO!kQC^d)pNUr-@DV=87]A#B$CM*R5O/>>aMt#i5&tH9[
m$=Tq7l/mt*Rd[M]A5Uo."QMK#ds3+QLm2EL378h5T]A8p4OYXhn$H$N+t;J9?]ACI+V&[%fQZh
a94;%lJP456IdSa^Bbrr7re<s"@Mj"sKnH:AbFlB`!h]A5X3.N>_OFs!:a^B:+iE#=C#.g\;)
]AK^=\^JRCIJHO2)3hb#($8tipi6iUTh)_78O(pH9/f!Cc8%^,2cYpB?FA%T^BP'HA_\P=Z,=
[$TA4S-R1X#='YT%BFA&=G\r:D8*'JC[7H[eMq.BJ4H*jt24=/o,T0[H/ejC+5SNU:d5AK2o
)o"YCL#qH\0'o;6Sk=jKk4eu9EiAmaIJ=3^[-_N[L&cc%(Kp-jkkdh,FB9iL3=0([;m>(sC_
3=V&)`I>.]Ams=>cG8A2L<]A_JR[[RK(e"l471s:F:=hHZ'5lb%7'h`\Z:GYo<gi<Q$6Xi^f66
<mHt.B(@@/_mVp`K51Z-6QKG.J1)I?:!,.C8l)#&ebOsm9c7D11YS=h_mM!F<4B"NW4)WlTG
<cmMdrq"7%_<\Af>TObD!W1m&dd]At$tnt+'q[T6%94bmLS/!5MG>l!84VX-qob5N3J6[lFV]A
I'Ic5U,`8N3>V\T]AjpCI>Sa8!$H.B;2$0L+VkrID;C]AnNCA?&_h#:MV:938UGk!eA%n`,;k2
ZqO7mZ*th7,MGV(M6:pU1b;uA"QdiJ[B]AWc88$Pl&YHBGo%\imRln\:1G't<,DuB$L8Qj052
5(A#'=Xk6T[OAmTb!-N^>0<0UWGN8O0P&THhoo-_VCgN1=["BlWF#0fq7n)r5$n,L>-j#p/u
O^!%#:7ek,'Y_,Bq3cYrM3R/3*$ZNkK69b6T,H_+;+G[G,\"hu6?1)JDdQ;OXL'eP5p8MlB]A
),,llGTA/[=TdaZ*;Mc4NM-@L@l2"$U.ik"S0-:CZFIhDMk#/WQ*'c&2EhgMZ)4X41NBjBSZ
$2=V+/'@)30Pk_RrT5Q_tJg="s=@>04r..N'O;4JiUn?Rn$D*@qhLD=u'%cl.GMLnr:]ADei1
)&S;R3J0+XV#,l.iTRB>ZIVcUAHIp@J+@s;MVgRITT!dhMlbu>CY$<6.X\D]Aj#2;En]AV4WZX
spoCH1FcBtUUZ]ARFANZSc^Da=I83f=).7Ot+m1.Oot!HKoB4q9#;Rg`89p-GarKbldhS)s-o
o3cRrZQ?ouA3JnF3n"kV.mj8`&br$uu@#%NWXuT0?Ya<8NWnmrOb`U@Gp38(eJASZ9hg5BB9
?L"`"IR6"hlmGDe0ioV9T5Jr_I,'+/A%e\b,VI/2m+2a0=%9ZmPL9tghEpRng'4XSEZUB[[S
?IQ#(\a^4XcW*V=t+^2eX>P1k#Kk5Z7ZQ8#2:[h:6ZT8CFi<l_<tRa:q3.%&YjS?ID4qa5+,
LO4k;&<EMAIL>+kL'X;#81[ia2\&1NTKQ?WKV$'=YYL$WLr35=8^?kn38;MS&,I.)1?\A?N
[)#GD&?"6a-"GX'g(fbr7l[W4]A=O%83fjWr@,mYA5XLr^'@A>#>tDkTYW;>+m^pg++0YZX5U
O#:/.:4r:^>q5sOUn.,Zhq/!%b$uFQc,,P5HOF-tK4kgl<>mOQqO7G&0gl\(61IM/XlB_t?k
KP&96N5NUR/=Ob_\;:cCI\M)cN,ql"Nf):I1_9%J),87jEfhG]AL*Dbmj[5AkHq]Asg\N<4"6+
T)Fa\Zr!?RS^ICS$2biHf=&lmubEl>Q1n>^E:i.$5urX9/=Rd2pr4fnqf.B&&^D'A5pMpCFk
$Y-/q&q=hq'TImJ2a,P^4O1M_9t=O)1)rD!`SR5%Ts6_UYD+--rRtR!.PQR]A2f#=&"SnRq_/
gN&BFXeJSUkq[i1Gb8pM;b2TCaLDg@K`7Rh9i,QQ`k%fVmTq5h/'h^la,dm9]A".-]AXeL)MOa
AT<<YGdC!3F6@BQbW3V[qcAMmT."^u.XAC.=Sa+`ud^;(d9;.l2h0Wb\EQ\5';*WT_HHCf0?
;h<M#t+M86>&0$NUIhKN+_[ZQ\C,^@R\%]A3GpC]Al/EcA=J%[E0MW-H^j>ROQI2=+bB9aGVMD
>3"='L,eYd02OL$^Wb.RdFSAa@ro&*YZR0,AkBYXH..mobH-^muJ.B-niH<^:^D\/45l%h`W
i&9\-N7F8$<O;eQMKDh8d!=r8luGbNQ#ri)lKo`!+EncoXnXR;5R:lYJ$&,gT<S5t=>,s3@i
'L`qGdehp!KCAG$>%7"V]Aec9@JKM`sn;6$Yr+p@r%Nu-+d0G8n)M*jKQ()>jHpDr)c<>J1A4
&OOTK!DXV@;@2B<Wo!io@)NYB@IX-)RX_S@q`@l?`Co?0qM*'nCh$+'8lS9el3!;+UfFADYo
sd@aqN%G4`;0d'S#-L!Im\!<='/5c2PH!m2CP]A<je]A0s^&p,)Z29Q3ORS#7)qQK<D1LD&BX)
SPc$1*!.3bj%J#go6"I)S.+$?m;e^E)h?qW_R&spQIH4nY)V-fM^8Q.!:b/gM#q15$C:NNhk
"=@^^ld_!N/:qdO`QJFkB]Af%0q_k0L_G.u<!dYFM%MMsr;oJd6S<OL,bR"-i@)O-J5kun[pu
g<D9Pe9OV2P"F`qtTi?4h68/M4*,9lmZrl)MTcHI&HHUd5pTd/fuiE%t3EY?NrNi1GX5Af/+
hQbD3o1Ffm.B/)d-+H5.B\Fm__B_;Cq2b5`)'3JD]A3m)SJE!7#/o;`Fro^B_BH/Xf(0NWBG1
!.\c*_MDRg37`DhA0Qger)\jh8-h-fi-Wa`k7gK>*5"944N;8/:R?c\<jN"@k[Q5m%4[j01^
[,qPCXBl<_<-PW55>)a<nSbp>2A[p4NHP&3MbZ\7'Clpamo[AcDt7f^)rSuUi$fS,?W`"^I@
hiOC-M)i'gN3OM&eU%+!1#Q2C>H@10"`orEIJ'Z1&aOVB(Df$e#TF[!d(>V>e9(gQ6k4i\B!
)TgZfE9G-F]A`b7,TX$!gm4C6AU3)Y#f,FIXDW[ri%>iWXm@VGo+DV\j\02k"M4TNX,A/Y`dm
jnZ[_&F&O$XL$+_p_H."M6U!$Y":b@k72mZtl:tF"S^(h(`D<^?4Pu!*<ji)!@a/e'pkR3sj
15IYhB*6,[#jN&.F6<JQJF-l9+N(9H>?%N-mL?-`#L\eQ'[5>U_Ibi^Bt_IW7+!g#JP&YrQe
W#hul&mLDDJLf=']AeJ[q@,o7=#.j/TB+6Q9D1qWZJ,C2Lt5hO9Y6k",WQ+al9V0G!Jl[]Ar+[
Xeuuo?le$_J#![A@ctY&ZFX33C:L'bn^P3Jj_e]Ahp7B/-JTE-<TkkB,mVafM,_pWTD=!fs@j
m4D2jgHeqU$L-Ug!A5[BN$OI!\Zt%hD/l(NsnM>%*\@]AK_:kiUGj:1\<EMAIL>%,@!X%^RU^
_:-WXBXV_95g-?2WQJ03F0XIrnI#f`H36!@j)5q]A927OU*UYJ9-[*GX!V9]A3aHOd@m-[FL8g
MdgaLIL)p@6\[fUou(bdQMk?XuLUH)dumQZ_ZBoi4EIHog_4gA>]A#R3=Sk\rOmgSHAe&Ai'!
%nhXq(:gRa(k3GD*X7lO^upf/54518Ihje#YYkD2"11,\12%LsaD.?sS@XL$eGPp%?$Jq;L.
CAX:;9?fK7ob=*F%fmI?:i\_5&n:jNU@W'_SuT,`hKY$ZO[WCTHH=Z)L!uk+%]A$J9HbfKcGc
&#-/^5MSSrR)GD$eT9GnjiCQdQ%.#@aoi[),E(V1rJ.rTTr%%4o)aMRaj7F;LTCp?Olcc?r`
c*V?=L2_qhCoWQZG[[(]Ab;O1kN7X$*RF&RC/mmA4pn>C_!9en"u*ks6fY6^slCZ.gN-tp0@k
NL'VK8s6nI`$G]Ado`?fSY%)(jH-!MDlX+LFd`5J-f?m9/a'lbnDSHIQ10Sc3jK)=F_P0amiB
$g$jSs<XJ/.RJJl=r+XJ[ZZo\Q#Th!Y!AP.1g+U2"FRbL;.Fk_R(\W3A;8q8/7.r@L=r&NuO
%O\D%NXj,ZlgGR"(![3l`+t#/ZG&__e#)Omjp$![jtRqtfAIIr'<@q;"1:l\`MY/kdcM*r.q
d(VH4T4hLlgCn%@nbDP0"lBp0.=B[&?\U`CbkS\pfB(eS$L(f&_\]A%FXY`Ut_L4X'2CdQ<Pk
]AS+$6:UEs_q]A$LZ=2mbZ+AFA@*c#2UNTD;LRGI/tL@"^=74UICi"mJ4)^W0'mGsRAph;Y`An
^HmfC+D"d$+8IIG+[)%`mV1V-+'7Vlbe]ADR25rW9,k"ga9S.Sn#Y!JKqqm[Y'`9Gb]A!cg%5T
RG-_K>cS.\6A(;af_qKu#+@UTLd-!u:./rMdShhjAbqJ0U0ME)>M11/BhBWdL;nJlk\q>HS"
l<:'r,t$ga-qS@m7c3eS\7jiT&Fa'["9a1Ocah8o/MH37(%Z"/QaXlGGG5T[F)97r(+'^A1O
n&lMGbXj!6>1nRD9(cMdh9rfHu:A#hZjM3rM]AL?+cVCr2oSY1#%1`FQ@a?R\AU^^:Yr2j37+
\Q;+OfN(dqB)I"EV;r<M^"#E)'4a1(l_n-TI`$<@Qn`fNmg,c#3J#P-jl_LW`h=2T]AL]A4]A!E
TG\RTmZ$'GdWZ^M=kb/5X$,@0HEEANO?-NMOSaLPTg?9?8-2&ZL?n1@H!OMajEmXkg(-hI/@
&C![dD=,IAsRK^nG!I%t$@ClGbGnT[%Xpi(E3+0-ioe]Ant-?;-,nrOTSO'R!#:h@Tjqf)AX6
P)P?+d@U5h2'MdI;\@R,X6cYk]AKGC7`%Zb9+8A%,[Xbgtl2mBW%urZqq.d0Mf:Qh!A<FgGPn
_>]ARZeh]A9P0`1\`Jaudg#ua>b)HIm6@aYkW,JF!S-1'7<e#*0O<1dO@sJ.@WC?g"APASO1`7
E\nm2*FM02QO8ST-Roa7ON_hei8tA82-B-TCI]Ahp'?hufUru[I@NHa&U\jn--b$2trmO=a"/
YAdUY@)%g/pc<a.h2sT@#I\<)_U?e%;mTR<5\W-%07l*B;_UYeH!si4;nJQq/5"g&U/+JVc]A
jj6r^:aqSE@\Tru`*$o.D6`U=<J'B>.fW#IcZ'g"YT@ZV+CkjT)`p'%3=LcJs.kLG9ei)t?R
I#P91O50+t+Hq'g&Zai)Tg?SU02\s%`%83W$fOuF<SA\.M/)BB/BQ(FM#91'U?uEe>Nr7J;9
ra1i;J7l[1OL"[qYS#rOAQc@?;-p0"&Db)AsF70S,bu/[10V_t1t=.-qcdE?f)$b$e)$!GfT
c$7?98.XBmTP6<UV`c9AsZ39)g3RSqhcJ%H$Ypo1GDo/2uEK=EUGLp7^!t5-NH$!=kk/eVef
h?[_J@lDIPVk?>ZBG_\<`WOES5SCsSK9gBB4kl?H-Oeom)4IffCo6'gI$'i:RH?.M`BdC(3u
,S?L+mXJQ@-ibO."cHlMRSAZee0=/rOVNR7!eGOOD%BOe_'LFF_V*0>3ubkN@<b(7Da,fFIf
%,.n8[>#gpR)\u;]A*BOFb<<gr2^N-]AQ;gm/EFl8H$4]A/MpS98*FE>0W91n4*ioU<0$-XK1rm
(u2(Db.3I\hAc!qO/pbR+aYAFuqfRD"TPZ%+p!D7i$3S/A@K*RjD.fin^^%."J3nR.]AJPXoM
4lB(A[4?Hro]AYd>5H-IC6!E[2?_+-AN04(>+ge;:_7:?jl^qa[@oq&j^f$8>9bi+;RH(^1CL
3Zj,3URk,*,N=c:K1UA4PU9Im_"i#A3"oqea3S*jfZ,I6GG&bXOfos[+Bc-,F6%X2O9+bh&$
ed-,Zh'r.87K-lg#hH'pI^0OW)\7L6j9W&(fsm*/7]A_oN@E!NWj8lncGc!+AK2kdqf00[X@(
ji_aKhWTZ6Ih/9=NLes_PDZtl>i)B?Ddi;M6N+EG.UJUhf`M06BqUX$S'[3jeSe77EG5dT'B
58&_;$!*$*:D,0ZKO$MisaWcDHNYOTA(Rcf6iN+R$&XT+i[7PUhnjRiR!9?.]A/YHm.j@`!2`
o0M;$)#)7p?4f2lU7OPNTmH.+d5*brgT/Q?Q@6^g<U+g)bWj+Dsha2NI6r(MY4Z:PZU`*I4g
mDnPn0cN_U0F&R\MkM>IOKb7j?85#n4([k[gnIW;_hMI/,">mTi-/>@).^RRb;je^]A1nh7G2
kTLU[><lhC$4'Ec<d#7G`tf6"&=e01)K=_Mo--q_K/d'9lMZaj:I+9_MG`$6*X,ZTasfh,P2
fpTQ3TF#Q`n(T:['_$r#)pJaWhNgV+/3Laro!qIM2iB/S$1oqZLV>fR[VJti]Ah'IEc"R(-j4
&)&i8@;b+GY*p\)f/]A+J&&9VF!rM-u+Q-1IRQP;0@YNP%u;ZZ9="oWgJ9Yf[L._@g42DGq+>
?f8Mgd?l+F5olafJPI(U"+M*<>8*&X2[0Z]A<(1'[L+9eCL.H^C/]Al@1IDVe*dpX\ZWV^JAP:
Thr;c@k5h]A=T)i>m._7.(3X2XLk"/?0>5IN>jfUV71W$FP5.gSS@_mBKVnj8[?r>84E.I]Ada
\&M*X?I.-b%)hfl2@pmbc[.EotNUj]AJm38o+sG87`tXe?\.H[U((jo(SrDQc.*Js\QN\]ApRB
OI$K+)f51!,7@8d@^4ss8+m<f%SKo;+%LP.ktg_u$94nr]AW;#sCX\Y"*+1a*Sn&1,QI<7ra'
.M"V8V*b6#VYWb:YR`D0%Y^2Qt%JPQ=liUl!;Y7m=+IE$gZ93i&!TN&JeYL(8Xi^f@rJ:\)H
gfUZ<u)$nf,L'P!`a7K^`/(@!87$/#)=2N#>-s4`GnQQDE'j0@H.`Va!0hTc]AR%-imfG,4&f
u=U=(%lhEU#SnFJ5_gD?05[(S(D7+mq$8]AkZ[Ue=0>fmQE=VQ'Z<M@`D[(d0.(E\Op@'@R)@
_7&EZpFa>$qWdAUV\aVkU']A;t:FP*F=&I:-rUlj_B:T#s/**8a$7Qfqq/c@AifT[Sa/]Ahu+5
5O['KX;Wf4SO(&'"1UG.6B\gNO_ZP1JD#IjT[W)*^]A;oPi[PNPcSt\ZFp*9:3+LiMfB6T81:
[03_fiMb\87@cZS\M[XK:1EeQMq4/@?"tDr9-Nko$mePU%U[V0^AliHB6B"Zu*]A+2M6HG?&r
Im^G:n[9iS%Sl4u*(Zj-Y$T$-:E?D__ZZNW,*Vc4qOV`9/*BH.)#,`4peo4./DX'm%(P;Nt1
Ac,?[7)9je)[>6rKbQMUE<N#n]Ad'6]Ae3`PS;)(WE,]A.3Jt6<[Xb")QDrrFk'mSH0`f!)FI9o
#&d!AR:W97gtbr.S6KmG=n4>1skl,GDPBQ.!h'u0f]APTVG&f)<sOR3gl:FoMVESSY'hYl.]A%
m.=FjJf!37Jg*e-hVp:+`<Pq`a\A>,Ien?8-t1#b27[/[WYR"9c9cppnDb3)Xen'jdX+>=Vd
u!cT^7R*a`2X'IRjWOdW=2@hC/sKg0t"^L>%to>W<FV$`Mq-0:YD,$A#3Q2]AEA`f$#4hj??_
=Uj+ldr&F(h>bU2RDO)]A`ZEc6d]Aj'+Kl`b]Ajd@h(nq_]A&Q*DX,+;c?W4XGIF<_V`\fAaF'T,
:"n0R8p;"Y(R=)]A(\$/+pY&F9HZ.Q'IUNtlJ:5s>WoQ`fPY$Tojj9Ar(ss,Yed&EBU`V7ItW
F[?j&u=*F_8P-@6N('19@,(A!1bV<H^gP<*%+84A*L16?MnoMft#)JL0/*Gom=g-8\IF,O.c
#g_u[3oEe,DIG?>>R8F(Oh1Mrgsq\]AVI99[,_36fYt:5b8Ck?n+2NhrM=;_e)>QkABjW&ML[
?9<Q1611>(k\M3i9\&CI@LJ5co1ecYi=q'=p.^#!7q1W:B+&5]AUos7S3Z#0!-j6e%hZ:<CWe
JgFkLsB04<6B:9ZlOgKElS2?E(%]AJ;Sd9!'q-X^lFr]A(^'>s1la!XV5T&c66E1&Ai3[\l[GV
*<()@f\M/&qXbSoB?miPoth'#$_np80-lU>@&<0>qTNedh&B2(q:S8#*0[mo`5<VmRqQrg>:
,&k4SQ*b\1*bjo%LGRGB^GRi]AARYW:@D:u")N,<6?ns)!f-k6$'FWZ4a@CoducEl_A>^:(AU
e`Sb:6F/-^f1_1iVH+5'&'4CE0:UTeQ8ZnWlF.;Ro9bq(l<Z'VI1ZP^^AL/iDeouQbnr%#UE
gO/3_q^HfakiXUQ*_2\MMaXlJqaLEuFW[$(5H2r;a/[\sZ!Vmd>;G$HB"sDD8o_1=%?Obe1a
2S3W]AqjkJp.s*f%`6.eYdTIuMjL<pJ3bLUiW=ubTHh;-k^;>NlW"NG6SOWKB50C6L.+a[AbQ
9TC/09hqGP+0&2b3hG,G,_14ije^Vmm<Um9@PpE%Xl*/dZ/i";0Ra(p$(gR%=+RbV2d+1_:u
)p2M^Ae#7U9PV$!D79FL]AECW4t9p6<eaO>XH70J$=f(aYO?pLo\&<^L<,Vd&)Ji@i(-oUktr
\ufR%GpMhkDfHCfOX^6uk5tMYYiX$DK*PsmD3X/;"TO<LLl1odNPn0hN"-t^K;7$Q&f^slWI
-<PkZUH`L1I*J`L'/J5Oa`WP-@G!&=1uSEKcT"D-%;"HbUL?QmbOeM"I0f/N=+"P7ZMM7sT#
pgkhpR>gIBLi:"os[r&r:>eAb*kBL^M-5q3q+Js4nh8kpPOOjj`L!bjrfT$5Z%T^7.g_g47<
_I(n4tN3d(ZAc!2b94B%l[SAC5J\Wqqm!J'EE-HG3iE>0:hh`ip*K&T6tF$Fr-,TGt$,q!bm
-la1'?+!eD(m@rJZT_F^e;\B&$/i2qIXq[B-uQgDjXaHZ>'VMn<US:djb`u45]Ahm-9.1D:T$
-@"u".CM8cbo-@4ef0O$>.F7C90Q:]AUYTQ01\.k,`0_H?klSa>4L&CU&s0;Pn>a-L!OX^$8#
]Ad&@A60"arC',3L1S%WlGUcic4TX]A91"MM-^_\Pk68$55/dW7/'nnqu5iUob\0#O#J5s,MbT
lW'LKGe@k1=K^?_T1ZFZ`kO1L;Nafr,hb-.0)AapipJk5!F*saooEK[B"+0`dGlu\Ei@mW$p
JfIsS=]ADmS\+:3-GjZ,$HB\W)HPha7bto?$6t'm*Q#eAf5_>G1tB7##/Fc.[!d;$Rf)90D8^
1`HGG:a#86%Em5$dPB6g7Ym82q85Ft#]AjCoCQIcAf:j9oH`b[@1>-"9%oeZ^DWS7V`@O5]A['
ALu:mdh!rrM>-@]Ar,KKZNeD!*jJ,>,/V_>I]Ar7);@l@Wb;L,$j>BPbsj8,PpUM5mV28e9qVW
>FBi<\1rpeY0EC>)3?;)'_get%=c_f=6YA)d80[I&*-J.o\^c[&%LRum,E_r+*E?<kP:\k;T
-\:*2$7j[-r4!)0/s/*UV,:)!paj4"Q5"<04Cob`A^U_^oj=M,28cpdp0Un'8>,(KdL#dL_6
*dBT7OO"cV5u1#ZTRqC54b_Q]A22LJ/-[nDkh+FVs5UM)9L-7-dku4sL:&PDg4Ap3mj50O3//
82+Z,9^B`\sX:<IR\S?nb#V/)&H','Db"Q/kd8R&gn]AMLe%-P!$OYl4Op#UbLkjRWE-P3*#R
\4RQ"YdBQjYNBFdDQb$R$<rK*9du]AFk1c5-f^IG88jd\/HPiqg9MN^`,KVHjA6a%>>GRRdE?
_MIL@)hC:5E,^?Q\d<"#b=d>c+E)WC(Bt.gRpp*89+5AGYFsR@>2.E'06_ncT7rh\BZlaN&;
j!ihqM@pU,BIX!/KZ.]AC/]A'%hoB,a.nfb[*L)[hJ`9=0XM&tqFFf4,;JduZitJ9=SH<bW['S
JV[QII1n*O(97/>q@M'hi6'@`FFO6;=7Fn8NpG;pO-&*cq#pO.^6E3r!HY0RKAtm0MddY*Lp
i.`[I=HUHc&l5!Ff,oJ\I;kYp*b;u<TX:$Df>%K_Q[Q:b0%Rjl*F-Xt,2$%H+i]Aj(g1YfJ`X
dAlc4<9')c9e7.L-.k^ZMO^.F]Atsn1e.6fWC%I*oip[)jd05#,$#fV?<rV990sTBlH2;Cb:d
\2A%]AFDKkp"^Y712`&g[%foA@[@;'P3n\@36Q7+X`u&=,N$*_H[kD_-^odq,sFO'rbM7I'ZP
u<<@g?^W2qLF-E(9B^QF.fbs:[p]A_s=LTWBqYag_TqH#i8+H#XY0`sKBC?C-J.?9]A%,bUop:
M\AhkfhWY-5M<;`XWiN\[WBkn=F8Fq8@?$Q9$a?_6ZNX657PDGRm17`E1Z&>cEY=Id(kaFI@
`HCDroK8;(,_9(1)1kUA'hp&I094,1DTF=2kcAdsDIL2GIH0$5"%g3Us*#Lp.<U$F?aC#Rki
[#mG/TpbV,/Fap/]AI0+_lo[7&"-E,/4g/fahM7cSE6YXc'8rYb."q0.C;o[imFp4'ZtBhNh.
kKWVZraSDj@K-*:lfL^\-d3j`5M0^Jaa4[!Z094[6)3c70NYqqS^:iQfY,PLt`:$oTGRq"ej
[O%3s2&"^-=T@4OeUo=Zn3ZQ"D&r3$fTLZ-jF#HmVXU3\NmS%$5i&^+[h7c\&8\W2VjG"u(b
7YSDQd9q'6qRI:Kg]AbD4kGhLXc)n;S*jo5*l^-Z'K$'Q80lVC`T,9\O4LbJailX@C/n860t0
<g!L.NLbQL=76^:Fm/urJ*kfotY^BKfH4VP\gd,b:=2ujX4=8!ro=RV-jU9:=@cchDDcam*O
%o>nT$4Fmq$L_:)=Xpfo(rsVb\)\)%R#[do7/@nR-\S+^R#p.njji$iG-Q1<qk>iIS_`@8PO
+g3SXhq)&U1T8^U"&FVe%3Eo-`H0Umo4<?Kohs`DqNX$lDS6Js!js_F^8JBFstb$"JK[a9&3
Fo\Fj!o.JWf#9%8YhV$k)BP:M=`N(NuhYo+t"''0*Xc).FjYjg:8mU/4ZU7*]AW;H/g!_$V=C
FPloH\a$&n(USaJ=Xp5?-Y>am-_]A)*Z9tEEL7*')6+Za(^hsT.`2P?kN_YA(B9MG;=j[F=)=
-EXR*+#ZKHi_7WEnO=O=L0E&s.Z)Id6YV-oGo.'k?^37u*r(n3ChZ-CKD*@')t%KfO0b@%&?
PN:F'c\a+Bg:'_F+@-e#jm<9?s)mO,#bQ2>_:7$TUo+7S1a"`D]AODU#cu\1P?uFc0i?7J>/L
t%&/7/N&`VA9Pa'(+Y#C[a4).C$(Lbl1G^2FZp:B5`P8(?K36CRu]A]APDeRa[@/`:_<3*2h2"
S\"CumCI/;4?(@MPp)WQINp>5'LH5F37)E$;d[g[e7_It\J&!0aZI;4h4qgD$,<crW.YMB9%
CCt$l';0K>L6c-`VP/tK]ATf[&iVE6j*j^MfA<bj"T?CF]AfNJF>j\3Q"qP:26mGA0O@HDtb>(
(+!uG.4S"$Hoop"=>l>:5f8DJit)HWTY%2PL]A0ruW,@`ii%"(B_I)A9iK;p5R@`:YS%8."=a
GGR5iAS^@+HC(lUS-p'0)o"/9]A@^qi8S"6crQP:FU?B>%:<R$OSZaKd-q-kn4NLQCm(]AOG*c
PdVH#F@M4#?qTO,jiWG;1'g`F#`V1!FkK+89(W=g2F0CNNW^CMVm[MQC/_"=,MTKRV5nFpW#
)ALN=$Fhn9(U,pqPW:pI#WtiPAI@KWm&!a3Oh?T[mjtfGTZ2OL`@9*N;.=/-5l($RBWroqsY
N.)5J#0C0BJ;Nmb8Fi<4H7cHip-C.\?^j>HLPKW,tE;B3R(o_s)pSmRm93QkY^OaVQfUFJ6_
W(gOq;n51A)XV'8-(\LKQQ37Xh2-7;a<PVYT%)kRVnZ7LFHU<[jmja@4kBiq_K5:p!E2A+Q.
c#n.9Xb*78/VCa)T2rcsAA-jMJTXV'QR!@J'YXSK]ARq%L(.WShUmIb%\@')1]A1d=^C!`Q<mm
Mb":.h.;PR,"5\@D,*aW7_.0[O]AQc4p4tr`&/kQFKo8c_2NU,nK#PV[""9JM_72s(@IdfbmQ
MW`Itl0b-dr[Fm7[S-OG()_r'W6R#`LcgM'`?!_&EUlKe-+$BMlE+hOmq^!o!One/(B\aDLf
;q\TAkT#O>-SE5(GHrne-/_2j!=\Y.6+SqU6D@FGI:Gq>h*E^ZX4tSmie\rM?g`Q:[A09lW@
25'4<sQErk.>m!fnR",>AkB5I^;`#TYfq<)<EMAIL>\<0fV!<M%Fte+SM`90]Aq4-tHih
JUHR/fbBOC)+o9dQtto:Of0p*2d3Y3o^h[a9q(jeUG%?cIjbIe.*+6,k6t:0"5_D&odL_,qc
?]AKKNR>2+0[Bn,RE"/B'QO>V3ecl@\couGO*7'^ENI`Wqa<ul,LK0NoU5Gk92e"@E,FF]AUU#
)Z7ce]Am7k&mDPbNB`uP"SF%+dYr4So("W5h]ACcG_6j2&e7-c/IMf8D)V@3aX36usd[R\.%S[
0t,jf'7?B+P<B[/pdi:3a3@<(c^(+b>&5rmcb!=`$ij1'NpMm4T8!+,"Wb7=HL)s=tC_$&-B
HW*2%biQ0H^C)'rl"f2JnpXcaE<?2::A[osUnq^)Wq!6]A!9CDNfFLt=Ff>XL:7Jg$*$_n#ou
>E@65>uBrPKk6MU^R&.GIH#9>-6Y)]A$]AKAJfX!V4KeHB+Q5P?P*u^)'2!*)i:;<7R>4GiJTr
q9(C99QJD',8^)Ihikm*!t%EVtOjS/&uP/=T#+@1OG:f^+K:a_#amEk.3j*alJH5rU^&XK0a
Xgnk4gI":>0WMF\Om.R6iU,aiK(;X#lgI[YV$s8kbb`B:TnQ?0:HD@rdBf?g)LCf]AcF#`E-,
*(o*734q_]AFYSt(5l0@>+/Gh"Y]A&^ScRY>D<#PBaBWdL*:+D`)M)`XSPGa:L=!F-0#n-S`\K
VYNl)t4CZ@haKHT(kUK.t74%$Fj',C25%h7bB-TCg9FddkIjj$kTX7)p$Qc*T[nS]An]Ah$jZM
^3Eo@q.c,Kjp;gH+g&b@WSjWR*q(ROLrR/CmO^o'))`\jA'ht$+:DU,"NW.r9U2/f<49J=%s
l$O\]A%F>gm(tE7(AT.]Ab/1+-71hBQY(EU!o@l[DIZ_:_!4+9RP@UD#-8E]A!\coFK<oPqNKa;
V<&S,u&g/N&s)Y<[EDKi5I,>10P@fc4e#s0r=o2C!/66$-a<cugXm)k%W:DD-7I9'6Z>e:r!
/:Ol(YDSOp?J_%Pl<Ri?*QXDR&aMnH+7-I/ab$\h9M\VFYiP'.lmK7&Q4AGq(*\oOb/iNp8<
&!AET6fIKpg":MII`p!@"&+C).uL7<+j$,^!#e,E]A0D'*XrM4\n5BCKAUP<]A?(@aD3q$m<Fj
0`rDe%+mNX=0k@2)BEr`hrC0e-Y40?FoC3R-iQA7@Cmt<,KRsYrGtPueb4%E@"5:nhJm-*9C
.It\W.U^daf=1I,sqtS'XJC%idANEXV""3Lh@a^RB6cYB1aLZ.GB0:&#A1*r2=?DF_9Nrlp\
L;l<Vi@H><EU4B=4K8m#>L(J^oA64ON!SH.ShE$\%r@CcmBH_:]AT0oRCoj%!4#91.fR>gg+?
X%9\2s0qT^&ossQ\]AEA1GV'1@'cpP;;Yulj%jP&]Af*n7*)T<AYC5OBKJ5O6X=p&FZ2__LR@o
i7em6S"edi[,dh)F!e>8mcW+\jGhHG]AMS%#IpBVHCa%N`m?nDq.'h7)^_l7g#q]A.*s4r8542
#mG\3CDfjtiH^d]A91(%JH7HO2M#K<rN_R(A%^^lUj6@/^9BBL6D%j!5=PmAJ1P'\(PJ#!Q9-
lY!Ufil?>\l,J4BlQ@0_"L1O:lWd@D<muq-$ZoP&Q>%rtmUh&0$c$ZFpl3q-^&OrpP8q[-YF
f`bZMEXe%TAYZAp7-(PW*i#%%HXON-+Cd8M)-i=aA*?]A%7je9AtCn::irB7cNgi\F"]A<NFjI
0G@j$hIES&86:e2?$S]A@ZL77/Z*;m`R_ZXDqlFH6=68NfXk]A]A>$aC7:I6)mnhZ;55X<s8DI6
0-MU"gJfGA#fY+!e8/AYS`XGYG-qA2%h\ondDV4>C4pD7L"X8_`Ahc9_3<Q,ul67o]AFkJ-;J
bi[15Ck;NaNBgkd]AG`Cb[p)%QmV+Vj)f=1oGlRcV9FFb$V7R[>L(5jQGId;q^%qk?JS]Aru>W
)5a19QW%dBI&>_"`V.Lb&f^>E1CHQ'NLs=FO(Vq%p9_E"\7mZEBm3cGqL@.eDb[!D5!Mmabq
gZ,k(d3MFT0+IQ0Ds4'fEOTLWZ)T&1Zp4rXdh^m&$E.ddHJ<\!5/1oluR,]A[$i;CNNI7n,Pb
UAu,AN#$koj:D>1,Ck=pfR`p_WPnti4HX^n9&ne.B`WI1(%+"V2Y4X2V4aEbAaM!HKoj^ht?
g2gR(cPi==c-QY:^K_MTST!5D>VrqNIc!J\2C[0$fM:)^3;gq:l(E81C?EL+=@Qp'+-<K9J!
Fet7HP6`MknA0-_kMK7h*0h\*kPTS44GWp!>ZOeO^`#Sl%,ukD2)heH$q9#najUFPOW,_"b'
Tm2[k)l5n%6ZUK$+3#/Og$U35[D!_8krm%RH%ljZP7>Oas*ODZ([YZNY9'X;[R8g@@%W"1[^
NiR'Z_leJ4r=m#rCl,LpAX?#P\PILrUPd&Mo53(&<PWjSK'.O,N&&e(g,tKa/,7Tr^k?;OYB
p8Ak<\:t>4ZHssU-ef=+JJTne,9MJoELAtr!Rs+=7-%;3UiD<DH+@D^uEJf<HuEl*e;s^'\d
rU2j<?"4qbSfjIsd%$@S?$d9S;gQ43Q_\PX-dW>-r_BK,tGoIT6^Lu+6U1>:4?Sag*L51>f>
+.skb/+EQW&:etgbnkbP:0U#rIq.=N%>=a\Cl3_j4;`h6G4a&^M;T4;eUK:%P15oQf':o4eM
HX:L@BK/\?5g$%Uu*k]AWi.hfr!L1,OHM)3@2eP+FZ3Z[]Ae\o,g#73cHpN!Zr`EQ=/@tQiV.#
%rd#m1Cfs'pmuh/ZMq8Nk3PI8IC>64g9>:KK!/6\XfJHTW2T%rX^'ji;=U(CGeM!1XL^8_OU
@k!V6muuBEc9BMrEM%+T6K;1,\\e%CeR:UKkoW60)U]Asq`8;"W6">R;YVk%V^pD.'DcpLcee
T/k@SCt=V$<7#R;iMk/D@O?BSl0c;.%._,jqs,_1=3?efnDI_8q`P34Bk]ABQD.Pn*P:4ea,[
`o]A3Y'*#?R=AUA7chP+ls4aC&14LFs2s26LA=ld87N<c-91R3:g-K1<oRGSX+C)V($-A.`fR
<T$B!Ppin3a%pn%sL8U$4foUbLeR<;a=:8u=R)/6RFUfpjT@j<t`rK`5oHg$Sn[a3LkRSor+
=#IWG(QOBDK$1'(MU0lM2G-H`Z*Q%dC<UR.,J6Hjul?Ve3TK$ikp3,m&CV,*rcOpHi#J1WCo
D?+;&JN[]AArN0(q>2FF]AWa-RdrUuQ&7q&tYA_D2PVn-E]ArnUoZse7oY-DrrP'qi_%i%i_!*]A
lAre\M[mVX,Un(+\fErW@>9<.=D:'%@&;;CPlr(I:b.ACi!pNTX"3K_u3V7k9L%ImH,M(]Ar)
%,[#,rW7%3)c2Q:lme]A3K^nX)9>GT8LaW!l(X/_W9@XT9+Z:&n$`(f@hZNZ1a3Q=ia:c;WEe
";o6XZR/c!jE<W`?+LY8)<_-$'5l?<IIgk<5+^J6NofgImkn*0(D8/+M4YTkT73iabROq,9;
4.f^N2$]Aru2s5N`fBi`#EjDD;)Nh%L<-9;rdLpr_@?j*::L]Aa[Zjc]AdQ9iudODPf]Au=%l&C(
r`(oQ0>(#?C%,a)^!YkcbNnTaDA]Ajn+(V5g\f87!*,V"mJFJ6`fa,.`up`W$ff!FId%SkAGl
%i-12cWG1o]A/DJk8/)'2eb@ZN"!!Ffq<o.Rd[HD/:@=Nd>iB6Q5@=#s&FD#3!'3Fi37EbUtH
d!lFD)5Il!Dih2#,p*$>Z]A07Z9tWY+@s5nT!sp?F\`:]ABnOMn%.Rs25;umrh`YHO>g$oHG]AS
aNLIa-liGu5"d3':NMEUQEe&XAaPVI=a1A\%kGs++r4q1%&ocD[VkX^-g9Ll*2R!?7\0+s8"
6;,fqK6)50Y&h*kG2$=:fR^@l-PpV07PU&Qs5noDe"%$Zn'1*=8-j_/C"l9(BF3aOChJn&$E
FVCl=aAUMc^aTpn!_G-3]A1rM>uK2/WjDLW*+!!OUW/]AR,l<nlNnh%c'^fLF;H`CulugOBCe<
DJ[;XbX/n1-5Qi#&<W$X@\]Adng8\*5q&<EMAIL>[J@QinpQq>Q#c(hsn9O7%?7!r\4>iRlS
8?,Z7FL0o4,O9#73R31)\rJIEi%L:B4Y50,\SQ[u0/$L_MA$5Z!4IDa!BYhj5f\OaZ@;f'$!
3EK/GV[Y6d@R"H"o&IFV/+3#1YhWk'ilMI%/K3KD<?*L@HY4A_DsqBjt2[776538-qQUX3hj
d+Q)_0njg;f9(!+b"nTPbK6Z6lK6SbGo%@[\K@*_Y$^jQd!o<>NcRpU[$5+I$g`?d-31Z;s7
XC#l@/TU(Xdj[CZW^Pu=*G.3)HGFET0/8hGr#APqC(?V!T6)8StVj;#<ilI-r1OP%C":%5D9
b2RF!q:)/\ATNWkL[b'(Y)?+Yi0SD:k>gJj]AKn[)FN1g.uZ%G3./b+M]A=pmT&!p,b>X!OVbR
XO;:Hn4eW!PN#?IL83H_+'RP%K1DB"-5PsF#73Z]A\O%9-##\`el#c%CP+[0-\kb>M^cQc.*(
g`C&G?J;W$Y^3"0-"lkj!lHe($d:b.SKGWA^F''NhO(m*,+/\hfF$%72&m'Z%s3Z=+a1h7$f
8*gTs4WB6C<5F=Mk4\M?6#tZ^t!I.EkOLuD`AS?"!LI`i`/06j.=\Rf\Q><Ig.\FuC'?V2eE
_K8+mGs"CG3>a=LMdK>k2BDmaBW=r2S@qq!g]A._>&Z@5Yh^+n_lF7qSZg@Rd>hmaN9k8jAgk
$HWul;so<RMCh>g/.!,hWEE$I_Z<DoZYi$XkpWC]Agf<WYCh3*=Z?<3d/ADAP8G`pWccBR?`l
,=7a'H`$`RX9(BX*lT;hrr'ATIW.k50?G;9?>_$I>F<-h617c:W]AcfkLLZ&Z#QSV:n/@-hUG
<a_-:0Lp&r-sFdTC^KdDKs0,N(C.3GY7qIE9K#A:p7r!JG5h>LO5rPub%?i/#e04*A)V$YN,
^oZEF'YqTf>KD>DLl-Jstf5_Z)c95)k1m@2^8&:I>e)5%k-JLH.$X3pm&rt_8b7D"E?%+C5d
%L9%8L.3tPN!F>O`c#K>V7F@*$uXFk4Q<0mp7nbo=qjPOS)%V>?dRLR7q4=%hU9;-$B`\KiY
r"NOiW=79Jb'.X3(Ek.kqS%[I\&KY;>G(XnbrZZ&Js+W+K)cWPOIm!Gr,#bl!i\[&NX/uL>]A
'K3[h@6#<iSUh.,915L'gXt7,F]A8B/DA[gL\Wa>p3tT7o9]A]A\[YCFANoJiMV2C2q3h>or"Y=
-7u@XZ#i,)(c+[dQnOpe'bS0]A=-lN?/L!^?@M=k:[WAVG^Rk/),(3T_+f$%S;U]A=96!T8hND
o;?D^6]A"k9XLKX'_]AIWSB5?7$M7h/&1DpXa_enhr:ZnuCF[-nZm@4f&7[.Cln_TX>/!'A=+*
=f5?j1Ok1Eo/&MFSd'6+LE-N&1<,;0TH[L'h-4VE5)PC;'E,9g*r6f\9a6.-)GI]AG[mgiqCF
mtXYM&NYTg8KOHi]A#;HSWiFWTT$Ia%q_3/!G'JGP9%W"Aj>"@`MZ_u%aE0%aTtiFWaDZ]Ac#c
ZdDl_s6W>?(mIBaJPO95o88u:*7>*&B7k.gRe_f)!MJ7P_[QjP&#VC?"pM-CRbQTQBnd;/4Z
=]AP`$uOl,tF]A[MKZ*-Gcb`j@fS%^G"\_<(J5)39TKWp[(7A\$TL^D3N,Df4%M<Fg0h21H[%0
qKRnB`^Tb0G@,L<M>U)FWl'A]AY?(6NskPHfbf'S-m'gSYR#cm"HP>d;NDZcUSU4GX9qhrD7J
0MUk\k.9%1\43f/R[S@;;%bB[RU0F^FX'cNZC44#C\>U%nK!qYY"uJkZJ/^l"HurjJKF]AK9*
6APk\tdV5Fd6Al7qu]AUL'd9P7<;`49;m&G8+3XWR/<"*cT]Agnk*?-Tue-')@+\?;"l4^MB84
$8=VuS]A@A!O_HXXNbpfrNB+K3clGIMDGc+a@@pr%0EhsRR1:orZ2ju.KW7L3nH7O-bW[02L"
8/pW;CiLI+'40*ph<6`TX$t3(ferl9%>mGlA8iL,?IncA?mj4eh2P/laGS/Yu$H%!D0W1"]AN
"a>\Tus/PuE55X\uNW4\$>V,Zj_`cK"Tb\RdU8[L.%gGNo7ql6YU&$Z*,f;;mB3>8E+8XtX)
:Up8*7NigZEcpA(fY?3P]AQe!/J!=%]A=kWj(!0%LjMdriMG]A4ur1Y,P=[mNYdt$QF;n+=F]A@_
Td!r.X*=;u4]ACU2=P772aF`_4U2)5f9h:4;Xb_Duub5]A6:?K(a+qs$C`uM$`IYo\sgZ7Rf_2
s4Y(WPs>UO#\(D;/C3,1M[FXHR6-i/U]AeK+0(T[.gE@0qda+OMVgd#JbiqGiIqB"E6$596Dg
*u>o<=HbO9&+jXg,C]AEH>6XhN,/ICJ>pSSi8B<ht'2BM'bN5`Wo$[n32d2qp:So-BN^t_b]A:
Lq,V*Z/VKR+6BEN=^Wbm('lM,6,j`F)Ll)\d(hI^8C1CW\2a8?Z+Ku[[i"mD2kQJ&GTc]AYhQ
l(a[Ps_'M%3k]AfY24a4OuINrX;g'#d(uLH&L*?4mDO%J+DGS*ZK9XbNhdO8==eVZ9?P'r96n
)a3dlQjFGI61W81lj`0!/NR\sW1d&suD:TN)N]AQ\A;(]AQYh2$`^;S1NM!cD42mjVT2A]Aa@?F
'n_r;e^;pAK,ksQaj6</qYSlR4B-1[%W#a.U?C]At!/]AT7We?hfUFI,V-o.r+KD&O@/7+W"^k
Z;`@oIiXh8A[/YX(+=LKBiWTYUgqpfO&[DJW[Unn=@SZ_W7s;nd5Iil-hT^(n)0i]A$lB%,h0
EGIA6?c2U]A%\pW1:FrBJ:#mU7GQ\tDT*o8U3h6N[EC]AU]A:`]Aqd6HShL;6&qF-$2AoKpt%ba+
"%WLfu`msa;'"+iiJqph\3lG'u>Vh^L;M$kB>8?^d1$kY$NmFin?0.`PnUWNUqu"Hj:&t3/W
X#N=n'%4!\d[HR56-G)CM6=c`:b\_`lOadt,'SILYu1;K3XTh,W.W(V>XS`$<EMAIL>,%
54J;<l15L>8$<cre#W;Lm</$]Aa3XE/T:IWQF*Md*g_]A,l!JbDJc/pM@=EV:V5Wh32-1$J"k!
_G;6&C"l%*=^"li5B`6>)@`nB/.Sf3TGtl`T*NB?s1;I=:[[kk]Ao<NCu8Mou-CjETMm0qdNA
Wj+aAfT4bs?Br"[7!M>_gD[CG'abu4@Nu;?8*QXRHp%T#H11U.C!UZ;L>![40+]AYUu02I;S`
S\F[5:)jS>n6`k5s9G^ac>:`0'*i&)a3h=oIAR5M#F`kDcNSj2+IE0f4V?0YSNSE"'3O<io1
p"l,J:Zl_g1'La3rSg9YUo#F52X"Sqc!j#,ZAIR5KcPN6$jm?r-BQ^]A\u'k&rb$$sZ44g]A7W
q6+;EX2f!^`l(D-\nB%%II&o%am-6TG"4/($7;&91!//lXq?aOLQ_NZ_..Y%,+1WS_9C6O)u
dOlOaBBgUEY5JCdQU1=$eL3=mt7S*.UK5GQ$8?LpXOn>(r0ak=CQXl,'.l@Muk/>69mf!B6.
M;W/t2B9+tPKkb_04ET;>qtD6M^3"Fa5Qb5;_`Q^]A#cr-3V$Ds1s!_O8%Qlbed%'gQH.K#\)
Qn+]AT',i\&"hp&B)l^-#q$4B'gTG@bocc)5mi]AEI`hDJXRANs.,#C@UY8fLqu,P[Fl!<]A%t9
l^T7C&Ki#m\o6Zu7\ccgcajriM3Z?@HW5QBl=I+N=hqre"<qb`M2A"a!f7r$,-SbG7h;Q#jQ
p('f=#6kMnX9%-[>9i<"1B75'&p4*l?dS>d;6/T#FYkpAU6$3\7E#rLL@<M1Wt/'3J#ftgV[
9P-Q+_()_YV&A!E;ju<n63#ouEGLIbYGlL6<N*^;j$ASe46l!&uhj"]A<8]A>LKF@rk%]AmYHG$
s#NU<=mip64I*R+fHf5Po=kE33h<b4)S8H3GL.0qtEWrO=VpIBkS=#7udXkppUL.rN@XLL/q
ph?ls#:/*ST.1JC_\QK,Eqprl@`bepN4C``Hs-2mr2BZ7*,.;$p?'h.5?alm$R'&8Q&\i<`C
VIOd:1\\TI*%##tog+>1b.s'PdZ]AT.BpXuW`Dj@/M\=0]AKC/jq?j1''CPHf5="^92;u>UkJ.
Mc'O*;cWK@L56eJ\!(o39!V(N]AE5]AD*\R12/"lM=Bb+`N?hrNhfM<F#fA%-F]Ad9V_X%TeB*E
.3TW.PKh27h@G)iCMIp+Ts"D@NusoZeRuAb1@F%6<eO"+N)Ci0f`8b`H:n=BY.5rX*pqi*q^
UC@?^U>Lqt\WC"L$2QYHtnmogmEibnl`i#]Atic?h05LRo[qUL06"DO)Pj3fg:ksqoE(0]AtbK
IV>'OiMEtM4S#Xm.^'.OlauR1=tAT2rCHV[6mgXl^fFj+0CHX?J!fcm*BSt&b">;-rbmW/*3
[XKQc6id'.S^elJ,-o4Ng\@V8^=&MmnRUXOtI(+>'+GfIh$Ttdcc_quh`n7S<=4h=7J2'&-H
8-t+BYCQ$D?QJu4CkcR7"./:G4&%NuX)\i8J*_VZ0fR=:d98DcJHclJ6]Anr9DTQURIFTH>F0
'\+Ik>)O@>3DuPDZ>E6C<@hk<Eh`F*9aJVUNdtkKM'iU29UhV63HM__kSDDY^Bi@<>)!#?XH
@7>ZtWRZg+NRZ`B;B4[Kp"s\s0B^?]A!C8p[3MKR.MQGjqG3mIjZ1)fo$GG]Ag;kjWhD&o7%Y1
[TlfE0S328AsiT]A-DD2L2IEj0SD8.(5j;l._(XH,=WBi1:>aS`')g8JON^@m/8.GDa2(jL![
=sUV!0l-68ZH<ZW,R;RLcSOTc_2'I.8.8h1:ijAbL5LaH--f#eNE8gl2lqdJ)T0]A#8?\"V)=
d)/;"^D@i3(efr*4E8+2HV$tjPo/EbE@ZOeM0ibFoUXQsM[NF!NG>)jotg9k%F?2LYDq<@cY
CT\;++B"qUG?!ltp"+d.*:>#<17\N;EI.Ime,1^'+2:U1-U?h8*\$@<V4Y4di)e;J,:%B<)P
^NqlLTCim&^9d]A"T,:k-)hFC"?I4]A3IY`J]A;VcpS/@:9"Mq!.h()3`<L`DM%q(()!PAH@gkW
90m8W1NgRjtaBU&?g>/m(7Z`'CMqcIeSb6eg/iT+6KaCV,:s;i]A-b>N<,O.`m9E4jj]Arf(V2
M\$&S0g^Ff17$`ICrM2=C2M;Oqhh4(+AYHL)hDeYC'..4:%g`n)A3igHrOULa.1Trked,'Jp
-<@]A$\nt#/.@65q@=$"6^U9K9bm("6.\YS#*Bp;JB0HRa"CKQbhsi,<.?_qQWYS*':p4(NAM
67'jfcMdr[mPOCo(b7!&`0<R-b?Cg.I:c`1$:R\lJ-32PgEMH$Ib9TRZm?-&Ri;e]A%!B@7gm
-lY]AP3@oJ0K$M:A<3**tKAHZeogoTS7J[_Y!X/<]A?]A[S7/EuGsUX.7l"*iO8/NaQQ7oH)1,A
iks\l[mh.=.u=b'OPq4/\&Iqf%3-<g[ja;4n(5C?e$mYdHQ09=Ah1@XQk!GT5G:V-ZXKGoM%
".f\OEsfe5Be5<#!r5<a2E`+hASGSo_;K`QB(4+7[1<jB2`otG0`_9\,"JL2Z^=*OPIT=X,?
B"(Wr'U9U-_RSU!gFN\L/bG%>\)@mj%pLVFFnn.ImRfn)_M*Po.C\\!6$l]A1)?*%!aq)F"A9
_0dDpImsdI#`%Q9IJ?hDX,/qoIUWJZnb>`opAo^h%&`EF.ifKjWo1E)shmgok)"#Tr8FKp8;
59"!`hP0p9N@PbX]A@hgK^D,j>E:P7`6`YV)!]A?3V&"^snYYLi>e.lmts'YZZeWD.Q.a2Vm?-
T7$5:[cWn.m!0Z#ZBKgl7ua$Pi),g#b'c\0*35%M6M!lCaR8?`'>:=jmLbSeg?,H[#`o8@%&
L*Jq`<s(1,kL>JQX'So^[!8%RLVK1_A=k2[,N3NNBq"E4tiM\XjMU.>eC1#2H<@DUpb6I_[#
n$:bQ.^`Loc#S2"l*1p>\m-Kc4$B,U84@XXCG>Jke)0io#2(fEaC\-`ioi5r_&?02oArRSG\
1?Xh21>q>`trd,k]A*l2KKAcf\J;j&S$oIShf1"HtSCfVM,]AKA*!S6WCKq&^_VlM<b=ZX"M#q
V$G*t&X%"\T("cUXB&,H(b:\)9X9I4L\$M/nr@:t)N@TebCE=$'mE?s/4.ME`'8,5*bf#b8!
(.h2*tT,Ad+0Q"C\NCe*hDn#_?W0.g@V*MH8O[QZj=t-mN%I%]AeOtFTVp;00N#%l%5*!se-3
JAkZeQK`8uK;1>U#09R7R&'Wa_$6(\p7^>?&*Jn3EE&MVC?F]A9tr<,Abs;?;UQ$C(EF^__Wh
q]AT8peRAand*%42I>_C+Z^.:2kg!m,6D/hj9G%+HY'okgbZ:0rb,mkF+B;5328bAa,<P7+-)
@Gu3NFR[=,F!Y%idrmTZnkTh/BV*_[L%9r/Fpm>2lo'j5gA.d5+Ah!r@"TP:#$A1lQQM?.H$
BK&@Wt++'C[3aJh"B#<EMAIL>,,D@\tT+J<?o!qLJ,r9oMIE$'Dl'^PiCFXeMU0`B\rkDc[H!
\p^&O5=07\Lu^?%`tc_VMjc5a:qLrN*i=dK_)sHl1qg,j;D\=eLN<]AC,J@?9TFfb.3RWC[&2
+[PD8@`=`^d5:s:`fS#DDU]A:!j%rkfE$\9M@"k:uR4Wlqj-l)g[\mc=7Cs67u[rSBOaIXp//
<e#`9fIu\ueP=oH^N=2:b8hH6WYkK"Mse<G=X^$!?V20(9\YS:PS)N=k(HN9a^Y-,?(;=o$J
^,ui[IqH;GmCD\V.dP_iP>Q-_$c2GC%ft%6l?I28k0i//_E[QYI[S\Tcg>W>[C">Vh,.Kbce
H<)j#A)!Bkl=M`Ce.Q4(1O)2Mp:XK7a_1L^^=md&(d]AkMX%`hCaiiU&W11/h`dfl.g(\CZ1.
,uYQKjajj%Ff8/MQbipTcokdcqI;P:mfa34EG#dYEJV8AOK-o=s?gjlH(Yu`[+jD_85L)m,Q
8Z1Dmi%>C*nM)a%*9=I\E6Vb#r%;HU9g+;+sbbs[%fj-'Q2JQ)b$d;H6DqQYCQ84]AbA.[>-0
b'4a!K<5e.G:.l7R(k;`*DLA$$,J5qM2\I4^6<gLffdL&qgJ,.,FXL'F=?Fp@-=(6:Gt7+N,
IJ:nT@K)`PEm233m7-PZ+_0B(`C%[R4A-c1W+A^MDa0e&=CS?V8)%lX=gTnd.LnD5rN6[AkX
0amE3`A4F%R0Jpck,?8((SoG^>gqgmDHi896,%f-)\1d<@]A1goAc`sahmfW6*F]AHr[ohJ-Dj
hr[9/nrSUKKC:"6p?c:=&5,.&rDYNB#!5nomF1F5PAq2+%f%iC@g<N1&@-/H<NVGTCIF=k^%
r:H^OfW-\`R3\Ym63_[T2$,/RZ'2.lP&U*B]Ab,%m%TEp^+s_tPSiW0#K"2cTa)Fb'ANF0oR3
]AlX]AXo"$b6L^m$.#3/0D4s)TN,4%Zm:X;Vt6.c-.L+"^;/@S,5(%3csf-Ra*<*1Tr2=\AVNU
^%)gr!dr&LE3H;m[f]Aq9X+*eEWTN34ZCY08K5.pGr3D_KsRZA;&n3;gg7[CmiYuhS^SX5Wsu
n%"5H0]AIbQbY$Fi1r)Z[=Uu<]AroqDER,^"<l_5l43L*m)f2>jrq0m6]AAMP4;=+R5&/P<jk*=
6U3RCOM`_A1\*tkq\^!?dL%?r[T!XWBHbR]A>H!NHGu^sm@jIS$fElo<Pq3*W<fTQ9;XMX""k
#;[W%HlEPfWf5f]A>^mri^l3jm<:Vg4l2<cm0]A)-["/6#rg2RZc4CQ9@k*VlXI@a_,=S*q_,L
J`"bOkUH&g>IaK"*X:\<:$OeXh.DQ/#f^Arf2fh@le9mi+4)9=.a@ghjg,>UpkJdJ,cTe!Dg
Q?K71B?_QN:>@><;nJe2=2qQQGZ%3a81?=C_S[pb#2I[-fD]A&*b4aahpkMicXfeqkcFK]A5RW
F`'6H^nr&m`0V_4(:IB3X=Ng7r0?24'Cfk`Fn_nhc[l%do)H<VPIA9%]A'9oa'/ma(@W"%siP
?TLM8l(iCH?,e6%cB8bqL3mBn(e7':%ab*meG$jg<n3QTcI"I[o3uKSbY!`%]AZKNd_Ie?.nF
JlVW6&aQZVoU+L@`_:"K)O#cP3,$F!h!7$IU]AXA&OpO?rC4?0DfMk'+\S-E]A@Pp4Z\gW15*m
b0JG?SGt]AuUo;_W:0o\[]A.o/3n'%QQ2d@W_kdQ'@r+YW%HJ?PUgeg+@\kmJ7"OMJ03`6*\f,
NO%b"7?BKOn\Ba\q:Y5H0Z7R4s!*?8(-,HI<E3RQ\4X*`oX^D#sU_T9WU64=Dn0JnlBVb^H4
'T[mp->2KBL4fO-EiG8<T+6)8^iZli$qN0]A44-Jh[8=bMc*7[Ohe^eqISbp5u8kTP=O/S`P.
6Fm:e=c!oqqOFlZiieg!i.s2k[RWD5:@9#e9Fr;VB&/PJIa7kIESA)j8]A<,$=IIic\dkmbC1
0`B$OMQ!1YDV5B_uXM_3V1DG/?Km=X68C]A8/I4ZpY7nb[7:^M]AP<dj#jC*5VYf,=IYalV@c^
ln;5G/js[crFmGDrl:2bc[A`aHiMQf;tPuZo#>,d\m)XXOBL<tCLW>h%?JV,`RoLb-nO$C9:
#&!2qtpn>%N/c3SDZ@4QZh=iZFpG9O.L"E$'n9mN_%L#1!*$mg"eN0Bog(!UF[W\p<@MAliB
0BA`DCi/`=)CohteM%rfT@VIqiYe5t<!.qUs^kLpQPIgn^,]AL-%E2Rn@l\/LfK_(:0hIous4
7B$6\Yg,(U.K+[g+ldOll4h<c?G_]AZXgfjWPBKW#3ciX5g20ngA/<*j#rm,qEpiK9ALs]A3K9
lm_;$#O'<@>LfKj<%CndB5qu$G@8TX8*o*,ZnIsp@Ac-%a81>3\'B4.FcL_A-b-=1PZNP,e(
fPh_5nn64]AA6f40^2<bE3O]A/mFtJCtXgDfVnf=hGCEtP7*FrbN+B;:3576X"8XofiD3>j;)5
03]AYYOnhJe'bFo#&quI,9^-FPd+7H9_"oD),UDTo`Ce&<Gu:V;dC?CLnWC,TtU!PIij#4H_%
#@C-ql]A)f[Fc>PrE.cCk`^e$:p^@JO!V<O)O2;JMh1DNqOqF6hID]A7/0c!VH@^/)sH%0cBP2
VS:3h[^KjKD6IQ%ZUa;$ZErbdd5i*M5T/un[GF'j:#n%S.+d`l*:kX8$dhhYnef70Y&Fh5EE
KO]APchq!jg6BopFd6W^'u_.6F'/`nhrGa,[gZ"s'R.^e;6=#0u*cnf)I=aj+_);"d7eIjRA#
S4,1\H)&MM:H0d>[kk@._n,+<o5_^c(0a,`bPLScs+8VX/4KSV$_RlICZnn-Fk8/M=\.pXbp
iU!M:CMZZItD.0J`AEBDZ4IT?3n.B?L0f*%&>K/A[B*<+/%b6QT9lQ0rL5eGX-_NPgrO3Q'r
KC$L2?0JXq-p@RkR%*h4q*uC/0/#ui>WiDpZ1BfTYP&WOC@P/_RXjuS<e.ZWf$LGJ]A;$`7AV
_tAa"YYFnMT+"l%sVr05Km,+]AP/TQU0M2coub;DKlhUpE"Z$1L/2l9;\;"egjpqEFTQ7_[#Y
#d3k?qNUb3T9aKD8cp<1=k%<"2,(NEoBmlcIMr%A0U?-u#-C&>p,McBO8+H.==X`hEaV.JUX
Os,drJfUtJ`uJ`4;(Z[RJ_,&W^3RMDPgAJW/.jBccNDlArKfP!;gP@mYI`YoN.mq@eYe!>=d
`b)@pql61O8*mDk=+#MdGr&kXPtf`ucl6&#VYBMkl4<8T3psk0j)2p+m,Y;jji<*);:'#qgX
@%:@sQ.fM(3bXEP<\>0&V&+J8k*"Wp'pYV?sJCLX.S4"?2[FI!n4:U<\HMT9sBdK"R\,Ak2?
p+q49RG$gYKmc?C!(q_cP6)2oh$7QnKDO8OnMR%43Ru]ABED("]AKOT_J"nHHYXQph39_M6lhT
]A94XN(4#BK&E@'8I<.R).7O2oP8?7\.jdJ.3i]Am`+^-[?m5<@)b'ecs^TqgQXq)H^-:A*W,%
$mNJP/DZY5ol&S5F_*]A'mDkK,9W#;bbl+?H<%T!n.45@irNIR7GZ?mWaMi0kj]A;7T3c5?_pJ
(,Fk%AigLY7&[qM/3CYLa6mas\pPBYR&EC@3^b4q[\rWdI[%68JacD0t'D*'>OF4OeJ48h<e
34J6hUDC*#QHh_OAPL$)+lc,h*/Ekt%CL/i4'O,)i`Wj]AY%S%h1ht]Aho>Abhd1ueAN=YEVP>
,F<oGW!%QNS&c3$pdET"\V=%W0m]A.af3F1I"092f(o0hdX_S$qmL!3X_l0t'gP>m$$'J661S
JU[?d#7-QXoB;J]AK6/e&ZMg[+bk<@5;X^r..r.$iaSW4?C5E@Gp>gK^l5?Z>s#+ZMM:f\iN7
eWBji5/-)/o;Ga.X6Srran:Wb\S<X@AQM!3E9E:dBgJm,0%LeZKSA@&lHlt%lXm1JPh22X(L
i*r]A9E%!_*P>2+T1+7m>B6'a>!$dKPf6rb+[63G<PL@:-LP-Q3KEhnV/>r0pKDa%8kC#@534
<P,t^+<X.7ZZc7f+"nEP:r66[oI-c_(%GW,1bARRhJG3\f]Ap&@[)&Y0,#bt,aqlF*H8j]AM="
i2CDW(`oqX^s@K"eBT'dap-bHK?-W)#]A>bX1H1JYk@l,ObmTtl\U:ki?a-kG3k1r'J27%TY(
nZXoE2!\a!:g:15bE]AQ4Fr^\C8cHki1%QQ*X3`.9HF\2+aJp@N9b7Cko]A[8+L13G.=E,IM)c
UuZEq&=TkBoVel*;!q@i+qQ8\:<hR/7*/D=<pA#Sa2;C4(SI@6S/UF(,?qE7rQTH-diNu/@V
oDP>R.lBrB@]A5f1aoI.t-#XC5t,:nDO/.cbaNn]A).t1VgoJEEP+cfCGU]Ag^YmDQjuHUHY9OA
65io2XFu^u!Zs?!q6"q0NS&G49m+WDb2IS<O<KpZmmg?8[IEX:imZD]Aa6a34l1Y@0=Gc#6KW
=#6dL9d%"n?EtBSpq,]AWO<,<f>.j;FfAZ7Z2LJ9`<L.:g\(5\Mr6Fd9X3sL,NhX^L33Z!#dC
>%5ii,P#BQfWIf'@FqI-k.GHc;XL-]AiM;pdYOmPcY)\cT8f>!g7(_S[rP_cNk)[W3CFq\sa?
XWs^#]A+r`NlIXO1n\L#u6G(u7P\Fi9a%n#Gf-O'a6//ZKXH<b)`?RI1V'e+=8H$bN!*Hb>m,
bYdqHX)=#gTQ!CZHuBe3l@03Xh9L4QbN*8YI]AD=k/f?U]AipA]AN3GG@32rL]AOsL3lbC1SVCgY
Qo=EK58poOGL'#MoaTfgl[Ro1p!'DKsDMAW5pD)RO".nM!SJqrT=$\PE/Nqo5Z(1=GgWP5A]A
=aP=h]A3\B$dm$Iqfm[Sl]AH2<fV/A4Kj?>0H96EOg1]A=sqmgpXo'(DLK9mA/C<nEmEak3rZ']A
sgRVkCa#sV0\$([5]Ao^`&I<7B=W3Y+IG\"hch1I-5G)M5Vn)'K`i"YE\@lh!W`^]AF2*o5`oo
N(:$oT@ls,)R,J`'6J8-5r$'UG@Jus><J17U>9k=i"kppF"KItr4O!K)iXtqREJ@ZQ[+j,^S
&'[=p%-*WUNj&0IgM-f>M5I-dcV`TSkPG/nqq'H)G,i.+/i;E+\k;Hg+PT8t'GVgV033^KH5
ATtSE?'brtW/@1t!`/]A6(S7qZ9#iX`ZF)ga?n#t"AP#u?WrMl*h1pqDY&&)i$Q`%hk:VLF\g
$a5p<2&*:P;$X%_E=2'eg(@';q!e[r6CE\aV3AG5iEGCp#k`P86)2p_i#Wq<Vd.G^:Ou5\@<
JS=J<14[l7uukh0<[kI"m2ekbr-BR-"&prRtIfc'jl.rYX+R'CHQ#?i^qXpAo2AeW^K@Z+,$
T(U"<eT(O9.gd\!]A_bBgg`7e#@5u8Wj%P^i5sM*SKOB-rq\a1ZF#_%CB?ur,q7^OpBP)C95N
hS8UEn_mFm`*EA`Ia&=^mKU6UEYN24[JW[DQ&!fl&DOE,g$MhV!'/A-``IPWOLu:V1VJJTt)
NJR$dj,k`'d9^l*0Uqt+.54/h(&!,D]Akb4QMWBb[%#B]AOS#ua\:C)p(N!ssu`^TQHs_7h^bK
2B6UR*nP>gA\hBm##n4!Do%eG$g+<Ia]A+X"Y/:`]A&r8))JtEsq*8iT9]AD<e=P2g8UD6JG%AN
AGH:Z^_6+P^C*Te5C&:ERASc-;h(#tQGI/Qot#r-$NFIi$*b423j91c?^o']Ahd=>E3TPf@QT
ZK'oP3PEDS4en,h4B`uWW9NQa2FCVO9!<_Hdb2:'?[3R]AYlG`);=5O7@maf!q1fJ&VY9H64&
[boWa!e6aq)FF!8s#\H1.Og:pAu@bT4!IeZsH@Dq)dLPqh-Z#Fd"@QrDpEX\<_H;cIm"`Q[1
L^iHmi6(4<FlD3^n\12cHK[Y5U%r)11Z<o<Z5NG.h4e:XZjpJVs?$Ec(DWMT6Vg56[Z4WK#T
WaEnfQ>]AV6_hJk)D2"32me=E3Whn'%JP%Udf#$0aWCa'gLQ1FiD2#u"!,>o0$pG(d[E@tV=A
Xe;R8@TMpeNVi'AdF;S"*8X13DNLAZ(,917q&]A+HfCF`HL_^RpQ6>P6#T<8'OS_5O1\TMMK/
kXklXl<9Gr1LN<?@MOPf=6tI3^B(+3T;'hP>h(HhJjNGK[ll.3Be4.l`o>kUrfl4OoIn9jp;
.9>]A9$!figf\3=0g\b+7A;hjNMomJ%BAQ0V?D(YcR9LrTT_f]AHRI0ZF:o:0=dXpku3W7!(m*
T5>Wib[@b%Or\9=\Q*28A=A4V)^f@Hdo^O.,*[mG3V*nU4X?m`F-n%pjdu=GATQ]A:'caQ6Ck
N2kr[uXdh,!>YV<M!EHmR2-(#A,-;!XeQ'S;u1q(8i,70jX8)]Ao,;3DpYkVAD9[S00-i(UkK
4@DYnd!_STZ"BW9XXD0'-#"UjBcHrfhB;:EgP.?3Fhl/uUq$bHT,bF:Z,=$X*94#5Cc'9$n*
s/"(#fhKm%Hc-(K]A4\H&NC4:2r:@!'cR7VuV*;udSdpMWmJ-SQn+efra,J?CKWWP)?V)D$K.
L"i&GR"HB-YQqCot!Jq9TW7%Ji)f:X9%TJZj,bZl>N6>8o]AE;,Zf*ma3lqBX:.=gJ&`iFcfO
`*DbbjC8A43b%d_NfrjOQq:@9/@`%eC0EtuO!pCcCQ7_Ofp5tt>T/be<_9Q/fKH#:$IBO1"C
=@EBKE1u6lLo;$VEOY/9I0"VY,OBTiqk=(X3*:QT0<&S2KhG<J>teT:M9HJN1E0'1+FbI:0i
9B0]A.JaQ*nhZp0-]AN(uF"lOQ(ar(Z9Ts_Id#@/;i1U#>_`^B_$!nZutqkD#+RPg\hEsj@[Ek
A:h"YkWn!LPhH%m!0duLYsG;JB"[]AYYJl0l[an,AJ!,LY5Ti@,n]AejAbB%eT1c+TH9>6Mpi@
4=ppfa+eEB#iNB$_gM]A5[&pU>=$=[I'8F5@BF;UX+MP-[A$Nb2<'TVgUb0O'kt!>ang>$7NL
<[Sbi=!se%YE1J0XJ[TE:&`E`#YOM[$(RQ/D+`S7Sb/92j>"-Tfda]A*(535h8R':M-[KDrEA
gjahQ:rBU:tB,JT?*%lbQIqCcSru%RY(QK;Ho3E7:J[U:=d_$9287BA"7B\Ih@_k=F(jOm*V
Tsg-g_pYNY:fO3nju7IWP(Cnu_?mp:q1kHAY!kW;qr1O,pXen(e]A!)le;qc%unY[+9U]Ara;2
]AfX!'bis:sO5udhPo%-<VC5\M]A5Z0%9j1;sr)Iubl.;Rn46kiVRaR:">)4]AHb15+I2d=8pkg
J27$#]AX@a#<7([3=ha@/`Hk[o-Jd)0+AO5S.McPinY)'O*aR^]Aa0%A4\Z:m;gaOP:ljh[r3M
K1>o"8I0ii7>XH4#!DosQf-PGDmDA=^F.,QN-#a_H_ncPs07(EBh@+si8i+8PjMcGrbHd9r@
40V07alMIU>:cNhfpd#M8(6#Nkgc":?i%-L,*Q!a+m0#?G:e)H?7**i]A`aC_&[XGQ:L9"eqb
%Q7L#15IRU]AHkaHC=]A.pT0j]A%/_Z$6!]AXKc#0=NL+_@t)l'[FfB4K9*XO=gc/s9%Qct;0EMp
K<O.^jma59'&oCWPn=i,nZuo.GU_I#c2>rW_6P]A]AMtQa+gaWqeJ[+W(3XQ*DVTmHV0m7V2I-
.D:mWe5b^&u.#/G9uJ>@42TRZ?)_9mFVWZl@@)\(UF'Xu`of9mN2o@i]AaHM&J[45J_A4%-4&
H0l+pAAZD%3g-]AcA1Z>e:3e96&Ibb*!rS2T,i"EBt7W\Lsq"Qfl[<7W@7(N<dH50:3gg-HD6
t7*f%rMdGkCl(ZMKRhuKfqF\In'e5Rd=8^CAmeHb9sj8\u7H4]APXb:-)Q3q%?4&l3%.\+Y.P
SY9or\Zc7Q/.Mss>s1\no>hRI\U&AB.2bCgi'*K<1danDTNM)u:fAu^<XJ5is'#Y5OD9O?Gl
/\b</5rAA*f\-g]AW,K:=4g(PYmDg(MZcOo>NF":+A"Pi*CAQu%a-f9ua,V6S',(Z(&4qra=?
YlkR/=:I.5C\r@Gq:nP2=oB,0oDUpGaUuRq6b6d+]A"B[e<YS%fLJ2%m>M`@j;p3)^45QHJM:
U3`SZO*YV[t[-"kE=&A&,)]AmcPEYamYB:%8k&E/8n28)-SDUoQ9O1G\OfWSXZjm0c%n^qMnb
OkUqE,)!RG!$Oh\Q6sIK;>7`=st&2TkR\4)N-Ie'TpKVo^VAMAL:(WTAif_QMtfY5j[GUB(F
Q6]AEE4eW:B4oR-khH=UK`!bJbfn9PbRL(r2^eWeJV!O@ncN$!tT@X!W>uo=TWRPH)je>p`Pu
^1Z"U^0LC'o=O>KZQK7n]AiP2+cLV&'LZm&H"Tg+dVLFWij_k\#>lguGOJVBSE9tCgp^pn"\`
PUu/b"nZl>Z2X??+Zp.#lAJ\63%2cOarX)6Ouu*qlEUp2L3l;P_!8,LH<hW5irP6>l0Q@Jk3
_=@Yhg:63j*OO_\1E6.Q=6L[J*AncR8GdMJ!o(D>#4)EbG)d;^k]A4c?Odap/%iF;[n'5["/K
W9=C<M3!Tb]AE+pj/'m)r(1H8A!&+[g#F,[nrKZg*PEk`eF-+kb/.gaRAhn/b&*:!`5<u(PUj
4Alt"bo3GY1dTaQCTH^L1oSTRYLP_?usg*QH]ALWbbR@f(7[JLM%B8=i*oB.nZNC-jW#p1r4c
C_f]AJ[#mNu1BP.a:o-O)Zh3OW_Qlc!WV\2&Z\/HGpm801-AUJ(YLFmB+Q9Q4M%i@.ERSZ1d9
-[3P;r:98K:XIRNnudJbB.?!d.`pbAY?6msZSU*dILt^DJm9pe<]AgQgj:j/)Ak1=R]A0j7>__
pOmg\gPa53')JtOp%36,<4&KG-,h""@J<6BafAae4.kN=^q:5#\AFlPB[fJG!Spka[\]A*2+d
$n,J8'\J1kuST/%0e*WCK"j0rnc_XCH"hXl'E^.K=1cp,1tGLq;;i#h`GmDRFt'E>&)MkM(Q
$?8K><A-L56j^%O.WQQ1qt2*SLS("*ppMs`6":d]Au$0R13CaEE_@nH@+%g>qtD;khF(np#5^
C$-Ibg4o*F.I/9@;C7n3fAo2;^OHZ'QVi*Ui8Tk85Au`n!GVkURb"[BXSWggSsfj]AP?_h8Yh
+h\%0bTu3&e\I!4CmOf@C_V/aQEeP'X><-S]AI?(c-+$H=D#2'I$BQ5AdG1fJ\t(>;R-e7jkq
PQCRI@olj8QER[Uu7_tJCSMQ%I)c^10=,nU=ZL%\$Iq`E[c/:m\L>'LqE/7q5lajqd!/:[O#
++Da*1/de?L_%530ph:R>0V]AoO;:-3tCM0U[e6&/ObRS`1@rDN+5>S!-RB8.$n#E7N$=eVM%
B:T?4ka<m#9-r4sgqEuR=7j?\poI9d+2g>\%-"'mG7PLE^!U`QA;W^I#U_EHC5r1'._>?K*f
)Q[T$*G=]AEQnA)3ihh,BVoAjUQp]A,bfb`q;;F6W'gFMlMlqJbAcH@<$atX^4[)TIdek<ikrI
,DCneCn?KQ&T++;"<ZgV!I;[(YAV.o2N'i&UC!WB^J=EiJ/*&``a+mL6s^s8MumjfB)?*?E^
"&nM9Y=BN!k0)=(V(/'1-^Br=]AZAYpa7e2##Q.>B;r[.Dfl;3`P)Jjht6#YgT%FDno0C&nXj
(_Alc939hIR9Hc%1fu4D`0:g!$5X&?f_cnR^ihr(D![7]A!cdeUHid%FeUlO>W&1d;NJOCCT>
RGr;,b-?aXtC^Et*Rgha([i^ou>Tk7k&5Tt$Y.EXH=Pa(7$8\s',l:,;n%I-)4?8&tY!J^nU
*\``#!j\qa_PT'efp*uh;u57g9?Vibl#$DmnntAEMS!K>m9TL(BEJ*Hf<h/&)WdP!)^!sHmF
;bDmg-EW&7bfaG._?qK0jde%LW@12jR*MNl:RGi"+fUpC*oJ>#b,AM"k0LP7"%)?Cq*nfQk8
86tu4-E[C0mD02kDX04:sqr?hV]Aq@9AdTXpsdPqKt4NF-%"N34/&n<W8rXt<=q#4,\\:"Q"?
ZCS(I25-WeuMTJ(:I!H8ILd?p2ICj?^#3o07k0;YF%7fSpJ4g'npIViBV5>GD@1I17ZW0_L.
OQqm!_!o.;C?M$tf-g!;b'oh2`o-AQQXBa#oGgj=^3]ADluA=:Mb<^l&<"#;B9V36edU>V9c2
#*I"Fd$$0Z4PX0=B\GL8CfHphBW&)>>]Ab4H/>:uH>.DQEC#0QFY>CFcB9:IBgK8>r^c:Kj2"
=)V:pN1U/Y#P8#-\C:htq:jO*k6(]AMFLIC4JoJNVWhjY%3-c6*Q1j4,;4u(6:Q[>)HcDDL5#
AT$_tI5(=PK36Cg+FfT.sF`fQ"+[/*0>3OWe(Sqi+AgR$uHS7UKjMF`P(g3_LTLke-+>5$Xg
UQ>uYrhhP]AeuU$R'WeL?7khj4[sO56-r/7m$6;r+r7f)Cs-sF'qB?F.$/eI1cJVXo5Gf7WpQ
mqNc^U&@<:46Ub29qHX_Q]A>o^:rTeWIh?*MtahgR`1a"RATq_6KA@f2ClGc"b,;<\ZMB3Npo
Ut+D:Hk7KfD;Wj!ICVS_]AD%?"H:la[*1PB]AKX[l_Vu,3dQOI$1Sk%F(i;B^UY=,h5$qt\Nk9
'A#$Coa5OKP5(IQ1?^od+\ul>&UEL3N*V]A!*,eea1h2n/j:cjB^pDoB6%X`j5PLnBtsY%Ba%
9+JRrhCbjDFXFJS7Rn0&'*TV$l5bUZqh0%mj,-*@b>`%BGV83YEkK*d#F7-T0A4Yq;M57Jp[
'G#:>*ei`Xp+c55l*K#W\3%lr%3G4;L*c^D#\i-L9&R6)IV66am8Qck<aC6%*Pe\YqaoX=g?
HkAQV?+=_Lh?;,0&54)@sb;MZjc2_gHQ9B3Xg-0SY(mljm+/!G$+H!7r;<t<:so;^48/pfGE
)iC0!(Y>_tb9$@nEIRrkOX"B8p^8sYV2&9#[#WAXauT[fS5'gA4dZ1Dnkf^HI!OQmDLN_#Hs
8bi9!ndrr?tZUK3\KE_g$lcYUJ)9$__+j_lD:]Ar`0b+.:NSZiog4gHP(@_R+!0j;G"*ZCi*I
.bGmN`+k(1NAYU7iVMkd*h?pln<uGN9kbT=tJ1FcN1U1N,86mbQkq&4c]A\&\XF)@qS>!W!(U
FM^jgMXE!kucd.D'e#FM.9ia>!]A"XCV?>AcBtj_aJVMB@=374_+!#SRnb)&Mh948LEKMbg4S
ZnUW'))J0%2H;dg*chH-RdEH:tPNU&fM&GOt$s-,huhs^_ufJIX:n/u[@.sb$GZ]AgXbhdo*^
VHh(>qXs0OW2&1@W?$'*e./'+[IYaHNA0JlGdJT9rHnE%6d]AqJ6o<KO^K3#1Kre^WLG,PMT7
#;\r:W9TatS5;l+XQcJYh;<?0MME;#eD<(;(O\ZsbtA%kW>lp*,:RB5X?Af%7KWQY0P%gr+S
H,&4fKZ8=?7n=8*K%pq:Y)SA!@f:7q#UtQnbPlH+bDVsobBq5m1VTn@ZZ2g_5q4gIKU^ie&:
pB7LDG#)KJjWLUImTEJ."07Dka$pVcp3HJPnKVdJ9#eJ]AP<EKHE)P7(#()mVHh@c>LAM!C2Y
MdQ*36?;n+)ob$Bdb.6t73^A;"O/?68<))ZRi6";#_RnA/Uc;1s8BW?R2r`8VBFOVJ(o9_,O
UI]ABVgbfeYmRBGY%bT^fLVRO.iB7XA!cpeCNRe<HnZpPS\/\un,PUk^Y+@/seOWJ7KM:`g:(
=Al/7$h"P/-k",=0d^rl7k&7Aaro1jO6rR/+%IP*)Wl1;u>`U08"JDIBSc0X"NpI/E.T,n_K
,QeA[W8o4MFC2.E1BjeuO<u+Z<Z-^F#ZLHDGD0%u^:T:DKr0\>q;t&:e&*j4t1Z%Oh!!8#9\
/p>#Em3/nb.m8N,XBC7,"op?h6Gls=nS&d(r)H%RsWg-HSr1E:LTVDkhYfUOP&bZj%`^)+FL
kBM?nO7.gK;/USU\q-"55m$]AcGaPZU1h<"H0W1@#-Z'9n`bZe"YMm_Kk?a5KH:bF_U?PDtBE
f:><GPkc\Ch*Ro5W"F>r;;%dMUDYdA>Q4m`/A]AVcOqLR4q^9]AcRThll,j0l7-;@GN"tb7lK,
QF5fH/0?lXG!;dE56g)<KCJittg1:-i`H."-64->m!,3ouki9d_i-AnKsu8H2@<,5KgaiIEQ
f#uB0i*7fo2=&`tV0,nT*iH*<18`iT=g"NPQgK81DiIR&)s'[^D^4-Q'?so)L^GSPj,u0,7>
?"I?JS9/=.]At%eAGBs9$;R(.c(f`Lc1?(5QHQ5$cIgV&<S3nj;_LfLLMh5pP,Dp*Od(JmfB#
lRpdYRk&M[_%hQo-eiN%uS?'^,6!R32g,S,#qMc.IB'pi%Gj^(jZ+L-2c;Cdu;PgHh.A>6UB
WGAdIh&"7Z4]A7_lK;:q2)[c_ri*N\6I)GRn[.P7or7lmm.K%JV+,VBXC`m`\:>9@DYW1,&c)
q-p2YF7(]A_Y%[Znotc[O>bHX9P!CrS&;&cLA::br\-=d8[Q,No@oG'mX:jKU(_,5N%$=![r\
\#K*J(N-@\m]A>QRmE#9!r^rgZWfq.4bV53-X;94p4GOI"ITu;\HEcI-.]AH4OT"FVVq$1FH7Y
P&p^;hiUrb00^/BV7PQb+H[pJM&ju3mHWd2En.nYde8BruBH->_p)`o&((dJ'-5ug_]A"ShMN
(-L"KHX$+KFE,=r$I8XT<IP=A.gYB&cX-Wk]Ad)Nl(4d/JYRUaf+\ck5(VQj*bJZLSdZ1j^PG
j!lTmFCj3DB.O,mPcuIMY"e<Fe8ChGKL]A[_K(5[1.V7S<;Gk\+rV(/mNAP.,7*lA\Th"L0%O
8XR#Xp,"S#?/1LjZ)g:*RBWWsbgOY'*A"q'ku2(*jje0W/)1QGVsl7ncU_mU19F6UUBlU8is
&2'd.JcEe[)o8]A`LU_u*Ok/L:ZNs>Ncij`RYANkYM5:L_)2NVUj0WjM[X"EHGI@P;_jPBVMC
^7a4?N#:c`!fY`,%9OH*S&r1JLMA8-s\lFpoW:sAGTQ6J#nrWcP)SFEiSU'bQOVM$&\(BZZ<
<U_pVf>LpNq^I%sUu6!bZ#YG?sZhQp^REQPK34bDCS\sr?XET@_.%>Und`V/a_2cAqTF,&T-
l+0mc?3kl49:+QRZ>bG>8,6<!`D\XlD_4s,!1t05.!*mTAd(TgK+5S(h'YDMWiW\T%D<(,V]A
qLAfK2q0d+`IVf$k*7\LsZr4NKIsWI*r?AJZ12[XZKEpmjEfoc(PIb`j<sr?L?cns^G75!S!
=MD,&J4Zk_ghPX@1Jq!B-*mo46r)M&9c;nRrDsoH$dPlN&V'kKXBV6H$Ud`[Bo!*&l\0BN#N
PAUAcWXV8,e*`jZ*B3cD@:97Z*_@`'Ol9UH%_.IQY^Dgb1Oo.)54p31\rmo-potV'gRXC$CF
Us>NE^P>UJ+TL;"uFR%S6"![LckbgK>\>&oM,!E(lC>[DA:34T2?\eZsMdT;$kBX@;kS?b`3
d2=G[4@TXh$_#kG-Ui:.RS*?OiJ8?h4.h&Ii=$#'Fbd.=80rc.!^o[h4-0-QR59Be?B@b`ds
K8]Ac>LPR;Xih?qP^4=AGX;b!2+)s0>C,(_F=9l"^%&'A85R_cQ,>S!,#iu=/kpNDVlI25&>3
_:-ecuD-#0XkGN2O_&2gFS5KLs`5q*-*)PH5XP]ARgO-"W:Od=l]A"LVF9e7P>NO;^V]AKMor1=
2.%!m]A:O0@Uf[e/e-4IKgBl@NKXmJ;N"9W+7)]ArQ<S]At(3Yc7o@1D9fuQ:@U7iTmPN?K2$r6
ipTI7hmY4AaR-V?&R5jrZY]AWUC;Jd>&p6jY`TGC-[>9$q(3DWt$U`Y;$!7B*m)BV2FQ/&:b!
fM?i;G\NScA_"u+:CVsY4hl]AQ!:1M$.JC("T6N%7.4(ZEWo#:IZ7D&Eo6.JXgr%aR0(Ma(R"
aa^U*bpN%Q2i(K<U.KHuK&WkH&9R]A`)$@h"Mk7Q#48E809]AQGJC6d.)#'NSYudHoll\7XX2e
9P'f(8BAlMhC!,]A#ef^U^L3GsS>5[SWY^Z)Ll?+B!SfZ5^)B%tRPZ]AC;r]A1[;XDID)qtFg+=
LGp4(+e.O>B"eYYiF-A>,dtY!S`b/>1kKKPaD1gDL:Qd\!g,\<N-I0"f&J[W*L1o)04M(Q'F
[JP!k&hYJP::RmsZ?@C('9a4_AC^B+CmLcrP)4mpdiHdZ\lhg@07O30kqfRXY3Nm81t:J%#g
eWJ@j26)SS/dG[=\Br"PLT,J2]A2tN]A7t4kO?9\Y8343SdKRV>4U`k-DJ4_`.jGAsD$f'Ce-k
ddYa;KEOA,+,^l!4@)*:UQ5D56fN"TMB+YaJ3U5Z*AL`A&5u!F3M-jD@aK)-JV[;Yu,dGC*8
V:,rL4HWsuL9Nf8VCCii!Kn<`ZbA\?8DV+F1+(=!q#+FW7UtHqJBf*7I^ViGWdj';IMdBI^2
o&f-=`R<e5GET:%:YoZSG6t4;fg,M1f$&;7paAD=uK@@cLCDGj4DN5RVc:\o&$thW03a6R81
$^7;_s#US]A9BEW;"Kp_P]A^LimW(ps2V6!FY=9l(+B9=]A(M+P?RB#<6\bT)NCq-^SWM2.Om[9
+gM*eZ6?/am?@HC*ur[/ONp<g#dM=:=<`1Td\Xbl8]ARV#CSbeLJ&:CEpf`?qKXin]A9$sdFPK
__-JY`^4G'EbfNUMEqJ$joPLrum8-LmP:N/Oe#.S<`Al4OlZf&an<b:5B-%=k,BR0cCXNiI$
d\S_F$V[<@:2S;'KZu%E^-T!V$X9X=&5?XBL/=]A\$Hg2MMjY>FdAJm2#PN5m@o6k2$C\M"$`
2Qsm?ML<krno4gMr4X?"cJCugFacOS6^.85NDhIXS8g!bf-nGK"u(o7M$N$rtqHgQKS=tb0[
))q4iT<9nQb(^HQPKcd\)Mq-AHLBm@t[d!t-uZ+8%=+(8bZiV'gUS'1+f1AXFBpS6*?djO<*
J;.HXb39dNOACPg&XUWO7eJ=rhpl+gA88I:`0,<\+[*He>l9mAZb9BcF@:_;(LPNS?;At1K;
S/-/"^_]A2Y,_pbN5h9ClZ-1WHIcd\N9\q??:MoU*T`oX;rNt_0R<$=a6]A=PC*@na3WPQ57#'
sk`W-t-!pL"=i8JN?iMK0!p;Mj?AuEfRVE3p%-GMVfZ:"?CW@<9ZTca^fcY=BjlnN;j2.dIX
ca,u`Rkm,"O]AulEfO3L4a>'1`[I\R913uNj6iIZ8oD]As?H$fV,_aA3f8p6ao'pD,,n*.+rmb
G8NsFNp`BsJd=mLbG_<Orn<uP_al`B@K^"6U5UHe-g`d]Al_e;<.mGFUiT;&,'aXO+mf\dTDb
O:(^14E%e&R@eA7dP5T;#Tr>H94dt@S=H)HD;R=eTMi]AFkJeII51GLsW)FZ2E[`L/<XXt7d#
tsH<Wjh]AFbBBW@E[J3A2/e;f8-)j*8YTuccaK`;4a>LIF<4C`W";^Y<i6Gf,d/*Cn5"Tp#[p
nW16SjeGJllgo[-pq;ZQ+#p%UWOKWA^%_(4npoue%F!;aab=7bj%es\n:Ir[k8'8fO@E*\t&
UgJ@I<[iK.C22AGWhdsWf#rCD<Z8GQ*4rR]AR97$_03]Aq0>bF).a"o0H\UsODFE_cOH<5"7_4
_J<iMbtksfA9;#tAsR\?P]A7(S.k(qS\@#G:2XhX=hO>/.5:XE!AdkNoPL[BFA<I7@UhK)Z6,
jnDl)9V^7f0rkjd'p-CY\\?TU1@2m"+31F!HKu@AkKdpmfO[L^^>7?*`3)2W=@@>2CBcb5%<
R>r.S)7%)HJ3(?6#=+5:(Z'RHERQ=_lD'!?sohLq&gW=2d@,;O[Q%-u"-up[DM#_mFQXM7J\
0qG#Njk<5S7K'6pd)aeta%Hb3'<G7bS!gLS%4u77F6=Q\D'-<`N<El@O3om]AIr#WrYr]AW\:]A
qd]AlhE=I[jQ&VONJ"SdYdotWbDMhQ?9?'gVN$5b\-S/V]AGk.a[*a3+Vbb%."?f>B]AU4XdFsV
4oj^'-_RtG\#5_P-:Iqp0B%<a(FcdWlYC4`:F5A908G+$;U!<YR)DT9S-[mpoMhpL-!'>HaU
R1G7:,/tIJQ\kZhZCdjWp9rG.ZisH:&3QB&Yk-'jd'*=gj&Oj05P^dE2=7o+`fq9hMnA(W^T
o3bmJirBrA4$@M_R/r]A6+>DIs#(refgRP%_k6<7aW<=0<$EMM;2H/;9Q`PnO)f#TGs9R1Fc^
]AO65bmc.t(DFElT\egO$V7Re_^!86b7*lB49YU+C.2^3MI"a33,m,mU&TK%sEN'+-r(hGLMF
0KTEA<&j4f.ZVl5^(6F7V$"tqeGDj.X'(&3Y\od(_)kRl<Rm#)Peh\mEP"RRE5mJ_G+R,Tu:
T":+$$"CEOOG#sm.(XC4f.TgoosPXn%]A]AA+>TN/NMUWT<='?5^`#@mkr[TgL<<Oan\6Ds_\;
^f/?BG96[W$a+<cpgDA_mI"XQMHBY`)6u]A_35>sTS_([FV!M5pcBX.!XZ>EXM2I3I>M+5%UR
kBB4`-KL4Su>eq:u[<9@S3[-"E-F(oDa6+HcbEQB:YflQr1;c8._D-o6n$*j(g$+.ackPU+<
9(EK"Oqqs=PU!#"`4TkI?^t=]A=ODA-F(Hm`UgV;Z@+BF55^hh^@[E,OcB!"#',).=\)l<;4Q
G!sH44^%%LAU*#He'Z'2B%i;(Z(Yd_(MD=#EC3A0ep`.puGAq1&bo(R3_ru'>XB4-gj(,oaM
"ubFV=Ta=B/jHh/[\;J]A'[83[;@3Te)WP_X[qB)+=;!R;CBBFo\R\iuqNViEd?TZA[iW56[t
aSbBGnD_EFJG@H3HRdLsQ.=@&.oZusSs)n?^N3qT*?#l^/QMXb4L)/rh-gObYg#U^H_f$+`#
s.<<#>Tc'M?X8A'qr=lJk\]A=B+-aaDUr2F"]A`$jX#f>j%&)VMO;neEq-Ni1Q"=Kdl2%ooP+7
)DH\(cH6s#!<EMAIL>;6\iL;JTF1cbV&f9-_2%0;ZB7tI^8'PUO_3olU<@h)9PR_ZOnsX_sa
/_8FXIYL<eC;ugK"-f)Z0O3.*LH;uA'*fr"Fu3>ME]A<(.<(1:=okDXJ[6t5gNn0Y]A_f5(ih"
uWN;7VbBEhRTrA;*bCB*0lj"5HbolSjnOKP)#T5fD4Gcp8>-ZskjX#_+o8uP=ZaM1@IdBq`E
p#8XP9$rKLnC@,9h&0;tn1Baf@@&&g@W?C-eYp05^iSe(^I@too*H@`K'\A,5NE7dVf^p"!=
_u>B%mGeB,Gdi,%Jmp]Ar2dIG$P6@F,:e<j=ku"Z<:bAfkad,a'70jl.d4feQ-RBY=S+c`>nr
8S%Jt8MRSbdhol]AZAg]APCHh,U1/(XYgoLl/EE+,r84s>&%3Yi>/fPf<Wom??Z.=#B=dr-ug7
"u0W_^]APpm0\"C8oB1T\,I>&S.`j`s.7ecGB\6kq9/pZrdj7O)1P:GRbQtI?^A=ejF!p0q%n
7e*KJ>-L[./Y+PFUq`j$PfSlG//Tue,Z@m\bFUQRSqZVs==j+KtVA+1.pC/+t=WSo2LD"V&b
*fUj7fmg,1i;_s`neq]A>dij#S-k8)T!1peRA"OttV3+o&X[k`;n(O(IjHoB)foOE/"3D]Aja,
2sPSsb=o6?EW]A_t*%&nQY4uT#"p&WUd,[0C_+ofsqAF5W2m7Ak/jYbo4)4E'K<->s@##o=d=
3+X*gcmS6AKSLD1&1l2!6OhpRepm,8D>i&j^BDDVAE,^,pSrE"q6Qjdmj.uAT$4Og/67Nt!%
5_`P3V>e3^LbTVX.JL!i9$_qF3;I>kFt>FqqitK!9dDu?o^@MAB+/-p7Iq[LdqC%>Da%c*2J
VUC6%"kAp]AZ,iSS8Woe]A``V+?X(Bn,#&<&ie)G32f@/PN6ZUjTu%k:j:31AYqd9'Q2uoXdjB
?YkBq5p;Rq9OoFAU,,.:3b6^#V-B>_=g\1&^?m/f1$-V.Q;0]A4rdk18^-C0Z:rTp2:#@M0%A
n7Yn$WB%1=*DNqs-aW@k$bR"!tG'0(P(P[Ck,jgnYjgR/G<j?<Ze&C!W!TM]Al2j]AQpH2DY!6
5:M867MBH*J<+/&;]Al;L9Kr'-4WidUPUc/P+g9^&+0poYSRg#U.Qs/mtg%clm.:-n'o9qOkf
>WO*F0L?##u6==jL\>akK\SNL&Ud!OheZac+nDZ`6<:&4L9,kqC+[-QJ4f6!l_jC[^<8`Ri1
3-lcA]AiG63uuB<WJ"9k18.V<7(7bR:aG2s_/0je7";^K4?qZb=1E>R;ZD!DX?<'rlh=@*/+D
[W?%2^Ui*bpFAJ<)B-A9Z,Y1S0MhAlq=9\Sdd=P1*WR1`/s1[TIcWYi<e.@;6;K\srZ9rq_i
[Zj5NkQ0PqT%*ZNKXIUoWPo8[L!9;so,T!L!/lL6F:aDXlh7a\i`e&NlkQpQm\^9"OmXINNL
3/PW%!_3D'!Tr-5XMsqoHO28Ne,!Xg7Rm:@[8PI4]A72$pZ[Ub!>F[=i1+>Z<(cGMWo<K)_TG
]A'8f-<MsM\prjHEY1"0b?M`\^K/qLKoPkc9J\>.DoW:-*;eqT7b%L-7!o<[?sBLWn!iD&"'4
!'KsOrjq\9g)d\PLeD:<W0SS#);eOHLA'c75o"U6<+X$45"?D1IMbqo4>O7c:hfi9]A?!($Es
E,:6ips,Y>rYb+N,?.dafZ*;]Aj3f@@TaUQC&;P?9EU#FFDO>i'@e3+<;jL!Pd@Pd>5'kTgf+
uMD8L%hj[=I<=pA]A'r7>!Rp6PS\7[A-:(5L=c\c`'-Z,nTSJ6qA<p#ai0f(b-=D*J:Dik+DQ
+".t,2SNY^Te2/J":)&6M@NdG4Tr3AU;Ri1KDB!N65^S<FBH_73M)'Cj@X!R]A)8suMr&YbkG
;MntGRh&'>HaDCmD/O\[_Hf(Qub^LmMad><X)j5oea"d"*R9-am-bZk.BNR]AIc=,G>0JC?rK
"nb(4XBga1U-6bofpk3cfg_-:H"N.lu)ZbdXNY;a0Vn&2KW\#1P@H5(NZQ^_Wm&0;%SY`O>C
bk4C(o[DlSfQ$ITo=LKaI1A]A$KRLM`Z-DUJs4mR34l'$pK-L7/Gp\-Sm.t?Rp^cQ?VijZM]AO
<B,'6Hc&Oj![b)_0UZ\":_\XF]AVr>si`I9.n<JKBer^2o!PBs*FnV2acW"LQVZbfN(-sGl?W
X@ct7g(r',:?]A+BFD=tWn^#_79,?'-h^#$^YXqlFCbI`OI)?KWnF*-1ilGt5u<NRO?dm:)e4
j!Bp$u_oJr=56\.WpV!92rOF+IfVZTdN.i:O?'=BVuMnPTqtV2fFmYmMC@;7p!*[Q*3%F2pW
&Uq!W(H#FmTp[?8[ScXANunPJehW1^99?XWX^a2i-<]A5'Y.M(487BZf6YR,6b7g%Fs7b%o%E
$k1D,2N`2Ih/-0#85-6"8s"hDo9m\0N_Yi5;t/mqM_N8,n]A?ohI*?(Vh/]A8EI:O&fh%Kn_5l
gf[%6XRs6jmrW-)cpBINB(`&B+GOCl5k:b!#,p9P(Y[(L$2W/Y^kI8=3;1L8?!Igt:N3j$4e
Jp6VJ0QS_0HT)e6NT$h1MG3HCq,hop#]AH,5OG7=C[>,K#W(]AT#HSi"tH=YGo?8@A2iGuXRA]A
XsOAgPebAmiP:ul^E2!PlLR8Xk1J3iZr()YNLY$_NQfL2o'P!qoDde*FB$NAOqJ&@\kpM`*-
F;+k0GS:QWQDdL/L$OXBV-ha/k5s*Ncd>=9#$1Y\Kd1;:Cma/4fS%,YD.o4C8aL(kd/^qa(j
i:rpqUdl*-DcCRZ=)jJ+G9>`pRc(8&j\&FPG;f!K+s;pllQp&a&':5.,c.]A53cf8.HgM"7CI
l]ANp+K,W]A9qo$iq0bo0P!iIDZU'IF;3)?j'(KWiV3h,0DFn]Aj.qY=H/"T1@H3!PTLlGfAYJ3
cdE\4oo((f-SA\NJr\j)dfqS&HU_if%*p!Q$+0u#0jX^L0$L6lQFWrU:UNABblQriAl>#:N(
3B+so&af,&-&,UoFrEYG\N;2"K)ONbdoJa>nfYg29Dh^!7h,Y#g'mC8KkmX^3uV**T$9=]AJ8
Q(+Q.TG0qB<5@#E4"dou`Ke$?e.EODrX7rU0B*=`>@nOoTDr'g*YblE9Z"Z4C$;^s1dh'!a"
.Y+[0]Ak='`I8qo<$flP2@:UVW:Y@K^26in`P&?FLnD&6ZG5hfi(+%+hP("dB[L@]APda3d[Gm
%*M<"^0dIJNfo_Q#CJf[%9,cUeDqO7slB\u1@?'%FqTEiAsX`/"E'1$lC(&q[P:rGA/\&3Se
%$",=&JFh^$p;q!2pn!r2b-(M`S$kq"X`N&dXD.D]ADc%&ggFW*Xb'-6>jtZFrmelO[ZYt4)Y
S-'Uhkcm0PF%X9M#<^Sge+?acH"Eh5#-))7GW%A0UtDtbGcXfJ@um,Tn@58/aPK5d"DElR<`
5Rs6ktU]Ac.h'F&Dd_j+kh_D.%>6hHPT/M6t0[-m_@Eh59)>aJRK9GD#;'!%1i#&Hmd'[$/u;
qtL%R+hqK^fSsG0ph*)eirCG=-0)b_qB(_\/kq!M9$%VE:TYam$/aOlm\rH9f,n6j(-Kt/=j
FJ8kUBD?iR^\#m!it:Q`d]Aah(td74VrNnRb%lC24SUTK,5WqNTGPJh8`b^6*PpQ[P:XE%]AKY
p4H]APuaU&_/)MJmq,\b;-F0fo3E8/[`>FJV0V^3-iY6WB0I2Z@rgZu-kekQ`oS8I;3qQ#9m]A
;!S8eq,#RoQt35^AbYH"WN(T,@5JMM_?>o^O@6f".bI;RGI:jlii(+pq.U"@p$r&$o+!4JH>
s'A?e+`WC7TVL#+#BW\T^*o+!o<.hL(*=9o@S<\t($.8G[Bq>YbV9f.[?P=LX;R4&n?#c:\W
"9&XiajK=9_*S^LF#5Y4q2la8#"')WA,7-LJ1("%UQmrng9Oud>/hNu9TK:g5</iW"9&La>_
=VJpN#iFgoIkY[hEF#8=^V8,j,J)qX/&fC;@l@8Q9[5K@K_3,IX6'R9Q7kb>Na2JhsecTtpP
><UMs.mEsi_s'>i"N$9TAKH^Uo>)!t43S?*S2^cuGTjA?ga9=Z0SA^+H7bJ5Tl(AX'Qf_omA
lQRoJ@U09B"D2%gAq1fjF`R:/>-W%#(G(u1A6ki?_N5l/\1rUVe;bmnGVV(9m7!3II6L#Sd.
D'g)lW-o\Vs[e1/5LR"'9:bk1A6>OV2Yle5)Nh6R&f-JSf8+t&5;alXNTjH!jF,S3I&b']A`*
0b5M@7TsRCC?%taDFtcf+ho\YoPXKu<.,/C>'_),=M^:C2;N0,I5u,I[n[8B@]A-9+&nS=\m!
AhIXB?k2m5tFl%f/8_kJR]A;>ae]A?F*T7b-L9Hss0K4='F8OuUT]Ao=nXYpo=eDERqQ*EKJ`9(
kj-9'3[0X=7Y9MpB?/U?F"e<&Vn&/3&:hM&`M"f/O&FS0-\0q-S\YOWk2NJR!<XVbI'/TdN_
eRi>SdaZ[3uM7&nCrZ-79BdOpk.=(`O,9(1!Q&2/p4mY6hJu_,.qmJ0U1>>C!N>H$O9"[hLc
ICEeseBbt>?ciXru^[8$fkJ;GF.^ig_t2E&'%>`Q6G:(<E-==nd\)"dUpVNPJ=JBjOb/%!L$
KgL"Tmp>*hE9_bB9*Cc$LSnp_]AhpdrY5et4R]A"+j<<[U7o:M<8X.T8BCHQRjGFX*!Yj#7X[)
R*%qZmYZ8&<.M=Q844B56E*jj-7T,<M%1ifjLandB[bET@50pU#=g@3kl@JHr;D^rs=rgpDa
JYb\RVKrCfWF#QIU`O0d%bopgD67HZi(`i6Gj3@IT:?k#Zf;j<YN\,HrI@S7!$Dj,Y<s@S7&
0:7kOo#cjpKNB;qqVsB"U3*5J<spMVrQps^k%+YC$(4a\DiJ>G&;+%A=.$Oe:Qi3M$$mZH\p
^)AbgEnD4QJV=hs<EiOm4fo/h[sVk>2@<":S++[QO#d:+*cq,DH,q9m:q"_\@r4WC3Gh2'kr
LtmCK2]A6Y_RVsGSH1qI5$N!EOY:!&ZBX<6CI1_`e)36WJD^DS(onL9%M:O>%i[,rLp_<PBCL
soA_ZF+25jCPSB)mL(+6lgOr4`3(em5@b=qip64s'^KZq$206RLan$UJ[e((L';T571Q1l+*
fh3qgeJCer/5nH9?GKasDok=6+Jss1"*1>5,r^>kp[?d%2bme@7mod7ERn)jZJ$?e?cG/q@a
:)T`3+p?I+?oUBr\nThUl(Dtb(Rgs85[>9CWTk[*tPMO39GTFaQi=V3VPIR@`MJ2h$hX'.#9
@%hqhh%O)R!gFo2CJRIU^OO8]AX.Q-m#`%!#2ZQ1afm;8>kgQslm'<O!Zm"aT`6H^]A>%7g[@3
EH\C5<;?1(-5_3_*rN]A;99FH9EN`QFjj!KE)`"M_4Qd1jIDL!F)Y92mXD0:Yo]A,*D`OZnMQ0
8e+'#nPu>R**WlrTY/4jMBW#gLo]As3=tgRR8Fi[unf)dPJH85W=fAZ?MVHg4e:nF5aX2.OG<
=!o^D9!dh]AWLbFVN"ZCsdjP`u[MqZ7uXo",I)CL9/.%/>Eb#e]A#bII/%A!loiToCnM\B8PT'
na'PbqGKa`<#_omJHls)oH'0_*Z06]AQ(VbVc0(\\7+,pF+7$u:7Ld7a1nf@2m6QMdQ>a?OO[
YrWo*:E4E-<>TPUo%^0U:GKI1':g:bLii\-4lmAdV.dc3bHA4I6\pKQ)n1OYOoFH)7]A9B%lP
6^s"]A#u6:!mD5&cSc"AC4(CU%Wa(M[qN`#d;(7p;h[J0fo-Y=6"H>FA`I#SY<Hqdd21t?Cb_
]AP3L@:Jgi;$ie]Anfe#_aOA0/4?Vk)%mnVN1@3mP7TQKgXVV6h819&htaK&>q$P@F'*QO`"6=
iB(S3\5$#RT@8^n`<o#TJ0s?Y2MoIC9RZ"i%:WXceOhj/-Y66H%-ssEXC]AEVQpE/Oc?MhR0+
qN>A]A;dAnHL:8mm@G2W.L6V;2#"W.8eI8"ba:[W.'Vs02W]A^k7e=&.?W^QWiL[\E?_[s9ddS
4R`;)AX")P*3'j;Fg4[3\2J+h]A_Xc0"J)W+$f#79mZBFrAu=>U#hn?G:Nj^/VB"28dY;P,8e
[EdO*C84LZI?R!C,;hW%5R5&D4f@M90Hp_l.,.9]A]A\`EK53Nf"'^E(fGdN<sp]A=X]Al<a@(hN
t3QfEHTtNVc(b?H>r0OjBlJ2p=Lig.p(OOf>,1o(/WPe*u[3/'antPnfLQ-74^4mM#9=XOt-
IKU`f,1(.Mu+QT]AF5=td#5'GrBl^-*S9+:h>fCCAKaa-]A3_^FW9AZ)u&>9+?k=1sIK)[n;^;
e2oSrlAtFOBlGaL1c0aF+L[#Mu]AAs]AO\Y:XJ56hYt]A*6`_9t*2/Vg=:GeW/l3\Kc/R,J2Nd1
T"5,;DoTceLZi9<Ao`LW6]A-<`WYGS[oLf2tr1d[7_5^&HO'=.f4T(C_#"V4aK3,'\E7n%RoP
BOPm7fbm.u49"QtYF[t.pc:Gto-WV^DXp+\3n-hYp$N\7(g(R6^Zk(P?Ir*2/2M(UR<N[l.b
f`o!k;"*5&9fL_Q=D^?^;J_G;I7#$f\UK;Q-`rMa7XBZ=eG8?gCR8NKEMEU3D&%.B_9Vc@n/
1f7juMI#Q(3nd6i#;?3$El`iu='dlb#(MNdr0KGqYhIR7I1oLs3F(+9#:Ts45F3$?]A=-:0oS
Cd$f]A%P#N?m?PnIb=I3-3&pqmHncZA.n\:q3<*i5X=6e\66mm!l7Z"P>4goGA<UkTfkt*H#C
O=`C6M*@X*ia,*k29/"rH#hf>`U3!QV4BREMY>2og-@<3Q`Moc2*mN[=XG614WISh'F&+$g:
b0%DRKmO^@&\O?97e_k=]AO8[Im1)$=FGOoZirEaFEt=ap?Pr'r=\m,8U'DlYjg6skpSh.T3!
P4Oq>(pt`\,)p<e:pfVUC+]A'-=1o-W9Em]A[c`j3OBklO'[)01($<)6h?8Jek-J0IAZURV`C.
A8(%j-,hU>!Y7eq2Cd6\.OjOEZH4.-'E%l<PEltaH2h,aWDng#Hk]AhdlicgGbVaFQ#b#[?0W
_P\h&t0%a:JXZG[W"cqMDk6*cHE8i))ca+UlRJ@F1MJ`Y3BYc/YJh(^6)hJmLnkPA"s"^MDf
Yhk5<M^\H/im"U]A&L)\K)6d+6QRKh^)g,"T4J%YjW5O)n):jA-M.[>HMk'm!)Bm.*^uTT'uY
'RqdeC)['.@:95!a:6lfCj9_fAYI)#l3!kXnF$F*]AejH)'Tmp1d0VC6p`&Y1Q=7_p&nB8Y"]A
MT+aDp0^M[ajIH#MaqE=l]AW-C7n"5*s`C*X_!AEF"pN$S]AF$:M&WOQ*."sKPS7W1L_F6&+MP
hdsH5H!G_f-nYN,+j/;[B\%n3dRgcgEQ=I6K'5iYCi2qFAI?2%^NJYNr"3JBmiK,TqnD"$oV
#?hJk^b<\FWl#9;;S8''FKd[Y`EqfWu[Yte@W_8[N\aG8><'e#;:(MWAHHUYen.rB^F.(KU&
=Ue$?nYKn:N"NXi%OLiq*cPtqFdX&lg3:tgQ/4NC0Ga&hOYO;8.nBa5l5!kLKaW<fm?10VK$
Zu6RH/NVbU#]AXpH]A%63O\D^4^J@&7"HSFWZn9n>LDLB@;bmNj\j\:!1_D"kf;:1lOY]AP,M?Q
kfgs6H#=e]AU,Mn6q%:X.m[M=uOp(ZDsqbLWBon7u0lDFQmo>*nnZ^e;.#8r*&AbC9bt3@Uol
^9&^L9TH:nX$20kj16*#[!m`gB":cIH'r+iYZSSJe**q?bs-&GA:g0TTBTqNg!qm93pklPWp
S@igVZLbAQK_#R@8r*M!6#Z!?sB*h=[>?iQReDZ9q8VQ;31/d]A)FK!',-hb3-u#6\#08OnoW
_O]AtKoSN_tg.Ks'fp07a\R!CB`s7;O4^"VRl(>sekXQ%@>:25Ypu4b]Apaelo\c6fQRk_7NlN
NTB$!///r7r?<W(q_V_*eZaBu_dkPQ2WlIA`T.dS&Y1]A#-PG1ppkV=F=UoS4I7=OU"T;-rYY
cN]ArTf]AP>YKtKj2T/+XnkP;3":[IL*$iNhQ)&$qDG*,/n-))bB+eU6IP-[6[epr&@"74K#:I
M1uR!Bbb:9)"<5+;&;@iL!F8UDG(\Loj5>eSP(IWX!WYdCJdN*A1"T=r3\+U,"A6(=RWcqR^
*"A7+`ObE7PWkMI7]AV^+$i`u;dJT>T`nji+S-NO@6R0QTJFp-;Ze0DJrS[03:KsqTamuJ_pU
OeHKN&p<qAk*J,5;(IE:f(FX;)FQeP!MX_l%iEH;-'+-)@a:-@Pfj&Q+#LeQ5n>G@J4dk8uZ
Vjri^F4p+b<=NJ.YqG:t=83&ZbsF[.iPSWaF?d1ra>Ra/<b<q$:!PYgrF7BD"G3u0^fc4YJ,
mP25YO<LN`coVD4;tKfst#0=ouMZEN7nIFl$k`4Ni%Na]Aa9E>oc46T7L7qVEOcX;",fp,Q5;
SR$2uarMTh=e3S,7r-:jplBt%_<L)%u5/`EU+/PWaMWfcEgq!e"I[.%d/B(g5Zb$eE(nB^76
>3XX5O47IctBm4J2Pg2W\.hsc_+1f;]A>WWGS'qpXcW/'1(Y[O#'W9`;["poN7_;HqV'Zk.hU
j'Po-&q]A)V1%D"h^[b.ABV=M;LIKZ^jnkigZgUXDQ)SiJegAX^urNjPg@Y^13W!8auSQFSa]A
0S#<(aE68P$0%8(LhDY5hR?I>C=h""Fk!\+)kX=%6e2Tm1F.R(amj:AZXj=97-^@(^)$CF!6
>oWm#K>r<m3<$n3%0\[C9i<rn6:>;<TIbEI_3EZ`,u>"=MG2S:(8)j%!`6oTGR*756Ns6S]Ae
\\1Sl'.J%J5+!MnT4:8Qen9P7K:iMON\EULZ<TVYEYpQ$E%V2pW`Pof;T*C=@]Ae[NtXp=39+
mC:.GUY.ms.kGO3sS$]AI]A+61O`X^:#7d-;=_Xn-JE#"@*ra4AL'cet)rV<&EFl_2\%^q(kDW
H`Hetl1&KI>\g!f0*A0oG=/@M<&Y#^PEol30]AJ:gUb-r/ltJlo1*XN^D)(m0`Y9XjRlCn*iQ
6.V_*?1t-Vo9g^?;PkATI02q=&]AD:DXkcTB9:OId,Ke7.`N0%J=ee5J0VT=9DEcV!dLsF9NQ
N4[)`p9gN/tI3s!T#cBD&cGbR?c4-g(u397NlkS:O%*I0dTI2ndi[W>"&VB*3<A#S$@H:bkf
%jVaMH,bR(OPTj')C\]A"&!J+[EP^VeJd#.YZ"77#T<C*&Vd?K9=%Ze^B/]AuS#rYkJ2amOU<.
JGGPijiO_1_C`2Z(%%Zpa<d9^7V9R:]AOrCC(&oT\!?!]AQ:pI1DrDKD^dgu@`Tjpa(E$-T#;+
g89p)1nJ6h);"n@27#N=O":/49Y.Ol!709a]A,<EMAIL>>L/<U)]AK`B&Gnf6LdUoDBl(`Y
dbNXA1:^2IN"cLZ7fn9:$f)D!+Ee4)TU>6LfuIa@@:?$pmJ5`qK"C"<_Q#:V`'Z4<3^7XPjI
/'4c?8scCWj?;lhC]ARRd_g4W&qkB_)&!G3'm(%_'GJ#^Z*;/+!VR]AHlkq?;*]A1%/gp"$MNa^
$C()`%C[KKI\>3Z>2s6d-:m/4jLsDJ'-.&1DdjkfBK]AtSdZKg^iP66s.jQ):-1?b(,Z'*:33
A4D&.udgc/R?i<9?R*K4@Xd_lJgN51\cK?r[gGJr^0D40Hm?lU%&`fYB(63QbV?@e=^fPVN/
IJ$*$O.<7f96E2N+;PrWd/>SQ#,QlJ)2``L8".\X>WE0Q$5f\Hb;NoWC^c@b`UVU^$#nu=R?
XF1=G.OlpfUoc)QH/KQ5'FQ+kJZ1)\FRGHfP+pPa5g117@e'P^5#pDti1i)=Q>l!^G'IRsaO
oFrkg_?&s%G/*)VW0>IK]A7%@3lKo"VFp^K@D]ArG;;jJ0oNJAX3l!A?bog1O3!([KYi]ANn5d8
eW9ZuDm5hQ\H$9-hZ^]A1)H[sh+Kb,=&nVacVoD9H8MY^:62GLf]A)AlcqE[kMlAWD@[C'4^1Q
(,DVAtm#r@qR9\UQ$9bG7m,M'0<^P^kOMRk!D^5F`hgA!3YSEYSC.ml5uZtC^Hap<VE*/<:-
0OJFD\[ImZ72)c9smr<Tl<SJ(+hJUuFfj^LL7i@%T&rS]A@&ON&U%$*/$,n[t4O:eLNZ;EU)G
1RpMrpd]AQ)\Dm9'g'+Ugc_@/'$`9P7/dU#Pg49.Rj)Q#&KQ(,8OHm_)pQ%)H]Ak\$(-RYi-UT
QeE+P?58GQ>bPGl+m2&N7F$+Vm*.6e?gED4&O6:k9'a?6;?n_I]A>/Y:(R%B*Zbn)*OWB,#-/
Sdd!]AF3+6<MXLl\Uf?<rsYO>G!0+^k`M:7U\.ER"hF&p):.QBu:#j`2go_0b+).?@`Q(Hg2U
a5K@SiXLjrWhcZ&V_<cl@p=".Fgg_<qZ@P2E%U)fYLoQ)1n&7E<6gS?4%Ot%4:N.H:XrmNei
^Up*VBmXe^4Rs4%e#5#@q]A3r'*)'1u/5V$IWmEZd\3[Ej)^E-@QBg2))3cpQL)fS&2X'^pf9
Qs&Hh\/W&)qq.a#Sd*8>%B>Pm;,CalW\:m27%ua1U`kFqr^5ou%(,g@`B[;&#sQpfk%9AVL&
gr=Unsj0*s_#q4]ATK1Y1B518P(t>>g@C3JtP)c4mqeZ>C65T=&gsQ\0:nr>TPYB!)5C@K.&s
g<2pdL2f(#^_)tS;F`f"mU72=BQM;>[(.4ek)3#u>m($`i^NAV`).^Ppm(4jC,&Pa(JLX(sD
r#535]A*p/0_Nl`o?K45djY!]Ac*]AY!(=[A!A*nc;+8:\ilZqiLL*EJ%j8:crqVW[M?_7r86,q
ZrH(55dV)]A,/PCS2Y(8Q'q8)>Ei!-s*ls5S_fpHmbhAi"37Ji/(AMNhM4%dT57JikQ*TKp.:
99"V,eYER6;3**UEh$E*e9bI;G&o%B9RL3'Mjbpka)fpE;'@_rr:5?F6E^T\INmb33St3*[8
;"Q"@-k#4b"R2.<R]A/ItuCbU4s6GlCR8H=H2`[mBr'hWnGVm_!c!a4CaGb_DfdVj6#Wn]AG1#
V's]A7jO&&mX+m6&%j0W<jZa<>BG$=\8a$>:n6\'P9)f9/YE5nR\._I<"e<H3a#[h+r.?)JD=
rl\Dh?I#Zs6B&CIuQrNaBi`LkQmq).)@8?qF^0+dG4TikH\[UgVWqo<_f>ckk(.Ia+3Q5q\9
<[m_6ST]At7hG4`)PXrQ6RoL6ZMZRH?6DMR&WH/3+&2*[:#2:Fp2NlE5;$qJZ:8o;gP/AYA<7
)e*nu<.am,)eCcr1hhngT$/!:BQ[D3K?k\*/;j)J?:E[IAbm$2[/6Z(fj*@E;q_1qh.NskQN
!JF(>DY<2]ACH]A24sECR!'3lp?q9AYHP?rC\Ao[\6"XoRggJY$/GX9':IEe/tZGE<o-)Ks$?C
Vmd"rjS0u4s,@UEEe)H"=#/+(#VRq^!M`>`+(U';6,gA?[gjPG]A_[<`N.%UA!df3uC)JIbPI
L'U?qrF6Z9MjjM:BW"p6F$</5nt26Vh:tFFkdW?\;>l#=O&S<,Wm#<nj<AfD>Wg#B=qq3i+M
/E/E?1LjI;Aj1t-8rk`8i)]AS$Qdn1/*rNaO*p9cq6:J\8TM(`:47j!_a0msciD]A2e[e7g"rB
(DsBf1;0lG&QE1^ij9j,8mBp=fpdt>T7Iu%UVJ_Aqa>)CbjDUWE>$/*Ic?(nk5@Yp(52!#5^
SB7&)OpOAPS<.BqCG6,-0+nDZ**@-[RK?g$/YlPkE_c6F.HCYgC/VI12us<)3iehZ?>"Y:Fp
AV*6a#+_m,*aSGalQSJ^Z8cB&A<D8^+^H6$"*CE(E`:AoNA!(r^"Z.;ofis'&fak_!"U3uq2
\43;PRm$mkK-GuWuM5^KDrj>Hb9>Bfq5tE-,s-,K?nF!7)E]AVjb[1AMRb&(.-\"jgna5RKFE
*#?\]A8DOKin.%%9L3!OfuYj>#[^&ITTHEr-h*\"?iPX[5.Xi@68a,^rQ-GK0Bt^2'Me;nppT
6;sus)XQlupEUk(Y\Z$m;,nW\`NFZ1*0TAK'T;,t<,,OC8L@\Ze,St1AM+'r@Za%TeC6nkh?
i$%9%E+TGo6!/_V`0X_OP'!hq2/I36s]AVO'hijaE%c5f:$CHdn]A_j<EMuq,:8D/F[VfZU.=-
I0V]A[Q>El[/8U-o)V>,Vl&pnUaFQi>Nn-Ik+e\%Z!DcJslrZF4IqE8OF_=$6>fD1M-$/<c;a
8`+b@j;@)Sh;WtBo[FaoVd+7O04sI("!M&`pn5/7\qI/T1dWD$^_MIa1Qc23G/cAG0jVZGj\
5f,JFR)3K2FcmT9*uC!W"'fm>e<4-o'8?Ljr@ajCJb-)$,p07EZO$eq*Hg8VHPiV<8@!08C)
5B.t!bI4Q6/8eD#0p1+!aoWj^S?^je3:Bb_`n\3h`SnGE\6]A4FAa3b^e+L>2lNg!cKFeoN>Z
&XOB4o8=_o<qC7:DDp=f)i%'(7t(/DS_h3opN-^oW3%=pA1%]APQtukMPh]AnkYm3-MJ=#LH%c
#.'N2b(Z#MjI,1f9hSM*=JORZQe.uA`^'LNB.X9Rpgh[u*am8Kb[o4ilIKDP3(.+UspK^P4<
qYJ@0#SFnCk&jLG0:_>XmL&N<#`Q('sHsEp34"D3;,!75^=tnqh=D-@F#0!0aH.\>=)l1K]A4
.9[=6hR<PP`ei%nc^]A*Hl]A=MO-GDS2"uPVZYIK"-;"7Y>F_+K0Ts=:>UcT3=37AP.8d(%Zs]A
I-'Tj0Qo:r*pP(fPJ['YNK#r`@c42NQBk^"WK5a5&63<s/1RI]Apr0]A;N^G/C+9\mbo:J;8_h
"<<ZZL9IrKlOQZhASm-AZNWOY0]A:.O5n<ekc>F@^GL>f#8#lmjW\]An'31J2WCril(B7J1+;&
ga01t^eq6^DA]A+^!l6j\SR@t9(G;\r):`L2m!J66V:dBB>'Obg;]AR]AM%-G/5dbs9sY!9DOrK
$0H<@8'5E2_QN5Ft+IT:`MV#30=7-rol[9\"",.Q`mD]AB9@"k7P*OJS7[Z;.g`l$/l]Aq=5J'
oe'G756"(,XoTI&$hnU+Z,AcsQ72Fua17Re**E<,!%Fk;jo;StGX4?PaE/&j*6MGCEsT-8#1
V<Vo#=BMS'>e\_rku7m)2'j:'laS286QV0k9m">-kTAkP')B9G2H\#`bNNm;Ig.jUP*1JP+]A
n4=@[/*GTB9V)9f_pF/gRe4I)=o3/%p1Q^>ab/q-/G?4kab*2G,G)4[\*#OCC9`rb:\Q^PB!
e46W%K-2a@Rg25Gk#=6h0I3H.V08E:dL2D==[;crlEd80RjYO@[Q<h:VcRKU6=HYBf=jDlU5
&$B.PG0-(m/`AcH/%2ur4PM_1Oj7Z2_SZQ]AK`sq0o"]ACs,o)a#D[#?+T9fP[#i=N?050f*Qr
B(0`\NX*FjTPrD3>X6L*8PTnP[6.mDXgg@).mB2!QN]A1n;+TL5[WJ]A<1t3&#V7#\;fZ^-7g6
j<p8O3*0pL2KncE+!\=C&*&K)O&<lrF2Z2>,cJ!#!#Z#>^DD"Saisa5\Lkk?b0sX8^SGZbpJ
jdG8`c?#^-a?7)OlO6:;mN2Q?Jrsk9.08N>WVigpP;n0r^JWd=d`.Ib5IFOo(8H9@l>/Xl`5
Lr\M(nO1(%f/C<Re6Cc2CMZWk"3%@?iqPsQoCs'0!N\5p%@j-jheJmp$KjDFG4#@;Ri_"a"8
"h/6>15EJnk*8?_"9L>,^0EN>#^$pbDbZJP(3$@:\&n%-$Bui\&M$!WY!&7NX!Rjgou[)Rc`
E#b!h9!/ft75`aLF.1nuVuHJZIpaF2dLc4TeX@Ba#3WtJ=;Z[>Yk9`[>#er+Rb;A?4/2(G`b
aar%2alArO\Ld*2U?knB0^ZNlDO[gP+H<8(E_[Hj\Wn5!e]A!W/]A;qf]AS%labI#9KqH^b.S;[
TCFPY-B6D'V`5EuE8rX%Vg!iF[?._/=A1iZtqq[O*g-]AJ8eoImUj)X?%pD<X,YAKB/0?RVDj
H/(;!s\=!Ecr_>:c@#=)gj.Kf.&\nJTYYi?WafWFNYhZAh't1FpdN53<Sggj&TBpQ[[lBIoQ
f]AhJ!Sg!;?\_uS6dQ3opT1R2jF[_nX$&_%W_=6ZS7lP"8B6.Z,,CaPE59e43pjQ4X[#16T+H
MB[Q`8UZGTde?mdhnHL6oK!<5QN0U`s8#TkLL?-o$>9uB7cQ,EBVp1U"B=_6R<HcFUH8p[>R
#4BO$UPu5JS$&OcL`j@=cK)_"6%Nd49:^'DhTcXRaMGuo>2ku"-O%TQWLlD#d<0_FbN#M(Zu
"GpXrDU&hHib_T4$7S3#4]A)nq@'-8iLsEmX>hHZBe@HmjEmeBX4meqcP\^!Y&&r*Dl&P27dh
=2b>X?K'Ce.op*uTp#9'9X/VRNRH>=tGi_D+HuD499RkWoU]A8:grUD8nM:.aUr$5B$9/p1@p
:!(m9br+BjaIe-?Q6M]A0C[=K*R%2+W[H:;.b]AEu@QMD=WPV64numWc9*6"k^4W;kEK9RE:J\
<@KV-gTQ**<hoa;oB1O)k1n-4iMXnAk%%K9[D`H<qGLM9U@m-PB="k?1/BO]A)TEK&3-9C!Qm
()OCE`o&s+m8=4VI`a@2GrF)UG<nHfhDaXoJEZXlP/pIWbt(cin<JjrU[&F.3TKmrijsX2C+
E%+LE%8$">N5b>QZfZB5\-l]A@X!X3#.P5EEHFLb@q,c(frGJI;&$Hs-_XQCAiRcoG4=%VL0R
Ir,uS8:mpdR9S[f^lGYTlD+aWY<kiRj!H\0b#q)7*lL2o]AL2\669catD>ZAh:\&e$oLO+rQY
nt<2;iVLPLAXni(H??9a!m\W7AjS:d/:UV#,sHPnGt,YF.Tl,J)4j*lGAPHI3UKcHiq-\fFT
th)l/Xt,rtm;1'nrE()L7e+d8cD4s"%bR8NSn%7u?aIDe(]A_PBUjni$MV7Re9`(VN9%bRotI
&E/kA4k02[[@Y2\Y8(F4p4`+8X+pVO8jAk'NRk'B('jU"]A/,cK+6@25YB$-0e;pc9D+2tZSC
3>.H=Z0E6]AUs?2U.1H@Q^L?gb&Fok<1TLj(M!.Q\.e%<JK[L3q+CF2Xd%:-H*<mMh^:nFtgN
VQYPu09[B'PI7Y;(.opHIX]A3o9C6'AmM!71Fjd-TOS.NRC:g6"C04Rp"$]A>@Ud5p>+_m4V\8
P.]A,7EN[U5!B,mR,QgZf@CC2HeFpFqq*amA<<3O8c;(&PI0qEKAuC'l>-$YeAWY9it>#rhGg
^=IL(@'ZB88q12S[gR[9JUk.DRU(oB1>Jb^*o`pZ]AW'=6f4Jb7k.33ZZ:^bniD(TMrFk8Y,$
<1lH/4#6(scFs.^]Adl2-JhFc%e/QAXL%"P;'N?G*3k]A`X+;o%u42%U^;-35'gq3sD8s\_$m*
4/[S>DPG9O0/V:NO/tZ-0%?GPNB(0Q;@;o"dC?cl9nN7;#Q@09]A"O(?e^Nljslc<Rq[h\%kq
do\d9uJt9s&9/s/@.[7G>LoU:A_C-!<`6e[27kQRl-J]A0&?D!G'(0$Ea7GmV#M+!iIXUYJb^
\>"3rrD'OV-libSfK)R,g).:b8"^G#]A]A$PC@SBq<Y(dsiD"9b4UO^tG?p;98)`U[`'e&XMOS
U.Q#R%TMjAO(_QXUa;8"74,F8WtPqIHAHickM]AOY"7_tN/cH:;Cm@=>J]AQK2s"N;V@<AlPY(
l1<tjk)0Q;SgEGd5I`B7qC^F6VBgd,JfEP'5n<A0L?&sRL,+7c(SIi$7[9Eo=Mnn^]AcU-XSL
X8<22B_/.f46)jh_iA;'=l]Ac?:+9Bj:>63;.[SnH0;FIlR0f."otj[+>.*8t6DR'6J9GBL%'
cVCcmbmPTu?q:.G5c-)QOe;#PcBckI^4@Ul;@N\c@*`SF/p_;IF7O1pLc.-r0O=^SHNF6Y1W
=+b85Kd*R7kEOk9hb2M4=(.]AD*<!*Ur[$Z>WW[q")DXI@o/k)+ce>>r]AD9O5TZ.3Ht4n3++Y
:P)8J&\lg<[mcr4Q#S#K0XWZ_89Y[5eAGP7,+q!<:FknAkr1-'+ETi5t0:+GRPLUQ?kb57gZ
IJbjAk+iB3?a,OJTKn<j@LN""M_9KR9L&`f`9H;W"VPCc]A*4J'YFBd6q+.2Ga_^TQ27$?`;X
C?QktHDWq]AaO9BIu^>_.fW7@:k*[@LRQ(oTQ)4E?=XhhSop-3tVDmT$RLr6%(V4"+7!K*[eA
1@;FHJedD%+7n87:dK?IYqDqI@hGIg3jS+"TN7e\i<r=2F:=5PhMR2XJ'gtmEEh;J.eZsPG/
OnY/[_[(NGiL!lNpb9;_$th]A&R%18n`TFMCebp`F*IqqZ\XQIm;4$g(n'\G'EDhkA3_;)KDO
Zfe^G9c'o<)7UTsZY8)n<b8PR0a^,#d;BD9,W"dIYu//02:?IYrTP8I,EiZro"WDPk.>j#Pn
Hg24.pYRjiS$?7ob%j>simtr;,U%nM:gDRq,D/\7DCeXtHi$N(72FtK3VCo9IX^)(Y@"3INs
CtM6k(FuJ]A&rWbt!`q^,"h8llILmJ<LQ%<oKF*a%a6_2#[]Anr(G:)6X79?W$h6pV=#/,8egI
/aAn4I?q-cq&f-;9_*m<G\l4EiL&Gq<b8)F*bIhh,L09FdLP$X?#eX^^g'cE1ge=06BT*!oa
!>.VO"8T]AliSFG"SndINiIY-IZiEs+Ij:8Ljl^!B2AimFo2sI]AmSqLqQC"+\K?I%QhJQ+cg1
6<Xt#]A2XC>jiqaV#cL,]AD@Lc[lN@\k)$*kr2uk0tp/+j>$RIjV^._D^0"UFV,hh_.V]AaN6ba
'#$S"K1)CV^3Oj=e)%S/=L)T4H+BI*mqL\ACb.k8M?Ki*`\JV5%6s;%?g45!naTVm&u2A>f7
Nj5e^V@l[;tW]A]AtF=BTDLnhhT'V&fh_?jR=;hmI%JUa*FIY_&%/@rF0.h/hsdYAp?3VRjACc
_<96R_bN#AbSXhJ(:[VjbTcaJmV;,aI^KRh;%m%N%-Yl22cT%fjV]A-S2Uq^:R+9Be#:38(s7
AMnm:%bph2H!Ru>ab(K]A@Y*=i4;KlOP)3(f%Vs5Yq\/(2J$lirf'<?MQ1pHP%39aM[)fqQ'j
\!n+#>N.#F3"*"X@,[PF"Q-h`R@nsT=)DK#u28.upc_Mjbkkk.dF`NkF/>M<sWOk7,uP2k8+
\Xe3mpt<T8<*e=%V_7.p9g!\+*6h6*"<A=Gd[:Q*V3@qIG;5?3rb^@WK5d!k`OTsZZErfe66
![pN&g@O$11^1q6(\UIn,3*gfDtopHN6^c\KFKq94$lI4D5=Eb+VkpG/W_V.!D;D^baX\^Af
l5+Woga8c&0f]A1GeH':#D?_=O-!r&_L1:SJ70A+2t+eN4WKPnXF)mAK/>+D5cS[Wr.D7BUEc
Wm(A(<ma;\(.?GeT0![-2//po2Y4]AQ.M*[:!KNB=rckahAkqPg*j5eat2*1cQ<30'=lFGS*G
[=doie_LTrhV"B30c1E@OtdVJ_SQ#Sncb2rH#HWPq2`&.u9#cmZ9)]A:WtGCJo0BYUcNDjdaU
C.onshM.j/E[`h-,OJ3APnrL"N/tPm#D>?\[7)6')Pe0C?ej^GLr3HM[r8UtR]A[W@BsZ>5?7
in@ChR404MLP_dABaP.#T4&Da_B\Rg]ACS]A6\gdm4s_JTpSGIe+DTY>\Na$jT/:9'mt?iP'!u
()pe]A7+_g74@0Z1eQMF?#\>lBL#9liLR*S7&'oYM?]Ad7@/dk,&`8=C>?-N>o$EXA!C^Uf;TS
t^7A(g9geA:/3`NKH#F$R?7V2H2m+V1TT8AZ^*_N9%_47D.YRFRhH'TGH%*#cso<^L-]AYf0@
?=D"c_R*O9\(`eGJqYqm\jnsN\t!ttV@"_/G'MWqQGKEnF8&Q?A3Xa_ipL\-N['R8KfUHpCS
gV/=%/)_>oSMs2q=!)`H1hF7:G9jW^;;j<O3cN/<6pIknqP6XMUccZ.NM2g$O%Ja3)If[AeY
'Es#1u^g![CcW0:("9dArhIYkcGUPB4c.lQH>.lgM.H6U.o"<Y,O_)aGN!6Xc(I>0`'#r<56
^!#Y9IBntd:(IaVAnPJ6@=CIBMoEsFs4/-\6I'afC:Oj/@UpUfGcaVXiP's)b`JBWS4eXoTX
QM]AN"EDA&A]A7'iZC`MEN*U]A(MMtKt#.XW8rZ:\+BG=ei"mi/s'/9.>Mt#aS^jB+bq,_3UVZ`
cVK#\Y6a!e`=AUZ#=HYU+tk]A-#H;Zuc,>>f'fSD;1!;b[SWmP3_h5igP9TWAP1"cBkMNN+@\
45Uh"^,]AO9nSJkFk%h)1#$Q]Afg^Mu(D@8F]Aq^o#M-pP0/6H"?(?jdmY!5T'Mj%cRN9Uajie#
9&50g3GA)-N\ZI*$DfjgkRYB`"W;860@nkKA8jo>?mk1Du1BR_o]AqFlpDa[9g?nZ8&lM&4L'
]A(1FY9I;k2=.F<5gp/8fZ-q$%kLB7T#lh`6r?[Bl1<845&1u:*^*0-=RpEi2dAgd?%bAYku[
h_gYSN_1'MVWnG8FA%?keRe]A>4tKh:,B.Xoa$.6m!2'.B@`o-%$/L>-I2M=J&#@#U.N,<d5p
pm2hlNS;5hkmUN^2GaL5$87uIPoiV<aK+/*?$UXY2`.]AVg3aEi\hb!i_n3pLj#S]AFfes5I_B
+ZleR(Vu6jA4dRB&t*Q*UNFU5W_?.l01igXSp7/S=\*$"[p=hR.%i8_qN-0e]AV3tiWTY'469
$3DM\Jp?`C:,+H8\'G![C&u(j;S2IF.g[bFnN9g!lPUrO=HP;WfuC/s'?]AG89RL"e]A4I9ou2
1SOO%fB:TM;BAQ0]A8mDA4/haJCL+E3`!e"E)]A]AKot;"j1^,j-3\>(4HueiMM^MISM/c[Yq!?
1`QfTE%mH5C'GK,$20^8!s1.H=mfL8tKF32>QM3F.W>8dqGbuAo7Z/9Z`a9Fe5po#=5gn>i`
6$#/k%0;h%j#/ZK(D:H_($]AN/*hMSM)-i42k)"W++54;UI#GKB@8\(R-p3iqF>IgEg5CX#^0
o=i_O/t^,-<`3n&1YT7-@&M+).Z87pM&d(8[jH4l(j3t&!1uN'5MGp03ITBnD21Mp"AO-W]AV
<#DTdN@(R+o^5*>88&GVgrK/rZ5W$;rIp%oiBOaM,tEU[8b[V0j%[8i4Tm.KV7^)qu7P_8TW
CmlTN?_e[)M3,4kQhf_\Q3ol2TXnFI8?M\N/LI#X8#k@j"p/Y&/cHdV[B63\f47c=_n@YTo_
nd>*.AZ$QB,)EdrB!9CbBSK.j171ogF1sR2)QC%2'VE_MBYLp&kdWs$VNoHHds3?UHlP#P[6
o>9FbdoiLq09dA@B*Zrm&W]Am3tAQtZmK]AMYmV8Op8n#<nAqc[;dA:5U^pSeSje_1RCd)*W!4
p_e't.DM)?E/W8sL(4M1pc-mlJY!?[Wb8+LLQ74LhD^d?0m[WiDk7lX/a)/eXd?Ora9-e-CV
5oE1`gH;'u5uWn/']Am]A0_p[q%WS]AW`@4-(D<.W@rMk5dV=^;/c7(p+kK3rh]AHc`c%*`3\WEQ
@55ZWP@ekP6[g+]A1,N<.n%4t(<@"&7=-+Dd%EerO9bDc\Q@p/G>T*tOUdB1um-HifD,U!]AN2
VReZHQsG$hq-Eq/cY*5iCg%JAGn]A#i.GjN'[+EA0l@</5)FYd@Nd(,VDodPnDW-=qA:lq&kT
tFd=^mKf(FH%fb]A\/r<s,(@!-2J4pSEcZ3IbBY42LunJh'm.GUXb*rL@0FuZnB)S2j(%iA>_
q;0rba99CWk'7bJpYKjRgY0rhSWpZAq2oti<qV[HD_jAYp+mMA!<MirF&ss.>^91dlo6[_2M
jF1_H!!fQsmSt_LLT%(W#H>bIIoO3]A'A@IM+-V__j"kQ`XfM]A25*WCLH>\@Qj;A<0"7)1ucP
)8)0d[TQ!`GjP>I%07RE?P@ol1PZ<fjr8H8'<mbMU0qeZZ"=!^H4Pt!Wr7_8pp+)EKrOm_`6
$b1q>gUBcd4,=2CmXAn?9EX=ft,OJo!=)5DG^TeU\Ug-C\'H\*5mhp`qNeY@?SZW8Mn207iV
hUEF)qq]A4WsL-\fV#^WYtn>Xp%%K9!]AiS/*S*%H(X'T\`'XP]AUTb0tt=RG?ih84"bVC2]A?1(
qc`MJhd0.]As&WQSjis/1=\D(>5['B[Z:tC9C366Ne)gk=.Va2rWD5ad_-0*\1G'hGd9mFo1n
R8sb=ns$W8CXX\?d>F-AMl@5m0u83;I,qi']A+-hIU-kb!?I3`Y@^M8(hY&pojW>)Vrmj?2p0
d(q5PpK[877a+if$m%(aJ2Z7$j4AW0)d?@TNGeYe%6fJ97YX/-.g"#^J-aF]AqRjGh0(m0d;>
@X[H%5`l5>'_%SV<8CJ*tKX/8E=I)2ZEG`l2"oAkG^Ib@X7FgIf"Wd<*a]Acdl(cfTD5WF6k]A
q^,*2$LDpYD/CJ=9q.j\?EVF7<U9Y+fD1uQVW"bpsQmh,]ATj@ZLVIIb<EB5L\1PpHXlHs&Kn
GX,kEmQ-7XaO?dLc15W"l[Sc-n!(g'B[?THY]A6o\F$lYt^%osjBe[$n0Y#&,9B)=4S0qlUcW
/2;drI!DQ42j6.NM`t[=W0:;70<mrJqjp13$XtDK=6>7,_ke.d&Q<B$04K@'C,HqZY$)@mKJ
>34R4tILWbB3TEAYR*V)rfLL<s-AK9\qEbauD&s!-e=D)h'1+F]A!\'#[a"f>SlhI1H4ef/l5
O[HNG!L@Zh%Sa["D!OFB4\[<bUgHD<aR_['!7^q':j&nrZ!t,+crf?2Z/V&,om:fg`Y_3Of!
8C&%V7WD5kqH[tru/o8s1YG]A[n=/k)>O>Hk*'cCFuX9(&m0'i/,2HtWnXYt!'.R/SJIZC4V.
X;/:-f>F[UTJED%/'8DkR*+`@"Feg]A@]AH2?58glObS43l^@Q,:=k(KuMZ6&aH\]AMlF`3r!W/
6s2&7*fL8VfDu8;JBloG0T9o[,#u5nZTS/V<2W?nd7i/iple]Aees;cA_Mr^@-9kFe(YpjYAX
<hKuAOh=oW=Xnr6_4[M?6C\Z'q>kP/e!J\XJVgbL_&")a*,+NGt>Wq<k8;%ngD]AUnQ8VV]AA-
'j!]A>hjSJI!WDJ9_.n"@nY>;hbEk($IiDe7,3HR2B'5j_P_!gYA`4%!lK9OI(PK^kCKshJ.;
,;aJ-L<N^h@.CdQ\/VDWSP!?]AKIC*&@Y.p]AHZkh^rqDf[ouPg9I^k4^+Ia'L&+ZBU1&TP,q)
7HEk#b0:_Q\C+Sb4S6LrC-f^ebEedMI$gldj)t5)/n4^d"eEtcKr6$mO5o/u'0,`^'c$7R#5
a#6<XI^n?Z'V>Oeg">]AK/k7fpdEAXN'sH0Y$`u<7h"!/NLM;e`INY#7iG!3TM1B3@fT@!=/_
r+,0FYMn;s(:%c<p&bVY!0Nc#d9X&%qqn%cof55OC1-m+;&D!oAN7BA/4)^6LmnUi^?@N%+A
e(iJjMo5KSqidT\D,p@(B")7)_[h+E?2a#amPfg%6trVE?%+&WJRVBZ(iu>_bM3EUjPdJnmO
KDBr>N^7RpcJ>+fe3=<EMAIL>%<V7c0eBR2_Q`Hq7kuSNFE7H`_m]AVM"m!k7Xnu^9h3$d
SSLiLXCd.'_0RG81Zb!%LPF+`/]A)s#r)D6m;M:;?gA\M5_$R;DptI,&%p_rLf)/dq,^#Q%)q
g"C!*gAQZ7CQ1@%6<+<r%.p`67cMpC7FmSqUMdHj<_[W<Qh?Ce/DN;u@#BM-QY_Va5QIE.7Y
$.]AF7h4<P0B\H?Y,;shk(rbV'TI(&+tI7AqZmj[d"l`^k_5&NO$<7A6%+GLk&omRY^1tH88O
AZR1FSW0oI^tQkE%$:ZS]As7A.\V0XRaD`b*mm&Ik@Th:r5UdChC.OG6XRER?(e!)@d<i9,7q
Aupt_aRs6+<aa2ar"\OMeR99]A)F1>Yq&[LV2M9"W]Ad%%is_iJq<<#3G@pGG3Itc7:Gr[$.N]A
\%a1>Kgf!,RlYh-LL5<8`0?ImT[Q(L(RZQFOC(4#=tt[eT-*^Jp!6Q['7-dp"V%N7D,\,*g,
u6$ffu)R+`OCmIf6FUqEMGZC/e&!YLoV6bd4]Ad\1UpCjX!Mabd<D2[i!e;*h/=0^sZ)<Gi*0
;gk"37NkZjr.Jk4=H59:;ZPJeoT%^C^qgA-R"bU[f6L/!.Nqtd=*nZPm[.\qd>['_;56Wg)7
aG2ihl-5u.=C$!m,QE0QM\_!#k(2[KN8Xqdb0]AWD"8*npu!9NYt@s5o#Hd?_SZDQUMmb<QSZ
3jqr@id7_-9Rf:9<eb(78B</crJOQ^']AJ"!MsC$i7#V9>";5O7RD#o>3U("KiDT$'2]AH,js$
amW#i$`l`'[RaR5*hj9T&bdpT5d=HrE_3Kpp4&B7lmLVkZ>.otAZE_PW$uLI#.60EDq,K:2)
F#$()Gb"1X,7&29G]AVT]A6Ig=IF+MX=W*o;LOiiQ=/(F=7InMA1>&(#HJFf=^Xa;&^aqG2dip
8YU3es'tCp=YN8nGh=bg6k9a%"aOXShI>3d]AF?=6j..6$Z8%1pn4nHe,=P<;[3\EqK?mA)Dp
:C^0\,D40%ufZh;79%tMiPA#6[4\$QC^r$)I>6grbM9oA2k.Xnu:SWcU:*-!hn&5P9S3-M/(
U]AZ.r:U9(8_!kUk!3IKmfTYr"lEHnrkUp!!mA&sFL[/1ph1*1-&T=/Xbe+s2Q=O#D<^LM=TU
qUW43cc=An@8Wh@e`F^$Yg%f9go>>oO<?WK5HX/8::;:@S:rF3Hq50I69;i2H9he`M@BphFi
R,!)a%"XQ1X\c<a=E_)BLO'Dp-Qd[*F)EV<"H2H1U]A^95XXfO5JQX!L9<;+1LYkdCd_,o*>j
:YZkPGZpWbEFL=W`9Z,Ii:U.NuQ&WBX_]A+@E*u&^'GPS%`3,e;%Dm>Mc",CXkMo_7&-h8]Ac[
6CoN"WlsEq-,8;M-+!tW>oIF.mUZ&T%rSIeKk1X9"RFka/.dYApB_9;^*5X8K6>Tf\RgkLN&
bSh=L3djT0OGFK)k]AEaUF1Z,[\An+1RN5cpm39YKW?^t(ZQ9!9OB'JX7^%Hm]A!fX4R=\Y6WZ
lO(h=TkT<[S<sX1B9>^3/^1/66<u;:[epXT)k4*I]Aku'P=?*G"KD8bc&9`i[EoeoEa6Y=DFr
-uj"rsM.l&?5aFI]AFc)oV(VNNq9kNNO(GI6cV#*ctq"=[)1?RE5")d<mlg_.Ls<^T!\[,1rn
i#34J2E?uhR=SFkO,.,650N-7N#W_,/o@D5:C%Yq7,]A`NkSBEZIht>+8;c$/%c-EnV%m8.[<
+dY@f56T@ZDXDuM$TJ#EZPVnh_O\\]A4S:=EF`7oZr^51fs!K%jINR2_Xrie%EA6+X'1$@GFa
k#J(L@UAT%(`Y03U_=mn:W#5`<Jbt9JO0?R=*pIR)nSDF:[O+)Y;%u,&a\0P]A)j"0H.CSQ:D
NT(\>_?7sp6ZtYHch_Dlk6C01"4%iWa\p)O0FJ=M:\8Kr5(Dt$jVrP7d+-KMccNZ:,fAD["@
t^f$JE\XG^TaQZmbJS2MWs"d"LTBG@Xm]Ar5oc>F[,o+)P7IrGr/c/;q!ZT#0iXpX</(!c,A?
)"*gZ,Zq(LA7.T@(3j!;PMcab`l(<[E-b&dg42-pi=)=(k4.@<RCVQ'MQnj':-iFl?8]A_=Ki
-G=q/jdk8gaQq_$Mi\'$Gg_fqR",aj2a=3%@<_dSVZshE`[m'B`%cQlm)7ImN8XkTfO]AX1A]A
*P>nYdV7?^WJ(U9L_BOLIPCm*dt%dh0Q)?OjUDVaua6*91r;XK,Z]A9je0d8FGj+a^A]AF0DYi
dt$Rl@V:1-\^-n=Wr6+nbL^#<U0@RrQ(MjN8oX#TiY)baCYNU!:!C7h(BMRYs',\;olu>_-1
?A%8&uc2]A#jo[VF:qT`eNf\6'Cq+DKNr*&uj.W+P,,p>O)/c;b$7FDd)k:1dM[.=#G[Lp#hk
c1p7!XC[)p^2cZIHpn:$_[B%s?\ur+_EeqJ(>$C"`,*ss2:m)p!YLcsdnZp1Yn?hH<H@*XAR
imY0_/48fB%ljX[#UNNScq_:oo?Z/acIDf<mZ-0C'MXs[7D7R4dR.Q:@io[QJiTddmM&I]AT\
ZmA9.'5Gk+E$1,?t5;Wi>k.I#.GSJGOme0ZhdU6E0:m@7QWVg/-[pRtXFIe1-g/DM7>B3%sX
@TNo,Ui9W9,BIYqQ"kmbojT`2H]A.#Cn%EdLq0E#9?GmVkf\J4r]A4!J+bm:NL7$a;E#%E7DI@
SL.9BY!V!p@NkB1^o>H`%gm2"82gVT-THc=Uu8P7drMN6/q20TV^f@q`p+XPmMk=!c)9q:\r
u@IgJ3fC\"pcTF(UlZpFkIWf"anU?3j`DR<mT]A-7dSEl"EBBh,.cSSAMIUrt[a1oZ^#;YE::
W1(Qm5=\%43Xc9%f-/6?`DF=[Q0bi1XsblC1CjVcEKdY^efnG@N-\mXmE3^SfY>+R?eVp>E<
jB_E)hH['Z9FMFJV`IdT$[^rVHVo,2XlFQYiWPLZs!)r)hLB4k?q4#%$i>nA]A0-8r2t9^d4.
\3)1:_7;-;\)TL"0Q_dN,&=FaI)\tJaII8VoN_OEN:2e_-0:-Bo@-P"&I?djX5r$:J,]A:bMm
de%,7q+rP'8k@f-7r0f`)ldik"r[[7=*YLj1XPT2*$;lWp.c@.[l4iFG3k%F[;Z9%#q&7G#o
6Jd]AsX_tOkJks<8:LtlQ(W:sj!!Fj>h?03NkMsk,=apE50hu/)79"nL?lrnTElLFZ-g72@ij
`Wgn@K\$`.`nAi_@YkuHYh=&$Jq7D%\.oMb+^)2G;WuamNGi9LHt.Tg=W4IA>_W[ie*%A.db
gQ5DTm]A8R:&1c#KY%F!S<7lJaHmj,*e'$#qiHKTOY"+.GX"$q7$b9`WDQOCDOH,YK;j=jYdF
od%&aP1H>6*EP>"mjB&LX.m>p+'<E3pJ&=$KLD<:rl1t/r';<\);s8!3TJlcW__4CPU<Mk[<
r(OZM<2*"0pjQ9&/plXd_2>51[!dn"Yd\/8(TI4Bp="?W4gXM3f`dM3p_e!)e!ga&^m_f$SD
fq[4ORN7=RTiW]A.k2+h57=.Z*s<cMld+^1'I#ihfS#B#XTe3/bSmub7aqkqL'o?L[Xg2NkTj
_0'kSQ-?6G&?@G,*hGO^DWaUk,?#U\bPPMLcGUj?Lhe.]At\f+n7O$I=7Wgm#nIsE_P`*MfMa
bu0p^r:"KQ+15D0\o/F#=9*VW]AW$-PS<>pa@eNtS,GL&9HEAmI\R#o"?']A^Qraq]A"r_iYlq=
]AJ`C(Q,,>MO<:0Wc\NDaY]A3M&*]Atjr]A<ZfoletlLa9Uu3*=L6SBZKHr`KN_XLM;',iKp7Eio
KS*7'S"(h4EF3q8'>Vg=O[bE">//W0"dHX@4rbb@q8W$s<\").moF+F^c6:Fl6Am-_sg@fso
"JPIR@29<1INK:^[Q(^A<jEFN?VrGhkfO$<2IHt%)abiP`B-!k5_of+(&h1!#i(38;]AFG-'L
PrNpf+e6]AhW^E=os[21(^.20!g$P)s(t3CNNj&#Ydl5i*=!d6"61l@-QR$'bK?Ced,N-?3F@
k@SAET<iLtmV6cg'Tf#iQl2-^[UTasU-:CRrKbYgF)min=7]A8Y[A"#@^_ao,PDGIZDaVOFI'
O+d[u_-\l?-Tt^Y=(Pnk><?V(>"*;f0WOLs#,4i7jQN&/$kmp^WoSrMqtCRlO3Q1`!fNCW8)
:g,KV4mFrpheajg;c`mYQ]ArXY668LlDje$GVU2d6[hgY?L",$R*-BRN5\2*ViLVgD:/.Qm"4
!`-Ouq]AjmB#(W/0:[noaJs)%8>L;n7t;pRg9?bpAq>VhXdFgrV2JXEcsJB7T72sAtQMF!%-A
K+sWeREGe'Rhao*/k-jmTCp=<8p\k:$a?ioUc."J$5#BW%@Z.=tEFFkRbEl3ut-70&+^9F):
Fc*e_jgNmgt]Af$!LIolOa:PFn(2IZ%Y/NjtVigf>AKJc8-kGCYr".f#Rp")PfhB,1ht.oJ1s
(?9q)q^o5/PnrjP.eE0%2(PAX.Fe%;a%p395GMFt;J5U+niV%WO8eRCGZBEUCQQPj]AX]A,o]AJ
Nc,0+OM/VNM6/.<HS"hR>Z^lR#'dopj*:`jF"HjXNcGVCoM9WDNV-hTaT*O:jm')BCH"mHp,
=1f@:Po\qYC;;lbs1fb6"2$(3N)DI`ZD0li*a)YSb?3K:!X/sOu`Xh#\r_,fEm!<hORompMM
MgiDFF$Dd#1J"#hsX[*qc#"F<";>2l#VfD7sHmPmEmWp3qc>*7jjh`.Q-(5=q.mj5cLTW$<%
-P7/e*.n(Lgh6\q;RpY&Cbhsr<&=uB`e"1)L]A(fuV3#/,7HaS7QnF]AS=4bNe0'WquRt)CJ7!
TdDiiquUm/i&\`T@F`.-Y;lff!!I"cI4$Gfbp?a@5))/'^&JUr4MXN4_1#&7`)R+8JQq[O#s
)?Tk,kTT)b`47gt0r;U$+*!k\]A^3*_BFRNZlt^:+Qf,L_auEk.'FWV?jVZMO^81NA^7Y)(BG
rIPGiD#eMubJ`=<kqJZ>G!5SMJ4>3f2*jetL$.EUoG$.PuBQr8PA]Aq,]A5mflc:@QULS"%Y/<
o_`J_]A<T2Q;U5!"pVWE=&.9Q0Xm<Hp,5La"XR5E$G></[2Y**<L'2Zh_r$KrrnoC:t6(C$4o
Jf#<jaSReE"oB^kb=9%$d+Ce_jeW':%Jg=+a7>+2^@[KgY9P;4;Mocc7iX7mN/jVTQ=6FIu+
^Mh[&8J?%W2C*FIPt_-t,*if-k0asH?+]Am_kqlKZe91?==+=kW%^dkh`4#^A,uF7F8>HrKbV
j8apaU$oQ$C8T[m*pc1"uPSn6c1+Xgo^r:N#kP:=L;#.8&]A$oA#6'&HP9;+2)4@3&XH2r6Jc
Jqp=%P]Abp)ce7HlU]A(&Yb#;Qr$MXIq/FL^><_LZf6Y;=f$io%=DBiNU>k0C;l@cN3\]A]A9`c;
PE8.`N?Y7]A/2^ICpJ!DkV4Bo)+Bfb^'Es*-Z[DNCYckB(Kb2?#!$4M+D*`CbJ+mV[>rYh2pE
I7>8k5lGFHG/9a+Ra2%%X3F`_a44=je;F_]As"l%YF_m1B588qom5mRB2YT8UB23;\L@8gd6V
A6;=KUk\i]A]AV]AVcW20)T;I8%*KM^r"cFPb!e7n50n'/k0F2''*a3*T3P+Bpj#b(%JNdJq@:H
'iX#t$#2fUl)*rrPNsetu%Z;bkh9XlHk>Q42$N_2Y4h]A7[<)S38bf'Q`D"!i*D=TN+^(b^7*
Sr4d:*j^]A/LkLG\Sg",9*&k`e,'GMksp1p4ERjoi0)"t]AY)]A:MJ1MkOTJieEm0O=fe(]AsiOq
-W[Lfa>#7"4#eh<E1MG`?P2os$8IAL1?qG5#>=(;O3]AIP.moiG1Ne05-;/5nD65TLHduU).O
;W`4t9nHNWs!NMl_m@u'_+69$'.]ARDR3Cg2q>+FCV+jR'B5PnKK74_K.Mf)fO34'T5Hd>>d`
,>og/8>iV,aEQ?qSsIX(^;_*Jm0&Ng60[dWXX&E+o4!:o?Ds1`9LB8JHn?HeiLEY9U'R_57j
&no=t+D9a!gOtDQ%qTdUEBQA>QV+DU^p?9uJq2X;BfU@Ximu2\eZGo!Z$tLY]A0eGip?eipRn
[UP_=j["Z'b(u.2X(SLeGLjOo*Op4Y6*/ft/YE^bJ<@kU]AeV'o&1Oe![Mng0S8;<0jeGj>00
7XK+=8\O64'cL>p$&s^roC6PP*EuSCT_9$&A5<[g+glMd5\beqef@/qt"#('&qnXH6k1YU/-
GOD9j5Kk8Tn,LN;kKO%uNG*Z=2-VgX!f64-@&YZ=Q6Go-4GGVH,E0pN6Bm^5*g#K&AG-90O+
RmN5(@\U$nc2S'UUn[>o24F:K+/WejE"hp&]AJ@V:d'<;g$i(gGaVQ"F>cN2?18mN]Aj!At=0e
UG@OEnT)j2ddFo?b*-WF(nsZRPFh;`UTUK]AInG9TfkW<:90PLFVHZEG+EYP,ue*f)$IIV7DJ
^e@2dIY&6)8.G,e28:SY(X%ki`/!$knY!(q1:!:nR(o;T@s!H4T,0%J)HNVegJ*T6bktTJ2R
/Y_SY`[Une:%;4nd/BrUK2]A@%sUp2htaa')K8a4G!D\qVEDd'a#/R?gY&^B[^,K!eA&)#O%<
&>1QE`]A3fbLb58?B!,8bn%k'KR;DbmD[*6eSPGP#sp;@dFi<Y/O8,MJFa8f_PmQ;.6iDAKpQ
GYE:"'BK31#<[&_p"ah\4X*pTrb@!()nHdrA&thEB>FW\j;+%#18/[(XVW^O_CJ$[p+^QKP]A
tTQ^DA<8)DrjkdY*[iW39*H36BQl[u[qVYH#''fJ9+9DrKb=?aBsYD9KtOR6mPEI'_ZkFKK3
%hk")ErdH$3%2,GC^E')>!;&pU_Ac/i=fI:o$L9QN',,Ed/k_iG2@5D^(XgX(?!?-g%<jpfM
WOOi^0cB!5^1,-1>6-t+R!17<2EMU]AX(b9eL+I2:,+]A(MNqC..IT2gEV]AQ7Nhu_GL%tI+Z$p
%q=ajO0\Ve]AV\qKXl1YdZrP%P,9gDiXH]A;_OJq;`T3;a7s+llp(hCQq0`l>"0K-[^@)fmh;a
EdMtf$.j$X/-r$V#=&)`>jTO\E;J[4*%QoI+s&a*3sR5Je]A8/YNaX4iX"p*$dF_H#'8D*j6>
I^Q]A)(=c9kK)&&T9HF#:!uG+QR';7>?B!Zfck4ETDT8\TBtqS?VfM8Z)PQKkItSis3cuiJkO
JP`.;b>C,QT=]A<j+U`cH`*XO(Ma>+(%G>f)Pko$n7CG+lBF>1b[Ep[^p_E%FD[R\V5jH@i?3
O;bWQH#+j^LnFYr_Lt?>)S<!WfXNCY#WtNT_)JS-jWmEp4N(J;?X?&5M<n9&el^r<-,PWJ4+
$YjZn4*D^*HQ<VJV+2L/*c8jbt@e2>i<0em[@\_&nk\q*3(^Pad*EHCbZgtDQ;*mQSPJGJsI
`>fj<9/W#t\HU2G23kSWc%MQ)Z6<^HqKqHaA+d?g.WfW:(.AR8A0QV]A@`]AX/)pG&10n*38!L
G!q,Vh2@[KGBsPL[Tr&^C:pFR(j2GNA_=QG.XUqLL!LOZ6<(T\;]AS\m_nshl-==*7")-P04n
kS-s`!I7V)Sq7/`$_dHhf@.\8KFLS1gQu;2K^Z`VdF5:CUG3c7,%]Aqb5HSn[RCJu)u..,ROZ
b!E;V'puJN4Y-5/:n`gfOQrW0n5S*$9d&jidX!Rrf9YsJ0_>.(oV8s=2"F,SFRl,/03Wp^gJ
[d_`u+,fMA@^<"l_KJ^%0HqOX<!Cj.$2X6nngqHM+@[;4?`rGX?Wf(eHUc&oo_N8I9OAi;S+
YhI,E*;cq^5AbU?r?;mHEUH'E(2_(V\m/5>;/W+l]A7%e,/K]A4)pa^]AW)kMTi%L^bRZf=gLN@
U),p7P80+aPf6f?ZKX0U;Ki+(0p8;Q3:AH[0N&%%PrTEf6aneN&`;q@G!>pa%;p'/s5`96NC
X3$ufsaoI=0-h71qq$W;+N#0O&%prsV>]A`B/<+k7\]An]A1gPdWJ6h,VLl]A)TGan,af?dm3qlF
<j><,<;pT'ugb&,JB[G),JC`a]AW`nr[)Q%Dm)B+08,G`DfTi;+fl"uM5kd*qp*(rGr<X:s1D
7*K%ApNG^M)ThK\/I%jiV5dI9'47c9UC-JU<($9`lKEP'F6W8<bcGrJ-4qgn_maJG4b=g_:,
p=K"89/Wh5D/Y.Ec:jVhg3/G=7P6B[jhn7!U%2"CG1NP]ArdIBNL5(pCMr6r>gD:8j!6V>%%3
G6B:P6e+WfPfUf(tj$:==QOF'<qV-8+o!c?JjT/d4&3"6e!l:MM48Bm5'3*k-Xi^7U.@k0i>
KD*ah+DL)iAQO#ro/%9b)Pn>SuQ#$Hk3RLi2,SiJuVCl\4DWgf(LCUdnI*`#m&Y(3T&>51a;
F2_Na'U,>-i5l_Qd+NjT"h]AeW35hb:J0%g.kfeBA7s3u$mI2Pe1()7eG68pdC>!W;-['G6K*
[fW"HiEOED8%A/5EcCk(@ZT#F_pCZJCC=)%/>T.d5dD/D-HdQUWEg8-K;/m//9kF+VYRt>SY
LYDu0$)nD3l_2Fh%njb9n9_G/nA.j92u,UN%/b)l1o(9qUJ`+.P_=4)[e%Td<Z&MC@!klKZb
]A]Ag'F_VH+ueUbk>RFgFBoSE'UXq7$1\BG%OP@>[6snrF-qu&\!/lk8nk$HgBQC#V68=1bD1$
;;7Id7eo&'^:Ni_&NZ*uFBj[;.<[7??A&Q-]ACu%q<c?<C/Y-W)npl!6SoeDek:+<C"DLj%eJ
04([N2F(W?=/n5b3h&l%IP2k;T!H`5t^Mr.03t\obt`\O5I/NenuQE>id'1_(GFm1'4lV@Gk
caG9HMf/cM=$j$sLd$n2_Q=@N@H-EHM9$etp1S1biW>M<WqcFoP%NOj=Z#VA^0\/3YG&&2V`
<J0$CkE?oa#SV]AhH,ru^XSE"5cR'h:V3_0/C/S[D]Ae%5T,T`t<''kLWNeTZ4c"K[[V(W/T*k
XgiRf7=PQ!bpVT8-o`+\r,-!CAsQ5n'Xc6nuAZqZB'.d9N<):Hf'daSIaLVD@C+7pkk!MDS$
`hC_j;?SGrUH%SB"JskT4@+.XLiJG/2p='5\HKE^92Ig"j;dG^)^hX.gQqQe?B"h(+3LcG%G
9`4NO'A.=,Du.EQP9i(S?W./nG;/ULU_?/ISR(`D7q$DDUmO#c]A8PH=,jfapc+M)n,C4[-e&
`PIOl,+M(glkU`#=;ri;FW$N3NCY9kh!muT2D`Gh^6_f0I,Wgl]A4$C]AkD5?3[,HT^d("E>EI
2D`7n6elOBe<%F1(rNJYMe`tNA"6$Oit/Ujpu22P7r6>&/X[)'H@/>P@C=^Nrg'sK&7/ftQb
^$RHAK&7iD,%LbBcP#&Khcgp)jY,Xm\"]ADl)ZX>q'Yn*!lM,Nobd4`Mp@c7mHdB=F^sc25"2
d(#L`qSqg<3&lsd!?0$(eq5tGi21k)fXgpkh71j^bp[N%A>"/+sise#6`04OYD_X-tjR8qbJ
`+,#r1ees4>X;.2,SNad3lubr8-gpigTnmC9f;c2b\\5L6g#%/mkEp?-6Jh.6Uj#;P-<@!Kk
k1Ps&o*ql6h\:VT`.9cn2^J'46E2`AMXB7KAj?E)%,+(H4p1JPNspU"$\f#D(`7K*n94<T5&
q5S6P[cfrNq9S8_aR1(.ISb@br5'OVA,<FUVr^YP269D<%RJY,jV#kiek61ZnS+.<3ehr'mT
OW8d5*QL*A^<A]A5YY8JD0M>bPtD()^k_rW7RmAIIcL%mA0g2c,["oh`]A\0GQl&lGjA&[n2eS
`#tD)o7b//;E_d."/.56<W%@rAAWY6.im6b_qd3h$0)V&8W*C++:@!B6aC13i1_\,kh39`-3
2/`o*Bqf.jlWq"h`@_AER<Y+Vj;=Kib"(rZZt3.^48$hXd"XShEE#'?q)[<nZgJ2DQr0U9)X
=NEmUe?E>\SuFoHgXE(`qePZJ^6c_S("7&EMkdk1J1:ce#J"cZ'#FZ"@s'ku\)]Ab^):>g'E>
]AkSe8*l*Bqh>T>Paj8N1n_oKYYHPh<S[$6(UnAa.JVH7N\Vu2"@uu=W6E&KfT@8d$nqdRt]AH
5kTc3MDKS=thKkQJp'iS$<bj9tmd)G$rF)_#lhfWXML/"R[W,_DQ*e.TZiNK#t6YMR4Qf0%M
=qm\6Uc@6tNQ2k\Mag;fWrSIQ1\sA-T4bu1&/F>&aqkm%TR1*AnNVgKE.a$OWGf*:-qSRF3g
q/d6,hCi_\LQ"=&0]Ahb<XldPVnO$E^HO`F%2-Y)IHr>JMW=3GZdL2oMJC@<IdXA_FFDu->:S
>*:X[6990&`;LjSu,+[H'3:q@BBL/'^oW1'ghl'n_*/uP\__H?JpGKfG:;\#Di8)C;t>jYF-
(m:d`&M"/8G0U#>)?2@rM4;#o8ocdh6*6QuK[bhG"/$tK6E5:d`o18%*E53n7dIj+Tqu:/JN
>4?II);]AeR=$Hn/OjKon!TE1g=i5s+1;2HY.JoP_,n4.?]A2]AdX;4_5D^rV0()g!d9b^LgQnt
+IO&5Qg9e_VI7n&t=Vl(]AFGGVIN1\,4>UF1:gF=:6rHtuoiX*6'Ee5rWiWDM!:AL%UJCF7pr
dR!oOf;,lF=k=bIl]Ae59*F1#G.4HZ9@BiCcA)us-7;8m<!F6<"Y+q,*)Cg-giV;:I`FZ,Wou
R8r*?C:SbpmXp4otXdB/RG]A;T4Lk0(3R)9.*ggII)"Dh1F6G4$5>6*j.S_q0dX'`rY8hj9a5
[G/r3CEJ;2]AHW@seWq0(]AZUnmf(O2.j4eF8[^?-P`7+C$oRbT2S)"G4+/@Y\T>-]A6D*e,L<f
4>j>KSh2Yr`3gHegdVVW(+gOQ`"0`t*T_SoaE*>8H^_IYk\ng%bR:6[+PYQ0h+2>Vb!nC4n(
*ii76"R2er;7J3:;)?JoP%/mbVj1OX]AI4G%><R`MCc?m>0f>j(T>lJhXI&`p2@-:^0=G*<q+
,o_2ImpW1kW8r0%LWS*n1T0FrHu(J3A.4a?W/g2OF.21mKi8>UPk8]A<YiX(&O*!ZJ]Ac=LT,G
!O8XCdmN*(?9*oO)N9R<bsc6;Bq!TuLkE5WA:Q2u@Sl/-_W);^o1(8K42']A9Vuj21s%)cT;=
WDYt*g%3<<SZ;^uY52^ch>::VO1MLt!W?EK4,)9'Z/7+t2po:PHagn>9_`1A@@u/gAL3'=Y/
+O&H)Yfu>(4bK0J!*%R==XFN1uR60XUbEIE8tnf[B"tq7'!-kcYu2S/goO)((k_lVsa@-d4n
^`$kJ[d+oX]A`2j*h^cRPq[6JL.ni=eQ5u#%KFR^7k?UI2sWAe&X%<lY>D-]AqQWs`7m)0qh4<
,#Nd')0%Or?De356r3P!7Z\)7?>).K&aaY5j7!kL*pe*nB)k,5(hPQ7d\b)Z`2[cXFI1I^NV
NCY=]Aak'H:*_pptI+U2)%6XHK8P?&Nlp@t0/ciQ?^YQ.QT2i.d::T/+-3G%>N[b;O&oLB@uY
B1-Ks(=.K:Q<aFFJ*,b8*_9;qHmE;uA]A;&;Xo(hJhkqK7b,CQqReU<Ref4(dYLF<,WL)bG0V
1>%;b]A0B`CSm>cmFJU6aa/I#V_0noJ1)a(tBB0<WeHNagNBdXY:pV0`[e)pn!qHQplZ\b1<^
1o;@YtYPs)drAK:W[3J3\H-,#(19o<l2<AcT\HgnF;%B02oJ(N2_L1%MQEpTZghfFcQj=.%H
o"ir3g?@lAGkqqp?mi8STIMi\Mp4FD5TI_aOa^^\T&3.1)\jEnb:UqmfC-J[VInD<HWc"Uj3
u5*EWqR#LCh,iJLRR1U:i)FpMdF>\__GNFdI3LUkA4bD='tgO[$2Go!d2(l#>G#[ASe^9s1]A
E./A)Wdi*'f9r_;<bX?H9hr*rUp47hIG.spkbpa<JNL)_+s:E"*/?NS#/KqqG:PeZ"3a$A"*
Db*aUR5E3'uO:i;(La`^8>$8@gUpQ[?&OS`C@g#Q'4iMPm%anEL=j,7R;4J+$,4OLl3?ahI"
<-47Fh-]AYD#1Ai#]A*.g(c;MQ.QN_6XB'#cW(PlAF93'L\#1@Quf)Dr4M"Lb5#jQ.^.+<PEeD
FA4El*WP&GtXWdrL><i%PY^<>[3)*L3or^)SUU)7H23+]Aa3#;!tZ"UAE*4P[L5A?BsD*4fp+
`'"@(d:b3Fe$VaiqiUPF]A+jAphdOF>U-S7tK[M/DY#.i[M$A$LJoH_#5]AP4`S)IXG-p);`mt
;<)oXfr5Xbj2In/k:91'7?C;pVIT9AFW-4UpjjZFh53::SOoV=duOgSD72OmEt/"nfae'>)$
-19,SGX_+F(al#cU'A!%;b0)K>JLeMk!t"Mj39p-cXCrp+&(`#gEu#WE(DWCLNo@s(b1Y$oJ
lnCcOIB)UHC`&rnGRb..Sl]A7tHWk,'Xm/X;nEAX?.Xp-Mn!CKOHih^f>0W[_2r*aL8DRJ$<K
(l,6dsVW(H,%l!_jd7T+#WlPL'D;/`>65JduUdF$q*Nod;PIqTOME>O$A\f=0Bj2,`.c?`D_
ilk,1BT32i&bL3.\Ji1iQ9@tn)7_RFk((QHr6c(C=l$P52.AoTs&L]Ar2h!^IOQoQ,ut<QS7?
FUrq7Z<<\kFOn'>Usp-#\G:o!Th@Y#cN+S$#DDMqOILqDjpS6n:B8I#:Z(#@@PfC&'0[[fF9
)nI8ccoD3F8Y0[*=ui>E9;OUfFDG&@6CRYZ_to)r/9E%*Rsns2)-m/tDr7&t,9N1L>Pch9$D
q/H@DphsO-9mlZ"uC@[mt4iI&J,B[q0pleW2)m.9=@r>isK1efbD)+r!4rcDc=e/p&8SFY1^
DppgOR@#QG?13E"DqFB@"A*&GP^)nT\k?7e^_dLAR]A=6l$9imJh1*3@RRnjA:^-:db$,*Q)$
MUk-ZotRuVV)?B+1b/r')q6M$^m\U\cJ:jU.A3kh.L?p6!UF#iO3%cd:ad9t;H[=C&\VcX1m
:f[C&;j]ASe`jhd37!lo)'hTrs1"eC!#U>"1AF,7R[[dPB#O"`VSbXfZ2!_RD!#kV,!GI[qcl
@FP%Qj-L-_R=sOGG*8\4YmQ5XRfYH(HMAdh`mRWOj[uj%l&jr:=AK4uR`?:H3/o,kPVba>^V
UW?47Q*Hiq_Gb@-uOb)EhK"L;$]AlDQ`mcC]AX.:@18qc:6kaCP,"1YJ$Q\'=XgP8h84fU1FH$
i-E[Q6J@j,FT/A+s5Wcf)CBT_f.`LiaqWkU%>M<bE9b:9(9rjS_So%]AiXmmm824o6_^qu%Au
t4?)HVp[r9"agZ08:1%lK)W7R<7,Td#[k[<Y[_h-`bNF/;=E'X$SYG_EW;Jef]ArXG?BAj'2e
A]ACR2ds=4'Q!&8.3h4%(S!.*g1-QVo@Mf"f`De:59rH.]AeC?"7qO=bbX9;e.mdBU'Y:/Pgpc
L]At0-`*Y7YLUGYE2<qR`Priei730Cj_I6_pk"hQ5?N3GJNf:b"uu:">e\nlJOJA$"qWSg_`4
I-Af[>9r7<$B6B=(G,,I%Mj9gBGHZCOAdBU"fPioHT2=MUp;T>*b=)ggR7WYX##)`4Y_Fm/2
?5K;Pk$k%g>Zr37Aj%ZZB>qM*snbcY-!/ZmGI2)d5)KCl5?+5O[j_*)Qcq21Z_OVXtE^Jm)S
8U=SI38oB<W"ae.1Lr)*KRDbW?C`2]AZhFE^W*Z=E$^A-!&D-)%RUT0ht&))&A!F>7m/%fK>Q
GHLc!p%>=,k_p_l`VZ.p)Nt]A5jepAShgF0XN=7Q?,PX<A[$K6lQaSQlXZ.@8G-S?2/0Uj+mK
X'f.n<i>aW.^"IS(OC,hA-X>0q%gfT7@N9rs5h0EhY7)]A'm8/7l&g5'DR9.1bB<X8miCO-q2
IkhEh^<cFZT`nVW20GOLYaLE'@mlZWup8`"l345.2Z[W'1h\=aIUK(Pm'E:`*h#)4%/'XrJa
MbEgNJ?H'ofaD7/5+tb[1SnCWn_XKi4V_!'O0I]Ap%Qm.69)NBmJYjGOdcetMB,jG6JP&m_0&
ru%:8r(\fN5AUY^kG4:N_np>q\b,#ha7$C3%BZ!P>bL&a'V%I8AnBZ(2<k'fZ%Uu-`%V1%?#
DVE6<$ST'R;aaBB[d5)qM&lGf]AZsI#1NXgQ]A?Oh]Ar5_<n4D2[@GZ>c2T^_mG7d9\1]AFq[6q\
>\Sla+dXdLfFP=&)J@;UWmB^$?^PbVk]A!aSn#9NY\L?3T;T(oDg+tH9VSNi,?W*-7)^:PY":
LM&bR%ni%Qc)uZhRFthhO7Btp[e[QT]Ak-lm8-P5jgrEC[g.CL<S8D&u+cJW(<^2B6m6A]Ad8N
OmpUp9'3rMX^gC(,rk&^PSCid\kVZ!&@,TBJB8KGdH'AeU(d5;:1rJ%;E.U5(4ij7t]AMuBkT
<2&qP%&$=q*@oOV2rW#KnC&Z"qDa1Z1H55SbnkrM:9%:Dp:hfn+^Wk4U,pF1;74O)I5C(i__
BI/b(-;`q`;s2dm[/QBT@`hgJ=.RN*??2RN.A^R/XDe.`(DGikg,.g%4=WuI,[o&7qaJs1%*
$ujkRJc%Zs=]Ag*YE.?-VYGB6A\mb-NtV/fMM=e]AF,6JdD^VtQhYtKp,J#W75sO(AK=jmHA_*
P^(i3]Ad5jIlW/d!n=]A5@cQI%ANCbV>omkK*u7/.$*o.AS]AAogq<2g(@)F_k`ACm!&04OKWni
np,sT&s\QgWrJ2IMQE_&ZT+u!faV:9aoR-F#o6>VJXTe#6E*&X)?oem@elRko:YdkG5!k0bh
/4'qfm/NrGAp$/E8r6B)^C#GIRV;YaA,Ed:?^rn`#h7e6(-e)]A6:e1]Anh)U;of9:s8j8jOS%
_??i9RfWR5S+^i>fGgJ*[Q<gb0O^,Ra#Gh5db!%(;%P[]Arq;`,[5Xj@Apt6uAXeZ)%:`3i0E
*f^PJ;QC`1)p*S>q<h@IV5!E&BlUGY.""iV\Y-n_!DX]AlDCSQquE8U.St$1J,AHE41G!mO=3
c=69!LhLtC@<eh1S>YXV_@%P()^q3R$p$D`pQZqjoZ%__L?J#[d'n<p.$iNSr]Ac,IH\]A#hRM
\]AB&6]AMG!(f(?eFH4Qa]ARY'81bfr0AWo<10#-oO``Ff>b1>JqiQMAnMr`#c2m)?faZ*^l+G2
9sm_!8TMQ:!W90\N'Ymj=/3k>2P@a8865G`hk@ddndWJSGBC,WN5FPS8Nb5ft#2Z&#HO/M8D
4T;=f@=k>Q$N77&keh_Pa[V?UAbPjUJI>ID]AHjtCR&?aT0FY[N$Sh++4`_)[G5nSiNH-X5HZ
'/22rl+r8H4DUqU4AuP#tOBDkp'_#O0F5HG,:SZ-54"UNgHqrQmLBq)i+XJ%_%dQS^qm]A>rV
pesq*DnPYb4!]Ab]A+lBJf'`Uj24kLF+ITV'j6LXH$l;HbSH4RKg^UqHuqf?mSP/06WkSfVuKn
$3cQLc9W_i.qq6WE:W>_"h$`*?<J?#4s"CI\hp0`!Ve/YLl\KW^f"ak%c@N]A<P`4hIB:5h2>
W;[5t%`&G^\gUJiZ_k`hskd%KXSYRM_7DRnO-^_ELp+MKq:rp$5I2i*Y%@SDaW6G875*;]AQY
>Fj^R"iXr+"=O9WfOnetVB:<PUs#i=&YZ9>JFibTZi[OT4+6Btn+'j"bRu@g>HoggUt:0bM,
R5IlL^ga<E3P?k/L/#BdoTJI"=gj<V4E,c^3?/2Yo$%qC]AG>c1Ts7)u^i4`fg=.[aI91h?sC
(qn?%M`X(uBXjneoEqU/<DsO#f_WRWB0[lVjlJr`<PJsB)0=?/QZ%OqBfH-m'otUf\K-48-h
U)&s.2e?<EMAIL>'WoBd*4]AFDuZ.]A96?-dDRIrYA`<-Nh!O5`5@d:$RUI=:
3jDj70^!/h$L^%,u;X!87R,W>=SFJ]AIpL)*17en9u%#3LT3b?^?B!k&Lr(KTcD8A*lGKD"AS
DkS*492Xm6.**&2[4]A(E6o(#+m.A`pb#.(TQK*9I4B>9RrE*@Rh)I2<!*='>qG9H)>:ru;7"
KeY%/9]A_A1snJ\3knK[d:%Hc:Lj:OYJO'OE14dnU_SZ*fRub,O;hI+.;!<1cu6!H#7T,'$be
*?m^-h<@Q5DQS;*/"s1+k$=I-C+25c*\$&lKq@0DYFP5(?R<(dkN!kB\=fjD1+1FWYW#$o0h
m$5Lf]A,DjOfiUAcKo?0UZ<=i+6tXAGPL9O3_s3pC;[:E0M40(eBle/kK^8$T+)MAZt$07\Oc
1;.CiNhA_/Go^=Ij3N+FIAi54:=Fia`MQ`m/i;,qC!3&r@PV^h:W:WG7t;WlHX/rg8u""$4p
C]AupSC@Nc`LmL!2i]A&k-Dq:b7;EYP,\!7Q$&*u3-@[%AEedq[8'45i[0'IMo7#i;sJrlZ@ha
ij`60D,dG&i)imgI\*m0s@%OS"Z+"&E#%:Mn5*d(_ji=;ScA^+AUAj]A<r.jOO=`)2(u'[Bg9
$577:%/5[c-FCc+D"AnMj`d^o<k2!J8(LGf7%,%UDm`;=Q1AaWM?'`Nba;1#EY`R_CnT!*g7
/pW=Z`E1R03*]Ai:J9#R^GL?\Vi'\DV;P.)[9Nf+?0;T1DEb$"+lW2*gkK<X.GYB.bJn%gM#1
e19!`,1VhG/lL@kO]AU/m[/3qV4;:K!#Cj@O'c4`/T.[Fcn09:M\[AGF*IqX;1ABo\<&J_jj]A
%#mo$6oH'Kn*@h#866&67NZK^T]AF/9AkK[kJK[T`Xd560R$?ekPQ$1WFt^?$E8QcBL@eDOXV
KSfragH+@\-n?rd?GWS6$T=f54iqR#SDR!m[_kA,02e0se48_C(Am-hBQ\MJA2E+5Ls]ABt:d
'=J5=oVK`n!m2iP%O:goh[ksTX\g$]AcUo;g>,qH=`N^0CC>5T_/53hqO*sP-/mOQBQof9"4_
=fu=M`cAFK"_%W=Yd!fbdNRCGFg`69gq'`Dpff'L+5gt^!)S'5L1Bedt(?GiR]ACeUYMO[U;&
-CUm+:d(`&0aT3FTI%G^j0FZK&eK,0AQ%o6.<N:MC73nXT`D8W)jcucR[8K?9U6oE0*j+DS,
X2)7o5K7).rp2-.4<%copPrr,`efP/E[BVaJP<1ggYnYkOa=hdqG.=K]Ael45E_>1u[ZFfWHG
n.0[-EeaanBKKKB"rU?_b2-l^)mLNuFM!2;[sed#IMlDRgVd3?=!;[G_n]AB[63mYiU;oW0S*
+T/U/Qn$_5h`4.e%[>O>*)7?rcQRkZ"18?BaLcn0dc?$g?'o;3G%".s&OBS`Xs3e@9[K-ATX
lJ^CYZuVhG(Td1K=JsV!m5B7Ws!t/n6jjj"f"H>B@@t_?Zf&)oAs#M)TWdtbp%i0V8;eVqKB
kk+1Yt\S.'3qp/[[#7$,!*>`<r-54<XtqE#2u!U%(;]A;t"?Z7Irh4MGaFffku:+-#jhV+a45
B_bJt-/cOBHcGkk9dq"]AbJmPB-<s?=fGR1O-)^d51sIDmpg=j6-D&AWj5I\bi<WX*/GdZF?0
:8<W:AH%%E$W)g[bUG4W<1]AWU`V?S55JH<0*,Wqr+4hDE,NecEc*7,;ZFN`L9Qq8^k<J=;to
LMD+Ae<tdj_WNjN.Z@CI&YC-=D%)Gp)mFM[)#hB,,4alE*P*r$;?rRm*[5%5#);6GO)QX4/^
??gHiM49Z@10%Q1G:!e-!,OqjI^ar2;G!5WkIT6C`lRl!&%DG.`i^3)0,?]A<pF"T89;]A\Tr*
fa5B'=H&5Y#_ennrH_BW$/fpD1_j5,BiDs>AekB]AU_eZWQlhJk"-rM2`s[EqT`:lc`AQX8dg
o3MduMr=#Or0`G:+*WJ6ZKohFIE#n3^&gB=R,ha-7BG1AkRQ:,=AX:^Bui:<70gcT8_!pacI
<$\X"77q&3FK"d]A<D%QJl[8=`MiX-8S=5mNWT-]AibdqWgO;o>".aUdX4YjW@oGe1fq7Xh33i
Si<CV>.@,_^HG#^Q+bFS))Qo7oT1KOo'F9u"@:j+pH'<q.:g-cF-PutQg*=7C@R58<Nb?J8!
:23WjQ,ijIq;"h%"p[=75^Mja$+(I#L2;3s7WAg!<^4In&q2]A)")6Y'mMM"<<&MbHT>f1?cN
9TM(ar"e5bW3Oc"tOHP5JWGkPIh*f)2nKI$>W)t^hAj>%Iqe`Y+`?>%d,1jb"$g-lPNK"7<V
,EOMe+0$&urXgYc]ANUo0GNeSWESoZp3,k%!&N*HO#*tEW]A\.95_M@'FW1,(3iO0(:4Q[R98b
?e3pfg?0A2Be?4L/)8<3e6.oeOO^naGAMUi\?sCEn4::dD/+)h_i%-eF#uFFihGlr]Au,FED(
Lh`;IQ7uC+Y7#Zr]AlRO?1)1.-5+dAn3W8^cf2N$6#6?o@!N!NXX9V7.f\_^[b[Q!!ir?`qTj
F!08qn&.HjP+2"JV[5EQ0R5E`is\kD%Llo$)ujshd`u='Fe]AQhm<3_lAr[R4?]AE**DG$82-B
Lkajq#W:XgJ.!0EC\BjuI79#.l%OSNchSi#ea?%7e6HUtuZ1%",DVL\a6n`))U)BFCY;sd&u
5Q7)Xa+G9#4`@sT+OQ[SO1PLjl;pFb63X^3LT$.hqTI-J]A-MT*_PDiA-CKZ3kFH2*/oSo,3t
]A;=ISbJKM$`Pq6Jk*&8ro-Oe'N!n=ta3Z]A;n/CfD;frVY;0-AAp%7]AkgfP@AbH)&!%l'b7V`
L:nsn1]A*=/T6]A.rrs3=C.^VWJ^;kkZFC[YrJ6PR`;0*/F+!*^fmg,@nJopHMU!;as`ce^gY<
m1(6eLmS%M%BT$:N-ujc(EE[7=W4,\+_a:#D`okpB0YI58'Na8lCHA=E!2o#_-%)RfdD?ma%
[j$mPT7FR7Jd)J[#8(`Omcf*W<!`aE8`pnerbYAa;D7iU[aEFM."7uWThW(^H*XCShDom;ao
i'(DgCR8Zq+h%)02jl+8'+no<&'5s2a(]A-c-q;LJC=:^(.BD0`?TM&drupd]AH-_U"4G\A1bJ
]A4gl#2m]A;Nl0!76bAtf;_(-reE!"<.P`c!#ce&EiMX8f#N7(BGt;b'VhM!WZ7G;TfUHCcbIl
dF>hY"etr^o`b\@V,,R#tTb-Nf\0mgafHq"?*sWsEV8>7:!>p\"i^733k/Lc\h)]A36(9>2ai
AC>mo/\Kt_XJ\80u474Vs5kX55!K(\kK:a1@K_If;nN<HSutOqe5l679\@@'HNb#Jc#?=KI@
-1,>TiG7HF\9RgqB;aeiQ9+HssY/-*DE20k+QQ-Tr&![*^kJ,D2P-`92R4m;If#oahQbeeY,
]Au/E3ot'K=<BUVAL"CVn4geRdS.ZXOZ$Z.tk]ARX*c#+_]AOF:FD<X.KETfI)1[r`B=Cn(oiHr
TP[^pu%mF(kHn0B(J7"-<28$b^QO-k?`e[b.6ZhHXLLFDrg#Jt6GU^>.QY4Oh2@@s12POP9&
IV8.+'Qo?l!Dh)/^f?V_'+e^m9daP>?_m)l8;DH9"5aLm^L!)31puH+B1u>+<S*AeC;C89DW
-?]AFhXs'D#?[bU[pRl@,lcA^&._*WbKdO.rr20RB>2p2fcfa&g6mjoZ6MQ>H?snt^.M!iOX'
j)<)ab0<Y(%lg-#IUQGA&;)5&=P#Jg(eiGKcY(l/=X>Lif8SW6!pAcMian6hjDq!Kb0m>HH5
&t&=D9YKP>N4&h3JqB?U[,>49.:Ea7hF`An(--8t>F_l2#Eb[VlN]A3ZrG3EmDW&S6i5#ng<s
p)kVuor:<8*`VdI]AFC_BeHKTq-p./>JF9X1dQo<6iPj:272aGP-r9\_$m9!$A;<2>V.OhF@Z
W7=)IPD(M!#EYG,U$FjQJ1moWqX>=@Fpk9/Oi.j0)B6PTd)163X/dCIj:J[8r:BR5&X1e!S5
AL4L8Z0>7Id4ZNS9Ml!DO',7RZ1#b-iXAZC98.9Ydh7,d)'F>7U@tFo^c[2@cA!4VOqu#;VE
kcEot8p%k2.6]A9nYkQa1k7-XUS=dX$p5)*To:;Da'G\]ABC0>]AetR#e$3*^LLl'SqRn`q/uVo
1(q]AE,O-4Jh'3p%Q-b#2(oC%2;8rXh'0J*SZ82l`mkZk!;YUg_$2FgI;.GGO]AB$P>rbaTIK.
1rlEF2+;#W?Fd*[6aDimnn9[R[qs/?ZHhl?6!gl9A5<NR71LdU&"-\4EUA$U&P-0@[nt!#W!
@C44Ork09AW$38#S_=QI(h0n0gEGqq5V_XE4O_?]A,^V%3A`0#Jo6pX,tV>i27=$`o%K%^bh%
Q[f(//3P^j3m8/bH']A_mt>iFN1qRDCoi&<+bgL.&4m'>!+N6e\B2iZn..H\%a5M:B#ERD.6Q
\uRhI)o?aY;HO5K>GF7.FtfpBO2^[$&\e&>;,hMOG=ar9t?:*.r\QMobIc`XSDlUQQ&&a'[9
[W;u&lbqY]Ae>_ec@'$377(<Amp@A-@2)NWM0kf[af1m4q&f!RBpF@?=kc9WW07+,C?D^<j9C
q`.q3d_uaWJ$\`:Q85p`u/rSPB"AP]AnIPb\an/#&!Qr&@$m*3u`E_!6YZ&EkRgO8i7'J:I!l
q&j.".4$AcM7lc<hpV3^"af'diIR**G]Au`bUKGNnO#^ik0f'$*\i&Y$h)@$JBBJ,]A:0'o#Q-
nj_kI\K>K\97rm]AgS["S*DBKe7><FaOXR0W1.Lr]AKL]A@[Ml91bYd*EHBgdQDOY$H[42%jnpH
^@guUaA37!lVNnXL^`<Ic$,_rB`Mm##sXO[.S)CMLjV/Cs_i=.,D31#d48prEuQb?c%_-iWc
.l?,?!.0aERs<ZFd*U-$hmTb^]ALS!,dm2A*_T'lMH7qXo(=YJ(=7V^OVWWNICbTXZm9/o;pI
RDjjc!Ykm;bBgZqN$s@i>54ZbpeLOE.3+Z*j6KMcP>Rj-_UXPnsA^&Z1,WaCF+^M?&fIrA@R
R-E>n,mdHcHO8aKg"-ZLV!PB'k7lAaiRm.&r>WPRgqR)P>8hL"aR9s7(A9^iDO9`HM`I3dd#
e^q+'q8<]A"N)1Um]ATAhmH+g3lml[F#<P'gReuF%k_kSf[bo%*[a7q`$<#=$Eq53(0+]A(0^&.
(I3rm"W$:#fp$.1#N;LZYuJ&dCPY4""IR-K+!36MenFV7[F4npWNSm8h=6D<TGJ`]AfA?od.^
P`e<@<jJV)C,EH=V(c.(D]AoB9:Q?V(++D'NbHc'KZ=idYW;p`]AK6HQ)GP"^NO//`kiQ[KK?P
C#U[KnBAIF;XX4^S"HmstCj;&j-L`V^6MYi*A'*J;k0C!LeZf$d,_#^=J.T#j.6Wu9l7Wuj\
q]APa&`a04k-:s.!-KKPDA,+j"%@\WT<G!WLQT&deHD1)e$gXST'0EEG&JKU<]ADL_C-Q?me>G
,9cFCs9>cl#=D##Aj.J\Ffh@=&"-o@,h;t-$oYp*@G,m'-b5=6M^ZNo&oO>ol'afI`C$'[=s
\g9<opL(aQDQ&^!kFZC>ADqa<i\b#VqM#hcM%K5nBQ6=o&oA8*T'ZL>Nj%)"lta7&d$qg2,"
qpFdhkFC/i\ut5hHsYFt3Wt%-G7m9L)amiV_m`#:s(lr*H2f;n%aB5t)5HuYfaZqG=(#,gm7
]A_KZN&XN)XR;gUNUn+ZfBokhgZAmcclil@_TOhIJ`PE`tnYX4(?gj?G8hjQkeB@,gQ!a8gG5
M]AtR;-9HT1>qG]A[C3a1XgV[H(TcG."Mp[p,/E2p!7iQ(HISL&jY$0-N9P.YVU>SjhKRcbER@
g3oh[bZ/r6;RjR6n*I9h*2uCNE=eZW\m["]A5fM=6@3=MJ&-msf5uSTqO%KWada.VhkcF_)\9
\Pe:).:1V7L30O4DH"K6a9e9jH8-bWjJ%rpd3:GJW^E-qbFD_"),4eR^a9\gd6qk%WWK;gq@
N]AkZYSj37u9Mo\;*8jFLEG%S3U6h77\2'/]AV\9ma)"qE8h)>LAg4`-+.^%6io<30V%A)c5^W
]A*Qq8P#\W4UdD_.7ND4A/pq+__u#Nm.68Gq7G._m)f%IrlcV=s73]A(`bd>j5pckd#JdW[6GP
-itGt0]A,UiN>G4%+:Y3B]Aq&DD>`%#.l(s9m0@$0]AEU0sB^?g`)YE+")ko*<t/+H#s6%&1)WE
]ASX84LX1%RM3C[6#Fk$798l<pf!&Im=ME+q)68oJAhO*a>p1R,+M&HCIcrBBgfp#r#A66Y'_
#NB2uN7*LE,'pkPJC=@d,$7j#B$Agr>Ee=mfYALXd`*;s-+d/9m@29:i1VrOJihRNG/BY40:
"J;V+:3lMYURh#SZBJUa]Ac*H=MM$Z=lQtRZrK%4((9.[#,)Dj4OM1i3?TM`ZXYf6T^!HF]A)P
u00!:eT2ik=N;6*t/U>0ZXqpjc',/=S8heaPQNq/P:(QX?(SG/cspeaL7Tk8ksJYFWk8HLK)
Z+N<gVa5tSV!/-U;74<A7!_?fL+f*bF1hasHp1WAtD:*%hMpbo,G[-ZI6t75#]Ak%[Op&;"/M
^(+&/-))8!dg4PA?0u:n[r:e=*Kb5?;DNp3-07d'4+'iq/b;b$`e#Cqkf*$IiG2>WY;*qqR,
V'Au^#PErH`q:`Rc`B]A=(?kJGj0>Zq3Zp4aWnD0E'B8JqZdNkL[GELEZlg=r.6Z92P32:Ya/
T!,_6k'cZKm6DhNrS!iqj.(eBI:h]AHRe:b#A\aDQi"N%VD$t3`$FnQ=->L;T4=Cbp*X+R(P2
0s!gHBjn_L08rh>NBl=8%CPo:9_gP-(_kS0eQ@R?Gu[Ba,F3??9NEb3qUBeC\j;e14@hZ2eZ
P-j6EB5o?cu2=6`%'09!<=]A6>&JbS4sH(/*08WI3bQlQepGuI"\Euq\B%-[Qc'Vo1jMYAU'f
?)h<'+ktMa`_GVA)&_E"'qeWcE/I*h<"Le2HIDY5osN:0!e''1bd$J8BGb2)!b<H97u8Nf[Z
Fc^\PR_'KMT2Y&a(36,N,sUA.^"lM.R]AVJ'^"=rmEOQF@a]A'Pj(A'bd-+NZ2t`&g;9a^K9-#
P-V,h-a!)0foME.^*,0.-V$H\E7Y(,iMi!e\.K^/S/:gdiM;,M:8'b%JFp0@9b[*YRomsrm4
4i]AXkZ_$R0kVHE9bu[J43V-B@/F\*qS$A9$q\Vfi\&hG+d"]A;tX6cJqmHA4;:OZBnV]Aumb*@
U2KkS:ET=Np1\Q#RYc7`uLR8.tHB]AqroGV23X':!JOUL7Yi`dW8`o1r_Gkc0;]A:=$9nWsSPU
EUf]AH'>uu5!C#Hd':/IC+j3^8p6O4FE!#1\V<V[LHdgZ-@;qT2Z-H&>2([E3-Ai<s$\gZ)1/
Qf`:d-HGRkDI:c]A+o_2,*M\Gl)bjY3:+]AiXX7(oK(dRg#!RX\)4-6tTDZ9<Q((FK<KbqtWA`
bR*H-0bL/hVZ?YBo6Ar0kTj=en,;D\B85(TGJqYam/"Vh$2oFL%6JbQqp(JIhO#Cb[mH'L\d
:tFHnV/A8OG5gH2c$&d12?b>FkZoL+UL6Z7hA%_c?q2YEg17lecgJ)ba!U+'MPNebMrBnR5!
35@cYT2l)Nqk3:?mY:!r0bR`@A@(L>P6qV4Q,(MiIjV<Z5N-/1Jp0Sh\V>EhgUG"2AE$l:J3
Ff,a/Dj?hp!mZ@]A,e3K,S@G(qt&*C!TE`)"W44hpLiC9J'9?fcG<&>#,<@!/Sr\/+fB$&U&]A
"&rO%dA<BbL?&"bn]As83eaXnEM.GILCRB?"EEaHI6OPZkFH`]A&DPKMEfY\J#E=CtfB:qLWfV
$3`$*^/'[GBUj%.?!LGa>BA6L/\A#XQ?5\<ipk8iEfV7]A0PVPe0J3&s5Y%/YktiJl3SiX5%@
PVL'-NA6C[f(@OES7Hc9GBq+k^g]AG3VF_O26fI"1k9&\_d75[CV^/._iB83AZ_g4OI;9(u7C
3/.CHJhb(,^2\'1:&1\/]AlpBiOP:cahK39.o$l61b=;&XK]A'Y[h"%SMR2>_/g67)uI\^=mfH
qqX%Pkr'U6Ae\,@1Qoo15RKI-cP]A4[!qLA;e%=hlM%OG@?=k_F>W^fOuP"qqcTBuXkfZV+AC
^L3Oo07K=H>XQ[k=ik)_[hZs`G]ABm+i@+YSkaCAakO5M+E37`)/$m;$ggl8d,(i28s.fKH.J
&E=@COj.#eDCT,a:&1kSXIu-TDVu6S!+@rojL[s%&/5i&&_l3DAS,;[!?`$I>$![r@mL=g3d
]A4n1XPZnAlXXg&oWAE!8!k:o#il.oYI0c*$%sQ`F)_G89+O5W$$WedL>%18j]A<!Kj19s&5V0
V>oP]A,$8_^Wqq(A.!eAOG@Hh5/'JQ>,^TokUX2D?GbnKp4[]Auh597#0DI4@n59Mi_3!*%"0@
jQSsA?SN/a?*(+gLlXW]AL3[hF(bdK*<fYEddFaH5%e'e\WX(WWs%:b[sKbU.R+9t9=O9YbR5
&hF9/*FqHPpBmKf8s&=LYG#P,tXL.dJKi'*8Gbp*63,>JiR>e(JOg@9IO"OKSSHso5!@[Sa9
T%\=I$XS[tnSVfWMUpI+=aqr=&eg@XYAWJ(3,UdJdmX(9?P]A,<1\)cRVbN\K9M;2!Gme!DGK
H"!F*\PUY%@VcQ1Mcd@S$;&41(BRMiKWAMUO/(h@6TE#9rRR*:8h%aSGl^H*D7%h9YZm+m^\
3pD<@@%0a8+>#\;bOGs\ShnC%dZNfM?2\]AEaiCg"b=`L!"U9gQ^duK(?;?#U3FK93NZa,SnK
3+uEZRb:`ja$)I6-Xn2fP9Aj!DKS8fmb2!hLuPJ77"#NY4?@Q)jihuSm9',d$Hh8`Pk89Mh@
[/I4FZ'U;cW0?liW\hDkU&gY>jphJ1K8FAJ[t7cSuP96X`nj%USkX/LQ0m^]Ab&W600m0XTbI
]AT,JXaKln=LlR=>f4okAdR%rnT>,SjhaKcd@ejB3qUPt3@eiKORT^<Q"O3;R^3uq476$*dOL
0ZQ_b]A4V/uEEpJn"6?7Gg:?"bD'_:L@Xis"d=2=o<oY@;"JISb^Nu0-DFToY*P"BOFR1'+nh
&N:.KP:;P?7+N@tl$'JUk>b.nAK@D1`UE1$[3!8GdSBrd0*!H1lKiV2`!0P_MYp9VWrSu:s_
(.ri8:>o9-cPm]A"8<2pi]AcXiluqiqoUFlcjrWl*r09WmCq&`bdGdm36]A>UMPu0PKEQ4s:7O8
iSGrL@l,_J[+NEe%.7(0@W/ORkmq+pbG"ImS#rnY'qncjjAhgK^Jl*"q3J6lI3)-&5h*T,Y>
IG->(T4;M9TX=(XFm<WI+01llcb</cg3")=\up)*S,<BVU19L&c"@ekHdB:4osYT)QU5/V,=
+h8k1umo"1^p><T4*1,fCIfTe-8eV:1?.PME3*G=$UQrnT,GGYajUFU[MGgc+@+hmT@s0@nf
p]A`YkF>$?%e$F$K@1+pBTFt(k>5aU1Wpt+Zqji]AH_:X++6/mq"eV@632o4I@5Yf3>4(@5X26
`QA]A(7XlI2qP=d,g^345PP%tgUiF?[TB1)i^@GGD?QDR#RW5`Bn,te<dVEc[g722$Kj5F!e;
F\<#`5Q=F84!!3]A[0IdJYZ48%N1CgVnMYKWm@innCCWGn+5c^LmbgGV[AYS4WufCis)W(frr
*-X<5b1S5q^De8Db<Q7<9eDU1QrmIP`d9h94%tQcqX+4oEsXq]Anb)Am`;lC]A5SY`I,1HB,eU
Y%>ikimbT0J8/I%PUccsEbu]Ad>'VBf<,dSXURTOC.'!RqWO)^=T@8&+QmSZc[A3M7mQ#3e$7
nn@W;29:F+)*kTsPlmML#=17PIchLbQ3^K:8%n800&tW>)2hh7g->g0k(jeF+H2DRX:^-dk4
QCf-O%S(^4jWY:G!?%&\pIrk>5%Z3iFo2Le`&%]A50tkV9>Yg5b,JTDSYZ=0U*?$5dHl"B$Lq
lecHAqXH,[^-F@lc[f.R=U>0IEXOJRhC3k"[51:o&YY:`bT*UcOC@oj+e%(k'>Js@iHG:LR/
V*i*R.eI'75%nXJ8PYpol]A10TBY,LQ'f/damBWQ+a?sgJW%lQq'*]A9d3WrOAa@biTZjAb3a(
L`a\R^#4\ef]AS"]AU(7Wp`)BSa$_8Ul4h9@cYSeb#]AK<cFhVQ8s28BQte_r37>Kd/M4RBAT`B
GU!Xa<mS@in+/j1-g/XKKH+%I9m<u;M#B,.K=73EH0b1U-d*/XNh]Aiao2<,.WBY7g$9KIg\/
njHkVu9S_&$"jrZLQt?2l"8=P/:)-MTGKLIrVCFpE=9TP,TeR@^^d<9VaH[R6K(E5`ZWl,ZD
TsF(=_H(##bC7)+XZ8601'pEL8rnp)_G")9AC`ZZT%%Eu8@%3=_s2oEOV2jk-65M,0VFmWe3
d!uVrLj%-HH:/0*6(&'OZ:npm:W3shY7giqaaPTn[N=KqqWfX6e^`1YFk8bqIGglI\#N2_Mq
Ykj2BrnqN#UKH,jmWCJh1CcB7c6o4IM>\8+X+$i&p:/mo3Yh%/2=E19"SG&Y&96pMOF5XUTP
r((:d#(cGOlLt(pA<=/-Ii"IEB74)M[dQ5LHR5mB0H%Lss-aI!7DCCY;;KF?5D"k*&I+4m(i
UX5PU3_ug`)38WHU.l,[j?kIZt,lIm'CS'\6?%F-,kdB"Vp9*0\c!1DAI9/%.uLp=rAH%1lU
g.4]AWbXkq?f8*RmW3OY;4"-MC%P=d+X7XtDE%SC[BCk^20Ip&,MVlWo=K!4kEK2F[B3)e%=0
&nm<T`5E_KUMN!t0^q,%-N_luCmHdQoO"#Pq;0lqr;#q[4[B+LZPA*A:7O/ZE(R=miq5p/p!
&'PeW_@!(eu;GXY`0gq5_0XrdP7Zri9Z)<?nZbq:c+p*B'Rk&O<)-#%N2CrO>u&8[#="PsHk
&NIeO7-TF\oONmc^\G=X-je=MWeb6#G"6Kr=,fT4"7!?AfQA7n0Th#"\Ju(Ufl]A'+aJ1q.Yb
KOB6N,f\"_rDUtZVf3EqegI,UF[rIlWfBM['TlTl$bD>%4NbT<.7(--K.sF0a=]A2]AQ<OjP=c
/=(p:":MlZ0Sg[l`qii3X&V7(@0H2;ob`U13=g#IM^GAm0rWuogdC<6IKS!_91r;uH[\e!V&
4B8Wl$gUU+kdH)W&t/.$?qhS+(')I'SrSti@2d+]A*7#ie=br4bCtuZk::[W$(p6qRHjr>\6%
o>F!)$r9i5g1GC]AY]Amldr(-8P.#?0jp_bn\>,'$^Q%R?@8NYgYL,RSsb:DEI'Al`;'$DS^]A)
)nib/d[-2p@QSCpY2g7Z:oB5#^q=>DN(Oc=*s%$(h>E84pIeImV2,LqK5/.&D?<'iG%4W2Hb
Mt%2MR:[nDIKEP@9Pj[njc]A\@gW*Wb6&uPqmTQUhb?SIe84_m`G"We)*J(QliF?ScZ?>,&@6
?V(-tg\!KBYYRH4M/Dr>;48I(6MjI9WMQXMAPc))WZ!80F>TD;<o[q/I0,//Lu2M-P2Y!9br
lIBf:Fcdc.TUU@Xpah;j7iD%I"QZ9$OArE>G2FoLs2f3]A5PNBfI*]AF"[T]AuZh1GUDDPk,<7$
0jIB:s+TpM`cIrfGlfIGHjPNTV\Rh.o,h'_)J[B:Cg[jHsfsR/Jn;j8VetdII`IiHX4d&$ID
p'P:)>$5Yag6_bAr]AE'P$ZR0YHlTD+q"o)EKL>g"'mW8O:4Vni*F`GprTN$8;PVSGa$/gu14
S:l[N.fOpM<u95\`o1EQ^3d0qadMc6qD"0SsAq\kjJ>3k:dkjCZF.>c3<rGs(8cOB@;m&/\,
(c`R^8W+".'4<HG->H&Vu`Ie9>X+^cW=-!q5!:]A0\f@p!eS)6H`#OHCl$T\E_l,FHIdTb6#5
_Vn:lqEKjmHuo,F`2!;AQhut^o%hGRCTpF]ASd_Efej_0&`k]APO)K5'9<mR4Vm"?@?:/D6FVE
irPl:70uN^>ljgK]A75^_tOnAIkN:+Gp4$,-30[!qEts!UKWE^#j%07mEG81j]A$'?0GTm`6UU
@F&uHHV:XSpa"(MAd^(`0W>9@6._d`F_`3lS#3N%*CeMfFOc?2U4HJ9o-$t@:3s!Epn$XdZ\
P-q(3oc`FL-r-#gWXJ1WUfVcN1WN6Ts!7AE@\Ej)oY+,_$m1MX^#[/7u=@O9:7L[43[PTJ2:
%)Er<L5HTqEl=Ms<n`]A)j[LVjcB&LQLNK>'VAEtOhiU9paJA=o&]Ae(C\2hgI/V#j!f-4uTZY
ZEs6G);nQmYdCt.BbQB3Icet,]A(F']Aa+Ch[)@:ZT@HB-9PsfTe4V<4Eo9?tmWTg\XiT]AspRP
/j26u5LV:jhiH7Dm62-fno)VX^:qdfl3=q9ceHR?j/),O$\l_8G(]AM6E']A!URKM?hfD/MuQG
qM"+5t1Vie>E"i9F=@789!2)Wna<hoCf2LP&eZX+M@Z-/knnTJP%[i(9\[uBm6hY6-^<tchY
0i65VXWt.O9cIogBDGBH!*&E'#?*S6CDQ&oUG`NQpM>(9pOskIS3?5\PNR34*F.@6),$]A9LP
B%)?L8OG^H4cX&5Y7A'3@^Z#JLQ:3(bqHKiY*PnPer,0cQ@\>8qtqQ;AUUELHH]Ank0'o$^r:
8,/L%-4rRJ[iZD&S=5X6pZT@GA<D=D,rt/#<EMAIL>#>57n\3C_\@&Rn\l,[8
/S,@mn96#9[k(KD=3FNN*h(bdZ(`ltp;Qi`pY*F*BChl0q43@*J27JisK=<<R%nYnMbK<aKS
?WT2o^@4O&Oj6RbA&TN>uJc7;#ApiY5n8sr,^kI/ZD_(DomKFVL$hTJ#YPc\U`VPDp=TZFRp
XPDO0#)fQpnn;@.5jXrPq3F#Qau;&R+U\L?XC%lof;2Hb!>>o#4b<5T#^:PQu1L@I\sH>1DN
5?o#n?Ojhlo=&/nUMKDq=h-Q(,T7IRR5+)@Yr_>8UK>&2^/8Es#-M=BY$;Z89h\Wm$r1dEK(
.m^.se^Zsnm^+g4T=_F_Y/EXikARs++;`+p_I9ufj!dR_[9MA,S0sU`5Crt&Df04Fdi9?;kM
>6=E:UEB?4/a7\j\uV.A31KTu&J3i.q9FDXG2%kopYE*VanBA9*,g"\BTV:rKCC>STeA3cUR
O82D<fR.BMsNbcr/QuB:lD*i,WG$e`Y?(4jaRDs^s%17Vi"3%`\T\7Cc9`]AH!ZFe@![dAJ'D
aVJ.%@]A-=8Mgd'<3>qlm_M^`"7aiiC-73C_:SHSh<3!GRN*]A0(TYYAY::TnH=ErMD6<24Wnm
j)rWI>!GpU93)m/"FDhb%CAChZ/LNc8=VL'lkGtdl*FkQ9h-H`;%@-sg\5KQGUXHdO/C=;03
5S/[-JcJRBl*0AGTPD2>nG._M52;-+SfL)CLV@2n6&->hQcG]A$d@)Hjp>ACi7IP,-X.n)qC5
4DC`G;BH#"$*6@!WR'Z-o=m9lrTYLnXO@d1*YUdJCV4KTP[%E=G^0jkEN)VE>/e>Y&?/5bL1
#TjmYu#'@NYd,FE$DK*'Pd+AIGgSSP4!5hL-U-F4%69!X?PZ2,Uam[R3e+p*4'`V95FAD8a?
2<+S)kr*CmOP=&MF]AGA/"<i"[q97L"'qC3!ZEF3nYq!:[B$fiZX\+@_E"?MA%GiT?"1_]A#>;
QQ@t*/o/77j.WCr"gUO+W1_H,bN$u<q1(P*1?q*bD1lp`IS+6?gV]A^rLkbt"Se3E]Am2`1KOg
S8t3WR2i""n/-\W$ffZ`_tk$39j'h`Ki@aH$^GfF/_@aX-`<N?RnHXd(sPsd=(.D0YPH,j@=
mIZkg)k*('D\F44om![D0K$eJ@<]ANp"+iMS5Z9P2IuZ)5Uo\UoQnu=';kgGlK&67e<fjQ!Dq
-3IRA-k$iF#4b$K<FeFef=R\_6e&kLJH1pHuU+36-\q?\dl#VU0gj_:3YRigaCOqtXa%@nKY
&WE+/?]A>\n_h"o?\mSCYX5F8:LG/9NV8V+$"^l*!*^WUbKtO'-eH'RNo$4A<l"K*#t1>q$\X
,u:BZJTh$F3qS#CC)GChKH6a%3T='*V*g[FU\#.#DY?`q]A::HjB18+O(iCtpkc<U12\U^Ls[
>O)j"HB]AppqdIEk^[Wp,(dSd"E!""C]AY`W`=m(aD*aPpi3Z1q7Z[:9"n2gm\nG%>!)EbqUS"
WWg;ca_'gkF[oR>t2-[_eDfiV-+6;c+$*7`neYLdmf5nfC1;*$]AJ,7-.H/qR1r@h7;[fL&1W
`&(2e1@rVZ-OJ"Y4oX.1Xe7@+F<>rD57F[fPg%L<hCd(V%fMl"*MAC]A3"td"P3J%GEULrG<i
A-<7[ff^]A$Z-k"/[<Z9.%H0_(=VWiiiW'd7@p<2L6L(CP\eBE&p?(TY4D=!^i;m"R4r_$PQ=
DeHAmZ<Rg:iUM9qaW-if-kGMZo4&q!5_peVaBV&<jV6NFR=cuF0?$\loebEK`;e6j]AhQIJWT
SM`P<`]A,PRqLA\$TquSR$jE$31!NF*d@8?p73nE<(68]Ae3!&:%J-;G/Y-CseZdq"F!no3<Tp
06t'+DVZe,;rWDJ%`'=laHUM@L!t<"'fCoGG/79kWn4d#XD*kN&0!.ETD@iTpQ-$D/FkmlIb
#O$_:Q3#-GHs!`Tt<9Th8Wg^[8I)jFem[6:B$*F&F-B/h*ngpGrk&C0('H_9]A0gSI,mO`*WI
Z-f=(epe^q#&a1"kHH.;[:ZHP``c3YJ[D5Q=8o1\J@ULfEa9I'[NNg.oN@[fMSTrI$3,JR=m
OgS7.@a'ncA-0@oTr!R`?'KS'>"PtnHTl@r"kO0MWn0/pASZ=MR9Xl/5W3-i/WUiqUtP>`5j
I;ltkQ4Df#jR=SUB5kHt#uY'Tm;As_'28[<:H<iAXXF<K%cJN]A"Wf16!jGgIH#aa/mDJpm"c
O#Y%n=%q/I,HO]A)SH"e*m,D(B5iL\m%HteKk[R+nt0/,M4)^.kBC6dBnE2`oG:]A/&aBrY_,Y
Y3UBKnGXJ1g2A-C8mO'tH^B.XF*+=T=fnfJVC<S,DSSmcne$>^4kV.n5/RmG%R1Dp>RfJcDh
"hW%,QCDf%!)`8:suQQeW#d>\.LU0`6)@oTE@HgF$B<iM;-rkcd94p@>D.u\L-=mmtH2(m:3
<dUgmLXaY<a4I!l5h?KmAlUPrTO;FUUI,miQL*AgUU#;V4&o>^lb0DJp=%h-r\C%?;NIs<d@
E4Q%sQ?-gmqY`)l-Nil[1i!5O@bI[\#EX16^27>1lGo-oNk6u@-]A2XfGm2(qm6,DR`Of50)b
s5,IF/Q5ElKMSF3YIh;3V&)#Sa>3g)U4Dd-)IGAL2UH!G#Dn+E.&@,:k-f`/hg!]A;89[;0PW
kbM,gW8ZD&6R!"1o3p#0t/s]Ai?*rTsIi-]AYO'oh(2?5$9m1^b/^FQqCSaEl/4S3Nheb&IdKn
1'f=Q$i.5R")>S<CVI41j!D/D;A6YDX-IF[>Z>,pmr$!(2&VfQ$@k]A7$7a>,Z*IV]A0KU#*JS
HN5$t4Xh#<;cD:*R1CoDV)X6J]AG4ZG95(-2<VNPQ't4(HX=b9ob?E\ck":Uolu22>g1kV*M@
0oDr6,KrQF@R2p?4QKY]A8/_d-=TL'47PX6PF@\;u(4D#KcX>u(Ud3N&TF\h_"/]Ajs'!&K;#u
B:3=K=7QlPH&cAgbO%=$L$e4b+$>7JC09r>\+YVX:<+d\+'%4o=d55E)6lj_)`R_?VfS"h/e
X&A$be*6Kr_P[5`:&5#XE'6%3:&-PC8C`8J4Zf#$"*sfo]AR3k^3L?RK14ooa_nsCC2pshYmj
)6q*boR8cGn*#7W-,G.V)nD)Sk.eOQ!T8._?fD[d,nhGKV[f6lMrmg$m!9\!+$[h3/RJA-pC
<.+sN%%@-@bme2eXQ>bO%sV'Z.=o=PT2&J3?)JAlL90Sh>EP67MtSkBJ?Ndj>BTHJtb\3ZcE
0Rb)TL<Bun]Ar-9P#L+h4VpG1J!Q)`jRrc'-k?_aHo!!,O#&69X`2t*"kGog2BTHQr>69Ap!H
CYA6`j*I@]ALT<B3uZ,?E<PG'i8;T-W$FkTfmp-keDFTTEBR3SMBFb>,VB#?.5./*WSaS3bk<
<"'GK,R7A]Ai%,I[$#L(bKfC,)c/cb>9;=[e(S'(m$E-)73#N%:u=$f33Q3MX'YhB&-g3MGRU
*/?eFR">c'o>s;-W%DRU&:>Jn3%]A3_p(S#[jTCa9#7`7r&t\D3T((1a,,,fkek0A&@9@(YHa
p9gP[cuDk,SC^\)Nd%CGkpJ(F^F(WM-\HE&:-IF.kd4d^='_C(TF*!*8e0Z"&ZU%)c7FKC&M
@A=5>nD=b;H.6[GlK>9JTCrL*Q(eJUC;;bN'99;91":uRj\>`VM3U+Aj@=M-s"0Bno</rb$k
3aW@m[LeQZG]ATH]AJ1D)2doJFT?gd!+@+[rSS#!@+af4(*BMp,lL]A;cgu\SK^_88O'9HB&Vc5
3f)&^ph*sD3%%Y<;Plg[;f\,*-_[5bk0LQbfGn:&fU&dPua,3.XQeFJ"K=XDe1jY8lZYfS;-
?No#&V#mM:$>1.[2SLQ/QXQ\=Ho_[CJfP%Ru72tWfFl`?aK"CQ=CWk>FNb>r,9qt=V@@!Itf
P6q@`+"22p5.MZa")@A2go,.9hHg#OFDM4q_Eo]AQ5I;u-AfkjTO&&A\M1+`!Tm=m(P8&:FQ_
21.>'poIHEA?Q2YC?U=LC+e_bgP)X<Y<(@a%obou<I,4]AN9I6[*"YBkJt]AZ`Ca7l&T(g(XCJ
^4K]AW]A?'Omg&@e79ndRVP86,HGW;*O6i8bi:c)&XFt0!u)[qN6L*CSm`!M`2-`O:uK4r>WLT
/F1QC1a>?cVb4rR>Z-[f7J`OiH'O^54a)*D\)^bT*[Jk)"*I/9de7=+BDIi4_T)t)rK+lEY`
Bb;dFLgp?`rBb8p)UN%5]AsR=-cb3@faaoc*cfq3://?lLEH*B,B1_C_,&P`fsVbaf1pnf0P:
]APiij+nKtpUh">2_PJ5nY+#`Vd#[NOR.K(I$\:aTX?6(5W`?QTO"ar3`$!!U>PiJ]A<uTkbEc
pglmA_XYDHitJR0i"qC6T`?D35qNfI&B+M_O$dt(hi3md-M%7'/om^0k7RromE\7-li@CBF.
ehe\+@1Wp;"e2iOQ9\nsU"Xk\1/%A/lS[O_tHHDic3lE"V1XcE^HdE6I);^]A;Nlc#S"/6B9E
`#nU`DpqXpSOafCQI2,4@be4.'To/8G"SSDYHgH[3+>@ZQRPObMqOqET7j[X21uCQ$pHb#F6
-oYM)1[eT0'PF@f$]A:%lBDiNlBbp=BOKmZ#T\"*D;k?<04?qVbA_a9%h/Me9!sjmC2+hX@q6
#/aVY="AW4NZ2r>EiJN;U(5k[WRJ^t=/_HkFs3ch@TlR\c)!%Nu\'V.C+IcXRDa"rtMmnTZ4
gCl$WRmYjXWK0C73!3g2#de/m!Q&QlrV?!>2;5-.F[J2`l]A4"PLP%XX!F/E6bQV,L)E^EA)Z
+4HWHuKKC$.C95"U8s'&tL.XsK3+iHsad@0BBqB<46<?QLP!P$^R,'uNh_2R:-\*=%_5d%9P
6J@sQO\i<YW5o/QeD*#hhp4]A?[D!)3+$Ep.A^/Io$EqWqho)FdcBTg1V(PX.\*S7'R:#T[W^
D!/GJq@qJMhDZl(njC3gu<H>Ha$a<9@%+p<:")TcN_r*Fc2ae$.)%+C-go+Yn`FFY>,XGlE4
%_FST7qhuedph1GD,,4g$D&!3>GDtDTUr]A&mc(i_7""2lGsl1;qB5f,5ZW]AV$8=4!TM-P"bN
T]AQNNks'.HIhl<nG)Ljd6C#cBU@YAr`BdkIWEVA7W%]AJpi:-I#K>@1,E^j?)L1)sn3ME9KN-
_NH53Rh.OC4iZB;>h3$X2a>.u\lK#l/A$+uHt;STsd[-NBE0%BWEaQk(^&*!P8\PFGU]Ao-#p
q760^HUF$6N+:6]As!nDbD<5T66O2F=Y5:@/[a"YQ0gbdIo=Q@DjE&A[@mP6@+Hs!d69A7:8^
;^_<fL4)\f5E!$$;p*.o10<?Ll<<=Z0-1-#%a`aF/L7D5jJ_gO;$;YU5)\[pGH1=:'M,hhF/
;"aQQ3?n\--[(AA63!2?OY)!CuDE3%@%;D'V%Z%#RrK&^Dq6J`-1dE(:l.bI(>-&N'(rT2V"
W[:dM7.O);U_J7^VLbo*c2/Vp&KcgQVaQX8^<$C3^_VaOJ`&cF.Wf<B^;VW":ZMLjSk[NW-0
Lp>%MaZX-q,Ke0X+XR!D:b_dbpaM5g9_5G!f*3$tLIcnEO=upmHqa<#XIE['lbtBmV?).+!F
#d$#9'":fF,$#cXDEKD+d#KR*A4$A#K898>e.#AtX"%)n@W.LkR6)X'F!k0Q_#qT-G5(hnF[
PJD43t$]Ag_8KFkng$(7UB2d]A57b]Al@t4dF&=D[XB*Ake!&eDpdL+2I#'B'mqDotu"u2>H$^n
7q`m$gZg`:$V?'L]AM-@hhM)N`]A9lf?W(@[?TEYWN)N@r)Hq-%cj-b+B`dhQ9dX9(D<,pjshS
EJ@X!b]A>GICl]AB:+#rk(Bp1r6QlmXu_$iEsLBnI/02cgF(k6=."=PpkBkgEIL%\@=Mo4k4`)
H:Mj]AGd%`bont1gY?88GI['[YMs:Pk,n%jhHKt__jciUmQro0j>bt(WP'l^!LdWa-[%K;)Z4
'RL3,C7;0>sY,[]A`QW!:M*3@Y#+c8VQMfL4uIsQ0M;!:YW:P-S`EcNNl>O'lr+W*to/EPRbe
dl=<aWTldr+>g+9G.iH:]A^E1/3O(GB)UgWXQGa)JWG@k(!<f]ATNJ6'/V4SQ'98D%:^d_]AAVm
)!E;ThSf,-cQ[cCpLW_Sh,"$2*$WDGPoK1Lh?**nF.Q,*#VG]A_#K:,*>@KB?MPiTQ'ckb*pc
/*%A@ET$E#G9j1L86+.c%gL"2+=J6!ejD$kqI$HbpJL`HmBY5R=bi@Gmj:DqAoD(14]A#/RY`
PPA;8Du'>_k00dJVUK6i)<Sgm'3g"#;T#W2QbA)W/4G#Z;=A!XM8FA2+S?%UPIoSBa$.`)6]A
;%15gL;t@5eduR:2+QiUC3S6#8(g3eD8R<:l4a9W9\ll24T-9&O]AL(XDKpPfU-^Z)l<Um@Rj
\-0<R;Bi,_2Ka&pQ(nt8B>sN>m"5ZG(55PgU?11%qMs`TSl#q0K#=t)#X-e0Elsk,2,eGc4F
9jrVREf,:'-O3^^kS?;hSNUJo!ML[6`b%7out3.gAg8ULVp4oOMDq/d9l6f[lA=jMdNopplH
<tED6)puZM8HZ^1_]A"RV)=R]A(NKcO;gt=Cc!^.8Ng-P0`5E(m('e>YD!mRS3h4-b>s/O;RLY
2Kf47*;bKG]AB6s7HpTH3/KGliOOTLSk&&(]A)>U&DtormfQk3&<l.D#IE>KI6a.N!L2363u!S
!oI.'h[nAeiA@96Y:(7Va'1W))?$g<PG5TrHF/\Y)ri(6pl]A5^FAd)dc?i?,;QQW"ijWCr9<
<80`:f;*C'1>:&)dYjRqIi_RE"c<Zrdl]A>&_+&RWR6;h#Zd":>=HuHAHc:S:s,,rJhegOS_f
he&O`8m<[;Eij#\6D\A,h4#QYAKeRN="2W?l50O^9Z;bdL>MJ<7Whm@'HNFtI2]Ag,5U?@$eN
<e:Z60P<POPm]A1MY]ATtWF!00WiIODDAU5PurVWpH9bIja'bMWfIK1)-1a'&KQK_Z.:!bkmV*
R$50AUicq,%MENrW""QG>J-F<#.,-JtYVKi.?VPV<D2"@7fLE&;7,b^L[Eo0DRT#4`YqeZNn
;5<-(\8"dRi2Ol*f@Yoq>2fi]A^IhkKc/kQ,,O8d#I0mID>RN*.b(5/'-iCZM8oZW_u7%8@K[
WX\CUV=Vgro4/i$be`3Js.PooH8a1*N1'r.kD&,TCTU1ZXFK%!cYmdZ@N5t?q[j<91jWNnM"
+*!G$Fc[c2q+28Ln9FECHuK,-_erVP>k;0V:@%9gA?,kb@6E:.Jlp#Tu%+D!14l8+1l06_44
?037b&H+.%[a:WK!:aEB=rQ<?:ht+K3PhH6(!a/T?pku`pC+QN6KD+WV;EYjT7.*$:H5%o(%
gW,Zs0]Ab"+^</8+e@Q`G&(DL:T\-'`CrC"Pc"HTIgf;c4F*Xp1Jr&mckTXn;;>;SCog@PRU7
#fKD]AgQP)L+Eos4bI!m,_AkJtR._AmrYs#uu^7=jHgGc/I(RK?7ou%@(08]A*%-/'HKi4^p(B
V0nqIR`8W;\UfoEt4'H(W7IGgmW#Ohg>SL6%bIf.5VP2@&`_o[qr&W*[Ju4f%77H51pVY;I%
=FL'#Ti(l.Bmg/&ibSs;U^Xjt$*5p]AG^#^V,-I*4#dq6aAc".3VqN,)cUqG)_M6O&*%?6%kY
InP>/IrqCDEqZ71'5GY3D]AFC.Y[co9l#jkh+[KsZe;O3YYn0>GOW&".=q\J:EO"78LR9_jZE
FBNSd?9_\##'NcNO2SF8]A1W!H=<TZ3-t*.F^,$9(FSO8WPjt&%).`1uT.+6%CtcWHdUJ5rBL
A^.J24rbqkC1KsEX3='7(8hHuV@NjC1!WF0g1c2!>^W!/A/?62!P>mhCmbQ3GnSpqTZY'S86
UQ&\?/=f.rV4H)h2-Dj8T-%7.]A1$P0D#F=JG\-FfsD:6?kT4d.q`IKl4;NfcqR)D(ll5Vat^
?i>g-$%YJAQTi/]ALml#PfibR\#pFY;utM0Uds#[15"kuHH<.c7eNH,[7.T^rY5JB@DUglb6;
XUJ`0!r;$Q4$cAulH\Yi))+eja9[Fr!@6%_@m]AiA.5^KX8EHe&8UE>d(C]A#n9Q'QWaB/Y&TR
_L]AQ?&dYf87F2aLf"Fnh`+Bj0gQKiFHt='))[mBWOVI!dj*q4;RtYCKs?l:I-#@O(!Y93#XI
u.9m)+?cM6]A\XNC$9MVi%'bpR+A[q5J=.(N+A,t<QSP@I")k!@Fl/:o9)6$d*NIegXs4?[c!
@uWKO%4\&P2C'DG#%<pm0u\C<IFW$0Mc\A!n)*o[f1GVP]AJ&SB=G0sY#G6M@%c?hXC3q4I#h
K?a3DJN^SN.3A\t!N"?/.6%(N5u$2/NW4ZtrB[g*:49De!LoDP(<nVq"qG/J+B@0Y*-;o%m&
\Hi8<4*lVk9kuVcU'9Ci%XOBCKK0?<3.@p>n'clF&>'8?+EBBu>%ajEMsRe\lm1'_?LcEZ9C
9O15ZlE;Cim&,]A7p13rR.Q>QkC*P]ATcST'7"/Hqr#Ep*$^'^N"n:6dljb]AQM'[gedk7a)2]AG
9Oqc)^E/[#8`%;^tJ8367XnR%?%!rh'4jVBm-DNGuGkUKPP?^+Q<h]AG20gL$sFspf>)JCB^d
OT[>q#lp0^Iu<HWJKYeJ&-pth>pg"jU8O_B9l#<H2"=oLI[Bh3JS7=^$aa3?b_%IR]AYJ';rJ
F#H136ul8=BJbcuq@'O]Am<8W4,l*4s?)q@'d`TkMhR_%/2rAAF<eC,#D@O#jh)6T"-t$oIqA
7Ru>4dG&WOYYU-lhX.OFl@am(_gfErBMhG/je&=W,%-X#G!4UVbXmR_Ee*7ZWEEA,aSGR;Jj
8od5iWGWUP^f^IhRd2cu6h[LZ/(T:p]Au9Ajdc3rc5oE0d&:a6,!b:G)IPmB^#-?elHM6F8^#
7T1IQCV,ZPp06mO9dmX2?B./;(7<+J06aakN:I!/`ah8tq).raseeGDtb;4Rh5]A?FdJbuQ(Z
Tj!-d7^R`(]ASnlPP,Fi\mGFpFC2[m!t8H$Ucet`C*di+SS(AG<jXtsI=VXdd"S=p6A%^j3n#
Zk4iMP.a#Q^@2ah>UV5C,=/Q0U-BGL#s-$Rqs&A$?'=LL!#G,us%h6qbsYX?Cmcaa9B]AU$<V
HgS!fGK=%d&SP'Y-T?3p1#0$"M88j1E-Z;=G_E$)2e7X5lSe#*;`mN6eZlTmnIF_=)IbUI9e
bWVW*JXi1C='#2uOA,+T2E9/)!nEG*9"Pp8k@h`7n:Q*G\<MBLjmF[Y^]A8()h*p/G"XA-cp8
0`#96e/B/?hc=WsL4kuZJIk@so?\N9<NGgh4.NK;uDgS=j4sh"bD/^#N5N^L_E8Pe&@#47KS
Sc!.Fe<1+6P`MA,^qENN<"lV$rb<_$Ti=6D+f`HOCLt.RZa6pnaiB1\GbmBPf5a7m(jtm<cM
e$`grPp4::#rF19,We!GM.L9"./pleJg;=d1<[pMO2fQ5R<,pki[_6I4;d?kUtHhS6<l)8;g
r$`kF;uckBL;TU$0?[lgEr]AKTPP35Q1C)JW-HK7R^Ot%IQIb4H5"5o%o7QDsm>n9%#NZkd=Y
\6!XH9.%FR__BnV'<EMAIL>*".'P[=$_nr6u?_b*K1B?OZi0PZ
"@K3J3IW[++cCI%^_cK`fNe(&s%);$arV`)D*]AGIEP*Dp/9O9:YZL1>0kS)loi5+%CBFcb<\
H2=T&a5>(7;+lD@G4oLbaDhNR]A<7Jd.E\(?WRtY$GC(<*;m3:DRAl14!cuT-Q.0..)2JW\hP
So-Bt]AK(]Ae#Jq7hZI";_#eL6AX">Ghd4^4Y"'Z'<Er%b/F35i7Qo4=QZXs1e@cR9O3#f>,GM
bKJ#Q^1MtU0/8eiYMr]AnV&su\R'a@D^QEr$Q'rB.U[5Bc\g4QBOWn?Mr4!66n]A7NKI<;Z,<)
%kP`'d6kCF<VALP-S,HhF)"4DL7d]AUSnaGOO2$=)O7g[W^F-R#&\XWcg4]An^k#mNKJ4#[KVM
KK^;aa'$EY^5\/4!Ifa@.`XgVMeIs5T,s&)0CuFHTK/krs0b^7\m0?1JnKdU%Z-9S`q$+N$j
Hf30fhN.=87*GjnHY-&YA;Wq7GAU@Z[SXX]A5Db::9CQD["kt&?.k_Y]A$ekPBa!>9M0kb)ON1
6,>&cXK(thL<,>W1n$0Ds<(7$$pRW6`q/s8a3`eK-m+f=0Rfj7/F(2o)S$e)O[p2drB?8%jd
IJilFe^NN%Ej)QTA7W]A=g5%cc5n=5K-\#ue[c\SCp6^4h&8oOA;<egG-;/,t)uH;)pOXQl&U
?L_0!Z@Bc>CPGcTe9@-*?mtXOss6aHX-u_,ac/d[M:sQYG=2YM<7$Xd^1gm`W5*7L+%c<-u7
clsej&@U.XWF0Bh[O!%*ls1D[6,-RlP=.?`LGnJRkoR>=;WGCqsn`,"+Z'A<0amo6$U3:SY+
)\LrXe4bPck3dd3kZ`sn]AC>iW>MUJ@VO,-Drbb/<EX%fae%Z)H%MQ7F_#u.ePHk,r58RQb[S
[]A4#LVhDd$,MnH#3XMg^;sW5PMS\]AMlBMELp=Ct[V`<EMAIL>'/13Ac9.gMCo(p.C%
=k@c#,4boURodfpKCh(4Df=6Wn\k:MIJik[s,tiG@3U<hF5rIN041;?%Oo&%,f^=aoaoDD;_
+io)`*s(6&G=H^9R)baQ>Q;F(4rl,S9E`,qk(&b.@S`39[KHDU[`^hV[H'jh*e0>WW9*,:D@
E@QAMd4MRaqNfXIS6lL*lc4)Do)enbj:4JYb?]AI7_WgMn%I>q"p2sdnuUA^"r/H[HfWS.8#M
0XS%@+bB(f>-C/,CI"mEVXh:D"'te\OM6mDqkF^RcA5pnfK^)P[59;6GsTd74^;+.EfaVC(0
>n^+fEBa`]AHp.,TAMHPr*\]A\K:R_ds<`9F]AO,;N6sr>F0PDF9nVt@hRIE*]AX)#E6Cl>hWABZ
A'=3F>Bar.4,T;4@ClkgNNHg7@[]AMfX3':A%fQ_XAMj7$p/:2?ZUTV-O[bYNg)%Z9"kGo-Lp
-8.&BZRo@XlhbO'5bPoighg]AO'3nYQ%IaVH-<5ZY_7<f%X]A!f@e(gQCdHS=nd(+W]AT(\!MoE
d`k4`&2YE4lq4>#P8jCN^$$7b^M.Y&/8),Tj,km4,%\4TRR7]A1BA7b5_(%9[6,q?iV*p4FQh
`q"2+)f"BU\!kqQe8.p:(kb*?<5;]Aj1eVu2jY<H@#OS2Pd"XD=&l113p%CjJFkYd/>e!')b"
\?lYC%u>lkfarolF=mH[RQ6VI[Q-6Jr8&<;u-9#t-PD#11l?R<U'giP7llU==_H"+!kosmUT
%J=`=ZEL8A$[Et!I/Tb;YAu;AoOl7#du?63)]A5HtZ7(h/g,tXs:(/MWXn0!urJGX=`"bn",?
2E)X?KG6qRcbng(`L(I#F\;^\r<6[Ha(GkP8n2R_`WlL>j,p_BKoeD7BgArMC;o59A2>SQP?
io!V@=aE_,ArXGV6INk8rk@%5XFTdLgJLp<o1N(*aLaG*$PPXo]A;3kQ[=-g\<$B8PcS:aW4#
"HZmp/E[j!n`9UbYGFR^W(MVj\IqXhNJRr()\(iDmO:^[+nk8AGXU^JH#C2cu"40B^C?q3nM
X,[_4LPr3DiP/.BAc9*:gXI;#=YimU!b0,Ybs%]Akk?k65\(?9D]AQYH7D&g`,!6R@0$-38JB)
Mmo*%I5'WI>nqr9<*H`*Q<X[0S2Ukfmmf"GiVWZ8kE+3\+0R)4mE.igAZ+NF3q!+n;B]AtBeb
:$s,/q@)'i?s*&]A4/'H0K]Afhk/P*CiI@f\Tp-F:DlMCSa2t1KhqFYl8a?UY3Ob@8*S*>5.fV
1&#KUip3<:s3A$t!DpRqLd)3qg?9mPA3f[ZNVWYXkM_Vu+\>3)fSug'E>BP\Z,)_A;RWX1+m
`Du_Zr;ten'1mZB7IT+cT%'M^S>h_c-ab%"L;Z%SidoH>*X/R0rPR'#iH@5aTrs5an)@?4;C
dYG#PRX&0'lG,CLU>$4LaO=d;-dkVguiKJF_qc/EBP`QP9$0s)^r,q!EMB=<j8S,d(@pV+!1
^%eh\ePq0d:@ebSc-#riB`CC!nmn#WQaKhAiV^Lab_@-F>=4Cpm3euE#)5.q+.@Kj?Jf]A3GQ
\PjaITfM@Jl>VoMunh"re&`J6\E!O,$a-+!!qMF17W8nNF@<CDjEr*r<n#]A:%0nj!(O2/Z:L
42s%HLQjl^R+8#>:*gFLg>8afOU)t^CaL4FQNLuRE)]A?0=i:>SKCu214Cj+"C8]A()J%3CSSO
g5j@)B@tHp4b9:`7rVUZ+8j!pp>!n>nQ=mGVo?$NVND>Zc+-Q1aN[4]AD.,c=%_2h8/[8?dp4
mK!g8h&YO-f9iFoL89U,?Tk*"'#o%krl[/?%b_R*gic/GMDiYKr?_FLC^dM]A&Y&3XZ.V,%gD
Re?Ft/O\ZKo$\E&H1eC;C$Km9#ceKJBMWB4[GSqBO_T\jV75ML>n$GnMG>,Q]AiBX6b+HaR7L
6K8T@lB-Y?;b4OU<gsh77?Cb%rjkcdb6'-Za7/K0K5!"_;&<eORBM*6>p-'E]AjubZA5Z@=4c
m`6HL]AU9dqfkkO;m;J!aVAEb/\$p\aUklnDA'c]AthkXH3!UH-)PDIbLR0,sEY4o%U0Y6,mp.
f4qrB;ERWWU[:J(7pCj`mXfD:O*25Rr+'A7^Y.f<#jl![(EE1^=AKO5-I`PL>;sja9.NGmli
]A11)6:[<4BMXVUO`N+9@XrSd+5##:j?$aC+3R*)L3"A/*cYj+GQI/jV;Sf!;lSRe@"1j*j-_
d!T(:+u;2#CbgXq]AB4n-^#$]A&(qS,Q5@!cko:>Hi0&!/IV<N:TS(bYd7fWiKeQ/7lE4P\N@U
K(t_+ACH)5N4@`3AmO7CUU#mZWZ4$eNTgiL;J[&*"t8,C.;41Um1>*ML;+ZhF%g<1Im"a.)/
>/ob%KpAM99<ut-0U_1Wh2-%moS@O9bFP9fAF0gr8@9\hu==5%qU&ZFu30:,'S4<mZ`W-L@-
m''`ajB3Fm^]A1;bBPOX72TC/\lcXq)5g<-gF2R>mNQ?Bc"fE=Q%cGR*fcGBI`%e,`HNfpZrK
!oBQr;%)\D,E>NPH^[eYh[`T4""4i*H;5JI%4lA;lEBI&K3+l;IJQi+mGBBkHH;",?HG?7Y5
3cEO?91s8BU?L7<QmQ0GGiUQ/Q$h5.o&MeW34cE]Ak-Dc*U9H:rT8uWfbc`UTb7Ejc<`Q^Md)
jhp[Vg.ZAM(Lt`<12cH=s5:mAoWnO,K@.0s->!X0oJP_m%8uQEA+I>Z:MF`h:7>]A3[<pnmh,
5(8[_i.W:sN0\iM]A"NH356\SJ*CiuleEnsMMNp"*Tj7WN;WXK<^B3me/+N=CLE7DMBlfA+b(
lbO'OX<?Zf;2)&Xm@n%6:L/+0K*Cbe#]A8fVP;4"-%?)/=[a&-Ct!<s3d,tL,'P)^h4==m>$u
>fbd"0gho?/1L@XY:Ie)SiU,$oN?8Q98FmR0[o<F^!;nkX[qDL:=l!eE2f9jifd1&ir0SZ/'
qF$Cf^CK[feHr,I[+DVJdbe<Em(fDu=1,56?7F1K>j*/Ne#AkJ1:AK:St<0m\sDjL3BOsEqt
#<Pe:NkerslaBDms5&ZRFIW\)^WVhHK9Z#g(Om0:Lm)C1-+@Ai`9sd<"sN6b.X*;#U/5)YbS
qGuN(>Ru/WZh2<+./^<F(J;RrBM!`CI%_fiOZoPLJ:mo]A]A+1an#IT.G!';\V[m^KS'Vl5OZ5
9V\DKMu=!OSCNSR?"s+ekbq_gBH;4HJ,#TNR!a)I^HdHDBMVu52^'mfKSJrgjHG2M@T*s68i
]AVSNjI$\EHO?=ShfP[k`-1rQ7!D:f9F?'NJdgA_Fd2)qQP<j@RN8X!`,JVUDTK]A:pq4%K?:]A
[OY^D\nr2\55jK[E.>dUCWq0ufM]AFH/GA]A#Uo4S!J_\N2X]AtB[H/2tZrlhgVLAA(2pIqd3C[
HGBi_4SY[f\p<M3Ynd>>VeT'r12l':PQl=KCo^+#[b]AbhLjnjgZ)>$R]At\`V#V9(S<CEh&_Y
VhGTPaS6B:dS4"qS-p%sP;Xp2&-egaL^QGU8Q.di\o*NI<h6*?]A.,"-+^&*,;d[.+0gF;L?3
Q_@sR8Wt%=o/;RC2KoGQ^g]A9+E@:i]AOh/Wji_(@j+?U1l1UmJr:[pI;s1o9ik%Dl^rV(GZDi
-4W`W72qX^]A\hd>O_30d\N3sI8.?6%N&Egf')V4%\ZdZoij-s5=7$d<a7=\pL/h@@VNd9^Rc
D0@.;gY@IQM8)+u3p8c^V`rbq>'J(,9ZPI#GD\:"M15<nF\?gbrc%=b7N>WN\T5t*X-&9c;.
gq:p\bZ,OH(il6_^kk7S]AqX.VfQdBf=@L2)akk?d,Wj*7lnhR\`%%5KlR?^sHl+g+(p8%'l(
KnH,OQ>o&/&5L.eDpi!?'K#FXu2W4N/^:2ao'Ub-lXlk+:N-*b\"Y4J]A5sG+`_0^M)/+;^bT
SnH;>o2]ACot'^OBKW7@[^eLYX.7/a<MQG\0q`1/cS$fFF=^OolnkuD7&l;b[$Z.3m^E/ARq?
GJZQJiV*Y6rP.i/Ba`m7r7-<U;7D?M)&glf[3Cq"/F?0Y<O\p'O?ao_dHD'(`f<GHkt1M#Jp
(`P6JKK!VOH/*%/apLX/R)>%X00$+6.W"#$W2n6;<hbSEp<[?Hf)(Z>`#ZolPLJI`q`5]ASco
4fGn)!@"$WYiIkI0hWoO$mh5]Au1gNmn<X'5kf1>3d<UI)ITY-+SrM/Ni1i!qMRMe&K]A4IJpC
UF!7q?=h4n^UD'7m?66BBL[dk"82&Mc2keOSDlbU%W[JFJfW[Oi*-H'dF.@,[9(P'<aienjH
=ANY-=XrD)a$?3N75mhj/pmj`tHuf2LR*#F%j5SR`7T`W9'OT)hUHP_^oZX$V\G5S;S,_.AW
,=H'e?4obp_3^tN0@TBkaE@=iE;4gq(<6rgg,maYWo`k;aG7?:6DhpM*a]A1I`Jo#`V71o;qu
)='`MMW`!2dI*u$qkR]AK`$=&0(>K\KCWlS3ng)bX*C[SgfJl7"fW%Tt=dDS'o^!`N]A7$=br:
LEAI)Dap:;&?n@r0*Ub(Jl0oI>-r\tS_Pp^cNmjOe<;fdcTPAZ,meNVB'Oif1Xg\$Eo$8Y.*
.U(#qM"6Ss=]AhXZW.lQ/K,/f;o8)C+u@mnseg3RhFQiuoID8dp%"RYkUL,/p^j:A60Vc7m+<
`Mo-RuY#S<cr70-n5(Lo54J$B9R;0Fa^$OqBs@8r;B%J'#S&h^D)T!BGOsS).bH*Na1OS\k8
%W'IDJC$&nej`[B.-)rjrj6UKoU\R489$%1m&8#b$\O:6Sp8PsgLem;q-m1$M:`oPK+/?E/O
A0%s8JmLLCetn"8`eDXFfWAMp]AHEh8'3d2VTdWL.m1pOTNAPucF9FmjSrde(.qb`bD;;Ej^u
Qb5-@+!0In+UKOB.n0^;aJqjN.lNL?CZH0BZuZ"Sg.d.<mFk[t4S'eN7+)B:&o(,M)=\4(kA
bYNrr*8?h%-X,Js)H<R$t^:'dtFkImHn$Xq=c1,plI+_uKao"l\]AII*aA]AmAZ#4rQ)\PCB%H
`FLN.@hP)5AM?04-fZpFEH.USbs@%K2J-rdTo:@<BW1*[g?d<[!4/75XeioM`rXW/?E5*q_2
?o&*n6H-DJ*N&2Q2)PC(qt]A-^;-m(#anC(%4E55cJ)\5M9I7.pSehjm&-DI'aqJ<Yj4<MUj6
U^.6^1he-[THGX7FD2%5Zq(enMFa'TUa*Phkt!Q$C8;$/MGsVU!]AQ_2pN]A"ehoqY%s6e1Fmp
p))oBa/^[J!cS457>RLh$8"T6aRF#9kO>M#=o^A.0,6$uO);De30VY>Et9ndXZK;Q@tLJQqd
e.44B6@&-i^aiKQs)Z:M8"YGe$pIphX4naNnL;:]AJll*A6UJ]Aen:ZUTletDlE'<K=(TAuC=)
Q!Qop&mjK[HN(Dke*)3qWp44L]A2S/>ngU^F7UboMTDCK8N4&s;Ude4Lj`P]AkB(3AHpl]A)q(&
Kd_.h`.pDBPdGdW6+"?i9^aN?\JU,pFXrI&=(*-KUuh.?op0qor,D)-AM[F(OB;:=WCZ&s>]A
"'%+#iDJ`Qjfo1YA2C^\E/PRC79Q]ACJ9_Kf*WdWpGEVm*Nn*9c<f8MX<#<NT+dKrs6M^>Afm
PCL;80<GNtB7G$X#O%8+S(FqB8XBbE'?HM@lQQrc)GHK&3_t^^+k:K]A$*%2`gr;b74!'>_XI
0e4UC7LFI8\U5iu?[Y4J</%lpXhmQK'D5)%#O\Q^H2WXS-^kkR-+6s>"GjI($'S9[gY!+:"e
4Y`/g0kc]A)Tapr]AE!I.RP6o_m,(Q-etF;M4cdDqW\=Znhel\]AgC43sG702fD[*8!IS.MAGj=
MP8\46*6*ia_JXN@m.s)ZjF?7W2O88H[p<lTgfYkPqG"p`\ouGs?Z]AoI%0k04@Bs[$=YGI6f
n:f\@Q=DsHiOJdIBPq]A?-7#0Q06rd.[$m';*#OLu8Hr&J:"2"AI,\rK`7u8VQO2!"Jt!ch;F
,2"$'bin]A=N^I?C5I#cpa]AE6-mb7$\ftlNIsU-lsC2Aj?,3m9X-`h?B[D6f$i^,*&(S:P==<
Zlk8i1[Q*iFAu#Q7W[Nc=gb?L?P/p;WVHZ-sEtQI<'X+qHSe*!2&\R6$m.7-k6o.CW1:.\&&
-TkgXBKfR6W,QN+L?Sj/">'!BRuqsh-J<FM+h%p^mdk^pM*2")stl>20QL3DISCs%1`@68&U
-NRJ7eRLN'=`N^QouX%a@=9J$I.67LC5F<;FtZJ='3Mol<N^E&%/+`Ah^L76`OGGaClpfjZ.
*6Z=XPfL$:QFpbUDNV7\bJTf@>"R4Mm^_Zk@1f2)ZACE1;d%O;jXN-&$@;5g=\gDccJL9G(p
tb!O":atX4GkQL@@7fMT>jYDkJs"`tor4ZRd/6:XmP!TOtc(SL*p9;G_^%A_pjcn^<\10d4X
o1!_(d`_Q/6QF<\$CW_Z'YL(Wt]A-nmPlTe3@1rueH+jW?On,Xhq*OUf5Z=6fbF(eW2lRi6pS
=A-tP`.f@r[da0365rhUVslD0CZYSkpMssoBQLd?R1aP:[5.Ji:N`m*"7MT/pM-O>%BkP]A)+
`*i(_Cmk.b>8foBb9YqHp+6\!Ju;O0Cs+\Ii]A?,J#7oo<hdD^NdUfKDns1BN[E;_-nNH*O#j
mtDe,qIg2J6D_[feo<_6@%EPX!*]A.QN]AP/QooWlP,W@uTHo?"KBNVldP<mJYiG3lqii#.1nC
snpaBN*9GOEFk5%Rtt3c95m8NScU-=<*cFOULRlP-l-(3ND5>7DH?9255"G[Sm.(IB6BK(>t
C]A-*=sDj11oU6MeOg?O=]Ar?4o._cJj^#kQ<Q.2!2C_O%*V'fhVSL;U"qf/p0<.SnI:KhNr'D
/PMO2't^=Q*i72+V2/1p<<5S9G^,P&RWQoLtl#CY8=@o#[rpZLCja*FpQe#Jkl-M+^Oge4:/
#("18Jl_WZO"^-MALR2MWf$mguSboGPP#.1%p7lRhHnS1jj(_4?[,C&]A(W-MJjH._KVXI91/
HJ.]A1V%+CGG+;mB--+m-^+=0(HHRqdkL`l^-Q$p4NGfI7.lTrc(aSZ[;ctA!`VPLchPa:.-N
uL!Z#me22Ei>IrOr<6"<';2PW-uD4>.l,-nZ=Li$@EFZbOP0I0c=kMGt@%)\ZP2n0FtQ.]Ag:
Gda4!88bLA4d^02iV0*ctB`Tn4b[(L>`o<]AVCWTL%:T/J7QnmKrFSB\XV3dCVl]ARro=qB'h3
[Rj-S5S1f3"gBY23MHY+6YF`-'f/sX"R.*hncMH)*r+be/H\Z>Dr'AKT@`@:hs$e=2^*T@T6
lcZ/XeFqoKNs)m\P6^VY#dX3Nd7GRH'3F>lt<:bU>Q@V)H,pCXXa[B/Oi]APADLAgj:G*"FaE
3sI_c"1@fRU6c'+#.WE1']A3..Rj>Z$`&XQS`rroO:6S\[.#T[j"1+a^TbpC<_?2%+5m:db89
!RsP4/0Vq4/WbKOT@;WDMW$d`*b<QNIk-Jn/%-:Q3r.Id0(g/`5&n,?-SX$MTtQl$K4rSVkh
>RYp$iY]A`0!"e.s#Jfud79]A@7+r?OM\0Cckuh^mZdpms6#EKKicDl^X^W#e^-_QiM4*;/6A1
;JIY=h/QR<cn0JVJj3Jp&gN^F^ZNtbaelHQtOHd3D/&heOJu*]A1qeqPg'$dN3g[[#Zp3V*<t
QkQ-Yltp8\[$p,I4U/d##>&T*.fD5<_@3OMf$,#5\uY<K^\^;^l^3o??dfAeA%(r)MUIGV1_
ZB!K]A`D:-Ig]A:1PZ)thA@An`1fDg.LXScdh,tp,oGlQaY7n65nPI^Kbipe;F&i*/Q8S5Q"1r
R.g[nC<eg\9PoTPSSDmo:DmOZ1Bl#2CK#ab&JE+SphbM%ARq9$KbecT,?CTCV_Qf*"qorOa_
V7EQhcMm8*Ghr]A'CXBm'!iOKmj00\6]AWKpOZEPiRBNKaiqf?C#fU76Icd[=b[7<+=$jgs.9,
36"(C6Ep?qW?gR]Ao$gGnC;tn<.BFBTXUkPC=T*pUKXQsI`Z*PC,"'G+m@2dMqeHAR(j)FIFf
b)0o"P'FBJsq--)J_B?/tXUmhDZOGCW6TFNs;r,`[5R7?tXk/fZd#.kRBI`9<[qP8I[RD&IJ
/,So7a)YCF.+e&;0R9cH/T^bg7;PI?i<r_A`WgS7HSP!B4_V'R"htg2<`+(Eig*6l=HSJ\J:
^Y2B7@R=Q@%RsJ;C+(p,fc]A-c^cA<J8o2Mq2[$&rVhL%W.-Fm0mY-53o\X-;!i_f="OnW:3l
0CUG':$)-c5aVhQt[gr2@)%Gi+P8PQu$e)Y3!KB:]ADO:86r$g4#4h2O/idMoVd>2gC!<lRkH
N!>JdpT6uZ:^*+'P5CtC+[DL[Fb8i<?*(%GhT7$O$Co(dEt.f_UF?Km8eH@B="?Hn_d-[JrT
<^'UD)_MtP,RipA;6%VkdJQ\>KDVRe:kd9f'TS.CYSk&cZ:U@cUCUkJ.02=PH4aH`,O=C9e'
R]AK-3I[+Y'\\/lX\'u9OZD=Dpgg.DKLAkR;67,Y2"4r>^$cm1h/<XRQ6JBq7)V^)Um'L#2jp
Q*F'pliZnbIKHLG?`3WN4B-#''^=c=nu?KF)eQCQkqle/bEE(fLaeG[?\Jj3E19Vh*k\:bTA
PM=Gk6-H5jciHVca5ia^b8_Q%'e:Dl0?Fi\9IMMcGe@JW1#"Qk&FCd]A%)18S*M?A+B^>ZFJ;
n30pO1"[&;Ti`S*M8FfSN^YV.W<W^=bXHIn`L%hnL1bJ8%qc%?q'e3R\?<EMAIL>>07e
Wj@:%^!#T'!$:%TP84cobUX/B(!M*A57o6`$"=;JNF&D1f^`c[R$m#EG]AI#`HLK0q$&o1SB/
>gG#GF1>e%>sLhg>^6Ug@cNW.b!+Dk6AB5Ck,F"$Y0u"rX/pE=I(e$OmB-#ALqe\RhnCW^!@
d]AG+K+ITQ',i$(N3);5?@DJXij=Y>oOP7%NZOS8:FjZKIfE:D:`]A2HF.81Qr&r&Xsga@(n))
rN@f5phR`E8m`pdl9;]A1f%S/(%Obn8XHirRdc\isS8i=l58Bsp"FVV6e.[JEl36uV&QYaaFB
1HDb%JOLo>I>cp$U1/O1Pqnnk1HFLF_lG:/%UidCR2A8o`l6COqeo"Gc.e>an^,i]Ao3J.0T@
&<nW&VoqcFPnm^uM)+et[&LuQUJ6nf^rfM</p:gT(\!1;l&=(`8r^.D<m3\([mbiLOmh.TiY
>=6:GYYDFD`q>TR-ekO0+Tg#KMTAV:#\7pn2_sUt$D-f2JDaHMma-T)?:4?*:n+sCc,J5hJ[
d@6%FWI5kVI)\*8P)=YO*(eo^*Z3LJ.(o#t5'd2p'p;)&$cap<d4@UZ4MZ/VZ^er1P",0/`R
G:fdntkpR,=kjWc91C0HORTZMSJh!+\."&6Y>jbih5%,?q7rG?SJd[I)CS\CM+<lcpR'5q_"
(CMjMDs34)CB4QL>Nd*_WQ3:9AfL(G2J^AjOHIk>,W'LR+H"jE%$O"P!"3-s!`<mQ-8`=K"!
/C9*snu0?R@KNf!5M[IP0]A]A9gs"d9P^hR:=9(MNEn%"$fOeXL"U4<OKHOc1@W5cgdC;?LCk,
V6s.HD;d#`(LjVcC.Vm]A8EW4;J8r6VW&5O\312ue&/1nkXFZRb9BJMAG?tdii0Gjb\V+pcHK
=$N'N8mbb6$`3EkTWWDAS/A5n/TV+!7+-gQgV@0YLZ[_MZ.O^=)UD\pBAZC;uh+db$?_%Gt)
Ka>(k'V/o>p?LL"<61So>0b#QEVtToRWE&dlm;SC`kRj'JY1,o3F)W7<^Ei>e>e;4Cb"T6kD
t6&\njbLqiPG)NoHCrJ78/&q&9QJ3`Wj@8"pZ/DN6-YiL=Q_#:Pp1[c57%=N/J"sOa9&j6$R
'J;1qJR&atYlrEQnh5HVk5"<R!iD'*=:DeBmNE/BS\[cpS$.oF'I"=I_/gGIlr@<?jESBN3c
\?bsQ<0&E812!>9/KiEkM/'`jBss?.p\chtdds&<T?=Th,;hkBqV*Sr_%=qQad:1$pi9DRZ.
1HOiG*&f$EPCkFG4kS1q08uNU?L7[nJW4p>3>FSnqX3TSj-s]AZe>g@anXeksr6-08eD1(MOa
Q0n)t"D%\$-PU<<L_BV=XS!r&G=@*\"5$^0:WhNIi2,Q0a.a#:b!s"aMfn>D00["'(1;JDl@
DMmbs/u[Z]A(F#6f6p$)Ap;!cPn_a/$Y56Em\o('&c]A6]A:i6'k565E$O.i=/'[H`,%4-0ept$
E(<`Z(bm-Gr.!!)tA%H$o(I`-mpXoOmi)ZD!TQ`'$a2sUIn:B"2?D,5)9\-\=TDdf[-T'j=R
X,78Lef=oGH9C-S*l4Cph,9tTlbkHa8:50u[I("PD6=d=J67LHq9#h;:W_(CFO.A_5<UeQ)!
+Lob/G`Nj\sr.;O#JPm+(ta&c>3ECk;F'6`?YJTaW>$K?8=AK=LI1=W]A9U>M0_3/?9LQO2f,
]AnqWd'[[P&LSl.T2EV79,0YoDQTs^%-aRqRZ)#qrcWd?<e=n2;*g5L)KW<K"0ai>Tk7IM'<G
^B)m":3-qQn1D7h32e5gBd#++E+lpkk$`8IQpN-$@sJiVMUu?m$]A<tZ]AaD%a48J_N8N.5f[1
#Ns#X5O_]A4&IYVR&rW[tnjs49YhcOC*W>O-749$:5?J2Ns/QEH'd!uZBH$")7pXnr_HK8Tk?
6$<V,K/N/oI_Od^G.(NV#%:@RNo7h]A33(0C>m?Esi#26q'X"9F/@:\I(JGEd!ORjr1VRc-JL
:-177fS66[8gfMZWZ/^8P>?@!M<%imuerN0I,rNqNuDr?%KNZ:,HId\bR@g]AM,:5[U%aY<V%
YgBNou4i#i.Z&U6%V%O!e=]Ak"Z^$]AjuaE2X%9HK_;3iEB#YU\@;L_1oJ.pp8C$Tq8QKKk[GT
AtDC/L'$=`I'?mO<1OOY^aTiVsJmIoX37P]A9"f;;L]AOdQrLP*G,n1el(:r[rC=9Qb1fUQ<.Y
2PlslfZro?C%4&]AdZ\ZO>1PqA&7Z;(8"D"R_;GHRf43Y,,HmLGRtUi1%a(lb2a4PV"c<q;a#
d'=lbiT-cVbP3tlJYRLfK:S9a_*5hmr\nT&n`^OhXS66ee]ANRX&fqqMjC4q-Z@u9Hc/]AU@\!
a&4NuAm!G#8\-1T=diea`9SU8#uQ**R=Ro13nZ"t[a,8Q0#:3+"(Uc#b-X]A--^h#PZ3c#]AqI
)R'2T-gnJaJEjqULQIP-DX+nfGgd\E7FQS:u&9WX:X<0T@c@%>$YE%\Vqu]Ai"qPJ[E,@ZC\!
%4L?I>Uub=dk+3]AqW^KrgJgQc<5#t`Th=#^'*1ud&.HqK!uI@e#`J9nQIt/E7C*6bcn#sO8F
qubj%21\d#goJkdheOc$\J#3Na'W.eS1H\8(8;N-E-9L-HZ1ASbhk)/(!WObO(&=5]A$ii:\3
D#BhDF"$Eo%7PVC_/IC_%*2^H3IFD\NXdC=@Go7.]AuYTN<8YM+,N#Rm7He@85V%(UR!'/f:>
d=90]A(ms1#<`Jd\QXH(+r^CBHI7i)_CkILKId@aa-9:h2q4FeLq4&mE_o0a#JPY$HiDDg&c!
oLDr<!IN'<)h&+^Om#2C#q:P^tpa&W)_g@YgA^1L_q5dkm?1"W\$$*&=\\,npK\J8fb`pb.2
Y,^LV(7rd04is?XIi!MW*/@nU@6qh*_eGBgmg0@bGM"@VP_K`-7"#b/$*'Bk9'a:5nh6N\j?
*Y-#>A7/X42i^3>8WFfotcdq1^h>,AePCGDV>ho)e-G!4%LrRJBhJ[R4/TXu6d[PK'El_;>3
pXj'Q!&Y&>$B0>I0E4><UgfZOqka@-qDkt]AS62CET&TT?^G'H._Je&j`?/I4/'*"4_A+Q:+B
-$c=t=jugVc=UMb\+#mMbfMe.;W\'\HdcjQp]ARYSnA86eImG%DGJ4Y2*eRG*T.pk*%W1/N%Y
`j0R706oh,f&V;%Y$eA['mK-nJQ5eC!9(:o4H,M3-=kOcG*X:2G$<CGTc$Q@Gb;5,_pEIh@d
WtLBBpRB5dF2/XY7bR_S!-Z88`-YERABFBg^.HYQ8SIDZW,U(VEo-P]A(0k&*cj!"'5i6,=nV
A/(LHRtS>5&d-SfFKPs8IR3V6@,C;'4Br<1G%bHc%>=ds)OVfhZBU3-ZKHB`N'@nTCUf!$o2
rj[)l:HsGi>m):6/!9k%.Dmm(;5!iAEl_diU>)7)`,T=j\#$Ff70,gIhl>#re"-Hc4#Xr4IF
M?3=5gV>Zb*AQ\IkW_7#JN0bJkhK,.PM<ZH8B+J)(?j8b^*0jOomghsSb?.15E/gN_'*p+F]A
jKrA;m65eqU=$tlU0>FRphG`BgA8c'!;:ldB[s*,*g.$S**XJ0*;83-j"29IGl#T'bQu%YKk
"lCeXXeHd'>IU<:*qSf@)f5toK"S3]A(CC6B*KrESEsb#Z[It;f&l/o-ejca%nB^n(TZ_O05a
`hI4@';02(NYkN439\`$tjnTn)6$!J.%HbCB%F?"@LomFkei21gJIuT@6M<%-oT,F(#Y=h"9
k'`Pp7552KqKfshINbH0hUno7oKL_E'c\>Bi6N#+IGb*mZ0ui%Abo-6r(Z\DNoTRop:\4K"T
Gn134\;<%clfMpe8JQp*9UG):=;c^(9Q>^Z0!?s6G.0%T"V\ejr<hcc(D&a8&MuZ+^*3$\+o
>bp"'+Z"Q*4IJsA\'^$QO;`dM@hn_O$/0h[0$dSoN/'NeG:Ui49aU\$Z'ZN@GoM`CEWUAXdT
G*E+Ck$K"aIYN\kq`/-Y]A!8<J'"SfG(d1LN_lHhZ9.nA,l%c-'kZBBqlj!WSEe%Ag7\1Er2I
Es1gSl=!HSrU%Q^[4e0jJ":Dr_ekVNN-AQJL*G%Q[*36_bic+Bl5m%AN^nDBB;o3Muqa*PCd
rU`BF<)8[mSIeWhhf<tC)BE<Y!.Y.6e$2S<pOhLuT)CV"OIc0$]A^c/B%Pogu1UFBppsh+&S(
R@RBE<WY"os2D`9(@?6/T.mZn\hlZbb>rKtEo<@UJo_;2o^;(X^POq4$]A5jq"VMf\CVqfE>s
g^FVBh_NM7ZVu)7!_c!)AAmPQ<Tj'O&qU>DccdRC="2c5KgJ:EQHfn6gQ;3MA!4[9*P"Y8N6
,1#+pVP@Li7U%Kc1;m.IhISXZ1DO9M<N6T5*5>fdmS.r-`?E.ruKEqRNp<A2C/Z<6iY"0T'O
.9lEA!Ys"Akob_XG35?M=UAlcpd\-dDl(s!bY2$<_PS_0OVp]ASqNAo*%3FS<-YV^[4F5MqR]A
_#XgNT9BtnNF++;In+KWdDm>f(";<hl#::Gr.Dl;)1-QIYq#`J`33(25Ib-[7lJKRHV[<2"9
3:'R.o$/\qq/cg_'T04TWn+:C6m4OhQtK#M@/H*+o2.o_er//\BY&ZdGA1Idn]AQYP4**L]A:E
hEXDW2,fK,fM%pKhIfK~
]]></Data>
</DocData>
<PrintSettings class="com.fr.base.print.PrintSettingsAttrMark">
<PrintSettings printType="0" valid="true">
<NoClientPrintAttr>
<NoClientPrintAttr setMarginOnPrint="false" ieQuietPrint="false" needlePrinterOptimize="false" inheritPageMarginSetting="true">
<PrintMargin>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PrintMargin>
</NoClientPrintAttr>
</NoClientPrintAttr>
<NativePrintAttr>
<NativePrintAttr showDialog="false" needSelectSheet="false" printerName="无" copy="1" pageType="0" area="" inheritPagePaperSetting="true" inheritPageLayoutSetting="true" orientation="0" inheritPageMarginSetting="true" fitPaperSize="true" scalePercent="100">
<PaperSize width="30240000" height="42768000"/>
<PrintMargin>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PrintMargin>
</NativePrintAttr>
</NativePrintAttr>
</PrintSettings>
</PrintSettings>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds3" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="6be8374f-75a3-4f47-ac86-002c9122c1b8"/>
</TemplateIdAttMark>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1685067684672"/>
</TemplateCloudInfoAttrMark>
</WorkBook>
