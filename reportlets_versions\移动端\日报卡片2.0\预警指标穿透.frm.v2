<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="body_fzjglb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="zbid"/>
<O>
<![CDATA[xzyxh_jjzkh_20230820174427]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-21]]></O>
</Parameter>
<Parameter>
<Attributes name="px"/>
<O>
<![CDATA[ASC]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[3]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**
	SELECT * FROM ADS_HFBI_ZQFXS_WARN　where　oc_date='20231113'
	where tree_level=1 
	
	select * from  ads.ads_hfbi_zqfxs_warn where ds='2023-11-13'
	SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX
	
**/
WITH RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	    SELECT
		     ZBID,BRANCH_NO,DRZ
	    FROM ADS_HFBI_ZQFXS_JGZBMX
	    WHERE TREE_LEVEL='2' AND OC_DATE = (SELECT JYR FROM RQ)
	    UNION ALL
	    SELECT 
	    ZBID,BRANCH_NO,ZBZ DRZ
	    FROM ADS_HFBI_ZQFXS_DDFX_YYBKHMX
	    WHERE OC_DATE=(SELECT JYR FROM RQ)  
) 
	  SELECT
	  A.ZBID,BRANCH_NAME,A.WARN_REASON REASON,A.WARN_STATUS STATUS,A.VOLATILITY,DATA.DRZ
	  FROM ADS_HFBI_ZQFXS_WARN A
	  INNER JOIN RQ ON RQ.JYR=A.OC_DATE
	  LEFT JOIN DATA ON DATA.ZBID=A.ZBID AND DATA.BRANCH_NO=A.BRANCH_NO
	  WHERE A.BRANCH_NO NOT IN ('9999')  
	  ${IF(level=="1","AND A.TREE_LEVEL=2","AND A.TREE_LEVEL='"+level+"'")}
	  AND A.ZBID='${zbid}'
	  ORDER BY A.VOLATILITY ${px}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="body_kqlb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-20]]></O>
</Parameter>
<Parameter>
<Attributes name="px"/>
<O>
<![CDATA[desc]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	    SELECT
		     ZBID,BRANCH_NO,DRZ
	    FROM ADS_HFBI_ZQFXS_JGZBMX
	    WHERE TREE_LEVEL='${level}' AND OC_DATE = (SELECT JYR FROM RQ)
	    AND ZBID IN ('Agscfe_20230820010715')  --'gjscfe_20240520155411'
) 
	  SELECT
	  A.ZBID,A.ZBMC,BRANCH_NAME,A.WARN_REASON REASON,A.WARN_STATUS STATUS,A.VOLATILITY,DATA.DRZ
	  FROM ADS_HFBI_ZQFXS_WARN A
	  INNER JOIN RQ ON RQ.JYR=A.OC_DATE
	  INNER JOIN DATA ON DATA.ZBID=A.ZBID AND DATA.BRANCH_NO=A.BRANCH_NO
	  WHERE A.BRANCH_NO IN ('${pany}') AND A.TREE_LEVEL='${level}'   
	  ORDER BY A.VOLATILITY ${px}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="股基市场份额-营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with jyr as (
select jyr,srjyr   -----上一个交易日，上两个交易日
from 
     (select max(oc_date) jyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df)
     ) a
left join 
     (select min(oc_date) srjyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df) ) b
on 1=1
),
tmp3 as (    -----t-1当日股基市场份额
select fgs_1,fgsmc_1,branch_no,branch_name,sum(nvl(zbz,0)) drgjscfe_yyb
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日经济市场份额-剔除北向机构' 
and oc_date=(select jyr from jyr)
group by fgs_1,fgsmc_1,branch_no,branch_name
),
tmp4 as (    -----t-2当日股基市场份额
select fgs_1,fgsmc_1,branch_no,branch_name,sum(nvl(zbz,0))  srgjscfe_yyb
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日经济市场份额-剔除北向机构' 
and oc_date=(select srjyr from jyr)
group by fgs_1,fgsmc_1,branch_no,branch_name
),
tmp_bdl as (
select a.fgs_1,a.fgsmc_1,a.branch_no,a.branch_name,drgjscfe_yyb,srgjscfe_yyb,
case when drgjscfe_yyb>srgjscfe_yyb then '上升'
     when drgjscfe_yyb<srgjscfe_yyb then '下降' else '' end flag,
case when srgjscfe_yyb=0 then 0 else abs(drgjscfe_yyb-srgjscfe_yyb)/srgjscfe_yyb end bdl,
case when srgjscfe_yyb=0 then 0 else (drgjscfe_yyb-srgjscfe_yyb)/srgjscfe_yyb end bdl_0
from tmp3 a 
left join tmp4 b
on a.branch_no=b.branch_no
where drgjscfe_yyb <>0
)
select fgs_1,fgsmc_1,branch_no,branch_name,flag,drgjscfe_yyb,srgjscfe_yyb,bdl,bdl_0,
case when bdl>0.02 then '预警'
else '正常' end as warn_status,
case when bdl  between 0.02 and 0.1  
then concat('较前一日',flag,concat(cast(round(bdl*100,2) as string),'%'),'，关注具体分支机构数据')
when bdl > 0.1 
then concat('较前一日',flag,concat(cast(round(bdl*100,2)as string),'%'),'，关注业务变动或数据准确性')
else ''	end as warn_reason	
from tmp_bdl]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="股基市场份额-分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="px"/>
<O>
<![CDATA[desc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/*with jyr as (
select jyr,srjyr   -----上一个交易日，上两个交易日
from 
     (select max(oc_date) jyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df)
     ) a
left join 
     (select min(oc_date) srjyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df) ) b
on 1=1
),
tmp3 as (    -----t-1当日股基市场份额
select fgs_1,fgsmc_1,sum(nvl(zbz,0)) drgjscfe_fgs
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日股基市场份额' 
and oc_date=(select jyr from jyr)
group by fgs_1,fgsmc_1
),
tmp4 as (    -----t-2当日股基市场份额
select fgs_1,fgsmc_1,sum(nvl(zbz,0))  srgjscfe_fgs
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日股基市场份额' 
and oc_date=(select srjyr from jyr)
group by fgs_1,fgsmc_1
),
tmp_bdl as (
select a.fgs_1,a.fgsmc_1,drgjscfe_fgs,srgjscfe_fgs,
case when drgjscfe_fgs>srgjscfe_fgs then '上升'
     when drgjscfe_fgs<srgjscfe_fgs then '下降' else '' end flag,
abs(drgjscfe_fgs-srgjscfe_fgs)/srgjscfe_fgs bdl,
(drgjscfe_fgs-srgjscfe_fgs)/srgjscfe_fgs bdl_0
from tmp3 a 
left join tmp4 b
on a.fgs_1=b.fgs_1
)
select fgs_1,fgsmc_1,flag,round(drgjscfe_fgs,2) drgjscfe_fgs,srgjscfe_fgs,bdl,bdl_0,
case when bdl>0.02 then '预警'
else '正常' end as warn_status,
case when bdl  between 0.02 and 0.1  
then concat('较前一日',flag,concat(cast(round(bdl*100,2) as string),'%'),'，关注具体分支机构数据')
when bdl > 0.1 
then concat('较前一日',flag,concat(cast(round(bdl*100,2)as string),'%'),'，关注业务变动或数据准确性')
else ''	end as warn_reason	
from tmp_bdl*/
select * 
from ads.ads_hfbi_rbkpyj
where ds=(select max(ds) from ads.ads_hfbi_rbkpyj)
and fgsmc_1<>'客群维度'
order by  bdl_0 ${px}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="股基市场份额" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with jyr as (
select jyr,srjyr   -----上一个交易日，上两个交易日
from 
     (select max(oc_date) jyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df)
     ) a
left join 
     (select min(oc_date) srjyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df) ) b
on 1=1
),
tmp3 as (    -----t-1当日股基市场份额
select oc_date,sum(nvl(zbz,0)) drgjscfe
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日经济市场份额-剔除北向机构' 
and oc_date=(select jyr from jyr)
group by oc_date
),
tmp4 as (    -----t-2当日股基市场份额
select sum(nvl(zbz,0))  srgjscfe
from cdm.dwd_evt_rbkpzb_df 
where zbmc='当日经济市场份额-剔除北向机构' 
and oc_date=(select srjyr from jyr)
),
tmp_bdl as (
select a.oc_date,drgjscfe,srgjscfe,
case when drgjscfe>srgjscfe then '上升'
     when drgjscfe<srgjscfe then '下降' else '' end flag,
abs(drgjscfe-srgjscfe)/srgjscfe bdl,
(drgjscfe-srgjscfe)/srgjscfe bdl_0
from tmp3 a 
left join tmp4 b
on 1=1
)
select oc_date,'股基市场份额' zbmc,drgjscfe,srgjscfe,bdl,bdl_0,
case when bdl>0.02 then '预警'
else '正常' end as warn_status,
case when bdl  between 0.02 and 0.1  
then concat('较前一日',flag,concat(cast(round(bdl*100,2) as string),'%'),'，关注具体分支机构数据')
when bdl > 0.1 
then concat('较前一日',flag,concat(cast(round(bdl*100,2)as string),'%'),'，关注业务变动或数据准确性')
else ''	end as warn_reason	
from tmp_bdl]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="客群份额" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="px"/>
<O>
<![CDATA[desc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/*with jyr as (
select jyr,srjyr   -----上一个交易日，上两个交易日
from 
     (select max(oc_date) jyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df)
     ) a
left join 
     (select min(oc_date) srjyr
     from cdm.dwd_evt_rbkpzb_df
     where ds=(select max(ds) from cdm.dwd_evt_rbkpzb_df) ) b
on 1=1
)
,
tmp_a as (
select oc_date,case when organ_flag = '0' then '0' else '1' end organ_flag,sum(nvl(drgjjyl,0)) drgjjyl
from cdm.dws_evt_khgjsjtjb_1d a 
left join (select client_id,organ_flag from cdm.dim_client_df where replace(ds,'-','')=(select jyr from jyr) )b
on a.client_id=b.client_id
where replace(a.ds,'-','')=(select jyr from jyr)
group by oc_date,case when organ_flag = '0' then '0' else '1' end
),
tmp_b as (
select oc_date,sum(nvl(hshyagjjjyl,0)+nvl(sshyagjjjyl,0))  drjyl   --当日交易量
from   cdm.dim_business_balance_dr
where oc_date =(select jyr from jyr)
group by oc_date
),
tmp_c as (
select oc_date,case when organ_flag = '0' then '0' else '1' end organ_flag,sum(nvl(drgjjyl,0)) drgjjyl
from cdm.dws_evt_khgjsjtjb_1d a 
left join (select client_id,organ_flag from cdm.dim_client_df where replace(ds,'-','')=(select srjyr from jyr) )b
on a.client_id=b.client_id
where replace(a.ds,'-','')=(select srjyr from jyr)
group by oc_date,case when organ_flag = '0' then '0' else '1' end
),
tmp_d as (
select oc_date,sum(nvl(hshyagjjjyl,0)+nvl(sshyagjjjyl,0))  drjyl   --当日交易量
from   cdm.dim_business_balance_dr
where oc_date =(select srjyr from jyr)
group by oc_date
),
tmp_jr as (
select  a.oc_date,a.organ_flag,
case when nvl(b.drjyl,0) =0 then 0 else nvl(a.drgjjyl,0)/nvl(b.drjyl,0)*10000 end  drgjscfe
from tmp_a a
left join tmp_b  b
on  a.oc_date = b.oc_date
),
tmp_sr as (
select  a.oc_date,a.organ_flag,
case when nvl(b.drjyl,0) =0 then 0 else nvl(a.drgjjyl,0)/nvl(b.drjyl,0)*10000 end  srgjscfe
from tmp_c a
left join tmp_d  b
on  a.oc_date = b.oc_date
),
tmp_bdl as (
select a.oc_date,a.organ_flag,drgjscfe,srgjscfe,
case when drgjscfe>srgjscfe then '上升'
     when drgjscfe<srgjscfe then '下降' else '' end flag,
case when srgjscfe=0 then 0 else abs(drgjscfe-srgjscfe)/srgjscfe end bdl,
case when srgjscfe=0 then 0 else (drgjscfe-srgjscfe)/srgjscfe end bdl_0
from tmp_jr a 
join tmp_sr b
on a.organ_flag=b.organ_flag
where a.organ_flag  is not null
)
select oc_date,concat(
case  when organ_flag='0' then '个人客户' when organ_flag = '1' then '机构客户' end ,'股基市场份额') zbmc,
round(drgjscfe,2) drgjscfe,
case when bdl>0.02 then '预警'
else '正常' end as warn_status,
case when bdl  between 0.02 and 0.1  
then concat('较前一日',flag,concat(cast(round(bdl*100,2) as string),'%'),'，关注具体分支机构数据')
when bdl > 0.1 
then concat('较前一日',flag,concat(cast(round(bdl*100,2)as string),'%'),'，关注业务变动或数据准确性')
else ''	end as warn_reason	
from tmp_bdl*/
select * 
from ads.ads_hfbi_rbkpyj
where ds=(select max(ds) from ads.ads_hfbi_rbkpyj)
and fgsmc_1='客群维度'
order by  bdl_0 ${px}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="px"/>
<O>
<![CDATA[DESC]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="4">
<Margin top="0" left="5" bottom="0" right="5"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2500c664-8280-4a12-9f4b-d2b2ae1474b2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="simhei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1714500,1714500,1714500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1984188,5143500,2402005,2438400,3235569,190500,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="1">
<O>
<![CDATA[数值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="1">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" cs="2" s="1">
<O>
<![CDATA[近一周预警\\n次数累计]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A2"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="客群份额" columnName="zbmc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="4">
<O t="DSColumn">
<Attributes dsName="客群份额" columnName="drgjscfe"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="客群份额" columnName="warn_status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="5">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" cs="4" s="6">
<O t="DSColumn">
<Attributes dsName="客群份额" columnName="warn_reason"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="6" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[len($$$) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-2206385" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(LEN($$$)=0,'当前暂无预警信息',$$$)]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="A2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<T'qe:$,U8l7%:Cl&IT/4i2%&5#`a-X5907FphK0Ns.2AVhchTq\eLQ9gn;1r_^)`Y]A+m#\
+d:).U*:WonZapV-+,kM1.)4m/D`EfP5's82f1^\,3d52Y8tTs^`m^-,![nF5r6]A6*S[T0,4
tjgP0aGB$gmj>$cIO$i0.$V%SPcW2B:(O4WPCU&SPG'Za8;r2TnJFs*1\LUSp160CgFFLmm8
3'elkQ%fSf2[q<+hYFq@;58o,1guah)1'PlBU]Ag/G:JU-XL7Y)9kb2XLEX+EemL7X%k8lj_k
r#KGXflLrm,#X8:qo;24C*G\s'K0.uA9\M:jTndIF",&#_UGhu^g@B)S"%H[X`KE/!1pjMC-
&TFkKM+F?U%aAILETd8fj0@AURS.Y:T3I-3?l59I1>g-iF6B6Z_PP&ooVm^%>bb`#+cVV,KY
+rcpT:dB.k'dC$S_U6ER60ETYYJ.Z)cr1UWikEC`j;,DI?2#%/m]A<Ig?lungbZPR2+[q=LT,
!pk6uKecN+pm*B94U^We@(P[2\Bh+IjE#Urr]A!NID,H/@82bb$0"6foSYbJ^%$QtJS%N_2\*
X*!kiZ6jp`_AugB;?KfM$OJUms+_`Y"N4c8<)o`^B`AucjZMVKWJfoU%7,.,&iX&AO`rjI5'
'MB=`6'"7YGh#</=U[q7QdFoZY0#OQ#D[\icQLU>d)9p6.qT_>>^^@m8DQ-:"0!@]A.';Xe:_
AmAH=Ibf$`RsHTk7iPZBW-9.55;h2CFeC\b*4m\Ao=loX4bnofDDG9sJLb-ZgmahAdq<&@\&
int6nj$ETMVrL]ACh&a!flp::u/4<[]A1tI!u;iQg@hN&?Z50Rpa-(Sl4H&6W&5(eorHZeB'QB
4Y#?Th27Z-#X4ARN#Qle?*03"On"O\0KAnN!0PkfK4P$"/@L/HdX,YU*^,T@FCNWAYkMGhgA
l+Ehf]AK1#V+3#S@fKVNGoEV>cbQ.EEQsaW>LUSi>?>m'63d;G(g73rrIFgtL>D?rKl'p(*:"
H%cH#EtDuiknr&:N6Fc-9\=e-(*Q:r\tc6P21.Fk*clOpq4+ZfFAcn3^^dPs46KU0OaY&.5r
'U3Y@c"BR'O;*.4"D\Y.h1T-bpB\,*h"MV2K)nhJd4,6,X/>R_i;R=+h5*O/c$X/cb7oO-L*
sLjm6hf$6ZG6t_5Z?2o4^(2\^q]A[G_q5!L@9?g/Y97+1JWs$bUsNIQ`A?96lZjsY%8^XA*go
d_8&kB*g)p^U8WK!SR^8i-sVsf9:BsfAVZLGdeZbU=2s,2eW'\;/'E&_FZBd8U^\?X[H2E9#
%,SgD3Hu$YOA[k>-u]Aq+1HJjblpEAeik=[dfB%l4Ap5\<FQEc.pcR]AoD'IE\ks[ACNE`"=_c
6a@Wa;cj=:0epE;^cbgHMUWK9sAm/8IlM"8Yp^SN^kh0^,KadiqZ7")Vo,3mmcM2T@:>tF_U
k*L(6O=a`_STG;_MJBne"#Z9qYXA!#qI4&./lZHWN=i?XXVQd._n.V4'f]A4[HasSCCB]A4KOP
4C[U4R$]A\mf%*a5t+=FVb":dlIVPb-DFsd$!BsB7agamCSh&nSIZ]A)c=N'[(0('23BsPj<lP
O05#&4A4u3tJ+,elAE3e">83BbV^ePB9_Sg&,*W[8hl_TjZ.(!R=$Blll*JlL0io!k-9N`7,
<VX6=`Um7hqfkA'ous<$1*uLXG@Iec_S_iC+k!IW]A<RCWaM_5[85;l`q`@MU-Hi+Z&+m?Oe9
rM5\bXl'8$BjS`'%-G>X#+Dr+`gCZ8dSc.;%]AWB/'3Y:<#j>NCtUc*&V`k2A==QP=gIi^Y\t
6AScsV\Fhqc_aA0,H:V5ShU,n7_B2dH=774jD3m;p,XPD5\!.g>&gBW5.]AJf6g,--<)[Cnro
N(O'ltF!T2?;hjj/IMqI*J8#4bC9]APobc2,a7qTb=QI(H+V>N-ph,+)lh%i1I>11Rf#QY5>!
mjX\<%?Sc?WeBGUqf*<..dW6QF\,Uf!+i.kKEQsVV4o6-W+j#QOgpBQ]A+1L#n5J8WAL&(8=W
gAtlX3b.EqCrC_QDL5&:+!@)-UC<nW!/AW:PD>D+&*laHNCSt^L-37RII;?q6O^i"<e/ZDdm
lM:b1qg\;d3Q2-L2>:aSpJ5p!@+XA`nB<e:-pZ6X2aM<OAWQum6Mf2g1D[k-=D'(?TK_9b<]A
\M$F:dbbS&-U3uL'^5l0H^"\?p7!@'q._2!>jqtn,hilERt[[-Fb?&qDJ0Cq@YZG[2\)Mt9\
tB?*5A-@K0ar[s.\$Rn!&b^/9X#0q(+=4JDX`fFKBrn-j;VI+bHR/9J>\mVEaSA#jNHE.@D-
ki9T5c),fG=RLu%Wo[BV*:jlR-5Oi&rf_UPX!(sNAcFC1>J.?QF%JP%B`t+q.Fg'RaonG:H/
8[[IbG@u9e)j[k#WYCDk.@A(-m3SF#"?("Z1:)uWaVN!)+7&lp)N6'3%AfDCGIZt+PF_G7Q?
?k@<u6c,W7"EJf(^ZQ.1T1/:jh?BbD7s1UU$oH_nrLL5pHK#muc]AnG1IWd^V\A0+8p'DQ?5%
j.%I&.(ei`pP7[hh\Q"-4<?%%P3OdVbSS-,16Y<"65H,flE>Suq0\=r(shDbJbmo$L2_cudn
BeMHnY?-A/&>X;.F=qrH_QWD:2]A4>N_BOCY\=@hF\k/#3j3\;.g0f!HNkEWptR*b@(:$+;nC
5&\ssj*T;kPOKbk^0%fquWR:mp+"C3/9-ijUde]A_SZ68J+d?X0eKoTH#."e$(Pb0E*oBd=iF
Hda1"1'a(BRGXKGWkS^@5n6W@&37<1uI;8;A42PUk!IJ:=ua_BJ9$T7YQ1==3OD]A_<7FHkEQ
^,LFa0oU3!Cq-ZBMk'h&YQAGL"=L+eWI=A\Iu,HRYP\A"5-Qqr\;[Mu8u_*=c)7IY-?<_aH=
8<NSO^rcL0W_s!IVa6d@Q:\(Qpb[Ff9jnm6faeo$kaZc&7RS,;hOaPkR-tq<M\i&e<YN%cd_
Da)HcfY[-YqlmWMBC<+blG;@Af8^aoaX%:&%&1'4u._GJ.fg4`SAdP"[8U_SFg^WGi+;UHd`
`"#KD9frR3,jG!GNXsc#/[;EgB%6+d0D&&s0c9bYhq)VtQ#6l(mi:3l:+ObN6VXIV!$aUca1
OYN[D@nRCM2o[=NVR=XZF,haHOR3ojq`A;BO&G@l/`tq:1N#tGp_?VQK$=u6!QMpfY$+'qL)
qWOs$uuY:rU):sR&2an/l[BM:V+aQ-N)>:W.(l?)ALSE,c#k\s37ChC[OUnte@<jpnl)?n2c
D\nT19(Z,)F%a^h3[PS#G;A<+"Qt6"\!%"94E#co5VW*HAYN+%Hc:kne=HCCq"p*+Zm"=*8X
K;;%ZM7elu9;6.4(NOZ,/?D!$t^sHhI<$;SP$BfD,<n0MRa<c*:L;]Ae.Ep[s:N/Rk6#/D:'i
fa3-"mDUhVSdCk_u&Q-3"OO]A>l]A*25J5."BnB&]AA7SeuDR*U<7P@UqK8;o<Q^4?;cThT:fqk
gLs:nU=QdH9_"(N5E!gNOgtp2t]A(1+,IL"9M!ibf&]AT#Yi6#mI0l:%MW5j_R[hnBD<9H)F.e
2`6hiLTD4&L2BupM&21<&j@a&Qfj1Hm8(dN&GZZ6N>=*9Z-QjApZ-uXT^F"?:^C)?*PM=7k"
Ln,<E?[-M5>A`t3/22J_MJuen6]At,/HAf4Je\c<<1Aa/)S]A%;$iYYC8BM"ub]AVfTF&m\<LXA
j=:6Y_TR!q>,E"n?Hi>UZCh4?>:h!rjV\889#%]A>;I->Blt2K.7jKZS&[FI3kn/2qA<Fc3rF
/G$1;8ffTYjm+GU7=91uH+`WXgag*_&"Ep7-hUuIU!A_G?:pBk#N"*1RQ.\Yo&@gtXjPcm]Ak
c&2Q34dfrUS,dM;dZTu1s2ji?U`?N#_!Z3lGW)kUqM"?afRalGJUET8PU3X;u%;<U`OOpDDb
QC[VioL_/R_s#Aa<*RAbq&1tc@0F['+/+F9/6AS49nGs)`6_RGifcX;=[-d"VeY@9i3Dq=N\
J<g!^[@R?H->t3\81pP]Am'Zk\]AIB-q2?Hc<XDJJY9bm9E8RHE+J?1;0QtLNmX4#"[C:B544d
adGD'KNE-nSDd$RrP\-ZRAU47_h0DOLh;SuPcu3+H43^[l'pgWfG9$a3"N7R1P$A[Q+-?4SC
a7`H&d\s?]A$2ED#il;J,@Y'lmf9I4DcoCb/Y/f.Lrm&Z=MXI&`-;]A6JCLM]A"k,0\%r`5nZ)J
[[`Ec>Vp_mq:rXD$=:;O;O_Flte"P%bIZjr2&4#'.+9aq>n']A.hI*gHr<)q"'K!!02Eu&H$I
q'O($ISo4*MQ32sS)+"d?=1<qi"D?!etY@\l?a\[X6.Aa;<"Kd2sK-@9=C?Zd;?75(.[V;!R
4=6[7cdZ_1WXq9J39O2T*?4jL[.m)N4k".:Bd",V_d)kgG?+hL8I\4F+j:-<R8P3YADN,$(!
%iMSZ=OKP'/?62V2:7P@@149"\t)9uM,,U]AKFK;_uNM'aIJ$H1Ki$PIk9#?10?/@3!%KmIcF
uK^,q8)IJqc3g)dX>\#m*`#E?a/\91jjc,Gna'gee\dN7_.Y9E[r:X9!Pq_dIVF390Q**Sgg
@T*(k#-UrVil[5hYf!SSMH#d4_VfDldC$OPH4^U'QQU"Yh.uHN$j)hY8B*-kgg6&M#/LQ.qZ
MNE0[>QZEMl_B(4]A?3L^N),T4QE4J8OukrkEQEY"k,%0P[_:*^P0rlrd>7=9#BXpI$gc\)\1
:-IV=4"Rq4*T5`*51tD7&1(:jhWVsIR09<O:o3nC?'PDa1QDMk=nMZ(:K9>H/'b!g/N:k>6/
FXNj]AWtip[6j7Eh$p)G3e+Qh%b:$K%&QZEiIF`[Qc6)AY)-AdmN+G:'[HPpIbd6I9F!3%94a
bUsg\dKPjo`C9&B*+WO1:R4c`b]A2r*9Tr3JhM4&UT6'O/]AGg8QLoTCJQ?^>Ii-m3ugQG&DU5
;5/B92&I!VS5N>%E16F]A^uEm#IeK&lRAj_oG9IE[,m5$h#C7?^5unRmGodo'^7P7QUL3.Kn.
E;4BJ9@H!s8Gk*\B)U8;I,Zua^43>H2d<38Ge!qh_P&ha9t@.&"<V?ITra=,J(?[l[QW#\]AO
ht`*c,\/dNb3U<b;P/hA0RN,K&LYJ<FVNl&j&23NLcj9C9[3?ddYp@Ro78r/[NXh73g,Enq7
t)P^kC"FeD'(E3^5*1j++bBXa$bfOWcZW`=)-p#eLUM$*E83^B!DYEZU,N]AZhT#[dn#FLYb*
BV]A<(E0NA\)-V&n[FZq!L`2<;$7>'?oL5LS84dinQD:5?j.6?;nJ9c>d?>lXYU0</8/Q.soB
e,8dP-['HUl<9(r_G;X=XDnN@olR`CXa72#aD^GT`V.b:J)hW@ZMfmCF*WJGTZ/UgM<"H?#7
rKFhee=ch<]AM<P]AsPi+)h;5O-+5i.SWM;M&reM-HG@O9mf$'*Rc\#hq%P*Of5OVaunKS<tlB
NjPD>YEaC)4u5Nc89)\LV:S5T\(HY<F56\:P#02%:Y-*U5+*/CQPH>m.qVJ>2hXk6b&jZUiY
[d=+E;tWT$kli,5@DV1j=UVPUCjBl6#@[S)?t+g7V))]AZ$`T0jM<)jpji:-M!e710hn\M]Ab&
Mb7S'Y^QH89]Ah**;Fmko/:72TkAS(Q`l$=*U2NKmgb8UsN_e!l;/`%:W28iIq7M#.nAOT!/Q
E1H#TU`?b7:b6fCM8SF&*Qpu:lLGc$f`V*CX9DDQE112Vn!Xf<\l\*g6nW0;1J5:Kpe]A/<Ss
#_P#/tq+m<I0c=qm_;;b!J'o=\>S#;&tB08]A:"D[c=0WVHDU;=ZV0(%P5]A%"Ia7]AL!0J*WA-
qK2hN$k)ib:(kH\jD*cb7dX@MdB6a'C#NpkH%P6_m9JbZk`b"7]A7XMW2)0=A6l:\Pk)fBkKu
s$ZmKSVNpusNRL+0OD*DN-Q!ObHA$gFOTA/%pS4YkD"+8mDcD=3:B,]AN@CTk>U!%+2L$BcCO
gi;]A#<riD^9'l<@1ZM_>$<:QuMA$`t'Ha>=5*bFR]Agb>So+.%D>%ekqgW3eb[@<7Q@Cg3-\Z
9GUPj6uOiL^Z!PiS%B`\c)m%MJXp2HA+Ii-Cs`_jh*W?8]A<m6+JD`dA/3jX.U@?>,Er^j+kf
'<V'2]AqQ"djpR'>9'PhK9XRflGFB?1*fJQL#NAgX"Sc?`*t,ZlVNHPf'"6Ib-lR$kZ_ToiUS
_[KR$Wr<$>#;&"4Q=Rr3mJ"el.j_0V1l9Ocb]AGi@$!UroSeUqO\]AX"dTBfJ`mPh2D-#:&u%L
@kSMm"-um,S4;S"12\S`3leZU7dFr$LU/f=#omEY?N@'h"fVi[(6H<t`geFdX%l?48-d;@b0
(?LkJ5B9.Z3Odl$+91fok!;2Gm/<[r7MX\<DKRCECPi%LF8F:5oj0Or,pWM>i:Y-Sj6OWQ10
fF'tb[e&[n#h'K8()9!=]A-K@cpu"$^'Sd[:JF='!PfAVH;[$RX'\+@*8_J"#lf%nJ\G_TRD*
c>RNemY2CL&8a`lTB$^EE2\6-I94-oqIfflgb+aB;ilE)t1#5<H-U_F$@Qi#,N=<HVd-HOs?
EjHdqD=q2A(8t#D3Erm?iJ4Phk(t-o!d]A#/Vk85/!X6Frc^.UeA_aeP.sa(F(ZLGP[^\f8Tc
ZaHSu2+c9<YDQ.rk3HqbY@PR2R`*[LceLVe"T-0BK/E;u`\6NGTTm-tE?c>:QFuO[U`P$<]AP
qfW?>W058u4AL`CEAD:Vj9e%2,*,&h-2Ne&oKI:@s03X,oR,mhV*mr>iVJ\9Y1U&dY)1k9RW
bBjFSLrW]AMW(bL^=.ZK"AC9/2*E<RH/BLcm!1P"EZf:Vb`L[f+"anFR5]AJnCrFm,"^_6Pj'@
Zd1'FPtqt+/+2eht:*qlg`$%a$El?^J,.oi"Z<EJQj5c8Z+Hpuoh6/<.qWQc;uJ&a523D\LF
O/*>$"Jok_Zb#t/0*I29Ib*k;3lJ%.8A;Ti`gB%,WAU^nY;dT%U;*79s5L,G+%Tg^=p'8M2q
DAiK"-Zkm#FnZ>D3SZ"c1V3kI:eE+qq.kO@]A5n!\=>KAY6B]AHd#8T$d+a"T&"dfpQ</n^"a+
4Lfl)<*X!A8T'R`W4E_-(]A<+iQ8?Go,::5@!r8'cuA>%$#`X?(I9?fQll^n;eX_UjddtDIn#
0Tp'd*LKTAM9UNXWu#iW&W`:qR@d9S@0O`2+Bsa2U1lca3MrV@u'Xu:cI7?aEs@*7A6k^HKa
J;+=)'877!^q20H,'GP8p&+hi%?<6X(i;!4<JIap8l3SDBYo9rThas%p;>7LmLDP8B.[&(;M
"@rauDl3)XA\F<m;LCRo;i8OXnW>8U3i6L.q%([f$;ks/'&s=.H(2mXA6j?!j^,P'kuD]A=&a
:b;$!JWLZ%Q)lGW-'FA)e_^T"3eH?O=C?UT/tAmT!0["E4lY!#Ce-C)f&F[^gQ`kor<7+^Sa
=q9e;BmSt>DYsNM-G!M2sqJ,1'^K"%?TqtpBAG6oZ9IVh5e6hf&QtYj6E'?dC_!q//8[tOA+
u53Snu5/u?`9>j+uCB1DfL8\_eC[`gR3br4nkGs?t)nYg?QNgHYdN*KlF1=_W`4uVK+j6[7%
%5._A?\/!a:S":_M>3)9*XaP(6rejt+H3j:@d@[MZS(EeJR1^XbW.,EC'.*AE"VN*_f/&4sf
nAA[oB(H&9FoA76k<eB`_>!*2:XdUiF8\NT/PtXCan7)KB!l2;nd%fBF_OGA#gJ&PDj=d8?(
0Rj/VOIRPB'aW]A_HD2a]A$-0g<6mnYMc$!:r`dX=*>O98>(L0h&DB]Ar;H_WprK+U&a90M>c>J
4,CoLu)/rDCa/f9&-,a`Q*6gqkU1kJm76Lu%.<HHnFGjT,rLRKJD953MHtE'$P_i5GaJfF;a
i\r?n,7Q@Vn^qu>s`&ta_b"Jl,C12\-$0k3Z5eJ=_;u==6nIfC,Lq*``f_)+%E)9bC>hiQDs
!O`:J<;H#D[2BD+@lB%;3"@0h2qr.CFqn:jO#k14!MmlDiq;G5V9E^mJ8"i\eL%!7Emq&/t[
HC3M")a8W-V>%=SXn5j>TCPH7.AP#LAXh`-AaG$'[:tV\-8(ln'`I47JJ>q^]AoXj00d!@62g
7>8UaUE9P\tj\RUY#Q)agIbc[5$!a;;lh]A8aqI\sYlD;/Q?=\MH%"Y2m-N'a%$re<N0[A)(?
4,>4\'#*'X2[7-Xa6B.S[j=2P=kO4`6T*Fn!GZKJJ?T_kcM1dl1o:lFXU#D!4cZ)F(fqXWl3
&NngUCq"%P/MJ%EA"VP=t9gQ0m4Y5:#0Z&H03Z&>94[sYuOB[S'!UDpC]A,9HM>[BchJuV;EE
gD]A'c=5?hgb3$OUXdO'63X>i#!9O);ocP>P8CoPk3%GXR4.rK2>SM]AG;_S6TaW2h2k]A1g%r_
,Y3%Y<q6FG1GLYGdbfc@^G`__h-BK%8WBJ;J/[p<Z0nQ'`==M4G$\12+D`*nbG8o(f=5*ghQ
#Yf^tDMZ;52k,?)5*,)1U'.A1fMGcZ;&,E0FXQ6,4*+n,WAkXf_0-NsA-Pe\ZHm*6d$t7WmC
^F]Am$C?a#TT8fN*m]Ah+8Fh2Km#,q)3UmV.lg.iTS)R%!F7C8Kk!gk[s+%??_ecETF"RJ'0'.
2UN%WZGQ,iXidMh3*mh`KDAXJ!nfrr<ru3eGOltirCAAF5M&GioIKNk`Nq95=oQBkk:s7OcH
LHMM\1g\$6[R4P4L3hSZWlbThSe[%="?!e&AWB&pg^gO.kQ"kRh,\=9F$f[i=c?'Q%:SheRT
MLAs7qQleD.4t*2>D7,aFjHbUK&fa5&Lc6SF@qO9=*c$<Ud6mt?*Q?\R!<'uf,)3"rDQOj6!
/^@5ad9`g0O=HC3[G<<]AsAM1b4=6/\:Yk#?J(K>']AMqf%8ug`C9@[M]A##Tc`nS43S?Ga7=X[
$qi*rXUB1d:Mc[0o71$M+$l>Dj6Vq!2&"q:S93?=Yq_Q-k7qs,lK7fUhNueFM)"^jV$J7#p.
JJhZ&0J:B\;>`Let-2Ll@6Jl!S661mPE/r(LT`J>uE47)1]A5IDtJ9BC]Ag)^g$=O+''3ER:8L
,]A<(3mVod]A[d3CY]Aaq/3";.T(H1D<f'r>&(tTrS,+$.KP&s;&FFPpV1$.5"a,_);P,`e*1*)
N!%[`cr32-]A6.W'lHe]A/im;3$Y<gr&Sh'drDK(]A2-`28iHjfYL4UBc#(MZ/C851%N8Uh_l9U
K!'6!nB'I16FI@m1RRa:Eo>=(fD\g<X(J&'j75U\LDSYi$liPClCD&47bBRhB_@4D-^ZR3pb
u;cP018,Y,pn1rc-/j\+$-Prg_p#\VQRL3j^Tseg?7l:RgU?67,&n8F)N@,dMhO_.[%#/2/@
f@e,UT`#,]A<N;TRO6U33ai</0M*r^H8[!-^%Oa8_q"FZMGStS96)=sM!Rld(;H@fJNBlJI#0
r=gS$,)/bC<FTQ<igNMq8U>8oL^HM4n[H"L_(#O)EV7qW?3W^U4f.>j5Xf4/1T33t8.JfS4S
3#Z!khP_d*RW(Qp]AmM\Ke?FWMf+F\=foZb+PEqo<=<btlIFJmO8q]AeWQM<J)g6-S,Yn7'f,r
`j[@Q7]A[,7Y$Bhi8L93!sg=ldl.6l.(`@C!<T3NVfD,UbKrBN5^#-[#\XGe6D#tm:oE%/%+C
eO0h>\PoK$q[gR#K%e'AtOXA$k'O!^#H+?.A%oi/bOq,63D%#<:R7%fcYT?L`[?BGp(?h[um
4\SZ\^d+o=^%`?.nR"mJBHSVgh>K-,/\t,5=.c-:?&@&iZ[53Ep`U6l^R(Wr8d,E'CJK?qac
7*j*RD`'s2!$3&@,3f/m$u=2?F=Gg]A:sX:qjLRXqIR7`R$l.^C=24>s@ObTpob/lSsC8T]As#
Yem;e):%oNCrnDm^V$2W/:.F8@sd<q_EWIn=MSXj6"d8+/cTW)pZn-gO"2SrPdh72bglBX@b
`6L9h&OM)p$<EMAIL>"4^;W1,,Q8^H\eFV:&WU:bW(9m"pm&.5?H_@OZP,R?X2JOH[e09
SB[+&kJS3^!29fK+<=u$O&gK[Dbpa%HmTHJJ?u\e0.:*]A?HT4jjParktU`f5FM\]Ak'[8S?gV
suguVlF5!-eZe;gKCu.='5T/f444GEeQ!EeQ.hZrl4nb;Mmmrf!KWI"kW&6)1YY_\ZC._1lZ
?0)`%&^71e.Frm^CbA6temcLA,fo4G9(h?j`Ll>R/VabP4V&3k.BHV$I#F?>-D-%`3:-HJr)
O\cm2^pT3'_pm'/3LV;G.n:OMl->D]A9LV%p7'E&X&T(h5>DH/7.<acOncmaW!g]As(<f,p!J(
qf_3Cm`+B&%4DIEbsGqO&Uc!L9Aq3FPqRjK&J2\T9YD#t*&I&M<NXYA/fRf1#)1H0=/48sP=
a<]A<al;N>9>+%B?"ZI3)pHU8YM8l`g"h'H=^3.0haQb50@,3=(1U%.fu5O1(]AjV3=Z1j;dNO
oB:;PQ"S/cJ]A12ob%(/a"$iO,f:hN_!qXI.+N^g:LCNgC8]AO'm7/qr158U8RfO9NeSfh,j1r
`U%m9>k;pifkEa)<()[KrGkk":rf`FknhkX<<o?p,*fTse'<_Vt@dn%,(*>Gn1<Q6nCor!9M
I76Gl%I2%u=mt8e1GA#brbMLidqV>p$qj.YUnT`(]AZpjG,f$rCl;IAu6E.Y,b=O\:#kkR,":
iHIPRe;Fh;siX%_@@U]A\3<A_O91K2[RS6Mn7cDi@I+2?So=E4HingRo-V&&9JIjs.t.qLFLa
X/<(hcgd]AR#%^$(@/[pjUL8+pAXAZ1dH>'Pm,XWa8mK]Arq2>Ineh?c@mTp&4t;MOV*bX(!s'
sJkC[K:JN"\^4<<NVKt'IlI7*[GlShSKkXfU>\X8.:7Z_l^(acJgWfUk<d/h;s/f/'&-ZQq;
+mN>5(0Je?&>'5)DYD3UBR:USF2p7?#).GQ(/%1q^?N7`Y4mqbGK)p\jh9'3S"-:3'E,dJ\W
R!EHCX/BmCq7kXsoOtOO/$f+0F0O-mY*V5hqoL]A^Fm!Shb<UU$cU`0Cc["F[au]A07j660tmc
LK>`ZLg)\eX+P^N^spV&*S\4P%<--V(GhbhF/1ZLk,+GD"Z%8)^f'DfqA#G7SR3:\pcuV@(s
oq%A5f6W_@p[u'*q,7Yms+m/+ni/!,;l?[P@Np8`)%N'WSq3A[$h.Jt`oYOu;`#u'0'laR,!
oCRKJSk]ABs7r&j[RDkh<_tWF_7.%HbumF-/0>t!::!RF*6qHbl]AU4/'eW:P\;.(DJW$;qf*:
KMeMB/5`RQPgon)ZjJU>#`%2?>qs+2oe*N=5\12_1bI/$;cOVSN`?9[,X(&#QS7@mV:>.8Nr
C(GF7J/^n6>^LD(pC,N\VDSugrC<!qHKe'!cK$6UE77E@138EZW.6TW8_t)KRq!iKWpqXnWY
b8dN_WucY-]A8nFP$lP5Lhe&6Wu:Lm(1gIjf!i?<K@jYcsB0#\Mjh:*VQTSO'cM%B'=3?Y.QL
,_)VXd09LB;+/U5'[SCDHeKGEdVg`G/+D:<$7-#F!S(>m^=3T/q@[eN(OH-eFqCl2Wc<s2.8
Tt<WKf0XLGdOuQ%@b1>+X*uu)rUE;(='tpR!d,E(o&9o0$>G6!?lWmA2gjWW@[Y-hXWKZQd7
8%6RAfSgR*rC\1.@7Suc:ed3d,LcEsZn!-,00j%K@)n2Bq*>n9rqm[8DHG9NoOj)'#HE!;+k
Y<%T6HO$\&+uX;0QZJ)d$T9lo#WB,O$JRYfN-*A=aMn7PUm`3CA#kFaceU6K41/dImAbd5<J
g2EBoS*Bi0-%O+YW8:2O)EkXGo8SnZ"'?RA>;iM=S.]A!kF69k1#9A!WpB:aEM@%-UdiO]A-*X
KVa!#<g)F.R!6b'h9i)oAZL89T\j+"6WuF6*W36/u!VSu2BKo5Y;XC@6/\5+E\SPp,m5f4WR
X7W"FI9Mu(67UK%IIe%`mZ8u25R%!_lAtY"$-,C**h(ZQCF^*35<UNWq1&J9-JP#:;.['Ne'
kp6sO#J=X?tV+e_h-;[T92k]AA?<MWS9`UueEW.hp?l9Bf3(PWtMY4\C=n(,aujb5A5d?/]A1^
KnB"GD4Q)q&t7E7*m.4M?g#VcgBO.)?1.I=Wu5fj@p]A-(f,7kD)^Fb)lmmcD;Y[`qI,NsVDJ
+SJ$Ef-/$B;q-2OA2>\'&JtP$<_:Jd'rMbK>qY#p+`10:cm]AakR<^r`u),nC\3"`ng'oV'"\
Pm&8I(m%VF'=\5.2Z"q(jJ%u2OB_V]A?2tX_]A#!.H4[DhWL8;[1Q#41]A[c"gn?EcciJ;dB]A-(
"l9aEYbO'W3kUQCj@$QWPt&"5nne&Hs!LI.X&)8-Rq#6geVOTj#S!mf<IGdoD1=a.o0O7]A5A
8Cnt"BiMP@!3T/pE;SaRdI5*m@;rkmXSrTXWkBSEE^Q#`p7""Iq1KVu0WY?YmLp8_6qPHWNo
P]AY9qRXS[-:U'SsBd$`8>,*(qCO(X,me.m+[rF8%Xh^QYh=!3A(Js!BS?[T`"3"(%+=<K\ZB
Go4C(h76lkFAFXlQdh8^R7iE7Cq!mbP3R@)+PZHgF44X/?V.h:UM<8<nT$.AWAp7C]AMlIH[g
;MFRK?O8?XGIi;=BHhV!K>s4G^3-[\'^@;@=(r.gXN&NTk<r[HD&^s'_pGErD[,!cXCV(XGD
?N/%Q$A2@IlbiK)`%!*DSY`jm7>5mHoc'*a_G2^jM49!=kT1BJ64(-S]ALJ*$NO$RFp709m3q
<AL*d4oYQ(HV^Vb'SX>t#icercn6c=Xr=[paM50VC;]ACis\'*b')[9jW2<!Aa,#78)domKS:
!]APN2dXq-eIJTtKeR-,,T_q6G`B]A".CguQYeMCh;(<XjNn!mJM?'Z50:HcnA]Amcg6XrqRI\*
o_C,s%.K`Xmo0X`'VkSK-6pfses?DM_uNCWCE\TBh.ua+N=LNht=Ac90F"IC7l'F#<J;N)n4
rTD4ZVs%L]ABE5%YXZFV$6419+`86[S6?5%EF@FQT<dTe!>h]AK@"]AitAMLmS##nukPm^%"U>_
`"Urcu!^M8oe+7.(g/Ag`k$6\E7UYA)R>Ld7XuZ0:[W2_(YGamajU$:I96u1'Espaq,c?s5.
s?4`Tlp?%D2dDo&e0JmHorZ/--U(isA9X(#]A-Cl!6Wabh7uo']Ag;f+M3O!3'=H.<'eQI9F&*
l59,=./+ujb?o9TjkG%HEj\q"\N(=(*rPK#EA@KtM4jkBQ(N[<<EMAIL>']A>j_;dh
X"A6o3`;ao`)C(r);!4UCn87qTlQT-rYtQ$ccY&.iAOE.Vsj?b1EmTVPprJ15FF#T-'UrIXB
nh?nAuLqOJ,V/t5)1Ic-F6lk,G[gJJenp`Au/uu(!]A0sb+dC/aO4nKO![EsqD<*nZU)ZA%e1
)C"9em3dc_Lku49^#JjC9&R<5MfS(m!nLE1Prp:X0Wri:kL=eK'FFP80hkuX+*E?pZFBVquk
o\7GiuXm<&I_(B%XcXjlpV&bLPqXC)"a,5C)]A\4=2UK%Bt9?LJbGb"315P1W`%ID@572j^%(
YoP\t[T:.*\*K(3opq.mA1.`%:-fl/1Ri%JOMuAaT"tgfb>eJf[H:%=\peJiT`._`m&4=?^%
(NhO]AMk\5;lRG.UYi"hg#_1oqRZlUtef+7<q85GQ-NAi6V<+*DFW<8XKANqUX9(.>KL`VuKX
Z@'P,Vm=4g#c/:c,ae/1Ya)1YLB1J/=9E>`nr&sTu]A`)1#^IrD#hqIrXNiXBieI=dJjZV,;<
;@/0ecg#!ouD4<U)@r$pU46D[jrABQ&fJ0)0\G%A%)rd9VG8TEUu6`4mdP8)-O.BdgVY"A"8
)se.sma'Q<R;m6cLW3/JDjrkQj.ij,d4cpl7lMbMCA!DV*;+iKgi#ljFB7urF!kZHnLGI]AY_
r@PQdWV12i>o&#PO$MTENP2dN;i;HcWf=rQQTXbqXP<37r.->!QFWqlCYRY3g'F8=<kDkn;J
q0%I[NDs"".p!&'WI7LN@0HV)PB5i>+([\o_C;N_=lL#X0#/jR657aR7ZBXIG1GD]ArTOq7><
RYC4'HO2c'2;bR=:AJh1:0_%-t>*"j)mV^DJZn,6)9fZF-D=Pu$*/HIq'rtG>F_6:%mf*3'"
6b7FQHsO1IDFI$q;%M"Mj5X=/H=]A@EaJfp5!bI=ia\q;9O%l+4fRR!B0%7fI$2!B7?9C4N<_
%@"V9,*f.<8Gk:>Fr\VP6k@^B]AR53[WJ6R'*CgQmbo8KhHAHL(<>DTp,r13JoC6LRfgDp?ut
"Vs[!DEPY_+n'4CU[4l>-XdTi^D9-2M(:7B[4%F.f$7XPSD*JQU'D7Dhrog=%PoYAZqi6u+E
6*N)qoXMJ;X$9^COQtN\[>he?V6jleD2p(;pm9_>UE1J\fi1#n#d"."`AeB*)Gh?nHG%9e/b
3\,N%dp!!/]Aa:K:Ja2C&.cG3%Uqs6R`]As.IT`MlpCh]AMr7Up_iY2W6NfSb)bTnYN1m1LhXo4
uQbmmn&NSBP4g7SYQF#AsoC=d33<Q_'oX$)DBjD[^kPhL@a]ABGG;t6f:p281st'^98t`qOu8
?4V)PIK=^7\A4T+EKopR#dRp\Bh21`@r=!05Q#\+/.!nT>.a0AZY"'$_g/-%@iD0Lj'I:^A(
@BYkSCsmp_%D'8Xb^+aYIgl9i:WX&Zp!N"4_RNN+$,PssgdE.d1*6[c(Id2VU,botqrgXb$=
eqtL[Epf^lUoLH^OuQ-6<>Z91AJiG@.ltfLCMbeT93:s(A*Zhc#"cqr]AcG!VR+2OYNB"Q/Pp
Zo=itAY)1gP4"JiX?FJTWd&O2uW@OLKR5?r/dIq]A>J4jb+#kjcNVOoOS*QDD]A8t\85Ci=H8"
"G&N1O9*lX0C5TXfKG2bX5dYkJ=;d(3+!3f*j8/f+#`->69MpILfg);*X39"gHMFlKACV0@&
o9+V=?KG5Tt8D?EMO4nfId<%%i.-%bXo_!`,@rE,0lKS)q98EREa4J2eQgQ!c"r,Of"akZ9r
FP34qAc(Q4A(u(+cZm4sM?03(\srp*fq_n]A6dCJ1o\7:nSYWTf-N#b_#"[1o(L/.,-t)D0?K
K&tn"9OB<&%+uj/f84lIOMV3mQB8"SR97d3fFPR5`GHI>tiFJO1Qb4mp!(Y:?*\"B_)[,>-J
qCX[.Ro@e5VR9bL/B8RA"G5rC/14n7`3$rV6;QIG#Y<^&1RmXV#m)=nUT>%?UZjpqbED`n7p
!^*R>W]AUi>ttE5Zr@46nfg"s(5hq=9S6:BhDp%s\McBRmhFQWmWRsIR+AtE6WSNi?"C;CXUt
IS>A^+:jj1MR>XJZi4&FLiG=NZ:1Yr?D9fs$Tgb"<d.YAc,E\sDUgfTg@(#J<lY)T#n5'jLp
^qiJ:'+boeILNQH]A^B6:?=)WVR&QtE;0epS`*__d.F5CN<KXA<HTiQ1da3SJ[X>qq3L7i?46
c`8=>[m7nRWYGgo#D&aZ?dm31sA/JU,??L2>&o/17YSR:@S.73Eri;C8N"$b:T-Pd3=Q$\qi
ea;CYrdre:<I\)tjV[lbk3ml+.-=H`Zl/Bam"F98-M^\INa7iVa5Xi82.gq#IMAo1=&\#WBB
WUpUU1\KHVu&+(!p0JO=Teue5cT.rY(!!Z)aABSc;/fH`hmU1=I=5fRac]A</Jlp"WOdbP>W,
uMeQsZD%r$!;!.g3"lG[((3P-@>o6]AJILOUhf]ALLYKUMEp]A1<REUHYNV;LtCJ_kkQmL1/ZCn
`@ursm.DULDK?I@c!.C]AYg\Y9F>rr7$>q4T$H0I&dc*4o`<d\n<0U@6%eqgT<FN,M"EbS^gq
uSB$!j;)&$8Wa]ART^h`i[1[R@>^-Y(AUMWUC!VVqkV3>!hP$1f08gkd6U1n)kULbXVHScnB\
gn@baRRV+M&n0l@g;1h:"]AjG*GPC'\:)_hs/)u"VDBu3<PYX$GAi7#CDSJ+'=3Umr4k6g3=E
c^itZ`B8pa21B7mD$0sZ?9hJ/ZnHe3^4(W4>*@H2j6s62h-,5LOq@3?<a*+r3ncTSNIqYi"G
KFr<jXdmD<K*eP_(E6Nb9hdq&aImmLf_YkpQJDiKdSD.<O#5iD\;B?$d]A\E&Poa5;mTHIg:$
^#Q^!cPJ5c37$15F4VLtjuNJ)O.G5iG4N\\GTPsaD))XJ`?/NYLZc2&(3JTqaZd5b)\:8k+4
kTsG@(2Js/l9)rO;WAp##&LeUOY"<r]A<$s-EL%:6q^D&,cUf3K:%gn65B^^%h-q-Mti1mjrp
f/"QUclYka#iG[[niG[[niG[[niG[[niG[[niG[[niG[[niG[[niG[[niG[]ATDo0hgP!A6Tr
0s3rd1b9r*A@@>5MqS"=]AWiF=`(VIo>aM=L&[W/4-i;G>H#4Cn=rs$C3cMmpr:#I!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="92"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="279" width="375" height="92"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM02"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM02"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[304800,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,0,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[分支机构列表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r<';VCoeD*$7,Q(;NIa3<<&i_2pXjd;GP:5tT\;TH%!o0T:Q8O>jtKL"#2nj]AHSW@=)QX[
()<Qlo4k\%IfsT]A1^S]AHdQA!X\u6S^.'0cL[Rhq>Kj\QTmCSriGSFMt^NS?15E!g,FHenm>t
:cHO5FACZ805/3PRQoZc<?S=4BN64d_9h]Aa,*Uqa/./!QbJA*(a!h/4RN(<??=he(RqT7W,2
uafq4QH9r8C/XojT4htV2PgufRnSqB2!5Tn9Fr`,ik!Hh"(aR21aOWJH_nK"TqsjGc(YU2mI
iD`QDfPGNQ=uDGAbd>N&gO?K@dm,,>`j_8?"CGA_,&+YUR)_"?QJjU#95CcV3j0J'#H/`JQp
o/?kYBbo-FDp-ErRgeK72u=SN/T&%IgMm/__4MaRo/CIoprWT'pZ;>jT98ergi+q5iR3)=^=
V#@1uQ@3ir`TaR^HtbW'I.G\r<eO:TQl'oAo30lIK+=0=U,S#=o*cWfmUnE_QG:=-mSSJ<$^
fTI([[Ypr947)D^,h/rotN:fSI,h-@54W50_[W\EsF`KfhgRW3!(Z>-2*.]AS4+C^H_T`&p$_
cG`7nimPr\V%2u5P(1Amj0;^%oa>qaA)uRolD*4;*R8lkSf^"n7dU5=S4g8^:u7Sk:OF\I%G
jiO<E''C%%D&%O3&UgB&*=BsgnAIXW+N(?orkaXoh_>\$i>lhZM:*O1&1EMR8537j!Ba(PT"
Tl?mYNN1O-:_i3&87AtD,]AmtLL6$8&#C-HM2LIHV@dCEDLiA*%7`/u*-+I>Nho\3\GufOCJV
=-J&n]A%Mde5`Z4EQ#Bl(@.m'>1BNT72Mgi5.clp:@6q=tt^s;/lHH5Mo@>'!UH@oT:a-mQ>*
m:ujW$SIV7ki++&QTTYHCC7Z33Hb2_pm_Uu$^$Bd7]Alr1<YQ?T7TZlSpQ1fMS_m]A-/Q;1;*f
ZrU:fiL_(Up_&X'Ltgm3B3hsD)c=oj;;![NiZ#>Wu5ospBqFjf;<mh*gcBL\eIjD*66#IX,/
8uB)KL1&.Adgj@]A>3'#9rb=JYu-/mh7QQOd+#PI>6eMh,;Gebg3gn6B`_m0COIoR=+CRJ23O
]AXATi/!afoe.fd`:DK^7_71"++QGg:d?Cj/JfkRqbm0tF<LppViFcNPRqLa(Lq!g!rDb3G%e
K0a`LO@<1\;]Amca<L71ApW4B$AIDV4,`MV1K^t]AK[DB:0NGb&*f83SV%F#FJ;lB8T5o`J-tB
_6!M9;^TLGD<hKOpZTm-DJ$Ie4RdB4G?V+@YcT_Ip0$=p/_4.Jo\;_5-,!`1obA_N>&8Bctj
e8W[p/jCoq7V'IMWJFpK@S/7@)'`;9><MbF=3Mb\a='+@s\7TYAqe]A`5F$*.=qhZo#R_b\sY
=.7F&o9\0<13Vc?C99/9,tBuAM\1Ij\pN,aXtk%L&RkBN)I;Q#X4(u4:NMtj#Zd6q'#FAR8)
ZMoDu.NN>hF'GE(Lk@a16^I(&em&HM#02TGUOC9P).L,q9:i90--e`@#apYLD`I4FA1pm[o)
<1hVe)uq"/fHg6/kT-mIr\[k<40U6&7**7Y+Tsj>j0(OD=Th@/L1.EO2#(XCOsX3hWQ_":EN
bnA+DD7VB:^r&"4@"TJu-aR5T"]AL/10@u_pU';<@$0JH;]AaRN.;3i.SE92dQ5!0\2*[).E&)
0KP0TfXG>o4Fb'(NQhMm#er+#oo#j$*A&3&2f+1hg>?eb?R)E(%SKC^;%YT6]A!3e))6)\:+_
N8dT?>Nep[s<E/X6bQgJS2]AG43CW1WQ=K@2hSk;+QS./q2mSeAZ"(93Wl$4)"u0'LY%9T5uH
U#*]A*`8sG8c_%Mu%Ed\,C]Ag5<)-i7-2,[C:T*,oK6n4L[Rd&%BJW]AmNoD,iD("SI87:J^>#`
-=%Z>pMG&#QkYkRW<'NlG$tE(G\h4b9gD"R)RX3h-'GO8iX1>b7kQ!EYJ%Z%&KiQc8lVZ@dB
.'EWD8bZfRu`/s#+UG%*D6*s4^FX]ART.Gcd6/<7r&0!:Vr6Y5UG@EQ+q;FLn[HPXnS`i':;D
(rT)d*T6)("pRqF3%T%kTI&lhka!K#NeZh_Eg8W`QFW)WXPe7Udp,>"^+o78c2m7O$c'3I4;
>sq7L,?8>Uq#?2n?na)"k>a*8B&`:Ps9.FRjJnCd,ZN/LC"m[RQ3!Q\g"cu=94n3W&WnMF%H
0%`hEj(&sLSK,e(*fbFS-q4NjP$kp`ZrW?0e`&ZhBmaEW@JH2ieIc_EfI<Wh&FDkFMR4*3MO
Ik=,!s)@$ru.rQ?>.O>jIe?Eb[u[RL`-hrM4?^U=6da6_Qg@FL!m)+L^3XVs5nledU<"Xmdh
=X+cM;gWkkCkLk=Elh.O8d5_0<Hi%o)q".fB^9g09NmN,+>a&Y8Epb4_gb8L8-R(`;1EXhNY
?n:l^3MW_-,E4'mXA/fUQ%H=+^8\pJ"O<l@JOV3Y1>L%WQdA;f#MJ^MglK%e%.s:hmu=7Y&0
$m[m?j@rLPS7>:r(87GWD?S%S`+s)RRNPs!?ApSA)^5*6X]AWM\$;9m]AHhP<I,I_!!\mJ53`3
*"<R.XfdG9FP:7h"c1,@4.7c8Z&d`."M<9=$34*XIg6W5:_k?9GK++ME&HTS+t&Irf"Jo>cC
s9Us5GkAf!qU<j&Ho<W>0o*L-p+6+)[*qa.TX]AkPN1;4l>6V!H]A9Z+'7k11ZEt$JWdK=3,H.
9c0e/&1Xu95r52>P%>0hS:P`__!F^A7X6^n019i]Ahp,Ro^4Cb<rp6WCK$d$<LY#p+aaaX5I8
)piF/h#\?;jCf=C^GfLLh:&)4;rgt/9bk;f9H@o;`#OtTsjWUH#:^h2/Dp&.*_E^+XB^-),p
4_9.F+=gEiF>*+324_0rrn$3>kgH+).\=uCFa+!E%!!J$K1Z7<Zt'1:'eKSGG<Z(%l!bk\&@
<QOC;Xjr*GD!TZKI+2`*XDC3k9uqH\%7;>@Ko=M@PD#'E$(ckFo5J0]A"DggaQCph%Ikts,bG
sZ$k3_-%aLgQ<g9&cd(.:cLC1]AoRAA_7D=H)Ej>%9XM;MUhaX.-h%Eu7?3C_/o)m[BW8-ejc
U-EZNFOH4'\@-c>o)14FchkA+2QunrqB+'M82E;[=2'#^0_=n9A\VAiJ,-aIjc5SL=h,s61^
8b";C'j,V=5P)YHeV#8;2LG6,CJu$P?llr!cbf,Smlfo$e\fGY>P[DpZkic^eT#o@(W!X76m
&A:YK-hTG(=d,!XT9+N3-`=,<=(.\sLP3ifn=4sDJ-?i*dfEq#FJUN:4kS>1=18]A;QK8cm0h
OcH5nYcGml(fT6eH>efohHSn9@EeaTV-0$/";boHF2TLea_gsEX-n0gmuh=8UIeHQ;<:N9HA
Y.`3rB6>gP,`>q:bDs#u!]A-"LVp:9s+J*/<F.uJ^l^2#T:[<mmuR.G`<,#Z:Ib/QlcVNp2t+
,,I)EYj`YhQFB')),QUh8$R/Q_7o+'(73st"G`HX[PrV(`%L9!VX(V(9*@-37MrcTqRB7Op\
FN.4>39VbTUKClG\Cdt2,I([4J0`'R.N.O"dGXYLB:1F)iCo;5*@*=l*-6ihsUL6r7#!6*U6
3-n,Br[XSZU$(B$gaU,oH`23,'FBIT2="M?J4dCR^6DH+uFHPs_l$=F^a6AHGG9-p+FE\a47
1bnBV$+aiV]A_aCe$Nm^.pK0??j^d<m<Yr;%\b/eV:K\;-=u#qg2m@IA^WYdVLOPIW-e+YW/3
dVp_9@#6>N9n.an9#af[Q)$b0mMk_g/&)e<+3s6tR=m+,tS1'Ff7+"B)/0IhL>X-p>f%*SY<
XSs/(#BuX6oT%HIe^F.sI:;oHJBAj32H3/P.OS6nn"8u)l#nIC7@?+DT)(MO#pr9SgZ4fQ[]A
+EJV-JHus6!Q46OmWhm&aAFgmWpI]A`+EHUO&@4F$lsf^F\MltVk*,q$"SD,r<mL#s$MKoMCr
R)#?9`;&&5CJMp'&RP#^c/\R4q%fV6p1iQ,5%5(S^d4(.3,I:X+9`,TNg+a[tq)O)Tn:UXcq
J_RHK<U-i!#KQeu<`>WJ*L3A*C7g-o**obnDXD'`aOUJ=Lb1M0R$G+(ZT[2na]AMV'TDI.#a*
s*:?iSHQ`/VadiKZ819+:WBn(F;W&LaH!1#I&Vra]AObqtTdt-Y6e]Ac6JKs4hkQpT2_%Y#/aH
]AN\)!I79dpGBNu(@:0E,tPNSP1_q!Vb&`9i<Id(W<EYW=4?^mi<0".-Pgq6_n9s/t]A6mEVtL
dj4q&WLHl,9"sc7PphONG(^))Y(T$HbH*CVhMfGPJ)9uVSVHo_T!Ymn\WkTan+8FT6O]AI/8j
mDJ@7"Vc7t2Gd3oo%UM(@$U/<b)7aWV&7AFN1NM99,MZ4dGm-%B_YKYk)qtgF8n."r^loTCT
T)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="110"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="371" width="375" height="110"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM011"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[304800,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,0,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[客群列表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i/qP@krWBs$+>!irJsEj_aFLdf7H!et3m#_E;`PY2@>80]A;b$6b./g/_$8UNcpaV,3hNO@
Xm)EtBpT-/h[\6jP&Y7ZJaI(ddX"kjIg*m*,I8J%h+lBCc"Lnf7I2mk`;?YE&mUYM6M2bfn9
npgCll1UGV,bKkrZ9U7,Qjcl1$qmPF5IG\?ZR2\HSO!M!%amIFAF51qZIt,8A)`EuKL$SJ]A&
a4I-f=L(E:ZE).hZ*Cn79]AOJ:X^]Ad"4kIlhe[W=C"-bCa.G7fT2Q[.p>l2&Jn&F(,r_t+3lI
KdI.7fghQ(dFG@0%"U7P-7375[8mLfinVboRN9Vh)+FAE=(IX'kch<)-l(8_O(cL@nD$r60U
GPGdF8T+>s`@pY-VZMHEhbSWJV4deVXUS%oR@.@9pIWFad#l,u+.0j4bKe5#?YoZ_"5GE5/P
]Agkc5@9ha=KPgCX>,S:g_Ta8u.GfT8Gf@Tcicg:[;F+(MpPOT1hKF-s-<_-Q7*fGdkFb7lf*
scmp>12cQCa;![73Cj'Hr-[AHP,El-,_p.jYPBX:goMRHAHWsHFT%8Dr_9[J2'J-Gp'm$#MR
]Ad[i[m[U%]A3oh_/Lm&S"@J7)9O;m0-Qr3ni;cpPZU[Uj9&EbSiX/).`D>-t8N3eQW+oJ6a('
ptJ(OV&?e/'5MomRP,G=='C]A1#gJgHDK3,"oHE3pga_9-U1rG%V8BfnVkktH?Y%?IlPB=@&[
eYAEiY;]AY1SXnP)M'0!8:LDM>h.t)hG:r#NY<`7Fq`JfhmoEN#@3/[&MI:3i>^,_\7;o1hm5
Aj>f+.?>L11s:8UMcC0lZ1r2_Lgn;(B1PCJiZn7`ApK-;F2hKt<RcqJ/&=Tq[L/gfI314%M@
rHJ+`*V>e<"Gu$@4a`_)3P[o><kGD(JHJQM_ruDT(q&BEMV?G2&:)2ic9:GWVTVUk&oiYaAJ
[d!(BO8-Q^'?e)E]A"8A^&]AZC(T82;_,B#k28_2NMQr6^afNS9>Ik)urPmU4Q7"l]AYCG#*)N#
Tj2#/m=n*7*K%P=Q=ad$BV7Vtu,7<+HsD]Al18k,D*I#_j;(j3Vf/3e..L)H.XDcODe^'^bsd
FpKa0hknpG>HmB]ASF)ed<]A7*m69<!AMZ$M8mY;VR)ru6]AOPdq"725&83Z,SWU<P`JpuDQ!qk
eDm/ra#ap3S5*EEAs*noXi%2on8"[&X!`p1FX;jl\Wc,f_/KA=g3`Y3dNLqZVPB+["F@,2ml
tGVOU=e8b[-H3l0_FhL1mgh_qj79kS!$:=d14fst_7T34/j&"m(T+Se!+,j0?j5[A%%HVdrR
A8o.>qg)@hUQl,R'e5\jLQ@VW(I'aR&H!Cr\i:5lM93elU;jBc\/9-$a2)N:sl!#)Sa?aT/c
U2G`k@j2XS(gMnN'3F._;=6269!Ig1\2?+dOZ/SD"2KmUTrp=kme465.DQ4uN.LQ"u3T>-oN
g_uCNW?H:#>Fuh?+kj%*gQ`BfL,CC%$&WUSH)1IeotmNj*a[g#PARju4#/P7m&)\+,^&\0b3
L\7bL,L8@^'O-lKo8%"/kZ,(igUSN]A-i-.%)VI&l":>SQEo&3+h!"/_BD^f2&(je[n`DAp^h
93l3F*<EMAIL>!QaAY-t?8sn:_tmZar_$$<3FJQY3qXbPPXZq6NFUQ+e)]A41PVm<?CsBcVd
Pr<Aih<T:JOQ*?G.68*'+5q#`uBNYCJAN]A@ZdqBddcX2&dBjRKTKYV,Rbk''?_;+(uc`8n9R
V,Z=j1tQ"5Zj^F)ojE>H=1-loNPa<SaGmpbSup8%26<Ze.8D(aV;fMp=F5Li6Q\]A!fH+pCt6
nC?Qr:*Aa]A4ek'FS/mE"&n6Ajlca$-.]A1Mc#&eDe^HH$^d/V`+dTpnN3I3fEe]ADKmWcZU[c7
=G*AeO'+83&"q.guV^rEJ>f6[19A4,g_Hp]A"p;"3>\]AX!U)e"$YKi!Gr(j5PfeJ]A66iT`c*n
-i?)t0>9;4akNmU`:d14J326Z1FW8*GCR<:MgaXX'VW'$!K\p<').(<:V/4\&a(8R+=@Lbap
gl['na@FIKat==<u1Wh`8]Am.S"'LprF4*GZ#sVQ92O;&]ACs,U)4^UYcBZ9ka,p$ijra?Tro3
3DmuX0tYJ"_pXaqJp.E5Ke_1a;u=i#6:%rLl+C6HuK.`[=EXKRJY_2O,&V/\tdIVu?cK5Rj"
lb_>n:TL/pmIb,R""7?Q-ouDZSLKaBQ$I;9Y4($/Q;[nm$jKG>HW-G]Al&=sINoBUWeT0l`<5
/aM7dGpKf&lSaja<O;/Eu;e'P6p.Ja`o6eMkhKeKG4!elqj-5EZPb7nm9@gMf\.W9[+K+<&a
(!K1/);U<H##*QQHZmcqZ1&=]A^JQZV7h;!Zua+Ji@GAFpCAi*cVTNDZOl*Y,NOsnI:lVu$"H
AU-U)P1CDQSK^Ue"mtB*>i4oZ$g6?1LJY+Kg)Xp_UD:AUs+'@7"NBH5Fh2>^dNoA$/,nJTN"
6.CIVF%<Hf%eR:oQJ-$^bijM*uA*p@"*b+3#^:`;bF\>K$o=E:MW8laPr=?=nqWq@A&IP#1*
Q`FX@&QS_8"d;kOed+,eff0AhMk%52ao(Xr[\63CDP3ip_GbHI!t/hlVf;U21$O05.Q>LnOr
lisgigcK)tdCK%0U099(.\>h4irr0;O#]ALGg?@juMWjo0`bh\ajdYeY,6r%sf:LgUj8&V28Z
S;&T;pom0!n/Eb)XJ!bn<9i=bOQJV9[Q/BKHlVZ^KrI%#*_p#.L4LA&:R<7F`B/2h,PHY`tA
gN@`e=8d[oe=+[+7X$_3L9Z#mGV*QWG]A%(USFX0.0m5tHf6#;^2'!S[VOC^n*-2TCdb(ER:@
\^]A\#DN9XFOI#^j=%qSRn+be#)f,F9aPM[`]A4qO,ZGP@n-h\(-dM(n2"jo/dgiFdYXOG3>+#
YFeVi^gdnW7WA:eDt7(""R10,1n&`&5OAVVa`'g#WNC;5iLACpK`bE88I6W0Z0oXWrA#N4DA
2b9am$$?]AO&mO'dW(fqD?&A9E-/ORBgEd^5h8C,]Aq#cqF"Qn,?k,Jpmt#rN.s.6-m`=NZ&\=
4rsGar2[oCEO>i:MF5%tarcHs<@A:/4l?K@!^"p%s<]A+RW8b#"qa3$t2LT-*K$9WVH5+,l*/
0j<:N::3i.8?^Tc\Vs9^;3m6H*q(CPBsaGii?@#fs=!s='s-8o(pPr;"HIaP_%(=>k8$qrj.
hrCp8dpqSr@hc\+3.O73=E,'p"[1H%pbm-*9n#*u5\GiQ^O9n&gP85Bi*nT0HO'"(BTBtr?7
O"ILAm*OrhiH,-a^UYiMH8lDW:.I(p]A19VC69]AYAXS&^faQ@=d3SFZ0A6:PBlf50*YfHLNk"
KER@eq-Di1k,up6`07[A(dL8S1JWPJA6WHG*9nJJ;OOkkQ0F?"2R7OPp0FPNL`V^\NW4"(%6
8e'9;RoJjbCS3%A>Z>_8!827;GO\1EKPXDe(<*?<"5IUl%e'@UJM_aI^T_>o*8s<I2`POI]A,
7Q3-b_NZ9AbVuD,([gb5`"Jc&.+G8*hH=o8N`FGdP3QI1hdAm4D:oIMt3cq.GPL,p5%GROB^
(!GX-5,7nSfODEQY1?g,Vr9)*2AN!dROGhfEg4n[b<>.OCrk;(0oC\$B&/)Pd9op#;G-#,5.
k2j2u9SOV9lE>==Z66FU[r_kXmASXnEQV"\qOMj0lu_=LhHR']A5OP]ALb[fAn*"[LuYks[On]A
1)Fb]AO?\aF]A60]A'`U#4ERL#qR:3A_EY&#h7QJMM1b3eqH:a1(X1ALZYWfVA%oXEF97i5T+r]A
J?+T24D.]A,f^%-XmZpB11X^1AL(WFrU(:,M1=5L\.SH0$f5QVR[J-7,?!=;:^!YUTF"=,)j#
tR>\?feI\fJAHI4$Kt]AoJjth9p[aY>e[s245ogthc[LE1lF?%1+FajJ,f]Ap!!*?j!!3^^!!F
DE!=1pj!YBm#@CV*SX3A4CM;tlrpqY8Wdf'nEr#G~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="77"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="202" width="375" height="77"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA0"/>
<WidgetID widgetID="71ab7a7a-7869-46ec-a42d-733b217a1d47"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="1" borderRadius="8" type="0" borderStyle="0">
<color>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[304800,952500,1152000,952500,304800,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[274320,457200,2743200,1524000,1066800,4099560,274320,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" cs="4" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbmc]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="3">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$status]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#"+if($$$='正常','6cd591','DE554F')+"'>●</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbz]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(FIND(".",$$$)>0,FORMAT($$$,'#,##0.00'),$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$dw]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="7">
<O>
<![CDATA[近一周预警次数：0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="5" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$reason]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="4" cs="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="48"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-9644655" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+4,P1eQtZgMXgS4UuT?07j/4Cr8B2NqiiBkDWk8lWrCRV,'p*48Zm8(#IHl#^mg,G%c";D
Em2e20Q=6c:4*-jU*+fZ`26S,)c]An+qM'KBmhdpNo,?XnuDiH(TGs\'@%gDYqJWIn3.&D6l<
8I83CLPK)l`HF(+mJlt,X-"U4qBDpld\:Rj]AI'pF&jAU^E-o4j&[/1?_F$g2qG4]ALnbVpKm.
r!-c(g_NB%Too\]A39!fa5a'-ht9e"V]AVi_IEFLaCNQ1TFsnO27;'%]A+^;jZp&tT@L+F4"$Jm
QoOtH$`/&JBtYs>&;"j$>a9Hl]AuS5ISsC4+bUqm#uqHd=sFbIYsJ:DeBITVRSdk`h%(Ul3`O
Qe/TO05_nLf1>^7?=TOjeFg&)m,&o@aqcoW2mKg'ps.SgI/-=DA=rk_P'u?5GMAX3-m!Qg+h
jI?e!?,ETIhD&MupP6CqTa&-p`?8pg6?hFb\+FTN/\*SD(5K-GeN4^5btl:#9&T8nnk3o:C&
@4-4UZk`-*1W,%(*MlUSE_E^$8Y*g%5>-\ENJsYB`QX^Oh<5jcXl_Eq%2C90*^Y_ueA4lle9
DZ-e*rA,ikM!q9lgO)p7kF8N1QcEf2qGL%q3O&Q3N86%A@M%cKB\97b'!k*4\]A+['&$6NWMB
L#B-S,`LC8)t[hu4S&A,PO4!(<SQj`$Uk*AZHi&"AhA>,c8<V+B#nfC7>V(erckCYqTZD+'c
++ad+d9ncH,#(b+R8M"SEB4ebQYAR'FCGG+EY=pa1nUhdK!rDHY*%ojX#Ti3T`:n;WmKlu@G
*n00:iMZn0t1WK\B=O+mMqYCLln=B<=fAU70XJWh,fI!+TRT31chg<Q6_2e-9uI[E7eL>pM*
GB":<Vlbi0W$fJ#oTq[dS*`ujF1>LO!TYu?IltsA)(Q(Z2FQuobEh@D?)"GJf0aHTim)W6`=
p+U(OWo^\G<i2Wmdh`uO<LV1]A;$D(Zi=0g6L1PANJ#TB64E?Spo&+@)u#>jZ?<\kirS<e)HY
?ka::IAhpI'G%AHY_$99q9"uhuH*+cHl@n:=ddVU8F[kict(?VX#^<^+.@K_Wtmj&C#liji7
ZA8sK6^04/B,eIsWna9_9K$W"%ml*`'s`?N$GLM'gCRmo`kuKL$rE1si.[#rZ#lOJ9fWr85o
h\]Ab"-u:q)%2:Y93u-ZOs)?jd'Q4.^uAVj^IQ-Yi=3Ko@u#^@p;:KQ!f[,W.=$pl'\.=0lk5
E+qoXKpB\BUK8g_7,%`/]AB*RZrk'.uTa6o=%(;qQKF<N!p>M@jeGZ(),:HlQF@(J%\]AGrR]A_
>%i6TNc<%7KX.In:fr5]A#Wr_pC)B)%h34EgYNE(lC1=J10.)?Cff@ERdq%J^\(q/XFM2I%,]A
H"^BfHb'1C"Yg<Ga%L3`]A)?e4^53cpe81G'L5Z=8"Gd\*uF+t3-Qk]ALf?q=-.Cd%P#niRm]Ao
3FgUd8_CNL+i501?KH`)(F2\@7GCX@`M2iEMq7D^ge<@tD^r$Pa!d?F4_3O,r,a$`ni>+Hfe
<iS.^KbA0/5Cgna&f!L[<n"kE!ZiOi9M&c"Ij>"Lak%iVBtF,-S`rH$<c&4n`t.o'OhF?_`;
mZ70Bbah.nsSWGR,ATQY"K)1e[!G^lCR3:I_',`d@ps^RMO[N^ON'cG)Ta#ms5\mGsX(W,0l
GAoiWQH]AnJUIH>5LkP*]A?+iF___rFqqn=!1U>al2J:%VC8YC\Y%B/Kp>MVcLQspdcsZ"&9iR
W0[sb=WC'HB-eO6a.9QtrT8171jG*?X-WjTt646LGH14NKZi*r7&hANl>eWe00%5V]Ai$0-Kh
]ANq%arkc]A5rj<?H94Qf?npc^HYNG7\9k(M=\Z@cQ$>78Wq,X:[;'gpGKu^4R-TQ<i5ZcQ":1
e8*3:inYasJKh/Pn!#.m6Oq6CU/r^`pi3#4Y"gSP3W&S\PsK/QF[r2/Vk#[FT#g6@tT>no,/
bU#[cMfiU\r2FCRk[>_@C=L4ruFB4WBD)]A+.6[;cl?k3J%(W?R;WcZ<GaW=(I@A!#*_p0Z+P
Mt5=k%h@3bje/R^#I)^J&.8\kM'a837o>\g*^9bY"ML-g#V"984/aVoCARBa^"#&S<0KlZGr
1,$+#!]Adr,>"4s@F=*iA3I)g'8XmTD5M!YKt@YX<VD.Y"sX<.=nCk+!Hup-%no@4oNnC$+r;
<M%OIp@1`Us5[+?ir:dX-;\fX&cnpe(,dP$CP4St$n"<OnZNaMX+\m$WSLZ6W5W.0^#3h="`
9r7[_W-Y,L'$S`0q>-R]Ao<`j<a-op#$jhB3=[91C]A^qeGCWC+0+<qRZ4/dpLm\]AdAW+m*k!=
"7[gg[CcOEREoEman(h9sBaLd0)Q[)BH$;mNA$7a1.1RD^XX?"0EX!?:BW1YV.>%k<*/gK]Ab
ad27Mo]A?nM=mi.`Z0^8X+V?qHI]AiIECsDR.<Bt3:n$ui+aWF%R6o"T08kWRKI0Gm+FLS;plI
pM4WXbifaR*oW1AEPlnjP-)6QEeo@pDhY@,m+p9k3fdR'poD\fC/&8Rm%GVIHiTS#uR#94`-
.n"CJ81;$=-TJL9Gs2cmO+aRQ\>ltqTf4=8<lm*DP(;WKWs+^]A7*N+0<@qjC85Vd&D>XgU9@
1i/oC[ZpRU-=He-#k:>!kU[hFL,q]AU_'%c%P+`3?+P28HNoS5#E.b>pWuGdImYKU`!Otc-HN
3Z&@WsmW^!X#s:IbTGU!4r;H#!=qRC"-"+]Ar+:U@QkfV+M^SLbKS/]A(0@3_d*B8Y[hG4CdAT
ZJ,m"q_>'AC$b+hs)<C?9;)3k$7*bA,<CKVk^q_hLRi:OXHU<&EYbQm:YJ^(G4%>_S4*Z?O>
,IN(<hgca37E^2O*u-gZ,3r_F>3P,BqRpCqG\9fE_h+Q8";Gp=keX.C4@$<+71^j@r(G['YK
]A_b]AG&OF[(pg7f$V-M[$b:6d1eOQhnH,k`GoEp4dO*['/2/uPHOkgfl&La)C-.sFJL8%$fSu
(ff@OPZ`E`Lh/6G_@jEBZn$-A`g'!\k587>p'GArl4iNXQ3M>-RM.^))bj#p%6U#$c+T.t7'
kD[>t0FMZo`'Md#sDS;i#LB)>i&AYr8_\KT$ak`+\G^o4;s2OVg'7^SFLfRoe:NX3@<YG9!H
no)GDc&t$AV]ADucS$[S*j1c]Ao3b@`IhnE+L2jbp-99neUM^a#gjNI8N1LPI;+AqEd#eXj?NP
<;,8CF)-D?)#FRph%5/b&ffo&[T`>4W\&DGFR?t%$WRZ<(S5*5IX_cU@70#?Dn=FL?G)0nL'
&<$#&-n)36Y40/K<O=o#iV&5mTO;[XQE+U8F,o"G@6e[TpbZ&4]A[43R[UeAs`JC-(-U<XWc$
t8TWU$Oo[F8'd3B8/4jGK^teg0,pD7^s3<j$8Ho]AJkM/@<GG]A:i*iZ68O/1e%s1fl!lCY(:I
fF'K^F7mLo!\Q(;Eg@3!g,J)9Z#OTpf+0#f6.ua"OXLqFr5PL22Idg28lN??)-!&C_Bt_=B2
VW/"N+2bN&O8YWUq^'CF$H$/1M`t"hgeh6OXd8[6m/DhKV)%Bg1+c&98>9&3Z%i+4n0^rMWO
_#r6/9P5:E:[3WLu/?.`7Z\/oqs_'=Ype?PT\.lfGcmqOBs7oQ!'p)DEM:iqci4pal]A]AVO&K
r,/?R8F5Z3(uR9o@hC4UU;'2o%ojBS)Rn^d,oquu396[.K%-TT@/K(\)Zdu.+"IJC8r5Upi#
,-V-6O1$L9o!WhX:m^MTd<iQ>>snD9k_rB&7)s9RX&#$]A1N6`<9M8,[:;]AT)L^-H6tc5)L6%
h'q=HP;aH@>C$5`'FFnl7UR\Itb2hZ^kR97r;=gf.C!pri_N<J:(LX_@Hg;%H799$18^koL+
+Z4Qko;3RZ\duE@HK<:o#CtHMRK.q/+cSJCU6a;]ADZWE:>hU:j/lP\>f.t=E^Uk4.F4s24f3
2h4VWm#Hd=^HYPE'/?l!p+C6Cbg>JbMZWi&16%?V2pf2R,e2W415[bu5!nk<uNGN5)4>PTTE
euqcVe=9$cAio"nmKaQgDD.bj-ZkG(V0`2:ckT<U3P>j(FTtceBF75-NnDq3/hB2"1@Tt4>R
'IN58Jusi-PZp1dFP445NLm#FJ=]AQbihdU5^h[Zs#!h5>_>SGc$+@(<%s6FCSel=02;6;:*+
nA/os9$hrD2RE]Am5U+.jmo@s`Zc%/,40k6oId.G]AS>M$EnH#54u/Eieh#VrGIRsRMr[5rsPW
Vm,Y@3fSL``WX%+>Y6#bE)J<G]At9a4GL./12+NTpo/aX9tk!_>hud$`':YY$_jrD5dtdKigA
]A@*T%=K6SN^6/IoBDd1b'%mkIf/=u$*^qN9$YeT:3KS\R6dS6t8FoLGt)DH"A!%dF\mD=-C3
$DQeaRsRUC-W7S969@56F$_op&,'W5%+sV%d!0$[)^35pHmkshZQg,"LZ+ZN1@@TF`;$oo1Y
>Mn7Q'd''0W#K_fA&f\:o';QH#FM9Y.tCDiRA*jSMiL"WO<oS13>-<S)+d4n:=7kC\?NWpst
J6?-2]A6!"^N57?\r\C74'=f*ga!*bbu:7ft9,Z_>#H0qiUCI@=,[q=T?W#1"]AW)t^!/>eQW]A
NRoLj+*ii'H#UR?WnV,q,G@):AfF9m(*KOnDhN53uJ7rFUA/?'7-5kC:73_$h'kA:Mq,`o:$
JVg.:m0Cqa#lOaW\j<Y`-U2B/5^\fI\ZdW;H+O?ddjCjUo_\%7q?ng7&L4?:5j63TjqY=kM_
nJY5V^IV49W2bAN*dk-,3i$o4k91pebr,lO5F^?M@jnE?r&,=oR+85?h0K=r@XmlDGX\lf)0
T(fPb(2`-geF^)3%Fq]A]A9Bjn;WJ*"C"fWh7]A(j`@'jQ/LmC0?hRh(]Al2Mgi^DW;<U@]AkgJl4
c=[BnIoIX+GT;j@"PbqL4M\-mabFkC,iY3`JEb*Hd03!bfNN?:i(nB]A+h;n!;_;fYW++!MYe
atcq%ZQ2J\q^6r"0N[nSj$IAP7V::XEVPs?^GG>7:mUdf2b50i0.fidlq!6(FHP7*d]A9>0*\
4.`5OgtG1Z)2@u:6%b;O>bosEVI9erRUgLJ'dF]A#C?8:V"[+qT5KCGPl6\bl(uF<Wn/P>G14
PC^EejMFtdSRa,:Rn-HDp)<Opr&^9uU,EU19nb*E?^l"->Z$aa=Sp#1'KQh2$ultHn/mp@D#
UGM.o'n^*fV+@dQDNr^*p#-/td-q/>.J:'S>*bic%<1'T.SL+KVcLRJKUjC[bb8#W[A)kokL
rZkb72&TBuuZBGG?h:pOa^QIOOF$sH7(8%Ft7m[;ZJ/".2:h$:k5@F8-bA@?WrXH&j_ol^hP
?1]AG`<%)W1J,3$5>R@Wf2]A!\ZAT?0/!6Ssn5"O9&";u\I9F;gW(b[O&La@^>oV9TePI'J+n$
er^+^fnHFPKX1W;';30lIT_5$:HXl2h\dKL6JL#q7skJI,WM*)!IHXTZ6I-gPZ:!a%A?JbDo
CR2l5P4?p/n=358Y"rj`WZZoE;FCq*_RN2S`S''6+2+3abV+QC(_t)@oKcZ#RX9f44S#:[_:
;UB9/I/uiJ!"_15QlGKU7]A>a1'3o^7>&P)=^C!5C`!RN*cSVHh#b*-<oA;o]AL5!3X4`doJBt
rd172'n`;+.'ct8EhI&A*B&p+tM<3K\As+T*3'`&SR8Y8*5DGFD<;/tY<\Ze![NH5E3gpVQe
9Ac=FaB[;OX>dOnT2X*d\`c/_7B42+Y0Q^qp^&+14(ft8QZ%-XmL?I(57\_'eRA`%QS-H!lE
?T2Q/OD-pB?NQ5XtQCO/S$M`C@GLE,&+X5:C_%4Y,KiQEr]A[IYAs#W#":5e20Uk9b=M';eMZ
WOO/TV;l4'LVa_`mXo&r6'C3[h7G&Fl9J2d/@kMOO67b`20IKG[XI7NgQP<oI]A9ig%J4>0]Ap
#Sl+<kh7Pj(4oke'6t#Sr+ueD9Dam]As)R^]A%_Bc8>5Gr+a?f\[<b?h`6j`P)_??bLB!>lKK3
E*]Ag[nTL]ANS]Ab?-W`4gq_Rm]AIY::AK-MgBm/PS*?V4b[NcA>]A@`qi8l<Rj*g+obYs:a*CjH+
gX$<iLJWB6:"(aSc"q@$:BK%%l^4X%i\P\\f7>EqJV_pTJrZJ!k%B)^sA]ATIkt-Cb!"P.$Qt
tl2p!('Me7FdlTmeal"="Xg(]ARi(A8;6dg9$s]AP<h?D+RpS^<^fddFNsKn\b_i.fZ0$)m`+X
/2k`8]AJ+s76Su'B?gENJ@p880eZj&[V6-k@Xik%;n@stgjGq[GB<MY)+lEI:Fg8s[lXaB%eS
j0`e!'d"`;-Z]Ajl3R?LW+J72#R6To9$W#7G57.3?,s;>?4Cb3kt33<[lAc'_OBqs*7F%3CtS
"p3Jms:Aq\bhsZGh+9$9X=^"EDQL*A?!IL@3kCI@/I[R-_?\L]AObPo3493-%"kb%%>]A,S/g@
dB:j/,uSQr[X]AMBl_'2T`]ADCnMNS(`[(OSBGB!).5Ft+3j0L847^G6/+uj)\Cdebh/-_5b5t
."@ri;tHD)*C;I.)A\qo(B_8nR6nqOFcgLj%10J@emT4FsjQ]A5*PE@!FF\-^lT*jkIf.]AbTZ
.tiS<g/Uor8gspN^`'/*2c(<EMAIL>)iU-%?Gp<)5SNfs@O=^t7&4BZFmAW`1SsD<t,Pmr
e`(@7(3-a#&eEhHJI`X95Q-3Mm?raMs&'&h^uE8K6h.p:Q_Xi1J/<3(,f4iGHXTXS#eDH@jI
6XNO!Z8!9MI`f7N<$d#Z,P+'Ole&kGk"C>BQaGV+8f\,@:<,nmu:dXS4U3c3*fg"*2$ePZ(o
n*T2=jj0r")$'>jK)/[9QJ,6YZ_.>J;]Am\a#N<;3-[1G_m\1u>3"C.Q^rG/?=7Qh_WF?5a/G
cG,TkQH6/Db:[IF$'$B2IbU0.t7f?Yk#RtQ`HjP:iNkde<Wr&aa=>.k"3)f-u"a''Y1jsi;e
^Jkd1,,c$t:kbJ%1CJ:"5:@!&L9q,-4HH_s,o.AMP"@M&-<ti90?b[^!Lef"+##GdS*Qp$H`
5?^!K$DUPMD)ROD9(Y2S1lIo>FPu/JhScr)U;G3S#h`cua1qG/%;Q@,11T_h$*'<5&rQ1SjY
O^ql]Afl3E3tW))=j+)aloLVu[<5Z?3Fd0>@LKE:2f#:d:HHTkKMBSI6`$A%&0U9u?3^:)e%C
63(%_Q&Ea`NF,-[taHiW!i).-"8g#fbYYl'l0N**/U4LiQuPlc8>?tR=j?V)d6IL9YITcp5%
,,+(hL,<)sACM;?tm%sWchlbUYFpDYTK$kU-Y0CXT%F(2K'"V7^Q8Gk1>7K>eVX(kWq'3^7p
XEt1`He]Ai9TbEmd*FH#BlBniFIVko]Amt`Q\R302O%eM1AO&$?jPar^AcPjOOT%c,#?lf7jVe
N?jKJ6Y3f_&n@s$E@kO5#+@]A+[1DO7)dXF+0TP/gkJlq<hTtBR&$]AG;=[D1a7sbq9PkBa+"!
3s8'\\-n"VKPeQ.,:3@>]AXF@C9.e<PdLX\P0^VlB5FiPt3lMC<06:fq?rt8]A(gm9]Am`t*P8K
[_H107?EW8]AVOMKIe9>a:be3/0R[-'!d0A+\TlhZ@r.*6,"rq_c4@3Uj8\C"jQ^D;Gn;grjX
WUO5R2(^>SF&?\1j7#q,g#TELL0&l8X%5RAh>-(kC*J.bZ[8jE\2!WRYbFlU$;&A:h%Mq*<#
-QT<>J%BmbIfK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="73"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="129" width="375" height="73"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA2"/>
<WidgetID widgetID="c8f1fd1f-80da-4165-8e9b-f4deb653a5cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR F="0" T="0"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1714500,1714500,1714500,114300,0,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1984188,4240404,2819400,3352800,3235569,190500,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[分支机构]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="1">
<O>
<![CDATA[数值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="1">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" cs="2" s="1">
<O>
<![CDATA[近一周预警\\n次数累计]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A2"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="股基市场份额-分公司" columnName="fgsmc_1"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="4">
<O t="DSColumn">
<Attributes dsName="股基市场份额-分公司" columnName="drgjscfe"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="股基市场份额-分公司" columnName="warn_status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="5">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" cs="4" s="6">
<O t="DSColumn">
<Attributes dsName="股基市场份额-分公司" columnName="warn_reason"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="6" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[len($$$) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-2206385" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(LEN($$$)=0,'当前暂无预警信息',$$$)]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="A2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" s="7">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$px]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCIRBdVISqD"DgOg=G*<2RAtp)Dk/eF!PV3/*Bg:*3cjRR8]AtRMEms(b-]AYT73m)R3Is2@Jn
S_0VLYY%MFcl[Jq/`7LrbKTqnCtNIb+A0n'CHNF"Pt+hg(d-CHAZj30*UiY.O#iNBdI:Z]A^L
"`fhd!5HU`g7X++gdos_SdA^cR8kRs<:B#/"Gr9etWLOPA$o==X>j8iRri]A6J\lR^)H?+!KZ
GH`)m.=qRVHT`&hrW2-7uliHh"1?mnP-&cY?:<fd$EQ28hP#ilFqPgq9Gu++A_l3%<?_Q^1!
V"bJY'!_%87Rbnj&==*@X\LXC=M849CIYeUnK<s5q,EKI7*4f)M"oA]A.pnSBZ(<RsU^V@X!@
8WH.6g"2mNW,B@6]AUs>HFR)[jK;%*=6%@HNqEi^bZEE\5p]A8t\&!KrJe*[\UKGL/,$>ZaZef
\"p<)PK[F#^.cV"!!J5&3]Ae$n\XX8P/1>geYN7g\@_WSCRXH?4);*>ju(!J>mM8LHmdO$W`e
-CO4fsKMhkuSa$XNT9gLe]A83t/<5h#`gi(G!Uc]AaA\pq`ggq$Un'U0t<[43Ip#IM2<@iJ`VY
nrpfNHW)NWu2"`4P^!(_,9O\qqcd.>2]ABVg\B`'qLpon!"/4Y?RMu=@(IXrI)P2<S9n\,V,V
H,q0jm"Q>gQV`Ie2MiB^(.Hj=E.bpE+DC'dqrrlm87FFOTn?[?tb0D/eBW[/,o[mOtR6ntU:
j*Otd>o$C^lAS`\ZsaX.S>4f>BCcf+41#A]ARAecP=rJMUP(0H!eY"J=NrZ/Fg'&1k=FRg"R7
jGei)f$"MIEYcPrVl(gYFm-#$qc#EOR9\oZ9<&(68U>S%:(CWNm(tS"LjuZ2r(R,LNud]AKl3
LoYm`2.R>cH=e#Im--T^o,M8G_X>8DtMC+&L=0)7".:8XigGPd3R-Upjo2^KUNEa%$IWr$CF
.Ll+m1Me0c6"A^C#t9Z&m<Ju4NMo[B[CVU*%;GelG\:-M`GqGNKD\hB%d#T8kR^4IW72)pjN
)Eo#Z9>XnH"]AQIVp-KC.jBK=$-^%;9(-$EtO(%;fAjn^l(@P%Zd=f*U:YBl?[fo/./ZHe&Rb
U5LaKUBR?)?EN,epo.h;[.%<mbed\JGC[(LQh/o:p;0F&HCkl5We:MI##"Bq$0WH1),N,JHM
YG=0W)U-J:>L+iHjH]A($k\Y<^2_dOPX8LG6J(gKiZgZ\AUh-3ir[P$eXg%K<SUi:4&GF-[-6
;$;p>G?.6('^pr'-Zu-)LGQ:X6_Pi5XaWqpFo&lCTS0-<W9\egfCTF6h4SO^#rQ+L^$Of>W<
3aI<^aU%hi`;*k2!\KN$iF6#l_1OAiZ\,G/m5is6GnPUr%55.<@PI%=ICYi:0;kM=PJ":bag
LHBnZ24'Iq*)7809h2+k97E*'"gi4kjgC0CLkl-T";ni9T)ip@8TSk(7"!apZJ6NqBmoIR#;
;8kP4=48%k6nsHEOI^pTen.Yc;2>;?raV4rPS':+?Yok2LrVD`-=6W5c6\.L(kMq$D-Tn9;#
b4([@8=is3?l+Rbn\li9fUuVr"i8iT\CWiBsTQMpA'%B5TQ5J$eD?NOHU<_ek/XZG_Oa#9VV
I>&?]Ag<jCc!is^-0b>`<@hf50H_oo+sStpEPC"]A*Sf7OBk!6@u)VaF+YR@=@=H###)qXJH>o
Y@^_Ddq_m;Z^THe5oK%SEkQ="Gd8H@bug:A7nuMAd>]AQ,=K90]AiC?hOmJ,NP=)Z:Xf8ro2je
AXG?[jhjb%2m1KH"TSaDJLKmX8^Nd>9nl+9aFNW!p[,Kc1`k(N_YnL?/G_IMf;4.koVqF3>m
/nnsBklk8Q_+/EFci2]A>gM.P[)c%^=RC7hfcFSQcg-'9t?dVhd(.P)$bel>k^2,+or#gb)ce
h*#ZgQ<.`3t-\MW.brF(lqjV]Aq]Aci]A4aU`?3ZUAl9-QD$4dJ%_(s++2YCkSdn.J1&Zt!kj/q
W\L)^_!+`bqqW`/YfRb%]A?-gcZQ4Wkt:Smt-mN!2VP1Yl#E#2J5qb!!c]A)52^2d=b:;+o#'K
,Ab/`]AMM!I$/kR["rf_GnX;]A=6U(J'ApqGG=fook/uCqi?X:UIQ/KL>?X06MQ?YVb20g/=8V
:8.o)9.*qe]ArgM2"j*;r.<SSXdL&*f]A"UVPh)pXqD<:^<WW9R*47Z]A25VG,&^N3cPrm>)fsO
!hC0X'gUTsA*e.SLo8/+=a]A3HQNVFY5g0gE$+ZQe-*ub2GjC#B2fCX'"59<k/?RrnjuKf-ap
p\qq'rtQVd,ZGgF@*;YlbUbQPb+?p^PK5aS1Dpq&(EL_Z![sdggR8`935DT#*UHfTPYhURl3
iR6NL(np!(;)Eg!"P=$P]ABsDL@dCd^&%\hZl]A#R1lG?h>YLEB'_?["<cK)0Jl[G_4\Nr,l<4
n(R,Y2\EA'+'>KO)jRCA)5Y'fV3rSN_:JG5fTq%&#0"#&ZEe1nW,]A6`#Y>l>"rHY'<3Lk<1=
M961j=iA,b42^JfC(NMj8?>tGhQYeJuTp?glW,em'abp5!_8D5"Y2<kP(Sg$m2)p6UPBVX/R
MsY7Y-Md^[ij%.Y*LDS4fbr*r%7slZHsr:`Dj_U]Ama-QCP9js(L*V6S3Q#2XB\gHh0m8?oHZ
5_h9J_;P1^"QcIrGR5;4SSHj0l#LR5s8.rQ?!5[3f5`Ea,tSQ'>69Y0:6iLGElB-#?SdG2J%
lk18%]AXNiQOoD1P\$qImLl%&=Jl_@%b6/a\ioeFlI.2=QpG)=DKo7Fre@E#89),j_9I.j]AIS
uf8(3Aj=^N/BbC;P^@1^.Sh:_=N^J<$ERQT-_QYLhE$ZD7,U6n*,=I$IA\H"/J]A]AN$bi<RWI
jkHY'JngCK(*1^RA\AX!H^4#9RGNL(!.Y&2$uL?GeC]Al9-'=#"KtEjbCAk0sTT&%b!@,]Abl<
+f5u#B1CC:6*Gr0;7?FW@V-h[jRe/nH<kF)?Q`4A&euFJg7C`)1Sbi%_`.E<)_u:XqV=PK'p
3L\,_GiCRd6Kll*_sG^+-N`^A$#\kS6D)]A\Cof?%BcMatQaJb778trCj6fk9(aMYH.XB<B!e
,$VH>=jl\+3WYb38B1i/Hg_o!.]Aq3$KIM2ifdp]Ao[p4\R]AU8"8)MC3m(Z>kOS7=#,P5U)Y(5
j<4Dle@O'$nqPCO>I=!bmftmB2';3:5+?7DQTfI>Rf.cWMAKuNDZ/;HA\Nc8*Gm!,Tdpel-N
TKET;U_6L^;W<&c6pA-m8J(7`6K\-pR8A?t32'?03T[aIJ=n@(&-)kb5?Qj?5X[k4kj,]A4p6
+78;o:_J5Ps-@ZuK>X0g,ZZ7M5V.nZhW&A);ONPZN#rV>l-+OV(_u?7e1,iNgu=a%2]A6%Z&?
GtYOb>FU&r*7:b!K=+aO_5IFLkdfYY/pu@,>lQCn_h[<4gL9&%*?;1$[qqJXY"akM&a\%MXa
5hpN:#2]Abs&!!/sjGY)5Wi_;RYjtO8]A'X`eIH*g71^N4#34'lJ<482?^8%0<h3S$(D<_?gZg
NgR[F2?e@g#oAgYYXF-aFa]A^$)8W2o+cgV@rHNtI"-ds$72Nckks1L76OCRMV%j+ifXi$lOr
]AWQC%bqBBVJN`#!h+JPX'^A*$(J"*okFISDTQlo8>,>AJkYe)0:mI??[ZVVu'm_P+L8FQd>J
T9Mq:.>l`CfIUs]AJNi2?Vq5Ign%pOr;lb5\1t=S:mBieCcLg[Fs&9Nblj.`Dh*Wg%J@CSL#K
%]A!8QHZ%nMVQa*Q`V0_<c!W[\Xg5/,0;2?oi+uSsmW<UuUd7;QE/[4W,@D9J-3r=7BG6.*UX
[s0Le+XJo2jB]A_[D94VK"p?8ru[)+f<dtBACg=S!;"_e3FF#]AfXoF<*5"-i-=jR8)l!MCAL[
l?`t3$?+jF]A4G>7ZcllEo*4<p.ue:ag$W\mKOq!.Q=(+9-8%>WNB_6%]AUs8-to]AOeDPWsA[g
;_+>hp?Pq(0P;\%.,A2b?QK;r<s'343iL/tXScUfIK*Y,;?kY%2CNf&WOLf?e?19)BMHuj2Q
h/-q;-,oW.3oB9_B"Q>H!Q)*%Qc,Wkoj1hl'73H%io6]A\O!X6IDs>@r3ZIM6kZX5:\<Z>n6_
S4^0a$nPNaf%8^/kqI\gkgCcE(%5F'`Z)!g8PsS!!'J+bW^]AhS*XlrFUA*:#EAfjhhhK]A.O#
B9VP6rd:]AA`$R=:+a;h>9U?uL&nr:r>%5NOU+;/lqFe?6dnC[EecaJn>[AmXAemt<p<(P*OE
G\ftA?)Q@r^1EL=ZO4E5iD0HWs6#&!Uo4^D($er5c[bR3RBINoElFcjWOf7CceQ>0ua0h:)R
J%'L)r[`]ADsLp#Glp[a7G%34<Bh:GRX]AT*:1.Bji.^h<Z-R6!ikkE:6,%Zh*F\]AR(`Os*JFV
S@&&H$NRJcMf.@'c?bU#/_VV?qaDO5P&;Hknc:`9Me16<_7B=`,J!]Aj.ccc8JG94Y`mhQrSh
J_,OREt%8Fu5nig6^7)1?gH>JfXI^E>rc+tKjP49dE<UR+1_k_`O9ZkZ64k@:fMM[kFQhGC[
@lIiQ(MLKdFn7\"s^,nfNL>fDVh0)LM\Z)K/`*3I^q6:td[T'XbIn2L+>8kl1%o)FE/4DbQX
&QN8KWP++dlYJnIFd5M[3J7'8[I"3c?;de)%MosMe,c(Qh0'Y$:Y#!'aA!DjRS:n;Aeqis0t
)chIfXX$X;]A<Df).9ShA&O2'J:LN*61c>A4E]A]A$G>6c>Wu[P?)q6_F<'`J0/r(+BR;54`*9N
<:jPCLE@Q443$F]A+8adP(5@A]AQ+h,G:EQ1?W4oIN,]Ann<;GESV)9m`<EMAIL>&Ei4C,+tME8
Qp@X##+gZhN[`:$?s7<B[9`oZkkM>gnf,6O/Hpi5<+<TgOkQVM]AeP\+RPV/$`ej@%*+.-%:!
D,uju9\'f$_:@EN/1A0$\\XKSUSlXlOj!d"-(sf`)M'FUVgKGcQ3XKQt1M#b8-sh@!T"sqF*
(ph^n%U#1Ld6X3Z!=+kkF2#iE%^WESs-$[".k-<)G(X,K42`opC5&)U5FG=RP6_r9VR.Q39Y
j'SOmRV"oD![IjaRdD6IcfPe6MekN%)GaRB`LRA-gUF5^kKjBk8HpKi?]A,`;$bnleaSJ:sh"
SA3p9PN'D]Ajlh`i)J)Y/i6pcTqTL;ZSU/1*i@JaFr4G6C)]A\FT0!_rlrBq]A;0N]AT9ks6$NFS
6%jr[A'0@@rN.<&Sp2rTc8O5PR_hb5Iudbohe3'Au\R\,K%+m>%:gg+3p_!%KcU\XbWbO2aU
PO4]Ao*-U*P@Oe1`&CJ?V=_AVCKU#e1BrH^hFUY4^s>id::J@g5YVgVB4R2oBRH&mq]Al[p24o
IY,4l[#pK[A\8A9NM?1]A\2'RGb4)MR/en2j*Lps^c?\(PDNA]A:?ct05AoGS9@I&<ZguD/NP&
<"?R^a[Y=_6VmId&B,;;9YP@o4dhI\$oA5-64s&1'I1gH(Fg9nXN;l36@na=kUDt`V]Ajo50i
Fc>NUR[]A9Al70hP1qY^#?/Ot67asa(S5(jJs,=.&-hM)iqEr)K+n'QD(sA=.,`L>*3I)l/Nb
cB',i7?&]AisT"GgMeT\$nF7OJg"hWq[NT9qdD<pY%7#0LO,a._@X,4f!iX5lb(OSU$LJPorp
uOgtC,HC'EJC9pLQe7;jc[K@_/P7$.<5,-#@;2KVoMK0pBQ?`JbN4ooP!u&*Z:)E_`\S\744
!psE&V?dJLX37+IjkZ':=.)r4feN&G\4U=MZ"Bbe&e1$h'`.-6oWh]A9q8DLeBKL_hgCH2KdE
8Y#uJ*LApfNsBP"J'$`bEB!W%Sn=0omedHAd2p^h`Zp.%ptI[<5F5`0G=5C@MB8B:,8JU)R.
><p=ll=ULUa5J8!\'nU#Z9LYaJM6VIOu,t)5XkC/T;9$aa`]Ani.T@aA)"pBtTKNNM!QJjj$i
9AG>#tH#6Z,D:44,E=r?/6b/+dZH\<S?SoSl+'$X2hFdg3=R?DsbE^6FB,`e6*6$3V;3)0\u
B:>?&k6R]AN5T5d_2]Aj7X]A?Z'S.-kttFdHsO*.ai.[c"PRD6-I#jncsiIlE15Mf!7o7WZ@^lj
9q3dN2*ud*V?q[<8E5c)`aKO.FkDh:DASY4^IbD-b9;:r;=\qU[<")cbGlf[,okePa@HjfFg
5bhLFi3q?T##MP[u\>r.ZEaaU.3h>,&aE`P*uSTs\rS,(4W-5qT88!RR%38ZmP+<1?iRMtB?
q+5:]A,(&=kVJ)'Z]A).:0[5)N)=(:<b;?tLDVYe=/QlEP'QpQhEaZrQ-RUbO"6Rl0+lqY2JQ(
;IXA;4qp[0Q(bB(M>('83A4`ig,oTUfUaf:28ULbMu8c.T\4!eNf*C[<tRB*kt_+2s@N+/b4
:j@N>cUYqn)'bY$+2o:%BU("`<0q979?`D*hk7^@n*(O$+4mcSN?H,nE7+MOG`Fm<dh-L]ARC
OuB<a1V'b:QCt!pVGq+KhU=n(@Bf%XBU.IKoq=UM7U`LVOt=dn<Hu!]A`m3$I@7!?AX-IR0:6
nfZ>6+o53nt6c5'F09knu9__Nn(+HaMi_U%>2.m=20+85u2"Y,2;5BT2m[-3Yd"0qXSBPV:&
R[B]A\f&5k"VJGOA/S!?@qVmBpWIoZ0NC$`mQc-:p5/KM?1(<:<5Js3.>f!$`YYW]A?:\Jqf3l
-3%1+P\FUd$+S'2>og',E,O`'<-l"jg5\2?I)c]AfN8,%6c)E@qg\'4ibb`_pH*Hkb\FEXaFc
NN1[XpFL1%2Q^@9-14U+,(]AEc0@Fn>o%WHo2aiR6ak.Jg3EJ':7oAOM2p]Ap>CVA(fFf:^n"M
L=h9+:c+3phd9K8^g]ATR6Wt05oa0En0VI[KOZ6of\bICQjt^cZ!thujN#n<W1p?\2j-3UHTk
e^qnO_&1C'"P<1=`KP*35bQ-%LqiuLYoXjM?RjD0+//\(X-B,<cWp(;-6IW?JCF[KOo.WpCM
j:MK#3i8em*[/'NFBlZ#$.U1/RG4A0da%RN0s-R6o5.9nfU3'%UeJ2M?Zl2]AZVFDV6&-cCo9
+"X^/(0.I-*BoGW%m;QeHlDfcq[s;G_JSj^mT(Th9t'B*msudCED$Qf-5.-OX7:!tpf<31mA
ZkPitQ/2tJ[,5^sqY8?^Coq"-:HJA`K&nJp+Vut9H*VF/NA$TNCX[-T"X)B4iCb1,D9oY55=
8tAhb2p'2lV4s><EMAIL>%=AGKh`sPBI9@ZOA[Jh#RIN!s(B3`CK5Hq,O`2C
HN=R>r=C$ee![+/^^#i;iJKPic@I:ePGid;\`%/q3&61ie]A`qnSi7o\7K55,r<<A6q1PbUf@
Z+&dB(,<uC*^QDZd&DBE^<N(>qgg-[X,ohh%TMjSi</i!'>S)/_`?4AZT;Nk-]AerK=3-+0>5
'eiS(5F^<k*hL%H-ni!@rSGT0#Jo^mnE)G%\HN1Z;&gGRpQ`RllC@ZWa;7/7=#+Mu`02jsQ&
!W1_sOPo,_*dJ,,FSm0k;m^rWbkMh.,&3!^UDN46-1S.iNj5LfdEcRlC&&#(eV\bB*lmid%M
o"\Nj!'Vh/5;nq(B\l\?.@&:DXTH%g*;*#gN@HrQd(PXePa]AcE64Bc[NL#[[OBYq&s/ptiUD
aSR'E$.X/rDSh<OM%)V!ba<G)fD?:)?;iE<ea:K>Y_G?c4ljpYuUeKp-hl>6\T/QSApDHF:s
aK`\KQPPqA9iYWdglV$.fM9-iE92Gm!klLs()+,clRJ/a$MEE@kP-lFIC*jE>/'V"oR$4^&k
GrDS+16/J`J_e'+0)Oq9#A9@2/F$b,C($lMcdU3ld\\gsRQ*cs&-D2hnY>5KZ/fCoc&-+h5Y
-TJ6o4QYil)Pjhh>KqVH$="=7:YlaU@]A^p]A^5K8p#Z:pfW3_lVcBAJZ7N>m*Q*F$!CZ'hVg9
Cr_LP8uYo9"m./p+l(hedBb1do7IO"h!e6`h_I;j^1&e'4L'mY2r-#_<aq)I?Bl+2r,_2L,.
h);]A2K0fpoZS-GR(VFaST-!gFW[Fbp-&pkTBH/0d^%KV61%'*XbGIX(XGo`6CZSoYMIel)<J
=dQX\\T<U[SUYZ1c$=roe_9-)L$C(<UOiVo>+<EMAIL>?^f8hZS`$84N(s0A/NN_DF(43H
mrd8BrFEB2$p&ZKA`4,+\?#=Q^@^^!ofSor\k>fT3dO(ZF(od)sC'mo&d$N!>OfVS@BA[9R1
kgLa-D(Atb69UrlTHb;*$Ze0Z7.+So(Yp_<%lBj');mi'miI'MLqBT80Bb0B6gV#!pr(rkKi
c@pq[1dlJrU<-'#3Wa>02%gr.WOLbO&WIPO41eJMXIlHN.LPqA?/pWZ4m=Fr>[;,^6U,!)IO
@)<Q&F/A5N_dg>__1maBZ9'Y*\BAc>YY>RTQO?duFYm\<GDA!sK/Ni-)EHPO+KAW\e:&(79=
R,?Qtqbq:J;HZrJ`lp?l7gH*V,]ACs,q*T<4K(f9&bm4?gU[PT%I2KO]Ac<8V5jt'huS"1t`.O
/r4j<N2MCo\1drGRlqA6>5qlQ>\UB9G^3[NsA513EhCe'$!taP+cWg/mL6qQnCj0s$&++6ZO
??SkcMl^rK"U<n?8(n_"#:n(e&o"`MbmbiHuF=Slh6q4-N(&Vmj,e;S</L4#s>[=@92mt^[d
\HQ@Ui5%%O)r"Xl9b@XRNgc3>!1-ietqd1mr;/M=K3+P8W)"o!g,50Mc"If/9gk3A,q@gSSD
BCp7SGRGaX8;O5:(**a'uNKLm&Oi$(jER(SR9>@kAfP0$CZg+H_:oRR0)$]AB4>TjZ9B?C=DW
]AtEkF8$&ec%]Ak^WVRdptoS1X+3*%LG[I\$B!1NMCc>MckNsh7js*5]An/OZUOi"t5H&``mXZR
DbiM?n2K2%3[KnH4RqOcV8\_teBoSV*EQ^S<CM$19rQ)$u[H%i!T-L1)$-bR_io2uF1&0NCu
Xs4LD!rQ%2`5W`=C]Aogh/RXRYn)>4U6pqC,dH+oYq?@&cKI8MHO,/*QhJ)&PaBfW=Y')M'`E
8,=4e-!XGMPlhtr0X<UcX$aQleCIEBp9p7M#;_0=5FG=F'Y3;UNhUSgl6-%WAS8f@ZaFoK]A&
IDd(fW!DX\rX3sZCmp$>u.Q[oANW%Fp0)s?P<[jJX;U!8_:2&_LMB"M;kdsEd]A0;+$gUaFC"
A7n:@eJuPfZPEORkn*nuXrE[TE6NE7@,lcQV,lM)>jkT@YrmDO=gc937#?0u4UKn!._N_,3;
oO($nF\\7T=e>`J>f1ZHG]AKQFrcR\%F4mDa:s]A^W"g.r68D>)r+haVB'(jdl3?H1YZ`7gT4[
F@Mi+G'0uLR]AGg0j`tCrF;?*1o5*-H+P<@_E5&m<\$W\,h0Ss]Ab79NMT:S75"9?V5uE8)V*[
31I@m.\ICB[\\je?Ier,6?r?60V3QX<?9_K`'*lFTKoSqS9!3=b%[)3kTm0MMrg>1X"tn#c<
/hcH*-MfFUsM(?gMW=]A>qWEn`4HT2[NDLU`iX:LT!kmSjok:<%9C"q1j_2.tnPi;lM?^FnL<
UFk=3o/&uq0YjuVh&uOW>sH$JA)4XlU4hZ'icp<\J,t'CPlXtDmfuTFG&p=Q11^j4,/cYGd@
Y0$2!9JBI8M;9k?tnD=(VDdha0eQTb^_36RrCY"`GmqeX,hnHg#,:.nhW;7M*g;D7,t5)@t\
,IpgQQ$YGppQAf[fkV%-i8B"'4\;?!91?UT/5Yk+e5BKi?anqZB)fFS94<6A-a>$?3%@*KqQ
$-Li+R4%8$buOVS*4AAZ0%;9k0jCS[^7eNihBu=;Pg,V@*!GQ7GV?`2n,Hc<(LCKnlYRgiYs
paX\g)F1?g42L_b1jaW^ViV)]Ad7/IhcgN&00-ba^Q^StiD(A+6d,hn?E\IGsl!ee@4*a4Wa1
DAdmG]AjYr0_e&Xt4'HX/>T)%8Z#g+eV:#tIZCGCC[HUA8/K-JCJRfrcipV+PJPP4N.M`OD-H
3+>TlXA3l:O$jMrs`OXLVWI`n9p,;PdCr]A)>XqT%YQ>IfSpt3D!=#m\lcR(?cBb:/RGmq[D!
Rq>T5#i-\%\h+THUL+No\am$=)1Ps48**Td#Ut@*>iDV\%5aG$*SU,NIHZBi9Sg@"M'9]AqXr
6B9G*Y$-l8Pj`_J)?r:q*B^=D5@o2`;6G<;0B:ZS[R7'?=KKb[=X"\R0t+P<d4G^BaObbnLS
S&/Q3?HSDEB%QO05]Aib"TfFJcNGF<+QA_Z^@?q@Vk4D#^j(M+dDAaU5[X(L$N6cMJq<@SqsF
W,D[INE*>,?o3]A%hkZ5#*iEca,RVZMLBJAUkAN9U@#Z?8`#]AL+FHC2S]AiA6OLNGUdqSB7-s7
uH2<Mi+k*3CNIXEpC[^PI7f/cF>2+Ps:@YW/2mU*ma22P@>7!Xhn>0Z/@rZDdgC:CcE?PNo3
oc*@q>D"%3D/\tks8;*#)Rq`"0,4W8XBBSLgMr!4E0VnL+>$p*\RJ+-=e_,V8W-R-KSL[+eq
*>Jg`%1Z$%T0,!_a2%b&*\sZY)@Son3Aq!kQC1RLhjCRCoE@7mmpM"j5.;$nC/nW(UM,g,d-
D2_IM?RStT@%lnq76hk^)U!]Air9,Dn_q*>!"@PN1T"c1%)XYtWsJa:m#[a%Uaa*)+lfB$RV)
m>/$ZrZ(4*Qf2Qg@K&PP&16cEI*J%RlAeR>;']A!t.&uR]A+0%WUfioEXhPWhCe'>1u1?J";qm
G+d`K7V/kiU0@fnq?8)Er^f-dtRZl[.YJ,G_(M%?c(6ffngD:,L=Z.%.f+'Vc4L.Jqo2+IU/
(6KG1_Id^\GqYWcDC-'j2mX?'G7eg<RSQ&p)).QZ4)]A'D?$VSLi#8\/50uN2@!m>;4aVn0Wb
?3]A_,aa_-LCr@nH]A>efj)AG/\N$lWXs9_S2F,$3^<;MO1ZL`sPnY``BuArC>A<UCH*-XGXN%
_NlQrBmoNl5hUdgFi1U.?>#\X&_7L?u2=L$bDBX(t*OCNe#2=O'k0\$46,<(ap%B'Dmg\inu
W%]A*q!hc-=3W&*b[;IV)WbsB,Rbu#"d7<q$XJCOJnMMR$s)P:[8Mt.6=#pXV.r4a:[4J$0q$
mU^7O;/9))?riU0ud26ZuQk,IW5uTg6Ch5'ujU1N(7dhS6ZrE1Z(n:''r,G80RnaVX-o,gm=
GM\lW^"%s2lgmoc`&XeD,j:b%e/-4[mR9B+P5AA(AA%a2K?EHrk86OL<F1eKHm5*Sef>,Z<;
U!Q4o)dfAUVcd\o"]A]A4/ILa2@(f1KXaCCOVI+=KNs,%YZ[d`A]Au&lZ4j4^_n1OP_e.SVtI`H
QLCrD'*nZBB6FI0^8;Rhg)&TI5hDRo9]A$\_SK!mDI]A=]AP3<A<ijrbNjBbXWZuOEn:fqm4r^]A
:NG#VfMJ<)]AE*%sG7=(<R^hRPcuW@mrJmpaD>"k>V9([oeb_3n$(pYKq1)<"]ANtbj;D&ma7]A
iJN@*SXOoe!<+00ZZ+bZJ7P".`F&a"ftP^^!8qSnNWoa%?WV7l"&!FsIU;s/+7gmK=QG&a96
.Dk7.NIV('Xg*uWKAVtS>a:4GA_#'n'N'pu0I?COr)#n22E,qnh!e@kt"Sg@["62Tk717Zrl
U!Or,l'5J3:%j#0dn+l:S^B@iO(<1GqMh:+B!EuXul_&J[5dNC=[XDr\@lfcco#HYshL"A:;
8K>EH'"2o4N`M#>OR3e6[Sn06#gnte6#:q@<;*#"UEq:5Xc4@_WRmk6_P-.G%%2/2hTFF7<J
V[Lb;Z(49i/-,0ZBLY-i,a:90B.@"K@eT.g,JonV#C%eK+MeKlA9HiXfE%^UDeAjI;(VQ'>V
JrOQ$Jsm9u%/`jFr7%^:u5R4U9;a857AR07fDrO(CV(,e4t6[N_V)HGo@Kj@I3+V=7)Z4^&P
<3nj$l#kqr6C2)Bg6Vs6hFU37]AE3%,fE9ct,5lr^F5A\WXF2%1ECP%&#*OTsd,.eF&i>CcGh
Q>_UjYHO.O&(X3JhZgEbRC,tnP#n(KO^e23RnqGLA%EBIL"7>ii<m7R+_Q`;,b[]A'.a*Cr07
Msa"B[tK6A]A03eb-D?TY2_$tQGe*m6>=(g&*1B5"qqUG66cG_AFJFE?Ml@kPBjP#mI2]AuoC;
+]A,=t[RMt4cM*Z+a$Fu(>5<XC^('o:cY_HJ.=5jb0aJ3OBbh5dgHa6UCI3-UPK(QmV3C!)D2
l([W83/nS]At>n13*hFc^?X"'>D2__>Xl$\duCS^2M9->3sK9;%7d!l:th]AN!jV;R(>L,GPei
hZ$50P?sd#=FZ8<2idDbfThK\mmglV8^%IWL=^]A?agE#l2A/&N`am%"90h?k3U)LG9<?t?/b
9%d3Y)Tn,.F@ueP%Ne1fUgSk<Z)QY`eE/oCn5&d-,j8P,4V#7#qViih-/EXZiM?Z,m"BfOS'
hAgb&JV>*70MVBY$f6!\o\4Bq*`Tb=5"k>JG'(U(TD;(NqfF'pcFpQ,+1?4?$@s&ihRr3'BJ
7u\,df;$LpldAF6?QlNYq?Er*</ZYC0XlC:7g0c[qQ_3n!ta[@i#NV.2?!0^D[s6_b(o]AZ8N
g2B?GX5F.[&Q[X,<hgm6t-@mbdeZ)8Wnp%DONVQaHH0F+@H=.jlAL$Z.dqpP/:>c=.^;Jc9,
h_=0q2-kG/=-)L/N6]ADs`Z+WFgrPI`8Y#I,bGVLj;;C\[#)T)s$"N-C=^_XT1T#t.f>[ADJ/
T!<tL!ne_#(]A9[JU'2*kj24J[mj+UW;qai;I@2oIqftbY,2JR=5BW'B_XM]AgA4<emS`Om3T#
QE$,rXX'th<Vn#6a(,=W,3]ARV^u<RCd`kLAu<??t:4Fpf0djtRn&Z,lFH87ur.]Ac:<X^-tj<
CQY<`G4QBFZjVQoUC<KhSd4!SCVspX1>+X[Kr3L=(+QOtKKT0g[s)Ml(O-Y!6ao3R$_%p2?=
?%0'X7<.Zo>2?[9/"s?Yn51U?I;Y##2_rOusY2B*g]AkAdpg0QEoKFkl%r^a<(ijq9<AK<;(e
X8P`cs<CLnCQ`cSf)JQd.QVthsn&h]A-(OuI;2M&G!#:R.4''67B>Y_T=aHPOlhS!^.%<Aot=
fr$H_&qFu(fG]AKmA[qRH#<rBdsLBm%nkJk??u*F`A\;*0L'7@-eD)+(Ze"sG:L4;IU2.6jr)
pmg>bg"Dh%`"hE5:W!ls)<@V(;^Egl4)Yu,_aP7?DEc'&!'FK0QbCMUo<59-"MR_GN^h-W=n
h:8Yk;Hl=//\bL<-"m-G>:,rTlZshL<Q_,'G:tJ^^p@ie%bEBumg.uY:ESCem(J5.RB"$0D,
e+nWcVY_QkX"q%*WYmYWKDcD&0B)l7=C4RSc^hq^=&B]AYq9/J(QCS!bE6gl/hKfg1N;SmE?*
XdOch+:%h'p([q]A\e9WfBh`FL4Ln<&+cYJJG;CpLD(bY=KTLCS./d#pZYC&FZ0cDO$7Q5h1n
5E#ZK'H[)7:gPKfWs:Bm$JWO0:)$d6_!]A2M'[%Uc&4%XE0T(5qT7J]AV#eZq45+XLOXWP/8ul
P#fC[<4,ZtD&6gLGSI$?h:'%T-=i&/DqXT'(#pTOLS]ATAB/=IbQ?-K\U$#Wt2i:@.k!gZGCm
Dl(Qn23N!@if@&$3e)joQ1WKsIcGlc-8V)bU++C-4\dn+r_1+&P+4ou9L$O_$Js9/K6Mb(r1
B/#84bG!CVh!>=sr\H_7djo-F%Y=*.9TR5fE@5lk(L"^>,^<'OT3*Vs#:Mpe_$'[T>$P&;Z`
OEXl#%E/>h:6Sm_`ToP?=M(4't$K8r,hCPoS'.-8$(-f#?\B9p7T`PbXa05PN><tBZ"??"^W
0hBRAV>*WF`+W"kGecN!5>`>JS_Tl@]A4aiRcB)rn05ti.F<\"Z2U]Ab*$H?JUN;,@nYdt.nWF
/n\g8DLOCs41AW&@o`B(k`%_$sIPWT$<MFnkOHU&VpG[Uksp!CpJ#D*(>ZrgpqGjV*#9).#6
Glaq[STSq"0t`_'!8Vljar=o,9WRqBHN`rd%TArka2E`J`/3Rr?p0cP,Xr%kOC%d/Z(*H2$R
Z2a\F='>Yo=kf?PM:S^3j+;7n=dI3g=";/L!)DTes+S=R6O]A_RNTkIiLh`CKH&9;C7J5h.Wi
gD,0s]Ank'6Ebnr,5/6?.$NC]AneJ7)53bm??M,;QTrR2c8q7&s[@qo+c2i^gU7ctd]ApO/@4p[
3ols>n+8aUN=hghQ/@,TG0da9-I#;6sTL(b-mt4'c>(0"VtckjdW]A^LKVk6G8hf3Rsagdj(t
?;%9c7"eV*MZc=J,hp<*t_a:H-pX3&4Ag$bmt4>6m)LSO<N02*>,YCIQ6=LaJqnSC.&C\VkA
LVNs<.Oi2OLH()LoX^,W]Am&tI=++I;[4=NA`G"d;XjoC^E:A,/9B&<$g&npa`un&uEil/t;C
ZHO/?ig]A!B*5l(!fdZ$Kge"k*h4HUg.>%ShO55J*fCb"N;jk^YJgc1K3W1BD[kHY@@Eb6l(X
S^=Z0s.3W_\p\3^5HFX/qr/,V\XT"3?&\T^[+l2O":MhQ7Z/l&d]AjER1n"eMVa>P#!47@(X_
/.0DUU"]A4SqjBJ0b4oE=bIXsN>j!9RcamrBQUHV#AQmJk?SWTc(-AAa*5<KXJ?La,pTNk%$!
3TRhc2MnNK<NV:s1;?"2.(WDdG;0GG>?(MCS)Z\jV;`Pnib0jJ%W&21/aV8S^bi@k?AUCF's
F\LD=p?cAJ'fm-O^g&,s$OrZFJh.'6_>FE4m$Q$qR-Stk;P_j"/Lht;,k>biinHM@1!=F:LH
Cbt4"E>O81B5:PDgCjo?6mm1?dm8,c%q(Dm?-3.chntq%sgSo0p<Z.5#HS#gW#(537iX(.g[
s.9NlN40]A-[(YK*+`u('B`Irc)7IY`<(RbA>+T*5X&-RFF?4h8HmG#]AfiDBG3OCIJ3cri7KC
rCQ)b_t%`1hX14H\A[8*WV"3WBBTekX81PaEH<WG.tr)Utb']AV;U(b=\#2?]AQkh6*A#qGrA?
g\W0t9:O`JImlD!a%%Qg@qUldJ]Am-PHYoL=.Wr&maY8@[;bQ2P6H^l9nr-\W>>0HDFBeI4YM
PMRhK.fYn=<hI`2)#L-C-\%nK3.u?3@?RbelWT4L:jA,oPVUYU&S-,T939)I4AUChSKWI?<9
jHhWYt6Mlf=j[f[40=:H$p.cO+RVX1.KTRK`6"=%RC%q/HWR7mP#uAsLTPZe82/[]AS^Gk)GZ
t[-g"'V;?Nk?L#WiKA$]AOZsA(rOUVq1)GA*Bj\U#MHNWmQ63@@mc.aghmT8i:8L1@qLQ)IlX
YhQu+iH]A<!5!`a3iraOLLHnY*T:u5Wfi+Iq[FP5Dk45I+g=Pt^Yp?T+sT&=>\,,I@md'D2.l
AN[ah>Jo`8\S3eDMGN%Y@tMJ\rhprm.o9)T.^4HKW_r&jJ+,[>5]AhRHiLJp$e+d0HIQlD3fg
[uD(Hm3UG2UQ7=($dY(1U.McaYL`e;]Am]ARnS8.PNChg(S^g-to&C;;41B$KofDZD@3X'\8G9
Hkn?nZ1O9Jt(!+X9"Gblo",Q*=Ct\sk![TUFX:LBl20c?s@j,A3bfCB^BO"5i27Qp]A&r24'!
V_#Do0Ycll>o!<g$C>Y?3pKbe[g9tglrb@t?WBZWRWQ]AD6R7UN##">[2<!@1DcB&]AMg&p?d<
?mV)paFn@0/'X/%ouIk+oNH;Wa'!V$8PR%0i<,-6&<7G(-Q"Z6GptBYhi@CJ<87WhKlq#e<8
X2&#G4=q5)o8QhB$2I<bOkjhOH9FLTo(J7fc)!RP*`hZuo@,,n:+[hmZ3kusfadcqI.T@e)R
1QlD3CdLO$"?h^K6-X/1;(j3*0m,+WofUBG*RZ/AM$k%JOK+$Xc-oFRq/%k^`f[q-=Sg<cLL
p!fK8!lR87)>o_%W;aS0ec=YZA:I+lpW<J)!EAAS-;U4j4H.$h"oU_DXCQnZAgkX6d/lcL,F
qP%G\+Z'PF7JKVnj-MWT+(Y80+%3j_gCUU+_CpmGom7]A6cmk![OZ.*#mjQjtSA?7upr-Lb;B
mfbm9IXf4p3u[UNa;>S`;e#hs456j'.XH-EKE)imnku,p[s6Z7o(W%M>2O@W4SY!gA"GijSH
X4XST\L*kp**?Nn3H+\o0gT1f1Wlg;ifIXS&h5D'']A6WABCB6eksm`U5]A;VJH>1*rc(Q\VE5
FDlFu8kR-ikG`Rjn)Z8Q*BEf1rJ17\pE/ffgjcj6E<"nr_u5Afs8UoYajF;CbKcShjOH`FX'
M(k07CS4W'"q-h.t'7mec`.s"OF+r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=*r\4=
*r\4<nBC&7<=tVH-1b\=PWjd$5<u#@D/si3Vl[CE95Q4i'5')%\h>]<EMAIL>)S(H<b]Ah@6X*
cq9#j)G";rrN~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="79"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="481" width="375" height="79"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="true" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="237ee0ee-c719-4379-8e04-7b557d802411"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TITLE"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1104900,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[11544300,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="1">
<O>
<![CDATA[预警指标穿透]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFc8S;V<)knDdV\n"q#i@;1q]AE@,H@_tT((/<EMAIL>\aQ&6KS)53E)jn-!W`mI%MAo;[28<
f@#'G57I-P)Zt#Pe?V$c`fU8Itn?[CXVaqrN:ALUC6"n'1k1pRclZhYXBsXr[6/>.JIu1&q;
TH6;eW%KI9^kJ'YRSnm$3b%_$/Y<0B[pBD2sNf#ZpaJea.M;N!WhEE<r?_d)6%JKVc>$h/+[
-94C]A`$F&*S5>s"+5Q&oC98eg[_i9f6Wll0No.sCqR<Sr8*TRVc-kk'I+[a_@\E1ikUrD(R3
uHOJ'?Ah(Mntca+0Y\ICWm*iQapD8'in,YF>fF",Va3$#HHnKj*.8(PP:!8tBd.Ws'\gN@R%
Fd"7I4o_Rfc,^#AU8tYW_fN5:WNHl\Y*&;@RePVV.F%>(Ru`#7'QN0eVY@?c#O$^i`6>WSWn
SK5Y#=F$*tmm*,;Q+D!1rlc(BM>I.80:_69V9.^?g1:o=5>>$PQno@?t72?D6K8qkY[=84bp
d(d-Y[42sf/m(/fepTo[Q*`=nu&in!u\pB0SiU2F^X_.9aa+Vb'i<P4^6-CG9ZZ^7l=&>#%h
6/\S\-@NS3%[/uY1W4eYo^GD3b_J;LacE7&Tq6kTl4p`#%P&'e26b&Y!'i?0l=?_X1*r\,23
P'WS:+g8El7lBGZV[nHh\gNZ`E*:K!6%=/.Z8$;&lV\(N,;%@pL-?7Zb]A7?N=0=kQ1Xmj.a-
F$.iHhHnp5SQ^J-:d!7#oLS7oThZR?(3+,cR7_VEYYI?!'m,_Pe.V`-r08]AEUt,=rZB06$2>
fE8%*hpL1/_TM`T$j<aV$2Gk)_sdX)Qkd=96YgO]AkLO)(CK_WJ<MS4t:D<V4Z*P2h?@/@`"V
SkrW[]AVT"<1Mf?Sr8S1JB9i:'^WQN<AWl*!U9U6C)r^Pc='i_B+PbiFW,4bjpNsC2oS.,,WM
*oYsG();DQlX2Q4i<e<K/UYH8"<Bp'-q5_q<PBXQlb$/=("td1ZB3';b+*/U.eXrg7:[Zp"T
972EU&b;M7CX;DN:qRoJ+qP?b<m+k+5cq/:_3`,#9Te)u=gU1f)]A0tsuDid!$UV,1>=.QKnY
Z0N.kZsU_hYUQ]A1^O%G/6E.2DbM3J3=)3iLidQA94)<b$DD4&)*S'X)55!0s(0J$*BN\H&1t
cIc>>8YDmW&IWP5_INqpg/kE:R*-U,[D"eEag`6c\+(L?7+_\M$Vh2C)OFMXQ]AdW,5-?B@F3
),,B=c)(UmXMaZ3[8sW,(eHU?(fhS]AS7!f>fMl4/uX!Bd`m$eLP+B.snTgr4&1SS:=A2uOs<
$eT=MM=M/.Id?:X9R_%lVp;QgnRK5:*qE#WumB\JQ5a5*-O3,qp2R3BQrU+iM]AY>kp:iK>9:
>iH<SN/]A>U#Hf>)36?F%F0W?O;P9rZ60=N:i_"l%691,V3S#7$JEd_C%)N-u%#i2Kg:JmMQ^
+]A8,pk+7<e_+-.r,4Vr<p/&6nq=N46U0H?OK!t"6KUV=/.H6CgF-m"A'=OB2a?NLAPV$j4W:
u^lkXQ5cJ5,(!WW`@]A2-`cChGOa'%^:l=#Z`6>;5[Z*VD?CV-^[F@jO\+&R_'_4_.s\Nd7?4
92]A'HAI+9jT:S7Ycg!n5]A?C0N&99W".8q6M/UjM(LJ=k8s%7N.dddU_/I/7;R0e29a"`/C)-
?]A`Grl?7S_QagfMN)3hf",&66n<6FZ8j?s?Sm]A.mE09S@P4AK2tW[RPGoPrWbF%:gf\G#k7f
K<0G(LT9PfdgP\rsSgr_!%%c0l7,@LOOg<kca<M@:?m.)sEYmqP*Sq&J9,:q+i".cS):LoY%
5il*e]Ati%rG9>>?U\)E($[]A:ND-r'09Ai%e;:Pl!aMqOA'lVL^d`!^FUSSbu"O'o2,1Hh`Xi
i]AS;FPRHrR3'aiR!'@>#BtS`u-npZ[9kHi*<B3Z<#NZIZn$h]A;)o%qXfYZ-mts,N7u_\]A&`'
-'_ukmC&l$=Z4\RRE<VD[B4jB/m>*Fa-`1't/ecdVKAE*9FBARf8lg;"(g3Dunk$IS(d`&Me
M+<l<8D[JSUp\^l,mi*;_TIe1Qn2C-u^t9103O3Bc2'Hp#$/ILP7ke.3*t105_naBnV_0AF)
oW'6lZ-;0bd#VsE2h08U^;I*f@`JaV&=rH9e^;qb-cS/o:m&rh$G)IKH.U+0p'o]An?O9l_9V
o/]A,o[lCe:DMIu@2a$'*:MiGBJ*b)X.OqA_",rTbq4230U3nk(4`OP7Rc"'\?lsUsnSTS\`R
V7P_(jMo6]A8dad)>0\!o0d0aOM"57u0WjV5rnFEj<=7pq/7D?PBj$-XX_Rqe.3ZWub'c>meT
BT#Dr$aAE/TUQ6pqdGPupYe-8C/_h%fj`2P/5GdF4s-Ms)lfD%oVd@?>LT-9]A-2'^I0ICMU%
t?cYg3g$]Ague#/:mPZKgJ>)<Ig]AkHi(E^H4^Xqqe%\]Ajim),Rmm)*/]A?)mU8oV$uVX!tu1=`
2&5K7V'ZR<13RbgM)X'EiNpa+UNV0F7Vj5a%#GB$5Gj-L^l`TpjOfFf!L;HP5#q`;.1i_Kf<
2*0@NV]Aqp8P(1mHA>:nl-5j6c(?.q^%INT8266!T:F0;E>4nT.l-)h9K%$Lg1gQEVnem@^\j
bqZ"J\fVimqp4U`-Ci$=k%n`=U'mb8P.(VNI7FBN52(LYe10T[o'V.g@>knYBg(B`h?K'l=P
L*C!64^VKq/lAut@>qOAN+\]A'M\sXW\*ceNk2_"+V"Gb@9:?$kOhBn/RL_u!B:/NXM&TfS^/
NJS&FbnC%S)@IY8TlU2-^#9pBK[>F2P^![oH*@%gfIJi[AC9`7p\BFs%$fOK<Hq;$9NI=GI7
U9--olOFh&Dai4'ihALD3@eCD7U;!QC!c`u/$?4aY#0MaNYIf+(JGCI']A,P:eleUj;_ESB'R
LOoE0qn?O#TlK.qd3m=Gic$L`Abea@D0X::6QauXh^'ci[8ZWk9##;V4'h'D*`0\%*gUd7?i
+-o`K:&p3Z>,-QaXb3bB6F(R#B2KY+EqoE?f8o5^!6H$En(>4mPp-G_,dZ1[f'TnPY<]A&r@W
3'$el#RNGa+@$aZFXeOp0m_ohQ#tt_%BO11Qf'>"G[]AG?_@;"p7f7s"0#niH?Y?&]A1:`.h>r
*&K'p?]ATUO]A3$>SWf^ldlfNV-1;`jOct4j@PkUR3#$P5NO*]AV)`eP)(hU9]A@6N_d]A1GlM:4$
I<=hsLY.goNYZgn=,5,<@&%JJPDg`^PH&r2&pZ_+Zdl09aToF;JmSF!(De`FR$jMWC;HLS7b
J#pb*O0Q5Ic9V=>cE>Rd?+J&X(ZFuV4n\VIm\MJJ'uMsI0:o4uUJ)F@D7sfE!qRAf=VF''2)
B:,o)?X4GBNf[oD$2=[uE3LoU,J2F+Dq/1mlKOq,Z+K)HE7-o,f@466G-L^%5$I!3g<K6t"?
8KuF*uh)ZVd!7i]Ak"2NKHKgYsf:Zf,ji;=2n6Q!ZOL+n9($t0E/(r?i=0SCSX@0f4;_Z)96>
*:^7R1F<8YbA\JG5U=<q)@"FDkiaj0(aM#odsBA?&5emGp2DA]Al1qn^3a;0UUM!oJ1*f/$;l
UG'VZ.m-q#3c;2@OQU(E%Bs%<%)oY2AK(7BJY^?3,df<]ASVnVf[4~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="129"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="129"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="TITLE"/>
<Widget widgetName="DATA0"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="KJSM02"/>
<Widget widgetName="DATA2"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="TITLE"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="body_fzjglb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="body_kqlb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="客群份额" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="股基市场份额" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="股基市场份额-分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="股基市场份额-营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1699702547177"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="19b65130-f2fb-4849-a43d-7d3efb476738"/>
</TemplateIdAttMark>
</Form>
