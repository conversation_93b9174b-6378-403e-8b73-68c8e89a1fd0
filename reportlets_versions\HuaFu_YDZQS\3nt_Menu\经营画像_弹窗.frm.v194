<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024-07-07]]></O>
</Parameter>
<Parameter>
<Attributes name="pany2"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (  
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') 
)
, CWRQ AS (
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(LAST_DAY(ADD_MONTHS(TO_DATE('${date}','yyyy-MM-dd'),-1)),'yyyyMMdd') 
), DZCP AS (
   	    SELECT ZBZ,ZBID 
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df 
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and TREE_LEVEL='${level}'  ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+pany2+"'"))} 
   	    and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
)
,TAB AS (
/**
1、从指标台账表配置年度区域展示指标  DIM_FILL_ZQFXS_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW, 
		     CASE WHEN (TRUNC(MONTHS_BETWEEN(TRUNC(to_date('${date}','yyyy-MM-dd'),'MM'),TRUNC(sysdate,'MM')))>=0 AND p.MODNAME='财务指标') THEN (SELECT JYR FROM CWRQ) ELSE (SELECT JYR FROM RQ) END JYR
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, DATA AS (
	   SELECT
	  DS,branch_no,A.ZBID,TREE_LEVEL,CASE WHEN TREE_LEVEL=1 THEN '全公司' else branch_name end branch_name,drz,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	  CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
	  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX a 
	   INNER JOIN TAB ON A.DS=TAB.JYR AND A.ZBID=TAB.ZBID 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+pany2+"'"))} 
)
select 
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
TAB.ZBID 指标ID,
case when  TAB.ZBID='bylzgddzcpmc_20240622191229'  then DZCP.ZBZ else to_char(DATA.ZBZ) end 指标值, 
TAB.DW,
--DATA.ZBZ 指标值,
--TAB.DW,
TAB.MODNAME ,
DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长
FROM TAB
inner JOIN DATA ON DATA.ZBID=TAB.ZBID 
left join DZCP ON TAB.ZBID = DZCP.ZBID 
order by cast(TAB.XH as int)
 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name, tree_level from ggzb.branch_simple
 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'"))}  
and branch_no not in ('2097')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name 
from ggzb.branch_simple 
where branch_no = '${pany}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx2"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT
AREA_ID, 
ZBID,
case when DW is null then ZBMC else  ZBMC ||'('|| DW ||')' end ZBMC,
CJ, 
DW,
XH
from (
		SELECT 
		     A.AREA_ID, 
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))}  DW,
			A.XH
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
		AND A.AREA_ID ='${zbsx2}'
) 
order by cast(XH as int)

 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb_left" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[	SELECT 
		     DISTINCT A.AREA_ID,
		     P.MODNAME
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1 
		ORDER BY CASE P.MODNAME WHEN '财务指标' THEN 1 WHEN '考核指标' THEN 2 WHEN '财富主要收入构成' THEN 3 WHEN '客户数' THEN 4 WHEN '客户资产' THEN 5 WHEN '客户交易及两融业务' THEN 6 WHEN '理财业务' THEN 7 END]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_dzbcx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240712]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx4"/>
<O>
<![CDATA[bnjlr_cw_20240622182609]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (  
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') 
)
, CWRQ AS (
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(LAST_DAY(ADD_MONTHS(TO_DATE('${date}','yyyy-MM-dd'),-1)),'yyyyMMdd') 
)
, TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW, 
		     CASE WHEN (TRUNC(MONTHS_BETWEEN(TRUNC(to_date('${date}','yyyy-MM-dd'),'MM'),TRUNC(sysdate,'MM')))>=0 AND p.MODNAME='财务指标') THEN (SELECT JYR FROM CWRQ) ELSE (SELECT JYR FROM RQ) END JYR
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' AND A.ZBID ='${zbsx4}' AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)  
, GS AS ( 
		 select
		 branch_no,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 	 
), DZCP AS (
   	    SELECT  ZBZ,ZBID,A.BRANCH_NO
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df A 
   	    inner join  GS on GS.BRANCH_NO = A.BRANCH_NO
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
   	    GROUP BY ZBZ,ZBID,A.BRANCH_NO
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)  select * from  ADS_HFBI_ZQFXS_JGZBMX
较同期：当年值(DNZ) 比 去年/上年值(QNZ)   SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX
**/
, DATA AS (
	   SELECT
		  DS,A.branch_no,A.ZBID,A.TREE_LEVEL,branch_name,
		  CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,wcz,
		  CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
		  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN TAB ON TAB.JYR=A.DS AND A.ZBID=TAB.ZBID  
	   INNER JOIN GS ON A.BRANCH_NO=GS.BRANCH_NO AND A.TREE_LEVEL=GS.TREE_LEVEL
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)  
, GSFL AS (
      SELECT BRANCH_NO,FGS_TYPE TYPE FROM GGZB.DIM_PTY_FGSLX_2024
      UNION ALL
      SELECT BRANCH_NO,YYBFL TYPE FROM GGZB.DIM_PTY_YYBFL_2024
)
select 
TAB.DW,
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
case when TAB.ZBID='bylzgddzcpmc_20240622191229' then DZCP.ZBZ   else to_char(DATA.ZBZ) end 指标值,
--DATA.ZBZ 指标值,
TAB.ZBID 指标ID,
NVL(DATA.wcz,0) 完成值,DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长,FG.TYPE fgs_type
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID  
left join GSFL FG on DATA.branch_no = FG.branch_no
left join DZCP on DATA.ZBID = DZCP.ZBID and DATA.BRANCH_NO=DZCP.BRANCH_NO
order by  decode(FG.TYPE,'基石型',1,'突破型',2,'成长型',3),  data.branch_no desc

 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_left" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=value("bp_jyhx_fzjg_zb","AREA_ID",1)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(_g().getWidgetByName("cardIndex").getValue()==0){
		_g().options.form.getWidgetByName("tabpane0").showCardByIndex(0);
		
		}else{
		_g().options.form.getWidgetByName("tabpane0").showCardByIndex(1);
			
			}
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("pany2").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="7417de1a-9ac7-4ac7-8dae-41504a9f711f"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="gs"/>
<WidgetID widgetID="731fa7bd-95bf-4422-a2d2-cabaf5b1b846"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany2_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="pany2"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="pany2"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="gs"/>
<Widget widgetName="pany2"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,152400,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<a%VPA.KZ;]AU!M'N%,aMF'RZ!JD#aKr.0jdVBi&ZnS0e9#e-a7CiUuMNJgVTd@o2&0Vae!C
BXMBE9;'":.TeMf[1T1Z._MpTAYb?['UqX;B`arKR9I]Ammf1+,IH.2?)jUM$!e\!^BsJ0t8Y
Tn=A@kpCH-`DfB"\mfWd[Dd>61A/U,)Rb3tklQ2o&Z0@AW//Dc>j"#DA7sA:"r-[Lklh/a(<
G_-:4E@.@@)!<"`U&2!lD-`4Y9.WtL+\i1UR97'&HiN7G:5f/F0;FAB;oOCM4)i9%bD@&p$B
L[!bZqfPaN(pR9d5@1%a$9BjgKZinZg)r2epe#a%=\I+`c\B%C:gS-Yf0>Fe*,r#q&FVas#s
n=qhRnBn"$-9Nq/B[:Vn)UCi5Y6p*NF^@4Oj@F<X7G8Cn:\u*f]A6hioEaRYV!m);u(`9@IRK
u,LF8:8DH%3tXbdY?8.)6)j+7ISF]AR'L2^T$\%9a;i%J,At!.D&1'k4e8fRqLoA(3m2F2=W0
Bq!?#V!$Qmu8HCc$n^$^:pr8M/G(rFQ>_q%*O57e"5"<V\(Nug[ENoXr^?Y*"F%*r8^EAXp_
:/kKfQ7W@#QJ:?^3'-9UK*:tPU_6\!rR?'eo]A=Ogt4N`cS/.Zi;[&6Sa\;=3V1t'J&&7kE,O
,;^j<BtR6NHG/Ne?sMYJP<*M)%kktI[+HH+d?g$IZW]AC4m)7FmH%DW"b1pWn+LEZtjfdUpm?
E>/3A+!4q<fRNkpmG(T(rf^eR<]AW*,i9*YTYTe$5:XHFq)<LO*5HPf4;SX!mqNEHfAad:?4a
Hm>ZcX$I%5Ue_7ot[qQVL'kI8O%uIKI06T8IMYQm=_ZP43W?U%R)V.O$6b\3\$mJ[8ETg<+X
GePGZ;+mMc-A,`1pg$o=,Lp16PlbI,9&m6OK;d8WskHSA"E=$-\'r.LngMOuIRXM3J27*r7F
_4DugKZ(os4`nkR.5/YgR9,$A\dtSVDn`nLib:n4d6VZ"o>h]Am%S,*PV^b8Do=#Q[4ArWrVZ
ZbVdL2\hdfB#Oljq.S$1W:oqQh+Eo4P1E@qJpQU\#C(H/]AQb]AR&+[3RT&)jM"(BpoHRDPH(H
*7A/^W0qb:E=Sm^:D8XAS9Ou!_nej@)rRdq)8luUXiLBMf(BUo%/LH"+#)9bQ+n3[S)-B-ce
g?g"MLa<X@[O'-a6d)<FjbZOY(A144BaK/)-k_W%K]Aj^mO/gX)[=mirSMtJ:l"h+4U_V\.NX
G*cD.M`11F.20qtfQ`X):#0b:eE)Fr*\.4%0AM&bZ4h:A"n=@$%AX3+\#(,bo<O3q1n$lQMW
DGXYLZ6Coc$9L]AGJ%aK)lPCN^t5/gkbt5Sf;[jeZ%S(%>rk&eP*]A0?GEET[29\5f>E\"[:+h
[lrs5=8>,"""GeW)+VW*t<1sJ+_]AP_QU/X->mJc[tqf_#AHgIl+Sb#Y2]ANFS#`_p[1\9&0_m
/Z5`S0bmB%@P>%[(2Dce)bJ+>'PjamfUf@C<tWj[GGi:f8V\Yra;Z-lSD7@3:*r@==kO@DX=
m)o:2$Ic#';sap34<\Bj!ZhY-(Dcqpan7oK<G!8f'DiJZRj6K3GQ(Y,/i%8X_&t5Y\@-L-"2
<6$IOs#"M_JHRoUYc#PT!)56G&Pp_,%N*9`.$O(YlGSDYiG)k@!C%mkkkgukT81h3&HrFS4Y
jU2Za6^TN;KKS$_MB/a]A0Z%dL,W;SM-hOe\+,=&o9QB;9e22]APa)Nh!A9f0n\0%>equi9W&m
qJMs<CBi',ZSji?f5pM#_Y"5VaE)Jj$mbUB?qrmm]An\RPJ)WY%&(VM/jqjqQo6"O?P/n`i!r
/[pkoRXp[H<;P>N:^o9OV=*`q0C4s7_\QQ)"uk3Qa.H[a2=AK![;;8rk;4>[SID)tf.I[D'"
,@omN6>XR6:eYcd#m3?LQk*Xa<be*Eq*MJt/R_K=$WG*S5HF+7q57_\:sXh(,L6,N"q0Dc^G
082`RO?LgSO37C7C's#15#S'327h6$Q^SPSYUK?S,EL(DBX5'=D^KD(*$]Atq-)cWo.6nt3>M
Kmtf"7BEhB,911H$pkh>L)_+G=[sH./`Peg:UC7D3<\)\ia9"3imLP,krq!-F*+kEeh9QLQq
8N.*(Wg[OmFn^rPm/ip\5inVLIoJ)XT\MXZn<11#GdV<jWlB@37Z2</?5PrCGteX]AeC$W_UA
LGW.5AInN\?d@oC*t#fW\I@8#L<FiKi`loSf23+l2o\3#Mm9ppc-14=L<5;]Ai)glEK9>lo"R
tK<5'Kmm(I@T278,BbMUTPMPI]AHsHhKnTJ)-$QK;.)d<%5Lue0>nV3P:f'Lp7[k420mR;o,n
u.eG6iALcZle)8<t2A*=b#/bjmR(9WrST<ATeV;b9X.,BAa9ZnuX(dI'&pRth)^.saqJdmZS
Ih$VEu"As:.*fXjYcus"aI2^1jf,'-$5^9Huj9\jO^toUc6e+'Kg1j2qCKA*cl&"Ys(6`]A:[
Ub_Q`\^YH<m5pgkIYfZM7o`j@S8p7fV:[Q/#0*eh"o2:g[LisiE[ZQ"3:FjjG9Q/ck'c5=N'
KUq&^+1Q\8nCMA`I[ffI2j;D."H/9+8\Pppd(A`ZAA>o!j%j(^#rchS8o6d(.iCt,[g'#T.I
&P6E09d6rh\k&^YD&^U@_ENi2('%^6H2[.+4UDG<KAII^'S'D%Y!1A5kt$k5F<)`c@"#X$@9
5lDR=Ym3F*C+IS@o)mQAAb98o)iDcBQ!R*m"*K_-p<oO#SZbtGRUrg3Uh_rBMjSoA`!*agXL
Y;Y6`*WB7OWn-BBJ$7#nZ!LUS_=Q=!Y0BEo`IsI1mHt[Ol\2!dRl*K!m-9t)TVk6a,WS/D"F
\R"UCBXU6dX.9FJCq;)%a`htb5P58d.:ZnH,UjAK8tOfUe&VD[`(Sn<]AGY>))4qR+*W^cSOJ
.WgMf`5jSBQNr^VF`rSg1`PWF`-OZ*#AZ6*@8!H]Ap>3b!?*Zpd=#1X^P_$&$.9G?NH[g8n1*
cN;F#Lh7\?quMO]A2Z5i,3+*?X.K]AO%K5DcMTW/5<+n<VWhuY\FsOk5$QuGs5AO=ZR1Qe2cc^
s!/S5gX,@srnm<Q6*#.(UiM^+,E7jUF:r*+&m6=1b97>b5@7rCh>JE*A$Ze>mgp#M/&Ts'II
Uu9Zo4Ic)0q=+1r^PgT2%#N./O%pS2)+U::rK>f<okFBE"FDgW%D802/Ym_oB,bg6JcY<$CK
iHGh%NmQZkB<MCJij90Y?6%*H&9F_UGGjK='p4T4,"h'66crtHl13m`>:1OZFUEks6lT&g40
qiNOh5+2Fc?Euk[+("DQmN-R-O/<`mkcRnbrqAHs:=sY^S!YQm-,F#?YHp#l:^k)1I:d*VL\
s-.CBUokrVV.?U`eh1$=jV!A5jK\<tkMXLJ1.AObT'7]AfHp?F^s9M/s)6d=78cM51AT]AY4$/
U"h9.1&pP;"r;<8<@Qe]A`2kn/IYZj7c)8/a^b6a_m]A^sV8D@D?m-c>+fif_[#Zm*W=n]AL!*9
ONpo<;9.-]A.3.nG?pg_L`D'c+#DMOD(Ws@b]A!oOhb0dD7uS`nC.3ShMu7N*%^H9=Wuk,JQ-8
3TkmduEh@uT7VB\Tq6H>5.Poj?Tbg;`7>b=d!JBI%EY'%hOiu`D,qWA(Ejj&2Fk]A)ocfCSd"
X$aD\g(BB^^Wt$/g/HbQ<</M-Su_O5pkcTRV(MsqOV@:>6lXS2%aAS(H0.QG2>d.5Z6_i8Wl
RdB&jbt=l*_/`P_o(%+7:/7'omiR=EBk=p_N:<@]AuT><UJ5rK8PtW9<Q^%E/r^2i=c,"Nc/9
UhHUtqqrMga4N2Y-b5=,.pHS!?YFRau?*Vt$gDM%n&_uLgYrlPYCT*2D+b?H%\N;FWqgP.s@
quk810UlN=H(l@YKfToHM)>SFoLXmY]Aj'D-sbK5G$\F<&7[HY7t:G)#VGT*0l,P,3g0bcGM\
eLPNj-`h9>UKPoRTBGLCmh)IArKY<[Z6\b.po_d%)*&f'%C8mqq3\jL0TdPUaB;nV5g[>GW,
@c-kO?WYfa+bFg_XAX<;gmAUiGtS4`%Rl"7KFm$jV[U]AjXEC+m5XhY7XgZSd'q;8%7fc,Wq:
K/<c^j&/<[HbN=.)=&?b6g*\OijV@KZ$R[Y;kE:R=kg^I*)!XNEM7Y0RRBm?No5/e-ZKU\UU
jc'GKAQH[1q.Y)r9U@\]ALl_,o#M5rTrUFX8g]A!=/^k_*+f>#uE#3<R(eFup8Wie[G<DY?CdR
8-8jDAroHekPM$N;*PF<udn#o/32Xf4Zc?h&XXF,tUn_lgGF+8W$&)T8S8sn.G/#)!td<KCl
*8S76i6?cuK>Er.TiAY\rC"Zbh'V+MO(Zi&-j@a$(gRrto7r<+0k<Q[)be*&;'b4_t&\Ank^
NIs;B7BC]AcPFB"t\-T"_%.n]AIm<mrAIKBbfQBG;_25s`@J%dJI4K5?KZdL*h0K7TiY!Mb&4Z
dC(mPe7/::%6l2nj/=*")kO4LnUM="!S8P+jQ$<cjbgk-R/lo9I\pHo"UR!5[SP,!"P=qu0h
oWLJe-FL*8mlpH:Y8P*j4_`V5e"MgbRCVhSCXNMR=rm?:-pWN(>'>E#QS[<bl1"=6.pE`Zel
Ba-'kMraSPi-N6"7J<6Whu4[X1nI$e3'\I`*:kJUc2ss^Tj(1lq*Z&(?p!0a>Qb]ADgT39`$N
6*mR0p,.T.e,2MY.1%1QD:F,/^i1[Pmi9lK>do@DO#mT98JT@A2egGbZ^?eVU48$OH23prH-
-+<<?f(D<ko>u,$H#nDj>@YAGi2gG@BBH09h,69;LkPJ*;21#2/TpaR14Dja6Y5A`]AoW!sYh
`ljSq#7XWKP[:HY5sI(*+AVg6%t)/dma1\QT#5@s"kGkCq1jmqmLP]A"\3I[S]A>;'iH36+IX/
=:c?94]Aeb`]A]AMpZe9qY8O2Xt/XIuH_$Q@^b/HuMo@Qq94(!R,gMY_qIh)Q=J)A8o[4?KZ,iM
J)u04fJ`.F#JIG>5Y7)O'>8UU"_B.WuE9,N4Va_!PhZC>8<j3m!uLrEttE-CL%k[W^_36W+'
TRgmo:I`TiE5ipo]A$l3K:to19=Nd=UkYb+@C5&b,1iFV%,deOE]A67OVJt)t<1D=mn[TDrc<t
\e\jkDea$,G*4u2%AYt;Z7M\=c`%7$hGqkXdUDV5csLsI%M_AYoJc;^(_oaN8EY\BrJPeVc-
s_sIa<kbVPc#j9of\24eo;pBltq,30_tY0V2CY)D&N)HSC-5L5dELBej$1\4(=aGUVEq9Lb(
$8DitSp408J1Lfp;/)q*2q<8O=:NJOic6V+"@\RW^`.Q.984u6ph*Kj/^[IqX;N:)1d.;AOX
G(V;-]A]APeh>`L<C8FjNGQlpJo\5j\8?g.>gm>cim='ErQdIp:G5^&Z6>?k/`j27N3iN&k<pu
%tk(PeNRh]Aac@Gq!(%E8-h#,ncR/H01J^4qd&nA4B"D_"5Ff,mM6l/N5Ma.`V-VWBiD1;u`9
DX-6aE,'M$a_P@rY"?I0i-ZuJ2!J#>1,[ME%7k'&e`?\"U*$:5pCm2kT]Ac6*bMQUCW&cA*]Ak
?-3<-`"6Y6ra\CIGWa5S]AfR&/4Zh`k^lAq2*<2d'ojdF+>h&jQbmr3;qXLj`P_pEBV@ucJe,
@OWE!^pi`R[EAYiC4Tlu4-'CP1Y^&Jrp#H&a:K<9m[&8+V_]A,MIeRe]A>cj)YqH]A\P\Y[8p!r
Z4sJXA!g_I%0%=E(GmE:e84F-e_uJ`LUNQn]ANbU.4^DcPasLQ]AaDRrc/[MpO,-um,e1h6kh4
F[Qk<75@r&)k[![[NYqBJ\!NHB\-@l`5K&LEQj*_O&H<3W>T>pu9,gel_(W(At8L1XB[1@lQ
H55rm*8PHpf\@,D)3'7OcIV2G)%DKMV<nT0'De<*KX;Bfp!1$L_8;8U@i7!f70.stfIE&q@=
\^h`4Gr#!FuVf&^?8_HVs1PI-'qg^/9g%DG*J?#1M[fiI;R&^C,rN%8_bMWY!$r9k!lHaYDS
+"3?@-`+YG]Ae>Ue^(QNKTBSi>;j5>&OV'9q\iAN:5/uD6-C>.nI/(3;7RnkKX0:EV7,D$pU[
=;LUn;f+c;M">cS6D8<g?;`gkj.h@PK6ldg(\>;k?e+l<<EMAIL>'bCmg4IX^>G1XpX"s
E7UhIfE4+oe-n<D_%l-pELn8!D`S65U:=uuWa,&Vqh]A&DAk#tT.JHiQL46Gf:FE,_A9jg(fB
M)o3H;U"(]AN)S>sJ`8c^rASiZ;$O8,8s7;3c/ZH:BmsV1!0S#(HF_`NE*Q_g<2ig[7.%c=BA
(r"ZPed'X-oBm$NPf=k8B(C`9!PAh&8KPiG_XrQcGm'XIk/.cB1\7!kFh*,tPuD4(*$;l0G*
7Ir&>->NP2`Pc^NRU;[HA,R.)-nMMSM=]Ao*D&1G^FS3-[DpYTuI(]Aps+AAq?G3Z[_K^'N4(g
AVB8)`argG7BY#.k$@Y3o:jg^VI=GLp2/?YUK*U$#i(FjbQeFSYRM"+[9)"J]Ae4D^t@)8CN$
"oYE7834r'0EL2!de0I;\o1nW76GF1em?^487Z6e#=&)<W5o@RFR-bEqOH`c_)n?[uDPi[m&
b`YJs(Ig]A"_$2)!_mhI\;mV[ZYS1H`O-Z$i$:*]AELW[i7"5RT4jg)!Cq*.qC\!rIOq7[:e1q
g0$ZY>9*Yp\$MXA62o^sS:)W)Gh).tuAVmCeQT,$QOQ4,`FJ%n5aB,\sP`nZ#P_6uRO&_OQ*
pnO$uH76Q:k(*srhp)23#3[T99PB@.ZUC?L[/]AE]A_$;dia-Xpba%'WBSFXai+*1s+S.p-BN<
Gln2bU>kF%Ub^#B`Kg3nY:UCk[=LGC9T=W.EF6JbRf;0e?ViU^/j`ZXUmO/)dh`;2/Pp/S#O
@qq3c_V_oXY%'uU0XqW$6+QV%K2+[?NbBq[)GD,hTcr5'N/cAV/\d29K$J7KVREV=&p,KdUZ
JU>n*)qJo+FHG8YhjkOVh_RZd"@dB6qiNN,7>mY0_%>6k'9cPk50b?6)m=*hfPWI?^gaTA0_
)8`HU[%q.kHjp6%Qh7-+$t62VW:j`@1NGNWHi_;:jQPA>C$lR.2Y`i:?R4Q)t2l.]A8s0]AFq!
h[Y6j)EXO:;OkO/iDkRZI]ABkT%n9sumpU!4HB[#_8D-kdfgU]AjW<rs^t>,`"82m'CM5$k]AYj
o\);G'8Cj8uO?q()0_QC7X7jBpG>?jGk7a5!k+:A,darE*MBb[mq2>##7T8--%j`fCZqZ*]A^
paQo3&%d*Uoc";=@(/r'c\%^Oid!"PDoJ@9IeX)7SuG5aO?XWL5YL:.=bbl&'O"GXqZa'T9+
U$f2cJF'Q&C4It'3PnKd=IG^?=rg7>=u]A[gI!TM#Hrq:+DM'HYL@.o#`4-LVs1hj`+hdO-aZ
.P;=scR,d0\"XKBtj11mb`&3.WR<HQAe>,k*lr#P**s3`-$nfF(/&oL`M_O"cUEf=2alCS.;
VqB3N2l$Horr"JgS:sOW2HT&d_DmZ1he/0ClDC'[DLnZY>(6;jV66KDHI0G7kqm0U<QA,k4D
J+nbLeI0YkK#[_dHfKOBdkMZIQ"2_/3]ANNIR2jNpo.&kBih?FRPBs4n1X&<,Hg1n@B;<A/^7
BY.VKgo%eP3iiPOo\Pb$f?jEXXFP)Bs;P.4!.qu(0b`e>5sIC@;J`TaWtaZF.\:W.V#;^gK,
0!&;VCWlmYZVY09r95YkH[/93V6*:XS4-#Wqn:Zj*1]ArqnJ^ZA@3XZ6.F"gb+mH\Y7\A>9,Y
:!i^;("uW63%L;%ZZdF[+\#pu3EX+i%%6#jOA#e'f=4aLh^L-qEt?a>Q)[-U9OsS":eO_WN[
GbLI^q>0pM;(Z:?_e@lXLY/+1KeH''&e!^f;@o'oipQo:17"U.O_+A(sjs'sCR/#8s.:sK)k
=%ot5]ArrTaGN_e;>L'?dZq]AaKD<pY.n7K+(1l_:@\p4H+6BbpRus2jH%+dO.6gqn$D*hrI!k
%;f=3X``&C%^S^TF77G+Skr>jld$*7P?lLF%joT\F\MmC]Ab5e/sBm`T%TiaC@)4rBJ3/ooQO
^D*Rog-.',&]Ac;:r]A51JGKf&*j'@R]AlDs5:S!$$WL/hOZW3<-#WZH_J(:b#2Zh>W_)(@%Ma?
o&WVAC4=Fb,e0$^@9G'\Tc#?"A0>/;JDB5O3;_B*WpqQ[^%eL,o@&!)'Q=)Y&C2!FC\_s6r;
$GeC/gKf.4t,upRK&b[P\E]ApmlkmHn_XsF`YUEE(5IH4R#FR4;qfe2ETC?X$pm[2U2UsS4TD
ZA\O@\%@iQLu*1P&s89_S<&U]AfIknG*_QbUoT'VLMWRJ%c:F)P5FgOqPMPbPZ1T_[AZ-\AY]A
::GahWrC5M)$6DL]A"DWCWO%sId\X=4a1P?=kSXI4S*O>Y9m#Z7CA)/MlQ@mJ5.*@C+BF3Aq(
kkQ$A<97*bo7?1,hEMM45303)ZEO;A#]AS\L):_YKJG\Af:,gWIR3;NG;hu:HI/T"e$;0!MaD
>@L59m)@Yet5I-0%fhV:T*%ep%1.9i<&7l."7(JccRZrR)l(RjF6d7#JW^ks.D,pDDA?X-JF
0kd6@II7:6iq15i-5!4XBZA\!*[=h_qI#Wsp@QNIe%8,6,&cK\9GXbBJ\mspRjKd*^KESi\j
QDp0QWcUf*_>%2X%f5>V9]AQ3peTBi)QgId8-?g7J"V6NLDf=o:-GDdoWEXGd6IR*p'=KRW+[
VGp[R*'e?a6$=$D$?C>L@:<[@F6jK6]A+VugER,Vf_K2^%bpe[:U(\;Wg)eO?EEG4#4#>e-2I
^m^5Sp=jr-,+N\h;SM9+*#02EbY9IpP3fA,m.HUUh3iShlP\;@k7J'V69%_:DsJ0!8dq1_+s
o#s)C7K+*QIM,`slk-3CjPt_s>B]A*o.QiOOaDJ5QHhKkrgXC"*)l#=k#qhlLVFc_Wcb$l2N/
A9KaP.+u.S6@qdS8W6m%kRFeA^PQPJUQ1m]ALiMDQ*qYdsih/a"%"Y_'Ddsf?1;DcQ5,"DAhJ
M\pE]AH?suCQJjt@.h$NfSB0eSXGfe:_FrSZlhH=#oZ#[mT0LceS/2E>KK&l/1546]ATOP9@s"
3]A?1q@>>nSqu!#&EIVB:U=k-%/=bdj%?@71:!+5Vj_$#\Ga:8$#R1QdQjfr3F_Js"BFM=I]A3
7.)CeN5dBh2<iO.dPTFIncs.GZ!Jrea+M`E9X2=:l%q\^JFM*Epo1;ZQgQI=Tgu"bhbHBC\9
Wi,))r=N!G]A9O+>)9tD1EjleE^-hD">.G+PW%PBdp,2AI^1[Y.L[]AB?#=<'*5j\L(Y=uD&e'
.V>#kCr*d]ACr;)\BQV@k6eWEdYi[_8JOsSt0<K%Pg#%stTq:AmS<sLO4>i)nPQNs^m[`TC<C
Ip0S:K^+L9j6,TdQLFYG2/c"l*.lU#+G`O.=7FOf>(`D%9SO@n%-m21,$As@IGmhE8k-s[!g
>`W]A%2/rEerkpZYpqKDh!G5e:tU4"37-GPh47cG%=KqdEmD>8KgK#*!cKqKp,a2^gi.s3k7c
J)&m,bUosBNl<$'eQs38bQ/oBD7>hYp'4FTC.mt@X;+.)a?eIKn5:$fAI@++!RsWB/2HE,E$
?W"%U*2TjD/cTG=#"]A/X9OF7PiL3+jukmdVM<NZ7&eMmH,YG'+V@&?U(Hr4d4lU#R(tYkji0
d`,uAW&$+@Ca$:r6qV8!C_1Qi.l+i*gf%.l#Jm^$s4CM'e3lElUK,=U8+Xs]AZl&PskLSF#2G
7^78"Q,aEVNo0L;Mqlf>j:o'/4o:dL`8eM'\EF*MTS^E-2?\KUXYOWG5pd"Wo_T?$MUbBgGg
1_3U1>H!6Mgd89$0'#Yd%k"87A^!dMWMf*UBWqXaK[Ek4!/4ZU]A1Bs7[1rEV]A8>b8(iZ7$K#
//HOoZGF(l*gnObXL@m76NJZ?35$h.\iH<%RD*>"]ADu261nRA[BQa9>AoQYQnVt\aZ$nd0"H
iEk;^R0_,-_Vn40_eV*?png+iD=S<\Ba1&=t`b,rqDp+\N*=QQ<>4O/VAN>Nfar-#,B5r/g+
-`mEE<`:2:`Ai.6ooEEn]AZ2XubJ.b,@ZY[&Y5De[lY[8rkl5J`:ERm:m8p1B(-9>\@."ZRA'
'J,:9&t3Y_RD[LJuuL<nUtJg"DTCP8`4EOQXc1n/:MbZm`n!FckP`![(<Ab<']ASS)-Vm5LX7
b/lnK*m?SC(U.-0!,h=kn'E@hW4l=C8,CJ!A?f@tJTMr/<i,g^7$0e-M]A=Y*SDH&L^tQRa4t
fS*''POjq4XYY]A!Y1'@CkT)5.4&V5P'rA_NH-s,cep6^k!</i2#um@rq)Mm,YTBa:,BJ:<'W
7`pgmXgp=6mC;^)3-hKo9Q.c;Bj,f$r=F]A$n8&[H,OTm@3sg:&le#qZeWmLPVY='bCp)9]A,Y
88Dn,I8TIFf__3V>B=@8R(UH.?G4.<uTq'p#9fSC6<1BX/#U,QopbGNeU2nA%G7g:SHP]AFQ(
XOOpDkiqI'agtSC$1?9E\dpbAp]A=s=.i4p-)t!<FP^8&mkaK%cb6s0%PNeU]AriHr3W<IeDH)
]A;G1:WsB/SSCQ4Ka(LnE#9&jp;D6]AYPRY-u+U=5s/1p`]A#$]Ah90#LYBV7Y#gnd#G-<h9mK)`
DZ0)*-^@rg;`HYdYbX2WJ0lW4+DF(2`W,:T"-*>u.gu]A.gAO6M_T]Aohs0Dh,hk#Z.OL^>OYI
`q'XFKdkJUu5_OAUBfpX6%iVr+;oZujQb`sQj;_(FuQ2b0TR\+T/W^SQ8a#e\XD]A6)3dI_o/
<W5e$QbuL$Q?58HDN;d-KJ@g$;Q<YuQXfSBg??d-c6nQ71qt'ihVH,b>Sf_q#^hs(mDMD7'I
H).B$pQ9+lWZ/\C'"J.TDl"7<4rC)eRo,Ph@(C3VP!\384ih#J_t]AT0`"IcDpLEHO]A?CBk0?
=CH%+6W=-1s;Z-QNU4(WrQS@sHZ0fV@HL@9$@$&KSbFa$ALq,t(5pI>78*D`H;'Q\]A*l0<ep
+=LcZnS4&gY1-R]A]A@.Ln]A^2I&Sd=hej<F;SfJl;&g5r-i3#8cCPA[j'i$XWK.\7Tl;I!-4G5
6<9>]A*Zcf$I#sX?IsN"s4$[^mVs82m@FBlu)g]A8uAQkk=2O09RCMIg.(8iXERY\K<ajM>*CA
4a]A4-jb3/Jg-O)a<:%d<iJ\&D:4BKp,I,:hhK'SJYq0cgL]A6C)r.o%rMK-9A"JPJ@2.>_sA(
gB:W^^q.AKTdMd(miA,3E\\OUGF^I6\/1,D#cTAR7gOYP*fSeapq>[V9V4BbjNq>.d9`*@W#
V;YO]Ad)4TRXJo#Pq\EkhMlYUq1EYgFD>:9G^<F/QF7N.sj8.<j[1jQ*Ot=<hh.9m%)'+c)qg
2?W]A_:4Z+#6uDeqoRguB]AGKNT]Ar-^4/nAMknasEpo5hJ$F_mFp"(?6/D(U"f>"WWAKAQSPfC
_R99#NJG#l3lj[W""9DBT_"nDCE:X'E8oiK'Q37HI$'&mSr4rk,6`f;lrnR\%uo[WN"Ps.l^
aM5+G.5DiB]A?GH(ca&6PsE55%PM*fU]As3VR3b=lX2GZHQ/^i.7"3\*K\;`sZTp:c`ZJo%5UE
MO`'O$\!fM'R=jG;oKJlaLeqFK?Jb7YBs5+Fm&p."e65(mirYi!I)+5H*$5S[E`HS,XYV(L,
'UY&P^9[**62+I[9$1m't38VE_Uk!WKDG0hZhGDGXJctE-.OK/`g`=5Vkh'*$p(MXH&DICSr
]A`,]AaTUOD9]AN6+$\Z&_6C=2%@naR_da:.l&)4R4L^]A[HEe'FOJRWaQOahEIFQf_M4LQKO)fu
DtufZ)$C$HRn_HMu.JORVKED![BsJ;`5ph5tl)MA^SuSc/'f[#,.[R\Fk:C1)cP12:rG55OB
r9#QU-SoX--Y<cig1Z#qX0/JDUHCG,t%[c9T?6#i7g@\!Jq@$G%`t:?S1pcImN0LqI$7KFA^
KAcSA7k$es([AN[Fq?1dC\b37sbp_`@#\9ACG#_Y`HCYrR13@A6`UjjrW(\V*"L$E28VS5"Q
LBE%e;hi5^(DORj49P=%s`cE1PO-V%hHe!m*Jk.4=J.*hVAKs`Lsl2kb%Cl[02qJd`V8R>SR
KPoeA;Aj%uKdc&o9:5lmB"@i-cXn3*Q'rH+;<c[9K9([r3b)ZQmZ6l>TqJnWoh\`62+`6@21
I'%nQQ<q[i`VB]A62UcYt6kiPCj>RgQq%UZ7i$Wc,6c"jJbE,h=V*P1W![]A&0:13;@^fk9!:T
Ook^=J\Od."3]AJ;Z[;h>m>3_`%<L'SQ7`@[TT+4dg3AP_/%Do@iQ`8ekN4Z?8H+m-OUB%$SC
/E$"ZbhiV6%9PCU,(WTaSe;+7le'@VBi70,2THq1'a=31AF'W*S!Sb,_Hk)ge7PJ@JO=&`I>
h,`]AQttZc?*)p8oTEVrkW%4"JJ$eRRE=`_;f1Z0bKI?>8BMMdpC'/Cd[j\+rOL#2/rk4<:,Y
aGJ;Af9gHMd&O8Qn(+.8Sf4MocZ722L)Vm+?-iG[#58Bq+*bkLXL!NF^RIpb'bGVEp)(QB3H
6B(#@t,#c?rc'4P/^WCfPcPQoAj+l>3LIq(YF]A,qD`Ii%_qSSj7RUF(2Aa;aV]At$V/aaN9,a
#^6^ji`ph/<EMAIL>)b6ndiA`1%>UYpVgXSgMb]AtY5Ea^67S5KNM!SHF?TmZml,lTac*iB&
Hm5_K[#"61Oe-i,)YZCiCV#C"'Hop$#IJ8?u_Sb[",?B&WUdEE^^^3n]A&Nrra!>K]A]A#jM12G
@.ZT^C=W90^q[`UWmi(Q;8KVDaUi6^b&1JbADir0-ipX,,YqGr7?iWZUtIN/HV$SI!i\pCjj
hJdg4]A8!SWPY?q3FEBcm7'e"O@u!"Url<h?@IRg'Ymk8cq=4JSa3js]Amq"9F^(RrsGI)q1mp
gotbl8n_W$o:NG1kI,aiG)_]A_A/*Qk20\*J>0`[PAXo=</j$N<-lT>I@]ACXfW"rhW"$"q6sg
8'%'uo1dk6U`du\P_eXM.\Zfi13,tQ9G\*)ihY@A5jb6f%N>@P</LfiD68Eki/V[CEEf)qO6
%m2oG4ErE@=2Bp?eW)ThYVJO(#SD`#U%Ee,-C*I]A6K.S,']Aq@8[%L7!_A1\F//eJ3ul<_kJW
pl+F7<G-e^EAlHqRC3f!VZd1Yi#Ft*`30VG^4-7TA9n+40iYn5CNZP#]AV>7r_58Th=(<JY[V
A!N`R::6b^Z)ERP()Js7#6qLIiSG%(G6N-dD*p&F@Kl\LX7>_bmKFi?ac>2#n!s?]AXc4X2+;
-"0)W'7q]A/,,7?.Mfc-Nh.jJ,L%kME@OfbL>G(]A"J;i\+or#.&f>4d@o3+fF"*_:1@S^nb9F
n?Pl@h-P0o\Eb?s^A5pRuY13Q@1,uqYI;rb.kOr]AS,lE7k?>4qidC46m\EL3]Ak']AdUGi*Nb\
iXLo)LIg!DF,NXl"EY?Q<G(!dk5GbUdUn:RK)*G0qDWB"BiEN`cp9.U(Fp&EV?O%aQ71J5_V
9R?s.]ATgTIIs.*Kn#00B-r#je6q'[#iL%Jf`U<7=jkX>knRk6&+=RE!io0/(:ASU7Cm!`T]A;
fP8K"`)pLGNV8FV+&,C*d8F7A]A(o6BiG*MWDn/,R?$pt%]AS[D?SBaCW.W\6>:^LE3&CIFqg0
3rTT1m1T@^F/ss09nR<K4G;I6'`T`$>_>UTrO2%_/%&1Qung**:,jid<aIKGW=KouTho;&LA
ZH<93NnNlc?'c'mAiod;oA&**pD>X)_(X*\F*]A5>]A0"S\JT4KnY'YX?/a/DuYRjYP7S:%fF=
+54L5JP4Z*1)qrE=.*Tj:Yu:D\Dfu1I(\1ni5DH(NWd1aK5?!m#_'5alQ,#!PmIG[V4HnKp>
N)r)?Gko%OXO__JJ61sT7>WG<M"i@Pd6_a>0^@O.<1j-7[\J]Aq"6N?5slSaF`&fWE&ra#PYi
;;t9VWMN&;T;tlWCg6C.aonq9[0&e)ZEe7PBo@XR34"pf%Meai\M-gW@!pE[=Vp_u).s7ak;
9E&m7uuT#>4RqUtqRKUUslc%Na&$.SM+OjpAJPr'")(#E'=[mGhP\X8,&#XrH`6/X*!nX9VN
q/Ir[8;*X%YA-X`A-7AG<%SURH7H=[WG'A2S/m8?=L_?jcdaoI1p8-ThJOWuanEL=N$:KY>F
M6*iCff&hiPNgI$n#mO`"#P<deqsa!)hjSN`*&_&ETE:K%?IuL("5S7(*mPEG:eO=<#5Li5i
'cUA]AWWFm.n!l*9'Hc46'5ME&E_eUlKakJ\3H:<Vr<)LGVU>r\:9AdL.pX'r&D#poWdFL8@2
e"QWnlJ@g^M&`R^&4biBA_-"-4kt4N3^Uu?<1GLn9)@^7_HsQhDDqP+RCj-V'!:j9Xlae.hn
WS$e(oUc_bs42/r:;Kp1c=_k0->:-?`97/eohn>h[lfDE\nQB=%+/*?k*o;UjnbOpu$O0k]A(
:7DlNpnt4YG/dWF4RE@dThU?\+Nj?"#Ti.&(4Zan#c0,1&bCKl#2tN$*,FY+6/r`Zl^asJ!J
)/6r=()UGMM9/b&a]A;9!*(3,Y(FQ$SDRT+dgj1-[4E.H@=2X7p/:h\U5b^7biMdf8JVMUhFI
eQ"sm=>Fht^lXqRB^o+<:8!a7)dOrkI+3@irj9tn!kmG_f^IR'kV7?k(rIj?D^fg"=>Sk]Ar2
,+pqH0/2o5?#'OX7R\jO9TlOXn`.=>JR3uuA";#).A57Det)2j=rj'/QddH=[9srR'^<4*TB
;/(CTS`>T4#@$!^rNb9m%::?tXSL9Q2%T?FA7CN`Oak!dg/jn)g\4;qUm#lLrlXR)HQ6T<4F
Mm'oYX/M^4e1Xe2Q*`%KP\%Y%M!%7Wl0hT%N`r&(LK4oL0kj1a>cQ@!3.59+n!-Q.M(QI*.n
XW787BNVAL%)'ZX=mO?NQZpF7I'TB5b"?ZmK+@9Q3o,N;L>-_D+\mO4SMBRfJr?O&;X/jP7,
%c`(qUMYTHEKL6Zq^XJ6!,1r5`;_Ph"5`]Aru$5/DShP\ubTO'HGSCg@JM.)56k4OT+u*+SN+
[]AqAe*r<Hm/rep:bj`QPA#BR4Pn74>c:*7V4=5>7k$:N=J]A1<ALNZso'5h8K\3\'W,5'\+As
)32U4)^JQUkg9j*8`^)Z[]A[o?/t91u1qb9CeSCMK=k12Z?m$mt=ZDjQDNq>nn]A8,>]AfV;W/=
4YA?#.0!)<AX4u(g,4(M.DP9uMCkPg(\a`jA:K^Wmb&/Q]AMJ(`.)=73Z%K7p94Qm08n!=RP-
K)=7;Lp4"Z1t1$o\Z9sh/MeRD_:BI9!j%_JL#?eLFh:P2`=:D`3<"4<EQ`4hGL\!-Ds%c3Zp
G,d/*Vi8]ANiSOceUU*mh6kjtPtB<P;&4c43rbn?al*;cjg8SA`cf%lU-KA0C"&SAR4;)i:@9
Mga[.I^\aZPC;*9^u0q7B=#V75FoHpr+oXgN=CpFDe8sG:B,N*rYm_[;i1Nla<+1bl>=1nGL
$*5VLn;GGl"h^PM06Ba_.ZqRe.<P?u;5%kO,k\Pr"U9#W5Xt=_s_MmuqeE%A;L9>n5DfhjQM
NGFYeMode^DDK>\b_c""#';XA+96O\X'f!kGgD/AACU<>br?a.8-1'W+QWe<uj(sZZY!R4^#
;[sqkrb=ff=[3J9r:;geUm>9r&X3[q,rR)LXV.i_ub5_.XPS^bTt5MY5;JF'`7:i:,YFbB&N
:*$%$\_F`143mbp858GHj:30d)9WaN>)po[oWF5oCKV,q`9g(a'U'*6j\31"K?T))fdibO/;
Kr*S=kFs.IpiiJ>aPE,mhBS1$%EPgo3jXNk05>T;-G7F1;9?<hcrjHLmcs+ZZ<SF#m@Z_86I
T63]A6C$tS^6jjhno3=M>V@pO-G!$cNKe*mMmTMF)p1c)&a[[\/P`V,mC;KKVZb#GFrYgcWJg
MTkF5_;K77dh^"U$QQJgD*?,^e8OO'fOmDiRLX4oD9B&8[:'`>':c\"<B4rr1*fYk,"l--=k
WM$\Qe/u^]A.ijC8s3/,U2c"Lb.OeNq+]A]AK1CbhTPt$lS?&cEn2]Ajc\i%(g%R^;ZW;u@.5"J'
&i4!S\PeU<GHPlU=Ha0.Fq?gmr4'dU9_ijU_C.=8g9'^E?BpH6!YIV/'Epg/KthUCO@AH1NX
b]AD!7cW/oFNA'R(eA%r3;?*Bi7K&/@(\KHH?duihs$*OPU-k4fJ'rY`Vt@V_X9jjXlLCclrj
_mXn-Atk4$W=NZnK9L_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_Cre+^P"P
Q6VK[dQ07s,T!gjk"9`V5qQ'"2s0$V:n\;ZZ[VnAfT>([3hh4Mn/RB2UU?pY+WPNKY25n$4l
hCD_~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="273"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="273"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1695796,1524000,2360814,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,6096000,6096000,3048000,3048000,0,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<CellInsertPolicy/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(LEN(F3)==0,$$$,CONCATENATE($$$,'(',F3,')'))]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF($$$ * 1 = $$$,IF(AND(F3 = '户',B3!='个人客户平均账户数'),FORMAT($$$,"#,##0"),IF(FIND('%',F3)>0,FORMAT($$$,"#0.00%"),FORMAT($$$,"#,##0.00"))),"<div style='font-size:14px;margin-top:1px;'>" + $$$ + "<div>"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3) = 0,"","<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3) = 0,"","<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="8">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fgs]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$yyb]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1" paddingLeft="4">
<FRFont name="WenQuanYi Micro Hei" style="1" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[XF(7L;qp"aH>F^UJr3)t&L&El+Is&SW.7,uH;.@b7j-64"i3M;"%4C;&Yqdi9*l2,OW(#!#f
S2L+nQ=:#l0,9gj?a/.l6),56D"oZhIdQXnA"CH/<c.W@:s<&C3oh/hX:o#5VEG\@D56G('t
giuGmWp@`HFq4\>$rb2srRNLQZ=g#&7Q]AM%9Q<K%+^3JtVRF8.Rj+;[KnuRW)BcRc4jJ>h$?
KQsdG?:gKDVgIHGu<RmRHEcp@Q;JA:#Kg(99k+@C[%eocJ[r0noBPpCH"BX^Q&ckL.sL"XNH
smkT?LH(4pRHh:FZpKK7(u"Kn8-B*?"n#IF(9XYqDJF5?<:\Y%T[T'`k"kHgAs#$b5ONkCLk
Yh\Vic7ZUJMm,5i]AIh4DKfdZQOoV\MiuGaN+\5Y#H'%WCc!V>(O2C>-aXRUhK:EHI%3,T@jq
;pY,o92(,OX\6d9"m+']A=:G]A#VM/$T)moF6#r%gG-uFL]A?bIUSR(#>c3`C*"3t6Dq-ooRiN]A
DjZ8\"Im,(OKBk`C+<&(=5PiS=_ge6+;R_RBqt&$kQ''#M]App<#4O:qgHA`JWiDs:eN;f1^h
F7ZlDq5*:oeJU,jm)Gh(WS=mp8f0\=\eE\='[Au2,<$=RnV77.'Y5uO`4RNDX0[2ndiNRb"7
Sm'[[7TV#9gr]AOZhGlf/#-]AQ`GVG'Eo2"O6IgS(V.H"f-$Paaj(,3k5HEZfP"NWB)ME28@4R
m/6eZB^P)GNstD#,AnUmla"lBUU8,5dYQ/5qV<0<f/848"B"qP\@=0,\7qt[#`I_(\BF;I1H
5ARl?RlkKslG[G+j?sD06]A!oW8kchB&!Orp;:ak'41CJXMGp)i-%D7::4EiiqL0f#+JU=L"9
I"/JNHc%gF5?in;iY4l4rjoFtIqKmu(4f5!q4`Tcu5*VJeXD#O)TZaR+.Gn:EhacdJ:_W9/2
Rs`@\'+;''RO2,f9C:"TA#QY;c\f.b$K^?2e0bX.=>kKhoUS<7D:4XYr\9N/Rg?DdZR'@W7[
(s*WRLT]At;,WL(1%2:g9-^mm]AkYM+I9^J?9MgC[L.?g8EgBD^A3Xq#"A@)>7I\>ggHlm=*kD
qCn^VSOC)EK<qr:8)PpFSZUf+)CN1d]A-Vdfc6qHe+-o7tSkp-WYV3f^_$&j7*(*o@rgTn%5*
OL?Dm.a(*2h;',I?^h(@Qt$))D\4/h_9-UYfj[E.Iu)e>e_dBeQE]A@HrQn;aV`9&[1>\biE!
=\)<N8ks#;IQA$(qom;o8d`/IdS1?W_3dm7i]AdKL1_cO:fTu+4\L`<rF=0iSH-Xd;jCVGATl
12s*^F<K+Yf8p0GB_D_c2l&1)b6PJJ6ks.l:CbRE+WKDhf=!t4Mn9pbhJu53IS%`&%!0Q)Tc
71OMX+s(i4e,9&3+RF`D1R&"LI\G[^WIRs@4IO;'g=4rT\l=ecThh<@s\j\eJ+]A]A=jQ!Vo/o
An+.Wh8EYP=A!gA49B747t8cujI]At>(PDSSiVW:$s6bUb49-k>mcm4G%hXW>DO&j.^nt.ukp
`I5[Fp//$"iNhrQY&e6j_4ZA,IB`;U]A<bN&d]A>g:f:N;bcDI[_t,pl+$D*3&_@uGXH#/U>e$
iO*$CNI"Rdd%\:6pFEj%s#/SWZ_\YWs5BInJc^`ce4hK)X[f$lj1t>!_JN%WPMYZ<h^BZST3
O>#U-#h9niP`8EXk\-h8mqJA>,r8ti?n(g4h]Ae\/t)?i\!r;u[6dqAOcNn/0B$3')Ycs4100
!Vc=$Z3Hh)A2eU!2L@it0Z`V0l9qKN0jgX<='RBa3p_bS0gLVb7SSMhB.G%=8Mcc(6=!m(Uk
E/ESg0-i-3!aY0N>2WH,rc^QEm'C[o0mDrbTp3A+S7QLFo1N1PJr9$\7cpf]Ak%HX3WIsr(U[
/p**Xr68QU5[EF3!>Jb(fD&aj/7$SU45k<K%qf0M,KCl2d)71<?B-&#ToF&?eW#E6BI.e01/
p'a4D#+u7lU(.>!9M3e3_f8^qFKlMR)Vf!plI4/)Sq`Z+["40iE%+eYs3sGFCTIq;in(SWLO
sTJCp\t4$!JV\H7>0SP-N]Ah6*M9aA2bejmat$`HeO7q4d_AaOVBF$r#9KPHc?]AuIPu*polI3
6:4pn2@?pVL#"p7Z0N)2`mZl&;A->nKNPdtC1`M1Q(gbQ\VCo>uc!ZuS2kb=r+KmUd9pN'\Z
Gn66;S^4?cH$[):MFW+B^Vta#-En)`JP'P)%Fi=\n))Fc=cRQlA6G5N<I*'_9$5Hd7Flo_(V
)4inB.kg_,`3(L$cf-*)#)YDjGVR?q>*hjU4PB6:.o6,hb@0YuQm@hf5c-:Fs9!G3K0Z&X@Y
.G=K/W[ENJskps[Kfe&@Br%U7)n$l^OoZ.(61On+&dHGHI8nrt3O!cp7&c1+@AqS$/e+2_IC
%H-28_9d3a]Atp'.Ghfk7N1><Z77j`Mi6:3`<oAci'H_3HujX=V&WY7mAVS%aIci!UZa3HHUV
KpPM!cUn3bth;^7;6,jFI/QIF"eJ\6o%"O*P!en#<,pTL1f/`4J[r)^&TLC20Hgpf3;/,&9`
dbr9>rBieMURV9S)lZSZKhotSX=^]Aq>KOGTZ'tTTC@#8T'##Q-9!mlErHqg;EKt=X;W"ajoS
4Hh_dqllb,D_[>>HquLq>md@-)*8D,s0f'eeD8hon;Me+gMa;R`i>7$H?pRQI%hr7If?PjN8
0Sj\r&*iHl"H<V4c]AN\4K7&stX&hQHQAg\^RRdY[VnM[1B4bit!Yl]AUaKbJRB(]A+/?@Z[7$f
2`-^Z9Bc,itL6nOK@:R+BhIKpa0(C`O,S6`,q"i0`>TW;*04u3'/l6@9JcdAT/^JjCW#sPV2
gQ!^^n0Fr$t>ebn_0WjpXiU4LCP'AtQ]AGU9'PF03t3l-o)"iLs:t]AT0]AK[r,/O$f4':@6ZM%
Z%l,TQseT`FDRO;mb(,'`pR0?Lde5`i%qcgW?YE"qY)!\h7p3*?DpFtdPthZ/,_d/</&qQ!\
A4p]AA.ObcQ3`0C&b!c`#>UPf5Z5S5Nm0?U>3>Um=(Wr:"8/X3q`AFjn\Y*NPCT:EB)t]A_uuX
qL(P'cNI6Hs%,c,kkFk^>?H2iC,ib3*La]AX44BH2q1Is*3dm'<D7i.Gd]Am9U+%+ji8=W"-4=
I2H$i!G;u>*)oH<e.5G9Q.-c6Sl6gP"gh!5RWO>i:X(cC_iRYOt%'Ke5UK9j,pY==P#qT)`Z
iBrZlT.)E9ShH.3NH7YrRVOM]AsRlDq@="K0(F>PoPbpb@Z3Sn:r<+SL,.%^ILHZWZ_2DjH?t
h"(Y/Y.:kRG(r@OSAZr*%oO%!-$VEZGS.!U!A9V$kp/5JVrb";e?=8m3;?%RP1c^f!88PmKk
=fBjuH7KNTUm\]ASqn9DQbsBcNi,]Ah_&kW?gJ)77$s]A\'h5,<W^c,E:Lha:J8Bb`kFOp8,'CC
k8--#=p!suSD:nhGpq.68INB<l^M1_Qdo-muhNhh:HWd$2#+bg-0d9'4iD/(&J#,E:F<5)mJ
d%gs<f4CC<.X=<p!/Y!>n.a7*qWVP!T-HGEGLpG+4n0rp311"I7J-,'Q@c'/asGu(NkJ#FLn
>15e&HEAb:s/V_t;*pSW-``1A%YjIX812g3a,85jsOGhUlUf`mjuf.Xi?,PM_,o&>O[m]ASP)
Nr'hNDB9%<c0]AYeqca$b*o57b"#!u(M`WUAWuarr7at[Cli-1.2M[WmJL<C.)s783m`lY_g/
j72+oV;ej^*4k;!_.[95?*r]AVihd+/?5.2R(3Be*3sXJRmMoN)I2F%9hGOjQ(]A9SdJ#*-f8f
+,bj:k+P;eUhS>)j9<I-Gi:%cfku+=i9ZLpsR3bQ)>i;dB+h2gCiFmQJY`JcjZ*(?,%."M->
GON`s#B8`s7M2qP<ZSLci*<7s1^nf?NC'!a4=8Nrr6sWo;dkWSA3<CCJeh.Vd)6'ln7n3jA\
+0<=gene6(1I+Yh*5e(9S<a.ihTYJ@nk$aD&nW'X<mD5=cdp`BU/HZ9%)q^%2<ILStQ"DrIp
0c%+QA@$a[JSOt.3,Ies6h>R#Op$*h>VR9GE:bl3cc`IFp2GpF-#X80hgBqP3K>ts?-*QNMa
J)dmhVZ5<Z_"l@2p!?PH_nC0U_^/>F+\UcEBL<3a_$Xp"iIt/gAo>@t)1og#/h.dfTSMW&\g
k\[]A.uUhKa>##B_#X3@ni7.G-laCruoAip@)\N06F+VVF%Kr]Akr:hS@cN&`E"Z]A7\L4*LsuG
)#It7Eh!@>E)R^]ATQ^<Ncc?*-<r33a\JJagA6)2OiFP0d&d)3El>;Z,d1m@68"318[,S(WNj
sCpn2"M2R]AQTKu)fY>;F[_)2hYdZi5A1^6ks@:TB/MH(=&\rCTB_$&j[_gA,U=#tD4f3&oE!
%:nQ%$7b'LVo)MNWT["5.AIE?r4?XejDKn7CqP%Xhq049`4CHMg4[c=X3Am'E*nG6cAqq<cT
Gj0gR@TND/%UPU?kMfI?"(EjRb0G.P(dQV7PtTAR0"8@NoQ<WA_ou_.G/eni[cN(^J(q>.?R
/k?NFoM"94SR*t8P]A(!;Gm:Guqf`n5/X`m:Xh7u(.;F1m+bHI\u+o/Sce?$WB+`d@)Q"5%Uf
e<RFX>`UJ?oH/knH0HPjQSYZT7gY'`@fWu`lEKnBrF/TlLL@nC"LefUrH-D\ZFXo?i-57de,
$MZ,$:jW=T=\h-V0e<d_(_Nl_YKU2P@';C&A/Xc8Nf/.XNQGcpYRYH8QD^BHjtj40'8J$XpT
<'``A-8%uQ$>!b-CehU?N7Yj<%sJf?3;r>\$BlmZ0]A+MI`V=j_Io@5303gU"]A#*HBL2!Uk&`
!GaDCuBfZ8bl?0Q$rZ]AA5Z@?!g^n'GaCY+"atmWMNlq;9r%eGdWsBnH"pb8U]ALF@7g\\Z@A6
^S_TTRT!@J)?Q60efUf)Sje+N<.7UNr.rb\N03RBppfuk(g.?7N3Z:SOG*r8h#^H7jaZH"4(
-*FqE8?6fBBn?frlaZBqs11Dh(<]A31l4t\IPl&>,EP1ufNB,ggCitO(4'sX3!CA&Rf]ApV2'%
F/^jCTYUk1)<MpO%_GLhDj`a29qh?Zqj+-=P)mH_#GXk[6[ZIs^%jp6O8R3H'l[;["3Oa@VZ
r89G5_@J62LLUSBgeubJ,n9[YY-)`fk.^Z/OX3V27=,Is8?N3gS/ZF'`HnX,gDoB0^j3SRUh
G7/]Ak0=MD%L:+E8g2K7_;dt2mQP`%AF-!H-.s5_JhIJL["8(I(m$A'Jil/Fcgp]Agh1Ho`"j)
i%e487:?@\:G=RZfcnso[8sLe2pH49sZ''`FU*4Fm?ED"@i_0iG&)`g7ZWtHY&0u,CDDN;<R
#S&.G<hh39c5LL)pl"c?_?OWF5(I>dt8"oeXBtdP:Q?OPJ\?A_6G_\L42jXL=7`gHkbps+<b
ilQVT/rWO?J&AoHD)'fta;5ane"@hd#7<:SAM#8P@h'M5"0`%ICZMLk*3'u$UXP_/B:m=!+t
'j=P7s*j1@0sF+1AP&*>[6(-%dl72<i`+mP_rk4g.bKlCio9/m5@cs[V1\55XL8j$Jc`--c!
/2B*SR[IReAZJ]A.JL&A>>UV=Ks2m-LFA!KTP'q%j5'bhr,mF4+Ur:q4,9NMS%K*ecPj5UT`f
7]Agu7(5O/*3J+E\M?.<BjU)rU!e1ig&Fs]A<=Aki2YQG,-LQ:D["WPt1YQA;nH*=Jn:,VE==.
%cY/)abZ/?;Q2[n884M:>[<W:VT.'PSWa)YpLZP3PK.m'BQijp$kAR:prBG]A8:I,o&@$+!.M
]A@g'_h:)6#Q1Jmc3VX;YEKGHk!cLFI3e+qt,l5$,Lq82/LRiiUsLjH;htq8p;"@3s&X?2RWc
3eRiG03d(O*rk^O*oKX),YSRR2#bGD'Qmgt^J-.8]Ai^IoRB`@7$9'VD-)rM@<X!]AjmZg;9fM
g@TID[k)1:Z#HcNa`1WD_4V8IOZP$eAnDXB[Pn,\'r40qM_<(#\ml8<*0.]Al!'uR[f(,>=+I
!aq,rb)i,^Q^Q.9Po+<qqct`)r]A1VfnpYT5(.ZhW*:VV:@7Nkn2N1^L`nPN^*m20+kf[A\O8
q.45kRM6rBN&))8<X[l1PPGZp#MMS2hn-5/+Y"fLo-_VL\Fa=0TM@@\l1Y"(j5ljLn;L,Vpq
>AV6)a*Q_V(jLr_u+1;pQg`^T;2c<F"8X&JT\4mn?WW7sNe;:MOe=d7Q:L)&h:2hD\t;h3qS
/`q^9>:tX&nSsqn3_lg^<VXXkGuckO`i+BRbF]A"?QoS9jl]AjbFBR?=jB%BMmJG(hk'p8T_Z[
3(8ei2EtbmcA0SCf>+ABl@_7O-(@<LLd-Q+qo3#;-9DN*"BpgMI?1a:J!&Ro)%q/>N\Q/b77
sY8MT,`54!]A8dtOe8>#m-e06g*Ei.Tr5IFV#.Zi#UV9T!JBUO;)Qo78cIuC0[%]A*A>Qb+B[s
(G3B)N@kUo,RaPor+%W;29QE/A&Th[W^`[gj2A:l;_qjR]AtJ^434%lnVkiqMFX%U&<81;F5+
8LjKA%H9JimN8X6=)"k"ZnL`l(*okbXcrf38D^m6@S9lI,<OFOlHc4cpKL'95<XRU$e^-/;@
hlb,.5W6i[!"qV=)+3TuQ>HD*(TQ8i:4r-bO\qUiri98($DuAahfO8nb4Hn2/U5UmDJUn-+-
V!,1?%(5kn]Ad$s2iGc@l"7>7Z?;*A(ZT-SqnKMC4nVhF'*V4hlKt!ZSq*TpK3>?":LcVmV>C
)40[_2hoM^unn/lO&h5Q*B/Vo6bH-h6$A#=7q1GcDr.srNp!.&`8Ec9aX>M5Il9%C\0O;YW*
.EB14)d6H&Z,GWep+An+p?(Q2XTCKQ.a0kr/s&VY<&9(X&Qh(,#TurnFedho=F$G.YscC1R3
";dOMRZ/NjWY4t&lq-.U4NNEH^?DpkJagK/_DN,4BtD+t$@A371_?9aF!Rf(Q<a,p0HP=EC,
qC:-F(g9+*.2Y*_f=a?`g?@B*8C65gme"sUj,(Xsk-8-_:P.>dpukStNhH=OFJ]A@Kn;/.6#3
9;ArI4Oi*8f(J)&'&.hR,1Hi+9Mu4nKncQ984B$-;*$0Xp&(m2r3%i`XD^!*a&pYslg,5_M?
BjcA@8c:'MQ&oYR#=U";7n!qHn.Mg-Y7s-Ge,>M8<q.Lj;Rp)B(=f_Ri/5p2&6dVW:AP.,d1
Vg0Gk#0@[LU,O3;-B^qXLkgu8Qu1.#-JEP.nR?4<mH6MKcIYLGmtN*i&9;mWnIpr-./c5_/A
>Tq>nLS=ed6UpSr&IM?(/7;N7J=0.g%p(M6\-Jdb<"U#ieo?*+9>Qir>GR2j!V.\jWK#>B`*
-KbEdE.cf?q5QVum@kh;-Jf"F1X1!gNGZ>%Iak+Z!uoG?eg$N*aQ$uV#.<9M]A^aIh1cV0pe(
Sdt;g=sk@%,c6+ge=&NR*8<@:U$M?8)Gfo$HklG,<%9K\uUJbcSg-T:o+H^%><KoQ-%@B==b
3392sJZ7d$0(g.]A!K:3FG;3/^?]A/r"-ps\&UPmTn>CB*&7L9&9as-qG:8PY!IXY(Vp@2eltX
RaGpWa:<]ArEcO(&#D/`je\U*gQP5oI7,eo]ADt6CK*Q$j9GA;;'T!Q.eq;`LYg"W_Hm'nQeqQ
mt*Uc^Mp`rC>29^Em7?47@<$UcM"lVFL!=Q<+LENn_Sm>)42>J9W?^pCQl$LG?71!BKX,N_.
2TERWSO%;kEtsZX*4"1_IoCC3^)+(=p$`k;("h?MqUjLZ)7qob`!G3N5QBmeN*!OOH;E>Sl?
&AO3K87<9\]AUE.:?]AiV)k@_Jdiq8[/!F2/5UuZ/AsA$Hl>T[Q`!<*XupU,k"J':%$lkOeGf;
[42Z%L7Lb!r7\0!YYK4#(@bdBPJu(Z$aA4Nq>eE+Yn)19h<MmIi%VasbROu5tIl"Iak*L15a
6I0R?6LP*h5*O,e@bU>n]A8,[b9Yf+E2oL_USUP8,W=:8Fhn&$>n;DU?)>@GNKH]AW^/j05*-C
nN<P!%38-YKp?2@]A=O#'=hGI3R?XW%]AUDe8qHPi[g#,eBpnl34ij.!C=eeg?)%9F?ee_'\I1
`83SZ("d<<4S!8:YH3O\\(%f.hs"\[ei]A4dbj.AB$OiW.FTXEuB+4.29!!iU.RFbLH0?_EVa
^.3I!26"&^R`RKpJBKnqShjCM5V9Sa+dfnafO6DT'`1D!(*c"Yqu1,/<dY0T-CVCK6T5RSC>
&jX$2Yl-\5WdF,mYe<-uo`Q/E?$'<B?S4koO)>3@_O%%T/,P]A4bBu+:p!l#dN%<WM8IIlPl)
-$1kIRR43an?D-@R'kW60R<3>kugl.pL^hlY0,8SfXT?6UGg;!Zp<5qs)/s?sHK`B'U[k!GN
uXON50^#8W3K)1Hb`OE\OJ&XWhuA*u8Ef#K;$nqetdmk9[J,;0>Hq8b9bqR7EP=LgK:d(--0
CAIDm5\?'6C<nQ\H"ZH=+2,2?X*?=C").c;hC:/P*NL\G!AX1$NaH`RDGQQ1ZqD-gAi+_aKT
APH4-m#,o^(2P9QQNINBV*gIN'rU7E*=:9>$$-;)#9t.M<Im;Gg*%F?XY7`j9fnI\<[-<luE
"7AQ5!J0`*c(YoX_"O.W=LG3JPG!5m?Y%^tbC?Hgh/Gc=TV'_ha)7A3M-r+5Se2_R0D?*^BV
;.EJWnY9"S9]A"/Pg_Rro'G!sdC8I2!k-`LP8Z:/B,:[6a;4Hf;V.Nd\h0J;DU1MsSt_<<JK.
lB\n9TEF/iM\D,Ji("Q'f;p>k*fXR5^9YS@A:)jQ_Z'a;G0]A$:D>;tfS@IJcc]A4N)U]AlACQe
G^+HI\po&Gk.^ZJhH,!8nS.R,IfS67YCHFhhZ,$@RG.pI>MHVJbW,<EMAIL>&r;D?SP<CUh1
;U!$c/'DT]AHZa*A"P2`]AKsZlWq/?LMJhUWhK/)SB%(S<V_S'HZC6:s1#Yu5_A['RID<%cRP^
@!Y`otS9<H(.a`(T<K5Z,DiQ[9=GbrBV"na9V8ekB3tOQlVKM/0E>"12I?;#Hrdl-H0Y&/?c
9H+6I\b[F08<7jSDJ<7%;!%5'dV'rDgVXe+ALB2;7M3a32BbVR/S&)bC("#)do$)N&6qP8,O
Ou4**:AE-i+lCX,*Rl;X^3:X'!Q*B84p_iGl#T,rEB.5]AZ*m?,L65Bc\n>>A*QN0soIjCA:#
]AY8\[,<'d.3913&W:LO]ASAcK6A-)#,lXW<V742TOAcpi4ljQ5rC+if%aF[lNI`Q%_T.ma>`:
GgA+,Y/ViUGsWEaOng$gSInFTB4'+[/m!OrEaZe0G1RV)CZ_NT@Mc,.%gg%^<?3#ca4@A/Y>
YMRuJQ_N6)\-OMrae+F3YCiB.!/cb&anPhW#0:"dnk5`bS.fRdXX\rI3UsiRL?Y74'j]AC^oF
E>]A`Y`En[-UBEgW.uTGSGk5P;79TQY7RI%&f8u00,ImUkU4\oN,f31N"GdZoh+H!n#FB9nCo
J\8H/[IoU+bek(<i`))(iAR+qKg"q,T&5ktKW:$dMb7?ckg*@52h.C6LYINm3+JR,\Mef)(!
>u+XCI!&Nbog73anW*m0))Gg<(#Y*(PW@Zr9*[-H0Z3%3Aafe*YHVleBo$]A`On`CZJa0mG!9
sWP4)T\,Vs:/Z_K/cc:SY'V8;7KNGg^jU:kVd#3`4I6%DW*Ybdu#SAA_)[S3q4VC^5:+K%Pk
//uHN.eW,*8h`?MfTg);$Ccb=9A?X-h'X)fIZk\RNi%Lp)#VEBMU7t?8Aha"kHfTL%?pBG#W
/J-..#VrPP`7)/?#FQ7rL`r7N_m*.l#_.0?GE5>7F8[33;;cSm<e@u@WV&>P05n%XM8KK;!>
Ji7<$<.j^C3Obu,V1kfg(S:RE6-h-G`oXeI,`"q@@'1L[q[MS<gT-LTrA5VV3(c(.3%/BXrZ
TE)?pKgmZG5uQI/p+clTUO436P@+i=N"ZI7V]A:T&1f3dGpig>Z,GhLrdh#+!\($aF$$CM,#m
N7C_oV'6Up(47gUA(LGGUmQM"^+)0_#fWPI?mAm(K"c<eZN&E?%Df+T>\6)2-hq3FU$<^-NP
gS9A8c#CA38>9*,a*Qk+OjXNnA"-W]A%Q=Q.**t0"'9TL!3GLnAWFHL[!c0q&NW$#K<N@SMO`
rG^`LW?W6e?^*PJXorQ^H;enh*pDO,BDEj^Vbm;>6(?,S(Uo"2frp!RomJ-%;ZB%YA:PG@A5
_Y,\T0R>)uoo"bJ5#qO>#:^l.(0+Z]A#&nm@Z_S?3HWEk5%P5-1Omm>@hLr.ue1`&bs1X#]ANF
EIVTGp'nGVZL*?e!HN=i/"ni"c'Ig[,@'fba(cq:i0]Ar2C=O_)CqFQ5^db3<D/j&_m/50_Pn
=6%A)3Ie-kk.>FB!9GhJF11"0=RTKroQS?Lna8<rT;K@XOt@5pN*^g0dkT1jHU4CX&K_1h33
K3,!82U^$Tg:VeWF>`<4JCtATu]A8kQHl\SZ<UI:?mNin-cbg=jn_q]Al)r^hGk[0D]ATlT2T4'
HM0!OO8L@#Q_suRYu%^ENC?G#4YFaF4.1@7F]Ae"`*P'r:t1![W\9(BB5""Y*a'^-!_'i2Sf9
hbpe/'>`FiJ@r08Q+eTSCiWZ!h1g;f)g70n.>;879+q_O`0`=V-.qC@ZI$LV#D1h+Rjq-8,O
+^!N=p:iTmGWI!WGh3=Xb.>pnp9iU[94.[t%#;PL$"'2#p*KXupSB[(TH^*%#.>,W]A7ol[S?
%X+?]A2+7T>_Ec^u9o-%X-sB`@'sBKO`s#2kS";c@VuaZ.Ab8<b_fa:o+h&.t-5>+clM]Af.#3
hDDLb@TZ2f)T@B8oZ(7SK?9ckI+IgTup<`T-6`q8e>5)rDr;-Hhg9smiT02S&odZcSG"sqYE
K-tN"$J$@#OtgQQUmWk947@4SO*aFF6!!"%ur&CU<WoAIH/=ZK>%T2pa!YNS@O\CZ2Ca'?$#
o0Eh3Deqtdu`7]A@u;.(Ze3Ve_!hF>WisYYdX"DD?$KB%7-A'E4HN;4*BBQWhYhH_+@#OrO0R
#G6[VX`e$IMmt]A0p8[['BRett:i*2^2e+u<nDQG"1BFZWpSF'/W68WcUa7C;JFS(]A[B6-gWc
P1'&I/WS'DP@nm-3\<iN?e.r;>rS<ub40m4iqED")ETAs]AK?XB4*Mj?\^=DYe9LYJI97\fV.
>h`dBV%BU#,A,Rn"fSCV*mEC.b(.#t*@&eQP(K^$*F<@!Rnf;5mgMS[Pn,oBiJ9<o6Gr2^QV
r+GM?@S<.#BIo"*\YRUa<U1TZ/>i&F%*!`E=sC=GmT0"*IckFa@aal?/"Mm;ILpPnI^IOWnq
[ijU<Wm!8aFiRZNeQN>]AKKY<ROj5Y'St'P.-!Jho0QL>-3!!]AU8j%TDG]AN=i+"2]A!:'o0d:0
NGSKXK=Q!1/l&MTOcge+CNk)WcF\mH"PK<POfrrqH@;c?[+MeglAYZ`#HgCe;#:J_5h<fqEY
Y4/iWGPd%cZ%1VT%>p-!'mcRHgat`Jc$fU<CW/m#*c&(lJI%W+7pcHR@Y7KsunK6DdpE(hCh
N`Ot7>[OYG90-$A&361+5)K/s;IuoP(=I8q)OraVY4EUe^S9#lDMAZ?3njWAVO2iB1LF&&9V
(hGXA*69[]ASF@n'S-^_T/fU2Ce$,lJ%K%8LR8tk3r[UC8F(o3rWS,dQ-beY`8AEba+"\F%M3
Ld[jNMf5<C4(i1Fqi@0TDA/Or.R\Em2g%W>`<Q@F;E^QF?N\6/FTHcA\TX*?(/CDEK:AYhtR
.>;1Np`!t<nV%0e+NeX,rVgi1^/QrKU.gMq(\tF\`,oW1reBf8^X'T@<OKU0O%_I9Fq.[NRO
"<Q_-#]AO6puA&3\EI?]ArKet?r]A<GnR.%]A%^L)p9iaj`XX=DZX3!UkY/n?Or:]AiTk5a0::L!c
X\q>Phr,p48/)@s/B1=b[XB%KtkDbu,E>355q[,I>4L2?A/4IS+N^e`!@&V%q&K?`&#^A[)0
('b4Dnmn-\>!o0'sX//Z/3%b+J%0aN\Mf6pFJWt,<Sg7EQ.B$VAUq3(%Ss"d%\NFrh#=q:1&
6MR<R"%mRq[_XVjUAGIk@=gpXr98qU&/eZD.r7r&32m3suWcRlcB.7PP]Ac0ZNgTB,8W@sHI5
>d32ZmAm3b&V"nuCdl"o3!A^N/cO5)NY_Jp`/+1lr3;=WJgnfm1?d3%6VO2Ep+M_49nKOl&m
e?BmVd412rB`1?.tK\:3B[.!mj;(nI8e?kdn_-E#iU=Yq2[)j_$2!XYY:C+uUU>s$LR6B2s/
;RVfK=Q*/Q(<RFD0"3>cfQ1WG=ZhiQ_$SBLBHA4F&G1PSA,]AsZ.QPFS?iMP7\RW3/+^t2h&c
[=X'5$=)SbmN(>hKLmcPjC0^XudF)*Wg"UC766]A(>p$_Js)ctMPstKKrg")7I=IJ1CMoJR93
Tp=f$B>7f>JblJC%eX$q]A]A8>b;4NXXi6Z(k(iIIgq#:(u209J_';A,.L&fN]Ar_UsX=N2gOMJ
e!Gg"R7C.0BLHEDlT\[+a&4L+i`GfqB<8[_UIpuk!,ZW[>*e!,)OR!)$sHQ`XA[kOV@'uQkr
8:R6_-m9,U+nr=8<EDeBoa.D5dAamXu.A<Yb?,6*(6R:X$-3V:7ar*]AeLlokUu`[4]A1ZSB_A
DQEu!lNgTa.`qlsN<tWtJ)8+3j99HWhcr;GO@)138!UoZN2?TguQj;D#9ZkLjNT(Rtk)#>VY
S<(k0&0&`/op086V!DS^St`;6N6ZQ';a,siE%g)<h,HDc.d2<W)M6,I]AKfR]AKD>jrMnD+2-\
.f-T6K\CU)3!Rt!2inp^>8N<=]A`h3V00UH='STg_7-hq<rS^Cc\a,Ef:H.eMp<*3am`)P&#+
I3.EFqlJ/M.(/J[CC9mB&B"p2)1+_)i9sS6l0a5pp5tja:-4]AL(^R%^[AqQ$b5Hoh:ErSsO$
S]A0gJ\?52stU3els\eo7C3)0%f?G_eC`5R_98;?li\2JgSKjV[B\0SZFf@=@p:[ei>4%<;0#
YNND"=a7/o9N4C&OVaI+s5Hg"?ICPX&4]A*.XoD/(o9ERbe]AK0`1#jda7[uTVn;,I:jd@WR[E
kpM6+N&U)/Dmjbj35cL0Q\&WmtEKAC1@n-V*;,0Z8s3Si#oJec=_I*DFs'.[0j/OL1.*pFWR
RW#(7N.2'6UjM^Cf!2!);dHZo8UoAORo_X!/lq`6W&DH=7c'AiVMM2S]A%V*>HBh%;5*pl>=$
_?EU2-K*tQUu63O=OlrD9T@#G<S`Zq@GBD.7##3`eGPVRpS\kTc#5/V<u@M]A5!i7=3G]A02'i
K$ZK'qH="QCNV$iNe3+]AprP\9MGdd2QTc3dU%kQHS&`cC;?Yk%#Wu7Pq*Nc5`ij5k`t@!!eQ
8?F;<kX":X"H<<-'E7KQ60L>4XS\#i05<d6Y3a7W=i<loL/@=$KlW8=)Au;\O?7[7Qbq4j6"
87aJ8m/Ib@/\2C'gh\UZ:%\_]AS&nNMn2d=]Arp8(SJITNZZV-p7#8?rEHp53k#L=J!$0M#gj#
PN?=hj*$P%Fn7F4TYC#mDhn%.^mpEpKe]A]A>]A.(<oKDIH05Vk-X;n-iZI%E+\(Rlig%KNmt4A
%rH%Xpr*Ui?/(Q80KbG'OI`&l*dGYVT8JA!gL@W-UHgJi'%YdcQf_AU(jVP=R+H*eKXH_:8p
Kg!D@l]A=kudI<omFke-WoQ.epKLTl2i\73n]A5a+I73O;Rp3sHQ&,')E`SG"muGgCZ7"@P##5
H6=\O8RqmRbl))(2?q<<!T:qrBbl\.3!:)!VD(._Dmu5n(Ps?O,3>q8M&otd^f]ALW7o]Aig#R
OG[o<T"eG:0H1;+[0YJ$m#:;nIsl/\4a1(V8nCaiXC.aiG:3uJ`^/UH='uF4i4ms=t&f`c-$
:raY06*6-a4YWj6Y$OlosP%bslZ]AeeCX<kDTP^W+Wm)4Y%!$`M4ZT]Au!K_)=M$3l..>foS1E
G^:s?G#&T!Tg26%!>s!O1*Y5JK5T=g+F2]AT1bAng\>3)Fqcg.3MZNPS\ne9&!ZH+8&UlYU#+
;FrXBfU*\>CN]A*:7lBenUFkG"Is14jb,2[[6@=L7'Hq`i/V=qD6^bnG?.t!6ga@l;i#ab^M#
Sp!:E%+99u)D]A+;<6fF^l(Z#I`N.6/rmb0,Pek4'LM-6.=4GR"$:"+=j;oKgcB\:]At)rH/=0
O-Z?aL(V2q/(l-=LP2NU%%?K;k_+kMW[qm-qluPmr8".M]A_Ra#V8UO0%eQA"7$>]A9h`9)WmF
g+d4GP0s&%*:"KpM+W9olWe<TK`paN*gi#!n)2&X&TAJih1:LC2+7IuZK0XU\9-2qGSCm45i
P4KIu.!iAR>`t&((ClngU7-hJ(3>X@,6A:0ApE"\aWjQ'nk9k+jB-U=clq#p-*V4,(rCC.b'
Q4RYo>?oQ+WQMf&2/AFk^6?HBaR>e]A!=Y`OTKM)&:3+KQ.+nEg,qCCMqM`IB?g5R@220`,2V
/:q91A4kF@dC.6#_h=/A)P*nmg5O;>T1g9SHVQ?\aW!dd6q+_8KELkrqN,(l'Zs%$V+R%-1]A
V*bp.%J85JJWWV1pU#63\`TPeYW(dhY5!eJ6!$RNdt!ep)!TqS]Ac;[2OKBRTS"J:o@OYt@/`
kHr7-_uoIZ4Q.S(4I+@r8dU0!20Pe\!nFD]A,qE-UiOH!#f,7q/!-&QiPgfeG->P7eRtkMIXG
^Erq%#s[i>nQt7Semea_AsML1#f3"/aje.Wek"P]AQ>l&<Y(GVAgCi$se\0%nSJn-9XOr%&8&
6MN:qg4uNtJlMHG&Fa6%'/n40\8JlHN+hEp<5TXcD=C*T5]AQ)1Ylu(j3kYWX0%]A^u0AE'7:b
F".]A0!\D/3C#YJI`^74.@l_OUL_0'ip+bOa3KY29OLumN@-]ALVf3I=L,j?H,>f2b\)"`0LK!
"L9PA\Kr-46Sb'VIs%BjXA/i^%U]A;:Os*]A>m=DV[M.U(rZVDMD]AjDD[[$sA@K(BO.AQuqmZE
N$iAc(?6-[a%,L_O]A27tO,!F72l9[qn2Csc]AIpaf`VN':ZJ'p\\<J"F0t=&Wb-o56!nIJb.R
[M=GYK8*`#[e7/SAH3cNYD%q<XgUG;eu!tJl_)se'uUsG3),W)fr\.sC3DA+(ubhtH"eO'?S
2<*((E4I*Jf\@5SA9gRF(/]AY-mRYZ-%f?S,\A_Cp/-rA>P;d$`aj=!lh]ADr`f9NOj,ZiQ-K*
u$hOc!\Xen3_tSr#BJPN%#b4(i)?[&1#u23Oho8&a-.mbeN+-lj.Ln+@K.G((/02!X2Xc4&(
e!gYo<;GALLAX>LAWVr_I\!%ZtbQ7ilH.-K*l<4XeK%k<(g&%6s8'Rk=X"d9!8qqiB0CG.;,
<m2!,MP/5ZqG5qHtALCI;N+!Y\F!k_IV>&-Bb`e8q$Tc!dQ;luSBYgn;3]ABCbtaQd(dDg"nA
QQ#KTP9uNJD(FdR>N:@7;fuL@g04;uH3/)gS@:]AToJ35]A!7Vq%5%="]A#&*ugdWtW\/%lPHV0
LSF$\i/;'EK<%$Na2Td7(#N%\ss:ol."/f\[*C,#]A(G1e9H;DVOrqB#)k@B+EH/@*q:9DGG.
3Q>Xg<$F!aTa.IkAE&mNH$VX=X>"Rboc\ZuVR,,8-#=);/kc/q>RI[[_:Cj>D>(8nep$NZg@
\S%g`k[57d8;1-J6E0V\H98_s"sQNEZ`$##C'h&p''OGE92%WGUbNsR_O<o9!$^=f')*rXPq
Z@8gIoK$O.E!7Qc*IfqU[U^DB]A!WC[ZfV/MU/etfIC5[M!Z@2qYIPg?4<_0Ur=B[&PteY+3&
QfB_9ijE>SlW\18+de=[mDT`Md0Qcr)<VR```6U-)>]A[0r8[Kpa=Q3+;@_W^<(3tpEa7t1d@
%=>MFgC*Y$FEE0876LA&u20+`tX+_Ii1FK]A4WR7[6\:\K3[0TCUL+4]ASZ!eO*1F+:%s;]AJE/
YA4i>a[fWoa^8DNng1bL"\5u!cIg9"Y!DR22l<kee;-e'KN\q`b<DJjdq4QjVjB.90V1e&Fl
SF9c#T<&$ZraKP?TZu"\Cj%`q@nrKE+RieBKFkLk=RA7XiNZo=BE&;X3PuC@*Gj]Aj0(@m^il
-/IL^6jF^pn:_"IL";Q5ja-^?i9IWZ61)HI4@cBYmFk;J';eS,U*b;cSZJ/`4icO8]AA)IWZ&
<.M9bKLra?'mbiM`Th&Nm6r;b>Yk7n`@cC/)$okU;o%M':1a7qKr)NFI8oV1[;i&;\J#Jr]AB
X:Z.Y@P[ZZL^fOh!Wd\8c!oCDnrij7$*.'Bb!j1EhHAhm)oX<RrthUB5qHP)GUl.H7N7@bS6
9%V;SlhT8Pi?B4KXn<<O=*!K[c30!JE%N4"rf&2on\&Zek,7rbT?a>R8mO1]A]AB#LH4<JR<+T
Xio[qb$VEm-$q@k;[:-NDO#SfD+0dhYAns:)$U_nTYigR(dGDrk%$aYC8G(+0>mUQA)8"#M0
[(LqAta1#jp6<fJcaXRZI8gTg^_k9b%%4lSmYBg")V@5M7P*Kg%1gUI]A,Ba]A3.%<F051I1:d
4L##fCjSA8`tHh^q&Si>m\Eb6d?f+)2ji+lbE:hH2Uf9E\c/mMaYP)`Mm:UieJ,+8KKGpYeC
E+FgDT0+$b['W5go+%!4?aU?g3J^Cha\9c*6)>i:YpfCGY)ujm>5'T#CE63&%r@nq]AWm]A(r\
IKEAI'&h,1hE@#R4Aei=H>GC)aR1C+RBJ"qb\G`We'J<9\G^62-BW$0fe\HL=]AO3SCW2j"]A7
>CsIK>XirNA<-pK,YOs10Elaa7U0NOju#DEB,ueoU>6H!6juR2s-jrrc6Q"#oB)b2=9Hc(8c
P",F!Hl?_%&dm`@ag??m7>Q<p*JAp=Drla7>!ePbR>;c`6D<_'44oc"_A>:PR$MJl:QXg,q6
B@,>phN,gon[r?*dA=;n&&J'91fc_TXW:ke]AjV4@^q]Ah8.fA.4&Gh(pXp"%+57hD07@VkCA`
#m_OqA\n'iPtZ\M?P0Ou#k["S9[3;NfgWgQt$A8hiYZG1,nEh@m*LZdO'u<+%a4End@3AYdI
iqK5j;7K7[Z-6kcOllFaQhHPt+Dd0:Ff$:RYe9^HhILK^AOe1l^'05+*r^je>^.!eBREAAY1
]AH)1V[;WM\s".iqga!>HO';A@b+:<g!oSpGV3@dhT.3FC7a$?rGa.`rZ,>m>1'2FH26)&>7T
OM20TDIiRWr%f'9#Q6Z6S8mJ`ZTqY91T*^GHq5jHS90f_=R6_\>loZi?5`S'UP6@6In8*]AlL
T%S#3Rc/IO$'B"nHje_@]AWl1CPNU/!ctK%NN762MgIsAV)\h%SDO?[+3rqEqp"'U!^Bk\tT`
f^;JE^gjkig896a[f":L'V6nT0m;Ni@a?%9H6+9+hpM5JF:,-Eu?BH-DZMW:.FjX%Ss)j#h.
^,E(6mc<`[om[g\qUAD_&[UFLpLTHNLq=E"-cd.;1ZcWcYJI\ASftS4ai\n-/C*+\-T->aQe
]AKbkgl?WoE/V8ZI#$9SbP?<.bEErd&0NB6qqs#$9LuM+.>[FFdh'13IS1JkcS<6cLYB!<;rD
f`4H-g/Ve"\f]AsZA,rF"n\dg?E0U'td2E<?tGKe^TC>^T!jD+W8#YMRkQEP5b30*me#0D.Uf
9`45ec@0*upLlW\(JgFPe,JYpqACr;XQhaYj^p#r>+1=QU<_4[g<=`H4\+38O3:_Oqdkps,C
cRk$t)i@-jE]Ab[5jqR')9:?Yc.?Bk@^5b2l_U)ZQK2#]Aa3aXqnI=CaM0"OHS;duS$mYU8i+D
&I&AJNYXZk`[qr8$LT4tgfaZpuaX(.=A.WZ?I;m!+[9'Z<<!B4DGKD#U;TY]Ak^VSYeE:/s]A>
J@Xmk&8'h,0#OE,(;IW7!0IDIe9\%B"0F6SVg&ia0E+kKAICTNDt0p/0?=s<*slibB>m?a\?
!jKjtkA+Y.larVG>X5'b:LQQ2\@%ABh9SdVRrC?OIkD/:]Aj"N9aZbJ2-s10)EYDtuI5TsCuu
;T*O%hn!96Q]AL4E;GaVdP[0.>Q@EZ%8jV<aZ0GH]A8@>='Q$;FJ#^rV/A%k9.`/c![!5Lg(4/
^5QCDM'OelJ6i-*Cm>Kb3RC3lD=Ajl-98dX7^q06_GI;//du#6mf41pE[$r$o?^3DJi2Gm(+
',Zlg$F-&@iBiVc'cET+)lT;:928jaU'4_82Im#<[GW5%Wa&U(`1cIMY:+o.Q1*Q[pdrS86<
o]AubqQKX2*#AJ)rt.7T;%5rcPF8$9h=B,BU,K'Y,0Xe)+*uaSqJHoCVmQ!JKsCZl_s^)4>'8
=Z43W%Nn-`)ONAEk<IK1"08^A#ZV>QW/@MN+,J^u**2ps:/De!,RBM=SrkTUNGgiVeJmO4[^
S!*"hB9teK;4A)<hpJf0G!t@QE(9,a<8qjQ`a.+*e4NG-9s(irkUUeWGBkkg^E.*ZTg=%<;d
p%4)6XD+d0/Jk'Q@9-F19KO7aF/EI;rHTO"',q3=+lF&,bNj)um9eal^%QDW"[!DnXob0%g3
TUZ[a&a(g::\>Hg/^6<4mlpe(Rp?P:lWJo_Mdnragcp/,?i$gY3[bZ2&_T(XY=Q9b)"RCr%c
-/6>MP2YJ;#<9XiAB'Cejt-.7bc.^eTE8kpbj*I1@XJ4YSpA:)no0P=-?Lna#=e[)KF'3/#_
N;QItX-q2%DBb?jL[NB.8TpVsAR[Prp2AP6[BWY^Gr)qcLG.]A8\S#?\-Kk)g+Za@`Xo1@qVW
Qee;'BhHWNVJhHiP:QOGqu]AX=DZU$+F&+q*$`l!LH/Si*V1Hs&h:BVW/PMY\[gI%Ih&pdeYE
g9oNLp?>3jaCQgjX5.6ks-.#,"R2ML?Mk#rgg&]Aho(DT!j%8ip!=G4g<d4JLg$),"G>QCkPI
+!<KDe:(V3.j56I8IehKUK/6Tq^XLO/]A/7i\;Ca#bcb8%Zpc&e&p=rpi'ADg"h5n]A$JS/4o<
C`qoS6Mm&T_r;]Ap\n`!Vri$rL1g'SeG@]AY80Q$H1<0&YM1JX^LYm:IXQ=1gPS(K$5muX_;mY
Z/[rE[%]Ag9\m]Aj7=/DBt1kU>ZeqS(kefSjfoBopWs?P'W<SJBpEjqoIm($eUINO]AO4,\^#K8
>,hX&3fj]AY,3*b[fjDiZeSDBK:"[IS\/)-gX-"Up(l$MV/0diqZWJmXs&CV=Ct*=DED\j/Gt
7Sc>3TppP1esf]Aq`fXkKO,$XL)egpa7G:89"!ANBL;aWt#R^\\MJ>7<(1]A`PZ*R!7I_h/c9s
c<Sm(AGD8RD>0`ElHTiuJMpfArg")t1DU'>*Vf\.c_u]AWu^gl6?eFGk!f]ADXOdV$mIS\045A
*hL1\ApL0)L419BHWnFR.a3Hntn5pmBT7NV^nI$?<F>(T(k>F7/c#$%_?Dl9Cil*q%O8*ce(
mkdg?jPjM4E<kZp]AWkI2Q%63draS3r6_2>*E-qVXMa_kbh,JD@>dhs:6%rjX)pf3eJr'b3WV
I6`\0qN+YRY:7!t)M9^Zmr"rj5*"_H=&L:tG*#ROn:FbP8TYl<jIEoE2GfO'fJ`DeXn;aG8g
]AOd=j=Ubqa,,QZhr!:&,]Ac9`GHXFrTiu$kJgl36_6U*D0!5<B1@6jWto#/#_hMlil+d9EJ2-
\ot'FWo?bL4H!0%VIl==='V_)`g\:;rk/=.nNE)GY'Npo52)aJ8>-Y/^UdCulMEmfXZs(Wnj
_1(BBLW=%qtYNBIZnK%Q.78c`(\Ip1KV:S5FoWjpX:9[L7:gP=il6>1-8hXekcH_.:Hjc2W<
SJOf`qT6l%5<pqXXlM.7L>]Ahfu%EV`mT^to;\rbdT<9`bBo$aUL>$NZ5Y:]A:<.MkeY]AKPcbY
drAf%)O@c3a*'LLk>pTk(&O$m9.a?F`3MAe6Pb0<:bn/S""@T?HGNJ6%Y.PSe6:9"aWX#cU2
5gh'Med_QR2VTP8+9:,YsuKVc;)A&Ml6G#+Yj^:&LC5Cl#K0bd_=/^'uJa$*p*XHP]AcT#K`C
0bPW/XNUcK9_R!IsVq]A(b,B]AY*[tVU<MU(I&VH0!*=U2K5V5o1V9]A/Yp;/>(%/[KQ%<KULZ@
PtQ(ZBrNu,>4qE,*Ir4(@%ZlnjXkPaE:ftKcULbN:a%,ZFfX\QJ!u#f/EMP+4iE[JSKS7Wk8
Q4.4SODFSql#n,L$g7oc_8[471+73`lFmtAE)IIfhmP%fkh0K#rZlb2#5Fj5Id,W*UpE[+a.
N$-mQc^f+n.k^m&1=1kl4If)5K-t$jjT.d&<pS5p7($ZK*Ho,mFgc#\$,8n]Af!;B]Ao1-W*<;
"Mt8t"=bE*'68e6=u]A>l!5<V2KRQc1OH/YkXcu9:L*D.:=b4f\;N?q2i>ar"62`\^#G5*TkS
96&.,OR\j?5[5^R$k#$o[=>sUoj06]A3pGRoRU<i6hFOL'*di^5Lo:CF7_tMQXTEDgl@?aZ,e
pJeJapE=Uc#\m"6g%E[79^08cA3(j%4MslI+PN<$P9&!)Bnd2QX@j^05:!aVCLmk&"o_G8*5
_''@GM+p)&&OK6%g@HW!lUh:od9Z1L5D5[Y!3-)r#K0/'&IkJOUJ`R?kQrSWfR3mt8eWP0B,
^?s)*h1BaV*)c)`Y$XURKuVhQH[_K=g\GH6THD4jWB4h(aJoCBj>qSX&7ML/17!DG)ohDb7Z
6[>f5_XpK?E,nIeo3(a".1PZ2tL9,bq.7fjBNMposV*(&0$Q6uqP]Ab=ULT-qPON<6X+c1aJ,
=3[HE91YgU;2VOB!jKSK'B'o"T#)o;=lh))\T88Gs=_a>9CYTFHp0(o_WbCAj"#'Ct+b&41a
#.)X+jW:"J:4Qb\%VWAg$=P*l-5gP6&d(`_>;RV*'X<R36OMGB-IU&DltEC"joO#9dJA?n0]A
BB`ar_`9-Z(AeDtlg9C-Hf-8d<_:p%q6'#aqtfAYRF?]A<pPLJ^nAK,s.5g3hh^q!aA9+767,
\]Aj5HV.31l-D/q@VUDse>b6QN6D&4O;"Eh[0VXmX<tstO>@%V0+PMr)c(2etX<$7'J7)]AGi`
r775Q-2CCM-E#i9$uoDl,RWM6cj*oA\a,T:ZGl)?jqV)dnoHM&ZA-Rkh0IHl-0)Y$LOm#o@E
u+Z3Lt>LUjZ%o)BhEjop`8#lDjC3RDBUU6.L2PCPFe/P2u#0jls$j<kRK1N28en$e,>51h7a
Ff24ZFZmpag0L329D/\%dL1Z/+uqXAt>sEA5s'<ZQnQ;ppeTkqfmCu/tr^a!4lKA[#c>&qS#
R/CqKo&cMb!1MU+An$'#1:Yq!#2(%M8cPUq!rhdZndM;J2Q,\eu[;9\[$c?X<EDHu<9dQZ0#
RJF(`VYq/j71uXnJ4CkH6N(A'phLIGM;tFnA#(sE6)ebMY<?l.<l:F<Qk$T?PH,=G]AfSEcEo
@5Nl"JB=_+9j`D>mn\5sOP9.f=,adTN>q3X'e"_oC0%;[-[BDd_)W*WUB42"jb=mPFt$/a`P
:IA5k=_Au5K-fNP_*EF7]A>WZ;l\ok]A``U.q?,2%+7b#u3eT^I3B8_h]Ak@;h[O%LLdD('NAgP
t+c!b2<-[b5Bd)+]A&.95k2/Qf,QdOFM'RebgYHHO#qKq1MsPjJmh,mL8KPU#9Bsp$ihJ?hFH
;$4Plpi@_l7_Q.Raekkru07O5(`%=BDj>2D]APZnjX\g^oOYfea;Dn&XDW1;8'DLRf^@Xe-[-
D5j!3IBiN09..'HD'O$J(33]A.ln>SeBC=*+Et-/\ft2VI\a9;ZCPQTj`^(nHpSlK3<aj)omk
J_<s0oAb<*PVcn;.:qZMG)uI+^;e?DCHLQV,QhF/o4.4r=2q^@TKKWHC#'m'Z#H3I.q$K%=S
X27jm;+PI$j9Uu>RC-W6T<a7eg$.lQ]A8e3;;^+N"P\OOJ!+o^FPcq,0`XE5Xl3RjOhQ`g9KX
cWGVS@%V:J1eJ'>OV>G&\hu?^K%]A(R,!D'R=$AGR(gp:)]A"E<%8S8(5eFV8@X!7r\&%"a^4a
&CKN?p1qE)*_M#!P(]A".M6IW@*dZT185Mm6#fg@]A.jY@_BYAj9#)^+OU/WfWL4^&b"fbgb.1
J^L[0AR8,ISISCmi"J^VYAE=//)PGV$eDi[A%_=*qu-Ql5<V1.IT>b&ZDrmU8,l:>a?7th\U
Kh8i?EKVasRk'h_p4@cIUpE;U2?5%d5*F5Ikmeo#COn]A<?[l??9=bo$s&]AGkE)bgm*%3&"\8
^]AUKQ2_/c8gmdoG8/%oOU2a`&/]At=/gh=&Z>DstoVbcH8[d[4bK42&/lcZ*p*4qV]A`>mtn;h
7T-jRm^>'Sp09Y`0BP6HY`?K1L0+oGfIj)n>c5t\@ME\qR0\QDd8dS4,IDE51+5Ks5o3ZaQH
$/Ra@Jh`H#OQB3*LKn>VIEq:s]A2Vtg'Jp*K/Qe)tNUR^P+Y06l.f]A@%G]AaX]AZ4!kh-c.(@qh
Il.V[lZ:t2(ZD=F$h$-]A7,?bgkEG8Pqu0g&:-=X&Yi5J-6R,/U*Tq<_d"lMTDe#tb3gBrMr"
<r7r)-^dQP`_!]A<:(nkP'1EpM\07"r7JW&!h)t\V[0UeU5>O[S*eO5BuVcnu$c]AOUfKOg")3
FPlGa*d1/9XlhjhR5AcTe(W,<JV'&Fr$oBUm"u%(O\nuaedE+NVc7_BZmI#P8Gc(-qMkB`\?
e[6Ac?$[)V-"A/H=f<f?utr*<Sq+h:>baqrK(!PbCR@!I\3$B4J2A/NU_WmD7n<_no&Dl]A'@
(XjYc5uRBG*5#ml2]AV',&8lQ@@J"j5%m7j!!Fs.1RhIeLq$FRcg5kpJh.ji/c^gBT*;fu"S%
HPCEEbS(.@'9kcc)32.T6eN(^*o(^C&<R2NX+Ka[\!f*AhjT+)_#r`4o&0N1":nS+q$[h]AE*
*sIm6EoS&emSRi<ULi#&S)5J636`D?=3YK8P:.E$GF[>nE1.J=oC5*1]A?V^bTZcW0*iENZ'o
mj:F6?_>fuDS\]A?,s5U3l4$<S3c#!bF6K\[-p^InP<Zc:4Qn^'N.QJt=.QJt=.QJt=.QJt=.
QJt=.QJt=.QJt=.QJt=.QJt=.QJt=X<\hChG==a0fLZY^OC'p@KB<9=:od+3T:0dQoRGE49X
Ke$5oEVPoQ&/PoQ&/PoQ&/$Bg?(k,&j\gUZW,M!+$fkH`A^Ta:]A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="333" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="d694b6b1-c4cf-4c5b-b7f9-bb08b7028fc8"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1657350,6610350,1657350,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,2743200,11578660,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=report0~C4]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[report0~C4> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O t="Image">
<IM>
<![CDATA[m<IeU>[J35BL/BE+G`T#U(\ee(l[l!]Ab>NB"u/.^H3k1SW0sT:ke/=!cKLfeS'cR<@bf)=J&
&blH1&_"5+Lg&FnWGu]A@K1Ch<R)+H9,f*G?OA9!!#W60^'5O5QJVbhiQ[1ha=QAWW<)8Zo[E
IJ`!AXgpQ4>k1]A#La0/ar`6mlbM;fjW?cQucTG=c2+,3f-S_T0fiC(^/Nbui0S1nM;ZF`9>"
_?]At"SUf8M;th2j]A-F*O8aK^q`F6td;9mj!;M%fKpk(\@!<*n3c8WrAmDKs]AibU@mceR)QfD
<)J"3C8PUp#P!!'!F5Y?Q_/I_go8feHh!bgBpYl\`f+//`8qE)fC!1>#c&qH,2ld9.nnAUe9
g?[P1*>lHel0N>-6bqR`Dc8oP8=H:&CS+iX3SrgHOoX-2E7Y.dZ'<'&^03h18a-]Aror=[JCD
n=lTiY'-:qH60`>,8-J":<geu;X$l'(I/mi90ThClb#$bq%[7XjkY^0\3t/[.8*&eGsnT]AS0
;0!.$"kS"3Rb+dEs+e^p)3_I6WJaX>1'RAT_@U/.@`2nLROdDopl3LR+!-iiF&o.\\%D*:bC
/LmA*=dNT!>*@pA]A0hP9+Xu&5't"FP&j/p/Kh:fY0npSiCB#%!:52H7g-d/:S0gN.]A"FRs,7
^"Xu%!0fRG#U-]AF3.j$-AVI=3G9h_t=CjkH3ZEoW9`"p$p9:Zk:8'X;23"m_Z2Z^ZJ\,.t&!
S,>VBDZ(Br;EV!l1=<s6LTnee&c[[+s1J)b0Y!J&"j]A<V-B^5[Ic0WMHl*Ss3OcCqTl1.08p
Dic-`c#CA6RXl4-UYMfp@B#pu>%OW<j=.`HO1BcNqScD*&!:O``sFO9#WSXK>NG'd%qmok@U
KRC3G9.Y_hd[[LD_rq`rub-i426OP$FKH9g!O)X0"C0mkA8ju0uE*GnM0<BQ5Zp]A,$"%Wq_X
`.&=al\GR2h*p%f8,pp[J'WaYo.s[-V81Ck%uU62[,#-/7(M;4o43l[>NG6^T$dQ:>jDsakU
=!&1h@+F?dN7Gfb/CJs=L]AT^d0Xc2(QaK@P7/W3(RWVbQs"mOrg,9o\P?EsDh@clHhXH$+@:
"9Wa:Bj9UJBI8ZI3BK)gf.)KEf:Xq^:_"b=EYkneLZ7j,Ric=ZZ;Y=%Q^stQelC:g>[Z6L6@
_:H0T2Ot:A+F*6G<7$rJ^-U9A]Ap:c=fb@&6h^clNL$,2@1#UI;I#YZ&N<U+:ElJ`o'BMcE:!
U`Vf$8@a]Apg"=Zs(Qg$k[7A_npb1T[fGF1a)Q?G5\nqS^_C"N$>fDs"GN2>PO*mh;^*,P\ls
)iTV?7iem1e<Uph,/PccKFLP8b9GFa3#uP2+'pEgTYXnq&M7;<'X'B1QD7"S^/CNEEm%T28/
Jl:M+gf*DcG?jW[,:2nhhblM'uL-4<e*;u'H?'P4YY\Oahr.rPX^,fn'/,c^aufh^?p]AK-4T
WA"2@)@SeY?<AR[Y?@*lY.*skfCiO`PG4QQ:JI*G"@dDTr2/73#YP-o=-P+:I/<V8GC7nZ$:
Bj6%?Z#*ou/5VQ%T>,JSSc,Rn&i+O2J05<Y>`(]Apc+m.lB)L$ck.FL=iUR2Jm1qYXh5"al`"
gcS""LN0Qfrj/j^*Y>3]A-_$eN$_[]AqafK$F0*YS47LfBd(GO.n/XPJrI[h:oX2sB`^,$&P5&
UooSMMO&0%d#a*h]A'\ch0Zd1;rnsNHJH98DJSn@=L:.K5D`Z/n+?<$W;Q]AaM)SFn>[+`t<n!
DDWLKP"!C$j@_(4K:0&$jhM0:+ML=Ee<"I\Y;Rq?f=\_dc0G_k@m50o##1n3"ia[MO3bl8JI
d&l=t,_Y`3r-&@noS`\AEi;?ZF6H-lD"6`T<;.YmIp44oWfGC>IJIg\_7ZIo+2UaOQ/q0:#J
a:N@G+]AZ7Qhc-k5ahuBHgGeU4LNgEGm1rH:=7RTV-r[83kgiWh2(Noh-lNme!1S,4jhkb`3m
A^&B2rl.lQJ-V1+<$lQ'g1o#u,1.1pMi.J8c`,b@[]AfePZ4))qh9ljtE8&+W#`qp\Pd;$lMg
o*(I:`BaZ>/X,=YaY=NYJ0Y40PZ%9cfViAjW1^AT-GKS!&<lV]AgVRU/Z*tFNr0c'AJ<l#d0F
iV)>*ct@W=%idAUV\ej9C(GC[5a4Dd=I%_VeChS=p+"n"gBoq)al*H&U#8%6"+C4QSii_\./
=$<OW-VX_p:MF#BQ+\"C\ms*!/#b!#d%URF0J$cUK3VS]AXf7Y_i$REGpNsR0dtSW$Uj81<qh
PnArMeF`$rEg";)([Dqr_^0Hg;l:Uh@"(gk662nb^]AtN?mqAC!Kp*s1dguhULk31VWKW:[e0
#(^13C^[*!?2eS/?.:12rZ+,aOX/hb]AgmTjU7Ms_!$/Z<0)c1J)JhHRK]Ai9%"e25aq3V7Gsp
(iMBEYD0Q[2/NY_Ui]A&.`r)fU%R1mlZN3uqRmo.Gg;?.jXIM$-KK)V`nYU$iS>qt/Sjl,bZ.
;1l-f(sMIkf(:A8QeMebG&Ar#DU%6i.YdUo;#G^Hdjp.lM'p!#GJU%SiTN0n7u0>FP2]Ani(C
V*1B<3(#"S@Sc'K91I"U;Z,`i2#<D=@H[=VT?fGHBtm^r<Qm"SgcKDlcWSO#`87f5js+%;>#
"1lA<C#-(0':,^W\ZNCE#DU83M:'Gdu?:X%>#\IDjNX`&d^!eh=jB,.JV&%"Xa,BE4b)LpDA
+_XDte@7>&D%="XD>e^4s4$72"4[.OtNq!l+#[Eh%giI9mQ#LQTm6I^I3*Ppr1(*DLbY"b&p
mobqS`4)!4FV$C/m(hTRGEk@qmhVLHDZLTS>[l>>37u-l*j!B_@!LAT/3?qo0MJDgo58%ABB
HH]AOVGD+IX<sY+Wb$Xp[C/G!EN.rlJhIQ0;/gI^U`M`;-'oa&k`N6Y5+S5#/ah.b>Z&,KVe.
=4KRHqlNRq_=*f%o[GE=O>35ZX\FXn%+L.hp'HoV^3+)6"Ua,/:DcnG4R4E_'7)cXa&K'W+K
<Pp[A+HWQQF3g0h%^$#7UUF1uuEs/sfsKU9;,]A$bS3c\WkV7'N1q;Ub&<q*5,SuQ2o(l3KIT
pQD\ad<QgFN0>i\sne6E62NYi^#+*1%8%u7Wh3.A4Sb@CpkC":07lmY0VOTPN=]AB9/oXf$)R
D#92Ul6V,3_:`W4\osC:a33?<9\[i&RD\p<hn6:9H7tCq_dJl5uH-Dl3ZL<ngaFOd6Y*pq4+
^f[ldi,oiGs:.fL3'>\-hs3r4"gZafl<V.rjKW%b?VpEMsL-S2&MA,9aC/VK]Ajb^nkup'\c?
Do#1=8]AUP-&'#B]Af#`U%s6]AVVE;llm373Z>f.,U_\]A!S\@'G.;?I,XUQ,mI]A>PP>:La4I`HJ
"mhP\Sc>:hpC9]AG(I:ipW0_-QC)N*l5.[>l<'LGRjW3#u-((J+@/WD<3pQ7,uSpn_>T"m@F"
M<-*f>kVo$?kurHs:mFJFBBJkWgE`r&b]Ag<@(f'nY4F8<om+YN2os9,9Xqjp_0e^Ae3NfP+\
Wk^!-Imj*_8F``>uiZk6QeXB047,u$pj^c357--Vh0"*Qqb#.FYF<^^)J$WD(%(61mToO"VC
8\o;;p7S]AW/Tf"mrV1=n9BBe/]AA%#*7uGecgW0#SF#AQ1BA;WFKim=C,N2u2]AQGK*6.^)9%T
KnLjQTD#M9G;i"'@[`A#LBll*HbT@\:#<@c'kgEC"XQ]AVg:K:JSI7<-+#DC)59&^3aB<dMMb
<BTnGPe?H&"M"KbEc)pp%?$<d3p?Bi**=r0;47;/3oX?W*GVUeU*V[-.KnQnTltf3@H^h=uq
oo(d)+2J.SXbP;/>Dr#'((:sLU1#^lP2:#Z$>n<%1-itCei?qRL43CqF,pPgGd@<5#<kGqp)
3`<Hfe:XmNs`m/b4!cO4j3OOkEE:%@oE.cnlBR6pXKUXN3;5j"qRa_Mt[I5,+/N,NkU"Cir[
AcBH&3]A>,L[eB5o.TUgSf(q#QmijJj'F?_tDX4aD8#hmFeJ.cFp\-^q920/soKVs7m[o)\1s
Tg-PeUao2d$aW2YQ\ZJ*]A>((#!*hM'7[`&VldDbZn/Sq;GEuU?4X9$"_"rTN97nHeg6GaL)R
m%[<8<U23VXQAbd<UFpK$SRY@6fBl-pcE+G5ms@bfI0GMll3n\0?$6i6gm6X5CWh;\K.D(W*
_%/N.o6PMP?@gBMHaGo2e9FK)HqfSV4!itW.4n*XJPU<*Vb@abEeBu#_[=O.><`"9@"8`F;?
_R+$m)G,UmR6CHMs=oT[=4_R2\2-u7@E<pFj2m:q*W5;X$k6=I7DbQ3hBt0_JgrFIXN,VH#f
Xi]Aq%i6H0\mCLWLd*Q.a`5UCU$rb\q9/\XJphQ;-9tC3";bga0/<^YRDf(&)@9%bb-m0_1Yq
Xj/fe4^YMQ*`a%+Rf;1(EpuD.A0memV[=!%<DqCY(doHQ1Qe=n_DB(G@41icV;I[A)EiKJRo
bH5,?H2#K5-3>Sggd$MigQ%c/&r*PI@g?inZQOa%R9LZR?H5h\"%n5MBuP<G6oHr[,YdiWN\
.EGQ;%rAZF<IaFZ41S[iiWp96AbJ^(:QRo[>9Nd_B!7V!?<7n>GbJ]A7'X'uK91[s\Gim;_/E
9N!@#PsqAan5Amh*-e#s!mJt^4%rhTdp=`bf1po.f4l0eXo/CjARP\nTU`XZ8He&P.%:rMkN
KKd?FBe!(fmPE.0pBmU;)&.?-_d%&JLN%XFr0'_t5\et;GL\YP5F[G3f?Q4!=tY&\G4XNAc'
ni8q\Xe2]A%<m%lkQgNADpJ@7`%V/_cU7M4+V7_,gX'X-37-ZGkN_BfHF"o7e#3tAg+)%Gc`6
%>Je76.$qQI`Y#,6>:J5DE6VW+1jWlG"tnjA/4-#Q[kM92mFZd9MO3qGKDdK*C]AL&.d1F/k'
2-=P@5QL`VFk5<(Z<[C/irl:uET?co%gTdFLf!FmVQ<#M8h[.+<3g6W['MmqC.7Ws9Al\UK"
k?Uc\\\1s3GsCOT@g>?l*"qmD*?K5j6G;A>="0m1!dScV\p>'MTGY4=Khi;IReol:Pl&kqrK
n/;nJ]ADH2VU>CR%nRjJ'S[*Uh@sA*J.`dB2M8>Z954;104l?(AC1OU:@@bKf?JHRSXD]AMDDa
@:LBK2l<JLYX&DXHXDI&r'%h1\5%Dr$!,3`S(>JSZ>T&&U$o"R!D`3Meesnh0LXupZ]A/r^.]A
me'=j$F*p<;iLQn#m3%a)0:"0B87>8kIi$n?=b4a5_jaXnRa2`&lLfQMAeJ'#JnJffe?<Iq+
eAs8YZM+VU,R5/<?o@(X)5'Rp<4lG);,9+aAE8A0bRbQfbKO'"KVZ>uVN7[=^"'*"4'@JYE7
#:k(s0tdi:NE0oTj<WOq4PJYroeEB9>2+,ZHmQT9:Bt(F=?P/[HhXH'3q]A=F//s8+RR"$C\n
u`I>]A<fAZ'`^mPEaeO31qF_$'qKS\a]Ar6ttQUf5lrfqclp_K#M7Kd\c5*eI=$fjdOLWrNoa:
6JcL#<r261`M%dQ[We6*hQssT.?:^,q7,R5+iLUbO[BK*rH_9/eY3&+.1`V/M>DQX-7[hu02
>id+HO`cn6"amidABt'cVJ1!N]Als6fbJaX1_Z3$5M\*l-hq9:#7l7WI"A!nE>htEMLIbMd*1
Z\()PZJllFdM5_#GSEq`,rao8T)<K41r[dtT1SZ?#*osbTb"GBun9J)hEK2k]AgbJdKGdF>K$
s,HX8M<.>k"9NEo,5m.&Nsbi"=G%$$LdC16k./cB8'2\n=;A.?UR*L7]AkB`9ceVrXcaRV9.m
KS(ZA:_$Gksc"]A%-ZZAgk;XUcaO<E'b4.Mbss@[>W.5XABA=C*p#aIZ>bMRF\)2V`fF17I4*
/*cDpqPEKL7Ea;@XDot,.AK!5TO>/XWT$*om+bKT="+q]AIi0'qNW:ME.#9`ol#n`SE`Us)?F
HJNhb`JM1'Q7TY?ej@jLp`_cfLG`^/[H+a1kaLKed:3"?2l`>?g3HfG9,s:k()!Y0;11eE^0
b/hDPhh1NeX7#*.95!V'T\O=i4Y<bZ':>.Xr1_<ss_eEIONp52sBf"H0D(]A@5>@Y%F\\]AWW8
sVaP@;Z5u%+L3W0REk)**9V,;pH-4Qe>r%*;'ma)BBp0mcHN?,,'&5eAfe?$2Y*8KdRk,L_X
MSOOSYN?2/_acmp1933Y-=:62k:s2Dc9n3Asd39@V0BQ<^)ED[i(R4=UHA_3!Ae/Q=E`R\+F
L\ad9TMAp]ApO&X*PCkUQO`m['WZSdWZ>5)7XNcj@nTPX3Po!-Ibt7W55JO1=jdhS"B<(4"X5
J.c_VMtYNSC`m01?g8lQs1.Al9OiQP)N(mQ<l@$iXJliKaA?6;J+pTN57HXoQZ3KF`$fH3$Q
ao2eQ#DdAb&S#UJ3Y;5cHjl)7)4Ped8T_=\a3fdhhZ=*qA*k*fKG8t9uL<>)[cn/1JS<%n*-
aUA#5.R[!\KaE.PnHeZP5gX*e?lqjlX!jT]A<rV,TVO<I&+Mpm07]A?Y?Me=F)6D%-E]A&N;,!"
@UB[Q(10!&uS`XM_VO%LnNQlqU0n)?Lg4E7JRJ'VZ1%sA`a;.6?4^SM?u1opl>+>`g.UtSVE
h\gM+,qbF@BY0SlDm!99l_.UNj&+/H3*joKkXm7kj(>kG\M8,;jO!_KR&M!O[@"\)8LU!@/u
d6C0&5Rm:p,WFHq<ij686VBm;N`u3tOUMrTX$66rUC>%!'QZG8(+R7i1&N:AO`6?P9LW&g)9
41I>si\1]Ack[b"Xajn]A>[/Amb=Sl-p$@)r2?hU@G0,JRn9*+i35S:WLg)VceCn/%]A!%JHsRR
5EHX-)[en>dOFV`<kM[mQ_DdI_XQeJg%s8k6h_aTHGW^.k)WMe160^;aF/$8Ti*?8&'_]A"+4
d7'72un+c?9r-dW=G<#"i!DD:V5:-%ao_ht5ZLHLk@Lm:C%/ptN"M[tk3I'?B>.U<=aDAm8B
.fSM(6epj>AS<oJ#0*=OrFC4A$"eRpY((96)8De-q@nWV).EN'YCTq\<F/WV4_6=LP;NdfE?
reH(]A]A-p92E'c-.g8p_G5Md>KrGL3nc5gW"QoK/<<Y^;Y//f&PANoT0ALsc_#K[;Z-'J/@l]A
=D>hA)2VI9jA>&.0_7eSIB#sAXa*C7Y-'S=TP2RRrf4Y!EONMt)Vu?J!&/M+0@%OD&Frc(=?
?X5%\6noCaQir_P!16Og\(lAo.Z'EHP"@&kpOs]A"V#1`!t->_2PP>b3aS<`NL"PHXQtBTA[V
Ui(Rt$e`D=8H-OM4@f)(hU3T.Np@?'c`Gf4p0:1nr"D*@B=dp;1:-r\RdV@9XOaMo2T9TMX-
Zg4O(Mbd01Nn0/HnM\VOL[SdAm;)_O\d-'8bPut_92Ig$/O@[cinGq[]AUEE9>0rrZ;!o]AIb=
,Yr2Ud#V@On-d^YpH]A`0d;C&/XC,@j%S9<?^^3eMaj<+e,?N1ljQL_%$^07kkOP<$d:2REEd
mGqX.XB=g(tQP\3I"@)!S/mIK:3mn.$L@j`AWi4bZU*`ZmLP.cUXZ?\cR?#0:\oD-'@AXV!b
NDq!rm#Jt]Aooe>/j\1Sh_'Lf%[Wk.6u1&'jUj/<GBTJ3@emcf.rb8GL)4VA#bGoC(!<kWP=?
3HTu1^l.7TS$crR(=*FNpFl*a?Zfo[8^f'f@p)>.2kUmk94iE+)#P#SW$-Cq]Ad:+XUVh%Z8b
?+n65&E9mYlP!u49U"Ibh/;&&CEq=^I3g@a=Mkasn3A`d7XZ_u8Z=aV'.YGZ#K9C'>I[.BU%
WlA@Uc8i.8m.-fj&j<2s,7s'G>sqX`@\L"tq.?YCn5;U/7IQj9kH0^BGF3Cu:1N]A%80-Y:=2
L!SrKC\,XR.XMk+.q"4$qLUr?QW(P1oh>[H~
]]></IM>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9'frP$,D?Wj0`XehgZ+_j\SY)FmM":f/dA)ZPbbZ#e(#OQ2$gdh'J+!aR/?@Tl,j$;+$h2*
-"+1I]A7%bX`>j&lFe^7O<YDK(t0BE,IZ8CAp573c"]Ao>.Bma>Mjo(ZhJflpA;Rnn&uj6%[.'
s()Ro,R<X2V`RdRKmt5c_o8L(%KaghIN/ZQcH@E,1D`g`<<$*hbhl@5L1B5h2<]A@aP.?^iZI
B[;+<12LBqAY?FO%U$s5)D$-/LjnOJ'R/V+!s!qPQGTS@))@#c1GsZ&RQ7?Rus3rb)@^e\KS
A1DAjBa1]A,htOWflW]A@e+:k>F"OoU@3?#EPK/nF)udA@1MZbcL%'q\r1IRANGCmdG?)<uWg-
osd0'9fmjXC%?W5qlbGg:/P`_p9$\"s&C2DmHStQV*;1nY5YHlp[j#.a*q67DYK[1mpS#Vk_
eVrMl$)<g1</p#1)_0]Af12_hU9L/fRemK@k+6b50Sbl7RgHl@I+N9\Sl8kOhYO>FK9^GAsQr
SDm/T9Kl,AHoV^"65^sXbC]A,ja*Y;Ab`>n9BO8OonRm*ABp-&=W=51E0VnSp^>B^<.4D,`Dq
G*$Kf"NM+eX\F/LV6\6R+Y[qM*DRGG)]Agm8Db?K5P$XX1&1blb^TgZZ./\(jpuGaqFoK%Y<A
@k<UmlG855MFXu<LTcTc95b8:>'?9a\"b]A;#TpfSI"GppX]A$!:U.WumA>Zu3]AsUBOTgUD's%
TdPllMs'LE\/]AUSS:ts!o\Gf']A),3]A.@NO;6.,l8JVdG#%^?nG'`mQFI`'/Y%kKFqETVN<7S
LpOE]A.2:k,.C:8sH5&$pA_LeW]AAU/N2ZA#-S;k`aRQ1KsUX4qpcb7l(phh?*-u%3&acg1:P^
09Z]AWq9Qe.T]AtV*D):r8a(up]ASR`$MNA7.(8R\ptr[[O*)fu<C@]AHBs!q5Z#&c-$aa)h!'=]A
t@c$mFNMjUV4s%QVoUA]A(m%N"/Ph6cLUTArG\T\/mnC,nAl:1"gZXU\Z'M^fd.c+gis"j2FG
BH<6,:*&odQAKZ[Z1IP=K5Va\jBQ^I@s!N1(-!'Ltc0EUmsc4F1'UJGK^MTmc1p[P#?Konck
1GG[+-q)Ge0B\u?fRCL>e_RZ^_\+lZ*6&0I]AIk">_-1DeocrAFo.80H7'YuIrqoDF2qKNa(j
WM3VR;c707r5ld;-sXg\dKH6\OpMSK]AM!_RLhp1>iD8PP2f72T3@-6FiH#:iXmmIQ'M4!n"K
W20s#;L,sfZjiIogLgg##H:P_1l.DAFfSM*C%&m`_`$M?gG"@jc=7j#hSRP[7KAAtlX,2p-4
kjkXF)>-0;6Jo9">XYO?='A06AeSXoS+;.QG0X&#KXZ]A2<7LoAg/+8:qBj^30_HKO>q,XDiA
s%PniR[J[Ui;H3lnnC/W7%SRb4di*mT`Oq$eMr,mYgMV7q>K#^O+#^#fPioaRuolL"ok_+ah
rI^@ST&?CY\o233]A2a,A`fF9hQKERh(F=0Wc(.B<BL1j>I?qglg,V;9@AuGE>-,B4qllKP0*
L@U^"F/WW"ZTH^GSb`]AXjn-2+D=g)=*&):Rjh;pZu&C&pn6#0d/U'6pCfFhIDVAE*A'oS1a.
bFY.5e?$?:gj+CuH^6%cE!,M_jd^EPIH\Y^2em.Z/r<[[H?*CeP`LQm.)r)E!qH;B\Fcbj?-
+/VL*UBmX?V\>'fVKGa"heXh*Ktf,;_RDUY^<?U=fKV<l06`>Rk`4A\>T']A=!jl`V*<&Dl@N
7$3AIoM:X>P.Q<W]Ap)$*nG0GoC-V+iCe]Aq/lQW2%I?ref[b/stiRI8]AjBOAe@LN,EPJ6V4.m
(?1[nP'#B+rPVn=W::2crksnR@X&3ZIIOYkBuY.TqZRPO>HMW[mONWSojTeJ.Sdk&3EF#Le6
:2SdOp3%V<_EDG:p4Cg83i!fa!kRR72_=GV=KY/>Nb>]A<SkPJY^&]A#*\pLO5U.n194ej:bmT
f#rh1V"'B`(_oSa5)!MhkNJZ?.1[(NU-BQaLAmkF6J(5ud-n[%\bMH`4-SX*6M/qdE!o"&nm
n#bW"?4mZM3S%_9"=LK]AJ*VfaDPoe4/JQrJ]Ak+)3U>c"I4YM<SqM$?7$@RAga=9X.#s#!om,
KA!%`#]AI]Aa4t8e*:$V@VRLk:48(WW^:o3Vh"RIiiPa5*q]A&ZYd/r]AgT^NCb!kG#eT0/!#n7G
buA?+El*8O&(pDK:BOcJqk0.!)<PJ+T=0S"3uF4ZQ'4Rf+E7fYSZ%=mSQ3XA/m592Wl]A]A-U#
B#G)hhD2hhX\KoeI'\-"rSqPDkMmq]Au^C/%9"\N+RD68Qoe9i''aQ:J=Bt3jVhQe.W?QX8^6
1In4QP2OIZnSL"AhH6"/W)Lo@=g)Jnqqot@]APg,2P1/R3hT6#KY,%@_)L8`Z]A(tj&`3eP/DF
m<W=C%Le<oEh+a7-@]AOA_J&R_rM"t'qZf(lI/uS,fM]A%/BJck;l8:a0biT6Kq`X`&@n*+=G#
?;g+0T!=c3D7R),ADO0P=8LZ4pQ::nBE]AD#LV+2[q/VG27s\k"-b&iTYBHMV-_)PE#/6HmNB
rI8eR@^,*r5D8E29`)@SBitdgaRPX[CZ"rTQ<A%o:^F$TmJ,T*JH&O55&J^%]A'SZ&k3lP*ef
`kl7H]A.$S5(SaXeV>Yps74`g\0""a864I<mn2$[S!s]AM\_PGUorYi,9oZM\Oq+T.&fP"XIAp
k`^nP:.T'PP3iZa(/K`@53p@ZMBaP_.gL&F/<m-bo?(9MY[u9LqP6mt&'!.?-c/cNd5.K@[;
/$,?L[Sf3$d1s1`i7(o#U3)=&5i6:SMEK@fY53h<M`&'JJbH5<D=7)&TCha8CJn<;pjAI"U#
rV&?p=/]AV[<#m.0lu.;?G6&]AQkkFH1RT7sFb_USiTFi9q\do1Fql\e6_BF]A0:=_(3WsIV$<W
(M;]AJWUeCBm`9p;HLP(m_E8$FGFlHWGCE@t+"Y6r4iL(,Z3m=VjHtR:[=(js]A,s6*P/m&4'g
PIrHq=\jM<t-rkslfl<o-JHb$MAFcC0m>nH2E3VUYN'SuRs]AS`Y_-1%%OFObh\bimtNuCTD6
ZAcu<ujV*4'$"4.#9JpfL%MW*l*I%`eQ6N'Io5%4Sg0S=KG%KuKicPlGG3):.LhDK=Kb!q^H
(Y\7IMGP1R!'=SIo-59.+S#,DNh0pPLb3^RV0Xt5F6fdYkHEoLP<,Ul%$=&b@kU6n&H*E+Fn
LZO]Ad\eN3cKL_h-WH)p[fDY"HppnrW,5D0JTJ7(;ZTY'k]Ah*KU6d#2[K)PGj3m'5^ci,ujl*
SP4[&Z;4'^K(EMiF)nEU)6+<KL_Sju>0a:>8o7k`n\reL48@_ujKH>#@i!Z46-mI[A2O&B]A9
gRp4l?BMLs_uY3/qNY.gVr\fOXM^V6'3Q=JfLek8t_OY$!u8j?kGHBQVofAc+;;ASkj1\+k?
<9b_8LmOgR-Q.M4fas*N@+A5bVj?8p]AFB%mLH-iUfeFUQUD)oXfaNrUW6iaRY;#AB=[GANI&
7$%WdiRrBB9#Dc[3_#7&;g'"?%4RGUZ!W4^8'a2N*#_%h:$!PT=9lBp^rcr$$od+N`Ne>*a*
e9K?\SB*u$JGe[&:=LjW2HIh`>[0e.c\?l_Ze`64"!jrb9#eVAe:6Tr,##k)&uOk2>1&ujp(
6M`=$IOC#Rl%*nt?_WJcQALe;H!'@R0&BZS/[l'!!9#*aolQ3$e)hQe;0<V6-?8b(p/I#TQQ
9cCFf\*e&4?!d?-e-S,WQiX1sN[n1ZVA%EO5;57>Q94:f>..ZfH0U[Upp((jPt[3uG3Fl^-K
4RIDu#=m"&'kK+a&_0I#+3m6>G>I_/KHMaQfmp2j7-1dl=eaBf]A1sKge&m:b^W*F,,1e$I5k
9`fqa8>Y00Lr,>bC4P$7`pd(D]A01lSQncm]Ah;n;Q&0:<:jZl\F&pWI#ham#2fC\4)ATJT[&F
bWPf^?m(Wf(iD]A1T=DMQ?:$P'*I$X/B4.WD+3p[$4_`PN6tJ"cf8T\Jf0c_l/VB''*]A[$DM:
cS#&YimcQGS<2`=k/9Y=Ih,6Tht%1*nltY7d?BO6/sQ0c?0<X*6b+sR;CTQ67B;WmOKS+m<Q
T<4\2;eZnS2iJXcb[VSc&=Q991BIN<WlN#B?@!589l-*I.sNUS)G/Vk-oM7c&qlO&]A80QbES
YGmtgJ.?Q=rUOr?'X+&<UCTt-rn\+l_#11C1MJiRU%bLbD-?SjQpF3pUX#^b&0[WNK.lKhsN
b'YaQBj6O$UR_ipR%)oM]AEA)%;g<<MEI?TmfNdM>>h-R<QUiY-^Z-K#l?$T[\4VONde>[AWs
*o7HdXPnus^&R&F^:%s7&d2cl%4`[6t?^(F#c5IfQIW5GM%@Se\JhkTBIV"\+&TYVk%4'NZK
g\ePB(Z0.(IdjurPHk<I2UJqtI-lnT<NpN,JYQh$P&n9hPJWVHL.n&k3"[dJ?T^e^lSS-J1p
W<<Uj&3&Z",sEF3%l="a/h$,=QG&csN]A$ZurK\fIjqJ[`%7X\Xc1e7csc0h*ZCAX-D%YoF"+
6T%'o=[`W@Y[beRn4)kGSMmha!p'K"&%e8Tf+.Zl#V*):]A;+46]AWkajhq<oP8*!RE3!Ems$m
(`17ZIH@C@)D#d$0-c`OWm2=6+Q='&Xt>V,@jfnNp^jnr+^!DRo4$"UG23>;6d*>X+G@"L0G
`FB<6?.2d=m"RQ[T3,:*U$X?e.(C,O*Y+ong._5rX-.Xf8Q@rHSE7'``+*+(G-mf]AH[HWW;l
^qK>Me>[6b=+.7`qNOtqX@,Q<jB^M:OW'@39A)8d#>ZJl3u-=dG"Kj\8icN+n&I0@ZcEGhJ6
W;&$5tX?9_`>'M>WmCENH,)8q&"g@iX5HK5+C'\6*5.>c(CSU:boA\PK*Kfuo/j7lpD<0<O=
M6e:e2$SE.NJ"!UOi=-<DL3sIQUoeATl7KGs/5p/SN)LCUJV#BF>^_V]A;nDkb0-KB,)%(J:b
qK0ad>URJRWDgJOE0D)g_[="0VU(,ELKF\EaX5cQgtBd=S@S^cjj340,lQpRudku(P/$dkg<
pi;\#^7Yh7OO>^K+N#uomq41QLf#CS@Z1ts*m%d;W>KA(*XosBbB:mq^_;G3Z*kNV%6=F&mX
:[fPs/SC$9B]A&W9g(o<t"lrCG-W,[MTa=R,9V`l/e]A3>rNMB4Tn*<&c=e-3`W)g1ZBO"/Rn,
DTPBk4rTUGpO#?,&2e3j/_14(R<Ek\$UE[c*QYh+*?K_'kWsI=fE9FS(Z`+Jnb="02-Yb^`I
5W1p1.*B7$k6[/%_^JeC_W@G]AE\N64/s8!]AqS^Kc&3H9gIdC,1b51n7N^`!o*O&,[qXGNe`n
2Xn0*T>cc.nn(\9'XpeF.!?ika"1dc0`q&[5^L%8\+re2VIo]A]A=_Wk.i`k/_+O-.*D4(\,2<
nV!<qEsLIF-,KoEE\`?R`FLCo4C'Jj'Z62;;=O?37O6hqu?).6$cRS!EN?=H2[rBo.$I0EQ[
a=mKC!Ab@NYT,5mMZ/O")W23_j>jab,ol!._5YS4*8DcIr@'jik!p-N\t+D`2RiQB$%&,^oW
meeEc+9>;-E05>EtB`E[7J2=7(-cPihn:6n'`?>Mh?Mf$d"K&',6[`(I0MPLZ6@GE,?,So/,
HIG=;H&I4`CGKrS@7A;IS*/Kn\V[oHQ+Ro'7QUYaN\s@+gk@PUErlC1DQ&d7sYQieRIaqTY!
VFR?>)m&A70n&q("Pb`[jQbK5Ag2P<[tLIV?oiH('&D/CE,Bm]Af/\Z_*l$F+QqX;gZ;(4>+n
\/f-#Vb4:dSLWXJs[hPl"n:lOkb03eOm2csfN&C="*2Od'f1bE'm;%6JZ-<HmG#qkf\/BgZ\
mMO<`4bX"\j^WaP.6CpA@Hdu_O%-GuRt.Y``c5CU2J1\sigLkO*GAL,()i]AG0m1O4VuE&E%`
n0E<661l;]AUk]AT@8^u\EL^Er0)/<Z/\S$_?V7$j0ouuA7>C^&LhEgj!7j8b.56BALcD0PC/J
'.nS`0IOAJKQej:?rA'gHKj-g!Q$nTrUs?[Y'/X#O-g[$#n[reY/HI@hW/tR24mCe9m1%JNc
2.-oeM3nbqQd]Ap.6PiF[+(kr"pnK4HhL;Ob\A!JZL14T5c`6<^r3\XJ:A+k'G!icd+Al3or*
5k-'+d64o(Y)dYWs4_7K*Yl3[u>*BksXn#frBblnb(&QcC:UiaC"6bU`9O\g5FU^\;*1(7S<
B&OkTA)IJ<>3#BX$/s(`49Ad]A]At8g!4/;0.p^b\9$&FA,0R\C.q&B[W7*#!+"!@3ti4%LR(o
iU6COT^</<B'+%(AuQTY,7!JmNf]A.qu3sRRZEh,J.1:/8S@,eP.mU&hnAeqK6L\>VApbqqD+
^nhCZ9Tlhi8ar$qV.[*:Mj$g7bQ$%1t:nZSp^gF)_!b>T>O+sZ\r/tl$au6r5+q^&V&n]AuPQ
k/jKG"a/ElL''@Z,DO"%=A%MrnR4lm>U5M<MZJ9=W?cA+8K?i<)%tCJSXhDK\Ml:'jd/88%g
II/9n_:rFQk\]AY;+3SV>*gmt7p>f_/X,c,8MR`%s:R;jA):+S&OjQRqKK>N+'N+BHE]Aa=G76
.+,Vn;='2RBp`3W#VFa6:C<THO#SLkI)J%K5Q(DqNo'7*D`P^tO<2an+)8![iXXkJ6ep`5Fn
.?<!FCGq-U1rYMGBAGlV3>d4;AbZD6A34^q&t&^ZrJYQ4_h4s5\:O-giYE>A)e2Vgi3$k9'0
l%/Ab!J&hGm$ho.tG<Bp08RnO,o`46D<]AE:2:6oQFfD_V/@InWf)iMO9J^L``\j+TU?J%l>U
O.H<R]ALkG0>Mt6H[`L@3D9,EoV&2NZ0%8OC]AGEi#-\,E!#r]AXYQ8P?)o)[IrrG6b_+?7Q%lA
"qNCAb6&%;JMg':C%X'#*kUPg`uUT+biK?t_t#Rs+Qclb.N%75]Ao+>a?56A*W-1^FQ-s(LB(
SPFBQ,erN-Y']AcqLA^&Veq5@L#6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="155"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="542" width="375" height="155"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[647700,990600,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBMC"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[ZBID]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx4]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount count="=1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" cs="2" rs="2" s="3">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(GS)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue("");  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gs]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand/>
</C>
<C c="12" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<Jq5PNhosY.J.U;*jHT21cMC_9d&W.N/Eu+@2aMPgBJd(6YU=/6rbl89K';&g5Uh:D=3LK>
[fo#U)M;PR'!/&g2k_&,2T*c^FggHfr[+1O!4R6b`<hIW'Msms,Oq!HNM07cfUb1,<(Zr-HL
O7cl)Fnn7n^@"co&riM-<NVf,Ba"N21IV!B+VrJM#p!G>VetZHiG3hG?@`RO-YqK[[*\Q$nr
o(SL_RXIVGEj!5am`V`Egt)]Ah2@??I+-P-Idd2qT(gddoSYEi9H&cNer/s'YR3dnK?$#I68B
UVLSbLE0L9p>QOcMo0e)0E!fMbj*a:!A.er(u'P03"<?2(bElB>EDm&B'/T$FBY&RKD`;V.7
##$BqO1mS%2Lob<&d54kVYU!"7OTrLq`OXO)_n1OIf9EAY`GgX+/Ia4g(WE[:YhFSA7Vh^%s
r2&0#)a<XXt@bm,!>0;.VJ[+Zn+Z<^u4KInZ;n+1G?7p`jNp8Ge;Y'8X<pa!p&_'=DR11:Op
*6[GAi5FDl3hQ>\7,eL%/J5ZC([B\_W"fdoaoTfQs)"rIVA9'=rY/3r?`X`c1A/T8bFF\Fn.
&+qV*=7qFb=?T)WabqW:^&k?;Qb1o?%0bWIrOZ7]A:Ej`Y*G,pl^*03r1B.oJ,dYIa)[H%W,>
;E*Re+(cBUPE>)7qs28i$>YZ40T=S2k0U9E0Te?E@jf2trCh/7s]AZF(UaV]A0%KSEnFNgp6tQ
/%9^"5MA):SV0pAJpY3DPZ.Qg:@J='OD0'LUnPOSHiJ<=<k>Tu[it2b;tMbNCtj!t1^gq1)8
/DU?!XHl##(;-mZr5GfBo<e`Rm"rTJ_7;,fI<aXTnmLH$-s_B:lZUTD.qgr=mNiQgj5L^AEJ
*AV^!^M_!I/6>W`I?a;nsBiLAr\McK^)k`D>q_%X.jBarQP.i]AI.09]A1NSa\P'$do1iG?Va`
[QLC9H(.&kKa^A_X=t"k?VHG,1@RX(3]A^8P#W</OCG.dR-np'jU<kX^qr_pce;X%ZR,O7)sd
cP(6iCA7qsr!=PH3l,Nl@A)LMHeo2ur)dg3bsVWgpE6UKd''ht#1E#+J.-og\:nZ+]AL)9s?"
qR!lq5ih"og`L*<G+Ul)T)\^6<Dgsbj,emalB[O2#Wu)hf*l!\r$61=M7IPSQ'[if<n#BU+p
.VETK(%`("nLT7hZ22!M6#]A<HZg*gisK!c/I5tL@kt5K39VB"-7<>=jJ-[0_,)rqQWbY6s(b
Q*NbE<+`sE9bi_3e;;,"Oh9'KhT;BWfk1WEShk^OAEe\O2aTpg>Z<mLM4%a=8:-dhKhh%`)7
f8qKq-(=MreZnkKUFGK_Aljm8=rB3W+oc(d/IR4n.<Eh]ARVBA'&a@%jQ;PTJk(8=oEp(;;1r
dm^=4LCk%B.m5cHG6:9XlD%t>QF<CW5;dri1]AX]ACA;7`M]A3j%k/N;3W._Odfgs5X+7o[Y[JS
$RC3"Z0Pc:Q6.L?<MhEs.4W]Aha">R9:;6r1nI?'W'c1<FH7t&Y7`KHMo*#s?alh,0N-$&Kj>
.qF?V::a&>?XL&4DE!F&.3BPDuMcB\Jr:Sh^ldW2%,]A#FKqP[Iu%H,/Qo?h'>>YoNAma:>l$
nV=(`,I`H.[<-O>K>"43rq<FZ96[P-cG^ahopp-#(MmYb%Q(mhp5HD2eS2fGr'gd\hE<a`J1
0ObM@D01:;4TsHju7m6JNsMdc/`.D:Y+brbU.+!e>b$R4i]ArsQ>IXEZ5tF_VF-!C(D^[=CP#
o,h+>2NNgQnZk`QJ<"Hjtk`EUU$^(jaL-CG2;6^dp;c,,8...:sa8^oLf_NtpAq&cBn1;jua
5:nB3;]AI[R=J1os7jWmIF4;2/^0<j2"#l*hR2&Fp"8]AQ"]APp);`9;il]AWe)a\[QRE(RIZ#'D
P62$lY>K_hslqj5]A1S[84\Ir(W5h*EaBn/ld'%!NBe57t1I9pEB=foA$Y:0(ZY0Ee7Af4R&)
fX+nj,(S,HX'c[<61lc4L/XN1R0i:g_CYA]ACSN4FsjKWSrDm<:qSSC4_p*@ntnrkL\Pt1kp0
=I`']AcSYNTW)?RXCZ8pOg6OC9^r`1[n,m%C?7pu6+@lb:(EtCGoP9rbb)DRFm.<a<Xf[EThN
sJd*]AQXdD7O(nHuCW5Uu<b<YJ9\-=,'T8?eJppkCqIG`Lgu]Ad<;`a:C$]Ap\#Hc%XR=5^V7!4
_gRA2?F3-VrTh`bot7J1+-m^#D?<K3#Oc\KifV0a2eJ3cDETkr,4[G)r__Wt#l*\8E*itInK
>:#QcsQWo@4@$6f2>^DU&bH85<Lm0;mTk<<qi'2\3s/]AG=5)`)D]A)2`WIT3]A1_@JhmQd5D"G
Z(DoS*JbN\Ce2bd>8!ZN]A=@nZ-'Iie!APAqh$#'1g8>dUoG9Pu56kMn?Ks*#sh>H>.jeqoUq
bjgRV=SdjSML;"\ZO@-q/@&)Sb&F#)U/`Ud\I73m''KB\0oD<fuf`]A?0q->`ei-,h$+bec3i
),4j_u$gti2AI$#'>e:Y!0/E(;^,nKLc_mM(V?mXMW4O6g=7O#VWA(cYHjq&)1kj(MSne5A4
&detZ!p:>\LoqjU@-iU(On4-dNc[q]A>:[P!AD#@e9A!ilW7Uh?EP@f<eMlqpg@%6JXIY_6WG
d\Jbu,e[\fr1L$%TPi_\A%Y(-Hg2!!@i2J1UNd,pJg=kSrnW)/ZL7qK?3,ClM<C6r>1e7eiY
4oGM6e()Z>jlSE[\TRC1QE"WB#ie#^45FfsC4C5&C#%&:&lXIV>@W+qNaWCm@Ff.*SPqiBl!
ZXNf.%!m'^,2I>>NDRs)Xm:)&e0S_=WTRgWoiKFKaf5Q4kLS`bA2!^3$6hVoYDR*%\APZp@o
M(f%u4?a!s@.*@Br;$XA:ToJ3EO>')\4$1EDk7d*n$7>"AN920ls>4kJccSm`]A(!t\Vca@21
;uRgU^(oTBMC9L<!k5<8K\T4%OZ;'0/p[Zi=l#d>Hr2pbOP'$X<eGQaD&U-3l))>X;_!hO<g
)-Md;mHM'LT&bL.f$qGS_h!;@jpecC/Mccd*Igl:nLSgEpAcI0q[3)>bI(HOJO(;U$4]AEJo3
:,urb0[5;Mn'8Ma[KG#^5@/<L55JDH^fd7!BM8D^o3;u[C]At0n]A,Lk;rURh@^a%j8i+`2BN?
P=9?:R6m[n9++]Aj"0qjedcMC+e*+[Nj2.6O6Y[BUAR6G5FSMJKT`iDDNFl)Y0J$8Bp4eNk\r
'LCloN4T%K'0)V/$%g?To1V]Ai<`?l)B1$f6Q'_gYf>F`&Z,MM=I68tt4,E9'$:*^n@cPj?@O
D5CXpaKas^2Z8iohgI@RadI$1F07eV_q97e3[V(99K?flMO(KNi^lM2$IO4Ln</GVD2F\i.1
j,9UD5=7@O=0dTZG4#F=bVq.?RfQl3eWY$\Oglrs1.4o*1!o(.CQi?e9s(McRhJB+Yrpcs?(
AP,X^G3lTja#J$eYHYE;RQNYr9oL2/EDks`tT]A]A/>=!+no&lL*p![M4ZP,H-$!Z;A^[fA:n?
s._9[NdW)F4;OZdECp#g7nAu66HhMnKX%Ioc9*<)qu@^OS`-HK\C8g[!)4VGaQ6#>EYdbc)9
A7p?Y@n#oI]AboX"28+f=%Fat$B;N@a7S2(NM&f?:iYXAr17F;L,@AE)?n?@2.%a\&M'#*8Sq
+16WhhUcTNmFtK$FA9jf.\H`=-q']A4a`@4!StA4"i'\X[m!QdbQDjCY1@h9T76ShU=atE#Qc
WPHNp''4ok=1.?GplHJSIniD4l$9>j4cGP^`J_R)lmROsJbRYrgKPWkRQHiIF'UMmAN0oc#d
5m\\[\Y9WWm4RSa*TUES546l`IK]ABr%'D6N;BS[*smTDXjC',LUA0q%Zot^8]ASp/X'g'jp,n
het=l#&CWj1TD_`Km`m`o3!X/_SXoH'k3)CRlS`V&*cLE=u3Ebt9;IJ70sb\%o$4#2,2/BX0
F.l%t/U]A'fZ4"Q1Kf\T^)`$#tYV>qUsEYCr-J><CC\99lX^)0ETiE/`mO7)k9GQ",9<+3m]AF
K1Te`C`[FPoHta=!bh*(fbrR@YM[!CM'(j)a@"Z9Hgm`:\mduV-"-M3XQ?qG#24Nn\T:St\4
E/]AU,jj7m\scdE'aTb,knC9[3J>MlRL?(/@^]ATR\L-3>(1/u]AJrCCX,u]AN&7k"pcM$=+4opC
jd<K\"phs7,,ruNj+cGnX[;`*;$$jD+7sf_6nVodCD@`lMln59A(bd23IRYWgGPBVuUR?)qT
b)k-r:QL"hNnI7VDDT\8rXHT9Nocol?_`7/]A/U+Hb89LN]Arh5IgYTu2F(dW^_4S4gGb=CMiP
k=r:Ac7lsW[?Nkol-,mu@^&sn*3%kt[0<C1Uj*:)+TST;g<nn[PR<=rs.NA]A<'p4?Jb2M(?[
BdRQ&n-5(dVLhBOP[]AKGXWtPOE63cS-BiN*\0q.#$,=F,edd446td=5TPn7o2o2g#3o;-HnF
Yuk!sFJHr/o\%ObcGKl",b4(_"1+\]Aeb@;8$nO0_/T45VPea@m)A`Ep!i):@DG@!2'gZCk*n
e:Mgnf&I!N-"8d7tcBNT%>(><SpgT"o>dTkZ&,(@i!!FYJMGbdnXW]Ad?1"OlWqA*_@c4.[OL
V5;c7:.<9E^aGi?f-Nb=10l[>u^g^6RV4s`X=9d3nmFrY!9W+($=f_e-%&,I[^6B6$QBH'Um
SH7RWP7K$!ue=rO.5ZI\Q&n:LI2T*.L1kb1kbF=pQ#(3E@H##,1>:'^R$-rCXH[8,j;be292
)i-`!Bb;cD)1Uej7.2,F;;3Z]Arunb,oGE5"B]A6mP31/h'0Ubb-G$@;KT!>.96,g2W<Z_NV_Y
g(X%;ds3AL[`R=!rIKlEQG<ZCLSCmb'i7)baE,WICH@*ARSCC6[n30WSh6;Zf*?4=s_`.>nu
,\dgs\IhulHS[$VDUJA.uC/&F/ilU;IqSJ<a,GBX'$Q*jX\i]A!K*:bCUbj;sh[1Es83UiQ!g
lrC3YYQYYA!Hs0+XJ<o_[UKe$)09m*t;C*YhUNR[Qr^::&(PThRqZaJhqHgkAm9&2-FH7NeE
i+J[uOTU<6G/=W`N@RJ*a$'n6m6I8Hnh56G<W_.2?XRROakgi4]A-SMKANMV46;OE6[4"J6lQ
iF*"]A'fb7>NBJ>;/ujf7rQ<%@\oaFU3Xj[ZAE6\@,$h_mG_0gFcfldiVQX"qS>p)5Nu;/+cN
&,un4-0@`!Juc/K-hJX=jud,Hea>M=TWn^.7_>$W4nb#U"7,%gKF9s*dMq>fXN<]A\%826hIG
b$"&36YtT;NUIO9i1^(hR`=ke)-8t^4HIWq#`[A[ZZ<\ug9c*F>7C:QBi;eI+eM<oW'Uc>6o
9$_s8K\\X;mhY"5CB["ELJPpgpgB(llC\8>YbH)c$':,\@caUWhVNI/0.79_fgb@b]ANZPA*a
)%bT1_(-I]A0lI1Y9p=f-dbLV.tn9<ro"jB"I<(^4gHl`ZbcN1,uH]AM<GA@DJ1e)8C=sV9O+#
]A_SC_g&fXOKuc_L<6ld\(2kD@mI@RniCS2]AR-KD&(0LJ!j@"duQ)N`D##rS+fpOAGXn>@r]A!
?G>Gt&D*,jIlj:"Fq<3,$kA7;JWM*&J&Xaa^'GH_T8ZA6o.LHh*N&;#q`M[0PYM?@TY5pI1b
=+'aq*-OecQk_6jRHCSB0m3Sd/N2LmKIQ.`%McqR-+uY#5/E]A4fJEonN6EPLU1`T,4ZQAa;3
`(pQrjMuQ4nSej5.Z!I`4kDQ`OtP.k>T;`a&`E=9()Q`[>toLSL9uZGe+Fe2S"r6.<>[Y.8A
X+gdE;lIqtl"2t'VM(&(tSN&@a5hMncVHKDT-LF6"5RYAl=X>MagLJ@g!Wqg)u%oS:W[UKXD
F7D;@\,-?Ms6R68".'SmW?gbI4<Q_XPk332<2o6*X<dN(d[EP6I`?jid=*cBooih"J+h^LK*
3Su%t0o`T6C61Y5MFGX7ZljXktGHrijKpT>CPD'E&?-^fS*H8ppYs%=;@&qQU!74$=GS>eIc
/fip-)4m*nMecA-`:d^=\H2Q3d5LN2q2e4PglVjn,&m#_CQr1inK]Aeu8Q,htBNs7Y92/4`Q\
m^Z,IofMFi[<XUG,+BXQatoU+2HqOK2A%&M99_H2Z@UA`ntVHFWgFQmi(A`TW6ufG;\.?fD0
pJeqDr:fJHc$bD!pg:>5nJIT0hUlt_kSQ,&-9?:b11Z)h7KbA8G4V9u7[<*o]AqLT<@Hlk[6[
@]A(&$Llrh>.=$at/qsg-Nq'l=cjBG/XJYmC-r@o[j,"J943OqFST%O`P>aZleo@g$B6jTZbu
iD/S:oZQnD/Q)q_qu-GL?'(%UYRDEJeoT&jPGba7t9E??HPkD"pZ86s(SBLhh9>*Toi`LuO;
oFJB5)CUFeMED^B5c]A@!=1JkYO>tt+DHsqhF]A=Se;o]A,@#G2jt_SKPc)*8,!<MFNGt,$Te6l
%)6q?;DQlgCKcgjAqct"7\A9E!;&%?8ls=#^J9*9$C>9C;\qhL3"(M'q$t8O$7MX8:r`6fCL
ee9W)Lb[bE7PWeUd'knlF,A6H_;4-mtI89ZF9S1[U^]AB"]AoUJj=@fgD2]AG,.(JJ0hb5nXE)W
8s&bPc<YSdh9Ug=5?O'FIFVO0XeL.2mK*X&df\^Ve$!Hji9Yt.WlDrA,J9j1d[TC)%JgWEh&
Cb.R/@9Yc:mI6MB5J`Zd!uUHmGbDD;Mq$Nh)0U#>H]A3DK40AR12V<g<oT)aI`Z7#X3B^'b;!
$6Eqg6C]A#iGm/%-VgLAVQ6:F2V1N/;:NUONc&.Qq?l<k]AV=i,kNA)E?.N0b1_-5%4rEIY<oP
At<X5SC;h?Yl5n[[@ppLuLj%Nh9XZELH7_Hs7=3ad5_daI-#Q[HGRZ:ijMVOItDFKl'S5rU&
?fOE\%phEJP6iag^N6!LgD[S_mJqCo*$0B<adTKi%!'r)bdd[FWu^T@')A@"\Cf*\5q=iP5i
(D@*9_Rr1n,UPsLA>k>*inZ+Ef:l%;^f`4T,gR04?9et353MuGDG'oo<.e/"[$&eS(R?bp(*
rS4Tt&_P,\,!0*-h1<i!nXf9>%KIG2@jl22lOm9KJ4F.aXn2aZsXsI&ZA,P%)PZ^"0qi%l1\
dS[71:`kVdGORKsNI";#j'T'Z[pIoJ:[YP<`RtY^M28n4(Crs=*qJ#e.E:P!l!lR8#4ilb#>
*Or#`#>(2Mn,5>r5^uPip"A8YHhd(HA?S]A7bXc,*_;$oJ"pPcCRr]A@eI31cF6d4uc*^\#:(1
R5o:Y&9;M^q$g.F%Wk)+PpFj&_JTI/Q=gVl;R+>tOq8V*0[l4a20E%<q7kmqsPkZ.+>'H4@k
"HT&p*P&#/k7[[=(6kh<eO=gEop<-Dp3!GB\4Q\3,uB?lA]Al<k'/.u+a!]A6S58ibOSnu'bQA
UHP8*T_!Q3@_"m(8KGX/V<*<`FuuA8)u1O]AJaBSQrq%A#@$JC";"L_obAdVdsldpt%RT,ZZ]A
^WSfC6=<%ok]ABV8P]AB@I0PlO_TgmG!]A?dN?9ia^HL[WrbBlgR6b\/@@p1<g<PX)ZK'rmW?s*
raP"M0j*XE!to4-N>lp3ltAV1/g<pS`:g76Fb?Pq`)DHJg,a51H0mM0[3RGS8lO<SF>.Q<5g
[Ql0![:T88>56_U7rGqiFcmE93%"SJHNm(l[=\!*&Q#(Eu2(1K&jO-0'L>tl:fK$P>M2ta8;
ZZ,387*kFRWV(l[-!Z>S&(/W\*u[]A7c1\pE/\^GS9.\e'+!8:&S&u2dKa`=JABSJb_+Gb1Y*
".Q7bu-(neK`(F!kYa?Hu!MqIJ5`&WJ($4fHpZe;oT38E=eL_U'.C&BSBqcu`@/iM1Ug)q(4
Y9o-[Dg)7a%Y7Z>S>HPo.8f/etr9)%mP8s;W$nP$+6E(&A(%ul<q3ZhF2/I_N2NTo5S30bWo
:B"I&(CjoW-O%gSOQ-PK>^XIeMe1]AFCE+k[-FjF+dj<ra%@3K/3EDXpmm?&i7'OnLrSUfaP?
He]Aid;:B`q=%]A43Ik000:b)Nk:R*M[;3?&7_)RGtP2pU40'N5$']A6GVn"KJqcrPJ:D7-%E87
dM5%V1:YeLn1g)5B^j-6pUKP!?W#2B64WH,>NL&G?;!/B/)"X(``!91XRVWqVS!_3Fu=Xt%6
bH"C3\aoY%Z^t.g3FZ70+B9NI=V,6WFt"HD]AQGhN6H`?E%emmdkTg3)?LnaGbX>c#4JjJ%VF
B/rnSO8R1U7Z4s+Ra2^9.b4&OrXQVqgZgJ$]A2o?_n<=&pIIA4XG>VY]A(J_6A1*^U>>(H!s!)
AYc*T^p)N[N1X#Ccq2I-0"dFAr^oVL?5$AMQ7JXC8S4=5Q&7A%ZDIU-=e9RjeFI]A?&XVPRsj
Y8h+B<>Cc*Lrn%>Y@JI1c>@-F1#=*a)(@.Cpsg/&i['VJk/opQHKo<b*:ogb7Z1=:sE+Fklm
hBt-Toj1:.8]Ac#/2WUI]Ag*'K-s246>TP;3h_-e@K-\7ReXgMPQB1eTCPap/?[u8u;;BPmjDS
Fn"M:u,sm.i:8&Y0J@-#3"[_c(?9O8B`M/VK-896+(0&oB[.=$eP9N@M1oH',n_qrY@&-?CB
3L*F/D4/D78Y<2Yj:]AbptK%CPk0,TW@kD:jb_$r'KW=5egH1d\lqqej0pTX214LJ_<gB5X3n
kkaW4N,7N=OE3l15)p!T/q*k;)HZGW=[P*>jFS+lZ5.g)']A1l2:Ol=6^L3bXm@hYQU<+1:aD
U#[:,u3Ub$#)BLm-Hbr;IVSqFHU)(Ek*qQbr>UZ5C]AT;T\S_IJ,PH/+4s2*<V#T!f,-)HKqn
>+7C[oD!HFPuNcb2=-0Sr)Od$hRQ%'+re9B>)4)$Qj:c1%T,^Pr,aZ*U-CHOd#:6hOqK>&h(
q'T(hC2h2-9%+m\.E;XWR9LI*?ft3;8eUF7]A2p*NgesMeL0=l]A*QA2-jE.ph?25n3sZXeP_h
qV>,oO[ou?6!K1-?'2_5H$kGD6U'1E`Ln&L;%(,Gef?7HPnoq"_o/pIlbEG[aJ%or;V">SQV
%0WWY!6r]AU5p\OmVRtSc_?ku4[R*q-(mqQ8)`p>\nZ#`U918=D^B[KhfY@W*\KV%a[0<kNRG
'N#U$YL%Xl`8l6BmF2\C.1<VEpKI>NQU*]Ah^,JlUiIhfCs#Y?ZWdbtcMA(QFeVl1cY=]A2U-]A
o:;X4M>%:8A.PWQm;t6&-[b_Ar$/#W,QiMLN+5tk^AH!TDZIhGUV7Q<WV]A1K)`=/T=a]ARX!N
]Ag#:p;-C^"0L.1IfAS#m?QU<843p<%*25b4H3+/m&+r/%*%+''B,e[1s;3K,g&2f5:`1r'TB
2)6+oZX`Pd4gJslCYC=^Cii6.1X?*I%0>"joo0h6jR'J?o[N<B[\(90Q*Sc]AupW5e%j+XS5R
5]AfB9+)Y+2'9+#n)Jo]A9ED"\TNLK_%FOj@o)9R/FaL8f4c'XEI-)\Y%Vb<@mc'F13/&i]ANuP
LP]AF5Sd5B!mWoa,OilgWW;n[;PZg^n85>c/Q3DOLMQCDB&+M5R(8c>U/4#bf\5g\l.OqPB$0
E]A"Hq58`cu4rQ-SId`fMFc.G*44bj$r?m_ngR(@7:QMf_16'0[s2lP?+^2:_&-2a1CNn]A'Cr
slNF8bX6=<0+FoY^L"/X>tU&WK@+h3g+uTP^cK/EK+;oc(sJ)J&"U2k3:/nJesT'i?T.):14
>BX]AG=2@dW4L9WHWFIUG"[pQQ-6U4u,boS.%Un%p9's[DEYLq&J&L^Z:I^..R63W:RBASXtX
fS-34!/ll53IL7(;g"#bfIVq<B48C(mVmKro?b&`pjd.N_)=5o240KXCV)6ChfT*h2&\pqkq
VC)>\98CroWnmVm`l;YOPoi>fA'qhGOU=Bhe/fUt(..J8l3H`gWi,jN-bdJo-f?V3)f#WCp8
k=5dY^&jd%T#8e)gLdm!(^ukTU\$84FO`*C*D$ZG^%h:ls*296oh.7hL/9QIFdom:c.B+3'_
X_$C1+(,3>H"+e(tts9EiF4&2S$@3V?AJ<QT*XCeWt[5o8k2[=!"W2O-,TVE]AR?D-'3n3(.E
iY$>/gmPOh;lm-WFh<EE-SX[WiU8$W`dX+5-J*WD&V'<8DVg\"1[:FI<?.Ps5bccAZV'7/En
GQF!R9MaRiDk+;?A2J><Ho6bKid55GSOP==5d0SrsO&XB,:@0GP&3UiT%4-(\m:dlMLK0HiA
Ekm[qrCK>517$Eeu]Ai$u5DY(E_4._#hgJudfZ\-fBjX5GJ;$Eeu]Ai%"Ld!e5;j^"B<l\%l_R
l&7L\<5j\EGQ&N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="348" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[565265,1562100,565265,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5932967,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="6" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_left" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[A4= $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("zbsx2").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_left]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="6" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="12">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="12">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8dTO1oC<EjHZSJH,`9F-VC323!W7-uiR9J9W?>7&VW-Lg#Tk:lM.@MQZ,#+q0@qpmY2k
Z)+AjAs\Lrag0<$Xn$7,WT,c1cXmcC&gh^WnmG(2Nm$chm(BJ+)7kI@k_IFM\Wq<`N'5!!'+
.qr,UF!+:MPo"/Y5U8@L)q\oONnsS<rmnUpcN3V>[[A1"5'A%cDepVUb_tP&sCj=[Mhj3$LS
FOACo.]AXrWS3>=4=^B.NXXe3E+7Y@*sBM4Rdm%XYBhi@Oid$2";ZfeghR!TP4"b4pPsU`$I.
$N#-$E_bk)$)XE\?'HJ0It*Nik809s^$<#"BM[YH2B5j,!7A[d_OIX,>4>1#7<drAO)iX5b=
9IFE(lq,>r1nI8)gNhd\hbM/NBq4GQgU@ql47rO#1m&%&\#j"\2WAZ/lSa-1bV&'O!8ujm]A&
'8^)t^iE!:J09G-:f4Bn)T!lL9^iZj+/>[i=ZJXhS"mDqM`RI"UGKGGgras'gZ]Aa`Q#R'oYm
n#KTaA=*p+,j0I-?;^e$-rSjF5Hud3'*HtE+7t,gBFp_XOacj3)Y%LEEBA&',c\`cq&GJhPK
>(0n6hsKEVO$ViB&nNbbFe6.GP&e'XZE5(J+id4qOa5%RlT2/;a&mCrPRrFo_HUuC=[dPM]A,
gZ_engjq;D)SVhd!qrjD&dIXKf]Ap"Hr6>TbS\QM8,'(+A.:r\03L7pAb/T?4m&VGk8S9BK:\
?`-a20n*o17mc[/:7A@,Jko1R=4o5f:PJW*V\bQ2%[TN;9h!Za8-?!m4u6L'0o1.M9M50Ta6
OU2;C6n@_(FPEahLXi]AYqLn>V0$DReU?/dnl6!3fQf8:CQ0LJJkb8Yb^#;?i5IYm+JE,AA65
3T'*k/Y^US+,//2kM:Ae>_8I!pn_;.AV?=f/L!SmR>Y54Hjq]AT;&4cb%aRR9,6s6;_ZB"l!j
L%M;3qo)e"[BY&qk4[bAE(W9puX<[/^_*N*!Cu@fd!5[H+I/O`k@Tk%(TS<1<1O#;@^&P0^F
ElN-%>2RK?0Q8kQ5&"m3h`.40(%_L]A(,Wg9c6lTG![J>P:l@fKt\VFRSp<e?E=E@o4&@2PA*
bbYL(`XTajhqcW6>;ql4&1]A=:nb";1ShLbO[H'9'(WJ6p/d,rlebsj0n8[EXB[pMFiKuf`*Y
+TuNLG75<7QJGfD97-j0G$@8R\j<,a;>WVgj'oaI`KFGPbmCpHkH6ZPCA[4E%\:cW8u3A)?T
=N:==_U`gr;0kkXXRp(d;+K?%&b?E?&I:$;[8h0)g4!1Gm%!,E1AnoW#VZ)m;*(YjPFN8@*E
!jKVHLkP'4&32@:5`s]A<o&!<Lc!l,6jQV\X<DDC+E3=V',qQ8rTMib<qF4R:a/V&#FT.*n`I
'#o&qDR,D:lT`j2=UjPH&bQ0]A0RB4N7MGir6*X"UQEPH'A"L#,gnYDS9VqN8VlWSB/sTrE0V
6j3tgpcU8A7B9l@YCX>?I9sDLZ;sN"C5%\o\f5u1H@/gFc=q,YN*?;2ZH?skn:pF]AVJR86Sq
UbT?<!'oElr1e7eQ?\=G:TGe=7(rNl?j;G_qpU.MA[lOA7]AjFlBc)mpeI;$^GLSZBI-u<O6E
HHiu9e$8N+[;fNi\Bu;e;A*F]AE@$@s]A,m_l@M2a4EX<%,J%,(l-0>/ES9fo3QPGoT"At,07O
CI8j_dJDR]A'h9",LS1Y=#*qbDW`'pq<O\#j'!b1fRATIs#^RU8i>YS>IkMbL,WO0IclV>@uU
;'*h5eUK%;faH/PhL:-pBAR1@Y#M^THA6^aCA[IJp-V+p!6,e;q*)K)]A!U7VKGYKcuV>,(oI
&+mM)):m,T*Wkg5\*O>i[mn-g*tfo=CW\'Oa*8Emc4F&C4@1Qn6^Vt!rP$9C4Or3g>",E"Fg
.R>5'tR9P1V%:FrCYOJ*4kh1GMZYWA&//1cT*QM*bWL!hJ'K6mU,O*/[2n+m&@,4Za%?hKc=
c.44C4PSNN\qIglSa7\mQ3,XjsqV6rD?K;<'?D[f%4_:%VgcLHpk%dkhL%c^=4FS`TB<#`rH
*h=5E.qq21t=ql49=3q$I.n-$,e-9^YjaEh`nWn&oHO+&T28/RoM+$j*e83I^%:1A6eW^#,k
XeW+In%6qU!-)p7\j;iM*dn967fg%l%i,XjDTZn=bkpfuq$q#%(Zf=7jZ(H&P>`R5=gMc;G*
R\R-&"ki*YpqApNMiSNGB[T?frt-"m1aI$1+/Z;H.Q8ipe]A#nq*T<1rK!)@+pPpc2'MRngbj
a?+T<3^t`LHW7:T!hE=l2Ne.(_6eWl\2?,)M-[,.iFmhYQd;<p)8B`iq6EWhT_^7t-P%G5rQ
d.]A(dV3m>k/.O!L;h&RFT=\R*Fqn;`#4[DT`h?$`<7pDQ$<Eg"XLl(Im,).`hbe1`[1)8K`X
a,oTj-MtOi!ue_p26$_H_&<=VGICM&bQ!37!CW$"lj"-NagiF&R?XgK#AOa[@FO+DsafXk!E
jo;[@Q(NIah9EkSebHoaksM4Q!]AGhV*HIUFQ*ebF*e<):LQMKSB\L-mr7]AYs`fnk3o-Rr+EV
*jCfM>"_X;9>f7fHUao]A`iH(`M>-,EJ]AcMO6g=H%eq7o*PI1$*lA`NaMc]AMf[j9J+[[$.EE?
]AlS?U;I]A!q,e-B%&-GLir?7aOY;pLdYTOAp:5]AXbsT;gA<0[#&Q("e5&5lOpl+ggO5[,mR*j
"kVBha4+Cq'1V>(rN9$NEoe;k2D`IO8qGc#3crrAk-"T81Q4jhsLltK*9a%=D%sdAi?WmTF^
7-7*BJm`M,8C5<Ci2-N6o0r8[=ZJT8WIkCq"&E3-lt:)-fjEaC3lp!IHe]A9J_7)D'2]A;H.5\
UJLN`ni+UgNH$Cr!ZQBkh]A92JpGCG+#FE.ULVLW.p#Ng\g(l&'=s7DGYtGN2bO.3nLKnY9>'
BddarA_6)BU^P:?1P&-SYd28?l`g3l>qjYc,uY%/9BN\2S)5X"jPt]AgLAFO,phT]A9lB(uCEM
,M)_N2=6^:Ft&Rnisc-<&'k'D.8A9'IE4!lO(m?LCGKog_'V"51(Dkdds^9\A_/?Eq$I42Jh
"<O`41Z=BrTWOI%42UTbm3muE$n<Gl(q?ntEiTlX^oWSYb$nq>t37[Wqd8KP1.ca!ZpafI-.
'c$h\1,,WS'!sNC95n38sqcTj2`Rn$dWi4>!Bmb9k:Mhlp@XEXN6"H7/8XP[0.j6"s.-OJPI
Le9jXu>jkQ=o2K<qh&1%f,i=X=KgR.,u8<^W6.Uj$0H[BN;fBWP`Vm7o<W=+7Z_k["N11Li4
g@o>=`k@Iglgm(A0J:LJ;FGJ'Ka#5FcHUF4jb&rEr,`';\1oLlJn.fu"o%NIl/FC7X,D2WSM
@dJI/"[2N$o,mVHF6fJN$,@,GX@6X5)HkC84`).&XarWq(pqJsh[TW_e?tf[>#^-B:+Mg:?%
s*$)1oFUPn*6"%j-o%"=jO:V&H?_QP`:deV)/3AM=H>]AbLF4QT!"<=RP>$ohi<OGKaWH<QY!
L,S1ckm38^\YAek0QX2p8N!^'%E&T,NToh8@>rpl9M)p#ng%;M%5"0dp/\rRP$;R,fmAs:VG
9c=uk_9-,a+JZf>Xpn`_Y_d8d<QQE+f/>XWX*\Re@\i%TE8-q$#&-]A_BaP`$Nm!]Ao$MA\^)>
7<)#,WXJ8<%'N@H,i`EGGY%_32Fm%&8XB=%^YbQ[W2/"s6]Ak^E]Am*-r,Yc2<b^PJ,LX))2I>
_M^I\k-d\u1)II,%KTZSb;5U4GO4#l.WBh*`4MDF2j"6=8=VKJb#se]AokBHYX[`)l#sA\%9d
H6AQ+H,n?lnZ**(/Y/hXn.I;6a5Et`(f@`eIb-CH-6V>$Jfpl&r9:R^m\&(/&ennSM=bl1I;
o(8rYl$R8<!]ArUneg'*4I%=(RV5jI*i02nNARDD?rSbtN8$I5T"iS,_#>\pXPc/TSZ]AE2O6Y
TT5A0<7?+PLSAY6NgoGu_.[GI?gQQo;mpC]A(@7=/p^TZFQ,UgF2<9J@I^CZ;)F27j@R/@lr+
l^(SCU//h66I6%WdR=([6SeUlqjS/uj+mTLe1+LT%WZKAe"Wd04I50\"p]A+F^uX1gT!\uk6:
M:KSD3(h,%*<&]AbnpAAQjIO7p+3C[oQ%p)=5U=mm#2/;VLNc4.q_#C$,*]AGV80PpoAV]AN+Kt
sRV$MV44uSt`odJ09pLDpW19)c%,LTQ_Ji=8L96[g#&c<!jFK,5Dj1G?ikbdaF9N#s2tK&I2
a<#tN3"%u]Ao@+Dqm'dJG1oD%\'pu."V7)f?>7T8"=iO1XIt!Sk3J<2>t,I+B!FeL+blmY6<K
bBDal]A/+8>e73bcJKa[a*hgPI[>9,YlIYg0SO@%Ao^fS]AG=F=OVqPdk%c*>M^DSF>]A4>Aqtg
BHoR9oYQM#JfcLrr4UX:&]AG+4C4IB;n@)T>oT.N16*TX9[l&BJE6sUUePfT$bmYQ]A^D1E%,I
;7e3._":U4c_dqR<N5j4BWIXp6ai6Zei);8FG5e!Ll-8W6?s<Nat"Y6&a]A?c!L4iO)RE_pI>
0=6$\$dMJY!*q?*-?C,IF@NAY,K8$Y<9/.1CS;1auj=LCV[qQjD2\)$HRl'T![6e"ZUg089r
/CV/#i>:fr3]AGh)06G]An/X,&@#I7@rdn5!M6GjZkgO^Vr)&GA_4?fddq\"Kk1iT1Jnss2&6a
1X@F[>rGA^U5lNJFZ9d+$l\X=K^?7`7;7i8p&]AIG,A@XJqu;AD)a)B5&T[>!c(F@X_!0[M"t
#Wr.>;>3QEUMG/F`7akU]AqVk-5#urWI]AMd'Z6j0LG,WS&GC+r\^R.7"]AB6#m-t4IUn3B<a4=
$f+&JH9)LF_H^rE)DT<F=]A-Wr<Nt"52a-R9RJ?>oj:cNP`&LZGcWamqq.,)_u)th/N0CbU]AV
q;ia2PTJI5th8sa]ATS@f_Z8@>2c,$]AQM@FkE*?eY:NUr%qW+:WA8Tc=BMK5TOcWtHXZU\+<I
N7#^I;BUpW(_M+5+ebMKVg9UJNh%/IgE#SWkC,1863ef<b`=Nn!-TnF^pcTe[UdQQR67L5EL
apqJHA(%tD'\V8sVj\TBeA"o`#Hg-u?re]AS:mE*%8,9_$2g_I(?(?rbW;ZrMFWZ:^R'+)Y+M
f8/MOp1X2G/P\*s7XjUUF$WIp]A#B9nX?soK!TjpO:pMf&fDAfXSP`I6&%BrQ9"#:oZU/XBK'
'!Hiu5)WGHcmp'k/r^ajXh\nc76*2@c<NZZ!NH1\Q@'#S43U]A7UZD5Wj`JF3u^rdB:j7PhON
TM)tHJ9N]Au@9<ZF_mF0hIT)q4Vp%rMl)(ecN%G_fb*1EJV(56(bl%S%*H's2*]Al+BP#3__#3
(L8Yl[[k'1:JJ#/gL*qP(fSCg'qA[W*a<eMj`;eW6:YN_SSn4a0>q[A`X(A6qQ..Dj!dtD&*
Z+npJi>!Y/s%Ul+%RPJo=8`%ht97r>(M+kFRb9;?rZm$`(+OW;@V+A6u[bE(4&diuj>TCQ]A`
D\^0c$Ni.bjMObDoW:-Z6fbT8TC-lMgad$]A7ub"30p7buS\s.<[$;@plmj%28L0YKf#J\s58
&Z.*6.Heo\c_s?=j_TW`dYh'IQP+%7F@;Dp]A40_`[0L*Y#5Kg3EbBJPYTAj88CZMsjmK;Gp;
F[('_h>q?AcmN!bojk#NQMrX=a/',qf*faYTO9\L6f(%KI8E*F*.o^kG+t!/@9O\2^4fhJXC
R?oBYD+A0H'n(';VT4(iTX#a9'ROjr7Bm;dOZVoH0lC4>/bU(VtL@j$>K.pkI)"7+Y-5IV[7
P=[a`6=g5T,VNZY'cqGtua/HXJqODijseo%"=&"i"qqr\sVG,Q6ZJfkPt/LP&ml$)?[Mh14F
,=T-WGOsII&PZj:g#l"'MReU3?.:BGPk%[iX2ne7qLrRDMX]A,`.A*T4ZcifOXA!r77PAf8o!
VEMPsWGBDcMVRk(7:Q)X%>@T3(K,NBq>$K$P(c<\E6<&C`Mme22XA@=UP@<7!:m8$:@pShru
X5E8+U0f`FtV450BHlC"tnN1O8J18%VSSfKODhaGIE0Q^?Z7+XG=81XhY3V'iJf6Bt%PFQcG
]A-kiVp_$-\HJlUne&niNIEcDF?16@iFMh8Z^=WC<P=;=ZF&/B)"%h>@I-b7LMnou8oe\qMt'
4)f,?!Y&HYpl@GP%=R(LABiWO3X$S8nH2):)Wn:4"/&`[>/!CN#Z_?lfTHki0WQkdVX\FKs(
\=8r0Z5B&V+Okc"mB.@VE+90/ercS:o97u[=CoVT/!`#CB7I-(!?%Ja+3(l?H=jQ"R>Un.9*
]A[.(ZLNtA%dtj%'ROm)VMQ;D?">4<nUF8Xus/clnbqEimp/rm7CF#O05V9A8rrsnB=S8&/9B
YotKld$-MO>V^5:Y]AE-dO^p91hf/HAkPr$(pc'\E/E!BNG)*Lc`RT,5n_c/0c_>3>^efQ,r3
t^>7pVn_k3m"Wb[7q@>j`<E2$E^J!.;@Y+heo]A!V5rP87[?8n4\9]AMSJ^"CaX>._<V!+s_9Z
JMqZiXR*Zca*npd1i8MUQ78<`C2C:\Y1l7Q?78MLd!OD$)$M:,Qe:K;=/EF(BdGBdX[+K)Nq
*>QnZrGs6:jbpaui"DVr?8l@4V&<UA8-jHQ8$F-Dg8CkTm]An',[@'ND6*mrYhlUP"gOn_eP,
/gV>B=dm'lED)M6aReN,o6a$:S0\ZYb9:JTR3N*j\o%gk_^u!Ff\:)JJMeQ1#+GI'i.j%=_!
ckd\#R]AG+)k/oG?b2<C(4`g[C%2$%^96u$RMo[hHCM1d+)Gel*=\2_+=G7+7n-gM(LH(n4R4
agLWH,QJ+i_PIUeoMKD-*\W>Z^Jtc8Fs_'+ST&":#OpJLtEo=poa2AW%3TRQ^Uq&<0B%^'2s
<j,K^s#;#T_d-Bg,RC49d/OM%mdnpC_5T:0/9%;K$:Vcn"FD>kqQ0`gO,Q&FY?D>So+_q=Ep
olWhq"Q)DDRZ:1sS;sab7F<QJ-^-<S8@"S]Am+CCDKYD!q%NdW(-=^S\4-:kljL8>>dlcW1`6
%kP^DCC%bB`0hCfBJp,$EEDW0-N9pFVHoP_R0dUdG2tQ4b9d:.C'jnRO*I^u?h,^2(*pZ*o0
-Rq_>.3]Au+[I>h%Yj(D$bp63=@U[73uP2M<#;e>ijJp8=O\.*bUs5%s\qIc9a>hh6e`WVIWe
c`%E(!E(n3-r3T[5PCoa]A\Ae]A:S*G6mA`i_h8`7iV]A)d"#!mn[XC3i.^OO$igk;0Q9'I0V"F
alA"i@C'n&1.2dHJdMr[GA+62Qi41$MVa-TR,r\maCe(&p&A$%uEp.,LN0G!6YCD75u+46`e
QOY!k=?pTFT.fW_qZ-ZLT+uiiqGD;9FS5X)IGj(dJ"^@d^?iTtUO2<+rj+g>rl+NZM6Zteq)
_%&!9]AI`<WM7ECtHO3!5Q$sNW@sa\75SF?<B\4`H;P%"KX\tE"<(2]AF_4G2QIV5!j(+a6s_&
jOdF-^j<a@0H&91%?6V%bQcMh\-1_(UniSpE/8`5kNLW:_ZHi*qi=C*IbSG/MJV?jkUb-]A^F
9u1RQbXnd$![Ou^a3q+>QU8--#a/q!gg\KE!]AbNruZeN!1m+RGi@K]AUrm%f7idYT*'X8kIfT
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="1" width="186" height="347"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1562100,381000,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5039832,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[


_g().options.form.getWidgetByName("zbsx4").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<T"6P?Io<]AC)[iFciCT&N*068gh-,b.9F]A68)4<+uOt*)V,W(V6-J6Lh/lK(EoK+Ki!F*ie
dd\O:YRu!XSu2+X+B&i9iOJ^\R#]As4hs6mL7B#'@-3@o:GM'cgNm_K?H.g+pX,J&.fBa\b+q
X.foe0!r2fin)^rV@QqF@&GYe8V=WSOMo_fT#sE83H$irpc$MIPrV5^nkV;kn8Spbijc\#dB
?oj$&,`Q^=ufU\)l_m/5FSY&M2,7b^Gs_><-BSG8T@VtS"#>fS*N:"8ZGDZ>P<JCq/irQ4[n
Z5o;P=B789;WS1$DT:cHl.8O(Vf3"Nd[&#G`;%d%e%dQLD5,-%m^0?$*#X;IX<oB,`&$2$["
RRhO]AOu#5AFP0L1/ukp^=B=L2mk:STGRs`BkKh.+$'3f$^G##b`-_JRic>4Bo0'gWc;?n'.4
s'Ohh&W'21L-Mp%F1`(UCPq+QH_`GCZk""HLKZFS6-d$=:H2s,DQE0.WoA9Fo)t1>Cm6Q0`E
YSeqb(]A$2/:!Ur%l"nqr"rJV$<_Vhn5gEQc9^:ps1d)>J3NrPm%/SWQ$SY7-9S9qn33uG^%m
dUN"]AUuq>_;G4dGl''*J:H$=s$?):V2=6FT>+J(JT]AIKNL?!T`D"23f9[iEA2pH\$j]A->5$A
U\o%&kHf^SUae_&O,oF@OTKt6`NRhM/EilC_VfUbb1om@+#&p#V>COJp38F1G@F\S4@b5bro
m=Js7Z'_nOn;WaC'>nsBEn!f-B]AFtf&Z#;]A0[j^TO@?atAqmfCMQD^n$,nY8n.>$h(E?IG6^
2__hP\]Ae]A2AV,p+#1k_)f!XnJN&#O91m$`&gVspqZ+<H$=.A/N%%+1uP?8qso"S<-)`MHSJT
?Ut%\&_f[1o$"J-kI^"2OIb6Nnde$;3Y&A["T\8L^KhWpajM[&S$Tk*gK55N$<<TJhT81DDh
8H(Zfkr&6g]AR_^?`d,]A`l2aMQP7S$0dnV*?s$Df=f5;UY!_o6!ofP5c@X2)pJj*7$PCT%CDX
]A)8bY1l1D!>$`_W[aE+A.Q1S\=1_Dhp,e?2bR+tT#uFJI4]A[5L)SFka(9L"Z'&:?]Ak5/)4Xn
qsiWc'\74_Ts_#;2/''g/B_#_p5a\mDf2X+p;0DgcdaST?W<q4e%bn-!dL3b*(`YL(UP;)S3
l04N34K&$AT5M>^`I<%TX/"P6#$J&&k!?%=h85-+[(#"M[W)G>@8QbYR_)E^+)GAVHk'p=@9
mOSO0'aOmLLNeX#m$Y&9uo;Ii(#A[\f`!oi70-$[)5".3]AQ*L38T0KOV[<UAM`6<&I@Y(WDZ
k$:_`_9CAV2+q_4T&cs+[<7:`YkMC:#t"I_LdNAeR%(%f.h9j@*7>&_V*[0.pe@k)J&o78Pi
</OH5#7i]Aun!Nd%Ds:3US#iTtqQXlb$h!jS`ZOh2%:QnE$]Ar$HsMG`c6=!1m:c"tO%S=a/AJ
`b*_%bCiB#;jP!0j0"\(g[c'B9TAV31>V!p@?sd73pfp&=@iiVq<Ef_WUVo)>)Q;s[SR&AYU
%6\Jm'6VmolrBM-tO]A!VCtob*2J?CXDu_R.[=]AD*-j4AS0/-)8upVcJ*YSB5L&QdJIAe7$h2
LN\FY,./odfZ;ZlQe:sU#MdoK"_FjYFF$lRS<=_Upk\YGAEt-=[o/UT$UZ(k=F#$\bL8?3Ij
mXY=A?%g@nDM2&Wi"Pnbq13Q%pDe'[^MTY4ES$t\,,B?@Do"$PGV)d9jKIk-p7h=&m'?1*$#
a(^N$MuSn"f;&6+Y*'Tm8V[%Q&<e)4+/6=j2j73okBWa<nr9u/d=YZ9Z5NO_r)E&K)q^S1bc
#.ekHl)Y7K/!aCm8W%N(VTBc1Wl4"QlLn<9%AM_C8%$H5d]AmChpV@2<et[b(9?T`t"1<r_h*
Lq[@lO6_c#!leT4#e5O%kT>,mYZWlop&>N-UE>M+#qZ)+oP<6jrn)R>7dWlRXW'`2EHoAu:Y
b8cP*G?H[t7i>mH5[XF&"aVrU.lT*HV*i"CSkhq?p1:0La"aOYoJ!DG="bU^:_FiqV3sJ&5S
Je]AAI(U&PFbJ/\?UgY!0AIL["^9mTGZGi=5.*>M:.*[El:)_D6BAm8DIG+T&TKH4E:4[=1F@
,ZD;a^"X'ApGcps`d;+\LdGs:;\J\L@4MY@sd;;qdQ[69%+:H!FAgl@*fSUk:Q0#!;fd.b9#
k_0YgK(e-/*jXutXCfGZ;W6MR84(`Clmi@'*c'9YbE;\-CL\]A2BsK6H\u$:kU[*P8\!'Z?4I
dGHqLUKS[q*a,?.!"u,Y=-ORER4q`j1\$:EZLR#e_)MID4JuL?Q4*gls^=oc-rTgbdgAKXt4
tJ>0*l,B"_SY8i.OH=pkE3V6*%&_,"VIG7Ub^W^]A^[G&T\d2j*PZ[LKl94i2bc.GoWl;@>>,
>&W[4qT0$,52m1<I0\";7)E\4p@VF/[mQYlXAd']Ail?Eoea3c'?$dN2V'kSJIL&+6tTQX^7)
>=9/)H:W9JJ/\%)`kq\e78S0%6*2hH?SU$6hGj;tYcOHM%Co(tDtLjQjT[tu<,q0A[P:=bL0
f+>f*U<\k74kc51^P$ghaLtNd)_t6+9gpEFQt:1g"Qur5&&l6-5%u3?rLig.V`ld9S/3uOE2
<P`db`XepGD5J!^]A*Da9P35]A$$U0DKjHCT#!\nX\5t%9@]APin+so,9'ZguUViUZp4+N?+-uJ
fPs2uU2k!%3'FQ9&()Cdk%sR\ujZW1g&c37d`p7,h.3W?;3!"Mj4/u&!oLL?9Q41@)+Tql11
Ef![k7Y`\;3Ki]A'Xh04I8)fWHeH=0cA_ZtLOin-JikW*GfjgoZ'Vi':$+2nd@Ho1'^(bb\XR
mgdl5O\1Te:S`71a5J?*38%KW/3m5#5AjAlZJ*<Op"-q">)O:g/#R;U"nNYr]AInT<Qnet#C[
m9^A(/ga8^:st<hZ/iF0@4X8/NF7[/P%LqJ\cB'&:Kq"ip@FZKZ:j-1<W&VjHI.WOF_b7(c^
--RaMC2,>u&9uqA-.^12-iI&T12<S/rl%1Fdkg)9YW616=.f31HK6J9[m'ffu_rokYH<<.u\
(l6]AO$6--e!Bh@p4`k.m>"Z$67c3j:GMQUl]ALj-9BZhI,^i,*&$F"LAfHC+<ar-\/,Pj%3M!
*gf)OYleuMpb;fO06(&=[/)co$gZK@d4gNU.+d;^_UMPI&j<G#%%EC$%W9KVr-ZDXMGB0STE
+s$C._(^b$AB7'npta<')rla[?&F71H%^G330"bVk$WW25KCF44SM58RCjh_\G0S7F0'Y-6;
_'AC<)'F%`VXljE4&$d5S&V".8%3)U'3<"T)7T46=r#Z!ecYZdYb]ARW(\X,3Z#MG/X1N+)*i
dL<qjo=NVRQ>_Qh4c"&PHg-M5fCtq6SVj(>3QL[PM'TCCaZdN#@*:,I9$?8h[?GW9A-6Nqrl
YKG1&\Z-F9!52'9sc#;`n&P<]AgBQ&o1M'j2"6gg<n+[!385M/7GW(C:[>"93&*GCp\Yf,hVA
c/!*$Y5F'4SA)gg;b+OU2)a>9F9\/*N_iCS-IJ"MgJbJ#OY*Kkk0'lj@!3aBl$ttip:)tUaD
F.<<.?-AsKA&M#q="2cbNc5)_iZB)+Gf)p7=oV[+;`RBTY7,G'a@gc!R,2^`kL!j6fXL4&,k
5Fgm#;CtdC:gj1JOe[/7?kkBLmL%r-60.]AOl#B5&\)6Em!4=0'?N*En0tt#)=WD<MDr*PhHN
A,_@_Z71pjRNmE0oDi<hmmc)@XLU5#jHrr+.\RAo:pOURs]AP.a(#24S3SaT,9pCSsb:_IuG)
5`sSX$P#9M1k!t+_f*&pjMbR0LY?l;3PLbdBfV87X`QW=T[_bFRe#kk05*rl3"R"km'NE'-p
)\6i^SW-V#[JgG*LIpcNT3'<i4,\+ju")B^PL&j%%Yd90bX#I..UO@_3d$_359L2I@8"$Ri#
2@CbPJ::3KY#j^mQ/<BG5#=2LO+LkuB$6THqC4<m:p96UIq!hVXHZ9piXSSG(Gj):;j#q]A(l
Z;9*e;@5R@5.JL9YNG"@%iuuYG4j,1O7t05.>W<D0cobX\EA[A:S!E^bSZsHIHaGjX>R&4\K
@O#hC/\q%KYRJbEQ6"e;p[ap1+05&>Zsf#Ol9^$\Gg,FaqWcLKTU4&np7)?J/ZY.d@8s,?U<
N(VM8aT"?dF[0"-fMH_K1R$^n<jbR-e@C@Mll^gMHZdh.M]AG*/b2N3%sX`AN\RrGLT\jF(:M
8"]AZNr7D)jW79K_f($D#LLaMgnOTiA\K-ZfViK$BPnpdj4'$0i5Xs0boV(ogJn_[co6QTMeR
4UCYHQ`3k]AsOgZp92iT:RA=EAc*TQVcri*2B;0WAT52NdImJ-^h3=Ktfjbn_MopOuKh1q"&p
<^;,LMKODQ^0rG<aXF$"jRk8VSton\E2nK8,G,Z.go=6gp/]AD<lR*5LQ1s7+6Uj84IURJCU8
g)n)29mirIcf*BUl\i6_I*KUqPbW<XcptLR27hr$pI&rU-BZY(Z=@QC\X!Ie&OIi-4nma3n[
2$@LXI4mZi`b$k,lB1?\[T?fU-!Zl"tMud:u#VW<^V*AA9a`5NWfjH^%+P0NSjXkW+9O9D#0
*]AHQXY)W_m0R9V^hLr<;DW8os*Zr`e$&>/R/r;*;=b-Zn5TYpWh(#U4Z,,nVTUl%MmO!bQL4
hJbK4[H[EUWr>#Dn->S(eEan9!lcYlh4$D0?C,NT":T'1F792^ZWj?g2qqSe@+hOX<VFFIOA
Wr9Y.l_1J`apO&KGP<;m.24kph>.6k))hgaPdIbeO5&]AW8>J[9;p0Zp/GMenb?'^m>!@T/i.
4t<h!hm$Off`Q]AUh]A.TS5hDmJIeVQH/8aZd[qSA`[a!XM31<E]A)6[Mt"Y]AO*)A_esDb8T2uF
oMXZkX&#TgdMM)T8:3_,qAlgfHZ(c7!$C/^mop"&#M.EbIA*Na<SVkqT;O,A&NdUm*:"EK`d
9tVhq2W?6UQ82Q,.68tmLoiX,*mRU\Jd0ALrs7;U8gD*W>7^Yq!c1f/c%Vp=3e;`P1q$MWWK
Ajd-W5Y#FB%0_$EQBm`4]ANU7RkDk(S]A%=`.*CB%LSL))I2:q!m=Xqj[5=6&;dc8,D[\,_W_"
30o;!'84CRWbPuP-<%-47)0[m@.VL'Crt[jfiaiW(L,=?/Ir_)ZEI#)00c`/80'jWpmR'/`@
IojQm\oS4O6X3LAbfj)R*BR-JV<GosDjO?hsVtfc+eR#nQb#1n%VrP,>4-I&E9=.^K_"Tl%P
n)f>SWBqZToBm^UTf@b4TEO:9s1Mc_>&+XPB`*d/NJ=hR6V,11^RD[k6;L]AYOgtdAc#kdf6I
!]A\>2JG\W$$<CT=*c4^Ol73%R^uWp/#KTSM419P_(F,^2%)aL\&hP5)kO$%"WfD4^1<)cFR=
Q\aoG3$=;nrGU<kKQ'NEp`6>3:,[m[?9m`C%X!a_JI>$0^Z5tL:hAG`sH<u2f!P,A+f0tcpV
7*$_dKft]AYn!Z\jGF69RdUi(fHgY@%Ik`W6Bat6\_?3a8U>"5pmU+RH?$q.B&>U]A7HkiA>5B
fT/h9bgW9%O_@#T.;-C?1KtbNtmna!]A["!Ro6!qdr!3[gL::mbGXnp0\3fLF?ii2Dh^A(h`<
BM,j5gntsMn/JMErRiRu*^79S)>77".@a.o<WB8]Aj^Cj,es"`of_-".)CkVmZH$hYcENa(iN
QKn&K/O.XE3#F!Lu;E"BR3$(-)8rQQ+'u+Tg$/*bC.rkIX+d3K5>BM5_Fm6=:8YB.=OC+i,@
Y]ATo:k2boHUa[%l2!'3a0]Ar91_,R$l[U'4k[bqN6[bFr__m'7?opk&[>$f`P!!G>+.HT("=-
armMnQ1blck0'a;`QV)TH,Z9KPe"?'J>J)tY%5L59JLV]AMAN_U6ke#`_d&3pNG\AFr=[Lq@i
T63qCLh3;'(t;kGs)LF@b)2;2>GCcCcEV;60h-0A\he9CR>qeb4Mg`c@DkXL/TAe@]ApBJTON
^9<dap_DYi,P=$>nWAK>/:5A\TN@=KddGKHOLH8uhphqE.?4?ks=QK=O0C"d=r\onQ=Pos)e
(?4cmltCjj$Ng2ie2i=14u(BHcpX:X\9H0%iTLE)Nbc]AOsq2Bg)?+rPeXjOElpl:kF,uV?c%
$"-I'O!n?:\@N/M&Fm-$&GWgs%9J@eUc""r#33\F_ej.+7e:=kA]AhfJW9Mh4XY2\7rsnGhKO
s!Nds%oR_05b\%@mEmCnVpieZiL+D=]A`Q%e<VO/Yl=#hMpZ]Adabdd/<N%P"PF_:tkbmqSYCW
)uT$>_-jn'f>Brl_?%qtFCl:CBj3(+5`STY7K>1XOn@oC=$^'r]At8V!VBhM;O*G/PmD!IgQY
_5\_R&I!<EMAIL>!1eceODkP.%H@$A@>q,GlcWZqm/p[OIW:jsq*^"l%s#0^Y-W
.!mfE;D[@+;aEEFnKihcXCpm3XbB*IHiUA&&>7i:9jo#DaejGoGTRAjG.tGZo]AIbPZ:)Qlrf
aFGHUKYW`UCkh`Xklocc8GQ>MMn"'?7lp)A&._G^Ye$XpC1XSQ2Y7P%8Q%ns^Cc=7Q9+I-:,
$J'X;UDj`o(c2BTlQM*-GAsHj*%Y.\C43_bHkX%ERUZstjHk!:WRk+bb@`tsYpORO.?5t<Dl
*>YJ+ad8NXL3gVJ#>0j1Xa")'mu>Ae!G-X=>Pto)R1I06]A:=fD@Z!EXc3I8X%e&BB:i<[Dpi
mHE1:mhK.9hj3?rWiN%$tg_n!?-2$+=UTc]A":9nnF%"gPl&1XABInQ,jeb-bSUYWLJD$j=md
/d!icI&eqMlP=a&iSDmn'RGX_+d1cbtN7m_SE\BZ`Nq).5cl1I29Hjo[Vq<I:@[jF@K<*=I(
Ts%KhCAmRWrC\i-9q7-ugYB0R*HH4nCo?%VLdlFjq7]A4Mo(TJoV&A::0`>$)0B1Wl_MVJh2,
:<W-r[\r\DJ&4?siI?b@0\YFA%nI71jFf7T7=3Z'XG,#qGL%rGpFL\(ek9'reh9gFccg),N$
?'G$.pHq/_!A$2[sELN84fqZFU3Oq>8<`be'+rk+eX4?kAa4aWb$l"u"),B_gHuf!:Q(El!B
<q86(g8MN4-g<@G<AtN,JLYjb]AqnRT_VoZG-5;IEH1e`dL<Ldd^Cs58XJKE.Olu4Se7PGtts
!mh7R6oHA6.,X;'?Ri.$!!3bV6:+G`c`9J<CQgi8/juC]Aik&)?.)V!FAQ8:Wu&(_K?`"jY8C
:_>,(n7cRPq.>>:k1c0E/XP:=(.B9d/JbP*X+U?:<OF+OYDShR;@)IEW)8T3%8>OY9i`@5=3
h!GT?PSOacN7IUgBDXZ1`NG3&]At+]AAb>C-UCF.IY`6d5^6jFLsE,'Z2e^@te<so5K*G*a*)2
kfD*gHr*GR:<8TlWs[&G-AO1OaOTO:]A!;[Q#qQ'Bo4m+R(Ol'm^&C*0%/Beb[:nlq_(o\nM;
&*$.A=]AmJRc]A$5:SV@nE\Q.kBShB3I#9Yacj*CWh)H`[W<,3n>oReg.O,5F75ab!ls0I!VBO
L;LX^duu&1gG!c.i:d`$lqk)L^?h8qF)W/[6aaK;PGAuj[&!dCphj*-pKN!%F7spfA`4f5Wi
QH8ccXH4Y]AC_E:s<*qVnMs+-M-!FFJT.:MCOA<:*S_mc;#A*7<1K_Pb0Z;2`PX%D">%0nmTb
5Ud?'Nhl(W)`W6*Hs,"e`]AfGl^4btCMd.a>/)N=Y*J@#r;*6-8XgT:_1GqBiD9.t9BOkc$09
ZD1s):fLZ56=S0enMHP;fBD#E8Aff$!rJdA6"QidkXSbXVsKVIY'1nn@2De+gr28m9+tT.oQ
+D'>BnG38:bl)44pfBe]Ar[p<p3hq%nfFe?SL!/\&$r.&fuZJ3Dpc6Rug2T/[8HQ@,bff!usL
VAdHHGK_Bi@FU:2i77-d>$#UVt$*[mmQY+8kt,hqXdk*Mm[`oUBZGk_HZG6WoeXlih2Qq@R\
#bC0Vh?&T9sijY/p#aFGdJ"q(oTrBY&eBh2Pi\.hgG8glhoGQ(PjL+U?ZVlt$=7>F==fk*%_
CATs<.4i[s@,G2E^Z&MZCf(tg>[c?G>:'[+\#Bp.AZ@aChsU`.7T3ONlko-#'aYG:oF+s1c'
?,DJVE(JablI!JqNPTs/lPsq;$iJT'4f*oSFh#&?i*a6LYA?'8h_O>&<Yke&4GCm@3D'S-u^
O[seF\T0t:m(_lCK&3FD_lOsQiCN=dPqa*IM3;qnf:bb8m+Cr-'RVp,JT+!o6Z3<"]A&lE(a7
c'NbKSP<bIkHR`SbeX/f>W3k2qpnCCP&p^@\V98E(X8MR\WG+mFT3d'!JNA1Ifd#dL;''85-
9LC3>PjU=&&K.VL%k?@>E3XaE3VV.=</m;^nt^Ea;L<I8mQ[!h4c<i-FDE7UR.Ugl%aLT[b^
RQcac?.5S"Gsp<=@QBZLm*Y=5bPB"oHh-"S`YcU[\obpnIVl:n?X?luc+WW;@]AGPX\E%2F#E
locT\7;uk'B3[AiAC.l!#\2E3Qeno"pNO%mc/!rEm\=13H`mfA+D*q_%Fmh'hoY@DJJlcgM^
(Nm1n3(u0HdqEb.bh[2)c#1V12Q#,c[H]Aa)#EE,AY:3K2H\93<$J@-%9&]A3fUKY+e7]AnBU('
sE-caLK:iW-*)k9BO).S4@1\S<c>e)(=hS"hX2oUFtX6ka%igm=tDS@ahU;XFPFCW667V>,g
V*Zfa#;<^.uZ%M>Q'Eq"%Ai0KbgNKRVI*D;,<N7=7RYFIFheo;Q`nEnQ*9R,]A-'Q\HoOl)IW
=!;:pkU?(h#-'O==RJit,Qb'QBm[YQmG22dnu2hR%IGefJ`FZJ?m!nMh3WT\&c[@,Ajnd7r@
@9P.$Dk=FCK8J\F08\?_W4h^\f0_GV9d3SX5rhhBAds0523fD_[YH)MiAM^cH]A$Al8j5DKdo
W*uJ'%ir8If615pupqhB"HM-FViglVh:Wq+\nd:Sd,lac37Rka0lVPV&aP?9;aB'gGF_A:th
fI'PHo^]A!SdR2'dQ*YWDJ`[L0K.8>'UmOM&(gUf0kc\JmiL/6Lsl-D?DXK1&(TKLqmC!=)_P
#A30LKZ"<I)(lN?ZOk*MPa*ia72/E"aa"6a6>e$W%6RDe+B&te-*^7,T+)^o%)+,cd9=e$4^
@E"^^?c%fpKGC*d+t62W<Aeu0Er2;8X\\<,*oj.qR-<12Nhi[\N!da=6\a47kcYh7a;6b3d0
e1Lm0SF=o7g',]AZi32aaL:,Mu5VSJ/=\A8^(.qk+pt:rf(fW?Fa"jS-18gLP3k-;HBHRT;Qb
YQ=o0(l$I?F5g+-tl(F`!TNPjX)>(l^RAJWV`C26^(0s6=]A=_]A&isqF?oo"%+g3P"/_e),@V
bi#t2;leLAChc;9ChaYOfh?PkZd"F_BpKMpQ=NaiO+2LF]AJ;G[^Y*qHqe/A'A(]Am`Eg9n"UZ
QqF%`E^\N>)ucLPnM"pG&'1cg?9]AZpLnr69u]Ae80L^6rCTJ[cY9kdT`[ei9BWV,dTb")E0Xm
i2[9@ZA^e;$Ka3884W]AAIJ>"kQ)d)CQbp2!ORkh"VC\Ob^sh6*D!p>FDa?-uj+#k7[h$7Y-]A
g)Y>">,lR?W#XIbp7VH&6(+XP-?n9;9b()/m,'k7AKn#uc1ho63#J2&QS/`n/UCH:%+p$95N
m_qOW':!nSnFqiKC_`rB$@\$C_c!_]A$s1FDqHJ%%>(o@E#L!Tqns%@gjOJiFj:N&HR5?,b<4
C*@H5\g(t.+6Pcq%D^f.h$2L$6;QNgW)1UJd2sOE%+af\fqq*6NGp]A*a6QB"i!]AH3At%l^Vu
1i\mej>AnLib7-nAOXkB>T1,l%R0$HSL*H*:]A@MIj(q[-&#o+s`HlnpRf>J5q:JZQdH1JUFr
0si1Ff/oEsCoU@UC7GlF,0mhf2%`hW/*f&AFLu-!@gRG&MYt\#I&(QZHlD;ti>.LeN0_.g`G
M4q1<,H72CZ'b&pm.:n!(OKhW`rqNGK`jc-Uk#fRn5k*Yk%PrhgO?fl?f`5cVi%#5)T9R86]A
&N(5!>HZ5j<n]AM1A:r@utWE=#$F<0%8,@)SRFu$d'Lg0p'gaaQn,60LmN/;2c^`.8Hre57`)
;sMN-U'S:9S_`'W`!F$OfeR:PP4*5Wt2SW&k$8]AQ]A9XhH&_FaL'EI-lGi4"bj3C".,+iJX.=
\U[P:qMkcXU-qrAT)Mg3qi@,,+_2n&B"!iW]AudH1OEqmiSp34'e<n4iDn!8)=j8*DC]A>N2?A
6r8NUVhjM:Fb*ZYaVEWc.BP<G$UA%k@LlridMN[)gk#I-A*7@QH8Q6oiL"o*02K=tmb$prHT
Z%Urog##d5F^C`ooc]A]A8Bu4)*K'8[\CHFJ'up0a8_)1e^\$OO2!48=+.M';GNpZZ(2,!LYt5
1c8FHAdOJC-C<IO9&LZYs\@:a<i\:Kfbd>k3_MlA]A#ZY?-^(`%H3k@1IbkOn^o9`$ca9bZj9
ot\`a/$<_pl<ItdZ:;4/[f*m0d:t*`a*n1r7^$S@?,c%.1@2L4+J@jMX<S*.L%;;*&@L`*h"
(h?KrjY8q=l3pP:-@mR:AToQ9,OKgd6[[Vk(>6(#gRHoGJC9Cs,De\(QPK@.?L_.#^Tc_hMO
CL`H,?"*J^gXC[VW]AZJgYggB"BdPBcXd5I)]ATaLf@)q?#Jnr"[+%jCLRVli-^qdF@887N"Q(
ZC(4%:"T9`W!O7H<:_ilHi2;a*CkQ4Bq]Ah_T\9H2F?)bZ6:sJigSOfB+De9KdDd.g=6HG;W@
l:Pk8o&OfFjoFZO7SYj$tX@K?cnDJe@"G3mX13!q[rIrnXn1'_,bPQ2P:;r;k\"6>>-T]Al'S
!np(Jo'hU<$p1f;`rrLP#r_Ym-h-\g<gSLXufk6H;*'djUi@_LW03T@)G-3gT(^2bLBZ"Y*T
2G\heR)K0V+jlL.PI3pA5&@HZ(]As5)_]A=.5n*YoG*YCT5?\e!Ym>rbNWsaf;\I<^18>SJ/<o
K/f`<,&s+-?U0%+T_[9M8X_\<k8t^!CE68:p%cq#EB8K5ZR#sYa<PbmhnA.IC,ElF4X'pV!n
pNrZ\/#p2$ncX3c<CfqKMH5C\cqOaN^pg6".oGPXNikPQ%88c,&l83HOZ(UDB:=?i<K7lJFL
WG4s8q%0Wn,RqBAsa?W$N\V2se!J*J$[`FY_^l'dS(&6n?ekQPl4g6ehJYXB2iE^[EhM$14?
$I1BKm2K]Aq#>D[S\$SW[.W'-DInBS;TRGk%e$it^S/$jrVlNN;Ra,]Amt/100";;WK"Kqn\-q
_V]AAP<O$grVYi%DMImXi(/0";;WK%o3BbfKDG/eKClr4ajiYW6(\_i8%'(B~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="187" y="1" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="2"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,826935,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,1179870,2566219,0,3238500,2226365,2226365,2226365,2226365,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="3810000"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" cs="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="分支机构"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="分支机构类型"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="4">
<O>
<![CDATA[指标值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O>
<![CDATA[]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="BRANCH_NO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',$$$)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-10373889" hor="0" ver="2"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="FGS_TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4 = 'bylzgddzcpmc_20240622191229']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗1">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[text]]></PopupTarget>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
<FRFont name="SimSun" style="0" size="72"/>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="auto_height" mobileWidth="40.0" mobileHeight="10.0" padRegularType="auto_height" padWidth="40.0" padHeight="10.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4!='bylzgddzcpmc_20240622191229']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF($zbsx4!='bylzgddzcpmc_20240622191229',IF(AND(F3 = '户',$zbsx4!='grkhpjzhs_ytj_20240622183246'),FORMAT($$$,"#,##0"),
IF(FIND('%',D3)>0,FORMAT($$$,"#0.00%"),FORMAT($$$,"#,##0.00"))),$$$))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(AND($zbsx4='bylzgddzcpmc_20240622191229',LEN($$$)>5),MID($$$,1,6)+'...',$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=count(C3{LEN(C3) > 0})]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="1" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m(?t1P?YXWWlkm(%7I2$/;OY3PXnje#U6rr+@JLV+US@J,j8-B85VZ681-ZY#b_EbV2-8-KE
M?J<'#Ru8eO]AD?_Y8]A^ZXs%m6/HJc5>aB3/lf<#L@e[]A\@R7pUhT>g/sc"M+EP@g9aGq*-g.
Se#"-6iP1r<"om%bY4^tT9cJB?QS1ASc((QsW08q2^g&jFanFO*$)^FG4`fMp]A-K8en_DAcn
<1EYks$O;mKtjhH*2SUK<22-NK%f<T?l:GSsr+<WOQ,4G_c6"VY5QFmt_0Q`4A5FCZ4U[Fhk
`DccVCNG(^6PBVK7$Rst>6I>PY2,2>OkJX%G1)/t.G?6%UD!L1#@^(@`Z5V0t8+uF5PflMFI
dh,Cjo7Q4HjQ91+^>s]A=KW"2cn;p*%5me+W7UFmM]Am_L%"NA1,<+4+M]ATt!H=&X^8?s2$'"Z
to%(Nhq7SbcchZ[$<_Z#0NJA<A&uk3%ne_Bn"j^AOlTrFG::s'3uj;7P]A<A,j&36fZk<T>uf
+"-pSgh)k<^GZT`5259NLJc)CgYs%$Vk=@JHP-2u?5e1g&B+C?K/oJFeT=#"&Zt<*kD=lstW
t?LA"-oh1`Li83o<G,toW:=H7]AVUAp&(oFmq$a<JmXYoB;Z^KcO2j,UteNCfLX>,rNumH1O\
/4V,['<rt%0,j<66A^[ZcfO4(5<#bH<^0F)h!0`1%,9?T5>7$m=q4O5qmhbr*>FcUQgjDdZr
@FJNtK<Use&KKgdE;i29bfRBA5COJfD;.4GKmHVT7KhlKZ\T\Q4!Bo#Wk6sQ!)u8ZhM]AoK7U
q+PCt`uXT-AIF,j^KpAX?,G&-(g>Ip%2:gSR^>@X"R-bl1DqSCbR2BKWmsk_]A=L"8l<ks3g'
02"GG\5PFpQ+#O!>%[n3G:kNK:IMK^Qfl_%B"Y4K%jQLlJc._)A/d!K^\_<Osc$hf\O_69.C
b+.":[GNcU?:pE!WH4V9B,DN!E:aq]A(<Y-a;7G%2A3cKgV+[o"P4AoUDC$43AV->g[QFapgW
!]AM%-"[%Z6q!Qu@o3<_Vj,0A?DcN`)WFOO:%E8KYk\hEJT\^$]A$toRE+Q^S;M%UKQquFTSdQ
p>iR;AkQ0oNoank19>j&LMB#$POguM/OI@jMiPs4>u[d>4<hfMh>"a$lP)VR8CT!'s6Yq.j4
V-*WH*0a<-+#ZUm-/Y(6dXI.sq!j?e;W$k?fuW^APSD]ARb,%S"W*XYT6HA;^'1D`?e3,>O1)
kKeLl*DsqiHgSr"VEn=;a9W\7A]A/hoij@E!_Y!9Hm5<3b+C==oJhtqk7T]Ap2XNVU%@pF3sJ8
k-i0FrZqG*<M8EW3<+2Kj_0rim35D_C/)hl>CZNhT=H6)"QG(Vg-/7P&RCM5Q!mCnsc9T^?i
ObhjN]A`=8]A"u"S_@kpp7bNoD^$sR[r8:qOi%#l?HX?n=?^c;pElf;NPus8;7PSEHpQa"0j0W
L2`0ZPcRH"mB@[H@Q)=*);__hr'FU4c-[CQ_iIc\RNLESjS$?1V&QRuMdg&s,jijQ#@$?.k-
5.0h`l@5f@`#$50$PpBa5OFIOpc,k5;]ADqXRCXAuL.DYoWCZDnh'r`p&EdW8IB^Zn_lmQa4k
0G@_/oWVRI"b;K2PPW@01Tm"5,+9mr#is`/,d\jSqQjlg'2Wl]An3Pf+&agsDcJsc[s4DMm+N
/SGRWC`+l7@k'XA\OZio(uG>FFH9i=EA^c51*l7pPE`tH)l=Ih/1tb`a'G^^ki!\pS4!&]A5k
`s/#j-(7bSNdT*Q<Q'X=1o!L57-82\7.aK^M#P+U*dR6tmPE3+S3AlFIWD<<PCbMRr<-f+\7
S1+Z!Z2a$]Aa*sj[52;"7(#&JD1]AHoE)s]ARlO!8q>3*IOAGb-`$7bCN]ASMq)M$009TVr(F&+r
>RHQHfAK75l"n0L9O-CFc]AUM_i%+1LriE?jDn<SP7l6,=6n7R"psP0V;X_U"k_p:ZO+!Y#md
lM7QUhj`BK$q$M?#aP-=cSGTJh2(j'Hln8^P_ndC0KVNRC#%[>M''*c7oQ?Xr8G'hkWT36C*
\kI\kLcggALUaoZSQ=f"A&/',LeGocbHPl\4mAHmC%ru2arI$p!bRm4t0UQDg_54OhkX91tH
H/^j1T^oVM<R(,83S]AZ]Aqp%:;;95%O"iA<l+/_P!VAEC:/m=8/d^1o1_+YA>TO(CJG[f[RAS
`!7=BHC;*NQk%fED'>NTjgirBel1D_`c)4C^^ms)qJt7Af2IB[VGk#Y@Zoh)'V/&nW_1h8,n
>AK_*.W$M<R,Q`1A2;5\T[n8C6P[Z.iIeDg,uX20sDAVib*`!m@->Y)Oa06n,Fb0MPp-1BU6
L?1\Te4mpr-a<+QG:I>lNrjuj$Oe_-DFY9L>H^Me:kk3s"LlGKs12"!E<ne#;2C)6+H2R8kP
j:<Dn^Sf*b(S-0p@R!CdWYUh0;tTAa*=%s:A"4-EJ`ZeGj[![*kB?IUJkCag38VP3]ASsIhc`
5CqUu0;EWb+s5[B8\fDfA6#qqo[jla5Ef`2NTekcr1&$dh`j%t?dqQ3?_""V9i53,(gRd;Ss
r7/a(LrJmb,8FIb>(([AE&NIfK(89NVKLFL\lErM4X*Lj6_e>I:[AZ1_'Lr*+$sZ7"1'T#p<
SOngq'*4oFE/;)Q/D]AVhig8)#S^)>d2L_M+?_LNHS?2$7)eEC+7_uV;?k)Jo4gUAJm8ZgXkX
i)b<]ATX.?G.QB?NUfPmG'/b6g='nYTa-C_[8Dj5NHqt/uu%USM%V.UpFq.k_!^AZ;B'9;-!0
Ze:AY2iVa%tMfnJoln822/%/:DkR$[$o;Y;.sL(>j-8)-uVt)gh_j^6/^MlO!a=I)a`1`9hW
P/:HN[%'gm"s=9Xc'+AX)*85'epTOSiYC`frMT:e*m"lN=`-(9I6*30HIP.=jl3Zqc#jp+Q"
Q*l7=W9,a77Wnq5F60a59R[\eK]Aemc&>]A4^r6+uX`nG1k^4m.BVc1W8CIa"jF'QUqj]A>)$.?
aY>0\B9VH04sbRPSLk]A1I:6T9G7BRKPj!+$cd,c8`/[U/tfZR88j_MkF.</BJ0,?YfL%:@_U
IY(,fK>iZXR;HVoUQMP=mAk/RlIr'*^!K-j8J]A7B;opNY3;qq5nb0M.iE:9L]Acuj'WL<fB2l
!n/nPcbGO/E9/Dk+o8"PF9Xea3Ra=PoUQT69g['3mL*A9<C<o4?F<spc]AhX[tBc:GgdHK9N\
-%B`!S!+hPSr#<N_s:uSW[;rYB0:Mbn70HYN#[uTcmJ:.,WV>gmYn:nD-rs?Q!QNt/bQ^qCM
QlL(Gp)@h/]AXsR"(bW9,0(OBm#OPF4@:k6r&(4aHEiCBqC??[';pLU>_;8_I0e@fJP>ts6Ln
K9A7RKC+U3p<BD1mC`3a&XX\i+MMN&d`Va&KenSm("p02hRMF0,qR(>3E)UeV*GD#3>=1(P/
[>iBHpqc3k.ebT>bgX-.:2VY8gFTTi2W>qk!^&@[Og)`W3;@:>&0Em=Se9WT.^Nq2.OR\Bs5
-r*$D_a0JS^&"P:QV=rbKsG+HC2$d14?jGDb6`9>?$r+q#0Xd#XUu/b1Ah`S<CH3\@jULFM<
Z?2r<H>j\aL*ei<%L.&X/;9r`p?o1,e[eYhT/@D;lWi!5I<e#:S14aeFsKLMY*IVj*(1OOYt
ACSe.:G+FOYGK,iKM7lH/PUD*WP[`1Bk8/%:ngP]Acr[dO4b&f,,\Ja3X]AP1P7ZF(Z6O1.pC3
=AG!Yg"t?]A_D!EXlTM@mKW-TEQm,b(\69*64=6!8j0(nL)p4B4n*q/(Bjj/)=FMi*u\>oMC!
&B/#*F-?EdEULp)1c<l>F_V132S*7p&lP@!B*)lrcruY,s+5(e&]AME@CZ"DYSmUVk^hut<S2
)bBOlOL)VlJ2<LNf#e7"Krf"(?Dg\4#RAq\1m_;CJ"RClL4VU,<EMAIL><@[lb([iL"
rl/J5sHV-LYtF;ddRkoR&Rn0g'!F05$*>9Bn?*Mu<#?erps7taau\)*2OH".Oh91mTT"7N=M
%nPLAE5MHT;[OdT"ig"lgD2g,Pjl"_HSjOUnfp2e6]A5>_]Ajpi[7N]A<n[eClLT86&`p'+98EZ
='#;E2P;k_po'$!7\gSB,R_E%`!XGD%6:iUaMOHSGYcZZ5(`-D5e"DTmE>Tq6XrcTCN1;i"f
4[l8[C@=:43J?@1m@+(T+M&oKSPPq)POk*$r3*)mMkHP,4p2h!a^%"3II@*+qb$&k=m,J\E8
a\TR3d9uIBQ95e^:fh_nf@,j08oS$^Z#'A6Y&e,n;W-Tk!c<Wkf>"</!0VYX/WR8eLmA*5(B
riUpWGO$`%k2<,_QP"i1,.>Q1*sGq!f$V=NYq7g3]AIm+I&B_d3&o7U#((<EMAIL>
R=L4Gc/M7n@.CjK$F`\n;0nY\"5VkDn%L]Ai3a)oD^q(WNBFV;2m&ecar2"&n0OM!`ZYgAcPS
+a]ANXnsaese-E%S<.nF]APK<^'/A8:<m[NX.6.6(kk4jWM(PeLt+6h6"P`o-l6(bfqKj;,I8c
+a17C,?nRQ+,'YEG4P4m:+Pd>a2e:h0eeI(Gj561>;p-MInD":1BW/e,fma&%&hBr[k5Su?0
@)cH<Lr""Ca="6p`f4kY1))MIBGl1[\9OrCrjWD"?jmpGD56LP7uIKeP/(k#C!9/KG:ILN!?
?..5E+OVq9E2rV,#/:>f^DGnme-4Zd!!HK2B>!*b0j5s;q+bg:W-7OuS4193.=l2tQ<""hC]A
)T([;H3h\te::s"jKQe]A/p$fDH)$MI9[/]Aq(iYOo7R(6F"5@sD"tl0tP^WrOPReBXLb3E,i=
08n3A1g?R1.Zk54%mNg3YG[T^*+h=^p^jj!$Z/FGar,`<!)^K`3KfUia$K+"a.eP8C%`*"%P
2/B/:h/Aa2Npqi<#:nQb2kbWPWXaqXE4omjrO6qk(9Q.G4^`mUM57Vt/Vun5a='rPdpDEh4W
M6`@oV#auY>a5<Rd,0kUT&o?!24=ZLKqoaE%Ct<O]ADpVeb7H/GBl%tC;XBiI\a;T)q)kNStO
:'lfo.N$8+RM2HmT>U`d)ENNkATe\Yc]A#RMNOK_+Ip1qSY\q\JHe#6%j*jU.X*c+dF`\^ZVn
8ol';V;*+.8.*c?9TA\RNb(tKEN8k6R<Q<<j6f?qFk-K-MI&KuR^NF<:P:@*7XM6aUR/90'h
cIq+0/"15lQ#ejV!bRkk;2Y$Dc*B8;2)E(6ju>9j`c$k+N'Dec$`B2')!W.ek+C]AV&2uq^+V
oT[s*Vr\,i`M9FJoW`a+p((fX+.3rg8+KVu7g2[aTToVa;^3maHXD-P6T#Bnt5Pq"j`;7@!p
HnF8_iJ-@6[ea=-3#tQ/,G*fITo2Is'h;e'kfJ%E<cNTg#Nr]AEZXb\d@^tDOj)?^LX+7iDtJ
_T1)BbdZKML6kfujLYUq=`lXY.QFT$0q)O*M.J=>TT[JNYsZQT5i]A*VJ&k#+S2q0g&(,=Urj
]AcOF+RZ]ANc$Kp2K)dDRpZ09IZ?"TWjD2OlIcdJ`#9t)T?pZp]A#*6PV&T@0?IC_jZEPoYjQo/
M"q9`oe%J#gmK"1.t<d`_BF%!4(MQ^O,T$Lr'6.G</TBb!W$D2PI$&.-&#&iRH5&D_VtTXO2
iV.d:$9dC1<:QQ9Y?Q;Q+jZ72*RC0u%LW$nh4<Sp>[ltV0Q9L@PWWA%;XoYj[(tS\JZu[9`1
ajqs]AdDO:Q,o=H^q:NGE8>oQVQS]AH+HeQp2\dJ8NK)36dYA_7S5*&e%If8(NLZnaZOhJldie
GkU%]APdQ81'DO?,6;'(iS8aS`sK,Kr"%"A,)"Lb:LZnBj6)._'4drfPte0dBfV:%ut1n1GE4
7H>AP3mo<uNaT2obd@1T.."s?pVra]AOfU$7+-bS`dfBGi"<@(*eQm'4FT.G,A*.258DKTOs+
E\!HC<u#g?6]ABesr[eG;kb,,"Qc"7F71FXSegW62raNJDuoS9aZ*Z]AMIPl2iADgWH+9l)_,p
K1_-&.Z"lO4H2;+^inMrA.tJI$3P!g6%fu&18TVUhEuQHRBek]A*b`T/_BWU[+as'ib3kgWI=
euFp&>g<WaEsL:\\OWb*N+WT&C]A&E-qrm(0WW8k32IX&hSdeJml;1[G]AE9#5P*F[ID3['2a?
Z`G%kD<H=P-iZo&hniIhXeC:&,^&K8QnP+CB_q6LL^;IEU>AZPPI3.#H>VK-^l7!J8q#G_rm
i+o03&&We)CO6K'Mo7VUVWcKdE+p9&9X'$aA2,-]AlP/u:+H?<Yp6)>+TOC2Hc0dp01LM4"+Z
);OaVrih)sbKMq^$YM!AeKumQNbPB$fcCoO;S>=J+;:]A<$_dD8q/W\7$#DU.`nijl@2Kgc0G
9!EEP,Dn8_BTk:K<p\3>KP>p(MB@2\n!LZdWhfoL$HZ<.4(l2[_^&KIpj\e&;07OKjoak#"_
Q=O$Z50W`7UR1]Ag_fK@0H*HpJebdNPc!p,:4)6Fq(5YkTr;:0>CQK1QJqG.Ed"An!d8=5%;#
Tepp1j8,=s(-ag:ubi8Zb!'@*c&g9`-%S9#"N`:+oO,>F`3Y"0tP>77sn\[KTCKbT_?$qC8t
#X0]A:aSq9RA:KKcV7T!tpP"$J^&4*",q*1dMX9Bd-I[.]A\kqOaUpD#)UScEO0JnJ&Iqb$?*W
Xp7"\)3`pG?dEimcMG&A:,J[`Sr#*+OKHB1[i@L#CI-iGsnYMsQ68,@.CKIdnaS$=j6dk:e>
U5c:NWg_!faQ_8_"n,r,r1K7TrfBKm0]AsPGn&a88r81(e=YV2_X!:$;YZ7lo_<(#o1=,dt$X
Y(-Ag)RfK%FRl?b4qEk"/#iLfh`l.Nn(5!LJe"qB6G[kq=MqXC!>D/Q61?b>/\5=+=C6F3m(
@P\;%[Ld;*3NHtN$(?6mNGEe$hg\T>`n\%"p?@5ZZHUah34-OAp:m=:I>Uel$VZ'86-mKAJO
4phPAK-KKUp(nI*^q&cV8T4E6roKD6oq-lg!VC4CCOh1"e]A3;BJRasB+pX`Ik.1Dg?!EZi0o
X?NRd(*Dno-sFA^JG5rQ#95bWASOq>O0NFr`+>,'LZ06S(Fs)q%PE;=I42p-Z0krR\`h%fD)
0;GO*@r6\V>6sn18TEL<-8hm5[1V*;f#%+XL".pJZ^W!3-Yu%`@eSE0^PcM\ER3P':%dF+Ve
N+\A'V,ao7*[Z-[@nJ5KkfPSGm+Ri1`Jp1(^&[_CS;'ar!cQDH?^M>hp$IY4dBmu6A=k6=6N
obW#%KMs/In(pob2^h7l3Q5;WUbVKNAojBTB>$fK)X$hk6GC4@KdC.T^0cjU3*%($Bb=#=Xc
[*!%JpOO1+0)F.89;X$c1)rYl&[*&g)K$V`e7,ib`B*2tpapmiX-pT*))_$:7SOY]AQWCZUes
o-E3),&Om11LJ!f5'T'i$`l1@kt'^^VWH,/5IFOKi0Ejaso*&1_SkHr3AkkN+Znq$C2b*5mS
5I-&sM`Z>t0gEXJSOPN+4NkuM":&I-rl;_eGq5"%Lqdk=^.Q[f-/J.CUd2*Q5!ZKW-P9lkSf
X\9Mg,$3iGK8f.R2q!F)k(["ZqS/M56rE:qZ,9$AG%A\`2BYt<gA<%@I3CDFLMH%TieEY7Lh
4RKHC^[U\Ic#O6!dOo`AKI^:m@*%8-_W663LUN0o/UeijFh;%TEI`j=DfOimgbRrH&ratceA
8<a;p"j^&M/mf6`,W8&M^^(I8EJ!DtcPp(o?@mIQm$;-Xr08o+jBq=\*%(#=K<QO4DY6LrE9
@j#EhCYe6H<SkDZV-Y*$,'@$9P4d@)2\[T[NnW>@,)\>cho\6c%;MS:Qk;I[O`G@)AWJHoAd
bGV(3iIUUeLQC?b6Y:?=-,L2Q_n;h225FM\+%dKM]AM^ZbcSDA7BD":O+l$?!X).?&?LD`0?3
o'#I77ZbR8pU@1YS[(TMXNoK%J4m\^BRl^$8hp#%cZbp,>VCj\\Dn#l[(0ZV4^+Y=elW>*%'
iQF'.o!'@tf%d<Q22J*PX$.`-uM(XT6=3P-a)0\Ws,[Kb:\:ojOt+#TVBDg52"Do7Pf`igq3
jA1oAH\5Js$&4fhR]AhH*Jd9F0e#(HE`>`!oE$?ordi7=]Af_YPs>r8$VlZFcRg0[COR1hZf`a
5%SK:Tfu$H0%`[#mPhJ6S&g_SR9hgQGBnO)eVKAL<#Y?-h#\##2Dtk^EId'TKY;iD,Z.Kh^o
%4V3melOhXnc81?N^uBGOFu5>U\g&<Ifkd>2os,DV)ir,!_RC4H6CfC`N2MOqVK"MN*c7JtO
Q#_!%6qO=%?RW8f&#@m4<mW,*nm]A>,?*n&HME8n78!io^W\__j<D@i82H3[7EH's?Se"^(8p
QSdhKp+\@WMe.9#;i'g=YEW9_,k%"oj/SN`8:B,D#md?N(\,#jo?q%5bH^%S(_V"cjX7EX5B
7QgZah78X1acFT7crbjE<s7[1(J!l?%j<DrBa3*OMpJlG-j9oLWtg`q[':JnqAe+k*B.?Ue!
+$#F7n[&S?h[ZLOsc!C[@CHWNj-#s0d2#`hTq<#+KX0"dBA?YVoU'J5)=?j1G2iJS7oXX]Ana
@LWVLHS:-[1q9--8r"<Hp"_k3SBp1#[m/42GQQP*eZS,#eBc8j9aJNqED(\KUQn<Vb68KQ\A
H.Hcl`.D32&`;5X(7*lCP.?VZ+OR*j[V:Sr:C@*VV`5_TBI#gaX(h1dMiDCA`=:VoY^d=ap6
I1h.M_Q*8^;=pW<gOo7B0lX3'!+ei:/UjNe^JqQ-<J$'eaNK@c&4=)E;43jM#dUVEjRBdmL9
Ki5\WM'UUa]Arrhe<\6)m/Gc[uB>s>-3dArQ/p/c.HG10']A:@#3($DOYMcJ;6%ir*QQ.S9Df"
_Jk\E?`jFC`\U"mK5$-je:H$*@oYC=N5YOX5\_SAnf^abV2l_DdXKc=;`M=,hOiEE=_=@HhQ
1^'KI!mdiXE,!@"DUrDRR)fau^C4^+_/V]AGT[0g8'>STIVm'gJ5oNYU*n+,e!S#j*Dn_]Ac-g
s,-6Yhn<=]AF]ABM%l0EEs3+s1j5\&A;Yri-g&q(6_"T9hheN1SBY;'?EXkPU;mdu3o-.=LR>O
"28)=;R:7Ij.G0T`mDQM6ZQe0)`GV7]A>ZX<]AX8=XO/9I@N2::VRhs*2ha8K_uqG'2IPXC6<W
0S;`.mL.*QU\RJb^=f*X*1BO^GLu`.M=::bHHA/LEd^0cR[YKn/KWP;psJQ7e7=_pdG`AFSX
LGK.Y_[QOG]A0N-'cbEP@Tj9c^IaN4jV1[hN2)GGHEZQJVG`@m6X\A@a-n#2_qb5SM=n]Aies(
uCPTUg[,!ER9q<Hq2`$@l0?f5If8PO'hMG`S-eA"T3S^Jr<\j<bDe4%GjWGN1]Am5a-I\eYQn
\Zd^MXoGET"1Sn0NYRj:=i(J?D,R%5/4MLd.9.7G_Fn%#;*UYBOF4T.:Pk9eXeP>9n$.J,?^
/B&)VC;SZ;6Oi"r0TOn$!Zr(^8:D0!e$002F['1;I5IgS(*!MYiBeXGVg?imj+7)*NpFMPte
N.:tI[7*c8m21eZ?@BZ$H\hgi2te3\E_r<olNs7$`bEbXT4$IHK3HH'PlRcgpV(qA'ts!`Q>
W(q3C%1''j2b^B"cg.NLLJ2Ha"_Go-E.='V<0_d=4\lR;'0De/8u4mC5V*SKdL$C$hbo$lVI
UYQGY8P@B'C)C)*<lZM)`njZpj+%TPp@ors`ON\u3Z1c8Z%%-m[9QH#[3ko":#+$ag;#g6iJ
=_C)3N('XrY5p&"83k*"dR'+XZ_=jH1,?>&efO@;JoZ[fJ55ZmPiWO`j)0&[f=PZ7<gs`2U;
Xg^HL^UH6*B,mW?'c+/4IQB&HON(3k%C]AX>)adSdO_<s_/fBNDC$S\ZAE`PWaGeBof&$N_o_
hDuRkJ>tE^Jp:O/:X>$G>0u3\5lT?!o4=BgZ0ei=9Z3!qAeDFM"6JC,6DIecXdMM1pA2";Nm
*QlZL[h4_3Ap2<:Pl@TsNJ2Jt;nhS^k-b^*\th`RrLW$V!V.Tj(\ThtNu0q3M#b>$>TZ>3Mo
S_r,PW1*/#N:hh00<#3)"9\:TAbgI?,Y7AL"J5Sa@XRYCeB"Yd>T$?,k>SACAK1ElVONjIt=
^gLENlUgqbY6EZROUic%N-OIQ,4H_+<[,:+)IQlP5@eeBXU-3D`79Zd4W?Drsca+!Bmhk52+
m^<gLWdc-6.7*Zs@e_MU>o!&80e7rT<&0PCm=U;m:JY"i-5:HTkGO!kS(K`W^]A]AZ_]Al';rO;
(n'?+"D5N.M\pqPQEb%u4]A1%f1W$#j4X)eSlUP^/UKJg?"rJ3ka\bNP=%3*sc_/uUbJ\Kol]A
L:sJ`>!Zl.=Z?bbIB=VX.,B9i/df?O]AAl6/1l,/;([65k&rlfinPV`\HVLkUZA[1=/&"o"rm
rY4/;J$>a3ngW$.Ji\")Lo'F0aDV_ff/LhI:*%3g".D9LHj[6;'Pr+h#3r)T)`7[+T\3>7V`
CMr5VR!T_Ag"&9l_'*#mN9IacmH%n9gF1/_^@Mh0H(*i:LqM\boCg3E'1b_i<ri"/n&847pS
WGqe8^Yh7f'XEGi5nX",)ZXsDr7dA=D1gjJM0;4B5!68tL:GPdr?le0RJp3aj*XJt;[&jc4'
rq^gj>u75Tb^.r(+)NCo2UhmY$s;qdVdNpTi^osB8V?$@$f1!N?XJmee[UAU'&O3A(Y-S_A-
SeKA/0.e:K.CW',e7S!r'+W6oSHSb%9_Zi8MKu=kA6!DkT9gXU`nr#)8^%'VPBt:GID4"!p/
-"MT7/cf"?W]A".)K?`6[$MDGruq;4MGMm*Z(4&_?RLHm`42+QqqDuinm(==@'T%h7M,h!?"(
i@cF5hV*AB9s2/'><>1F-"0k$=:HucIn*2MX29u#luFH='$S+K+0]AUS(!;(&[t2Z5Ea_WfA%
XA<d&=`l>)N>bl,mAjgjbVqR^L(e1D!(&+<Om>.SGBG(i9\1oNSsl[s5R;F?T4TZMhZ*_Xfd
?b^lCk7'Vg=FWSTKkN;=ZhjSqT)b$%H#C>]AFN3H'Q4X=LOhOT>jP_>o9Nl.5`T0.ICI!6Na&
5q6e*5(oYupeGgVm<h>"=!9.2U`m/D(#a:E-uErI[F[p;t<?"YP,RkOAfMd$siVHjlu>Q+UB
L?t_YU83<I59br\R$$Pf]AN*`Or:"uEK^CAn9n`9TbZZZ\t$jp7pjWa1_PPO=1g54,R`^'%P=
kElYdlK'pkO8)`.SGOM%`dQJCBhaM.CCg]A:BLr=!2C/LfZItGf%D*oYAEr6gW9A71Q'Xbqe4
>hWgeJd6eu1qR>n5h)42nmj-%hlbQ5DRHZE90'VZCR=BsHV"XIIu?Q4qBof+(Y>"?/IU,jn.
ZN>C?E7E6s%,Z1iO#JiDUEncb=WU%&&;nbiS@(M7Ak6q+Y5DB6UtR/&Sf@IEB5oM[FJ"W<;)
L[fe((ju<_[jF`06fa-PfGr@9&(pMRHW-9/O2[<k<<!ig*.tmo[<M<mq/^O)T6@gm8eV^,6p
*HX4RhDf-E0^uHn%BOjGCF]Aa,*T1!5SW*.DNpW6O!K(p)6W-jL&e/uT8)nCQh3)HVgIYhAP7
KZ<ub$:QsZOsEV5HjdmF-XoCK`%FO6"e89fTS6TV*,&_ib[bf#N0ISn)J$F(+5;bnN)QsPnL
r8`<,ReCVZh3P\<jr-[ftI+H`Clb2k#eXf75"oBL>baT>Aq.uorNqbghX:%tB86=bs&JoTdu
e3ICmpm\8%Wup&B<(pW#Ylsp6pL,"sF?Id$RX!)dM=BWE_ITHf,2u`6nWXqq/WbP"F15o9Zo
\7;rTF=9rV#j2cT<;C\rr0tFWjsjc7;lt?0LcG$s!!X;0o.)E&P]AuJ<)P\_pN?._KMoa9%Bh
f`U;sPWo=k24!<7CaDiRAXf>E7M7m\jJF>X50#Aho4)+SRG48Fi`:Srtl`J===q0JFIF*:IE
som"m2-"hPGKeN]A\P(N5!DCZ9dD)&W'(CL.ZgL8dETZr3EO]A<U'MS@B6\\VQ/QBN>g&mVP.-
u,!jrA+b>cj"Z+@bhH+f96`1f1@.?Fuead9(4&d@CKYlnTRM$59M3IJ#'O#%a.XftQLfA0lc
K?8p3EOAQPVBHOg"%;n]AeYP_7TN<ZhrYg@s?Wo2q-]Au!b3pMIfJHf#M1A8]AdWm$ZnJS6q;i9
lA::URY#rH;D4\QPE+b"n^Q(_D,C@`8#`O.^+Z\7bTYFG@ATVtX\\Qm&upX+PR?b*N7`E2cs
AX&VDioa>Mpp1RVf+O9bhGo&s]A:?.;cmA`\6gq0Y$L8A%.[M<jUH?3AYn@YJKN2X]A5=5g*n,
b2ne_-Z;sjC[gHTa4C'Cd?9N+p_Q]AB9"%bi-[!ZM5![a`!3k9cWiC4AXg+$#:S-spmOhp:?B
5(d#rUqTbO@S#iDp_c-Z0fWU8,KQ<BufUUsho9P\C5#E8jC$r:&=%kOeo(5%`]A!9?`)*i)d@
rhP;DaF)X=j7fLk(h1(gldmJ/kERfNht0?_rJ8s(ELl@-#F;3.-?6,i"Wh=5^P3(C^[Q"<*]A
:7l=8HOs6o>dZR1.iBS,"Q80C-D]AkPa-heo0Rt(I@B48B7F\=M.-Q1e"YIW`:!r;!b2<pG`p
2+Sr4JIIqtM1Y*\:G1Q90]A/HH+YcW/`en>RNqU8kk8LfR?/=.EDLe<*B-GFNCnY?B2J+oYGV
8GaAO5mq-B<H?S-)U8h^pU#sB[P/cW%o(/aKNof+uq*sLhP:1@U7;M8:"!i3!0@q_oMYeUn\
.);<e?LY9bE_+8`.a)6lBWi1feiRea4>+&4m%MM)!?f:]ALQE1ulh:DB>%9*S8P!"$?/d6q;A
R,M268]A$Em(!ML.4hp0mBAnNS_-0+^X"iN%#$OVnhioR=+7\8B``?uB726)P:EUQK=I"(J39
e%4T)!BQJ(il_l*9H8W/p8Zc*eXrSEGg(9Gf1tUs:l-kgj:#oE#nAn#bGS`mEZbDUB4b=r3c
_'o=)k1mVQoGMM(!5<@(r:@2r$?s!EjZ6&?ed"#Dh!fRX\QYMdIAuTKBZ.\bWN1d1QW*kh!j
7YWsf!FskXt1Mo*Raseh(0*OH:pKJ[E?CGb2.fm2J!-)mV9P!gaZJG/Cr1!^oe5po['/[XCq
(Zf]AZLuL72GN(=SDtH+h`<^b"=?TRkiX#YmNW5*@?TbpmP@rlL4]AIrLs]Ac!AltE30@Dp[)+R
4F']Ap<'ma<i\9".*a,9lUa\@3f".*_EeWccG^Ksl7H^UrVSKW@;k@Rf1\4"p`;q_tViWF]A>s
HtaJR6Kho\[[T*)6WJ+V?[1,j^9'"t0:6Q;V^U@oWai:!^H.m;'fqs-b2V]A\;!He.I_J^(df
mLI>AD6NLd",D&>C9a#dCOdq@42m'J$I8=NI7G^^0e<'t2gE19q3-@!P%G>US<ZM!(jE>Y]Aa
`Xg6g^mEX[g-fb#/Kuhb(s'('Or@]AlAJb"0`9icrNTVkj["chim$b*jB5K+Xj0#gg!TETUbY
BI:eoi/6'"qcjs5Nq5Y/htUf[EJS;bTq3Y?(F1aN*@c/AS2C4sRug6R@DT%ss)2h-i%DZ3!5
;KG%koC8&qjToQgq)k#+%AC,0Vd'>"R:h]AnfND2Ip#L+Th:cB%e_fh]Af!l8"^&lr6&/UrYpU
beV!7<GnKT5k;]ALhHUTg[1&lP^hlqu3EP/[i<CmhF?fj*I*3dJ/gKA"NcG=!l_6e=[VSH`c^
5P!7IX&-Ya#8)t``6J9R/bs+r)I$Q<A5Dt#f4'2Mtf[S!90N(&]A5/-)@&V,M1J*H\dqk\LBm
ndQ$a`JWY`_XmRD5`g(c?GSCcADN]AE=oN8p&WU/CCFku]AuB$RW#J/lmQ?BT.M.r4'aX$X7/D
,b>@e)=V6B5r-!1S`p@_C_-5(neRPe8s6r_<cnsaI"i+R'0UM'Hbn)[8Xp`":S8YP(a\K`)+
,ZAQ9.1tLA+4f%R]AAD?eGhaWUKhpUd'4UtXn)/a1UZ6a?ra-C43*,,TA8=#0Kj$enkH>(te2
DOnDQ+YErGu#<OAlT9C=;8I#.gPmL$;maY?1F1fe^N=8+TgVn#1RGq?O:h=Nt$#JG\4P?0^$
eW`:1F*<]AK%7cSqk0%Xu<W9R1>]AsLW%H#C4))B4._B_Lec*t\8k\H>c3qDl(4a<6KuYU"''e
q"<(@e1MFK>7=IW"+o1BW.jb!J8/M,rhYWYoM2S)U-pu@S?8GHE/5Pr528DG6GAE(P*kKHK\
s8h`K^L.K]A/JWmQ34r<,8o5Xgc;kHOU.='".'>.fp"3^$M7ra`)MT2-6VUKJQ?Ium[K,>UH]A
I5S;HZK62L&qkn^&e:TJ74i$)VXOND!EGIrLb=8OfU4.;*Tdt5N%-V6PZ%O,RgJq?!=IYcY@
-F86shn^Hc:E/8'9+aV^#6E^/L@q*pg&3A8.'U4-.ndH^d&%12HqFde=;@fC<R]A1C((jN++g
m;CIWr=F&<mFom/?3p[1*W3XXonk)5^U#X:n1tjh^$c8%KLQ\?O,^V'//m7-a93:shTc4fo=
3qfJp*UkbF1LN`Cd:sKapPb&Oqj=^6PXcL#8\/?+Lg,q=NaXaI<I'Y0,&UWq$EMf;^]A2=Y)6
d9)eOS.=h*4=c19iR$Q!$R(WY(>+>MDWfANMDlY/",C<11uLR\Hi$pAc9]Ae"'hScARpZ(hKg
P9[.U@2D7S4>H\5HP\&&U>*:m\i=2*UBAAAT0QL-s%W&jbtg%W.hUP5/%kQ`a>1UKNlO<jdH
2D5Xk4]A)3YIK$WN:jKZ#ZW6<HCY.@7rWj4q;8sGN0#*.E3.R2ntLF&TP6oY(6T^mNZ_e)Z&P
pX09<b.?C=4[<8fm4m!0$01\[D3/S.aRHcR/`CM-9rl5bY7;]Au6JX1rfjQ^4YnQ)?1%mDhH%
+>FaA_8fqZhTpqr'!*ug7,EZK-L*@As"0$F;[OBO)GcCH@j0gln0l"mH,[;8]AB?PWQ%;7qI-
G"?GU/@CEjs*L2cY+O1t0u1]A[bq@`R(-(gIuRa+-D.)*6]AA_aHH-qPDSDGRW4@Tj797)DU0R
UWm@lH)Y;3JIJB,mVEWG&n0DW9DEZbPiJM[")_Gtn:Yk9E!5,\"OtgR9^]AAP$VGCXaH1Fn\W
TSh$*=)?m`#OR,>PQqoKOSK(dJrG_/$b9)>0@4N@RWd#FVGn.F5!L-g`Wf4Wh,#&Htt@K6:t
cP8p!Ifi*)jR%ji8#d>kZjU*s\loU?"iVLL/#(?I4]AAa<U)m7DNTdZ&5otH_p8#O6`C;O&8a
djLG!__YgA,PtW@`a:'*ij?fjD&i@YJ.8Aoq7j?_=$Lf:%jXnlW*3h-OQs7m08S,_RtfW##!
6-b@5t.":!QGm4Z5W?3Slf@/p"ia;@2eX7Z/tcoN+53V<2]A$PWt^G@00r>_PSI%%8LK^muLC
2(dr8]AFp=5$Y,8tm.$c<S,.<#)J4USeaSg"-R)cXSMP'e-XK%"kV%'Z26EK?h]Ad?nUGoW;DX
Y8L1`J:`09q=*3i%S.V9CGp'1nC(VeaW"&"#P*S]Al/q>g8_]A$LYV.'40M:l)I'qH4f\^Yed<
K4"teIq0:a`r.SA&g:\iqe>KJ>_B(J@oDoUZeSL5d.T:J\G1Au+j4famg!'R2\&@,S,m4GZm
h*X%!K:*rZH2;$7">KJ0E6%j3K25=!9;`7V?s284X*2fTH1pC=!&);c)D[W"DM6IX8*F23U<
=^PEEYlYIu;e<@.!,mj:]A'$4n\ecdoIP-gF^G6X,Ged;RYaUgZrV[d_.%bP4rFo3ee#5t+3C
)4\><g2%n$!GC`ac?U*S(6ASml9r[\FsBLc5k*qZO$]AVpI7HXd[h9W$jk;]AVH8FlZ]As]A!g3:
Tq37BbM1<cV.I9/6!AMsL[idsLf5^VWRLPF&(oNj3`\H'l/0:tZLLigB04c`X4L@kH/;O>'I
P[-^ZZ0UsUmgIZC;E(\_kck?BP"i6a5"HJ>-r`2J!/?u&kBfR,t[hFHQbpt!$QM0P@\37A6V
2-.QcQ)HM'1XQ!eTC($Es5#'UYkY.m#88X:XE7bJO$U+GEL^ZEfW=Te02J%bYar)O$r#)hiD
fq#BukcPs[oV'%3\Zr\M%ee4BY;P+9[G]Alnb`oo?<@,KHs-_;^)UE\7><#M^_0'GXh:6.2#&
Hh<qe:64o`&&`%P#-h7/;io;$2>NfX\fX_sR3QUi&-C6/M0Q5*l$bAks*Q4Q/JM1M*o^J^2!
m=G]A,19]AT0bZXbqm"I.QsjLh!Qd?G:'/?>0\198.7r5gu%*">J?SrW'_>\0@gQuergM=k!rF
si&03\f`T\(\B,:n60+WnB*XOJ(a!,p<<SZV/$A?_\b#(^A3GC$kGZd-Y4.N4R(V3nRRgf0$
sSd\KF8h2C_V.poH7,+VC]AC#Bg2ZYB.Ld-n>8`oi2pp'[>lF*5nOgd5(*n!&Bp%lD5kTg^cK
57)6R7.E!90-\VP5VEsh(P"O3BCoJ\O-cf>agA]A!c_7*sPp@$qmS/IaQhC6b.lK&Gg8150!k
ncG\.2SE0$3>oIRl+EacO;s*'B`(`t>KI^3.q23hL@F9ocO$$Ph(Tu$/1s]A+hM!j&)aaaFWU
fD#4h3>Jr)*)u")E:^o\rf[\5HO,(*9-U<&a-7;$61m-:=Lj#(*Wg)]AE%o$![G_[e\nhj,n9
''X;'"=@`.LN5"5C;6%.Vp1N2l(i\l_R.YY#S\MN&"-8\*Dh$sZM9I()n(_i*HIR4dmW"od;
k8O.6@UL^]A@@["U=is`(U_GQ!0I$n3$#6h6#Pb-VS$Hu2c+d7I[?#k^qeeFM@uHc?.hsF)$A
kSrH0q#.P_9.d!-(C;0T@=rVdD)SNoIQa$^2!$JY`,L>5de1]An,uVFof)TM"FQ0M]AP_b?d7V
ZbX$/06:jaORK3td8,Hp]AMbLfSG_(apTdO)iF(WmctjT>Q>KjCE@mAY6ns+iI8qLTSuh+Z<f
H1&?0RfPeIY_(J)6du`Q*X1KbA"NU\JF'/@+PnJ.P)7n*Vjgf0h!FSb;bWrfVC<#R&1T0V1-
7TNEhU^B=(R2#]A_qC*-G6f$nuZ_eF4[.q?VU-Q+%OWR*g)+V)t_6QHP-H$"2.glU6i*/%.3!
WGH0m1;WREosP!27/9L7G+jXW]Acb#DQd6q"Rt'H#:TW=GcCa(el@''.PV1ECjG8Z#0bdQICr
>-Y'N#okeH+Ha!9*eB68WFR'D!<1\0eL78POBT=l6B+u<Ch0h8Aq70DikL*@mEFp&1kqK!B@
fSM`)!jXnO5W[JU<8FAV[n4VHC8HYaF`gNTC!Q?Tm76D,8*XBdR4]ANKI3I+[igf.R\_f!F=l
Q[<d_,srUdJRtD+.rJgq`1MNrj]A[e_SFg[`TBmOn'TGS7p5K]A?TS2;]AXs/Rn^tF_i(-&.1F5
'>^EW&#DGV:=%`.n`mcTc%ahb]AkPYm0+/%ET,Pf_g"Q9dTUH&<$5'r4/(HVUs7-_$_S.V(3@
cL3_55*"n"PT#BA&)o+&W1aIW5qW5.uJ1PJ)p6&RsKQ=NdIfHJ/M@PnA,)P%b3E/3Z&#\pF'
dn]An(;IfgDRibWI@9!g)1q&HU)W)t6='ka0_$&gQu-!9[PYf5pLYY9T/,q)uc6_mBedWg9eH
Uj5'6domO=qgJXUg6.WuIL#)pRo0$jhmXfm'X=ZAZ``;BX)6/g2ClC9Ra46-?2cgoYYeu+>"
J5?KgJb-)F-8X:Grak6s[tr.6hp7jK7;]A6%P\Q#Wjk:FRL_:ga`e=V,ROZ%&G!d'pumd!$Po
tWhAL'g,_`%2a/UOp\s]A>%&h1hKrcuGP7`+1ZTbE0IWQfTNUIm4\ZHFDTuhe8,UL^QXJ/3iQ
9,A%1d0jG3Hq=35UVaf[N!K:51;)3>'F]AjT7ZjJ"Uh`pq(2:af0-&^Zb[.1n5+@`")r,I1a[
'X[4J]Ad/ui::A2mP`@/0fNI;\JQZLm!4>1`]As;iLZaH5lRG!iYJ=_2cO7W#Yae*Olm&<Zuk'
O7mN/+Dj)'j-:R@HVlckF\fp7.ZfO!-pT7W=>aR&WAu/&5L%4A$`[==mX00-Y"-`!,A0t$'6
,;M)kWl@S/7F*D]AC1H9Eam@e.s"WCuUc;5lrY1+0GAf0mcXO0WDS&h[1ik[miUhMOO-bEomE
#MT5dH"D-Ltq^[HE)Cf3A\q3Q!d?HgB!4dN]A7(/_f,hcB0h?'^QbC/(cOj7LhNEL`A`$+K[3
4YfQL^-.V))1`Rg,umG_VOZfgLpZ6$?#qQ8u)k:9*1)0:4E3]ANG`)'mm.CX<C]AO`:od^o%K`
%e]A^enINq#Y675E2aU@M6oVo$q^V<mfUhf5&%*K'?D*9;Ftk\AuAnE$Q?Bkd!$.?\W4cA;)T
CC)N;gV`Z4_u?8tI[5ad9/^Y4T/D-og/8uqN5Ag;UKFE4qTi=WQ7`a,3C3&H?4(\:&lSM=B9
&$>^Vq/RmqL+U'cV$JQlI%1ME-8PSAN/264G=L\34%mO06aY.iOUHLC4PHj9Z-lBH[QJhJ`-
*FjSd%h;_5^Y^Jad+h"*GU/d;u1H#OEY?%ghXHi3=_YVHbreCYV[fpe/Q"X#INc*,.`/$.k7
qoeMDKoCi\L7?gMgCq(E!,F.mk__[Z9JQL42R3P,4``GcE%3u*%N/NQ2!L30$b!-0.&S&$8C
E0eR[XX<+PV*J+M`XHNFCrM\_V:)Z5"8noQ6/6&0Z$@)`X>3,#\Y4qRpNFr8o%XAFD3&G'5E
YsN&3hf8Kr:juPF\^rqI1%DF,dcfMlY&1S3;Ci4FWm!Ktf"@,nG/`&08+7N#bS6m1\G8^(Iu
'dDYE%7YG\;ee^7S24l>!fs=p_9CMdrEi:bRE>388VQ_$;4-Wc@VF=g8p]A4$WTY;T_,JU:J\
c9$[stqBY#2OIN"fDA>H"0i7B4U[E)u\l\(j!Q1$.<R&BM\`'(KSd\ZlO5Z5j.I_W"K.D?Fd
"o:g>\)*NrH9V["iWBdMuEc]Ab=J]A)R\E"B"X"$pJS8"l6cb4Y%^e8He?gMPMM_aKRApf_>G&
2hA%t/gSK:JU0gTi6dtLKQr>Gi7[u9CR"tl0hOQ8VPb')*\"@"/t>S`i4JEE^j`F\?R_P<u&
Ab\=V@OOsn>@L>'Ck$ilWG9nWMX[:@]A2jT5_gK_-'S-6Cc)DngEsti46&4?C(0Rtj%Tk=C=#
g,m=u2MZ(4/2=%al$+8Qp)Hcg#LG"f7H$'PP>rBXnm[[Z3Q_#C0T>g*Q3DVEaaRY@Nopdns2
2Ms3/+Hi.JDpd;sU3?uR>>5A9O^)4WM\f/Z;dl_#B=S9]ASQ>81"/Ws+f_9<:kDK*?LE.5jrU
<6''=M`@!G`a#EkPlk[0:"c'gS0$-r#@6K4g?3m`bE;slSFm/?bK=T2Vl/r\$-rn1^\R['d7
3hg"pL%C''@+Eia;1"Q/h),nM&J.u5A2]Ac]A6"b^i?8NtP2a"Nq]A=5c%Fe:j3c]Ah`Vm@$Bm35
*WLRr&HrL?FP&Yp8u_e0A]AfDT]AX+UDQE0U(/U=K'1uR"YDX/6q6M\Fq=n^MAN5Th0N7lpXfY
j*CjaPlnb(l:t\6%[ciH=+"kQSM_lC"$8!Ea91Mb_pYg1R33]A64!cd)UQHiCeh=<=Vg,U&H!
j0Ah'*?8Yam?akr@>/-rOF6J=N%YnOKLi9n&fYAiCYR_7#6ir?t%aI,&Pq->6b8"q[L_M_A7
FdLT!AKN-/RNV+:bZ73bu)$8GJiX\"EH(&'!)oDi@1tJ=:<nD#Xi5,2RME)3T`/;(k%8[[dd
a'eIt&N]Ac8gT/;12MaHb&8o9QuuNai7c##DDY[GT7jHhCR!e72MVpq+9pT!c`-6]A:bCFWHO!
4?\sE0h"eKF8$8%*ANrZ!]ATQ`U+>8Q"rEqSi:Ujn`g,pcDa=DiZUaAO:>&U[O3a6G/A#(G[7
_XPY-f#%B)O%0)@=E;G!p%u:A=J@(/+V$"ujSZ3uW8t8+;<T/E,BG_<Q,BB5#9%W1/YGaSKE
d%B"3_%9\@te@PmRD![&sPK+8r,8O/5>p\CQ_l/N>Hu!mD_,2+$mnMV0JYa90'PYIj#SlL^#
/\bs9jS"#:o1`a`TtKnk$KMFSr$R>E=H6#:mBmc0r%73le?:;#&>,8)qi9&r]As^(:'h.<F1?
jQn/fXEeO?Z5q`T\3N]A!U9:`';>GiuIAeYY?h,ofc\I*LbND8i%nVLSG\`I8)WZWEch=^=bm
E)jg\5'`XT#1lB<L2%Hb/1&`4L0$^L-%=Bli@"$18#2.Q'LQN2e3<NK6l<`DL8u4gRIdHjRs
V5O[C,8c)gId\J3JDtXf`JO`Ap@>G]A)J.;S/9`W"8"1kg[ZJ,J?thiLM&Ff!n.kI0b/\(J7;
8#6F1<KJiWiF=53H3!t@1`FDuAOA6;N,QgN^SuX>-+]AW;c6]A.bm']AH(Mh9JCF5K%%!@domXi
Ah@\k^?qMWF]AS7aB!7tOuGQ:,h9`o0N)rg@O\0PTiDG7O.1GGAe=PT`fC3Jbc0_:]Ad[^V"4_
6\nV@&FbMTV-es+iAd5V5nb6)O/.l*Dn!==I0)[#@iXjP-@!p*Bo@D9ASlkI2&;hfp2_tC%a
8Nbfq_I7S*]AG,uO:4^)a,=62-ght<RZqIr`<0F!aPE".4DJcAVX9'Cr4ZX#)@,s#WqAPD4fp
WYqkH4Q*8J.hG;>/k`YIo+[XaZ=e&[3`g\7)W<4a^&,]AKMnJ?fO)trbs_Arhf5PKjV;ZEUKP
L[`Qe2A7r$GH#sMW6uU4cb?3AZYmP%WXHQS[rPQrQ8iaUOJKhYo9)Gh'Z[l!'/50k\`(%Y2!
]ABS+`n0VSh;$]AT1=rtj,W&E'Xin:]A@H=B#&%7C1[@Yf]A8O]A`6,q'[0T&N'c^>AQHn-=-Is74
33.%YB_jLSqEC'\aLaja=Hq&Zm1j``6"oNR8ap54tSs-c;]A3a-7[&d4h?S_kBZXZSWUYW87[
nrgZFc?,X/15^(;VFkq*nY1p1\e7>fDfpc(DqPTm8;Xs4T%OEpCK*FV6ua511(j=)Yl>_/h%
pJUjSOnBO/KJkP5Iih?U\(9nAX1?FqET#n$?u;H>h69%\BK^S404ek@U/O5E#+i.1Io;JT9B
("ZF]A@85F-7iU[(c]AtnF*UMf\&if%q%["_hDJ]AIcWhqp,Z3Cb..(6tjZ\4*>2HAUEjJ-dFuN
K1W@3P*P4q=2I4,`M?i^@#h6q5a4lb%HP[f+IKK,D`cF8CCN^1KLm4fTtB9`84#('(/7<nUj
kKJIHAhi;E]AIOni#0Z!CPXQMT\(`0_I^!`R'"[0Z9ZUp^lP`nDd4VP6<K&Z"97GG3sh>lZM"
cfGbrb\<]A7Er8mRr$g2H\bj%VH"frXQ'_'bEl]A=UMD,3PO7VgBc<"C:%9&V;9/+>7k9&cg*;
jNq6JmhPhMhYdXlIqQZ]Af%]A:-dZb0af&'&,`1]ANadGej3?<OY+<jDW:f:3a+LG=:^ktEUgF)
el;??(6*]AaK3!rD<[nhdmIK,,see4X)+&eCnhR(0ST<%c1h*>75h2:E1r/2Q8C)31Cq"Xr:\
kbB':*@KW*KGRVB'mq':QW70Xsa5J!4TPVH'Dlrp/1Zk*1@:O!6`%J['#\)Q(_);Wj<t!1PU
/OE<d3;%sJCe8Y/&l=P'm]A0E:b_$7UA-''Sf!UlhMMl#HJeeLQu9r[j+]AcP1kf]Agb:GDjKAD
V[c^6s(7Ce[ip9ONLh"<."Msl_Z%c<rfT?2Ns>8[]AnBg_\>XR[UIaX^s'@dmr."b@)*g@ueK
j!##s81ZPsHc8T5<]A?/6m@Td>TQb+an*d5Z#i_3$GOma<R"C%Wra'S1`/@@j(`"1&%5Y9lA.
d>MW<_`<%5XV$J9<+<XrY8uP?01_BcQ!X>GGE=8N5%apU`H4?EIM:;fa:]Aa%!JV4R`TnSStT
^K/4q4G">s**pulPKL"n,'7jZ^uH6=YjQL$U68"$@YLfa]Adb3I/UOL3>cZ`lh\P.=ZWHLkXa
W=fijqBOSdZee_Am.erq_1X;98qN<:(V[p'qn<`eo380>Xh\uWq=:LP-hrV;epm3q;8jf2?Y
LD`gGh&79%p`;$%[NR1u+O_TqgIXVe75$J2b6@UIH46("G=*Y1Y0!b:Bo.h(V=ub7BgC6n_E
T=O]A?(DlW8#[m\6NS%Ot)IWU+2]A=D>fg1ISGD'k["KGG8-q%leR>BM%d#9RhiPtS3*dSIC-j
lQ#X9r\D\MMT7..W[3K^5ORFp<P?HS/Zn1Cu[/AOnE/5FKn.9Eo`SV0nLcrPtP18tu\ggmH\
c,+r-%.Le_&8:+BI7^LmgK]AbP(Q;m1<Xbn9@Vtg)+KjQ?QWM"D)SkMI_%Ukr!CXuQH+4J&]Ai
u@JEu+h7?Kdse6a+HXdEh[emrdD!+rr.O<,CZ>m;\jD8=`5HLb2CP!I3a^&6gU`XuFV/sl]AD
)0X/WcFZ#pbW9&Shu<m&##e");UcRj3L8]A0]A.k1cmX'(j(9?i=YG"tY]AWLsRmU>b1@\D%qaa
G>KIm:ZC.JT8;qKF/rE\&@LBG7U1f_s6pb`Xm5Z5L'I3qlg[gB[$kXgIY6DafhWbpOgYf[@`
;5pRG'*]A'O1api(0_=T;>5p)@<AqA!QE9K$&8Hnll#hm7sBk4fC$seTiD+(ITLl(>=j^ZlBS
N0Tl;Pn\TrU1cCC9=8XVq&-_aQ0*thj?+Wn28A"ToQX8L8M!q[GefDe-6hIAcut1>VKO9G*+
oLK[/[t4V2pW91?hKmSL8Bb4^(QJ)4nf7okt7bap9>^c?4"2Hc>OE;+Aq<Ak$B>aWVS.">"V
mq:(E0P>e>^D^[FW?)0MK28faWm\=-F-15p/B![n9S3Rf1WR9Z+f:tKB>?Fg1K]AdkMPV,UR7
Djufnq_QY@An"&!VFP0tuC"cC@s'?*.fTW`QW54Yne^fKQBEFGTTfVP;r\`nE)sDtC?h-^t3
n3<)W%+nMGkE(QLWlYK>s*kYrg's!FJko"<$Z>VC5_2:>r=A^KNO^naKC&%M8Jt;+;h>VBN8
[bpWAY=l/Q",+:rr2`Y7m_s3I*.+`1MEKG;<[bXk8gh0lQc(Dn,OE/F.VEdc]ARIG`33JrJ@%
Lg(JQ*N/;bV;O:[88r;Z0#J%"$)E-o#>q2]Af9Jbq?I\p]A..XUd-9@SLE:Ar"N"Sqs@d%?t2m
eH)M'2XlX`0#=-7([F9Wl@**XP2$$6HVAGhPWBKrDL>K1p0o(R3Y'gFKT@`@X3'*OjDYK\Q.
/:9mBZ6]AVB([V@qHr+j2sI2cm?L*MW!-n,QG3718h*LZ.^\tA_(ao?P4\<Mnu6bpr#oHMVZ/
a3H9g"9ekCkGs"/%:p5r]A'Z+R=T5p&XMN_8CIge42Uf7m%_RlYUP3_<?^2]AT6eU6r^9sN!-:
4dU,[eKqQi4aML.H@7fUYfX<n#:U3VbXNdjE&6iHfa^'5KmF[ENTNb8<8L.?9?RLc[GE5d[(
J!+*Vn\s%A#<gGfVZJN]Ae,^8Z/XU6MV&/3LE2l3OE,g;WDb)-<Sjk/DE&@/9.j8<7A%]ARO_Q
ONkbPodn/;K[G")EM#+m01u4USB"357o?AnBFRi%;.noLpEf)gPjGKF#ic@[s+fJPSA,5QF`
9D)p?I_-1KM`cQ!%p3kpA0`=B$HpYN9Ajfl">_aSiS6oiL0u*WUV[o&`BJ</$k:[)[[p[6J/
@g2106F^&LPPlPCs`<'3<Dgg[gH[!=m$V[Q^.J:`[Qr2h?SV_ng?UMe"YdgXH%q!1FTkGO5D
05XF[.Hb>U`Q@_&K:hMHKn_q$5&\EON[oEcY8I&^^+rT/,qA2h_XUuWkRI@@$s#o_PZ"Q,j)
$pOEJU4#*gqT/@*gk%bH3`E9"::_H9oZ`LcVHAWFqQ=4c"ZI;9LnYlR8eI<Cr%DJblb<@KJc
eGd>i>mU;)%6A:Gm'0?7W[!UD=o7\-Z^';m?.OFJW'lgkH2$0B_Bt@,V7c?[!P'7A5=l;8hu
DT3OnB**b.uYTIu`.X)roi>s#$hD5J,onn^0R6`r0Q)r-pOJm4X*NI[4LBIp`<hL$im"(]A16
tq#C/\o5-_;lJKk(Ha*CRbWd0-e@bpB^Q0=<qeE:9%N)9S;LTYR7%Qk$9t$nMEeJ7,p[sFqT
]ACUoGeH0JO_H=%p>pK_Vb,1Q`&;m?g.>PZ[XB#M;;2`/)]APhS&?C=*=,c+`a3%RkhX=oQ5['
:3_0-+jAI`lHT<AkeV5!h_paqb<""OT]Akp_Tf^<Ft,WZt[3$"pK''\;M;d4GVl.*E(#TS#>\
<rJsdqS?:'<&b5_FhQu[:%)rZSkk2I$iAu%@upR,h'ecuk@?+1:2mFPW"X^3`,PRd9_Z6-Y(
8j&rd<#g/@#f@NIVmjl?6-_^]Ants=jk&IUJ8u:EDS>bs+XQ$NFd92na1D=CQ0SU`m<P&?`Ia
I>2(;Jkgqf8c4KV[_=>_DpYr?1aHM@KBm\kbaM58(m]A.l+BI]A081"Hdf/`,.g@(:!oU!9c*k
>\TFBk<GhUa3r"?_CcKk-5MdPAtGprtnJk9q2^;6H!S0mJ+CO@9DfU]AYBE&KSH/GoBk9+m#@
K#FpEMWTR>(-';c0;)D=]ABos4D3%*[@9QuXJ"<G:DUgTIlMp2&u(5P$KXE.P/0p*RB%IIV^]A
kL<5QOcS(%bJqN:VRKR3iF:mflK!e&4GL$2dT]AMWX`E=e-rG0*(SP]A]AO,%+XlEmMMN$J17".
tXp$0;KVQ05LCXN^BW45n6unid\iXV/Jr(OjTU$pfAi?,<4^Mt$ZT1fk^&k9&_B7?@<P\JW1
gLjP7sc]A3LSYXP2j:d2!NiKHrn0W0:RhPXs9IQmu6->,j/-0%,c3=OF$gnH\fcd;!=?uN_7s
7jb!(Z)NIkNgPbbSWOirG-GOrZ;SYG8%>Jm:YaLZ28)H%4r3"-fnG9g*=XQ?LFrmT2Sc:F$e
4sBWJ=q7>1YM&W:S/kVok>_OX8[I:N<L)2X'%KA4U?>Q"GpR0WBaiiA:,HSo<_,<.IE3eYt)
4gAs(L!Je[O7^uAn\E-&.XQ1Be77]A5[,SdH>3D-F>Z))+)2o4YL2RoVJm^:ASaK>8f'c3h.*
%o9Rb>pW'I[f0X%`dER@%kMR$<ud(X]AI<A'GD3&SEQ]A.)8a^R=U9Upngl7hPlbUhk;q)p;f_
R`@Vb3?_@5[\D_tkb)819iN(;PBNo-^6N)DD;jq]AG!U"U60C#Xg-Rt"Lg49M:(3JWb=L-7hG
N6B::E3>I&mtSLZ<9$(Sk^Lt%?<Sc1#e2tBn[!VX1Fs^1a?2Uf`a39N"OXcHRihP&!^@?ep?
<uGA4!qm0k3hT5m1UH+7V3J>2<FdgSeTrj>Pg0@6bPgUS%=p>b/iWHgR[d*/6H)`%G85%J'@
(7a![jkNfu`\"gLZNVZ0<qs)b'0o"Z'@Rs.'cn#?S+K!L@X7QS1F8oYi\d#A\57m\Z/_,8?;
Jm+l:Mhif0edafa"[Lf!L"-ORbU7c!ECS#j>ULV]AiabdYQ4IK^I^GYI0HH'[/)h^pW>+m`Nn
a&LX*j1h@+@4,&VZZk!!!V+T!SL(5Yj/2iner%bJPBngmN$\4/6\I^#8Ek2[4rs9ad.<lV(o
g>t$YSr?gD?YFn.<L"_mAdSE)mCkLk9)dbaVgo!]AA?5!;_7jTnV0R%j#V@$W@q=Ooum)h45%
^&>?KL-QMVE-U'G7[Gro$WiB4:<dM60g(Z.JUh2jHDf$3trqTZA\O6Xc6"!n]Ab9Yt,j%^XOS
mrEQnCfM9KV"",(XZ5``l.JP6V&=jFnEU'Xrf2Seh]AFI25>JTpQQ^!&]AXsm=(X-p6D@rEkk;
dNFRn>.1")Au25m^AWnmO2Fm\s"iA4<I=>fC`inXAF+DqBi&!V?\I!JPJVo(N[8TXV3&R0o2
X$WtQuBL)('A!"PJI'Pl7mYaWG:l<f//OaeFWnGDQ=_q$!N1#s_-ct)Nq_V8knKq)PZoh#-!
\!:XJU-Et2h^-"F!W/J`ogn]A.9kT5N:Z`*4C8;-C#hoITWc_^-6_^!XG2^U;2fp`P(u&#%cn
ql$mMjbJ(Scje)kZ0Z@:R&4O(+)jqEM]AbMp[s)LBZVUN*1)"?(>\d111t$1dl/<:3_JVVQqg
$!g:f&p8.M@PF_hG\JQZ;8"MLSs/rW>3)!mQHSQ$nbM1sZn3::/,*CioRU&?XI!(M;GsFFU[
pL&_6peI]A=E;db73XhE5JQF1`9at-se.oHLG-?/FVX6k_M`\]AoY=Yd0EIuE>rqt)`4BlJuNM
t"R%&l]AD40EgYkZc9@SqeKtp'[#b[UdN(P7r>L64D9)cdtm!A!Ah#u#/aB2,KIb!\4Hkh9Wg
_`Zs1!$Q\]APbmG5V=*8pqn9fagsn##]AeOD@CC,E/bE`A+P[uQ98%V1hf"/YXGL/u[r(rG*p)
h2(k-/i%X;d'&3"RIr>X+!0_YpdiPg4$B>GAIJ5i-j2Mh^%]ARgJ">Ofc#TaW=mGSW#ih4N/r
(%tFhhtFgLeG?ULphLgeD>%FYbcX*4:qHrk1u_+SMjq'heKF85HpDko^cQq=%*OJhecGC<oe
LE9Bfkr]AI!.EG-4Rt2E-uMf/,AA&T/6C3C!4IJi_!On:&l2VV=3;06uTKCjA1^$'c8TT-\rQ
o8l4P+4;gHBETPKO7H00U9)?Xi?2ZgIX`q"ic<U16]A*4J\1<G3krm-qe>gKU@-%fHk.QHe%G
[6PT)8J*/5F6-5aS@*l&+U\b;mQ9q*Qb=B-g3E]AgdeJK\X&pCQ$em=1M7eBJ,A`P?uQRI'tB
X_5VS9[rm,3,H,qt=imZb1p9ruL(nND$pnoDi+8=N^o<9*9>Ts<af(E-Y&ooNa">.?+;=P-;
n/$Z5GcG306+.2sR<?\Cl-^uVQ,QSo"4Km8[NDAQKJ"^D?M&LL[q:Fd't7VL/eN]A\WC*nC5J
3cF0"\K62jtg^;*W#"0\78Pi_%Eh`>q5(*rRr[aIEKP,S<ibVoSN`2C$cel.[`LdWVf,&@)`
[+cUo;5#sV<&UA!1&.<]A;QI-YR[47+N<Kq&Un(73Q#3P@3HSZC%>An<iOR,:0QpS0s.<t22!
ac(c\:RJYnZ(TabG:!G5SUDrmfJl.+p$SH*RmfM-,lKX$gi*TDk_L%nC/Cpp:A[#6eN^Ac*O
#dM'Ji42=t&>Sch1>>3<2+5Xe7YFXOI'=LV:+R#kufaKZcb:QZ5$mJpH/G?pd*417buY5>Ts
7hrXl6l<=tU<;r:f8Lkp(ntu:^1)Z.<8Q_dg"%qlin#K5fP^N'BtLLY-9\\qK<!Mh1a5+92,
F1f<g(;ai2mZ9>'`$Z8W)<lIgH)]Ak<Yk-_Q0]A4l0h+VUQq-0GHatjD=XV"//T>gf$(sd2VZ#
cH8JS32]Ao;V-ENR-)#U\V#buF%n&a1F4m@!/`'h'"+E&]AG,&!f#,.CNknAHn1nC,(lqg6'A/
!<s%dsNtGo$ku>H/+r_o6DuDPLir"A]APlANh45arCN\6%l*cG`;g$HF>nq!OPEf,38IWbZl>
e'SiQabKXU^%JqB6KF0gV1AqplTR^>uS/eu+_OKa&jCCHBp3ftGigp).#.=tXT.C8b)`6YG?
\Eh!DfR*uWX:;+Ni@#+hJEH3O55dG$:WBDVfI7c0`E$]A2i"--2oC(kMq;Y=Yr03,E/Ic8s]AE
uu*B7ulm0350$3E0hIH"4P0Y"bOH\o!FTGg-n3hZFtlmW)q$9K;O`,iu]A_5c]AjWE6Ea@d`rL
n&#9H;'#dQpe64<<#$F67SH*J?o3r@NUPcl1]Adsu#OF4!'d$XOkU5PG;0!CCWoRYn]A/d:S`=
Ts3%1-@;0gA]AKd>E*gi5%.KtAZkY)Jd3!(4'X`DUVoVr+J)+/)n56;+Aa1C.N;RJWLg4`U"^
8nXB@^JE[]Ab4VYT+d,(LSM,-Cg`.K8,3>XN8XOm("6=e$6"Oc]AF:ho=QP]AuZf!)#%L6lidU9
@a9Mi*o0"sBEY04#-RFV;[:E.iebFTpQ%gQhh2/#9:e>\F#W15S]A(#a(N,)0LD]A0G@M]An=e2
HPZ^:8,(<i02W&c770Xtp9RQ;l08hS!`Oku)d$,k&u:2Sd,:Q,Tgg)+Qn]A?RK4Y"$0EgnNq0
&4&t!K_SQoB,JTuPd;ub4j(Zh4-JT%Hg"_\T:fWEL'*M%Taj[\]A?\$"IF^I9u,hgg/p;km[R
p?5VM'"'\O#\1QnM[`DT4-OW#Q'ZR>:%.TNl.cb14?!:<AcXNSYjVaTZnO:3caa+:ED.45aj
T&Bc.tU1D5g,?@p!+b'^9A/4QSgd=PS4">I*^IV/r2YLT)Ac#HV']Al'$W0*[YdDFUg5C>O/<
3'VG,&Aap#;a,/@1hh)uO9\<YP]A6sD'_SI(=;AadJqgi=lTJpY\)+T?Ki_nJ!.Fc**,bf_Mt
%b.ECQ9UU6[:t>DQi]A3HQ_ip*p6L4A(/Pj!TVqC=m(/1ucthlQj4G`IAiS2EZi#]As[8&P'C@
oEeEW&;-J]AAHQ(RXJBokDf%e]A`-gQAUZeb1&//5>uqK1G-9Oqa),J7,,mV\"YC3?^Gla!ZRc
)7@XI7Rur:-`l50N*e[$'WNgmb:VlmC,'PqJ)l=%1i:Hb%d5#UY=\"lYpQCYV,K;nm,,,U%E
EE=)lbD$B!)W\*tq&r7E/"Mslo3`+..X`l4`T;?LE^F;\-gJ,[<$:V'\[aH3!J3j2?R<J1*i
<gV!=n`_p;.lfY'_"`VKebK5lSdSVTQ"AmtaOD7s=SaLeaKmlXU8!J\qqBnY)RgntJ`Df`<=
+4-1ft_kG7XcHe;^>MQ#Vn?obO>Nf=PAK*[2nj$VrPRR-abuZGO/GS#c@g\$d=-<Uh;f;AY;
U9#+5keC1Rr3g`lql/e/>^b,I+HpmLXF@."!qcLbac-tA"f=@m!r1Ja<=6jI*E8T:WmBr7"h
>/RU9L-#?]A4_"tL?Q?U)I[>6p2LOY%FZYS9>]A-mV.t?(AuG+M`:a=)-XG(`>O.;T@pMt.H6O
Z.;b5e"C=ip^oD\6um2gUjS'[+HJl[bQKJ"!(\3[H)Tl/SdVOIo%(oMA7Gb@`S,,K"6_>o0T
=#(4`9&D"QPl:GN9MA=s3cnM)p4LLbaq$GpQ\IAd;PhM@0CUgACdi4I6-@'Hcfomh@_:C^pa
3GqT]A9G-U:)F,pU(t\AkKoC6sQRI4uK<-P\RRg6J6UmSeMDdqiTWl;RdBQBV5C^'>Rq:W8mP
LE.)2?GduY<HrDEfZ,_I/2^:6VhoX1W^0WDaC9cH3ZFabFH@g,\ak_scs$(AB#d2G.Q`6Z>b
!*CrjFC\;IOQ/+X=LbJ9cE&/=p(!p-V".VodS\p_Tk';1_?D0BcHHpT%eB9MA00[dTF3Xr]A,
o"MO.OdAE0nYFF_fTT8POb#=<$RUkf6(XudU0k`=d=Fp-FKVEM3d&-hg;-P2=Ch'`f*e%&&<
8O"e5gF+@Z:b^li>kni>Huop3<&d_ZKu1P!hkZRlE1MFjW\d[Z>jP@,6tRi'\`+(*DcCj*6G
6`.>.hKZ,ZnWm-8=oKX1S]AY-#.^LD=:RIURVsU/C$fR0gDC\!'h)$nfT!ZW%]A^NQZT0ocu&\
ol@tRup_GARq`f0"W8E5?Ff_H%(q/6/5*5_2,d8TB]A'B9mT&l4Wo`u'G]AE@09QEJfqT`p).q
&?I=f7F/T^MT8L7r%NW>@;/oUho-CbN]A<QiW[$"4VW[,aQR_BV$1AVcWodb[jk7Ao1`?K>"X
:U&`p:%fC$L_f.Z.[/oB\@LWXB=ZDmH#b"4%k-bd;&Y1InmneVQI"J*cV\k!B27>5JM4U]A8U
bNqEG\mL`O9MkFfH716#HNHAbArf*\SZseZ<o28efW)>8?KX?pA@J#%QF5]AVpG;gNcFU+f!`
?lU"-uncl;*jm;s*qRr/I5$?>iE#g*:qYL:c2!Aq6uOgP8RSP]AVhh^2]AZ7cmnLm^.,b/a:ed
IhQPF2C;4QHS=m7GA+/l^4jCtF0Q?%C3<71IP1;7$S$ccnfDjfk]A]A3VDBQAf<rSjZBD35\%P
#>119#Yqcg7bRtE$Fik)^lstnBldSDhTQASf8F.2Bp7uB!9uCpKs>bl=OX>qkTPUc0);5#Y\
2gr,aT,1+Lm(?dC/7KZR7i'T[C:8/$sjem3[(c7o#e[8"&ma9r[-!i=T`#;<8,c"4A-?uf0.
b(6.Li-f,l">TJKN(Zm>D:@P@d48FfiZ]A]A0gg/7]Ab(dWW(OV<#OK4?/FuD:p?N7sjr1Y8qL0
'C[:RZV[`Du%3\%UI77&>k2St"J.KrZ@&,<iLrGaBXW``k'!epVYja*D'4]ANK:B%ci0B5kom
5A,l76@gV-_b5;6'oa67,hRjnXAII2A=SH??OtQ,5J2*B2,ehL8/-nV!3QrHkZZ*L(L,L9n"
:R6enSBDik#b%R@Ancq.h*u0?uNOO==mXXQg)5.!HfddJ+[M.Em\Tu`l9V(UY"@gD22E@g3>
'(<SZo#'&sUX@5l4\f.9$SWZ;YP\J(5H#6U7Vn&q]A?We`h&IWb<c!,pCZn+`d<a>lGLTFLcM
YaE(.&,K;?h.l3p[[sS#LQ//fH78>_6*CDUo[J[F3]AqH;(GWfe6H[RV-'d)!7nL<h:idB"BG
t-$eT_$+MkrT"nUJD9LTBP=TW,IK@X5<EW-s60Dd^2t6>Du#^6b]AtNm3Mk"S.rQRYc`\R\=h
\j)\`$!lKUgN@&<CY8JfB0(pTo5G.Tfhi8[?/.E.4\AuG%Z(hW^Z!*NuSk'X+PW4teqUh2]Aj
sfDpLr21).kd?6<8)bNIhm$tm3Ea4EPGQi1h>uD1JB,M88A18XfL:X.kg8RLs"OY_:*8bJ^;
1Bk/DAk?SG/c+'bk<ePcn4E=jRe^c#q38?0&I!VQ5ccHHhgo1?2j[o^tC""=%":IhBlLmLp^
a\Hlpp!YjTY`,cCThmIeX"ct*3X'6/4?&`I7W=a6n(dQF(^4XfGMiS,p14VKQf%]A8P!(./)W
/R@4uM@3g6TA(U0NZB<"PjV1rS]A=<`KRs,L3LH^9(;E77^s]A\?t7+',nZF]Ab*iK^;G,\p6*-
SB6]AE4C1*FrnCcs%kPbdM,2Sk2NY3`kf1eq6k.:`B\>[`8:$!Itj1k=3kSiR4.n@B(&[I,TH
*S^LJ"UU-"alkg&i8W$bFRFhe,uMo]A"mjW`PV_6&<-3L,<I\;1IM^JY,lA(D#aM5fkb<(ge3
F\Y(X0mN\SqU!/uD:j]A%+Bq5.r$i\>WTn3i:SN;JsAbMZ#'&$G^MTQ+jLePI6fi@:#$Z7a17
^V"Kos%is52SB9Q6J5&TV1;K*i9l(`1=)k<5ETcj]AasOkPqZBN5?`h.0]A5pSiRIgkfq.jHX[
BJX58k=MY@H))4b="D`W\[$*of-hd7&J><O0?Th6r;^Z1<X]ASN?_HFK7jZ$JcOsiIkn_YdFo
)EI."%`R&i*Eu\-`opY=c%20QYVTmWT^C\HK=R7<ODFr!Z(3%O!o]A@f_bVq>r.J_@^KoOU8@
PDpVE0UdFai!14gui^Io+nb_6tlSPaU[CUjR>]A*NJ(kd9&m>pr,%]Au@*&&2QV)q_H$RkR@K)
`gaj(hgp2&2Dg?U!T-hGGIAX7clnM>b*L='R`F(bWU"GYm:euXS1n:0?uj]APfh&3qoJm)uc2
=b(LE'.9+oF?a%9IOZ>$^l5iC;:iq^@hpu"VJC&*NAk5#nOs[.D+2&PGjq5GUi3WU3!4"=0k
?mC[14#&JF\WN]AI^lONm$RM''d-h9QHYng9gPOb*H[1]AMSQLOqe(7J[FQX5TLrC@tXUa;]A3V
/.N:"-ea*nWNSD+9?`R;4,tBsg[2,#;e2X/k`nQu8j2O^:6fu<b^!u@SP@1Be$?Pq6+<ot?M
;WV1?.0U$E^VKH4;ncYJ2O"W<U0:7gVTr=:>D:DS(@aCk?aN,X+%7:!jFGNI*[5dIjQ1k?8`
g`7k_'aG'8%t)$u-`GYl!LI:;In"jX7>G]A!jX+AK4@orUCF%+D)LmgeVt9PaMua'U8A23\P,
jlB*I3qfA3ba*-2.LrtL;Wa0dljiGOf&VEO7VeP":_!L/A#.GjpMVdj-R\!;+lZN3hr3`TO$
,_`F]AbDa:iq#)oHq=^l2d+0<7U%R&D5DE':7;iVGUf'dFLCOTV&41qB.:[jjkgUf38E(.<pM
hG:.mcjk*P+h+3V:2BU2oN5>6nCZ@9E'EGAKNbj]AZ*,i"@>%6XZ\E!/&pNcAe^4CHPQ,g`%<
<U!eS&qm.LTR7WXNEBVYuU)B@O*'^Ghg`cU^de(>Ft9@2@2E;d]Af+^^>PNseOXf-S0'/TG:[
FUO&BO[TSs-;OA+Z'XM+2="l%kRWRV`p#gk=F^l=A@djj9-V4+(%AXS&\OehG`cLlJ>=ocFo
qi(ijA8'+o/7J6WS!U*NoNb1&ES>]AlM>!s&^67bTL(:r!#B/AQ8LNj"i@917JF.!A=UN;!C#
/$5he7rJMKT.]A^:F;uDb(T=cRW"f@0sSpnlAPX?C`]A(UUDJsMHB&aGKcaa?7jPtpbl>B=A@E
0LVs*]AK6J5V?=F5-TZ73>?qGns1'D/\*JdIR2\t73reO1dP&oFQ"CV;JI<SI68M775*<IP/G
"<Dt-YM2;fkL@Jb*I6$aTj12O1(]A`cNcu3.^kW%l</NdQb*>(E),W^TQ^ed=S2T?PadGT\.a
M#Grkm$VquhN#i^giT%f_ZEQNB++RQ[jalm5nKbJ;kD<0C<;97I$`UO1\T;_V1fQr2n#ZJoG
2XfM;Inu\@%p#18]ATEO+(6"X27HW5VfqqK[pOdAXros#=50!PCn=h&;R/?i*!sO"N.C)Ws^4
0rg;gX26%a02bc$L^W]AGIR4nHt-^3=Q="TXRF26fXc%X@9AIPbXJLY84Z'eW^utF1uhG5@iF
NJG$`mKpPSn,!%a9%LIpg>?5ajW[CM1a8[np09!91Kqjm/8m-R!Nu9>"FAaBQ[C*DBg]AiZAZ
"brE^fL)u>@j3lZ@bp;m^_bf7CJ!mBYpFae2h<7N2*HI9;K_CBkZ-183LFMKe6#U1@W,FeF&
rDO.tp,QjbU%Z>r=P8hFdk+'oq>/hf8+"Jo;6E1s@^,,!5,?sQLG="d:nZOIY;;pM8]AEKH/m
_lq'\e)0EoA'7Xq&LH:+rYngE)@[Ouon/2o5PQ4FE>>lg@KmQKrKE;H4u!'>Ms4R+hFBG=,V
7WZ0BZ9lV=SnV.O"[!*$%eg']AoUTjs1u]A!4;Tb-\OMdg)K($K)Sol=]AP/dO8bP?8rJ-k]ApXa
,J/h"A9\-Q%2?`D&PMJ`ErJfflcgs(C3+*V;1*$2H]AJS"V2#$+$$pQI)0=3@PH[PjJqOcO?P
<80BbK98$7W$&Gc[%G=iR.7`FtV+5O+.>[@KY4.;i5?JJ5cVNg>f\6fgM`a6?_sB'E=X2_03
._F"a.p.DKlLOCXEe^TSF;biQ`S,GC6'(faOBPB0^Z/.e8]A(T8M6,oo9;C$sl_@-;]A0ns]AJI
?)>7Wh*R,i7qO'NIgBW)k!66[5e7K(We_("8d]A%V9&61<l"]Aj2=;TaJP$`?E&)KfD6]A6bY]A!
M%67Ob%YMS"]AgO1QBLqa?!3LT&,Y\2J<$`Hk0nFC7jGFd[OYa#Lo,2]Ac7=C0]AWn)rd^eYNuO
'5BpP2TF??iD-n:>o>7oIS+$.oD!*=G8_nN4T&5b(?u*W)TO9n(*$^8SM^,5ihSRModCtt%+
s=R#\`<:mqj:VUs4*BQ40bPf$5fJT'>U.i@GPJj.A<l=ds&&M(V'V(>4i\\D,-<$o99``#Iu
pdJ4ksf*c,=WBTm?De^`49%_MdLTnqBELX*iR4=ftjX5@1U0gB;PruR\:g9pa5.l$(YKc/(=
0`#a)1sH?p1#DQZN>TT0<0mUWe=CF_mf]A%aR,kWbIFWO;`=H2bM$FU-e76H+YIbam/SDu^BZ
1Br0#!gU;;BY(AE?b$D>i[h3h[`e.CklV!I3R,d5k^UNIs3%DiMB\QdkZ)Q+&=mjZYYU2!7V
0:4b2_XMiDM]A3M)t8SS6V+8(!YbI3tgFF2GZY@"Ie_#9hmA^CKWX!Bc\C)a/f"=<_d/pA7U=
k#7c@'HVW*pB+`6#+?)1"9I5)qiZLX\r(3d-?XFG8UY94t`UT13C*NC6"_%osFNb#A9OicW&
O6U8'<rUBpqYiSVloATOWkD$5j<M=bD+riNR!2bjdOO<,f,TI1F8#l9?9Fn!HLIb[qa_0"t_
\%Me0dR#%ZA)j(#FQ!hQVMg&\h`BWp3k)LX*tpB)rMR!ZS^.GbEQhGqRB_#;Bk#+M]Ae/X$gG
7^L=rTiV1=Wt'/atdU[B@Tn:ZqjJi;E-YpS[!K/O.a.mUm[[s$i?D'Q6:rm@+aYBJN0L[7&c
ViF%R.$ORTc:M6f!)WC$i9K:,.<RPeI3EUuGM\Q!t0[tb*-HeK#H%"-a+Nauj9`)LWgRUi>/
k7.@O&d-9Y`^mMSn:iYc0fI;Hk'=JPuIpPDpl((iO2&Tp?B&EYr,+Q!iSW&X7YDD=eT6`R1.
NE#M"'`j5so5MI_j%^P0H&B!\7^6#NEcU@0juX-%!\Cc's>Yg3D8Ld_LR_?;>cm.UbNJ>Xlc
?fEi@Kd"!>R\-&I^k,K=5t\$3NeNGHn<LDLa'W.SZtgMXf%:e!;C;qe``pP2pafZ6V?6L<QM
*2W`55ti'YsTC`">"[br2hEnthU(14i:.Mkj@EXE&rI11DQeHnTW^bf2&#Figi4A^ElcZ.jP
lVql`R&L<GV2Uq8KaPfQE/4$bG"AW<<"ElNZ_VQNH4a7r_EsG@^D"4Q7d5<AbC'D$hXeo4+Z
(`t:qGGD^UB,h)acVcTmD*L\0O6clK&jmP&`B'!#.3c1:`u[(`\3"1#@;t\E1k2b@9riE]A;k
54902m)V,q9+!Ue&iFTk=l\j#[@qa;@jd!AYYra[hGV,JjWM+V'^7K>@h$31?e(4:QipFkS%
7$O(']A3#u&'5<g.HMRl3:_s=lGDn=k*O>'LVj9^K1-a*>8-sg>aZ#dnXE3>PWNg@7XdZG3/\
O+Dc44$L)\.,lr&GqF'f+n)N6h"`E#YE@Z-KB"Aa$&D2l>?uqBdqqJT&E*H<7EI3[#GlM4Db
di',QJT0MBHYSb1QGh4*jljbWe]Al/S4"L45[=%t_nqY0*Mf;Hk0;kM[(6:A-gef-aVNp]Al:r
PI'=JSPX_)%^%s=jVNQkjOM!!Z<.r4UsNpQQ&/_j^f9<HY1o@2^H`H-O)I0#`R$ff<n,lmOg
WU,4Od4M5?@7]A("M1I.97/+*l_4_D"3m<g*n,mU"Ve3+bV1P,'sNj>"=.b;)Y?')mCjFp"IC
+<c*B)`aug(bOBNLoPICmEEYo"NV=QQI+a:VR\")-QbG\mX03fa/g!.'O;W`D=jFIRpD^pf^
r[0L6RchGQ6NgOU4+jOPU>X31N5VR!#T-JqH1IjM[`5#A(&%/"b=aM0+I6"+r%G4GmB^1*)u
]AX=mil&_TEVp'[*ur7Lg,T]A,L"7BURpq`:D=8C5]A%27l<UbMpN5h`ocJc8cRl'FDT2j1^I2Y
MZ<E/GY<(]A!GGQ:;tWq%$o\9SDn(QZ\dG.%8E@$0ZlQU0??R`j.#oV1A3EC@e7ggjQLu^P]A@
W[=@6O\<H@%&UtBIf5.T!S3V@sC=h,b@f_Dq%dX*`qkpCnW[Z9bm\D>p8^6k#52/SIScs-Gf
<Vj>@*)lYLA0sj4Fd;Je6q!tGFX>NpK,GHFI@!u2jD6O;\l;Q^R.FinIG6KUm[l4-&B6<^Ef
,09&d=.NN!Etn+\kU&9Ipg;;,+`>?1#a!%[cEMIS0V$k*>*Yp:3VXoc=FrV\gTKTuY.HUReg
'%%jBjOUo0>.8t;4_#08VCs:sceAXB'ZV;75iHL1Ik/qkppNRW+0">7f=rCYeBB/2%Cj\hak
+*6G!s!Y$"l,4_1,+8E9'[,^b$9T6Sp4TW&_/nXnBdt1bR>gf**E(@F]Am/.YTQqL$I!I67XT
;uU&EUmN&gOn::ZZ[s)k4D=gi4iNXOG*89%RH+n1`m$V>Un3udNd!OMDYj<(ct^\VraP)AO>
6P>W$$g;&S_^]ASPVCJaOfBV;MF+8Rtat8Sf20>M7H&QT.>]Aq@,+-phf6M4QFj_T_PfJ]A<>Mh
tWaIPpG@q\</k0DW"m"<ig<J`qR?^V7$J6F"!A`]AAUOM/%0HAVqBNT@@6D[O]Abc?A<&ck':f
<eNt#"Uh"[[Pu!X[Q.,Hl_7*B#q_6m,Lh4ZhDTh#C8BlptNMu^^HYqDbA0;r3UVU.s>guu".
[GNG<KP)9?4([#-7T6U5@_.JH0F0PQ$=/2^,pH[HVkm;bN21i5-*qNDoh150T4)N#PuUZ/g?
A$bYl0f;<&`b+RZbB>F[3WG[lD49sOMq7st@Ug]A18ee9-(2D$bh"nUI^dOAaA'da=9>cCSl/
F(Sk=_)c5h*p-5:Us$9bThL0onPZNtZsPYO=B9A#5.V)]A!C;]A+--[t/AYN^Hige8?5)0b#A*
@k2fLP[<,8OE*0/0Mrph8(DO\.<]AC3oaKC%>c^-q0]AXG-08BSV1:pSsB:mSkXmA`(aUjNW1>
:BSIX*f1.R>MYA!4Y-P9)(8nb<3:+e2["$F30GgTYCgf9"A&;-k1VPHbS*C=g8:4)r9iBh!0
ZmC-UuFk_?o@OHS3U'.rp`Pma*$jK/.'pf+6AYeHMGJ'5&<n\&-etqPh:U4\U8qj%_EkFnJU
+.lUVf5,1'p2WqD>4"Phh]AJ23nh14E!dhV4sr0TUohl%Fu^H`r0CCBgpp>/utqLZ#t:*Y7k#
SJHs\dN:ChM4n+^0Jd^^.i/k,90B-pFPYu_kW0b;j,.WInI>7Z&rp5`Jie+iW]AJIX6l<cGRG
#[ia!6r_TBNVK%Dl44A`\4n\OQj9Ug4fl+lu-5.7qa"ikZ*kIQ&o/%+AWU)Yb[GJ3KWf@BQF
P_,Q.S%]AGdD:$Ytn-kb:[C'GSZS\;-D)RSG$D*9C6Uc2,),X!Sd-mpgk9dP7G,P,O7m#]AC0W
tL3`VMG[)aM&MG2X.\8<Pte8_)gD;nH(l?E!jE[J(q=N<T)X*g;soJQ%4`3XI<BhqEl+^g#/
]A.3q;?"Rji?Fkh.XF<dB*5,=oAsGk_@5>jh']A.%aZRTRBrf`jWZ++n#)9Su]A5`]A6p3B"50YZ
oWXMq-OOj`&CI[/i;s'nVd%3=LK"]Ae'SL7gbSsuWjkB<KkrU`uRk`69ok.5Ig\Z;lc?%OLSh
u>nloCF/hC)$&hUnU19-j9`0oJ2hm^/@:IN9qf('2N&eU,.rEG]A3^JMnMBn)_'i11R/UkcEb
(&+Vu0hc'qbmF-_8=-iiR+D#Ksphk[?L#d/hi3=-/WO`eG\7dMUnA&W)0m:jeM4+)<QO]AGMO
HY/).oqBrm:K&$^_Xu,B-sZa"d,8C.V;:ORY3fbE*iSK<\[EU&<n&QYpt%e>F,2WJRp8LX?r
#nhVZc7s,h3C:XA'_WWRAicWl&=Xc!e4=?-9u!'D8,[Kereou>"9=_<F"@e3l\J17EJA[>1(
N]A-Y!j.#[sQnk1T!uL[BqFL'qL5(2/_[X0?VQLu&'i%A(_XB'6RGSuc7rBV%pcnV#V`goRIC
K,OB;4V$<[0DupdLHtYog<BL<t4bp/igd'J7-,RucH^**t9<:bBo\mSk]Ae+H0-$E+UlMd1W1
!4S0LIU&jRCXPF#]AR<M)Q/ibuP68hO&KE6>>ipBJ'h6cNbc$,`r@ZYF$kR@EbZpUVV9(CuXG
emeb=mYS,rA=JC."XaY>iV#q1up\>/g&oLYYL4eY0_tc9@FbqAkJ!We\W,$TSCW)V56J1R]AU
bMYX[(jTK(Z5/s\e?D("F[2eEcu\S:t4;oWMj7^sDaN-N$R<t)!'):*l]A2m_`qe2@5C5^\dL
$aqO=X?_TpCB/q%<(rYHC:lejD1=Z^&2&u^+"(KM?o*g_[1#bg%lLGHPUKG5n*f`&[qG'=bD
"_1h%.CQ$4^ohn/ls=?gYG=36iCD)YgD`StL*5ONJ))AI,crf=Ue.Nt^NCOZYh`7U+g/ZX)O
`8%FpQ`jCKAc$;e*pGHt;n"YN',nA4>)2/6<J+e)L$V9Z%^b^M]Aj3DGF;AV0qE^b$)F`XE%-
gaHog#qX.Qmp`1LG47WjFpd`pm5^&%m&]A%Vu:DE'?ke6b2[VcoWscP,j4N,l4IN1I=Ubr=17
C7,Fd!$\=ajB8P/0[nQ1nCK'DX4hFEiaVYZ('3dR8j"OHoj4B%BM3I:DQB4n!F!P;Ws<jR5E
I,-_&Qu1F=@U+kQW1ru20`IFBhR8$X%hn-NPGkm#=j^duR^;XN!M`')[`NJCO2PUAb2.Fur\
qWX)nja_]AEl[gRgj(fF@Er5rotj=m0Z>XBJ/Q&Rku385;@J,fjE5W4C-*aa<plgJM)&Vem6t
b;6uA5#kmZBhk'c"e'ph*^56ut4$L!a7LVJdP[=-:^O3;C3`-iW[Lk(4"Ul)Ec$1FI,el%[b
/)!U2S,m,#pjQ*M]AZ.d#T!Ui#o[p[YbL3)B,Y&R-a!R1DBk`9>H1k3h[dn)-cP_Gj_De#L=(
h/aYOE)A^34;%W\?L\>[o(1NhA\@PYQ:6+!j:o%D[('UHPH#&ee_Kl\j)!:/=g]AHNMYC9\S-
Mkt6M[J.Y48Ub5Kp[Q?u!9)']AaW04+oTVc>kR$p33-9&e_W)AN]AkeYU1t<Y$\3c&;W,6A\TE
LYYCV@loW^o++nJ!tl$3QfiW#ZVl[bZ<)$maP0)p@[<88tQClg,Uh>5Dp54`ZIm]AFGs"U;Z.
N]A"PWUQ0`P$QFYJC)?.QA(tg,^(!UcuNDbqQdV\=j&WU)gF<;O38\\fc-X?!1qScL/r4m9?P
=jG!M1^E)k.]A;LS&6$+(Q;tTS?djl'h&Ao0BOkmcBAD3O#*SuXYj'>?`MSD)_^S$-$tW@QK_
.9ZA)NI@aNYDX)B1:\gE5:[E]A8M)!"lAO/Q:Wl:T%R/LHqn_nE(?Pe2Y0q@313_SQI*&E@bs
7cgP-7$.LF\u)&N9DY$#2!`h10?$l\r*ob\q?_tRWFq3#O/k/!oD-j81X+7`mJt,.m0<"#<"
rfDdA?mYVJ(^hDKtfu]AmL?=e(80jQ'W#ooHLXpgF\\k0@#tM=_mK.XI;Hj`$WJNI]Al`"9nt6
1-%[o.D4H1.(WSmL9=*bq$QS:;4VVGi=W9UL&2PFs1i,EiQE+Pak9^3-fbbcAXCN]A#\9,K9B
(U8'TQX-IB7@M7e\bag@F6cOWpd:8>=mA*+h<`-Q$('K;LH6d8I-S$?q42S%'Wq"[e'7<7?N
bEWfilp9]ABJq7^\[+M-ZSZDWAMX5?dUa9)>NJNh1;k!&X$'8<&\sl-H=F,O*BXl<,<bCb0KX
4QI%1]AcH8#aWdfKrcKmPSD?X.J^>L1)g(Y1_B"Q(0/>$K/9jTd5p")f-_+?M,)(Pt3^IKPK*
*U;]Aq5<*A*QEW0s3I2D<Unq>nT]APKT4BQMjLaPG6s*j'mdQX)4=CScO[QWm-^B#=`%()q4Sq
/hthgC5CLG*@r+GV&BH4jPC`#a@Eu^7c;*um;:g&H"F%hF8P[%O'6>WDX5==K5p0!,(Uon>O
/57">C&F.n3D8JRRF9ZSofV:[2UL;KfAn?A\P2(HJ)<`I6nR<Q8Ec+#o2/4m3?^IjgUcr1EF
*/.LE2I1R3-GaG!&pjs;We>o"FCes&4$B#1sQF[XZ)qg%L;-I'\3I?9g2*n.fs:gGcj/3+%S
:JR.JW?=d9JCU6&-T#H+GT%Yd8R7Ooj1L+*'=8=fLM8F-FpUcG_((7(?)p-3,SPoOAr0(R`U
+1uWPaM0<L7qsjF(X43;_NZIgDWl*aVqlGXJE*m'&5&ct@g!bI27R;)Y`66@oH^i]AGHo_:2W
K:&Fa_o'<dLQNl"L1)22Y1hr#lG)Y/K@XVe@:6qQ@+QgL^bl[jH-823=ou3rVIK,5l't/?Wm
tCuM#=a$GPQR*WqA]AO`W)n#2'>.4.i!qo4raNlJ*YQ:<\YIliXYf(54_@RO8F&W@DM\?uap/
kZI7!kT3%uK#-(Sn,pYiRN6,,$3e5ZLEiM?\n6A[9I&%27s5jfiglaneVB^7#&W55L%V2P5j
3<@YkXgB[!5%\F,rr#hQnZV7I!TGLHBfQs;(#G;Kj4B3Ib0(s#F>nu:Gi.nF0+T@bWQt?r8m
RGIZsAC!><7B0O5!EZ\)OD;O=T,Di]AKk^+tmoNKIoBpY%*48pYr[Z"^kU[(R,Gp`#\*p3Kb1
:GqK,77o'*:MRLLTeE(!RhaqpM[&5/(7_B\&+RN%94#PN[c)[IB>6R)B`\g\2Mdqln4PQ8p>
0`MJEaOXG"*=J@<[WCEPDfSQ;NX0lQ2LLp;\ih:$A5n[g3o,(@f9jEL-@_-D@UWbU=5bh:FK
5J9!\*]Akq[?P74YuCr)Un\2SP?R+[hg<(L"_^RFrrSV?.$*f#83Qp0G1JC7>OFV%FKXBNn^)
2-"`GMf[b$pu[-fj9*q8JBB"H<$f^2Zj"K5.,6a3b#uVLOEY;SOBH4\k,[BB.o?gKN:V5bD^
VgG@RF*]Ac*lK]A,GQR(piEk?c_Hq>^^pr`UkCXK<f(Xus&"dJAZf#q]A6E`edRU/gS[:!>FY2Y
5N"[\A<@%cQl<K=Wf3iZgfm$K6G1VLK2mh&*@=U/D>Q.)`pABEHJjiQYa$DUSl%*@8&o7:W9
brc9[(NiXSEtF97^$/PgLL6>$n3&mnt6l6#rAnml=,lukqN,b6-stNcRo"Zl"=>2H-Ge,4aC
"lBZm&YKIc^%YH9L]AbSqFfP`D?Ja6SUSGB5XsP66IrceQXDMEBc60GmCN-6bfFe%1,I&g#BC
Yln7-L0%DVITP@"I>t?6'kKY,PUjDa5!1t?D)!4>pmMBtJACR1%fP'Y4U%Zk-o#(+bB!cpdd
`d3`u:1Ho33D70c]A.q<TF0@\Z]A+j.AeEDdC0JNaItZ<[5Vb]AJD5i<h3*0.*P.Cec&mCrg?[/
i(j)KM,r!PdEerOngO&i7f8]Aq6>eH0r4%5?0$M\cVJ.P8KlBFbJb-4>#CV5Gn85]Adh/_dPmX
]AYFs<LJI@LiCMB%7E$.'nURNFl?ks*kt*o,(_/Ua9)d\(l%j(3jV_857&EVlbmW^`WJ1XBI,
3rTY$Zn(`!ttEF.&fr=7]A-0ZAO>'Lssp`7.?q]ALo?VHjYmh>6W^U'bjG#GYLb1=15Co=P@;n
T"Y+%W[0OuHcnL2&J^+]A_f3'WFD)csd.-,DcI&m%1\stUlma]A3q=pnNn4_0O`@pu?cX1f<C]A
S(AO3%uphaQg8#[fF^&lc;#Ud_8Sg*p(ME`;!N0cL^\ch&`:?,+RkU)B*irRQQ3,g9<<I&RM
G-\4/jL=p$;$Dc_eeas9^MF\B29cj\$CSSgrcI?<e&pjs=FSfX@6RWlg=P?L?/$6PhaLi]AaY
#6-Xa%s,;fm*g<mf_8caGCgR'sUDhiLXb[okl#)3/j-h__82Hl=D.MU/Q:YI%:38m0!@gfoh
bR*2mp!ihTEJNtTDFS_g9\ot0f@lDJ0_qssl9Q4rs)&gF6gZ36b@\s<B%j]Au(P^q?'S^hGh4
/EG<,HM\-&JrGn'ZCOnFn7aY\j`(ti;u6o[8NV+13lio=[S[4EWJ%@Ddb//Oq=F!b3BMs_Z0
0ZC5.<1nF6($m(7i8CbM("=Z0hM!Ss':"[$&8GWVrT;J:oujkkd.a.!l97ncPADBA9;=#)M>
9'5,I4F"l)`lI>:iPYE)U:Xr#PB,IVuH_rT)="0@"1LA&`acgh2nR3#lDI:Hh!9p\TUr`4t/
>\6?;Rqrn.=ah,0AW5-Rftd!j5RO(JK1KAihY0fW=3ECZPn'^oMW'L]AgK,&OaO.V@Ed;'R=1
L'C>U)SC,'Onn"s"p_`BfZmY8>,n50HfK&^t.4;2\n#1O?k(RS3'K_$^7fXtri-Oc;?YeZJp
q*?GBf"THL@`"-2Rd,II!UI>@;CiRa#ZG^8Yg:l$c*LV=8hL\dM*W'[/BRnHaWTEUihXPM<m
<XT959b=>QCS/NQJ4I`n_Z?7qV:Vi/+o?WRVA'B%A?Kj<32tV:p[,\7FK>B$i9Mp+&ZOi9T:
aobbg:=6e$>RQgc%*h01j^T>7I"=8p$l^`Rf,gQbWl#VF4i)9:C]Al5M\_#*L8d"m($*n++cp
oq`nYnZA-$EPIgku+^bV[bqMr@QbS--cs7Qf=O88'a_O:=;dW(ME4EbgU7k`3h@Cl5FC8o@e
;5D>;^$=KpB.#q2?VVT$0kA`beST@<);U/+LAP86aH\l4bpMK*4@C/uX'X,VOekqHupL-Iht
f!XqUl46\<rYJ@mpVD>oX6n/8TeV/fUh>YWRrCi-Tk&a/O"'PsL^>OuGg^M.HGXW*$_%t;bl
r8C6g<*OWbR9IWY#Jp2__He:es#kat".%+kuL+1:9/VXMJ\_VrY0r]A$#Yt8U0!K?02<?a1T>
)BE*W1mOS%g_F&2CRqP1c"WX@tpoP&"T71ErjN%pu,gtI/X0e%=lh"d`rrhpW?b]AE;RA]A%sK
0Q\CGg@a9NG@0g>,%.ns6%oLgq3n0gB8]AA;9]AQ:d_"r2K8[s%E)q=]A+O[#leZWbsl=dM='RY
2\>rplhj?6YN_dCq/;lU`sR)Q_GT1GdDJ#9rKHXW;4ioK6D.FUe0M&u@MUt+BZs*5ONdk:EC
.K:h1Cc+YIFQ/A[]AN&UAWtF["55.og^1)QO+M$L%S=JQ'U25Ql2iU='(8MHULU(s0',$im<-
LOcPfqWVoT#3h7ro]A[X=,Yp%JVDubTBsGKC>oDnHEa51C31iO)mGa*m%6``QT#DZgm]AW=4Bm
4ppY808R#Thd=!M69Vu\l-YMq8P-)IcGAi=BpuPEa#Hp;go(AfWfDnNc4^AX[\33%,kX3+oo
boX$?d<N`;81"E_Jdg(k$2K\.B0O>.b3<-0BGKqmYXs+##+R:aNj[amn.H9Q1ZmrZe^L%E/d
-1D!)2cl."eG,sFM[N-\LA<87r6gNUL7WUH[u3I37Oor_nDaU%%2AVfEup)#FhfobZo);U'3
PRUmPWf/qBE#HCfOl"m/b^p+erZr!te8at"#pm[u!t>+im-a=$PQ;B!;4=a2Ar=#mjCS('B)
8Wr'\9=NGVjY6S\7)[dCaIW67cPO&D+<E.,'+'(2GX';?sG1YRG\[N%ln=/Mf7\WmW+X-ku8
<gF#W(PT`abUD+$h2kaXR%4BS/Issf$FJ!eY4XG=VElO89?0Po[Yr-X4RZ$H;E0$\rNL+#R`
s23b1rr6[8Z``S`OJL!gUdVp@Ra^55S*VogBO[,TRp=CjtGtJGNj<Uf)Z>1I0,j?+!C&"C7d
7b<Ni&?!S$Km4Itah7Aqqa/HoPiF>L'QWGm9s@m:I%Sp7]A9"d<;H:hOCZUl>$:g%[Bn(+!sI
0l,[%m@cZ;8<(.6K8H<lMg^D'0lZQVgt_P'fu_\I)UW&RT8@Ig;g/H.5O^*,-tO2^S6mON)h
%5+<lbMb6tOu5N#+s&_0rep+k<-hAuIKS^f#?h/G'(35;C(T7)YC`>0U/_*f]AK?oo%-k;.EB
VjH;Rm7Q\\%fL;&=L4L7`Xr'*NBKG*;5l4QV#Sf^#,j#Ada_,-eUj$fZ0*G7oZ0!=R@.qREP
:?NFosg*FQR,9S*BrOhci',[A]A>#A^llRIH<Zk9;8A^/E&NYpe:RtACEkg&5TKI!%s3/5T"!
uNOLi"?AYO#`K>?!ukbZJNX9p.o"tq`I@!#=UJI1Agj7.Bu5*loec)0(B*eZHh'>$H7b`G?M
R%dJ*9,S*gOA6:B$ce\$Qp;JK]AcD%+kKNd5"p>.c8FG[Ri$Z&CjU+emB+1d/1/$R->,@F]ABQ
3*-W,]ADMqh24:,l&%iG)EYP-cCPl:.NXtRMM+I=>-bNm(.FDg[a!6-gR%`%uuY.A_5MjR.<V
RQONF2\8ITuD=/!\\aKYb!2@V+;nWjI^W@T^K^%2,E0TILDRpkbBf>dZB(IWJIZC`0O]Aq]ARF
:s]A;\1I;s<>9)4[G6@kWp&hN9'#(?b*K0EH,3R`&^?MXC=QqK\iJN8V<LOA.4eEA!oLBAWQ?
bQC%-W+qp)FCZ?C*>=@]AS=K1M-)e.JKD^]Am2qF<Nn=V,mg6Ua33TV1`Go;*c=(JH/U..j>6#
+sDM61/^LQMY%NJ^:eNVnNF)>\\90CG?sBb56U\np+#rBlacJlSnCP4MAW1WW;-%CW<uo8H=
K+pM`Q&p,a$fo.rhZ-+<mXi0k=>^QqiK2p5i'5UbOj`(@D@DdTUL9:XqDoQOs_'DHNDMSpEI
YfLYu%P%s[\_5:*rSHD%?]AB"f[i3(OsoZh-eH5B'&f0HrHLO\jMWeC<^!erj?>$JK5$9Smu-
P-c7VRK5D*Bk1-=eS@WChPPH-il[dS@-/A^*i+l%c1s8+#NYqkCE>G"8[#n``UIC]AY%2)4D/
t\MPu&;`4?B[r2Rk-cBYf>@d7$]A_=J+jUk4`Mee6T?N2S)\R?J\6MhC*UU&,eEjF3l*f0Q\4
\66<.9eNWD6>[X<`*Ahm4)O5W(YaIteRD/,>]A(SM$+(tF61SAL+Pf'0RgSnO*3q6[m[Z#>hn
.kfe_\O]ArgguiC+)Y]AAd-I[M[2Z2iK!uLeQs3`ON"PUW<8iH'6_a3['d<D9A`bG4*:@AlqX-
D#=rZjgL`C;\J"g7gPa"8EZ=,$I_%1E+WQKZ8aS/\o+SQY!73%8@kZRTX`.@N*k5AsqRCfq\
0OF6@"o(fbGHM/$U5/n\Ur:hkX.fs?^c6ZmL9&XlM4&GnDLp(ahNJZZM6XKE5RhLF\cI=CXZ
kT+X6pAVe8I#'[-*4(Z$2_Qu#a#!^p,8V%k-hg)R,.lVg$k5Pld2g@#;?qbULBpT>H-_6;tu
p7+C9e'oT8jZ$9`Mok/26f_E7(@W<R$;3@L1#?YnJ1,hQ;pYe-RF;Qql<n"K++NIVl/I*ppU
msbU$ju?PN1cU):1-VEIAMhSY"/VPao&MZ,K+h3;V,K9M%G&o6hP,qsHm(q:Y"L36F=";P"<
3:]A8''BC?;r=0kkF[>K`FHZnu:hJq=Y$FXWM`;ZJD5KKF]A3%G%D%"]Au:b"7TI'5enD<Bn;"1
QU$TqJ2NVJa-&!r?XF)ImgAXh*(NtTl91sOr(U9#JM?n(:6u[0qSsV%dDGR*%c+uG2*p<mqM
tZ;%[N@HB@A91gMpV*"eb"l-$b\j#^>^"L!<tEFj@,;k%66[+-:##J)#HSo28N_4b8Jf1o4l
iJYABgPuA^U(^#RKWrjI2(DX?a%8C`]A06D+W3HYj\.ptq6U4::PHQKsh!D!GUjMG8fr[pJ9L
Sh4q/@oa&J3Z^7YGSCVfE\>(51FHW-aOkqj[tAi"=$>eCQablE/QMq"in5r$uS95#lSjGu3D
$,t/'r2P-9GMh#*F^g<NS.5_V6@LeHeD9CL"BJc<+=PU[+3:-d8(B#`HW/8=bX[E;j`F1hYT
(*P3qo_5(Pn^I]AKO]A9"1Go@oT8nT=c34O@-0@UkBTc=>5fLoH+"[Qj#!Eu0*j3W+bU<fjqXU
.=MqhO/^e@RH*nJIH.#pJAH]AGj/ll-n8DXtf1,m[R.hgF@SF]A)Z^6_![41Ha2@&1"pS7Te[r
(rs9Q$QPV\g!QsI,=aGi9.mJEh%TW.ZT)I`0>M%G\VX"Fs(Vs$F)O-`h#0uYC)ss^DD+4=7K
Qr0OugVIVUq)k=%R4OU%qg)/fqX148eVN(OM1&\[buA1osr>O,o@XV&`E^>W'BZN.#F.AL8L
:qG7AOrNhsD_)ijR3XZ1`(+R-69MC0qc9GdK`uu6pZ15t9C!@hkJHrLL9E"&kbr>\!ikq)`X
+8A&,0.:<hWDs&O>qX#9?k`gn6UT$-)aX)iQYPO<+]A)XRND)H#\jsDjMSo/]A<OEhjU^`3Sg@
#LQ9bM0S;HpqO5HJGd\GWQK2'P&mJ--!QsucuZ,rJ8m/T;oMqLZWIG`KFpPs%/q_:)Z:">#"
8YGZfa9_)";U.R.bYV^CZPIFd**;saE1)"n0GD;aKlsY2lh:DV/d7)Yqm_qrs87_)mS3/2/\
SN:V;V<cJBe58&Z'oB?WRG+NCE9:?.i[&ZWUU+*NHfncbV]A:;^f%c<CO&bMl@*<pk11'6h#I
I[i!d,7R]A%_mbO(kPS@gSp0([)=1I1$$3Q.n*JDY@jD$6Vbo*G<CSq3dWAV.#^3]A5q#I]A?%"
\a5_@AWm&Tr1@"8iIVkF[%a@GBDXs'@f\G9hAf*VZ*7di&4UjeVPS3Kj/.NPl?jA@FMBt?)a
CN^=>Kb`#f]AkZn:>[O-!s"L$6q(43bq%9\d"MrTVe/Rq-&@iU,YZkCB_.94Oq`@c*S#2s$^(
=L&unTTal6+/$4cjg-UYY\%!5J=A+N\c[Nl'D7GL*p+6T$:9cLL*-2op\6hi\ZnVD,euG.4t
t/"G?ld4N>Mh@!Y/;iJlF/7LCHNd48N"#ao5o]AYpL=[F=0?75:?$ZGOGc4XOZLZ(.Iq`4p,V
+$]AH#ag7(ri^,ObGW^nLG31PP?r6#`B`&(ZOX>c?';5*h#SD4KiADF]Ag%uk,X4Ohtq7nT^F4
[hNs?`)`d32OUsjm;jB6P/_"r_#@./K3$1->r06:_fP0ZC:Js0AlJpY$4J4`]A*9;]A"C7Ch\#
V/d'I@L@&@)cA`6j$c6%r'k>*/s-VsWtGVki&d+hd^r!GaWGrSfE63ir1K<qqhQokcE1<>(e
kmFM$;+?N&2=N.@!6cKo'^AT_2a.m0&QJ?2IJ4(;2-rE-lT75\FH&RL662C\.>.0/_)%#fn!
3g?mA6#1PC[.)6AtHn`_U@pM%G$7j44UXk&'UJWSLCs$Y)&sB4$^PBmPUfAHBrADP=Q-*/",
Y%9.;4=o"1DH"RK\5'i,p"9uI<nh8L\<Z6"#LCZ$GkRJeUH!uN35l]AqL$$]A"ZnGbu9S+iAr<
<;r%jc:2]A=D)DUa>>cR[#Q>]A0Ti[,;6ST9>anG&M#Mo_7PIs9r=PD"&,:m;D+[M]A3Y&>5V`V
0S5W*lJh5qJeYcUun?Bt%-h@q)`n^6Nc>OpXJV1>c+nRcHJN%ER&`Ko[>SP9jg8XpMm9kT'/
NJM\8pE13pi,9=+$%mPTBpsL@='Qhps+S'p3':"+CV3:B9;;3%0WQU[8@1^BFk]Ap$S#_`FV&
<*aI2nkLR!Xg2eI3bYAHOZ!,p?9=Hmlh;V\h?aSZp1&ZPPXh]A(gq58H!]AdJRj&r#D4*mdSXX
kBNIR0IH$FqJniKL(6;?U\k5Eqh[cQQQ$Y>&hm1"rWgEKLmr#$cU@#0$3r@m:!Uo5*NHHrM<
Bt9Vc5O\d3R/W&:d\&"m&3bf3hNOl`Dgb3N.JS(S%$^-q(r\2=Jf1VP.o[R9H>Z29a\"D!D?
a85O_4-PnBR!'DLlhG>t$2Jn)2jg`K]Ae8HAk*HacG-<J9RDk`SMMeCo(-Z.-:]AhY6`dpKA&r
Y^<Al4o3W*?r2<qV4sRQD55jY9;c)sjQ)8mfG2YST;YX<Me,/5'dki\+88t)BZ1_?%aT()nY
jdb2&lk&l&O^EC;P(h3,i);lVZ+WPZX37LsLpp_)hb/-7'K./>_o"!Sj&.>]Af3&,i-_[G(D#
@'ZrmIFQh-($587-/@d=K;7,YiX"-B\L(53f0NW7DVl)<aiTB#Jg_`bW!WI5(&6&8b[;BT+X
(;*rX6V\rpV_#'mUQ2jcd=er6;"4+4$,b^1[!sVF8e&:[0FC$;=Z*J2%EL"YbL@tK9(tO>BU
3`^plPlAlOOjk'X7\oobR(N7UEClD_*fkLfIjYka%M_[/M$PN=79bQtTKVH+b2SMDiZXC(eN
$n[2VaK+0IZ2!7Pd]AqXU(AGK4Wust'5/H(*\>bXOPVgN,Zu^q)jB*>@9nIcNSKDVn2_:m1+G
2%Pj.gc"6mig@nr&1<%E4i`a$K1*K7ujQM&NOc@:egBl$CDFAl@t=\NJNBZ=o/OTMjXj/2+s
(9$h!q<AUGfjGEjsh+&<9cfN#?7a''P:8=ZD'3IsL#IP@tqaet]AKs5eWD0#%0oIWY,q\h?i+
pOa"F:4R[2U=0Xo0\-a0__Y[FtRrMYIsh1m&:<mAQNff\W>)BaAQsnXnI4(Yja@[1(o"G#/B
3`H,4EY3O3Skf."qf9ohD*B\,afSBCOLh85N[/Z680[j!<CaWcF=)chE'WYmI!7!-)kC'Icj
n;n_SbmACt3/tFpmZ:oC,;p.QUu]A\47D>'o6Jl)>*Ie%<C]A1/Xr<)<K\@\h5;:\FAJI!2B[u
s.i;krDnqp)l^rQ$m:1jF8p]A*.T%>oFBp)I#nFbm+p-QY:;!_pNf0H9C0m<KFjG>IQ;,^l)B
U@d1B0"t\lp+%XVW,U-nT8o;#\0Wiba)e($X\-!h&nh@3,^c/83TOcf&Fd<Ht>*/naW%ZC+1
U7B\LL3@&NO,PfpF]A<'Q=PMe;?>I`GSNqSs"N'3'"j[ud\&FC%hLN(EV-,=XZ>7GZX.f_3em
4]AEnH4f+*YY_Rp^24?[#W$Z.noa9$3.O;*b2n'GS-PpQ6[uQLaqRKnPRVcCDA]A"-9S4Q@rjk
(isT(>"&ol0-hD-ae#=7[BtX&'h5$NY@N:H\!SBlqkLMu_=!,,Y</4ucuL`)#H.e?HH6ArP$
08$`6^+b&+%\DZu>#DdSb[PFo6P9[GK2@Cja9QE[<VOJXpjn&ODY>(52))r:aiC!)<#e`\1C
hBo,*UZ%*5Q;fTW99REhL:db)BT@JF./7M+kW$Ib'FH8!!b/>]AqA-f*>Q5pD(<Nkt=9sOEgM
N&q)#k2+4nnf"qgP-L0n#j1jA:X[O&_gX,`qlY,<2pH('HD[uDVoY7'p#V)SuJU%qRAlW(TK
ga'ILtln++TsWckB)`XUA$/mu&koV:]Aq3:h88lXS.=.?3&m@:m0t!i)C`c0/LBVh6el%p)hn
Tqu?SX<0]A\Z_)Eu(Jn/W,cBKX_\s6?hmNt,EFpLii#U_b`GoiNpfR^Qj-Z+&%:AW3M^Xq_;/
rOre&^*(230%U"4Uf/!st55NdoB-]Ac7fE^TRE&$Vj>,kf</=*`HFDL]A5H^ZJ&JXVH9guKkG,
_[9<<C`4;F?bOR)<B"R;s&?!fa^adXe).2&8I&!]A/PtVp<>M(TP_>AJk\/Z>Q\JsTc(RC/&K
_s2p8XJCaj#U'Gc))#B8mg/61e7DTR>FAu8n=YK?pf^S-fB1bZ\djhTnSPXWM&TKG3nG8#n7
WsXNA]A6Q1g*\Ki[Y%#LJ&.]A&K9p9Cr<qd-V'>*%V_1C%pgSaH."c(Js?$%7)[.W.eDZGat7$
I!e&u$X5jVHH+4*-Y3)&7-,SX\B&?@(c04+Cbji"7H+'7fBB_PbRQf2/+<N`fWH,d0NKN2!u
&0Jk9:UZah,XnRq92Q=LR&CEa%:pQ[=d'rLVo5P?1R_2FK-1<"kFjB2l_775QAmFS7t0Rq)Z
Jq)qmU=/i`,#iC=JR6EM(/PsIRg3+lfhei%933]AleT`o)95l.!SGe!S`gZGYE[1W`_-(,G:U
ViqQ!(lZ"UcefCrC__&6@cQ'=WUKl8)9)8:icg$q&8Y)hqO5r#uVE'AgX@"i7gZV(4IfePW6
4p$FBd1\lc*9W%/^$Yos$$mS9MZ2K\&gIOTH%2rhtWJ8?:%q"0fs^_V]AQ!Iq)!-*QAY0K"jQ
fO\-+6gi<^>[ieM-&ZkKPY[U%6FWT>s2FHY@`e4;[)S\qa/oJh!j.f2MN._+CQkdu^nS1@$2
Xg5@q1]A+80O:3^,P<(,<Zd&pEt@"T-*bTWu/$pWHaR:Q6Zk756$s$4A@/qh:e1ajGB83ND3Z
OU.)]AeR5;UoOEB"-ahEen.&K$Zqs`"WSI:sn['UM)>8jrlaN0le":SV'eO.DKamuQ?pt1.B-
C:Zfa=89(#pJnLk%d"Wr3Ldd6q<FdF`[7Y+N]AdJqt$(A)RVW^pmc.bq\(h/Ss-WXZ1@X'8B#
u6Hn$J&m6hs!,c#W>bdU8$;RO6+8=&rmP^*?F1!>KDk7NF;U<L%(Q]AIBl[[CmP%o&Qtg=RKm
SEGpYphZ9=Dd%:,I^Q$^Hu?%K%UAY&5O/)?@'i+142'Z-FSO+?T=t2K^\6BH=ebq'V<&aS$`
[5gd_i*ak!`f2rJFYIDGf5J!1//2#bjTR?`1k!k?@:k4a9s*q<uQ8Mh2c1K%@&kl$mCAbZP7
j7R$jY8WM87Itga9W&I@YrTCU[CO?>eq:RKVqaL7F2g/P%=n@&A[!3(]AH"CdE:TF=L-%XG?B
pC1r,sT"AAdXtL.Qd,t^#G75$_a7G/,$(B*Bb4*9)?nXJe-m$GWsf;(/`:"\eUuRUuK2L\md
=eTBB.,R;"-3X!bY3&+u_J?RI^t&oJV<5=H!TM:7c)%2W(2Z5D(]A@o3eH\82*=kh4b3@?Zo$
(;jVDW1+8OJnaD%CqXR,Z\58[l97Ta@K2B-37Vj(A$D$ik2=T_nToP7pqi4O73X*pT"1E<9G
jCUBM6F\N3,&dpo*HUR+4T?q\r"<.sYTNDE`b@^3CT"M%"VhlT8;NT5)eRP(Dk%g/;`f*Sm8
_cc0"3:gMG"UYf!3^Mor'.@Q'L*Rl+$et"OH,W8u%43RClS*Mp'CLWM5q9rkTI0K[/*rIj$f
jD:Sdq2m$T[@H:=mAlOV`$n7(W'HjZ\gi2a*$D*D@L[,f)La'fX^+t%K.465@ppIl`H7'@&^
gaSrK\<Cp%EjUfM2s?!/>C4%Wggn/d(;N0=/+jM7R9l==#Jr':P;RUJr,,"<99(%:jGNcB(#
5dhBlWioGCbZTeWej&_Y1_i23Bto&>&kt&0_rLZo".)%FE6+/Y5:?riSQ*dUHn,0AGnVr[ra
7CKG+Nk0pUpr]Ah5Jl&)NBNubS0Bf.#0,2p8.Ue&Sm"74@ibH.e.8<FSA%]A$=apD1=k<0KGC%
(XI)eYE%:49<d.23kmEuQ0IQUJc5qI:Ngk`]A'ng1m3;73D[TrY$_Y<3*"@4*UA,KYm<nbng7
]A^7u:c3+!!je#'1+F\G)lR![$1$$Pdhq)d\`CT^a<u!`.0le)JSC<5-[dOF9Jn7,EGTTnR59
E5"j,`:l(f+KV`*b@=j:E6E_%m.hTU6iloSE)EFOdKEohhd:F<C6'VoC,%W,-j\8AL,-]A7[r
-=n=OAn#Bt7`Aq!]A?/qTIVQ/Zd?gi(?ULpkaPQ+OOCD(&?mA!WoUm5KcX,5^qrCNK8?Tg;>b
_6VeEkS,4Q[;pDN,jT?i'GZqPuEnPVnphIrB>61*]ApjY,j.#B*'/",OU_bK?%^H)ZZ)#BU43
?5-L4G!+T-\C%Iapi>;j?<EMAIL>;]A7$tI[R#uAB,ju;&&;HciU[Y+@/1:o6.h.5/F`'7_8
Fo[r<N#P+DQfQ>3sglGRZ"5*5Q;gm9bXG+3>'7jI]ALgshO6XamYp>L,r)k19^XcPG%m"jBX.
l8lB7NqX6%f#YeQJNIPS<!&rk%gN&C0)j0+Au^QCD<6K\[rWLSuC^$[V1/hIK3=Sr'9k?PQ)
UE)6eTrhD8-/>:%`I>G/M7?Q=q;t4Tr^-hX9ZMpdj1?Z(ZmeB:e'XmIUJeo;0r(KZ@mZK,AM
'"g1<n+&ZhLOgBn<jOA8_h;Ffl_@kR5B-cF+dq)7p<.e\X$MO2<3l_r4B!`lsmoj/)._.[S5
o-/hdkNTX/TP6Ds-?aWp:]A!lLg5=]Aj8?Wq4o=u$5GfdAsc(R/F&s.8XG@H1`]A7a8QsXR0B.W
p5eUE(f<:o?i5?ne$b@0*NZl`d_YU_mC,?ABQ&L':Uu'Bt-uZLF8eB`1^U=A[!-.GuVdq_Lj
J?Tnn!elt@tW6pVEO?7Ar[DW"8fEH0\'S9\X!/kLB6]A%<uW@nASjnh-osolQb.V1J;'Ar-l$
3mP9PC=4l49=$XjTPec,Aa_=#$/2^oDg>#AGfp>QBqNJcXEej4A@_'KMWFOA(gTQ5bTAOehg
TC;ERSt?50VK(6Hn$_Hk$Idb9+AKJ"qKa92QL&T"A2k)9l^Da7p'FN%(VO;$eO+&ujQ'Q-k:
F,E_gu?=nBm59%B!'=I>sb*i4k/Ia?#@qn=J]A[:bN:2%WnR.Pf$2MIE:#W?;B*7R'j#B\q6C
+iaU+Ee;blnT.a3(nalC\ksG/%f!Hl&3e(.rFjT3hP/qT;Y=S0C[LA)U!lTEU)WI1fXT/H!)
@<g-(6<1=NbJVrh^f7!c>)$tT"9brN2$VqP#M/RkH9^8cg4bM(h^m98=#(.gdEBm/^eNM@Z#
aG7_U+:K;86r"J?j2*=1lP@YsGIWnJ%d3LtoS/`T7QWFl?d?2a+t`Mn"c9kFiZ]A?]A9U#`Sb(
!;X)@8dpb;TJlbJLR1`?V:4S.foT44nqms*udZ!1QS_m.CZ7B?h1QCstn]AH!Q.;$FPas4f0l
%C-F0d5ouc7M$`GCacHOa"KARmb)Umb=*HVe]AW)"Hb<!UMe<mF@(Q-[f4"1f*'6&A>.5p@<f
An7C7AlWlN'u9P]AU@4p<[!\EGR!'<G=Z8-Rg#_aWPa#)0p43,IT7m[JYi22SXEF<_t:ke'hW
kGfrnB)%(D&'W5"O+.I0bHpQA,$K5c.c:aU)rQjbNH>Bsk7HlJkJp'-s&G`!ahI*F$$8rJ@u
_]AI)]AG^8VK>hak]AM<_5*\a@K1ogCU:--<UEC6oE=*9;\4:$;l"@FudjrWXbEL1MTAaqJ^G9[
MkUP<FQ7=nA'<6NUPC%B?j@6dj!Vdu;X?o)Y*]A[M*OK)jsoXSjgkX,V/*:nn@Zm'"*]A>DQ:g
o]A`"Pm0$GKL/.Q_dLQkY(oTb!7'9.@Mm1Q&`A5pOGKt"H#6*5auVS2fLo[Kri#ZY%7UY^c3d
RGk\qS:N%Qo>8r6(b2-,D7/^gIuEc3*N+6'Uo2-Yq9fRb@Pc_&\YD;(M^4&c4Z+p5h<PCbjD
,.OR>T:Nb9/,p,sC4:k!4m1Z=)>9n"jtkb7-6#KF!+f5dTm*[TQm(X@0IL5=?bT2W[75`/%=
fC%:r)Olm/nUh`p[V97TkFOH44$BpSq1D[25OjAFQXn;$j^KH65%4HakJ]A"u,9&aY.g0lGoR
/U!_5Im9/rd-#SSa?bV6r+VFCd(X5d)c(9-C5u'SKu/A?E8C$9@-.0*>gmAi6oORk9mb5g!J
%]A5md"I7-h+Zp%Q;&.%Gfe6C>V!+r-reRG1+rZ7VkJh7fsBNF<hAG+ch,]AJqf2lehTR]AHBHC
$LpW7+\4hA_<8+?ZK4/bf,$)i(sNRQ)phQ?o^!B=kQ3A`BD.C)^d#)4/gQ1Q8S\VDK68^^>;
N@,3n=WmGTrZj0#<TEUsK'B3.PQ`!k`g$s;EWeTp1f4m]AIPK"ZV<L!I,EeO'&l.?2<"<J?,:
gZ#0V(BO4)PFB_d,'j?.<uT5H'`9bQlZ\bEX;4T>?a\k)3kO?]AqRLk9P1AKj]A^Tl9.8XXU6K
P;i-p%+\]A$W)_,CiA6_Mcb.CO*C*Ts:VD".4`BVC"YtB";/US^3U;g@o7^-DQjV<^.bSH?/0
7j$*5*R^k"7<D&E\h3Ecq8B,4P^h)e\.c\7%aNmi;g;*aSApf_AruW2aEX^(i]A(71ZrL!ElK
uX]AL4#j-eUiX:5\0!0qBqo&%%fA8SOjp[qFMdiOa1Vh>*#7;!ep5<r&Q/pk]AZC4R`R-^bMH3
,tN7)DT[!nGN9s*%l5'YIJ[sa5#7"r#A;H$b9]AF6910X2\#Zo*cdqN<\92GCi_LhDX4g;@ZK
cK`pKiE2F2638X/T=E_s+PYNXEN_RWfH98sN+`rt/>.osAMQCm&.Z6udY-Yfb0H.8Y[\Dn"A
4PkWup2>>LMT4r"cMeZMi4)lQ'E;W&Qm-q"t&1k,"Q'i<h43Li@QuapdNShj]A)<@UK!45-s-
_h:E>-BZW#kE_X/V`<=HNa#!+i"APK=E8-5VWhLuD/PDtD+t8gfihal$_a?TOIRnZApFoAVl
QtC>^?0Z<[4LojfnS%:NF@XNa-QO4c!+&/Mo^1Kkp-)crCr*Z$&.#=Wss^*2O66boD9`$APS
PVV8$lW&e64'e/d-oQ;3Z27i_prc$]A%+*g/)bou&.-,^s9rDs/BhiJ?1?GGS[,kFUdYR3^sB
(i1J+TU0?KS@LurooKcd#rhas2b+%04<=r%<6<@?XPK(#DtRhm^1"E1;M,dX?H\0.Ht='i;a
qfK2]A$t=40LWW><lWd,L0Yq.aV@[`Jfk<l05\H/Ncu^AMEs=%cOG2J,(2p@6#hKM-o*:*/'8
%9$'ij'2e-Sa$&Ij/'d@JPO^7KSr>g!H.2\Pl*1l<Q&<fTcXUL&-Ci7h)D<C1A9t,Hgr#+^G
m=TAqQSea&\?03n*71/3X\b1^9_k'607p#10I2W<X_<4ShrW1Bm-LO"I#VGf=V&<R$5`A4#P
]AiQlu]A`2;jlXSWMNUl%_=kIpOcn0!XU[UqA6O,+fquH8rftVm41<lPutTNt,l4<\Abn/3k>u
\oCZTlbW&;g(EL5s'l0#@4<^u\T,kY5EhS0T)\_b7u9q:k,S%Hs*Y\JF%/2$!4K;tas0YFlE
&XEF_a#H^KspZmecS6b(HnZrt#FMPR[QL_RM9PN!n[_\1fOo$ff+60J8:lj2/;[/rU1u_RM9
Pm!g-OFESpMAQSCBLKaZRc8/f[GtY+6FTXqnp+ILokZb.go''9kq6,]A]A(`12GEFE)h(WM/K@
9aWc`eAD@>Sn9sKlLO+(`12GrJuQt#XKA\^I:A,]A\j;ZkA[n1\M,Xp$ff+60J8:lj2/;[/rU
1u_RM9PN!n[_\1fQ%IkrWS7n[<&kAZkgXD#8_k5Wu_V:;=%-ai)3]AHm7goVC`.]AOQFrq`b~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="386" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="true" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="cardIndex"/>
<WidgetID widgetID="834ea768-5a17-4190-9880-8463e7405bf6"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx4"/>
<WidgetID widgetID="d4ed5807-5003-4fb5-8ccf-40d9a6d6ff29"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[bnjlr_cw_20240622182609]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx2"/>
<WidgetID widgetID="fb849881-b9c8-4ff4-b9c0-076208fe0ba8"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_zb_left,Key:AREA_ID}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="n_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1143000,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?N#tP'7b`hob*2REdA>1.a:$l*Akp1eGk`kr(7ta"f+1C)\'tU1Ob"Z1='D3G0?E.^urD06
p.ZDRs=1YBc?HOYj_F:mut;YntcaIro_/^UEao*7!Mg^X)*=4h(/UoH)4oD9^Ba+UHM>F*7H
lJsISqhiC!RT4HP<D2'/@6T=2!qgP?#E871:oikDM'3or?k-3RXo:8*9gf!l71QBaK<>j[j%
HGZ-#M!OWA$C0#R<f(K/2u4Z7`[(Uo]A7[R(rPK<Xn@^$4S>h/U&t`4gjX(KeL#!tlbi4:DR*
&kC-YcT$L]A0Ba"tCag0-6>]A/;+<m;e4JSAR!E&<2cl(65<+,qmmK,]AP%jhbWbZ6Ta`#TBI;d
#TK9*OP]Aj#j'KE2Y]A6MZD<7BoD^O.N^rG*$lRi_':ThSdL7h/jG9UA$"S/9+HY)WL)`7@RE?
g#cHt/_0DObG\dQ]A.GZb:O'm5(V::W>H>o/_Zp086'TT/pgDVbNX@-A1=1(^K65J[Z*@0Ve#
(YD$"a]A[;.Sp8LCOhQCibH9h9J@]A,[Jo[6WEnBQoZP'I'hAR$.BD6SU)ouoNo&%MstI.Mc"e
2l4hDP'j_/RBTg@<0saXLh7Y:nj^$Cc6_8+e;^PE\Ol3rqZ]AeR0h\o&2jqaf.;hCRSs/C9b$
MsJkb'l\)m-pMs'k(-X+LmHtD:2jd^_h<l2kZ?8G$#^V$rgam%:p=\oR<'@.%;m&+c*On_NF
9.(&UYi1#0/?HLR+eQnL948tl:Xqkr!rYIlNKQf1E`(RVRFq@1GZ2I15>K11;n,Qla-o&&Pa
2ss*,b\GL1'r1Zi2^GFN%3p9.`p:PU/P[f:X9(=u2YoWiO+ec;g*\'k-f)PHPABZcZ#oO/,^
#n8]AjY7ahp)Hss`J#bTm(*O6k^r-Q:*-$HFI"k6NrjCce$nJYq5>-bB/;c:5Ff`OVhNgj<<i
,g6@KY8qQl?l)D`J63h`J/(CUo3PhD^^RBFa1!t,^sT/P2GNg^?LJ#NPQ&Lm\IXR>e8o=h7e
c":h+]Ah?M2@^AZ]A55`*aLUFd3X>/ZgNu[?!nXqK0RHbf:mW)QZ;[7#mg`Omo)`MC:rPAR$kB
*#*d<G"1^@P7C'XUD@?Q&J`V?@gXcsV5$g58-hq5Qd._.1;q[uW6hp9Kh'O$B`kWk@j4Pog%
2;;*0jV=1:'70'?Q"W*o[GXoZO4bUJ<5gTs8&j)93KJc=rC),0p]AH?b$=4?<XKt7IP2L^C+>
<*]AU`!po9&rmFJfZ+LjP3%ia0b]A+TmP!AY9,->t"F@"[37s-&kNa3k'a%:L9lZ(s[)$%l2>,
*s_in#kYCm.DGU&\U<CliqfYZk81N%"&Zl>*)k.N<UT,>e%8I9gi1R+6$*qa-g4[H>XBf<e=
i%TUX:kSULTBP,V=$e/8nnntW<2-X'U:bRVNh)Xu`@LT14>*$Qa4WGJfW"=%TMh.p5X#MYY!
$6S'_+==>8pe)IQ4g1r&Fdfuoa?2<l8QU'(m&#&I8QXAcn?0EWGGStFaq/G@%ZLn'2cS=)j/
*__S=ahK*L:QO$6S9N+-9("cFrW;d`MKCJLkn6Wtd-(Z+3J=r5m_He^QHO.GMm\nRE50i@g=
@]AWjg=a]AO&,=*l?%89r&6b$283QB*mMj!OZ6,]AP"qhDfp73ICWPGdhPPVk\X[B*MuANdDq!m
*a.$n.!mFR_DC?hjlR3_rr+BfHAe>?+Z7X9Mh7Jj,jod3mLh*X1[m$h?BC&j?&RG^F=.DOr;
T20&DgVSH?Dh,H\l9DX$u^qYM@V\)uRX3,;Mlp/;[[&j\S2L4U*]A.7G[Q&QE>6+>[R5cl*Qd
V]A\MtR5X+EV4aJ%Nf(=XUo,1kFA3RE!!Oc9!=DYR!Yh:-"XlV:$;c6S'VGI)+/@8CeTOP,h:
M4L/XC,gpnoE6q'>-l1016Hl3&\C,n*0a!YL5R"<nD.#Xfd:&Vr[T+qXdL@P$$jEp7dls0uK
kjlV/7c>rQ:r#5~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="FILTOP"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_dzbcx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb·" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_left" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
