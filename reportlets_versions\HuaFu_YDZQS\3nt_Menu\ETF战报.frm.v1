<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select 
up_branch_name_kh,
zkh,
xzkh,
goal,
case when (nvl(xzkh,0)=0 or nvl(goal,0)=0) then 0 else round(xzkh/goal,4) end as wcl from
(
select up_branch_name_kh,count(distinct client_id) as zkh,count(distinct case when sfxjykh = '是' then  client_id else null end) as xzkh,
(case when up_branch_name_kh = '北京分公司' then 1306
when up_branch_name_kh = '东北分公司' then 528
when up_branch_name_kh = '福州分公司' then 5538
when up_branch_name_kh = '广东分公司' then 1085
when up_branch_name_kh = '华南分公司' then 700
when up_branch_name_kh = '华中分公司' then 878
when up_branch_name_kh = '江苏分公司' then 648
when up_branch_name_kh = '龙岩分公司' then 2063
when up_branch_name_kh = '南平分公司' then 982
when up_branch_name_kh = '宁德分公司' then 1576
when up_branch_name_kh = '莆田分公司' then 1556
when up_branch_name_kh = '泉州分公司' then 3679
when up_branch_name_kh = '三明分公司' then 1726
when up_branch_name_kh = '厦门分公司' then 1635
when up_branch_name_kh = '山东分公司' then 627
when up_branch_name_kh = '上海分公司' then 1177
when up_branch_name_kh = '苏州分公司' then 111
when up_branch_name_kh = '西北分公司' then 792
when up_branch_name_kh = '西南分公司' then 575
when up_branch_name_kh = '漳州分公司' then 1513
when up_branch_name_kh = '浙江分公司' then 1305
when up_branch_name_kh = '重庆分公司' then 100
end ) as goal 
from ads.ads_hfbi_etfhdcyqkmxb 
where hdid = '202311284879' and bmsjhqd = 'App渠道' and up_branch_name_kh not in ('NULL','华福证券有限责任公司') group by up_branch_name_kh ) t
order by case when (nvl(xzkh,0)=0 or nvl(goal,0)=0) then 0 else round(xzkh/goal,4) end desc]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('TABPANE0');  
filment.style.background='url(../../help/HuaFu/xb/etf02.jpg) no-repeat'; 
filment.style.backgroundSize='100% 100%';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="10" left="30" bottom="10" right="30"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="6e34a912-9ecc-40ae-a985-d07aadb7bf49"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="30f16bda-ed4c-451b-b08e-52c6b72651be"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="T02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="T02"/>
<WidgetID widgetID="6bdc46dd-49bf-4053-a66b-07f5e9a40a1b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="T02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="8" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1371600,1371600,426346,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1574800,2997200,2057400,2057400,2057400,2057400,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<O>
<![CDATA[总报名\\n人数]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="0">
<O>
<![CDATA[新客\\n人数]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<O>
<![CDATA[目标值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="0" s="0">
<O>
<![CDATA[完成率]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=seq()]]></Attributes>
</O>
<PrivilegeControl/>
<Expand dir="0" leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A2"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="up_branch_name_kh"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[&B2 % 2 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Scope val="1"/>
<Background name="ColorBackground">
<color>
<FineColor color="-460289" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="2" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="zkh"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="xzkh"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="goal"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(LEN($$$)==0,'--',$$$)]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="5" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="wcl"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72">
<foreground>
<FineColor color="-9007630" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72">
<foreground>
<FineColor color="-9007630" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0%]]></Format>
<FRFont name="WenQuanYi Micro Hei" style="0" size="72">
<foreground>
<FineColor color="-9007630" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9,?O;X)bFZ>uh&3DQ#!(-9o]A8kj)k1dQ+2c,u3lns4m&Q),[MMcA&<VCX<eEkJ6)UR?>NnH
pafVPB[P6A>i(5Y>>_Pn[$qJT'SmRHf1GoDJb54I]AtJ^\>4o[F`%I[FS[QZgXFRF>m<3[KZj
9)&t['H3sdY"kNZ;>n)b2/\MmN5i5'PESZ-@CAnpfTR5uS*,YCW$P_!Mho5[YgTK-)f<_-1U
-jEp:`:@(\nHbEFg9oZj51bNR[nnlIA4%I_95-:Q-R-tU"g4:Q`PIOW3!nOg:Qp7V16CL*f]A
lX'<A,q6YCF<6IBI?BIJuJUJGlDD_k]A534LL1Rc3qs+[`FA<r(91DNPC"rFE3Q/m7uV#kYLH
cc_:`gp:<ooQ5584]AF9[!Wt'ji0V.G-<\shM,o!\p')?i&?UM`3"P8=[]A0q*fO9Y:4Qt_t>i
*7F3!'atdmK6`>1r>$p>QX3q[/B5.3&f?E38->nQf?[i`*>EAER9!Ae(=or=t(IeGZkhZW=d
kHs-IDrJKW"KM.'ns+\['&1R%FG7jUM4b&BUBMB.VL8l)9n@Zl/@E-h[/ggUt_t1fMl2C75m
We2WHJ@6kJF%u@"0kd#DoH<$e:O8ga&4"7p'^i!+2rYfN"gGZBR[+8no[*KLq>8EKQgK-ZHC
!HfZ+K?VGa&K4(0TN%Hfa\K/2!sg:ul\!S`'Emi6L5pJ4png?N[iG53jK0^[_.rp'?D@ciA4
L?,B^%Or/ZQfMVqos>5.!k^7C/feTK91QL%KlP)'%L'-io'N&L*uBoDJk99V!G+9(MF-q\\Q
irO@R%?gF2uBQlkB(_>Wq;IW41^rT9oBH%UnO%rM_IHS(uPQ@0&DD+`&uYcj!TO?e"R<Q5!1
<]A9Tj$luE\1;bbf[Lh#6h<d5uXhTurR]A+P5Ro"L<*E+'9qF3D/T+g<T>#IQ_1MX4s/I/#gMo
O]A7X2t9Dt,uV$T`hkoGHdk3,7Ng&8^G2^,2<,j-o@u&VHa5An%>.qkSRMMfBu,(2P]A9<9.:s
m@-)NpS5;n9,l]A:gA*l)`@&S39i,3rFpAr-EHdF_/1e\7/9<ddi\hJnA@]A]A+R#()o2;=5gMA
PH9[S'#,<Z,_#`Hf),^4[(Lb!`"0**0t7]A.]A4kS<M\#I-/fVa]A%^F=t'G+.+LE[kk=,)XL.F
(m'8Zorj6R?YI.5sj6nHFYti3>LaShiHo=IT/\O<ol(g9gIZ9\p5@#h7MW.b%bc_Y4Y&n7DK
WgeKftL-k6tPD:N'Vb?+1d)!4(iBVB[Lt?2eTj54K3KpP;LJUtTib=O*<"sr+TaQ368a`50P
n&1U(a-Z;Rq*7;p!oafUkEhb&?HKMSA(qgIAoGBU%",?K\F*KH3aW\UQ%Cb,%@`NcYRs[Z,Z
J9KXtt)CnKW`4Wi81(5F(oKm9Xu3V86.oQcZfGq&E0\ouE&[0(dL\;oNW*HoZN[PL*DnS-OM
hGW'VZ2A'1.*7<.4[F>HkJI\C2C?3c\K(G$oq/hU1r8N1pIb1:1D^k7T(CY;Z%'W`<_+3'A'
\p)3tE'.M&*fQb&ce&G0:bd3QZU4W=eht=RiI:D6i,0Ue=52r*:]AS3ID;`fgC_u/*Zb6$$3R
#%cl#1`4IF(2Y]Aih;*k$<PL<<L5AI11qk)O2lgGL^5!_M]A\'$)30VE"radZ(2n.3#G)Q5,&\
'p]A)l@4m5^iSHsjKOu1K<1@UI$uW-2`0[MCX$qSrO7&SD_qmUg:VZtjA_,tD"TN?1`@d(h,C
1glj8B*gDP2#0*gfomgQ(h[6cF]A>8g$TTh^C?1Dd4:&Ca@W(cu2EkjgJ[qs(<te7T'ZrBAl"
QHFa!nem4ubkU-Ko=%sDh5A>:M[?0ZoS4*1R^&?,L.IY-I>jTh2<XUHIWt@s-GjaVBqr/3iu
cIV3S4]AMh_0Dg=tU'I1_=-P1p$BP$j7"W!=Qoj[?idL)sW[n^)KW#"le#BC$pV\+__tDT$Xe
bdlLWrB&;*f<o<msj"sg[-JVaq6pKC0WlG<Q(PFQZVh8,(2IDo`6(X_NK="GH.Y-hDBqIFC"
9t?MnBJSr70K"Wj6gM5X/9L<"gF+Nl2R1>I.^?mM4&)Nr1SH^,Ud8:.>Y-Qef;e/<:<+49Z?
$4BE,AGljF6#fZe)(7(hZg2#O_;S%#79PH$M%=?I#9=pJ`P(qu]A2^Rs>8C98"#&*1:"qn:pf
i>MJ57GL0Qk4SQ:p<iq5;o;.#lV%AT_\BYG%4Eu8<g<SjM?Hap(R2[t"rWkQ)Z/&X9gUBn!^
j/!kC,G$NeHZ>g;UB1;]Agif'B%rJ=X:<Gb-^$XZs?#HTFj>L.tLRW-0'h8fMmm1SmhMc)f'6
A:O[]A"5^1U,D1&%Q^6GiAJJmP4fd)iW5-KcL2K?am0ro)J8;VO'4N^L@c.;+q+4a6Y$U"0g1
]A81,,8J@nUbII*4T+K1:sCq+BX,hMZLcGhm"GbhMCEB'nC^@\=S:<g8lfl`1]A@dl==kDSeO:
=JP=AKQC4nQ/)bj>[?]A<g'd?j8eB3P'@3bSf<X))[(%?XlPPF'W1hOajM[&#`XrTI^Y*A17T
Rq(@S;q&=F6W!OU)+&j.KtN18d)NR"p:e#BEI'.T3[r3P1m;!hoe]AF-_kidEb1q!U^8]A`V:V
.^VJMfmd42nsXD7LCtCgg7VACU`WUIghmH*kMbD\05Y(-R.;VW,*'f]A#nZc'iFp9#bdo^qsW
?cbW4aX7?V#T-*D=^f.NQ16G]A"r8Mqke#m&F#"u-e+uJF+@Bgho@)9&SNu_:"Q`5eTW,>)$"
]Ad3'-*$E%qr,88CZg7-GCZ($FRS0kB;NXXhd.i4RVAL/hkH.t4J%B+jh*)q!tLNmNCA3PP!p
N08li&OC_0-=Up\>YVp)n5;.I5S'?@/EfW2"`e$,C8Rq<J@/gqmTM4SlUKPTnm]AN^-$@.B4K
(i28lQgJ3P'in:?\[N00DOkZcaR36tOb/DRfpaR2js@P\YuPNG%;?.B<E'>:lqPC,09+Y"Zp
j+TCF;XNij]A(\g(ZZDgh)\A;(ZJTbJ5!0pg#VNR[^]A!C!(r3\Sm.nBt4jW9i3JGIrf"'ili_
%Yt?qIX>T_+n\PU:rAg.U`4]A<&fBQ#<-Im3]A._K%M8)u*Z7:eA8dWb-ojh:eAgFT8?0^4h,`
JuJgb-1ds,0<olf&TmZ(,dsZD$>>D7($$]AZtZUP$G22*b[A8!hetN5\YJg<"n%6>0!2Ip^/2
H8q;'^Cl3WehZ6BJh3uZF07!iJZ=qEo)/M$:hKh,Pl9EoNFA:f?jVGo8?S'a@:oG\u.e<Njg
E"6dQ7/0%,JNUqsQUW`YIS]A5M%I&0jcj[=oR2kAbfUd4O%/D9(dT+.U]A#UMQM8ZcaQbFKdR5
/DIEo#ojQ2&PTo7GS3.E-ZK`je%@CSIFLp@65\`e8O:7;Ep%?Cm-?;F?\jmj^:Z04PC5%I*n
k=WtAUO\809%4DQc<tF7&WNBW62*%=[Ef+]AC=eRFFrpI.aAI!t$&5/dq]A^NidL6-[]ALtFK+1
W@!.E[.$kDSAb?pLFL3/n%;g-YsflZ#+9*:-LOhHh?%XBXXV<k!AJq?dI>'(@g]A*K0[:P$@U
O%gKG6f>m=Y3?L9iC;H^=nrPkXsQP:9jM,lK5Z*<"dL'@2J]A;K(d?rD0`?b<#]A:D7Jd4#S4[
!eQ%ZY:@uiFuk#5AX]A&-:d,mJd+81<:5KgQ?56dhL<unIRW=lN]ATR-hf'Pe;QC)OLH^PAhig
EiP\p,cHik'36<Ge+SB$A$l%G(f@P>&[Dfthgg7]ADL+aFbY@jFG<D+qgCbmu9t>&R9Lpr2hp
N1huMoUCX-]A[$#'Y8a8+Wbo#KKV't(hYA]A?p4D`JUWC#T3'Ubc-(R,=054=n9^c*CZ6:TD@8
;j9BU/2i23,*rD),sY<4gV3)#583_PfoC#'k1US<F`Y+ZSlq9q_q:<ZeA0I2\L=6UbSAX.@L
Hp\cn/'hN/3SFL&!$TFu3'V(d.)a1j\G7irL#'+ed_Q7A$g67"A/^M`%C4I6_FkY@I02hF3^
eFC@P&+ikoP9tF*JVCY(9T5''?eL+FJt'/e=?8Dl(]AO:h!SscL]AMnS;kq@=lp0h]A6mjE*>:F
/eLTN0AeEQqF9iqWH<AbY$bHEf]Aq:)(ds.V*$e2_>Vh_YWH@%]A+F1/-We`]Aps]A?IPTl0_Dq@
M2:sX4J"8<a]AEt+l9MTa)$D7lCP-R\XD^MON7X:%6P6NBORT?G-9*[8\ABT,nVUu\7;b^g@7
6TY*O/)=<HaP^cb:PV]ApB\4E.YcZ1k,a^pU<LSh#`?(M$+2Ob/_&icbnFIT0AS!e.[@`s_Q*
b35UHqHK>uP2&@e+\J`)[Fh'pQond\Q\^NUNRW'1n1Nr'WZ+hFQ9Hj_JBr_0E%4^,H"*0>+\
G@&4!7V^d8e#8OoC$F>HU;/,6n70I]AQ2p/:]A;Jui*`7YFK01'?jY@25<`p,6*?!@De5=#V[?
NsfA9,ku\Ec/@-bN1DjdtQkPce/AO9iroO1`u3`EVlN3UColIi,8Qg9u73mW-1=iglfU8X\=
??ca_%NJGigccnh<lJH(!("bmR)<*)!B9DPR=Y,iKSLYOXP3Kl8fJ[)jjiUICD;Y%5d,\070
T^jgj#`eJi9L<W%TLKLNY$mO;0SEJCLZ5K0<-GW6mLO'Qt.@8>P&>+rc)TBIsN:R0`-a%kO8
-J=PJl_!O_gNnpRc"X`*o=p"AK'8dsm[.H2hWD?Z)7J,:IQn@mRE0=\>>7^#B;\__DbDrekR
qlLdPL;,Okn!gL_91U>;9jSAO5@93bC(+`eNm6ZuEjpOCT_0if$G#6JIe'B`_,m=`\A@3+mD
hGuNj(H]A\/bDM(dHdg`Q*3NS0dVKg+42KI"k1l2[2(="'arlB6(=pH:pCbGlLpYd>ji9MNH.
+7=1Cs0XeZE-oqa78%m1U_]A+%'QbleXX^9JX'ec+.<>M3@:PQ$r)?e+NA"0ib\A`fE\Aq&iG
CY1B:Y.MKh&eXZrb'ubbp013<lS'6lME2Uq07GhW+VZak.R)d:8T+Z9WS-0l1Il/2m`l*9+T
L]A"#V%)^$\4^"%`=4Yq=\(]Ammaj>KfC+P<,Xp3iIZlW.R.0K0#r`3iIZlW.R.0K0#r`s#6Is
9XDI\I_iZtW2Xq=EU2^thY2i<p(k"E]AM)mO5FmElhQMl[UlNre3`8K580+ZSFek&IOZHB1l:
;qp,QAjIWts="=J&b))UkE*DP"!g#D-jk%f~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="308"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="308"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="T02"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="308"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="400" width="375" height="344"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('T03');  
filment.style.background='url(../../help/HuaFu/xb/etf03.jpg) no-repeat'; 
filment.style.backgroundSize='100% 100%';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="T03"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="T03"/>
<WidgetID widgetID="d7713023-3b4d-4375-8283-4948843e0f1b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="T03"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[381837,1406769,723900,228600,723900,228600,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[10668000,1066800,495300,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" cs="3" s="1">
<O>
<![CDATA[*活动新客定义:\\n从2023年1月1日至2023年10月1日期间，没有交易过ETF的客户]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="最新数据更新时间:" + NOW()]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="4" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="3">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[ 
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" spacingBefore="4">
<FRFont name="WenQuanYi Micro Hei" style="0" size="64">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="7" spacingBefore="4">
<FRFont name="WenQuanYi Micro Hei" style="0" size="56">
<foreground>
<FineColor color="-1641473" hor="7" ver="1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&;VTt#l.@`VAEN-D!tH@MPp\H68l6QV.kOD2E6(uud*.VC*)WXkbnOtl1C#[ro5)^FA0
n&6S>%F!;,.BfVPo<bGg:AVHJ[t9Ib423qu#Wki8WI<elmF([F]Aiahg;OAJ&0i*EehLA!!"-
ep<9L/!5QoQ)d`nkd6GIJT4\-(9:<,a:=t;4bgLtY%9Re.biqa4WS]A"oRW]Al0mIT0fZj2(%k
'XUep9)Njr2al;PB/IR00FHYRbMRA4E`Q#s6!a3?U+4S.Y!2g5?R*3RKmP4&VH*mg`$0><0n
aWL`olV+E]AuL679P=a^K*]A()JUY"eL#]Ah<pK!.1W;LkU"Jl!<<8tBN=-W#SmCWF?j,U5RFk!
DY]AZ&!"*Pqfe.+72?4k<]A@*#.QYPMHC?eauJ>WX6>AmXS.+8A;BE5j8=cYC)_sW"R1$$1ISC
m@*ht<Q%9gSj<eZipJJ8N\CpDe$<$33'##Q3'@h#@j*IB[eQ[&7_[;%*HgO21iL'[f_hreCA
G$DMkYG_c6B<D4t:LE$.$UHlo&J0Ek1+smKo\<Z<m:]A>j;VuO3b[Uglni'ZH6rI3SHPfMe4)
E?+OFm0>T]A5(uOP@RAk84=o4rXs8hRfEtNOQc?oe%+UBVStc#]ALb,\9X"J@ImI(3YX"4IM!H
,DH\qL_^dhfH[Ahb&)\C1Ec+*'W,UYF?9E;[#NOi*qa6=1SKVk"?g06f'.WRN6&ed?9bdRRp
"ca@K\7U*?Z!s'Pq!ijF&i80Z0#aWH\&^gPN<&bl'+pRuG_Ph<V0VW)KfV5D9G:Q!P$!dL+2
6GU.qkH&'V]AhoaFZCF#.3o)%;!Q-^TeKKHq#%e?c[W]A/1X[C8]Ac`+O0-=5?V`"qGD$EQQ<'L
T<57#_(/_@B8iAMR$&T@WN=pE)=Xp14hY=o1!5RR=j$sf]A(kZ6nk%3nHB#$obkK+&d-<ZsS<
/X<7MoAQn#aehrQ75=_pOtRnmBZO%YOJNqgZ/O2e5U(4,pbD$74(SU]Af/SNfVRQ&Gpqi+n)O
D0eZ1m&Z>bGrEmPKranCA#?7V)Mp_qt4nJY0AQLIPS_kFZYDgencT]A3<G,l8Z)r@[/">s9Dj
b.-TH6]A]Ac'BD!VS^Q0Ct%5=rf^-.>T@]AkC^N8t/DAuUc#37BA>,O&Q6P?i'eMg8C)C[<*(qG
c%:<SS10321s440U-ndZ,]A<H/p;`f+/'EgVTGjDGZKrDj9bXLr!2ICS==Bn_YZZ0/i0:q6QN
]Aj4^h]AAXKpA:F1B[Ql\iia-[=*Hqq3Op>#O9iOR<2bF65XksIot#j0$T(FsG-l)f/CH176D/
SaYISiO-SC%htFJ\/RAQJH49O4hVYY8)0%`TcJ^1B!XsbH$8?lY@[":pCE!_J`?H>ciqSiaV
E'W)GoSM9j4RPHheJ#']A>YL<:?.+o,Zb3T(D=rU.,o19L;;SFEJK,UpJE]AHiR&C"q)d\0nqF
`B4ku;hkpq3j7#UO8l%!P'Dt)CfW4If*^c40e.3l/V&_:9<+T=P:LoTd)%V,#2s/t$6Bb*h9
[kk8NI!\?JW1DK3DQj7K@+Q<>K0[8BZB@^04nCR:,JZ#b,D3FtDMI5$tCg,W[]AS1r4(8M.4c
S>Z#D'OHUG*]A7eWJ=ictscrJJqA;m@NMUb*qpe.3/?$P).O9`<-U,')_6ODX_P-im.F-`I;P
PBN6L&S2Z+/^"G`D_oVF=dTe&3T2kE5a:RR8eg0?_`aiS#.L+e;TYpJ\/@Ur0Q`uCD$>*nK\
t/a>j&[.A%KZi&\3AjKuANiYP0SI!?oA3([h4V'X*T.<O_Q(N$(ecI=SEG"kfgoVFR=gD'ml
+,O(W&VOt&,%,8RnP.-RkgoX)1SZm8'a]Ap6s)s&Ws!:_$)Pr*GWgo'JdR,JsAB2r2\b$jUHD
IB01I_>nCq&@#0_e\H?2GbBh-2Pu!c:H.`)5rYaZ;=Vk`lU`%>9HVf&<jpkS9KOC9c'nqfFF
H)dC+UC)pgT@+icb6";NpMC*7s]AD-^Ua^hgTNG82!DRb;LSa)AsOo@qAP@G/56]AhFiN2r+@!
]A*$9j+:9El)*.H`"r=EMmX_!\@FtdME40mEh`_ii!2296E+\*TM9K:oT5".)^MM*=M^a^?f,
prHJ#Ob>/5>C'<ngek\+RU&+<?Dr3%KF_P;bNir_g@1EqbAVJ'(khtk_GTC*`0=/Qoi=$+s6
a:DIN4UQH(C$o_X\F.Le)`E73EVbD0rMclea1BlA0YI'rGq-L0R^iEl)`1T.1R"E[dLa!&cL
3T^c_8Z$r0jlX8aTNp#rh)3;JP*l^*EJIkGgBsNk*lmg=u.T_[]An7>m"03^_n9u#G&>hX\<`
0-.A&8^/Ae)J"D,(l:7'T;lqKRpK`,C&&J<S3NL090;W/iP=%N0>C`@_B\I=Q5[m[r?:)4B3
4uC`&@TlVc@tXJ^LnZX3Y5RD!2YVdB9g<qaW<toXuAF4Yj_gPGJ7cBXj78i%<"Xf.S='r/in
C0N$@6;Xi5Gc>s-jQaS-e9rYToY7*Z=]Ac[u6rK$>]AbhH82J!CNR0N7DtuqABSf=_Lcg(%T]Ai
^P+)@B?VY)S(/dXV"QpUW,9[`pjo\:8KON@_iE!qAM\V#M`_@u+^+hF,haWWP9#QK0\-UD9n
?\IiUYeLN8Q$BDeOeR3@k%q(l\9bC;AO=H?"81o*@_'0J]AQ[h#jDrHaRToDWqOh4tg!Ins)C
G+8:%A3`oslh@:%H3*b<Q6Ft<e69^CoJ32>>kJ8K]A_$i`df;T(M6dmkBV07TkjQ-UH42`t!C
BZg+T5LV`E#:Ybl9>P=K`6JX_eOi`4JL"e8I#kbVH[F7kt--p!p93X4r%\R)0=NTO*7s;2Q<
kq.mCpbSO:\8>m`#V=cKUOeZlG<T7jk'<k)?E3m$&2@dYq6MH/`K@jgr<LR*=92#"/>)("P8
=:hi\>\]A\O%k\X?o:HYn+"]A6S<:c_0;WR)DSQs\<D2I/Xb!saD^@"l$;T`u%+kfu,LBun-NX
($5/[j)"GaP=*kol.hp<o5iF9,S'9'2ODp$s8qXdM4&<NDBe=Gl+-nT2+jIC^DRnC0Yl&qi'
?^V8/K',B45-T61_iCkZR>+6)g'dmVh?(*0iL9(G/jT"G4!6P9*62m0Jrs]A[Q(X_LA!;*`Iq
oAPqjl.JLZR-4]A;%W\=OP'CSAB!R!O.lL_F3.O?O()%Ye:G&J/H8g[73%Q(.X7feP3XY61_<
,`N>Pt60Fb;Og5bKCp<7%;&?chk;7E;a%Y50mQs>>+g'!j#)J6uG6]AK9')[80F+61jAA]AtC?
f'l_^XlAqMJZFgKZ_?MLL[sc3Y=8W.gLL<_[V'ZbBWr>E^bMqkK9oQ:5?cukCO*Q@c+sORI`
_[8@mC&N]ATg%A2[]Ac:IQ2P:)N[hp\Rt;6/OJrg?#BW]ABm's0\4XB;dO09nEdG&g!J,iOkmeg
K6,F=V:?*VMT6RJi'WS'I=G<)\<EMAIL>>2B'e+)[XLU*S8EK%Dk5kS*f=C-H6^8FW3=rtCiU
032T`/j('mV7TQ%QS((+CcL[<9GHNO6-U&10f0Nm6kj@?7@95bVT+V&3=Ah4%SY_9G`Dr^!6
3G[ejki*@Accb_'%=+0\DPX8&;7<?6IMZD8E:7:atA,[K%.:nKJPjNAhBg!:uDr#9/h/<m-%
=U.9?!!NZi'.Rg%R(3alSP9Qs$6',Td3oc'iIdSSl5/>9miBb-mc)qLXB0^#QNC07S<9r;#p
M4G!h[Ga$AEC-V'o.'@MtsZ-n9;XM#-6P`K:nJ-d*gsDW3Vkb61_H44dGMEP`!\agFR&&?Z_
/$6RN4;P(r)3a_c[#@"sU`B%QUGqkPO*&.uk$bHmUq&Kj*)qH[*auAYT5.9V40mo(C>.dc;m
P/_kmBTb`_lT+009=J\Wi9>hI.^7^[ri1;&dJ/f%=,2lAt&7X=7l<RO',%3/=U]AG0P)<-(LS
2d95L=8s$l"0MGjcbDW=M$p09+4B'Me%$b>X%mk]A9/]A2Hq45Et&iH!X`^JnW_Eb<0#e$'7#G
+2[&*K7p:Jjl9#CO?2BUi@?pg.gnT(E)1iN+/l;&a@GqbNN^d"pEk0jPV:b2=hq-`Rf&>8a*
T,GQ!)pheo9e:e!kW>!R=S<SL.WDbL>bfW#c>Un;2UBqVJF9>`in6ml7$MAqV8W\arDPER%Y
Hd^BCldbqo6j)npAU&]ApP\&/RJ.2Ghd10peB7LZa;NV#mbm2^osl'3R,./C&.>gnES454Co&
2TO,j17_ud>pB3H4X/d(i0TcImh+*/P/%^]A(4o%!c)icVo@f,/2]Aha%0tp\9;Ds'N2uh4p3(
42^%lEYn_3QA4Li&_Mg*:(6UN%D3ZKA7Q)]Aj&bWQ;@T"A_GFeS'P=-c5+'2a810ci9.9;a0J
KYp+21uKo$7OKf/!P3_[m^9ptoo\duCm,/\NF_tt\_Li$*gJr/U#cDke:O_??_(Hse=<+r%7
@-A*rkj\3kX'Z;*EHZ11J=kK,BQ?T@t<-IG!WVrI$Ip%,UmK8eB!?[L@Ltm$F4,-RifbqCgE
QnN#R:1e('-^Z1QpD^SpsEGq?e'9uhCh!*eC>90M&fA=&L/idP3):Uhr4'kaUZ/ZC::b2oVR
2!Y@]A0^iB8GKV?kb\m'm=r<8k%RsjAXNLTBdjKc(LellS&C`R[R!`ahtjp3M$g_oc;JVgm\q
aqkZn3p-'GeRQ1oPEU/Y[1eDtu/WO8=\UROV97j+=1)7jCB@hkJt]AZ:aEKr;4'U0`G]A!Hb%C
]A9<d7imNCan0nl/fN%0!UoKlUT"]A4JZ854E.dt=-Up#Gd&X5Ag=>GP[]A]AMUQ\X$4C@!GUi.^
;M<F_a:<4M_%6fmp]A2_rt,W:E_.<5!ro2\:UNmMjd7Q]A:gr=kqe_s#-h(JoTPO9ZBa2#,N9r
f\^gTsc9%Xu4jIZ>,aZqhF25@u6A]Aj8!nR5:#LGB+3M<74(P`p.=h;7Xdp+,BWb[mHA%eD_2
84Rp]AUF>uQ4"?\8I.VV>r"%QIH]A2h'X-+=np#KGL/n:CiU$_<S@/%V%HXl0&^SR\g899?U)N
9=(bV_oAkS+(drZMgk3G<=o.[In.fZ2AB]A*]A/A@RNTlgifE=C?pZ"1^Rtc^;@R"qebE=<K"?
1HG]AC`<E=GSNBELK?Hs.lI)kL;BL43U<Y_L3p79CY5U!`6BhH2)7l?Led?IDW#:4bD-u[@r)
c?8ng#G6lo0f2FO47FERp?-7iF3ER<!H2Ud66P[%TN@9\Qq%=uBc]AX&Q[)G6$1YMZ%X!=K(c
t/'/8p%d.XaQ-OpZ#uY5pW^':>7FMs/4ZqOes/$^&J9#%B=)``tI*L8]A-/*1`L:gS"P=1Eb4
i0)>rlp!B7CA25W)D_H:C(lfGt:E/bc>LFiuuoA:0/u0&'U9*>;)LHfXbEPb:oXlD3YQOSqM
qWGpHO;<p.O=Lm_Wk>",I3W@FXuR-)f>dT)B@/bsb45?]A?hbI(En?Z+b8l<ldJ_rJR(?GT%B
"fZ"eJIjH0?5Yp#Ppj16jlZf%Y8XNLWiqL4MOgY'`Jp:^%Cs(<1dqg:q@^a2"S!:7,VUr%"8
m,5QfmB[Gj&'1hf:a&A2ago56;hgZ731&!>esm"untq?$.^mH"IApEI3Iq;Yb"AQn(W_gKTC
C,9tOO,P&CoEoR2$CC)fXWN3gJasb8^0$u)Dh1+28[QX43X_aZJN+0j#X.Gp.'flt/gkL;*Y
[ME`"WiMd+1Gqp44oR]A7[uo@.hVaM?5>!d"pF3]APC*$g^D%dSgoclV;B$<M:>j\VZs9#^,B6
_e@&7RhJIL?YiJ5k;*_f::>ml="\J@JZ+7toteA]Ag&:#^WZ,sZ('mSC=_obeQ\mCQ0mHu=%s
,ND:aP=hsjWl;daE::m<@hUHFcRgd`_T.De0N$9P;9^=b^CH:VVjKE&?f!=Qab.jOhH+Dj]Aq
:eBd"M8+gU1@qD3cFQ>(ok=T:3gH:s7k^POP%ZH;K]A:E7??G@23RP)a3-`R9Qg%`$an5C0nr
#.,uGJj9A5QGFqY/_R'H'O-M$2<@'amqa7/8RVRmtViIhjj.it`qO#,`EL]Aj%B^/H'SZ&[g4
26r=S.2d)OE$]A3Gqm(.3LKfH!;"/=]A)$B#h),)SN"VSl)!+FjkH2TG3gaT^cBK'_mD9eO`pP
Eb[b186DJ3[GmbuIb=1TLQaUr26r)Vi#NT0(f^WC_XPR6op7`mc=%BC^mGZorr\gD'6=kZ6*
Ofi/;4McV[l$8Dh7oUQ]A&n9lS9W$4hTZ='5qrh&?X-l&7R#HCXFoH'!F"ijE]A-$W]Ak&+`@Di
1=Q,1Y$,cD*?SK9+0*c0os'#9]Ara4(3NtlA]A3UYkcbC,tR&_:fDj%o/1tOMo'le2^!L`+T?t
_]AJ7>/TDGMInj%BrEXXSV(2R<\7s+E[_-&>Mjm:o[;@Ue6<Bd>B8tJhdCWLOnTq$+cGE2"nP
7;/f-Cca\B9miXmD7%-+=6=)\rJP1&,FQoCL3SDh4C4K,ZOu;;X_EKGuEsV@e;'h[^gkRo(R
em80jjW>o;aXn!(=jmDFX3E3&oFFqnu55M[-9^)1"cm);2o;\a8d%*b9)P,PCaUHA<li9e(7
6K.";Wo0(d"U.0LDsl<AB?RitEnP#s)WR/*LR=#kKZ^@*<;O_Hb<Yj0Q,O:YKHG1o)Hs>GDq
%3+9:*/qQbO"=.E@FIE4+=F-Yuc[62>r?S8cB:8i@Y>DS4'Z^W`%7BWVRZ@smTkU46K(&89,
F`FY]A<cafpYK?H7]A2n6EjH*q]AY>8@+0&W,=%dkoa,^/fb#C:3/rl*aBODS!<[CrK\_4!OEW;
Om1c-"_g./.OJ!Nl+XGR35]A9_2X]Ass&N'q[he/iQ&7'+IF"3aIO4fH;Tq`-Wk/`GW'DB]ACV9
<EMAIL>\fqkY3niG'>MhRa&nJ("6-#/?HG.D@Ds*DA/uNbi4_O>OH?6KY.16e]Ae4rtu\A5%f
AFU[mfF]Amr4DI<I,tM9;gU]AKY3*^S&UtI/Mc,qWG9\]A)2Uo,R42&r;iT&J;<T'5S"l(&d8Xb
OWL:^b&:,rV1-HWk*_Eeum#,eRG#"d@4<:7C4U2uF8q!k!RYQ==nO-U!gT=H!'OWN<i+e5e5
U'\NejPn[=J4*-8Z59*'(M6+Fg#YX6'P7pkJ=)$0b97N,Ze_;,"c\&Yi5G'&X9NI9aHuN8Mb
%3N#e<UIQ+VV<n`+CfRU4^jQI,dZt$1g`NdQO.?c=smQhb"d:%6Ifg%@N_9WN$Ou0)UohM;2
0-Jm5$tj)m>'X*(W&/CVeYcMSaHEK^7(73kZaN_Zjk@mY4qIcq/"B_2A&S>>R[5tXSO>,/Z8
4Sn)\;Ef+B3p*70dt8.tiGRtCEml@'df,9%DfV^7`ZX<,LO)P.R6A4k'uPhn1k#!l*[Fg-lG
`(!7>eDKm>8:`6OR=.a,aK4)SpN:D/5hbQa:*TO*/cj;n]AMXFEn",-,"m!rnUX?A3bMQHj5I
\XlXu>"34%#KPd8/P^XLJ\hdcQa'O&pdm3:_`Y5-N!FBkdSD'g0S2Bq05NM#sl#h-Vq4oH/\
Qn7e\%j>kZ>MRBN5`X.mn;tB/Q&4e'h7fFclm-(*'V)Elh]Ad7Cc-\dpVYVO\T.Jn#Nq5G"I9
u`Z7V4!8WfOI0L,arY=:2.VgM'NNSo4n)l[tY.Fttd$PH;!Z]A8(gNg>q7fmL$<MsJpBI=5o/
hT)S%nV]Af++H,-HgR_7\[?-62q<4G`$e;b"f+H>7Uk4b9:9Y5`.@*$7Tr-#ES5o0Ak;;r$?[
L.o&'JQ_g':u1b:U<8*H!>m"coh4InQ9DDE4aErmNZWLFWDt#,&Z>c91NW/D0ldh!N3MH:)b
o1Q(NE8E4&,1b4HTD"?%#:?`i!,NBmWJQ#1Cl4FIufWF4SJ1Xb_+S8;iBPoF.()Bmci?&Y3I
b"'L]A?E2s4SlTS89VCCL"2lmQ@&=LE>4m0jkoYh]A51U/>;4I@nXa=RSccCD_EQ`s-ouu/[hC
Zi6]A/0d5./-We^CDP2_n+SecKI.l83u5Vg^7C)m<PlqnddDQW5/*<)tlM5FHu6gFVJGku^AN
<Ja=DCXe+sNU[0.U3_p`@%cIW=naOW#P'+fG<!HoEqcrb=E-j!)gS-/@(9WS0B^'3RH!fk,/
E2ZnN3Tu07:%!LJN+Abfm:`&7$sjeOD'_`AOm275-"QEedQ"5NgG,9Q"Q<,!N\hiN:NccMWG
B8mq6OH@ncIYi%(dDQ4HBEKXn:SDMD_=1I&qXTU$?;u>`?p,sqqN!s0I`*).$M(I"PflC4hb
Ru[1gOg)G3?l#hQ<X3Fi5g\B#LA8b0!8LSiGMMs91hIWF@H/KONJ!Ekq-43=*.#\;7V@+1B+
*t-:gL\CJ[CrY*r7?bAfT+&bs@cfmZOC>uO2]AJorBF"fLBu;<iLU^ZsB>o<5$YQdsL=`'[#%
eZPLJDiB8ZJacHMinS[Y;p+hpcQ,DfW95jZW9q$sH<HbW\M7F[PNbf&F:"-$?om_A#+GEi-P
)g868+\mqP0F^7M2Z"8L3Y9m'b2qX-`Z&Yc#YOM%\ZuK,\GTG0>[VJ(*Jnputo.AQUf/1=\g
-4g6?\<fY-jpGM%MXZ;VQn!=BT.(XJ;m*>VWKUm)WYk[2SA,X+-Q";TRK<Sqt;9.H,GM&ka-
W3BUo_ql80Qf+l7QU#1"n_!r,BWH,FBLoVkfo0Mc9`<al1DP(]Aj=ur*Hri,Xi''?mFsZ]Aaga
8XIIf6fAK53<rNh8WAp#p[eoT-?T291]A&8VDQ^W4<&cL_Ys#o<,_p[`NC]A:g^jpqW+dHe?fI
6FjPD\8*Cr@Q&.9"beo'#al9#Moi%&NI1rY__EJI2NX%$Mu)p\[D4gj5Y2q&?+iUM]Ah8O+BG
#m,-ne*2jYtIeI,+&54K(jKbq9o(F\$nhQF1(]AV"%4>#*\TL:"]AsA;cN[4,B40<6J2`<koCJ
3;Rq7AZ+0g^?WuD([m^Pd]AL>-lKtX;k>QlEk6NA32PRP+VSo.$+I4_Z\\`Ok1rLI&g;Nh/iM
"9N>H$Q7TrQQ/:VIKG;]A,nOSn'PG,Q^Ylt6\O?7/e36+8tkFB;,)fG-W'r$RE5<<%,PEY[QM
%uYD\gj9Cca,B'[j'qP7:l4$s^=NFoZ[a":3,h3gFoJhn@4NdA!.[KbJL-QrfM%_V_Z@XXa0
DddC!Z)BK52C+)tWOi@slBeUW!6gW*H4;9:7\HQ;^-[eM:^&';F+"!pYP81]AjI>o!B"g0+4M
K4Ij^4AG6cX415LrmuhWU>]AlGa[*o&NM&O%$?KG%nI-<q_3ZfP&.fYD1Nqj-g`PCI8>D%"b;
bZKBUiOTS#[%L1jGNA>\oBuc:mf6n/FQdAEI,h$CcQo1R+Ct'iG`Oj?2DB[UtK\&>fPfY<$6
j`,NbJtR<_Y/;$/8iD2GoA6(2XI$r"BgM2eZZT`hs(>KV#(h:&73XCJ6?_HV"7V,2gcgu[*b
5CkIE<R"[0B-\7.6<HYYmeTW<AFE/d]ABe</023B9&]A=g_'F[*"9:KDJP3\/hDP/,Z.).L<la
2d^<D+??kLG0Z9Q7suo!-5@L7L)7&@:#Y$1=\F'3_K7hp0q=9t#3cE,,n4_pW$@;4UG$JeDT
)bk(Z*$?ZY7j8ml.n?%';LY3Q_OpdB0?"1@Nne4U?XI3,f#-;]AO;'IE`kqbIj0B/hA,l.tkd
1?Qc5f9pWT.VBp?n9a/9%n77U^?AIdNNS>Nj%UZKXi]A1%L[HVgM^7:\)q=W+U1^W^?jnIN&2
:b_!Et!!,(U_G<=[UZTNH*5QWBr1uc5WsR=_TA-N`]AMNdfQXm5?:3q(M]A$18\7:_mC'l?L22
E2FDHTg/aPrA>9^-Lb*5#G>6*hYb*"HMFA5SQO$RXZ=t<`ekFc6H%urLS=]A[MqZM#K(&;G)a
eEg*,a'>\:VdLulMnM^KiOl;hQf;b35\2[\6-UIcpspn#OutUhFOjM=*A8+aZ3FB9SoJP;9+
IfL7Ae-UJCLFAiWK^`ohF"L/LbN%<-cksbUlLu;.W*7.3PVdo;HXHO2qW',d-W?^.qrHp2ZH
Ng,Uc3OPY;)^+n,AmV8iA!L;eMiKc!Y`1B4ILFG7DCY6P;<kjgi?BGtZB)c.6/tlT@$Oi9qP
fNVY;DGf=X9cP;Z<gMR3AjeY0;$Me*Qrq19DJ3(`;<Y>_ror545.^o\k;Sr*Ka+J*_9aO6-B
,Y*`3C-iML=h*ln&V6>/VR'n(O(`<0gn/ALEo>3.4&JG(*dUJ6JcXa4L6I=TI!JMj'+Q+8!0
n=Xm#bh2ofU&+1[36?L1=n(_<nttY/:g-p'hI9sm'Jh1&"YV!H!Tm"Sk<gVDjIGg,_ImDG`l
VQ#'j%%sE51V#_!)A@%D)BD$EK%s.WmTIkT8_7"0N%9nn56I>*t-S,4bun&fWub@R=*u[aV(
[m]A(5l\F1B?Nn7iC`S2`?NVjp#f[t<Cn?O'9;O-*<_49e<?h4'iJFO6O6EW680+WPW66tukb
75eL%L2:o_?`M-?!L"k:(^sKOekiq5jQSNY6o:5VC+D*2ba<`J+%f"\p2_8a-`BF.7annm(l
Es5k3moeL:TK'@=4h"&34AXS8QhS83cqbV[g!X)$eNdU]AalOMGh`".,=4)aBK9.4-A0W&tXS
i:7V1na99oj')t3IE2P$5:hI78foe48$.c7V%0=inVCQ*'4\d&'$3?O]AMRK4-&41FX#oiDi%
>/-_&Ikt%Jek1A!1D5bpZTq0qAo`/rA)C+k0*hJP*)r[GPpc@lTmMAV;dYR;Tg\iPqSDI&n+
'h9kl2qjdAcgRbTQ`X[GhkTm#$7cm_B"(o_VpaG0hLpXrE0%O(UH)YSj(>)=+-/#\9i;?,re
h"67nW.m@ZuG)E2h`nOc9h#I<_@RLei@$;%hqiq]A0fm`]AP"fN)GL!kUge<'h*A!'(8M^Sc^]A
?l:d28)n(W!Ck\)mp_uTDEE$++F?>Rf"_,KB0T#.E'q"Ud>,ln\9@A=a@H8embA4O0sc,%]A?
HeJUOnD;`6Cil;ah3E_Xp)=+l%ba9aE*%9rVX^,6^_9RGlQtbV:nmfr:;.qhWcb8eQB4;bm_
ts.Tu:\'9+%q%j5$<pjUf?#ANkE9q>AFXaPp3hH#Cp9Guo;Ts4n%>LNkViCYOi0C%AW3[[Oo
)I227Y1cPDB+lt@A#Mk8!.fRXglTZBdodKuc:M9?%,M(!/W`K4h4c%/Gm1&EUG\2.;E\oJ`%
bB@8XBTCThLJ'=DfZgmO@m9Ej5oT&aF^+J*m<6>/(VlU-(q.c>:+g((eb:'C'15Ll'W-HDo3
^+).*V#PQ]A0Q!a>;PB2'FZdYk)Y>0`euS@o),Mn8^Ro:qVT.mF4C!ZL:H1#tB22X%D8QgbEH
C.^am0o08\0(h7NWG8CUS!%@YX9^ds]A@$,&LJUEfahjTZ`<6L647_qILBBnV]ATTP=[)8Oe)1
.kUDm$C7eQM@]A3/rN+Odb;f9cG*_&>cCV)+sH9"j=rqRCo:k%,nD:=D8G$X&&h`nMgrQnTnc
rF-fsAcfjdW8o!L3728]A?D.Am!D(N<Hk)QUYI0;A\]AdC.\%glX"r.R\<1^CsPke9NTIl'Fh3
V.NkUB6(-KfG(LX%/1=)Fpb$L`Ut5d]AXVEVg^d#Qr7eDE@D<uhaeh_-OAD0eU/*^S'B$q]A(A
bu5Y/!<s1`tTs(9$srrpBq7+(2<jmaEo($W^;B"up3h970p7f<;0QE:+37G<U+2c>1as5EP5
It_]Aj=PI'(aqhd%Fd#Y`G>W/4MUci7^rA+p.Olr0Nb`IP0`^-_V5P]AfnYbTUDYt8fi_@(B2t
86O`1(ubDrOL(M)XrOrjV[Q3h3Kkqo7l$+bm[9mIhS$T]AFG6b#^_%X84?i^Pcl&s&FCZDSf,
X#rgfigk;/9'5kZ]A\-Y+Q-JX9DE"d-+9t1N0pY\iZ0C@OTp[kpN)eWeZ>G!B$F1_$n!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="744" width="375" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('T01');  
filment.style.background='url(../../help/HuaFu/xb/etf01.jpg) no-repeat'; 
filment.style.backgroundSize='100% 100%';
filment.style.zIndex='6';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="T01"/>
<WidgetID widgetID="dc4214ed-8a4a-47d6-a154-7dd47988606e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="T01"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="d00db552-5849-4131-b5cc-6c895de129cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,1257300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[10096500,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="1">
<O>
<![CDATA[完成率前三分公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="up_branch_name_kh"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount count="=3"/>
<Result>
<![CDATA[=$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:800;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="136">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="160">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<X%[P$&a5dptJIR+3U]Akp.i]AY*gdi/W*JqN\sc'CJ'S5L)Y_U9kg?OBW_B"B%Dd,>2k-q\W
@1Q<_A7$B)8=%W/)i-I+C:G44j.3fD!m@I=M%t>l5)ppRM,kTDRF<q47,QroEFM7<uAJJqCk
*dm)QW;c$:ir:3_=YgQFP:qZ@I4Qs0.M4`fL:(D/hTau@>R6>TNqYHDe]AC0>l)P9-+o(;_=)
qT<%I>_A)U[$n4h]A1@@d"?bX;fV`%D<'AHmKdc)bYiEYiU4:jlVBZ_^:nLq/MQ03!f`B-"9o
%F<ti)3Tk>B$GbJoYO^B1c9',-QdPCM]A3ptYGn%G;r5FSof_=aBlE8_@`U?'ALY5"!.JtF_C
q:3fF5HOcOe8jB2HY2I;0:fo$lcuCTY;AF=YWLfffQGrkcO3i\-)^^alP(%>9rKPuG\2//V:
!NlZE+*b8l#L=r,XSq]A#$[u=>F7X%rmi7oX4RIh;:?jT98ZX"1F\YkT&9H0A^b#h]Aob.S_HV
6rJpu"R)7au!eO1nU#"C'W;;W\d]ABr^Pj(p$2jh#M=%?`>&2&It^_pJ%3.s?DIibOU!Dk9_,
!^;O>WUR?L\5pifSPCQe#\3^5`"_gqgf@\]AO49G?Lc;)HQ[OV.R`<R`R/#-@7L^Y('p^dFU(
3X*tJH#$5P47KFN=h<I:XN'Ue<abMtV8/0"3<-PM*KQEC9('*OXHW"5Kt.)QEpeP=GNX6[@k
iWq9'MrJ!bH>,A[^3#-=^G$>+F2=?#FFH[r;(4cO]Adb;&\4rYQq6Co)1ZQu<81Q-T0st7:pe
)3iQL[:pYM%i-3/gJ7jr[^^b1$1@s*Y*S7OKkGWf84Aq32H`Z+2YGa)(CE%ggIUho7KAjZLg
K?TC-XAO?[%@-iM+iAn4MP2e9]AlV^>JH\4l$+*Pe%)@YP^Yo^tH;?HuFY?2t*5`_JR0d9^d=
t`3P8UmLR-tAQJ595R?kHm9TpRqq3<`P.i"u54Ss0?+R\C_UI^(";Q4$3%AW&tP4^>2m17AG
V#TcGKpo:rF[QV2WAfSOni_T+/rHOA^:7,_f3ZaUd;64;>,J1Q`s3jbD"8Op"a#Qn9rKK)0`
5]A4:#<@qE?Gh^b%/::,??h%"[]AN]A2YPk:O>ZapRB?DKEPPb38,'LEUk]AGDLK#qRiu0=ZAor3
;O0cCCgL%X=Xq)OGBm5FNPqO5@e6%`/#E=2U<:?^X7/K3L6%"3*1a2Kf&a?\5EjM;<%&+=du
_(R?mEaq*a,p7Jna=MNc0Y`<EMAIL>:7oiCn&;.\-]ASetAo>JiXK?ZcYN_,f5<>A*?F
7l?$45I8[JgD_'Eer&,[\tSD=)?7,kc&=QMLHaK^**S^JYU6mNYFlZ&?:4UJC$Bh+(0fhZ4X
''Y=KE:f]A"etqZ'bqHtTS/imYAA(rU1@)9c,P",2e(&.Y/6_1,!C!8%cc[]Aq'&g4,IcL;9G[
S]A"Bd^11/(nOm#kr1N.d-2AYAcq5KHL>A9rneDdQb_H*jZLM!^;0%_YIMN#F]A#`gQj&7?3L=
eBFD?aa+kV9'&g`>;AVM35s@Z=WrJTKR\!Zr,b9/Q-a-DOo$$Ke<)<0+[V/SpBo&GRK_]ApKU
]A@]AFu9X<d&C0D2<1?rVq!&6iL%R;'JT4Hnhcq7.j(3q09N]AAZ,rg59n=2$s7/"%?luota&o2
Lj]A_HL;AGe6iHBTC,:KSNUpl:iGrOpa'1/-gCuY]A\>3\2qB^;A]A+N*\G*"Obrm//Z1f`WKb^
WOU415e/7XhqLt`FDr18pr+L@$"g5]Ahb-\T"G*!BJngdorY9@?dE/Af2;FO@U>L2$pg*5;[s
s45`sS\aUlb]AOA^>oU&Wfr7:1Skm[qoFR".CA_'2VN,]AI$,V?MhHSd+B'^@PNTK=2;(e3Ya,
<Y?75k#+&q4aiHl!!`E[6>&DuBs*V"W3Q]AUTpPf3<R0O$=rgi-XZni76q0Jf`80@k?o(^uJ'
;<A:1\KZulNbmdKqSsOW6*[1fO>b??jiTp/Jf%)/FXC+V>B>7jQLul%(B.Y,.Ou4_MIN4m29
TA>-`Bj/oAFV\E5]AsV"AJb*jXjUIRBUE+uE\D,LnVKDV_#$]A?Z(tRe_=;9X%VbRf_N,bj(Op
<b$%#k8n-[T'/Unh#HH6I$@d0mos,p><X[-eZKkm-p9K@UJ=9%X8Dg6<hZJRoSieDr1Hq='V
Y*'92Wr[6f8jbKtFW2jk"nIne49l,UJ^Q6sVdB:I:c1ErcUJI,YYLBm8o$G[\hPm`\0M=Xa[
;+l7a(:K&D0&i*@&`aH:1?pQ_ZEoA0d)`>eT1Co;-eCnZDa]AKY?\8D![\l8aIjYBY]AX>Mc!d
Q>=9!RQFBf'%^_"!fP)pE;7q+FW>4T93VIp<eE0i=\?/H,jjW6"f6f2n!%EDKW$B#hCW@nGf
P]A((-H"fi;d\#Eo.)\i42%k<e"-Y_OE("6HXT"+4jb<*W9GHMS]AQ(BiKYMmSD@pp>r/*ufC!
?V2mQScXqi<u`c:ZK`3`KF+)c/Vn*ot1%6c\S!As]AIdruJb]AuliXMC]AAd]Ae3'iYg`Gg="H!O
,WF&tQM/r:"CBFI'rA_e[Z[/J%<b/#YO3>!n\O5tm<`Z:RjJmdO[]AYu`Ie""e>#U9I\VGH3r
4HVCR)OMpM"]AaPZgq.)b3\+ot@8_Z;7<gg&:'2TLSSfL=n#qRBMC)MPfs[4K&bu>!HBD8NH7
?Z3.YJPL-_9\M;9N"1iKdW))THrQ:[9M3:9_H3,j^]AkZT6^'\u'_V1OK))Sh5`BPY/1OJ4))
CReiP[QA)6;4bMkCMNZ7+]AcfnWBb5g_7c9WLO9O[eUmh[T?#c@B#S?E@f-e?(OXADOD2-#q(
>%(AOu4=_9"8.u)N4Vi?977^kk!]ACUebdpo-o"A9WKNOiJSHYEDC;>6RFNk&uQ"#GSS[P,Lk
.hMbTLl3U)8]A1sC=)g;Y+;O*%>@0:<'Z&>f6%KPfW"cb2cM!^$mb@X/nlT.^fr8-8_2$snY*
$Crc-;H:1g&3bdZBcU36mb&+s2`S1`io!RhR3Q;O^(p?=Hjj$XI^KbA<JD7eVso?AN/e)*`H
=_XhAmf)b4rblQ>[Li$KT^Ro#n<`^NX+0Q5XlD27uSOVqBr.W!kGR&ZbZ^;Da+[V+0WF71oN
%Uth%*jGkj#a!Npamh@D3sQ\H\F*64Ilppb*$(bDTJRZ>6HnZP7Hs*`gDe]AZ1R*hrP*TVaTY
Tt_Z#U2_qjf,>>6ZB0l=o2T=FLqTc/1FY2Q<=#+'r5o`g)>TRW@d5uM^5gAKMU.V-P4?0I!5
Qp1NW$\('c-)10?OrD$ra%l-<bstaA9YHUV95#d!>Ra=Ck'1&:ic;FmG[S,J4]AV)8J#GT#h1
,mt$(E\IY4*.o\lrN'/7uebCR@GJE9cBEWFfZsDrP=QWiN#VI@,m&:oU0*_VCUt>V57l-9R1
O%rKVI$s%Hmn3D5ZIhFB;-=eJ.hUOZ"8?[5]A21eQT,eU:R<q&sN"4%k^X`F&$3eQ-52*,q@A
F';7,'_9E,MghC>mrJ!IZ!a+knTIWRTMBA)E9>t=t6a>+.7KO;kQs3+[&uYnIDc_R*`8!?Sg
7u'J).tdLJJ1-@eBDhBXubDDLk7XN-.a^YU$F2eE.@&H4Jl6ZuWLcZefu7oU<$fob4<+(>l&
,JY]AlGL,$ZEiW,;ZcP>@JU#a]A]A-BdJ"31(a=cU.Rg;@94W*8'XHGi?0lc3GkiVWHu4eZi+F=
ilD>rU;:]A\OdXBl9[A]A*#cmQYM#T`qREZ)m^dE`IN*ce8VM0:i5f7F*4uN"^R+B=8T'GoGC8
BF7a:a^:r!BiO_FkJH3qb5Yke4(?:5)@^C"e/c:&oV^"tWP(cpUEr5(!;)fOWJ\3QW<PO0;0
_b%7-6BCpCn?_bl>Q/FEHEeF7\0p?_=gU5@[6fl4#5Da)Y*d-cml9>\!j7fj#pleE+!p1:hQ
8F/5(lp`+o1qUf_)':?16WA[[;i[>008i:F0@DI;CQ8nCiXr?bc^Ip!@a]A5QY7]Ae-rhrPiM[
\csZ9b4Dr(Qk(VVDt%(iTC-f,Tm8A$BHQ*g@XZ_E#I[-6Y'l,&E%1ggg9Qq<T6s3%\(j_%cP
T\>AE:hhV?ln4\V]A(c"+%LseeEUXFI,I?43>E[U/P_gCYblHZD?a3bEi]AtjTk)38UC2fb2Ye
]AMc5KIJl4bh0Vm1V9G0EVP;eA/A]A\\!EkTG#3#+4UhF@u=cEO&9FNj3jYc/qg;.o:M)pElE`
H;=Y5:WJKE@1r%fKW0fTA3_a.k0JpR&A7l1([9@NRpABTZqs4k&,QsrqW<8%]A\%a4@5\Z"qX
MQR!3"37+dTsm^*G)Kp3?_gV%^a*GN<Ke<iA'K9E4*DAh6;.!bHU,iV_eGJAf8>qHlRTW<)u
HU1V7=ao"oS<nMGp*d]A#9<^'aq-C5V`T>+Om1-QTlZmJb0Y<E-IVd0P[!B9?*W_:rYt%i]AQn
,/KXm@bo7i%_m)Q'jo3WFiEFjYSK</YR-:/ET"4JSHf>>"B"s(FH1eh\NdbA-dl^W!i>2OT\
$=9c^),SOd:Kd_2u&@+L@#%lHil<3nq8o=OJKt_I5:_i/dU?B_O3oF`AiDuep[.'dgi4;0]Ae
&j/u-h1mf9U#mN2`B6H0sf7tF6sjmf22AX8#Sm&3TFFTUeU=>X>CthVPS4*g_RMd;Iqb1/:1
o98CZS4.W!87'M&"/&&r:n\'YQ3mrnRg3;7,ADkub-#BmM(cQkA9oHCNmeiJ"PAb0;I0>p3g
*uMIB'32oS)GD!Yl'BC#A6h.?":l2CUXo1XB%`J3ZOI#RX`KH<j!ccbN,#4)\c?h]A([1uE<[
Dasf%pU\@WB-I_`ka8kJ+E;F\gBr"hD,k$+n"VZ(?/d3iM7#gaQ5aX8[&J+-4_noq4hI=/qR
OpZ5+Ubte9rr'mI1s$aUV\&t1?S46OjB]A0Ik&,oY:$IX$]ArZsp[SW6\b-!hfSU7tt6%fWSUn
j&),L#9W7]A_StrQ,4Q-<N&,13W5_/oTc>P2YI)ij;L'mP8>g\efQ"N>V"]Ab.'HUW20h.EkG!
6Aoe%8/%^=Kaf8DgWm"n6M!OZ*Ye#:q.a*4!M9Mc)YaWEsh!2e:9K;A^fqn@r4'M$r7INg2V
*LU[(+/Raa$bO6gVSsI'(m2r8T%hPD6l;RRIJYP^s,KB*oSXm\^E[f+NXdc:e@[Q%q?)oiD5
'9FH:e0ti-8irm<=Bq3.b$+R+HT2Am-57E/Y#ba%CY,5U'heM69)kR+lrGqr*6S"rO)[Ba3i
ch)uX*<odKp?1k/TJ_b`joh%<$PsGnJ/2X($o)k\JVh,kH46E/nke\+ko"a,84B\4d)q2t-:
L;"c(+Qhb4++B@gsN8?AL0"'T\HQPa@sTfa$jY'1R@;NiAZe&aj,UPA'!aK@)s[t4OsqNH^?
#(6<1\#CE_0V<g?V-:/1381>hi`('G,#G+`=S:gR2Q:0VY"oMN([(TpN9,6/d,$2Uc8<M(3)
>H.p"ho+lXlHsQHXmkd.mb[4to=YdMk!1e,blL2KK\*E,"Je\\IrloLn0UsJ!$q*oF_mTfE7
W,u8[6QrU2QZ+^#]AXSA5uT`T`D,<$&HTkIkho->jZm=l/i#M#YZ3.<rh![aH#D(1]A\)RX]A'$
0#3dp=fMmdlo6TW,"Nt20@%o!G6EjQkhZ&\`m4;m.ZUobbqC0(3K`_O48ZBV6Y:s*cgpHGD4
OIYRBR+D=8VP/9,%[P4\bcXEN9Du)MV9uq\Hpp"l3X.:W1:7@b=-!o[Cgc#j_ki>+S?ZV_Lb
1$kp?$t1ir@\26K*4T<=%PH::D:)!mZMX=7/mMO-]A:[.n"&kmeSZZK1j2`usWAB\/091@ei3
:h1q?:F#jq?ctDnbGMjdo?Gfg7aFV[)ME4D7h`9fc\X5=,^\^+T14Ec+@'j'5OcW^dR4e5a>
+5WfVrQZG)6dSG(CXG<_e)Yc[ATTUX(SY*_d%=(r)K`=B8\jebPH@A&7_Uh3/Jh=U>#UP@Ag
TljIf"WI5F030`pG,1D3Wgs6.Aj3k/B?l.WsX88K+am-760(OY=[Zpi1d##O\E>L'uN4-6XL
!k8!l;URM(ola0Gkp(lH"oZng3'9R0$_-BF7%P_*gKHc0CG"1.5_RT?T#:Y:fOp<QstUP=BY
(<=blr<U;93tDMp3C<LP"HHK.a:D=`fnYpPdUX7*VW[Bf:(^**GEh!X!61,m8=nG!;nU]Ais?
>ISPcqN"e8]A!\'$3-e[kU7rc,qR#6Fb1=68C)C"+3`dLnk]A&n:5VrPm[:,JJ<;q-o5r7)$<I
MC/:3*R#13g=9@i=(/_0Gk<Mm7^u9,^\tY00oU00_onHDj5NjS/(tj_2U$Tug&"UBDF@=7:G
ho2ch*qnf%p=M<XVkd,[qD]A$lb)'^XF]AMMS.aHZF[/W+_^WuE_.2JGVDX[[966dnMG?3Y;d4
F-rW;iG?Sb_MR\kuXb>PPN1R_94p^a2d/@l2+oX?T-.Yj1+l_%&K3NWE6+r-0]AV@>N$@DlEW
]A6$_J9<@rZmFek>aRH.4.dp$LHDckh3LP_Q1GcO6!]Ad.@pJ3lrfAZb5Dk$gE8_8!@+pC\iNs
MX:^A<D"esAco$Sgk^n;RN_h[)#HSF%3bC@+1l<Xf_`Z&&^IR,ahT8!YV3bK\4IHN_td%UI"
]Ato[f)t$WB&4.@N5,W2uYp4RnYFf=1V[Q.c:UHWU?j0=HgKp"tN&>107.o3.f*=S1SID$:\i
@DM4g2V?Cf#Y=MG/X-K-%m$Ab=)oB>*]A8G$Xj'TK&E%n'Cc+>>QjaiIO^?eeY[bpFB/CO(]Ai
C53UU"GK5f+WWJE6?ZE\R$":Dl4obG"qsJ6a?KMS<TAbHo:C/Q*;/U,+<M87Maf;a_91+Fa-
S`Eln1jgg@u(<&@p8Koj6oWlrjR]Acp^.@HYq5--%&EM/^tMHS7r%Lu49(aZ7O;/b_[e/&T\a
0^P/PgH#!_iLh$[Eh6[c?jPR'9``pO%%Q"gjg@/!3"1L#"Lgg<,.nB(_G/C,:=co(NL9BF@@
AOcEd7gAlnlg#^Y16Bo]A_#D%^C6d8bmsnlM:sq[\=e,(F1qU`c_ajBm[`KH*G3uDH`Uop-h?
=R&f7-4(,a$8i[[d*NUd-%4/^0TV$elD`=*mn,9N5^9At.VqYnTBtZ),eQV[0<["DASX)F0Y
PtRs^SNr:LS8pN'Vh+6lbaH*">/BmMF'*UMVbep5`C;5b!Pu%952D/Xa'PMm'H.+b:g_r7c0
"L?"Z]A@TH@NclR7t2&U2Z=jH-AsgaI3sW0[Fj7OsE\?V+&JWWGA0GJ/O6A8#Vj$j(RPG@#st
L=SmkkOB@CF7Ur)4qR;u&(5=:.CAgn#L3)qhWi;eek_.eiRW^KbY/7:dZ%XX8A5Y`=^Eqq_>
%PO8u_*$>3hf#*3R[,UXpU\T6M[<C2u$Rln.E#F\"jA/5/W1ZYt^W(d1J>[Ccb`#l_CYXUtG
>1)4CZ&L`So4u`*//JdAlD959Jf]A*KF0THqFh.hb8k14]A'gq7,(e4JQIHs7/J"JIF33&a__D
*&tfWH;WqE(45>onL9C1uEmM0&19>^o:70(6iR\i6n@P$;Ga`Za#kY'$f,u;W0g#^7V"\r84
a,/u*-6CYKKb9t8Zd^+)S#!E['B3(Nd0UXD=Imdd306$>umj@VNO2RUT"C?gkbh=u_/dGAI+
bTAP9nX4BX<i!Pn;!(Cuj`nVF#LBG#Wah6Jf*SF_U85<$6(#:KSo<;n+KuON).8TRpn(,<G[
U8D>]Aa&sNZ0WBb(TLWBM!If)oI(_=hU`[Y:F$aRZQK[l'"EsVAP<uq5L#7\n/MR9Kp-Z,Kod
?W]A5<>bJU&;LHo5qmHh`c\&pA$f2;J#2&/o0m?B5'n`KBZBa$'JF%3B#)3JB43StkSLj<ZVG
noa.o4]A`la@\C%moAM7=ji62CUdI#(@8/]A/:4c\qJ7l6e%YF"@KV\82p7qBE/`q,>bA'0Ng!
*f(Za1'%SKN<*?qjHUk-!%ND:YJ^HS$e.%<t9Po+=!0]AB6Xqb"[Vqjhl>_:C=,m8S/[</b?7
Z?%(6"r]A8gGl_X%1("Q%+)gVEM=4c0h6G=.Jeh?M*3=L"`=S-O'&\DNbP:tO6GEk4do'53Rf
%<q8q$,QGU+5-*5<36+T3gLrRTkIF3]A2oW\,3q.1"`#Ehb3.="j\E]AXGe603AG:J5l6%45[R
P>Q=mZIHL;TMs9mHQsR'P)+\^:7<B82Y,oO.3,5LmBXq&u\eas,OTcB!Cg1@/p0D&1jH/s"d
MPSP-l1I4A]A/r:T)?1jp"PqtI&r2N%2mJ`[qe$<.LA88XOX)fUH$DP3<gX$]A(cl#r0F_iIdB
Lq),9,o^$ttuIoK;R`=oMA5<tMd`S_ljVU(*a'!;7Jl'<POn#r)u`*1]A)Kb+_c]A_l)o"m,ej
9lZtQBI&)GL5M*d+Ss6i\VEOq['s;2,l[*q)?i't#B=iOR=TP-QU8_5?8WK+kk3A`NjX+g.=
cfR*=D-d?m]A+Y&eM!G9rJ-X_D?dm[W]AskUTfN\#GZT:Pq>\T>sDaZ"(Gh$:9Lo+@)3mfhS%=
(PUF`ANJmtB%^_V@Nr^"t?JDW]A<LSj-7Ej4Kpn/cT*ZN?&%CUfa?EkB<O5a:2Wpd)[JEK\P@
!0,mZ7KFskUj,^\eCJFBql<4+e%)FC'1jOM\EZe^Vqh>!R_[r-ZLOLo??+EDh,QZH.#*8_(<
acal]AFmM#k<Pj!OKD"m`TtDdihI3:^RVmGX6kPr::&4bnieg8VUpK[tr^:tPqhOIgW5,X*"j
L/-C4!p)mO2JA6$DmQ]Aqf5[G1N=AU:F&fKlF/&FF,4k;R4VqFh*osSIfm1PN08mh6f(Ai(r^
G/cP@p=)Beu#P8dh82Ms?hfdG]A(Jm+*PD)*.r[)iWh`)&nn11$W_ZR#FV?B22#oL5*,AKk7M
^F(W(U\%>D,#R\0IbRB6Op[UCm"&\9#Bdrs)YcF;PDeYk`D;")'<.N\idU0eml7#f)@Er7Ub
-Q;r$m5%&fUE3+m9UMKCjkD'V0_g95mPF^jZ[P*^GL%S>0CYm-Yl?I`Y3IFj#Kc_Dd?[LE^j
6`n+G4rDKdrE(jY)KUKWEH'L>;r`r.')C\Pb+hDg89;eO+6kC68/U&s3.#PG$JeO8btb)cJo
N>!KJ9>e=W#":R)W06Lq,?AVT@<9XL_)C"#LjYhE&lmo;j!"#Oe!UIcX11M1>W0#B1VRG#?8
X'sZ,+mq1Ca+p:V@r5LOPI0CXP5<fqq`:k4F#N5s:F0D3%DMk@SKjaftfBgX=4):".V<O*-8
XWqZp,WW$6qM+@95"8glmgeIfi7,"QMU,W+#VMUOb3H\3/:B0$6iV:g([8p]AXGR^gl27NkBi
#5jQCN'`c_)8_,fAIQPK59uV4d9%7JZ+!2"FZ03\VJi3j[EsKF'j)E'S7EVmG7HqQgGcHh"t
H%`Y=BI?Oe"ir*WkbnI"m\7@<DQ%Xm#->U-B`f8+4\2<#NoNOVG_,H_X1LhjpdiYlfr\40^J
rdQ9W.;[El)i@/H%t<bag>:Mcr1m]AjrrE~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="350" height="71"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="13" y="234" width="350" height="71"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="400"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="T01"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="T03"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1701741677265"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="769cf79d-617c-49d5-9451-ef16aefce558"/>
</TemplateIdAttMark>
</Form>
