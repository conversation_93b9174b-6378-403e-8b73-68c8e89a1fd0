<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="body_yjzz" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-12-18]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**
	SELECT * FROM ADS_HFBI_ZQFXS_WARN　where　oc_date='20231113'
	where tree_level=1

	SELECT * FROM ADS_HFBI_ZQFXS_DDFX_YYBKHMX

	

	select * from  ads.ads_hfbi_zqfxs_warn where ds='2023-11-13'
**/
WITH TAB AS (
		SELECT 
			A.AREA_ID,
			C.AREANAME,
			A.ZBID,
			A.ZBBM ZBMC,
			${IF(level='1',"B.ZBDW",IF(level='2',"B.FGSDW","B.YYBDW"))} DW,
			A.XH
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		INNER JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE CJ='${level}' AND PAGENAME='预警追踪' 
		AND YEAR=SUBSTR('${date}',1,4)
		AND B.STATUS=1
		ORDER BY A.XH
)
, RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	    SELECT 
		A.ZBID,A.DRZ
	    FROM ADS_HFBI_ZQFXS_JGZBMX A
	    INNER JOIN RQ ON A.OC_DATE=RQ.JYR
	    INNER JOIN TAB ON TAB.ZBID=A.ZBID
	    WHERE BRANCH_NO='${pany}' AND TREE_LEVEL='${level}'
	    UNION ALL
	    SELECT 
		A.ZBID,A.ZBZ DRZ
	    FROM ADS_HFBI_ZQFXS_DDFX_YYBKHMX A  
	    INNER JOIN RQ ON A.OC_DATE=RQ.JYR
	    INNER JOIN TAB ON TAB.ZBID=A.ZBID
	    WHERE BRANCH_NO='${pany}'
)  
, DATA2 AS (
	  SELECT
	  A.ZBID,A.WARN_REASON REASON,A.WARN_STATUS STATUS,A.VOLATILITY
	  FROM ADS_HFBI_ZQFXS_WARN A
	  INNER JOIN RQ ON RQ.JYR=A.OC_DATE
	  WHERE BRANCH_NO='${pany}' AND TREE_LEVEL='${level}'
)
		SELECT  
			TAB.XH,
		     TAB.AREA_ID,
		     TAB.AREANAME,
			TAB.ZBID 指标ID,
			TAB.ZBMC 指标名称,  
			D.REASON,
			D.STATUS,
			TAB.DW,
			NVL(AHZJ.DRZ,0) DRZ,
			D.VOLATILITY 
		FROM TAB
		LEFT JOIN DATA AHZJ ON TAB.ZBID=AHZJ.ZBID  
		LEFT JOIN DATA2 D ON D.ZBID=TAB.ZBID   
		WHERE 1=1 ${IF(LEN(status)==0,"","AND D.STATUS='"+status+"'")}
		ORDER BY TAB.XH


--		SELECT *  FROM ADS_HFBI_ZQFXS_JGZBMX WHERE OC_DATE='20231218' AND BRANCH_NO='9999' AND TREE_LEVEL=1 AND ZBID='khsdzc_20230820182807'
  ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[预警追踪]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=pany]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.user=user;
window.level=level;
window.pany=pany;
window.w = window.innerWidth; 
window.url = location.href;  
window.grnum='';  
window.parent.document.getElementById('YJZZ').style.margin = '0px';
const elements = window.parent.document.querySelectorAll('#YJZZ *');
for (let i = 0; i < elements.length; i++) {
	elements[i]A.style.width = '100%';
}

window.bakdis = function() {
	var u = navigator.userAgent;
	var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1; //android终端
	var isiOS = !!u.match(/\(i[^;]A+;( U;)? CPU.+Mac OS X/); //ios终端
	if (isAndroid) {
		var parentDiv = document.getElementsByClassName('css-1dbjc4n r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';

	}
	if (isiOS) {
		var parentDiv = document.getElementsByClassName('css-mbp0r r-105ug2t')[0]A;
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[1]A.remove();
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
		parentDiv.children[0]A.children[0]A.children[0]A.children[0]A.style.background = 'rgb(0,0,0,0)';
	} 
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA3').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA3"/>
<WidgetID widgetID="cb1d2383-2abe-4ce5-bdb6-50cc01fc0826"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[400334,571500,152400,566057,304800,304800,952500,1152000,952500,304800,304800,566057,1152000,1152000,1152000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[190500,274320,457200,2743200,1524000,1066800,4099560,274320,190500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50">
<outerBackground>
<FineColor ver="-1" color="-657670" hor="-1"/>
</outerBackground>
</CellPropertyErrorMarker>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:13px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[body_yjzz]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" cs="5" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:11px;height:11px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" cs="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:11px;height:11px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.parent.FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="3" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" rs="7" s="3">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="4" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="5" cs="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="4" s="8">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:9px;'><div><img src='../../help/HuaFu/sj.png' style='width:9px;height:9px;float:right;/'></div><div style='float:right;height:9px;line-height:9px;'>详情</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="zbid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A5]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="zbmc"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C7]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="status"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="zbz"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=D8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="dw"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=E8]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="reason"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(len(C9)==0,'当前暂无预警信息',C9)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="px"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(F8>=0,"DESC","ASC")]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[/**
     var branch=ObjGet("get","branch");
     var zbid=ObjGet("get","zbid");
     var date=ObjGet("get","date");
     var pm=ObjGet("get","fgspm");
**/
	window.parent.FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/预警指标穿透.frm&zbid="+zbid+"&zbmc="+zbmc+"&status="+status+"&zbz="+zbz+"&dw="+dw+"&reason="+reason+"&px="+px+"&date="+date+"&pany="+pany+"&level="+level, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 68.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 10.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" s="9">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="7" s="10">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="11">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="STATUS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='color:#"+if($$$='正常','6cd591','DE554F')+"'>●</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="7" s="12">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="DRZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(FIND(".",$$$)>0,FORMAT($$$,'#,##0.00'),$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="7" s="7">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="13">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="VOLATILITY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="6" r="7" s="14">
<O>
<![CDATA[近一周预警次数：0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="7" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="8" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="5" s="7">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="REASON"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[UP]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) > 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[find("上升",$$$)>0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="64">
<foreground>
<FineColor color="-2206385" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[DOWN]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$) > 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[find("下降",$$$)>0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="64">
<foreground>
<FineColor color="-11426439" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(len($$$)==0,'当前暂无预警信息',$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="8" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="8" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="9" cs="7" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="9" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="10" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="11" s="15">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="11" s="15">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="11" s="6">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="11" s="15">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="48"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="0" paddingRight="0">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-9644655" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^HdTQH3DIo#,V;kKhk#V;R:%".4H`L1Nk%'m,C166kmOQ^p=c1XdWK7LaV5B*;qZ`62Un
XZ^W$nI;L^9f4-jY4*AY/(Qq>C9)Ib3*lFnL4TI[tQJZeY<@Fe<`$E:0h+et70c/0uUMp!h+
D^I_"6"K_Qaq+:-HV72m9-$AnEWT-KR@e8a%A)LE@+Da9G42*jm/)8\NU@Ff:TKbY2oY@G;0
,aXiO!BBDmQC7;^A6#rG2`0pZk$uo9^.LgX!.$n%il=HKEZH.(K]A2r3-rDQUqd=?14-2RMEt
ioE4e/^q1bNMm!,IW2G`JKNdGr3UiNF]As22s7Nprm-!1gJ,5BG.-UMZ1'1U=a6*IdYG'6%*r
e)G@-5h[mA97q[nQf95(JEnNMXIW_VTi0F-X"NY5pj[Xu)ddYX%pUIa==5D;ZbQkA5=OWO%7
oDtZorYJa`G,PiLkoY@.=1&Icmb[fS@b\PF)3SYAtYmm4RnZ0eRcCp'tGC0*SL0]A)9-%A@(@
;_tL(82b/M0"4sT'/I5V?i<rWS)a^akBsp#o?9G3GC<hg&L>N`&'R0WJa<\Y%cR:YRn'T7#a
ZmcRA\Z`N*\&r)S4E-Uil;qnapcqho?DZQP#'0srM0W4_R92/,oAFlD<&s5*Mh5nLcj+CEn,
CJ72ZlI@WuaJ:BJKFV?-C&J4dqX_BFI;qSVAp':QGA9K8t*d)j,2E0pM+^K*#oZ_(np"7`EX
*^iVMPGnM_2[*[fELm#J6oT`Nr/>:YOqDJ1q(VZ@%tbu+deP"ADIu$0[LKd:`^o4A-h5X[$O
Jh>D@=s*Z9QQZa_<qf4GkOT^Suej5,[$[6Fn'-f6M$"/\&q<6rEejYYQ`id"^E_bIBa!jN%n
#%C/7MKO=>Q`,8SlaYZB4Yo`4!:k0#;`^Cd:m8@L.\,;8Y&.7XW6&?\)[7gSHO6bjsp>='Ua
YQjL#_@i9[\SKCM&&;XS3lE##=BFWm`eXHLNXS<=61:Ci01SLMAX_nL,LH*%&Xc:(4l4b&^m
4XfA[oo)jNR)KY=I*&X,]A_Et!Ib7H^5CUZ0d.S(Wc(0WV5T"H";B/KUDqr5K9p7\o#6&m1-r
[4(Qj8[E20p*XJ@PcKdfEbIq4AL\*e4XJVKBW:]A[O3DQ<^uN"T/U;W6MmI#L=jGkmW-_FLc5
rEF*nRS`ZN$iYO/6GH4'ur<k!epVYX-G4Cs@Tf?W#\"2q?5TI_MeW3SG$6Q$;sLS7FN>gGX2
a;/n;[KudG^/:]AK:$1Y5LNNt`/+ObFunO\24MhW3b@`r(#AN2E6%mce)(0E1_$rI/k+mpC63
.tF7EJm.ojAG15Lk2mubdo!c%DPf5U#X#>D8R%Q;.BDY_j?PT,-Qd"p[fZanO':(1:J^!FR[
,i.spd%>AlL7/8KOA'p.`MEH_!UF#UBJ@KA)r!^3d`q`G`'T"_uoa$Uro#!+&h2E`t69u,kS
52Yo)',5/"9<ICQj6S&Jj"d<U*#;@IAW!ljloJMC\nXIdi^K)VPPa&roM1DpM-egK4iQA<+`
-m#\]A4>U(=_\VWfIX#c&%<HHm5FqS=a^:2#h!?OZL7tWRQ<0B'(.[J\P_]A>=hdrY!j^$\aC\
W[?Z\mjo)@="rpB$B$=GNj:j_:Ci:7.^!:=$5-hgnZaG4lR:]AYeZ]AjBd_Cr7b"h#*2i@D*0X
QM2lQ4.c+i/36=lcLWn`/.9X:?^8D?9WZ/(PfhN\+cFG+*`+BbjXSa[g$ip=U=']A-"kGBLS4
_ss%.3EBVH/-<175q<jo5o<?Vp_7L>.?B8BsTAQYR]An+7XFQ$;r'eqG_^6R\:Vk-]A*u(='K(
=D_i+R%'+l>9!r7K"W"&!0mtoW2Y`09Cq,.\b<]A23J-6&0iqAkWuXEBB)E=(JHdR:\N2,o<:
l/`YuuN#O"S1>Z7K4S]AeVY&AdDQ(5'F;[JRjiiI^o\%@Qb`29;KdO'S&(lSPJ7r[lQEU%!i6
.L3g]Aj^9;L>ourPcP/g&B,MZIOCiZH*Eq9rUR;`)G)88G1^Q6(/Lfp9(/;<EMAIL>.I
A<ZeNRPCO*^j447LV;;AmW;m$5s^ZCAi^,Z.ro\D,N^0!*=C5/s>';5M&A']An`hNSm1o#TOC
5SYe;0'?Q7"dL4A`R&Z9`,o""$?'5,)HC2MhnY:1=6VisuPXO-2OYNV*5LNnQMr'^GP#"o>[
!`6hjAFoUt<Q?fk!g0c:0ac)o";Jd7bR$["$m[=do>pOMkqKBn(=m#38]Apc6.Oe$*42DH_SZ
PZgH.b)FM01)-DR<"-FGLl7O&G[tkbHGeo4mWW5&;j:52.qoenQ9iD]Ac[28T2LNAR(K)D7(P
\M1FHOaF*<,-%\5+G'G)M.WOd.:M-#f(M-G4V*Q7MO<L&M"s>f$cR2HOqP\XZ=#/L`14"A3&
IN^@M#=FT@O*KjM&Og3CW8!+C>HYIq@10eVMr5$(0SSq$;3;UeQsIc).QH]AgADH'1=kfn'CY
8hkPd@eX0D3B*#1^]ADYZHs,3%ZG:XJoJDN$^b2-(i%0O?N_=RTSs9fXujWElX-eTYYid,+8W
Z*b'o5UhSYpanQ<cG]A&j[j8rH$XfqJ9halB(_+b=&KQ9S&HpYAkr#Gd(SU:\ia-uREB<*s9O
AATaBI*G@9eM@LdgYK<'<Q_?W1ch!VOj%cs.*Mg;Rj$=`<'m_BbNAG(iaN$41#ZRUr^9,i_e
s'>EMH#0g%WTP5)9U1S_eqZZslenW9Qn@+4p4bulB8p9F1COWX1ZU)LLam9R@jU2!7YH<'"_
>e)5bc:Q%VJESa8lNB]A"3$HpACIN(N0(TZ_Y4*J3XIlN6.IWsn@+<>C6,`M_6&F?"cpgA,o9
I^?18l27"pPiNOtrE?+:![&1ET(Zd2I+94AmM!0tP,X7>+-/"gZ[H5OY&0LCl%[$\S(OBSQ;
$2&n#(u1EY;*rBH(6?,e@[]A)eicC(S'73k.*^lX::'-+K,qLY;a+alNQ!Ea3r'n%kA[)$.7?
;H1b5"6]A\p]A.X>O9P30O#L<qN=)1']A5P9^H>:<bm>/+0V1[6oKq,3YYAG7s!ddHVaS;pR:D\
H20]AS?kJmMR70+h/h^IVV_q61L[(H[Ycoj`7.:/h[860SY[Nk]A5Rs:1;N@ER,WTk)ck3S(a\
S=Ga4*RHif2f"ajXIPg(_!k9$_MADA`P+gnf!pBTE^WB$$\$(TO*/0`+1D+[.)D1$)bNNUH1
9*ddCS>C:60oB+I3AK?X27^d>?<n1k5,$a#=EDGUNdko,_OkIfj+5FFC/LB$hiNt1KKOMLrN
PWaNq8sr;8&X'!Ak3-2g+Bi5WINrkV)tnO5hc9XO#X"3/qh"T8P?L<`LG\A7f.>>s6#:Z9=/
SD,W\B<4>.kh6Ni`R,T)"Z>"Q;j-$RJQmBAaAr^6Y#%Z\CGW<iWf=c)fY=1g<cjm=r=b*c1u
!%/A$)GY<[mDfj8o'$#9U1MZLf=GfOq%]AWJ2%]AX6FB*GfR4$qA!0oK.kTAJ1F=<FAtcT=CoX
,1?Y7PZ'?0pdRb0Z[t1:dfL!g__K),2*V"N0//C>.&f`l*@(4[uVdK7<U&j"U_LX;*7PeS([
["L`e_q1QcDP#/:-8+HVSsA'HBZ"W26i^p0m76Sg.(N]A/5R]A+1`kW`JtE+DFj5CsCOn.N<YX
m(Pu?*e#ebYcpeS>6`)6$XeNjB'sTmE10j%ab6)2)8T5Let[Sp1fJaj)*aN>Cq3pZ&Mh:'<*
b'b5?dt9E-$NoTIPFK5ug.n8`UqHMT$hMe3siUe*."VMSiD"<U7_`lGf$LeH$pE&N(3k?uO_
5=jD'Wo(ele;/`_L<oDU[(s3K$-%]A0#$+&&W]AWm9.*mCS!blFGd2@&=@D4]APSSUS/NX9.UII
*alZanI>FSERq=er<SRhb]AtlmnBtV-Vs'\@(EesX.mJ45]A#<6'k%,f;a:!n8OKi]AAXl,@lS)
L1T&%$X$0,=rJVI=;kHY%RI'pmQ;kbV9g,W"R__p!ffPE?,6A`]A\f^<<kbqYP@psVfg)^c,4
VdUqJ\[jWoCr?FuG+r.+gLlZ?K4Ypgm):IX8EV,F/0cg/4]A&4uR7j\QOU7h"<$tF94Xu8((;
]A;)ilX4.LKY+5c*!*nea/M?>)`_7IKF\HGK'm3M6FjV[.>>*+o9-MB._IrH66.V&E50=hE=g
oP&`W?/''Pna/@t$']A1n7SIa(3SX*1d,500]AHZ$JJJ@ED-BXgWoN"Fh/\?G%?GG`tZ:LT1;8
_;P&`]A9XKo1>Ch$dtA'IT,'FipKh2LZLms?'sda,*Yf^9S^&$,):ahro9#cY=c9M$or+e,q2
WV?JOl2U(UG]A#A20eTgjRD%Z/FK]A.`EnX/6b.*4$T6>fXKh\THqk&a!Q!]A3j>'-qG5#:I_Ir
a8Jm!:-`UkXMH]A1[QZ-dlJj[Y&,0uFd(?#QKIgnijd'l##M7?sbGAh]AQ0Nu+]A,jA1i!i7I-J
lR9HJ@A#k7OO8^/Z755P/`]A0CE(bf.&@^`d=]AQlF?Jj#i#7EaMn%gIa)274[@[#>p!sKjIqp
Udr[FI8IY*a_,"(shXW31$k3PCI&@d9U^UC-'m`Yd;?#(QHhQA[NOHUhVfe>H48u/64+P^5,
LsHEEYhVNFj>Vsr_"7:,09^q2k!B#$W#2KHf87t0UK8R>HjR3cu4qVQUbrcDGG6)BlX+D<D&
!I6H!1@]AtkpGH+%S/^IRtn8D:Z8Tl]A6(YF)'+8ofGNoii(k,GjhFX$O<6rAphXZk3_Wj,K[_
,WL@f>*0F9&_:C(Y787Jh_u;%R7\A_$Y*q?aT\`h5m*74JG7Y&n<O=S5;9l7G:GX!?Zegd]Af
D>d4:sC:pY_Bq\h8UjSj=mR-$Q/hBMU<F2$tmL:!]ACkX[YrB(f32=,0PW908aOS9\!>\iUio
qUVO>&[XHB<ls?b.1udnrZpCm[k1@Z2.8HE-$qPR1Lj==R>,B!J]A(D7?bNWelY.GsM?$/9ld
q%M/cUtnr_l"^P;iNCJ[]A0>4PFsD1k^.e_lM22[(PYSQi29MV"rPp5;g#)eC2Y%T"7%u!I?i
?[(oR"O7\g6uV<f;)P_Nmha".]Au$GX.;2S>#rTgdU)(RM\)/B@Y>34->DMl(=bFG6OrX.q+A
E3CH\Rl)_dj:a`TftY+1?2FCl2qss6<3=dMC<KBp2Ch#k^I$@md@?.,cCMo+[Q-5P?@E?QTP
_K@C+Z,M#!cg$s6FHZW0iZ1/<t5a@MZ`P7@_I8!5!sD,W1%uY[RE1eSb<GCr;7e7b_O(rO.S
RmimYU@5^l"fu6J=9ObHh1S&>_U4'H,TE"'JCGu?'QE5goAAhn(fl*Q3FT%C9X/TGP/Ef=e%
.GjM-:X.A?98(4\;^+bVW!PbPe(jJnQQ]AND)DC)3gl5am3a-p9-ZcA@0bGY)Io=He`V?7+e(
Q^/IGdJI!B_k?bl0k^P^D:2[;h:m%C*(CPInP]AAl6u^)JI,7s[\qUTQC+m!O@Qe=;EDLSg`,
r&Wso0pIi,OjO-J;D\e-%5E,H0cr1aqH;C="KQ-:*c!b#M$J&@*<@$p9RrA#Mu#^#kY8J4/=
VDfRLRLD`,3^3eH'QQ+b+&[6=#.c2r/(Rn'_EYn\2eRC.`fgj(UYP`5RW(IUL#caXVQn5&[k
Uf-O2`.=#f"2kE%pqN;7oRVONjVq)$8Ej:XM!u5WsrJ_3Qj99=AFlH[)"j3C`TRqe>^@(Z&#
pkitUM3!1CN!,NGOj,CR'EjG(K^j4`LeiNdbC@YY"K0eX]Adr5\p8#6KAI0mEUR9<q6_Y'SQ[
lIb2Q]A1=V"WMHMQ"'1^9;=ZJV02(SYo<'scher's8ibE32j9<AOX3/Hpi=%$AWBPtX!-*&Y_
6CM;/@D_A.]Ae:&Pj?&>c"42ST.e1RL.#:B/_E%>M1a<"3k`BQsQs4`+1'UBtN*:Ygpl&b%V5
?qX3.H>E4M4r<Yui@s3gN=/L+=F.1"W.?6i%M5mm!EO`'8%5s&!Z`U!?uaJi0ou`I.b`/l=/
YSX*GmHC?JCA6PJDib&WT9Cs8'FU7)cljJ$A`Uip_O2gtdA>QB*h&^7QhO'mVW\1St.tAHlp
3Wa,\Tp7,!2`sTQade#:eBOhk?c0g5I;HE*Y*`jSU'm\c'u3ko/?MaZgl)T4l1[]A<.9bj+n:
F]Afjlj2n95NMMi9FGr<K2ekY"e<0mcc?Z(7j!60+,I52)dMdE//D2:EY/RbGJqZVh)M!9B,k
5`7ag+S(+fNFH>N.rq"s1f-5ZOD6#l*-Zh<-,_5=1AJdIMckDMa$h4_#JbXaK#4ld'=Pr6pZ
[NCR;I5Gf'R[ff6^+WU$R`k^=!q53oW;U)%;p_0;IHF]ApElCB20*:N'Hf7Ps34Sp_,TeS5IG
UN5c+kLd^7`'S@V1h?r*;O1Bj<1(@XI?HfQj0k$eg[=r:#rpL#\4PDV&"D3bl/L`3K/p(TVE
q,!nS9di+`9FsiEIg^@!MjZFQtAp0TSQ&-eSHpd'0V[kAN=(@c)mDL*,[U>?E/T;5f>eT_^-
Y,H6UN"A6i+1oKhBV$@3%RFNB4m8G#<EQakTEIJ0-*fp.m.=9.(j3e7qZ3L+]AJ01ZQ>m8Ig^
QQ475:1VGeUrhJtKB6\STd\S5H6#h5Sd:UurF$?,)U_SN9U/?*,Llj5?]A*onXNG!55AtpheV
@Aj]AN+(7eL5"kW)5t;UQ'hWO*ao\d)PLN`<;XG-,bQ4^B0XtAp`o!'n+Z0Xj[n_=l0(ulsG6
L_A[V1Y+BWGihsr4a/OjIRE3oL51U-*6KLUE)f>DKp`E+E,).fQDqaoR5>.O!m.4mXCV9+k-
"cba".]AM'c"E,ME4,IC5ggg3!?!-AJkh2i@>=5g>o@,dAQ\:8[bIt]AbO$o0-mF-mkh,8#\S^
k6M:LJ\<G9/_Sc$Rdi]Ae-J4kZDnET*%"%5/iUH:VoSYN:.]A"B*UP3#>u'A'Gi6S5o9CFe3=r
774%=/%cYBi,$_=`cg!0q58%+pGS$K)I#YC0p=[DTD3ED^lp^t/ARKp>s4R;dJEPW:'[4_b%
hjo$BnP:itpjFS2rpa$fNqfSG!"G>!%M=i/ZFcF(n(fZ,of-*?49nMjg"iBlo/Dd`Q]A,ZkCo
Q\nb>:i:p2U9E]A!Agq4>I]Amj&uFrfR>P]A/7Ka&uj6p^C.^1LIg;(GacD4l6&,hgP1aP4[_h\
)3u`IG/Ni=0O&4JWN,Dl0c9!^ZU^/e>)X+N0q/<"=%^M$`>dUZ#/7440FN>.AEZnd5\@dj+E
unF]AN/a)]A4J!25cs!N:0DtGc.io-HLt_?bEqkC=afB6CW2S?u=iVqp&?:,<ig,UOrYL*THks
a8uh@ZHahhZh4TUYrY4%+=d%]A]A?0%9=5%`?e^*s]A#&8mFQn<43UL*e!8p`+o]A4%u"#Dogi(B
<XLBUP6X27P_UR2(AE=`JOT?;(1fnt#l<OZm?Wku'Ish`hk-dNq4m\*s:mMR/TN'Wl((bOAS
c1Opq`GtGjb5Q0ATehV4LX9@$dTb^&bp&[+Xpi"A@1lLe$EoJ;kP/\AP5nT.*E*2I2XT9(Zq
:pc=e_>i=7Q:iMr)D01O4-L_mhaHZ.Sk[0WOX9MEOY\j_I>u.9D*m<#uZFVgM\lb%-neO:rJ
Wf-T>l4c4bV$ES-m/(>PYYGGFZK#J1bNRc5i2X=Ino[P9_lP;5`cqfKN$gY(4MpVnuN'uOc/
U&..t."8sAJS$.&';itBr\/-:F;<!T.BaG?Z07;U(6bghYXtS>'"?/8BPfZ?M_Hbe-A0d:=V
TuX$%6l46U;dkk.V:fNp%te2WN^>pYliNhpKY`YEZ$Wk0=U3Koi#^/;Y`q0YKu2N,`U#jJBH
aOrfZFf-ToqOX=MG]A?<5d4T,D1V8H+H^sC!<`BO=>Yg/Pl+Nt_6d-#(rQI.>I8ds`aB0:TW$
X^Xe5m,LYDY8VN;YAK8j87F:f@;F-4h+pb0iFJ@BdOs(-6Q'KED/k%9V6f!c=Z<,U?B0CN8W
=1:RnnsdP!DSg=Ykj[>UnJrHC(6T<q_,\rURrhiFbG/3Du#8/0J),RF(:07?/.Ea8#l[Gb(3
.Ag:d/Eo+CPB<6;%3#(gIO?G=rJ<!o&5-&K3L"'#RqN(6_,r*tN9@S,"a@*M,j]A(6-i7g$.*
Y5OfA-Y2_c]AIZV>Y,7(`AKmVX)\U^h2ZEGpeQ80AKJ#j+K17f[[q`M&Sj.\*d.P@2lq&2N96
YY-<l`<'K7RJ#FDOmT\C065YXc:`=t6=EWn_bu(&5`maq;i1@l$!4&*h3`K4ofUmfI94Lg'E
R:7R%VY2W_8$.id5m-V7^0'gG:9P"gJI+HF_,dG/(^"5IHoD9IOH.]A/pnk]AJJ1.U]A]A,`a$-:
!&0t`*C5%%lm)g`W$oLXM/&:"dMe\WCL$lYip;Qo+"@.WYS_3jd[22nYGlPlG6Whh-!$;Q&:
08iIm9`6K[:?$7b+2;Y!>uLs]AcqcZ]Ap(&&BZsQ&UmU0#1PG]AQ@5$'=5d0m/N!?1f`?A&msF;
s7Ic*5\4mgP6:rS)ASP@j;b#Y14E6d]AnIcN)%ZK0Z*7DG9V"Y7Xh`l%`\2(LMLN@b@;.SU.u
RjY2@-`*'WVjg[OJV`Xjs3b@qs4jEi+f<>=S`6%UgOd7\0a.Z`A41hQ0`aMUpY1VBsq40XKr
<K;G45uuA?;H44g0>g0=chK_5UJVVmd/7eT<lI&p\;jI83#&@MBq4ND3-bI2*pPACQqhdVR'
@XPVHZ$&r?>r_jXqW_[u57djuVUWFjZgHebVg?q2G#$4[lRCB4t]A1:J_78pT'SS\cOA']A0ZQ
++;PJEcbiZ";VMN2[U1u2#c0D?hTgkg[Tu`;,H6^WXi-,9sE#:M<_VC=Q*H(EMIfK-=7a=LE
U&Rh6=@L:pd6]Al1,?X>&G/NKtcmV>&YD?IA]A/kIBk$iWs,9J>\;?\gRCVBD7>MVq[8f7LjcU
,"Ku3#j+6J_g3Pq_g(4<4kp%M08<)]A^JhND:#AQWGW#A3Y=5n<P)seg>Eu(WI.ld,`+4<Bg0
#d.Aq4sRm+0#CH8Z>`]AZ=*quPp]AOo=*eAR;Z$F.I?RThV"d>"kpNo?4Il3^C[MEGX_[Efpq<
l^6:;"BIeoZPq]A-*7=3JmL2l#da#(VH!eR>UtFj,MiW+.m-VZrBC*t0QcNgWB2Nu\YL04m#/
FnEBD@W*&6j."IgQ;RK"ok]ACu>33/:a!+9tV:MZ^d[a!Vm[4+=?pU5.dD-aQGqG$hlEl4)2I
D%>WS1ZQg:N<D]A?\VY"Vdm4I?ht34DQhSp5IU(JC^F$d'k#)&#p&-/F8^u7KX*,5[c-pO/kM
R'a*DNMS%]A.iK>X9$sK@a&$Xf5ff8Ip5id$?KV:o9_?b)M<+A\q4r[D(O/)Gm?V2;4>R)m`%
1/+p5:0YWAhEiF^0op.Em(NRjQ"P2DX'J+nHoEP4I]A5`<?VU?&UYWW1[PEuabW#Ca`e*ig[b
*sTr7;m=rbIql^(oKJ,M2^rV_<GH:@8Pm`GE[^=+U"'e`N&<&SUAbq<X;\No:cRScI7<hJ4N
8oc?TF'm\cZ6qh'K;.Y=ABENQr%DG+WQJ';;N(8DU%%QP,l!"JrOrplAf78-OT)LmH5ALO1+
8sJMc$&dDQ5U<)uZ15*qf1@1?kbY@$>H%j+gV=iU]A-nPqhLNBf5Kq4*Ro:aE604KAPDs`dY1
/O'uP292[g9g<cMJ&XL5-\=)qbesggu(que&'`IaTIDpB::5=u_';$&7YhY^Ls)K)uht5RQ@
E=?"SUU=F(4dJ7O'>J'V5[F-rK=&+9X%oLK\UJ#@;ENi]A6$.,FH:ASX)'C?'rR!nY-H-/QG2
.@AQae`(&7l\K,L5(b*mb'#5W#dH<pk!Xb5l/6)dO0q[=/qaDIPmH!jpS)dK6/%qBJ3K3&:S
o\.FjE3k&QFm_@#Eg4n&Lu1A/&P>AA,/mekH5sbADsQ\QfFX8o)`8SB%X.G$hbt]AR:3k5WaB
otRYq#@mqB^QVnWFEmLJ(FnXUL0AC2E<;'KU(JfBJrd47ShG6a'fMpS3*A":R,N\Lb-=G4/C
%HQ4i51sKR`"qSIIq[#bQ'sOA>FV/;2buGd?'bm\=4t:)t0=ug,&pi:sgr\fqh%A_!&s,tCc
5go#re(uVqk&lBgD6nuI(VQ&pA68gQqn<WOck1M;7._YWM-FKg:YiE/P.a:=''FI^$jhQ[;t
n&?^krUU3bHKLNACB&J%9YGZ4bb+-IJGs,91ALDiDc;R'!.Lb9OIoB#TcoYW?5":s?NkW=.g
*\Bp0rGWhn3/+>L5d.!._A[WQh#!Nect0ZEQ!cD2!B]ArIMM\U\)&E9rbLE2Xf::D3FTuR89W
&nb&lkmm=O@Vpr!h%:eGUNBX6S+`cu[CV1=)Omp50:NL6Zo(R,Xe7%o3V7:bUJWn,O9KLqM5
Sliq0l"cL\BWQ<VVFIrG#@%Omr7/(ArO?n.oq0ZneoKAfBN&@+sUoma4*8C$M6]Ad-==`)=UC
=KWI$BSrmhrlJ-ql<Y"lgN,dWo!kM6di56d*H.l$cguq\SSj,bhf>^aRnC2gkC]AEXai5<_>D
cSTLe]A.Zncbiou5I.*[R%eK8/;WHP`AA%mD'Xqo#n[]A;k.C)t\P@6>"6nd]AtAC,*ZHofgW5t
r?dJFY0-cjnPWqk`ZW\GkQ:UtiA!6,Z`MG65ARF7_p,%sn&beQCpGG2pV$rQ;2R:/8=+ODhF
YC#*U+H@$"G\OiWC>&OlthA8cB*O1+#<[TWoCKJsqbD7lqC\b7W$97b+`S`6<`QO+QKqqp3[
M%qR&AQMq):AULkqB<*reKa.4XbY]ABN[tbbM7e.u$G6=_`+-I5I(IR]Ak*bTP0%lqa[Qq?(m.
Os_B=V-as$sdq=(^`O?="6=k"Wf]At37=rC[H$3W+@rk9'>)\'8r@4b__L8MMs3lg,LnH(Fl.
7#6j7.0\9MZu;^ir\`Z!Xgel,`SI=D7o,e-;cqd?KO.`NKWrRaloq!1uphj&r2A3EsX1/k:3
]AHEU+;Qj&egPWQ,0"6P3kNQSrHFo^hOaV5j5He#YSeE0#I;>D1X'E`%cXUV2<pPnqh9-)HHP
W;)*>4+6_gbJ0)_GpYp$YMrTUiP"4VAqR.rB[DT^i([89r.1^fI]A01sk?X7d86)Ii&$0nXo7
D;]An719B=uMcf`_$cK$oBF;0&(W8++C1lu8KjZ6js3T]A9!^Y,&cPc<1:B!QQ18sKS*/bL->*
d;Fo$$ZDVBc@YqlQ:^e_p46^D;+nLR*+n'Ik06\h.U#IM]A(UVC*@:K=Z15:GlA+F1Q!t>7kj
Nt8g=n0P&P1Z@H]A+dg('>Ai14JY"AG'j;ZEq?\/@MN1mm5]A2)Z(t*.NFK\Q94AM0MKUU=!Ws
,XF#bnF'KjVGY:ja1o)b`9K!bkBUZ@AH@D9'j3fr5FOb7=K5>t[`g+MYq3te'YP6?'"*bpf\
60')uh&b4FF'52^Ag!5n_MdeQVlUQPuQRdl;J\r:_Rd;'J_BH^_6,DB-,.'S'\b\T)XHLDYW
(Rr%%A93&2EL/kB*a8rV0<G<lb5<;i?gf&1"[I3kb'PZ$(:Z>iS3?<^Wjh48P7*l>^fl;[u!
@PrdR^]Aru)BD*N-63NI7&*YoB_<U1p7*`49TrFVV_70l3JNeuQI$<+S[la>U)?uA3>JGBj1.
Gks(>p.5]AeRFM8:(/&,E<P,(%rkB6-7-F7idLMG]ACHr!=<KNgPG2Qu"3=',?&2*Y&"7,74(=
OO("A*/ar>D%;C?>J\iXn8=Zj:;=&EFct2Njj[I+E("8hK]AH(M8XDpd!eWsHBnr2VlTpd*l%
HDd[fjNC^=-JVs._=*R)&3^kc=5J^7:'G`Z&l_0NW_k<4f,nK2t.ld?6@\.CrA'"0e&pHsXH
Z6r0qQ>+"c#`;mt&a!W4..I>N]AK:fai!dg@ciY%E(2qrq;UXTh@p'TuKejsIO$^b"7-P$(B9
`6gC"Pc+MB_#ON^MPj-]A<7EjaoKhq(kQHX_HFdpM6q_)npGB:!heIqCc_,a4^0,p>??,9VnN
Ei:-m+gVNpMP3!"ME/r.1WoidmDEKkr`;ctgTT-_=i-sHYel\q0m93i&+[k-4':T)GiY3o%4
fjLrijq]AJQPY6rkYu-haIVlt9&uQrUM(E(F'1dLdC%/rEY^l-);9-6`hj[Ue8t_W8%p^&R:]A
Y/N%aj-'Tu`9kWX!]AWON'"m4"]AL*P;%!P!jtS#i)bHWo2>a3GI&S*]AK@(Z]AVK^KfurFV"t$E
PIQ<V282K7),DCtG[c*ij(7U-U"VgL)&mH`UjiJL&i,65Wc]AN_nnPVLso8=A!PbZT<iGk=@s
,els,IQ?OH:eq<;?m]A!RL3:CT*bF[X[8XSk@K`a-^kb5J'dX@X$;bp<*^W:>C;fIokt7,U5L
g)/[M4L\.D8t%X812?UE#A1sk=5PZ[6u`ILNrY?.(./CsU'cs@A*ai?[[O5q4!f$\LKd<R"p
E=/qIm7EKCH.O&hWkdHmaM(bi-rnr5D-LitD=p<-/l'K+40l_oT!'2sE/OlcO%4<*$37<fS;
<nMl^I?GQtf]AeA;^5>Am8;QDGSYrG]A>7lj;s.Knhb!17+2g/bpti`/+gos'04MbSEY$1I#EC
;hu-]A+4$LaP9NeTmD`4Q/d$iA^Z0-N76iHVf,X[`Wp=?m\A+b'FfRr(UK1l\D8OIb17L!a)Y
J\E3&j@na.N$eoI\b`Q>?7pUhIm5IOC"(dI\!Pp?JmW3$7X0!QN7Y)H6G)gVr<QU3`]A#eYl'
\p_XMFD@)f7Z7r!IM\$g'JZ!lf>L3lg#;]AuVnV`XU4Te#C>7gIZ(PHOHp\b-*7=0KO3Nt?$#
ab[<ob3nffP`14A+JLWPH3P)P?ub3_9C9<h>9Gg)@4&U<qFMAuL8RUpE3%[!5a=aA'\@!nY>
LiK]A\J8N1\[O@OUd;(YOj4m5M+f2.oo=t/#h$6hSj<*??WN1nRmQNl$ou_"_]A6RU&.cn69TU
Qq;dqP_!036?X,R"Gj0ESU)lk@(9^P<Cj[T\Z??8;@(88BeW42l9L\J\=,R8*#KIoOMAF5HX
m8#k^hUp[CPQ81+]AKK&?A<O%b0ua^`?71%*A68BrrlqI>Kc^6n:T>YT@N]AM>;PF^o,IG#A/Z
7X0BER$FY\M:(4\F_ih5!"!T3+?jl%'($+"?sChja;Gf;*E!;gh?*u#OPh?jBmV8T#eXc&Cc
aDBX^B2s0^=c(2VL0SnYmn<8tZOu(PXF1]A,`:K^X,Vd#leZ$Y&83#n@Q26Bb7\,?Sj]A0ms;?
_n9os!ELX6$39U>G&3HTdYsfPH;$AIR=mgD/dO4qrmc:O<gA;%KIq/I,0,W"*&YP4L<WW,3a
rGajDGjJ+Q5%%+I^@L?.C^no2J(Q)cRMltZq\TTbA7WFhj:,(,jC:*LSio@W38/0E8go2Le/
>Z4iBpFl_H`,b8a>6n3%W++j>87aj&*pAc<]A&OoO1!QLC.kC0s3m%9VPEI2**\@sV>]A5n0/7
8kjFu;G?h`K3j`\o+M%B]A%o)aD95OZXpcI">^b!8$0GBdgRXlWk.mq"4X>1?pI_\m;mP)_;5
@@WR*1LBhT3>!0USuMY;UI)t(^ur`G"PJ)baQ<O#F$%#OS%NU>2Dm!)Z?m33\AoS-b.\Et9'
a]AibjujHq)+(C/4V@q!cZ!j$F%FoSD==YC%jf^1LC+T&hLgH@%ut[KX7YKAVW*OLW(^RG+XR
\['Z`S9Db9meM,6=3U>n?F_@k?JQjbtbUNu!p/eU2p1X:U218_Gbjd.5?6]A%6!)9YP[hn-j3
k*^%?@+jZKjpm>/?+Olp^^j'1I+.dd"QmbpfUY\$)<bi&A_prnMo8'cN#8Q8@gcC&l\X2KaL
3#-ToZn_J9oGL?!PmhAqI*,>/;m^%H3IeML+]A&%gKhB6Rk8+3^H<V@bJI.;n:6D'jjdD1$h3
BuBs+iKYD*0F`.[91SX=')ttfBn&!*3mDr(:J+9*(09<C+l]AHLjWM_+EkaI2,!Mp7$4sp+j<
\IZD<<lo[s.,o)9[TqMNn'Rnhh3gj>;gFFe-#IIuqU[SRnr/4^8HbYW8j'OIYS5_@7Th\aA$
T):Q`oM>+oq$BMD\)iUVS5[Wko=59NfZ8^mUF,qfir;%6o)R<*9_>a2<>dk8^I@^bG?F\uMp
lBj"$`QUc@=QoIJ(to3O*[[u+dh-":^;KHPOY:Q:o(_96$)it/%b&)r.Kkl?rZOc=Q=!g]AV?
\oJ9AJ9Bk_?&Q5O%hJ4N^d&';qFr[]Ac9?^UKSHSOHXPOa6uSUgB-q9AJ-SM2:K]APGO/FqEZ-
_Do]A'JpuQNr7AlpjE-GDFq<==4^5!G)`#i@iWWDoJ9&B^Q@.;/h*COhc2BM;C"3]AYed%CjV"
:,$GJ4BF1\MZc</mM)_nBE\ltNI9&o%o=mdFi=.SXPM3^6[`mk*7`OBe`GLM]A_[(,gdi0>Ka
Rp7E`ob%p<<R0lRbjZqX0Zj6Ka#=R92"X(1A9heS8^;u'-4Z#^(q9_`!#'"MO308ke^hmAJ-
)19=nBrqf8q!/Chk%l`rj-e\!s*Yr>hZhNXQD==;P!4`K\Dng[-:\Kj\X>H`qs>S`meH7X(p
(+E+NBk=fo_2<M+EWn&(O^Su6"?>#OTSJFtKLZ4I<?rT[.Xk?12Ql+_al%6MT,MB(WM9WfXm
,\!gUX75h6j"[0!s+DEt1d-D(f]Alp&-%pgaYMn"GKX43.@/&Kb,)]ATRm#DI&?i!>)99?(sR1
ic/l/rQMDBjT_h[Fi*-6TjleitIFr7[<*Nd*u/VI/fb$\&,6D\-"BpDn&B52YTbF3]A:@KjV<
F+=%l41;)7Pf5_(cIihKEXfHj*4L\/D:G9O$4.1!0m!T8f)&2lG`.II1fCUgeb\("+MNCnt%
@_s5L`uOc-h-Ku#e5.^s!'+J(.OC64P[&IVCZqBYrL_SIT(sf5PDfJ=YJgO/CsFEOa'C^.bi
]Ar%bY7]AnuP0*f=!0b`tm%:A?LBk#eVO[8$,fOXgE6e?`?r^J6f6\Up7R!o#45u*?L6igBu[8
BLB)=&O297Q)"4\3QA@a[-S'0W&tO_%#@TD=.$?>$=jRgD7<(tQk<[WfSM/G]A4fi[(n3&RHY
hL5Ic$-QMHBG6nr0l73LW]A/XIn8K,d6>--r]A_&AF0-:9$uS6/1mRZOZji?))5*[Z!lgX.oGe
u$OZE+l(di3'luHg3STGaY3c5CF^/Y.[.4T;ASCjG_T'2U$l)m1!QpJ^G9A,*1Z5ffAUYBO(
jpP=Sl:MuG2XB:Zd_-ndQ<X"O.)^A=Ft0YU6dqgclTk7^MQmSkh8<fr9j4k,95<24Qd1hYMj
MnrSKGl"5Ia.D%k:mj#[""KXflp!@T.$PZc+3n9,/\iO4M-i[k/c#rGi(8':mQ?=#r>C[#Rb
f#Nh11ahM]A?+%P`K-k7?B(nHtDT[g``p;H00;H!G]A>i`skALN#GG_YmqC^D?m47"aNaaML?d
r%THrOZ]AVXL/0j#9+cX@2[m#c@8I+`KM0U?)b)"4lM7"g$/&V:g@jo.8KA@LCp)aB"A\HL-G
Oe?L7%-^`\-[:?XH(`]AmYi@^Li$X8jsPNJ:^l;T]A(,"lOfDsUco($H<`VJPs>`019nc39_o<
R.DR9f@3m]A8\>DD&f3>K9F4;TTZjsI@["RBtG`SW]ATS+4-!S+Y5!?34`oGVrU\?`(KMWYb29
=m\"!SkGbq.%'*]AKtmn9EN-;[5,lq!JVLJkV6mGtrd:^1DjRW49/IC!\?jA`6U2O+^Yrh3=0
^=0<E5++c3;BO!SdYJ87Gpd4j]APMriiJR`cFIA^UUApO>UX"b7_$Njb&4L-O0i;C*/=@]AaMG
#?cQ(Xh4T&#`;@(u]Aq)J)ULQIE#2^df#%.^kQ/-E03hFVF(on;6[l,\?<LS+ra:/&XDUAb]A4
-7j,.-.aYbXkClb'bo]A6P9[fqD2:*hJ+Kr%I0:TG8UWK\RNaaYQ.6P0)@ul]A&:jcMZI2&`.m
n=Pj.Vh"Y?UHl`oGobR8[2A@4PJiuoAnes83D.q/mf_u@LbLt)nQX'o;H/RR.^=C\7Dnj6#G
BO_dgeS$MR4bj8k!#`.PjP/JoNZ_N;.kkA0n>p3>>prC>b)^2Ghk(V5%0kpREh\4IN9-f:NR
nW0.4i]AUWR*,>URP7L.AroFOCi&oY2Q&uYQ.VCh3VP%SEIU<[N$f!iu[o%?Kh=_T1Pl<.9=D
-a_+/8\\Nmq=TkPNg"!iuPM.mbF,kotV]AP/-2e5-^`,raW0iF&u\4'K!3kE<Emb!CR\L)dTr
,FteN8H8(ZVB^tM3ROfTFpiE`k*+Kd<ieVG<.-P9S&Ba993,4fO&Up['ag9P7Lpp=Wb"b<HW
/%V#7MuN&>fn/BB-7-cO?dLe$%V]A/,M:Li9$I=k44sg+H+3@bZAF<uHgtiFEUHPp"4+RR&lg
s^l7Tqi99V+*PEr(54]AI+J7%;@**!gd!M%aBV?!AY<rAlk.05?#2IPFU7kpW+Qq8dhK2@,rb
C_]ApiXu)-G-I0iJLa$)##@]A>P#Q4\N2:bLe&scDZJ>li6UX7KfaO*8j;f#+,O'Hn0+u%cF7T
D2N+a:<-hOl?W?N28U))mPR?Y`okb)k=7,mM=?DT-fU!40VG%c=bNm?:<K:%UR64?jC3;pVE
oqA]AUr`GF(=7f["Y==apY&$5.&Nt&N3#uke>=-Uba(K"]AZ?K.XHFMFPbs54o&Z%VLMm79-,Y
JY3#).rqTP<_fZ#:Oai>M;Ae6sX8%)HD,"N_@AC86,'C)6drj_.<K*c4DHC&J7reRc4p%@Pe
R=X6)2+nRCNbN)d+No\LSVhoYeiqWCZ/m1qio[q1_`hcs:l@/=b8#?hor<;&)uZCFcGf[&3X
6hGqJDKi%C$0)?_e$:nA>(MgH5PN04AhH.h)YXhAa)`s-I(j=afH3.PWN:fibjJk2<CqZ;Rh
/X09ZuU;*B,U!f[W'$lE44&e?r**#l:E=eENL]A#'6u:,aZosb67<c4En0i)*%edX_@P&hW_(
$5$@P+l8bWh(tNA\`koWXBD!+8H@?N*+?u0qV!";L]A.XHP;0Vu+HVL^\9UeDq0\s1.8OaVk5
:tB<%l-L-b\;@h3^D1C?3?p9O8dn*;<nLZ1,NZDNNg<W5EBO,=JJFdLOf=K?r''`]AcmGIjA2
6WH3FFk%sp.cG3)XLB?@hML%=35^I@oWpJ/X9Q)k32Fbo>Il0Eb_8PE,r#1@16-ufud`"@5Q
,Jb+[Q0a\Obo4QN,aE3R-.3c^O9YI*343pe@_Z!4ek7FO!=I/\)7YFA6c;+>Vo-Nphs\@oD:
H-Tf4D&_!;1'?dC#6Xd\n'!R:,eUp"2T6H"Ap0?Ie[*OVQf\%G:,OU[gYJm1#,7Q^._Llns_
`,pug,A'cF:#M9%+_2Ea6d@eX>K[gdkeVV5p*6d\IP9<:hrGf[E>FfJXoQ#^qZiG!!rd<>e'
;8Fs\"f60oGU!gI5On^(]A@oRoa6kJWfc(3@Ieu;n+dTQ+HY-qHY'`OV:VP&s3$fqQ(JhSf)R
n#jo7=omtupB$#%3ur)f]Au`!5V3P<T?jV6g3WGfWM:K5-pjSMptjM`>_6:$=d=I&0PN?_a.*
o_i_QZO8Er[!>KP&TL1>^cYKdm;bYoena"N*Se;GZF7iQ)KSU:j6*eG_EOH-k9HJGJO@h>L,
&u\6s?@"G(OXf0,`/VfjF`Z$sCBJd/SZ"YPTVOhlCMdGSj.R+LJY]An'c$q[#L']Aph\rh9*4d
bcI^A8J@4>NbKYo9B#3hl19]A;?n'L?!MZ+?DBgS`$3?,$VgonI%=L+7'g(6%u&.4Q6kH@8:B
'5gLO]A]AZ:9^LHQ\Nkbmmn9+)[f7<.L!^,Cio/->4m(C:$emQ$59:'`$.(^XOo4Jtq.,>J/9.
NgMZ^#F>jDg3d0BH=/_ZT"D81^3ZY`u1X?#09MMhCO>D!-+<^3*unffaVCO5FmlH`r3HTW4h
4EeL:Slr1Bpfus&quFlc;ubYnH)L(g+n*uf/L>0B_XH9$3k^Aua<aL@l)-?:!MA5h]AS4f:f.
r?0#UP_q=sifL`HY\mV5>Y'6RE;K6jeg]Aj<!Yb+(jE;4I[:=_`Vu0KB^;h:I<Z@DZ@=bNd[l
68SaYMki51@!nJ35QZ0NT2p\@)V1+<.(aFY:+0/9;c\QAs4^6sWNV\o(1Y:TF!GqG-AjcG'Q
^MtOX*ZIcb5mJ-?T!*[h7CMReL6KI?61]AaCd9(B0Jt/%41Y9OYqog\&X7=2F0K>"%X'I@l*b
%:<sDKQ\(Z;bmMC>YKA>9^"eZLq7I?)[;7/r5PAA!98Ri]A^#MqP7?P'.pb<7(]AP7&f4E/WH'
<JAUuJIFg0cTH91>I.00)f7V)Y,Vmq=76OqcW":E+#^LfhuOtsBmAE*PZ`jCrRnLr.b/tiH:
`EfDr7eZel_;om8&4`#"S@H">&k5S9UBK=S*Xse=]A%47'k*Zlu`T4Bp.rTpnrQ9k"-Ymg3)(
J\BK\Nae1V0.lOogeG?e42Z?-c$J,kmIF-MiD:9k"U3U"ifb$6@UJRfh&8PbT?KInL7P5g2c
('+"#H3$P=D3M379l`^d6^grE(b=un/+s@2N@%/b%ai/AIhkY9GNkKG"i*-_%@Oa`=GQ2D]A1
n8UPo+T%+j8(rb!MiG,p9,BH9_*3G6<k5Eu+eL2T<,/sKV=ZRLC\VWk22j61p5Se@C&(Cr*5
91/)7:iN/^=_t`>6t-kcmJaZl/lc^^q2UQ*er.tE`_$9R0SQ['Mnl[pNJBj?>0:2fp]A?X`k:
O)FoQiBCJ\c'4%W"X!\3@\_CbkGKWNfR;+n9r.ol0lkq9cGS.kCiBEl[&,qDP![JO_8*'sR4
]AJ!V0HTcDFWW;:8,3;iMM$=3["\$BS=9bJXG]AM]AZgqKq4221=;W_`P^hkSr]AJ>2rZHC7[ih4
oQt*WoX;qBsbT/j637sN(OD]Ak8Uct8"s8Nn4W;84JgjW%3JsSL+M"2n(8XYjW+Z#e?aK<"V*
!#CE\n0hLr\L=5(!f_@N[s4uVg4#/auf6QosrNF\(57]ABr=oQQQZ@4a2,o$g.'.gqN!E:-h"
dC$0A"Vg40)J,*3'k6p(EuN9i=UgMc4_TW8YHD*s8laGf#/e`;52Yfc5NL5QGH<7mo@%tZRb
f>LF7#adfel&llE)tuq4sa@>2G:+iD0s05)=Y-!]A)a<&#Bh?H+A'=?0d$f@k*oE%i;@P.qOt
lnT_^j5+E8f11R*BM$Qp7`6RXV=7_0a*M"-n%[LL+A$71mNY@>&P!!%?f9>DF7@^3f\jNhW6
Kp6^[I8p;>a2uK[`(RZd]A"M<!3=npn[odnlR-`MXs(QN9BWbBm3>=OM@d)V:ms\BZ,W'-."A
20aq7u/UB+%%[HP(WPkIG3rn9MJl5ehiju,j[PSl8*1?+Y`?OYV/0)_'op8<@.bcoZYABW]AD
ai2jtTO+gVaq3]A(++5[Fb5+)i1t4:Gp%.)Oh?*`A2U:(;&rLV@n$k.AOER$`3Go6T"ZSOk@n
B+]A:qRi07;99sR`eIll>Idngr.c8<e\Ak65,Y4=mWjkKsa>[17hG]ApA?9t`VWEZ,pG"Y+VKt
^$SIrs'q`09HK_3bDI='A$?f+e/@PYZ;&I4`2eOd&Ya9cQWJpiDX#!uWJH,-Pbsn5aD.eXEM
8%H5%5G6Uf[2&]AE+\\i5M^@XL1YVJkX/UogpI6Q:MBc,Eu)Wa)[2RVFT0$AU,H>h8cQOfrAN
U1EM\.S7_\!CFW"E_.qn#_D4@N:B"4&8mG#;V?b#HPcM)hCMe&_(4rN,N?c#(6KJ"\Uf3@_B
Ak5Ys@+3>cn+RBf!3QU\`Ljk--6rN^h>LDdL\1Sp^m)Ig0?U,?IXSA%mX=M1]A8s&OBrR=Yhu
?%HmIpWg;,/ls>;kBKbu)";U<5FKgZJ9an;UWM!aH8Eqq=uj'&*CnmFV/*7enCBY]A\c/8ESL
<OOl`qIj#I`HpPp3]A_R'-dtHBCrKH8^J9Ce=hig?1n)'MrD&.Q(CCSB-+faNM'K%qTj?*015
NuA@r4=[K>K_IRbU=-,W'KgOrcX^iG9*[9dnft&TC]AAO$PapLmbf.i\En-Ns6I\Gis0bRF:#
^1\#pYFp#70gmsa>^gX"dEs)s_S:j_Bo-nC)A4bQUgIR_`Wea>H#eXmTGlZ_)Q#i@:(3de]Ag
+p>+BP*ArFf27_mIH8R);NG^F0gTR3`\1mqSbr4UqkTg"s"ZQ3LO]A7X"B%1.qrN`5;U9Rc.,
[Sr5770_^lJQrs&spiWG\]AKL>VM%_[YL4V70Z$lIE&@-#Q+u.7!@XQgZRLq-*d["(-qto^GN
HJ):0.S'mQTL&Z]A&JGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGt)uJGpQml
]A:r;g=%'k]Am%JuPeGL0qWeCY#P88n#PA%3W(a7#;D..n2=IrN;^]Aj37if9jqu6Z~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="610"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="28" width="375" height="610"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop = '10px';
yjtab('GR0')]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[76200,2667000,0,457200,0,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[190500,3810000,190500,3810000,190500,3810000,190500,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR0' onclick=yjtab('GR0') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, rgba(250,242,213) 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat;border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + B5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>全部指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="1" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR1' onclick=yjtab('GR1') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, #DBEAFF 0) bottom right,linear-gradient(45deg,#DBEAFF 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#DBEAFF 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#DBEAFF 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat; border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + D5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>正常指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[正常]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div id='GR2' onclick=yjtab('GR2') style='width:95%;height:66px;clip-path: polygon(0 0, 100% 0,100% 15px, 100% calc(100% - 15px),calc(100% - 15px) 100%, 0px 100%, 0 calc(100% - 0px), 0 0px);background:linear-gradient(-45deg,#E55C17 10px, #FFE8DD 0) bottom right,linear-gradient(45deg,#FAF2D5 0px, rgba(229,92,23) 0) bottom left,linear-gradient(135deg,#FAF2D5 0px, rgba(229,92,23) 0) top left,linear-gradient(-135deg,#FAF2D5 0px,rgba(229,92,23) 0) bottom right;background-repeat: no-repeat; border-radius:6px;'><div style='height:20%;'></div><div style='height:30%;background-size:contain;background-position: center;'>" + F5 + "</div><div style='height:10%;'></div><div id='scnum' style='font-size:10px;font-weight:400;color:black;'>预警指标</div><div style='height:20%;'></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="status"/>
<O>
<![CDATA[预警]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="PAGENAMEALL"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="4" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[STATUS]]></CNAME>
<Compare op="0">
<O>
<![CDATA[正常]]></O>
</Compare>
</Condition>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="body_yjzz" columnName="指标ID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[STATUS]]></CNAME>
<Compare op="0">
<O>
<![CDATA[预警]]></O>
</Compare>
</Condition>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-878336" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-14386946" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m94.%PM*OeEQ!aua9Y=V"L"bg:o'G6"g@t.$6gbt!fE)d+[?*hUSn;87N`h8377q_&<K:`U*
-+.kp:uk+pLS)KS0L'#e:85F6:^F0mB12J).O-Od5UPqg8$chn@q9/N\6&aj+()a+56D&e\+
*CsG"48:1r(5MIGPP3L<CL(Y+G-KiZV?B3<>;Kl0sLd:km0<_IdCS.DsU8R,'PEp-2C#<Y(T
pb.hkguF6&e9CePf_R1^1:^uZ`Vp$FiOq5pK`,.5CM@2\FSj_ki1nUj%t*1jn?8IHp"c/dQR
A9:?n>Dm[?<B]A*8;O+TaPIrl%J`$lcXlXeE@o?P/N*:rC"hA3H>ri-QgKIX0I?<i"=<9W&Y(
b<8W-HGPD?a&.q*)HcGi$&Rtg*V3PPf/"4oK3k:M1@<4t,DQ3M0=jX%?@<_MR1>f/8)lK*e)
'(C8P$K#T@)`q#/=ku;=nYXg\K[*H>V<nXWk=L["V%Wd8[soamm!ns-.;UjNj9$QAX`H]ACl/
f.g!:On%FHt%f6I*R^u<'C-9oAg:P)38sXH8StBktkL]APBMqk<8h>KOM>$A1Olc%06j<@LAl
r,A<g;sFY4W\#SMddATepX'hI5Qg(]Ar-'6gI*@oH;/Xm+g!6=T8@AKjZX\b4D]ASs4btk?I"X
$VlkDYsWp>:MV_e%u]A9A6^#P8`6Pp7i^%eY.Pi2.&$=XCRkrG1qMI7F`f[0HAk1*S%]A3Pp_#
lE,Fj0.rOY%)82$aP@(on)5a-J$UI85E*T!?#P1a[(pS;r*Dp9UQbmOeco\mWELppAu4>/2d
1"3F]AP*$68CLgB1<ZY2_^i<$8A-@)a4R&7Yj`o$M6M1;QT-9U29T2NDe1pW'Aa_#cX?S5p2C
lhY0@-Jt4m1WjqTH5sQ$3\DT8)h1GF4AnWc:ikdFgk:b[:]A0/9=d7_BYSN$d.D'uJC0K-N1M
#k#,E$d28iE44VP2$cnc$nfa"^Z)QV-tB%d#(p::+c2nX=iPDJu'Xa1JTVH3X3_;laqZOUTM
+&UXA\USn&?>k6UVmX,R\OJ)]A4!VQ/HtX&s/"]A%ZWO[!<5ep$]AUepH@2eoP$^r_6Ieo7b;XW
o*Ebpk5'o!5!m/_P]A5,2s6?e'a?JtN0iA.lrt0r>OF]Abd7qRZYgQNCQ.FlFZqr?B*O38Yj**
PNb(q*m2M+jm+f$Ea:CKP7uJ1.WlkJsoNH(%BsSDN[W$@pJtcL*F;\d6kTUD]Ar"0:HUP1Hl3
QP@O<N7GfWDD-(:/BJMNsVJ#/*[2sn)7Y^E6(O\O*3%1<&28e3hCu"ahALh[&MGAR@gq;!7B
n!Z7SZGu7S[E70IU_J\nPb=\J^_Lt;>DYe,quh*eLU-c]A@jkpFXTWpqqNd5n&R>#[XHH(GS5
.9[GcrGiMFUA;T8VXTs)M_6?pQp%O!hM6K^(]AkE?24/"rXtBUn5D)KhUCWhOUW2=c.A89)60
f#ZLK8KMFU^\AeL=0f4B+cMj!YaRmK>ZM_kc1r3p<;4/oC1*4V<gfZu5bNBd%_C`q)4i0CM:
T44i9Kn@l3Is]A\Ljk<\lJ-LU2KWi<_k\/>&^rS(:\=RZ(JeoR)+^U>F*kPT?UBj4dQ9o([hn
/F&V@9OKiC#:MuO7)<#nd<Q^4n2#C/=]AVb^?o(U*$SLqC/b2+ts7?Gl0EoMai?6;1ojSgi%a
hL'%gn7>df(#>Q;o8!MOV1[iAFt9FcL8hV%$L(R`q43;D0<JqJ0-A[b$`(3q_B<V]Af!ueC+l
.Sr+kuNG2M%6%(YkM6iNOXj:qGuGh?+GcSXH*]A=_G's-)mG$>PQ)3`DIU^tDW)R19H1o/uC+
Kq@e(&GkjP_hS)j1bB.n/(45YQ@n+IAK,=Oba6r7XJk0]A)2l6%(i-7q/J8[&:Qb15ecW.nn.
5B07Q5+o'd3E6hi[m%!-H".hZc>[6JtDlm&8"0Q[O\*XB&5ThTDhh6YW%4#;#YGT?JI]A#'4D
3++Q9F.iDI1E^Qh=/KaG^%%'2,B-FQ_0%Ge]A9$pA2.E;DkHi%5taltgUL1u)b[ODD,'@u=Z.
<t`=@>4I^)3Tg#a2<?7.\/j@"UAq2!EqO^4_+qX'_olN1`O/%7^B##GmgSud6/Fo8IGbhed6
b_)=mV<p>qO3"Rgj8e!iK/'AfH0HohdNQY)+U$K3lF?oBD,'6!V.6T"X!:[qO?W(W_pL)1?(
nC>E1qprS/P\Tf:<#^Yc!g=Jm^$4mM'/@XH(QboDLjNPV%*1HV1@#J=^L`,(G3p&8`Br<34>
(Dp##C/fhH1RS2a6hA>kMpWKr,SlWj6c1J/99Z+BN!H,93lPh42\YRXT,6IQm*mF#u&mAY-@
Q@*K+\FMHuY"m!Y%,(J3%VRiY\XlV&hHJ&bB6Ee*Y.<knWQI-976`N(%-Cha!H8%AITd@JHU
EA1LTGpF"gJNS+/h#h`>`J7lJ_sXIm`nViBKm1OHDYEFh5T+*oUTs>Xrncs&UtRc8pB3Mo2b
u_b+#A<U2kEOPc)G0q*o]AA+IQW$$5V_Aq=U9KNjS$ua3WS?"b/%B>e"Q#b(8kl=\i$C\:8/D
:dRkTB0purf_jlML6m?aq9+/TnM#e)1<XuhX'+Gle+CYhrbqW4iUh=cmmmQEmW/u/KTXE\'5
;XN@qEQ0*g^oVYG&A/7;>RE1E#&m&+9r>.K0EFV`9<_VSq-I"\ogcJ]A(X7P<kOobc/O(Bkr=
)btT&Y`B(Vj[tf&i5b<73Lfq8hEn`9d$<3D+)lVdlpP/@LDtV>ke1YD+LKY-0S67Rqf)VoAL
j?oQVjN@I6?'3CLpE8NUrhG=H+U<3V?Pb;p0P#=O-\46kD2sN`Ub]A#TFCD[XeI2]Ak%+`7[ud
&)45J+/a%auE!QmRPNteFa!,PfPJcQiUbj4&.OAgkaVclnA6DRi1I<8>B`!4&lR1\WXnlJ*$
X[^fR&;8\>;r!>;&qnd1liUGh::,6.(!PGt>FH!)s002_^dC-I`OaA!1+k92BjR,t+2;B00B
uc'V[gH[qX\Dq)+A0nN6bjKB.,0`4^RY-$t#YT$%na>SDni*/c5/g*DmH4+*_V^5]AXa/nq3@
"g1L%hgKhT`m$eB-)\d0Y*N+BY45bUV5LIn?2t=JYo-fXU;bQSXNDT'JXV$U0@"mE]Ah]A^'3F
%BcKfFp:OCN4g1I9U($D3pp7dSHc:iTq"FF3?^9?`Rm683HK4j5@W4H`WbCiJY+cnN+(K7/e
*N7Gq58G-dmIYn?lC>bU:[7?!X_griObk(%X>")^4R>U^u-APpuT;)n763Qf[:L2#nbakk^o
=o?>[B/oS+Z]AK>\B+`gsi+sqDI[/dNXqr?3ftQsiHiXhkQl@*3PW@M:P*k:fgmpB34mqC1i]A
]AjHKk!I)r%sHO<)q?\d[IMFI\d4bZB^,mRC#*u&`gQ_9t>aDS1kJhr@dgE#BQ-5nncV30HR9
1K.@>Ad?7=MLeH0&*sT`T955-a@PnJAkt4!rs7dZCa+4UN[,$]A55<nCVq/KeiYZ$'dSgsAA1
TL2WI)P>pOcqpE'"ZDG"re]A.b6]A_+hl#)(]A8i3**Pucl0^tN$>SG.Or5+M*F;*i\9g5bO^L?
bL#Tu<m@2,QHLI03p2%n@*jFu#16ptl?q-N_.H13?>#GL4WQ27JLPWFR]A6bnM%AABk#WuP2e
YAlY=DRF(b#OU%L#e&Q.S),]AMo:e]Af;Ch5[0.*b^<(FP=O'4nn8q11Ka77grS.Be>j05d;']A
Z`(:#+Qib#,Mc2o;HSbbG>q0_NpN]A):Phj*6[q<\9s-QCYZPp;gm!4J_f5VcRq1Y9k?l;hTn
k;(Kgd%5%Hh4Vdr=C%/EuYt)n=W^gH6(LC:!:!SnE[@n$^fa'*0KPM$RG.PrMW\-5Yn?NBiZ
r[d4P_d;$?]A.^)=XCg-%B_7N"]AT_Pj4j[35_TA6na7=Kl]AFQ(;u$tZ;pKE'DJ[2#%,'o-k'%
($:>n:fL%qUWjH=c$(K%gN*X,i2T)RRHC?:\=LdbuVk;]AF"EQK!YO'@.b!\+'4"(\lEl2##Y
C/a/X=Y*t+7k^ElnA(m#PQAFXs)e`)o=W*[V\JG=&7*f[^l=.$$mi+,V8rBtpA(3>bS?`nK]A
p\Go*5-'2pTSVTKU"Y#?^%YH:&AL1!7VXGS2OKV*f<>GW=CV$$Eq]AOdt,133GC.Gns*&!acA
AJA"X^EM,`YUnuT&<N^L]A#Me5ZLU-7?Pr'0;H.I.b-.W3f)eQJC7FJE68KKleL$IT2&Rsk<X
J:J5Fb!l$)>,O;G5SC63m2Dqn#i.lmbO:NUh=tR-\ZGkQ!:.n7)sX#2-ShZh6FAD)s+rmF6l
$Bdh@",$NqBqKTa#L<r`W[)VUY$Vt:7G;p,5R8#oVXH#`d#ZjjRlO*gH1L]AgiU?1r)Sdt5Nj
"mG*n^E`:@B"L&n4"mO&`eQ?AdXiu[s52tjD/B#!".W.\6a'MAN<QqhXo`N-rG9Ug7i>+ofK
e^:/G(E[2I-+^$sA9a/bi-+^7tblq&Ls0LL;Ht%>-3!"pVS`+P.da-7TYe0osoS\uCM+EDS/
mbtJ0?W?/AC=QRf]AlN)76Yt'^V<,h4V%^nE4?T"e28H"F"kt=FY9i@T_Ou&O=!0O;f?X3uEk
Z\M^Y_^%_hV''E/AUm[*4,t.Q<fLR)qnn]A(*Z5+EcOPWPYSZlofHU$A3d8s!&Kfr6Ae.V2j:
-"JFc]A]A<,O&&FEO,.XG!\I>HY0$+TseApe*R(Xs-6Hg09*sPTBJM+_L.2I>%ajdM>]AV>9>dH
M<ib[Qo.iFXtO,11#3?jLujgX"?2)-qF75Bd[Wb*a0Ao6@dd"BD-kASF3I\7EP8:7FGHi%0>
+Z*q%^<2@I$>V[sn"r3^A/ZcU9f`H?T&(R7F;\%Vm)ZH7-Pp-_?PjWle[(o?$q)=><<Nk"0m
J,k`5TCSq(qd,-=)&ia@mcU-S9;aE`DrjOrMqVM^R6<^h#pVi9^Ksf_]A5Nk)Xch@r1HIVaO;
2aLP1#KP`ANXc6OJB?fp"hA@gaui7cKpXk&UO6=-c_.TXDMD&2"&5,*.=m1>7A>G*uV';61L
?8FtVKEKr=QbrV3ZZ4`(T0(VEg]A@S)RuZ1aBD;P4cX,&Wfs5)?U'WS^H(Z\8D$CtWtnXd;C6
KS`.]APZh"#`Q\[.gNtJ1quT%nCB-V8!_WNQ^i\!k7]AUY9!r=(AR?ag<$nA!)RI&BY=`g,NDm
7=P*1e/1I_t)\D#O8*FRKHEHe!;S<Y7`X&"i<I*UT;Eaj.(WkG"NhU:tpHPCN$S*92hAd;%4
fTiQ!X&T^aM=A!Ad1oc3qkj+Oi/,K\*9k7Q=@fl1B!o+FQ(I8@93,9XC2C%SLa?g(`O?cefd
Yt?>.SM8oTN-Q(!;A<]A.f8T7:L))uWXcV4qJ"0hlh20qKJ=Np3^uLO<U\V]A4@TD<$L%:/oIB
L;iB;j[ad3_0Fa,IBWqWJKjbp4=8W`iP/oHVQ%kRaNZEON)7XLF3DA7#-mO=q5lpU"Q0.XQX
^Rt(H:qZeR@e5)EIu3$HZ]A>%0.CJdHlGJ*-XM(i,BkjPYJlb/urYNt8f1jBfK5kB$27Y$co4
_hNC;KdSm[]AOk>NZG5r9G;B`SP?4X7]A`U&5-tm(_fR8CHqE(XeSA:mtTJb?gg1sK5BR+R<a>
JMh!C[JSZ<_1sipc\*5/1Dp?"rp#^#,!4)<lbZk)\0tS=q@d$5?4cg:sBk$?pFBHd+\'LXN]A
EFi:>YFdSq3LZM>?XUnNKt)dqI3E=G=L6m;[iH('o_^p[:`,i[T,ViQMK$%WDK(^P*90X:F=
mI6`8WtdO*GhE'mH('a7bgTIh:WEl1-_7pFQ^g;d`WV7!L.Oq"!hl.`ARr5Vk-d_/HSk4HW/
CiJEX@E$K_k6#IdkYGbfN!r/Mk>Zn<m7\Z?KEMt>4s=Na$Qf<RN[b@8ilorXf5B0q%BN%_n$
lUl*07oR\.*mIHif+($gbK-'p/tS`=?-m6.?A]AoYKWtJ&j!IR[iV2'd%n]AZI?.M<ZVt$]A^`t
bePF3JS"X3+pS$>nZt_f)G5Dk'^?fVUObO#<CG@#j)DTasgat*nE%K&c,DoJaKIg8XTCV8Oi
32pCY:WJkp<P`bq%JV-1"3X0V&Q6^[pKd?G5+Sd"/1no-4#PX!`%emDoiafcH_aV\+s3<k\8
(]Ao(n`N,="0*BEl;5V1G0n=RdCmX^h4l`r%`Z\+mT^,X$nO\0Y8C-n@;mfIIQg5L_As$]A@kR
DV05m_\/s3MkC\2:`t.W'2_IYF]AQ4NPE,NM1INH/bT$7aCEVeR'.:,[[*QXsaOU)eRe*O@\Z
)kGRlYZhSUNSX=<Ad;,`XJ'*as%B^)V9A_SbP\^OE&S_>Tr/Wj`m):/oV2:p/"So-;B?3oR[
M3l8iXJ6=G+HHf&*LtfXA]A?CjE.mBH*hj#r:qs(I(Tb(>r<dgugPdR%<HB#SpKJ;\^Uku?fN
%aqMM=h0e-3ZZKX2o4ZN)]AC_XLUnNf9r[gKW#BuHVG]AfGo.A/7.JRb3(t7*</+(/B]A+pROPZ
fASej@4nNNU"N/$16Sb55fhXHO>2#mf>_<JN#QQ[ud>C2E1;fK)\7N&WO-h9D9k.D4OBr$.<
l4>4IO?Q[+Um;tVX%?=(=iDio8j-dQGdE4(RZ(Y1,<8=l_Q=o2!4mC=8o5hWK'5+;g1H+KhQ
1^)#]Af==lG;TVSPnj.Op2)'DqJ=+HunFrHDiFiTl&M`=J8WY[$M5/RRdR.6D>fj%6ZjUbs_$
/@c\8""1Pb(_$IiFnp22^7;g8@SM2"a]A?Oq?ZTXP@^Tc2gTP7H)g3%As`^M-*JV^W%4%%&68
l9(A;)2Nnkb;=STSrG-0>K-DZ`Dis=t(Y1T(?q[UgM<4"n-csCSe@dD036,*Q;MK+CjZVMK0
JI54Qsr04(m@r[pnk"]A$tL!tN&"2"Z]A@T;oAR&\mZ_c"q/_ZU)?@MX^B6]A=AX^7!p#]AZ<^Ym
Y%s<k!:\-%??8ZPSB[>nd>u$naQU#6DH=MGN(Q:'drR3PhA0bHWhEp9l:<K9fFS'';]A1>Cor
gaQBI:iV^**Mu!:(Vh*&@%='"b7FiCV?X)p4YN1H6(Xa\%^B"S(QCOT)UW1X[cD>rN^^R=@d
'5!'is6&%Fmr_7hKS<oKk/jhZlN!1r*lBf7r$XYO&hbW-T[10Di/!-J2kNh*$hL$BX`D1@s4
@C5K8arZs@Qt^3H[T``Y>=>He2]A\&-b<*#VN%#>]ATM,?qag6hD)TS$Ijub5R!Y:aI?;U5qos
&=lBBomL'(BNBDhM6G?F#1.WZ#pCLeQjpp<FTI+mZN_fL9>fQqCa%#\JD=tXHcag@iATlV#m
<F`<T,[85P<V6).k,!^)2r/[#$hoM]A#r<rdB]A9t)$&OSglXc@"c[,Z^f2(-Zi!.te)fPms^^
[dS2rFnqJ/`G0DSQ^k!WSe.,p@&+7sqS)n8,4c2.:TKe%WutnKTt&Y2=`A(]AW#U,4elhpe1M
23pHJin\5bahYb7G/7rcA=hcK?+Dq`LL`=8AiWaCT\3\mmD@ea_)oN3F#'#:*62XN8hg^-JH
-/&uThC?iS[COXId6dUrs\~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA3"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="638"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="674"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('REPORT4').style.top='-12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[38100,1143000,38100,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFH\fdT><+E:7Z*]A!^=6h,]AQkmUiRpHgts,_V3.I0"nH&U4VAG,*sVbaVR@==_Z`lXDM^diD
rlM<$Du4hWcO97E&R5Po!@u9FqCPp]AfMgle6DCIm&9DJ,Zr1Y)=CRq4.'TSb##%Eshm]AG&7-
7\,P4"_.!;!j%$h'E*/=/npP`Lnip?R&6K/BruY#V3?4GiV,`TrX4+ct(r"XfHko)fi\";[j
o(0gal,7efAja1kjhb:X_EttBZe%F"5j(HhDamaNLi<VpgW/f?J"r%`GN$N1pZ^Ag)``Zq^q
,:5!+/<"s=s5Xc9?\/'r%obn*C+%;M_;8^-J+,2uD<'Dc<m70HMr]A.J)D0"C":DhU0ofX:=+
/ThF=>F/i@n<NFX\Xf]A]AC_s0UI_$)aacm$g*=m%g37B^<fdaa9rnS#'SZ?Fg"Mto!$]A!$_+C
0TXgm4EV9Xp^Y=0E',W1,gt#FYL:,"I&^3oo^ECKp.sNc-)b9[JQ9/.1tIai\6K0EeP2?Y(L
G2TjUUXkRu:/N<'@\lcaKYh4oa;"S24S@2X"b^dSLUN*aQVU,2q6<qa+ack/8j93A6#P<>'g
4M)&/O+VH(g8VA8RZs1j)<+h9O>.6'L1?=R,'J>PZq=Vj^2]A.MeHGO"D<johiUpX[+BS*Yg4
jnT.jR"l\@9D'pKI,+CR0)=1mp\pjB6CFD(HT$Il??&*Luf>*JdA3Gj96)CNr@3K.@2j$hNQ
Y-$>Qgla4*k0?Zm/p:7i-W:re+brJ^DD1jLqV&>mC8#6MAAf^@)\#dLreD".e(!?2j:F7BA[
;@/_CZ.gMB2.ujB&'<S"kXC@&T#mdreHQq!OG;2M3iU@E=sA(2^^nl"N:`0SEsjicU5\?qED
j,4@cr:#adq>.,$S,>oqD.>JATV/EM?5AX,/Pt/j0#]AMVlVS_1bNSs-Wop]A!K84/VC?Re,dg
umj5qP'6rB1&fQP[SNSTurmC)(++ob'`3S1+bNjo!=+[1)M@L2+a;MT8j8`=RnKXk"@rc/q0
@>P<lc!8$>g)V.D?QD.@JANjcoV+"VI1<E@Wf;5):Zr<G,hq_ZSEk`5s+@QLqhrcIIP(Ab(@
A5Z9sCKtR:-ofE2MN36CE^ET5Ian:Ze%A:N[XBH'`kK%$pA?4S31dcn&D/A07,>NG-dPV(P6
;f7ALd9Oe,!Ni<(qp1'iW^1^HK?(VW#MDTa)D_!N\WLBni#O^SLXIOpoTOh1@>;ON$=$<,Fs
]ATc.3V-!fj9bH=T/hn^L[q%M'6Lo2\1WnVssTPJcokY'K4Lu^+Clo*+l287FS>+Y+O6<C<O"
U$_&N:FhR`Ct20;bJd_;L:0j;tM+]A>6D,?Qgmtof(5R,)D*nA?i<Yc<49iHX#_]AGjoM%#cWH
c0-(L/R;ij^)@L)'Nl<I_r7`^h6ALN;P)Z:gBB81'60BMKo%8oos/D[:RkOS6[U,\oJK)MZ8
DcT]A![jZ<8,iATJD#C:.kjeNXer=a-ACF6`"jC1SatPWe.a0rS3*#]A;VJp;&V;^LSX#^1@kN
((sl4*AV9[NMe+2#;Y\iR01qG9%Hf5?Z@hV3R:Q'?R3c!MijT=QGBeM)lQVD[A4.Z@akQAfR
jPPQsW4F#H)d:M(\q;qN&[d99a1\BtbRh<Bg,\Xe@Sgf,;jU-?G85iM-prej%]A(E_C#jQ)`l
00p`h;Q*b^Rme(Ka'!TX$]A3?W:\ND;p&4!;#[9TBcAeHC;LNnYb&2lPX\f;TNX%;hP^3L?T)
7HS3#@'>PKZ+<SjH:Di7CThjnC*Q'[MM9.[1gC3_\bIV3=-dB&nb`Zlu9;K`;j(&^7U=YNt8
?4Q$diB:i2(T19#jY[$Nka!^QnS6glXIR4LW-KeZDTt*rHO0iW=n>X]Ak"9e@QcF>@Gt?,Zhk
g^Y2g"`Njb,s&'cs8&$FHW8g:9/-,(mq`7AY^)JIL]A>[\7jA`(R'4"cnAKFD$D`)mdGe>5FA
JPriRl-InjNEW@2HXiuI7;4Mc=bC;)^/nIqn)s^?'P0Ir+j5`6:[K0M5l/SV01fVZBk*;oeN
Z(s_3fDe\UlKqCb-,Ro_23@aWXU#2Cjg/'I+B<8+=YdN0^IAN?mtm:h5"H*W6[o@RKm%g^iJ
dgiAt2+$[%\T$Po=<C3QsD:j3/RUn0,9D)`8A*mNtH]AF'q-eF?o%ef$1gNF/lWmn'mS*]AeVg
,:He+B;TU0'Wdm035GIc;',E7oTqE#PaGL(+e,*/gKq2kWYFKXX7bf8dWK89NAhM4Cs+B1m$
CB:78WlEI@rH#Wm"U&k:j$6_8LAOq1s`jNDX^%d$Wpi,m"f/gFN?lT'8r#OUF%_YD<b[oH\,
C[($[^3Ek9MMp%:%,[.H-8ERHK?a2>^A2t=l.Yb7]AJc8Jb:sdXc,Q`:H,;cMkF%P"*;)-q86
>bu2Z?jiq7<VoioCi[q]AanBkPDWd>lZ<a?T5<4-B/DQhE9uR@[^&OiEuPe&"q4RiN\*0%WOZ
p`Yf1uOXjg#=Es"Jde[*tWps$RAmOkT)hGn5*jC\Ofi'5C0$%$b%Xl^LfPA81?!Bp:N4.X#A
YdP-(E2A3De4@c#1etPqZ%VY[;m`Kn`2[@O^0;</qY^G2I%tL9Ck<RSROuF]AVs0Jn$W5DQ+(
9R,Bu-N8_0i9)hZ-i9N%*TY$]AP0'&j8f;-jqUX[2,GIaSA&<gY>0rq,adgMp<ebVT=!b0-C,
M35m<:b`s#WJkHUI4rHlunqPbM[&L#m*p7Xs:PlG]A(U4;eqPhf"]A%Ac_"!=!kfTKodA5%'=5
]AJ?6@\NIhUC6<Mn&.2p1./7P??T>qdkm5XQ6RRb3'A3dR-]AM>K)9KioI\S'n#BD)mGYG?<_j
A43O+<?COFdZ>d+QFDdFZCEI0R`<jq^@jn$eL/ck,1+lQc,&i+iX3-sVS76W?-mMcM:/Th>j
`4loflN]A+\iiKs4ml>M0o@u'gUS5b_B-'me@or5OP,XsR?dqQ(h6%-Ylqc-QgS#aRom59N\<
WVJF]A_uK>MT<;VMnki%XOW^aP-;L3G>;1QS84i)3T`)Vfm?O/]AiIl$dQMT\l#$)s5c-VeS2/
tf/`L$J04U9PV?(uFHOBc&oqB#EI[YRodfcZrDJgG<ob=-/"/<3kj1c\Tt1%6NM4[.5!X&2m
t:s,h"pLp7(i8dnU+Qpn)o$1*P8aTnCJsqrGJ/XKStYD3E>>cE,MiM^FqZGFFu11R;qZ5cbI
V)=RsmF<tmep7('HSmi<#mQ5iLBUVilsjiTUHXkCme)c]ARi0S=SsA0SY($YCW$J_s%D8D/P!
jh#d,=MhTqYGfIer$7+".79sej1*HrIqt0_5H(a%s/qUSV50rHB9#>S$"^aAO9-W%p9,Jq8X
-+mPLdj[Hn2o*_h5t3.I0O&>tb`,3Sqe5#UUgcj?TeeBsr)t'"4.N'dc6P"u)*%BYM$!I%]A4
ajd,U_$I12:K(C;9AG)'G?M+3fY==Z\V[R/Qhs)ApcYO1Z(<I$GSp5CMokZ/AbWd]A42hopg5
CR[ab=^#EEO2o)`J'"388gFAb)LI+($]Ai.b?i3<n6Y;Nlacs&d8_j5ei6dOg_IP8DsY0Zp8M
1G?b=4:8OM4,\;9;Z&n-TM-[QmfX2!Ysg>;^t6E[b"(Hj`V%5ci,q0B;^f28RjY!%ir>8aBs
M2HrKd$:luK4N`"#:i2jYPLh0c"+hh>s$mG[;`YKjK((WXQe+Mn4T?E"]Ar@%_M-?.\[/\ue^
??;o=<Z6)<TD$5_LNRPKYOe@Uc=^O=\lAc3>_I&>q*iK!oO&IG;n9/]A;E:qudKa7m"MZPXR6
#hP<F)SK#52f\+OQJ$md:8a@5\\4YWPO9g-3c\qSpce;VT:i+@h:latQj&XUljbjKG8V]A0'>
;+\Y:+NkU053ORj08C5)89i*hYgtBIYW,*Y24IFhC;W%AVV@6BBOup?XLN3s(HQB*Bs>)D7-
;9NWU9Z!!bL!!=j(!"!$5!#<9L"%WHq#*8ghss5c.&#eXY9D19<n9m$s"oC;D?02kpk-6.Nk
YB/=]AIQhXVFo+@J4\X_bC:=_&Hl^2gT3dQc?iU9G^]A4QmJ,p'd!!=(R!!Y--!=W?9!uSfRrk
IJ(atGSKNt\trXur77)KqqPMhKmc!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="status"/>
<WidgetID widgetID="0a94d597-ede2-47e9-9a55-29965f037fc4"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(0);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a = this.options.form.getWidgetByName("DATE").getValue();
var rq = FR.remoteEvaluate('=format(DATEDELTA("' + a + '",-1),"yyyy-MM-dd")');
this.options.form.getWidgetByName("DATE").setValue(rq);
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a = this.options.form.getWidgetByName("DATE").getValue();
var rq = FR.remoteEvaluate('=format(DATEDELTA("' + a + '",+1),"yyyy-MM-dd")');
this.options.form.getWidgetByName("DATE").setValue(rq);
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="stopedit" name="编辑结束1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var rq= this.getValue(); 
window.parent.ObjGet("set", "rqsx", rq); 
var url = '${servletURL}?viewlet=HuaFu_YDZQS/1st_Menu/考核督导.frm&user=' + user + "&level=" + level + "&pany=" + pany + "&date=" + rq;
window.parent.document.getElementById("KHDD").querySelector("iframe").src = url;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr enddatefm="=DATEDELTA(TODAY(),-1)"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=today()]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[预警追踪]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r;t;cb$\6d,<k?<aOf&h'mt]A2_[+)Bb.:W+F^t6#/P?"VR(jUbiSg+i/+2="TOY:o"8sK_
."$D.U\:,[a/p"B2oU8M([/+UQa'Jk\QC8ci6a2FM1js4VsHB'9/5kFO]A+htPoIIeWRukKGr
O#;adqJi3fV&;ZE,rq>&-#XffBr2a)\X-p26TFm"I>Mj[Bn%5YJ]A:lIj`PT<d_=`qeT,n8#Z
g-uOblPo$F^MUo`,GY9r0monlN6g8msR8[r*e6KD6*,Crr0hl.unO.']A(fhH:,U61I6f5A'Y
9orp+Vg++LnY]Ag`n.s&gg`Ij_c63j)eJ"#A`nV_BO*X_?^Bk/4pU[Aj<@3IPI/[hAjl4kVVj
)$sm8V[Fm'>d:F'<W!Kmil>WjI&5:oTedEg#k/T/W!'3&LVeG80/N!_bYF2%KE).W6K_[o&>
bZ'4#O55-jb46_Ptjjiq\kMG0);[9*\4,_:`ILVP@![=[K-oZRG0rJ`*>M++h773Qn(bY'm+
d-Dfj:Gu*VdN4T^WCYVi2?#_d/3;Q/[L">^uKa9RnF+WJ6)AM(^S^d(OH)a-9icFMRT4Ac9h
)V%1-S?Y6).]A4.)]A#crLfs@,B5PC5lD@]Ae>3m9s8D2e:e5tRd(d^i=H)FiPMjW9oa",BSjL-
U:Y&S:6dY]A`2MuRF&mM0SX![c5-@rP#101EF0@pT(Ro/#_GB^0KLP[A/VJ1ULeZ<N`X'V`Rb
c5*ZfZb\4p+=m(`YGS5i)W.XIB%&gHVs;&ieYt$RV]AkTN*&9>pBh7$6-)8b;D'"M9:m]AatR2
kUrq"X&_Et5m"@$>9d'\H)G:8:t,\C/_N4PID!+Z^NIcMC+-KfJ0n:J=N)KR:n+nQm-4"BN[
&4+Ee6JNFR&Tu5G)SljA*makC='A`I$j%KV2P/C[SIKRQKN',R+7i-TEJX^$lbBV*r33bL2U
t'PFU,Ju0\C-d[8MZ-aeo?Xc4D&D9[1On8Z$![E9'9ec[U-furcm)EpIH`C#7Cfi'=4fuC8T
'k!`,r]A(JI'rI.omd]A8"-,3u%7I9`/:]A\;7IJVO,\;1"[BW6Y-[>jH(*,#acP3?P^1lZBfDk
2%R@o'guTedL.ZbadmLJbYoEUJ(L3^3K7@HG@YK=rcgT5**Wf>^-EsD:L.&WWlsrsX1Hj-Eo
0O]A-8#dAA>h\BVY:FBBIE,.85$-?km5uo^sl*pW2'`DC7D>m>a(k)-e/7q'J$th#9]A;3+gKZ
j``$AY+O-[!,jT%'F$Je[6fslD:C-nrm<hR/K[)%[[Pp+0\_.FNY'VeHlSi.IZs)^TN?-4Na
U]Aa0W&=j5p$,Nl\4t40]A=G#t)Bkdnai2]A-fNdUs'kg+$@aQ%g5ZWJ4%j@%@]A]A[9c.X($'oo'
/ulMBsh]Af2`BR[tbGX`98>QV&4K4T0:,`QT.UP-O0qqn#)2n(2-+*]AI@Q?7?(93]AEL/Xt;:\
G41e[]A!!S>R/4YWAM0=&-U%*H*1bqlMuD[drYF3*Ac$f?U8ggB>>#s;"!XJ`T;WfPCTVbfk`
rZ6Z6#G7Cf[+<2D&cRS>i8mlQ[BdiD7+V&a)bJNi1asf4UBcj7aM9e#7SArJ,QmClN*6f)_,
q>jBU9dkulmcIk,#YO'[*<%7F]A9k%b5G=i+3dOu<c4HPrb.R[n[193[&.ci0,2cP7t++gnub
"IpY]A;p3=)Ok&B/I^e5UF`lRT[5^jL!Dp+0Xhgbq,gKe/Cd;,1cf^;,GFED;GNk*bHX_.(I-
ciItSO9X`h"?KDQD:A>5!:C/$>&`%(&V#kt$66g]ALl+pg'8Z@Wco)p(*kO4uSMQk,.:6LK:&
GI%J7Bs(gLj(Sibd\-iPBZ3eHdrDb<:r3FtA?SLY-M:#s/n__='U1FT$0mnMn`#J`D\@.4F`
)`2b]A%58"k?kWG/'-]AnJW3g<*O,SZ7'5WZk<4@Q1A$kY)"fQ]AE;WYHD3>]AZo4A:#J'jZRbn&
*A2mOL":V]AWUKe!a)mGIta4?R/^C[H3^c&:0RD_g*]A_t`lQX\!?Wcik&KH3!#>_C=oA7g^%C
)/h%NN]AHnXgfFYr0maY.$qO5,lP$1RaM*/LTud)O(I`PB(\;6/hOIBD]AUpgS=2LI:;]AN4d`O
8GMQQ1t)l7*5S8hWMH>&fUBcOGnq\AMPEm&FrH.&X@7LuE1RVUnA,YkC3,9HI+n)3YlcZs;N
/#2@b`$drE.[O`%7C\IB4RZ7lb*lre\aJP8^GIX!\HSj?6KbDLL#AQu/'U#p!c`n>,">ADNI
R2KF83_P5$.';UQ9e*M_?6%agTUNF))C[(;TG,+8=6ZCi?=Op<egC$u*,n9l%sC-F8/B'A:+
,0cm*9pA0)M@1Q%S3BZO@OrZI1,,)\=)a%"-d/-L*]AZ^MJUYfh><sAAcTW;k%K"bIhUn,c60
#S(8SflQQKXH'f!p0)-H1p1X+t'.HBdeQ/%lYgn+K[uDN1"-H6?Pm$4R"KuX%<rM)bH+nPLg
Q"@abeTE7lLO_,.qe.87[5L]Ak<;5`D9K3`,BPitC*3p/6#cM:]A_WcLll8Q'DtjDgUp0H)0$O
Xn90oh<A3q=X/fo2VoQD+-0\q=YqCpB<nY:KPcc-1$Td$3P*tNL!\e?i.@oMNj,aT-q[S<;G
kDq@);a1?2aD@MeM@ZlHV=@Je4TfIQK<@98E:4FNNQC%?*D*=mVH4!-bl16]ArgH4k7Le84Ur
io`]A]A+o)aPnk!H*iNu"\LdpSW]A;j1aNH@@J>[?)m.OATA``sNRY1EN^m3?l_2`/LQIqqT,\,
92+-8r^PeE"aq!Gbd%\#D:S=6KcR(>Kk3TZ>3C&GC.N99k,(Y>JOcp:L'69q*3L'"3H6:5GM
]A$6au::CHT6YJ6D).CTW!4S)n4lnc2P]A?a^SBDf1N!8,!9UQ;-(MI9kK:$cH`YPNh=cIJ2dI
_s(2#X?pak__/^L_[qPVKHUb4#X]AHG&;Dll+Uhcb6Q!ZOL+n;>ruTJ-9C8(3msFdKoFAHG;4
f!C>P%b_qJY3_>@76VIos9<eh9(qc[U8tF7)W;X,!$[8e>dmr9_Bg=9o:C+Y8;#YRhSe6<FL
$@3I3"58R?EoSpJ#PMT9a0>f^YAA!)6hpF7[!W~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="15" y="15" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SCTYPE"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds4" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_cwmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_zdzb_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="body_yjzz" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="a8af5bc9-bc8a-4045-924b-c5d06fdc6c06"/>
</TemplateIdAttMark>
</Form>
