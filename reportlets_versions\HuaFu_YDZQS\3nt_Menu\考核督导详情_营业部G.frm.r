<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_jzzb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-23]]></O>
</Parameter>
<Parameter>
<Attributes name="serch"/>
<O>
<![CDATA[8001]]></O>
</Parameter>
<Parameter>
<Attributes name="SX01"/>
<O>
<![CDATA[分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
3、本层级为分公司，营业部层级，层级（2,3）
分公司指标数据维度：公司得分(SCORE),实际值(GOAL),目标值(WCZ)
营业部指标数据维度：
		公司得分(SCORE),
		实际值(GOAL),
		中位数得分(MEDIAN)
		未明确：所属分公司营业部得分
**/
		SELECT 
			A.AREA_ID,A.ZBID,
			A.ZBBM ZBMC,
			${IF(level='1',"B.ZBDW",IF(level='2',"B.FGSDW","B.YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE CJ='${level}'  
		${if(level=1,"AND AREA_ID='zbsy__jzjxkh'",if(level=2,"AND AREA_ID='fgssy__jzjxkh'","AND AREA_ID='yybsy__jzjxkh'"))} 
		AND YEAR=SUBSTR('${date}',1,4)
		AND B.STATUS=1
		ORDER BY A.XH
)
, RQ AS (
   	     SELECT 
   	     	JYR 
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**  
总部指标维度数据：
分公司指标数据维度：公司得分(SCORE),实际值(GOAL),目标值(WCZ)
营业部指标数据维度：
		公司得分(SCORE),
		实际值(GOAL),
		中位数得分(MEDIAN)
		未明确：所属分公司营业部得分  select count(1) a from ADS_HFBI_ZQFXS_JGZBMX WHERE ZBID NOT LIKE '%_cw%'
**/
, fgs as (SELECT  
		     TAB.AREA_ID,
			TAB.ZBID 指标ID,
			TAB.ZBMC||'('||TAB.DW||')' 指标名称,  
			NVL(AHZJ.SCORE,0) 得分,
			NVL(AHZJ.GOAL,0) 目标值,
			NVL(AHZJ.WCZ,0) 完成值,
			TAB.DW 单位,
			NVL(AHZJ.MEDIAN,0) 中位数 
		FROM TAB
		LEFT JOIN ADS_HFBI_ZQFXS_JGZBMX AHZJ ON TAB.ZBID=AHZJ.ZBID 
		WHERE TO_CHAR(TO_DATE(AHZJ.DS,'yyyy-MM-dd'),'yyyyMMdd')=(SELECT JYR FROM RQ) AND branch_no='${serch}')
, yyb as (select 
      T2.AREA_ID,
	 T2.ZBID 指标ID,
	 SCORE 得分,
      t1.zbmc 指标名称,
      zbz 完成值,  
      '0' 目标值,
      DW 单位,  
      zbz_median 中位数
 
from tab t2 left join ggzb.ads_hfbi_zqfxs_ddfx_yybkhmx t1 on t1.zbid=t2.zbid  where oc_Date=replace((SELECT JYR FROM RQ),'-','')  and (branch_no='${serch}' or simple_name='${serch}'))
select AREA_ID,指标ID,指标名称,得分,目标值,完成值,单位,中位数,NVL(完成值-中位数,0) 中位数差 from ${if(SX01='分公司',"fgs","yyb")}	]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_pm" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-24]]></O>
</Parameter>
<Parameter>
<Attributes name="SX01"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="branch"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with temp as(
 select zbmc,up_branch_no,fgs,round(score,4) as score,rank,oc_date 
 from 
      ggzb.ads_hfbi_zqfxs_ddfx_fgskh
 where 
      (up_branch_no='${branch}' or fgs='${branch}')

)
, fgs as (
 select zbmc,up_branch_no as 编号,fgs,
        sum(byscore) as 当前得分,
        sum(byrank) as 当前排名,
        sum(qrrank)-sum(byrank) as 较上日排名,
        sum(qyrank)-sum(byrank) as 较上月排名
 From(
 --本日
 select zbmc,up_branch_no,fgs,score as byscore,rank as byrank,0 as qrrank, 0 as qyrank
 from 
      temp
 where 
     oc_date=replace('${date}','-','')
 

 union all
 -- 上日
 select zbmc,up_branch_no,fgs,0 as byscore,0 as byrank,rank , 0 as qyrank
 from 
      temp
 where 
     oc_date=(SELECT jyr FROM   ggzb.txtjyr WHERE to_char(zrr)=to_char(to_date('${date}','yyyy-mm-dd')-1,'yyyymmdd'))
 

 union all
 -- 上月
 select zbmc,up_branch_no,fgs,0 as byscore,0 as byrank,0 ,rank 
 from 
      temp
 where 
     oc_date=(SELECT jyr FROM   ggzb.txtjyr WHERE to_char(zrr)= to_char(add_months(to_date('${date}','yyyy-mm-dd'),-1),'yyyymmdd'))

) t1
group by zbmc,up_branch_no,fgs)

, db_jyr AS (
        select jyr,分区
    FROM(
                   SELECT substr(JYR,1,4)||substr(JYR,5,2)||substr(jyr,7,2) jyr,'上月' 分区
		         FROM
						    ggzb.txtjyr 
			    where zrr= to_char(add_months(to_date('${date}','yyyy-mm-dd'),-1),'yyyymmdd')
				union ALL
				SELECT substr(JYR,1,4)||substr(JYR,5,2)||substr(jyr,7,2) jyr,'上日' 分区
				FROM
					ggzb.txtjyr 
				where zrr=to_char(to_date('${date}','yyyy-mm-dd')-1,'yyyymmdd')
				union ALL
				select replace('${date}','-','') jyr,'本期' as 分区 from dual
		)  t1
		group by jyr,分区
 ) 
, a as (select branch_no,score,rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where (branch_no='${branch}' or simple_name='${branch}') and oc_date=(select jyr from db_jyr where 分区='本期') --当前排名得分
)
, b as(
select branch_no,score,rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where (branch_no='${branch}' or simple_name='${branch}') and oc_date=(select jyr from db_jyr where 分区='上日') --上日排名得分
)
, c as(
select branch_no,score,rank from ggzb.ads_hfbi_zqfxs_ddfx_yybpm where (branch_no='${branch}' or simple_name='${branch}') and oc_date=(select jyr from db_jyr where 分区='上月') --上月排名得分
)
,yyb as (
select 
      a.branch_no 编号,a.score 当前得分,a.rank 当前排名,a.rank-b.rank 较上日排名,a.rank-c.rank 较上月排名 from a left join b on a.branch_no=b.branch_no left join c on a.branch_no=c.branch_no)
   select 编号,当前得分,当前排名,较上日排名,较上月排名 from ${if(SX01='分公司',"fgs","yyb")}

      



--select * from  ggzb.ads_hfbi_zqfxs_ddfx_yybpm  where oc_date='20221202'
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="name"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=value("data_jzzb",3,2,$zbid)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="4">
<Margin top="4" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="fd2993d7-8330-4cd0-9c5b-8d0de6d4a6c1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2095500,2095500,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5372100,2743200,3429000,3429000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[XX营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="2">
<O>
<![CDATA[XX营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="3">
<O>
<![CDATA[CC营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="4">
<O>
<![CDATA[A股市场份额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="5">
<O>
<![CDATA[6,666]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="6">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="6">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-5699" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-5699" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-2299905" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="WenQuanYi Micro Hei" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1555" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-656897" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="391"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="169" width="375" height="391"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="254122bf-302b-4f95-b7a9-65928f910eeb"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1346579,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4694829,2743200,4694829,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[AA营业部]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="5" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="0" s="1">
<O t="XMLable" class="com.fr.general.ImageWithSuffix">
<FineImage fm="png" imageId="__ImageCache__4ED04EE233638706AC25A2759F2F5385">
<IM>
<![CDATA[SQGJ_'TUY>&WfII\g4*TN\1>=.6/")mRH4"DCMQK7S;q5o0>9qo]AYEs3)Tk^kp*okrdo@Vh3
i]AbQ]A7D]AG'8$Bol\eVM&D,SYcpYU2,k%ag!"q\on_NJ5"-uM_8:M]AXf8>u(0#6dmd+LufR@q
!\_G2b9#<YsWY>";UDd;^A"\WW2<guco7_hSL6A$&Q$Rh>6A(js-%[XQU]AZ<@k"F7sPQBA4Y
5HUNbOft;7o+?`bcOfGX'm]AXm;`M3i-2H7]A,H&"M4F3b2eV3_)I-@jp8_kZm!;(g,r&bPqYp
Y83nV2B+<mh`<6WS9@u%A7Wp7qd\Hd-kV1JPL7]As3YI+jrM(C?IGI0n\+l)EM!pGaXI<@EBG
<S6##5Oad)8<g55O8&372F4V-Mj9ogo^!'2mAMW(X/JT1<#GN#$h^'K$up1bPUL1qgj6H4HU
"`mcQOUGjXb?C^RUK-:7Y+?,LCG8I3*!D,Cm.7`_iq(rh&`I91>Y/4(oCTQgd*o!=qs]A0\YO
e(0WUODb&'X8,W?hhBT%?&+Kg)Xr)g^2pj`\LiBFeF8`0#ZJ`8g)fQ8>G<OBNgnSf"rqiJ0[
NfkAlC$'-;Yk1=hOrUe7Ljc_Cg9$QmObDk[?6AS4"q8:b<><2iKd+tqCTm"oA,Y\<AE>jUM!
d,KftVTHd_A5/j?\uZb4<A;9Sli5q.>q&T5-brbb^)Z'f"I]A[G]AB487[_*5r*&.oP)TRMNn=
dX^(gX%Ad2=WB@NC6)/&^YC.rFEj6n"VihGN"#WU7nH>\7Tmg0[IDE<,qHa]AA1f765CNs"S#
K2l1=U.R_cj'h@mplFSgsEiA?th0%/r^@B-:n8ALlmF?^@)1RX._!6e[iDQCI9i&3@!fYBHM
^A$*-k\ie-[f(#`PrRB:QLf7YN?4\`XPmIIIfmR6gZF$#5f78"Ug.3.B/q+ktT?f%he1`V_`
@FI5gZB3DpZ@,CS*Z,9NT&n7q^-9uS:Wr%a.8rY]AInDAIRc7o?3(bYB:+;!l%ZaqAk(j*R4A
B:iuA8R>+4]AVdG@r/aHFHP;c-a&*b!uWm\\)RH!5-\lBXP)d,[(=,F2kpI;5<MNg.p-?3:NI
gnsHZQIdakO@,FJE".#qI%A'?EA0qO-mX=i+9hu+c)K&+"auS2m<G6$hW=pL]A=&>T4s&'4qA
JgNs2VMP&kT=ULJoEqZ\=MFEf4;>X`s_>]A+6m4q%O7A;_5)N^Br\R(u"ilO_#q@V?rHs9QqD
?aM1iheRQU&02@)#s7D#e.Wbs8k4IE5H'iYkgT+NG>\,#'=6,K%h8qC^6ErZE)_pE'MWHAq/
E!HNH$VmEq=p>,NCA;=g?cq.!OG"cLQS:uQ@4`="1'G)d4EkU00ZSu=n=KH73cK).S<E=%_>
eJ2c"7;B2_4?5LEQXGTWQt4ce0:UWSO'+02O);g@au*Ch9$i7IdOXqO."[*!"T4ShqRI`9-s
I#c<k+L=\NJng9T6>;sk73$(tP'ful_"h*W.7e9Yk*(HVV-=?Af5#lE]A^hn6D1Ji2_o:im8B
-+5Qj$WtKP=Etq95Z[)S&Q\L`Q,rm"GU3_9sb=X\n\s>-ESJfBD8O]AS28VZR@`JecclgMoXX
M6UG1IFsk;6di]A2liO_eU>K2e]A;9IA%1b5)_*U]Al,BDdU#VhfN_Zeer@hR:CTWhapFQRZ6j9
)$hX!T7]AgDs9@=Vn4E'J*_FKe4<OiBAJ6;!3):)0s<0"$]AoVFJ^5^.0?(@>SiZL/*#;hnm-f
233/6*]A8s+=>#JQ-?@/f3)QZHK]A8hnGa$m)4DFFtm8!F$#o(^LVCqMO[&ni(PA8.e'S@;nh5
@DJ6,=$Xh7Ml;]A5+mBberepiP8BI@p>UJLPS%)7e<1#_H!82^$4OBmlO"q0QFA2a:_LKU0&`
a`X<`'/817)>R>?`&FfT?8ENE!&sE?cEkfG8R7,F^,,-Qg&=.@E!<+f4X[pbuclKiS!*hgJ=
Zp'fQk3FB&kgDRHj3]A1c=Ar_.7/`.tH6".,I1;%[L]A>SIs"5,bqX<GJ-?K_o%;2@[@,7K,6P
,_?U)JD8c+pNbLZZ9!GIekTD$P:MP4<Ha3UOq[b0$;j8LdMSkj*M8o4\gH$XW1eHAGO"d.hF
!u^p&HgOII"k(^"Da#Lk>Gj]AjgF2C9DH3Y1S"CN/?*bFCl$Zmfn:FS&48+"nh#!SEYPEZ-iD
rqHuQ[o@'>FRs=Ue_&FjflVoT933*o74(B7"*rhuIS)jrA)d2LCe><ui*&(Gd&%1h1iVkqiG
-g&`[nt^2LHUamKa(8J5iLt2t#pI)!U_']A<mCjdJEXq0NVHplH9uGnn]A;%I-30dlKYEA`AU@
;Ag+9pCa;O3a.]AkU7MW/C_Q[u.`_2kV@\NS&7p#Qh$/5lPr`("K&TF1!df?#INe(<c`54g&b
hCm@9>f6&_u:kR'A<*C>Uu^`!mSd$M$/mCYiJrNhS&c7>g)>CPa3pjI.>>-e<qMo@em"tmuN
bdB-fRllV6Z+-5.C@=U8-FXeU%>'SfgG(k9q<!%WK5]AYV^+3I>4kGX(eKWQo:/MY:d&WIA?(
_0Lpb8F;O$gM#eH'1a/=<o\8:)?>5A2J6"EX%E=&BS]AaXTbO7YUM,s5VH2SRDjWi[-<$R)S[
hJR>XIU!)#kiCj8Q1]A.P1X+>T$DXK3_2\g]AB&)+?+9FX>RKDnA)GqS"[n[4j5i226%.!r;G+
FUZSqMYIo_e<s@?Ag=i7A=a;8_&,fs-4*6poCM<M[#I?X9NM`jeHt!#U;Q7o0mLA4\/9JrL;
:A$TqjCFDo:#&;amU<OJ+tfN>8Fg*?CC7=D6T1BHD`R_iqK$/&EiQ<4m)>QR"+d?;4P7\QE`
hbI>_F'jSZPE_C($)Jh&[q[C/i/ARVBZHhL2%Ls7Rpbh>(8l"9G0Gn%@d2PjYNj2NuJL6&Kp
L8);F'^4GZNjBiN%V9q!A_h;gR.D6mOV(2.,[!YZEhFi6`M_C(ID?/)<(QGlEMEeJ#1s_IV>
DiI*>Gc85+k^J?:6-DK"HMPimn=I&=d"&qA-\WV__Uia]A-A$"e?gsPT",!I[U3.aO[WN4MFs
:STp\*Eiq>J<dpl$`_REL<g^,F@e<o`/4)'6W'G,3._lBr\/E>,oJAhg84h<'%^3!F.bnl9n
VrqJojoDGN+Y"Db,8=Vm-;kIG*aq4<Wt"J:RZmsrfKCJ(lClA"Os^N49]AOi-q/&/2Lhl6!CU
`#OW>gQme$=c&"E/s3drM%..1Yo/7EL9GZqL]AX2Tu&@ufJHkE)OAon<X1n,n!8hRCt"2/(^>
7tl_;4c.VO.VtnkCaWs1V:-e_"tnTRg]AhPJ#l,V"imkZ@GXT"iIN,>;URmoTKYC+Ugp\A5pO
m7TDQnncfs'r*"kN07j0&!u*lF9;<!J5F<>-TX%`6EhcpPq+W-LE9(piN:8<P2CFM'NT/`7j
21U=D2*.cds3R!%uYuN,Oi)OD3M;l9IA((Q!D?>^kN%ZnYIS`\7b7o#F`VQ?IXa_e+;(6O*.
CaJj'2d6W)<rt4D62^;j(uFl<V&(R6-d&J;86)f?`n$K4cdppMF:CCj,g_Y<]AEuL?_`I\@s4
h=ZUQLg@'bLY]A07/hS]ArOrIAROsPEV"IpL/:[;qR*)j9kDP&BTsM(shP&m,leP*eER6^?Ksq
"-&l):S.IV]Ad@m(%bn:u)HmF9d-RWN,qXP-94*W+?@1N+@>B3ao!/R_s$p6IB3[5<BSZW&[f
.CGZY!b<9g--1O)#64ahhhFr2&Yi:3$&7ge?\AL$4jI0g0sP*nlf(D04M2`kJC0\iQqr%4a(
I9rX2#<9+&!!X,[sb8nk<7BMr,2l)=XhbA!kZFq]AD_Jg6pTAS0b=o7N_@$<;p;rUB;6N-FG^
PH]AX^\;eV^(.Di=G;Yn1-I0[&/Bj]A%"LY_S#3i6,#dF'dO\<D]A<\Tm5hEJXhDI3H1OsqnS*g
5(djYs7JicC8/h5hHEr'![\coN_*]A)=uWHT?iKkB#Hb_o_\Ps%(`@^`bM>q51l/16&M+Kn[s
"1[b_noYVm^1upT<q`NJ_QgR<kI<<cf.b[LhjsSR9\eV?.H5"\_po4K6gS*CYf,"TXTn]A%Rd
<MM_s+&BbuuCAVD`Qb?fVQu0#rXRk1GQ`lj'+<lnNkqs-s%l*Dbs1DdL!q$.%Y1(jke8'OpP
m6>1.#\LBm&U\)+.k_eE'BQ$5(F[PBg-)]A5@)'V@NCh]A/j'SfOFeQ6^R6A_Xla#5:3)5adtD
VLHTq`D^&di)'#WpTRu"@COe$8a#G/]A^ni:Dgoi[(V?\NuJ"k)aZHdjV0%+?S8Q<'0K,#mWJ
;C<pFLJX<g$oD(a^eDlo]AHSki@)H,l`*p]AAJPhb/*C"[$R&A:Gr7Tnd#;/r$Ol5njikOlN*Q
!lu$='11j)3o!JN7KFr_OB&pd)okE\hUBs>g/+9!$O&?0ck%hI.!\YH$6D0"n0f%PK?H065i
W*+;PX+AgT^`m`XSDS6+o0,:4h\BjU[d6@>J0gTV8is/3uDkK]A)Ucg0Q%CFm7G4XroVk=h*/
67d2k(#$VEWkbkPk\9O"iL\U=%`74c]Ar),kq)1BF^eKoo[qmVsM)N`u#2%Z((m8051%p\NOh
rgOm'@%,+Ch5P/C*XW@M([<#)B0mQ,J55gd*1*-"/UiV]A*Fhd"ullBpO\9n&$AGQ@H17_c6?
:rB_T)EGR;!3b%i8;:Ia/Ar;_K0nM5>973NLnpSM"ZNAh7r?;#!^eO\p_88_?]A'2&AETe+7$
#/HDT_(DV%\U&*,'i";hm5?IC6+;>7GNmLbC#J71T=B=Acm`+YC3GYFYAtR'884H9CElfO=A
m&$fk.#L#s'aeNb/R5M6l.fl#5O]A%6(t,1pEBg[*kDsh$n"0g<YbR@$lW6LLa'TJ:5DoIU;)
9$aLb$e,B,T7]ACmt#D":%4<H<>S"3u*`GE\%IriiRUQ8Kq2S;\-UlTbe4t`.*Mf%P4g%$X_I
?7Hf8%/X!Y\12i^-E,EefGkh;aM7dTS/!rA]AHpZYbT@>APa%.TKI[1,sNOcR_2OfHVc`g@s'
]A4Z^r/8>>3]AYA#'V4PsU)N&rbAC<<SJ)`EVao^isaJ$7TCd_p>I-PaH/H#;?_VKa!1_.R)s*
W^KZmWDfbeM"LT75%FdhaA+VJH/Sq,\S.R?aj!"QPLBIe-5kboG8?O:/S&MCZLs*:>a$o7<U
9@T(QMgpd;QdaT0^$6cf;t^-e.>*ZJ<$GE:H(MP^6h>c2_ehD2]AU'Y1tcm!>kI3=1`!4>/(!
\*:7'@'0_`SGi-l$^=`cba[q1oPN$KtTo2.Cg#j]AL@tU$<g&(<T6NZ7H<PlJ89C,tmN09:Y%
r0`fTJltOnmHb7m*0su7`kLiJ9r7o'M2OBl2<D%Fud3PcEqhI+C#>lgq:m2!-4hj9/J7_X?-
\#RQuc/20hmEqn\I#$*shua7$3O,QiBf[gpSLMmJ&XJQ0UU,@PRr=]AG\;>S+Rs2<24QNo*98
/)U-V0Ku/rmMPN]A?K0GYHN+i\N#9pkFkh:_70S!iM[rV367o3886H?bn'\H9&ric[!5t$KXJ
8\ml8L1jIs7SP[unhHloR/Wl#Yc=NZ+(Mk&T(qZ++EQfpC(*I#lG64eZ4!H]A:kT;V1V`;K/7
qA*.>PnH">BojOekJ5Ip;0m_G6JRskg99Ej7.l_aX%\E3q/HJrUY)FKla9#'ZhotVS7iBbHK
kL.S7->q^DIIdu_2b\$k)12B))dgnK!SfXiE(C#6!"cj)ZuHu0*9^fnWOD702+fO#ckrci]A*
<A`pd0n-@o_3Rl"t\KUn3\*T;D^ds+u6l.G7uh7ua&k`T=fAOonKo([F&a;4t>D#nmIM!)hk
LNPuAWrT59QP(ogr$t^mWsc$<9g11ZR*lHtR5UZo:=A[;VA$e8@t2I=45t4E]A5*Y9V'k-[Z`
1=:Ca":5YUHgr."D*i/Ec"tJAtZ.Oq\6Re_^IiRg!e&h8QJ5!fW$*YsYpq*j+[0!MrnWHCod
.-!%Ud?2[FOQk4,N%nOeIhF'P9!-C+\HJ"iYjTo0InH;""ScbqDYRD&<oMr]A$jc8IK*"6(J$
fj?Ucb(EhE0#Zm?Ib-r]A=2-Y$6"/6qc^UXgR5'+Hl>7:0+T^A\YL9J>_Gh5mBid\SB5GlXSu
&RmVa7&Y)/!oqY:HpcNt'"hP[D)m'uIcU&>MRM7$Yga/5s:///m_s%_2j(OXT.$PopcaV4/E
45u+><g#&?GQUON)pbo:U%0)\=VJGSeXEu-&r)^'k9;.G14^]A!/i$ZkO-CV[Q.A`T@/ksTA`
n6a46VWkGb(;">*-KEN?!T`ja?qa>ti;tV+7#%dFX#H4,@P9%UPOrk3$qqs/`<:W_q%J@6'a
sN.,`0+XABc`=%mRB#i>2J&(B=`ik?^U^hYf3Xh9Da_MRGk*Fn?+c[bGn2*P#K\0hr#+ATX%
`3Ze(7^+B:YuQ3b_$Q!RftUZ"9QZO7@aSR`qlH\1]A.r'?JY8PfA[;;_g2C#9cm1-//9fT.a,
`$eO(]Agh":\E;D>**e_cW_q1Uf>\V0:9&&j.':=mn*i?$0aj[JrBqXk!r@6^mb=h-$D8TF8@
X6'VX1=7AT^ci6,4\Pa%>'dY=*kh]Ar+^$G:`[:Kp61URanaE/;%]AIGuZG4<#Z4W,WmJ8'u$R
Xc6G%P@<n0%!J+n<lp<KsrSlqg^+_n*.nU[Xq]Aj%\3FiROup?Yc&i(42U<'GC3,,HC%N-&^c
5q9OGJXiHBYMu0[W]A`l3*d7]A]A_:ag$%/kEV!^pH(S'+A_,b"2t!K7!U.TmWer8dr=:-i\??K
EW[L<[SW!aOt4b%3s(G6BI[(s1(hZ-[Y+"0Yl:;3Yc1G3XVq.KZ)ai68e?c5(@PfW+Ml:NUd
f#b^e`DHu1")^>g'^L,[p6?;VQ_`:5.ZVEn@0c)^'S'g]AWm0R<V$_0#4f1OaQ:qpMW@fpnVi
Q2DH-:.B;*Pal\hq::Ha4H>K!/KDC_Uf^b95:"Kt+koH+66i.b4dO)Qj8XesF6s5'jE-IN9f
BKqej%P]AdoEN"Bbg;*~
]]></IM>
</FineImage>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="2">
<O>
<![CDATA[AA营业部]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="5" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96">
<foreground>
<FineColor color="-878336" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1555" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96">
<foreground>
<FineColor color="-12485642" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-656897" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?VNe;caaTUWZ_-&aKP\D/3,Y'ep&KB<b@7-jYWZ5U$=4nqmt]AGm0F5<!9;8Ed9/=6imB5$1
BhU+UFF-4G=>5N?ht`'I'=r"=D(4>-hhTbbnTZf")skLOJXimJT7rT<lkXJ'$PjkB=9#0GnW
h\Y%/._-ibkld*!kni$fAj?=F)d-07flcZd.B@p=\YFda`4p6#n\karpk_[\Np5opLMIq:5r
Jo4jWepoPV&Ia&F^>S2fr-"Inm(Y,Cj)f0TU1O"Z0$2.No''g1;]A="`2kc[@n0Mj0,E;99Q%
4\)f8p'/&sB\nUfDl\J]A@6Zr'L3*Kn'*n.C'K&<#<9$4lmOcl_W'lXiY,G+NZp?'FZ@MgOMt
ZObofj8e)M.=_"NVb^j-[k'7IKD`p7qge:kQdZFP^/jo=\_VBFA&!@X\Wn<jVI@6pf#]A$WCu
eCt3;r_S\Xdu]ALUVL%NTE=mo&Thr_8(\7mDU-;(UY(+4<Co<NAC02QJW4ur\K6pPIGc@5?B*
[jJs3_PH<WCeCCOd4dRnDc]AC]AY1?nShX>g-9gsJ8mI1=X_%tQntnl):o/&gal:ss5A;O7`$o
eMK;bKtDUU9LXOB:Ra-3$_tWU7*$R]AM;grWfMfKXD*d?BZ.F;kgC7'GaT>91ub6H7QlGCM(F
NN8iYZ6b]AZ?I[IjF9=Ks9rP'MYo9Btb_6lrX'`DE?s#Ha,I\/lkgW-R&D9&sh\fu7*9?J9Pk
,B9itVuTuQJG#rU6mH43\hm2S8q0*0.aK[l-ZG>`gTrslOSu4p[VK5m8/6t96KtgYIM_G3+"
rZ2qDC_mQ[n94&0/J[nk_(/FbJ.HX7LK3VP:)oO`KP0eR$:\PlpUeELD3X,stQ0l-`tcJUhj
4QYBg),tl^<h2E6aBh7C`Dm:1'LcW0E"Rqn[;-Ie\\Dq^p*a-:s((?/)E=K5)Ar*c&nGiYB(
MAJPUfZAn9c[Dc(8UdL.7qc+e<l,j?]AZcd0)A&KYuimB`b^*`lYJUCKP"[K*JI?sBZOl7N;k
Jl*4FL!kC$8fV`JdY96;U?O(40A5]AB=G4\YY`9<fK[$AH&hh,8V-5BY$2>J82T^CMtdU\*-?
fXu5Ol!*90>(VQM9<hFGP!_2qdEN"??@eJUIWWRub&q23.,-Bk!(l<oLU":fn."^<jtRlmN@
MSUgB(dQ`EJN^3`2)L#$5[(@.@#*)e[O1]A.V*k40eX2m9$-akT'LD&WJ]A/U^#*fE^59h@#A"
)Ncrlcb;lC;PC#nT4"`X:aAQU.;CTe,rp5nsUCfXH<WXX0W"^0E,jgmIVdJiK@d%8Zb1!Y]AI
$b6X(WQGd:6kX!(CEtkC9^t"`C@"T[\A)sRj'YLR9YQp3.b"62T]A'L>l<nW-E8OPZR=@\FM,
)+qLd#A0jJN,Uhh6V/"ojqUR:K>PL0kV1TAXn[T*NIik(,F)N,?9M+cIt>gO^X-04jkc8ot)
,KV7MUse'N[<i?O[T9)iSCR[VoZ/tr4`gVf;_N=2(\+E*5)qWeSNQEhN'KXUi*KNo]A@4#k`i
^,5%7Tok'["@:8JR"+p2:Tj=0WTfU(ZJP5%Rr$$YYO>cr<\)S:NL(K>^9E"M6a>:L3T04)S&
!cf*jl`'mWHYgh/_do[/nKUKWpprf8,S4=*TgJ+a%=[[R*9TH.mR/Wcc(gFMP"q7ARlh^*Uf
+q"RhhA]A`MFjE!Z*"EQLcemcQC^S"E\rm-$/uIa&GSoBFa]AJ*Z4CFr';YA@^5SV3j0cDNPKl
W)>t$41Aj:!C*7e!t='t[sb4dYN*Z;'FO[lrB4X3]A&4BR3&!Z&8A4sr-KG[TmJ83@N2/<q+9
L[Su?go_#A<,HjE!3J4t,ir6G/!gBL"(D,"=Y[9g'KB_'DGk-H`Zs&MeNJ[%48mla4m>53[a
jKVk,%X?6M6#?9L-4]A-amu$RTV)!Bf!0`*]Aqs[d^O=DLUs2q#gl"bk@[!tT?b^C@^BO"]AOa-
+3>Q/FVeVK-k&EAT(9VXXkIeAH[o_Cg6A)%E`a[]A+K"%`4_D*]ASrS&%TIRDM1\bahQ`Rpba@
qI&0Etr=FlIrr%+KpB(aXnYOpk#*RM&O%JRtc:Nq?VSN_HQt)jXj9HG\Lpic(=`8fP=M`E[o
G-T2=RqPB.CRiVi"?D:.L5:7`;('@NII2=4i;O&/0EIP7,3V<479`TLW+YMkkW`O^M^q)YF1
VT)<lgT"KZ-uPtjGo5\S,9mXO:(W`";3A!i>PI8Q2`*]AM/U4%9-huCO_r1q?+8k"6MBo"_=`
rtM\O_Xl5Gk="/03Zh0lshjF@V:&".E;@VD))rc0PPJl3_9I\hRPbd_14lMfbk_T)/./<k#G
RNg)KB>/ZK?7<N>6kpN>7P1iOu@VYhf]A]A66Ip6$L8pnI?*#H^-J!0!DIO9u&fHu/X8XD/>"N
\abWmmK<3G88nU2m_50`ao5ipAf)`TBm-lbd.g$N%B(gYQnCmSZ\0R/M4s;J\B9dX"91;="W
Cer:A^f^?kh_ZR-#Ho;#^46Qk4_c*J&V<hmApC.a^K$gue%2.@%N.W;a/$a_iKk3'O$*FRNE
h=5VR07V#+2&-"PJ3kP@a)`fSq=B-@E2Y5"V(n32cb5Z[G7]A4F;6"Io3FaQ7ZqsB>$5[*0]A;
p6n-UOX<nm,)tRsg*_aVGPfc23/;H@h")W`'T$2?OYkr<n;n?;o)%MoqV`7^V-.PF/mFY6ZQ
JI;q#g+bL[l94,D5s0"`flA0*#4GQJ\O.&B\IRY0TPp!%`qoBdmnfBtY,&[M]AR,]A_a2!^Ds:
Pdpk0%e&V,AqCKJ#oW2+7GY(FjY,p0?0a)g/I8?1,6'h@4!s>j?rD?c#Ffqd*bpC<[#A>EuA
,WNgU>P"d8AB1;XjuZ@%8MpOVIpqf-nE)SYcoRo1oQ"(ZD5h3qI4+?'eD*QZq\]A,8dcEm8Te
T'8hlA[X]AUpQ-JI?_lLaC^0>GO.)9OD)cK&"oudD5+ur_V/^:QTI1#a*]Aqa5I1X]A&&$8+e+#
?/<#Mnj%a2ff,J;;F([!*%5lm+Lo%^PHJOj2>WCR;Cl`4bEAV;u/%8ENY`b^YW/QCEg;2+F$
,nHFgIq@\DDEs/$<7(&0ugEb!(:@n;$b%Xn%qIrW3[$P[DJ2P=ZdHXeF%'iZmK1L:4'X^PN9
!i391O+*A(/A[qo?UiV#FZH?Y@k[HQuGFa&B:-s95./Fl_`+Bfn#iA0UO*nBViX'8(mgN@js
,J5.jZ@MAiTm96ZXqeeSVi#6WDNZ9O(Z4$foEp[1"Xeu0JKb%cTZj/j]A=kI'k%,>aNK:l64p
*MDiBLmO*1R341hpan,X$njsf(U&tj1S9)<fS'r6>8c?,FZC_n2hPH;``AEX&Sq2cF8GlBDi
dgSdelqA\L686mDpH0r@UNBf7u-=pPIt_eAkV;_PXP;_arK&1+RKKVG$5/BAja1r<2k7gN(`
Zeol*Q!FbNok'4(o=P!-\bp0;1:jqiC,Tkmn.(Totr1HU*J'<>#]Al=RkRE4GR[+E4p>/XL+T
8q&ON3&Xah_3sMpC[^gjR,0rf&sN"1]ATFaWEA2a><B'2-e^Tc9=.`YGHgNp.AXlLkpY6dCpu
_F9]AqE[7Bj+U"N(k]A.4Pd*YuZfXYcf/dJftt_+W,eS(kNnJJftt_+W,eS(kNnJJg'ph+("!'
BkFFc4$I[o&6lNI<S#uB"r(D:]A+k9NZi5n4SQ'iRH_bWWo$6D8qgB2&CNM`jJ5?N*:^DbO#_
c(\Khr&/:^DbO#_c(\L!]Ai?\qr/':JNnD$Ft&FZ`u`!IHK7SIfK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="75"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="30" width="375" height="75"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="KJSM00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM00"/>
<WidgetID widgetID="97a69e55-3121-4197-a9bd-24f7bc0285f3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM00"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="9" s="2">
<O>
<![CDATA[PK领先指标]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r;t;cb$\Dge_<(+LP$"Z.]A7>mj!96O;YU>$$U3`2SYk5lsHL<$Cg/+QS)e$Rd:/\hsa:\d
mX6+]A'ZIRCHX\6Gfio*_V%!+pj<$9EX@Hlj\(2ci&'#BC6k0q0gOKhg8+IY<-fCd;`uU_Zn^
%!!"uHIX^KR!*JQ"s/l;A1pXNm&HmW46'6p4r&MAo2&\R$c/:h:9MrZs^N;#H/\AaE)gMXCQ
B\lp,aX##8AUr$Mju^OVI,>;JVo@Z00FL'qif1k:,S(ggqV`PKW994o!e9&0/\TA#?3Uec,R
_FS.`on2H.+NWI&L#\!t7M`['N&bu.>Tq^Sf"EmT7B+Nj;[fY?1aioKhT!9=<YUo^o:8\:[Q
j*QTbNCOM;f0K&rZ_N0q?5/\ljr`9XA?kT(e0`7c[)oN4X5+i8<KMVhW=%1&k1NVQ]AUpY^?W
DV#(,8t\6hn6k[+jp?It.G'nGGN2JKh?43j4^r?lQQsd-U17FUFjSdQYp&Cc)BVAP]Ao.c5%?
'-(m]Ar+Z*QPO7c(B%-$NFTp>k]A1eUU*SJ:SAVg;qn_(a>2NaQSrJoi#bK!`0:dEBB8Df3nB8
-6t-?'rm<Q"&81N@-_3Z-e&HoD7(H[ok:%;09K4*5`1j)Y1p\fm<0h?Zt"&N`8`^R:[513"G
i.E3`G;e$VWD4"O%1l:op(7,pLMqG^#fjV6`#_PVf2)a268<lLrES/n(@ZF6b.Quu;?j9g2+
Dq.lcY*s9tT7L%0A"aQ)EPdX!>eY4O'Qjr&.Yq4#aUbM?Rs[$CLaSVL1rd"1%*X+GC$K0B(9
D:Kge1\RZRQOFk'(*7&4'THBeVs8W<$pAPSMUn=Xo/shQp:p/SbJ8]A@\N/eE9)tZ+SI7Z6([
^G>D:A?i)6&S#d45<7g]A\aK;J7H5.VX6n;R\La?2KeF<"@eI"HcQU3?kRh^KPml$GLb"j[69
2QmW5Kb7;cF-o!!\RX3Tc:l`J7&(nA/`&k<b*01kj"YoXlmfl`PKCWo&?iP.m^A/hkMOhRnt
!S*;6_uBniT,iL?O;R.jcBqF+X/8akJ:h<Q;YoIkc/A\!BWOYG+Fe"fVl;L!j_1JFO4A2C5T
YbHa?lO-(8:WL=&Zgio(-.%YC7(=ODbS5ITWMs\3RO4(UNG]A)r29j[Zom):Le0REE-I(%G4;
Ja-H+'@S\)jt`;cP.@LX2"jkd.E^&T4[\mMDY9h.!s0lp+9]Akl"s+3e+g6ifjA*;TLl7:b(n
`%:A&=9L!&gE"gX;28<g`CL,q]A&Uc;%fnmQ:c')0$A;K-/1\$G42HSEOC+hkACsRBj3L!WB4
)B&XW0#dao*!%6&P5-Q+hkD\_Fh]A-BmiEl>ahn12Jq#!QE(K1^!b$eFJG(1os^>22tl'K%%b
2*/>qdcd9h6MS@C7@@%Nr`o-H<m4#`aqPhHPP2;GHsf)F2_0iQ=1hn&KW]An?;ng$4FeG<C]A<
jYU/-6f;I[Xa)R%GWg"&;u3htgC8?c0)\6$I#G<!JYpJO>OI;rF).Z"K>GEA7X4?TMFWFK1P
UL=0*PkAnC_5m\S*MqcZ7`.?`_'cK8Rm9WLkR=2&Uo<TkSDi]A]AI8qT?'5N"eNpKduRkZpgb<
\@AZ^_.&[b%k`c_-$^Pc/Ei4_]A*V`.1K!=@?j$ArpB0rMe,08/';pB#aF5Kq"K'FupX,G9fh
KhpZfH%9mqLh`f3rA-7Jq^@cb'=`4VtNUEV"8'`I*4P@psjMEn`H7>Z9G-6^d"RMSZ.@$bAa
YTn9`.N#DClamH!M*RmdQ;77SVJl`YLgS@=(C68;k\-Pm`;0"n8.3-4/uoBf/K?n;)H]AFWZ_
5o;$F/QYkL#RUT<IAbtS,hCB2)*5Ijr3T@,31EQ-V`ni`WnXq0PZ.FG^DQ,&2R;@e*e.Rfi/
95?*DYZ?PoRB#'!,___M+d?*n5_bZ%eo]AeEagH,f%09-Km4-KM;c(q@9G,LJiAlMEE`h1P@.
(-2%&ZQe2q13WffPF\eS@-`saNYKIa;r,BY1qW$(i,R(6-`6X:DCk6o%_9e,FS$e"LZAX0V6
2JZnF!JKrO^HD5A9n?bB4j:?C@15+W&@I0,&peeYu?hCm56(B6T&)f$V1`sa(?G(AF(Qsgmh
#tU[*SHlor>i]AAGTXWsJB!c\%r50WbF&nI<DL;%H=`r`^55OR-.P^T_V_i=9iKLQHa]A_;nBk
YBhhRFiunShZ_KroaQ^YWU'@pgk=O=\'aXu!hcAcD+#Q2YTnuaVfa_&*ii!_JugIS2m0*$/j
XH0,8cRk;WXEDNQZ>fVN-JmesW7kAtdapc=D'0#Zb1CSYQN4ikA=e/7lfAFk)i&6MihlTd;i
U,NeJ2dmoGlc+o(0L+N5V!uU4f>cr,*rT2jc><?5G&OmUGKFpZj@>#4^(h&(07MoQe&OmW;k
@fmq>WHO(j<ONVeAp5s#ON1/c7H0"hdO8G"nL61g2BC\YNPU`:a"`4MY[E^qfOJ%F3FR)%X=
GWr7(e5_GXJC9498#Kr)!fQGQO%$;4eV/;9q)o`%O0:b.Tnbl-b,[o;@[Y_VKR-KP17~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="64"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="105" width="375" height="64"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="D1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="true" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="D1"/>
<WidgetID widgetID="7c5a2eb7-8fcd-4435-b971-d13c14ca48c0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="D1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[876300,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[11620500,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[PK详情]]></O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320@UNS$#%8!"TSV[>c0#r]A1u19"*()4<]AXbX5HYt7LJVQ*0>6L2!-o
*ip$G6f"e\Y>]Ao#W2uQG-X*,$)IV^Nf8+b22E;hQ_98=Cal"E;u%5&_["qn"b[_ZYsIm?2jB
*P)4[LjnhYsNe]AajIRW4(@:B1N^:Y#!\Fq1P'PM-u\$\EAcRS[q*:W<b$03&p)%(]Ar)`'6',
fh71q4IU`SpTl'mD?oep\#JJ5SZsN2V'e8ZeBSsdVuLhTeK=4Qspuk)Zr"/W9EI14Zdle]A\G
#SDE!'jIcKd=L>T'&X5:P$\iS]A_07I7'h9#QRT@hG=ZDW/B9l,t2:iDEd)4;F;JKN`SghH\h
38LV&n#rqklGrR/^A"GkGoh6D[N4.\*&jHN1Fc]APChB%.3PI\#pU@2EFp>_U.5-/Ep2-q*AR
4-1oXQF094MV+;,1V1?oVO+f$[B>QF#P_mrA<Y?!feAfpNqoUYt,)^OQ9T=$+.N]AB7lIB/8I
-X?OhY,XT.ES8eDNR@90?l-Bk5SW588^SBfK,Q6/n=4d?A`EU(2pUSL\hU2A$[Q#*#>VcBFO
D(7p\Uh0Q_/8r0If&-mhn&K]AE*%5QYJ5kIq<u?gOlWsoJ+n<Xp[d5@e[$Q0?G.2\A)kRiYKr
b$[s`W&rI5PNs3=(Llc8JkF*MQoG.X/*H`Na0Rem$[p$;;BA_u1UPjIg"DcK"piRI#]A;P6GK
p]AI8no5B&s%gDDA?ehoLfP`?Wh(_"gs&2M^b&<d+*T-40L4t2PTs/#RZZT`mSNk/Er&Qpogt
l)+^AM=%nt5U(bL2@GB5iGs8q4isrUSbNj_r?(=gm\RrVPrFMX$,'hjd8p7"g>>f?De&hqe7
%;>'A7hr"!Fj``Alp6R[dHfiEQ;]A]A&R6lHggV,pauhS"'tf&Mr9?VcS3rt8m[*%;*`p(O(/?
g#bJ4gPfrk94o!L%smo1Gr,-Hst<CQj%JpJ8q=j/B4)5@7c.6dLdQtJ02OE==hG8Yj,K$+tJ
a>YA`^h3acD#)W;#uSdZ*'?t1$Q7_,;L;=5iVPWR#.jc4@O\YIqWmO3:D43AT*NcIY#UYJ`7
.!Ln;Gdr7+p;p&60mI$\YHh]A&RjK8D!ZD&NfUr55P$hs2SI&?"Ern08b`L`g)Wk[?i&1Xr(G
eHQ!#osO#rJI%^bDd2<9pL6;6?Ts8%"@l*S!3NF]A@\*ekrQE>">u\C)u_G=WC&8O+)M]AXU)l
:FS0]AXkEO<r!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="D1"/>
<Widget widgetName="report0"/>
<Widget widgetName="KJSM00"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="D1"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_pm" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jzzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1701040452694"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="867c74ed-8771-4e9f-ad48-57d82bd56de4"/>
</TemplateIdAttMark>
</Form>
