<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_khzb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024-06-01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in ('jyhx_zhkp_khzb') AND A.YEAR=substr( '${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	   CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   WHERE TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)
SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID
order by TAB.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_jygl_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID, MODNAME FROM ggzb.DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '经营概览'
order by decode(MODNAME,'财务指标',1,'财富主要收入构成',2,'客户数',3,'客户资产',4,'客户交易及两融业务',5,'理财业务',6)]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_jygl_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm}' AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   WHERE TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
order by TAB.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_kqfc_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rqsx5"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**SELECT a.*
from (SELECT AREA_ID,MODNAME,case when MODNAME like '客群%' then '客群' when MODNAME like '两融客群%' then  '两融客群' end MODNAME2
FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '客群分层' ) a
where a.MODNAME2 = '${rqsx5}' **/



SELECT AREA_ID,MODNAME
FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '客群分层' AND AREAname='${rqsx5}' 
order by decode(MODNAME,'全司',1,'黑钻',2,'钻石',3,'黑金',4,'白金',5,'黄金',6,'白银',7,'大众',8,'普通',9,'白银及以下',10,'机构',11)



]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_kqfc_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm2"/>
<O>
<![CDATA[jyhx_kqfc_zbyjdt_kq_qs]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm2}'  AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   WHERE TREE_LEVEL='${level}' AND BRANCH_NO='${pany}' 
)
SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
order by TAB.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[经营画像]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="tabn"/>
<O>
<![CDATA[0]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}'
AND TAB_ID='${tabn}'
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="索引" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT index_name,column_name,column_position
from all_ind_columns 
where table_name='ADS_HFBI_ZQFXS_JGZBMX']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="a"/>
<O>
<![CDATA[0]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[经营画像]]></O>
</Parameter>
<Parameter>
<Attributes name="kqfc_area_name"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx5"/>
<O>
<![CDATA[指标业绩地图_客群]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="typen"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE('TAB',SUM($type))]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth;
window.objTab = typen;
window.obj1 = "";
window.obj2 = "";
window.tabnm = "tabpane0";
window.url = location.href;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.tabck = function(obj) {
	if (obj1.length > 0) {
		const ment1 = document.getElementById(obj1);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj2).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07';
	_g().options.form.getWidgetByName(tabnm).showCardByIndex(n);
	_g().options.form.getWidgetByName("TABN").setValue(n);  
	window.obj1 = obj;
	window.obj2 = ftname;
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="3cf08ad8-a13c-4108-8c39-f3d0986d3047"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题2]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="2"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('D2').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="D2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="D2"/>
<WidgetID widgetID="8126ff04-6ba2-4bfe-ab28-2d09f47083cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1238250,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[14084489,436728,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[*注释：点击指标卡可跳转相关指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?W#odoRU+J+]AgT/)Ed5Lb,p4<hWJ9iCZLD&1[eHa\f9?M$P7OiQam>(+LZ47DV;9Q>V=V7"
fRb6H]ArQ,gqJ[NZ^mnOW7*:5n]A>3i4-CKm-O**+$6eb^Yd7RhY6b.n+1npc("0Kn%0-Q^q2,
R>6+`>]A7C1tHk$-L@IrsYATK`j?,&T-!11L:"+EbhR.=[tY$DmZbgFc7646,_h7:'7^H]A*55
.C1;#FXF$:UBtg>MZbKDp-kK@8BE_l+s%>m>d)7]Ap$'K?V#)*>B`#*Kl=V%n(55Ef4?t3&+b
buj'%[5CU5m'eW4F,5p]A36D"jj>%3Id-9N.f3Scm`AUrf5:(S/<_h:AfPDCf<$i`!s#Pgobt
\W0FO36tSjdB.tfO:T`s!8p0+Ii1"RZ?J,Uf>?AGJGq>;kN5e1r^Bpi'[b`i@4>*B^:KUH1Z
&g"/BT0%m=(Os)<]Ajq5+O_ea%)M:E-&]A`!O)498<(X7M&M29jD%j=S]A?UuN6]Aj]AaB`gq!O6M
DaYMG(aqCp_-Y@B7;l/__"jlmR,?</e6=TUkNXC?f%uF[]A=8S_[9*!mL@G'l3i^W^EP\)^PX
k5^hF]ArA(aIT?Mk(f[]A6u7K@?8c7m:A.FUo4./\I*_(ZRes3]Aq<emKEj@r(YUpX>mNF\1nW8
LnaDOX/-90-QYKmoR80Et?As5`6'H9[l^S`b:JXtABSKegjl<.UuAKcQJFqcQ?.aI_6I4-R"
`?b2tCuW1O.joP<7?WpMgfG'N9Rbb$a1ajU&460q@EbHf:Y?%s(XrY?6_i?<Mm[K9?-Ygi!C
4!73`qrO?F!48au-30E>seI)`2b:UT5n]ANg45K<k!a(gIdNAqCQpk8pX:Le6]Ai";nDmG9it!
D'`u_BkbF341Z/40PobuB$f\:$qhI9nqo!3KRdQ@2alk)-j1#OA4E=%iP,3pH]An\Ep0BUkTB
o:i]A37!G(>CN\IfcP`>Ueu?2)9^;'P9rD#2LRPfb(Mqm(p*,G_00<f/Q9'F51;XIO4ON=)dh
/P*XO58+1mU=g\&Zggn$IRfZbitY)G%"FE'&cH!H-OZ_Sl"!&aD6YcRSoRPgH0pNg798G7Aa
A%@M:8'(2\ZM,_S_-;W8KfQ$3ad:bmd=b"'_-b;p&F1$iIH!g>&##ZBOIna:*9gB>%0eZVr8
R.$PB]Af>fi6fknd@Yt9oAea114)-nn`^YRtu$97aYS&F%%<`]Ae]A"hYD<"\:U(ff'NL'njQm/
D?=tLlR<7j`m@`tEXo>RDq\A!r"APD4lU%m6*+m@5Q#XY]Ar^^k:YAuk%['rgg/[e2rY:#L7B
23C\j^l,b2M^Q#k_QKZU?Ubo<ESpRP^Y_rARh?Wrupd6Nq/[7R=D[%U<Kf6q%I(uk*'1ST3?
9jY\sfV_Ya_P#'GBfLge\YAf\X]A^ueZ5W_aPY17`1T9O7<j?SS,`fb^ooDdmnk(@#b>R%&jj
2-nQ>Q]A1%U6$ID&M0[_cmtR1N3:]AIZ&Eu`6G.+H>B@4N*dc>*k`^+)o`JY[q[u\7Q"E.u^S=
<AJ&Ye<oEf9G9$s[*-BSQ-"D@\IAJ/(N?*Plcq)S**c=(LQY^Wl10gJf&lfo1)D8#]A,E[M58
j]A1&oaF8`.!<eOphaLa%V,aYMm8c88XMPm!q0g#ulDkeY4s".T:QeoeW]Aki8%KrM'sMd\\]A0
%05tjTV@/HWiI8G)T#lUYQ@/=l\0[`DG1U/87kJP^53!a%YMC7*3RQ+s_86/0)WE4tblKZ>e
A]A6W5P$gnE>XqS_^WjS*lobWAP^=P1*8ba.t-P-:Eo%]A>!K\k#Z1r.RnO>F"LNSTJps,b'>E
F5hEBj8oiW;kG=L+861)_,OYl`lFql[<8?QSs3]A-o'7an9J-4K]AoHuRB%2-mMO.ro,+=<!^4
14AT]A<u___JY6he^Q8kJtYS@?m<qVi%41*<-!C_G`IP:qi42V[K^;-F_6S(95^^H!]ARPm1hf
VSi/NkAif^/\3UZG^m>;B9tF'&\&OE0UIV^V$cQ7d`VSA;.Iu2ii4C-<p-hqi]AIgAcgtZQi]A
B5Dc^ou1L'"MI@4pE#]AK#88>eQ^116<YXtF8A=G6!S2am6Yr>r4U:>LLY&EnHl>":O5#YoNH
oG@B:07duO>;@2=Ne*qj><d_)Xe:UVY<=Z`ZC_iIr?aY4,nVO!4(G\=m;,ubc,0ur@d4Z6<)
6ZDBFb(uTNcUG.'\'6GjCE;?:h@+aK7k]A"3N9*4)MSArDf@Z#C\k>1]A+]A7aW`-@]Ao^-nj4D"
_6f9kfr=:JEa-I%go37)fa*=OM!`n,tDM9KZ^g37W(LA[S,"[)eO@Vh\_+\O$`SGc4s[&YAm
2oYQ%U>g(4d2<.:f"oW*[^I(`0geN<!FfC!pF]Adb`F>0))r.nMfN^Omh2"`3o@>*X-gd+ZjR
alfCK@'r..U;;splE'N9'j3K68T=lLBghg8:-dN]AdL>3nUn6'ZH:CK&k]AJ2deB\Ms2NX$Q3K
)!@]AeA6)--.C\>f@3B.g^M?5"?uC63eW/BN+L_-l=kI6#>,63Vt)b-nZi^@hqiLRHhNpm*ea
n"Hh]AEtH1@f21Lq7'Ge:L9n?K/YNcQ\sU?Mp\:%k[%rrWZ&BV:@MD?.JL<fd8f'bJ3Mc/Q(4
N%@GFg+-dc@buHV3j\^k&+;`&f('7Y\_R3jMeM.ac[ERt"&gY?/:%0Qb3Zs(O'Bf!&4EON4i
H5=.G&Y#Li'\.2"]AiYm2\9l]AUYV#*Dumgb`t4<dWL4*b[$9ITUW:jo''rAK)q$E<.`1>B.?p
W1,0B<<2V*I)QtWrs5,Eq6]AC(Ip\dS*gJ+6tJq*HW9&#*P]A+tCMjgZK]A6drUdh?tN1]A]AH:db
m-W!71nB"j]ATP4:Zuc.;Ff_X9]A_l[K\-f%<<Tp4)UE[?gH7q<u2'/-OmZpeC&R4oE0dZSh>F
J-G$gfBB%)k>5Rc3Ibr*AccLLS3MT*^j2VD@%63SjTPYNn8%3FK/rc!`SQ%7^B/qn$QT7t=U
hC($EBa+Wh9^!TF.]Al$EBa+Wh9^!TF.]Al$EF-(WbmCi=_PG&A/j1,E=iTZ:-$&Nhn6BU&f18
a7ghJ1&;2//+UC@>64e_[KHLC@#XJ__&H4?@&k+^Zn'J=Xeam.!ImtNi^[G.QhZ*~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="70"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="541" width="375" height="70"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[524786,723900,723900,302149,492980,270344,952500,206733,1388962,206733,952500,492980,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="11" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/指标说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗2">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[template]]></PopupTarget>
<ReportletName>
<![CDATA[/CMPY_SLY/演示/YDZQS/经营画像_弹窗1.frm]]></ReportletName>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="custom" mobileWidth="95.0" mobileHeight="95.0" padRegularType="custom" padWidth="95.0" padHeight="95.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(AND(LEN(A3)>0,LEN($$$)=0),'--',FORMAT($$$,"#,##0.00") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_khzb" columnName="较同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$),'--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="11" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="12" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="13" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="13" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="13" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="9525000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="4572000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="3" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<X%[dTcHakAAMgb0%r-h?GH(/P@f5ldX&*KVqb/WT7;ZLusN7Rl75lF\'bF3K9=<%QQ&8VM
510.1loA;[SCkJ-a<r6Cu^$rm&4GmlpLFF!'f6iV@rShYXqZ?>Fu?B<Al-IY1fQYLp3k_1Dj
5a+$8Kqgo!061Y'!Q2kh//aqRL=E(S?psI"B_K"@+A=7$8C4,Z0rghT/\(j&Zpgs3jeQ:>A*
rau7=4"Re[l-F\TCqO,[83)[:UmTUGP:7gc2LKW%]Aqh!md[K=cKn=brhKGP\TP@l(crWO^Nf
bGfs.G-"&`S^`UB!sANuMT)&=B2FfAl5?D50,W!FZL_UI!*J3WIg)`lTAD9#:,J2o1u(hi4.
0J[E>0X%nH"CKq[o>6`e89pOAS<p^0k=6K7gUp2\42cAcHi0WeY*HC.G]AB\A20nRIFcbneP:
NH<rrKcXEY>9o3!ceX9'VVo=\[HL9rq+6C9e@`ip`aP<g,D6]AfNsH^CEKO:0P(nq@^(JZ*0?
@hlhM9f7Hb8L-BC5P#(*9G%gFigo2"XK'BPCJ5c-]AIKCR>9i5BWRh94aTXJ_W\aaPmcpH[d:
L]AnspLup8B4pCAMsKW(7X1anq0U$V/+oR/V]A#=\pFPb#,Gi0'TXWaqVPe?t&!Ci$-eMl'+2r
,n39Rn]A3o*cLJ@?-$GZ+iHrXOQCUX_3!i)\>Q1%(*]A;F3=O-28c.=A@.j'udbMKGG)2):H,L
SC[8e]AZT6ggXahREPH/=iAq+j/OFYKKo#Y>M$FN%mN:8),/OKq7!",3'XA%L%11B;>8dG(?N
5=JR"]A"`GOhodPG_BCH,5^d16uJ3eT)c#&k_o:0/(;:j\<F8C^!TjJMY"*L.4sS#+)>/GsuT
%KDcS&qWN+Z';)K91>\kV;`fPgP/O@cb4b/ogKO5438iOt<CuK5IB.[Q@`DX'S03Y^4S/@#j
J1&sY'&,_4rHb=HYMY@OIIO3k>T,kr<*=oD4;JYD6K_W'u)o+1-(S0k9>A[7"fQ[$m#132m8
0gALc(NLO,O/,u.5S&p4=ULKqgMZV\jT.ofh*0"lb_QnWgA^nkdNeMZ@<OU(74Q2^,j8l\rO
P3n7cd0$+$A>0S_4`^#qk'Cs6=5UX+WhkENU82MrC3`/[o0+,^YWPfSQUnnu6mD$f;QXkuo;
0tRGfs*26s7BCLPkF*=P?9JYDgO@gaMF!_J_0'3nY6PfC_4+7ga8M2XICsl!S+$3#B>DQFeL
]Am8/B.:9O.q/u8+&:#F3t/J2gqaG7%^$O\Nl6*F.PR!DOV(93a_0_;+I(RXaYFd$Nu9Zg2>(
Bl2cFcE8`5ia2fj,3:@`:\b(_HY$Gm).KL>MPXjaDG&e%(P!dBd:/GbsZ/RYG9BSFEXCW'd9
*HB*='%6MX?>HWC,1OB^'k-)#\G-_KNkruR*@Jue5]A*^a3?ltA7b7>8.aVH&udFG,/=BrU5c
GQ-*e*?![_%Xc>cn'T<X^8tVh*(r4Hj'CW$r&h_udbo#<R.W>Sn\D4.S')B!dIp=Z]A[!o[VY
#fC@IK#X.7oE[l);%JM,<R[en.9&$,U0;hU&7p6hsTqD()f@mpj@I:5fg8Uh&OZ*Eoka?R3u
4X(.4G=>f>*F:Ia_eS5Em[@I/,^uN_fcpod\LHQ32m$1Q+OJl/^*6I-eMe%/p(976Y7Qqp7E
OhNY^\q>YYIfQlK6IVGDo_/Jo\3@XbJma*+DA)(]A?S^Lcp[\CQX0;.Jg`TW'g%DgDQa?Q#q`
&'4<;UYWTr/1kbOQIi&oRA>?DqG@!OOk]Al`%&Z1;"d(M^,qR^5/iqj5EA[Qb6@[h`LdAgJ67
,kUtWk3F?fi`F8gAtgKMqfGqB=Sp(WaLdl4`o05(jVMh/IHKt"2c7B6`gfT.LL!KCQ1d[Q:A
LQK7KY[>Op1?XJ@Y?1$#LL5ULIEC(5Ti8gTsh9c&C[<EMAIL>?TQ5aRPC)_N6q
l1o@T2%QgmjJUNkPp-GCq<Z-^YHQ!cuGgraY4,SZ/cLbXhHTWVXg[,fQ@8W>4)f#ht\Zf%cB
R6AMcH>8`;%`,_Y$G@6DYfK<TF]AM_s/ZZm0FFadH3Z?")\i1Rk"`:&%1$<*1Y#J#9)A&4X@@
%P[irL9FKDY\]AfqjqX/%sj/>qgM"[q:L"c3fl_JaTDegf&5$D-lVORP##qH-n<\3i'_-shL.
VSB$7[#D!+/N0'jmD4^j%^^$b]Af'#1$11]AH\_,-6*(`P<58B]Alj8[Ng&&JM$P1DE7(1+,M$L
\DaE4P=?Nf$0&%e-Z%,B(#"UmO=.9<,G_aM,a_p:aqW[eHn<E*j-g$[6"nJ1YAUdG,75ok,B
L#T]A"X-ePC#HE]AJM=Fj[>^Ie4[Ch@e:-RNc80>$DmEU40n$phn%#YN<M-&@0qBgdL[&M<>9S
44%B;g&B/(je0s;pNUC*.7+1U8D]Ahq@R9)XL`a*"<<_%3D)j:7dncXI;BTe@iY>T^i1T3AG9
r)kl%$nVD."+mGN5BTZrLnPAHK!OD'Dl+B\K)5;atRbh-QTN:MEG'F,\]AF>\c&#QD,MeMtE0
Z*dOY\7I>U>45E"U>Cp[BH5fJ&Ac\Zg=MBl=(?oLD2*f'S>sFr6+))O&IU0@b]Ae@nB3YsPP\
9V(>^:;ge7=S,&TM:\*a%U`Fc?Cu]Ags:_NJ!lNBXL3n5/R2nbXdc#X13,q?T]AgYQ*Y<RC>RK
UN.LQAf;dCBX-:lL=I.(h_TidcF&XMb8m-FO-H]Ad0Km0kSI<mXG'?'5+8-O>d\Lt^*$pWn6/
G8:VQ``>alW,/'fB=;I5F$GA0(r$8(b(Lr]A#3a\q1dfDK6o-!(mKrlJo>.!Vl@u^fH/,u5]A\
X=0[;\(-H^r]Ardi_b4<4ik_)RN>i+J0d@/Bhi%m:,d>%W\J)GV,aE,BY:P<-T`W.iV>NL!M<
IY,DEG;.3oC(a0^q$I6J#B#uhd;g5HL7R\Y-]A,BrUuC:k8Betg$fcWeBo5onZrQPf(o`=[SD
Xrhp@a?\FFp\@-3NUrdpGrh&lKel*1I(S(tNaUana)26/Tk@ETU(i'+J:)RuHn4"da:pXHb>
#I$OVV*4D3>:[#DU_+:^R$7dVQ)7MIsR,?uE*^\'[lLWBZVH]AedAF9*%Ju2OM"i!m<L*?f9i
,Uhn6-[jm_.OcZpR"[%g23o=l.$@\Mp&r:s2GeNfKGqXnQ,3gm*!)h-3"_#a9fJrcL11_a0t
sd2-I3p,f/bbdWn^H`mN4S^Q6^ed)dCmXqSpX<p&A#rBIIuR2kiCikPi/:kjJ!57<;=r!`TN
'N<^uipGTi3/$K(TaCYd(<FkLTjh1*\j\(i/$c3bYFgL#%=c/h>o(.M+mqReJ)N)Q$D@5iXd
*&ppRC^1Ce]A8\1k!VU$<$#pOEZpkBGBpIE@faLGTX51)NR5j9gH9knQGK*c@I]Ar!(^<1ROk;
YqqT$L4U;R1@]A^dc$<+Pd-p>W*#rQVp`c[@^j0@@d$:;?Vdhk\SECh=X!pc(nCP+8664ehSJ
6&htDW?>d,nUP*$@D%X@#062d@p;1'-XOfl!FgmK+!t7b48ToLgrF4n1`8%]AA^1i/rVXKgh&
IW)O+O=I:S*Po%4,\d/pb3#"1bbs#>p!N0C]A3NFL0IRg!3(`Jh4`WWbbj2kCH(!Rn6%kI`r"
)otq5cEgXS0Ujj&r+22)e;*L_lH/)D^Gf[J.3?5&RXCt)=V<F?92RnZqCLN[6j3;gSf::4Ha
qD?&-9D@liQLZ<^""P2>QgMMtn4F`0qQsf5/Eno4bA)gVA,H0*sRuq6gtRN@gH?;[0g)]A5Da
g@kF&/.ISsI^KA94[)+Qj#RjS_8Y,Lc0F^+T;!n12c3dI[*&$05-=Cc<"LeL'=9Am^`;g\@D
,8Z:WIZm*J)F=m)Mkf['HkqMSp6X+R7*FkZ`Se+ZA8f^,oE_&3E'gTo/eg^5Km)C8/ue]Ag1a
j:HI.Fe5Pa&lK]AMQ<\OJC_78;Wgs(0!b1D`!AKDVfuk=3$\P=GV>(L\3s"E-lu0uQ8=Rn15P
Yt6"a"kuQDQ7PE<N_M03\[t@L5rnWBD@uT<SLiec_5XUp2"Y+<.Fdg]AIgbN,l+$4P_1&0)If
M5CP6:Q1'EW1q+90bX!"=Ti*1*!.hc5RB?c[S4nsell:CX*<DA3Ih(LjX3]A=;o&J@+P+&0T5
LHfP9F`=f'+Tl2/qVn,X,oO<1>pFF@N[co[Fc-.m(kLi!Mm*Z!e3R$[s6mG<EF)0bpo8Jr8n
ZImcs')iNd*QI#@`KP(>E$XB2/FN?ER[,9h(NNAkb6>A-[g`orj:9XU_=3rr\Ip':dtT\!O5
eo@BU^Dp#1m\<R%)K_`A`U7W;rS[36MIe't;-VmbndQ_<JHS(Y--+D]AH_HN`'UC?t*WSirnj
r4>hVEjX#NM@`I]AmPm'fof>j5UsY4SkdHl3Xlm0u9Dc36j_iR+I,>CrpN(9;%dNH^I'>_t_R
rGhpkZX`Na(&=)&3l2JpO#c\4g/3GB'd-O%0nIN'ISY>qp2*pi0LI8CTW30GEhXkXK,rZaA(
/1D:83M:F<R.l5_^[[4=RI.OG\`c^)YQ<sF]Aq-AMX_BoNT/Cu3&XY1D,,brV4gN*cA_UE/jV
^o$!8-dY.5B&YOO.,9(^pGDjd<BSHh9O##/LM18FuCjD^o$f]A9Do49roKHVW]AsIq)1$+bRZ*
pX)`5n39#,Q93Elp%@:X(8=h+;u^Y]A#R3H6Rl]AcGe_,;&VckrSmG#so>0bVG6]A:aSHHRVV^'
]A1ZYWCKAcHG5HG4D9s6rB\1lXf)/q2d:B]AW[(T5XfN91LejiokD!)+4hQGY"LBK;Z\3J^"n:
uYqn%e.n&psX:kUJgW>]AX-)WU:rjK`>0aAH"N;\9Y>+BU?s+I0#:q[mPu.V[0-16S2gk*^<:
[&fg7VS.u;/T![5S?p\fZ78HtX[AJfEq%*h4h%3-;3U]Ap5#m0MU<TQ)nE*R+AJdrcOGsaAG3
Lqhlh"f%R;9u9)7EeOD[`n+1)-hrq0+CZ5e%9$e+p_%6?oQNORU\,uFFkb&PYH2@L+>6F%!8
aW7s"b$1sSm.`X&,VO61d^qZ*h5R(m6&2,Kh#aF+$siN7--I*nggfLVXX/=^FY2FKT(-OU,e
-Eho:1e(L\1'&0N1;.&^h&\*Uo!j]A`a/*rk9`_6rD6$(\><m1(ZZ&e)!T,f(juSd`*`J,`l0
k79[(OW)/S3Z2+m[]A]ABoZ5$;c:eslFO9cae3h7i"]A*4QJ"c8SV<O+E[8Pf4BW38^#>:POscF
V,gV0/A6R.rH\HLC=Q\(u-2OLI3^KK,?kS.OjbdlCj\cD#7;<Y`$EO16-2KQQ*<T(H-0DogO
2.5!+An4><5S'#e.0Q7%[6&Rl^N$,A$^p;!H$6?EgeDa18L(IBjfmbc.rJ\N:UOZ.8(+Nl#X
G:Q1*&#*XH'!J]AEKXbqq\.,ahQ:qj>dnm#:o,N\Q!F:0Q0'2JVa/G)[4jRh"gA*@,0HVRiWO
:;(BY\,t]AnU8s8Bqb*t4M(*B]A2TNaq=?mE8s#*4m#>r%Q.=(KGY/Gn+n70k^j[$RS%/l'eO/
N3Eb0]A/P\Y*MuF>FNT`:$W4Yc7a(c'kcTUkOH<W<>)2WMB0f"sOi6gZ@9oV);-''Y.g=_6Lq
>^/QlH(`".W)3$!*i9D>-iGFNIB_k+IKZOKjE=*oa0B)qQ'0Ghf^s`;&dX!o1(j`J"+<Ep[&
YcN.g$(>*WYNpOl]A5@^F/ImR%a+a`h7E7hE>Ec52/'e%?XI.95[;cu2sW6pgh#b/K,efRcEP
R\NQ3F9&&EAMF?$8pGEQ*`63P#mK.;N!X#&SW'kep5be]ARR!#rR+Vr=/-Et`mZD?dT#5ZWfU
QNkJ_Y:llEMbY05.p00g(Vd!'Er"51n4SMk.1$NNHjEf=1EekrD:.L9"$<]AA=[m]A9g&(M@aR
pilA$INi\I_C(T(C/`a[b>.#on"mP)UjnE$g3OX7sXpXi%[`S^j$(G_^8#,LS<HXS,SDH`92
f.&B%f:m@CAFG''9o(pRihKfZkJ@;f1E3,cBe3g1X.1uX^4dk:J@;G1^W(QD`!e5j-$YbHdU
rIe]ALS0.[IRmL"o]A'Gg<<tcRB<&C\P`ac%!p#3f^@5Zk<>es_GdX3X;B,;rh>,c]Aqf:nK60-
!5l.]Au">H*mFhF*MNRDI#5U\?!J3'<pG=ELZhXH9tI'e^+_a:VtoJ8?DR7Ui.sSG$2Z>!>[p
pc$S8X.lfQCm<k,/jn3d86_X-r\`^R!3mPkeqML',nMF.-$B.%$bobdpAd9)_rc.Sb"tS3-j
dA=8hFRkG+]AfOOoO(:Z`;\a6d#IngtC@0\gk3h?_S78ROp@E#sL-<4@bXb@bDAcIF(<5rDq,
\;&K/"%t4_P\dc<%WoF@EM19U;*_D\[`Ziq2PHiM``\?%%o%R5qf5g2m+TTma$Y9g]A`+fl_d
SKmn#b$k0Z[-X)$YR1DQ^Fl)ntHKOcR>d$i)g-R^Tqc8fe"d*bcGm;4u?<IEstUm(jhqok6.
Crd_jaX/0EToc-VWgBV$s.p/XgUift_n&Vk@`g1$(5[jQ5;li8Qiemj[lBLAaO+hQX@;o9lR
C6pQbkIRrgJGsNA)Vc@L'!h>LWPc/(;[;_-cAgo(m`>Y=!O)"`$m]AYh-T`rog_Bq"U>S_cm`
P(cT<dbE#83B8D,.WRiP%^peY$;)r1(qPJrDjlXLtK:M+C3C1'fKC/+E9_EQ>JKg4t);A+*.
<=6S,4*3pG3m,u!GH]AP,UE7podLYH[9`^PQN**.=YD4T7:WRK,^k0OO+-rfk-$X&qIJi+Jnc
M6#6B4/=Mj$I?9dOHGe-I<$SmHOD$p[O%-_n^\p6=u>mQ%#jij4mn&^h+P%RgJKHJnWY"dO*
p1@&\3<<ZQ]Amr5=!SSqf83-Q)YS)td7qX+;*XAjW(R0']A"%EffYT<Gbf-l0^PRKDhEe`s4`"
.uda3P8oc")4PS[/TV,q1WjL:%cOd9!;8&5YNKEh+U\0\<[c:6:3<Kf+e_>q@3bq<[1*<;@a
m(.*Sp>8e%\[9]AHJ#:HiN!!)h3>iOR7Y#g:;3O's[!CU2]AQP('6DU]Ag$4]A1=W)%Cp9V*T9Dl
XmpL0/0+Y]As\^l\3-N]AfhQh<PUo"eliXXH:!8"%_Zh7?l`:#AN*)86aK<#X-*]A%Xd/B'T(S*
BiN^<aG?K6tH$g+PP_Ze<PPBdUKB0]AW8ld4"CFIq,'&$[WQ!SJQs^@S6e9tgY?nG(pU8$:sR
*sK6W:%Qei20LDpU0^\u)i^>:QC[os$ii4d-H$mXd]Ah&c:Wr?Ta%E(;!tic/P%pl$.;l#!#'
7DNFUTsm$T!hC/ZN?1[fXPTP7%<bi2NLn\R+g;D\Ff,;V*D=p"a+W>A:DOIi!4ntD;eO6A*,
^psR;/5l33a"SM@G?=`#Q=ahOZcT&?peX&qZb+KbZ[SFVR,E_XMG5;PH\`QC(@W.bIa.dLRE
A[O.OQ\TP#<2(n-*jS?U/"]ALgHmuK?oBd]AuUhLCW$ND@W5l=FX)n_I>QN^YU@]AT!`@<A2h^_
&#`Pr]A'.gmq?Kp%8QB3?eHL&K&Zs/rP$/R5;QNr_ABM?Vf6`976$AcUo\EI&oLu/<*c8/f9l
]AHM3:JU;]AR1NWQnU[%)dM8\64/]Ao,!XgaKqbTjt4@Y1aX:(]A`D]AbTrKn1OMbWl4'0ne0NhcW
H!5,%oqbuhfsB3!G\lh*a\>Y:Bp_+h((:<kUqWX?Ts_,7\u%Ot*SqZGI'kmO=CPS00I&O+,(
,bHP[R.aRd2:ESadAYKRadWX,gl5>":Mr<3%Sm*4+k"EZJm[2u?EhjZqNG&8;pqhuH6_lTrd
u5P!bPldT[k><fm%]AlP2kLl#,Qc/U0kknoWn/UP;JT!k/&I/q[7'1`1nK5_sRA6h:GXr>0B'
lqV]AnE@tTe4igocN;dMXSBU,F?tXGrG+A`)L,R:E^5;(lL6EO+=)=n=[E!_B/\oMdJ!!b["i
#om2s_Ue'Vl,ruMCdDmtXeB@B$8kGKtNJMO&./5Q28Iko'M8m(iGBPAH&dbWccK[P99YC'65
Ut`%M0j08J7gLbto3J7T#/jbpnCSk-J.P@@3f>@BMO:I]A3QN]A7WE*`cG$U77rMtXePY%_)\c
`tphOthb?`0'AXc1LpgE+^2Y&jss>CXQT@K(l.(bfgS=^-Icb1E4EP%ZcO`r5L81!0o$b9_>
\-ZSFr`XSaC1982H,K$/=G1S,S-ZX@(Ss%S((%.X<;^reVa5s3_@If5_Jj[B_)TZ^$DIQE0-
@YjGSOP0E`3?ltp`Pcrd/23^Sp7Q%r\gMT-[h$?7&#[lJJ_u`3/sA*@*N.8c]AGXUCg$&RqET
A-?FBSEp7'#5J07WL1DLlL"GCre[l(7&H\sBH0po[b(@+<K8oe^)CMGpFfl=a:bFF5P_"M`=
KA+*.JnD+QE"8!Ipkj7Z58bu-goX&\P[Ci=%LLl6gE[X@o_qBqcl=D%"WNdCfV>Q<8o.Y,1Q
hST]A>n8kr):?=Uh=sCE;d2#p?q6a<i&N'f_JWF!Mm4cbVn./;qVpP%#tegXPQk9bs;08ne7=
d[l#<0>b;5S2B,Y@U'a?27Dij4WTP;/5-V"N"("C'WIO#5F/_X.-u5T+L[8oS:JCL)%i@G"H
B(a\K>'XWbeX5FB'>Il]A*?=m-??>\L_0Q_bB'9"l(Yn&fjs^CPd2"BP^FhuUX6@#m'A`$iDl
9*RY=e#FB%g3k"Y>Q??h`IeC^QmN-k4mLJ#h.*hftuO)KK:K$1FlX0bPDaL]A`Pk]A#)^*N=\S
q'>3QpYkYG=niUn'8U$P<Ma;bV+bXNZZfc:28-r#_5QC\f5Q`>VW(JYKU;Qhj'c8F4p=jqUI
^LmFo#^S/Hr$i+h;#A2WiV)"S*;$m!]AhH2<\?aCM&Q%4a%Q5%hatjfB@ua<%#m$rS,u$)0.p
IU,ccsRX")71T=``a^Sd/gS`L)0sY)L42D`P([]A$!<0K#oM"A+A9I@NCq"b9(*;&Ukhn1HFY
`s-0;kkda6%5rMdA)+N17"A="EfI2KU-JP-n:bs[l*)<Oki1.s+E_*kP5;AWj\3.h7QY>n%C
f/^fMiuW1_rL(r-B=jp,#l!(rO3j8J:Q9:).O8-U'"/9%jK6RPnjX//M@p"lPR/r#F?^7EF5
SnjYO?\DjjVH[X%@J1T4jeP\6lm._m$p+RDcA+_5.lAe%nA6*jf[*G=efp%.APqnhIb_7d<o
PndqmA\1@\eV=pBGKT:W'u=eH/k,dS#<jb%/^K;7s]ANApm2Si<ubS-@R>Kf1DibeAGRX/6ue
l<(uU'ke?t\0hZN6\Ro9J$_gBdn*os;K%N4TZ`7Z#Lr-e<L9WU#.L7"9A*-MWFJCqDqguT#5
`O(n7%pe/C3e?8kIB2fqs<Umd,OJ0\B->hnu=\4gTtH2?)YrD;n3oo)BdT"KpD[+'QuGm6n=
/"+),mXblRkI_X%=T[mG:tYet;0*p#i"-9'&ccNs_n3MS#p(S728>7L6(;Rp^hDj(cPlDk&"
gRP#>J3lA"j:`OQ+q@f8oYQb!?jd_2E5?i3@:`pAJ+[+oS%+6'3H<?Of@IUl$;EEor^?NQ_-
`M0"1]A>is'RVnDOUobWmQk%,EL#@puUCi<U>u2V&Q0.c(]AoJTF42TH4/@HS#.^_F8/clXl;R
WU+q"&IC7\=r_A+p>VoBcApZ)$WE%7n)$sW5:TTLt5/WY2./'s]AKtR-@^Rl0e^Ksh6bVNeA+
hhPTEC00G`Anb/&0uZ1\P^d??K@rO7QR:]A0b1hof6u!dL+2H.2&9!6/#J*hnIMDTf4Hnn%5b
oVql>PdqqgI75`9s'GJi&22<@&?@MMB^Y;UV%G&C[cUXoBnFIVBAZ&AY@<WTQ+RlRj`SQI-r
JQQmVYd]A#(6cd#\4BLp.&L[QQo`)]AN@L]A"Kqnn.M81\[A._<R#5/-cLco"4)MZ!DC&o6N6\(
:i@/b_c!![T>L4SZCiZdmV"GhPT6'`HXl.>#82]AJM-h\n;jsdu(qr.?8>oe`(utOG^K7Rmr-
NfKE`%CGJt<F^qF]Aiqi3/.CE@Y*sX)lHCrEu#NR8[)j<SNKU7Xk)2JK>g"LV7J="iQN>`E"M
]A52Q\!ioe#e?`(RZa3ncO;]A$NGu!Y44YV,AGqefhetX-!h'/<]A84`D)TnSTpLrFoAdHYL8*D
(QqpK_iRV<kS@gs@GWmSjYN.h7g37aOU=Vuogks.(?krc/JVTC>/f6`.(SRF)&o&_aNb^QEk
rEeonHWp0r*bZBkTCOX0)Jq.koRh)@p=f4/W2O^`;8LmTUgH058J\%\@&,rjMU:#$#aM"b5k
JGZ`C!3G/s-7@ied%O:Aj[1EtZIdP:r]AL+&87A"L_;a;n@C_%O<cd)BSu8TFmo[mZ+Q.[dh@
A`&HCF"V*lWeR8&SWM[L[>K)<bgc>@G,HhlK?L?k-"MiBJ!Ze-"P6m1&GhJGLlORCehQf_8m
.fd1=Sq$8$#VkZ#cbSLS>FZhs3XVT/_@/\)<Yg.oYM\A?p3S:>PNSn%_LFVHpG!#L#c7/?-8
_#Y%d6f:!^C6:B.>ZW`(q:pD0G$@c9W0=KUS11j^.Rr\F9oRA>"=Bg<3L2po^[l/"%Ph5(FZ
Vc>C`n<UX3U5k07^?oZX6,0lq)KB30#i@2c8\q4^erUs>UE<8"T*'l7LZiJ*iEn.^Z-Y@deT
*).7b`=$(-t*%AH$Xo.a@H3G[Z\N449=F9sgE/^[2qjBS2CS;IWFIc.=SoV-95C@ebqk6bsH
/C9X_oHCuQgA)%MC\V8P6]ApFmYK8UXXa0NTV1dM"sP.*FW"tT<ghl,RFI"eOjH0I8ETZ6m"n
H,R\au7p1WOQfSm1`=!Aoe$^4JbJQ9,,jYWIgekJ?hc8&>?V:#pQkXC'l52XAN;r,3eDX)J^
7UBLk9mBe0QA*i>piEL>SVh%M%*36IUH&OnFkKEW7)G8;ZgS-g-Ds*RYnEUs!pS&':R?+")k
k2>1hdrZhAXDmLtG5'",!Bad!-7M\-DZ*YgqKi21@q<fXmN1W1,<)Lj]AGM,ZRY]AQ>J0Bd7nf
c\7R*F<V:ng]AAC18;!D<cn:DM)Jk_F#Yu[ot(M=[c;GjQ%-9]A7o2\M,qum/M`efe>I[qP&g.
c^>P?2%@nEZrD/-rE<JP^h\35TO56E&`Eq<-HB/Eae^Q!`r6#P;GH9"t_;8[[iMNBR<Co+@;
#.qtR3#@2fYTEH>Yk'F/2A+<Gh*f+ZA/$d9>lWm]A1Rg'"@&tGaQ(34PhfRQG2O6P)0rVl(GX
+Mm>"SXKa;hFjFoPSN8Sbm/0u_EHA;ahcX5X[j!.o%06108R!99_[-Of<7Q5>[A;aV(XDPI(
n4RUrR^%?Qs2Ppg8a6>$k:s$p:q,$*`;ElrDr1*V"'":/M\)Bt6N3;-%6&9RI1t1%-nReD.*
V8HN:QDhj)>e)6a:f!ZQQgM#T7TEC_[1:-scFAl'C.VE69j+"_aIDG:`Vg_+7LUcr(>S:VP*
-\Thr(h,B\h[6Fh-.YarT0$5^5HQ]A<L^L!oR'mQ]AP^4CYVF_[qMCTWD&c5OYe-GJnNLr,g\[
k0.&$`j_l8-,jXlsJqiH+)+Zho7=C<r%B#q,O&Pg3`2MX.%qr&(T:4a"s['E`?&?L(m-$o[#
MsGYSlm7(t4,%ZO)]Ad*Nsom`NK<"l','ONJpG25FNV)*iVpma;$q<X7#ZaJI'<-7?b"Xm)t"
Xt9*MZNU:/fYN"M&5;Y_2W`^pE`R0.Pp8?+>U\@l]A,Hnf<2ip`m4?#KH$j!('XhiYKkVO>r0
?GDU6UTg2T$dZGuLHGF^tc)YgtG'nhZ-DWF]A4sa;2X>B;bWTc^VK\5`YQH&jF9eks[4"\WW'
-F8X,IO^AGq9icD351R2pd4u+&/V%Y7q31foDA^Nfrj:/>bXF2Ma^(g`G,=WVVZuhdFLuusr
@a:]A:M(L;g0<J!b9;=6DWQA?@0@UjrC>r/5@k%KcZ1KTj"AD+/H[)^"\&I5KZc;As%Xh_%/]A
K,WUDP8s(IgAjq(G'[cT'R]Ac[$"j=`]Ad[!G;TegD@2Hd8<8PZJ_</BCA3I9cc`Gm.ZZr:f@"
\4,uG8#nq5KOEuUG[<KcSeo_2kJd&Pg#?uhpX9n3peMbNh/uBK58IMVo"G"0n(fo*.@T#+;=
pFrc3rfho>5e3]APq1:0:Wj($g368_Kk+QE*bLWS/9HYkCrNEH/\d9_EUDuGN;PThTBMrr70_
t?Mt:t?bT[)YeUZpVsihpXjcj=j1dj?k5BZNk'm@~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="516"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="516"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[jyhx_zhkp_khzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="考核指标"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[     var date=ObjGet("get","date"); 
	FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/经营画像_弹窗.frm&date="+date+"&pany="+pany+"&level="+level, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 90.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 90.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<NtVP53ik1JQP]AV)d\(gQXKkP@@#L$e&3YPA-LZ/m)#?k!gh8W73f;U=P\nadA?]A]AB4^&<!
q]A(RCN<,+HcdMR%PYoi7.5iS+E0AT(AQ:H2i6iK*1lHiUaadlhXrRpJ*a/FF<t0]A=n@I!8C;
@3-l&7fF2$bCu$;M;r.=o&-.793dLA=KkLm]A(/Wtt=ZS3<M=UIKch*4:FGd>5=A87o.D&8R9
&S"Yp2`Edhrcm>9n/W&k1Yk">OO0>hpX.eWloj<-Y+oXcKP'WqsU'Pm]AfVWM;mHGS:sa9PJ,
m&Cqd<%c-Ha_,Q+u!SDA,,mZ$#6Y\9uHf.l`QTuZc,J=CK#@Q)>^Z.s[?F-ra0]A,CR5NrB@c
\=%1GJ2V[.!gNCm!W[_\V.?]A\EW9fnplAp!l>#lONloWtKdHf80@eq^dnNB*<ZVj[jFVdU)?
*YH<rX7,ND:.Mjq&nUc#4O$0Nd1W&r&<\gWbD7j=9*"0@Tou6K3I80HoODZ(K6K!5L2M:JSN
0kaBDR.'S)o5.WEhrEk10Uon<:F24]A/>]A\fqlhA8jXNL=QOA^82-,4<5@tdCp)hQ?2.&43Hd
)+0.?_7:bf_$i$qiDHtFb#]A8;;cGk@".5r3l&5rUjS<8_o9O9&LAl/0)$l,eL")1I]Ak?C8f"
E;KFC-GW7$BL042QlEG&A0ej%\mNhQE$_<[7VhcRf&Vn*]AeCRN,-p.8srj*!*D6p4t#Kj*S"
1djo>b_14B5UFfsR4p(,Cr&h/l\VlT'd;#/9Z@S_%TDbGG7A2]A[e`pN0Ga.=<q%&GEgGM%[$
,MhH.7&]A@VjLi`kLZUoV'&Imi-\9(sfR]AW/o#or0A-@"Xp8,de*7p<-buRG`/a23hQ2F<DoR
@N4I$`JK%j_N,b0b8+7kLVo2.1]AE/$2[5aa:n_Mis<)k'ofTGh%62QY31*S9f5-gFc7Q=H."
b4'cj8&<!@8,bZMo6m^l3H45^Mba8M%LHsjbUJ?"!Bd*9T.'[pbP'>Mo<DjikkY\o.Kdu(^2
.t^ii(1-dh7g:Vu+pI%ABN^]A>YsdjT"W8<:(+qj4g?HN%4WX!k$[I3k!,#UB65YJOBu7'epQ
c5OU1bL*BZR&8fYU']AF/l8<Qc:upeF\UUqom_a,s>oKHc,:_*-5(1X)T74S:oj>--qtKD-pZ
ime1ZU(=c/0;FWrei6,b(u]A*'t=VCP+'q6BdH+!BWmufouf_a#D4Z]Aqq)p@AHSQB4\,MD,0s
F4^so3rJMup3AZVPY]A9I#mGe9nfgnoeDH6fl!9_\Cm5n$R&WaKM/lm5H(OHs!%t$9=)2pSPN
*>LSTY^U(iYTdd)WV[GE$VA(A,4)C9qKQZf%@>E$f(E;W^b_m/aU]AcL";C5N\&LUb$:'u%5,
Ya-i/+J30=N^-L/Pj8Bk/ce,*[=Tk?GQIP@S3F+?1VSeU"F!orNZ`k"2qls[8>1>^lH7q'd9
R[c'<T6R7kJu9PraUAi/m\RHS[KX:o?6<j1dQ"%LSt4'-$+f]AHIL(.;oP(_Yl+TLU.RMU'J6
u!NWDhgYmV-:!Js+D:$F<!Z%o(4kBBU9"5m_*\IisRS*!I?5)J&5;&6]A/+&=nAJ&p'7GW>8E
q7#ITchSrmR=1;m3s,c%u.Gs`B9*BHY0b7"qqU-#R=<f:J"uT3h[Yn8NP*pqr1p_q_FY>mK5
Pj-t#`9&-@*F3ci+pMPNI&?.kmMmc=Ii9aB\ZWEOMkJ2$;'8Z]A%9,6+OrO'6!iu&e,4H1.mn
2kH]AtBgWa04P^sEagIn,*rSU7F0[Y!@7>'nK11s)hK#Nnq?&la1eV9L:EcHE@Ai/!t8VA'CF
7F"$@%;[fjDL&rQKO)3o.a01UU?4p\@7HOFl[&iH/Z(+JZcjj&0eEhb>@eJem:&1F*Y<5Yq8
nt9.gra11CY+'8YK[]A$M2W3W\XIWdXI6uCI,Yc5Ym.XTLR%A2!ZR(;,q]A8D_ZIu:K?GuRmTE
a$<%fgq9@eM5Mrd0Z,O3C2#:Tb$TMP5Or2F,1D:.Rq^lf=_p:Hl*r)4e4Ag.i0Mh*cPr.^e>
338Tp'lC.:s6n7j&.7``S&n/+h8MQKjJ_%ami'tiu'mh;Q;rL1+'B/FDLq'V!s/197!t#+PY
HmFBK@'r9Gl1MdQ!OCM_LXbr!dBOQ+HUGc=@H]A!BL@d;PiO9s?,k,W(gd/YVas&Tt2k.A"^#
ksJ*.H!i,+ah\VO2OfJ;7quSRis53d/+O1($YkjAPp<J$fn_7p9Vdp1<^oX*^kJ=.=[Z/%UO
&4gI<cuL;nd#U;e!Ag\02L,Mr24'oldg\^Jn8>jr/Lh"D:ENTUk0SIa*b/=>!;I!'s;5XOSL
ClZW"..`_OYHm]AT8as90rKmL[4K<b;bd^^bmlTgtc#h?VJWQX0hcu;pbecY4ULan!#pRX"I?
jC86a0&YQc3Fq^KfQ\<G2(jqJ%;AeG[>lK@Qr1e-Q<`e,`qRoKN5QuTpi(262'uApE.X>qS3
,91%e$/i6SUX-5@ZT@VEHMcILu.:.W+@_[dqmYD(T(ZTNfX4!#AJG]A+'Los'f+!&iZbBM&"C
Ak4jC[=M7eW%?#/a$gEG#cZLiG2F^IeO\g=NFRuVbcsL^^!>I/o\M:3:p-Pm1-RL;b+O<k#I
1s>!NO^b\+ER$h3bE-DFqr)]AQd3!""E6TKZgh#^a5TEQ1k]A^d$RbTMqN!pZ,&fiLBOa@AEh`
3]A\0*VcS/=8$UhEcTWGhpZO%O/R%CD,KU;6rNAL5"UMJ6e:Yo?iL7lff2m7/-8lkb.?YDQH2
A-FNi:VV&>%@?TKKF%W^K]Ajs7#A+Z/a6ONmJ=W.;P"Ro^,ui`_'nNf59*chaLo-p2&Rk"hA.
NrUhgG3MAXnj<l?!1ieb2SlmHI\88HmC!WXlc*^>2,m7r_WV#tb$a&NFT/"`49J_J0cQ8K9n
^JLl%6h9#;WsEJ>$UH5`[.@,d&@bBdjZ_*XT#=,S5-i@A_I:$W[-6ZZab2W$;j(E,q`(%0mC
%^PRa8!]AFU@NCb0\e:=)U$3jO;t?OPJ`C5-S`(X'M&6&><+^h%]A',mK^X`pi/qsQracRe&m+
'd:=k>g6urUe.u?-Z3<"u2MGojFO["_@riDHf)==4E,iK^om:V51IMi`+2IVmb=#G??l8Y!1
;RheXJ(^IRQ\^'YNgLr$@=+oIm'&,STc<$jfRjd'$CEGW1CJ*2bktY7^Xm3pe"fjql"]AQc)!
;-q$2'@LP/ZnnOU:+O"t0hgs9?9-=mo,$1d$B.qdXX6Mt:P*LCm%F_'_Wq/02=@r)hS(1IX:
$ad]Aumm9>WCDIq2MoR$j!j367ZWcp8Z-^^rb!1]AW0R-[nEI%(tI]Aitj4jrRZF4Na$"2.&pK,
PEL<b)B#^CT#lADlJBTTtOCluVHk6b`h5r.V?CNIYcnmMu@?W7FI2WYj24`Te\t_t[nuV#O$
HRkT",(W):&i\+/[rg6\VkplYg2T4/1Q[XuHZu-t!QZY=/f.A^6#k%5kE@[[4'm8]A2FR';r;
IO^e7eCGb+OY$ck-/T`RTn+$4E6+9g+F1HSG7B!CD%I1U!Q(\>rJg:hGIcAkoD8s2GaUZq/*
u<F8X6rieNOdn$>`<Hg35Ph]Ag*@Aj\+]AQ$AdN77/&>?+(qeS&h<,f8]AQ[FQ_/0HENTO=gL@M
G.#16qVqm/c9_JscOT&uT\57'\&"_MWZ&2mAFDFr^%8Nr^#mb>'14/8=#aU%O`uj]A_.d%GGs
LN.a;NZ&'%/LQ`X]A_M+Hq!4mA6B+iE>VnRR^(W(H5.j$:0+l:+@*gMIQ`3b9qREl,j84'[k-
>[$qg)#6!WDlIMMG9AJ0QYAE74;DbPL<SL53J<R?r_eqZp?APke`7Bda.UcgE9$/lgp+6#@K
c)THq*hAd4d8W,mg23Q$DoJ=bWVWbg@i>J]AYOc[_"&1i-5$Bnan',`_2QKG$a)MVAEmoJ3,A
6k]AO=PfIVu%Y]A87u488k-+C*kquD9?h250j`lX^X5;fg^-^QG+B?`us7pgs8.tEXm^s\`Rq1
N%S<J-VTc=q#MWlCQ)Th5QrfHWu/M+kco`ud=Pj)Ug5%9ige3h(=,GkL*)jB7P16in[.Q,1%
O\.b\i$,pb;ll<+&Sq_oI[dId;":cnlNX;1CU?S+aW5Y7C3C!DXTD;+!bNbh+HRQL8%SE._"
jhlWlcqp6iqjG*ep_iba;^EH-d.[?1i`5kbHfBunj=CXp,W7U)eUoAR1peNaG-[d6b?PnK/J
*FAlF,PJ%/S[']A<`WTJ2fm-uJR%HmU^hL(95`q%9*0;B8llcAm,jg8.mj_=GlN54C5&9J`+&
re>@i8FkP4YN_&\Be)^H]A+CQnQHf-HEq@SEkf$C>DQ?6$SuF$e]Ab^1sk"jUAd7;cFU^;V"kt
pX\gt;kcbYr#K2T7pi\mAeG&^!<9*E:ms_!G`C=UnVp31S(_.%EM>0sW8)k>bA7>q?:Vq\Gp
Pm4*6]AO2)X8A]A@@lF95BYE=f4FuU1XjpLZ6tN+6eGE>Ip$H9[T8i2(JQ-ddtS;p(U%QOltI?
bl>>JO2#n1b.N_WQX8j?'NJ2PY%phQ:QL":.,]A&VK\7`=1F18uo`F[aqoLdW+l\#_m?>LoD,
KK"4aF+$kYIh]A@I2D^*>`\SP"6NilX4m^I9NehP>4$fqA^:S/:<Ots6q>O1SnHu8Xk"r66!_
WOlN)1.,!!ifd&j[trk7Nj.p`m'Q2V<<U8%>\]AekM7^Aat?45O@n35!Cm5GE3LF[Tg=nM&oL
<.b#Y\7ZReTA%k6pM7W.MT.Z[s);#Qii!4P(-hZ9GkkB(i<;2QIC;%14qVFq-'1m=4>]A9HWq
'"Fn%%!Z@!VhNbCCE2(WqBcLkr<=%Qk9D2(;\J?gJ)>Cc[Q`(jjJCW!%TA2b\@%BH%@gEd]A-
sE@9%6?.Qk(q(Ca4*EMd<YNm3r2MQg]A`68ACqCopLC-O\7F:Pn8kUr6-H'2*1T\CqoDkboc>
MT#74p3<Nk^?j6N?,LfRK\YO%uKU$O9fp9!DaBOitIPMg:2^%PrA5$V&alW:^XRk97VMX5M[
!*3?37R<qqiJC2c9[>CTPP+!%[Up5p^2ao*(9RhBa>Rksg/cHM$2).(26C1S18X9$":J!7pc
qXN_=jG-.Q?=Z7=;F+G%A/7R]Aql]A.GrFm5M#i2M0q^GJ&Warb"[<0So,L!I2[PXPtNej1Oc6
(d-F>FaO>ULa.s"d[LX\nQMVklgo#(n]A';3+cZ8FAR(6K-?ECZJ[.mqfs=_unO,9p&#1jC%>
P;#t2+6cj$eQn64"^.Q74qpKEG3d>3q?(=CVJHtOh5]A":)U/F3?)s1,.C8neWq:<fe]AC]AB=:
45$R4m(5'R9blugWp`_hu`hrbd0jkmb\[UPj9,cPH<mpZ?%`Bklo,PPZI>X+3a+9dTaU5mFc
+'J,Co>iJXbV0,OGE'ZDB:'3'$m\jQARFnp^E]AMX%$i!4lIn#7[`cf'"L%[bo,[`6DQ6d"P`
1[cq4$l+Qi=oKo9i'7QZllD9lAD6+7mpgT^q=S$5b/,AIeAdJkYQ=,##B^a8D1d64Qpmel;r
6)A.sdNT@Sh671_ZIUcI#/<*2IAVc_Mut4L(,ZdiP!&(n6mRpVrIWN$m3*4(Yq&%OT#d58e\
7$A!<K($TuZ^ZC)]AN-0bPS1!6uYf9,]A\_Xgq]AG@0WC;7Eb4.<_:co!KN,C=6HIak7'BYi_^8
BsU8Tf2^kD6J$%KPT,bU?o]AbC?m/^psKI0*p81o\*NRXgXY#6WNAH>-Ea6#Pk(Or%!I^BOm-
t]A_KuaBcj)1H`jPT,JS,GGh0^CJ9ho2P9HP6u-kb]A3\*HJ4'\ndlb4b\sEb%eSB`5g\Jb"57
O-=ANARDB.*5)n04f`W6faXH'FO&>:lVc*DWckmB%/`k&hs$h$47^?cgjpsA>Msn,_Gi@(jh
qF^iN&YMrbOk.d;__1XS009NN;$;Dc2a:f/aC_Bej%t9<L01mBN]ArPluImJm1\HMOp]A"><=r
NOnI4*\&g7:FD-8rqXLlGN7=*`h&VMM6'S\i-uU<BH5rA(otQ'QAql7j3TN[\EQ$a[em(3Em
kW-[:"#qF#V!$-dVg*Ln#<SQ^a[H7nq%oeS;'b3lOTKrU*Lh5HV,(WY@5^knub#GlPntK6\Z
Ch#T5k+gk5"a,3ae-UaY4`%h%.5e>BH@>T=tpJ2u2=N\"hY8gq3h3Sdt+MCEE!$Js4^R.A3a
TO]Aaa'+1lG^@1!IDi__\=-K\KpC896#I(r!VXdeZ;+)gnY*`G4>N!9O/)"/Z;OYk@WYa`cfe
R=3XC,#5?\t%siXEajG="CU-HRR!PA;]A.C'Q&^B%:cXG]AYJiHA6#2QoDh4Wp(H]AELD:`iL7I
NUoJJuqI&%0rJa+MZ]AZ^iS\9*E)V-><Eh,]A9TZ""\qMP&2gW2h8L)gZlcgts?YO^m44JuIu;
EhOt3d8JbMe\:\Ur/M%Efc,_6nP@eJSZ9A<:DTP)`0rLqNt@.gRKsjlJR04%Jo,Dje?5k"dW
"?k;+u=q_`S6KahFU-FLT<0:8;ImYf\u&dd#V^IO"lO'P;PSD)$I7YPMM%?d!Le&BdG4R8TO
SL/]A4GND:`ECX@UR*XCk/>%;B\KbYl47t5AeL"Z"4giX':(q',7FAN'MpH4_`YZm*O4Cj<">
Y(.</u%d4h,7=-pNSATWbdW4gUI,,cuFkc&$J!j_KMZ:RL=$EVO9"G=Fa[&Xe\a?"r\_]AQQ.
E3<=3h>(T@"MbRr]AB"WnH'^a@:f]A.*OfD_8C?]A^=L$C'5Um;#.A+!,=%QB1]A-RnOl0Ph@ZR>
K@m]A]A%9&onpl05N"Pc$PetPGZqa>rC'fA1c3V.0dk`>(ZnhK$7\8"f*_iLFU59nli]AQuBJ!o
<EMAIL>%na:2!O;1CW,7Ye1@c")FI+p$CJi"R[3c^ND?jqB9YY-@T#<TJ
CN6l'maY_hVNDNjc+'K(#DG^:="p+YD=4U7FBon]A584!W:A6=@;]A".PFH)+Mct&*,\<'BgCZ
%H6@C>2<cO9;O(D-Vmr&:)bme#A(CT4>mPl0N1"mBi:baT@H0m.[<ou2qF/k\RHQ\e!TGV<7
gp$M1Cph_*9?]AgTmo-hS,T/gGuMap9(/^Am:^EK_D.95\b^0a*HCS"6/+C[6qG;!VXRG&=d3
g)7>G!Cr3lkrM".k-72Ho2@.X7c;NQ^9k*GgEGQR5VoOY)?4>r[P[=8(c#No'>f8.Q0IJ95=
Ca/]A<KVBl/rUbV>Y/ITWb@T$06@X#I'D9s]As1[^,G7!<j9XAa5LGN-NP1S66cfqmd6\8BVpc
n)i7DmY&Oe(pPO-bIh`3<_(A<@FBqafHe:bFJ)X/5+60#)K93Ti[.t@\M?mC>qua1(58`8c-
T%8cb3FPfqFSi7WJfJrmi_\7PuE.BD^fe2_-XEZTFP,1WsJWpQKKV<li-HT;"8>C42%B-?,1
(A?^qAB7R*U=ST3BW4%.u9[Mtd%=#(&@</CV*/]AI^2Z.YpQUJM""DV<.(V[2.[s(u&Z2jj+R
WREAg;'"]A$e"O)4MDe^X?#)p1pehE@H;!'pHVW-<$lblf(f<:7RkW$I5$FO=d\XK>eS(N_k.
-Rn#6We4nR+3WMT0L.%16ha0ufXkIR6KqLDJtWu\W+a<lT;XX78ERr^G4K#)nCKrjkiCiu[h
=6g--qO9Y1H>s2$T,rq*rTVLDf2/+AQ.Y?_4;!6N+2>k?d(KqZRSPQ=X0mLkZo7gZOc"+D.r
CIhL%UhA3"GP;B"AX:EXb'd`>,esH_,EGq5R\3Yb))H::hp=!%\j4U2@rBYu3W_@BDpj/*7_
lOsKG_:ADZ/Y9!\Y?-jO0$gLj"p1h.sN=F2gCO&`?6aY4'GA.,h]AUp.SG:IJM1R`TiQtorI&
VDrLS1g.#iF%@WT^5Z&BA329@o:.7\?@2B[E,d(@m"X>kO\#3WBWEYjQmWK7;l1!]A&"^c_6(
s$13Ff*Q,B66F8'ITB@6-.8S?.)8VU"0C(1W7D@o_>V)`'K$4(#Jp/nD"Jb%Z6=7+Ca=cpC:
&CJn9Y_0UkI0R$:3SaH209=r]AcR2;T-Xi%qdCL[;?2nS2"]Ai@tVOsW$rS:0Jo:M2f4'N=F3F
Lq*Y7bq#-+FbhR_3B-h8oTs!e-.-90:XnAqk6d<G=2>OHMp9l8!a<-f4*2)WToof0LY<T*aC
A"brgEXr(LNO#Q^.#@S54c!b8D`5j@7XO;R2H$k'(4,8?V=P\?C'!ds$UjL02r$&IJK=*S;2
<EMAIL>:W[g)#[%5+nGG4j/ZBTsamcjLS=kJ9-4`"Sh)jFX7'='>'h7`qQ\&"9A@=BAF
='^nVpcYN0'"G-7kU!8RNqK=jM,+E*?#a;a1LncjAl]AKtBsDicp!)rqJ!s,Z4WZKl%t^KKGb
;7pb'[oEGdGQ7["MdJ\%Y%[ViCcM+^@N450\,gJt)rLq1!H*E,i!-[lDL`\a#.O\OJ/dDN\,
lT4_*-_)`HIi"hKlA4SNQ%V[-H79^]A,)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="D2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('D22').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="D22"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="D22"/>
<WidgetID widgetID="8126ff04-6ba2-4bfe-ab28-2d09f47083cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="D2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1238250,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[14084489,436728,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[*注释：点击指标卡可跳转相关指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?W#odoRU+J+]AgT/)Ed5Lb,p4<hWJ9iCZLD&1[eHa\f9?M$P7OiQam>(+LZ47DV;9Q>V=V7"
fRb6H]ArQ,gqJ[NZ^mnOW7*:5n]A>3i4-CKm-O**+$6eb^Yd7RhY6b.n+1npc("0Kn%0-Q^q2,
R>6+`>]A7C1tHk$-L@IrsYATK`j?,&T-!11L:"+EbhR.=[tY$DmZbgFc7646,_h7:'7^H]A*55
.C1;#FXF$:UBtg>MZbKDp-kK@8BE_l+s%>m>d)7]Ap$'K?V#)*>B`#*Kl=V%n(55Ef4?t3&+b
buj'%[5CU5m'eW4F,5p]A36D"jj>%3Id-9N.f3Scm`AUrf5:(S/<_h:AfPDCf<$i`!s#Pgobt
\W0FO36tSjdB.tfO:T`s!8p0+Ii1"RZ?J,Uf>?AGJGq>;kN5e1r^Bpi'[b`i@4>*B^:KUH1Z
&g"/BT0%m=(Os)<]Ajq5+O_ea%)M:E-&]A`!O)498<(X7M&M29jD%j=S]A?UuN6]Aj]AaB`gq!O6M
DaYMG(aqCp_-Y@B7;l/__"jlmR,?</e6=TUkNXC?f%uF[]A=8S_[9*!mL@G'l3i^W^EP\)^PX
k5^hF]ArA(aIT?Mk(f[]A6u7K@?8c7m:A.FUo4./\I*_(ZRes3]Aq<emKEj@r(YUpX>mNF\1nW8
LnaDOX/-90-QYKmoR80Et?As5`6'H9[l^S`b:JXtABSKegjl<.UuAKcQJFqcQ?.aI_6I4-R"
`?b2tCuW1O.joP<7?WpMgfG'N9Rbb$a1ajU&460q@EbHf:Y?%s(XrY?6_i?<Mm[K9?-Ygi!C
4!73`qrO?F!48au-30E>seI)`2b:UT5n]ANg45K<k!a(gIdNAqCQpk8pX:Le6]Ai";nDmG9it!
D'`u_BkbF341Z/40PobuB$f\:$qhI9nqo!3KRdQ@2alk)-j1#OA4E=%iP,3pH]An\Ep0BUkTB
o:i]A37!G(>CN\IfcP`>Ueu?2)9^;'P9rD#2LRPfb(Mqm(p*,G_00<f/Q9'F51;XIO4ON=)dh
/P*XO58+1mU=g\&Zggn$IRfZbitY)G%"FE'&cH!H-OZ_Sl"!&aD6YcRSoRPgH0pNg798G7Aa
A%@M:8'(2\ZM,_S_-;W8KfQ$3ad:bmd=b"'_-b;p&F1$iIH!g>&##ZBOIna:*9gB>%0eZVr8
R.$PB]Af>fi6fknd@Yt9oAea114)-nn`^YRtu$97aYS&F%%<`]Ae]A"hYD<"\:U(ff'NL'njQm/
D?=tLlR<7j`m@`tEXo>RDq\A!r"APD4lU%m6*+m@5Q#XY]Ar^^k:YAuk%['rgg/[e2rY:#L7B
23C\j^l,b2M^Q#k_QKZU?Ubo<ESpRP^Y_rARh?Wrupd6Nq/[7R=D[%U<Kf6q%I(uk*'1ST3?
9jY\sfV_Ya_P#'GBfLge\YAf\X]A^ueZ5W_aPY17`1T9O7<j?SS,`fb^ooDdmnk(@#b>R%&jj
2-nQ>Q]A1%U6$ID&M0[_cmtR1N3:]AIZ&Eu`6G.+H>B@4N*dc>*k`^+)o`JY[q[u\7Q"E.u^S=
<AJ&Ye<oEf9G9$s[*-BSQ-"D@\IAJ/(N?*Plcq)S**c=(LQY^Wl10gJf&lfo1)D8#]A,E[M58
j]A1&oaF8`.!<eOphaLa%V,aYMm8c88XMPm!q0g#ulDkeY4s".T:QeoeW]Aki8%KrM'sMd\\]A0
%05tjTV@/HWiI8G)T#lUYQ@/=l\0[`DG1U/87kJP^53!a%YMC7*3RQ+s_86/0)WE4tblKZ>e
A]A6W5P$gnE>XqS_^WjS*lobWAP^=P1*8ba.t-P-:Eo%]A>!K\k#Z1r.RnO>F"LNSTJps,b'>E
F5hEBj8oiW;kG=L+861)_,OYl`lFql[<8?QSs3]A-o'7an9J-4K]AoHuRB%2-mMO.ro,+=<!^4
14AT]A<u___JY6he^Q8kJtYS@?m<qVi%41*<-!C_G`IP:qi42V[K^;-F_6S(95^^H!]ARPm1hf
VSi/NkAif^/\3UZG^m>;B9tF'&\&OE0UIV^V$cQ7d`VSA;.Iu2ii4C-<p-hqi]AIgAcgtZQi]A
B5Dc^ou1L'"MI@4pE#]AK#88>eQ^116<YXtF8A=G6!S2am6Yr>r4U:>LLY&EnHl>":O5#YoNH
oG@B:07duO>;@2=Ne*qj><d_)Xe:UVY<=Z`ZC_iIr?aY4,nVO!4(G\=m;,ubc,0ur@d4Z6<)
6ZDBFb(uTNcUG.'\'6GjCE;?:h@+aK7k]A"3N9*4)MSArDf@Z#C\k>1]A+]A7aW`-@]Ao^-nj4D"
_6f9kfr=:JEa-I%go37)fa*=OM!`n,tDM9KZ^g37W(LA[S,"[)eO@Vh\_+\O$`SGc4s[&YAm
2oYQ%U>g(4d2<.:f"oW*[^I(`0geN<!FfC!pF]Adb`F>0))r.nMfN^Omh2"`3o@>*X-gd+ZjR
alfCK@'r..U;;splE'N9'j3K68T=lLBghg8:-dN]AdL>3nUn6'ZH:CK&k]AJ2deB\Ms2NX$Q3K
)!@]AeA6)--.C\>f@3B.g^M?5"?uC63eW/BN+L_-l=kI6#>,63Vt)b-nZi^@hqiLRHhNpm*ea
n"Hh]AEtH1@f21Lq7'Ge:L9n?K/YNcQ\sU?Mp\:%k[%rrWZ&BV:@MD?.JL<fd8f'bJ3Mc/Q(4
N%@GFg+-dc@buHV3j\^k&+;`&f('7Y\_R3jMeM.ac[ERt"&gY?/:%0Qb3Zs(O'Bf!&4EON4i
H5=.G&Y#Li'\.2"]AiYm2\9l]AUYV#*Dumgb`t4<dWL4*b[$9ITUW:jo''rAK)q$E<.`1>B.?p
W1,0B<<2V*I)QtWrs5,Eq6]AC(Ip\dS*gJ+6tJq*HW9&#*P]A+tCMjgZK]A6drUdh?tN1]A]AH:db
m-W!71nB"j]ATP4:Zuc.;Ff_X9]A_l[K\-f%<<Tp4)UE[?gH7q<u2'/-OmZpeC&R4oE0dZSh>F
J-G$gfBB%)k>5Rc3Ibr*AccLLS3MT*^j2VD@%63SjTPYNn8%3FK/rc!`SQ%7^B/qn$QT7t=U
hC($EBa+Wh9^!TF.]Al$EBa+Wh9^!TF.]Al$EF-(WbmCi=_PG&A/j1,E=iTZ:-$&Nhn6BU&f18
a7ghJ1&;2//+UC@>64e_[KHLC@#XJ__&H4?@&k+^Zn'J=Xeam.!ImtNi^[G.QhZ*~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="40"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="571" width="375" height="40"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="372ed700-67b8-40a0-bb95-19ce2b0ef700"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[524786,723900,723900,302149,492980,270344,952500,206733,952500,206733,952500,492980,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,381000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[seq()%3]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" rs="11" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/指标说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="7" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(AND(LEN(A3)>0,LEN($$$)=0),'--',FORMAT($$$,"#,##0.00") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_zb" columnName="较同期增长"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$)='true','--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="11" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="11" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="11" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="12" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="13" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="7620000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="3048000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="3" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[X>PNggTY3RP]AbmoP[XsGRG6n)#C=^eRg<>7j4U8SV9H3m/D&J,[R,D$PaMLoqa6!>HT@d
&Wt&4/7]AKUtM$o#h]A3npTCYo:@aZAq"C459/h-rET)<Q`GM:B%YuP'<*inegt%69Zm"25L1o
c[Lr`@>J3Z(94lG8L1SOJ\kW2eHJT/o8jU*k]ARU.V4#=YAV&g[<[f*]A.:L`B1V,j%&^ErXLg
m3;2TA7-WjONAjQQP98T6R)Xma:0MXOXLT)^kRTb3$hEgW!k:Sf\It%Y&W\bmWR,_)uLuB8R
g["KnQGLg*eS1La0Fo'G$QQOKB5h[cfBOHS(33&6Rm`;&_Gm't>EiHefm>HWg*p$R_t[X.b+
YqL)QJ,)_RHH[h$V.2'?;e8&X^Vl$:?i.m:a2rFl%*t7,[/PY]Apn\o(`UY_+5krO.IW<`6=4
CVO8U((K%W/cGQYg?G<pP&.YbN^OR$L/S`<'SJ/tnO1ZH51q?&e,\VR+%`r$HeR-T.:=nCNf
BU=GpMpB#kJP(bQ*a1gCd]AMeOoH9L?R*VeYrDe4V5%o3Pq0/L<9b"h_NAD`:$-iZ)Xo8`gZ+
lS%Pj\lDm62k`"@l*20`psqS!OH0Ni##nSafu\LQF%V1i+]AR#GhO0BjSeOXKh;p@R/H!-Hh)
f]A\/36?Cb5sIB,c5`&<?_>493^anlO$(O@4]Ah3lods*u)(cOr`=t>-3`?q6rD(N#0QHVD!-C
0"eL49CH6q6*G_/aGO!\$eO;+WL0V`(2sJXUT)M%BD.=/('S65b]AMXV%(m9k150uYZWCtQ'^
kLnjr%0Kn3mn>r855T&W0-ahE0Y.Ti!c\H[`?;GK9tCOs^a>5$e<Ghta>4THYK%m!&QsWQU$
M2&gCbbhbtFhKJWeXEM^NiNL@2"jP44a.cS9RDB?dl?Rt7Dbc:K6UQ8Ks6s=C]AiiU74lGT9M
;tYo*JlI\[]A)3pUH5/$m5Y$#^.L)QeIH#TmG@TZ2[&$%<?Ec-kD*lI]Amn=+<$nX^oKRN0YM8
>]ArLHG[Z+nLQm\(S,?JF4\24eIj`g7+;Hk[q39')&>6h;b*`X(`dETD0Fh`5J9'#Y8d\l.4C
duOOQG$*\LI#`4m1r1H#&%uZK_FebjeH1'q.sR_h!$>K$5_]Akr?l*^I=4<SB\llmc+7:HjMp
=XJ8muI^^i2%i<nG%ZMA>Z5("$"P>n'E^_lnc^L1IaA.^cG0YT75<m6;:U?RkO^ONLI\YA?e
>VQTAa]AA%DW`m$nQ^:;NVnlVidr+)W.nV(HN8cAQGIc":DhcpZOP^:IDEC^&Do8pW<RB^_BV
2]AhUf^MR_QYpH2+@O'5U&qk__\V4u*#[q6YW:pBS43eI;$$:T7/CQ,4Vi;%XnPO`CV6^_FiN
(5bN/&a>:75jpg'C##7(uhf,J$AQ-ZR:))n;<M[rfWZ$GcYN3O1>.JUU/2",L1?7L4I/_AJJ
?IF%`LhG>&<k,LRlJT]AT=C'K^VK#X2Jer:o_&dTN4p7I*%R0_n#Cj>ni^scQ<L[V:!PMAK_=
K"4jgEf^$)=nN'beqL'4p6HHJH6LAEs`!rkRWpVWWmI;:\)!j:C)Eg>Z1,&%(OK:ouGOcL!9
3S=#dm*^9<nkUh1-4I#jY0DKL%"jW?-*GtEIm6)ap7[`/+S.m&qaFeL[#CJ&YWRdHI^-hI.<
ZPooCZ@.k0MfgHOb\_LAtJaI3V]Afc+r#,<EMAIL>"Bp;gk-o^<o#7;^T.\H^&k@\q,.IR)ul
Igf1:J2A<c5>%Z`=$Z;t7Sk3D1-PUoa#SW$qOK)7X=_It-+i[\:X=!mjAP:N$HmK4t((%Y/Q
AujZ9f63,nOdn5Y"-j"C<o+0V1rJi<)W3Q=-$L;Q"C%,lRo-jhDmp4ER;ED<pZ@'lML6iI`f
.WC3jC_"'Oil5)<K>4%%kbD<%?<=f2oN.WlON,X.$JNg/]AH2T#9W8W26YHm@i)khlYlB29K9
lnrEf0YlBiB>#h0<@GZ)>N)\Cb#@X?f4%J#AC]AdbS!=a!ta%`*$-[FKD%F_pV-5)(.F>gVS,
;;VdTjh&.-sT2N"KnjgR8*7c8h9Qk`d1VO`Bhak3?;gcABGPQ-n?[m%*/:F<]AScHB,d3]ACk[
3>lS;2'8'p:m@VG^!kc-1f1]Ab(,p<)>6j;BRSD@7^jS"A\sZH6$F;hfd(:9Dk6VA2??BPUi<
j7HKZf(j4);d1>)\"m/(&!%H$))[/a-O8c&Fn(4AQW;8LJrubK`E*i%S&7Mb>,-;/7*JN`MW
i:]AlnJ5-fZp5nU@)!?(>+q5DSBLmCR'_l$dk8)6F5*na(dWd"c:^>hXMYZ;Cg+S"i!a,MJ)I
nS5SPK]AoKP_F?&T4U`kprFY"%XoPQW<L=F>QrL7=K)]AA;8[>hR;B(q_H$[LqVFMGdgle(5C!
GWM/o-KInJ/brWPg2_)Rh+(j`_3Y"@h\ZgXCbPX^2N3F>u9g"Bon0MPN><k7id;G1pZ)1i>-
O3ArY#:l*9"m)rK-lD8P8dIVes.:rZXD/#D^D4.P3WT7+,9nEt&ae,4/RI:hf`e@,YEgfu!A
n\EsrKO*M[?2kZ'IRBZ=3"B(os%Op"4nm,I@p:_U"^PrX^uB#Gf!>j.SGY)?]AlB*ss*'Zo!k
s<G2G\J&&t&H:4,aXHKcGb,ZsPhN\m&?a6dUStAd/ok-UGaZgk+=sXWee3TAraX!#;I.(2Mh
mWGZm(kd>kBU#k`/ep9aNdnJNl2/d2B4e?%)G25<V,2V/t>pnA+$f5]AmXspb_:IXEXFqt>M]A
gGHi@2X8t?a#I8q0>l+MJGTT-g'S*);eZjRq!eEX<`568@p\'eggFj=_ed#_m^oa!3E5'M)[
DqDTGaW&AI%p,PbJ9>\KXk]ArhZmcJ=H,B]A_kALHP`cK2iT$o7e/*N*3D]A+[9f?AT+?5GkZ9j
H4F5*hPVO]AP4#Vj@pRQihcH,AMG4J(bdgUDWV0Hbbrl5)\[","o&B6p_5CC25,^U*CUj2FA.
.NKm_T69lL#/KW8J.tj,*6%<54Ak(Kb>)S4po5dXSG7;jU(GDNHo\ejdT>-*-;<Eld)e4ZcH
0'cfV^"?\aqgtZFnEI1+r-^_Eld,":lUp@ch-=aRCkBN"7lDg7g^;=jLUusZ&rZ>c&p%`e!;
f0NVO43m5m^j%>*S@_=aS\'$LZ!1Am(1(B>bE-sRC\q&<)M@dK0*;k'1F:;EJhBcPg3uA?4\
Cr#G/4b7iY,mUrHVYa/eKOqB'ln"qbFLp!:cOnb9G&?3@6$``!V)2abl-r4[]AL0_hH*h)P?B
096JtOL*EGNrq_5;Ag;HRH7fA@cuDIBQeZp_1_JVaua)\T"T2dZAc#cl%#;-Z!:$8W"lW3F$
2W$<?N&G&*c/^:=<#5BlgWdGQ5"$5\,!\?stnRNeg@fcc-X([eX/t&U;JXco(%cCcZ)Pig@[
^l-K@..plVW5r<@clEojh26"lh--Un12>&gIO9hdUlNk654+]AV8Il#RS5R>aiLmGls"LLLii
41=2!Yd,\^c]ArqC/TZd$P`$PWq.mU'nnLkT&C0)\(]AuFeO3m>4MS-PdP'^q/6\f0%i)Q1fY0
He$Q_SW$&0US'!c7n*bTHU%(_`XUD)cG^m6_s0lguQER`3q5?GYS3tYf"_7M?Wh7ESsih4RV
.YGNJE$Ie@Xd0M1`YBpkc)Xi?E1m@Zb(]A0Q*-/l%*+Y+afbaJ2S(g5gMTf^`FP?#RK&@\kT-
*Gc>EZSc)/r)e^tr(<P6//`$+![6iAi!qk\qWbCS<@)*]A0cuU=8N_pKCs&j=Y9,%>#b^L)_,
&D1rYgH"<pI-#>/$\p4(p.I`Oa.#oF>qDcjE%7t7b<4IPmqe0#r"GrVmfb%pPRHj!cd<F'9H
0K#IQ'&H=%AoSIQWa1tDqaq7*:X9M4_Dp%;*aH)+hQDuDXMkF-!YNnr.+3D,qZj%;[WjNi-S
C(DuZiXQ-/;N>5;Ei4'9LYh7[UGmW!8_6)]A#sZ'Sl^[TdmXI=6In]A"Cim&"1-/C6tG-NjS*E
ZhC=]A?fAlj*PGpt#a&$K1=&hPMg`tK=R,S/BH$LGP8n_[0VMq32+Zt0Jj@1mItnO,jEGoLUF
Ym]AeVS@2/nL6C"\-c0K`\"XOXY:1o5:OMjJWjub[k(Zj;4E:C4J.1Ri0Meg>->gX`S[XBe'_
Ae_AunpZ^j[%'rIXL<QPaLo?l'$$mY*Xma[Xa5%/L)Yq:HELeP*;gS:Yqi%QM\S3%nGS0Wnc
9^Abh`1mLqQ29ZPfi46LM$j@5LKp8iKM6mlTWY7D.-`>*f.*I&:NmmZl&8ff]A,,,+To>)I^[
A*c@JXF;=O6&WP$`Z>XiIeU6kT2Oe+/M%O9QN(iIe[M!8ra`rm"+pl'V?)6VBY9t%:^L&Frg
"W#R\nAf8q.JbSqm!e%<U<Qs2IXT-3q+&9-G:mi/)<p\FGpOU*$]AiPR^b4t%mZRp*BcqiTc)
cVH5AQP$XUojnREB/CKc3\#`%uF;KK$7K(>,OXK&]AWS'jVR<:]AmWb4S2mN19?qjGi'<7720]A
tQ?Er9/C+^#mR"``MfgQaVr\=)(efb0kk(dl;$,6S5)SMn8dR^:W6iBYf)PZ0bG?g/T&kq/c
q0!uUVkYIpi6T,nY$dLZiTQF#@D"SA]A,6%md6J#X46(!^TML`q#jE?faheCTC24D*^'>9@OK
HQhI`Hi.^W'm5>^!-m-)gpf<j)$C6S*VZF?ah+-?InTpef@S5QQ5U^lU=VT@KN]ADDk&PDEmV
pUQ,Q1^ZLTH%A\*$s4D;/nXg^o,$6<o8dd9Tr`=C8NhTXk%5?TS4]AHZ`L?1]AT)P0k>!u]AtX5
lq;UqcL@)_PMn"('a!a1GXUca1a>mAWru?\-?kh,^A)7NMum_l`G3m2dY#G71Wn+G[dIT02c
\nD!);NOD0$k_V)YT'EY4PF1iF//!=+L#ZJ6l]A&*b/!U[@dDs>";^:@_OGL@#)bp0cJFEq4`
12t.&_\2$IA,8SiCeL7k/\16*^B05hB3+KOT"D!51Ad$AlPa?"q&urIEGNUbRu4aL`@e'\'`
>De;K;U4Dej/&Wu^!)\5j'$*9')_2EV+j"Ap+H[bJ(QV=g(_,hl%aEBAKs6A9/:XK7[Vkk5^
WQj7M/ag>)@2(Ce!hn.bdu=KO;d^oWEMO5.r%@J0%R8Jf]AqaPg&=[u4eaHT&;c-eA$aX@l0t
&TM#&-\BW`Z(1pSJXN>8ke72&Br<pO93f7\s^l0UEfOna-+me*'h!4^&rl>SAnjXoTmIj/JF
30i4]Acg"IQ(h)_14/(\r]AH1h(5f[osa/8KBh;l\2-/dmtAna5nF753VhMKX%AIuCimbQQ6%-
MS&a?/)\]A1@YiGOg_^r;.m-+Z\(3^?*/t2N$>kK5u"lRq-@3VlDoRLf7MIC1Wln5W[!9oq<%
7JM0%3l]A[61j2B`/("GJnWqgZKo?m(5g%g]Ak^We"i1]A?^Y6*[t21SrVgYo:a5f=>>+'e]A]AH,
RWu)O*[c6tDM)UO#-\!m?Ip.pO[kU>]Aa!-ZD[8R<g/\D'<g.jp87U'2>Eb]A[moDdE4A.BH5q
54$`,^*G&^tp_S!/6jpO2XZS5?ZZmuQ6DDZ&XYh*Z-q=U6>#V*HZ81UeZ:-RqEYGE4G8(./\
0OL`%t\pm@J2MhkefeOudE=6cMG^+CATcO'^f<5LgV+s%;,j$!#]A:>*T0+O;07JS`0gLYss.
qc44ibo#9$ZhQT9L\$o/,fi7H>GBjTPJDO:L]AB'qaK=ahh=&9iAD`4kN[^UC.j)/"GeSA?su
`TqWD*D]A0KJC).c9a1PDa;?n+JYab0p=>`Z:E?0Q@'0#W^'Y91S-/tI$^de!a)A2r%"ngnJi
g.Nhi8qTUa/cOQ*,&!79^C?@l2=q(B*87t'aX[GqZ.B185b-)+Wgr-h/gib>pm`MUIA;)K'A
<hZ9%NtZeK;uhK69>fj<V0e@%*J`*0P+fYi9O:6'o5TGkTdAC-NsI$Gp<h0abVMq'i9\Ka1B
->L8'P=+=X[6IjNbTE^\18KrX[VQ$KHW/O,(5&V(.ejGBpHYK$_@,CPI5ij:r^UXq#/WE5$W
K(ZtZH#8m?8i2#,qG(']A4'lT20KlDk%JH%^q*FJ;#V:\*T=gOAE\EZmq+ES5X_.dRQh%h_b'
'_>\C?b,#9U/6P16p!hZ=@Pf!:ZFC=@P[D3k[Nc0>*q)urIBPY3YM4D=ZP11^/^GZY0oiEn.
p!\c-KCO9]Ae&lF>8D*Xa$GI>Rg+%MW;YKeM[,(]A0WicO8*U)0*qFL%T)nMQCk%6_2+J>5Y:c
t5t0iM]A\]A_R_nQ-kY8^lMl/.GIK%!,?iA;Z_Gr/T1YIS1>g)/`=RT8:YS[\1B'#?.dH'KTj!
EMhRtN[.(G?R#!;A:]Ap#ur9qO=+[.m=:P`*[2/"sD:g%dF(@=<=:Ej[6%%)dsC1CC:Xs9#HB
sLhD)7O']AHA-`gV?I6%WF9YfMbmDLp4?kEcr,6K^Lg=s.Vh]All\d]A21&Rg`U6!JHS$k^&]AO7
LY(dDF"cC-0,7,;NKjE]A9aojh6\[.)]AFN?pk;iEPmDA?k@upmtpG_\,]AD(;Ncf;[i#KbBG#X
!I,f]AP`sGB><<q+]ARupDp^)0r60TE[%=IgeYY(DJkD.g>BjWm2qAZr`j%mlBS5Gt#MI/LmJL
E[G]A9KhVm;HUaU[YJShqki9W(7]AM,``Gf.RI2LKu;Yp.JN\(l\MlZp+Ti@Z$BD8C:X65=NX,
>YQ"#^>-++`<qbbpZqglZF%]Anr,5&3lR]A\(6ci?$sn;\GZPg*!f=LUfU7M`OTbu,[X_pZ@.*
\qT)<,m9B'9Y;LX_+Tf0!DZTC<NuN(sGU!2RdApCE'?<ql9VQb1=W)Q[!r4MKP9!ars5K'G1
srEYOZXf56"<?`4r(?@$?$&G+=8)imd(F08>@-2>j(,C(Ffb'YQmHXGsd+H`sUYt:)PXacSK
fcd6<2:l-JN-'*kRBcp*(-bk0"J.9^^a;\QrK]A'*F=$f0Wd%SCm?J<COd3%g$gS?2ZZXj\MK
HmhWi@H^f]Ao?#U-1UX*jB+[l_Rd@H@d*;O'rulai46XjRP</WON@]ADS/:MU>>TD7Q;?!I^Z]A
mOD@6Y:5URbCTLb!O@U\hXTHV5'$;u]A)k=,%Ri&qm?57_5:nF6Hh9A,)3In$=="6[pH6ZP@H
!X9r8ucC(gL?3CEqQ?,38pQ8i'Zs(S[ofQ<7m*$9V]ARVT$(#W=[ZO*pDEbtL38T*EN+I;QK[
QZ[sqR2h12-mkato\G;(54DR^M2j"qEUoj@C\P(c"/_3C*dD1*je\tV0NI%kp:Euj=7>)SL>
Ib:cd\p@[J3,MC=OP!V/#?JC%hg\D-7Ja04'4Du_:<4MhR3^@5b`Di!J?ufKDZ9Gpje6)X7.
VkaoAF[c6>?H^q4?f$*JrP5G^fR6H]ABoWM/)*drXAij27n"&?+p3UTWQT9m]AA]Ag))5ar'6*-
*dZlbb`IlQcS[,f4m[UmN,1mp5TK"Tg(n=;tee/_TN:8t!Crn>'4Gm`q/n<oSK-CHdiX9AQ6
e<f/WQjpUd5CUkPQ&c*HNIrA^SaTho0]AC?0461+V$km^'JGp2r8Z6=+F=4:D`)#=O(qZ\P^T
TfY+C:%<GPn97/cB)&kU1?gTiCUq+lDp>am\f`%0UEYrGL99.]A=VF[Q,*K"i97pCMU\6fSZ+
lZZbZgiC!!%q4AK!-sVJ$JkU)AtBP7%R@p9<:(!5)59M#`W*cSn'2QbLJ4$loX;NArMT1IHn
2Ur8b&W,#7leT=MQ1J5o%L09`$EddMh?cAb-G$_t';+;L5[jme3"Hpdm<R*jSE")Lu#%@mn/
";:R/-@aT!CL2J[J<tf-_e,U"<)fJmL[s)o9"U1T--2Qo6UB]AJYY+TW)i]A0pooTc^DiW<7d,
nPLI1PC\n?*S,^rR#hWQI4)p[K0u^R(L[p>cM?^s'ftZ1R1*aB\3)c)qn_DL"NZ3i#?+bZ$?
qIKg7gLWnY4.-q,UPZ%k".P0F%>gN8qdml^p%bJ=F?5W`L5TePu'[\6INNdQ;&!QmMG2*2M9
XX$Oa]A0B\EfQYs75Uh=W%#6%e3q3L?3_&k<O0n9eSooNYW%psf1+DYns"[GZS_'.ia,;l>S"
P71Pg)+F6+@)5Bb$F(8hX\N]A?5*[SuY>0DNUnng_#cUqPlW\e?]A8Vi0JZJBo[\%dT/rfN5B)
':+hBA^No.B3HGkbS(7,o;Jekk_&P^UU^$2'G.r#K7p)QmF<;r?FWmhin>Zro"&I.#\D9G+G
)tNLb"m[lFADO&W4hXpE'1e2BJ8$e>04Qu*1&G2NXW-DApd"RoOtu8Xp(\SF=KCaRZKHQo$,
+)aXtesc3!9E2aPlbmt$d%'1M'2]ASA]AS5EM:47_0kf-ko1meIAIRS_Gh-FTG9ehEcNq@`;)i
+#m+@MtR79BQlG]Aq=(@MY6Sn7_hehtRTD\tI4H;ZY\nl4AU=*`3\-n5:UaGmR0Lkc:*E*,Ss
OuZ6;UGS\5]AH0?`XaPr1dJ.HMEO)#$/B?GS<UtH&Q?8U@sU`jC7`>4_<pOca7A^)M2%-C.k3
uMD5OVnGFR#*4Yl&kZZbQS?2<Q7(a[pH/.4nWICBe,OJ4BTk7<\T*m$3XF"[p9MGK4Qh#J!5
OOTsbYL&;eH/sM83g.&D.6Etk$Vr\]A[*-]AXt,RIi\<MX"==GB:+aT@5hoW[0_8!:-hXpeIM9
"[.A@;Yo%gQd25n5a>mP)4>d?@Nc;!YeTe[SBMVHMpnEAcF3k@C90;61D7^I`uj^*Gs"-:Ip
`Q).\8*2DG<>glOGiIb<0e<mQ)ehks9"@F5m`)*mDA.pGKd/1c6US-4ciB7H-+e>sdn3DKg9
l$LP*=m<o/-r#bC/W8WL('J`/5m'A's-O#=kYse'-%73;asodejVc:&+([,OQPFHfum]A7.sj
!<DjE=-.)gsEF*!GII@SiAe4NuhEAu.H3I[QXAn9dr:!Z/Q[dS7`<$"qZk&.:3A=+o%X'4T/
*IPO:<T`$QBES8^;X]AeDE?0_PZ!?"$q*+De%?X,79Co%0NODn./,cX05fKe7$KC<MV)ME<Ua
\:)NE6+mB#,P0<*ZoAsHk*;d\>]AOhO;S*K;Rp=^Z`EC]A+ZYgWj_W_P/&,W!u"bJWM^#VS<BH
VL7dq(Ep;cXO>a7FaGDjG&t"aPtqoMB*RXJqAi!k&S$CcS.tD:b3,=HMh:H&c/oR=\ZqNJ;V
,-*6PhCq4NHPa1pAoGPlqE!Z9<@/s'g'sec:i!GQO6R\-TO7$cQZQf=JJoBkeBPb+X!$:09J
m[UA#m/WWt!ESbB3cmoq!ZdRrkA3O<!5g4q*V8#2O9P!R,O2Y5[%!,63-g?j&RU8s0B/U9d%
W8?%SeN8#9LmJ$>'H>]A6$VcdgsN+8c9eS2DmbC<FfZJUJXA!DdbA#hP'"eopdT@>iRtrUQHS
L#n'+"q.pkSjCQOY&K9G*GnC"h:B5LFU5a>)@M1,5o(_meG&V`sWHBG"LSL]A8<e,c9*KIZ,Z
\#I`OPUS+CegFb'9H!(u*@W`%[a+?,cIJH_/Q5SWX3gQ[7!A$IYRP4PG0-H[4rFH5P'a7p@$
"1[#A!$K><?aS-q'RacF_%f`tFG^DW;\S:3>:1Q)[r9#=H&eFd5&@AVa<;DI6qJXsKS[CUkM
):A0ZUCngl`5b?ES6d;kdbIrpE_;cV<-q?tj:biC3UWFF39)nS?![lE+7/:qY^3<R2`r.!\7
bj=0Gj,:r#Z_^+_:/lpjj%.HOX-56THZ%ON1XuoMLnIY#A-?breB?iGT\_0Y6J?9_8&u88d:
A/jX4R`qeu2p4QE^Y+[*1:Ud$k`>fP.6*%SXC.[?TSl2P_jQ9(H5T%Q4*.r&5hCmG89<btnf
hV<'8ekTC266?L(DbA64J1cCA(NcklQ9.\+8o:"7BdL'<*+(hYBVLH-\L=ZrE>3K3P*e]A_$m
I8a=l`F=b%+j)Qp2lrU5L6!c\!s#\'[*!mMEqaln8K>!p<-d-^T//qV1:gP"aLr#/E@106bT
R@h-9If\WWKcQcFg^j[B6?I*2a41(^uHa-]AZoaVY=eNON&%<Be6#A*gN;3jLM[*(g"rEiGFT
pQSP0&V@:NeJ<3pdB#`]A]AIn#`G33#5<.]A:htlMHC\9Y-HBK&aS3G>22fFL#n4?WIJ$k+kXD<
h&<@f,N`OKhh6/#E*,hVpV%hHs^l(:Kr-V6,-LPdiq*.r<`"1N5TK"OnoZhXoYiiNuo(]A(-a
8!,kT_VQJZ(\?@NW7UHJ:JleHJk:,Kr0"6[^PfGkhI3a^Sb0kha*OhGoE;^=/S+Us);5l6D(
kT?jTgn/of0Y=^GD1.WSHZ@biS*Yq)`]A0VZ+.tfI$@cFa^??Q2itBTEF9Y`":cOep"!V#kd_
@enCX$20XcoBu&]A?1J$_;O\QCR>N$2Wai+0k-O2j2`#\j^,PVRUFEjob358$:?c2DsGB<R"j
Qk^lB:0Y&F>B>N'>%@1"q/H0694tZY$>%8?-\p5!F*C&5>^WjS&f)$'B]A-J%!PN6X$Re'-X(
3MrD*3D<s8a0XhQC$*L_j]A6X!F-d,XKVZ8p1X;U#G\i^u2L($.-6+=k*QnqgN4A<[Y.g?4K4
]AmW#,^rKNB:"OK:E+l/U%&W936<>LX$]A)kE<^=hTHCBm<o]A$a0qS'XF)k`M:^R=?Kk;`j\fb
%@[1l[p.Z=n8B]AD5pq.'_-,W(Me*knaH$H]A":ANU3+Dh\JG.pbP:QDkERV;QlMMd?R_+@eZ#
4B%^#7]A:!;3[8FBQ>E<!l##-DtU&/NeVc6b5&n\c^3SK]A#bZ*0Xp-#ASaOI%EI\Not7&8U7T
'C>NRN_,h`m8bKTS@U+\Fo[=SUT_Tb1-)Bp.`8M).O0fP9V;[V`s2mN-2?R@c`gBI,^2CrFZ
t&G[fOaZ!3V*h`ujQm<kOA8IU:'4DQ;-#a%K0M,1:dc5fT?91Mm7TYCV'5E4DLr.H6uH-^FW
f)Xb^L)e=!ift[d!aKja[h4!&jsGM/VWZIXh=dA2:q.9-d+:37ntLUgUt7.-,"R5XCk,-WX)
sdVGT`_A&*Mu">hB#P)=FhS>-0!t5NNm*WL#gQ%E`Z-Y>m8+EBgj([UE5C#5/qB@FDd$0T'`
Z\prMnAYO^D.gO1(GDGB4Vn3:N8J!B;g,p'SSp.Q#a.f;H`2M4QOX,=ed\]A@6(6t)@#a=:0.
V,p^!U6&p.[E5D6u]Au;e>gI.lGuEm@;cLVo`i!R@#R2hWe#96+WQ(XS71-!B8rI\Q8mqO_C9
ZoYhngu7WW!,BVe+>;T?p^5J$,#N;D^;+#oro)*r[4.BIF+dTu*E(DLZ*/o7Es[?:"DCem(k
acm14g?56U$mn1'Q[;$&@j*n+a<J3K%#P6)E;Rj-;gHLiCS+(.2E@7eDHKc!Ak$u@Run+PE+
uud5!Z"8]Ad(bAk4#dDO,@r^oW6-IY+-%g_SR[tL>PpW;8k?]AmgRS8AfW:Jd(lKPZ/uBJi7o.
c.:Y3&a!K">,6)^u=dK_@**Jtl\(hlX^hJO!15a(JXbsi`mf$=UZgP/$_2ZR9M%pOW+C$a^;
k`<bfdk*H0=9cjc-<Dt%!A:=")%@_T,Qpt,Y4KAmZe+?=K^p&Kn]A#65"qi;]Af=)Yjk-#fJW=
tdlt5NPaej*8pPl1mjj=3[+7.;"j!n[?4okb<.fn)b3+j!g#]Aj]A'LmZV8SYFI-e8OFgkNs.3
,o^c7^D_3Jc0CTl&tFlrSV,cHQEhn'"$)X,r$R!(H-"S07-/X_C<&Nc_5oS*"rj2cNt.!mjt
%!%KOFN1P6T!\iQeg!LNj;l>\*$JW:7*"jTf:m-%EMO6=9bB&8fNlmb['GV<85V'4qY36Bq9
.hb922_&hCU(-?&*Mhq+fP<%L+-/7+8cge;1I&DOnLQOX4kM.?W%<fd_/V98ska"o.k8W&2]A
,#N>[c`)"30p]Au,rQS(-pG.Q,SY2W1kRENOhQ9.CmtEs]AQ!0#rW:uFiF_3$HduRl/<QQ^%+p
:l(`=BG+k!Y*Z206R5)FAgo<^Bio&N'1L>r"Ue:i+MlVR:r;kSQKFD-(?iT8q<;MRVGBi<^1
b<F6t?mC#'/t`R<f!]AM7g.["J_m=`Gb8JA*Xf/7=a+2@!,P/Y!<s_^%/#@2S2:>n'o*NZsP1
?-iO%N]A\kfP7s/oWafL"$hkqg8RO(o*n<6K`o<Q)JU=@Kb@PKI,;b]AWn&X+QRf)&QZ'i)n;#
(7FWZXaFDl>94RE+%G&I,Ai^&><FuI+'a&N.llriq:IYoYiCFC4_scF:b?D6f#rLAK^p$<(9
6W+K5HPd',i.O!RiMFV,%mIpok.-LM(5EuY?"FFSV^o9-(?:P8$f8H\.0=?@`PS/\t#,Cc/L
kb?9_O3ZW55[27Fc:8YDAVS==BJ7`O1RERbV1-A&F^!i^6cOhn=?-n!-0HPnfd6O;!?F(]A#e
qO^[;f3DYn5oI="m,qKj$\8'&?cnaC7H7bcA"n)+nt#m_0FNFj,4D*K#X"TXcJNn.l-MsX1*
1`;%/5V"I-j"KZa[l.U6KFm/+Y^.?AQ<;kaC8g6$K3eiV[fi6iX7cb'dQi[5drVC5jmD2]AS0
XCa@U?@CfT_'heK*2R'SCP*_HY4&`*aSQV4c,p4AXQUo2JGUqn8pP!6`n2n1%q?#-F5m2`V,
ZZfDFU4CBgIfi"U8_25eYUm:@Y+_akU,iAo[ZKmHZdc-k+3*$=H/M-TF52F'tpelA`Y)-8tF
,;nt._kf(VN.bOL1pYt4?f'(WRWPWet:<V)j[Q`7k-nY%c(<O4"3dDY\&c&#mFGiDT$Ymhpt
Fid[q<mL\nF/l#t./e'<,4-\XeNAV-B9,^P_e73SH=S,LEV?i#[@K7%?+Ze'JbHb/iP\#qp7
QGu[/D;<XjPo#`'1_"Es6m<m]AVEnQEpPOPiL`sffXSGcCnZe,2AqpT:3Y>K(-N8<I#\!Bh`P
bHC>^4oL,-6O)Ll#3B6Idg'`0ej.jX?[p*Isp`E:m?NUXmG2@5]A/AjALAg%!,`7sHEm]Akk>Q
CeFjs"od/'=VH)UNZp[&hKgYNAiUGi3X113L$PT"I'BLXluu/"A35iFZ.ID([9pZQ$tpdb65
.RPilQkmeqYC39;9PdbpI[NG)I:\C0K7C\3mcR%X68o56ET'bNN8lDlk%_:$:-/65pIkD/)>
"N.;hlghS"<`Ieq*SE6:eK%+lgGtk4\2$D/=t?p=L[d9rG1GrKX1V`PO%c)pcKOhU'sM4r"\
c6NZVZ\-mo'Rg3=1*MaLu()nWRt8YS'brj)0u;MH)]A0D_g4J0<"cVC$D#niRs(9*iFu`q,:\
YZ$bOXFHNFW$F;MGPQu4fE\`CrGk%WBM.-!V8,^,`_@bKB2?KZ/$)]A(/o$aAt4]Ac\sDlgO#O
1_GkItQt]A+b$rj9#c'3p=WGa'uo'4o<j-A/$)*KM2a"j5JeP%603-m8=>c<`k&Ou83k%"i>-
fn5</3#--s=q(!^FuOK-s^1YGdPD=G=)Kf3jt]AnIMS&>Hp.]AlmCE6#e*mn9EiQ"'K6*2);2\
\UV;>=/W15g:3<@]A>naZFh?d%\N$DJ!I_tEW&X&Ef:?3P;PhLD-.IX>06]AW>h$ZGd08OH9V+
Kj`CshQi>0Hn?S\HoY<lhhZo.ejmqEjl7nZi`_]An%R(FYT>5TsT%I,Q&j9V5OaL7Am@C6GM-
_?DS1I/8r7i?g_k*^YTUK'EFud&:JjWf`GPIYMnQ</gHGVcdZ?@Ti3o+E9>g<kK-1TD#GpoS
:;jcIF`p"1[TEilpN3m:uC%G*Qg2@)OLEJ""<5rC9kLlZR8#[bGU(b<m$l\Dj\"h_^0W!Pl2
-$(+#@>W,Jml)VEH&ZGpD/k+0V+l+:,cJWt<]A?:mMq(pne:oA_lq5ZK\(CE5J*VLVK,&qVRs
IPkfK:oY+/7]AMHu+nBqGPmP%(pfFfJI+d)LmTX-i>8,2$]AFg+q&E[hd=$ZLGjST42"FBLm,k
$W!nUSUj1(&s<-@<EMAIL>*mWM<+[>FqA&"oH1?'uaFfUmS>T/D(O0'2
3*8I4%:l1n`Jn^_pW#iPR`-aR$$9E'Uc7t!IX,h!IGfLNnJZ,ucYME?G1o6^I*)fO<f\uC23
sm_6hY]A"mCMWIZM9MoJ)-Z_'jhJJgOQ3e5WHeKXre6&oTFtqqR?h#R\:1>WDYcT$I#$tg?5n
\tB!@9Ms/)nD`D'dCao>3SbJ0tKs.)]AF3>LEb+T&oZNck"#p>:T/EZ"g_`:^<R#kG!V@?/)C
.@Qht]A)kX#3oi%;%uSZT<fc1/$sOjC4X!FpaHgpl4m"E)&D7.j9__MiW.dbOBH_\"#QA&N+P
b57AWN&OHX&1E;<Q=I+dq/Qg$eG6&9`O?C)u@CkXPZh&`7!VhsPWXpn)A2a_Al#:&;=B:0Q'
FYiKZkVj=re4SHP'MZ;r*0-U1AU6FmB*D(5!1@j`E<K7&B8k(bS^.jsH]A`5ZkDVPFfkdrXR5
H5qtgTlVIjK%?N2*VJI5`Vfg1$?F@IqhbrLH`KCNnBe7=#,-a&/5cZ2#k3QKDdX2MG.smZ7l
ftl:q-+,J'`X(eRY.r#rikU"9<Lc(6Im+g'MD%6ml%<)3`^88Gsuq0:L?"uNftZbVbQ5<BcV
9iUBA(F?qib)Fnb$f0gb1GKe2JhjO!B=KEMnbOPSG]AahtK@WaQBu#SrmL'QfU_4s4),Rd.Z+
^2:mAs3p;SDWt)>&Ig`+W2J=q+HS#q..19:qs/$]AH_WWKFA)K?oB`H*%hW)CbpdC.7G3dT^k
7T)g6>H:gWng2XR2p+'KVRhF=0l*P!bMl]A4Y4V52E%qr4gVV$tr+^+3$&1QY)RGl\$Tt;7h0
;&KY*2(6"4\leSg2A!"q_TG<GtjQbD!`0iO2<'.*;9m<f/B8;Q_A;4V@l@'[0X+NYO,d^FgN
\EM&KEr\1;@b6_gu?pJ`p#+(@(1TZl0dr`i"[!S=c(8ZstdWZcZTJPb@&qKnJb9WQK",*f=F
lJpa*"/2)\d:ssMCHd#DjkX,Cp\,D!XjRKPEdYrAG:2H8&kLuld/CYk$l.s>otP)(7iW&)jr
KLKUJ/`6aj,d<,)g5u]Ao;OuXN_8oh&9S&8&LdDl[%c`94[qHn-ppGL")&GTV+IV:&gi!$'>-
$;6.g;*]A6(tRcr9%X)E%`Z;&GcDb]ASNC:'SiGW5CIbZ3AU!\8>i5"W4J>K#atX=qu.p,-_rh
AZZAKen.FmEXpDbcmt.[4q(&I$l'#o&m?"&j+<iM3U(ifhElOJMpfk3-P8[-jKKO8U@sI:]AC
[8Bi.k4;M)2m)#<R*S`BpYehi'i@_H3ZY/fJ)H"S>$gQrda)5aHi4;Ob:gi4quBiar'E#T#3
kbn%RG?3`tBjRi$7t24Ldi"([*$3<_TuUV!^UgM]Aj!Jb"7g#@D6CO#q!m3I.NC(#XbuputQb
^t.>hl_!d+:,Q!g6ubWE:\GH;igkZ+E,8f=(5E58Ic0fLqD>MrAjnp#IXI0TjIl31qe$(=+F
m`r"X1h8$=OP(B4>7o5cK;Jl';<jR#Q:39h.\L*\>+C_?48;?RHUYOW5pCNVeY]A^Ne2lD73)
@]A-3U)gFs2I%U*;,.t<!,=pkLX:M>2a_u,V"T'HJ0]A^R=j/,D(>UG)#S=UBb)9Jjs%M=:Tqa
@FL+4VTQ&ei/,8pq7JS)p&:dJ<p9cY[c)NU6!?%EK\Zap]AX=)Fd!T[PXUkk8/:-TqL/@lgtH
3m*c"05`q`%k0]A-E1&7L^pkj=BX;=qTV=3FE03<sRLH7i]A4=/RoR&,<[P%kC4-W-=s-.[-0B
S!k7Jlk@)V5dSg+E#pa]AA.sXd9XR]A!_94aCn5k9-0_7LX)=@ppN2;,"it-]A\fh!`]ALs%dYjj
')#fo;.;>'RW/IT;bKc]AB)!'U=.*@\0=s7M-,Wr)`h?s.KQMS0Togl#0a-Dg;`k*Df,a165W
7>L;lH\@0a+sup7nS4+8?I+:V;]Af!b56Vqi[e>U6BI/BpkMZL,U#u`SR*\V*&KUT;*2h/*jo
Y7hksMe]A[-G:,'%$1EYY9DB5d/*pU0i]A*#'G1::SV;mB`B5:g%#+7W0f_\.i7V1IICQ_Q''D
!SPl5ed^+YaV!c#]AiHY:T:Lt5[fHB\4)UP^NpZkR*7h0@R@o%I?k,96K:p4nRC3+lm&Q(5)k
[I@Bue>'991+V>ORmO\VN:I.DAbf,FeT:aS0bo6!1YcFnCAHhjPmqQ!QAQYUh.^(iOP9*iOt
E3Bq[2n@fYsrNgJT9+hJ/-!r!ZG#dIQY.Qt-\-`rVpF^q6nrI%nUf`cHRe@G`TPP<\QM'K2D
Doh)l)#Q-aN!V+Oe^&BV]A/@/*H@@&9pejI[;eieQYkjAMh+9eM^Qp!C2b6e/4'R6L&VCNiI?
E&rmr9'p4b24[)ia#HhqE2#sY31e%c%sjVXZ["*E]A$FK5(+Ms[C%:T]AQrDO)o*%!Egt'P0Xo
JXG8ck1P#b<$q_1D6:u'mo#F)2TU=VZncP*-%.62AogM`86@`G#tD[l'^&t3bd`KH<Ck=R9;
\QHe,rd%,T^(-'jZ<4:FHr*ZY=0m9pD<7;H,:fc'E'P,hV?FdfFJVa,%ZKkgZQdHL4"!^27k
3O:Iq$!a7>:(;Ml.8geu2YpU_I4(KS'_p5"E!ILJ6?+HJ9F#ST`gf&Q&lI21;T(b]AXcJ$K3U
IfcWd#]AmqaXZR-'QqJ21.86uq!W(A"+*ZYZ?C[[1KgFXdFVdoqoSu6,MMnOBMc>\*-+/TAJP
oW;#?]A9n1="u;`eE$Q>X&0G%j,<%m.FT%'UK'mE^"%=E*`b*JB_VUL?ToQ+QEg?r7.MBS@YT
dXjYZ/sG0QQH,n"?m_)&>P*>51IWU9)?Lqu3-3]Amjk%dsn$'36*I09*%$L5]AJa__lTttn@eE
T0td3q#2o_kPLma<5oH!%/H&_ZFOCGn`'LeTLrj2pRtF.F="WS8NUHl1_7Z@Q%\'PF*3TO+J
RAYQ,#/!#$H$IkDBk2daUb<7WtBOICg'`2Ws?9;m\9&<B_3P5Wlb<[85gF5B:AnJ$'H6t#1<
&-Sr_F.cgq^$O-3dR'ncF8+kqfLCZP`b%4YDk4lXF(+R#Ur3[SIpc"K`,"h@)XJ9^hk7_0`P
m'UN9ssW-]A:;4',*3*7N66T:HjnP-7f"b=Jc;`F+Z$?/0^<gk3]A6Q+YL-fOBt06,\(q\5C]A.
#XJ3-^p*_cs+UKG!*;W.*n.m4SFZM:5EcfJHbB*G`2qcA"!-X.3C)^+,kn/?]AB6/H[uKiUXc
@g>GA"QDe]A@g4:hR9fDfp5rUWgP!)>VXPRD-n^i=m$"M:q,%0$N,n0_[p[>$>Y^J4HUhrmBQ
P81c%eKafH(#oH<VQ!*RF((6eBS#auu]A`IP%0!C\Cdi[?&DS+G)T9q(PQ[03$P'HiVjOE.N#
Z9!*-p_-&QeO5^Ob6&T_/?`<KCX;gBL%Pk!tMeJ=OCnL6$l$tObrec<KGn@e0<?#%%Fc6%T_
3l8(o\,H0WKBhf"WY(!>ifoT.hpW/nlNirUT"3/OUDA0b:WE7-FWFCJ9>T9rqM\*8g!8\%+X
q,)3N1nkb?g'f^=%s.(?>0LG`Ogc<[G#%'3+3<*r!o_eT#g#g&PLT2/?]A@]A0:GX-SdTZtkPt
"p>+2h7MJiPt.GHh&b:nL'@Hf5<uQ<IlO#*=*,*T*85iiZeS_sPM?(RpBSB1\]AZU;B^!*q(9
%dpnV'g,-[to@q5V*!cYcq`1_8DYb9@E3%D%XE#0&<a6i4[ReJNb"32cMZ&)IO@,sQ7?]AAo4
a/e<UnR'r/SFM>J3+DrX`UcXLh'ck]AOV"Ural%$%PeYNUEr",S]AQW7PF18:"RE,>e&_dn'HD
_PI,JJEXj,qCosG?cr/,*#j%\f7d+k67f:Q_J:hihi*Fdp?6O>rXj5mbp4`J`f0$8btl]A!j_
)C4U:Le*'VTD_T-Hk$Tk9Ym94=`?h=!&Y[-S^7.=pThsR)6nCB;Nr:%DM\(4-i#OGf`Yeupa
t*U>pUL>T^to20lq#Al4e8iiQ]AM>"1J5M56!8tT-Q^NQX[&9g5<Ys(@tJ4huC+thdX7W1hs]A
#G&S_@%63kpO0u@FmZ1A9!*/#KdZ0Bc56`I`\GuNCH;N[+;#=g3"V)ob#03rCpI,JB/*7oCh
[d4mrbp*SDZg)o:#WkEU]A(;9XoOLC_\6>U41_64#iHQEHi)Ffm^+#=:@WEC`oJbl2%)B3NIb
qhs"\!$=r8[Pg\Cm"/.0dJK+2FCs%[1Z17^*a%kKe_%/W-UGCApTO_n5/!^?D**F3[CET]Ahj
i6Sk!&7E2p`80#IJ,[%VMt@hM0@F4'^IWj:rZ_;0qMXt^kqCIlUq]AHMOo?R'7J_r7([3U$?_
kG-s)4(Se(_<haBo3.iuGPG777X?^J>aFrU%iNU,aiT2Z5N.C[fs6s3Cr%"]AaGk8a$)H%m68
OJGbgTbi/9q#6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="451"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="120" width="375" height="451"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM04').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="PARA01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="PARA01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM04_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[571500,1143000,38100,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[426346,3981691,426346,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_jygl_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="1" value="0" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="PARA01" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="JavaScript脚本3">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="val"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[

 _g().getWidgetByName("tnm").setValue(val);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tnm=$$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80">
<foreground>
<FineColor color="-759737" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_jygl_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i/uP3F%.WMX8G1FoAnSCAl9asC]A'j1^d9R59RT[^^ms5VAHJ,2iU"m;A>[UUZR8@u6SAVP
mI/N%6@YDLdS"MLpG;9Ai7=HgX&[rd"OU0B[<"o6ij<p\d7i+4'h437".*&/WT0Fh\*25ani
G%fHD0XTgG.^KI>C6>l2U!.i?D>n7r8E@#CjF1MY"]ALMc0Iq;'A^[nS29bK'rWkZ8sUZKf")
o=jn[G#M8a5T!nLpXJ;hnID.*p?:6anD:aDJ#1XB=CZ\`36ClHQD)(%*lY7R0SB+,J&eO9lD
XEY]AHGJ4%;e<Z]A5)W76(aQVu"1FVFO?iPtScrW`al?FWKY;GL,Zc#WS:K9-K;=(Ng>pp-;Y2
,F6^1Mn+?++7$BFmTbdCGeuq9RNKA^(?2d',<B3==kaED\1Y\U4&M+442(jo)1o>DIKA"X[r
8/s&'Io'*seI.oMOKRBkTQS&@tn"gi'Y6r:NPE0(l?%<)<Ge:`Z[?0t?Yp'+b%GU-MK/D;HV
in7k?mKBnhuUJ?im?qZ;^m@=nH<OoVBlI569U780UH0XuHl/YG`H]AfhH#gH3:=/c9"4&qR=C
u&T6#j7Sch,R7Q_kS!UJZ(&Y)`r<$Q=V!,3LMCcIm27%rF@<Y:R$$!bFs=TDC5PUi[-SMc_&
Bqk#m+]AF'1Ze3`o/<_Yal!RE.(BRI$]A'=aEOT_JFO+:>1"T;[uhel_j/b)b?.+6hN>bgfo,9
&_1hZVGBJk4F9h^rd_kZ7cEjl(gQ=1<W;F*Msr"4iE&o[)87n6=L[o,/7A8bB1JtX\O]AaXYK
V='CTm[uK[s.K!A%Pc^B%YE.;^7]Aa^3DaEdTTShDI;jB+\H)1X&=2)-[/Pm\XT?NgJ6CRt,S
B$j=$KPP:=kVh<-X\;U31XE`,V2Dae>bmekpS"J_\q6Xq#hRutO"W)Ho76Npu77fe+QaW!8P
+@ir`Xj!$ri"c35qOmR@`fo7)"D-S'US0'W9s"4ImGc>iVE_^8]AeNqLr2,\,Ni62lhGYT,TD
rJ/(dCle@9aqMa#k>$]AtH;8;(n=^m.J$;[ql+fV!ofXC_r4DXqLQM&/(&e"I@R]A=XFLCbBmt
8Di4F[9TM&-,MIA@c"9<DUIQo7$LQb.4(_QYs=&raNu4^80:(JGl]ANg4!!O4TpC&oa+KdMXO
1P2nA`_NK:H@rMH;qHSgbV95GtVcD`s2[n'[Wa>FC+VSie$V;oqhp2sWtj:Zb'*<P`?C/d>1
6I?=R=GhU8<JHZWJbYfF[o0J9PeN(Nrrk>`C71ZN$1gA&seDOX]AF`<-U-0a`LU"WgbG+=^g?
EtSC$H'bM4l9SK]AlP3b3RdN^+CSc;KlrP"-)QWI\2r$CpXMrL84?=.)hfpdSk@A?O;=;[rGe
.T"ccP%AMC_4&qT"@Tm4D+B,kk_ZL[g9,u5u5b,7"pSJLA/D1DM$]A;,9N]A2?VPVMG*lS^VU!
.UgT9qZ0LZ7g^7r@S+YdU9VI;enRQGq!TQd]A@DcP;PAJH6sjE]A@6556h)Oqurg&i.*mkR'>s
jgcfZ3)5CJQfH;Nr5[O.'IIOPJIs2S`'`r?FAN,'.i/*JDt$nL!uA,T^)qWH.-4LJM+YTG(7
rP:F4$Ck<n3QF+$I":oqGC8Qa_&U;PsMq8HF1b`?V',p>Kj,Dhu0D\9&1%nX%A`%?Z"[k@rb
O0q_SXMK4Ol5/':Jr";JT\gi[b`Y[nmZlNc!nts/>dr%b^raA.u.n&?l9;i<+r<mh3Kg7Jf:
fV95+"3pnH0*0r-Vm^m:T<hS;DVG546XeQ\pcQPpN<HX$'d[Zi]A;2Jb+Mm-!5R30TPfIlPB9
"cLMmA;7W6Ib7.&T.7"4?,4`@AHn:Q)bC]AkQO'\lW#$l)^4!;<K?9Et-@,1MZ5tOl"SER#D/
b8k-Tad[TAFt<-\Pt2nK<._rT?6,P@#ZAo/n>2H5La.XE7OuZqSrKs0%B)W$L""dFsT<btVn
2ch8poQ\!pH4Hq#j9n=_$J6/\H)rM?mPfbFCQ53\F(e7>6LG]AmV4_(4YS.uqsB6$n+ff0+lh
H[-J2fYn#'&-6b.l/<P\FY#b4O9SGI-fIp3+e-IjG]AoUr#lB/a3-kiR+K&QL-*iUXm.J*:-M
7'"?uMmb!ZbpX2tpOfY'[Q"GoDY.DR-_+.$O"k/1CTHMU%%D)$;no(ss;`Q2sWP($5?!_#:2
jU^$9E6"8oQ&dZKn!DXGZI:p%m8>(j\l[Hh3]AS&i1eqMcUJeHS=]A/-n9e5jt>b$V=I,r_Mm;
?k1^9/kjqApBsEEZnYQg1O>W`:^@[J)1"*``^KNmEqhCH4t##c=D'5ul5..Mt.T?<c6h]A_E4
&?q`f5GH4.DUND6UhpVItO,j#QX0Ku=eZ<3V`PY:%Qs[8.:Ya>%Vk'g%R>0cm(^6+8bi"GT<
lTCN&mn9+h9=N=<$'U(."lp0i'.K"d:Mh#DA3rhQdG[\lf=O2H+pe05FXRS_,/&%*3,#[omj
lIpT`9XM;mGog[KahJU2NlqCTkI5]AIK-&OLuEheom^78h]AC1G4.La8BI3h)j`(b2Ku!M]A%4$
$(b^G<T9L[+d_I4+)9<L`jR\OXa@:BKt;B`YU7:AE"5X*BEN2Oo>RU:n72FI]A;``MYb3%Z--
)oZ#9&W@8t.I.o[3'no#I_&.qCNT>s&r_iElODKkHA/a3p_]A8p^Hr1g;l1L<4<U_[ZrBh6W!
Y^?NuubNeOr^<rsJ5gFeU0'3!#2(,]Ar8+PYg5bB,B<C0$hTlUBBqUBj&GCn2Y!l6AI>--2n2
&mtaR3QQ*bWP$5DQ$`<bjJh<qQ=Pa6dNg=[f5=/Mfr;:mK7Xk#I*F(R8eC,="m(8?kZf8Bc4
^i2E&Ng3L4Tqr;&[JilC/OYH;'2qjh"J08d52E*[g77>5F%)Jq=318VZJ3;iAVjo+P1'!?Th
P7I7`"r9C>%OP,Bj(.+)``ph]Aj"jZq'o[)g1oj1(C9L/VoBrrKPQkXnD,m2Hp0biT0R[Bd#!
P0_in[*BYO8_eQ?%+?7BmVKcB>s<9'JFSV*L62dp3#@#.[`\Xh6SP2lqK9>Wt+]A>Ca_$Eg.#
-]A(R>%GpjuW>BE:if2(m]A-guSag/W7Qo'"Q!.US,Y;,Bg-$/RugeHJ/r:^#.@D\Fn[peuS0K
3C:E&q,lR;IM'!`kScSRB.%t)6)C=)B7ILf2cJ9F8igg!O?NL'A/"5@+n"/$9Rg0Q74%UQ"r
p]A)%?$c$B<I.Gn48qap<"p__\LhH@Uf1V=GP'\jdB^r'H-5k2&;o(^@QG=?m-$dA%-+k<7nT
g]AP!,NCJX\)^a;TA.1e1IV)aWSE00!fXgTVetOZGHKlqn,*EVm?9R!8`"d)3Y:<]AirPk6[BC
7]AVQhd)Jq9$9=Rgl?CjGTUT0Xj#>=@Sp<@gCl#e4E)9N$*thL(lhWBWH>g^g2a78+QJ=hpp$
<s&6_kcM3!*",#^NGY;T"`eXTjs-81?*e7Q]AG#cS7G'pT_mEU4b,Tm*o83d4hOFRHZ+9;d>5
QVR[J-7,?s+aKDf!A9sQu89tH>.9f'a+ZfYYcLqRLH1!QeYN3oY#EK*sr&\J,f]Ap!!*?j!!3
^^!!FDE!=1pjrh$ukZ_kg2hhhdnQ^-m?O@."4riuJ+~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="99"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="99"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM04').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM04"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM04"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[jyhx_jygl_zbyjdt]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="指标业绩地图"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[     var date=ObjGet("get","date"); 
	FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/经营画像_弹窗.frm&date="+date+"&pany="+pany+"&level="+level, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 90.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 90.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<EtWP3KS:1JLeB2.i0;FNQ=Q4H4Ud&*\"VS7WHco8&0be3"A*+<iSiag4ZNBe&C)QBhc7'G
+-08g$Kt1s_m"U+$B-M8[l_CA,.2+5^8Zg>CWam,]A=j`VX!1rP$VV4SNpQfCbr2L7I&uNW(j
:!+>]A'T8oHG!&ao1I[e)Qhl1M\GQe*Flf>$Ts)p#d,Sa]A;"#fk$:oNZ]A;Y<[`js'iZP%L9b<
nfJ%01>(3/M4)9;gkdl1KpYi-VCJdfR=KLQ2]An8q;V]A2?JW]AMmdKB'fR;\"rt4HR%sGYd1?L
Mu/]AjC[Rn\5SAori[.T>Jh-Ph\TiPJ+>8Ih]A3QGg\RlW^ain(OdOq:2ZCZd0Pu!<BkQC@-Z;
__S=`cJunao`1>.-J<E]A46>-G(Y%rTlK2#[E7s@Q)iT1<m)IVYl8th1i::qp!8u2]Ae81-t9S
MqSc1h>?:u).BW57Hm$`GB$Kk62n:TaVpT#LFf_Y_oS]A1KuJ)DH:@8YPe/s6_"$41MjU2jT4
Kfil'?XI[.#CX2;+I=gkk!+=@ddII5FrZJdCU)3l0BBD.T?+WSbGV*U[X2\;4G\KUgJkIM[e
m.V^QEdSeNh0qZm,tUFe$HI1`1`uceLG^Q;3CkonVhhd:tRB6aHu11P\um_\nCL?WUd6NbH(
In'g/,;i_i;iJI^Zr'@>"%eB&6Wh?el&qoN'5U;M0%7:VAc`:WrY-1%O"PLCHekB3028[Me2
$:>D(-J=o"RWapSnBdLVYp,u,;!t>$Za)&tofX=GS?)"$bDkl!XMmQNDtH^Z#TN=mF?nDP``
`%O!nBr*OKDi]A;[%L[:Gb\Oc:hO*Q1\6?/Im-)V<5@Y=YB1oVh[E.XU^:n2O)_Mfb5N7'AM1
Z%#b_FU'3j.%*A8\4N/7B=[CbN?PCY]A;c:unc,FA"2GDYVoLmm(>9tJarNOTSB`h0)(G90!M
h-]AK(8+D^$#p$ho4^8i/3MN<G$'2I[W/J)>2TE4oAisf@Rp%,?d<A/=8LUg&t(HV[!km=:rC
.UQgRL0n>2La,^VLFDcgo_E:IBL(-T<_nj(^T_mb/Q]AKB[U]A?,`;GE_-:KX^+^ifP+mCG8]A`
lJ5RrN)cR\1M5^eh-=`!U@Zg>jifE*Qe>">31++MM;Zj1GY[n]A-PsQ370^fW#F%s*7TrQ0IL
g4*:;#AND\k9?5jZ-=_4]AL90lfosVa1H0O(c7fkb>XFM(lr0gk)MQ9*/7pdFNqh@tIiq=NCA
=loRBsIP.*Nqo4mYE@1brEJee4;DM;6FS7%;-*'O4pNYH[kO(Sl=4W<pp0Anl@0.=cl[@.L3
[_tI\DOVMT32Ok#tI>7JJ(JQlK\lJ(3P<IeD!?RPg!QoTar-)[d-8<8&'OPA3!c\MfNLqg'!
Gn">fP*o:O$t!'(1D.T^MR643TofXAh%3A$^6,kfWOAB'<Gb(WSW$"ipHLkOMuL[,uiA_5k)
TEaD`9c=p7@T?EER"s0&M/@X?79%Rdi0%#:lD"&37kK]A0JL8Y^<8H%sK6/6\[Hk+#$o;0d3d
<P*U!9,k?cY>q/Ku"bZ^brgRXM@9>!Y;1Wr0+RI&I's+,he1:!(E"9D.N(R*Zf\k^LU%G8@g
PB_&Vnb/unTfs=.^]AA6>+Ton=!5'p%`N[9>24#:+$/dJ'dR\]A`_-Q8Q1d$-)Q"JZ2l01*(gk
'I`\Z)nJ/&X3Dal1D3WQc\8&a^:3Q;_sgZIG:@8Ar<"t)ObtVpq;!7_C,2r%X?O*E4465.^h
P#XcCWtQ^lg@X-7LS_3ES\APXp?::$VkdBek6)n3Od>[$&!JsIa2=?hbQ(HT6(!lYZGY/Jo9
Zo^_<T)?4;Ek"$E1Wh8=<!80oN3m,nH[6[/JgY=q?]A$WC^O%qrD[RpBWf<m&$a[.N:_DTK.n
X(iQN"9&5J/<iSP,J%(cG_u&.,sf9%ff(KM/^jf6Z@uLa=+p1/tijLS-q9q3p%NWV<J<BWc_
d"Xbi2:jJS!pd4Dm/<lABTSq%+$g_0db6&iO$#8dl"Eu;je)?VqUpG@>oZ?lKYl'PP@_HE%`
`0W*B;6?j-B/ilF;sItVYi4h(J,K%WCs<sD`1V./+#]A?n>Z%CP9+,o6`phS\lVn(fN%?.j]A-
gIA/H'qN>K(Xn1+[J(Um)j>aK`YoX#A:Hk.2]AFWU)Hn)+!'GbJ1.!I/8Bp`!2DY>T1*^qQ0b
[riZ".2-!W$d_\7PWp@(>7hWqRg*HK=rug>C%(B`KM+/2bbEXEY)7=Idp1E-,F3hA$G=/s?(
m5'I"U)FZd&D-*UMsh3ImE*dDrCqHi#^_RQJ`dh0"[&rKrEC%8`U+bh&g2@J&;J9=QmAB^"A
]A8\"[+@_$V(N=_J5[jJ`tA<R2p.p)5(nV?gp-f%K<I+h>Ug;$s75@jM"l`t4g@AW,&U9Wg@N
H2\eXEnlhRLmQ)dTOaJfY1(_+.dpIY7b1>nFn8rdj.o)"'[oP`Uk"'[Eu?*QC5+]A'#mr3!jR
=(IQiAQ_3<8qI?2ma^`:U[HH?PdamZb\m]Anl5k2N_W+=F8M&?h/\ChKh9\1IqK95dJGVq'<o
Md@DS_So9EYDGaD8b4f+obS/Hf@:a?C=UU\5`KR<F`WK82"2"pAD?)57hl3F9;RJtOc0Uo8.
8e^NIt1XMTqkDe9^?j:*,%uP?0uO"Ci0Q2CE>H]A@/G8As++_fq.F:M7)T8e1bu.2orR\_Z!Q
>q%'(tV_VUo_GNc`VYT>(:Sf?lY]A&;$h;^PnqTOsbO-H'!3Xt<A.C_>nRt1)[8ara0OE*nF6
q@,[k<=S@jd\\3(WmE/("+!NURE$ROA5\6MAg:1Jkl5B4-uf/W[:NMj&Qr8kd@NmOKp(/oFJ
iE_Y>e#57k-#^8r0@q]AI)7&u@9uCQ!+RQfI'e%0H<A]AaCibMiqG[10Z(8Xi=.+V*@a^AU=#S
l@/^jJsHOsmQD#mWOfaN<!$iCG3Q5E`.StCE2M]A?T>D\j#1MQ$MJ^6Xd2$I8N5j4U]Aku&VS#
EU0)V`%s@F?Mm?RQs!*p*.V\H8U&32D++(D/Lqr/barGU8;*bK\.0.h$;JK[Z)_X4iB2qc0U
Jff*Sfj6FcrHEqNgh%D(2SMpq#fkkq.4'7`p+0$#_Jh>9Rn_8u''&RQ&8m!1f<j^S8P!$foH
9;<RE#MVbKhV2=ftf2f\^P'%WYoqthep*jbAQ!DSG5(8#9>ie,Th)5>nAd2^+O_=U8Eg`qH>
`L/(VX\Z;JKVWjs53o;dW;Cr3fDb]A;qCkuLNm,e*[s@eSlUAU>>EJQe7T"rn,7@M@Z%:^C]A,
A-b`[CXKAL0h?:N)hO.R=b.ng<Ql_`KFrp";il?QeL=h7@i**`]ANRj^Tj/2i/IorKBro@0#S
Me`q?d3&]A?I6'i_^KaK6gc2n=m:E7j(Jp"WJSP5F;[b"E@Dtc1Gh+Bmp"ia(CW'+VekNq.NH
k$]AD0Q,YH12d94L]A)2f@JL*@EN7X?7`6mcNl&Y[5!2TZtp-PCNlgUcJaH8^mR.X#F2?gJGV\
,jm]A>rD`oCe:P/KgN%Bqdbf2ofJkIpCAOZmB;UbZ0pI9OLghj1a%kB?0Uh,H5D;4Kn!NRjdO
j!&'^_8i9X1q)ZF,5r(.F?:$m9?LI6l9(Sh^rX\7Y,8n_k;9&=,^42cVH-Z<2($`,mWnt3WK
2J)Snm$^grS2oV`(FR]A,M5)t<QANfYU(\=elG.k4&4>Z.0<TbYc-I6&j4p<No!h39e[GWJo/
,F3S3Xkj[_.5$G)h4JQm"XU?/B;H*,PE[[m`0Pa+\0qJeVo_]A,$YMc5#gUab;0J-jr=e&ZI/
Mc0Iu,>oYc2LGY"&_tBpuY0j`39tI[@/SI[IkPHM711tWK`n&dM0,"9KD;k,d)P/Sa1]A1HPd
;orMk;L;F<aJqAR=mP(#X79'./tesfeaFSX]Ac2*:]A7%=AZ^P'+2JLZB9*`YeGE>AO<@tLUGP
q$A$*-OK<IpS2n(JPobH]AAB%Nl')WF\(_I5j)!&K!^NG@LjT6tOhEZ+2CabMj9h\V;`HWW1#
hl5I4<f$2D*>[V9cIVGRj5lU4bAIF&+8Njnq&9EEoU/Gbf1'BdZsa[>L]ACj>i2:RsXA>*!-l
AO#A+&unnV(u=IdFHpd!lClcYc<'6/\Rp6C4%bDM"mppuRNLNm"^Od)cdKLO>9"q,b_/a?_2
pB?6[!,TVVLYo,4M2-dh+_EHijQRhMX@gR"ZrPK"]AMNArL>rDCVq_M00%Sf"jNurl`5I8"+%
cW);(q8!mY!PkQ#N)hJD6(2#A!Ni\^IaKjK?+0h3r%NRX[qZonZTB9/dn<D`aFRkn.ldtnV?
@k(!fZ@S/':-Gm!>b$21\Qb4#0F"#m'KmZg8VYGt9FC,;e<F<r2@m:T2`?CO<pTpKIaW(jt:
D=t;T\`E3%R_M5jAUt\>MEPFeG?0^'dR,BWUh!0p(=qkC[l'/Tetgj[P7Y6jZQ%RlbF?r6Lb
<=pllh]AmYJ"as<St]Ag>,W@b!A9o;XNq4;!MLO^XWsi+aY-qVo/4KAqY+^OI:m-aM*)1BRul6
:af7QNf1;@A-?L/4J2"Vgg-Ou.(04YO'UnLq"fFRadToirYa<c')nI(P)Y#CgFdP\/3QX/;I
*9pg4AQjK&6e?.-.k<5,^dS#=D0l5`c:?C`KWDeo>uR-aP=T+G;n/<C0jEYB@eud]A0,f/)1>
uH18h\E>"C^3"$b`^qLSi>KJ3HA!Vu/K4?-XKAHUJO("jt7P_D[jiJ9j(cb\>'V1gdH>]AR$Z
L3j<!ZF2`7+Q?(Tal8F"4^6<V&B+VomD-KK"3LXFpcT3I*BH]AkOeBlmXHCtYGI?&.M?CL85&
j;"F-_7+dB'sJ9)5'W&jLCfr`_TYM,1&X2p%E:rEN2eIU[tO*m.dFY#_AD4+RLJs#`%APE+C
A+,*db+a0l"!HstI%:do63F&X0#'CM,AHTXbN>1q%AH9#Z,G&G'-b@?IVn+-u\T3R:@>AJUo
fLe%,hT`A-B>%Q+k!Kq$La&K=qLr+Q,uD-5&4=0^eFq*EKG/Qe-gcd`c>RV#G#=g!"2J+gSB
R9BM%Q?muu!Jp,Nl7ihP>0pKl%S\GFF[<sFh[f4O3Jmm5@qJCt:/7`_%%[[.#a^N^T6I*]A-K
)*/5\$ip33Od99NPhYh&&*>e.M)3GHU>3r=@qR3+%fRR-I^_4%+.0#Tr^8*2e;!k7qf?_0S&
$Il^L(iJZgEgR/UYe*ioF3,>ZR2'*o&>-=_uk>%rLts!_)3^N`4K:%<nY"*ln6OWe`t"Lq'A
=1,bb2)3=.KA@o8dS;ZfBl:k^;/<QpuV;iE)7[KtS*X.OGnVIA$8'/:4+qsY`Vk/i<]AaASsU
*n\*e8"]AZTsO.)E1?l@CsebBX<+tB8n=5k!!]Ar$TIKKI,nh)&?2Vmr&S?-?TO=VjBRK98"1W
pSlt):?-Zal1ch7c'5EMWUPi0`':G6NX;AOs6ZW>U-P$QdZp8sJKi2f*'cIp#T[GSE">-=eV
Ft+$)/A>%+ZT?g8p.Y*)V`rsF9-4gBTYrIDSr).K*A*4[ND0-7nL_L5<ikF9kMGe3,G(LN/k
0['Pt6X9)JUBUrlZ]AR*Xtfch$^1mI2gS*L_!E4YtjabX!N!^W`@kFA$=JXnM5j@o:`G4_)F6
1*!<"SKZiP'i:rD9%I'oO28.ME4O)XoBF_q$B$,dobJVo=88RnjpgEI2K?gh@V]Au(EDL#>1@
QGP`JED^^65&2\`M#a5T3@q*.2]A[aeg7c;Rb=4cVb.ERK:,C_*Lr=WEBTs!reG3?i)t<Oj!P
@F"k9)%30'4hSKKJBJfc5pm(nJf@7'6R+^(RZoDQ$trUeXTI<DdWCq@H2mO"#BnBaE`.PKI_
D0,X^aufrd*M?@_>oe0p-J#boff=6@RC:PP@aW5+K]A!-YDLOCFoFAhk/$rC#Rh@,Zed`m30>
nR.^oqW>A_OSn4$I4t-!B+Of'oV,;_oC4NT6M'C)dju02X?Zna,'pm0>6/,K$VQo0QkYg&)d
E$e'8n$k'G'5VkqFZ`p8+<WLO$<\cjEl/-aE"mfG6TLm!CQG8!!-Q?qp+3DRo3eZPPoA,1em
7WkfS^To\*fn(-FpOTjp1(SZaTpsfFmJC?DWu974*%qrbk[YL/%m<MIV.+B^`_b$l]AbG(P!5
@Ha*ur&I'1SSde#C]AbtIV*&osAGbXN5_5lKP*b.I2'ZF\!ijDm%B9495OrRO2phfU6&1cs4C
a;^*;]AI^>33"6Eo\`Z6"bOVf0\mp$<F=f=tX(?9I;8D%b`AG7@(QMiYA&^:Mn[kc4SWUKmeP
Di_iC+d3MK4j9r3Jb`r8)ka1:%tLX2g\/p+;7Tl<h9"G<SM^.;`2Cr2jEtmIMh3qe6Si?qr*
;X5qaX+5k'*0Bt`6[kR#t1F13>)^MA'XWp,a$\shorkYZ.mo;o5*(k^FECce\%H!/2#aoBaX
+qeHNAY:)*UD,O9>W&RZ20f>Wu=S<iru.f8I?ia#DFD751=^WDAUJe8]AEt!.tn0\^XHcZX:?
oUj8c;[;[g(j*MWKNofZhrBBMpE&W1f*N`_Q-^lHjO[OHgr2MMG,WFJ:+&5PDM\&u+g4C#^8
`gOILIdS7oVZdMAe:bY!#GMM0PE+B]Ag)o>:"?`:XLf4\s-s*H1I+PR@#4Bm-hD'B6R.1A6Kr
!>pmOQWP<?4Jc1OhIQC,.e[^eE\]AV+G\<e"fjLD8M3rcSn@-mOuGOK-MWcVrYu=Ltch1#n/V
28irB!WX>4p-)\4<M]ASl%960;_Esj83+7&>O=7:c$6o2"phY`mQl>P8l4ri'U6uSHb+cr*&)
YfFOQ_op6UWd?2)E$KfC98_a^g2tB4'7nt*0]A:QmQjQ&N5lK*FG><K\Ouc!fJY+[e?<h/Ci@
L0?9+q5fc*s>fF6f&4pVV3W5tTmes.d.$A]A;[:eYSuC"X$+dI3TC^6;<PHn9j5=J9K6C@QXa
s7)&45BfHuHQ6m8[l'PW>CHQJWZ*P-RBk4u['B&,D0e)d1e;Ij;g!?JWr)fEpE[PeCt$@'qm
12)+DP3MZb>g)+<eM(I7J)H6]A@o7C")nAM7@<Z#lY_\Sf&?cC/lf_*iR>S&J6<F"l8=u/(Bd
%&M6[FodYM$(*`")KJhuM<*)+<<8IWsDlgh;1AX0n"?0p4=(`X2\*9kh[-7L$rZ6j$7N8H/l
?d<O:bKoV9"kI0S8ntN#n+S^l0XhMZgcT^%Nu;,hMjHJ:8i<DLZ,#KkLFsgDdTilarg#e0FO
(f3.Oe$OWh3M7H_om?h%&W4t8p3(h3>JY#.dJ(5EIA3KX"*)AP35a3:i@FsNgm_]AEfj<cLGk
'>8S^0$e8sB6[9'$Z`p-\nJU)13c;8Pjf_5gm`W[-]A@@4?IsfGgsi%d'.hJ\Cp50g3)pc)Ta
Po0DUe6Tm9I_R*rIS/c*A>e>Ua,@&pWl3]AR0>Og;uKqd'29NOA?Wo4f@'A::)@bA9NtGP[!b
H?`FtU060A^%BUuFdNa_@GMG_jGp:[hiN0"/.I1nGW;GhaO;u;AZ_\-]A2XlY;lZ&PC"W*HDk
!tIaCZeT=LJ*V3qQhhcfW#`BP['ZC!2VR'FnKJY"?3h`^CG(,kp?/:\]A$Y/'@J<#+rM_VRIa
B0b51b%RYO9h'Z'@s"Xu[W^Y<sDR;tXdUj&9m?X6YlVAen-A'IXSeCD\>V`Ni>8X6-tYiuaT
%5^hkaOH\P2tN+Ta5>6GgO-M+)8jINK8k)9o4/U>Q=KZ7.s$rCNL]A,FKeT9-:Z&F0qdThH;n
'g?PhsDImJ-tFaJP?p_c3b\$oI[Tdp<=%_&6N\`-P3KqR";H:_KtnL0&[BY5om'8?JaU\*I<
LOTPhhUio9P0/GMDk7iOmCAMn\B\KNYAQ4.o:Lb1XbBnDfmAJuVks<bW(1[O\7Gi_H:32c=n
+ReSioOlS>6J#GVGkEsip<n@B3o0_m,'4:$/!RPI^*%L)VY4j9p7B>XV$0pfQ:9XZ6?QEU[=
sNgfMcJkA$Rp8Z+f'ic:g@%CM>q-J\/4i@c`(,?PZ/o7hq=4unS)e6sa_iTS/,52EXk<l9/u
+bDW9LW[;Ok/,i?\L4`)#[,MY#\p7t`RFX_eDA@]ATpL*`0ea;r($]A6>bg!a-Mgi`[,:qgD(\
0u*R0[6`d;.t(X;s@G-_1%_5BfFmNaGR&Xsh!4;l`oX>?YX^[ghRDU0XkA!MW_"F5#FD)1aL
d\^chKbtg:USo"A))V5Ngq,Sc5RMT%A19@JBRL=(Yjg=6oA#cV,'qZ"Ta^?NF]AWmNL_W\B65
@eFCfAk%-fltA82VM*"P+icb\Vh4"ZZ^scA-bSH-%YufPA^DVF5d(RW0!rj.AnUgF;a5?M`.
Sde']A1EL[J=j"Q)PjF;SZmN#quc>*YU9n445VhYeNG1WE^hTZ7K'DQudC&:udM919_=K'pYI
&aE]AQ9X>&NISC<=S!86uB+auI^21JIVL`t.P52(E1nTB1ouA"U([?K"f8mLDi=b-`pZWN:-d
]AP^mgGQK0EXqfHi=\?^E`M:B4,pZlN&acG-oOpG(+&Il2[5o"_On#OJpnfoA3bC=SqOhc(Tp
pC$;-^L]Atpp;Pp:-md!N_.,rm#SREFqfPW-*%-&-8,S0"0'oihH1F379'ao0u#_G]Al4]Ac(]AL
*=&k^(BR2,^RK\qa[mZ`MjTdqVTHiCKU]A=#=$oC^iq`gOobXOmoXW2J7!MKZkn8MO0%FQ!1a
!ILOal&Z^*'@nC-7ei:Z%$i:Z%$i:Z%$i:Z%$i:Z%$i:Z%$i:Z%$i;\MIDcc2-jGu7'Y-?83
Du@9apZmf7cY4Ug^>/J%a-WCTqrq\Xi&\@XY(Q&u3k,[&KBqGV\.5Zolejh6%^(HMi&\@Xrm
-1'1dO`_1R8""[WZfY391qPaSDn:%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM04"/>
<Widget widgetName="PARA01"/>
<Widget widgetName="report0"/>
<Widget widgetName="D22"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab20"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="27a8ef6d-ba93-4b1b-ae41-ae099f5363e0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[40943,723900,723900,224392,492980,67317,723900,114300,812800,206733,718056,571500,571500,224392,524786,3570927,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,190500,2544417,571500,190500,38100,38100,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Custom isCustom="true"/>
<FC>
<![CDATA[ceiling(seq()%3)]]></FC>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1" multiNumber="3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="AREA_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="0" r="2" rs="13" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="DW"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" cs="4" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="1" bottomLine="0" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=‘’]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" rs="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="1" rightLine="0">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="4" s="5">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__F422958C8428F2C3C3294BF00C2857CB">
<IM>
<![CDATA[!D<L$rJ=?G7h#eD$31&+%7s)Y;?-[s,QIfE,R=SS!!(sXR$[jR!YS'm5u`*!m9&1]A_CuO5KT
<hRN6i$f%E\nB#42]APKPnSPS&A2tSWoI3&4>=lTtSd^`\E%79d68<9,;gBSNhVucX:]Aps*4o
J?AXa)FXia(BsF+;\LKD72Y?mA2nfYGZ@"OK)4^()8A9PS?-_=RA8)Rd!@&DiO[,@ch44`SX
S@YZ*6aQ+eJPIoHhKRM^WriV6BAo"D,O.N3kGGl^?+<;+UGRg//XV=BdHd\*8"gH(_k)\+L!
oC,#=TMi4]AJS@Y+<t"1;Q#dPFmK$8[%?8QY;aT?F-5+]AgpVSJW(N$\$0/$&Ql;LNA#Vif]AdH
gba%.:e[g*l(i6XEmPk<Jrk>@o)B36([?aM4GKP5'MJ+>(m%-+1B')57NCMEb<2NT2-emZ)-
qT>[aA\-q_0-?E<TWui`UZdLWH^fM?2f76Ro-lD?31n/1/M+9X[TOb_V)VKT<hLU5&*?EiEl
W8S3m#?[tsTG77L#,Cn&j1&?$i00l[U*%`2bfsl/O&s1^\[F\(G!`&W$3^QkkYh+?b$q>Z23
"6drPf77J6"AM10+.Pi@=rnYd-3%$,0(PLA(@#qQDp7E1b_(X&rYhD,C^+7Jjpu*>TMN-o3=
*hHV)*l'=A.:i(b3XT[lKJ<]ADcAQ6b3h4+h5#B%h"")`n#F#+2bo':cnl55VA'3fEY=P:udC
Thk+jz8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B4]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/指标说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" rs="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="0" leftLine="0" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="4" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="4" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="5" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="6" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="8" cs="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(AND(LEN(A3)>0,LEN($$$)=0),'--',FORMAT($$$,"#,##0.00") + "<font style='color:#8D96A8;font-size:9px;'>&nbsp;"+C3+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="8" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="9" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="10" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_zb" columnName="较同期增长"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(A3)=0,"","较上年&nbsp;<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$)='true','--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="10" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="11" cs="2" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='float: left;margin-right: 2px;'><span style='background-color: #ffeecd;border-radius: 5px;'><font style='color: #FEB524;'>&nbsp;个人&nbsp;</font></span><span><font style='color: #FEB524;'>98%</font></span></div><div style='float: right;margin-left: 2px;'><span style='background-color: #faded1;border-radius: 5px;'><font style='color: #E55C17;'>&nbsp;机构&nbsp;</font></span><span><font style='color: #E55C17;'>2%</font></span></div>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[INDEXOFARRAY(SPLIT(H2,"_"),4)=='lrkq']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent"/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="12" cs="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:10px;float:left;'>      <div style='background-image: linear-gradient(to right, #FDAB07, #FFCF70);  width:60%;  height:100%;float:left;border-top-left-radius: 20px;border-bottom-left-radius: 20px;clip-path: polygon(0 0, 100% 0, calc(100% - 4px) 100%, 0 100%);'></div>            <div style='height:100%;width:10%;margin:0 -4px 0 -2.5px;float:left;background: linear-gradient(-69deg, transparent 39.5%, #8D96A8 71.5%, #8D96A8 -4.5%, transparent 49.5%);'></div>   <div style='width:30%;height:100%;float:left;background-image: linear-gradient(to right, #E76929, #FF8445);border-top-right-radius: 20px;border-bottom-right-radius: 20px;clip-path: polygon(0 100%, 100% 100%, 100% 0, 4px 0);'></div>   </div>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[INDEXOFARRAY(SPLIT(H2,"_"),4)=='lrkq']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="12" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="13" cs="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="false" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(A3) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BorderHighlightAction">
<Border topLine="0" bottomLine="1" leftLine="1" rightLine="1">
<topColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</topColor>
<bottomColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</bottomColor>
<leftColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</leftColor>
<rightColor>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</rightColor>
</Border>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="14" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G1"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="14" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="15" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=COUNT(A3{&B2=1})]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="6477000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="3048000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOfCopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$>=3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="952500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="4">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="3" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="40"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[XB;qp"bWb3+U!\,-$.1nun!htJ`C/e/#,cHNTPfO%4W<%\!P.6c$JjbTM5_+hB.(tBW%F
V%DOVT'oNe)j%+<ekOMJeQ)`jt97pQ"\]Aro(Yo[:XdJ;gqN11\<e'?[1UkNGI+H&N2Uio&?)
q^OUlbl[S"\a_GMfY\&m\D1e%m0DXNRqF$7[^#G]A@+Wb;GAaQ&OCH!`E4hc3F9l:0tb-m>rM
&l>^L-MYSG^0F5A"1G.Fc#FDkg8<:aqqMjn%F0i^I;\7eFcY-YRBT[01W`/\9nZI>Md^n75X
[`18=`:MES)LRq^9tMu`_^c]AQ+<g'`>Pj"3k!=Ag6(,5e/53G.)Nrfi^PocP2kXa@k&mc"1l
=C-g)NKfRW]A3F4Cfo`K/lLK%R-`>m(W]A^=COGPpH%bi#2oB2;iEYllEa$=9ZAN%H+48\V1j(
KoSJN0;-j6/a5eZmn&8Jp]AgVjkJ1M0@qT/N%1.o)Jf'?IJ%^k;sf7o[A8K@edY>cPg?.p74m
*EUB?iJM9(We)FJghbV&C*\YLkijA0FaJa@u1gD-aAs)\3Q%YoUU+fg)CPRC`Dm:[1dgidjf
6[oNHi%,T2%:D1]A,M@!C7!p=Y)SSri3j3UORDPK%Z_2Cn*Y$98o&Y[WAnW>/r>+J7G4?/c<Y
=U-(e,4DD^==N[6]AS*l'k`/S,ZQ4O@U[O@E!8P-E+'?(@W%AQA,4h$D<^*_%#SQEd?P:St`6
GE+++#AWGc/;k.UM%*p@QZr/pl3@^sG`_cA/_*T)cb#("F/'5Q^,0BeVe[#eQt5CqVfFuSD[
1*0h*@P#P_PakEMXO@\"mW^_]AcP0X$f0'3=AUCKe<(l[@nRS,BeI.`%HE>.rYWCG>?`E!05%
j^-heT0QlEjfa5u9mtA6QHaHB%)S%t96_P&G4:5-[n460Q\f2I5c7F#^8F#up27o05-Tfkj[
(RK53:rR5/l^B%A7dcTB62%uI&]A6n-[,Nkl5$Mh40u_3:9kgZC1KuFY3HY2)#"b$(Rp=2PUG
:GghciMZn1V[jdLk%V@VJ9h:Q:+"#eD+Hhs;fVX4UQ+fVEmBqO3o(N9CiKM-IC)s&n,#0ZG"
7@&'V[lq\i!@mIqQ:O]A?B:UKJ?R418V+QZgC+n-]A[#t=$US\jK;]AASMQ_+,ES.CI&$'9^b]A4
LjH?-)?'(YZgl'YZrP[jSd`f)Kb`XHI.FgT+hPg\5Xoei]A#rAW8!)ST7$`.Q>pa)WF2iQ'Am
B#<ngVSb??kSDHp4GNm8IZHa,+r0_-]A/\1tiNXW7U_2SlV#L-!M6b97.QSlNU1M/kc[hQsJf
j'I0V\^]AdbB:P1O<c>53?;T&CB:.!AX#&&Zr0;-@Pe\IO`!ok\2>"Fn+qkgGpEpbLU&"pAU5
hJb`-Vu68o7<]AD5LSD<I5JVr4ccp(;L@U)4`UbWSoMSX2)7_/9<kN]AuaVK1oI[l_Mf(j>,N*
+RtMe&$eoX4U"Su<sc$X&#_[#<&5&`%=,``R@^i?Vn"VIR&W&;VZ4_\;T3qJmo,>P9Multk=
Mn[SFZc,=Tar+&CH(W9]A=C;CmE6YF7[g)PnK(/9>=_E<G80Y#%OY[05"H@/ZHJOn*u7:UUlM
Y:Et.frVU(_<dU]AuRWQ&N\a+!`H3`)#1iY;KE7<gc!S2ol7D#t"1\IaNllUTV`O/sfHIX.1a
i%ING0Q#UJ\pk>#W!ogIX"<;K/q=RM'*MI3aD9t6#&Je3_V3#ohWN2oiq'+?Th>"QrSgq4E.
SiA/i(bmma%scVHo*Abekd5:2m4/lZ2,,$TZ5k.sTG(K5[!c[dmcImfWK17ilTV5(ptC;;tU
.T`o?`LjQKGd^XI\Ta)[rD!%mms,-iEHe>298%'K09XJrgh`67(>J)C*ah"F02[A'"'G8JNC
U5>ppOj?71J9.V-bHREgeh!l/Y-PhICTVgUT#d&IHn&8IK`_(bX3[]A&u8d>iG,/Uf9dk9VaD
-1WS67_8C(?;ol0,>_Z=>3r)*'LGL##SZ$?&,*o?tW-36"ma:VYfX0QTXB'N)o5+72*/p-_P
HDrJdINkWg?!'XhFa;STq0EA;M$b\A:Z"P.[.149Y-'i;>P.<]AA8^*5<)!>Z^&^"'%T7<C)!
f<XR3Sb]A%<@*99Z;tl_/Xk_p-oAbluZb+&`Dbkiu$jShLCHUh\sMRs1/u+d]ABA;)f.7fq)k^
<.Ck@_N%+GF)t76:Q-k`Fgk+d<Eg:^4rd"=HBTQ8Jg0_q#>c/XkDZeqo<V3:Q9/jUY>$grW_
dSH:4Vo`>%TtO&Z`bogp$/9$9O?=f[i91V@H>"dR7eRTP-uf=M=ISLJrP\b!70;fSAOR6UA&
W660q>%U%PB&Su2?2K973EdQ-j0no%EDh?gAMgB"]AATO;(Bu`jm.pbtX4J>6db,9@CfOD9AB
I,U1=^GifBde:1FHANH=M?B;oG#7<;Vb4K+=;]Au'O;mUIU5L:[aXoj*n0a"^/U-,5=m,X\<M
;8Z8u"-_?P'<F(uS4?Z3rESk?DknJ>nGGl^Po/tk;VVEHEsqDZHY#5*\cJD+j\Ld@tsYhj#C
Suf]AhfTR:2\5+;/n=b`?MQW_7DIBu--/u%TC7i3U/OF]AF\qT+Yc+_nBHdL`hT.'m1mra,HWE
/4ak^54ShJU$+DPKljNP0AtHEa)LpY&G>b8p_^\7/Vg,;)p`q;g$kf76V/12ZDZL14CppT/1
\4b%Wpf`h0I!H'2N\K^K55jdlm5#D,;0,%s5#/TpU5$r:"H"[PIUg3+-1pA5,kG`Yk14:CNO
s9g1,h6.m#2j'>_EbSY#=?9*<[6((-M)h9hf':Bim;`,k"RJA7FNR\q3LP3K5WBS^.C(j#AK
_L7,,GJ(qlL%76iJ,+[rgo0pq9(WOqZJVR_[Tmkb_*rD3!^X`*\'\h_SXb0m%;AY:_qQVQXM
YLq]AQMoUm;&l,[ejj'Fo3OA*lOm@febXa2+J16/!>WpcAdTpo_@)"os)Mu;1CSi2SdSr-P%T
Fth(n5[(n:'Vragu]A^m'B_Em1O-s<c(B8r#X5!f/Grj43D^;-[UbaB,o:=bL!s67]A69.at+T
*$J"te+#P2b/&u;=g]AmC/U`)GTA"'flI4s;3U[pkSTA3:>S_KO`-NKir:dm81+8<6fJEPa%N
4_c0GYf^$MG^7a]A7GRmEHmfhQ^$8$4fP5"GUU*IpmQ[G?A"t_2rup11Z5-j;GAN#Y7if:1,+
'@R<&tGJ\R4LVbMb^<5Y"E'R\/V9WaJsB/euj:LMIbM(8?)Z/d[2F<T_mC=IU;AFWsjd$(FO
Rd]AT6T@lBIRdu%,MVQ,'O]AGu\fM[GtW'b^;UmBZs^8:K1V\2k!gL$:bA5dT9QD`kX,D'orb^
u3\V^=r>L@3".>amH&Xb2pmkA0\.bSo@]A'_[.$#4/1TRYp;(DFN+qg%6;S(`TQP:D=U"-se-
(qr:T"@lMSLA,(^ubO]A3jhreX/e>r<f.eQ@h7d>E2>fsR;_]At6QdlPgU8qUqc&fm<e=KRJ^*
7O/.ZX<FlJrr#D(u#T3!!VY]A;+.>LFC-GihiAPYhaOX+Q)/9_@k@?)(hA+W<%7=<SE14\!XZ
+dq(GWC:XZXu=EEMsea0X+B-Y-<9Zc[>Lcu6Y2mVkHcSDZO/7&`&^Pt4I4gd8*,nd]AU$_H0D
hms,0T4M.f1E3eES5;sE:knN8QtgA53Kn?P=!:UaVGCI6eXpS8h:2")nYbF/BA)#V-dJOulU
3.[NqX$J\.;V]Ap0FMa2gXq\$jk;iEiPMl?2#O&A9^ru)L#;ihrKI0Ep[>mESmLHZd#@'bGP:
#[gN$..m6q,`YU%`<1+Q),H]ACDXkkRJBW<.*;?!+Qhqm3R^(AYur2'5)T\&0M7pt1ROZ6:ab
]AYWq3,60>?jX0C%=.OP!R5$Za(]A&LR%<!QN:M]A%KfqSn)i2+QD76(X#SdH:H.+Vn@fCZ<i8.
%#@9<*fBJ[tSrB7iB4.msCniCHr-VFkre,?I9C8b3)STP;Rh6[&-lIOeJMFrsQFX%g'9e?iJ
/jV2I?%#G]A400uq'\!3)^OWtP>cIa>@&[.U"tnS=X>0Xk5A@+S>hX$UU1*X.E)3<KZ$_t^iS
Jj0C61iuFAi6M.t>fRiQ:E%>/$W'If\XFhB%-E-fS?R0k#,g[B92_,9$N7:m9)DrZI`YPSQ>
@YVurU:7GJN*-8q)I[FXG5\Dcna:l"=D3=;6&uCs*J)[5(6k,mH(d7.uZi_(K1M+4jXZcDE&
+e9OWhq8DSI4Z/!>*lqf(*sYY@<_3Ju`'7bqq*JRs1:_2ArEn<6VqgF0C^/+SqB5AdC9+7&A
U)bZPNXNd)8E4$f!Z>421g4oYk<Unn"2DZ%f\8H+!f#3,9sj0ag8LMX#X*?/LK+H<-rL\5NB
Stf#.>7)-F>`fJNC*ngn*ZOC%lg>>%H6*"lmF6!RdSfC&IRn0EV[e-$HT/brf?OBe.d9,7jO
J\[)n9iH=S.qYp%l/[^PoOs,V3t!3,QQ-HJ*+5g@L15H%HT4<"sFGA,;`5nH*7d-[@@?h#."
]AR\$Z[o3/]Aj_;i,oP(4eHRN.:05>&>)\mYPlE:SF<&\/(K%L[Y&!b\E%L(U6YlBdP@+9hso]A
X.1l9`T>Rb&K#do<2>$5W5!rcqA$k_0EnT9'kAaRr]ADm^dLl?TAb,ShNL)MoTA<r*7!$AX'(
dQ&t.8WR4<3;h7e!ec9.,R%ItkOI`dCq0'1n($,Yr^+?\;>h!B7jGO280AdsDmfC>S,i30[f
m1)9>#>=AE?3'o,f<+4KnEl(YQ<<lH>_YNaJ'a_mn`?@BJO-2E\rZ1+nIlSje`%1%:LF$8im
Aj+AI$h>/T@/+:ioB4Au_g_K)VYS"7YqjbgiIG#NOh?Np@??XHMEY7Oi9%nomY\[^>A(P(53
H4(r4I-Gt8160e;`aJ3Yk[HUEWg[u`G0jfd>N).4&c:\9[&i/)EJFq:s7C>mCT'GEma]AM[dQ
@5"*h`*UX'@Q=>W*f=;]AX-!%Eld8gau2@#I\`m0=,tApr,/P+YNE+FD'EAc?2095mq17NJX?
A\I(T1aNV"%VXP-%&oQI!QX+W?`D/,LX&e;#]AMfOoB-3u-/@;_?Ag0ZkE!?@gE]AAKj-)ihEc
o]AgE>HFfj)cM(04leiuu<p%f4>ZmE5G;t5Q#A\S;PIA&`\/[D&pF6eVN5k6Bngj!GlcQXZL3
Aes-J9k"1t<o:OnnF+meKo#B]A'(L1]Am18i6I(.\5@gd`Kr0B8XDAD>FlX3L2f9LSn"LCF$*e
uFB94BK2?X"4qkldSO'mW0-5NfTg^['m7:_<<*C1:TE3A?H\B3mN'/gS*>oV`g\g0r[BFRXA
$5M$[7O"(p'@M\d!L%%dfYfGocobufhc3gX-gloPh@`,3P70jh&8fQSM^;@i@#"Ehdj"W*4Z
-iArDd?K\qgJ2*<B7)]A'^DdG%Qf!EU4kdS]Ah".f\E['$o?eQmA<qI?gq_TNF`abBN"DIAgD.
5XbgLrij\Ae=U+VXP'd7(WW*CWgK]Aa$C*1M/]AI]Adja]AO_UE#)2-^m.EA#7]A>qXm]A8PfpV^\:
LXTKG09XJ5/pc_V^m[p#)>1?G?i38L,BOZd8@?1MNBghFM\hn"U.EmF4eI$suB'K,4&i.X#3
%QO8']AG+cdi1DdbHbkG5Mf>c4X&8Il-Z?bjL]Ap6]AO&:"tQ<J(+9V&;Mm5*EaBY9J!!7*F@ij
EqS<m*qG[;78fO-&-@Zl+P&>RU'(NSs&C,P'2%Eq]A?5,`RUat%X*I,E<<LmQB:VUluXM_5M"
FV]AI$803sqtH[<]As%D@/=ro(dYDRT4#k4Q=lLHntr/]AK[5L1tmFmM,g`G%'X$GXrA9!IL2pG
0*`Y4QK4W>(-$d0&@e_4Oim%Jjg^qFjf9i8Hm(D*mto.KT$F<3kfsh9@3bK\Z$#g[NtK2\f=
5U3+9WJ9#bj_f]AChrer[R9t\3TMXSaAi5`(uUBW2-Na*Ci<V32#Yng]AlS[n5YtTOjBakGfV`
=Y^KHccBs9c4XjU6G*)KuY.8HI"AUZ]AVE'Ys6,&a"a"20pnQO=d_4-`;T:/n99i!p0rnu&A\
*2XLTf`(6&-hThO<AG-j_,Cc65<=PqTJh!X4^Q,3gU0R(\^X&\t@OIqZ+;WQDLEqD/DnS]A#W
D;lhV3Y_P#7dN0nNnnRt%s>?p]A=J6WNQ@(P<eP7UhL>G<jGr7]Amer[7+c#m)%@g1j3f^/b8J
lROl`Y,UZQZQMkaNbsc#b8aiReh?:RU^_I<*GGXF,YLPMBuk?#I/<'s^VO+"I$*tI4+NuaV%
;DTh;-60mpDeQ9>`A49#>H[1f'[1+7cDp]AH1WaptC^*dJTJ)R?6U6?LQbR>g8:pe=(=T^6;q
VhSs/,mIc[drKSARX]A^;^>(!+aAKQR4Y'IKO7!eBhFDn$C?8G&B[n-hDRTl+8I,&MB>\6PSm
Q5]ATW@;Pq(Id"Dhki6`p4>>I)tFCZeY,l!S"hc&fU7OE\i[sbaHk)u-(\JL_MRZ'$S.E>9p[
.=mLk^W!c_t9^F75+.@BO>_dT/1c.3[S;$X3#bhBaYDC4]Ab#1l$8AdVGi&\1!u8p'7*iV=8X
`bPHJ1/L^:e>_:+/):#e)$(]AVpI5sfCRoWNku$P\HE@8jSUq]As>*XDTBL"6uCdcdp:,iORih
MGTcBl0Egr\/q2Dl(%B"(r3;:C,6"u$qWr=]A@^5!N3TaDi[D<+]A5+(idqaFrDsTB;1(mahp%
a[$b4.82hYNoZH9c34[g_lI/ttdr"4Nm0A>9#-FLd!,HD\(\Fkj1n29b-LpW3C:@>]A$C!>Aq
XaL<*[^?^)6m_0J70+Cl>U-X?'p)E2q]AJ_d;P>"j\`K>_1g[\//?PPqibOrB=3mh_=:UH5,O
`FV^b,8!k>QC`]AB73aq0F/R7Mq`*<)?*%M$Pk#XnJYUGMfqHO>d,$MBdNoq+qrMZ%:qp+>K<
Up%^0aPIZJY1K:G8^5"c:LG.lrNH`S]A'T[Ym+4>EM5L&drC5bP6=@rZ'd@sMTSfg1\^MG1Bo
c\[$[_3Ma)ZS&nTF/<WHN?Ak*CBgRJ&hq_PNSS^C`m6Ye+l<RBtDJK*K.B:uHaU%KO[M7fG:
<]A2(ZD0n8;]AbS+D=m\bH;F2=\f:+4-TD36=;$C@diip^%0G"8G9m5HDq$h,)!o:j^,(GBZ[*
Vf=sN"EHeKkT4Bih_+7kNuFW?E)7E$[M0=+[kAbGPC#-dManS8)bJp6ZAeD*)W]A1'C'11?6=
k%c1:$5U<*a'p`38V=(;Da-1r-oI>L)J&n9F5>SXH<F\E2/Tl(-uoQ@Y7HN66'TFM"bF>JP.
lqW"NH5X.XL3tRl>0l_t`8b;m3-0I7D_R3Oo4"034uso?oF3Zo!9`T7(CPk*?c'N]A8S-`2%_
aUfo$Vhm`s\BZ8]Aqk`<X4IGcpe_=W@[F6.i3:WCn%qMq0Y[^5uKYWa+j&L?71]A`]A)U!HZD3+
2&WQ)<G$6&ka=N1R:VX!#(sFP-mn^DF01!8*UAl1ij"f;:pr5=/UDnM;Ns!j:)Ce-2I%1('\
enrbQW`O;'(>!s<!/cYl)Z;Wi-`a8c_Dl`2WhNo^q7r9^AfAu]ALfF%BYQ<rfAd>:If6MK!ck
T>EuQ22FC$<4lhGDij(nEVb1/&jI?&fn9s8tiV3I>Xa0IpK5,JKZ4ts6pC[RmDTt!E<68`.l
$NA=.3RQ@.Sl&URc*5(`?cBS]AOde#XGPR%2^"*'t#G66LImL4tIGUN%K]AoUXXIBVR@*^3`;j
3]APNl%ORq8@[[Kp^kbFgX4O-iPY]AGa#(QbMm#YZ!m=kD!Q"1>(M_;chcD'U/>ESr/jJR?_g?
1$FbF$3d!N#)s*:gL:`su\udsAbO[GHJ0;+@Z89EV/On2q';e2+_I]A86_Tr&+S&$Zk54bC2'
#_mA-"G7?>NY&P4H:]AdE@m<DB(hdaE?s(;"+Xri$nP(Ua0n#kXsMsh-NT?VAAti80'(Bk;Yc
nrT+?:3J2nI!6059K,5!?jqOmRN=2MLmm3Vdu!1k3qc[@=$s,mG46N,W-gQXk^"%AjM1nSS/
4CsDf$@c>9Y/>W<Yred#4t]A`_G?5QP,oV`+`j8l\posJ[MD"m"pRZ$$HOY`Rn?3dfnk-nVC?
*h6[M7s!`b#Ck:TK8AG3\t7Fk&2GUoCi?*h13t%Rom=o31(Ki"1qg<r31>/*e2D(p<.,*rf^
LMKKEI?/k2@h-\m*@.U(n6,^tLJ?04o>rg9YOf0YOf15@^Nl@,j@pVEPlB\\M.sDD+=a+D&k
.)W"ht+&HnT>8Wj'#T(('&mpfrN62hnAp[Hk/aH;LQAQr@B,k_a")aIotn%\,e/"d2SG>.D:
,$B)#*FD?/SL^L@2I=*\9gYfD+)2b59"@#lrc"\Ag1SJ;)?YP<(T7$oHB^@bi]Ah!J#RZK%M-
Z`C8YoY/c!`crL^\M@9Sh'bgF/.LQ_>)o!9@a^Y)^qWuKJnfM]A$GA8I'h05'Mf$5JC\W4$FT
>jNnHq''#hD%#;kLjX4W-3AghIoU*p8l;boo*F\+"sc;J#jP[L+U;EWr]A="cMN&W/rDRQ1Y;
Y!2]Au@W7)<1X*oRfQ,ZRHY94'+SpQXub<is.K=ud*(o5p<It;J`e\pK!:j?kiQL7b?l2o&mp
QOo0\,_n;8]A'[]AI[b>A#85%`#'(V6F1&DP996YI$29Y,"WcF[</4,l[Oj*Fj`:qj\,e>.r</
)J(hP&I^;&cBih"%<qs^ng,U<pMGJLf6a'T:edq'Q!%V?m=`-E.K7Kf\AkR9W(P6L1T/7p1[
EQ&=+c+50D,`'X(7Y&M+2Iajl%I:h7Bj(L\X6AnWDLl*rOu"<7*>88$?$EiZK3(Aulnp8a,]A
sk@6LT/"$EFEQds9$V#G1SI;=m^U&7pf`E9,d1]AsNH7dg!Zg\@9th`1qC)!)_*id*8%Q#h@`
.NA&E%$0r,SbP)j3\4B`XC)KkpQRU.;e5g_/0cZ.Zqn"H4@-i+kP(M[r4+NC>@4T:mM,YjYE
]A^LM>@$Trem@V'bI5t&D,nr>>!C$CAD(*?>*AN'l#7i8-da@-A.Rd4<j4btK/h0TAP8:!\45
U2*4T@R_s.NX0'%Qh;Z$%W^-u4/=6HV><0"r,/Ufj,*1pEm#O-+l_Y?6LFI@$7S>rO"BHQEf
0knN+d[+U;c,PrBl3C?doe[cSMLO0*WX-+5@.kSqjq5-GEqgS*"fS5>_E6m4WBp,I>S0ioA_
C90Naj8&C4Zfs@9Wu]Ak:bc<7^5H>7-B7@IlcJTICS5T?)qf=jpSNu6`&:<b78&Z9"\i?iHg,
CPqrK:9onM_]Aj?2aAHj'KV.AKQ/EcM-1`m+(-P_X_H0\KQp`6uE-fL#,Q\^89gi$l"a;#qIg
bRhh;kM!@BBC:-&Xo=6nSo\t*Q[_R(Eo_h)qbu)I9A-i7Dj$`(P[1)]AF-Bb5o2p%V)Q<)kC1
i%)<ArD4I\Aabu^>a&([r4*0-+i^7]A_Ygi=hO=A6&m:XuHG\:*XejR:GqC12T'Y$Bejf>gJm
AY6kS2,rP)<($JJQ!$E^bVjtJMN>IshuV:U6Hsq6\o/#-4sJiV%]AQU$"lX@tZGN!S&Dq)j"\
[b@lVT;/,Q5U'?T(7V@P,2&s$C]Ao$H+A3!dD$_+DDS^V.!?<\.N;YMTSt%X8!#h[YkW/"+F[
.SpdsFi:(O]A+"hW5%S)asjI,:7)SCrL$B;9U!1(CHSs5"u/+q+<B`G\jk2Ud:'p$4)'<h%H%
$'JJ]A+>iY(W_>l*m-&d1a!Sl(B8Y!CtiW9VNjT>c]AEj1VhmKMr(2F\(F:_Mb&-*[R\[aqNa.
)6,iibh+2(?q7E-=T\"S:dX=W0a#cB?A4+p<HIo?/VX4PC">h/Z*><QJ*a#q+HmZ$-_=Ur,'
%O_!IC$Rct`AH;2aDW'Ll3bqC+U*H!&ZY$^[a)pMJ.F;O$'S3M7g&::f3V:Tp.R(.dT$A+nL
1Mb+LR:6UU0J&c;JWgYlK13AIeQ/(>KgH/JZYMH<:8/0o:(%cYKBJd`meoT(W*nVPLM@_Cd'
A\nhY8$+3-5J=*%",,CuS\a;JYT63;;nXohDTAgY/,mZ0GBR*g"BbW->NVCa]AQg$.oT;\HD9
=1UdFQ1#;Dj.oX\c^,r6"sI:/6b,i0QN@&OG+?U+@.3EVn8]A!Tq!1X@Q=pW8!""mU*))U+Om
qPS+7N/Eiqul;<B8tW#SX=/=KH^:seQOA1d.)b<6Q1,q-/4AAJEm)o@7#['6ZCiniOQ51Phi
!=CI<I$^<!iYG[>FEl'ue)?5:eTB<BAJ6U@RLI^n.AgFYZ$H.`Og@[p7'73cb]A_b+2h1ai&4
+$k<.rAn3IENCl-aeMFk>2qH<g9:dh7O5iZhPSf3^MEKCGlJpm1-gOnr0_:7d(bf]A,kN]AZri
a*((8QBt<?8LmhQYp=tW^5?#!P\%u/+%:F/D-0sr='?+SJ\#G:0$3R[BG7]AgbrQ8H[J[cI><
539h5`>6DL-n9YfKKK-p;Ks!8=Q%/RauRW(.65hh9"rGYgB]A8LYOU[L/7/8Q]AK+Q,UEPe'5j
Q0RcRIaQD7[s2I#i&.XtLAg8%C$[OsY9emn>`]AC0DQdRamZ3#)/5]AeC\4npRI%'lhhaWSXCF
*<IF/A\V=&=-;s4mb5,K!0I!+6IO'"m\A'e;2@>h=T.Xi`C#]AG:US/CH#=*s3$,'da2)q&H8
b&t2-hU[<D=9HHY8%.e%>=t8&!4"5nnTY0,*I&]A0KO6W=C/NR2ki=BioSaWW'E%=lsb?\6B"
GrLnDF(/]A^h:X[pmGI"m8Y'o+?;b2#93N;Ej?`j19/\9$^%@=\%(h@i5TN_l@*9.$G.[`*qW
,Ftl0ki(.bPahW`!$66`<R,l2mWaMrpZ_OWimt85aCfUIAahe]A`-2_4*$MB&;/'m5`iY+WGa
,"$`s$L^N_Va>WV$H[pC`!5e79(R]ABGl:.HDYUIB]Aj>^MAo_E`)5WV90#r/)XOlpf3XV^.os
^Ei';A`\</>djd;/fquM65lG5gR[&H4=p[`!(Y:%SbNIA*^=gM@4(,1<c(3@H<52/#DbAo6B
cNrLCN-rH_GpZ\Sh2N9YM=B-`St8eoJt%LmA,XiGiSqAaE9\X!"2"&g!Ca;iYQiSe0U;q@4g
'<C69h&m$;)I`Js`%:!8TB`m+/ic#"iFZ*_;qnqV0pOq!XqLLfe%+>m8cDKk3;HXc'1agoVI
GQ%_+;b"Y,?Ib2%KoR&[cQo%&fju/WQ<@c$"$H.Kq>C&(16@&\k&t]A,S^Zjht-B3g?D,H('N
60c'\:.N$Lso1VWh<rU;VJe8St>:FeJ&;p7fg"UZ[!E#C5S=F^`cb2aciP&h?omhMP%7bG(k
E*ssK!0o(Q,fgPV[RA/sH!Z[5)'n?[4/B0E9omsaX(!bQCBJ=?eDIjb07F-0&\3qEd]AfTd^a
:A`S;tL<[ggjJmPZ%5j8oKVV*\uZ52^[9rpWT5M<+K`ejoad;4))@c>DGA#hF)U>hoq3@30j
?po`4&7.)V,VQI)""?G-kT+qLFpD/+9%SrGLG-u0ts.aJ<AjuQ.MMC2DL,Rl3#dX*5.&1A(\
kPP2:*&TT(\d)+&.nHDLk?S7@DAht937Q1[89kd!7B.0Pk+QJf"H2RQ]A\f-TbtL+nU9FF?Yj
2VBC>,iVu([OkZGb!5<ctPO3YB[=;Cn0jH.Fh`B[Z8SI\l)Cj9.A8uq.o5"RN*^@rq\8Pq)=
l>PM/T)9+T;,fWoK+tfr\o3AdXYDOrX?I_aGc\9Xg5kIBUE9[b[-3mQnSj$g4g3YO<MoZos6
X\"lVrO-A'>OCkMri?)/4HJ2CmV#bbs1i&,t,t"fkT%/,WHPij`\Xb%=p8o(`dpNpgG;'!R7
u/mE.aN)>2[C[<N0qQ)A1`@.&*2^O5$$?8<-G@s^;#2=cS(1-Mm;`RpAVuF#raa\>U:&7D]AY
g0(4q"1?Wl$Lt+0[38(>tip)Nt`^Z`4oM_WZ.bgeiQ_H+9!S\2S0%P!$J%<a%80Ur8[(SgNt
*[WS`%MS/+rBhdYf;E.'crdiW`LDC-dsc@qiLNs25%$g8mo.,B(im3=Ef-HGH>p>*?VTWCsA
frQ+L`m9Pni<q!3c?!sWe1ae"\C?[4h[V_Rpb)4q`Mg)FELNr?IAoD/2hbd=ns/pGS%qT49L
LXKE;6R94;O^Pr'p(JFhL2Y=td77ELl=T6LZ*fVDk[Bd>AQ!KV.JR)sqk5dB]A'$jOjI3k5@D
ZXKt<7$g`W!l13sN`8f(F`1BA6*Y%jHP)6L8mS85O19XkBYT=dYT?14U.@"Io_">pVVpMh!#
',G\34_9nHS33&PVYR?RAp(n4D+>Lq,%?""H<X4N#b*HO.)#]AZ7[*c=rnPc02-!ibGPl71#!
VQYaZgB$G*-4=+?!7[Li;h+5*7YnaC$ISKuf3!u>q0,WG-"8e_BA)K/X8ii\u:K3F44kL>#4
VJ&_B7?aq6S;O9g7r/Cd\:<Y?^=Ga4eL^6U:glFp-JCY4BbZ@pPQ;m:aMH94.:#4V]ABdlV*h
B#3S(QMMg4<EUk0n3jkU^pAJSV=+bC+tc''"pUd^i\l*9i*irk*NuSS[h`K1,=\iF-7LQgRW
p*2\)ZQ8O[s9h.P\3gU?%-%^4u:H,bSRbb+-F7*n(c]AUq7@?TIpA>?jX8lOGeW*u5JHF[CJ*
05<j.\<qfcshhDQ91'T$e/:A<=\&?;J<l;qa/1fW^d.SAW;Yhc>XcH0<`<2p?Qm(AK3,hS@S
7DMmpAI@F:B%4#s48d[g%pLGWLNB$Gc64k98!C7a]A_Jt1FbJ&-1=A,D]AVPYN,pr#dTJQ[9S`
[5Y"M("5"DHYV\p0uOYDk.n&t<Yubtje.mdCDA-jIoiN3lAp<8d56Ck]As4Z8F_N]ArDeoL@5=
q1SlcC>G1F\[E<4"H.s2aG]A9]A16N3#*_[[$L[X%cP=eA6r^n7p<JOZc$u%oU*lD2@7eU,rJP
IVG_YJb*^\Mfk9&V:BfA]A66ho;KCrE@qeYMp%\P=)OQ,4)hU:q#mp!Y!5b-He$J(jPMqZ[Pe
_tUb-&DJf<6%KqX^62nb?ZXVoLPh#(U;XW^)hG6[!<#dS@0L9]Affb%J(ZMr^k<#Aq7;WX[Nj
%uB2fC@5)c"$ZO;lC_`ag05M[o9NnC_&`2Qgeb@Ac(2<UO3;N&;bGg(Va39[s?Q!AWs9qOSR
BTW14:=6PEADkV%[sb_%Rjuo`AVQ5JHSK1"7%%7/-$"f^B>R0o?88k^168C.1Ik!-E'lQ=3^
&uRIPr;FM@Vogn&!0Y@s6s:jl:Uob:g.=Q,hl^i>J3\=7fRZkeO#O+^#`;X0-!,B%"oh"oP@
e*cbg+_+b'5>;J?$YL:N]A:<ua3F]A?\aR!Hgm1(3fL7u(l'l%Ga$]A*I.*L-o78-f/Nf$1D+gO
j?,;"r>M+_@9Rf;p58i3$Z&"56IfqcpjAd5/.ZXDRW7B[SQLO'0TG)e#o%,]A:Tg<M!ac$AL:
euM=:KcCY?rEe()^g4EEAd),.WdBi&RQoU/&t4CG<KRmA!4>:))V<HS4kR&Q7\(9U_5OC4e<
8n/WcY>@P9Z#,,*i:P5+2"f6q+*=J89$9G/--LeV$6mTKSZ,WJ#'>T=#9t[b3?/ngY"ZBH-=
G*T(.JiL?&#Sm+<'uLqrF(mbUbhFBK1Vmd7&$Soc:Wb;,4)^KQ78EfnZUPL:Y^P>..Xj`oM.
qgrVt[T)P^&16GeOq&k*RlW18bR99(peSZj\WkUH]A;km*U^L@<FoZYWam[>mo$Qmc6$o,!d>
2sT;U>`I91JIe5Y@$%V*O6t[V40oN6gZCVKfe\@@@%`'ge-moZXI(b(TZS#R0&Z7:f>qdlK0
%FRle>S66=Z]AoVJH+CaeYB6mJMTDh!lTfUIMbGS_i13EUU:j9!8I\R]AU2LY]AIqV@inrb*'f:
[nl&7AA4*A'K^b;c"NO:g+ulc@/Ln?Ks[.';V=$FZie<IfH$?V84k'^fMb9=8P@J!'U.@chd
lC(Or8?ScAMVfrb$H-%dsB8\J;1p$kCr93C@u:b\]AM6GGC9(;iBBg&pjn6c6#*o=g=4PFpbW
o%m*n#q`!Xj\FPg/-OC/TeX=#,#MKn)X25lB/C[3bB%okQ(AP$Q1#'!LTa1t7Xp9kC7/ZRk`
>MJ:"`]A4(.2@DD%eOAdm_VUa&>.Z[DOl:.HlQT<[cRBM*tds/I6:<X:m.86pgEH#>;Af6"su
+;.^3u.,PWnRRASU!9LRCWiq+?_&CSlWEb.$S);&JV5i5R:6n>63D%Nbcm-8AsFaFiq\dlDd
o("#UiY-9eR6<qRMCKn.JL]A?3M(XuK?&W3PeBU30o\9UjR@>hY?\uqq&PUs*<'*QdD@6-d@W
-B?J[_.A43F<F-1itAZ2t6`P_bp4rGBos>FeXe0)(Z/5oa2U(8TAomV90/=09tYh&Zq@=[*0
H"%%(rCb2qB.U-KMG_?_r.QFaer<kfa@p6K0.Q<3q\l$iq-Jq96T<R]AuG!nSP*#FR]A$cAGi?
.eW7Q9HBs38H5nXHpKj'<$.)qD%`K^kb/r^Ra1"NAr2o\[p$Dc.$FlMG6)'e4lt!1Es;sN58
fb\SQ@=k2=MHAmRBnIR.U,m_n\M(P-A"Mt'TH)m/$3\hoAfl-LY-qFRSh?1(%^]A7)PE9AM9m
RotBrRBj;W'Gm\reD+\_786^'Y:AH%b;:01;'I,uW,(;:JZRr7PRa(+fjB6T%<DjONN)M0Y/
?hjGe\(%"RF'IZ%7DOqNL!;WN;R%rR#^uThX%1@eWrX`UC@/$sp[UY;DmC7gOpL*[>I3pWq9
m-t@8n)'ubt"g3<.Ueq3Kfs&5pl]A9Jb$W=ngX:%>m7^#G%ZI*`bp`m]A-#e>1WihR'?o1fK?_
(9f!rFIaeEtR<ZR$tQ0W`9>TV$$2D"n67K5?0O-lc*m\*Ln`JQ9N9?.s?S*WhapY,*50ip:,
YG>rU's8aIBMmUL\K8ML$`baiI2^,5tOU4t^/GB>P3r(!eA)5#<MY+=.JV@]Aps+a1VfGqVU%
pJ5Lbi)ot3imMn1p0II%qRk=b'oY&!(\3_MZmX":ju)&*Tbm7T3f6<k,Es)p<[$=..L!F=q;
06<;<TgLCc4)7"ePrE*!i2n?.]A;u]AWoJLN!)c92Q!H0&'O:0EV=l&lW.7Seae55SS`K0rOe#
G+sC@D$E9efJU'_TWD#9a3WZEs+2n\Dbl62S&m:=$:iLI6_\hhL&C'Olg54E&IUYV[52ks:@
@X!Kff-aBiq<Y8-_FrN0#RJnB'.PC<ia)8ne^4tg8?)s4OnD\\]AhKDpH!]ANjXb23KcI]A"=pV
*&2G\#BH<YC#ne9'Sd)8</V&uX'D]Aed%U4<q;R=*eGC,`itBA@E<1S8uCQ^X/l%$SX]A:G3"=
!K&iIMbFb33pe!^-FZk>B)(8u>Nmi,0@O27f4da9K]A$1jDV,o>,473*^m%KY9H`K#8oNf%I@
i+<fV_mS'\]A(h[Ju*TgdH%G(N(OooMU/Y`#4P%p1L2Z4jFg+SX+!`IfDD<pHdlp-c<D*\Oh?
34+mQ(f]A2X6!Sqb5pE>FEStJ'!"q-G*\-&)Qj&RT[didGj<PrM:`*4!<G=tI,i-%8n1;I74o
CpqBQ`Y6e[r;OX)mZg]Al(EjT\+nh!N:Ht^POoG(V0n.bUR\#;DV8M(0h>W9nGn^Qf]A;n2PF*
=4_"B<TBJeS<DP+;C<5i,`E8M';35K1P1BdTC1en^*Z#AP>:j]ATAZ(=r/='2taF?ejefCo'r
DA<;bVo7'cf^1uqkhicA`QGdFAgf=\!I_WTW.5QMNUjdt.$q'1c"*G9pJD>1_Rp3)Ftr60V1
n^OX$.-!XBSEoMZguLeD[ISP<+0o9!<@?<JQTg'*$22o>4o8L'(48rX?LsZLch.eTC?,X*r#
0+Y]Ab>Kj6&&@e.iU7.(93R`:V`2dl)jMS7F,/B8D8OE3-An-s1J1Pa&&Vc;77?(=ptXib(_!
O3_Z+ReGhiDs#hL<"8"#UeA1WX<0"NH!uRfJ`t7^D?,]AW7+\OOr\NE%[8\?)&4"T#aS`X3ua
>>hK-Ql=uV^2,ICXLiQ@,W7u&/:4pU(8fg$EudaDeCXYUQo[HBWi^gE6X5hFV"=k4Ea@%3Cs
9DaTIPEpba[rf@K38[U3/u1Gn`:qH!*+'RY(`]AA_mX`kip";To_=qq7^?ZiNWc->[Y?/ois2
uo8O,1>U<u7iPO_G<H8CGoj!/76@MYpb5Zj4JZ=6+]A:^Mg8@?>9h<]A:WEHW*j\is7YR?>GFR
+lY=T8XqUt<G/#oSW\Z,H;64?>FKt:gNirmk[B!0+nJ%Q4+HsuM(:5-X>_sGtkik"O^Tm0)h
0Y`t?F69>kYJ:>X*F49+".0<Oba'Rg+&sj6>l!:7I046,#9/.s-&aDrETE$Xt0RH4X,tJ1(&
t>'W>Dt;3Zt^(kuDY3:oK-=7O&G0h=#@A'ML<3d(d92N_3noMjQWQajlK2ON'k;p/F@NNTVO
LQC=bT7H,1I+'\Sjf<I?%[ma,LY^L9(%3AA2@@2k)481OP4.j,j>m8YM"&%F3@p_!'Xg(O2s
->'A'dhEk@mA?<C&^^O?8!rpg9;r?;s"kA]AW)>[Dbld^1c.-I?u$a!oN@61&Ua)!ZC37o?uK
6rB.?:D"-/hd]AG$qfVUEkQ8Gc7ZpRCZ'mR^0FenWnlosp!U#3;j5!q/Hm4gDpD^F5CB!C&ED
gJUjDm@naB9auWgs;j_X%U#W\NhuV"=FZcYZ4OU^;1SAi9H]AA`r3Tr7,-nRRm2k,/AS;2@7r
[j;^;A6&pcE:f7:@rI,=uE$ibQ\,<>D(5ZngI-:3FU[ih4UbZ1;s8kN:c<d-L5c@,`-amc,^
mm=rOHloutkAkT,/$1E+s0"iYrjc!^06=1p`7pJ:TWB8@`pn!?.K=rgI&Y39m'N$ag#tcMJO
&Gig)0E,Sh4mq_QLGU7'M3+U5E/f/,lPr&JUHD"t,<1$=EYU%8Bal`F$KZIkAu'"D<C$H:@X
5"nQkrYP7X*_<`W#f*[hI$\aiIa,LfuA$#H$fC\#i#9F3n4$pN.*Y#6[r"?11CU.oJlbAnfr
t>/->46sI]AI70/aM6JkTcD\a!F?VS$i+ucrVmb1qR*[%M6#_-IqqJT0AM&ghgb%toM4iS-VF
=jM=c:.7DA#kJpoq&L,G7A[2MBfJJ%rSi6WGc<\@T=0/V/P,NSM$:6GoO%$5]AH!A6"_)[jq#
^k)_@:Y2O-k]Aiao0Ns//g;AZJ43NqN1c*B4Ej*!!SV_/T2,,U6]A\R9/aFB,0K5TfV+Ps_%@]A
$1O0RdLR&rhCL:I$X)T,0sUH?0(?0u)np1@PN,J_@2S-1dbT-MFj^2F3p$_-U\K9!RR!B1't
-7=\A#KnR`!Y%ED"%?I2dprWAoI"K`JlHn>Kr6oKqQQR$\HcWKk;RVk@=:h[A2$^W4i*=!sp
L,Q\TN+fq2_ub><%!J?r?IA,)8ak8MDhiJ[(BrE*_9XTUAZPk]A50uco-\t#.XP'4M1?'5[Np
:!oJV\C.u%CY=>:(a&;YZp8YC%[3Yma:Z*#?okLjAC#HJqgRXWNa?rI?Od_#C&/4ls1.X.Q<
8q=@)8+fX_QC<W?(jZ*th&F0ZEtdB$DEKNV")9(s=XFt,-lt.+4""QKqj`k00H3'e:1eCn!H
%h-gMHY+6IPg(rWE@</c^IZmn#,S)!Q7qoP\++[0`;ZmCtpLe4bktX's:9$Qf:Th6Tp7(r<*
E^i>FW+bbhK%EZRo5_67RN_9b)GPjYAUH)Y9+jAD=cNl`oNgu3<a%sHl?o=K#CK5ZgWgG.UD
!<<ckIJ,?j^_=8@9h8o#CpkB9Rm+U&qE?1A+.?pQVX5J:_,lqq@5^nM:N'?q1GVjMd]AuVXuX
lq485p^s!r#cD?+IWcrGU;IkM/cs2\5r1Y9WFm&*)$cf,h%8JN`=2!%M>,h:]AS8iS@+Q\`ZB
jDn5\+_WnP3au">/3&sS!tlM#1o[!J?CE0HXX`16I\SVeH($2PpX)TViMDSp[bFQ(FGi%^g8
]AL#bYA'3IBMrDMVqNdQSm0E<`^H-)C7YR)RB<n^<Y;!d8Y&DA;2KW2nUr_3jC\!)\(TJ&O+(
7G!TscFUr`cVO7YI!p,Nq^&BqD5Rr2bW\=a3.RB;",eC)o$ZhF=`3%JhEp="nof4#u<HmqWM
`An#CPNj*$s1I`'VMrf0V;2(N2\R.W7lgD9@[g]AB3kJ*%5eO3fiEk]Ah-<Ib*H6:sWQrM*%V%
XCLSO-K:,AN*MQL_lbAZDJ$WZ'o]AVRO:60@\"':[B4(/VcVZ%OT18>HD'3/0.I5C9i6;/"A^
,sFBb@B-'<#KjYH>A_@"n(3e[0oQ3^hfo%+4"E-mYqS_UqAhTa;O*`85Br8(,Xf<//-Xg)dX
'nID0EAUS0H!b&#X-Tb@gs3X/pgaJO#iNKP9a,Ts'Nh0O,TATh<5SAk\*]A,(70.+BZ`m.dGS
6Lu<ZcU(X4OBCPna*4Ruo>b%[Xfsl5?*5,=r7!AuuhR/[K/-oRnPNOie)`Q1:Lolob4\3B)*
b;]A>ILYJ376^/>Ej3-b%nMYK[ejDN:SL(Ppqlja.&l#GUQGkIiNCqBIB)o^r!)>d+H+@<]A[t
ju3EURVSSbOFL4\S6J^e_`Z:u&I.%9#L,<-M*5n7'X'H*F6n9,r3]A%i@A&=??,N"a\`%F!El
VUNIp%C)HnYHp5bh@7n<Y\@KJ0bk]Aqh?c):P@jM)XhWr.]A6AM@_IW6=s$J0*rK$d(g9K@e[>
N9bE)Bdr:m4?EK+XrNZCP^kfm!Yl+;1_1_=mAiC))t%KcWYWeeMY)HLq^8i)iN!K?UqCk%P&
t('E$XEFIoE^OuWA&1,#0M(a3,i7\eq7'jsVG$(eo>J5=U[C83G9[&gDaq.@s$uhPl``613E
>a`T^u9qr:;?V^R3EB>W]AHE:f2PZ)CAY^sfnf$a\8:j1XZ<e#kUa7_BigGM>?J9h]A;I!4]AO@
;.0\%n1C1hbL$>)+)C&QPL8.Cm5]AdRNklucfs^t0R5J`/73gOn\oe@A%j/@\-s7/8(A;Yc52
C`nmlU2Q8aiK%N#b$S&G7'lT'K@]AY2O$PRp&-L1F8,BMa;&Z#N!J^d6m+!GVJ.maO/mE.;b/
;2FY[?bGYN-)tktAY'91#nOW4h.FG?S@I0&38h^Je^dQcnqlX65)'q3DL!:hCT]A:i]ADn-]AP'
i$dk8OJQWR/e/LU?8qD61HI1DC`\s6?<IZ79!93N^fhmla`m/dE^DrM==L#npEuU6k>b9b-j
*1K@L7.c2'Q@Z$IgjP40'>mE"]AnXn'm>rhZ\00\c?&EWrXX`DD1<[?Z@u9#VFLR[Bq9.3`ri
%\i#du'Al3eB$+cY9%_gP%\l\0g'aEnTG8`pk-s+'=nHbRoSa8QJ'R>hL$P)Too'MNF4*+Q(
\Q`^?fCSi3b4_ELHt,;RF2Y%3d.=LgP1a]A53=lk]AGE8Udqc\[K(FHga5k#6Sdfe]A<6a*h\'I
=m"H.HT1QsnRSH+rNSnE_#t]Ad%6ts+k&E?&g^ZPHfqM4-FOI;/\r)h90rSkSc$snX8B*oCH1
>\6GO.(/*MJgCq^>\_ah@;EXLd2Z1_$5rL)80kZ`r>_pUd;H@rIZ8ej_K1^=Se2lC?,i)/fD
k^Yd%PKBPO"U+3"!U9I^.gY&[M?MmcZm4NYMm1TMa181E8#]A,^0e2sAA6lQ7rrXAanBA30+\
IHLi2qmBi#.!!=nh0#1R66%T;KJ)?5&8d3)7g?]Acr"%5QO#Z!$'uQIE&#=Z)P8G&X9.JFY"X
@nJ+Oc6L'Mj6[s)MFR@lG?DAK]AZH`[?9ZPdA68K\Nte<=8#ORF30QD::=eTlqIq%GCG3M`15
an6D-^_13mmO+'Y'NaO9O)+R1]AT%5o;0S$uM#,-j+)A%<d6V188'N)d?UGr0D52j,q$qRVi:
?2Gn16c+7`;)P?Rl-\e]ARBG\(O`iW]Aa"F:f9Tb\+jLgh59+tjHhUT;;(aMVmfkUININo_m2G
Fu\_GuXZiS&tErSl!\3!(uN-YbbWFmgMt3cMkh,h@;H0]AL/h(-S/3grSjcsF.#V5053<BM_>
:pOSqgpE=pR(CUB3fbKn'9Yh6g[lF)d?5\XW!NtuX04J8.]AV@a\oC9UC5`cZINO\X)&$/e\n
MC`Nc7;:=od\dDU]A%<#bHI@9PfIYiHU[D.2pWW]AS'A1Z`REAC>B+rZJ@(H\4@'TH>aUn-r8F
N;N;'YBk0hn5,[.^+c;.#V7hLKPq6/8GB17WchYe8(NJ#U3?Y+G/]AgQUFqkjLHZPN3GFP\P%
M^c_9-i9g(mN,tG?h.P/&B)Q89Y]AEnES,!m,UFFH`(QZR7XED255)Oks<KhS8n8tV)F>(qhX
$DTQDY/'AI4&lYgIHNM49r;#UPe`G*dNI+Bep(FJG>esAJi.3hO8s8T\U.fnuMZ3Rm&*OUS[
/?&"n%@qQ/BL8tq\Cd2\Y5.E6SG9++tFBR*?Q-M5T:UtK@'Ii;l';qpe,+Y<fG6_4$8=C?Xi
i%C!ud#aC6;ECk>Wman&*m\H4'h(n%]Al$Tgq+Al5?qH\3P6oQWLrRQR_bH4YBWtIDF:WN[aA
YIa(_K4u6=[1@=9Zs=_R&L'$pH17.RK%V.[ciUe6G$iCO$=:Km,h"=:0nP.>N7HK-[sl/D![
m&PMXH>JXpq9f_'g`CL7J:X,T-U49@iX89Ea!VrN\hts@ITt&7b)qGkO4PSVO]A32;cS^&"%1
4)0RFir&E@$(kP2%!I-%5pq(cPgnH[eJ\^F^8P9Hkm8)jKa;fpK0Sf$NC;*.'K]AYhsm%1B]Aa
VbO63Do!0/B7CO#:d"iBHU>+rIqUSA1?mSCCcKhYE6=[2Rg>qfOAct>/:+=#%"+aK_XGPI#O
22H]Ac-QT]A&L0-S,>I4=;>ONa8`LhoSS1&-c)q_s>B'l3.&raA4Kn1DdXKn83Q%s%-mjWN7S2
)%M,),XGmAfKc8qC:[G.T[p?g3K;s(KCkAerJ$a_5"$,8Y\#;e)lW<Ecmq))V=X$n1@d$\]A7
F//p.5AJ)JmAf0A5iNj"s*[V8*V.mBLfp+,^EYEEH`h,>$W]Ag-A0)J7sD[4.J5pe9tadoa$7
4Psr"3b^?mk10qIu9Mj)"K76B_kC)FgK56s87U$dRWiloBPCYBCiA]A<i=sRjA5d!M'Wf#L).
[&Ocj?60GJ6ESBOIOZC-(Ca<UY,(p1<:?h/%&g0TMW]AbO628d;/:4EoS]A@C]AE@]Alsfgfu^]AS
,e]A9BA@c43!e\AUroajd>$U,]Ab:Kg95FNJ_fe=E/<arQE*9`c]Amm4CCR\R@m[G&@\rGo?4>>
ab(<9_[>@s&Ao$Q;"X^%?lrJ^ro)qMcf</N?BTP?ER"LcX\J%]Ah9u/1WlXfU4(eQQEroHKlB
b6r?`Z?G$S]AG/C/&p#L&NV+K0,\dCmH(trWmb9'j8%$X^V64N4P<;G%ggA,2WrVLgT7jtNL7
^(7t*AD62Ct;lhh*i6T:D,pAZ20bK;'7"I]A.u^g9H=s+Sl$3c8*FR,8crgsglZ0iMf2Rj0kO
5\du5h8lk_='R_&=gAPHH:P9p_Knf<#Z^'r&(G8+`<MZstMA%:,M3YCdek1F8GAms*T*g5A<
Ge;E6!([t&Whlq]Aic?=Ep6_-F_B8W!bIsL^IQ+srYQHio51"PL>q@?I&AiX6f2Vi&o'A$I0e
fCA%A8L]A>^a`Og3f0[/`<Qg4Fbc1ikHC^HanA`!1Orbr[QSOSZ9H2q9R@Q^G*ep:&W7!K%rM
`^CY)VKc!=P#V"Ue+M[*S4R?.#?Kq-Ta?GjSqVRlZSZr"bs*]ALsH]AHJn5KR\<JK;6.1&.p_.
lD;8hsk-$m!jA^J)Q"Z?%W!$Ne(oQ@-5=FofHRu^C6U+4s9l[CrLr,8ZHP,i/\R;K\'b,)ZV
M?>4Z\hE/!&8gOXqaOOhLf"*FF/ST9<4o[[ht#OZ/,F64N_8*ZOQ-NpmgXkEQGl&V]A+Rf7!s
UZmi:5FVRbqP3;9VE^OKB_nLgO/>.1rb2/'l&V_A=#>?CbI7omr49clH3X\giA\Llo]A?$0%m
d6H[=Z,qaaG&ca3,PXJ%Y^Lo#JrRnX"#aec-U<oHG=WIMnZ-S]Aj39mEY_6~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="485"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="126" width="375" height="485"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA13').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA13"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA13"/>
<WidgetID widgetID="aaa7f1cb-21ec-4c20-8e61-7d30e705a23e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[571500,1143000,571500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[426346,3020704,426346,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0">
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_kqfc_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="1" value="0" percent="50" topLeft="true" topRight="true" bottomLeft="true" bottomRight="true"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="tnm2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA13" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="val"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[


 _g().getWidgetByName("tnm2").setValue(val);


var RQSX5 =  _g().getWidgetByName("RQSX5").getValue();
if(RQSX5=='指标业绩地图_两融客群'){
	 _g().getWidgetByName("lrkq").setValue(val);
	}else{
		 _g().getWidgetByName("kq").setValue(val);
		}
 
  
]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[CopyOf条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tnm2=$$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80">
<foreground>
<FineColor color="-759737" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_kqfc_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"(!;csl*mEr@ZTl!;96q#b[BZnh]AXC4slO[e?]AZA5qT:dTtH$cisI!Ljj0-9q\XBcC7r'+
tTQ"pf3W?NL:#N%>Q-Lie.Q(m9tt&jfAg:"B01?W6W(eZ1agq<klh]AR0,Cm\u\(*9+<uMbRO
q)&]ARL4lUn<aQP9"rVQC_SVPqPgLFdd_fB,Q"$\(F-AO;90_X%_R4'R99i03uWZ3;QWtjb_R
F+Nk7r5CjaF!ch5NZA.pCB0Rb,s;rnb?_4..j]A?'?^mR@G`TF#MJrffJMcc,Q#[sR[2;h-TN
VWk,:r&pP?Jh4!*R^$WEh?TaE9775#nMSte]A7qb<V8NOb2Nd1R,GcFa0_lJ]An9lZ[_[E.'*b
@S>;9.9PLH$P7%$c9pueFVo7qXkoBKQ+B5D<jd)IBru9)d9l3fmg<?F<hnR:c"D*gD8tm,bB
2mM6f3&!*m,CLp&5=A8CGi0X7bUSX`l)W&ok1uI:o/%[/K!>lTsIKFT'Eg8K*Sun[r)plsXH
c>C6f]AXu#g)d58&'^7p36Il<s1j/b%_[6NlkHP/hJLa-NUo7'P$T%n>X?B9tH7[\\n`^XW`+
W:PDFk&q<5(+]Ad-.CHWrFJm9q.PBJO,=#G7p!B?b$GEr@5A1/6)j-cRl97eb[pH>s2`AcMsL
(uSP0Vkn`JUq<G&2B(1K&s/e]AsWNl(ZqVUN,e?0Y5AOam"YG#Y$,fN#d+G)#5JX:kl'B%o]A#
ZXg6]A\ajiR@e7[,<6aGW[#hEUlIAU5)s--ch]AWG_6nLNh`(R3%JCV_qPm*JR1M*n66j_cJ7m
je`0b4Csouk6nE@nXN\5`)ONfK*Rn$$Asj165?eu/cX7"a]A9gu@\=4r><X]A1ARI[m.Bb]A1^L
;4DnsQ+-E;=2sZVo1&Si>k9K#6dA+=,EWIT7N6:4<5@W:3;^$V?@WLKoX;36spt=3rju1u*^
>(^cJ(cZ[orNWG=lMG#GuH[?cU*:pj_k@`H@;2C/,AJ.J`p7J#\G#E8-r,6ELr1BZ=2E(QdO
C4NpZW2E0_k0Y4=C!4?.toEqco@4R04kY<pmOrg9Zn]AAOL\+gt(p(]AMurX15W7cO*<NLlR&s
gUaW+I1rWj6$*LKMA'</-LiHWd`JKWSdfeKhCWoFMUGJ?_22uO;,8kZear)-J%'`4_*EAEDJ
H?oM8lP94EhHm7o1%em%9PAR/KuDKS*Y^\tm<+6Wt$jja8K`"q<>2qtQa@p349'Q7]A(3>U5A
f@s@;e5<TgD<6$WYOts`o,$f7b2WH86LJBda2"3=OA,]As*m;FHsp;:.koO(A:p-($4(OTLr%
%_hc_I:);CgKKf,n3E7(Mec_'J38EY1qOA+@[@n:;sN)$cX6[#gI^AI2.3lcXuktl2g,i0,'
R2lfs@-NbFkZbmoC]A3fDKh,DR&dEg:qYaM_*8_jP3s8"GTM9X3q8,RD8_EMfBhf<3UndVGtP
^4gVELlqt(r(\/u.YdG"h_7@4O)k["f)1-S9mM2N5$*AVW!!d=,Pgj*XS)gP]AASNS]AAJ!(U:
uIEJ>TSVdjBb'G'B?EO#;Z$hUFd]A_!=Gj2lbj/(<u1:?+lE9+LfWblIu2r9K^T!I:rm\'4j2
9$R1<8.1,O;r,e>F^\@)qCdn[8$A0YK7!="33BcC`(WR]ABMPJ%J^\M;&iA5nN+6obuBsFP5S
6kaF8ECl]Ai`p8s@HXX"2ONHf"CBg;M6>"GiarG8C)f=D:3%O'Sm@%40:f/]A#7!<[h9&Zb,A$
*9'?SrC-McS66F]AM4id%dhg5T?7Ok"*BUq!"oZ.^SM%U190*#Y*WXUl0Q7nPMK.WWcf^T+98
[Qa)/?ZVDs:ipCcQXTpmD$r&Z(mg=BgB\=[FS2BI\t*Sm2KBK)pD$3(DgQ4_*E3hgNY>8PHc
UTcP"N65%Dk0*9/q:;-1cNO$Ti"WVX@'qJRHMADDPelP[p1+nV4t>!7MjXpY;/cRED)TU%YJ
XL)MB^P=mbTh.k:B<&W#EU>?1g_+k1jXi+AI;:6RKOWpnW1q40E\DKXljLqA[qfSlokq]A>W?
S%soAd3o7Lm6hs:WJ+lTs9F/+mfXji>WkJN.n0'e^9ZD+go(4Ms2Y2eQPMhlINiAlQ7mG$*'
&_FN/:2ZG2I.;m.W]Ah>@u7S&i,mT6s3kWgNM6(n<=Tp;O<'ap==e&%pG*h+O27Xr!)(YI!fW
P[\fm.<XT]A%ad4J?eupfXg+pT7]A*RXM8Vb9Lm2o9`]AqM,B/ro&dn!hL\:3/&NU)eXepOGiRC
%C:^@>b0`t@9'^@oAW5nZOL4cn-!1L'tj#e;@Zc*ii,>V*.M?Z:=J*1)9:[6G\W#q?<Un&jA
cM5@O=%&M4snM;>0<iFSGo?`)$]A\jR8+iTuiDl8i^7s;',ak'6\_s/`rU*j4b%eW@7441?P%
kVo?-5Ul9MrUQP9C+s2[*H9M@3E3]A2L!@ijW#rP[X2Zq;^K@7l9_4[lr80m;'!QU7bH;7C%/
2Y;n`H&QWAa,?\(bHFN!KJE<g[Aic;d.p4H*SLoXf6]AmgVQmiopCK6UF[/"/CccIZ329INTi
<>X4:!^`#Ln;FRqfc\&NT0E+pmeeu5h+hh84?^!_MAt="-H*eCA7&OO.d=@DRP*:O@^g+DV;
nDR*&8*Q*L&eZfLF&7F<dMAaaOZ:*Oj.OgHs8nk1^C7]A1QjV?0`n?c42AbMte%c-iNTH>[Qa
2JRMV2$/.UOK!EiK6@\$RP)^oaNM&>90bHU3HS6f4:cMRe&D)0Y#J[qpkVk:13_mPR+n+$e>
fG7F`p5Q2oN4O;e]A&Ct9^"&ST$[^uggAs=27`mDg\Gj;K2O8ah``lIX@_S,e7j9b?o0Qj',;
'"2^&m0$GpR?df`V=qMYEQBfI.Q3;;j(O!,69`-N"YCDf?n/f3#@0DpicM;nml[FafY&OcG/
M!N"!`IjH,@EWLuDDI#VME.]An(GR)8-_SM4,E%]A78./"sEXNLSo754Vbt\`T*4%_3./*T7M9
(:,nE]A[S*!S?,q+Pk>UK2m0S/IG5#k\8MK`=h^,>HnU&uT,b10<PZOqcH$@\?NHo3dEM+%:R
aUT[nIL!Pg3-1ug?G6u%Eb-DaN_>smtnJ8UsX%QuUb5K.h1H0CiUUm1,77(_8.$'=G6H.dk'
p:c[:>@J3m5Yjof4PU#n5PCOb+h:@l,3c4+0Qa,?kVIs+'(HR52"]AXj+h)@C3#Z93A6=uY(8
5Hq..3<,Q7Qk^\/\e0d2QE$'fL)q15j8J<@+FNVg:!K9op-('t65_m,I]A\om6E>+5\eQJ_BX
,Bl6^+*LD\5*pUj0<OjTcD4$o+N9('H20!L[Z\nogN[Je_YkQP.Q$IPGmmgH."4k[\20J$WD
BS'fbcUadsB09G<unj?CQ@:D55]Ag`Gip@cOJmCL*K"1MEa3HQ$m\$BPY:\$mnReMJS=0)o-2
i0]A.@>hqVHT!QrF\QYLBS;O&[&=Rg$T2]A-,QA'nJ%YGYY'UL@g!$c8`tL..Ri7fP-Lj1j-<N
W6"Xoj'-ULor8sV,@0tj?5LtILDGl(R:@ngZuCP1!@Pn/Ttp:cLB([<WE7F!!*?j!!3^^!!F
DE!=1pj!YBk^"Q9;1<#l&g*RPg15A1X+6(\gnA1l`")4AJrhY.2]A/mbWMZ!+e4"%[qF8:Uh6
J-8Dn"A"%G8:Uh6J-8Dn"N^Tgf9Hl%,3qGJ?+a\;4Tt;P*'A<0T)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="42"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="84" width="375" height="42"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RQSX5').style.background='white';
]]></Content>
</JavaScript>
</Listener>
<Listener event="statechange" name="状态改变1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[
var RQSX5 =  _g().getWidgetByName("RQSX5").getValue();
var lrkq = _g().getWidgetByName("lrkq").getValue();
var kq = _g().getWidgetByName("kq").getValue();
if(RQSX5=='指标业绩地图_两融客群'){
	 _g().getWidgetByName("tnm2").setValue(lrkq);

	}else{
		 _g().getWidgetByName("tnm2").setValue(kq);
		}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="rqsx5"/>
<WidgetID widgetID="8f84bf28-639f-456d-b954-83ac1f6d0375"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="radioGroup0" frozen="false" index="-1" oldWidgetName="rqsx3_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.radiogroup.UnitedMobileStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" leftPadding="15.0" rightPadding="15.0" topPadding="15.0" bottomPadding="3.0" buttonAlign="1">
<ExtraBackground>
<initialBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</initialBackgroundColor>
<selectedBackgroundColor>
<FineColor color="-657670" hor="-1" ver="-1"/>
</selectedBackgroundColor>
</ExtraBackground>
<ExtraBorder borderType="1" borderRadius="3.0">
<borderColor>
<FineColor color="-1577998" hor="-1" ver="-1"/>
</borderColor>
</ExtraBorder>
<InitialFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-6577229" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</InitialFont>
<SelectedFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-13947856" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</SelectedFont>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaButtonTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="指标业绩地图_客群" value="客群"/>
<Dict key="指标业绩地图_两融客群" value="两融客群"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[客群]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="21" width="375" height="63"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj7=document.getElementById('KJSM07');
kj7.style.borderRadius = '12px 12px 0px 0px'; 
kj7.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj7=document.getElementById('KJSM07');
kj7.style.borderRadius = '12px 12px 0px 0px'; 
kj7.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM07"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM07"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM04_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[xyjcngq_cngq zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="重点指标"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" cs="4" s="3">
<O>
<![CDATA[查看更多 >]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[     var date=ObjGet("get","date"); 
	FR.mobilePopup({
	target: "template", //设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/经营画像客群_弹窗.frm&date="+date+"&pany="+pany+"&level="+level, //设置子模板地址
		border: {
			type: 0,
			//color: "rgb(0,0,0)",
			borderRadius:"20px"
		},
		background: {
			color: "rgb(255,255,255)"
			//display:"none"
		}, 
		mobileRegular: {
			type: "custom"|| "auto_height",
			heightPercent: 90.0,
			widthPercent: 90.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent: 90.0,
			widthPercent: 50.0
		} //设置弹窗大小格式
	}
	});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes reserveInWeb="false">
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" vertical_alignment="3" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r5^Rq^XOF3dP7Hb,VG(TNj:9.6ePC7"0aX/Zhcg:,e:M?XY\Usn1+m#TjlR%dSi)/9PAF?
qC]A[$2\r[#%V2=IOn2GbVmLcb+b?n+42A]A^K3T54Q\d4g404cf7.5j#DM.Q1u<JC<t0M3ro>
s*p\`Mp&Mldi9'L#gLXH$HNr=,!9Y?'/^Ia[_9=[KYO^Vk)W%it>=ur=]AVRbW%fMi"l,4DuF
+),.g0-CU7+:a"A*SSJ`Oedn!uJ,LQJ#6g=PqPu`IX0]AD\1doU#,pBftP)_7E7!AK$p8E7KV
lk]A-OGs2t&?lN$G7sYfL:$0$@JS9ap=)`:i@ISmS'ETB[jZ#]A[`-2:2H6XJ=,.O\Lb5Y=5_Q
[r5XQ\-DFE!.]AnQn@YUZP927)!5L2M-HXV^&@!;Wp`hT3hU9i%B*m]A^!8q?)k()fag]A,MMK2
mXomDK5??D"`8r&k&L`nFPdfJ1'VG0k.-J1,bXdXqdkcXpb2-@sFa4Eg(Y_[kN9!5QoG_iB0
FRtj@VL]A.O^rEj.tER3ieO62bMAokcYs(40=^STp8OL%S$E#N)u;(;eNSA(]A"*uT(B]A7Chuo
AI%<i+nT^KZ=1hWF%k#%N`'koMYD$B9^NOa,Y-lQM-.##-qUN+8dNZr%dMXcEapVJ/M>PN,I
rZ(@^DJFmX159l.VfKPE[K\m1R%6N.Y"QC:YJo^d+qok!a*=<+E9:57>?-h>*]Ag%%D_D!*Wd
>q>Ag]AG]A_h`R0[:8F@\+plp"#Vs^DEBi`_28/6!uR;qD[C*Kh$PL/@k$?F,3giHPXQ"0MA;*
-2\EdMnlF'*V4@LI-?XlR>2WJ00k2/#VN)'rGSbJ/?KWLH@'-T"R.N7sYt5W0\'Ed'O)?g2?
an]AD*3iNDiHIJ&dj,c+/SPThtbd;2aT]A*lk\2sn3]AMqTIt=oDZ$IuG&nI1bJ1XM^GD(J(u95
26%!/eD808oInu[S<\bAj>CT.WJV1Oi4bU(oI`G&WP<9%7S"B,A1HWI;,S75R^D2F5D\7hJ1
!=qnXO>Z"L.[.>$+2WQ]A.F>^2`2flc1V>IQc0M@di%2osL,QlX!HFB5khoTu7f+bh9]ALPoYS
o(T,`C4[YG8HWV30_-$p)ZFaH0K'#7dopIbBq"#H'^3M@m6`iJ-RPI#]Af+_c*ku`:);H(FV-
#6U;]A]A6V!q(X-HPs@7@b!]AS0g_fkn50J\`)NJ,0+A!(I>h&3KO1<SP<,O%'5/m=XQMh.Gb_0
k,FG$a<-&YibdV&hK,IWKf]A1StVm&`XOM[UYq[<Gphhfj-"qAQHRF3\FdGtpXlF@oQrDqm6R
:n1Irjjqa5&.$snrG8Uh"s>bVFngui-WbbVV[m2Q<>5?d5G@fbbE]A$4H\CcDH\/!mcktY.1p
J=PGst5,%VT_Q2[l9,C45M@ZKBfI3,AW,5O)1LdEo4pce9mG@I/k7W"rH\aQS$Y%ENk^XWH*
L5tMJhLO3-5=_W6i_"*mn86l#EUKuPd?2F!iuc_-A'#u#;Wqq!T6LdWoD.,fA_Nmn#e;Xd2b
6\Tjp/+gEIr-eNQ[u:_j-[2\0:Gkam#UDnY1ibO8Hh>=mo74hOYtI!)1(-nNDf)NZ6K6S:L6
GamtYADZ;.U)i3k"eIp^3*]A<dgk,+)'1!m@">F0;4R"FF]Ae"(1k<KZX.`/'-A0:'UEYhkXca
sL3&'(D#""t[TYU2!j:O[%EQS#&'qMH:\n"Hgp:eaC[BV,lh@=AskdRSm1X@=E"#b)>oeUjD
:MU\r<+/bi[9EqT&[$:gft=,dD6QurNms'K*Z4ckIqO7g@+>:WLY:Ndc>^=536XlHR1WZ3K"
[T0knGr.@sc)K>lLlh+U+8!Wq(pXLSMSSiETCcI7p3'/lda7CV[@4Q"PPc9'BdFS/GSk:q;e
V2M(.?<8Snq)-Q*N<Df2kBIHr77m8LY`$"C5>Flo3FTAP&97`*"Rpl=;'#X[Odt?aul2h%/1
@1ZkC.&.(X^WMJ'2P31mk=5kSH;M3_Y'(LSe"]A"ZbeM/me-EL":M/I\[ie.aeII20'(/**$d
L&Lml'530Jp.^<nAEs4^Cba.7%>4fRQa/amA4]A$pf^aDPo"q,Wlm>hpX4*ol%E2#ONJH,qre
2e2(^-EF0pVIB>:QK'T4K-H<G$sb$=sMHDh;bc"g(i&YsL,[Hf<*%5N6h4E[;gDaD-ea6Cd(
%s7s(KI-#tDa8c:eAu+>D7/NU=;0*$nn>S1f'Gh^=]AiePlO3uAfiboob"?pY_MSboUV'lY[s
f]A4rLolI/*j8!DYJf2X<EF!8/3F#]A/*T6k"Ojag2_]A<+g]A7h5+Q^Bp8#p(/8scV-qbE2l[!D
&.-l38ReJkp]A:_B:oTl%tf+M7lN]AmL`0D19/.RME^>UgOC3q6_WOd2T5<H7D\3)V'Y^1JCR'
h*$N&$JiV[[0aiD*/e*o1#KgK'UjVftqH[0I&a;s4pr3Cp/]A%2[\F1pCZ7_c1O"@#t%8$Zik
1!@"5_fH$VnOK.0Od_fP'Fr>tfk?QT)Zo:pL\jp=LD@jTN/n^H/IPNK+!*aOh/#P_!"LSUO:
`;`)_HdfY$@;f*j*_ehY_@A8WK(^6K.>ut(2s\kpH05W\6K281Z+=+on`7HJS[apt`FjSUbF
m()eb'HQ"e@7Y0m_qFg;R=l/S*hph>h;nDKoM)Ec$qTF]A<IB4!>u",NhTDc/=DCXm4^%l"9Y
T_XNO3r;&c<RfZpFTa926k;t-uZT)+3f)Li-Dlfa1[cVm(=?ij$itE`'VGK<"(VQ:'9O/I.2
jt-REMVBQlkrLV\e?1+mUl4dMjVS)"*n"j2WeR_Zc8#'5W+EN?[OF6*/GY2"N:liU)Y)rC-]A
$J#/[o1>BOFQQ#C]AhX-rA[]Al9;$gR3.hC(r7%17`>4X[QfQA81f6BsgTn/_g5G>I0N$ED[eO
QQgKnX&Sip9bZ-grllIB<hq7W2cQ#Q]A^2/Zl5kB8YtrGIeQD(6q6Q$d"ET^E6>&d:gV4WQ^]A
-Xdj92e08Mt(]Ap-f0p)fpI_%p-Blim".RVlu'q93h_ErR168<0R+d9"1'8WWBlCl!B(ndQE?
pO'_,I=QOK]A#)!(WRS@1V@^S.>UikO)4^-9m`oW]Aq3<>i+ZiK`Q#gMp"FPb4=?CnQ/&2csJr
o?E('!X0i\%nY\E0uj^8`N3ClA'#kdMn=R=_3&]A4q2R-3LP%@_m,F$5m:5UD)PcT-0h(/X?^
L@4$SlZAC#Cb([3o+2%s/733nd)=dU8@@ohCi`H7fN(j,%KB:[29*O:Vpo3"Q)3RS;^b@6hg
WTEo4!pu'%^OiV;'\@hdSZEcRaFprba7@*u0T,E&'[o-<2O5L*%aq,ZF",)9prNS!ZFRY'5#
*df\Wf$`kVVl??[$!O\)2?,%V4\Hcr,dg%lSs,A'cdohcT4-AeqTG02F$Na"QmbX`VJr;#SV
rm!JT%isi)._U\JM+`):L3*uFgZM$oDE-rS/gFoqE&T`T#537_=3'1&!A!qY3?OfrQ?n3U?R
XU"U%;glc<lM.[S^Z#>b7sPUR7/]A9!BapUa">oTL&IfrWk08(lAiYpW:BBs+"FTp"ps.+GR<
.m'cHJ"J,aJ188t(=R>GXEm(F$.(:%)4>crOO"YB=RHgE>5*;ER+AiTdfcT)o6':2i,`B7rE
*df`B8Za9i;4_f-.PM")OBtI@I**\2cO[\l7,')jk+_B]AZ:tjdFS1#7SO0MU)=sT1(>),TX(
e8-d[pHG*b.T6(j=e=>_+##QY?#)oI`+h2:E^F-#lV8V.3>\"VJGQSd#u]AT$uMSV"F-o#B*V
YVA1CN6UcXWfmi@3fP^$U/"r"V/o\>0D5M=UE;Yn]Ap(A%CYOglj3dkb&UK4P.apO3P2^sW`T
Jl:gqREo-E8qLbUho5E"kVeYF0!-2j@e`urNT`$eX]AW((392!gqq'bP<aqs_UcN"W($Zb1ai
_e2u5)HepsppNqUNI+@J6rg>%$D_rqI,-"Vb:Ah`Z-,&u=lN5YlRJjt0K3/P#*9QE__3[[%M
(Cq;5"O8p*-e65m:mHL:.\uoXB3XdLj:fBdniS5hO@Au=1[K^8&IXSq$/EU?>30.=qSKrLd#
$$FURt2+*,EJ?/;qOW'u?N/Y9)(R+%Dkg9_r7'cM7-%V8*314^BWNUUMIK52)CqD0mTmU8Z4
%1%*5gkK>C/bAYLsloY0GVeA+DSt<aZ(\b%fb_:u(F]Ah^s?N\(cg^.*pZP]Au5eeFu/Hn%j.b
KbdSb&/OtkCuZ0fA>u33<\r<E,2bX(ek+GlZ<HpFJ\i1+WHZuCN!4LG0>$bdNS)E5^B*&SKE
1#]Ae)9%Os)W<S]A_TCJ_ku+5L.qI8N/?.YK0t#RG.FH]A+Gc<Hn?iQ[[]AS+]A.%iOh`HCPC`]A1i
1JABXjClUE5,dUrc$QM44's.Z,_p^"HGSM19iLm5g0>;l=sE('dAnd.#h(>A&RWG"3VBNAE?
kO#csCh<@Q;YV%cZJ>XtL-<*Gr0W-8cRu>dH[.:nV;7(2o*cF-Mqg%_]A>aeM\_*XIu20?)Ba
.7Q;8\jkp%_NHP#$V\^2/26)fmjm'Eo9N>rVmiWN>@32WID'V\W@<nK&EC?4ab-j5FbGc<5J
d@pjaDP/9`@rU+OO[$#Rj"KRE_?0!jSXLm*c0!lM@5GTgfK>Z)W+2Gs-7CEhgUG-b6,Y!aU!
1GMlLD5[$jR;F^)*@3Ec0N$ZtjejQChY]A6[-"bTCb(BV-J/q.EiKSf$[S,-"BfF2ZbW4g%Q4
LW\h&T&omKjK`R-:C-s;'6i+b),1@PMH6'ihPJjp$45BQXbJlRmBc"Jr9l-6TW^s^-L:*7fF
5aEcW4Cj47)LW6\Y6[2$Q^r\A#Pl@c@'L[-kMM-6[PgQ$^Yp9`'$l[1@9`L@FoF?'Vn"elRp
5Y$Pp<n0kH;Ma;P&q!7PL9#^#?AV4H&nS1j]Aafkok>/ahB!""Fk+=5h`Zn0rp:%2o-Af"+<1
S*5%[3]ATe&8uVti2L,f.BThS+metd7N&k2-(RQeng-[f0'onAj#JEiXi-qE$tmg9VG5.;$)u
5cd\Zm.)#BF2]A&D]AZLp/MfZ+*iemp/R^<LS8)Aae`t4Eqk,LV&>6Ce/pUh-8nCs+8_;ZHtR7
Q*Nal9hZB]AeCO80M*Hoj]AN7X85LuR;$@#TA-"DosC6E(JNZ-FSl\TS&D2C8+Lm5o\RHJZMSS
F/R&\&Rbh+5>:1$e-M"P*&Yd1/2MLfd]A3]Aa>af,\dRO,>P*=`P3V^Nfo%E(%+YYmt#X4Oo<8
!YGOO@jcr2d7%%!@50f]A!F;`@r*hqWrDu5KtHk]Aoc$+p+G",)6Veo%?-"bLO"#GS@:W!+_"'
E(pho<0VL&IMgAD]AI#l7dhrOjCrH\\*^WD`jZ`X8H',EqL]A[C.^8p5gB\Ve^VAra^@/E*<>R
'5SUAn^S[bZ1XtF6rrr3fir^.6IUEi6M>?HD_S=\i7:@T2V;BO[jJ<nZS2il\\U54X*AqZKq
[-OqWi+bZqL6\e*Td+-^R.'T&89aVWa\)r@KZsU'8aRiWFWK&dQdMR(WD_:;9\aLT[>Rk93K
Pt$1s[b#4]A1p=0;&2Ef0bTRkR(%qm.FBYDtZ^+c1c=G?2")?/U5ogk65bVKAC1$,['<Fe/+l
P^3"_-E\`p*KPOG!2_*F915/oH33V[]A1%fF1OP?!Lb2q+g.DW0WblFE`@^'b;EGg*2Pq'tf%
q?$NJR(f.MSbFpMM*[>mR)UuT0@#W;(Xed[2<E+d^q9_YY6j"Zk$LM.MF]A#_?4Er(?^;V#Eu
5\&D#qh;X)e^6GYoT7-@Zj!0[j6@Kjk/_(Z6-Ija_DLXg<d52qt*i7cZrVC]AICXP+(-Zo?X6
[Q1eQ0n-BZThNaoTG*Y17OFH1&?3>'YfXQT`jQY7)i`_Kdg>m"[;_OA%cFe(!N#9?n,RKfC'
#0DMG#j]AbeZ_>A-CR:mD%ZB5fD*Tl$R0i"iKl/&:?INAfhK$*EglZ1+<eJ:Uq0%,D5d@6Diq
L$_]A^(e%kPRKShV*9=o3^?;Tu#Usc<mP@rDJJ&7ql#tle:[^IpY%^+.d^H\3"meH_ZN/39;7
(u%>"X2gGYllm<IfaK[<=5_GT>PL-3+Pm6S>BN@]A?uoE/%,?4V\%ubc+I^beoRr3G:.2=h'P
ngY.-Ed[bWD0)k*o7GUE4B/,dSrgs00.MOg:HgY(S?-?XZKg(a;n5'V0%J:8mEf9dUSY9g%"
L[lZ_Uj5OtAu&`1IN&!ehu$:MomREbQZfN4Xg^`UPq)XA#<aA?2n.,V?:E'jg.+e84L5l7Pd
&a/_#$a81:hH@.E9^#M?mYF6th>lpWB-.\QNkk_-"F/Q\fM>o-:0^@iIHc1H3c"G5LM"RRXi
k.fj'k+$(mc.>['TaQk9fM?ZGk,D>be$gI$K:e[#Ac&W\o8".l?7Tlgd25SfFNNh<RS_C-G.
0j#H(DQWn(JFYMBQ1$hF"i`tcYuaT-^'2TVS?]As>GXchO;/taejh]A_8t&>Q:n_Bb*6*oFdH)
UX$9$W&IOh`X%ems!0I%P*ho>GoenXQH-ui5K!&8#j9B()Gk*I^W&>QZ*]A@Mgj67[,Yrm&r@
c_`p#k!$%ii>*T@Hn\aN;?Jr?I1[+fG!&Ln("J0&=HhY)gHeIONQLR3Fn.:k@Q,]A)2HtV@mi
7V$]AH:8;;Kt44]A2%I7H^<80oHI::HlHogI1"KA21u=A1#_8P!!W2jo'K,AE3[;gnl5.c#*+8
"(=8+Bj8N[A#/*Qp=XcZgNpH3_iSTg8`ZMmo9oj_'K=HM.&C)KoURfQ49pk<crTkd>l9f`8-
/<^mR:jrC.)-G5._V7e/``[uRW&_aC_%@Y@EkA0IMkoQ%I[MB*9)*qTKXV9;MGV#m;#D&-nd
E/fI0bcX>B5HIRXaf(ZLA?D`mkg*XGL#):9'oL$plXA:_fM;f@td*(]A&_li$hO^=V;4XA%!A
PEbpQo?g&;SK@"Z>Ug:,GgS;8(9a[\n(i1Y]A=hD&ASH2(0?9?^mS\Lq8`F8s2R[R^-A)=^F0
<b0Lm:iZMAJ)>ZD0TC-C[9hWpi2,2.e4=CQfMS0dR@MU26_1.Pe&D[OZlEWeqc,Jh)4??*,3
i0b1aFFuM#C7k2q!4D]A>R=5BX]AFMA:AU8;<,-e7jTBW5\6%'#WIXV'-3KAih?M@s9NT?AunL
S&"3IWN.]Abb&LZT=Ju$''h`/UTt4(E4E#HZpZ0lbSJ3D@!,4kW^nY>B^t;N#s#,94hLjH1hS
Wfo,Fpf+;aSAnqtpsIGs(Wj%)Ih?Gt@S*-Z36='8nl^@u==,;C,Z8X#dp:*2@Q85$l/a)/#1
K@pLd?bEWp-oSIjXcbne6PpSJgWFm7g=3]AoWqf8a#Nle@ARiS9IR?5dRTGC70LX-^.eD)kaN
eCo9>Y_4.?7\S#^j.jFZfsf7rtZ6H'\3R8:^uYL:Gktl<oBm7u$BS0M42/%./]AoJ,3o1S*lF
2/dXCc7Pm:6(Pm3gAd;$AXQF.C[<N_.\@UsaL;4!j40qt49To+Mg_FSgd5cc?h!*>VOcNAh5
;u=PBTaLfmk"Gi2<7O6al)3hgaeps0Oe5(O^P7j1at_G+QUp2p\`Sg3H"oj96Jn,qaM$0]A]AZ
e.NQn/;<K!^sDmJ7NPKAd^g['8rWh@l8GTZ]AUpjppTR\pc.P7k1-X/@fB!iq!48;YG^Ot$#c
"39CWb5L:Zr>eN!JuVT=NnOEm=VnbfI,6@]ALDp`,TTED$3u^]Ad:!\LP<;9^>j`_?7=g`p-^X
#p!03AIX3n@D>i47S#d!qne<M$68ph*1m2*URrj._G7DT)-(fu`t\?Npr$V5-H(YH8mXK$k`
iePl%WT:e>naa-6#J<H5do*9_L4PqF?2*Gk1_<D`[Q2eb$r,DLQno]AFbEl%90F]AMpTqKtC4C
Yd[U+tt;i"0$J<?p3m)Qu*agr!n!0kD-I+2]A>W#2>AI*.1X<&M^;#b5UOu?0qCs(kl^f$U;_
mP/H"GB_s;2`m%J(g16&@YAP3\((dmr&!4_PI'l!5<e:%bg*`RB59X^eTE55!=B<*06Mlejk
Wmb^Zm<g0X,Eo9&Dj=p>O$.HmrTM`qb!CnWfVB7]A0ud#NptnG%e4DV1q2MZMLZ""=9^IVHf0
%`>/>/=,1GM-5DjS&2WT"pI0d#-J;J:s&:_K%loH.]A/O09K:QX(5RJT5]ASCScg0S[p;*WY9g
(9HH_%lQsV92[pQu\$pla@V"dS]Ap1>k"^B7jmc`nNfus=kG#Vo4>p_9$>Qoek%@B.%<"&d62
=tK)l>oBk^[[Go>lQ\Q5j,:=IefK!]As:o^Nrc+<pNYVJ`eJ3R;b"Y.$#+A&%SW5hN@9u5Q\<
8i*N\7NDi+ugJ+4t;U]AW`nUDW!AWXEYkHB+WU$:55U4UkZ^loeH+4u8IO^!$Oq#j$`nH<BFW
a*;hj^e@Yq\K-$GoY&9^1S*-<Drt+6T-E";Y+>n\[]A^H$`Lu7ef6"_K,7i)MR?TIU#b"Ych/
8E*'^EA<HDQ7S+!^5UK@fQ5]A,m69C%86s<pB<=d/,]AZj81o$`ZM[M:%cr6"-,u5?=[+k",<5
s4H""i^CcZ]A8eh&0(/82MI2_Xpjt"2kPHW0T*\lB\`s]AAJ8q;mEZY\>qVM72+5^dtr23)0qZ
8)oe%[>%DNqDcpB-or`s5!901UqD]A+%Fl>bh&JY-cA#jK]Aqi?C1FZqqh20a]AZ=#-)Oo!0$-U
bg/!o]As<S6YZa5.o+5!?H$,)MQ9H-">anJb3^4+p=U2s\dtGIT"hj'24Zem+qb*i!<`XWYsb
TYt4\<L99,J$:r7VFqu$(;9;X8JZP?6ntYZ:uCYA,qXRG0QBpHg3Un7s5eF7Cq*AM4#09l5A
eLTj^An&4E`<@i%8@/E/[*#0rjS9Zl!Wef&[I[-_-EB9imI48ma[a0s+S5goo;DXRk)JQAgU
mA1>bdH[BXOm8(h$IcfN<[3OZ:qg4(%o!t&)*u(L)a4@SinM"_)ghri:6.n58^?T"IgP\3iZ
chflLm^1V8e-G[Nd^R8+Lg\NenE.odaMd`Hl=PLd\l96%^7$bm(iDQ'FIE5$j-5V<@W]A&5gp
VGO9scLW)H`ohQ*iN#$HA6!m^*RI3qq.Bel<W*r*VVT/0u/O&$tQ:Va3*E<IBiB;"o\)%Uc;
4^nA5]ABl4_YG4!%r_h#femE=\.^,h5/Z[jF$hfqaR;)]AogW>dk?WJOLk[]A1&8/1B<q=FWMJR
K(L+*5NF"PWk/'r[@"B:#mln$8@fC[fAZ\+V-Z#^Z,g1S*tiab[7=_d4nEa%TF[aL(Kg->a8
[8X3qp"feU$a@%*&`8Zduiks4;*er99CTNM+Cf7,iW>N8W[5rulW*B`A&d:8+OqP]AdEeiR.O
mbjPiuj^>dJ$]As-jEaD@3OCcR;n,_QWFc$W@CLu6Q`NJ'!V3+gc]A_bMuWi152'6O!+>j&4#,
[EJ^'G[!'f>r_HV6mDrE:\Kt$OehSNKA$ZFuU]AS3ias#[kZ;Dp\CH0'fW)i\7mn";mo!I);;
47qVmQe.k%EqQ=^\-9A"(>OLGE!pR"/[ttni"k+">Assf_(E/#\)8&"$_E]Af<:[S4F:N1pZp
##.XSLuCmJd:~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM07"/>
<Widget widgetName="rqsx5"/>
<Widget widgetName="DATA13"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="2" tabNameIndex="2"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="108" width="375" height="647"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="false" topRight="false" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<NtVP$@glVp9!uk\W)QXOAU:*#kD=mF3TVPZV'K9581dL*8#JQ"bm">EgAtCRScoA;2A.S2
<)j,tC9*;FFIeEW?dVIV>;$Hd9+OpWrU4o34l=HIfEIY0Gi#g&'Y#Fkml.c@G92enD6a;,L2
s5Pa%o'pYBk$eMBnasZ;QSKCj2IMLW;p?VN&bQM53V<8#8nX!UC50V;;rHRt#]Audl*2n#>\5
5\@&^G#$+I_-n$/,!Vn$Y4;;djGZ$XN5mps4E<b8q_AFUE86@g@h#b!Jf`[Bj!?@%8/cH'fG
h,'NFieA\0[=@MiG%d\8f4F0I>q/W%Q8R7q6;g)9mAl2I>r>n3#-0/`E>8h.9@s3dRGY%T`;
5WQFX9#a?I$-eLk^%/*[[`2s@EF#u*;^?Cgj7%?^a`K<+rDSf`flI-_2ZZerEH$hubQr"U^o
;F2V\/UbFK6K_<1`3+6ahAeg>6i6bmY1/q$i!M\R+ZckV<(3MTUEXl<CoFVd%in%<`($iu9O
rX?,#G6V-_O'pT`uWr[G8s1]A*.gSl9uZ>P^+V5e?Xf5t**m@&i?oRj(9O]A]AHrS`t:]AVfJ%#T
/S*N6X&MiS1Q7EVY`#[JPqANV2$rO;Na.UZF]A-I.OO2nqB5I@^+IkcJ=%T$gCmR#8>`]A5/^U
K`F]AcW;@4oNV>=&t<CTieAL7QC1?bJ?7Y:18KR+#]AD*DkSA\#Or[9(E)EoT."!BC/\XGSqjQ
$#d<6bgYkh;LUlq*n$qDSKSp0.&VB`Btg9ad6\6"Q)%`7)d2"1>".P/E%8;L.$^&0'lp9jf.
_:g5i3d`o5__uTrq,V/:uV\$@bDV*=D(k4[_hIEHSiBUu1;9$l8>W,kTR/_8M)S\grD-l<?_
&HtM7,`HoZa578Tf^XCGf*m*c`c^r\lj21u,]A\9PieG;g,]AY%W#2B[>F9;M9@[T"3fBUEOuh
WpFF>AK$ImJ8=m(7-Z/6$I8d7e]A,8/M;b.M<bp6NJO]A6T5VR[g"kq=3p+$MED.!;YqhTS6a^
?+>Q))^_##"]A;uCLQYNUOpNg+-"\&.r\)E+qTNKE%geO0Kja1[ju;,0W;Ktq'E.9VG9GFIdr
>-SQZZ!6(H(&ed9]A`Y_=Fq>.:)U6$,j(SM/J'+l@M%*]ArIO7OC[*BgB,md;?h^_P<N9.EP>@
3)($$!7B#JWjgXL>6c3;R&jq)nL]AEU0A_:<6MQZr^Z<Z.!P8.NU<H+3tAB<!?j#$o5q$B?d_
IZ-9F2F^Ij>a-5PDXh!$2Q0]An-X$Bj<Aj,?dV2*"4#eE\uj\5&E4]A#hLlS4CrD#4d5LkbGk(
]A&L,euh/tWbVol-]A5:!5-gW^0!$7k!qU>bTF]A*9D1l03$et5g(%B5BkAF^*=\\Q1FV1/;<:f
CeV1!e6d4'64m(KY#m"*E-q(u$V7oJ]A]A*W?_E.TdXs;G4ED"[<q*f()eU5,ZUY.Qq;m9ZcIT
Tdgo#aS")fHfGf%R7Vb24hHYU'0UcF&HWu##KVgo.[+A>g$)m)cDS8Mb7f$C$FXM,F7.V<N-
D>MYPBFpIP]AXiat5C@WgW83<2FgG_#32!4]AD7F7(M/?D]A7)]AR$p\9O,8^UQb%t_*d$YPl?gc
Be[!ZZD86A*c!GKahmoeN;cJg6[^+&!/$;-)cg&)kV%q@P@:su]Ag@@BQ_-R)`NC^W5:O^KUS
RgiYnCJ_f\$0e+N5<74qe^Fl<dSc1ZF<$F-g1^1jf8A`LE%ffQm&KHbIi,,9gP]AKL\m'HB0'
l5?g%L]A:e+kGIQq/#lR(9TYgf[:@bb!\1G.$#dlOEc&-!D'Wr\*\@PJOmnA!;@WIkC#[7liC
cWm#Y\YGjZ":(75Xjgb*?OFAOl1lD(MA"E\l<FtmAC&<mK$%X.51LsmE:M#]AHL?Q(]ACU^+:*
<2QhL0QWHpI5CY3$QnJb#o+K[^19\Y<34g"@HJ7s4[6!sVrXq/i@@T`a8eHSqJ8eHfU]ABD.d
gJJ\cMKZe<SW:["?SXY?'X#]Aa48S$j$-Vn/'oJJRCccBO^>OnKXc6@Z(M_`0E^2slV$b.mS!
!$G>]A,pE^7b5.!%QN)PY;IV?g"K-/*`-\Aq/dLG<@Y-.XS3f_`WO"Gj<=B'<WSbF40H*3`*<
Ocq7YW+P'2^Sr[Yt9g'q4M&YND/422]AFXp:LJQ]A#5(P&ej2._fo:04_6$,i._qqI"ePZoOVg
KDRhgr*`:iG$jighr=rTNqNpscbN)$*=D,K?X@Zk'RJ_fnBK6c^VZm4c-W$]A6C<Ag01SG.f4
KMK=r37.*\2R>jn;mD,3h)Zf7`ct3BjTVBO$:)q"[rYG4Jr&\tgB->7^,&5("2&7rS?Ik-rS
Jp0n5j=VMF@V82>b@9nZb1!Wm-6i3"]AOA2ChkQ/r+pF94RKc-#Y^Q!L7md^q>6!lZg`DfrnG
N>9<EEBO$8D(u)f8f"h5*\t@J2]A'u@W!%MaLGVcXu`?TO7=?p/P*F!XI&ZdM/+DX$iNILgad
83kIeM#qO2mGF4Obkc+HZ@`quRFC&T$SjW6taLp+W2?)#fWj^%Z\G^k;1b=cb^SCYnufoT$)
m>%8UA&@FC'#ql%SjZ`&$+L*^B=!`@ClZ*qjsiFV'_c()a.1Kt]Ah?\Z->F/8UrM-"eIjVgau
nT8+"]Am5@`CXINItqcRgoJR<>o&DQbX$c?B<s<<Ve40s)E+jrbb3*TW6;9%iZdY<Y;/C4a#!
g5bL4Y=e7BUhj6*)U;*sAJ,"JJobC#cKRV/,fPofpLes7.6oba;qdDUk'>;18d!td=k3f26&
Lq"pAZ@p0dGM:$dmn'L1H?'7Q7-AJYZJi4W5dd3ZiWauE0#4<o9i=9/k9.!V2ABR2e4UVLno
",b=]ArfH="Z%8r[W#/7rIc\PofnM?]A2H[TSD3cGu)YinKutQPVNBko\'U6[0e_W#>2c&uP\Y
@$a.3B"W'f$g"8bYpq?DU=u6&S#SN[@r+4fXjD_C-(&&ggJ\ASHgC<kQWIN#%[\I,Mqdk]A6/
i_g^3h-ki^=j\547h8@[d=:X.D<;Be3li<J&S)<,87rT2lZ:(mXfQ3j4Xo&ABWLbij0W%0&1
P5).`Z?3'#RoMWI%bBQcX>?tqM/p;-43K]AlBdLB5H&h$g;Be(b9D3q1Sq'5M;3pljAMbShj+
&eeqGbjN]ALhQ]At)7PP=nZokT2`DP\0?lPRDtMV'HiO0a-",6fD`qPql]AIMLg-#nFL&GIMB,&
L0+aHV_e??W&;Fd@leJ:1&7IR)D?ngJTq*))6enT_!C46e#NdJ)7Vdl]A.'JELRXg`q3j2FaT
N(l)$Q+\&U]AH`!bYpVR`nn\ht)-Sm;Wg"I*2C$9V#[fUa,/Y+,O.?c'5s\<T$XtfN8Iuf$_*
&ZWXsc@YND332ebQUQjhVe]AdR<fIc!G->K$`OspJV'?1<D;XI53:Gr;Fp/DlL^'I=9:ihD.m
$s*/amimfnePOV+nIh/QXVbf&>nC7?B7,tl3$:t-/)4J0ECW7XcH3lkAFE$Q!FJ]AnOU:M/_$
8VW>;KLWYK9?P?Js4PUjl!LM:TgV*j$RCoV4k/I3YQrLO?ZA[Or[`?MIZ:jn*6Xh2;$BG;s9
WFr&/WY4IlC3$nIGU*neSDqS9?[6'H;tI!Ur%Te>NGA3CT1MWphblnf&,7c2\71)ZS7?5hTq
GOLL<hUD?ooRN&;W'[!A.*S=ZIOnqZ"2Q;38]Afu!I$Z_FS8QP@8AtYL-+X@ebpRHT2j:<&2/
Cj!@<:T]A@c;"Ol$_YMHc%m\O3$8e(k/n-VW<CSi">3VE50J#1%O;WIWmfBCN_X]A4r/Xs&"c)
LRqaOtpR03fMhl8kA[(".hcS_>1JeGqgEfbr2\fc=q/WH"ELQ=QkKrt]Akjq1.3>/J2Qp(8Dh
RJomN9krU-FF'k/<M?Uo;jtAK3mlcG8g\)??s*A6r#A)n*#,#(GSfY2Bi?\<QnOr^Y(iA_;N
/X.M%.bE"T@;-bSWrF+C]AX/(2N%,f]A35INT,3`ti9/bS&^k/@N%&]A3]Ae9.e4Nc*S523-r\V8
$JAM[G'p(_Z1fQq27'$<daeMbq)`JGfn[;iC(r\liNfV=/Z74AakLcW!mJhAAf\Mt7`dP)kU
25kV8@m$$=SUsm#?=@=&^LqMf7U:)@hMnE2`A5*_.+6<JY]A3R58;2ojRBTK`H0Tk/6Hk2hl`
&V9XJJq[sdfiP3eIRV<"=[j%2VKmIQ60A6PJ+fGH';aAS/:4+0`0"h#M<r?,q,^(kNg:2J(+
_c.hTM3F7pBk>CK5DiN!dYE3B(H?-jLSp<C%j@T?GCO?Tt>rlBE^Yl0;CqdTIpPt/+DM)*4c
^F''P'U$_1HLVP!;Bi6VGtOQ.SWn./Y_@chg5-d:<*L;3h5>2FO\l;>*Qrq9g<%Z8&!)VM<#
]A'FS@<DJ$<$eL<hJ*ob$ek'T(Y2ph[n'1Jk&]AWg!h7u"\$K1^'*WHPSRl)Z)h8Qu5PNV9f*'
5!FLLl=?O5d5T''Lcbei<?%>ru!5mGpf&CI$]AjVmbbcXRp5<Q\6Rk@#"C<dmP<"1]A@IQ:&GB
907@IGfD+aeZend8.s1i[3+LMQ"1W%_1-O'_Z&.7#DUYG0Tfqa&`un6>Em/!+mcsr2V"D6t]A
D*).AJ"P^.Y(:l0F^4<6A]A`Wqth+61C:'MG:.r]AJNX/rs#]AsA*jau<?O.OW():0^&ClN^I_6
oVrbPG8J)6m$QSj'4#L*CR[_L._jcYrjN'@=+$Dt(H)fJPJ6CAiPmCP?-5upe`XUM"G%gL[M
Q*Gd?I[90;otlL4)hcK0c)&@W@Y_7QS2@uQjAZLPYa:L86E6Sp*NB?:Un@DuS3;Re%shO'.L
'&qOWa!$jdKFR;77mK,eqC/+X!aa&u+OS(E'/B\W'ATq`f5M)(,;7<]A/1iKYcS"QK5fVp]Aci
;?MZfd(gncVRFMV\")Uksh/FS8dAYS2O+muBgZ)M=UH1IH@14hi8pZWVB?tmr=W;QF6W>#Kr
E:i.?hIE"C^2Tt<`W=a>7skt]A/#jt%)pW-;>EO\>DaIuA.,]A.c,?l^Z3!c,3Ctc']AE"lN#S5
%g^714cFgu`]AFQD2=LcMKI,rk="TX4eYi%q-T'XFX)+";R7ZQ8XBD7o1QUPu0S1!=/U?UIl^
'7Fj1)+/&]A#:qCTUdt-6BP^_jr#.tkChZOigEJ"(olV&(i>AqaLAn8op(T7QX)`0d=<4-ikt
<rOf7qS.@2&$&G\%'^mnF0.YEJ_&9XEl<3KO+KKiK=K'4'?-demU!pdq1Y/*stXZbsIk+f_7
FFGQH-PPY'0.kuhnK<=Feg.RBnBZ6mmdPljAL29N.P=-W%Gr3r&I/%W4s-S<@(bJ^;36%eoS
KT2ef3kA6=n(d>P'q.m02#(A#P>).-$sFTgJq1dMmDEuiDWP$4X4["HQ)tUR@<C6I`5!IUVb
7bC`@?!Tl?Jr8o>?60Ok]A2=i?hspgTr=;G-'PN\nQ-*1\hr`CfRM``$o.ig.&To+ps1Y/*=l
?/K8.:*%oZL[LYE^TVI^McCBh0,$,VR<tH]AjpQh3rXSBYb7JHAr$]A+]A31oIcA0,80IIO">YN
6)MB+rV$;Kk7/.`rZ*6OH2\(3E8p9QOBRHfX,Skb2QprJ*[p#/K,(607oA'23B(>s6J@)&qS
\/DSf&ARJ6OMj,RN\VJn)L;s^'D7%qh7S?[qTJNdCnYMfF[YG#i+Gof8=illi]AA8VM0'/"fs
$UBqLQZ//Df/:@$o?=U!/2C>WLa*63Cph+/NZgB%D]AotT'(:P.P/%UoNC);?39^bA<OO0<G4
Nb52f1ueZ7k9_rmoLW>\jDpKug]A5cdp^\[:lii[@8d>>WuPl3\C0"]A0jCgtb>bpb8T<8p/tC
,BO";HpYt\_s5\qD?$F:f-7ZPVO!1^UghhH)CH7AB!>*qir!X8`XXmQl]A=:k83pA`a%fZC/>
B%s;7qAR+3QMGgmR_CgFY+qe[>6OlZ.G&Xk:GoNYiQSUD9cjGT8#X04^WeU=jf2acFu^l.n?
%'o4EPl_i\BE9)Up"&78dl%ou\"BD.%f\8-25DC.+mY1NKAJ=38)_A7XCVEscT!;9kHX8\dG
#%u$bKMaFNs:e.KMK-M/,Sg</;Mn]ArL!MOs5@2sl,D/<A*ZBaVr)?1X<ClE98$Q;-Fr[W_,F
#(]A>LC;F<!oEU=L\Q0H8:(kEPa]ABU@u>#Ani$=gbcrE2o8%1>K21^Rp)nWJ#?HF7bH=L"FX/
=-tU9<$:f._HTa35GfBF9%k%qJnL'Pm$C<0!2%:CK4^Zo>AEXmlRljGA=%qZ$qW]Ac@+6HM9n
M[+.5E>hfnUp5Q$(V16g6f9Ehe@']AHRL3/t#o.;DD/-.92[CM!'WM_nX:8l@!<pe4^2f70/-
CYeKZ^3&B`+^t#qQ4;01*B\U<@=fJiMDHouu47Z_[0HFm??,%:3:\'A9a[QL9pRFW=!KW\%%
ppR^-e"p]Aq)fNH:W@4(+A-(RjQM-i`l/R<GkOR,$#sX`m%+h.@njFpW?aE\o"dqa]A193QLQ<
cHh>V/lV";HUAW(kK8GEjY!Sg.mg(HoSoa@_EL*TV+)KEj,PP6'[;ZjUgo_>U$j[OjLfSDh,
4F(eS+eU,B7$D84<EAh3fAPpBE%QXd#^DMLLAnU$n`3!;lFsn?Ba5culQtFo(5(b?LPsnjKY
5IZj>3JaKXDOH'kKil-#N5$?$ZOO+Ne!jI;B&HGd9(>Hc;ISc,l71)^,<A-C8&N0Os:F[+H0
IFZ!GEX2@sZ./`:Dfn?X#ad^*L*Ls.CnK(<=$^"lg5(NlU/\E'&=Xbsop`T6WBDO`2oOC'=O
o>.F1pSe[J4Dk!ig4oORDMn"Z]A\D=L'Q/3G)p`]A@=h[&BW7(ZVZnV'U/P755%0SRN@>s"r>0
7i09f*Z(X"P8h=T!dUGc5m#Jp2f9XQu3-jh2/pJA[S=c/B5+8Kp;R54u)Y!42d:2m+mH!GRh
MP%Z<VqgbjV`[OpQmN\_<ar\/+bY";)rf8\\W,#?a%lKZ-h=hhXW[0pNl%JU1ef<3&Zl5lEu
F6?NMe^+*\jWMl4&2%%(+V$"ZE#9F#PaIr*sb,Z<?o-b&[)c_1LLn32K1T@Fi2uL5TLK"c-i
.YTmm#K=1<ChGjD0g&>/f`ndna7Mo,H;4gm%s/VVHTau)/n>u11[EMS,rUn^$%6-i#Z!8Ghs
2H0$MJR2I.:C4Xg]AS:Ckd;D6R'EQf>aqm)QPXPq;/UH$e;Zp`#J,TEIN&V%9m;^oof3sW2R0
SqUn&Lmk$!E"#;m:,$[l+fgB)`$&4(#qaP>NrEuq0d@&p"B#gu=n??Y;''t?Vim!4%'FYaoU
^2-Ajc+G;IDRWoFqV,@X&0o([^,su(UXQ2dl[W@.m%fi?270o'"4ngV,dXMf[oqQmN6R>"/^
TiD.L^VP5pBL'<WO"O3VGW:YGCEmJFscbI76)]AYI$;'[/[!Jn]A!,2>Lr7X!8(CaVA&[g=q`R
*e.>FZe8Uk!8J/]A4RGO7OY7J'TDsBggk=1Nb(/ha6Nj?cHdL2l1@O2_mB:VDiYH`H)KMJ:Ad
2AZ(LAmNegmHhCZ?]AtF1@C>K@:kG>]A'mhW@lpR<.i\9Tl;65bL`uHQ)V-\dYm!G^BpYjjraE
h.X8T.X)51"ZkPsL8h);m)*]AEdDi$c>3NsYj\DAKV0h`==>Z'\bRoY+&F*g>4aHDn9R6XUsh
o0P(eS4a3`QDN`^N#1CACWFX6Zt5`]AShC,1L.RTR)/fqI[G8Xo"4`FsNC&PWNUJG(PMiL;7i
WiB$[1(ZgWVgJ?\6!F":]AI^&gh""34hcn:_$"*UMR_4Z2+#n&am:dAkh9ZMn-o;B>g4Z*fm3
,EIIYc'LYeNZE7]AIm)%Z(crXr^o$FWaJOipP1)4_Gl7*Wi!PRqJ5MiC'oUJ'Ml5jB-=Hfh6B
CUI5W49Q.2aK`BR._+-<$LLA8mA)BSddkQ3,Y'orGruabYL)Hac7(_r:Q\u'o_lQlT8L`d3!
/=S(\D(JT;PE6XFH09HRWb1hn\\cXY<XEO0)4O(a,WCD$RAO-TIY/8f4AJj!!gWU'Pp@;_]AY
ft?D%]AaZC,K"\>"fQE11RODX[!;n0k&>n924#euP*K6M`Si]A8#%eO^tnlNP^R+DkDbg0"Id[
,C+!Q'pU^!]AoeT=Eu.AFlpqf%.C`+<EMAIL><)dRV0]A\/1n?5Au,Kik'FrRaee#ee@^Z
0?D"V=(*lPFH8+_9bd2s2^ZURg*Nd<gZ0GO(8&:o?RV\kXmr14cRiQ5rEm0_Gf`#<IVS5d(>
nuBDa$Do%1V.]Aph_fFsZi^E)&]ADTX)\UIk#gJ$thp6(.pjSWT7_sAL%c^2K+4aINeu+b#JP%
8BLQ$En2cl,DH@fe;6^u5j=jMPW_\j[H'6-WefSu,::iabA3L6e;j]A37e!JH0tA-/!i_j:\+
HK&0[rHdbkfu>_69UH&q7*qu$gd>Tb+c]A<Nl+;k/1eDU0\Y]A)XW\C<:FIltM-@W\:2D?\JV3
;fiPk'[1aKQLM2$Lf<p%OpPi8!XpOAPWI'l1Xl7sPc5PVROf`>Vf!bMHl%\^I"B8"&BV\lZQ
T<oAS.]AmQQIY!?/@kp\89or=:)&ijKlos@q]A#Psa%*"IIhT\Md^A$-i\PqI))$/KSd6-]A&#B
d]Ael-g==.3tE,@R>>5SSg<@TdVFS]Aig=dI9M7<52KO>c+*WF[m;JKDM.>4\aL"_WN%.Mda(=
"^?5VmZmZ-pq/$g:RK9.?P?W%.$s7Ka8`FQ"7n:RJJ1hB+n;^T*q!-o&+,dZ<]As%5o(Wk0jj
<R[)p:3l;dHQO*$#<hjgP-@nsTAs06nW8!I&3j[\lj+YDa8\!'LJ8h\ashQS4pJ>-hSTK]ANB
sR'Ilh["TY"mJacAB(G:7i?.3Bh'G5S/An0pVXY@"fCg5buKiI5k`5WMK&)sceV(VXe!]A`^"
)9CNS,">@San.NA:0>U<N'"e?#_+X:t?MK8,RA0EjPplL92D88g+#ZJ%T=DO@r;!2V?^o)cr
Ugf5">56ui"EO&/qH[R'"A&sJP4o6\-+F89AgGm">59*/&<=F[lZY&QH,":I'Z$IqQ9,JV-E
s%~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg.png)'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="kq"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tnm_c_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[jyhx_kqfc_zbyjdt_kq_qs]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="40" width="0" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="lrkq"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tnm_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[jyhx_kqfc_zbyjdt_lrkq_qs]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="165" y="19" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tnm2"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tnm_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[jyhx_kqfc_zbyjdt_kq_qs]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tnm"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cwzb]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground">
<color>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</color>
</Background>
</initial>
<over>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</over>
<click>
<Background name="ColorBackground">
<color>
<FineColor color="-868637194" hor="-1" ver="-1"/>
</color>
</Background>
</click>
<FRFont name="Default" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(n);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="20" y="23" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="215" y="22" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="329" y="22" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="Date">
<![CDATA[1717084800000]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="215" y="22" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[8763000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[经营画像]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mF?P[;cak;nG#>t-qmq!9bAT;TG8"P'eE-`ClrI$$:Zib?:C*3!*UEE6S)5?77"&;ZHC)%!.
"-/G_G7;`!(h6:-qJQ6ngk.8W&T4#YA&N;ZGMN]AlWVcS[mXG\\IU)msOl"gS[jCDVb6TAdAO
FeaN:<D]AfT9dIOumkhX=.YPn&(aK[7Ea1R%&SkK[RWGN7RHY3-U^PX/7/?;Z]A6oYZBId%q:G
ULWPamV@AIE?b^mj%#Y^LtD9rp,Pd)2V4:049!BV#@d;Oq7F1^Hh8((F1=T(!UJ#4W0-2_NC
g+Pbp^P3360?"Uj%;@?s%Co&\&j$gA%^G,)j!).`QSIP6_gTcVgo8M"6;Z8*'*,JsS(1rEcK
Yi"$:eTkfJes4$5;f#ua?Ym(^AGO1DYk<8=\eX:O2((bi)JUlMN(DFb\ua2%7'=/aN:C$sO^
#&XBDPaXL%?0[s6X:uLpM(6Oa3j^@MS0*->4"0)Wadqp-(dZK\T&UXI]A`2\L4(Yb[8Du;:RF
#d>AR&?lpmRd1?lm4!21eY1MHp5*>OW.k"G@&_Rg'h`P\:7kNFZFnQI,g_SDT>uaap-"eF30
Wt=^*-1fC<4]ANL18dcqDQR3_.pkkbriVs0c1aE43X%pa>K('A169?IjJmQ#HQB1'_I:ZhHc?
k@K^!;%;r6]A:NjW(@7h3#9'WGk.f45DAm$WuCjp%"CoS^]A:?^s/[+hLSbQaMZN*47Jt@>]AX^
;ng1F=)-K0"<7X=Tr-M%%&"%4:1VI<bZS0D:g6l_i\OfIHWUE9[YpClZ*]A:U96"V)(VK'[Jo
j^]AFM.tM<a,d)BX["EO_<SX#oN$/`Q\quE8)f&.W&'\SON8m^=O$pX\J2'/Y"JZcJ>&u,eME
9^M]Ao7T47LCL:VRgg-8Lj@e<Ei:jQq@mAGd\r)+DfD`4ZnAFS=<Eh,b.ZSR1<gu?>hGML*f5
`+C17i^M3$Lh#nd(^sb\;R`Wo>X5f8Qs5fWZkZEB5.s?$'/2/)GfduVJstmgl,9(Wdh(.0&g
,ojU/M$I%?WPpE%sE9'dTs*)u[/pV&0L9o5C]A\rh1-6UsT!2DEkIl89:[BCTNa2JBMb0'@bA
UCS\`9O[8Y(D\7G#I]Aqbd[L(^*).;1e[eWI($hO]AXQAr)n<J;&FY!2a:C_s/<[c?N2p6TU'9
S34m(6:$4<,@o8!hGu<Rh7(B>rgBmJ+LNil=0*2QU%VgHPI4;sBc.1-G<=B2iPu%Z+gaX_G/
0Zm_:DNK52a%eer"'!X-&%iOgt>JO_rgHLg%#=d<No4gg2m*<.'mT.qH+*C-#b7J!Z'%W#![
,"`bnkgEbh'&OS#uql^r^G?ZMpI['Al<@5kRMXS`OFg]Ab+>Vp+HMGUo08BEB?k+5$-;d.5pg
]AS=8d-Wio]A^R]AO^ACDL\24A@C#]Aopfq!8-@nY'YIVOq_V)#Zu+r;+LD'HE:c^f_F.G&AZ#of
Dl#1r5=<sY_PcUJ)6qYqc:5YQN5N]ALLSb>+5$pe!-44CIq/]AP6%`OuoGeoJ!*:EEm_e[r63W
9u_R@E</niK_M7s?UY.<]AmkL,G)DPaU6Xo+tdEZ+*l!*kFgLlu;0ka#U3d`t5:dS>`'i4bXN
Tgl#*N0LUV\%^fdSg'4kK[-#?02-nt)pUSMX8$=#k$Z+qci'b"T:N!+,9D/0X[7&6W.u-)_,
$&H!U9FQ,WrUEh$OE]AX*KM;GAftNWSqeK6GRb_-4I3JB4E?P,1hR*`r*`G1h3I)uqS%*JCZ,
,ZqFF]Amhu7*+CK4`$;OiEej*51gIAIu!..KJJP-F[`bEsEp->o!+nk_=.]AKG^m'0'AH.La2'
/UjTKPPuWPmj(e=,jTl"_pcW!r]AC<@p09TaGLi^!*Mb&C.*h9h-.\&8\DXSt8N\a!6t0oj<F
q)qRS/NQJ6tQ6P/:U_k(<O=a87Og5"`VKo]AVBV[=%R@HBEF&-0!Oi[8%0skAHj[Ue>O2r,[m
F#N\=cT8/Mm&5#17\di(P6o_;r+O-+5As_'Y5$@hW"$:2'Dps<&do$DEF)H#B4Fp)"+%^dVR
q_]Aa/m_1i3V1j$G:TB>WHW]AY-DD*#':G_a4j9t&lM->;0k9U6\L?^KB6]AP6i)SW)aN%DbFpU
a1'[VY*X0632?jK(l$'b4E./gdBDD<dgA4qGE5t]AChVRemGh,d;)mm`*]AN)npXp=>7k?jCPU
,*H%Sd1$K\.t:L%k1$MH\5uUhCe1+uR#kS_`Hu)9Il%8\!]AQa@75"'MlUcoWP>,lf=-7WQ3#
Nb8Mo/lV9W,Ke[BD%.@BqXnnl<jmZ=H9mBrXi!*q>u9Q9ZllA]A#9-aso@#R#,?r7Vk)kYM$5
SNd8V>$Fbi=-"b@X1I_m*Wha&\P:]AG:O@NFFe="V'kd,5mUffR&*eLYDUFK(R]A>jSmfFif7j
+TU`lbl2u`NIar\!_JiaqAIVqp8:V[dJ"$nK3PF$\aS6HMWUR@:T+J_R>`>*KrFb?$Y\j)>:
i_qu4^eE2l3I)YP>qdA-0""EOIPU#.#/$n!]A=X^Pc`lOB&3Ct]A"7!!*?j!!3^^!!FDE!=1pj
!YBk^"<[^C3U"&g3]A_?\Efcu19=g8?^A5J&D33Lr)KLP")ID.4ah[iAL&m&.gQ9SA[3>>V+$
Ko`J,p'd!!=(R!!Y--!=W?9!uSfR"u(Q.bl8u1FF);[ft(LkYI"_IWEK[e"rP,U"u(Q.$t'&
;(r-+U0Rs04?iU9G^]A<MH0rNK`N\:W!-hV01?t1Z7g7MqM8$<VF4?&lM]AH$`qEU"D>+(6&e"
T~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="164" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="52" y="22" width="164" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 

setTimeout(function() {
	tabck(objTab);
}, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="true" aspectRatioBackup="9.868421052631579" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ImageBackground" layout="2">
<FineImage fm="png" imageId="__ImageCache__E06A636B9F69CEC5CD01541A3C3A3E5F">
<IM>
<![CDATA[[8^:.;caZ]A%%;u=&?,\`->I+k`=NXH_!Hq,&?.EfOH<EF'UPa)!Pr6DFGU8L(/h"R=>]A8o(4
r@m!<jIsBUB6.1f?T4KHO\1P6C,30E1b27t9AV_`dn`Zer;PD4f^'@j<KK"@8![pX&gM`%V8
#ceoV7hh/gRiXo^@PW-iCn4_e[=5+&#*cmD@5PCITRCk;cCNIPc(PCMDAmskN:G&8bY,aD/H
ZH[>b,Fu2f'p,EdMle4r&$&3Y*70MZPuRaDL)#i0p/dkpS[Y\aGEg2ppu$UZLDmS5^7LSl=I
;Cl*1h#R%!:7CMLIHqFi1B,ZX+Q89.R(h2RhV/SatPkt01!Ht#cd3BNT]A3I=%4gEQ+GE&A='
S16V_f,(Q+GUKU;D!W?FXRnumpR%9^5AYN/F28S@K3:)=>c+NqSa.7VeL%Z'kF9>I$G!`eR%
oX9g@TUJ'341pGs#)8ItQ1BVp.TXa`X$mm<rpH[$-,\s#IUJ6"$0D<*e=K?@e#GOK8-(UKpe
MSA0fReKI5TP\L)n[,?2LdY\-seY(dCc(L(gKAD6LQ#YfHJ!d@`an@74n2B92f9TKtpQ+oZV
(Q)eDp_C0_MTbN4#maRa]ArCK*>OR<_B.gZk;XOfn:2>1e3k[5$ooVEmd+B8R^_O[/r8^fd,&
iikg;/?Q3]A\QWHikiCqR,<:5X?gYO*FP7E8s#2&O9XarS+Tg9[ikk^M(5#P?EjKoJDWZ-+f,
<p)A*3oh^6002mQ8g\oel5=i;C8(OsVpAfE*;O-"^FWH3COGm>"73FVTu0.KO;4Pf@Z2>14J
kV?0S(^3B%oef"&.4TbjE+:<EMAIL>-r[W^^45pjDgU/GZG[HOZ'kju
FK:FPlX)&uH07^3]A-@35Q&cVk(imH;D90l.=*@VQ5oNE)a^^gVKFt:F.1=D?d;4mE^SFL2?C
5r3n4;X^&><qE207q1]Ag"l?8R^YYZmcGY[;0IoNDe2>D1^?:kofGe;X:IYtRI@2[OIRAa;4%
CU&Y&S0FN$RbP(j)fVbF*Hf`L_[2A7?]AB\i]AQ=`X]AG9X5&GO6/&^/W\u((RknEApnZtCZd_\
e1tT,?A;Te8"`M(%>TQF36lYiGEFlmNC_Wn>)'98NaUoj3?^"HKuL;f8YKh.p[&"CoQAPE:@
&Tq]AI8rtK0CU$lu\C'+)kFtS%'!-Cn>KL/fJmDUClE&;]ARo64<l/3`Am-qE)p\.hDjXg<_0d
r)U8oPY6ge;<G-qnb3(Y21.R1Z(BG&3?DWnYC!*M3#o9(siQoS*8RhJe4!\^]AgA#Pn<;-tZ.
OW-3L9`KRjG=Pb1NTss?Ju8lDn5<si`HOqB8#t;q1:_TG8#Kk\NVVp0b'Tb#H.1-92(d5Ml7
AN4+0I#Ua7gL2`nQ)")S7p54=FJ&=:`i";4O@)6kL#1l&aYI]AkC$5ijH1-3u&9GU4@DM?;\%
d3a(YBEZ\QC0@;.-iIl,k(`99bYMYnQHuZV\Xeh!kmFrnK'NJ1'ZLHZo<Lis'g.p2d&HCL)L
?ioYkA,!fMYdh&>.GuMM=df<tkrZ+H9oE.4"5Q>O0l=nF*ONd!%%(9:9ZjJMc2<19G'6XXcj
XJn+/#AL*4lIkX0TD"3k(?%Ug_\?:AF(X^!RH$XK-,NhV^8l'DB7B.!mIU]AaAL/!^VbJ5njr
LiA&-QP$qd3@1@:fX0A]A)@8+1.L-Mj!3O^LGCI,eF0g6;I9ed\DFjqBGEU3kPkV~
]]></IM>
</FineImage>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.8" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[228600,685800,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[647700,1496290,1496290,1496290,647700,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[综合考评]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB0' style='width:100%;text-algin:center;' onclick=tabck('TAB0')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[经营概览]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB1' style='width:100%;text-algin:center;' onclick=tabck('TAB1')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[客群分层]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB2' style='width:100%;text-algin:center;' onclick=tabck('TAB2')><font style='font-size:12px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB0')><div id='Font0' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB1')><div id='Font1' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB2')><div id='Font2' style='width:10px;height:2px;background:none;margin-left:43%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="simhei" style="0" size="64">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^,S,"#tgKbVK[Y%bXC0K)ke^R?)>&CZI9<0Hj7^\IO:phSp<@r&&SSD#+7VT8d=Hcj=<_
5AFC(3KrQ.R::,UANm3qN7Ijugucj.'b-S``#R+'u/*FXQYT]AA;B1o=_;bLQ;'d1:rfqDq_Q
ik1nWgQSr^70spqdF2sa2$2E0DVG2$%F/Mc<Nm+(8Q6IAnI\AumG4<?(/MA3e97>)F6S=3Oo
/gnYmc(4pVg^`YC\KZ4HbjFlM\i2XmT/&ch2QZ.OaPK&a3sTj#3dG&B[4TU,-^#BBBshAQ$"
l*$HHlul$CmJ5p(7]AFgse'4[!UcUlDG4]ABQff:6M;&CMh_"5^U(`84!$G6<qCpcZdW@5#ZoU
3?@sn%48K+o='IF6\!neJ'Y&L2;MM:MU*3NAFo/Nos<me11<&Aoi?QCN)eR"ptos_5Kn8Kma
ipiZ7PKBk`LAF,PS53WjO50@f7qBoZ3'[s-9kNa]A$5eeLmG[\QI?ne2*-B\n4(e[13,mAPDC
jpKkab\2O@4g!0&:F<AP54Y4dbCqA&q_a>6ARgr2R\p2`]Ap=I/u*rij%Woj?\G_[!g[Rf:E"
*cn9#uOGhEGdBbGFo?;l2;k+Vr$qsDuV[D\'9PB!I1&bE9KpH)pRtm29puFh4D>LgUq\g6f5
=Q:_quFHeMr*n"MoBi)7@C9:hl6qO)h/F/IG`I#sCECQ6U/m`f*KpS,cPo^MNC9I)O(B>aFm
S@Mct\aQ'"[MW6qC2CiGkUe-3R\O59.=fS6DiH:IkF'edJ9$E\#*EQ?3e+W;-GFZe?V$U@s-
:utRIKS"hS5O]AR\rYP>I30h3><IBjhJ]A8;':>b^Zqll^WH@a&BbfM^gkOT"Pss$S(cR4di/u
"1R#&j^amK10Zr=k`1DuCnKhiA)bE3cRMS&&38&[/LDhgR4*^*9HPD6BJebo&O[d4<;sj',,
54Bj#o6LO0PP.ADcuojBV1LhE+e@<l]Ar1^E^N?V\;YMG\"AWNPm:A_,.pss=^H5u)?#rmXD;
KuDVWGhInoXmOfJaZ)=1!)L-U*+0@k9H?Hk,VnRYf5b-!V(l0!+4`r33g"'_b-+6(oViYp+9
W_f^0\E,lD"GMBf[ku;TOXarYZaX03eTsgNq@_UpDELKk@ODeYf\2Ytjcg07"f1'A06nd2MH
r!H7o-2#D)G$Q@hF!FCL6p$3]AFLLZt$6j:U?*jN32+?'?'ArbVI8Q9;D<MQ,Q'Y+l[eiNg9%
0jX%'..>kW$Ki'`9qesp4pra_'3lHO+EC1_#/ncqsPV&^PKMit`g>R.YR+te.\uIK[[MNO6n
LV&p;^0@4;I!0t';h/,0JAX&mO;.Y!/9^:l*,=UbNkW&;7Cl*9%7a,HW^(jchWa)"L,V$26G
"7\V(Xj/s!T<Ms-alikhJ4j\p#XC8']ALTS_V&#rWJhLe/2_Dm-nKCY)Q#esasXGbu9`-l\0Q
n?@s)d:H933ZB`6',puNjSZuoCO7q&)kBVg#7&`^!?s_/a@qn+8efY#I:7al*s@f91NSgSh_
6(7WdX&aQAZh7)a2=tkRn$^j%II`481?4h]A<>[)\.1a=_hZ1F.K@>@"^RSO76S6q/>Qpk@R[
*ZYkpBe9<2JE<&tm!Up8ul0)VPA,.IHMN3D-7V3FbF+-a]AlE(dG9^SQ+'s^r:O(S:MT,JRgU
gRcMi`Ysal>2h93F]AQ$&?UlXN^i^+=\9;67Akd,"Pg,r'$ard8$3P&kS:u.Mir)&+NQ!jTQ#
6n"_ab$9:>?M;!:*4H:;!NfL7!)%u-tY-d)T!.q>m.J"cD370k/%?4B5o86d)[rTfAsl:\fF
'^F!CG(V@YW:6l(WE]AYIHuoD!T1!a]A-*+T&kZf/85NC$a6E74'(SPi@=O<]AR;D%?7&QNo?`k
Dl!HaDG]AD1R=>S-N:m7NX118'X-IWLR!>#(#5g#-(lTUnEDE^9j(9WV1-"OW(T;V%QHgpI)\
%"'E>Dpd-"jd5j6uQ4UB^5@>Q)0tk.A0eFi)Gq_NAAcf1R/t,a-UDSa&\!59pXej:pf.57'@
1513>j-==5D6UJI(?Mu&%\Ln0>)m(M))XH"YK".*-^j;5tmo+G%3:o&mJMWP2iF6[4$M_c4O
oPg,N0+mi5LQXpi9$"KOtNYLbO#74*_Q&*/f:(&Eq>W@)_-P(bk8SV-7Xej@ZG>0`Up6`:R1
&-.h(VK8:#Dam+W?D_td7%#u?dJ)SXM*1Sq=1QlHX]ALZ*!dKASp!heT4(O(?53>?DNSg/gjt
Ta)qcnL$fIU;tI<[TLD"'i89G+rH>t5>W7`>"qr.V6A_&5fPZ7oo;3B$4ZD8`AJD2CBE9pd+
iV"78grR?0Y==72s$%n$2R5*D$ZSUqL!:!c:-4)-QCci<TOCWSNLg#k5=Q;9Z.ATOPs5BJed
%gf:mM.b?m/!FRV,lcs+:AMNco,Q"7g4Hp1f?g_P@@?q/%pV6TEF,m[0l/[Q!T6sg-,!pd]A-
/@K0^SMUo(YZ`/FqLRTnDleF<Ud3@Or"Dl1(O=6DhbiKm'WK:JenZkZ:/o6CqGCbsUgGVVmJ
D\_b?K0u!1HL3YO.is#`ZVi&aZ(`Q(nS'euU#!B,\r,e:(-gWec+CbIU7pW76.DogC?n(0c8
P#]A1s@Fe9OAC\]AL9%VNeu@\YlE&3*ZdAkP'qVKGp*3-Kmd@f%oc#HK#]AFL8&2NA=N9<.Bn3i
Hh"UX/YG_ZO4@Fe^4]A*m;En!lYI10@tX<1ggX9^nM\6An_`hgoLk%O8teYFAJ4j2n+bo!h*"
uI84lJB$D:0?emQg!eBe?ne+fsn95+rbPXS,=V_0$an]A':)IKft!@MFH]A5RnIH.Fho49YO@B
b]A0;]AO/Cr4rG2:M<GV\C<Fk!!PT3)Ssab-)7X-;,0gPL^T?=@eAlY*\Fap(h'WP.YNI]AoiQ%
q2+7gbZgT2;JsY\^(Ts&W&rmJN?2!D,W20mQ'EG55/)D(2FC\%#C?=5<*>e?p#FWnrSKY2<Z
1Ff#'YRk?7t.fKIiPrk[kH(adT^.a:18"9_Z3GWpLg7Mu(o7`-UE]AkuLQ="Q.ATrZtOQ/Mhc
M(ef#*_d'h!RDUE$GhMcpL=a]A!3T0*:B","NpaDls"('i\:PaN`kM@6c-\K>i>X\;WCrYUX*
495?$2AN\PGWa+BbN#pB^c,E4B,"3L+pWPGE;#oDi)S1H?gCX4CLQ:l*rpPC[ta1B,b=%1tU
Lq)h(Ejj/P6s)5'c>&U2dZ;*ONo$iB:13=!3&p\goebOef4&I!*"keJ&p@VBk9%MpO0jeH>B
4c*$3LOhfpKh]A/I^S.S<=%:Wb7r5WO4&q:aK.n3PEn;X"/Q/bHkq#5N9LeS?8j<ntBbq/`3B
8"pLSN_5d?X,P<)t\ea^Q_1]A4+<;)$8uTToZE1dTh^=a(Ba$b[gj$@eBuH34D+i&N#1>7mN-
!J2]AN+rKUI,!hK#aaM?Or;1'tfnum[FE<6@'):fi$BA_uU.^%&r2^nZG4mS3-,cAKr0RZ9Ek
:gD1f?<[nSi)+d'NNG/eP1OOZ<UDuanK'[ZMBY/+_@s&=L7r_F_UM639pk=H&uRFH,,AUGYL
ST'p_W:.-^4b4+\9`BnCd.ntbb9NIg<Qr4I)!GG*ODUY7N0e&J6);-NaH"mrce32D5LY'ZTM
"R/N&,*iAp^M9Pi>Ke:DmY>U)*)Xj)b%>pG8Fjo0pH=$hS?gA5htE1pcfRHD=p:c#Cmk8)a_
a-;0<HsG1D9*Yp^B9@l$$2/W@k?dJ7N&AG^kSt,4:34m+!2$5p#Ujg^,jV9hZCo_rF6?TaSC
a[G41Bf#1r.H.S/E\X?S_qkD$8&bTQ?VOI`(P,"SLJUaPq&;L]AAN"W[h2*q?<Ud_P/k[_SJG
$"2#p9:Waq0Wj$d%S<^6Td<G=34=g3M0dWP@Ss>@?^(c%&C&umQ%C5XJ:5pR&"h;l#EBC8B\
A[r3abqionK%N,'0"L?"QL`ufW)U)E<+_;7U46+.]A;cF_-F8:m"QTMaaY#k.<""o>o7RX'%&
JGqnAc:cYQd=6OV8J1ou,;GP7\`]A<ko5`]AS"[h?sFEn)l[@@CSiWs'M^#2[Kh5O=+p&uY^4n
21%5B)`+p;eLp_>WHHKK3#nXY(+g`"&JB]AI-bAq\F<p,jcRB),a$fZ)/B=l.m(DZ=\^7l@sh
1%Ri'A@ChpBgdr9VnF"Ja^UbHRM)XA'M:lu:"=1$$V<7#!J$/;j:/]AZ=3uAn'%]A#7Chl5?,o
G8@0d.[gMa6cA22P9%jMLLWFEN2VlP2?Q5rLcec?#jQ,l_mt!:C-_(1#HVmlord!;Y0FEL[R
p>1!@?;KsR#5.br!^f`pn=X[J;g.[iGPiktC&b,&7dUZ091s&hOj]A0kd[CEJ^DhSI)=$fh1I
6N:kReZJ3RA%,Tl"#E]A:\,..1d*jbJ,L+Mmi!gX-\<QPcr.Z;L_`BrK/XCV7os`.3.T2-t^^
MEfejKA0@9qXcdS_)#Jm*S>1R_65lDVp;ghNj<_<&H/q8C@#OJ8^\N[PfhOaggGciZOUOT@9
,-6UUR#p)S3C[:skUo5P.`$gLfc-#VV'<gOs=8.[fAd!?#nQ+ViLFGI.&Nou+>a5e.iA(^KT
O"WHCJR^N'+&d1[=XOMqXEJ?T?>G1l!0T1bkn]A`\mm5W05ZY:Y6uj3MJ_]AZ`Gu8*nHG2khDM
\#>c^G4_hqrMVsVfPU^3,+$-sT=E6nZY@R_'qJ.L0b*<PGID&l/I6/AW`oc*94OfknZYfq@O
-$1=Q)1_5Xl,G\&Q;=ja\H>L&*j6+UfDH2T/4rGq/j4k`KV0&i<i=\/Pt9A#nES]A'n@"#1_j
i%qM0=Ca4_7mtfkGV6J*F?:Du<5bcVcE@#=.u=r&M*,&hYQ$OMAt)rE+CKI9fS7,IXL-afHQ
jUWNt%!QFIXaDi)8YN:CkF"-pn\CG;qDX7!).;Y4oWH./uJFN1Q?gd;VE$uZc?a62Pm7/u*p
8Agq@M;6K*%Cf;>^^3l8&8_8p]A[%7DcWRWh2fGSFgCnG,r_cDCS!k?Vg_^4XE%I$ko&`>R\8
M#2RI(=E@C-Q[3%8Mbp0Sp@=&AJ.YXY0Z9j[dTIXg4<N:n%oR"`gW,:_K(?'TG7d@4aA5r?R
A"5?!59a^8U:Bts68e.V:UAu)SiA2<':MJ,%(I0/jm54Er4*Wa*nEWL2**IT2Z9MmV(ICB+,
ZNUN+ChnZkk@MUV^ea5MjW[>qMt#@A"VYFM>=6gSXM.OP;c"^K^C`0.I.;=8l:u8=<^1=p+6
T"Insp66V;J4L=046ea>%_pN,OZ[*18<+a#t.E*nr`EbBpQX7*UJ&Z>;E!@V6(QE>,,D\b./
etci#SA&cPtA9Q)BmtV_G^T'rr.5uN:_gqkP`C>l)RO`CWiLmA/V_qoiGd"qE-ts,u9bKs,o
b"NYl/&\4B6;2)+"EM,R^.2%d=7\i#GB"q]AnIflHP/:_<Z5AGK)X`a_%U9"C5!%WZGDD^!J&
Q0iF@n;;rHL"#cnXI,YEj]AWY!m.mR13(1(rhdQsRKK8Ad=3eP49i$"M8eH3t)M#"Vh5h6&7g
JsD7/I'n2<f#Ir%>NdZRBr@h-LWep(3f4kJ/f67tsej"R(K_/-r=G<WK:)5Ujp]A>]Adt?#g-Y
W^iX=H$I=+M`*%R&8='"ZJ+lAmd54+fqtI8h#B:"er'[b]AFed\$\:ZB2l2GMqO69/VpkJ0:b
eh(XPmFV/IA>5NYT:rfPQ.PB$6cXI9TFcVJtuA,?b92tlRgK<b2[tKip"D]ARM[n+Nq<XaDb*
I3@3C!shH%6IA^/tsC)W&6%/sK!Lo>\_oah1uedHk2qSGm<@\T1e"Qhc%"Cl;'$-4#'SP<mJ
2>?6k;H?u6MPeo4W)n:K<n&)&EjW,>`$Ug/FjBRHBi)^)/KJ8(P-k'r5K;N+c0Qkm<h<:I>C
N4UkEXK*]Ai2-bc$H(>_&-u7N,`_T%8N]AalBAo*df.f-4QO6UOrl/G;Y<tM3B=GtX^MN=YGO^
u]AVG&Ep%Fe\)R^EpA(]A9JTt#g*_u=dn&S<6h!D1EW6jA*N_Y%o-EH=$$0WKpNI?UDaqZ@H&'
B"\-ID(/!UV%j4Zg2M>$1!$K-q6clNQl3YIth/R<14S_f*^/m<8)P7*%as.md1?ARH&'_NbX
l<0>*sPi(Pn!D)GhWI#)9$drsT8k'K*56[DbrJMVpg!@c194ol.3&Cu/H-Y+SS+_NZt[rT&O
Xr5gB(4eikHD0[eD0=i6eO+[rjO4]A-/1CKi6qd-qCMKKT%pUF3AdLHl-Cd<*]AFmZ5#f"=+FU
n9CaZ;\ed*Y+[)NGhj'QRs3M`NoIA7e<Kh\5hY0+s1PFKMAkLpk)s6YiTV]AYQf-am'?X8O2b
cimg?0j88*(L"WY[+?OD$<T*?lW)"qr""Z5)3B5Djns+P]Ah2Ck_S=%;p$b^I;OB+"UCjT=[o
gBEnCRG-T4gNjNp=&tA8)J]A/?J(ZP$14F2FP#4U#9D,\,)^@Aj%B8o%qW'(r[uDU0bVdWOQ`
tWVm+%["4\_1_9r+(0NESDV)dK8l1>V7irR-rT54%*;8):G.@6m#O3A6>6!d"bX-6d('8h,!
J^SC>$'E8Y!$35pg,"q/\M222L36YW@#5F(,VX8(icNa+8TncBKN/ga=L/Y*>d'#e)-).]AP=
J?uNbQ,E0=@a[!3L14aboW*p3?]A6-W"=Yi^$uF(VV1s<lN+Dn,ioC)GiRInrBOU?eQdO2`^]A
s:>)i1Dk&R-;X[j<1W,oWq-gbK4jQr[_&7K$4NFnJP5-0q[8o3$.@Y`U8d&6pHPO9T8$)[Z>
6s*`AQ0B7AIN'ON77edTr`9bGT'BlR8[jE:L?:1I_YdEj]A$P8g!9q`^Wq/$@5WaBnW!]Al8qB
6h-3q,+Z\qB0%&NlY$o,m47&&ta34U`TIbs_o:se0UjphO/`@Dg7.nnf1=rQL"40]A?\_9tFp
YV6BA.@t-1IQ7%"E+3c[ZYrlh=`0ct-J4tN<R']A!h_N?52%,3pZLV1tR>iUhI=l3lT"c58d4
sNN8m-;^-C7"CQP]ASHqRsViJOEX/gD&:fY):[>2Gp4dF4eV\27jff<$0&tS,Yu3E)/L:H(VO
nl78Dd&V,*-54s2C+(1D'0lIBmi$/)1gegD7]At3Dd[&(TfD)H!BXG?Tkl-SeM=<G^.97;j_G
g;=4Yl#\UEI2JkEJ20?%*j<2;J4fEX7l@9X9@\>5!311\-!-)429J=\E[b+44T*%?IgDS1^P
9_9ks1pU?d1_hDH*^nT-iMLYJn<N,Sr<C2[`dZio.%\nF6/M/3kDNS]AUXZ_k4m1J\+/57UWd
pO)T3ZAf1=q>=sZ*6cm"7F$*<A&\RK'*:4K<@G;#?kM')I61%I1\d;]Aer\kH="^(q*eKXn]AX
l_,??W#_%:HEhE^h9@^j91:N@86?Q)iDXk]A-<U(_\'`?oBC\4IF49r0?k-^XRW)p8k3h`>eZ
@G>Yrp&X/N-M<dh6R(i4?!=$"0bOp&!";0W`XH(=M8qLcgDeVK=5)gQ[<7hkE-/nAX]Adf@8S
gcOM&-c"W62`D&!`e2kYb20;E0\q/U^%]A:Hg3H^4F]Aq?2mkgg^[q28PP(,<^Gu7^IeQ:,.JS
Y?RI+sUC*lI]A>[KYkN+^DgUL,tYLreA<h$mZF<*A8#)MuPMM7e[)2r9jiK-0fVq?0R99h9]AN
W\C"`TVGdZ,F!P<-QbPD9'?f>]Ak[g^io2k*r6Y_m)0[bH=rc?Rp2+(BKXbe@ia;;*Iek`YX8
p+2_i3,*#b9;d-GQlgDR"C$GG*UL#/cS>dsuJU"=QZZq-fLkpIl(SF=EqCK9M[uEH:,0ofg[
;ib987@S&1e=T?kVhV0O$kP44s77u$pm;W$tjHu?l80@7HH]AVN^K1?kE.I-[F,A2&/)WSC+7
$@&Td#+BY9Mo+LjI_"GLgXOCpJ-V]A[7IPooa2MQLXJIh'4%"6>M74.TmCJq7Ut;gm92uM&fs
5NH*d6L/Bt0K+A:`rC7q_rp6lP/G>+::CHlM0Mri?2CY8O<YTlNJ[f^%n"4-bc2[6A#F05gl
(Sa0b>ZuQ_8QSUcN$05LL>HJS\-iru+dsmW=(bjkb!I."2cPQKo=RUUHth("DipU0//QP@ru
'#G[YL=..epaBPNb8sCONMT`#OGWQhR@<6`=YJ7O\E'k="Yuer#^\ZYH"J\)9IhZop4'jR9`
\SCLJb+I!/3R-+C6%#n^,C4!mJ9?TQ=\o'n@2B72tR+nS\Y["r`8p&sMSm!VPTQC.(RPK^j?
TM6Z_B[FtN]Ac6#I`)uEfGICd64mQWC9PHdO!Pps\T\7b(XtN:kbn:F37JTJ8&"e,-9L94mAD
d^C?Jou0+KDg!hq<&Vg@#c)q\_2I1'rh)p+hX,>bI?1BC=dp"2@XE3+o8FUCOK%pEuM&t?,U
$nX\#(j.cl@EpntEDi^K[I$s8ctUr,&@WhKQ;HhlE+Xk(XY5-Y-_?T"c)R\rZ%Ti(AH+QpL,
\]A(/+e`LI*\b_e.nSrnV"]A$VV*spcd7?[3jqL&0oX:X./e@i#.d.;Lo*QBEP:"Xfs166qi<!
s[hqrE?27FK-("-Ngk*M1XN(<u+md)(9CCcr_p1C7YrXA<<6Ym*AB6U4+#+fA6.KUmjQp)lK
M'<'^3U(?!);?^8!AW;Y-c>M2:H@G$!rn7M1tVOP`A%^[Jk^a^AXjeY\j3!*"l5u";>?'A#J
`J`?3o..(i;b);YqA&&jU1aqJ!6*rBmcZO[3e6:r8UF'`s%.Q5JB#pQ.]APY!)/_GkLH+4Hsr
^M^+;hA5HT!(=4N-i5BMYC$;6)+m(gIM=ZiH:9TEV$PrY'B1R<hiB'nM^?IWV#MVRZbs)aUQ
[J`BQRhkl6]A+t!_S1R;5B.Q>2*+saiOd"fH^''np9tpLqnUL[0OY#8B.!50U!s]A`C03,jpb?
"n'>17obKl?be,8l(g*UH?]AaLH[BQoEJ[F?KW<b!;MB^5,=J3E%^jITU\&aQo\53'2hPpGl,
oZ&QbWsK_MBL*-5odJ<YG4Zcc=1Wu.W8$g92eRB7OM7DR"+Hp6/TPci7KC4Zln)Q8o>r!0UA
"HTH`2FnYei.kZ;OLo5jHDXZM:\qt!.$ZO$%la#K]A.n-'H&Cp<P@qV)9O%qu8-DS4TOar1QR
Z1jiCh@''a(gQb"k328o8f;I3bIq$@LC&+`I#eREMTa#f@_`iJTF:rTalY)G\H9$iBj(?`L>
5#`]ACs@T/5!#.6cJA%:rmG)FR\?U1:a&Q+)fBa_]AJm0RMg#US.fYmn%g0:S>?^odhO6%IFZ+
F`.JS\:]A0ll7\1o[[@N]A=%%SuG/8Fk>\fnb6pF(]A]A#f)XCjL[1HWPbQa5lg\H$);h?.p!EDe
R,cKlFedpqYP&E0e?^Ml1f]A;pkJruM+Q&8<Ofr>ADRW*4(B+2FBU7DlI.^M3hWb2?@:&@d'K
;^86D?VfuaqdJ>p.V<VUG]ApOaOt'goEep#sh2;pMIbGuPk(f]Aso9c:*^DJ5]Ai/q,a20YEto'
It=RFaV1p@a7@>i+osfdpO#&-rq#BF9$OR%7-ak^#;g_<"IR7."$6AUS7;)(fpRNrN<b)Sf2
8H1puPh&W1,.s+"P[n^9J[gS_sqNJfU)(Z4pYeZW=LMJp:E5A$,@k!93IP=r`FA<'1i9=FEf
E.$C[gZW7H@3nh1L;H\&dG^YBtKBsREjSn^&D@Ce634n%E\LR;(QuYIu>#3ROjMq4]A4fI"h)
K,(8Ih26#8'+*42$_)r/mRC2<2oN:-=t:ZiH.BiFRenm-q_@FO-_IVCUK&W%Z`RgH9Xt?=Cj
fLob&TF=;q('H7]A:aP+-^Z01lAN]AV67KmjkpjMru>ej!0`-*25QH=5C^(A)!7%:'^aNK7EJB
1?e\j!P/UQ$8rBGV,A9IR;#Rg'F[0Nc)ZSD<K?=BiGgop1fSY\^j>3PAj)4+1BT7Y)ge^iKU
A9.e"G@F!LM/2C7=\D=K4l?2(MY4s7,l9fqY"`/chFuIp>m1M8%XYY]AT6:[WX@qZFPB?UmUQ
de70J_d9/glWWVPH?m+)BZOt4CqmF/3G5orq-$<^D;HFa73sS<kIuTGpqu_sUs%io28TcrW>
TU^'3Z&\EeK/=eqP1mdE>dnb`*m)-Zn"/gq!4Sa.-?e\`3?L4rp#1]A3^p4k+8Y:o35KgLd3P
\R,nDj)de-LDr7?nQ[]A+/r(et5YV]A1N[c#r+lN[O3uKK\L<4"8Q-h8`1ZU4O>2AoQ4(V&[D2
4h(l9'2C$crs7QI$q@]AcCBlsJK3.LEGe5u%#.p,i"58IiBK1"gp;E2dQ)<:AhA:Ekak[3M(o
*98?T$Er+fIH";MrI6ckM\`i-'X916.RqJkIQ5)M@\9Bmi$MSbV+/QmS$ZV^Kr$ql7UKb;p_
3noamLq*&-Ir&>,TkJ"BX`lGX2UK*02jcCcpiljT`S"0k8ka'$iR(9nPMV1D-NA&Hh3p8qkq
97mC(HIF&jS<-L^><4:<HOnTGNm)snc.[Dc<*O_kK/]A\"C1fW_2B:b!<60QgBSskkQE9%eJi
!6=#!+`=;jq^R=oquUVVK<J>p1U.UNg:VBI*;>6l!ZW7:o<,6(Inp'cIc(3&u2hl3rBg^1_c
)CO3E>X[!2Tre.'LEK@!W7[p+?q(Q>.1t@6:Wbg99KQj?_[7S@<sC#FBPP<.Riki<,Nl,+H4
s=/H\nP%d6Jgu3]Aa8A"dLBm>@220p2p8SA"/1"L<uCl\'Z3$<pC2u?o"A_e^Y!$_52MUF,c-
7]AH:WHp:Y0/gA9XnY\(>BI-"d,JeLK?M88uj`_m9f-:(#9O'XcDRM,d[&9J$jd0sgPh\7Oij
Sk`EPHGKSSIj*&qWI!sNXuC#Eq`EP-G4/e:JPW!X7%F9m:31cI9dMqY>obln(R@8nT7V(rRq
R1HbV-m5!1`\F6<%N'XLfq/?V._oDm7jQE17Noi"r,_R8Jd!K$`aTDYPmVP/'SKU4\M.i:@Y
c*V(a]AU#IHiR]AGOYW.s]AhT%UqX]A>F]A*5/MYC*E@Q#B\0,;L%YXe-Ne_)0IQkV>RSD(#pS!kV
&Tpi1K7)`s+Wm?s3lnXquMpcfB5m.6q$jD4nKE4(OL[_)YoOPl,&=A+7]Ao5c!VO;A6@$&=AZ
!f^,/UOaD+`XGB&n1gi+#Vm8,Mr)3Y]A/5Cpf>RI:0Snp.c=UWK]AeMKopL_-?nHs;1;(;U<d&
"Q-)`-gKeR*n`,)J4h:Bt&oWiuR99RIefY.U,T\B8,e5@>EXG)Ha&'Z[ahAlVSg,2#?-lIP=
J$6^qVk=!f5i7qU$q!ZQXG!UlS<aqiJlbm?uO(#P>/<,8*#dMI1To_^=\EU1h8e_i?qJ`7EU
3$p5$)=?`ap&lp.\SqoC&Wejt/la$/Q2>%u@SFA*#$arY+2I[`aTAX4=&Gb3lgqJ)?t^$4?;
6>A3td!7qm%/hT6>U2HSU`rS[ENO`6\r_8AIr3<JlrU`A]Ah6<8YZoj$n\]A!Oo_Q5F-J/%k*!
@2_e<aQ;rj9/Y@J+=BB/P[E>M.aYmoLkb">!$([C8/S/so,bm>^M;>cQr`uGgM5=MXR`C)lZ
a!-OfE8c(mt$?0TV:.G$=t=CVkT>rM@u$?BEu131;KEA@>k!l9UMOAd@Nl\8fre9Nd8(ah5]A
eh]AD*+nnm3h/MUg)%[i36gQGS^!M-k]AaDO9mOCo!u6pgrUol?eAhc@uTd,-TjIV::Nb8*34a
SbDp01"ng![FiE486n;8,-L.G<1f9ufe,@QHNFF7'%A*h/%`Q1@YJpMqIC\#-#Xq4?]AV3jG?
76DjTW8F*cTriF,!R7W3-X^(OL<EpdB94\CU0raA[><)l1nBrgs5Mf"_RU"l@V!GA#nW;9Lh
?pf3Q+rH3W:UMCFkY6qK12L7\Neo`rU(3@?(.6)<hHCF,mi,1gD;V_?ib18DFRM+0IYHB730
;m[9bMPW/U9j]AHDVVkSETkq2Q&ZR:06%XbBV/K'kO#m9/u=eLQQ;76\1Lt?F'HE"7Q<2PpRk
BDb2/7R5C$LM'<6a>3YZ/mp_G_"cCPb&#G)5[YlfBFIjr^r>3n6^:>_'DT+"]A^Hts$uC!#9;
8ra-aem;ff3Me5@S]AMh2:&p(,A[-Rbp%7`DTu=13U@r-e/-jKocs'Y$5-P#IIZ4AF;15[&ci
OEQcaFfjjCqbhOt1*bqS@5<%2CU\M>1>-S1mlpF=Zs?LS<>&N%!?UZ\0D/.$@l1%[[;))&"o
PNKH*j>pBetUZg$W"+!35P8-'Nki)8B,e[h=D*5#h-\ISOpCs-;<*G]ANhUrJE;RFWh&Nln?7
P;k7B/t;F7r9aNe46##B^CW$r4-m>D2'+umF^s:T&Z,5988N'f-#2:'tLLm8oe5lW(L)*[8F
cN;K.g\7\VB[cD]AZj"X@JK;/6>U^^%29Sd<c]Ai(BoALr8t?rPr@3ThN'HTY?'J5q^a"c_K-K
5Oup/%aBk3=<XuDp$U;X'(>:\"MBFb<,;4GA2G;GZ>G)<o9,C_/Z3gHB]A4ja3GRM='+C@DL#
S,&V7MeM6c:&BQUgn<W]AZT#h*eX@CYXItGlCQhjZ>DE_f?^)ag_3sYL7pIB'BZ0\+=j]AIAe?
9d2)>>CA)tBqkW)PGL)8ZMd41*Qm6fJ!=GW;ZQ3@iGOLas?mMWUK)>I-m$rgZK@.6oeni\SS
"*\'&J[+Lo**WT)5R,O9L&N,YE9W-Hr5g?)&,p<G6B,Cd?BPKSM4LEB0!>IRJad-De=r=00:
POQdB-sC=ZRJ\lNCL2C\gQMd,AWJOEB<.JbiIj9O&>#TE`&,^EA"n2UJm7:LQ&&`oSO[CK(5
.7AS]ArN11(3*XW<.kduZ\J$A:biN\eI]AoJH/':scReXf`'q]A#a+]A\Fir'F9/1"I"cB<#sBCg
;AQO<LW&'2)UdKMsPs5aKP*foOmGl18TnpD1ac@<>rDgUuBm<[tL2)K2qlZ_%(3?RCFeP=X.
X1.6Vj&EF<<oZGUi[AI@IBmqkddj4jhmlBHlIIFg:K$?L4P14@`75U`=>(clES_(+]A$KiDIo
<HUdeT3lfphA*:/<(R.,orR=8,"Gm+>3\;,pI?)\>F4=G'G,lrO.u@7X[jKhq;U/@.4DN0H@
@0Tlc`MF'</C<NpsVX3C:?@.^&"@^B9E:f[Kjp`tj"6Cl-Y4"U<%a9#bf?kf@ADFM5SC$3cH
0*4Ir&<]A!Mfk?/1=ETU[\0B!s`:^1<n;dq.PWf=?ZJ2g2f@j(cmUV8Zp_;$6#oF2@!TU+r*O
]A82"S2]A8c1HA)80GE0(#C"[Vc$9p-j]AE)gZ%,\G'Z6oer([;@PD5AV(9PLJWbJ-lL]AU*G'@U
YY<=sB1m(HfPVIOTJU'BWrSq;'[.-tWXBD<Jl+kWT@S+F_+R:tkc<GBfMq,_208Qt4!m&FO=
gTu!,om8ROhHmH`Qs!c[YO."D`'D%+iS(mRPQg]AR]A(:.?'ek*^/kq<Q]AfrGWn46n9G."b=@V
-?Y,Ti.\DG69Ri?$V*0epSN&=*.pWekZqnri^K+h>?(QlKe_`cmMOXas0-OQfLm9tn'$HY0m
g5C.bhd$YXBAQ<E&NXLA'[?pndon=jRS1Pa:dkYLiE"3`"dJ4gmB5TLV!_DA7[tFILJ,a3M5
FT,kM1O51B(;HM"K%3Y!!*V:3_<rl3aY9Y_p/DCHX)'KCBl#k*Y$>Zq,>OTY?0f2H]A<018XV
KPM&b-Y$P%UkY*/I>jh(UhO6F`pCpJc_g:g*eZkoVZ\:K24k/#Er8..#o>k.Q'/qqdTb?I@C
<ZZQHPCr>c5O1ap?B?K^\->Vj/e@^K/GJmen2#5d#=^LCm)e5c#%%rp`LDbE,OPXNO,)%VJi
>CghPP:aW1[o,mlSB#rjb'?",pR-X<>+f$81+;@1V=m&Gio!FAAE?'1?jJ?LSrGmo)N=_ARi
;o2ql:hiuWXFlOTeeQfHAmP1`:\\l[eq!./0.1<(3n]A^p<Z!K:Nf7+Co2'Uq.JnUZfeZSqO%
p4;4t\>:m5@TmX7?>pP+qfUWS7H"NYdO<C'auCpls09=X3e4NX;UgMQak&^ffHDnAtT:S:1I
gD;4&=W'Sc&eLW(QBJf$MO_\s7VJ5tZUaO>6ot#&VCFZ+Ua1m[g8saE=E,UMI.Di/QWKPQVJ
ga.\E'U,u8.V)U:o%4jCed3D)AtQdTZsb<@.X_FdD4+Qhj<]AQg^3^F$(pc#ZIlsWRqjB:G@r
W)qs!%$A+;1aEQ5Lc&ZL6<gFbfje81R.rRe`$RLtA,H6>67P/G2q[kF8Yj6tLDPb]A/kb,>?C
2<0,X5!AQ>jpVVp-cSjI;qRH>*@o&qTA^'LI3$<<Z<f=hKHSq_%TmSIXh3?tYb4j0Mr.\+f"
*8oIflHW,9,#OkFji8s.Pf1'`e=^V7sgDZkYSd$Tlh>\/9iQ1Mh[d[%Ak\lV4`0c]A8rs#$Vg
,e"\t$s.+@S"0O5UP",jGn>5Rp2=3<RgY%kC*r,HD/9Yq.=0WpQb@&ZrkaL]A7)9.PVNDqtFp
U0sl<H"MG8[tnN1RC,kP6_(2FC<.\BYB4\\/>Cc">RIMbKhPIP"s3A(m'^a^;nT<a#Ci9=Ru
C-25B[kZ?C(c_?!9s;uL/^V"tRBiB=ZrK.RX2=M@dpkVoa)aNsToV&*i1<mIZS'h4I')AU3b
4Ag97rOZIkqKteA3ZdM>GgHO7"<F^IQrJd$-8M9(UZD!R#Dc#M0uC(1nY?WHgcU@a1O$IXlN
0otNt$$?,s0^q8c;rjP'+QC)(b4uL!`qo#-K3.C7p1//L1m3FN&s4R,[M1@]ALrS/9pQHrX;e
9ROCLM_8`Z1%\q51A[%8GIXASdlPMnp*9d(rjS;4R))ZB<6#,Ys`pkkrnJ`/]A;E<AX2&u,Jg
"MWsomE0Voq/8TM/H/Af&7kfqQ^G$L*Shi,[O#UoSk5*Kaq);?l$\2^WNH*V[,i\^&r\3g0V
gpCVt.@bp2!BHQ@<5)I?_'k;G$!W\OEt.X'+V"3)ALX0?kt_d^'U=j-P_]Aa(U.$rhU#ZBD:c
,ZdG,pe&'^B\ZipJAhuT^9L`]A<<'.WA69?fL[(qajkfI,O=In/<f7e]Aq?<54)O=V")h)39#=
c;m3Nhd,'fT3(8.I/+GEH6"4O5!GmeY3_pPBec!0h#m@g"rK0%+XM[0[EbC$W!2fDj2%=amQ
QTZ1eK(C+$N.!Su<Yi\\6[cr.(Ku<1XGjXSW[>EBHKL'Y8C91//oV31Ab5QPI2UMIGC\r%O4
F+MP;t8%\NP`BHM)!ofqIn?TZCgl>GE/.NrOVg.nuPQTp<.<?2G>'e%F^&G_mEk+0Q!3VT:8
k-ZND-3[QAt0E,+6bbI61dnb6[b%M&I1Xl&3tIpF&KC%Bl0d_3DHddLcH]A%AaKj0+BNmjIW_
h?IQoZ[pfFCg]A.T)jh)Y#%dtDJ_@hT?oB\XQjq7DAcoAjq>W':%N0`hYQ#suJ_FA^<O+>m)j
h)Y#%dtDJ_@hT?oB\XQjq7DAcoAjft_E32D?);%Eo%=rrsg#,.hXhEgm?Zg8Q+i_krDZgV@W
52D?);%Eo%=^CdgDn_JURRif9_=o[mEC&:i*-\&c*IfK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="374" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="70" width="374" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="url"/>
<Widget widgetName="lrkq"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
<Widget widgetName="BACK"/>
<Widget widgetName="kq"/>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="108"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="4"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_khzb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_zb_dy" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_kqfc_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_num" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_khzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="索引" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="10e3db5b-ac61-4293-a845-2238affb15aa"/>
</TemplateIdAttMark>
</Form>
