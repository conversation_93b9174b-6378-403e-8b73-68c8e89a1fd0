<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_zdzb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-09-28]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx1"/>
<O>
<![CDATA[利润]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《财富业务--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}'  ${IF(rqsx1='收入',"AND A.AREA_ID ='cwfx__cfywjlr_sr'","AND A.AREA_ID ='cwfx__cfywjlr_jlr'")}
		AND B.STATUS=1
)
, RQ AS (
   select replace(max(ds),'-','') jyr from ADS_HFBI_ZQFXS_JGZBMX_cw
     where ds <= '${date}' --and zbid='dlmmzqywjsr_cw_20230915101731'
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   ZBID,
	   DNZ  ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	   CASE WHEN (NVL(DNZ,0)=0 OR NVL(QNZ,0)=0) THEN 0 ELSE ROUND((DNZ- QNZ)/ QNZ,4) END TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX_cw
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)
SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.GOAL 目标值,
DATA.JTQ 较同期差,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID


 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_cwmx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rqsx"/>
<O>
<![CDATA[当月]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-14]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[--根据块ID匹配对应指标ID，并关联父子关系
WITH TAB AS (
		SELECT  
	 		T1.ZBID FID,
	 		T2.ZBBM FBM, 
	 		T3.ZBID ZID,
	 		T3.ZBBM ZBM,
	 		${IF(level='1',"T2.ZBDW",IF(level='2',"T2.FGSDW","T2.YYBDW"))} DW,
	 		T1.XH,T1.IFQJSD				
		FROM DIM_FILL_ZQFXS_ZBTZ T1 
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH T2 ON T1.ZBID=T2.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH T3 ON T1.ZBID=T3.FID  
		WHERE YEAR=SUBSTR('${date}',1,4)  
		AND CJ='${level}'  
		AND AREA_ID = 'cwfx__cfywsrmx'   
) 
, RQ AS (
		select replace(max(ds),'-','') jyr from ADS_HFBI_ZQFXS_JGZBMX_cw
     where ds <= '${date}' and zbid='dlmmzqywjsr_cw_20230915101731'
)
, DATA AS(
	    SELECT 
	    ZBID,
	    ${IF(rqsx='当月',"DYZ","DNZ")} BQZ,
	    ${IF(rqsx='当月',"QYZ","QNZ")} TQZ 
	    FROM ADS_HFBI_ZQFXS_JGZBMX_cw 
	    WHERE REPLACE(DS,'-','')=(SELECT JYR FROM RQ)
	    AND ZBID IN (
	    		SELECT DISTINCT FID ID FROM TAB
	    		UNION ALL
	    		SELECT DISTINCT ZID ID FROM TAB
	    )
	    AND BRANCH_NO = '${pany}'
)
SELECT
TAB.FID,TAB.FBM||'('||TAB.DW||')' FBM,
F1.BQZ FBQZ, 
(F1.BQZ-F1.TQZ) FTQZ,
CASE WHEN (F1.BQZ=0 OR F1.TQZ=0) THEN 0 ELSE (F1.BQZ-F1.TQZ)/F1.TQZ END FTB,
TAB.ZID,TAB.ZBM,
F2.BQZ ZBQZ,
(F2.BQZ-F2.TQZ) ZTQZ, 
CASE WHEN (F2.BQZ=0 OR F2.TQZ=0) THEN 0 ELSE (F2.BQZ-F2.TQZ)/F2.TQZ END ZTB,TAB.IFQJSD
FROM TAB
LEFT JOIN DATA F1 ON F1.ZBID=TAB.FID
LEFT JOIN DATA F2 ON F2.ZBID=TAB.ZID   
ORDER BY TAB.IFQJSD,TAB.XH
	]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="date_zdzb_tb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-10-23]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx1"/>
<O>
<![CDATA[利润]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《客户分析--客户明细柱形图》》
============**/
WITH TAB AS (
		/**
		1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
		2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
		**/
		SELECT 
		     A.CHART_TYPE,
			A.ZBID,
			B.ZBMC,
			A.ZBBM,
			A.CJ,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}'  ${IF(rqsx1='收入',"AND A.AREA_ID ='cwfx__cfywjlr_sr'","AND A.AREA_ID ='cwfx__cfywjlr_jlr'")}
		AND LENGTH(A.CHART_TYPE)>0
		AND A.YEAR=SUBSTR('${date}',1,4)
)
, RQ AS (
		/**
			获取自然日对应的交易日
		**/
select replace(max(ds),'-','') jyr from ADS_HFBI_ZQFXS_JGZBMX_cw
     where ds <= '${date}' and zbid='dlmmzqywjsr_cw_20230915101731'
)   
, RQ2 AS (
		/** 
		根据自然日获取的交易日根据一下规则获取所展示的交易日：
		当日：筛选日期往前推五个交易日
		当月：往前推五个月，取每个月最后一个交易日的当月值
		当年：前推五年，取每年最后一个交易日的当年值
		**/
		SELECT  
		JYR,MJYR 
		FROM (
			SELECT 
			MAX(JYR) JYR,
			SUBSTR(JYR,1,6) MJYR 
			FROM TXTJYR
			WHERE JYR<=(SELECT JYR FROM RQ)
			GROUP BY SUBSTR(JYR,1,6)
			ORDER BY SUBSTR(JYR,1,6) DESC  
		) M
		WHERE ROWNUM<=6
)    
		SELECT 
		RQ2.JYR, 
		RQ2.MJYR,
		M.CHART_TYPE,
		M.ZBMC,
		M.ZBBM,
		SUBSTR(M.ZBMC,LENGTH(M.ZBMC)-2,3) LX,
		M.VAL,
		CASE WHEN (M.VAL=0 OR M.LVAL=0) THEN 0 ELSE (M.VAL-M.LVAL)/M.LVAL END 同比
		FROM RQ2 
		LEFT JOIN (
				SELECT  
				     TO_CHAR(TO_DATE(AHZJ.DS,'yyyy-MM-dd'),'yyyyMMdd') DS,
				     TAB.CHART_TYPE,
		--			TAB.ZBID 指标ID,
					TAB.ZBMC,
					TAB.ZBBM,
					DYZ VAL,
					QYZ LVAL 
				FROM TAB
				LEFT JOIN ADS_HFBI_ZQFXS_JGZBMX_cw AHZJ ON TAB.ZBID=AHZJ.ZBID AND TAB.CJ=AHZJ.TREE_LEVEL 
				WHERE REPLACE(AHZJ.DS,'-','') IN (SELECT JYR FROM RQ2) 
		) M ON RQ2.JYR=M.DS
		ORDER BY RQ2.MJYR
	--select * from ads_hfbi_zqfxs_jgzbmx_cw where goal > 0 and branch_no='9999' and ds='2023-09-28']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="tabn"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}'
AND TAB_ID='${tabn}'
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_zcfx_qsfx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-12-14]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx1"/>
<O>
<![CDATA[利润]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《资产分析--趋势分析》》
============**/
WITH TAB AS (
		/**
		1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
		2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
		**/
		SELECT 
		     A.CHART_TYPE,
			A.ZBID,
			B.ZBMC,
			B.ZBBM,
			A.CJ,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW,
		     A.IFQJSD
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}'  ${IF(rqsx1='收入',"AND A.AREA_ID ='cwfx__cfywjlr_sr'","AND A.AREA_ID ='cwfx__cfywjlr_jlr'")}
		AND A.YEAR=SUBSTR('${date}',1,4)
)
, RQ AS (
		/**
			获取自然日对应的交易日
		**/
   	  select replace(max(ds),'-','') jyr from ADS_HFBI_ZQFXS_JGZBMX_cw
     where ds <= '${date}'-- and zbid='dlmmzqywjsr_cw_20230915101731'
)   
, RQ2 AS (
		/** 
		根据自然日获取的交易日根据一下规则获取所展示的交易日：
		当日：筛选日期往前推五个交易日
		当月：往前推五个月，取每个月最后一个交易日的当月值
		当年：前推五年，取每年最后一个交易日的当年值
		**/
		SELECT  
		JYR,MJYR 
		FROM (
			SELECT 
			MAX(JYR) JYR,
			SUBSTR(JYR,1,6) MJYR 
			FROM TXTJYR
			WHERE JYR<=(SELECT JYR FROM RQ)
			GROUP BY SUBSTR(JYR,1,6)
			ORDER BY SUBSTR(JYR,1,6) DESC  
		) M
		WHERE ROWNUM<=6
)     
	SELECT
		M.IFQJSD,
		M.MJYR,
		M.CHART_TYPE,
		M.ZBMC,
		M.ZBBM, 
		M.VAL,
		CASE WHEN (M.VAL=0 OR M.LVAL=0) THEN 0 ELSE (M.VAL-M.LVAL)/M.LVAL END 同比
	FROM (
		SELECT
			TAB.IFQJSD,
			RQ2.MJYR,
			TAB.CHART_TYPE,
			TAB.ZBMC,
			TAB.ZBBM||'（'||TAB.DW||'）' ZBBM,
			AHZJ.ZBID,  
	          AHZJ.DYZ VAL,
			AHZJ.QYZ LVAL 
		FROM ADS_HFBI_ZQFXS_JGZBMX_cw AHZJ
		INNER JOIN TAB ON AHZJ.ZBID=TAB.ZBID AND TAB.CJ=AHZJ.TREE_LEVEL
		INNER JOIN RQ2 ON REPLACE(AHZJ.DS,'-','')=RQ2.JYR
		WHERE AHZJ.BRANCH_NO='${pany}' 　
	) M
	ORDER BY M.IFQJSD,M.MJYR ASC
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="maxdd" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select max(ds) rq from ADS_HFBI_ZQFXS_JGZBMX_cw where  zbid='dlmmzqywjsr_cw_20230915101731']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财务分析]]></O>
</Parameter>
<Parameter>
<Attributes name="date1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("maxdd",1,1)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.url = location.href; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.top='-20px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA3').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA3"/>
<WidgetID widgetID="8d21f724-6eac-47cb-9e9f-93dbd1103086"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[13296900,381000,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,13944600,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.custom.VanChartCustomPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="1" visible="true" themed="false"/>
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="false" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ 
 return this.slice(0,4)+&apos;&lt;br&gt;&apos;+this.slice(4,10); }" useHtml="true" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="false">
<mainGridColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange minValue="=MIN(I3)*0.9" maxValue="=MAX(I3)*1.1"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartCustomPlotAttr customStyle="column_line"/>
<CustomPlotList>
<VanChartPlot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="3"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.chart.base.AttrAlpha">
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="条件属性1">
<AttrList>
<Attr class="com.fr.chart.base.AttrBackground">
<AttrBackground>
<Background name="ColorBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
</Attr>
</AttrList>
<Condition class="com.fr.chart.chartattr.ChartCommonCondition">
<CNUMBER>
<![CDATA[3]]></CNUMBER>
<CNAME>
<![CDATA[SERIES_NAME]]></CNAME>
<Compare op="0">
<O>
<![CDATA[]]></O>
</Compare>
</Condition>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="0"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="false"/>
<PredefinedStyle themed="true"/>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="custom" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="false" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ 
 return this.slice(0,4)+&apos;&lt;br&gt;&apos;+this.slice(4,10); }" useHtml="true" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="false">
<mainGridColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange minValue="=MIN(I3)*0.9" maxValue="=MAX(I3)*1.1"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="堆积和坐标轴1">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrSeriesStackAndAxis">
<AttrSeriesStackAndAxis>
<Attr xAxisIndex="0" yAxisIndex="0" stacked="false" percentStacked="false" stackID="堆积和坐标轴1"/>
</AttrSeriesStackAndAxis>
</Attr>
</AttrList>
<Condition class="com.fr.data.condition.ListCondition"/>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="10.0" categoryIntervalPercent="15.0" fixedWidth="true" columnWidth="18" filledWithImage="false" isBar="false"/>
</VanChartPlot>
<VanChartPlot class="com.fr.plugin.chart.line.VanChartLinePlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrLine">
<VanAttrLine>
<Attr lineType="solid" lineWidth="2.0" lineStyle="0" nullValueBreak="true"/>
</VanAttrLine>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrMarker">
<VanAttrMarker>
<Attr isCommon="true" anchorSize="22.0" markerType="AutoMarker" radius="3.5" width="30.0" height="30.0"/>
<Background name="NullBackground"/>
</VanAttrMarker>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="0"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="false"/>
<PredefinedStyle themed="true"/>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="custom" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="false" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ 
 return this.slice(0,4)+&apos;&lt;br&gt;&apos;+this.slice(4,10); }" useHtml="true" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="false">
<mainGridColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange minValue="=MIN(I3)*0.9" maxValue="=MAX(I3)*1.1"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="堆积和坐标轴1">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrSeriesStackAndAxis">
<AttrSeriesStackAndAxis>
<Attr xAxisIndex="0" yAxisIndex="1" stacked="false" percentStacked="false" stackID="堆积和坐标轴1"/>
</AttrSeriesStackAndAxis>
</Attr>
</AttrList>
<Condition class="com.fr.data.condition.ListCondition"/>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
</stackAndAxisCondition>
</VanChartPlot>
</CustomPlotList>
</Plot>
<ChartDefinition>
<CustomDefinition>
<DefinitionMapList>
<DefinitionMap key="column">
<NormalReportDataDefinition>
<Series>
<SeriesDefinition>
<SeriesName>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=F3]]></Attributes>
</O>
</SeriesName>
<SeriesValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=I3]]></Attributes>
</O>
</SeriesValue>
</SeriesDefinition>
</Series>
<Category>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G3]]></Attributes>
</O>
</Category>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="true" isDiscardNullSeries="true"/>
</NormalReportDataDefinition>
</DefinitionMap>
<DefinitionMap key="line">
<NormalReportDataDefinition>
<Series>
<SeriesDefinition>
<SeriesName>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=F4]]></Attributes>
</O>
</SeriesName>
<SeriesValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=I4]]></Attributes>
</O>
</SeriesValue>
</SeriesDefinition>
</Series>
<Category>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Category>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="true" isDiscardNullSeries="true"/>
</NormalReportDataDefinition>
</DefinitionMap>
</DefinitionMapList>
</CustomDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="eafeb46b-17dd-4123-84f4-f1b5d7c28339"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" cs="6" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[区间柱形图]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="IFQJSD"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[IFQJSD]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="ZBBM"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="MJYR"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="CHART_TYPE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="VAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="3" s="3">
<O>
<![CDATA[区间折线图]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="3" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="IFQJSD"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[IFQJSD]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="3" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="ZBBM"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="MJYR"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="CHART_TYPE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="3" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="VAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="4" s="1">
<O>
<![CDATA[时点柱形图]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="4" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="IFQJSD"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[IFQJSD]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="4" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="ZBBM"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="4" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="MJYR"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="4" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="CHART_TYPE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[0]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="4" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="VAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="5" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="3" r="5" s="3">
<O>
<![CDATA[时点折线图]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="5" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="IFQJSD"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[IFQJSD]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="5" s="1">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="ZBBM"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="5" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="MJYR"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="5" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="CHART_TYPE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[CHART_TYPE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="5" s="3">
<O t="DSColumn">
<Attributes dsName="data_zcfx_qsfx" columnName="VAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-600992" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[`-_eh;d%^1USBVs'a"^KfMFmTRS"ZQJ0k=Eo^:),8?W';TFqqA.FQ7EkXBen7Kjtn#*@3,'L
Rr25XODDdJ"Rh^>Je$or`fCs$@O'S(r)23IC4-5L2OVgrSf-3X7VJ3&rrX%U?]AAL#CnA"(D0
.FY@]A&I4Vj[K-TIV<NXH!JSB0Mqg;2P^U"rTYP2+4BADO,#>Z_s+)IHS(B27"HH0?8=n_&mL
je(+:V'2'B-1@e[':-^.%Pl&3)lK'-2%i>%D=q`%]AQaLIAUuQR,aIdLNi8qGpn(*T\%VE)o)
BZHggr_6mHl\f`16R9g9:Heu3ng\ng?E.RNJbdk<t*F53/Tip,!6Aqs>e%Kg"s4GhnT#CbRE
OaIp:F>pATc3il<J&-L/%L5Ks>LfL0l[!:Z"s60U]AbC[b"^Joq%fCa<K>X\%*r+dJh:nWAcV
qT,=oqq3rlCQc6LDIkIIAfCVb>UV7ESCGdm6lnh(Pao2:NtE#,qLMIu*cs?'%$bELc'4LIqX
EVgdo<VhCC?PqtG_IUVoPe6<jd["m[bjJ4*K8ERU@m*@6bSpPE88(gfCYeY,<!O3s/m[o#*?
9<.F47Bk2CM^]AZJrW.`TafT)Q-c0e\"ERGQc:ji*/>-rBNH:$SrG,!IfFNp$Kq1!%#%b4VP$
95Z9%mN8.K)&moR.g9iA=ghED3Vh'iV"mJ?A$kpO<`_5H)l(i@LE6S4hm4$S4c.W"M<@P$i:
PrE$GfkE;X;mA4s>)@Wj&UOiXE:k?^[b.?<X)dp.8&:rMAu4b26'[d(6")7M$[=GC`5MmE0m
6eeWq64A*G#*a>MEK1707&b^GiQ7<t-18',eA@a7$JK2=NH+YJ>8floA.RlEl(c-$]AuiPNt-
.]Ag>'EU<kDNmORM#lq3I:$Y'^&2h]AVjW4n\2N8u[_qVhB7k9Nk^3#lW1_GIraM;6oo\k;3Rb
(Q!Z#(0bENfIut-[@8%DM>mF;!8t)1.cqkO&SfU0cTiLMm,>sWN*TODZ[(_T/Z=41,^*HlrL
X+C7UhY9@k5[WAX'5>S;b's5_BZ1@V[.FlLf@m8pM&'-9YEojIfmja#rMN%m3s#Sgjt6OIJi
);MF2gO8WCg8#)t_\4NL64:,NQ,T%HMp.1o:f3d!<ue.5nZZ]AA:$Mq(lhB;>U[gL'L(g"j/A
I)YKm[^-\c]A>,?H)M\6++;PoCHIGa2k![XhXo8>7o[E?80-s9FH;3?bKHZb.etM`H4V`%YYK
0g[g>%)Jb)^mZ[$_"Dh!I)3\aIs'>M]A8&UH9=2>i64X6OiK%j8`gjR0&$;Jm<Yn6_C8>I:MI
a7e+?Vs!%>Xib'Z6FFXNurD@'FZ>ko*Sg2Hk%Bi7C<lAYH.@7&c9&.B8;jV1A/H/^L2'e:Zt
-p-?]AKPRX0biANUkH/1m'2jf`:K=?J7-`^iosh`b9"Y[EHoc$aW\d0Q"/9Y?!G+1;mU@(rSE
.5*=Do&sdZD>'@:9VKW:*$m0;_c'0oXO*<Dr_;GN5iY94)2k!:o&h(]AqFr"3E+cmH7R]Am/o^
j$tW(?1:X6OfHfGI"B(HLAAXX,:/;-agc%P26Z%2HD,KeLdi]A3]A3%o@^u7iqs\6)<2+1*a)U
[jUkcaMaNB/&]AjQTX45;;e=1pAd5CRSD#W&m;2CEhWJeZ`P0sa;_t;e(Y"K8BI*_4($pC>k:
Og=c?hqc^q);0U879H8_f00uPVIL!,WT8+H>Pn?;eSjJ87VYtL"U'iF2`=d@VB10.FN16TQk
W1g'87P4J#U=ghbIR"YFbe;D\=D&3f%LW5j^onFK/>%_VHF8Z8-i=!/t1XcE%pWnLSL/-ZQB
Vnjn'nm.PiL&_-YN^#i0j!?n\?+6H!5t3qU"jZ7.Q]AONR)Q<1PG4YVb/mWH#%q>Qp8O/!*\#
^=`<3OT&KmWII=2XuWLuT"_2P\1o5K=V+p)@&-lAB500tB/B7RIMD_`&B$(Mu%cQS7BL#1X+
;L6b$,DnAss%FVF4RMo!b#S/_:Lg;<a"GHgdFm`DKd!?HMQu3]A^*S<ISKe!5"qD-Q-1[eKlX
Z*Sj%GBdbA,\rd[+3^Tc9mZ18#\bh.sT;RR$$[O2J'$%a(R77I+;B<7Z%g%l+R+oPoI-S,]A,
E8^3U`fO*,6c$@dR[4SV]A5aLQkAp-TH&P[\!"V_N8sWs-IOWfZZg%g'*9'I*f:)"D\p04"+S
U'7@OgLleU,:r%>lB/WPO^(mg7Dm,Z$?f^2p_D*jPrUff_l>E9Kq>(`U,*seCOFA+6I%Xp<8
1ulYGV,D*V9EOPLECB8e!"!9aa*KV`Rs2T38sf6@O5-0ncJR$7GeXYaotR>AeW$i;B5d-MIX
^k831VWIGdX9["'t&P!4te-dAC#rh;D,6Xqm(5#TNpksi\h+Z?_-RA3-W\:$Y$KNqj:N%neS
DTo:Z,ThhV]AP?3#ZDnLJK^4)KtlX0a\Y_8.U,U"`2(gbb#,^8)C5l*#]AamcM\th[qX(ard5@
(q/^tWe&<EFuWJEUFePL/N6`M=L6^2SWG-plc#sF#XojHA:+B6Z[<Lqqi8n5E-iHmrL!quIp
H5oCBV,U#Vi<>Ha,,(K[Kla[#5uN+I!ukA)mi+t32^_\PhmtM+"%p'>qUMulInjlZ:\j6cg*
LP9)d`Mco#6*!M:6f/Lu\4&cHPfG3nMRQjZhc0s#FB[''VL:'F7Gli6h[JEh%aJ5fPte$TRo
:([T%g\um9un`S=""G6X1UXHofP^AS>4TD+)=Ac1>,H;GnjdB('^M-%+Gs$!glj74;Xhj?+a
KS#u*3UHhc/I>Fd,V/)F)LRO)m0Y\4npSf*UkV1'O=#Da>ZEhq,M=[2'^"uj3@>q5MZ_EL#[
)KPEsKB#*`U,o7;;o&MW484*Q&V)$/\lAnkHtA(E84fN\:o'ELJVNB^3qYEm:L0T:Kk2gugf
H(M0C0@o;.HN(V(5OcQQ##+?SK,.bLRhtt!@cnD+(SVPuJg<;mVMBY7^l@j"/\uAD#;Q;!?8
UFg"H^u6jnqH.^[F&[&q)Is?[lR(MBp2jbj0V&IGti;\D"7EiMV-gl(%p,Hd.3FSJc0>8JG1
26f+SQAYfcA>s6#t.T'.@$!#`OT:&qi37p]AZ?)OjR?P:q#]Ag#Hc]A]AacH1"N[bhAoR>Xul!V>
PSGFU0)\lAuE*tn.%MtgK(efOdC'X3[IIKM7?.c0[!]AY_gS+VVtW/r955#K"3afL2D[Ilo/P
\>gu*04d%04A?13+&#I(6KL.on(V:GJ$+H,XH$-9G"VCD2U&7D;tb0,W2_X5kr*Y>`jE<:^&
^Q0t4SE&bKjY-JB,MSUMBHH/"5rtjA3O-]A/FN`r3\&7ijc%@"NqqHbMfECh">N]A%bQOb7sQq
c.n2gBPO[<!W2b]Attg<s#+[0jE*5Q#K>\^G1fq/b.bb-%CrPmYGj=@M).m5PddIIZ4,4fq_#
:M2Ln7+kN'7-Gl%6mH%bO(8<1t4.rM\XU>W9S<l:FSGkW=\k[]AirM]A81jDAoh0/m+R`V+=Dr
q*7u+I!`?&Ic")E6bBL9BY8\SGFZW6Yn-`r5i8U$8Vjq_TRPK,?"WoQ[`YcOKk&n%^UTO@&5
I4NK7?0PQ9aeY847mETa.mAI41lI&`@mh4DaE/e/Wf7feL]AoaZclN)=/cH@WTK:e`CAaHOVG
/P9=*_AYg@]Aq6;@3ZEk)=<lk/5ks&k/fBrq(/tYi+E'rH=TN0Y]AH:X"]AOiU4;`TS3I1o6XCM
$/8qHjNtgd\;t58^U(QMqA&6uNn@S.-BF9Xia?-/n3&.*Si8-4?a58*17p50]A!dht$@@@!^Z
r%mu0XD[_N:GgPV4]A:'),[]AG,k0DL9%UPicM]AS;$d&G"0HTc:j"M_3M4<#'dNO#h7nQi.fd3
bg*9<ElbWe[+j)!R2Z"0.?A1l$kH0CLWd6&M(!3l#NDD2in6r\H(KGLB+nfo^lA3%bpWgX-X
as?@GeI$Pp7l5)aVD.\d#)o=e=:jTV(Pm7G>'/@Eo,CXZD0PT,hV9)YH!E%=nB!(pf4K[/#)
h&R8@P;kp:J,j]Af@QFUa1'.1PFX;FcUk$k#ftl3<s+`t"R6rbGT%Jsh3!`XQ(VgYjXSl"[AP
=4Ob[doZ=6jEiL!'\ND8+b76<Eh%aIfTPolQNRleei?&BC`Olap*Qe=>Tg64[42.`\<.(_hr
6WRcC@OP]Aca2.Z]AJ<6GhO#[^rbM&T+%)s\P)r:.I:@@_O`1Q2;-$_1h-3rL><EX5Ubc1KQsm
5q'b8fhcH.J`doc!fAg6t8%+7fs,a=6oLAWgd[aFOdPO546M,mn$:fJd7=l7V_qV.L"4nBs/
sIjOSE3ON4oWljQ]Aj_(+krU5=#h;"0.iCaCOMXE_%8A+#;1j$L3lCi=$M9MRZPD:KAVk;?i^
<(lDTl=17ZH&#1>H29!K7'8-i``j-QQ>t-*CM7FS1,#is+7fs4gGpYRd\N_Urb!._`'0flo!
VHIf?]AU;<_BX,b-!S#nBn">1&.AbEY22CisRRn_;]A&V'(U"Yi$"E;QrgBJ]A-NHTgN)^%'=4;
=,AO`I\8PV]A/Isi&fPJ/&%fZe.O5GRqh5H6mE(7]A"bM*fIoRs$FU=3ds/b(i]A9[Ap]A1VYHE'
;"DXEeK2$f/3sg"'[9C75u-__l]A<D8129G<fNA?FLSD6\k;FHSbsj;a]A0V6k!X0T5i)5&eH-
\4Wert2,K;"5kbQ`4#Nl,P!t`^8Tl9MFq\DOoS9rON,HaX+L+^c:%M'60=,r$d#>4XIB)c*p
1O:,W8cEF=B&h*Y41'$L1YF)qg2TH%R\k8^nsl)5n/S!-cVO"9gQ(l,AW<=AD8?5oD&?blHi
^4P2>kf+LW2)H^9a`\V]Ak%r4haTT[8I=0..\GWK'H%DLtoeJ\d]A8Lk,?Co3nYE%lO-R0&Q=+
,j>PHq]A'@,0lsLd)J-P'jdnLt;fWe[Red&8?gP=(XORjTJP,=P8R%lQhC?FFHEL8r*)KYC-C
7fr(m>""!]AE!6$P#]A]A0DF5N'"56m<VL!)Fjhd=%&<o/U.kOfoet4nQi/q?CNV>Lao7#4.i-[
q?-8[G7T\:ku6ITP^bH$]Am&'hq^4hAk?frS')=Wmnhon(k)7oX\X6h0ru)"s,bR_[sfr^J>q
o0OALB=]A#sUK;FKDDn_sMb`OOT3_gI5>PrGgu"]A,]Akp_lG[t2ff574nX`2SqpQiSH42*JV4;
OZ*f6GUT2:OX\D!aqV_:+s*f%FBET)c"2A^/]ASe%D0Jm_q5H'tWl.$[XOL.>l`sZ]AVdr<k.;
Q^R,O)\+SQpgb(K^/#6),3CtJ>@@uJ4e\72+rCFrI@D`e3;^qMba^.TGf9/m@-[ck\3]AqsVd
.U!sg0KuQMJGTK;.KTrF^(^X%Q?-\6!6r2[9Hj)[>E%P[2W=DK62KDh<W>&Z#9*0A5**[4JV
q4mif>H<qtgtB,\LX1;S'"qCj:e+9taQ[77g"=lqoW"2`3hM%/.7-'k#5(.2:/oLe(0s"2H8
)T.p@WH_a\YAi([ZSDdROkMP/7+h,%U.I<D]ANY15ZqQDlKB<FhH:@pHamk[5V3/f@rYg=KCW
Mu.3lc#hEA#O1o%iu7BSUDk`oLIS[13Xi2SaZ%BX'8q$OAou>_KB+;R6*SoheDVL<QtEgmth
d8''[GmQ.'&>Vc?$QZF@$iZ4!i`Ahe\-DURf<jC5Se&o'@`T,"JR\aP'2cDI6>31X32h&n0A
gIt$5I0:j-Z]Ak*_sj3FTXH6V>FF.B'GI[uo1;!UD]AkPBeI5Ef[S;6Lh-oBWf4)^6H-RQmC@;
HedK707?1I)MRAAGBhj/_>K7o4.bdI*lq,9STD/W,_iM?7;/G9nkT8be51sju^o:;m1F$^16
EEFJU<9rb0qEf_ZYA)ubqdGSD2_"DQ\rTH>1r1sQfJQV="W=4bV]A8@5pHN5,Zfnqi4=I=9@G
rLmq;dO)\.@SIV)a^GV;[JJ1>8p!iN#$T,a).L9tB8Sk^;:6IP.YY(p]AcKH2,;qbug0An8ch
Q*6U,G'T;j\N)5Om6*9n'an[MMLssSsaL:+ab4:FoMSDUP=Wu=1lT;]AM36^XDNE]AF9UKDq`^
@4m:c@#L_7'(b[\MAA`S)UCpD%7Wq;#<r/qmbu@![[E6iBKop<S2dR;iaG0r_V(gZkp@-B$s
Q-Iar3h-p`NjJi9q)64s#:(`Sq7R<9m(;=Eo]A&LE]A6:5TIA#Bk,VA6a,.@aH^q^2beXpelm#
<,LLsUHGP^C0CB1]A%puT1;J^`Fs]A,TX!WJh_t%?;ZPP927X5%G%;I7>2c"R+G_B.``H)c)I7
FGX]A3q)4145i^P!66?l1U^8Fc>B5Ru!lSGu?78[<HQ@U9\n/dkm1Jg/G_3iLGCA7il<C83?9
ja@'$;aW<9XCV-+.bY3Q[p%.YPRniP/oJM;cHdeK/o0n;;4)MjoD0oEom*("mI0(ID7jAGmI
rMq7W?OsJMsp[@:g%<Y-&ZNY0[c#'MCZPGXD]At)T222TWGLLC1_fBRj7?62Q(IIjLO;cR6Pn
e+T=]AiU1QT]AC0Y7h_nKpMSFTZ4o5.H/B`[S*j"P@j?6_%A>E=B%-8jPDR#T_\*DRK=O+-R*g
SKp]A2!;(4,&jQZsFB;Im@K,2kA+B+9jAe15>g"NeAP?pZ#t=25a80`[\')YtrrT'<LRQQ>:Q
';d?Td9()X'afWY>n_Pg$=H!c'khR$P0X:Rl/71f+G@SUS#d"(6_0!.uMC3amtaPnGlp=FuD
M?UEE/!-OaRfQoY&nLYX**.%[_/"l$=>R/WLeMh='d1h]A[HC?W9)00_iGt`:gc+DtlA2?mI,
(iBk;H!0.<0c+[8A0+/;95PYcT;Nng#JdcIELm/N5`W!a%R"S4h8LP,kN?"GF>#Zc5bq!f4%
>UAb.2b?a;b`o:D'SDGX;/R7u!0dp9:]AJbE?G#J;N+XhriEnZruIajg+K`'dnmeGAHI[7m+Q
pW^C>?<6Z4CWa<3F=VrSX<5'H<r.nNAtr6%m0GDU5?2.+;g64BMKHXC3Y2h7'<;69?+"4u=#
J98e;gY(Z$#E),+#Rr.D&"2ld-',jWsLpO76-0jb5HY5O6'L$(;tncg8WtMGh&hJfV.G[&B?
!p<(GX*B<dfef]A9gdCZaASt:$G4e\gC4fJP7?Bp_>Y<<7`W@8DEI!A@fmR[+)!R7X$i9::,[
mn4:F>XB55=gZD+9U/M!M6rHATUj!`Zk$n?9cG>@ip`>BAdM@GN62=B!Sb.%1Ks(1Z>N6CTW
$IAj]AcL>tdNO8N-hQ6KYBS6gdDBP>4;V^ec4MOUF&;nfmr+Mil+a@8sCNM=Y)+nH9sAWC?Wh
o_W"Keu&[;l,]AQCoII%^FjUPXmcktlNiI0Bi6!,2P#t/?]AVl)Z&9-a@o:)M&_N@^.?]A-Z`R+
US'E^[<;9]A)h><@oLlNVYJuaoI+7FP)dLAmRBq=SDFq5uKga`E"sb#;q+f[7/`u:Edn#(2T.
GO#U\$i[._%I:5nM@'S^Y^CMobn$m6"+hHTKW1KEU<!=[3c:Q!5-eLgVBnEc>HEJREPpJ;JT
2C:;;KL-5H+qGuftjf>@J`E-=`qt8B81_<Aq?FZ9`_<!X.[XPgt36MYB%[Y$aPXFh8q=@R]AK
?*$GJFH`H?7QdUWGS5M=&lk!H2NJV`8P(9MVPS&`LErE2rLNXhi(S:4;ajLp^a;Y34&BK16c
ea@4`-ua-)?b^B^2sAO^36M@EQ(rh:?CbmII)0aR5?DE8mJZ&2L>ZpRf"8C,B*aWYF7C_2XS
6F+/[(Qra3eqA'U"WiN(o4obPE,F-553MJS3?#!"c.pMK+q!,!Xrkc<#V6K>W\m#_`A?ZV;J
AdpT7P77EMJLJ(or4B`5+gu1WYk.([+Eg3k7PbaH8XuEBLZ'V&`/<u(5bpoIW3[NGWe5&]A#H
9(NI%o'Zf"Zuu=b4B5GSC/8_h/[o[fCe202ic8K),R'3?ENV;X1*+U`mN1p<0"@APj&Xd:o.
c$q,1NcEd8>?eeRDT7XIqaIAo&gG?>]A[(S1(f127=I/2U*FUbPehc'+"]AXk*b`&>H?+:kTH]A
lu?Gc$ohA:2A^"I#.o]AVfKV'uWW8k0C4!FgQTYtIi\alQPqb[,[b<6CWSiRh'@D;)Zg"7ZHq
JAPCO=$59n,cHS?ZR%KgV@#4m)MK!hiL'&'2`H.jNe*.T>""'YU>5<Uk)%Fc>nqL2]A$o$G(k
07Vq@Sf<;Dn@'qlU1.GAS^up!dWf-Kg'@6YGaA]A_uhLM(.V2Ej%Ss9fWOnn]AfL@(R[f)2E?m
Q2>n[1@]AKN_d*j-odYf4*m,=6Y(3QTk6[[G#I3=%A?ZN;4OreEJMKf&$smG]A@E`hIulR4C\m
)*-nqd.g-_'8Wr*I>D$WL%5:_gIWDcu?2WUsBAC+FT_r1pVMVQ(Z?!_[4RgL<J4b`_J`22;/
>ZZ$j\_G_;Rj+$D0W1pS8ViW<Cn?W84<!\+[/,?4E13QUZ7Wj@2\U74H1/R)!mfiq2M#_#Q9
>Tp!aG*`'T=cZ!n"V\lHjOp0f8g,qf6S+BpdM:U>&Z>m_4i<B#F2'eH\[sV:>nu4KJ2:%2]Ah
.U2r0l$sCj?=]ABhH*?5]A9e_[[I8t"9"fHA2gaYZ[>)I//;V6nbUH.g+kBqM1Q(6`$i@E4kYW
OMRrD0?<0TqFSI#EEE/6qH5gXgkph2nLHYcg"lEPotC"^+p7,-1!;_s5<pF<=N%u[K1V"h?J
TEHtj35&U-F,BV;NBP*ceC0]Ac-W8tKeMO_=!4GP?*kHb/EbJRDWee@t)_\Zf]A2^19c-I&\1/
jP2XooN.eUFO'i$d!7&0NHJ@c&%n_N9)Rdmir4a2I8WC=:SN<N8riNQ?N;T%SW6YWIol$I2Y
=*K7PdidVkc2`<8;*7c[2P%A3rrs9=uA.EH[a=[<71dVd:aX6&?6f6rn/DOp9.$-C^n!Sm;Z
.JeGITMEl.rG0_Or1kB;j:.o:s8BF[R_5u(k/^pd=@rdTno(4?oY7aei)A#&IL0LeM`=ltI0
4\j<^g+TL6jn^T7\V/!^A$i`XR[?q0\7I6S0U(ci"CmW_1IO=8Oj/?jOuAs@JOO/Y*B8//h#
sme@@_$<1I,$IClS>'2IJ6iBQqI":=WLQ!=NeZU2t8dWkB?^MCF`>^PmS_o[q[BF>0^=62II
-E\\7)bGQdaD'$4G\dGu7"?FNbI+:<9m`tB!Bi$)*_j>LrD2FaNe7+2_-=h8mT(-D'7'KE)Q
dZK@2CWC*,G#8\kBf"Dk-)`R&3Cg`ET"b2D@<fWiU%:5"6&Y4*ft\@UdTo33@`amRmE*]Ali\
,%lCc*o-%MG9iB[',0ssF@n!*GaAr*GeR^aP3P8oYf"Jt6A+%UrGI>TKR&QCuWc9t:)l<AoN
^]Af"Z[cUGFjTU@0nh2E\$!!Qmp#Hsk,K+BpLN#?qn:86+pmHB!1-,u=r.W?31FH5?8E<*3h]A
L%ZkT?.0[gFinM4p'Q6JnX>J+`nq4ac[6:-5I9WZG.G$]A&b=N@`$*2?(N*rVVc0_$OFqS3IP
E:@eSXX?JT9`Yb0aWfl%Aa6Uuf9^5Os1h_Yr:pPBBab+'q'8s^_T*h0,^d4Je43Wo$DhC+5W
*\K(9=nD]AMuSl&GSU/:QiG8&Wt;T=/hO#9Vl1$aH32M2>Vu9IYXD\FdYWF.=>^A4+r:"4toD
QWaGur+r3?DJ1DUBP(`1q,u!Lk't%m5N'%-+(l;Ajo24apeBEOcbAesA+M2hr!g?&X@j9q%^
WJm"4j?nu,HYQ\J5=lP=6nX+XTJS/]A*>\maL90E^lq4T-/gm6c,\N('YB0h\pN57pfW"g4ZT
H(^Z^>YZF(kS519kP%(ec%iIqW0Gq0)5Y0,lWGrDXd^86:=51Gk,S97@a,I6+Zf7)`0Q'.5j
5K"_g)ftfD&lSTQ_^r7%N,JBsE+^&ILed?DSpZ"^lB2d]A=,76JHc/)[rH`@_XPL1-WaL-g+n
'\lcQ'gRVTWBtnGLaSAM&is2A>o,6J9R]AlP=R^]A>m*uJl!^#e36kRp;0_?5.uE;iQ\QPpe/o
[I?H^J0YA.gTim-Vq^ORXD+68*h@+3E3_it,,BRd51Fud4.q_!*8aJYM/3=/$g3/iooW"&J9
K_$+'TgYe/qe00UED5#_;C/%:b6\!$rGVTOC]Ajm0Vn<pRei6\C/h^mr`0bFULKuiJnW0S/dd
GhT7XoZ(u1W,-oFd`6?ebaiO/,u=kG$o).3`#@Z'&qEu$2Kna:O!FK,T\N4K3Q@"nR6Q&A?K
fJM:b9?`uOMA--O\'*RRZ1Wa*cG,Kg[9_S!)9%oq@+;nQ;+SVA$.Nq!!:"etfe3F>Nr]A-b!,
s3=5e1kJAVQ48^XY(@MfmZONQH;tI+I^,f8iro*$01_K=_R%d2T`lfA=$J^6g,PnZa>VI^aj
cHn\bbU1_[h$FbdMJAji^#*_hkAXT[pSq8-E!+G:ECTpeB!,)U43P+FNI!r$<QURcU=FKus2
:[BbRr)LF*jB;R,RLq7o'H&G+mI"VctGi92Of-Sd6V'n(_Gbu:QhE^XSlnRi,Q`O3fn*ujl$
hn`A;*G>:W^`$Y>u'lYsa9e_,0.&+C_6GSIe9VjkNX8pSMG5PoHh&.&Z(CLL\<j]A[>Ad2l4`
aQ(%X5^n`UkK%h%ZjK;q\'JlL$9E*;#@X->Y8TQ'8eW'\^C5AJNe1VW!h'=Ui(ohd!(>%N3L
Crk9P8fCdCUD!D5keh2a`3sP=&hJTUdu^S*^k4J^$iq_27AQFl>1eYaCWS_7*9b_h`(56F;O
3r]A&kRQcbT(c)7Y>OAo#i(m^aSXBq!l,i.bDKLnMJ$48">bUgXO':i)")Lg6B]AA<d#:m1hu?
2;^6fJC<!.m7M`-:6j4CoYL'VHXA-)D(B?Dq&Br4tKb=Eb7j*cCIJR>UfPk7m<km/F__FGmX
4?1+i!?c#RQd[lML,^8u-sI$&EGMi"13M%:8i2#Y6_!A4Zc7q`N'hq>en)2R/J5d'h'ko-:^
K(`$4M>*+^'g\4d=/4c/+tu3>KM"4`_:M@5%pNf6!GCunfl4D61=YALQN#b(A0\CT9!4d(OH
f9Bjm;=F[H%':i]Asad\h*+P'J>_&$IM)2]A4$X&+oOGg,]Am7+DmdSZ0^-d&bhce?PF,(K\2a?
UjEq3+8n#>%WGL/$.:#=>qZ>Xb'kCO;kB?d_MS.j1g7r4X2en]A8\:2l"@"9r%#B]A?UeE`kl?
WJo]A-+`UUGZdl>.aN9IB_]A%-11`D+-Z!*A#qT9A:.*p@-BK`6L)+WP_Y&TIgY++cHi4L(`8H
-e]Ac5:J-_`VG,+LLQ&fGEYVS$K7o?*PVe[q-G0'i9E#riK#kZj57Ci9kPotmd.muUT)cU),T
]AhGmS:]AOmr,oH\>f`=l@1$U3n)>/2Va@s+2=Z,"&KA/qpaMoHN-,/-DURu8Ldr3Y*Y%eUL79
ClSRoEQEKW/+*K3)ME:%J@4:S2h2;NrJ+)/jkFA?2:9CP9hO$7f^NV:*9G2DM)G.SJ<?;tjb
MdPcqW7FdgWT,:d[Y"ZXi,l"u7_X3o-,=T#]Ab+CEMlI':<2H)g4gMTZDf&#GVn["R:;CcZcK
#[>Q5MPF,\FoZ+de<p+CrYRi<DAdl#d[j#e/ZS@>*@FTfQV/%j&m#_*#jO9G3q05Vt_u>S_O
0FUFfhJ#H+q%^`?9(Wa@7@cbX<Ga:inq(4^M-R/g5d<a/3L+13o2[b;YQ9fZ"Hb!k->WZ(qh
WT<ZU9j1oZ4R=>Wk@9[dZJFX,$B6`T+;%o1hRQT_lnVSZX"KE-%j1#Q;=)OWjom/jn@ON&^J
KjIRgXjQ[[hQnqj.6\/&*'Tm<&buFc<,cU!0@T/rqU^f,)a>+Fj!sV?*7hj8Ukg7V)g//]A:I
`G/GCO;4.HpE%LV6C0Np)s%Ei"Jp+^LYYq1B))@TWQ^82_1iG-<J/s@V'co.G`j[DK4iU^7W
j!^I;Y<bl-e'5]A4rl'=4OaF_KCctD(*h_)_Daf3AF2;=$h+F'"#R$_&Mh/&=BQUKik\Gbd7o
L=Ya%5+_'4*%ojlYtg^W)+O)"\MHJT!>4G]A%T"J?4gkehus`s4Adr)u`Z4Ga[dp,JLtTn5Yq
4Y'd*)Y;[p0r)HQqaIC[Q=813&n4m]A4X&F/_'pU(onP>c(Z>nj?po<J-V`:1\\b%1cg*us'8
+!NTo-4k!/+:c)1%>HK_h&SV:.J.M3i>,r_(f$F-2Gb0k=fq".WhJ#=e&/@>sL;lYY,T!dUn
dL3f/eL"kmg[X_8m"/ceZI%6kuhXeYA!U?V>T?I!5;ib$?C)7S!h1G:f%U1_]A6F6C;'YE\>L
Bf)ua.49$U@XlmTCr^W,l;F?h-YQlECK<rL61U-GBjpGKRi<UNBuNn*cEVoYi@!\RWHX\VSS
?8EDpfsXllZg\rnF98dGWnm@U9K5Bf/Q,GCKB]ACJg$4gsY-o[4ZgI=10d',6R6N&/D0jJOMA
&IuNhJD)OO>r=7R2K<J6K]AeUGj<uQdUXpnh0e.l6H"S.Eo`L1NE1#)[KeiFFZm<G$OOWV$:,
/"CrFO,f2D&EG?!GX%8pPAL6nV#NId1'>nZNe<)I3m8W8fnY83Q4GXa7dU8*un#FPXOJ3+;O
tLXM_,F.%0FU6%i;jCh>FKGGn9</J#*Z&Y8OQTc'`bcViNr4ujpMt.qj$.P7V,f#*7:_Q[Md
4r`>#^HKGpC5*IcO!Z6hNq@Y3G,K%,/@'&O+&<2!8B\Og\SaW!5?hP^?fREeidd_G,&M0Ui2
-KJ`&n^W884[N&NaIrUu/O:I0iBNNl6"Ul`Bg"8k&KN3V*p)i]A;ge[;=<1\Am4U8"G.-X1Z/
Y(FVVWfBZMrRYY63U6%rm9sLFn8dQD]A*19odnW5jEA%,65&)'Qi6,ejZRN;G"t5&SU'M(=i!
7J"_+BSADn4F]AWS%dJb*Nk-&1SnOKq4=0js9p@O1&)]APuV=Q5L)B!:/\Z7@^03dkf\.&OXbN
0Yu:<+nIq/.c]Ar*s`XKk^Zdc5[kWdf!^t"d,1n]A@dX:D\cY3s/hD'Da+K2cWcRg"=]A>23bSU
]AMhTnT$U:S#^Bg3Y"d3@,JuDDt4'.C6K]AlDe`FACE^M*lBMj@b$'M9c(KN:\drWkf3YY-Lm2
jeSB]A7QOt:,s%7T65nP*mE95Af"qWi8;#,l4sKMf'0^\*@QGZe]AfE"!^$beCnr2?ud?I"g2+
+$8Zf]Al3;+UgCGi)nfV9/5AO$]A&)no\Q*hoQ#1U^+^\VBR3t9#F'VBT*OWmJ!$2`rY5[g]AE2
Oq.O^@!-A&?g5+reF6,!6`o!q!!JYYNB^F0KmGUH8da&XqQfJ&`Y(JYH'olR-iu<dNZBO#=H
O?Rd1T>f5X^$13:LCCO^#%\[Z-4(\YV1+uK@>8tCb\X$U\U>.kr'sm<")$VH[&:lp2(Y?#)I
Zc:4\,<FqjgUI0.W!g'EiCG"#;/;nT7$J'L7\XtQ2"7AqmdJ=m<TmsJ@u8HF)a'9dT6V[:<A
kgaQJYt<dn2Y'Y.5?"5N;?m<FC]A$"AU.Un,\c=9iT2SMNVD`1ueP+G.T7oQ_h^C=TT;h!<09
Z(K8@o'e9q^mZ51UA&T4U^2L7a!2!u^=RH%=2?<u>#_F09'Z<PDZ\FFdFL0)Y+B[C4<mIdHb
7[o:]Aft_U#O/n1ogX#:SU*EYX'"jfjA(pH4Ei$beEiLC/29dL_PL^C-G_6aqe$5^uF)%>/]AJ
GAgG6;I?'IKcSJJ(Q>buKRqbdP=>M$8:EWaWcb#]Agab:@Wl,'iBngdkdlL7;;22Dr;9&&,]AS
.3[T!OfIQ%=jchVf+AEIu*;g[IF,r?iaL^E6LU70gRNl6c,KrcI6U;1_L-+OJ-T>Cq7#P<#_
Z/M*Z6:+.[Y&*H_jA3X^Ug61IVrH2-U.ON[nD]ANNn$5$.6*]An0k:MPK/d=ine.q,\1?RKt8Q
Gr-+]APSa:Elo:\+8YpnO#=O/_e=RY6rns2E%C::EMQg/mA9'qe!Fh&M.KSiGV44Y*Mfaq(4%
0@++VBn^,-CGq4m=p[AAg+O$7.!!]Ai7$_T,`OsG:t.MruN$E(c+e>^PC3U*L-ZY!C<$si9%\
;[C&^8mCCh4Vr'mWpkI8rdcMnh.*fj>bAe#k2<b*?g8LA=g,48n0'FS=1REjMX$K;VAP=BBB
t@mBZKZ-lPiHd\X2\R*L=jf=[J.mBUXLY9.?N,YDI>3nI"pL@VdZhji28\NOC\187mC#`'+/
CN@(oS=ei<m(EL(Q,5L'ho,AieFno$0VU,!PCr++,jf-Sa6lLEC>+S9qdJ?8$AMj\\s[)TaU
CHa/O,*Pf.SKZ"E7irl7+(D+Ujr>V@,p<F),oOUmETtTMlOuiqXW%8o8mVPIB;/mj9!/A?#,
jRrZJoh9+nT:hg5HgWQkNt?XCg9BcV<M*fk19prXG6ZZXc.8]Al^u'e]A4c\1!J>p%kiijZ2;R
MBLXSHNC"T7f&?JMU)7N(_RC%-gE&5@i\j<<gqfHi!M2Ceq]Agji_he!BW?PgE+J,iWL:s^.Z
kKc`o41>,cXAfg>j9Qf5Wn*`@b)'%&saK#cuILc[+W7qR"-]AS>_@e>Zk?1]AX`GWIj4i3Q@(P
:FR+u8(9=1lCB92qrN?Cf^Ts`1[N:`8KqHe2k1"H3=f#IG7;-;"l$b_q,oND=*U`nBUC1D%S
G5rhOnJR_C"EW)09mTDmZTM5+"4F"H6/Tf[DcT9G0aYcJI7*$Pb7h3m5?hDfcc5'"$\_%kUp
R",kmL3%KKC0D1p'SH&Q@qt6m5!YR2M]AY/Q9%mE`f;*`$!PHC=LR8CJ2bnFY!)`;+M5G=qjl
[Sk-esVO-2T(X-ul-*Q1)Z$QE[M+"p.CG&F+=&[i)[&K1b5^l=BbT2^j"oGr%H_@`H%g?Qmg
td%F)[a21_l0CQ9fbpgF8!=/#XDq>8PCa(7c+]A[J''ai(/9V-r'>a$84$qSq%TjMF9A^mefh
Fm@T?!*Ae+%pUW+4*OGNmI./XTr]AQC#[6<IGL!\5"HmY2@lWH<7Yl5S8m(cZ)8)9<Etm,IUO
PaJ*#"k/o@r,N95qWf>'=btuoN$o(g/,ea\<6?9sd-C!dZrbPA0"):l>r4sbbnZBgIqQajeo
1M1MmXZlmu7*.*fP/(+fYUkM^)W'5:hYr%M8.u;"@?:o9[34aB012/>n@RAV7&q0!.qI_]ANt
hSVn[MAi9d8!!u=-"-U#LO'ClY,%,F=W#sIH%Gc$%8dGD'd+bhOhL7OZYtoZ$0qV*<Tu&VL7
9OU9Q;Qq*RJO4:,jVS9`T\'L+f='YNseOGM3s_RKaBaKahikJ%!<j-5En*"5A^1LP=1'<UqG
6NO.Jdm]AXUt<lLrV*gk+HZ02/"q8e`WUGI7Z:,bVd#!8?_Z%#Y#L0MB]A:mJs7'M.kHQA66Ra
kj#bW<Gr3_f(ESbFmWFB+ej0'UI5SYm0HM3%J_iF-BaJ#s6)Qsn+h"D(#H#_]AS;#(0-2:?#a
93#a_5sJ%8_N[PSec5X:<]ASF+hLh8oo&_'@Nk"IHe;=^G195PAME4!r&[^4WJlf!`&!XAM)Q
S,NQ<G^6;2%rY`Rol.<^"1TKVR6*&0ne3N6Hb7X=%!DRR;"u:"`hnFLt_9H0B(PCqlSFSiqF
+6:n]Aodu6p0^Fp0g:&T_BAo]ALBBLORLRt+mogm>LL2!nc2e4:OGN$jgn2t<F!<M%_+/nUQo1
]AnA\)4<SMg$o>C0^Sn%Z]Aoo*BX?/q9`HA=h9RettmK!&6-^g75j.`KuErV2B28G("gU`<SnE
LLa^s0<A$;N^o0(amjbnPs?lMp,pX0l^8XePUMX7"@i%"1Dg[="/UM"Cn&mX8I.3E99>&u^_
#'"r):<EMAIL>?YHBC+*ZG]A_h"J)iq!&jj;NiJTUelo%+#RC]AbSQnAWtMe#=X;2>h1RHi#
cA-Qe_)b+WJL2CeP=5Vb_2gR<\$_DA4r:Kj!nMuNf=;RmC+Udsb03*ECcXT6/Vq-Rgs!_Bp$
t,S=q-G@S0kOsPman(,%4soH?c+jaeXrIp)Ru$\9g6.u3Kar.!8oA@,]AF_XNL>)@RLdb&^3k
Um7jkVAN`=?OJ>#fEXPN3[!;jfPR%aeSVZ]AMbhuH8S1HjAYKC!Uc)#sq1&S6:a8'9Ii!,<47
3A.)a[2,BbeiQmP_C]A1K(U.]ARLs[H_"!j.NjL%ASgA3>S#;B([^lVUsgekdEs7""7eQ.G9l9
mkrO.(lG=r[&;^nh1"J(LSi;eCd(BGZP0622cP6pDtS1lJl*]AE7=,=#/!AGG^pg-d?S%g*es
u\h(=Vrn.:OMuqEMlK_hC$#&t7W]Ai54T/>R-SVoN.'GVSar,"`[;e/ArC*@0'.)\VRS&qBK!
7g$[.7463WNsZ:]Af;-sI`!WdfOn3g'A2Tr!/0-ohR'@o/VGtJM*--cM?6TdWSI81:[a-HOXm
01`mRU$B:Q\7hWQKL"=S+n<Y93_!3P&AeSF'4m?eV\cds\t*A^C;ReCf.D_M9+?H`Rq<ZPCU
0HFUFQbgMu#@Ke="f!CMYZ'hGb<&L89)^I'mHP,Qb%>d.^Goc"3O_JWYn]A(Vl,g"tIoAf1]AT
.#3:4/4TSj0r$g1`DYf$fB^3P2\(h)3L3$iqP#Y_mtsAk1uBc./afC`=_(.P\9R!7qIKA?hB
g683-ai<uh(\X,[8J2.>A*?U8eXnaMm8H!db,o@M`#BW%56m)rt)Ze_kqI^tmn*"9l_d@jH*
*XZ_GX7V59]AKXW<+5kV+ga'l"*>k_1\SB1oVtARknRo']A*,l@UJu(m3hRN_<&R966S9-3Z8d
-QYf$A0NK5d^0[@o?jL'FlA$ktf^f@hhn&"._+QQ%?XJa]AA)u_YO?m9N\+k&VSCeOtE-_b<f
gFhP,"2kVbGI22+DBfaA2lZGs.)6MtY\?["opi(0.hpo"=W*:'I09i\Mf"eZ+V)'tFZ=`)6`
fns5<nFdWmt:!Fi`ms5P%.@hWATXV9Mq;6tJsDdPsFSdfI8HhB46:<[-8b32:qaFUVM\qtgI
'1"La7EE9#6VUle5$J2A-?)iF*/\N>Cj<uHXDZ>!Y/s(p,)]AB7ar,ps!kmSk%XsB?rKr(b+c
OfZCd^N*cLTiX9kCq;PDb]AtLc#M\^p.\89#amCY)G&oQ8afAB+mDkiC"CiYRiNMg\--]AW@)?
;3d5VEY/.iL+omZ!'TqRlG/scOR*^Y^bU*T&t!^cQBoH!6_<g1RucfVWQ9DJ7Z/tX<O%52P*
5gO4-\1@(f?rsZI<O&Y7"ZfsL)Q\YUbDK0OMH:(&1$WLu"Ru^IAHcNiDm`8n8Q]AM*o)Xl`Z:
>n=pL=deN&0cbE+2:_\Eid;=78Wo'6qil"8ibs\8hGXRG!+OQQ#kW%kGRo,SjN=f4T;\S-Qc
,Xss`&2T=c"p.OUO6Pk=mc[W(F.!U&UjJunF\C2`X\OiW![Mm_JIZ*Su[dNL-1LbE`ZQVsR@
'dQ'M=+1ag4@:,$/H>Pi[)>f"sn'iYn1/kkPJ/=^u#@A:FAX4O?0#O1'q<-eJI7WhJnhc,kD
($UN$oDJ6T)C)oa/m#77c<0&fV,?%k@a=du:Mns3F)c'hpVk`g$XmO88^rUBT-[%"7tD#?%r
5KVU(S7_?$mD+Yi3umgeSX7tUV5:kE9nB[L%)dhJ..bO,Qckn+1ssV!1ng&I2r$7aQ=7R08r
cnMlaDP6Trhq!V".8k'mq2A3pfqZp]Ak%qep$)GgXKMK++Yhfaf[.PmlAtM*$oK*h#N3>M<gK
.cqr[@+ej6aD.bK0o**J8ojohA:]AKFch/!(1dX<j9%M%W<=b;&%o/U!0fc'/IXP.LAJkS\SI
_8S<L[QPM!.]Ajo?&Y6V6["rn4tR9V!qqrX819H-W(mE(Q<"l82;;7SKstRClTNHK1.k?2-nE
%@L=DDFea9SR\m_5kd<:C-h9,+K%q=mbmUqk^"iDqbV'9p5&'g)"@ik*bX<+J_\'$>AT.U07
a;IkYCWD^la;`^jgK.PBnAJ(eiL6"YGgA08)Bpf*W[8GJhGe7m-\k"Ya5ill'p$pmDB*KNFT
Y60jta3'%2=Ve=Rt[5j+Q5A?h:%aQPSR^Oqn=q\*c)45BqWkp%nW#C]AsiCWUa1o4WA>eUM.+
&C&"uhK'3#DFi_tcl1AOaM"&WS7K'%o3tc)Al=V6BOl2J4cOlRB\;YM_]AGrSZ_+i1e<j#$NH
CS@\^"jm*=uK8:MNle=pQ6)[;t]AVW[LhLS58*%k3PD5UK`4PB4"\a+m54qPGEOBO8nKh^]AM3
4[D\B3;WJa(o.Q)oAXl)o)\n#APr8rSKl5?9SNVic[cLHMp!)<(GFQm<J%ljZ]AmuE8g&XA\^
*WB6B2QZ0gNacKZOs)lt1r80A7]AaXL=!?W<Pds5Q;ss@JBcl+s[S_.")uIL\dK;rnTEB[B6o
%DGhpVG\OmEgT]A^+=t68jB2!iUZ&IR^Z-aaIJCR,c5H`QE^.K[e!L[jOf5L@?b*e&[NrKlQm
=[>lsdKRNg-?-DJ9:jQWrE87E9VXUlfqrB@h>5j2:$-Me+fo1T,>I?OE]AYj@mMtUfT,s[Oh8
VlX3n93utYqlk,kC9%SYfZT#2b(>I4LCte'iedq2JGs)4s,rYgOtZdPl=ng>*VmjKYmPT>)P
[IZ#]A\aZ"aIX^L;/:1\i"f_%tq16-#35H@lR>*$=_F+g(:ForT]A;Pj.2#`3'ueDUp9l"sdl4
&mA)ubos$h*+3S&V)IUh"`F@U2FJi7?dIY2WO$dC(?Auk09O[a$UB0>,,:&Y!1d^^C*s>8C\
M)%4sA<keg1NGOE3&'@]A7'*ha*gL9.if-]A>Br7Hg)mUc$bLNHr*CO;2AI>3iYo;L#JHA`RIA
$`V(bN6fqM^"=.U:lTC/8I[h%si;?KG.:EIVqap"H8an?Vgd&3%2jZLg\0'b$bT[Q2$m#n+7
Kl+V?[]AI]A6$:@s;=)oYUg"/hd5Dg?+(Xp`AO(Y\nsoelMGO1ZV!C!OJhZmt2/t0uj]Aj?0=6=
\E^$7>C5cX!Sb#B<2G6tNd<HltLO\kg;.lTEId4Q'_AK?5+QsFEYf>%H'Lu-O<8,jd!47c5@
iiCI-s&4&C+5J-V&6^k!Ed,BB;Z\/+jG?T!GF.r3?cS`6[3VaTP<6gj9[4+hR%9[267e-j8.
b65Nb*cD<aD8'*i&)2:1.G-@-sQdZ$<dhZ)gMDU).1p!gEVP@cS2P90eiKe5/:(rjU@?*E!H
$G^Wb<3)N9uL0F?BAHWN5PdS6aRE;@E_j@S`,E,tZf5d#g"gXc8"KSMHoGt%S[*p\lO4j-r)
CnCk_p-b>UCcu;XQ;oQf_q"p7';rfBC_2rn_a9RprdDP7c(V;=!Ns>Mt[VrY>+ei0rUK"i`0
[uZTb4#DTUpGOoJF0E;iqidtg<2J%XD.FC)lZf#T`PS2+kpT7h)7?kJ%tKKKmKK^/eR23SOX
p\b$+ND'FFe`r;aK$A+h83S?X/-=2E]AQCo:-K#d4/=LqK`hY5kQ8mCAc%<@'rL2gbU6'_6-t
l]Ak*4RZ`>I2F)HIPk]AGgDbaF;C7+*(Jf6&,DilG@`>?k;@_[V)BfVaaJ^o>QPfTq3NB_fQ*Z
/=G]A5=-N7>;^=h2+2KjIJ$A.&lef''t4^r^"j^YM5.$5Hls$eE04;nUBY:Mn*oI=\u(U%]AHN
"Hu<Bh$'*$_@.,N6;8CBS0j#A.$p(CVJ!+TC4R4,4Rrj<LSPgT^.cHQH'f'G$#3._BVZ\K*^
@9:tT(SHB;IsFb]A&WP7:D@@qVG5^.oM845FEV?neoo6iN,]AU>%ea*(7&X$mU5h\FK]A?dK@Z,
#lALo=>Pml8u)-U6lTdTi<5G/@LrrTjMtHYOCqD"HFrLg*@9rb-"tZ$lX4Q<5d0KU6"7=,LM
'0,#Xr+(bM?%l/r('`LHhW=CcoN4TcQDK8?7h'Hu8Z/DQFdO@ZP)e?:P0gI1Mu@0N1pHNb2S
R`aC)-Kal5N3^""@O:;U^D7>Q+aa.fG<gK*0%XrOj/NY$@cgO$Zcu,^t'D,$b[J,DYM-<O8g
`boOFO`eF/[98o[/#^>bJGeQ)c?LKe4gO3EKa\7qW<'[3T5na^Hs_?LB]A_#F)47;p9"HPHcG
%AXY@QtkU"^j0/[9/aESFRCFhiC@Vm)Hc^nMQ@.98>Ga2>CA5Plo9LOu\Nk//:&t84$%(0:u
T:q:P`;Lj!MB:>q]Ai"&,^gjQtS"":``K)Z^W8.mZEg)E7=r7c?$'u\13pOT-GK<lfb8k$#HP
rP%s#G^,n,N#f3$pCU50;j2]AOQF^4GkP8VO1ID(l`U++5&pTi+bUre:O*7a2!6i/Ttc?K+bM
cLCPZp$t3ec*[0N9J)bCN*AeKJXB%VhOdL`bYTPHDol9)nXl8uRF6m'I@>mNMO0Fq,kJAK8?
OfL(eR<5hhi,/``cD@J`,E;/Gja7Y<Bu-34SQ)uQKeXWm+Cp#^I:6cAB+3Bo:$7.V3Wh1WGP
l:,r+--OsIu.F[u`aoWO"\qf&mRC"k#[H(oD+n2_l'p-88j5[/WEN`%M=1I5l[<TD4^ILjMX
p11:eRoNHC1Xd5*DF<%Wejbp\97IoIg]A:_X?nT\OpL9N@r'&5q.(/j(]A-DLrcF%Uu480#5/t
T1L[2R0r=L]A4+_=0.Y=H8:&s(/)c$kMr4E0k>udjQE#O^Y!QK?o+o.+q5*C,3I1mP2+<hSWQ
Nr*n[m(aTE#ll_q$n.'5)<Q&qrTT5!/Q?WMA%J49^ql]AHpAgehA,t9N_'jr@F(XTS5a4O`uF
_,lFR=iEGs3aPOFp1#FiV7ieIse6>O"Yp442Zk&B*tWCYhiB(%Y:COZTRQ]AWBL-Hg5(-<#Pk
4Ah"UupC:W?aO4Q.df%'Ru2snJ4cQ5(D/(8!5Fts(2ZKra4@DLS\@oZeshhP!;n@0oWHNl.+
CY,\#&Mpr2T$PrNiSIU/E?0>nB"gF("c59RQfg+kjWt>_"2b(A2AJTWgDhND?;r2p3cj+gkJ
^W:.%ERAkE]AkF4GP^ZA"&<Pc.OmF]AK2Oi7hu>N"0LqCpROdH6'&F[K_:;L.0OmKQ^0E(cB#n
3132)!XFu)R$Q:,<=1M_an%irn%5Y;ai0rm/N<T-3@NS:oHM_Y)9cG>1>sfJ5\[1pI"i9nF"
1MEO0LD-9"Vhd,H!Qk]Afb@o:H/[U78s]ADGbXfSST(S:XUZecAfOn>$$C?#FMbhL=E17p$.!q
iGhpZY2J(rD3;5E)"^IglH^8SqIcG:S<7TY:d2sLEC3O+Eg?$8jcg7a-/`.^)K4Pfd796[Ss
:cZ`!8O3uqK2jB1g!?3]A;)GUXG[&&$U;t@<A_G7pS>lhgPdEY/loH]Ar1KueoB1KCe&rlu(Mj
m&iR:W^QrF*&/=d#ad'.]A5Nc\A)oE)C!t^jSq4"&Oag<UYqC,A06kj0o2;-6MB:jrTFHPs48
!('0/RDn7(H)^L!cEaKYh6l`Pqo*Y6UC!j&Q]Af=o/PUKGC<t\ZuMkmJ;#:?A7ed*C:Q&*0lM
lNN/o$oi2?Hl[7-u)`<Ses)b-C&4FH.d'/qp>]Ar0cjPC(o=+j%i,N]A2ZZAThU>s,;5m#A`:A
4t=n\rdle`=fAtnrC8_FO^)lKh-@ptsSTp\#9IRdWp0$NJp;tG>5,SiqK"U(Hk`s[a=$'P8D
Icltc-koa&TuZ*7=1,mdr;@sS,fQHH4cA]AdVmcTNTC)Grf\liUXO-RFTM/-/1F/SC<oYn=la
P)K*RB*(>N3TTnIW2"+WLVlb'LB2PuTjZgtp+8flpEOmmR'L]AEsE1hlC^0s6M%<L@)Bg:'2p
"k?C+PZ9H4gPZ5QQ0V<uK=iFoIE`c)A_i:&V0O:m*$'A4V%+D'`75(oM^oYsr9<iA%Z#+X&H
g7=o7V[=3_u,TQo[d0`7<-;!2fB>t@e!pk&JJ`>6IOCsd8Zs!D[>jFk$?b8f(j_Y^h9R^gsL
=OF>Z4DDU]A3'A,`PRfR\;UTrZ__D.WOpmJ+0>UPPQ1N7:9`3]A)4gLr5MLeOhg#YQb0,!q5,>
DV&_!iVH+dV&2o*o:4'jpX9-t;jUV@ftS%U(E8V/"lD#far\LGTYH*d=m,.YAE:E#k^MdXUr
j9qs"r?n1rU&:;4F8=P-4&,YmY_^o&(5Z)j3Ib*m0g$Y/)@.'Ya:Bq<_fIl)[9e-h/M-N'r%
u(]A*s^hF8X%3;suo:S>#qpPta'Ur*/p2duZ@eR.0-_XT\Xs3@+6*cajOoY9TZRbApF;p@D]A]A
CS6lqRg)T5a1b&p[\^hGc]A]A74&>WpSbcInXMrH_l`ZGe&P$0Ln)`!qn3MS?o(UQ[c9/i_iJK
C5NpV1q]Am4^$l-&D-r`jHqVVColi&\'#fY\5?pgWt*p!qK2a[ep<k1H.5%nEPANRAr!C?g<l
a,M=+BF\/!"qmtQ4Fd[Vna^,+479lD\07)_]A*.170\E<STKT.XoMfKh@9F#%j'T8\60Zm4UU
=!-DC&&q5rYr9U6eABgXOf'ij]AFXMqTHXLAQF8;[\%Y6j2$TJ/;_f"!58t/'@\YKbeZ<_:1c
<_85[9l/7IJ4mX9*E84Q"USk,4E-uV4<&$H"4D0-&NETqu5FS^`XHk4me]A<=GF*>?hRqeWcs
%duC)qF"pL0sj$mS%&4oRI+.DXIc^X[',m'$*(;;sLgNTh[o8b>Ct]A+5oRX@Rr7c*\<K8ZqM
\g^1U%9?iknV8.\+\=Al0oQ3d["-H#mM+XB;Y,]A>m?]A]As+@+8`Sj47B@#=3c.Wc[F]AsF^=a2
?g7R&L^Usk:7C,<j0N'cH<pGakan+agbN?ihH2)pj7KM<rhBW-bV(KRk50"K-AP+SkVdDQ,1
HJCMmi8_d7qPR;##9aeKHhPa^/gfmbl;Zc#MSn#Qj1\^Fg4=BDr>)b^CD@kDUn-gX^rbTiI:
2?j$E>rMbVh#T=^X'$m/ZInF(T6$RMCHoCo#eZlZ$^laT1-jV:MA/3.)Y0[+4j2Su[=W:Z$B
pU6X5rlJ,13,5nm;"i47N<gL6q:sX3Au'haF%lSB;K"AgP\Xa3[`5&/Z@"7N\)uhP$(5d&QL
FirOi9[;G'5flfo)f[g)5ONbB3QI_e`V:Q*W=jYIq.-Z(YtQYmV'V/JC[b<*'f+5musBMBK^
4<FT<A<P;$rG^V6cJ8ig9'e=U,%#8^^ATSd:mb]ARTL/Srin9EAE.Y,7Nj7Ln0WEAQZn,^(aM
+_#LHX`%:Pm-Z1.s]A7VZ+5j_o3<fH\)03.999#h@XuX=Ckhtnmq^%>UI#T&U2qP&dHD0eGY;
_1Vor)\Af2s!J4p(FDkI@]A:jRghWEloIs1qe'h&d,8,Pe]An&2l^b]AZj$k#m+ReP5:EEkAR=g
72@3q=p8,\Rs5F.dj8B0W?VlQ=4Ud(=m*,:>(R:l;1C]Aj!u;9qAA-X!!+=S$1'e-I@B=L,4&
.,^\N4u#Vt76AmYn[rUX'^i&t+OR&1CS/XeG1@P1]AJLMuF7=o3ZWJC^c`[L]A#[>/p6FEE0kC
N)!C7(r&\r>hJl>;LuNq-S]A2gi7*=A>)%*8;7TgT,*5]A*BZNK1YuR?GRLAIF4g]AjQdZp`JE8
q)l\(3HtD]A=8!Pj_aZ1skJ5=B@`m4kk.7-Y\R$^H&L1]AHR@:g'-+=).%f9!>!Cn>iF_T0MLG
FbsV1$H"idY(4n3EN4q-PcoXp<g/E'=IUG"AduglGn-JF/pJuM_dl^DZU\;*2^CtTif%E@N(
:CGn\?Ng2'',G519=hL@_Ca;h5Sp=+-11gV-*lp^9"C9CbGm/cp4!E`GrFs*t>?B\p)d$;Sj
f]AP#&[+piO\tob(6l&W2K*8UfA:7,VUW5IiHS-=D.XROEQmnl4L_+"!2cmss=@P$[?V0?B:2
N,U<oCl]A18HjPI4<W-101qCF7qIW`o*8)UYDSW\:@!5Ma0ik8ION1E.MqQS4VL0+#382ujJ"
2jf@7l=KkSI@-C72,80CI1EYD4=SCtAY!Jh19[.3-X9Ye\&Goj3h]A%6lTQ0f.&-&oO2#F'Ja
k!!b1_XiVqLpoaM#kegdNj++l3"C',"G\[lKN^`k64M(pd'WB^NQd.f0SgKh`X?$geA4g6N2
M4c\.j\%+$S5+lrXHfLmOo"S_K[un5]A&@H/%%(#s0g96-DThd-$(;o)Y5=AZ*Jj.p(=1;(s&
61SSSHo_I&303C6-66hW)SN_;VSorDT$ap3M:ABU?_G\?-V\a.2I.k;<<k9K4l9TLE/iknZF
nGngUoVtCLlW_.eMNc/toBX)rfVO&HBY,gVb?qU*7+&LYTk`3HS&_`I5eid+[XlK3b?b3'7P
6I:@3mL!ZnM0^St<&oB^IVSeKLNJQH:3),:1ZgL=(;5U>"1\#paM'RbTYGUET&Q\\%='WmL.
?lZ.n3JOJRD>@tKo3a3nLPn]AKTZS3JjiV<Pe[:>LcP`M21RaUQr!`lS&V53TP=S_sekRgF(^
4`@YmjL%F:*k.CRPL/rl:-=*n5gF=1QteX[Bqh!BJPQh@ch+,"pPRe-IWHV$T\d15:[#/+0C
uCo^mH]A4$JXK.4s$KQM(YGa1O6W:H=k#[--N:pQQBe/7[u"c]AiN=C6DBEY-l<6^X9u)h*P2g
lo&L^F>XGu]AP'oj%n=lbPF]A%erEesb%<#/@:Yr-I)._tI%4]A[cBc4jpXD&?<b(8(\Cm2/KRQ
uB6EAYSB;L#[/-l!t1>PnW/1Uc+N:^NDEiPjSMe:k&OB_'Fc(4=t'gjNYm#;5"cG[9T)qjeA
-1$M<SU="e4]A"!p$6_osjD,0#i\lpkY0'Asg5ho%#+.jhG658cdD?4]A>hIa1dc3uX_qrAjN1
5YP!XRtA7nD'2"*1kS4Nb[aF]A/s8\6t'.rUtp0eQcHGG3Y,u<mkk%G'Uadm\Ps#,9e>._EMu
ON*\T`"`m-G^pZJ^tm%"*Qm=3=OK\oDP+FeN<:LVcWbSdRrR,u#tlM$L'>,SX9R=,M!;EtNM
^L0;CW)f)]A&AT^tdScoNcJ7"^hSXlP58?h>o+AZE,/^#+Tdc4ZmXZVN"JBZV7\kkQn,t#g]Ag
KsZ=49[QTM#WGq`))9k#drOHZ9-dM?Wd/Xa\B@)#dnn%WR:l*9%Nb35csMEJQt%iXgl)`$#V
1Lr96/36<t9[U>d*Fl(p<L@(UbO/\b.T7'Z&[h2gTIM%Ghc+eWIr9>6R++cfg+!b>h(:A0'[
YB5&CeHl)&&gp+:coF&`C'Z<Ts$;Q3("if>IuSrQ^[PQe$$+'r"B~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="37"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="78" width="375" height="37"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RQSX1').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="rqsx1"/>
<WidgetID widgetID="8f84bf28-639f-456d-b954-83ac1f6d0375"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="radioGroup0" frozen="false" index="-1" oldWidgetName="rqsx_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.radiogroup.UnitedMobileStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" leftPadding="15.0" rightPadding="15.0" topPadding="15.0" bottomPadding="3.0" buttonAlign="1">
<ExtraBackground>
<initialBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</initialBackgroundColor>
<selectedBackgroundColor>
<FineColor color="-657670" hor="-1" ver="-1"/>
</selectedBackgroundColor>
</ExtraBackground>
<ExtraBorder borderType="1" borderRadius="3.0">
<borderColor>
<FineColor color="-1577998" hor="-1" ver="-1"/>
</borderColor>
</ExtraBorder>
<InitialFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-6577229" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</InitialFont>
<SelectedFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-13947856" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</SelectedFont>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="净利润" value="净利润"/>
<Dict key="收入" value="收入"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[净利润]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="28" width="375" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM02').style.borderRadius = '12px 12px 0px 0px';
document.getElementById('KJSM02').style.marginTop = '15px';
document.getElementById('KJSM02').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM02"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,114300,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[cwfx__cfywsrmx]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="收入明细"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + IF(DATESUBDATE($date1,$date,'D')>0,FORMAT($date,'yyyy-MM'),format($date1,'yyyy-MM'))+ "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?r<#;Y_*KBe&^\,KFM1/CKo+VO*U7RA6WQPNb''<6eVA^l6bU*u,,4MEU@/\'05RBFi?Z5h
#p&V!4T8ZVj(X+>ZCd,p,B7]AqVX`]A!Kudj5*#h3e*[-I\661S\=_\qmZ=g\'DOsf?YE6GKAZ
-+;!2g(XN%&d)%KsASNuA7s'->(C9(Mp[R&/>N-Y@5NG^uLt+KA)gM%fmL<7C<hE+A)u-fgm
q:3G`,Fs4D[39o2cfB#5'uOWo=O<GXE@UYhD`utJ)/$1_L'f#Y+4[HK?VN#_4[sdk6I.HICE
P.nm"f2MX5L.>HTH?jgB9Aj+\m6RenO<"/UcVHoBNAZ]An0Y9tqZW`34A=QPFm0it3]A(Hik")
I5<q/oAC%Jrq"=;lZm8_SpiBrMpkBtXkT1H)!;o_n2uLHH?lL,prp9K:Eba;YO`-7`lf-%E,
q/NXL=T95<4+jU7jHf,&H*EQlt9f3?YNe%K9GKTWR1FBB>Te%5)g#JMg7SUMoF-1DW("9$:=
nb.B\"qBIa?8g\E#odFr$?9%&FeM=TBVcjN;_i89;*1?>4r>sH=Zf0!Ycp/mJYLV$<ob[f)%
nB^Q5g3?FMeI3(]ALbGo@r1'``)!Tt7ieZO0l*Q#PSen%/X_(>.fRs.dG&a)RoK@MNa"@"fO>
/8aK-%1on@#'c-moX4E=;a52JGM-J$jZWaS>d?-DVp[Z:J\q/;LgKH8Y!GQOeanl<_8]AR(QH
Z]AV)DU6A5)k=P[EAa/+fc.rUk6c^fi?fgh'Os("Q0V0.7C<dU2dkq%qDpKtLIX8i3`>[M2>,
H/FV:[)E/J3hJl=WqG"/Zf%XtBJ(!u.(AK*\T:BY,p_3Uo,#;S(PjG3`gnA8+h66@l']A7hqO
`k^IUr7lY?^[<Y<AIHXF5kNpX6j?NJq]AGOL9RK5S)fB+XNQe`lV^:q3P6t>Omd!3U,qOOdb*
VMq?\YSA"P^2rg?AXkCV61-h7"pkpA!=I:o$5@3jeDKPn4d[+'9=^CQ9@g$h\,4pYbHpI1,c
qt0B(/.i]AI./=As.h,Sjhm8\U,UJ"#Elig!M3-Z(O8,go+;I'4Y^dXe%A$LMh*=^F4A?c#N4
[\fJ*Tqm^C_R\_=;6@>><?Gt.U+##I.j%mP57WK[<?7PQ$^2da**U#h7^gh8a'u+1iF:1\Ru
DK+9fFAj>H]A-.F&"N)\84Hg]AEak8Oo=g(I=s05@cG/cI,`2QatDgopA"<jXn/J<YBFia]AcSi
)WsqfAIj+B;C%a.7%!N<>3q^L*D)O&-c0C:ME_*pMo#q9TGMM8%<Ct#&_Cf!s#SIBGqf$mYa
eVi&8QqW\?mQWIj=t-WGA2>&4r6^u$P)-a<aE@,SCWNc$n9kqepj]ArV`sB.BcPlp@?%()S)V
qlS/eKd_aWGs]An2C_ddF:'?g]A7G<VY0Ge>q-99A;\(Hc'p148mr*C5,<L:cIIj<Y^.rUQZ[*
#NBlYcl?SN98VAr4@.,0JoPY"Oq1IZVMK%Uf/H%F;C3DGo+9>4)4dlCfs`N>%,U:Tdn^F4cV
$eUZ,`k,0Q2)t*VMst"WpuT?2LApg%>Q/ZuM>peY@D7"qkOo(qt@F!jeaEHYKVkB0--mF*"g
i<i?Jq06>Xr+mS:AJY\tc?'+G%H9&Ei,;.m_G-06_j`m4/[f7V9(Orhp1%nfBnT6O'\?*%k&
?k=DqCrm"SLA\_7Sr0hRm4:5Qq4=%GVq3(K_2N8Bk%nnc$FTQ2P20e&eg"=:/;$@Sq.-3I+t
Rd?0W`reL%BHppm8Bf_)FrjC.?-kRM9%.,F&K!MQoYpWPR3KMK(b.M[l6.>VKNbV07A(^0\H
e!]AhC()V?%C`&^).UAVu[@_5HQ51;NNmjTG:Kbu/8,?4a\+romae62a.RKVp5+oGIS#p>8c4
eJKJ31dfV(S`>FanR$^MC:4%_PAQi"j/-efR[>rg.\m/$dkQ#9%X?=#W(;IQ^LOmhH1Zs3qX
o1lInD^+[HrGOgO5h86Z;R;.L6fOeu^LE/4;2h"%6e&rlUpt?<@fAknH8Kt>F)n4Ukmkd?Gn
n@T\&Sj.>j<jV^\p9Q)*j$<6Am7WNj*8^k`48tW=Mi^s-1kA0EF'$bC9FDVid\8L%%Mt-Z:#
i5\*6_QFKUj!4D8tPM)UN9d,eG#H>0g9rT5!`Hl`3JnpU,J81>-8J9+Do<n>CnY@qV\@B-]Au
+BT06p3)Fs3b/)YD;=O!EA;Ys25;C=-@EahY(XH_JY=(L$FC]Aj0UoCRK'#Z/*2Ym,oG_*Y2M
ms$j\NrtY]A\=Y@ocd@'5IC<_B;8h4gg1r*.QdnP$014drDBWA/a-Q@SOk$l*_,CW"0+Rde'3
M3so+iA:-ZKdj+QW'McN[XKkW2eEP`@"nK^6f.h";eZtT8fPc.=H3L(>?sDFVZ1QFODa*;(T
jT=9.tpZj6R5p,C+L]A!qm+RsOF2RcA"ZOGg.*h?I:?OM&4/M9>92]A!UqKWMkYkOQ?^hQP.hi
?<K5OX5.mRcU*?M.ao;c2ne2AEtX(40?#%ZrJ,E\\cV1QFQ"f9[An\@``_r@WHcWU\Lk.5kq
lY(932c]A/8j[b&q3R"/^WRWN?0>`)%od&LOfZ?D0UQ_JG8?Q8`3q`Z=#JX:_Ntq"?i.^pZXD
dsAG%/I.D"D-fh(.f;li"V[e$RhuWFD^,8&GTmLSqKf_C6G$l-n-SSbbU&Sl:A,b!GD(9fkJ
cl!%FGUhHME=\sAM^%;p8OJZXh1!8Qr/uPXeT%a9nUi`aA<V_DR.+Q(2S;YK7PEK$tq5Nq5,
?\sr2X8f-h6m&7FeDZpG9JF)CVEQ]AI+9`shkinC$5sLU<Eu,cj?'c.i(rDS669^3WKY/s]As9
\9[D4(B1IOtja!,7BO.7TBRh.+p*^_WDJ*q,?kn0JFd32N7[SS,:SCUFJljFNqT70f9N@9Dc
PpCo<XA+S5'Xeo9RG^!R9l&oU@88q(L*T0TQ=H\)'T>`n#^a.lC>ZB*]A":4YEQcQcT&?cCT;
u#("aPT(F%ht[IZdo5&5=-B',`GMYG"18"[<]AO5INB0:@fb)fW"^5MB>JPiu/W3$5aS7VQf=
o6g;LXfD'nM3Gm]Aa\]A0>(,g$g_+4fF9lt7&09fVP&n+)g!5uT-`9Q54[q%R<'!%cDcY0,p$Q
Hpf9g",lD<69iN#t[J<Afd7s@@HBl3KWp^W]AedF+XC#nT5nDZ;3WFt*;?[qWm"*EM)l4X6U*
JC89R0d1FD8`U>1K(R]AtE:;orpjQYCLLcC1PT2*p:\GWMm4h\"_"hME_CL4cgL"3<!,I#500
U2mVR0pH:sU90uG_qPku\/Ik+m#1R7.<8kHQ]AJ@>2<&F)a!m$=Uo0(V<2V*ioQXD@NI;-<H^
*ptN3>0aIk93RZo5fN82@*t1Blhf.U9?$\t^$>CCDASHRs<U:4e4Lg/YDj"M6DMrZNh.8QOS
eko1-VUILZt,^;7KQc/@i_GS=U8sh\jp9+EF9\e&bj4\ts=0B)3;UWpt&fU9L6"BWnE`3l%Y
uFnW<6e[69[5caP><_!Q73=):V7SoPSDst0JStTK-\KkM+O`]Ad[mDmN,u1**A,Jl2)B.CDO2
jt[MdDF\RooPJ#hJlniX/H6oXc1PJPOOL]A\s_/i]A0l1@fVT9KS7%Wsb8:.!AptOY7qUp"4jO
/*&dOL7eh\EV%Q@QH<Xd-eOF!fr8e.>^S[.<i.pjm5_aCdb=1?m5m4l:\L[MgSUhYI-M3'hU
#Mo=)tf0-$>(1lF%(mpC"K'hWPN&1;8TKci:@dGTJu+?T&)bdHKoWl_Yk!irJ/%F*Z>`d#PX
gpnrVc"q:A!qq3Yh$NM'+!?$t")3=k:O9$5\!?$t")3=k:O9$5\s8V-je"]A(,N'CrM(JRgp_
JDsa-_A%,qoKY]Ab\-Y$0Rs-)#U"Wt!XKp!!!3EKJ,fWV+FjFn#iP`;$dBXtPPD=.#Y+C<jl]A
57q=*'e!W~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="52"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="115" width="375" height="52"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[457200,190500,876300,876300,609600,190500,304800,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,0,0,228600,1884898,2286000,1230756,1714500,1714500,7924800,381000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" cs="6" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B2) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B2) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="0" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="0" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" rs="6" s="1">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="指标ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" rs="6" s="2">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="1" cs="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="1" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" cs="3" s="5">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="指标名称"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="指标ID" viName="指标名称"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[data_zdzb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" cs="2" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='margin-top:3px;float:right;'><img src='../../help/HuaFu/sj.png' style='width:7px;height:7px;padding:3px;float:right;'></div><div style='line-height:20px;float:right;'>分支机构明细</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" rs="2" s="7">
<O t="Image">
<IM>
<![CDATA[!Mog#reXHH7h#eD$31&+%7s)Y;?-[s+92BA+:&/O!!%(dHLq1;!Gk=q5u`*!m?Z!h_a"o*]A,
087eWjoW_NbYi>!tZ1@`<ji`E"-7/?T>k&OaTiKVTb&8BCfVgUr[+J;#-lA^$MM`=<lNP[s4
(]Am$@<FI%Os;&$,G6M<jpFo>W7?qXJ70T7I#SQ#W#+Z)pb7]AHFlRHR9YTNIM&dAh`W33$iM%
kK-L@;>pUd!6F'+:Q.t>b?fpS,kIn"SI-;/a32C`@o&*5e92l"?k@ZLNW,Lmo3D>mV6kV(6D
i(;:79a!9^Hl`,XQ9A"6>6r.;ilk.T!ED"UR-fWCHblN%Gu7Zo%c@"V[62A*CWV^h0p4":LU
J^7Ia:",tsU6'uF/[p9!qcubbWk_NuZN?NlRLN:E5f%Nl=V0fu#X%Y`J=T.4^kbJ\pMDi8E)
kG=/GRLmCJ',CiK_Z<WiCM6@#qo+l[StJKNSTb`d+/ZbS@oSh@pQhJU(4*j8!=4)W8*\\Uje
Rp]A^lSlWeB"#?h*-!!#SZ:.26O@"J~
]]></IM>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weigth:900;'>"+FORMAT($$$,"#,##0")+"</font>&nbsp;<font style='color:#8D96A8;font-size:10px;'>"+C2+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" s="9">
<O>
<![CDATA[较同期]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="3" s="10">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="较同期差"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#,##0.0")+"</font>","<font style='color:#DE554F;'>+"+FORMAT($$$,"#,##0.0")+"</font>"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="10">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="较同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>("+format($$$,"#0.0%")+")</font>","<font style='color:#DE554F;'>+"+format($$$,"#0.0%")+"</font>"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="3" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[L5 <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" cs="5" s="3">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.gauge.VanChartGaugePlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<GaugeValueTooltipContent>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<richTextTargetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="true"/>
</AttrTooltipTargetValueFormat>
</richTextTargetValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<targetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="true"/>
</AttrTooltipTargetValueFormat>
</targetValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</GaugeValueTooltipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="false"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="3" align="9" isCustom="true"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="false"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="true" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="false"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
<gaugeValueLabel class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="3" align="9" isCustom="true"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<GaugeValueTooltipContent>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="1" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<richTextTargetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="false"/>
</AttrTooltipTargetValueFormat>
</richTextTargetValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="true" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<targetValue class="com.fr.plugin.chart.base.format.AttrTooltipTargetValueFormat">
<AttrTooltipTargetValueFormat>
<Attr enable="false"/>
</AttrTooltipTargetValueFormat>
</targetValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</GaugeValueTooltipContent>
</gaugeValueLabel>
</AttrLabel>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="true"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-7287309" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-600992" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-422004" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8595761" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-7236949" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8873759" hor="-1" ver="-1"/>
</colvalue>
</OColor>
<OColor>
<colvalue>
<FineColor color="-8935739" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="custom">
<startColor>
<FineColor color="-937984" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-823552" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartGaugePlotAttr gaugeStyle="thermometer"/>
<GaugeDetailStyle>
<GaugeDetailStyleAttr horizontalLayout="false" thermometerWidth="6.0" chutePercent="0.0" antiClockWise="true" slotBackgroundColorAuto="true" paneBackgroundColorAuto="false" hingeColorAuto="false" colorUseCategory="true">
<needleColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</needleColor>
<slotBackgroundColor>
<FineColor color="-1710619" hor="-1" ver="-1"/>
</slotBackgroundColor>
</GaugeDetailStyleAttr>
<MapHotAreaColor>
<MC_Attr minValue="0.0" maxValue="100.0" useType="0" areaNumber="5">
<mainColor>
<FineColor color="-14374913" hor="-1" ver="-1"/>
</mainColor>
</MC_Attr>
<ColorList>
<AreaColor>
<AC_Attr minValue="=80" maxValue="=100">
<color>
<FineColor color="-14374913" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=60" maxValue="=80">
<color>
<FineColor color="-11486721" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=40" maxValue="=60">
<color>
<FineColor color="-8598785" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=20" maxValue="=40">
<color>
<FineColor color="-5776129" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
<AreaColor>
<AC_Attr minValue="=0" maxValue="=20">
<color>
<FineColor color="-2888193" hor="-1" ver="-1"/>
</color>
</AC_Attr>
</AreaColor>
</ColorList>
</MapHotAreaColor>
</GaugeDetailStyle>
<gaugeAxis>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="verdana" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="false"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-3881788" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-5197648" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=1"/>
<AxisRange minValue="=-10"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList/>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
<VanChartGaugeAxisAttr/>
</gaugeAxis>
<VanChartRadius radiusType="auto" radius="200"/>
</Plot>
<ChartDefinition>
<MeterReportDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<MeterDefinition201109 meterType="0"/>
<meterDefinitionName>
<O>
<![CDATA[]]></O>
</meterDefinitionName>
<meterDefinitionValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("data_zdzb",4,2,B2)]]></Attributes>
</O>
</meterDefinitionValue>
<meterDefinitionTarget>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=VALUE("data_zdzb",6,2,B2)]]></Attributes>
</O>
</meterDefinitionTarget>
</MeterReportDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="b37640bd-05f9-414b-bfcd-ec01a4dcdc8c"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="4" s="11">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(OR(VALUE("data_zdzb",4,2,B2) = 0,VALUE("data_zdzb",6,2,B2) = 0),"0%",FORMAT(VALUE("data_zdzb",4,2,B2) / VALUE("data_zdzb",6,2,B2),"#0.00%"))]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="4" s="12">
<O>
<![CDATA[暂无数据]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="4" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="4" s="2">
<O t="DSColumn">
<Attributes dsName="data_zdzb" columnName="目标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="5" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="5" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="5" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="6" s="2">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-8880241" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="simhei" style="0" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72">
<foreground>
<FineColor color="-5000269" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="5">
<FRFont name="simhei" style="0" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-8880241" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="1" imageLayout="1">
<FRFont name="simhei" style="0" size="48">
<foreground>
<FineColor color="-5000269" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[^DdbFL\lts&_jRoC<RX3/g=f,f"johN[jm4i^kK4K9dE6`"DIls1d,:"_P4L5mLl)8!WM
MV+,<1p"Lkqi..L>sFcg^T*r]AZt;mq4d6f5g8TZJY+lp=X*If5%",I-=sN\U0+K%LEGuh%q`
7s%>[fX1A"Pq_li?JCEU%,pYVD/tP:O.6;Z]A6LrfPk<.\^0>IFjq1\K[>ISjJXSu_M2OV#6`
sZ[`p@h(E\%g_u`r,aWV2WkT(+F_q3.-b]AqXs/[=.d/fH1LY6"01nRqbso>'biI-14MK;^34
sF^*4YLl<=9WcmQ^toA["R#4/V!7=!^>rYON[rp"*PZ\6*';]AubdrGpZ:e]A8QP.60+RFH/J!
RERh7s-l@kK]A^&gq]AVr/?T3pqm#3hoS@.6lrqnlS[i^_$m>Y^TpWHI"5q-]A"Bjr7c'>WFIC5
9abpqnkKh\i5r]AX9\]A/Lbj[3=-2lF3I`439P27S.r"n6=S6oFPtIG^httq-2qJ"\bn/IDX<k
H(TmoB^iC9n+EX[&=eFU1H4N$o(XS.;GJ-'2>dXa$mp.moHU.37m-7HNMhD6!83t&?!9?%YV
\EOg&%scTHObA@Q!!*t<Z,]As$oDjbGbOf2GcUoqq_)bJ^R%`W<.f7@Ogn;d=?JBbJ8E>SI<K
r%"=k9Qe-iMq%I.$bo2HQr2a;uGpP3`oN#It1Oa(k&Z-]AojMO)t==O:R38+U,I7rFIa49%80
n-/k?+0Fer1arfL#bsp`8Al9M%QJ'f-Sd%^N@ij1nbVoTd1B;U8pS),^gI/XVGNV06@r)p#3
L6s\&GDN/Q6G.%G3fJrOr/<'+CWP:^N!+k&%$<T4PN?j<VaZ7O==Ln-Zkkp'mu:1;>'6E[]Ar
GmE#2#oT6%Z(n2gXpC>_5TRZ,m`1-@?M1C<`>u&i*23$AIROeTE.Sa!RN#\'-bK&0D#k]AlV6
6AEr2#A(m&TPbj>G/8Z[ILIfk^;E?Iu2uG?_Uoa,1RmX*@_eP>"[dj'H/LW,at0/6-5bY3L(
%\a;G/]AR9Y1P=V[IfJljW:_O6ajk`7VXG+":_i&?iG-2u2jZ7JfsQQ9Wsd4Kc4"T_W-O$l>3
^Y_J;$F$!L(lFE:\FSn]A_H0EfVpcfng@*<7c`>R%6C79*mFJV]AZAN;FLWc3?oGBN=gUhW]Al+
XBp`mECa`[o,s1P/A8)qIChWl#^^C&0VHOnU'/@K+HV>qFnd^uU4ni,tK-g1VmQg$F1Q74J-
#VdD;7%tq`%@9:ItQ+DY,p[9hqm93"sE[GDOEZ/80Mb5>:9K6fdO_9ELqaP/=ntVqW)aKTl7
XgKpqmNmNq(T?1JN(Xj:m3=g3c3pt,^d&]A7/Fo%$*!ZkaLikD?10*QAa)VLD@^bhmk`bX<90
JDM06iA#$6G^WI+[.Ah[p'_UK*VXk),AJ%8O6iHMn#0!na_Sm[0G5tIpX$k.F_J0o\U.Cjds
8k<dIr4W]AOhbAGc-5CQJm^6QO]AB_L%oBK9AZgiOtjqsg>nB\8RAg"+q='"6Fb[?=NeTm@Q59
"Pk3XP47-;i]AjLlj-l+AV/NojsZ!Q%o]ABoVRZ*5t^5:>agdm)7+hL:dX1&NUIg$0GNa]A<%t'
!;a]A,,4lGa:!ZEH1K#N8]A05h'V(kk"k3DoP`%#jhd1[<2!"A24UNbIlB9uHQR9,rjN)rs`k4
1Ecn@X7e.>T0rcUTE94Zckq\-&-Wq1mSPMNKhQDk!kWI`XT\%d.kCcs3S4OmQ?c)1[:qEN(R
n"di5mPlciG%NOSc')=;,JZI(#b?r5*,UFCf%gO:TQ<p3_N/njg@DWG#&j@GkNQt,LUj]AhdM
-*O%,EngMAE<n]A2\>BN13.L<D0PDI]A^,S[&:_k^_WpD+`d-[R&s5G5UCZ/Od#U*D*KRAt'^*
OXGI+um'QqK9VF<aBAQCq\7]A9k;=9NhgDh+(if.p2=eF<An`nf$7uGGZuR:DY"lr<NI8iPPh
'mtD@J]Aa^C\4s@(TGD;SH&!,_N[p,g*)Qsj_f;\#Qp"j+W]A&F-`@_Ot%OcY1QfIMTLi@>gYn
6611]AWj[[Oge\g)>CnT]A7CVfhOsku@PV(i-!`sK?G/(FgIpCI"tlI]AF!'\WkstDMHFsZbBf=
3Jr;PdXUN?^=$[0Y>$Z\W^FS$'T)/X!c\@fEUd;9W4Q-`XU5:t=\bb,Q2md$kmF5;_c\=1c?
"\'tBoYL;IT<>H%(QE[6UYV_'/]Au\bq@WeT`A!p7-tA12r(&RH/qO!0:^\5URT/:Q0g0"Fa*
KAgpaElfOmo.k7EHntQ1FH1jN%F(F!'ser_a(o"r/FQB^EaQCM(TY`KAm9@-SZ%BQTHo""7T
ka3Q.S&S*UD:I0"+a3<)DFU2[H9.$63)F;uAleEg5Ye[F6[QORq:NqQ(s'VL>S?<7DV_6O:9
B/,=cTR@8e[/bJ`me9:2D`MO",j:ITG,/ErktOraNO$%`fL9aH3-D$+61V_\9ht*2bQ\Xc>l
0W7,.;n($qYUD`*54J'XH?0)#3das+o<e(`c;Uj^_,nG:_]A8q1lif1%d\FgSD,J)>?t4tLZO
XF<1I+e86UA%/Qaic1@nJ'P/Z1>\bsfUQ*m*bum:?&LhEjb>qZ[D]A*i8m<?]AXP'lZ^W)6P"S
\!VgSm'eo`oKC)PJ'f]AA2\NATYal)1'4_Y(UGUAk1bhKdL<#>JA/jXJCBER[Z8>f\4A4J[U_
Le/+frOm2fr"1@SI,U2^2iS]Ar.'>[k<b\aTIbWW(fZGO9=S=A@ahDOO'0M_kVU2'S+U:X=qQ
a9.T:[Lkr+,I85;__R1R8]A95\c9>$>BZC[(h<nTp0(K`/rF?hgpjPK\b@Lc"CcKf\WIOh?Vr
0Ik,KLc3[HKAPk=Db(YZJB]A^+sBqQi$([1j,3NV."O(.L;/Hb1iPpQL>DZB<";ec79dc8'FR
)2.'`Z[+qA;.Z<4>CpXh#otA[K'd187Cqct#-5Q#^JAut-+'_g*H^K+Y#7Vq]AGB'sUnICK^I
ge"ij6<g!Uf20$-KVF#L,f'ED/$T3p,.`oW=-o;Y2Tb>a9s"OpPJN1pK72X(H<M05hJ8iUli
XMkmX,obrgO,8B1&i+Yfr.l?4W%>tGS@+"UfnRbdC#uR9U,"=@`"DAoB@,\Uh.Q,R7qQkH/k
OaT)e3;>fT9j1;U.H9]AFncNM>L)h\b,+CY]Asju(6A?AQQK5\bqegbi.t5&(;"-dt+>3iUTUS
`>VeC:VUkZlNAKb+sg;b:j;Hp$*9KT[%Wr?9+:bH.EmJR]AN6gDR,C-)h&hOO%tJkpC1QTgIC
5XMk^Bss_U9F%ZM8+;FQ>/-JiL.-@-2;>\IJ(^VAGRpa644of`/M`k>jABTo2bP1#LpKqLd#
K))K?PYfI*a(NO]A;-l4>,_!T0.`"hGUQ\\FXG6eN#K<?"=8nHIDmdjo$'n[`G^bhM8`cpVX\
gS0&`eGAr;)YfaRiA9$&:oR-nb^I5hZ.@kTc9&O,KYI9e^X/XBe;O:fk`e+b\a"8S^6KM`%?
,TC>VP'rkhWa>Ij,0)93\AsqIZ;3[P6/0f/-.mY+BWLk&;*?_=Hs#,3#Zh0q3(K__g*]AOkQa
bm?-1QrY23KA"k#CH?D8ThV3Ilu4.mO6SNpCkF=0Ao:Gj'%o%`)&$VL;]AQJEAGYFl[OD)?3=
]A#.uns#i!i[ImN"M(/?*KD.O)G8tXfL3IGUcGD0W66aieGF1lLO^k>@rbCk*MFda1NVm"Ws4
n'Y+"YD*Ns[r?2+)O.<"2U%(tWXag8A,ba$Aa:n:nC9.EkJ/%_HN;<T"G^OR^2U[rqB!E#/L
>^N?0UhH';79s,]A;)8N_*%oQ9T7+EH7qQQ`Q/ZBH`D%A^oadXQ_`FKBSI2.FhI<"-!Y'Y2pd
fB&m3G2D8f+T6;fI<lTRrfGWk$'@;8q6P1`7/4hK;__W^s_2?NRgoSh$A$YoE84WC5<.j7;E
)K),58#oe+s(9DDb<c03H+O'l*t[/7ChhE>P%1gE`s$Y3:tK\-HNn$HP:Pl(4riT1fIaP#Vo
-2@FT*Dkc#r`7,OVXWWcru5m4+F@Q7EoRO$k+$X)8-YH\IHXYEZpK=6,#"u;Xck]A7]A0+DIG=
BV5FVi$o.<&_k;$gBA>'7K_Y>/dR17VGMGFhYb]A&4MEA'D"r#5Q3cAnacB@_g]A_Vt3'm9@6t
8/-Yd/VJXSmG2C"1N67W_`'UTi2`m41lFbTN1.o_]A%I0au9X5HF*DZRX^`of]As(iSjaD-U&P
qr+Ys#kKCTjieW.&I*5>EA5GW^n\jjo`^pBt0.*Pa]A)M(N;uhlH@_H:m7?"p0*/W'sUL?SCS
=W=[gS_7^DV5hibDF61AuB1Y@Bs`]A\J#XZ=r]Ak9QF@cZ9-hMCk#FQqPQ(:R3UQqYDpbC,rHo
^SR%6q%`*JM^OXRE3C9M[L+]A0d5C=KINH&25qURMgBb<7i_JZ-.jr43N;Oi/r`U[gCiNtbDM
=2X%+0iTj4&M?JjIJV097^ZOVh)DX!Lqi::!sVmHNEiI=]A5#F7N(5Vtc/q9#eaW)Q5%%m`aO
eMV^223%J/n`+?7GI92!"ZH^P+g(S2fpt^fW5s\);[HTQSn.61V5i6$lkc+-g"uRbuGrKOr/
a"$5TZ#;8C>urs]AA!%mFKlHaNr@mYU^6.KZ5";jRo5qG,GE/jos4dbFeKuZDZ*fN,#%<YoE5
46s!l:5#(gklnc]A%42^_k-4OR>(3?lBVTd1ed^ZdhHq?Q2^fk%=hHFt_-h?Z=R/<2f@LF<4Q
qWs_+WWr:a'UJ]AFk7GsY>?BVa-_JMRpp*/>&*B;sp@Bu![+Vpi=B[3,,<`8h[?[&QBNZ<fBk
nIW<C<<eE\bQ_kB,3BkmQ?F.l[elC6uI:ZTemrk+9O]AZ&1m[J+37GD+$ooWibU*N@6@@CE2m
3OfIJ</?Hmdm:o8=W!d%G.1-;DNtildmK03JU$qf-EQa%Ohfj:hRhU*MR>]A::,4lM`pGra[o
PsM);DPofJ$Xgu=]AGW!,'Ekt51Id0>ql#an37/=?pD'%>'7JV"%UP4\R<$[VgY^Pb,ae2`7b
">E;u0f"1Lakl%0&AY'g?H_)`T=[;e]Ap4d&^(EP1:%-LLT(5C8I+I1Mt=X4YIA5bEks>J&J*
9//t4Ft=;JQUQ;#WH>^?o6,2BYk'>4e[97AX#dmXr9]AiUHX-^IJiU1Wjj"u3FFk0PXELeIPr
cB.O/=m90_a/R3n@>km*=G96Sh&=pIIjAo%`CLpodQ<^kmE)ngYSdlYs<.Dt6>pHs(oIYUq<
[/nc!k3&.O[>p"FG:GcDU^f/\VH?V,3;C1)"e`*F<djOh2[Z>tnKi>/lYo08HBKmekWE;a[X
%legEF<3Y3\a*$SFI4qYOi6;1oXG4=l#K%Z9u9B':]Ao7.ae$U/E(cu^>4TNHi.i7j$5D$2>=
?T?_#:t(jQh5p#]A<S3*Muoh\Q1]A7;"\VN/Jf4S%Asq=)HF_=.rp`ol_3r)V#RBTOpjZc&e0.
$[,-6'0HL,></nTjaOp0f]A&)^XXO?@NKXQK27YYsM'YO=hIg08UF?%`:abZDa7?o*e42Ui['
I?^H>B*sa[oIRbe3JErK#<'&e`QLU+rppAHS%p_:f<2TbnX81f)4,LB9t'/,OO"Au=[Ef:.T
Y]Ag#I9bf1*`o%u\SZ@RmXMXL/bB:2+UN=;<,mH0S"r[amMA^GqX9Y`8qjFrf$b)iVY-,='RV
9iNO#SL&&hMq]A$S(<o[>;ILZ,"pr/$TD;UE;)2Q!"opGP!_"(Fs'e/Y;&>N"n+]AuLHK7_IO]A
mRd=`Wt%UP37Y:.RQjBT'Q-2R>S%bJ=g:VrPY3\SR#AQ5d&g($PH6!"BM;-Of&7JJ./of>#N
PTSomk(.L`Pq+)o$R7g!:<]A;21OP&A:,1')bR6gCIC#tMRLBo.iMep2mR2?XeK^A"(0?>Zm8
?H^U06:<-i@a3fXu+"0D8RZARBgRRBN5f$+sY(Wt=^oiDV;>_ASHtF6g^UJeeLAr1ul9S8lO
E$l&t\%+0'Tp?(b?2g6\fZRT'AEF"Ij1/b?B2S@VaU,%(1h5N"O#EOaIqTdZ&@eEi*>d.VrT
'=$SqSdrM1J"0j\`]A0X;FtY'+3I/rT<+_.BKTZYgORG"$$i06]Aa;EcFN[3dLBQ6U3C^&"]A?[
/JoI!cQ'$=XIN#^B]AN/X:omR/$rTrbt07<fH;F-n+ciYk71/MO0.VKV$:F]A]ACWdF5Jk\KT6f
N>u_#qaYKN`a&nR]AXC*0_BsW!#4e#:MfkT56BBcUU(+q+-1\X)PpZ4RSXgqDgpnW>E+sQmHY
lqhIKGku=Kkr5\o3Yn]ArT0ZYdYg0bKlVF_aN2PaPk8E#TM`7320kC0GtCAWLYU.h$o.Ie-J1
8mo\nb99)=b`@blSpVOcqGB[X=qjFm2JnHY79%%Y$>8?g_S!jg(._ADEET+4gGW)T2#_eH7+
?WZeOU;]A(&NTmtdru3h.=8s?j_GA[KZ:1#cI1cDdb*4di:grEYdMeK>3[f.<a_t:@XP*$V"S
fLo2<DD\J(na^i^Fi=8m3l?U^[m`Q)mRbc]A[;*ABqka"$UCX\P!MrO]A^]A#^AK@'AIT#F]AV".
hL192:aVHQae#.M%N4(Yoi)B)76Qj_?J%I:@W9X7;.+?-,QfL:B.<M3gj?lIB:$/)\;4a3B]A
8IGRsK#n1-H?<Ys%65#jJd`ZFJ+'_OKRE4*s_5!ARq.B#Ce=i-0D8N7=c2O(S"bO1`G;[OJ\
<)QL]A$DbDQ=eZrX95/_-RJ)3akpKe:s+#LjeYKQ,**$5Q:s6(T!I*]AXZ.@AJjA7VmoSu.M/D
QPAgL7049TiGMZT0[rW=_a/6HoI&:;JtBuI'`L]A@:73D]AS<S.9+eIiA&eIU@Mn(]A%9qaqMOG
pJ(a/*MJ)B]A(qte]AWKm89Tk)?-bocSBjhPm\En/GL+0P`;=O[,=G7h.P4pL#kohb-?XQZ"7Y
=#+Y(E-HYjqPC#\@s5Fm4S&!Uc+3H+o7RC]A*,2p[-i.dXcKV)eca7h2TBhp;j;Lu\ZQ_faIG
>KDc*Vt=KoZ_FTR-VZY4jL)d8Ht&$tSNehlb$7X>*,=/`\9#2?#-PEkp]AEV0u4dGkIhbNEIj
#0#ZoY:(1b=\TANO0Tn/31d?;4&8b=j\B%m)%D.0<@ce#\C/7*&`F^dS(OZlBOY/AiYK]AG*R
eX:)3bKXOWfs#?s/=b!5_h#q?hP+Rk1L>,9/WYUSN:nMS.J65.ZNcCK*7%qL3c-NYl=)H\>@
\OX"!IU'UIPj^@Yt5:)EPWjK0iAYY6e$Y(QdD!CGuGY?%m#<o<HIbI0RbW!)f!0^3@c:aVgc
Vg4C2SCnm5)Y5[(=L:)D1l/`!?,A]A.YJ%Cjb)T.R1ZI]A:D6]A"eU-)ZPB;r8&bmBDI2Z=5jYa
GaTbjp\MhH%D(bjL?pn]A!%:&uEf`[JmrV\J*$PoXc2+4RquS:cD>MW9q6QT.Ni*U-C/@+2$X
^)Nd4(S8!K8F!=`QPFc"<8k-1n>!IZ,j2gtOo_dBH%Aj68>u=r<B#=ao\%.,=Hu@WB"5YuFc
>(Q73OBZ/#]A`$o(*6&ToX?b"5X6,tBMXa0LeQd\`=RKg,.uraT.7_Q'U2OWe=&dNirD^!T?b
hR#l*nb=46=51$RQe-$Fs8<=f_l-.NuGj,OX,X+U,A[WRM4-gP!J!H!tnB5p`r^8tp4>c#r"
ql9YH]AM6(\;P?!,N*(HY01QO^d;i8uE^**W=kh_^0<#JJE3\Ls\2Q";#2bjGA%CN>=@#Mg?9
%K7;\FiMJd4507.Q"!b\tZTU.qql;)6"t!]AWsOA7l8+/Z5rN,g<M2qcY@pk2n9AO1^HIEa'(
?lKa:Nq=*m#8jGaS^ft,Z:F*#fA*e>k!>RXQCVQM(o#6]A:i,;(jVpM8_Z*GLfIbmR$maB9PF
"HU@SU,Mhd2P>R71$N4XN-.li1sG6j09Z1;nThp3Ga>`T<8uX;;ENtC=]A5-gqe`S(m:dXRN.
YdGIHCdP19r<@bVZ.e20R@!A_3U(#ButZ1+43pV2u'*0Hn2DIVSkdadm%b>3Pr&]A_!HN1+l#
&HOOBlkN%TPXp;L6F0bImilFnT<3na18!W[=bP9C!Ab-bqE9]A0GXDYjW0j/!jbHeWJLK73?E
jVDT%r]Aj@^\J0#HWE#=Mf;]ApEVX.BY#rV1O?,RpCmVu>gEWiD]A,"Lp#.bd$VnW=,90/JR%V4
_1A?tXnB<^6'ZQUeo(4_79BjV^c*D9[V@fM0O?7Da9.#o^AZ6;S'DlrkrZaKL#)),tMN):h\
G<T#.5AnVQ&?Cb,`AXPeRk6i@4d(/Vr`![Wo8>N&^`&"43/<k>BC;ic4!f5[Nr@\or"9k]AKJ
Csa\@pgFZkGd]Aa+l!dT5#m?DLTV@P]AG+Wt$mh>g9iUhkt3M'B]A0E^i-6W]Apj90SophIO,\bA
56FTmO"_4=9P8!1V*'6:JO$dohQY9nffh"::p\O6cs:I\lcW:nq'7YYBZ!<*bWdut[[T,Yo7
OE`ib,uKP+"dh)uOt=/cS^@ABHJ:81R)'Zsl^sgC2h+`//kH%/m#G48rBsEo2919)_^MRmJa
B7.j[;mi3G/3ZItnUKoE<OqB=DF=u0Q7:Ct^CUGO(RJ5+A8-fN6_iQu$;.)NP'?HSK)'aSpg
d#u2RLVIG;S)hWQ,*%ndp?V4VNV>3.Ikr8A<ACQe7'8uT29,2-F^0L8_#fpFBl]A-?u[?nr]AG
-O<5hI7Bkre%/E?#'%11J=RAf/RMUOV#-Cgst0Z=rt/(INl@FhnIk^ZL:LouTF1FfZK"^!2!
BZf$mU!!bfLfoijX/a.jrg%@5EIp]AeL[">q\;H0rOA:D^M/Omr6&QP,]A?g<AkOkTmDg%-+MK
.]AcY6KJ(/7YrfrI(SJ=;Ere&E&>?:]AB(^A8^d<enmPoIsQ+md"0]A1-S;D]AlC*HcF>3E9;:8H
_95?isVP_/"61Zp;28cB3NVfRTc;c*V%7"%3%tpgR40d/A,A$hN,m#Jsds%GZRpQ6Y[cAeO\
o%d'_dEFQDs^Ed@[dS5mUFoLb[9W"B+^g^4fb%qXhqdQ_$C_"KrFO2XtB9b4Xbbe7<*%\3[+
BI(&2iX&K;IMOB<o*F>5(jS!Hu=C:8RA]AK\&J&9-Wo$mfWiC2^WS/k`XoG)CodA3+\2WBU:<
e-=B0p%ud=Gi*DIh`8:oq"u9D^,8203;/#,7,%ihG!6X=0NT9)SID*OeLms#*T#2F?+H3gpl
rj;*7+3Uka$R&K`AI%9D:T:9G+A[dPNe.qW&6t]AS\Im&X:f#k>mE?YP;iDqE069GDoH(!O/k
Z10Ul.Ki:*b'M2Q9Bn;.7U6,W.##kWEE:1^$AEP,QNF'0Va4ZGN$)!S<Ci!g9.Wo:*dhgnD_
47r5A;e5R:ShGo"BpU);^+c(#nSZ@lWli"UXmYrM;^ge&J-#_en=U'Acpfd[+;fn^Uq:Kpi%
[i&RgmbV5%i'bd_6]A.61*]AY:Y#t[^eaBV1L^FW^lVu\u!<86!MKrbQbX2$u@k(rJcA)231[\
,U.O=K/'OHcQ.*l4u9.+\1g`ZMoC#@U#>)D72QI2kAueW=h&YmK2)5NT5[I#M9]AYqj>&UjrP
?3uS[-JjQ,ROjW>f'*]A5PG9bpi/>;CIEZXU.GG.)5"jQH94^7_GM?)X0H0NU>k*,%<9Dn0.t
GA2>&S!3&F5G=jDo-m>rMTI1&`S6uD;rb;e?Q[HO'&T.%EdJ@b4C[3]AR;;8Pp^@fV?HDYjl!
c]Ap64PNI;)pA#6OY0d7.$><rKa6%[g.K//HPCuhrGUC1@#"ZA\PaEa5JM&J\@b)k"V'Wap;r
RTIlm`l*EQ&M%/us3Q(Fck)dpa"XVO,3Q%.5op0'3h'I`\?hj"Rs:8H#0njVA]AWG<E@?A#^K
!1\M28us`TdE8XH=K%LV0I5jqDpg(OZg9mirsfArhKQKL<>bU]Ac\k+CbYEX353Vm3&8)E)e;
`e-K/)/X]ATHC]A$&V9qpps'('s_G5)^h;;+<N2a:[SAeCZ8h^m)H=lI54D/!,QNt*[A#(,<Y8
fqsL@+8da959<*ZV`fO\%-Sl`apISeka[XD%HQ>B[W5QQLh!XQ1WY\PRFA;YE\nF53N[Z4U+
sKhLK9M-nDTJ?[ET2]A]Aip'cjn9Gr1cl\2C++:[&_M5MTf>-c-=9%"7F#icEo_jeB<d,</E#g
lai<Kj!,pHSiXnG`4$C@>pV)M/[/Z0F`Ofl1Q<<h\t8*oDb%1F'Q&PZc9qX!hGe8_NcVUhX4
bI2qKW1-B?Em#0c>jQdGcf<-,Y>-@OrcgJS>u=BIamu$.<7!Nc?CHbanb++X"(cjr,*JZG[]A
HhpIT@\8i`sFF;.i'U:\_N6N]A%'"pZE#/@.#pH`":`;E3dtdl'`^'2LhboOcpQ(/uTe'Bl^>
YXM.J<e+60MUDu#P#[[K3p(Ej9CcOipIRV]AehSF!r*gRsDTl@Ad?-$t.)SCZtQk$We["Q-1*
*]AjpX]ABXcS)TH'&Y"qK14B$t5oCHk8S&ts3e9*ijn)ZdjU[m%N/V"81'm0*Ir;"M3c$X".ff
Hig@f%jJs4&h<U"o,n^P'@bA:S'g=m?P=Yk,#;@8ItEVhMa=^I5#9Ij(U.`4H!H>cco[u^D:
q[Z005iK_(.o_[i:Q_QJToS"LKjVCheBSW<9_Vl4-FNeu2"r7_#J'gGU'+ST3F[7?L\S<p5,
+l6eefgB//.W=`UV<MN0RS1eaXBJ\KZ"WU&@FlNW2*h4_E5Lc7@[d*V$YF^%l9?eQA:T0]A"B
Q003j=\[klL9iA_$:>/mPp"N]AD1QiAaZJ0c.q>P9Nr(6<1$%%7\2KcmDYB_FpN3]AD?Z/nHGK
Xb&16i,l53/9$\PB"Jm7=ma&ZRW"*j'*Lb9B$f`QHG:+^6a5Ve.*36R5#V#j%m;EoR6ddk/+
C9Tr;;`\uESqp<X+AbVc>Gh(>raaN7NB5!&[Ha"T;(Ps_+o*`ojMGG,I)HdU+<6##]A^7I7`#
c`RRP/PioSIiom)7$u/Qjrn&JlZ/?"26*`F]A[NN9`Q<&mPGT:$Jfq9f[+hdQ;JHEkl!uAn/W
5O:;P[.J[*FrK`+YRN-Xq\<jYI.*%_TZa"@X3BNT7LZ[B*4qoj\<+a]A'qM@LI5]A*-ZF/:aYa
7%jL.5dpN)F\^jP\_jh.tWg=V+U/GnuF"prn;]AB*:ZNtY+VLgqQRG(hc5YjgW2%t&791)h&J
/8@<39JAPh;8BmiH),?e30<IJm3VjTb/WcSZBp_!/'0Uq%AtH)E#0=SMkjuJMqo^0J>((q/>
YKEGugLDsP*;mpBg^5:eaDf=T]A&<f80f*;88'YmieMp<OM5@[-,-l[c:$g+1aQ\9=G`DU,nd
KGf"=n`r=oP7gCH8/GLi"DeeCL7#XlM[NVNP<tAI0#j=.!YUk5c>,7deD(;eD&AYS.54Ec-=
4IkhCI*JN1N=X;7+d8-'0=o)*Y#q$tU/ne2.BoAie"M9,#hj#HT[-2_90=%s9Reql"!1Q^<,
4)+W#nAU#FV#VMsdcD<nERQeCL4FYD"p%LfhWF$>e81F]A@V7cX"k`hDQ,fm!Ue*7>unNjW77
.F`FLqspBaSk,1,tY't6Diu3bZO;]A#&24Q#Q8Jl`uoD;/q7Y/bI,";^$K4jhr;'d.]ApjE[:-
2jr+4EH$1qKZW*C(/M-p.7N%d`?(=,Rgf%R.n,*'32LP@irDS#?uC(F_>e*.JL9J$X\X;n^R
6mror:$6D<ng/3:4IUc^oK1@C:*ol8n-(,7g*ns<GFLNp\:ijQ]AJ2XE0->&7b)$"b%@VC!L&
0fG.QPRCZ@ht>p1-Y3:%9R*6>:9!3\TS'?Iu;q-\*UUm0O]ARWq$l`Nf9uSd_h@O2gV?\L;rM
emo7h3q=aAZkbs?&/8<(i]A.Q0#0a;OP^7Y8L_e%$i3ep?NUi`7!mQ.2,W]Ag6-htaU(\(s>'K
)T%tiZ=D:IX<:GP9FBV^F2a!egb<Pd?)DfO]A:q&FV7Tfl6:9.LmY;I!"nrFPI'h'F;F?d=,R
W?fQ1Gl:DF-!W_"n^c/1&$SgCVNZX(6*3Y8n?C5<1*f:/f3$kliD'+<hg]A:Okied`58n-6Hu
$7iQ_qXR+(&UC,H?]A,NQT'ZnfQP3fM)m"O82WoL71Tcn9EhSI!CPp[G0-l0j-Gt,tY7:"SJ#
oT`<MUV<.kYd:bElg;6[O7.Vo8@B!d*9b55FQ`Xj6KVL92r9jsrWC*NRA3;m@4tHZg[#(j/l
t^0e%I/uffGS:7B!G/.M]ABF+bG'BL!Ja*[m2_Ds]AY%+=lc9e1%Y=[Ylo'6oI*K3f!,n.1F5*
BIe(]A+m=V^D.d=%j&E(F#MXlcG2(sAX`l,-88lJoKg<X9t!`"MV5ca0^mGY.F%s2h"=:WJ\Q
kgD'T_XUf\gKpQjTnA":&"C@69]ADhX@uO'hmjGG_'6pj0JuZj[gD#5_Hp:/hDB6`o$f19&QO
EQ^e^q$7)[YN':\\kd-e6H"lTWrde70t^FZXdNs=O1T&aU\S3]AoDbiGk`cVE!U1Pg,1V6kdE
t/#HoE@]AZ@hnSnn!?p4d]AC8]AhBWinbLF$q%4\c+!hW&&u7@jJ4EmM[B^A2O#['HEqgS>1cE,
.!*gXYO0P6C<-i"^9,@(N=YGinVBYdnOZFA'[nikJ!AG><(YteF;Gns'hJMTnhY?En5<%p6R
qL)`cep8]AcTN024K4_,qYZ)b+eMh:hd-#oCN2f3=sASF*sQ(poi^Mfs-g&F;;bT`p!.QWVL*
*$[*$Jhp".N3(Z5/585V(gm)cHGP-gskhMre/3(itP`iB\4]A4.mr1KG-bSG!/^9Z.Krf`Je8
%80he;G_8@RJlT6IEZfK,%B\O&q_;@4==SnLe-,\_FdZtm8ECPEPnJ$e7,V182<>)[DUe?jj
jlR2(kUkW*ppFoe%$L7B\jYVM7<V)5[MqqQF\#2ZhBVH=*0W71L+!j_#=/Wc/i5TA`LLX"0h
*%XC>0$b4ko.LZQBmjt)66!iZshnWi3=%kMFCp-teI.*diDO>e&gY9XANH2OgNtR9XA/.Z&e
k16.9#%VcG>#%eC7Xgqc)kVu^IG8/SK)\CiDZ>=;ON8#X1>:)C'EZG3nD!50jQ=KM;T&jpMB
h"R0fq\IJC<Gke\+BM3a'/R:Ve(+jgo@,:%Cn/['o7&raWIC[%M1N4bAWr+OJm<^kg/]AfO?'
$m-4YQN_5\kWVX`rfJ\;I?c,=XElteY04o&,XXB?^]A_C=e!mt,L.@;n3_Dq^s.U83,i+"]AC8
YZkp3g1.1"0D"]Aj,OcU2lp5MpqrYJk=d-&jmn09MMt8XQWaYBd&e%91451Kufoj;m[><Duce
IiaOO>NAFUIr3i;l4Vpb<\ND+!5:S\!W)oYCS.(Sq\b2PM8a#f4j=Cb$0i#SXAgaQuLop`6l
HTNuc)SC?ffiJ7=$H2TZu*fn>5Aa?$tj#8,KhEk\&WT`Q6Ib$OTV;E>"<BIkeE<NH[LK*G\g
s\$-mtY!A55"ROK$n(^U(H;\A-h7]A]AZ;V/-G$:lmCpFUiW\\Xq0@`j<MBUO,A0YPZKCoB.#5
'VPG?-AAGUqiENHUfY.A1TRTUGq/AQm9]A(iY2ojQ'mG%G0RM6C-.4_1i,'F#=F:PTp#?m6qV
;B1-T;8-\Zc4"N9bcGGg\Jsgg/l\@/,o%^(Zhr%j[c9hjPMflO`"m61C-hTPs;oH9:eI[_.X
8XGC#HetIh!JI\Tg;]ASp9a\*)+gihX_o[pp?@8Nj(qWT$<;D43C)"IW"+\_<WouMf(21qOTR
!&M]A8upJOIQ.,3,8JV!UaUW:dY3.h[(l/"o6?G&hP*Qj0Ori&NuPQgb)NKd1to%!9KN<ls$B
@c"e`+TU3%+`bdWW2FP@E*EV-,R[/?EH0?9TG+,0=TCG<="Vf!.A:cfa1DpBccW*6!%G^:(f
^88E3DRLA\GPDF&D#7*H*ohdbA>_nW_NLW7r.-Kmc*lANFo9K^V[Cce['A?M%99>5N=qor$*
G_<[c/ShW6NBqo>qoem2\&>_EGpgD+s08M0K@;Ep\7i!_O6bRp4eBPoYJJ]AhXF.eVOpO]A#Fg
aY^\u-C_O\?(JIu&naXi=!'#ru<'>F:f5Ac1;JG$F3s5@EYCBL,93Rph+r7Uc":=_Z:sH?m*
`HVYUZ@#Io1=^lca-aB<M..)MqaDjZn)b(jmMF`gHB/idS1bKj![?E#\9JZF_O01$WUa\rqj
a-pIW7]A8\D,C<JD#+/,r+d*0,N\foObo?Eq2%BrP=@pX;q4nsMrANF[9Z7+u6PFk7\tR>keQ
&Q#s\a(G8Ul7LNo`)fkilaC<%mK@/tpMkdg9CW)6&#dbUB->\!9ad82M4*7OI_ZW0EuE1Q-u
,HpDlR97nL?AJ+#VX;2c[P]Aq?iLq>Ee56#0ZMI1gB&hQO,6H\FW>9eUXl3m75FZK/0eho/n3
c=hH7q/Y:`9'>t<>WJ5A1l"kYYlR)aLY$OtFg7*19m^crfppH+n$3<*AJI_#q]AuGDq*bqV@;
QEg0pNDYC;o2##LS;h6kEqQbTm]AJ`,.&T]A*4=D9@7t%";J.cKIL+ib;0*Cb]A909/VnI!JD?\
J?:'@Z2WImXg+\a'C-CYK%g"E_TJM9S+CV7#OkGrX?4%mJ(BUPbrNho./ml2t34HlMG3#5-E
lXaN$k'oIHT,r*[=5jr:25/QO9^$BZ<_VEP?BlLTU!hE?:m9?R_V7C^'fXnO+2>(Heb\A5WR
#hN@uD)@So-Vi0Z9<AOP!T\/iE2nKM@)I.&G.qi([-L8WUU8n*h@[/\B4G'24-V:cO,s]A>tD
o8O4geA-2'kH,X)>h/YK-n^pq=@am"FP!>GkCC(69MK*WSNqF\W<Bug9<%oYo.K);OqFB$6%
dtRq[RYG8_0#LrYNX(Y7Ri@>L8;gVmo-BB%50QqHSuCj$K5,/3,uL.Ge;hVA[Z!]AXMX@T`$4
Fc$O=ROf!E6O"&gjk#&.<+=ql0c+C7-*9<Zu?U@R[;H$`<EMAIL>>1R^7>O+
/SsCbh*(0I#/^JdgUo/0C@[)`Aft]Ak4E#XU>#XcFA*EjpAm2Qk)rY-u_5-6D=jKIs&'N&2LN
f5OKRtEXHk[JX]A5fjnGPHT9nZ8MY!k()3AlLH2Sc`@SD>052e]AZ=Ma7AV90*9a<S^#\iZ2q`
HmRB2#;4P]AR`&#)m#We(s;l"??V0L,<5ZuWX7fGiI;F*S@T.2o[g<j]A2Edb6m'l<-uNl[G:W
Bi/r6?0,NfPk`%ND0eT06tKIMKa6"KK"h":AnaIeM9Hq$&Y2G:!F)mXkjHD/PrNGp&QfI%.T
al1B>=?CoQ2pX=jp<X$5N$P&7,=Nl0R<!#u,%\^;ZI>$AmPc%VjLd#)Cdn@;-VeG[6H2tV6*
m*+GhKZV/aK'Nd(CL6Cm6eOk%l-t`>'<VOJk7N\o:=Q'4GtnahoQTY;]ALq*'LcuD&$M;6h?Q
V8AW$?3lrR?%)GD4G#ZL)cP<Q"_1UhYk#%W33KH9`1@-gRGa&@JmjQ#eaVBWquZhoKp'N'=0
2&iZ6mOH,tX3^E/b*UuLt!!bENq6EpZDBkA]AJQV%TNLVNZRscuWo=XT?ePZRn\t`t#@+RQK1
e=nWRN.BuEgJpi&APp`QsI*ANFUt$`92BO.\;s9.HC<E#uN2!#AVs:jktGiRfUCeEkA<UCH.
1()plc3fBC<Y9V'g?p/09_M)3WiFTfB:(V*P/9#B:4[)QLrC6Im4BlYXS.`I9C>!ZisZCZ9D
cms`d'iVY_0m1"MAUSi<,]Aa<;b]AIZaV;sf@qrNV0htOAL-0S[/ks1#o4-oIPk7^\^V66#"Y7
mr5#<:_[;mX+<_Amg#?t39A<lTajp%OMp0!19rs7+Gm8bk$(V]A=Wj?WXg2GS/kUGNY,QNb0f
,M*_-Kh!=B`'fQU/\,O`UO60%U8,ou*'G-s[X'CE+iikoPqns)fQk5[3TmD!e-`d[6'u/6^_
J!B;&L6QcftM/)rX#.$ZRqI9T\44I(1=C]A`]AkOl=P(m7+@TEU"@0LC^;9sH=3$=_J$Z6R0nI
dIlCg=akC70W-dA]AVpKK4(?bF6]AVp/`H<A'u]A*a=YM)]A?39h&<kc!Su?!_IG4=K@o6an=)bt
HX5*$6Qs^%4=7/Zk5"Us>&JU<e^]A?Kk_Qn+M_kPn.8A>@-tNY$onT"l5NI8XT"aOc1RFQ899
TE)GFZEd*WM=k'<*VcC.g;.T/8[[,>+I>&[a4Kb;h.Uh!1OS6;d$F,bt6mMlo6/h;*tBH5!s
CM35-8^f)SgaQ=9I^2qDu,*gqgs,ZTC227SJMLHuG4!tdRd"9SFWDeo<U?H3GI\hOFg`K_=/
VsH(9$@.8E:a*W/OIBn0p;t!='o!&KZ9@89Gt)Eqd4ZG(8QEL#i2$g2`e=E0.'#0%6&Y4Q_O
-P^A)_$mrn)ZKmL4YMXS6'^IZSFrA08F*RKj9aGO6"70`^AVOVKqntIFTnCQiI%?fiZ.`,M3
f0kVDKhe-=g:m%UqFo;OmeiH$T,WT[o,AS<a*:g*32&eQ+0f[3TTS$RfIe=UEq&sKW-XL%G(
uU@7+`q)(gb-fk5ND,,+9Hh=(7u"a=ulmTiF@M]A\B]AmR`LTZ=0=0*+8u4&#$%(Js7]AT22img
U'4_FO_@;!WGQ@q;=NZPAV*gNZK_Xl0cEHBFLudIDgOIA<nu)C7J'+DSVu.unb+J8)_fA,S0
NoiYYcn(Wd;N*e4tKb3%VQCOj6s:o`;apFUABJ:=BmS9D\>_9^LY:Bo&IUURW+[s@d3G^or%
(NeXrj+<I_%J<;ifm;t['R:Xu);5?335r`&iqqG['bl;,s&W]AsL5Wr<6';o\j=7/o*q:Xu);
s1_D/*U$D1,NasI61I_ec0`g.RU3PrIfK~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="55" width="375" height="23"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('KJSM01');
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px';
ment.style.marginTop = '10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[cwfx__zdzb]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="重点指标"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + IF(DATESUBDATE($date1,$date,'D')>0,FORMAT($date,'yyyy-MM'),format($date1,'yyyy-MM'))+ "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?VHg;camX"!V+f-rOWL@MBfK&OF3_X?hb@'sQ8p0*+p8_^7GL#[.C/Q?GZ>)A%Hs66[NTJg
B7UAIZ]An"Hpil!$NJ<"@KU(_0Modhri2<&,1A;\bH$Yo7,Zh^[F=b5<.@GIAQ`S#R"$dqn:7
8+OF/WND!,To#0Ob>n@:;(O4Nne5*?'$^C6s'XkN"=lYKXlIS,*&)?tLEVY^a)`l)A>?ATMn
0?W?kT8B9ftRJdkj)Q$cA)`T1']Ar1mXF:a2UukbhSJHmmOB]AMN?Yop:)<NVP?o(dP*d$7%NY
T+GS8>L130ppJ`prbBsGr>NB#RgOR3"T7)UWu$Ue%EDoGIH%KoB:rEWHFi/iu#/-oc<hC4XP
pJ=jZ,.JJXRaee"EA'lboFYrArRS1)=Dtu/m:(\N6g]A;bqc/'p^0E/teW$6fj;.$#T<rInbu
'L1TKOc!;,M?e4>1oQr.=<C(L)0sn%UC6/%SK,I_h&$5=gMOMSX@t*GMAX?R]Ar[[Tj90U*>o
17#cr_Y;I**16I:u2mJKmLbblRLLfgpIZ7fE\"*Ki0nm?dB\(i6/B*bK8=$c!2Mcsg$Q?&VI
RQ"i2&]An?RGCQ3fYq:k>`5K#rLd:%--F2Knt&V>YBG9K]A]A3TFD1d^qnE8"+PpP]Ai:;2/'5#/
khYkN*sB6XY5p88%WT.*f4Nm`#72htq">-ZCq5%J>pkC6\41mOL#^,@,*gSsbe_cRcCU>/DX
8sd&iFM;Hf\$TmuPm90X8/a!4QFlF'8#7L+0`$T_jm!@re%aVu[l?2?mD-cNX6]A"VYi8rFA+
<EMAIL>]Asot^:F7cE6f]Ac;F</81.=gHr@SnUO6("+8$ei#GJe674\WId"_Q>7JQouKrQ"M-
Od`Ci6mo$c*@B`8!I?\-onj&ho^_7@UMasJ8:K)HmhklfkOc5:FLL>*!a&_D;>tTuQV=Poha
-.g/;;C[Id]An=s-tfq&^#mjhiF:bLN[/AIVke>ho9EE@*%_a7[m!m@`?E-42@Yogr3PqGWUq
5Yg&m*ZN-oiX-XseRC);#TdaW0fg[@B$Y,hlrgqFR:ABGe&<7%eNle7-&6Lue+dYs?35.HW8
'jYg=PP:,]ARPG4'og6WL)E8V@%@jC0+lALa.baOFlEdgV=q-QA-e_f/MCR5R.2U^VWitr@SK
,qV_I^V>NTI-2N1q/-;uT+#="VO/bT^Gfgqgh)&grS^Yt7dG*PCN6p+[*<7>&:)HP$Y9iS=_
*=`J7=KLdHWZX@trV3"$A=Y2ZoXQmjjLDN9`_%P*<[.T$`X?UX$)"G5J*j24M1Od6En`-\;"
XoR2Wb2Tg7mDg7PiOe[>FMN]Ap-o1c%F!u\Tfj?1hM7a=:Ei5ZnA1$+Ns6TKX5;<R(c+1V&FT
t\\&3hnpXHl#0='8lcTL-_-C"eQFF:`U>V"ksn3XKdXn$dUGN+s,4s;F#rSfOYff0Ecs/af&
<rXs>8kBT1d&pRm[U;;<EMAIL>&K^I,^JR6V2"!mb/7GmWb6`!dbm4T[g/M,lh
4hCq-l_dbTiJ@JD]AA/X)C\>X[ij#fjBp[mH[b-E^.Z!R"j5h[+M`"Ote2d4S6bcCYXNm&?ti
iLCJ!IQC7qR#4_1L($1DVBiR;gnd>aS%QhDJf,%.fLjnso#SKXr#_W-=h&oVRMe:/j8s5&)Q
s8Gs.;8U3ZA<"LYQjS#I8?ZGS)bVk%GR:JRDYl+,#4/2<iBMa;?tie.A#>G5'3C2`&6AWfKQ
O1%$NaJi&S?b4&b`^_AN7njgD(u]A1dXP0X[=R>:#]A"I.3_eR[Le:PV)),8d]AG/1QfQH^=80i
JX:VMP2:5/ZR*__mJ,#>D,MC\:+>)A&A:%0]AXTEW%%1/2Y_Ge(83Ddf$E?[H7):oE\nQ$?L^
,O8*(,?.2038e2#+WUTCOEaY;/mVY2O\_3QHs6nHb2,6mZ%?g4Od\E,PX/'"-%T;Ec;jLkIk
$XhJ)?nRj-qm)9T/H+5@m(+bd#4Fr)s\Mmc=hnU'(^*[.INo-DLKSi6b66FciVKR/MR%<n$F
6*Tu3QsH1L*g4Yk#P9%Km)8o@Of\_<a?rmQWal#0b&L0R#]AacQ;"7HEN8o@.]A8A#!l%.mM60
"K<]A-2%K+T^@o7E9&pb-J5M-Wbaghdk&[aHFL/d7)3ICb>D+3Zrq3*7ke1M$MZl<L,-b(IL+
4^=:u5qY/>+24*`"4a.mM#Gu9@<`^bB'G_L,4h0lYXBXudtTMgRKNdSDlEur,qT$ZPc^,eMM
CkJ6p65V]As"P_^P&:;XYkEG)EC2d-B8d2/rLpXg+A'>(5ae6<O;Xn-G07%nScmAAX/ep(j:R
0IC&8"K7O#drC\OO_+G9.T!Hl+G#H+o7jY`,A%]AoAF^i;>@4U]A`mfpgIl&_(oca`pS:aO:2?
3ub4]A5ffj1N@acJkt6@e@ACBp8Faad&Z[uDnPH^0UY7V=\K/0C+(TYmd%n<0pbQ`2%SYALUA
;X"Up!EqNB-^\pdbcL88RPiO6I<2]A7=nY2\J+/PiONHje5WgJ59HNH=_Qk))<e+R$oLS!;i!
blm5$8nk,.3DiI\#U]A1W=@UfshuSk[ksM(-kbrE1e9/Gpml\nHS?XZ]AG?2/;GVt?5+iZj7C!
UpRRJ>nICSKW;q-TRnhFL-#_#m%LK2sg7fVREFlM,F9rDoFlH2*EB8u47?SU;/r-mi]AKP7lo
PlK5sco=lK)Q<>'Ad8+:6+I7rLeW/9t$&cnR@gEdk(8)Mi/qZN$cY7Zj7!ljWOTG/T"hAF]A3
JX@qTX`+ZAdVkYm4M!Q55;Lr(8DnG_8Hl$"`,Y;,(=MYY@fj?qCe8T/;t+!pV&R+%9Tb(B@a
C/5$gla[de*<'<4;@rJRV*2XbbN$B,jS;l-eNfGrZBca'Tm3-]A]Ao=JnJ,X]A[T4^HMWuBHZ!Z
b;r&`'?lBW>?KhdCKoPZ229sP.Z#3'JRIn#1t6G`21M!Qm]AR3[mikWJJJWTqbXXfs*_4guS)
fL?F#7Hq%/o.#'OsoTHNJm!E2=cZAdgb/854H1#C0BhFf;1>*Ig01"ZrsH$`A\.KK+5HS9/#
bm0XITT&)DYUY1Vc+PCo!aYg`u6n0DkVXMo+1.EGQ/edocneMkdN2,gX.-6gDE+EF!(!cL^?
5<p"-.^X(#"MIqa'@\\"*pYUNBIm=/?f&8\\VBcRlOTMLrOp]An:F:,n=t6X=rkGnj@u9aAt9
la)K\U5m%-)p6rV;h&aHT*Oa"`'a"5J'#CE$[c`F:mM#4ODF4rfGqrVM9>I)#c`K4`oD7'QN
HbXV:M2@Rani4VsEshZaMn3JG9:;Jt*'^'bG06cYc8-g5O?/:afaIbX>S<$7*l-)MR\C^)rb
C."pW)]Aq?#7*Mr!U#Iij]Ae&-h?omo7V8/KMV>@RY?#.apiQ)+S;Zf3W1gtX=jnpdj\(6;]A#A
/Q_Vba?)Qul,-mJN@b8Xcr\]ARu7jaP^n9`;WRKY=o;2@p3\5CKB<;maQ.1)G2Ufl_W2NETd>
^'o:"Plr033Rr-'h\K4TF%Ub5`1>mA["?I'ao>Q\tK`.[A-]A);Aaoc."D@IJ-%-L![rR#."D
@IJ-%-L![rR#."M;(U>o^Q?i,\>-b%]AIodNhYE]A^*3rIAunpK;4OgkX;p`t(`b+%PPs/VTZ+
9t*\qNrVEF"@)qi5QCpP!!GOe"@)qi5QL^hJ?ldV(Y(:4[%,tiSNZ"HK/r=?qqh~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="28"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.RadioGroup">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RQSX').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="rqsx"/>
<WidgetID widgetID="8f84bf28-639f-456d-b954-83ac1f6d0375"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="radioGroup0" frozen="false" index="-1" oldWidgetName="rq"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.radiogroup.UnitedMobileStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" leftPadding="15.0" rightPadding="15.0" topPadding="10.0" bottomPadding="3.0" buttonAlign="1">
<ExtraBackground>
<initialBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</initialBackgroundColor>
<selectedBackgroundColor>
<FineColor color="-657670" hor="-1" ver="-1"/>
</selectedBackgroundColor>
</ExtraBackground>
<ExtraBorder borderType="1" borderRadius="3.0">
<borderColor>
<FineColor color="-1577998" hor="-1" ver="-1"/>
</borderColor>
</ExtraBorder>
<InitialFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-6577229" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</InitialFont>
<SelectedFont>
<FRFont name="Agency FB" style="0" size="120">
<foreground>
<FineColor color="-13947856" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</SelectedFont>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="当月" value="当月"/>
<Dict key="当年" value="当年"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[当月]]></O>
</widgetValue>
<MaxRowsMobileAttr maxShowRows="5"/>
</InnerWidget>
<BoundsAttr x="0" y="167" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA2').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA2"/>
<WidgetID widgetID="cb1d2383-2abe-4ce5-bdb6-50cc01fc0826"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[0,457200,1333500,266700,1333500,1333500,1333500,723900,1257300,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1152000,1257300,1257300,1152000,1152000,1152000,1152000,1152000,1152000,1152000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[647700,837062,3505200,1947080,2292823,2292823,2292823,647700,2880000,576000,2160000,432000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="0" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="6" s="1">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="IFQJSD"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="false" foldIconName="fold" rowLevel="0" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[='科目']]></Attributes>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="false" foldIconName="fold" rowLevel="0" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(B2 = 0,$rqsx,'当前')]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($rqsx = '当日','较上日',if($rqsx = '当月','较上月','较上年'))]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="3">
<O>
<![CDATA[增长率]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="1">
<O>
<![CDATA[ ]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="4" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A5"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="close" subLayerRetain="0" fold="true" foldIconName="open" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C5">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B5"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="4" cs="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="FBM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C7) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C5"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="4" s="5">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="FBQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(len($$$)=0,'--',$$$)]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="4" s="6">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="FTQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#,##0.00")+"</font>",if($$$=0,0,"<font style='color:#DE554F;'>+"+FORMAT($$$,"#,##0.00")+"</font>")))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="4" s="6">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="FTB"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="" subLayerRetain="0" fold="true" foldIconName="" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#0.00%")+"</font>",if($$$=0,FORMAT($$$,"#0.00%"),"<font style='color:#DE554F;'>+"+FORMAT($$$,"#0.00%")+"</font>")))]]></Content>
</Present>
<Expand/>
</C>
<C c="1" r="5" cs="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C5]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C7) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false" left="C5">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B6"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="5" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=E5]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(len($$$)=0,'--',$$$)]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="5" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=F5]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#,##0.00")+"</font>",if($$$=0,0,"<font style='color:#DE554F;'>+"+FORMAT($$$,"#,##0.00")+"</font>")))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="5" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G5]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#0.00%")+"</font>",if($$$=0,FORMAT($$$,"#0.00%"),"<font style='color:#DE554F;'>+"+FORMAT($$$,"#0.00%")+"</font>")))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="6" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="2" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" left="C7">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B7"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="6" cs="2" s="1">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="ZBM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0" leftParentDefault="false" left="C5">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C7"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="6" s="5">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="ZBQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(len($$$)=0,'--',$$$)]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="6" s="6">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="ZTQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#,##0.00")+"</font>",if($$$=0,0,"<font style='color:#DE554F;'>+"+FORMAT($$$,"#,##0.00")+"</font>")))]]></Content>
</Present>
<Expand/>
</C>
<C c="6" r="6" s="6">
<O t="DSColumn">
<Attributes dsName="data_cwmx" columnName="ZTB"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#0.00%")+"</font>",if($$$=0,FORMAT($$$,"#0.00%"),"<font style='color:#DE554F;'>+"+FORMAT($$$,"#0.00%")+"</font>")))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="7">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="7" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" unfoldIconName="unfold" subLayerRetain="0" fold="true" foldIconName="fold" rowLevel="1" pluginID="com.fr.plugin.cell.attr.collapseTree.v11" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.CollapseTreeProperty" expandAll="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A8"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="8">
<FRFont name="simhei" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-986377" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-986377" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="微软雅黑" style="1" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<`JF;bPUu+pp5cMP(62GX\IN76kU(J;Sr9'PTjpU3,WG"Kj-[!]AE;4&d4>/#VGZKU4C`a8K
p#+6\%uH6:`8F:XIkQomLcSorW-aJ$<%F3IB&CR6>m]AcSNJ6/#L<J[dMnma2b[L_YJ9)q"Qa
ai;Epu#+k%4)11rE>o1.mme$162m^EVC+TM9[3?Qu*S_QWoR,H]A7pE:*ZEg.^!Af0*4ZtCo+
o>o0(SACp.sJhgaglK+Nt3Ne;,^A.'lWFsE_;HNE_t94n-RpJHJuEG[X&`'\_ua1jLM"u^MA
KZMh[BUag=SA\hG+U^YoC;Eu%maE.-dcdfm\Ta7,Y/iugX<9+^]A@TabQ2o+>!/WQN7fBmL4b
n8J1_nX%cC$&"g/`Q&cpLG1pHi$ooFKO=-I?pM>6/S$HW4dImO'%c%K(6,Va(D,NbbAc+m[7
0X2p]A`W.?s5eYgWeC'1s"p0ZOJ=Q%2kDLM;BZ%;20-`qZba@I<js=\TOcBeMc&Nmd`l1a@6$
?br_<bQ]Aoqg+NiBS+hE-(Cuf>J+Pr]A]AOJ"TfL%QjqR`p`SB1eqIhE^t@3e1Jh?:ZN.U1"Bn^
9&s3Y;pK5o-ISAbf**!Q)eulbEMek]Acrc6lL#ZGeWNJ::U)b;\BShe^M=baM&@=tZ71Ig>s8
-m[>A@$O%7YhFIBV_<*#e'K'.pcr?j\,MOr9/L!JSj'g'UYqtid.Ckqp3.LJj>R)TG`a*9tc
b,%_F$#`t;J>jSJmA\&k1"h(7lVAW$G`QVbE!(R5oNcU'D<G03j0GZPh"'CUVc_JM0_@bjq!
#V`Gae&lKjn8oH<U-;2sVGn56Vtk<RQ1q23pts[R*`Le<_!^>r@qFAOTR&'m&\a*PPL0@d<(
0IMEZ:9u!D,rVg$$h;-g[,4YoEl@E#SYgCPtCR;7>k;XY8nk4<BW_8,k2R_tLW)f:O$/%2@A
?>[P?`S?)^4\(/AU)-]A!UJB>E)Y&Ea;27joCFcbf@2k??r^jp9r)EHhCpC@O"3Obr,+_qW32
4'cuZb4,fQ6WY^^!4\nJHoG4)Eoh<pH:(JYr"FgA>#K?`Z[>m76X.!TaGAg%e'ODnM(9PN5^
cON6n`G!i@:sa/(?B@32[8.tp)'?q+?)f%@6M"&dDQKZ4g6AH?SbtPXh=r(`CP5U"gNoW/31
id<#R4R35dEmK+K)Bq/O22u,lCaE)YoM1WaRu87m$mIZ_\'hn$IlXZMN'o:hf.L*C_'31iK<
ppR=as)_koDB?j057)mLB&%5f'[9*ad#E'(alX]A]A7"qK`_ZK?c/j%]A(:%b*tkF<?/T9,o*M\
EX+`JdGW$4=+?)PB;s"1`bu5?p!QjNK>"=h[m338,O\oZ%0CZ\h]AuM,lKm"iN%XY#%3AP=s_
-Un_t6Tm+$o)6t>>]AHf$:L-D_fmr&d+h9%k6a.\EkQ)r-d,'gq<sN/P8oO%gat8S`X$o3_]Ao
j63dU5;KBOP#NbY5(qdo&Uc>3G1["Q$[hj+kT5Y*K"7I@;*4$1</PR,WdR^BHXN1X7?DMrr7
CU9au=s<QZEkA9X_&saPZu-K`L4dDm`YrRnjtScPeA`S^UG[qYUn8;+nZ>//.kCM)t%59>Qi
5fc_<>pbB:%Z#Q8&bhXpmg[:`Fq3+sr3PLp&1lUli"Nj:$mIIsZ7L"6cB[a9V@:ZRQX'M/3a
K!.Z';DVj2c+Z:hbo%S+..=24.#MY@m#T$AOZD'VTk*-(h#p*G2mMlQFM4<f>hlGgWufS51;
2l'aAR1O[-ccCc4$'<h"P>as!_@-+2'IYEkBk@%_;iMkaIYmJJ8,-.&fh`VZ&,Mj20k+e#0j
BH85Kln<4<#p2bq_]AOSLWU)(1Pp@;Y&MR4Ij-4GNKo3AoDP</joB<N&qLAf+4RPgM+%!%).&
lrdW=)2ec/P)rF>]A<ha\lC1aYkkP/YkQAMm5)]A+Lbp!N*VA5m^l^GrDlp_T:_25$F6Sg^_0L
$W_A$k+q48'fiurH%Hb)d?X-4PoO<'TVmM/EW*]A!3fI&SgKbjg7o#Lsrh&DlI@fl,;Q^aT-p
6.7$@#,.EG]A+tmhF9]AC;q)IQ!_T^oXL\31fDF`m*!R%=5N6FMC5K#gLoa6OIH/!b]A-$gtl^Y
H,)rt'84]AYHdRH:S+kE67>4G*iU8-FUOX;k=`.ublHPSZC6!OA#AX]A?%mbWhQ@-V&V;VlaY3
aO30GT=p_T3<sj>[0gAHeQ4VoKN8Z4V-!M;=o!6R93t_>T@S*m<qSPD)-FT7Eg#ReldiXOdU
.2R-cPsW9K9El/.Z[-hb"sPbnFedeans!3LD]AAF:%8`XCuOO,EL%M/g.1V.muY++Cd-Qqf<H
([CSiS)D)q,HNH40W[75#C67?9GI%Ig5%.<8TK[X0[SMD>i4Y:.LB82#<RY,:mAATQ6fKPck
W)Q@URXm:E!@'r53Tg7n#7PI_i9<&7=;lT:o[(Ilh%F'd-Nd\C858<EiH*DW^EHZm9s!$d#6
(n993_`h7,,b.nlrA^$>:Tp7ZO5["FF)5'+-=hk$up`IGCMWiCl?6$f3lI&KsuEObNW6HN5M
$!+%UWaU\[QY^qbL=n@LFYpoE`r-=q0t@BtJ'<RE-j;f:>pi4PMN=>Cem:8#)njVa>"DdP?#
*sB_]A;&eN/3k"hIG(F%cPpk=[d:XmP=B2%NH_2qBVPfXI6&RFd90AqK.[X<P#B&V>Dj4h",:
7%B@]A_W%O/\JWEPX:DdGRMl^+4lF4QM&(j?j5:MocS=R]A6"od0-^BkmkJ>M.\0f&p:H7g8>a
UHnJ0*@rncAJ">?_R+kX(!==o@tO[mk]AVT37kbU27\S&--f)$LY%h"'$;/VFI,%k*@]A<aX]A+
_tVZ*08PtD42;(u5?"%G.;<X^Xhc`W^\FZ%?/c32BajJ4gR=,^;7V]AI3E.[FHd`a04Aje\9A
Qd^F8gm/BcY0#:3P\%RDmIsP0AflU:XO(?<qKuGF/$__19S43>nmWXL4KH11UL$:6VgQ8#C+
e=m@1Q>5?1Y<>/g$XVocSg&r9`=)2gEuQMJedeeAARO91R\>2/*VLWr<>;hNqpPTkeQN9;qL
pg;RRk"Y6q;d6)PEmk*,hdO/Jn&uQb,Re>5'i-nR0LeXIqK@QN\%hIe__El-*!%ia4JbDjNa
&9_^PM6,55'A)%H%r)D^f:bFUbnXW[g94Ph$Pjt9kN&C*pH9c3QK1\m'I^[!eiu)8/\7Wh0(
L9C2lut``,GQr!4^Z&D),9a7,PiCg1;=JcH?RIaUsl`/pdV_i=eTKUSCe_]AV;'Q653rn:$TO
B,]A6+MQ*e%HICV1Q+_Q*@9.V$=nF]A?WPi#a:kLP5Oqt*@Yqk^/IWS+=*k^(ZPT;.f-Ogs9ld
*!XZ*TRX$"^jQ2I+)BQ44#=G5oq"V>VL62G5Q=E.7>*Sin/p0bV?GIjBl8T6^nV%BrQ;.)84
#daWnF:MV=c=o.J6o5(osZ%N5i;\&P;Kj-9>C3M)SorEXoT10Ei>(7m-CN>FIg8[t0ebJreC
)N!`<eP)s7L/T1iaH-^7++MEgW'['45;O6Y)=lGHF2@@?UXAM\K4OGb;a@h'tr%b*_mj^'@J
#)*jV((9"Z>'9u>lWgbF,lWS>W9V5<.V+n&1Ig=nVpGq=h]Af.\:GopZdZmN2:2b-,J#3%SO\
MBF,n$@k;/(<Br]AC]AE@V"&uj[S]AY'.Tu`-_/Wfg,HTt,<XjW*.mltNb(]A[0%H)RV#?d&84"A
OFYLZ7dH#Ch%%s55F)R<5Q,KDJ+/Vs[Qk7tQl)OHmXa\C)V-"9qgu)BgM]A/T&9:H_70F#9RE
P?+D&.Q-'ADC<X&sa>Z<89<rot=T]APRfT*$\Q@"XZ8[ud_"@c(o#:m&+W5r2_Z?0Q:5PF"Y]A
l75?igeTlpX4u;_B*A@V'OI4!n+(;gIB$8+lrj>C?5[oT.MDZBX$Fm>Zd;cEtr[W)#<GWOBd
E2rOZeSZ#JCkA6==PZSpbZ3mf]A<R:Y%-AM&P!s7Y+9EDFQtAFqX2XGAbe;lQ<;Oh02.'Q#a7
ooN"^qRVGe'!G2_'?,I_-Q:;E>o_Rq!e/mG"_AH0C!qI=TkK]A)>A@dDo%Lg.\!B@DarHsh)m
V$7AmcS@hBnjpUN)5og,u%NJTD3?@>\\:<nEKc+*D5!IW78E6$9H01%Roe8<)mOjVp/Emg;W
1@b`*U\Cj&g9RBBK8_IYLBBUX'%U(sK]A4hR(^19KA1Fr=Hk,i"S0D/2K0h)(Zd#'U)^_s?%U
juA2.,a,e4nKeELIdkS[\Y;dpcWioI+G/,FXXkG.ncN+h7ruuS,JU6Ds9pcLQgVRobioKVlH
T/UUkZX8BQoaR(g4:Wp?^='^*O$#S*6Pgio3TlOP4[^6>i/N),;a`?u/lQ[OoBE.oXY6"5\S
I4sH;HFBr9MXI1J_/,I<ZA>6qWui9rNDf[5Gs&r4]A;:9ibmRm)GLXlk:cHSa.G_(EaT1-E^0
9i4?f`^j)2G1Am/(F;KII9kr]A3:2#.D'-4Rs!T3HZr"#WERD3QApc3p]AHt=EVB,$bLpTbNgq
+?=h1&#OgV)oJS_[F'uJ6Xh5*mo;IfXQ]AjAO+m<'Hh#N.JjnfkPWtQ2p`MWj#4YnN>`78#:j
oA(m*tn1.0p`<<4ulI_;7HlJ>cQK[^FTEZF4Omd&em8f=jl._pj)[ard;&#hEN_`=^T'toLE
feL-h%BA'T@q>OOjQ<8!g&Q+s/p#PV'E(O+d%Kn7gCm6]ADA3h5&ZeVYr=12=jW,<2<n\s@GS
<X_^f.:9[9NaE3^Z_u$3b^WT-oA+kD\+2H?>K!;M(hJ,n",ULXQWm);'.Of,JlW%"s%FFSEh
bI@DoKNUJea/5_>I%7`80smCMLo97s$-(J)oRS4OiJKWl/e'B1Tsp,e(!g3nZ@Bn]A19lBo&&
5S3?_S?mU^CN$ncA)\.[$Ra_L__`Sm\f[OVF&B"#$3PD7QAFa>8"r,@&atrR9]ALP"u;[ZSZ9
7o:sn!-6'qSiY\1CFHYX#J$jU8a?^S,c$rJ`)YUTBZ+n]A`o%L8fZeu\k:Aq/o42s+ldb^/^0
TRhbA<pd_O0K8I,U2V]A/]Aa=eb'hb%q[GGkXVgLsUk_d%N7A:'Y0Aq_j2CBS%<$m*B$(0A+!I
_G18/R@J,<K2qP/S^&h8(UM=jP1IX9ehImc/Jd2O&DEJ>Jk9g"hN;_-2a9t3VmA:*!_.#^$g
c%bn_NB^jW"H`K"3ZZM:(hgL?l:ci=q.DGu]A9Lq1k@0(S,]ALY>p*V?KVGMgTQpp[hGV52Ft)
kd.C^`r.a^/4??e:mrbNL[q)o`%Y'ei_>9sDSu\BCLc5?3btUL?<K@i%=(`#tTqU0ADh;-0f
c(>T"">mLh-A1[,36Y0r=uaBc-UMP[A,lJ6B-G46l2Z_`h@NIRnq>R#^uWm0.:k$8!<^d#]AD
-,A%;K*YN6bIG!:pl_9Y6rh[\)*mX$UsJ,(h-8c]AE0`7Z=Cb^-=.Ahq(X#6MI)9pC)PWac\t
>dZOH93LI@HN.d^ohC,jG0p,Z;S4rTegJH04&.Ga]A;_eaW"bTdb`dMb)TtI"WndR>'Er6l?S
A3Eig*o-.180-+;_Tt)PPTt9]AR:<Q^/WX@o<8oCV7U\;l#i1';_[9HcFo,]A([Wlc$jVn;&7:
uRNfDZ'XQ?+-aT^0#jOJVcF:Bslks\ZVt+6B22Pn&3Ji+&&aY8hqGeiOLMLLhZFAg0TTsqrV
7:O[J#3BahZr\5i//+K,e(sWQd;f='k!I'o2?q+2)a/:@T$p*1#Cr6NX-C!$kZKOjB%ujT6Z
gN5F\V&fC`)ND0"ccRQ*X6OoR""K!>6.5A+"1TH7$S<f/Ot`huC/GE2)4ho>5K(>.B$?HmP4
Qq=0hV^&eF6]A%b!'\JQp/87ne>/j^GPls`mZ,>KRCo8J?2(>,&_uLG&NI?51)`&nmh`-NZgd
nc?VR*njl`%d".EM;$S8J^_>WO[/C80MK`9>1i]AoW_:0@)i)JZ3Mo;>WMTWlYPZ(/A/ah0&>
_X=XKSfUK,!nRt^>OrgMGNl5HYp+OL?hTbb_TOuc:UbFmsb#YnO$(-4J(;uV>;u&ZiW7Hqec
\?4XfGedToiL%P7$2&&4CG[_>2g>KCGiHIVFm[GfD+IM^!P[OrlVh`"6edM([hg'41XN29nG
u%#)YQMroiSuB7YW#ou,L?e2+3@[4DtH&,94JT_3F.(m'as9NrXHJI&l;nF'?(-\ls<7,-[=
EnV*j=%LHPRhdQmJj.:+c0A9l7&-K514N@&m8-?4DmJqW7=o9b.L$mLRq*i@peZ+!OicdJY#
46]AoN([SF2h]Ar!PO-&h*@_QSFf<I6Tk!>9>bWV_jHDWVhB_R]AShf,3cl<sTof"V(HPts=:,^
QMiC[t'3J9N%m[cZOrl8]AiZ!TkSA*=nXCW"b+%t>)P30M).kbIsDsQ'lS_,rhl?\@,a^"X>W
N+23^V,Y;Vt\12hujnL/'/T13.L>&@Hkr,Igp?M-@sG@hHS-tB@Hfch%hM`Z'"\kCHr?C?Ac
-".$fT?Dol>/F;b(j$hIrDRM8IAlY[`,@1Mbk^Y/1tH>DQW"1l]AKo@-lL^CL'-I;N$J-aT'&
R:aLq@Qhj*2<Lc8(BW=@FIIXITcsd,'6pAT(;dK?r-R3,Xcdj_q2t)SP00Q?n<[M>[1"7L9u
7Al!0HU!o,Ljn`-"2mAGD0/Sqlf-_B[Y:?aJ<aF+R\$81Ygj/F``GX_f*P>HeM*\c#t[7-/$
K8q^Pe8]AG!l_#O\58P,`3bVZS%!'Ti6N1&pZW6ca<A"e$dVLNlRpQY[50U;-Mlq$U"+mI.<Q
DDuua3I_G'NU#A&@t,[TE@F._)&!\omI.]ACLQ[nj^P,Pm$+pF0%$:4<\n2a$e=?.$qC9HPs"
n@gKgKh*aFBB_63YDr8<,Q6Kasq#P@UWb-q5U$8b`W2>$9A5?*5M6:.T;YL`m\JNbE!Iqq$*
@%[_4%_'$%f0tDd'oZ7dj@R+-]ARKkYU]A:fghW=X5i9VSnH%@\r`p:>p087pW6OWkM5In;ohW
NCpHp+uBcq303#r?Ubn;pGGk<R_f)jdY?nk17e/F&m%QhV2_f9"&k$_Q>pY8&qQ9m?*^?N0M
MqcTl54ZbN'6\'s`\A?i?^"V]A>":N1A[\K8H9V&;HB(Wdu,JjEUQ,g?;TIudXen%E2E`_?9#
A<+hL@l<_oX=fRA3bBE0(=6qmR.VGmSYbZ'RgDY3WRuMn7*AmB28t,9ddA4aaMku2mZoO;fT
b2LQMD22(DKpB_Z?IbAEBdTOJ1uV4A*Z^Yekh?5X&V0[tm]AS'(4G[c5IMk!ZK;ioimG_OT(=
SO#p@bT%f8nfCo.AZms1>'m^CLV>;o3i;HH2RgkKK=,GPX_:8GViEKW)_9Ob;hHba)uXo-MO
X<)PuNn*Ku%>V0^PGHNHo4O+5bQj0Ase4B&/jr\!focK%hRf/XtCf6W^[M"*uJN60uS;jH!@
TT&b@k_A8)hjPo!U!F\S[%']AJZ9'?@cl08rO\h'b.jr00'Ki?VG"_Ao^rn+T[WuVU3;.8o?G
L^Oah[5@WMSrc_WU?+(GhM9#]A`ceoUaR!ZNfUM\baVmVZm@7FkUu8+MQ!uX:)NOp-KFHFLUA
PZ;aX>3]AF#Wl.n41So5uXb>[$^LBL=V@4=@!^34&_3m)t7`h=Fh_*p4(uV`""e`=3;[U$%bt
qJ6[%*]A3brKn:[2d1F(G##-M^P>kk1UF:'Y]A;ftg>58c<q2c^e(c'_N!VYL:kn4r#4aVP?Ae
JeB2s7!Edq+\pGMADASYW%leYL.BOGO,0dHY6[$^CD0Agu'FYjK'7msC2S"7Ghq!15Y(1@:@
jE>o+dQC:YEPtaFQRc5Wdkl'TLq_Vobm">.kDZau<8>(>U!UqtnXOr_#5'^\APQVBW*/"&k>
#rmlkq93"6EamW0F"U%HU%O@rl-Cb)tL^hq6bA*H7KNrZCJm9BKtG8S\DJ'Rb41Iq1\r"I`l
aZZEnLZ[9!=0kG_F.\><*OeOsNM0*r!I;HhpEEuKc2Z'Kk8Ypgs#!V\,q`JV>VgqVDn'R^T0
6)&<VgfLM.+)8KY+7I/fOUKs.XVq,lhk-FpZ^WF+ho\jDR(7$;&8;K1nuKUBDZ]AeP-e!HNRQ
U99IK\iQN9K9QZQeY>p.IA,s%[I%=-ju9_h19L98T._0_'*l"ah##:6L;@^/+?4?5'PU/_"j
hh;_fFM"7eE[@K-:Qoc+9*"EmWMl)_@K58$#iZu[qo\^=9QJ\2%rL.H@D`/fhlIT&@6-h4mH
W\N=h]AfZg%7tnM=<S\sY><q32("$;7LP73W,5]A)R]A0J,gnq)TY'ZM_fhE,cFC(s_@OC4J*A3
l:VHW<O>3f"*X*$'-:0JY7P^;TEn&4@:A"Iu?8(UNY$AP2=O(I)[B&:n-j]A<kSGbo]AdqtUrG
4]AQU/P_eo^Fi/12oe;(h+j5fW#*f=XNOI;?"l]A)fIHGFh'hNYC3.GUXE>k?^$iBqQQuhhZ)G
/%JH!/.R`pku_%8e#KXWKWRORrpjV3FBK=f>grZK&dWpOe"4eJ.5h"ra<J><C%Vg=@mPLi%X
WVWl5F:kHC2b0TWSTH<F1*C:1D"P8FMJ=ES&8<fGq7UZ)pPfnZq/ahL4+7t]A8B=K=Z4Al2/B
)?@3-4YKb=L)^9WE-6O("S;dL\Cl?;O197ZlpsQ:2K[[<MdP&kOWAd8WF.-8Vf$dE^k7=fZk
S13*T*0'\UsP4P)uho5Bb):MPS'GJLl=VWKu5O51@g<O&`6?I&9UZgs^/c^5ZS)I(X@&Zh9,
E^#hG6n[>s="2b'/+lD+H.SSS0J<-F"etHbek=tc`o93Z6DLb%JN$ar,F_S^/b*$`OK,883p
L+T6C\6nFnFP-&I57p<aLSFR8b'W+Yi+o1Ai,3$%:P1Mn';,5@A<>IJZcN66+N.a1C'V,,D<
Hlum;UP03Z>*S'oDD_8$(-JQ1!gjpiE,M+s+cjF_uipC>=e*k_T)BK.8Z\H^qm6M%2]AfB]Ath
D9S*>.[pY2WQf@:gXV.7``]A'Zm4*(mr//)7bc9b>\G;P,LUcac9nf+3ITbNqa#6tX0Ft#>l0
2,Yt++4e!":*PTYrRookRQDV]AO#[hR*e/?]A6"B,W$kAY-h2a19]A%4ttQYYFJ]ASp.$:KJ"shh
[)`gRL2DIGng&[%`]AuR<Ul`rD/;4JBPZ?Wd&D*J&**+Q:mW<l)C9&%<6j\%Kd`?@+Af![*P3
U*%9:sG+9%b3KD"1kTW_!^>R''i$pIIp^P7\Olq(JFR$4FcEcNCMeCZUNh,Dtr8iJ)bFmGRD
n-5YM9j:K)hRIkrk^)AD9GtPnr%d*"2F#iUm9L'I&<Vh_5hB6"5MLB/YPXuq.)WS)%/U?1*k
-;+l[#1QU"BBEUDZ_>%RtoP:o]AMT[.\"g=:\p("ZOCaj[\NE3>I9YnP4)CW&`+KL?XGBD=u-
YFF!H%R]AX8'=GCfi0n!8;50=MYcnYD;FPl3+$366D6V=:Q)W%O!3;!iIh4RTXnA&`0*iCV#5
au=3ZX8GOpL"8QQ";h!;45g*Dg/\pjqYb8\k</g+<cAHnliV-i*Dihd)Hh)SDa4[Qra/KCjb
!b5`U1nWmL4j%ba!sUW;aB+AZEut?2OM%=m"=U?G[k!o$Ups2W*MM920ls8h&tMVg^7mEj\0
&he:NNQ?00qe#ioB:hsJUeO!a%W#bNL<d?>nAL0c[1k*XPfoW%UB1jOj,W\d^c8ccfYTgFF^
#'Ame]AmtRfYM,dMQjlg;[!pQ6:9jo*$2)/#qEg;[lPKnA/!IOH;%4Q/j3Mj%bKMeXO!=eXj;
!['c(PY?$RRUHqd;aEihg#o&eBj=8+@$Egp1N*_gHS<*1@<!?LcB4]AAL*>1I\'LRP9RBY6_-
Y^(6OK5M,#\?ItKbfWQ#5oo0(hg(peflZ94$$WdY(6<YLRGd)hb1.:#aH;=p$-o<LIbBY4'm
`NFGI_-gCQR<EOZ-LoEdDuNLP65FT\)Jep:"^ZPds@7e1ome_u^p$=!9k5GGY)p4[H5?[>(s
)P338fKQ[6Q-.^M#'*m@3j>9^t9e]Anp6.%mf-;1bk=0mkX;oS2_J+a[1'sEHJ9miC0JB=)kE
iJ<gU6".EWKW+`k5cY]A/d:df]AXs'VJI=8NCXcrFT1cJ_8@B=e*1WOVVVu?<>qrRk@4.*,dc7
3\0IJ`>[[+4O7(nlf&_@&oHZ+[Oa;PESOES#V8J.i6Qh<WhE2HEg17e;:"\*qiSorU6c:qfS
pEPf/K8EU^*7KBI!*$i)4PlV0gNq&N]A@5LKBiAkak'Bk-lW=M-.:WT-?pYL,%A1*F0O0On@+
F%+5$3.g<l[RZ8lY%"DjNRMMMQ?IQ9<t4+?/p)E^j6*(gfV'oq(,`JeemB)*TK"#On&O&,;m
p@pHXOF@a)Tf_0QT[gF_M$e7N!6^<;Rl#0<+c[%FQb3i;Foa[#nl1;%[PfDZ%PFWU5;HSJND
oO$//<O?uTDf%'p/TJV3p+Z'DL66\>O=+pKPoUYDcG-QRKiVK"PJ*UZerdFkg\3&Hn[TgB%1
\-4>k'oBTq9ePZH;P2u=8BO#YVoIk/*e'L."gqr=5+^H*XLXF2f]A-ohe#AGmmja4Ep([Z;4Z
-IY5Q]AAL@o:`2cm.!5!aNgEM'Bhje^TnL8fkI0bT3iO%CHK@B"G^l2N<uaDO[^ZJ]A5$p`F*l
F\")Ip3T#YXId9L"Y$bLoIa]A'N>5_(!#kAb#L]AUd^n>Ik?kEd4PC\'G$Z!h#RLsD"?(:`ek!
$ld7F&WT]AVYRmT)Pcbpt#!_2l$]A#QJJGgMZR/p&:"VuN568cRp56Oa:OK(9JBinc7,'pMiO0
ta48B`J3bJ1aQ[--7r._KKb"brE[ihfU9p'qPV'd;W2+A36tM=^g_g.::a=YqhWW>)G%#cQI
6t>\*r6UK4VaEfSmt)\FV9&)CC9cEdQrq+mTe:R8cneC?(t!(VkG`bErs=+_46E*Dnd=l;C3
^isi!qTd$IL-8$b\,DZk/[E[i=_:s$=h^2g6-d7&TPErjPA2:sT*#g9]As,diO3*C,/%kChm$
'GT]A\7/]AJY9hD`JT37nl"Cc>UQ`QLNq]AE\UmW2fRgTsoqdrB@CuWV;+:VeZ/NK>(4C`Anrm4
ko:kC&X=&)CO\qT]A^<EMAIL>]AY96D1GCWQBf("P"FPaH`gaWQ^uSblag4p)Z.MpI23SVP
?2L`,d[3X+aPBBSD'g_4UbYB"QSk)p>bYAoP=VU@L^)t%IhB$\0EFHD3s;Q5l/D-2eaXcXu'
]AV:8ePBE"V$p16m,/O%ph+HJ/2*Bdd(:r*RlaD_K3-B19?E4M!As6Eh.8g8-U`)ClgB;YeT1
8u_*I\ZAgla3MId!8>UoVSBj4RJS\k2ZjqIUsK:onA$>ci0:dD1O61ms/>oKB,YUQ"U[HlYs
\-;.*Vf^Oj^u@4hg]A3JCqrFkEd2sqV`s@<2,T]AX0I!om,4V6`go;Ce916:)a]Af0-CHsjg7s`
1p+lgJGA9Y[gGM-N@:Vc+qTRa6o^5F$D/jF6:D:q'4+@TSc]ApGr"?J:p4cnlm1rH92`5G(.i
Iss/kON^J;MoZ1UDQnp]A`JaRe@>>ZJ+D[_n)^`uL"lE==qEGk>XQ?ELM_Fa5h`AKrpQgEYM&
+A@+(k,i!^1*#u[?KQaQ'iWTnp8U3/G!clruXpJ;,QL`XCp<m?a?;SFgd!h)`a+k"OnT%8C;
k.L4j5dmi!pDp(rpT+LQ:9a)MiWsY78Lhq-d_<572nq:JSBXguB#1?Hf/G_BEaeGI9s\jqV6
E9@YW;!!g@)b_]A(A0Qd@V)Q?nCO"&j/4>Yu1;W.#Af&48&9K8^$/-eCAsg+-m1#C0\mfo,L^
$(dJ(9,?>W$9]ACj[ldKZKp=lNW;aAo2oCkTPaqT\#%$M:6<OcdO)1<]A>`(uH"8mJY[/m)4@P
PUMm-nP4Aq1Gm\6f3ZO$#=[]AmqlB@!P"M.,S1tY&k^SmK#Bq']AW7r)MS4:Z=,qf4??u7F=1j
PuEjnQ??><<>(o$Y(-=m@0K@#C]Ae3-8CiXq@OLM[f/LG-@5ojGfunT&$ZqR$jaZu<Vr5A+:W
-L.Z#s$j%drK5XeL%QMd>8b<(?_G*Ir'OFm1@AJ)qpE<B%K#l=SYQpK*ZG'Fa^Y/@1]AOin_p
K:'JSPt(Bq"@EocHs/r+*77B[lT[G>=6/i9fL`m4Lf7SBGWV`JNd>PdOgtgDAM6=XjDYC<V)
uPCW+l@bp:04rk<eQW;2?G61V.TSgL)C6+m[2S/t?QliPZ-T6@nLg??_KcW_]AV6C(.8bNL2g
0R.(h.9Gr16)!/@*]Ami]Asn$Jlh-J4-b3<(nk;+VMCVOhQC[k&bI5G\S`]Af)PJ@(D^<iQ?:>K
qer9q"XKYQ5XA"nj\pB>NF_a%$.,L*:%J(rW]AgDTB@(m*Lbd,(4&S.tdGGq0`3Qa/tJD\ZDk
J*H;128nM3C5A$YV([k<>MHt<m0G2&>$J/78ckl:HE#@&6X@-l`D*Z*m2g$QJcBNUVVX]Ac&u
d\*du>">:V+s*[qAVeH\8LPg"&O$*3M;<SV(&]A3IVBJOHV$7a\%>#4DfWVW&6qh[2#W5'sqL
K?cQ'p'BfM5r-RaNT;qE@7?oJZ;KfA+Q2aRFb$CC4m5]Aeeg_X\*.du=]A4@C6^fX9`D[X?*3a
/;^q(Sao]A"$-Li1D4cW6/T]A%>es@.FaXiM*#"MDjbZ(d,]A6Vg-MM^*G&,"Y%D9=BI.B>H2LH
!')"P`26^(N\/;i?2)gH*5/I([G8"dNl<R$K.#*%k;iKSFl>Kr%92LS6'ok&Fe5]A:*(K4b3A
ahHDe=O,/%1tVTdGRs85nPI2g-SL1HIpNI>Mht'aS/Jj`\-MQ"mGhS;nB8VY\2/b=csV[[g<
u.;paaPP)MOONn?_$,^7N.9n8d0`FrQXB&mE8iSH?91.oP(`iFuQSG')XEDXI6pg3\JVLpsS
CA`kn2IqbKf:8gI6Uf[d9UtaAqh;C/B+u3t^VFFW^*.M]Al!84\J72"ElWk0%bb?uVKR'dg9J
CfA[DS`om`Ogtd1CUN&*Y9CReIl9pRJWN9>73$PC6(HaOIMNV]AP9-*n-r"BZKq"A%7DOgd-B
\ci%n*oVl=i`>I.0_7r7<2RH'cXlaf:!O_Z/l3aSd1gVD0[egjNBYG1I9O/!@OQhA+2UmmYI
c*[mHMaAWX`DpiCW[U=*3"J<AG+3^(=(An!#A_KKh+W(sRkjqjLjL[2]A]Ag+Pr+PmV]A<\WOfe
9o&g/+flOa?FlQ+re-J5ClIhPX%C;>.4)FqgE_12pR_`3LZ3J-p#f$,Q0j&,f&ml6'XVKXI[
uj$\g2-4nLg\*Yro[=Ksf'/<TX]Ai9Cj7h5;:-,i^:oM\m#E6Ed8VZk[9]AEWQ"/m<Pt3`L2OZ
NqVs,`Y!#@U#]A;3-bXB5'&M$%"'_89e4PecA,73hAte.qWp/MF!aU76'Esm,@?neOWiMb-3F
cuWYAL3Su;&,'-^SsW0!iq,b?#nOm\EpP.29gMd+7C#l_OnkefQ%0O1$RVO6RcLLhb[2n!`8
UT2UK6TsgOh0OLH]A&qs)o!H%@bk3g8E\A!smF8qXOV--HC7q;2&*(BMgaW+9+%m!uaWFJrn!
619j#OctQH\hgq:g'P'h,eMQI;9l78u?*YsbHY,P"FG&l`'T4$(`t0"F,/jh?L=+c&.D7@NT
!B^&SKBunA!f2O<BK"L+M;ji=Xq(&m2R2X?t;]Aa>ks-/)CDMW3r$oN?:G7d8`3@sSa>?k)<@
AmH-IT3lprfrk`&c8MD<M2KV5"Ug`_H<sgZqDPT1^VD+W3/M/fR7lSmI*h/Tgk[jeF&mJ]ADQ
o2*)7kQ(q.Dp5J*)jY4WZ'?S1F4Y)?n_;N=/BTZ"j'%n-[l]A+Wr0i\\g^X\>G;Bp!%\K=LCL
V=E?%C@\:Q]AoWT]Ako-D55<NO63qm*&Z(t[%k+,@rV-(Q!TmBZ%Z9]At@Kqc:i)u)ts<bUGb\;
d(Z&[Ns'Xl_Ua2+`:o;@?fEY.&0YXjNCS(,.eeT^^(H.Sg[R'8a:?'tEuQlK6lu9+>Kp)08V
\0k^1"F`,[@@R?k6Sb/NC216<`eD+]APL??73=)tl(2r=G7q(TdY-a'C6+kG9qDL9a'(YudZ`
qLCVRYNJ3"^KKh/%0'[0EPZA@Who`kHKG&^PND9>_7[cGO^fs/4P)41q.5"dC'&:O@KF(/S)
AMk-Sc<1d5VBY(@:W/\>2,)k-P4D=K"Dp2P>[28ljN>T,O9Sfp'(0>C[lQNY*=/*aK0BlZp[
6,f_(Re.INqRTK0),M)nkHW\J?l3LohTi>[ZmNUHc&;0]A3?70`8p2U&?@_2Pg-:^M>l?PhCR
Z#;c]A]A@FN.k;6mcX;u'T1jj/oVT7!(=W[XF<=I\)msj:8&EYEuO+VhHS"+$?;:=*`pt$?WS)
gJ0J%]Aa8J-Nq<<$0*=mS?_<9'OWfa<+*a-28PI,hQ?Lr(4g"/Q1D<WEml#AL4k@rkU-gK_f?
g#(-<tDIVA.[-_be"Da6QX;$4at$co8%g3qbD2q+,Y-E^"UY)KjuA,'V6qIY`DcCXN"%oD/4
]A#4139>XL\)U0m_>t5c(K;peg0u+=ERG9$eK%Bs=1)n-d99_h!>ah[.bEeMOO8!"^"O-hZnb
B=(^q?E.=#CEKt7bkS!lgc#Zr""tlV:na)g1b-f!1!?P.4o4,kh&j$J^:YE%UgDdMWgFe=$/
-tL^4<)A`)H065]An.qWf$u4a(H1g0+*J"[bb$&df,EKpNILVc,0?Z.jD![T)E9Lod*+PcfqL
cN?W\,>nK,d4:U=W49VQ;M-$4cmlg*k?n:(hFSq3DK2X^V`(?mbQ'tju'u/HRkA$S^f3`"O"
dII]AN[_18:O^G4V;4U=kgsFO<iW1M0eddC\QQL'PuheoiJBh%[$Oe`0jb;ANFU<DQJ[QJ-gh
$0%t;0ggbN6WR\pgGdhPqLIR!h8JBjpgMa-O@#?E^iY39%%0Gl;oKGGP/pdW!97Ci2frTUEW
.Et.SC+Pk:(@RLk>5ii6P_tO@$9c6HWI?7Z*mW:_^$uV<#n$)1.3.&<Fg]As3jY!EugUnd?<(
[:p293?tprm/MI^$dUeuX_iL6hab'3J:KjS(<'<F+A*]AX6hN*IP?J]AYhe&,>!/h&GsEEiuaa
]A/j:D-Q2M1IVq:.iS<LhS!&st:.t:]A!`eh's[5JialJTY2KUP*[-nW+2Dr"qVN;H^HT@k]A>4
O\"a.kTXn*@VO!i5D:<*-V5[^2$mO#/2YKrrd_C!#r@O>=Z8d(BBcb_>ede=`5"J2Z&:hK.D
Y(Ku%tH!(]AQRY!Y`d?c[^]AM*??Z&sd$q"Os]AYIiU?E5CE$Cnc'u>s%f6a:PLe[I-K=6HcWjh
TDqL=h_uQY!2]Am>%.#no\E&SE]AY7Eo#Q3/0.,XhZ4H5hkDl:IUk$QBJRX[3pCAuc]AX8,igYN
Gk'S"mLdd^nmmcUI`r;>U.nJ)bT,j[!q$L,V?@['p>2?ME?fhV$s5Ss%(EEFHPAQd9s*3o=q
2oq$XNr\FC_pri/ni@i)UL;+.H)?)DhAbaI<Qf0U8?\Z<(Ii85@r+5ZHn<`*`Dn'N--Ut!$k
$]A4c?Pa,FcP<$kr8U!A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="450"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="188" width="375" height="450"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="rqsx1"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="DATA3"/>
<Widget widgetName="KJSM02"/>
<Widget widgetName="rqsx"/>
<Widget widgetName="DATA2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="638"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="81" width="375" height="674"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('REPORT4').style.top='-10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?hTi;qJV$ku!?[>L<PT!s1X+$<1!).&.qhTdV<''p(.R%^Q]Ah5R=S-LC/VuWg--u;(,<KO>
7KjTLU:`,n,M.!MVsf&0ME*KZlBkg3V%cTBG).m=*-34=bbClaQRkHb]A$!rSNn9S\*(CJ)0d
!*?C\H:S2YgVk^T7=nCDCZ2+rTns:C<*P8AMSPEE\/t!ddILQRpb`4c5Z0e]A82!GBtDoMB"n
uB9*4)oK=`ZE22f]AeRrM>-j"pZ6FEGr\k<RVZu.@<EMAIL>%LZ?#H;B$CUl^)`Q/oQ
\/F0BH8o>a(`HY*"kggY&G6`6c.%'ujrGE=aYZ5>7Qc6s\fP+Ifc,W^O3Bim-!gU5M2I2bLo
OFl5Kos`3qC^eiMI!Gj]A>IIW%Fn2?WliYQJ\UJ0X+FAY7oCAp7r;eNgGu`\`\8U'E&L5plc[
44[quCB#qGp.!fjI.m@jfUMA?m^h:OVE_hCSE;6^<.BNo3J%JK33a^<8IE5>*SJ25nUlA"\P
t8%gSM.DQMfeUnM5-$N/Zh,G3kR"gb/R'#g=\PfVN40VFNb6Yl8l\.Rp;KoHnFo_o/"U"\H_
q<^48(NFh:+`(M6;fnB$6[J`;>bZCU<F)$g-!e#1$IY4o'D2;gK8(L';_L<VX,!7B!@pF70Q
;l)g,Mb%Q?h_ROo3Bm']A;gJLadXOmZ\;G`2+/Gq34&?TH'>lZQ+QrAh,JaV<JfDfZ8d*Km1>
e\g0g75c5f2*kmROpiFn%0\99;\_$.Dk:P0c%.0.__&mK<hF8-2Y/=se^7/kB2JXif^M;GFC
_Q^Y,8Co=ss`=pn%&#l1P$rpl>[CKVk"YOKiZhlWR'L<bM?:HOr)JptMpI"BUr8hd:Mi=dCe
@YWjOaR,5Q\>F3u6"+"]AS]AFYDP;WVcL0jtT72tc;C1#W%7hDcrAk#l&`l1)''br7OXe)iV$"
:I[cERjZ'aug(#lIG-Q(,cfe@lrhR6ABL7Ea*D"c)d;^fYYA`RD+I:Q'ddr/%:+_$<jTpK-R
^kc*4j:5tYcQ%eqp,[#6mm,G8u&LFht/fMkon"E-sW0'e8Ej.WgO0@/[FNui]ACl+M(J1N=(t
7]A`n1"5?mrN-P@6!"U@)pktcGe!'Y8PIkJ9DMoU9%q4)P)t"ACCtBUA(F%"-.$G3]A/Sa+Iqu
l?.=DJlZR*T7VgH_%M2O.[n1sqWLp+G&9<T`usd:H5ObEjmT>gVp"='"LIn16=;HcCjXT%!i
oj%e_L6Ko>KG[ZORZsmun+"t!n";(j)bV7/&ls0=jc(FH3<F/oo2KeK$P5O2h@d$KtRE3L0D
n&U\r`D19BM.lbatgA!U<sqcWr;h[@T`4H%B>1.TnS9k4$b<)0)>R$/GCfopD-nOA"1mL1]A?
_/%P>ChTtU3j?JQrU5KX'ibfsPP:lTr4`@bN/"2AV,g$c:/62l]A=Reu%UfB:l&JauiQ*E'-e
O0,K9&#!V8f%rkk+ldsdia+$2Lt\+^jP_O[4dmrMRpmajqifk<S"_C:a0J78@^FAk#-=!M":
f9#m@kbR']A'@AA4<R+d@"J]A.=[fU.F&0Z/<^n[PZE=Z#8+`U>g\>-c71W^0&4L.GT,0^Q)4Y
\O1`EsK:e9i:;JLVa0sO<bmHX47rl:AJ&F>XH,;l]A;cte%kc@$N/26$/d7NW9>1d)rB'L55'
C&?XI4TJ1W1n9,.A#Jf3MfNGfOTLFDuaq2$[]AMW6;!\9$2/`&)[^,r2'_%coQa[,h8b\.]A)g
'V@nXQTghBD%N+/?>boW>K9KWetj#s9Lh]AL-j(Yim4'o>UD0fiUfP9eJFDrea@IhM7Gs1sa7
(?bh5A>\B2JVug@F[qkiG'PqCTj<Y9;YP5:UmW8S]A):='@aIiqjh_<G$D&_B5n5uH:*jK4=n
j+2IMg,<s.#l0M\uI<lSCFrZ,5MljZSWu=6D$7=%;JKF=^Oj$YKQNW!s7i\t&m*JR@hV#/GQ
.2fCo/T.!2k8hH!Hb^eb52c5E-2f+hUV/bK?l;hOj4=[Ee1X"2lClHT!qD%1CXRFNYBIH_o%
@nVriMMEljT0!8]AiTi7=cU??/'>&GCaHt-OkoBOA**TmqqHinhN2<?/bl6iS9^N&<E%.JdTZ
K0CL=Q)V2=HTV^[.-n(WcI>2jH6WI.-/SZ(1%>3ag0Hs#9Z30nlk%blTrFQCO*`#!em#iRUb
2g"D>ba;_G^Ae=bTkV?'>9\IlMhA@u_s9Oj*f,.CP>dH>k81nEeTh)W+aSQ<NdpX88c7rBi>
o@NkaGU(HnFm%+Q7p2o`-+e&QO5Ac5dSemQ8iVVL\T+gq?\EI8JMap[W8]A;d=h:l#j^9pJ8)
04?<m"9O?Bj%#+hZKo9$aOqUEGdp5NCEUenAKHTemL)h=!T#fu"kXJ_4M)n]A&qM-2h]AY41*M
bEK0#/CV$%n,AS_/CmL(>HU3```(mP1.]A1`Q9\DMcTq%(/H;6LNj""I?tS8Lt2#e%8$]Ab^/Z
$;9r)=*/&*_,7M*3/rm<."pAn5trNAliFX!G#`"^?"A7*rD/#JW<%YV$I@Yl:O-F;>mr.\,7
!-AXKN]ANcL4ED,eiX,3-^lhG'V(=EQ1]A']A?,]A'hrV[6.jDrO84j'`^3?9ot`s)MNq4Inmk+.
ho!E%=1oq]A;>F0<g*XC[-'QM\jEFGG]A/fi/>Kq*#K?@;OCub2l)<I4^LuHI6gEhE8([]A5?PT
*4V[F9oL?;8<O60_&6so[_Sr6u9hB[d/=#@Xd9oJ-11DXU&^hU?Uj-5.I%0I5R]Aq.q$=Ye%[
#?SFAUuE\Qmm_!IMnprXE(k7?^&f!bP3m>E&F61#B"Ih<45:0E#_3+5&3*n^L<>,9`S'HKc1
[s1&AcR(!LW1+7`q.P?\*h<[8>_nJFq7-<aFA$<43`U[Bo?4fQtuYCCJdEX.G-L!=JJ88XE_
SYU<Nk,[03Zq*LS!4QJXPD\[:JjY+Ua.qm;QJ7-R229[Hk8Gl;Y),e0IOLD@m2egod[!U$Rq
mVB;Fc/d.gh>&(^Wpt]A]A@2[p'c\(?VHdQS6V[sX9(m?OY=R[Q&l42XDC3s<df2--c_Tb(d"3
<MdG1"p0A8`OBa!rj!RK89tlYZ"@HgUX+dtZ$di#0oEk@E:@U`Q+ci)pT(Ti_5!#T8`'O%0[
<EM:]A0H4,,h/C>L=@"l(Cr`>,L^M5.<89M$Weq8C>7mN^pWtAT+Lcf;ci)f:bJja]A5qDgVR,
mnl`_b_SO"jT:RU"4'<Vnlqtsd]A$-c'U##mJU"l;P%ZP<X9aftC^I9'.n?cGl]AO]A(l#T\gP1
ANQm*^"1r!_7r8d"&2`lp>g=[h\9TL(qEmhU<&1Uh#7*WE#(EsB7`LAK\[8ia0U%`5G\j34A
kGF?Iq""i*:r:^qU\,^[9;naON4qN:`:l:`!a.D>no<5gMiGS*G"4.NGNDokSmk<G^aU*\LW
uUDgVb)@,)0X?"!*]A#L@o<+*&7qr8(=p[^:KrJrP0_bTZTl-]Ah^Yp>\l36IR@n&[U1n\K5VA
mo"AgUn-Rqg>lB7EH@;NOc6$DHP\\'rpeUpEEu0e2!0'j"ie:,n#-beA73j:)O&&lFe61==1
PLPHoc6X0CjgRoT2ISbpB:0hF#aq8mWWO1`!UF16G]AfGYFqMqQLk"T_7"$lJs[81M(_Y)M=#
Y%VjW:16L%[FHm&>Wr*,c,nEF%\uQ%=#jsnj8@0@FIp,XMj2Ye$&0eBE^(hdQkH_fo7L[03;
@:3dV)Pn'A8%(?3*=C(dkk]ABsCM"%_T)#_f<5:J8=++M\9)f2%Hobm?,FX$OsdL.3elsl%(M
`P/b,nVWr;`P@$+-K%P[`%72[QFb2'5l<Pm_<EMQnnP`+?/Df=&\"_RPqQ*8KGRm<d>@$^_M
h9eG6#g+j:$#:LdAWbG)Aka4=`b3ZSPt?:rT=S=?`Hg+jV=*naD-a$rTca+(0DHjZKjlh$f!
-JkEt7ejl'-LTo-H=-6llm7W\L+[V!TK0Y"FeI,k!o-Ce'o;mK/Bb?.\fIi0>\QnDk:.,IFH
9pX-a`HN_G+Y<H]A>@JTa#jXX2c39muiflukl#Ul![##[,Bh!:&h<X%)$lD"%Xl_[m8Z>*[&>
ap+HI)nd397-'Kik44E#9RgDJ;Sl=cG"UDito2o<H8qGReYdV54?RU6EN_i"1DG.i-Hl^t9G
qF\S-S_UH8Tf7>JaO-`Y8Lrrhgn#C,r-l,d)^0p7?['?bR,DqWE?_NA3Nq,,TWp,9d1Qp\f4
At&0$_M&NIsS(n2LDg'7X+$c<o&<1A\8c]A=^e2d7pEWY?rS@q>'qEdos]AEf<`SrE#=Cj[#'h
p"ZV5T]A.m&iu+ctkrDsBJUVNI>;9ZkSs$tC`]ANpEVXTM_h?6$@h3CC(a;s*Popl4(@R(N,+=
7#/#+*f,&,Y<XJjW<,VBi5Kl'RGjsOL:"9c5M%[0pVXo6bJK6JkZ(%3PLKG>h!GMp>H[J08Y
!$:#5j>m1I4FU]At84FD<U(OXap%iUUWh+Yu/&f/j/$j`Kuf5dMF&J(Yfj:-Za;NeZ.>T;EU#
a=^?(OdfcBC]A!'E>'CP8fh$:[KjeYg@`mR#/mV(8G&t3`o>/4[I[UpM/2_h+)Z"DK:JQXp3W
kO%DGUJ83J[R3glgct1A@+mAN*PLm5Z8p1Z@(t%^01rde>3CS0NLj,b`4hQaOAI^J4)gTeH/
pYJg""C6e>fl^(4]A]AqpKMLF!9B85cR:+*u?W`d!7@oN$ib?ZIWC&FM0eghH=o#`E'`;SYa>S
=k91KbOfqM?$#gGDO'V:fQnf!ai5iEPTqB?NZt(>oRA$"F0d,De#@MK.JZ<-=-,Z><MUn@$S
Affh(r"L-t>XXC?gq8Cs&"d#.tc4TGD=mNl_9Ac'D^)?_.^[B<BhP62cV]A!d=3Lj/b\:3a5Q
`8oj;7_Kc-[N&Pk9s+-D`k$6,J,?Ak2%(agpgAOOBG,fX?ZeAL57,q$5LK]Ae%C88@ApIle_V
BOl5PYuC^%3'AX@lr251?u?uq*4qB8WL&bS/0&rIH^1ZV.]AbU1!bJjPlY!N97n`&//)OmYp1
Yh*<DLW7(9dr.5W]ATjPgE=\h@itU"AEOIUgt;,HFl;fcR=E;6l\)dZF4KBi'tIFtf2l2`=oi
QEIMg=a'8S#-@&KI*?1b@?Ag2>k[3!c=<^9>r;e2AJ30D>p$WP>Z1Yt':OO1QT/t.D@MLN8+
AZRLRFX2G)rFWT%j&ZY>VMiGtX7SLF]A-*G>4X$o7]AWS0(jr;o.76-K-J^mYA4)>4.PapLJNN
#kD$^$;K5S78g^VAR3=,4d1GtKrjqYcrJq&E7RBp='(OMrUe%Fk>J_r(!srg8/MX+HUSc6d_
#5IVM57LSaq%M5Lm33Tp5Q&7V()+C8K:WFU9GG<A)E*CW;N&?#[ArIr1Q"_[#sV6-A((kK%E
\p>s$#F1m&Y)7YJr<1o,-ZmWHFc9[Fl;*Hj3s?-R;ZT9J^>?$LA:SrJ$[75No"76P+<XFZD1
J9V!qg)C#G74oZ_F(OJkm:8_8/'.)S:2<9$"Io3Q_3Js,b,`M1TD/-YnVh?52g's$4k#VgDd
i=:7\dl:r[fj^JRj/U9-rnH;&DcCF+MjC51V/%?TZn"6PYl07GHDL,Bs'gq2ntfJ6Y>Sn)b"
$CW'8UB6P'Lf^Z+,$]A;O0Y693'-4VoJWr`c"]A&iBNU`?6fK$g5:YsA@2mP,H"r8.k3XKp]ADc
HZb\)-'sHVn9Gh$,#D;)=c!Nl%AC6$W%(eCucS$r<QM>-_G>Re1CXG"!/8dMJX?(]ASP\WYq#
R.6HfX,Q!t*c)jtH.l=d=!\cWdqNGl]AkJeuo4-@JZK*P'TpY4eL/(e+=TW7JQpHVX9%Zc2f_
UnR=^m(HNq*2bFu-3A(HT*@:-E*)E+jetcrG/n`_8,gP8<l`hJqbE"mCXuqGi2ZTV3]A^%Lrh
cZRFKV(!R;8t73ZDn^Zt6_h1sL"B:BO=<lQ98ZdBoK?Ssnf"/9B(8ToRS[/ZaZ6YSPg,>$)/
J@4eR7[BLFt_HL%LCLJgmruGE7/I^75*<gkBia&VAPH"BHrUX4e?iTYVrp6\n_5^;5NIAqEh
4lOHXZcL>S[G_oAA&pcMeZ*@W^X=o(=f-_</YKg/ZM7IW#!pX>#^Dp;(_XZ75sbZ05rSbmU9
e3:-6'LeUDT(qL8M~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg2.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(0);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TITLE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8s`p;qDqID%SB:O^8.+#=9rlTKL./8fB)da#'Uqd9<dt'J3.H)CU.J+G!.C+X"q7W#.kdag
Kc%!Z!CA%H(&KT(0T2'GD[0JH1B8'*9<`ga$iJIm\Q<PF8bk^"<8W\+Y$sRFsSj8;),ORk.D
be2lTE!Cp!81ViLh=K@MX%c?M=Tj1*E!IgZ:'`T?MA<tG"1s)YER4>8H,UMjU6GQqanYFD).
M:\rhW*efeop\VWiNUh"mjP*LFg7dZX:$:O'@$ErZA#n:&eFa[lYn6V$;/2.OWDk#1FjX1]AM
4J2T=6ReOU;lj!"_p'e]Aaq"u`G[WZK]A2]A"(!hlH<h0GZ%?)O43`!'kac5^21;&ZE'"[OX63X
frZ_dA]ApQ$^`*0i+%NT/1eX%&9dU-Onj8Mf-J'q;_[935,bab<FPY9[T<k=(Ls:.JL/Lld`r
LpW?!cCG(i@7^C?[)U_ZT8COBfhC[Iq:`8C3(u#Vg5W\MMEADfN>tVljJ5Nh,fOD12'Em@`^
UHVkr"6g1jkl]A#LX'S9Of"8GW9g7aST%[bK\b&$k%p:qV$G<[Um5X$1fl<`Ii9X_';=2+r)R
\=ol*ie-C&(V]Ara9N_IF0C_=j"=$fRju)_U5TZNaU"`#9TqG`Td3`P,@JPu>PA0P9HVHj(*0
,KmZ.-04.3_V"@&&-3H3)mRrQ/*EW&9=XNb[d/<\htAbctnUJ]A]AL()<%R4'%MQ&\MZs>`glO
I*qkoT<d?5oqei97+lsYV!J8g\6?dQZL$@+%e!P**BC9'gNt:tHKY+"KVb0[(iuV*]Aq%W6S=
*"`YDT9G1X]AXNX3$lV)pTUs0:ZL1-["i!>Ajh6j41KMNS8hQ$$TMKO78X!RB*@OoDU]AJ/O'S
R]Absd;?4/EVNYZ.!c/A%@T[QpCQ"\)j/p6;n>5SDGi</m`,X#S34U'1$A2_fbS[bX3RT4A:/
7eAY2YDlc"+?k5?.1aa2V"1h-u[=kHVO>.>*dqtl$*h8:C5k>c<,=fY-GD<#t#>CA4?VJp-D
`r]AcU9ukS[F=H[2K6;j;[7Ya"Tq!_jaOWQO;^AW#K_]A\M6lLX%FPTO,O+Yu4^-Sr`QIlqVZs
>j;NuL=+m?60V'39_aocUII9N%Z'a[m]A2E9r^00n+3Edhe3*1HYY,,)M$2<8V+OO/'U@3Jl^
C=9&C]AN4bMs2o<=?<Aj5J_Y%BJ47<iCsh91oemM*B(,CRhdnE#,Q31^-8<]Ao697micC9+60k
Q2]A<t>pd%3a9&2Ftc`)boZu`GakNJ3@q>/<Jc$SEKH2J,RT<8HIqE8#]Ae8L2<m^u=d-tLC@Y
djgM;HK^lAQ,Wn#,N'[Go,c=UOq5<1u3<F?.q.Yd(#'`\`!GK[f`:sbh_+a5oVCMa@P'Y>8n
pNDDh)^*7Qc!_uuo7Kg[UV:n)a=C?"</_gQR*SM>[oN#`JBNaNB^<n2Z:a9lN6W*A2WWPmBJ
7;k@`ZmBK(TZ1@c$+eVqnq:7e"LDV(q^4KH"![7mo^c@FIl_P.2f$F+q0q:rrL;d-k)Z95L&
XUsoJ`DnVi!:$!<M0gD7/;FBGV!@[BVp>qu[18Cf;NJ^emK_>AO4m'Wgi_dUGA-q&)%#M^2>
BZEir'$]AfLe4F[6tVMsdi<$c#H_IYHQm$dkU3Zm^`'m_FWeVnjO<0R.6='u]A`"*rn)MqH#B]A
'1niTI]A4VqZ*>elSfKA3cj]AX/>q"^\lR^$ZZQ^u/l9B!3T^;E[E.^u8Jd"^q&P/^Z\m=FX]Ac
L&nl>/R7V`BN)uO_cgq%J"3;[p@N5_3_8J2hXL1Ef"A85RlChW`>P82dP"JJiaiO82(;9qtQ
A3+3oe(eQl'f7L'$gT*ZA*&oeci(u3U'n7!\Dm-niVgGhPMCI/KPKgpHA=%*399cLA!ic$8\
Zk]A!cqQeVtB2TF-i;oTM%9f:G]A@VGH/;S\7)fB.RGOn&c0dkF]A3CtNctNk+:aXhdpp<C'-s=
FG#TH!F\47""jbp*TH,X4]AK/"r>o7rUprP_kok1%UB?e&[ST@u'#KI)]AD5<C-4Iu1gW:.]AQ$
-rK?lKfOT:%`c5%kjpOeM?7Qgsc'hgOh3@Zpe!T`\Wf1Z@AJBDKV(B><m@P:?#pekE[g;'PY
tH:^--W:Y6-tb4o`u)NPYVF@BUc!.bDD+9E.25Qi8BJI"Xd!YL5R"<nD.#XmTX"HGB85J(QQ
DK<meqW@4S*5hC2JFSp;[Hjd]A^,E@6G^\8`=s8ClDr[[0`Oj+td7da\j'C^$%mpmX6:IgY'q
lgNKSiS<.RHS&#Sj$W<.p-*s+]A%j*WI'\,lVngdX[,R.=gmeq8N@9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="17" y="13" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="212" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="326" y="15" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="2023-12-29"]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="212" y="15" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[财务分析]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="49" y="15" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SCTYPE"/>
<Widget widgetName="BACK"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds4" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="maxdd" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cwmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="date_zdzb_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="13934986-260d-4486-9335-0bce82122c1d"/>
</TemplateIdAttMark>
</Form>
