<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="明细" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[2023-06-11]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098')),
--20230612 补充
--有效户日期，以及有效户日期之后的30天-通用，关联不要日期
tmp_yxhrq as (select client_id,yxhrq,(case when date_add(concat_ws('-', substr(yxhrq, 1, 4),substr(yxhrq, 5, 2),substr(yxhrq, 7, 2)) ,29) >='${riqi}' then replace('${riqi}' ,'-','')
else  replace(cast(date_add(concat_ws('-', substr(yxhrq, 1, 4),substr(yxhrq, 5, 2),substr(yxhrq, 7, 2)) ,29) as string),'-','') end ) as jsrq 
from cdm.dwd_xzrzrqyxh_hjh_df where ds='${riqi}' and yxhrq <=  replace('${riqi}','-','')), 
--有效户日期，后客户日均30--通用，关联不要日期
tmp_sum_rj30 as (select a.client_id,sum(b.sdlrye)/30 rj30 from tmp_yxhrq a left join ads.ads_hfbi_rzrqdjhzhmxb b on a.client_id=b.client_id  and b.oc_date between a.yxhrq and a.jsrq
group by a.client_id)

select a.oc_date,
(case when a.up_branch_no='9999' then a.branch_name else a.up_branch_name end ) as up_branch_name,
(case when a.up_branch_no='9999' then a.branch_no else a.up_branch_no end) as up_branch_no,
a.branch_name,
a.branch_no,
a.client_id,
a.organ_flag,
a.open_date,
a.yxhrq,
a.stf_name,
a.stf_id,
a.zhzzc,
a.sdlrye,
a.j180tlrrjye,
a.sfsylrhkfc,
a.ds,g.yxhrq yxhrq_new,h.rj30 from ads.ads_hfbi_rzrqdjhzhmxb A
left join tmp_yxhrq g on a.client_id= g.client_id
left join tmp_sum_rj30 h on a.client_id=h.client_id
where A.ds = '${riqi}'
AND A.branch_no != '9999'
${if(len(s_fgs)==0,"","and A.up_branch_name='"+s_fgs+"'")}
${if(len(s_yyb)==0,"","and A.branch_no='"+s_yyb+"'")}
${if(fine_username=='admin',"","and A.branch_no in
(select * from TMP)")}
order by A.up_branch_name]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[2941]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='3'
and branch_no not in ('2099','2098','9999','8103')
${if(len(s_fgs)==0,"","and up_branch_name='"+s_fgs+"'")}
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='2'
and branch_no <> '2097'
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="授信额度/融资利率" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098'))
select up_branch_name,up_branch_no,branch_name,branch_no,client_id,sxed,rzll
from ads.ads_hfbi_tzbjs_rzrqyhll 
where ds='${riqi}'
${if(len(s_fgs)==0,"","and up_branch_name='"+s_fgs+"'")}
${if(len(s_yyb)==0,"","and branch_no='"+s_yyb+"'")}
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="可用保证金" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8103%')
and t.branch_no not in ('2099','2098'))
select b.up_branch_name,b.up_branch_no,b.branch_name,a.branch_no,client_id,kybzjye
from ads.ads_hfbi_tzbjs_rzrqkhylcs  a
left join cdm.dim_branch_simple b
on a.branch_no=b.branch_no
where ds='${riqi}'
${if(len(s_fgs)==0,"","and b.up_branch_name='"+s_fgs+"'")}
${if(len(s_yyb)==0,"","and a.branch_no='"+s_yyb+"'")}
${if(fine_username=='admin',"","and a.branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[
with
--1、客户从“是”转“否”的日期在5月份（说明这个客户五月之前至少是空户180天过），
--最大是的日期
tmp_s as (select client_id,min(oc_date) s_rq from ads.ads_hfbi_rzrqdjhzhmxb where ds between '2023-05-01' and '${riqi}' and  sfsylrhkfc='是' group by client_id),
--最小否的日期--20230816 新增 oc_date > (select max(s_rq) from tmp_s) 否则会漏掉一些重复是否的客户
tmp_f as (select client_id,min(oc_date) f_rq from ads.ads_hfbi_rzrqdjhzhmxb t1 where ds between '2023-05-01' and '${riqi}'  and  sfsylrhkfc='否' and oc_date > (select max(s_rq) from tmp_s t where t.client_id=t1.client_id)  group by client_id),
--确保是由是－>否，而不是由否－>是
tmp_s_f_client as (select a.client_id,b.f_rq from  tmp_s a left join tmp_f b on a.client_id=b.client_id where a.s_rq<b.f_rq),
--且从“是”转“否”的日期，小于等于融资融券买入达标1W时的日期的这种客户（说明这个客户是在5月之后激活，且激活之后也达标1W了）
TMP_MAXDBJY_1 AS (
		SELECT A.CLIENT_ID, min(A.INIT_DATE) as yxhrq
		FROM cdm.tmp_rzrq_rzmr_maxdbjy A ------------这个前置任务一定要在！！！！
        left join tmp_s_f_client B
        on a.client_id=b.client_id
		where  a.ds  between '2023-05-01' and '${riqi}'--达标日期在时间范围内
		and A.BUSINESS_BALANCE >= 10000--保证达标
        and b.client_id is not null--保证由是－>否
        --且从“是”转“否”的日期，小于等于融资融券买入达标1W时的日期的这种客户（说明这个客户是在5月之后激活，且激活之后也达标1W了）
        and b.f_rq<=a.init_date
        group by a.client_id
	),
    --2、再加上正常成为的有效户（正常的有效户里只包括5月之前未成为未达标有效户的的客户）
  TMP_DIM_XYKHXX_DF AS(
SELECT  T.CLIENT_ID ,
        T.YXRQ yxhRQ
  FROM cdm.DIM_XYKHXX_DF T
 WHERE T.YXRQ <=regexp_replace('${riqi}','-','') --BETWEEN '20230501' AND regexp_replace('${azkaban.flow.current.date}','-','')
   AND T.DS = '${riqi}'
   AND T.YXRQ IS NOT NULL
),
tmp_yxh_hjh as
(select a.client_id,max(a.yxhrq) yxhrq from
(select * from TMP_DIM_XYKHXX_DF union all
select * from TMP_MAXDBJY_1 ) A
group by a.client_id),
tmp_zrr as (select zrr from cdm.dim_txtjyr where jyr=replace('${riqi}','-','')),
tmp_hz as (
    select c.branch_no,a.*,concat_ws('-', substr(a1.zrr, 1, 4),substr(a1.zrr, 5, 2),substr(a1.zrr, 7, 2))  as ds from tmp_zrr a1 left join tmp_yxh_hjh a
    on 1=1
    left join cdm.dim_client_df c on a.client_id=c.client_id and c.ds= '${riqi}'
    )
    select * from tmp_hz]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_First')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Previous')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_ReportServerP_Next')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Last')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[export]]></IconName>
<ExtraButton ButtonName="Word-plugin-export-pdf">
<Buttons Word-plugin-export-pdf="true"/>
</ExtraButton>
<ExtraButton ButtonName="Word-plugin-export">
<Buttons Word-plugin-export="true"/>
</ExtraButton>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="false" showAsImage="false" autoScale="false" tdHeavy="false" pageFixedRow="false" pageFixedRowCount="30"/>
</WebPageContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2362200,2286000,723900,762000,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4152900,4457700,3924300,5486400,4114800,4038600,4229100,2743200,2743200,3581400,3086100,2743200,2743200,3276600,2743200,4572000,2743200,2933700,3810000,2743200,4267200,3695700,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" s="0">
<O>
<![CDATA[两融待激活账户明细表]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_dialog]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName>
<![CDATA[/口径/口径_两融待激活账户明细表.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand/>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[分公司编号]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[营业部]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[营业部代码]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[客户号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[客户属性]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[开户日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="3">
<O>
<![CDATA[有效户日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1" s="3">
<O>
<![CDATA[开发员工姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="1" s="3">
<O>
<![CDATA[开发员工工号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="1" s="3">
<O>
<![CDATA[两融授信额度]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="1" s="3">
<O>
<![CDATA[账户总资产]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="1" s="3">
<O>
<![CDATA[时点两融余额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="1" s="3">
<O>
<![CDATA[可用保证金余额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="15" r="1" s="3">
<O>
<![CDATA[近180天两融日均余额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="1" s="3">
<O>
<![CDATA[融资利率水平]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="17" r="1" s="3">
<O>
<![CDATA[是否属于两融活客范畴]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="1" s="3">
<O>
<![CDATA[两融待激活客户有效户日期]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="19" r="1" s="3">
<O>
<![CDATA[最新有效户日期]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="20" r="1" s="3">
<O>
<![CDATA[两融有效户激活之日起30日日均规模]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="21" r="1" s="3">
<O>
<![CDATA[两融有效户对应积分]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="明细" columnName="oc_date"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="up_branch_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="up_branch_no"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="branch_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="branch_no"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="client_id"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="organ_flag"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="open_date"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="yxhrq"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="stf_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="stf_id"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="授信额度/融资利率" columnName="sxed"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[client_id]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="明细" columnName="client_id"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="zhzzc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="13" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="sdlrye"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="可用保证金" columnName="kybzjye"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[client_id]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="明细" columnName="client_id"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="j180tlrrjye"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="授信额度/融资利率" columnName="rzll"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="sfsylrhkfc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="明细" columnName="yxhrq_new"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="yxhrq"/>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNAME>
<![CDATA[branch_no]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="明细" columnName="branch_no"/>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNAME>
<![CDATA[client_id]]></CNAME>
<Compare op="0">
<SimpleDSColumn dsName="明细" columnName="client_id"/>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="20" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="明细" columnName="rj30"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="21" r="2" s="5">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性6]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 10000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[3]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性5]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 100000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[10]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性4]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 500000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[15]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 2000000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[20]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 5000000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[25]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[U3 >= 10000000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[30]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="158400000" height="42768000"/>
<Margin top="0" left="0" bottom="0" right="0"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="17"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="formSubmit0"/>
<LabelName name="营业部:"/>
<WidgetID widgetID="a8566181-2d83-4556-9431-90b8ed51e695"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="552" y="74" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_fgs"/>
<LabelName name="分公司:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="branch_name" viName="branch_name"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="375" y="38" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_fgs"/>
<LabelName name="开始日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[分公司:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" noWrap="true" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="295" y="38" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_yyb"/>
<LabelName name="s_yyb:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="branch_no" viName="branch_name"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="375" y="74" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_yyb"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[营业部:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="295" y="74" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="riqi"/>
<LabelName name="riqi:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="119" y="38" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelriqi"/>
<LabelName name="s_sswbbl2:"/>
<WidgetAttr aspectRatioLocked="true" aspectRatioBackup="3.8095238095238093" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[日期:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="39" y="38" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="riqi"/>
<Widget widgetName="s_fgs"/>
<Widget widgetName="s_yyb"/>
<Widget widgetName="formSubmit0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="1102"/>
<NameTagModified>
<TagModified tag="formSubmit0" modified="true"/>
<TagModified tag="s_yyb" modified="true"/>
<TagModified tag="s_fgs" modified="true"/>
<TagModified tag="riqi" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="formSubmit0" tag="营业部:"/>
<NameTag name="s_yyb" tag="s_yyb:"/>
<NameTag name="s_fgs" tag="分公司:"/>
<NameTag name="riqi" tag="riqi:"/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.report.mobile.EmptyMobileParamStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="1102" height="165"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="112"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6709E3A17B2712D6B23823F2C27AC440">
<IM>
<![CDATA[!CI4,oncL?7h#eD$31&+%7s)Y;?-[sJ,fQLJ-Z>Z!!'o#@ESWN$49?m5u`*!m9L*Y>t#XNpk
Th6-o&H1pkfAe<.%)2S#hING@l#bh6o+\Qe'RfXbf@5&Q0\oO=KdSc[]A%.!J(B2&VV\uUHp2
^YuFrD2740f2R^#-.sCAAF#Q,Ys4M1_AaeH%B',V5cFNNm?acG?c^Fh1s*:2cAkk?4+sJ3T+
sJ3T+sJ3T+sJ5*`n2g'1XaCuZP+L['!=D$]AG>a1b2i^S=$(qZi:fV.Euqi!\2p'4[1E7"#Cj
))E-Yg5okg[)RlXfWmtg[pj&>=]A;VMp@!/BaIJce6'13GhZbXs$Ws"n!<QnkYIn[1$C[X"Zg
R&r@XS2(KM2V.*?S3DJMMYs3deQ'ZcW]A*f@0bVR'HnmP'(P)#,K*1gG35$E-)u3LNjlNJgB2
S!E3+-fc2%>5q7IB:^H1qBl'@aVD*[_p\nc9Z:;e$qLKktA`$kVmM)EU+eJh;pX(P,s)#N.H
-`3DtPoIHjt%MCaaL)POh[?E)uI[E`67]Agl2kRI#B!B?u4_Q;^/h$J?Z[J$E[9ZWQZ8?M=s"
NR/mGRq2(8-"&]A9,lU:(M1JFQZ8";g*`./?SDdOplL;;67VVCW-qT*^sK8]AM]AFKqG?V&fW-q
V`^q`#5&34jX2_kXIPJ7MkgtJu05lc?4:T+H!-(?S88=BAi\WA]A2r.KG0[1BA?F56Le$ePII
b,2(4VtO\1UdD0m5eMhJhNMg/n5O.p%ZN*ikL5@?38R!hf:d_C(A.Jc!t_PRljC._g:tfTjn
a.8Tn_JX,NR1EC&L5;<OX;[D,-J0#!Y]AtTl.qQjg^a8!Ig\66I%)PRC[uRTI%*r0rmjS%;VT
"9hmmYLeWWIf-a7A&>!qZn43QQb]AV3t0.Y5$f"`:'!6"-P&p4Ipni)bui]A_4,SB9q@q94S:>
m"92n+j?6$SX)sg@7>h?f0fmcRdo/8c<7Ghh+mOG*lT;[Z*Yl#lOd=`rl>E<)q[J\B;?FS(p
i=4"CKEAI:j#4/qg[+<EMAIL>?tLm"R52Q-CZVII/nVcA*bHldQgq&gf92g3/+$;C5aF
B!c!Ic67<o;kG3hIL5Xe)lp+AS&l's9RW0W!1:?Kbj[g;`l=TJlK6TmFYL<=\oM:h3qOmM^q
XRb5X#hGmTc+hMlQG(>^rGn6_X=PX8R']AjTZM$%SA7S&4\3U*5_At(k<SKEP;^^ks$%7dq]A5
2fi4Aj;#<ID<VsBp^!jCe>qXI@P=+oY*\]AIbGOU4so&=Ij50*?lp^DE8iJP%r]Ak;0[dp:-\'
VP1"L+!&iohbAJQDmIQYQh_R-XHXn:piKnY2rt*R&ihYq`@-HFX_Z@i-^*/bEh2#5$>Ec6J^
m(k+P&*f+1UL6+:mp_,&&,Bu]AfbCu:Qd]AokXY"e;-okVNj+VP]Apg0A/u1!16f)>Aif^/VD>p
Pqg1p(]Ap797ds:01+s#kC.Vo7mYpIJaunREk;nFp>?e58`C$`,Y:)`nDd2@8!D`D6<<-Frfp
"K]An'>=aa)/6$20paSZDlO?fp6t-0Y43AZ7[q7a)'Yt1@@\d)CMR:'6;+$gjO0]A9V+jg&5m1
81u6SQmul?-m2%EWB)$`7p.k8c>J4jn*F?h:kAADu(C=:d?E/3&-rM3Q1!eA(:G-^[o]AblCK
^!F-lBU?P=(L%;O'MfEW)uk]AZbIBA5iMG@QY/T9I\XS_qJVTZq"0!\+SB#\k>k-?g><Y1?.A
!1^9J@nqA41IWY9;`-KV58>+*@)CoQgk[k.Gi.A%B+6'X0oP-;7C$8[k()%!\[0S$jj>6*TE
.(MVYYp3;QE7)g>h8(V0qLL="j&u<Ga:0VkHh#`L(S-^CRf8gRQJK:QNu9R>W!t5EG9:s$.g
M't18OcngX1)0F%1"?Bk6KU=.pD>>"*>L;?=SaSE;7#?sXk(6/F'Ge&iIRj:&R>I<^-eV4Y\
/#_6-XNU]A`ucK;,7oWsl^gG+N+\G^7+A4%foS'#e9Dm+B8`)(n#*S/LBrAkJWcNE+'eAh9Y^
<K4WW)`>WXn6cfc`Urr/I'#j&8LUK-XQ*1UnMqHlf+kppo+=f&R>A>^nnIX_#OEcFnlF"l`1
D,_B:O@nc7f/g,WM%I9%]A$g".@N$.H\ClYkJ0So5Yn0>@rZ:'9gs&7fkWEa"eLLd(I>&Cc6,
i42^+INALgQ@e51WJtoYefshIZ)eDEZZ)6Ik9JF+ltZ\WaFV=mrk3@R"":V\I30b*DNFr6Tt
aXHeg=X>J_AMD"$ljJg*h=5:QJZZGi'G?,(>=&-qb>X%8@*.moS#i\R->m!'&ugq2:uS8WSO
GjHUr*!-`]A!!(7aFC"\2s%P:<<1:n*%l`pSb&Kc^qc;%$HURV<llJpC674ub.=]ApMK`OgY85
bWX/MY$iU_t>p.,:!*6M"\fa#!ej5_Xo-L!=(>br92UjjQ/&3KWBSBo5N"6huK.74e(0P631
J>#;='<hl\d>Qb9HZHLO6A![+UV$R!dem-:EEVnI\WdK18gIcoD/rIKe0Fnn!R.KPV:rI3(1
*XnDF>XiRPR/rE#Wsa0^[(1ZN**t`5ETj`QlP9JCC&t!r^YH/BLI3(_#"P<iGVknX3Q&1=LI
9)Co:Z/F"X(\'#(ZHlT:uTS:S?YIYTpL0m,AaLW*]AcP`lJ=6iGuV=:KerZKmQ?WEs"\bj4TE
0W;`Zqd1--uG:.Z)DstJjC10RV!;,G:]A?_"\E3o2r1n-2^rt2Z2#6I]A"U_]ArGI.W7#>QfjDI
4_N8"2?S`"E79@oM^5c(T]AnJ?goM%hN]ANbkOh=tdVs=b[6Le?BrR#h`N)(?OcA(U:4Q2W/Nt
*Y8YPiTqp,YuT]Aagu;Kf&<jqXR3Wu;U:WrnV7GZ4^]Aj<`=8##Cp7a_',OI,D+/d'%:3(HG`<
cQ"U?W!-R:pgGkb'7f!aE@Y?S9C84kJEPS9I2fUR13Ec.5=gm6Qa?1a>,dSoG,s?@&MbI7VE
p/r!cU\A1dZW4gI[u25W1cR3f68kQjWU"#!B<M_4o&R6!ik#d&Al4*[jp)cTtL4X1`"/83s@
nm=;6GL8jp%S96rEs"[hb43q!AKj)MdTU0r:,lksqVU.EpgO18H^78B57KOB!;Z+$-6BaM.s
1&<U,cUGS.A5UD'*qGh*.r7RO7Q)AUU5Uh.QX^*92dGr9VC9sT>(U9V4J_,(uE=Cgt35+(#C
QQLaWe$6(;6S-IgR;Ib13>!$&NO\-III"@)(#$SY[ER]Au^ALcP7"D2-72%(%R1b8BXta;ck!
NeiLILkpkCLkpkCLkpkC#Qar+1WVn[%&g;-z8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="Calibri" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0000]]></Format>
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="true">
<color>
<FineColor color="-13421799" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[=sql("hfzj","select emp_no||emp_name from ggzb.hfqq_ygxx where emp_no="+$fine_username,1,1)]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="可用保证金" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="明细" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="授信额度/融资利率" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="b2a27710-01a4-4716-ba79-8d139de03a25"/>
</TemplateIdAttMark>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1685002312562"/>
</TemplateCloudInfoAttrMark>
</WorkBook>
