<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_remind" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-11-20]]></O>
</Parameter>
<Parameter>
<Attributes name="id"/>
<O>
<![CDATA[7c77ed464a1044428f92b25d3d8d9c04]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR  
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, FGS AS (
		SELECT
		ZBID,COMPANY_RECEIVER BRNO,CONTENT
	     FROM DIM_FILL_ZQFXS_REMIND
	     WHERE ID='${id}' 
)  
, DATA AS (
	    SELECT 
	    	AHZJ.ZBID,
		    AHZJ.BRANCH_NO,
		    AHZJ.BRANCH_NAME, 
		    NVL(AHZJ.SCORE,0) SCORE,
			NVL(AHZJ.DRZ,0) DRZ,
			NVL(AHZJ.DRZ-AHZJ.QRZ,0) JSR,
			NVL(AHZJ.DYZ-AHZJ.QYZ,0) JSY,
			NVL(AHZJ.DNZ-AHZJ.QNZ,0) JSN,
			CASE WHEN (AHZJ.GOAL=0 OR AHZJ.WCZ=0) THEN 0 ELSE AHZJ.GOAL/AHZJ.WCZ END NDJD,
			AHZJ.GOAL,
			AHZJ.WCZ,
			AHZJ.JQZ,
			(SELECT CONTENT FROM FGS) CONTENT
	  FROM ADS_HFBI_ZQFXS_JGZBMX AHZJ
	  INNER JOIN RQ ON AHZJ.DS=RQ.JYR
	  INNER JOIN FGS ON AHZJ.ZBID=FGS.ZBID AND AHZJ.BRANCH_NO=FGS.BRNO 
)
SELECT 
DATA.ZBID,
D.ZBBM,
DATA.BRANCH_NO,
DATA.BRANCH_NAME,
DATA.SCORE,
DATA.DRZ,
DATA.JSR,
DATA.JSY,
DATA.JSN,
DATA.NDJD,
DATA.GOAL,
DATA.WCZ,
DATA.JQZ,
DATA.CONTENT
FROM DATA 
LEFT JOIN DIM_FILL_ZQFXS_ZBWH D ON D.ZBID=DATA.ZBID
		]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="4">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="ID"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$id]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[this.setValue(id);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="ID"/>
<WidgetID widgetID="6c221d5b-2f33-413f-9d50-803169443d5f"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="id"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="518" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="518" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_REMIND"/>
<ColumnConfig name="ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="ID"/>
</O>
</ColumnConfig>
<ColumnConfig name="STATUS" isKey="false" skipUnmodified="false">
<O>
<![CDATA[1]]></O>
</ColumnConfig>
<Condition class="com.fr.data.condition.ListCondition"/>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="id"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$id]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[if (fr_submitinfo.success) {
	GRZX_KHDDCLOSE(id);
} else {
	FR.Msg.toast("请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SUB"/>
<WidgetID widgetID="598c3ce6-812b-4b7f-97ff-45da3730e068"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="button0"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[SUB]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="539" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="7e7b22af-7aa1-414b-adaf-607883959bb7"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,952500,2220035,381000,381000,952500,2220035,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" cs="3" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #51A579;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/tell.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-align:left;margin-left:8px;'>拨 号</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" cs="3" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div onclick=GRZX_KHDD() style='border:1px solid #51A579;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/close.png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>结束督导</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56">
<foreground>
<FineColor color="-11426439" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?_TndV7FUfkG'#D6%Bpm,4R\Su-,*9M%qf=e!Rfe8!,N$5FddLlC:!&8@TYVQA6AMPsL?jp
<:97mu^:=UA2ui!Gc-0h!3R5`rqc[JT='H(Ht@kBY#S56&rOhtQlZY+,H6rU0!$j\TL?P!?)
E'*&#7Z@Cd"mf7maJ@=@P5?^QX$^ofNJD[.#]A0hWkQVL/WJ+q%ZZ1C\BgD\22KrQ%leB2i@l
PHmVqrT.R%as9T[W_,q'ql.^<G^O$OEP,8grXt+>f&-6aELXo'Pt9MflV;_l-G^rU#*b"q=Q
nE"]A%Yck7r2OG;XD-o#9VpWLgE%)HYBSe7?!"_Td-loeVT*H7G\!?j+4\Rk4OHXRR:"%BkK/
@mbFL'IFo)Zl]Abj?87cj7s=gH3qr4\nq)"?V=]AR@]AD9GGCdr)%=ViU#BfYa@eTrsk#mOG<hJ
$0NpFuVYlqc[HJ$.FIrj1-a0Oj96+f_]A9s(8Xf(;c^.ViW-TGO$t8FQ</8g=/0)6hZGrSM"q
#mU=',B%*IU1?CBOS9!U>lf&0<#IYdtk21'!PR`263'qUR/(Xo"/<J+PU(ZFRWn@VnBY(c(L
<l9:h=?55-:G+6qWcJki:88[W4dgPEL>rf;sj*^rN09)_HXEe3F):9@E3N9P>&'W.-.d8FR;
KHU7lR+GAShZs*6Bs)kC_oFN\;-M7dYLi7ab\,k)!=/,^N4`5gR)mq%.&!5!Eb?QIc<f?@0d
.5V6PYr[GaT;B-Mj:07k^dA7E1r49<rX<I]AXl#N/%hFFJXfUOlEtBc7Ppps<I2AF&\RD69dd
;<llAAn09@N\i$_G$pUA"#+GG]AAN&NbCp:MGEB0JBY"XI^61Y'k3KC&7su_6O3-0$1E:Ej/M
ID#Wr;'K6KDZPY1=ND2(^UK[;6AK3uk>Fgt'RsqD_OYt^,>Z!$0?JMQbD1`g)FD&3=iKTptf
JW-Wl,DMTgC!=J#DY-39WO)k$[Xlc3Wk9-cQin-QG6C/ZkWXZ/<dX\cS'-'p?_2Kjag%dE&i
"C,e1*nXrQgd)aRMQ2sL@tDs1;of]AlO>e!B>nqmYHFnjnD?K($(#XIo6P1gg&_2sNs7qF8qE
Nlj4O$bHPZ(a`c`HcO#"c+8q^NQ0(1Sug5qQZ[bgdc#r>Z_G1!&fF(q)H5o!9r@gBrLWCq\M
G!Y6&ea3OK/KsZ?A-0^^fWIf4JO2PH'YU0TLX`mVXDM9KUYt1-(e6.Lju53n`BA(D6GjWCl#
Cl3F'J8!Fp2Y-M&nn3\t!R-[bFTJ[,5D\070]A8MDQ;6ua(E'fSZ7P,gHM&#Q^7M)q*cr)rVW
Fo15DW9L^q3i]A;lo[uoiN8t(JqXMr(an[NW_J:?3K"m?,l$YO*ns&E16rFQah&4mp?+=bWX^
9PJ2/M`kW,45?nhHrGhZ/mXT0ET`cHP^^ZEI*_,=9#6YS49IA;k)!0Z)$cK:81=8og8]AR;Ja
s/;L6q+oR)$0AcbKn8sgp]ABZ$bW2;3H!*I."<$W/hR[&rL9VJ"rc=TjVXtM77;f:E&uHj(id
lT@gf&\CK-h+9^TY>1DehKZqP?g<M^3a/SDC^oBJFEPEo,;nlOB:5(K#nshNOpB/]A/joe;lJ
AE):Z2kLH$bp[TVFMB<`:^,p[4Z"9`]A9rs>G&e&lU[#cnABlcnp*.Mr^<T/M!dgrq=NuGGTR
"%e:ZLR=4XTZ+I3GO#'FqXuBPt=/t9';O(5iq`0G=Y$4XM'Rah/;NE'6<gqD25;DRurgK&*3
-*8:p5?<5L<h#Q6\$;*]A;,g:f#rE:]ADV@fo?MG>Zk.1bF1G*f<#U%$9u7<'):j*n9+D<VWgf
0c&eZoSrC[naPrh'iEV</JTWDC2,.cq3j1'I4UkL*Lf=-0?1SqNK37G/]Aj#".5C5=5ba*j/n
<E#eI^/pG`6[sYk4VBn[C#VfGQZsK[lAs':4b=r8\l/2Z&(]As,dlnM3m/[oLgu;85Z?Ia.W=
+[m\\V>[9f6%shq*LZQKgE>^Qo#/aGp_&YjBQVCW3Ps3JI5s'^7VV:iKM,RJZ]A$^fqF+Hq^$
IA8n9hQL9)9p(4'nkZ!H:i$Y%LZW6BL4,+45\iKTZIAPMQIE28i!c3qJMr./JZH"Ci9kZln=
bk1Q=F\S1Q]A3a_<40^E%Ju.i9NXMPb<[c)*(t]An.L$AA4BV1\gpDk%18>AkXWsN3%o+I*!1'
#A`LW1kQ-t:N\X5TO=D#Ju:c,@Ib,D*g\tM@^!67<nAB?T9RCHk8fubdh%tTc_?;QD;h9OOX
-o5h1m5J]A,B0EXW4l<N]A<+">8k8tUR>bDpnjX5$YAGVS0(-9)F]A):hMVo4@rl6U<*:F2`6%g
A%?3)k$(CZp]Am!2Q?Q.B68=a6U<a)dJ$0+/3QO+A3T[=/sm=(sOp1VVgiI)F3,q%c$DrN<2M
/re5+@4X[j`KMZ_:Ib@)!2ue`23QQCT;H(Anir&QR8&;jh&+2'Ju\563=(u)Gl*hj&f(l`P&
tFG.cq@*6Rt]A)Y>B0eU6pM]AJdYp-GAlg`p.'u,OTj.(K=US1#^1p;4ocOZVE$k<X(r*@hU=D
hgl3h=SM1f;ake(TT#"]AJC2&,f&&.B%\j03@oH;m<\\^Z/MaRiW>ARAh3O2Nc7l8C:%6DP$i
4DM3%i+UFRmD>>4k`h?%8j_ff,(Z2Y?OrC^T8C%n)[s[1cP/0dLi"'6;)$nq7F>\#%&2MioZ
+IU>*$K&P5\Y![2L=oU?FlD&PjLIIB`Ho-Qc^@g<VjXAX'dq6Pf^`ft&nu5o5>J?Z2CS;VD9
mtIk2,OV@h&mgu5oc$pj1efn)#Cg>OWER3F9O''JQ!4nK`g!,K=tO*^%.H`f,AYQZ>sr72mO
3;DTO;W_VB_RHNS"j60TRNboGCuFF4X&2nGg:J-Jo4jM+"&Xk=P`0pL,_BshhJSeP4[dW$m3
g@@dlO#g*_TnC4NU+N3igYb6S0.[UG`>Ns5_G,OX3^[@Q.KsCJi5j<,"a[Y1TDrS/H[)MqfX
@E6""ocZGM*^[(%h7O=t\b(Z[b@aN4IefB`#E(ro2+3S&<:S1&N!mOH;Kq1X>o9Xg"4Y]A0BQ
leN;aB#=0>jcSF.rS#$aV:p^cs]AZJ?e"qko?j*KW@/bGUV-I5h/14$:HgN2^)?gX&&PUS"^j
GXm)kiL%I8Qbu^6'.\pRHBh*ISZjM<b[D['Vk3DX+BQiRQ\ejN!E-Y;HSI_[Wa*fUufhEJl]A
pACiJCa4rREd6Bh&#WPi:f[rQ6FABYC&NQ74:-BY*`bbLGMj9inB]A4R4.,['QAAP^b*!O%gJ
Y,Pd58;$"Y&oJ`5pmf0KL-KU.W-9e0Sc"90QZ7!1W0%dW:l3d79)f9X0Ma7B%`[e=pmjBCq4
G-DChC$Y$5lAP?Q0,TG-j2RC$WuIr,ZB#!*-SB)B+l]A\9.TJVh<R*=YV;%$#nPb"^?oJ&%/B
4b%3`p()C%t:TS/QCKB.G+.5Ld-tG+;T2cc,Dm(0XGh8k%ecEUjbUh1uHVnK<R`tY/.M"Zbs
5dIrHmWO^.M.9J?k-q`A#u]A2\l[qZ&"e&U)#:Bo2'fa#)pQ#$SgR-DiAPUso$?UK"f#4]A97e
B[mR\%n&42XfO2LHDj+B)68>3L>3B\19B(EXkdUuV]AD$b8<L0d":";/TE@m_)(k=2e3RJ>p/
_<Tjpe*`]Ap.2:<j0%<nsBft]AOA7%f?X8=g`H1:Fn+e74Yp6P>rHKl2+AH%&XH<4i>N6H(UF1
\=X.X!`dlKA@0:Du?`=\gTp(p2*3%S2Yho*nsNlo.:,MGXLVn"i+FZp4e)jkp/U/ZAf?ROcO
D*7e+ENEjEWKo#KrSQ>PkI+Kj?$g*eX7snd\e/9Ec>hmghg5`$=NCI&+)O-`%lZHkG,V1iH.
=7>c`<)ZXlVosnPJfJ)hE=s(7%YRlQ=b5T<7)bZ@]A?6%EB+p?N'3THSspB#Aheq/Z"E)*0/+
VG>!n<LCK_$j\mi]AoHpAHN-+uoMO=-O=h8\\PQMAV\%!1JY9lZ1TcoNOJTeZe($=+3HOBCjT
,Ec:KaHD3qL^/RNa2UMgfP$T9i!Qs2(>?1$if.#(G'g`"^q0lgN5_M`1>U,sD@Yo8N?=!#c!
`MC8"qNaP;tbg?2/^OhNA&nF:!qJ]Ad.opHAK`u4<D4TI'"@JW-<6l*7OCkM6(c=GK9!e.@0Q
8=6WT2'N3;3Q1i7$c;PBh.r;=i)+r+%85bHWO:I?jM9:sEY7I\*as17pi6,`('p+:&2,#(@1
c+MlN!K28Q^1$%bs=(I`.:J4P(!T/56Ok1]A7(q7a."$\]A<R2N<S1>XduEP&fu,0Qf>54ET?E
XP'WQVpRke+dpbq^":J's\Q]AAo[J&*\DY%]Al.b#$m[1LTTB%q1.bKI\M^"ItRg5P)Woid3kX
"/H'o[aZVV1?"F[DZ1>E='j1(@0b/bZtK:/2s0$a,@Rl"K_=%$8L4D:bD^WP&3n(oU-+]A4G;
V"M*%,&]A(EBlAdFoj<=hl/i-2ZB4YoIg'3)G*WQ;L)AeSGMI<_0ull>_L!)]A`<iB1^Q80cWo
_B/%`iT'VMTk1Zsn1;3,!SlGeQ.f)o*%M%d6'CqqPlW`3RP;[X0<-NRl9#qkse5KQ`0/cQ]AM
Vf1.7kq;?Qm).<V!fgN4]A-knNsTOcA4$lKX)ZlIlQB=GKRncnc2(Gf\+UO/L?-Wu!3c6c4,R
^X1u5,LJUR<9)u&/WdHkj#=LunEKM,bAXPicun.J0l;`A$8od&>OXegAi:GQ-O\G#g$)pqAV
&\M&iZCq)c(\gf@2/q^.FnO?)cVmUNJiR6S)AZBrfVWB&[FccmH7>XG+DE'Olba,[[n5//F1
j:7ibQ<tl[g5SBY"*_gn;tbDe>43HNLAK+Ik+NMK+3KfU0u[AK[nBj*8g>-</+rO]AQUss3g2
?^`UI[(r.),G=lYh=sTG^odR6Dr1,>]AA$l/r:T^C?.k^l$6TKYgZbI\#+c[+,X_@c@CEk@Q5
:"4`d/odMYC"q>g6V<_7DZaRVAhnQ[WR**Yog-P^OBpaCr#.TG/XG4D\'<?et',`+^k'-G2Y
NpclOOlo2*.Q'#7ioYQK'a%bLeD?jH%K*O#Wh^^eut4(&<[J/tirrr7\sFSF^sCnn%;[qX1d
H)K8?!8t]A(&%f"BhS-.$h$<:&Z@*OmVtNiGKmrNrf[oOHp@%\\*2is^TFTJ&3`$(H5TQd*Fe
B5oJN?U4l9HAg"9#u#(LCH;\<D3@.)oldh\3Pi[d2SVIf]A~
]]></IM>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="53"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="465" width="375" height="53"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA"/>
<WidgetID widgetID="71ab7a7a-7869-46ec-a42d-733b217a1d47"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="1" borderRadius="8" type="0" borderStyle="0">
<color>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[233916,723900,723900,233916,723900,723900,723900,1143000,342900,381361,1143000,342900,152400,381361,1143000,342900,152400,381361,1143000,228600,1714500,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[571500,1837467,1333500,1333500,2286000,1416051,1416051,2286000,2667000,571500,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="8" rs="2" s="2">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="ZBBM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="0" r="4" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="4" rs="2" s="3">
<O>
<![CDATA[当日值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" cs="3" rs="2" s="4">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="DRZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="4" cs="2" rs="2" s="5">
<O>
<![CDATA[得分]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="4" cs="2" rs="2" s="6">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="SCORE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="4" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="5" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="5" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="6" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="6" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="6" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="7" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="7" s="3">
<O>
<![CDATA[较上日]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="7" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="JSR"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="4" r="7" s="3">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="7" cs="2" s="8">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="JSY"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="7" r="7" s="3">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="7" s="8">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="JSN"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="9" r="7" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="8" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="8" cs="8" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="8" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="9" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="9" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="9" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="10" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="10" cs="2" s="3">
<O>
<![CDATA[年度目标]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="10" cs="2" s="10">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="GOAL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="5" r="10" cs="2" s="3">
<O>
<![CDATA[年度进度]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="10" cs="2" s="11">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="NDJD"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="9" r="10" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="11" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="11" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="11" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="12" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="12" cs="8" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="12" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="13" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="13" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="14" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="14" cs="2" s="3">
<O>
<![CDATA[基期值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="14" cs="2" s="12">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="JQZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="5" r="14" cs="2" s="13">
<O>
<![CDATA[月提醒次数]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="14" cs="2" s="14">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$YTX]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="14" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="15" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="16" cs="8" s="9">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="17" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="18" cs="2" s="3">
<O>
<![CDATA[督导内容]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="20" cs="8" s="15">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="CONTENT"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="1" r="21" s="16">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="128"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96">
<foreground>
<FineColor color="-7498069" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="144"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96">
<foreground>
<FineColor color="-7498069" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="微软雅黑" style="1" size="144"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0]]></Format>
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1">
<color>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</color>
</Bottom>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00%]]></Format>
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96">
<foreground>
<FineColor color="-7498069" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1" paddingLeft="5">
<FRFont name="WenQuanYi Micro Hei" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/3`eF3,rXAl,p<'n#/qj%QAPGG9'A8f?+T"VC0Q546J`\,s=AW0nlej3GN0T<:nL+G-51#
+RMlM^^0hV[4=IidMEF5MJseE]A!DG-:/$o6d-NY'GIh>E/V9gL2Q]A.f,VnY3Ae\*<]A4<clOA
q@I:&XV5!NMhRo_5qdK'TfTGHMJXTlrWS2dCU/.qj?9<NYcg@i@dG^T9Oik1jpS[@bqg?"jW
o71^T\^A/!p.IBhb_"L`I1td_)_M4JeKM3F\`:rMnD$KqOVs$jN0:&ji-0)B__B?lZ,Bhr9U
:C+Qb#FYhjU[?tNGFYJd0O#=0Pm1K%L-c3p`.8"L7A\tMkTr%e5d8$5@i9?a3GUa=>_%>!3u
04j>AT)7`_;naJO%Eqj"?>E`jp>tdG:)!,HbHSqf5<s\hBU]Aril$h>k4PHPUBScl5MAf;l$A
M@q7<8W"%UoCkj&9r0b,a&8nc!Y12jQZ+%1KXthH330)U^@eIe&sY!<\P21K7=<rqD5/.0X0
sDU)*%6:LQ:rTpl^hZotZ%"o4lH\NJ<Tq/C*of:(8dE5Ei`i^]A9;-n.S]ATFliS:GK-oOac-7
6(:Qht'_GrV`0Ab.L^<HF*[pd^bY8kPu?2!V4FI_9[I*F3h"m(4+G-7>pObj*<&kGkia&GMl
d`-FrP#<0)_,)Gr]A9pBF\GIu:Q5Gq&c]AH9)Q>`=sAeV.r6fo820aBrW!,*ZL.5<cSW2kbQI(
Lo*6i'tUWGI<LuoNJOKF]A6)p1<TQ&T2cTX=`G)+\9Sc_X!/!JL3#OMC,[INoh>[,UAU+`[F.
.jTiKke).]A;7rl)-@CIbuXO/>6b&NsiFCW`!@jI3pKA[Hf0[]AZ1t/2^kPQ:upWUFDR5?&4jU
&d#Rm>)hi.!d3kD@.C3c%Ztb/m#CH"7?%S.6[t_3nN]AFqq)?io/Bd\^dFg3<*n&$bapmU6L?
#1D24B)Z(RF['XWBZeXm<Di8<*Yr.=W"q`l*msjYbd.9F5^p!!$>g,`qo5/c"=CO2K$6U9[+
<)/'-%<1p=Rlq)6_Wg46kCda1C<g9B4'-I8hZ40@=o$,$YYn9BsJi*2:HfA,(TGG6?tUs,i5
H>kI!#G7:/GC:.,9Z+-,>ef$@6u6)IRcCJJh<kbm=!-L8nigA9fDbB2O(r14T"\?O`hHu[En
dl6NMG^JO8M2*2kYCBMpC%]A3q)T\Me1(YUtm2p-j@Rfe5#:F!ssT2BeicBnc#a;7K2KF37`p
5Y"\1r-]As6E]A*q?W.0&5u==YWhaD'VpEA*Oi13TEV#4'&!)Ri!Y)"D[`kISXak.#Rng)+a5L
(_kI/gKI9?"jE&Xns/p_psTX)(JDsq/a-Clj)\((cZ%H3<:8t=fC5QS<h0b[CC\'n2E+:_g\
/]A!n-Ii8t1MbPAT>Y`no`5m^!ZWVutJ(?.uhqX4[=.a-/uf\T%c"^?g0bLYohY(WmY3oHtF?
QNSq.GeZtU*IW8jVK^E21f`ur8!AoFRtPp296ZQkDRChrZ()H22%Rm:H$]AJ+;3IRIIOU[PL@
^e5ZE6&^Gf`V8_XYWA,qAO%>Zdomga$G[&4^1o7bZX,f1IdC&o]A&kK+%;A+>_*imEN$Y'S]Aq
%"^GT?.5&XL^:'qmL']AU[b?N;og*.Jjq4d8$Uk@nA$a=!Ls"2F4[^;^h@l+%;dmnkNN!@nUe
o":91>S9aQRe(S/3\#$[7i&/6PJSgYh4D>4NUV9]A)bpq>ctg2.3C,hgj3r\pR[+T>8Ilp\eJ
?57INm*^?[r4rmG"FhN4ph<hn/Y0Qi02Tl=,qM?nJ$>74r6@$Xa-q)`SmG8*Z8NCt1D@;Qou
H>24LchYrN4NmI\b8qKSfK10u_%<@;AIsj/XYCN8c*:e`HcZ=^DdDIXnp4VLjV$nQeq!]Ad48
iH4RZ.-f;/l!0m.5c%g,2X/FXrqu;=?`]AWk7#qpfcCI<(X;)85:c`Gq%^o`t<BJNmADSO)+A
*lTf%!n&5bAgDeVoE7'*?Y(6tq^84[X,(T(Obt3*EPQ:$">Yc)f<G30*Pd0P$.q:gAhCG]A7<
FAdX>>6+QW#N-*IEgWJ`7kBc\3&b69/p68c\$W*38R1]A`Kpo+#V1"&\/ib]A/m%D/[hGJ"J-,
Ac]A9eXLj]A&6$,]A;`/B#^CqP_[p*8QJjtG4EIsL1p]AKdi-%T<e8s7l.p)X59uqnOM/Q::pI@6
RL1uXS)4eUZ-5kI't_LYHU:bW('Y.=N]Au60>\t_rfFm+W]AsT[Pb8c)f.g-qNataGQ*DKTP<W
QjCg>K`6>&QKOTtZUNF)<EEP%^$FdhRmj`aeud*Z>F.140S2<j$\giKdRf4D!^:d%mV*l7u7
OQgDnHFM4b02rq;n%3:LTd$c>1QgL]AqMXk`rHj>DFf*S/O>PW^RQh0Z??XCj/)#285H8!Xb@
T5F)^%e;hD&P6O)5n?`Q%=KmQ;*%s`)o-AD#_T3fk$-e^Cg:83j)pY]Al[uF.O*nJK&*:MX?<
PT%j9"G)_0HgoX8[*\st,UrIG[/jLPcK//Ra/?Ufb(Q:30C'usIrn=HZ:2.bK8W0%_0b?6r&
f=PuWEHi.`DM@n&MN7F.AaOn&^ZRHa"$pNlLWBfrW:IDN=f1FW8b([K+^J2o9kuq6_@\m(n3
JLE9i8MPd9EbN3qK1fAC_Dk*WSi+jh-W^6-kbp3(OB8n)D)5A]A*21?G3/VGo6V/9W/g>+Hj;
;)Jl$?Y3?lM)j@I9d^H^J,i4(rQPM<NO4oKAU+32XVP7'gb:L>8n[N6XrW9EP"B\A.4MJHbJ
&OP<_t_1m65<Ri!!"WVX0fi_(5q:)IJ6)CHR(d6p_)f\.;`PUMsS"+BE;E46>iFG.js:7KN5
E[n94cEbR&49=>%IO6BuB4W)uq>3r6),@TdDc^o/)_f>NdKeW*aY+;Z]A87c\\,%S>Lh_3k&B
B<CidGdW4Qa.l7QX5['29#fioBii(nOBOcrbUUL&h0JckJ8mptmYIm[b.4W#V;nEt]A9]AU)#O
-D@@^K_073.KVd)m.uTK&1pe=5(agEb+1kqb=;b/2&ENWp5e_NkZNlY$qVodsq^oNjki,'h4
#9AXBZa<U]A6ajF/``/JcYp;nVm)G:6W)@-/WX<M4fG_i09`t_5ggjF-<q9gj\4o?uF`kDQcU
uM%Dp:b_Rd)*4gUh%plb<D@QIs;45C:noFkY[&Jn")+UPOZfSqRs3Q-<VqigpYXD(2dV@LtN
\'EMc6:[-DO9?5u,S^X2@gp!B`NmSVlLggHK_Ilp6.EPhi[)XL#b@;ucTp<S&AM=jS6$X]Agp
HIVCGIK>V0&@XR<\o'%hW?9I8c]AK8C1H$r&jcN$n@bbAKh8S=X?F:Pea6eVZWsl.\IM&St>3
373;_Nl)^0STN/9F[j=)sdFYA:*oSehV_>SPK/c3>R1$KZ(?+A(jP@Sg2AMQ;Kp-#$ocd.N5
W?qV'P,YLWbb!Xf1Vc.bq>q2nVBXGZ$cJ3@"j,p=+AG;Z6aba.1mU\ipIYhX2b!*;DHeXnBZ
YX,r[1k,`9K1`7n=``Riq2hPFbDI@'bJ>u:#dW[aTO)qoMY]AL,Fc(3W0scu01g8bpE_'j^Ns
e'l=A0</=!;kn+O(!E::]ANkli&f(VMALP:M[c'KB"SOhci9*V[Zcj4dX%a-Hkn6/UFdmK;Pg
pl!Nt.)GH47XndkGYrN2=XPF=UL*DO#Q&X`b@JAn/-K@e0eBaNa2qmp#up,^KJLfb?$8@m;3
C2'rKc']AGdhf-F`fApRVI1em<XA7p1bUR&)!]Aoj6<Wij7H\:?q)j)N@s7NR)RaEdBp+!S_.=
cf+G=1GlU7iSSaYI8'5=,JIV]AH"'0U]A!/ZElFnjc/@mDj]A0[F;(E\C`JL+pT4N35J_)?Va4-
RhC&Zc9>g3>Hq5Rkt0C:d0,.0u=,o*Y>KjTpAbLgA,o;P$OT,cZ#?RZSutKYPA2T;_rf=$W%
Li3-Y#WkO3Ajs+?;\c22Gn]AK`Zpfb*-MnP+&_nsR[?YW;>5J;>M9,WMb1R;>-[/(jP@b@EXV
Mb?+=*3H8R<nb%b92'M<pmjYt5hBR7GoZ[>2cJW:Yo$X:C&o^!I/iQqE.tEjb@&ZCHC_M3&P
0C4FaNc0L?0?.(VHiBcBVK1<FQ1T]AWc3u22%naCFp<^0B;OA^$3:fEKl=^2"'\eK?nI=62R8
nP9Jo#((T@+4$1T[BIeGYJ#R$<[H[Z<QLm+_#';"6)0kuqf;%-b@YkqML"$NB9+R)"3%aco"
lh'%j$2Wk"69pSa]ATl?NsBDC8=r(qIEJr2l^o7Qj1k%A5_G3n![E*+>+<")?b7/D`u0S%(R*
YX5%WZ6ddY".GN>(UDnm1uakiQfPbGrOONo_(Gp^@bZb/=i`\%+4B4Ocrbl*G1*M>G6nJh=&
h7^1NZH8`"56"g28(,+0`[b"iiqAs?5-LUU5B[^j9`Jcum0j^E3"Eu?9mLj"DR=c_msOE5Y*
_?SjXhmIKg9-[^:c09U\Iun@GZTJN+f9;Pnt/(HXQ[^^9&sN0hf1IY3,XBcVKM7K__80^/55
#,1:o.rS<%PmeRHO#"D)')Np!E_LsjD(Ye5Bb+5#NKT2Pk,`e3+TWuT9pi(;'fQ`4V6b>9%n
G"YOF_ELaq<^PPa5!7:Fj[5]ARnGkq^VgFC90CPliZ(Eb3KWg_JF`F'Ss3Q76C<[jO'sL@6`q
E!k!"*t37O".m,2faRA')n5YnF=_pP"r9a'c-r`B#QO]AW>9>*W/X4Q?dbWkX\_AfWsk@\p>]A
Q_tZF/UPojdmBA`K'Ne;,$q9$P\^t7<=94naSoMS>d6=k\2i9jTq*=Wo]AJsg=VI8=[K[^n$J
Me4,go<.)C<&.f3CoVUC74.fBE"o!&$#']Aj9Mm5a"Iqf'B2b2HEG>Q#^g\(Ka,BMp=L\S2m)
9BLc($hsP73Dq:VskYe2#7A+-D0jkPX-j"%g9a$\3.&rQ4i7f*,8A>5'<@K<!Y%de"SpcoWO
5?*%Scd7p8NI?03s&$F%hh0g@Tt.$M=$/j[PQG?*5g:HV/+ck2?!!eC%^(X:VUqEhqXW3q'A
gTJ<E>"4WnG1b,#Mr_<9eo`[,:uI/)<\ljL\c.Vsl/3XJu8Iu^$\B/fE:2?<l&4FQcS5j.dY
HQQ;%YTjV>A63B0VWaXee0TF!/VI/s9i.t1U1(*d6CW_NIs"8c8T*)5FN&7YYpaO3/ZnG6\:
?DWL0&cS<an8:EY#b$<*jdQo\7'8]A6rG/NIFN7UY-obZcslC8En/1*+BkG?KUN`Ps/nPrVae
el8QWL31[8&0pj9aZ6EjfCG6e#NhN-.YY,XDa$B-Ih5Bo>nb-JkhQf)#\&>j,dH$!/<LRdtj
<2Y_ar9THg&bls0/TsnOeM26r1FdtG;W-!eF.j?:]A7C<Z7K!d-F;Sh;21h1McW\.Agr:]A-R5
E==Ek)G:Lua1GG0P2RPPK2.9Y!8q//7F@I^NqG/'k`eboqmdXn@M[+'G;^anT2#-@T9NPcnN
;@*Ie`Jf.gm2]A''H==b!kuO$@73U!e;CoUdE/YECU>kmUl\<SU#^3\m"43+sD/ZLHk=nmqSt
iOWRnoW6"4_`s'eIPqD6:(,i8#pAhI3V,Ts>sQ9@YI[/BmGBKR<?#p!&e`'f/8hN?U0,CD?9
:K6/'XgMQRQQ@)P]A$>Z[FjL/\kcGI^Tl^g4EI`SkC-U;ArZb==q`[sO&qq4F8J_Vf`PN'TK+
JlTcqQ]A`sAJhBDDIT6ck'QT6$LH5\Pt&D7]A0CpV*hIZKb^"`FSqOW_4CC\&VGUYn'(:n<73i
scXO#n5%Gi/V=Hqh,mhRZZBaj(3*OYnEF1&gq*rJaqOfrjC,?KYke"VD1)=Fgo%9;9.BJDo3
AYAK>]A)oI-@qe4VV)4h)A8Zoeh\LYd7CbN;<+(ML*nLF3!Rj_1O0E!KJG3:"*Rs$>ePb<\cM
Okn]AO=^qGAgeT*\L.UJWT,3[F9mc^7$9XHlqcth1FMdWfb@md[]A?NrPnH&J9tureu`+/fS7)
MP^8<bXW4nbG`10qH7Z,2XVFl-*1.-kasS_.Xe+g<2tZ!Epsrk]Ap@bhsQN"F]Ae,1!_CNJK`L
B6a>NZ0-hIR@VRF^l?+-Yt^OZFUoJP*&&D49`IH0V$h+ZQH-[fW-PXaa6;t`_DrEWZcak=.D
4.1@G@tR,C<JpFWpe6AG1gfp!M>^8J=-WQhlsgf\P>@,*]AUcqIV57-T`fKZS9+1SD>ZZKt:M
L0(J)!r)<F0m-oID*Hp?/et\m+'^ctED78RS0q`gni509FT8cO,>TK&hj4*f!@miAN,,c5Bh
Msh[C&1!A`?j'QB.29L9=L)rfUY($-p65rj;d!\cm_3Vjphakm1(2gG(pILPe9DpnYgeit+\
g,\TS[Y30>60r*&I=q.Hq_\q"tk&2H="gh\7:V5CCl[>\R?K\/JrFiU>4%WYp'D@hffY'eAS
]AK<mcii0khr+ETLW/'/ZUgCj's?_#%$d:&DVXmC]AN6%bc:^14P-U0(9?e'Xg:Wuq4%Do-j,1
$]AhA+B7WOW[,2-2A\eN"('f9b$1^$4@.cX^'PGS+s?hSNo3^'S#)U_Z/UQ40@9KP;,HOj-^j
CCD.h]A&pkPdpArGX_0iP2P4ThdMg]A=E'm6In(C6JM6XX8K3J1j)T0]A0c"gk&4Co/$(/HGWMh
N?,9OAcIRgl'V@9?*<?)V^S,$T_$/O?_%\*dH>!hJ2%I5qjJMS5`+U0@n/AoE$4bMPXg/u"^
JnP^s/ITj9opjHU_Y7k`r9+0lEB(>Fm-ADGXj<X/lAL(fc]A^Q1!VY^29p7tg16VNkpg)\->n
/fKDhAV90Z231oLjXgV6<448A@7&4$2!mRr-]Ab&I\8b<hS`EmQmkuq8H;qQqNP'd1mH<("g^
mDKU'ONh&07oRJt?Al$n--(uG-9;FlcHeL_(3^djaC8b-/HUj*<+bF1.:8&BK#1JH1E.PkWs
8eF#1^SKaGh)"LtD`d)X@[Ua+X>]A:OhfI]AJ<eN(G*YIe8f+a2%K(;,PKC/Eh@9g]AK2:lL7Vr
:&(lS;3#HDV\RrEAbjYl*>pk;u-C^\c*Z)!sO$[S9bOVrlEU\Rs(eakC-Y+dSmI=+f%0aX,Z
iaeOr1JRQkJ%Q)*!dX3ck\..ZVV(/g@gUX=>VkjWY%I\k`)jj964.RaT?mR,&qt!2`K^)VrO
j<^O)56GQr]AhT<"aD-Ha/F9'C;>M^TH,hlk(`+,@tKg]Apl+iq331qS3L;Ri_jBrm]A8;lq8;0
':d*Z&+0YP2HQ?[bbA,=QAp=^kHc:N&s*FFWmV!8H6d.b6AIsW/g?D-K#+rakX^k:hM3kML]A
=*C9]A402%46Qg@C2=n8k9^E4--:K?5]AS'4=+TX>,4l*3-&F$2]As,qhLq/:+9W<JW%B`VXB.L
C4q(S,Vmb*D_JP"1,o41gpSF05(tf;/,5N8Q.H8MV\lp94FnLI+s;s"Z\8@*%Und@3gu5*eu
OH70eT*3$;$N8debjOHl8UYq'6/8i7aBPr7$#J4cYVOtcU;S@NLd*XT)<W6lj@cKN.Rf2ic3
pR\:[7FlG%\um;+[,5jJnk%(4F.]A#Z1Fn:9Spo5m!f]A?rXZOLFt2(c^WqT"2^8UF57a.5,08
''Q1VP'E3-,<PiI,&RK"CTEX]An[*t1U<kig*;8J_?L;cc_uqt(Dpju_j?$)Cb'rV*up>p]A5b
"`6W$@K#.,)lf1;/5.a(+oET9)AO%Gf'8`)U#=K`Jk[$#<Su8:2n4C2kPf+4[*P=mAkLOP[E
UVIC6c9q@l(b&]A$OFNRqAK:rSP%b*I5n@A,[n4heiCEj`>&Z*h*1#cg/%S'%<gap""QR5OC<
7Kf%U8(+R@R%'0b;OhtB>a.,;CN<uJ=/0qq5>^_igGust"ZDHDMppEsj+mQ]A$Hn3e(%sK`6\
7:N@^HbV#Hf^H=H?tS&XFf1\0QU*@Lg,0)fBS=C%bc>;`RL*a!VC/iA[a60b+4"`Zk"aWFR5
e:^:c./golb?<"iAV&'tJYTk236-]Am?$V_t?66="r*`sOZ0YLC![Slhb>`c10jR5L([+#WhT
T\N3IK.IHI+L_\l<7Gn#plR["jC;9RVU4f`.PR4/YGcaS8C;,"ioM!.^hK?K,`<55g!@P5-p
T<-^u12@X+d))fC&h!JsCV/5a2u*8`ZmF=M8&pqisoiV140%G5kYfd?:8Pj]AhC1`ON\Ee8Z:
e,M("Z.99c/kF[G=kWJUEo>V9)`lgO"@LZ-(Mo]Apro;qPGk6mFfO1U/Y.b\+S3VYM^/OZ0q7
sCoD(Aq$I@7=AIM8Qs;n3U;rS)l).rM?R*a,&p3"Dp.Ji=\c6l?f8--'UUUACP&ArN[`h'qh
TARc;ci[_l,r8W#@TIP/dE-n]A@'Y]A6,X?I!et+MJ)[%>BpN]A/a2n$\<sYe]APfN;Pj8X\`<Y$
lpTcMi:Qpr`$%"b1gg[jE`Q$Dm;a^TW"4;mHL"n+/Sl)AN![Ii[<%GKm++2\_P`?YR6:G<*g
7MOl*lmnNrt*("RN`5G"P0MI]AO=J9?%HuR2/9*BhEq?'qjup&PPT_B9gZMKh*sW"f[FF0Y'N
>NeQb.Sa&PdU0LpjXP2:-ms+9WNj:Nrk#<s_E-@)12@T!qB@V5JT(E*Xj4IR2!'ppm2_FJ@S
/,/.+NoDWGD9koi3P<_M48d:3C+(ZF#(9%+9uj4-(ffO_F0+k+rQD-3c]A*)52T;(WBnQG^9E
Ui.b?b3i7$s^hT;Mm<bY?RiL@Bch!ad22<H-H?Mr;-9=>gr\+c!464.bTdofc%]AqH%#:G8Lb
?uZN5'@!;h=bXCC?qV\L2070rB#MDDq?k846-?Oh_$q1BT=rC8NpXUMM]AGVQ#K4.;o<n7.R)
UXmMW':X1O]Ah@[jj;o%"i<1!2F,i<#K_l8q5QgIo_q]A6\1G2B!%R=0KnFSQ%D6Es2OW^[hJl
Am*SUOhTIH[3KLY,9[DRg*Rcq%htj:/]AZK\XpBMpYY.1oR[VDY/dEmC!W*?)QOU)9>.W<l6\
>QV9iY<JPlB([W35odi0DTrO,g2A,hQ4tA&1&.V?DfC7O)S6(bM5*rCV78H6>EC?KCNIt/#q
@#'7'HP21%N\!37W6%QUg0`0'hWP%l-.A@?R/"lDB4ia13Y;*e9"cTTUd_Q$kh`g+;?W)GOi
DImuWBn=HW!LdL[lHr7(7&>3+i-[uO"rB@mZ;b3/ioVc_S:-83Xr3Dj3$m=4G?B%Ta6PnqcG
`so=Q%9^?o=:'G+u$:qpO>&G9H>[?<i]An9NJ5R;+\VL5F,D\1!9.hNO<KF;M\K6-)rgJ:M`r
'HF`if%^,6$%r3R2p(EAs_8a<Q!lLa#I\i1k>0dB-;DH"@JQ)PV(.$208]Ah>K[9T:[JaL*0.
E"kA8EC&flO+RNb&ja/:ZLljGouC'!nN5,!pPJpP>T8*V7\#/h`\$k0If:A)n$aE'?"m/Vq3
=,q@Gf`L;)3m`%nYG;4@]AkEuqB!33+l.hU'9GO`F)$HM[Jg,"`!&N,R=5Q_Uu3!8F)YA9mnb
%b'Pq<;L`[+8$e&0H*&(Z@7HVPb-^38Q]Al0HI@lfWP(lp>LpUS^;Z3s"X-ZVmT-GE:(=L7A^
smse]AQSZNZJbmP3);CO3]A#hDsU3ei#4,?a`a^*.`&$RCfk,K+Ncp<c$&]A//e:-!B"N/QXQuO
J!-9phZ&%GWgM\\n.P2o+NE35R)!B\5iD$q0XOIo9.'j0!4JM\86qtSW<sW-%iJM*2G'4AiO
uRpY;<PkgSl57Jf>.6pYH'UW?GE7bFB[DI7G)"A8L$@@]A'[hCc+M'Zc*Hh[:a;9741JV"EdJ
fBH?VWMS+@,RDq_I"McpKKKBD/Pd_Ptf^hN&%di-^SALO6%#-0kChb5AgCoG72=9s2#\3A\=
hY/]At_8]ASKf?VcEXi=DT7-qlP6-Hsg,W?_"TkIpe(/^U-Q"%C<=C8jHE4$8M9K]A3=2Ap@qa<
6Y#Ji7Ai%JI]AICq_=WNpp$CfXYDqY3RJK&BkM4gLMhJDn&/\XEIUE<Qg,1h0qjj0F*u)fVQp
?r6a0`/A.(53&UO`1I2kAeH+4H)4'-a@iOWsO^rQ?3FEO3E#(k3WO1sTJr]As_3&Q0&(n"gH"
WR'71UleK`5D^dqdVDumW2?6VpA9r`4_T7%a,A?_ikn)EGl\=dmF@b$@W`685jYS.-c[Agtc
muK;1Q$jni#U!67>\enMZGT&TVmh_H#ZL*]A`IO+?L9!VBH/4NRAb)<[bf6Z)r)Kr=;Onod"F
%fs"laj%n:]AO0P86Y-B+asBFA9NFTs%KCM$_saDHfX=l1b"6lC(hLsS+]A3$nF3294A6=VuF7
pDd$SU&0SK#Z9B2=Z]A%T)BW,sTd(,:b3UbsL?L<_Rj6J-R.=ja&`FO[Ol#CQi;eNh=5d-QWa
R#O%,5KjM/d/*ec<h!*U'Pb<CcrR)=7JP+^N.IcSVl(MI8k;,_0ql#J"r]A^gO=784?p$VhJN
M*#gg^=EQ8<ir1<JWUW&rGI/7eK=D,K]ARj&J""Wk[.^J&G1H?ZsO;Kq>UJ5rSJ+)j/-K<\Us
T`\H3-<RA@AT?0u$;DQYiDg#C74bU1_`glQ5TTs<kFU"0W5[QYM4I'm5-3rGN;*t,YWPk(c4
phXrhr'kjC\Ec$&5=i`2J4VPP)T'8"\;b+>U&D@PM*DgXceN1,nW53s59D8*S>1.fbd$E<c?
CI5,8nVEdkSGoZ</b+KZ$Ul#[^KPe8#Hj]AaNW,%U/7fKGA&)8aGI4*;pjo(QYE?:#%%kT2\"
CI%Z(/bJMN(*/=b<HuLEH\:CA&b?(Mghfn)&E_fC+WEM5S&qMjZ9q0:lDWFMm`*-11%g>%bq
N,Wk%`Kqu?OSsqWk[KRkHQHsqK@;CGBn`Sk+Bo??5*?B#%?UH>sSj+SXod(J#`)a:j+=dMF,
hNUl9h:rN8/U4Q29\j)H_q3m\gpKXM;T[qn/DiWJsRpL%^O1F_^7PZIbRL!u^\o/^r/Vck\t
J&]AmHmHd.^6#T)^#/%]A"j)i.(K>c&W4]AYg;SkEuVki=<hBIDji)3Bm>kND/-1:ggcJM#Fp1\
X*?mVtK+69\6rb*"C1XW$qHCFRB,[\#?*R*MZp'LKX2@&nM.)*96!;t#=Hh6e^b8\!nt*UB1
k:Jc)&@cj,^)@,[R,tV9uG"GI>B]AjXjIkGT"&k3P:SU4j-;!rA@c8Nh=UqmF,8TlWqPt`SWi
&:,2:(I!kg"6K13[mqSTaFlT_Vf2R+t&d"O^=;1_h))4?H[)C@!@H)P;u$T-;!$c+Gen74i%
\lG]A_D4g;8`B9mSo,BX)rl8frbj"h$8&q$N'<#3g^j%9N"9%b1K.^MTes:Do=9Zr:KUVE8/*
CuMXA`QCK%/@Q,;M3q7[jAP93Zk>%F7J`B%"R^*P1O0H.*W34D1`deI#'u;_D^s-gD*#`!!`
R``HYgfoY?,H8>>*[#rs*f)\.2k<)1u-pS4I%^33[[UJPm8\B\>)r?':qOH!TJgp3_"!qVXL
,0b*<ceYW>pNi;O.:mmSASC\rNHbJ_>J6H8JAXO'H60Q2Qn'uOA80u6HZCB?j[ZufpjDC,GD
7O!OGT$*mHShBFAs';tF*j%^go]A<d!Akc;TA16u1s"55PH>Eg(mad_<!B!,DQ'nVk@/`%!Ue
J3a%p'SB#c9r\LC^q8?9X@$(H"VD>4KEFtC3?pgel?/>+AepXLMEL>n@hcuENNYgRRjg*m[`
J0W$I7kII@+u+MEaii]A&f+=di\>WLK-J;@Z^74!!EgGEdNe2C=6#e,%[[ZkKB`Udh@m`5_C:
W6"FQJ+21J*p^eOsq5,+C]AM@s7O>YYPg-CgYJ?'>aQeRNqFT*T:Y)BW(Q;*_'+u95a260nbk
Wr2'3r=P`[:0`BZ@<A?&XIOs7.dp!nkKdeL<S?r;C)O$pUj/4d))HZ?b/PcmiV[+r:euW<R@
B45cQf_!DU2s5>bK[EE`1Z/0dkj7%PZh>Dr%]A:*d\TE?#kTM2m/tYhc74t.&U[Pl+#qF6cnb
UG'%/JCs)-3_G\O#&37K_5%@1OqHQiPhf)_FLS:hcnHV^LjNTVeVI0`KA2oJ@uRQNG;*7$lm
*(aj*P1?"p!2t.tCV."8n7oW5XCS%%j=#,/F(r3U?p]A.=JpPB'FD:]Asfa@G.`%u&blW'QAeS
.>'Gd5F-TO6'eDW[V_=5`[ST1^A+/E5>Y>Yd@>>>[Uf#SobA-6O3cG,uIPmS45h`7(*c@#)!
lh993l,dPa^Pc^n>I']A_uLI<\EaFdP1SPW>KDrT3j>b$+(TP\[ua.2&-]Ag*tN^&$HuGJlXg=
d(s:4ip$M8br7(2.**s[a,`IHO'Z;<VhE82SkI_./q>^gZruOo6Yje%Xtcn<"2*'kX%A]Aq&&
V=[tEHk8SE!K?;t;OWUMZ"Y3lR@+!:/n4Tu&o>;Z*h_(<.$`\@GFBSpL)2LE1D/'U&&^=JO5
AUc)A(&Z1s%HcW]A$(ImCVc9%Jh)quen0r>m]AqZ*KdA:1g@50"!'7ca5+6h=HFr\)g;g945'#
riO@AeC]A!gH`Ro5OL0"ha-0\<05V-/RJf82j/k8;$K`^GCQT"uea#4f0nJ68s?)BtNFl=T@T
ee=`nI1EmY9_gNX_B8:fe-.=iia?6lR\u&QPqa2hEPt*8H)<)aA1mn+kQ$sVG2PQ7g,kV]A>/
pRe+=]ABd\8d("!qkMh9mo^3MO8S<F8\Kg92`ZQ?9OJ]A*&P@lDPfGQ5^P0RPoO:jE2TQ#(Ci7
?<lS03/jp',H+b2#?@mpB3CP?':Z.e[&UO>1o*3l=<iP:c+0sN>d$`Q=XW>9'=kPSV]A4EnX<
`'[U`Y2[Da08#k$+X!p'eo_6WF)A/,da:OAEKPD_\[:uU9XH]A.5C]ABA'RQ<IqW'U;F4iFHe2
!s;R6pngk4]AOJHh%MCpIFH`ZUVLu7gF%&O?1De-Wq$Ed`fUpONVfO=)4%)X4%11WYL)Wfr50
G2`Io"*nD+&E%Lm=N&?b)nnAndjil><(RVA-CJ<MHGc!`qWV4=HDIa(9F(SVm;f1rE*Us,>\
+QGJ_K3f/Qt,m3Ej6Nfjk[DL<Vh'VC2KhPM,W,RFS%D5l3Z9kaJ2rie\4M4)h.1Gbe)/U#!Z
[6LT`G+TJ>H&Pbar]Af6$Q7V]A9#1rV1n>HbLVB5[Tr'cGnp#LQ(!;QnINce`cDCTD>-]AW=YKG
'5@;4%EVVq[mb:ckXL/5m>_VCZKci*p9trd;_I+qp#eesC[XK*Z*%A9F+;H8\1H;96G1_aY_
&E7mA_'PI!@m'<e]A,[&D[W4L.3Vo[BeaD.t4FF:<9t8s7k16n7%8)$]Aa@eqlR.0%OTJT2%.V
N7S67FU1(<A7]A;f%C\/@]A#>L_,d:qCaI-uipE&fdpDWuN"7lNif/!02Zi%6[r^eBUm?>O0jP
g$T9a\S;!JeWE!-nt'a*[H@5VMUk]AS]Ar"<H=Du[&^H/;q$ifV=6]A-5>AUe?iqsu0jFZ/"<k3
#JS3&hJQ3ZkD]Ak+iLqVTs=9k3Qq),D\]AiNbDU1Pj/01pA.fnlLob/aTjHJCW?G^Q!N!$t'Rn
Z$tB?\bVDqI>A.i-ddW.4&)4;fYJP:,B\?SNIBKI0S4u.UR0eHC0sG?TVFsK0\_iJHKUl?7Y
NIL$=ng&l-\n(0sTY'@_uibao%a=&TcZp:r`<fd@FK">?UU7eXOYDDEaE^OAF37RqCUUU<[[
(l?/Q23@p0sUSB1sE36XX-?;o'*qTGEgVBY!n[$%C\b>Mc7`T&Y,5fEbD,p">;>85"XQ%rSR
`@dU`;3tVR3X?KQ_kY2;?D^N8DA%2PpK&4+V#-#Utm^VA:(2srM$>a[o\.4WH'Z6!G1mF_[(
:75Spa&iF@WGg.\3gEd?qN>afr0(9W+9-8q%*.n%hu5*Hqp<31T2N@(I#p-X`G/h?p`^`\8L
=t#!9m4$dmp-/Or`o#KJd-Q:>b8Dr0B9%J'M/*5-7P;[I`oH6b$gXkpXQs(N._lCKA9u?no+
t_V`QW%lZ4&t_hKrJ+KauI-guPs/L=oo!Kgf=>TIhk$o.nP&*`8;s03DThF%4OH-/T_alOp1
<FRQ^NImVP?CjdGI'D#jYCsP4ncHLL'c!e^c[0"i`=DV;VdLo/ZiEX.GqMH,<]A8f9)L_]AImj
)M5I6KL9p7k'Y[O+@.$PlLd7I?FPY2%ipdJ&'mI!Rh"t@0h,\+4YT&n_Ql1+n?!50rjH=o/d
@2Yj^S&?hs:7#S$Tor8t\\a$?aaX[<RN!OPg-ZE&Hf;ej+_XrHm1P">SYM`G36l=/-]AWWCHG
:>4kQlJ/n99T4phoY%lORAYhK?Y>f9/Yi+tg*$#e>i3M]AjU]AT^PO2k*;!**g>V?g1O8N!TTB
SGaM/sLIkit.<DQ2DTonQX`!Ot,%2m?HULur9UJlJE9=Jc26Y4SnW">tDM[0BJ$),t_tb;9H
j(u<j\6,`EsYEiYkZD)X;S4<UZH(%p)Ti[9Q6(b=q8HQ45gY*LC"1-:?C<?O_[uc5>]Aitr^%
=W/^h0&ru/fdl)+ntB+C5O`%LjGMlX27U.a0Yt05bb,UM[L4-f'Ce\Fo3e<d3Ls9OtF`[oj#
AI^LBd>hc>\X7j.l_O-cGgbDR4E<7;I6U;*_=>2\D]ANIigT<.r%iI>fTQek>Z[C'Nk<DkZ(`
C*oAEi52)ZeQ^+'*LRm<6"%Y)`]A@^s5,lZm?%o/afPZe?oqS^TRNU62>O0=b`H?NR>KHjN9/
`m;a$`ucM&M7@oHOa)K,-j=3e6_DG[HWe=oZ:u/!=6l/pN]AtXkFsROkpH&7`-"aSs*nR$^h>
0ZnE/@C>V)Fg9fs`W#q^NgqT8bf6=L$NMbed@oG%*pB.`sI2h@Nda=a/eETf?Efa^b6V`MFE
V76XG-"c080@20Gu(Ml^@MeAPP_QCJ/*'SqL*eNG.P7oSTk%G@^4kA)Hc:,.*JjoSAKp56dE
6ai44fE`C9Uu>EtGA1'VnN9ga1lmdS2A:j4+Ss406@OV2JqF;e;C3>X"1"uQ^3iG`QW'L68o
8]AT]AV4R`4GnLZ`*#-d5ZWEAYEbm&j)-mW>AQXFd3Gc:u?XsoP[gK;o?#uWf%qOtV.g&[>EPY
qlEPsmncErSTXp_s'ZYK9%ifeZ,JRnRh63-UcoQb;Ieid9J:7rY1lR&U`j2;2H(7XW,U=<jp
[GFqb]A^<0=@`Sc7Dpb-(_!7]A+BMJ)cl<5`%]ADKK5i'0RQ(X$9sckrOEc,T&3-IX\@sKX#por
h-(A;)s]AJ;WFhJYD%qB(Jq?tg-bLKIe,*leK=K^b$0+u'$j38a@fM?gfZ!t_[232(Vr(k2`@
%.7`gp+<lIV12Fek+DqJ:V7iVWVk[9Si[m*+c$dp)Q=!4bRaMu1>@AJr&W^:AmRcNbHIgX:&
8HHRQq,R)Fj3ZY@=Pq;-<<5!f5jUC!P[7#Y)?9BU;+"Bbgq<huc@aGb(Q-F@&.6m;9U$Z\Vo
.k,YGY'E"I^89>OSL.c)-KJeeAOpN`t(_HumE#F&+aP"seea+]A1KF!+X]ALREb:c@RgAp3%\+
W+>QY7Zg`1)b1PE%dDp7\DYCI-1:]AoipOLHa<>,,4;lVL;YJ+%N1l8WdCqkA[MkB)[@IU!:D
MuUQN'*$k`7YasgQZ219p)lTNm5I(>8%rJ"o,ZC!ZH[2kO@;F.DOqqhR;u<.P5sO2P<>po6M
>!$&.OjOF$41Qfd5cmeY$`[e<BfS;?FTq-EahDPX4t"pK`bPqX)<%T9d_T6FO,/jl\@/nQuY
`H-@bmc4pCfL_f@*&B>@e&kK^b*=S4DM]AIWYVeR\h;peOrD>Z?gpSINN6JM+)isUs+R>2[(T
4*r&5>6U.`E4?eTss3)Ah*T-[Y1&Xt/iced$#2/^#gC"Lc(rGM*[H-=gL43EBq^R5`<c.jC?
M!s`NDge'[QKl4+ofQXlW_!?>hg!j>WgpN,/Iu<=!<&6%Z00eO[;epRT]Aq@cEe-EkF1:&>GJ
\+!i^73)3j8)7Q$4:C)h#%^AHS-Q3.>/0@?aL?Sm"sr/kU?R6$%*4G<=b),=H2fl=u=YMQ)l
nh7igJbMYLq/<!fG+CH%Vr;sO0[c0&1T+*nL5*-''k3O8i(]A+7+?@Fp`Mg"=nj+Y#W"dm&Gb
6O\E[.&u/UGXNb7%?P8>a*?k;frrrTYY^ucmGsZ.PFJmBEBL?GRMrpg*3B@]A6X<mr&:DBh9_
%A82$KJZ>T\77@\a!"Skl+"gSB)D04U=Q0tq9o@_us%-bj-bP01$3!2_:8aB`#LX1=pr(-R*
&('8,@q?pA[LQf:FM+uCb0<%`kH#h7_%@^3B8VaHVB12m!,0heoQi"gh=hJUm`+$@j"E92Ya
?%(Q*fn.URPO&T/Q#Yn7]Aocf7[ee&PG^G%h\^6B4:;DdR`U3?$*$-JWA'i9F2Dfq+lBf/7]A+
'1io$Di?df(nS@Q^WLGpPaCg/*m>L#dRc9`]AIN&[KIi?E1oUk^17Yj$MoO5?6t.\Z&+s%;$#
]AP]AMfpU_b/LNMO+,L9,;!g*CtY^3R5]AQdZ?]AA8EU&u4Js3u/forG")25"i),dp.62(Zddl*2
Xpl)EcXBJ7n(Uqo9ajql=ZoM-!DUpgTqsg=5Yb<n`d]A<iE>oU;U\jaetDESPaqg[Q.aXf/;5
?dSqlI31BI>+0HrCd&d`A?>II4WG)hfU39nZ15uDIgr&sS=_,kG59^.k)LmYNd#;h%>4<_9*
R2ZQqY5oo6N7cugVBWNpU+,ibg7KfHAMY&`NUhnaV'M9D1%,?7/s8YRMUFJZi&=_#GtFGE%N
r4+I]A7mS!SM`<932'E5:%edrRc5$>&=mq6&;W\ogH>\]AXPN"8,X>o_7*eFF)EFh1`SE*d\[5
kC_p_+Pqe:43eW9"t]Ahg7'ndOh`tgCGl4o"Z[R.i)Qg$KO#`+V^hCY"K>=(C$)Lm$jd$\.bK
GM\./,mH9R\Ktg)BP\`h<[<d'>*.4-Z=BPY:rB]A9q7cDtMSW/"iS6+JDFp7a$.,m^#B\';*q
Vk3-B$,&G+9GN\%VM!N*NiV=W8Bb*@NYQYe3]Aje5EU,?;t'Hd(eF%kjc+&L]A_bqqg'#APdUf
(M_pb`ufCj)eMJ9-EiI:n(dT37cZ/(b+&\<9\r!Od0?,#.__$S7sn6_Id:*k:-^lUj]A>tO[0
d:rj-6eQb^/\aZ0S;XYA$1Z`HG.7FI2XX8?iH?ho;+dp52/F-%l+WfP`o'W6PQ1$?=.UVn[o
m)g#iefPehX6.ss[.cRYY!dKT\ig5ESV&q`aWa2bl<Rhom`3g6MS.B3XF!fthA*Zq.<cS^%*
i2!ZA)`u+6mha5s`N1=02/5adl\HDIW[0]A!l*c'OjOpe!%<oF)<_]A^mFK2Y3ee]A_K8SZ@H>e
dCX4nrAK>#s.%D-p`N:U]A/c'CQ#IDNq2LoB`Nt"`S-$<=Pl&ccm&6n,K0kk&\7c#b=?rLII_
^6VZ``A)LlC#;ki5j7&p<BKHo@OPcB'#.r#L9q7)CP5HaD?QL@n7AsH)uJ,Gf<nflNtp<B#Q
[_7/u]A#Ch4;C&,[Pd[dHG@G7+5H;*n8G@0>Fc=;K3p38+4/aqQlh%2O4Ojt[[m.9u2n/OV$e
QqM6.h=RAUNqd_J8Q1:l\]A&X@"EP;:(K75;`irA*MO1VfUB&Sud'@gb+E$CXC>IFKTIF.X1Q
>?2h#sr>BaY1#`P\,_`e^UhQb+.<ZH$s"a7nu#@39I.>.'cE2A)H7[eT*("XE*9=4Sjn%acj
T>R%qI0uQDfn<K<f-9f&LJba/pX*=ZQ*/M`t`ATSd$25srZ.=Z1XIXO*]A)+0OX6`L!L>E/kN
'D6[5`[!.'T^.A;,%iVi`sPe'9i>c=1!TaH:)nEMnRR:.M1Y`P>Y3=YV)fbNAJ,BW(7E/`aM
L0Ko"Ld<NjNkh8UO\8+[^_C5b>=qW<_)p:idr@O_A6-(@T)&`h`97K4"4;/\>#i_clbI%euO
;68<B:#X/>p['+Z4'1`W_k3f2]AWSN\@[8&)k2-?.@:.C`g7l(Y@iDdm<NEVt1$uj6Kao^J^B
u&gW(p_EB8XC0\%?Tfe*C<(FnBB+1i_P^ES\XlhO,.+c1hoIjL?%J]AhLFok?a\8^Mn]Aa.`tE
2<I1d/E4k5dmjjB_?4@D&'ADej5"inqMFJ^m#/"m`VPh=LhJFk,ck=1Tn9O/*-t38F5B83BK
8\bLkfoM[mO6tcqp-Jq_'YPDJJA2E/4,RtiaZV]A+<gJ^SXKImp`c\]A"l`G?I!b]ADTW&(^?KB
qtAng66)@PU;Dq/FoK@/IpB\5G2Q5Q0VN%>arIf`\(`m*u`)A=Z9_Hg[OW3></YYA,BB$<QW
DrVf,HMH_[q<m,'D[OchBnIpXQb2QBf4DN-?P2SHd9IY$Zl;-BX$NpCNE"V7@;BIRo)_7H6)
!FlS#b"oc.Ooi%bD63%I:[2L"@R%N"$1(%*:u5OBMJOqqC9o;m5/J<uRe7I]A\\);0OYS=C.T
6JEaMDY5G^KRFP:_(G?sZD^"/?r"Y0(@r>'lgu\NA)5gg1:[s9C\\F@'rtmY5VR$13'"^.8p
A\/'=dhtBTApjd!?_#47GIekV/:obP4Ge7j?AMqr/Bt2r_m/&6R-:,IjLna[TA9!:AF9'ci:
>as+A7Yrugn'mpekaDB;6n@aZ2"&FT"RpF)7&$q8\*_djDbs$[F<bT2Q\RW=%>s"tM^s*:B+
p'EK]A'+^CgBU_1/RHC)4nd>"7Q2TH4-m]A0d-UH?k:Z8Y*T*1kR<^4kj/G]A*QqOl/X#A%><SB
Qhi*:SZsMYVu.r:V`^Gd39,ae%,HXR6Y'*t(q6"nEC7C4Wu[)0AeC\CmcbpigRrY9hC+/>-k
m(u4Y[_Vo5sKrUn*rbm-@c9<F6$=Ohq89+')Nu&Hmi*:6*6&B*>FUK>cErj/27pTO0j!9u3)
NA*p<4K)rin7q22LTNd0Y;3r[*s"u[WaSX<^?WJ=]AB/k<8.Mui[J4'f->HVSt2n!DG#Mom!<
E;2P<VVP4)7Zfi>YrgmJE=o,NG#!]A!Ytj"4+E[/nIIh;uq8b"8KX!Z>g,"n"=_3"X?1B+`L-
)=M2[fXe98(h9iB9p\DVi:6u-UaXq,X"A-?^Wq>P&6Gr4jnWhM(LJDuT9B3ikHg^_mre,Ve*
DTfp=Cn$@(]A:)JBALLK1i:Ob@=rIH0;:L"^b`;c+6:>OkhVTX;m73fhD#jguQsMBWi,05Xf[
m/'?Ehi3!?ETG"a2Y\u\C&l"nNRN5M"=/X>P$sU#>l_Ic:&F5=dI&D.##Q_=QG[L6TT\nK:#
WQKYd>%>H=UU!N$aGQn#@f9R"GQgiHeV7e)C)_N\`j$&:_)U,LL(Z&$g&o9D=Zd4:u,6d+e%
=r"ke0<,>tqeEUO'PEDjcM$L[Ib]A:DA"Y/54+gC.49[TKlk?:QWj^XV9c%V$QKMLV7`28X).
""FND?j@.GNh<Js.DDD]An[dhg>5p=WhK<[%9e((iXVOLkRaSV+Ep(7uC10QQ]AF04%GAEML,?
tnk?$?s]AK&f8s>KninJ.-Ps(61B0mZb@TI/4t1Q+j5;W-?rCAWle7(B7fS6Qss]ATqTN.Ma9@
>4f=q3Hq/2i]AiW"8`@kfpX'iARpmVY8kI7NdOTE$M'jH/NW-\)dU60QffP9kldAdB`Rddr[F
($lI%mb#bpE``N-%g!$INpQW^sr2co?8NVD>gZI/o7Yn;+gA3YX:EbBr;7:368/?I@h@D-9;
8VJ2Jo1DBR8(8b7*t9gbl]A_pJ+J8f2S)P7K9n+N#enNPsD#9a]Af;Lmi0\prC(_h#cc[5"$ej
#l=BMYEj*$<-2K9G0B0X:_XGDY0^oLa?'5\EFqn^27\[7.d+]A_"t'T(f@##,N$pk*Y:-KH"g
"^&X8dF$LVr!b6\]ANa<rIiZ)K"!m(-V,qYG-$rP.5oelce,+>7:tD?WE9+.R1d-WOMkdm?*'
;c8j3-c3QZ4,!r6F_mRKI>:N(':lg`?<CJ.Pl"(!kNdnE@T5]AVui:^.ge'/.)pu0CAH+RUKZ
hD-qM]An0$WFQk@Dkoff#'2+$s44^YN.e5T9e[f$$[3'[eJ8).hu&mlqL@:V&deSnR=)cm_a*
lcri[A]AnA"<EMAIL>]A[EV[9`H^An/Mo[h<L@j3&ilGQV'tp9>G]AE1_XQk%f8GiehCQ;oT);s
d6E)59T=p%OCsDCAaf@nmK!;(hK`4e1/Cb9Mu=12Di$&#?7puZ;72mP.Y[k(j5&'t/`^flNt
J+2IdBZ.jX7M)elN(trH[)q.-6q^+%]AKlITAel=?L\ZMTYe4mjWX>E"DscqJu=us1+3Z7@Rc
d6P&,bq``!8`d>2L!*!#:s*,\Q%g_f2KcJTr-#LLc&FKEZh:lf7Cn4!RRBZZ0T5tQR6C_VhO
_OPf]AOmIfdWQu>M_%d/as7'9TSQ11SVF\T,;,;-k,s?q.<,fmV5FT]AYbgn3,3^AIUIfHqD6q
$kBPmKJ+9L<*"B4+4!:22t:4&%bdQKf*4\+:D;Cj7kENGOcoI]AVT-XM8+413XkGR$-.+3!n+
oUdrq\#a]A2NgeV2\G*%EVcc<u*#nM4$cLYi=tZ['(2c%&\Xe&X@@PQ\\HOCU,?([CWaQP_Bd
;3=SrZg=%B"Eg0M_2-/dHjkd)J*N?kb<OJ]AT:>]A+^3\<EKZJA2!DICiiYk`ENHngnklM$-F=
56&n1241;.Q<l%r08G%tZO+@kF5^V8n_?o`ehSS3rEql(Oe51Uh)E0J4I0:[,l0f<u:pI9I<
Ldu1M*qVSXMhClNL+@C\jJed'7.g7dDU4]Aa\el\=qRS.g(R^e*LF$h1DhGk[._=VTF(osEZ$
Or<oXOgb%.$uG4st<NH%8ibYhrb)X*RB>("M-p6Fk,="*8SDOT(;l)IJP\XJYD/,>,@n7g&U
5<fgUEPsY^1)>)&g>e4rQuNWrj1[TsMmdA8rL8rf5/(0%Z0C)g=1,F+r'@'qRcB1Y?"h#Wdi
\]A>4fV$,5"/'cIK6I,BXZ[?Zea$*NHf5&X$F/1]A00F4decp,A%K\3n;T(5L(5'Xl9Es/(o&\
7S+s5"nEM_p?`99\G%Te2$aNY%6=]A=SO:C+&17WP["n%*@J\Ec`SQb@([HeZ'X\)>D(5is"b
DDuK&+f!5V`B^`1e>Md."XsPRi=ElVL-Hc]AaOWDK`DHP)rLM:;7-R-7A$]A*9!*ge#UP)>E^]A
@fnqe7)H+;Oe6fu5+L8[O(/t&<]AL[NtIO5%e^b!ZQ3FBniT9Q7[L17q<Fb`-*;&k)cHEn+br
]A%ursmSBUG(jr9GI$]A46I9RdBaR0fqA3L#$*L8a&CC2fiDU,R<'L7]A7^*<tY$\!Wo]AtCeUKK
[^6drf*EUP'-q&3Cl>AAao>+1758G+_cM8<^<qMnF.6S1PQ"@6k`3D1?us3>MEC?>@lkF'Y`
?d5\t7T;TbSo4'm,<D`='\YUb(K2+ogmg"H*9,uR?Fn/BC^RU]Akg=]A.V?W]AZ/9a<=P=!#!*o
W:&kPI!ocX`fp?K[?^FRHQ3+4k5uq)"@K^G2,7lYBNGui;hf]AHh_KmedBfShXEMa^lqb3d([
fB(4t!.#EuRg[1F99Xm<D&:$I%`<Br)e>6tGd&*:#9X3<.QCPF"/)@R6LQNR(H6Yn#V@gu^g
\$\#6fVr\d<>4c*3;2V1n8?`RI7^E8F#e<<9+NANe\[?RQ5(!@S/n!HoX8k(-"g<//V:@C<B
0Qb&C76qEGDkhY]ANQ)Li^eCAmHXabAJ5SEiU*bT94JZBg6?G4H$QmTj\_MKr10C"Db1\hs:%
0J=qhCfW7A&*l)W^P_6sOgQ1Z-r*at[MYGT)V<ni3jPglr5j!:ZT$+YrehG;-YQ[hn2A=LB;
9:Gs*:ZmM@ZO`'7C.<oRYquKRdF1]AKoF0`d@Fu:2-')R2ETFTA6-Jh?hlJto).;q\cemV,]AZ
9Y?FgW)]A\Xk&T(P+[nV<Hoai^[JK8OYf%P<:K/VG.4+2ke_?oqor!cBlGHHj\U.eoa/*b,\!
@.]AcFRE6e/DhV%db(`9<6+9Sb(@o@]A6iF^*,!fJKG`=p4/GMMl"9^JMmq<_S<E<``LY4eg!/
/aD%JTY+3R(mF*3]Am0WeG`=eYk=i&SLAH%^T^jL8)nFoF\6lFo<RdAZ/+!$q@fD7N>rmG"p:
f!Fi9PpsE8A:,Lfg,om&t]A!1&0@W%;n^Iae]AcpYn2K8+:JjPfsu@Ybu2R!hm;IlOSI_2r_hT
$C*'n3'Ju9TsF<%iQn+XoGG-o8QB5$3N7Fb=ik`[[NI`o.7A]AV2"e!d@;_&FgB<Dba"i8Dk8
s86SekajX/MO1,f@eagK]A3L*eK@6k(]A1]AnE_HImDY@@VJ(7#Y;aUl*R"PD'/I[+*Ps,mJX`]A
50nZFI@bfSF3epBn$"V$_@J(R)5"\C"^Ib?\2+R2bSD`L^<=TIOMGee0;BjI"`UnblIB[o=?
H&J1uIbb^SiaAC#-DZ+GTj+cl("(kmLY064]Ae4AU;G,.EEn1;R<ltf'":7XDM*%=`3:#hiN*
+YiJHnpYH"TM"1Z$c_I$h*Wd8:jo/fmSVQpL=rs@,F?6"qATHVD%OZ\+o8;SpaInk&3<OS6p
:,L:XJhFEW4D)F(.\ZV4Cn'nrrn9o.B.BOglX6oS)(^TIA0H<52UI>.-hu#qF%]A=Ldp7+Js'
adk+?Q"3dXCrET"'Moah+P<b"u@@=`JsQi!m0@QE&s;7j[9LTr^@UHW7*2/S=<mnRtBn!/C1
1/`UKY@meES7N2QN0Yc#[<Qg9U:a@irnYtbGN5=Mm5=ppQ;u,/7$Yr>V?\b[9b/Pb]ARJ85OA
,Y1Y?kCSk,T$@UISoc,/en"*ookqlP1iN2nqK"(ue-pb;'Q@)gTl-0ROmp7'L#M#Rg,H)]ADW
If+NA+>q1"oSIE[Jf.J4QV%/fT2Wa]AEGIiEfAqs<4%IL\lGGgBuL^T,.Q(5l-0s[ra(Did6i
$/VHic56)C]A8;ETkNI4@;B\(h0o/`G1WD(4`?r=*47Km86"D59P9\:juc3ObDMHEMH(jOMJ;
%qOAlZ%-#\[@);(H^XL?W[`GK@6LqqgDETb!DfOg34\]A[R]A.C$ulkH$mB69FR3.o1bVl:aKl
NE4uR%[09@'qu4&\GWd%-ttcQju=';=h39`LesCB`XV^NNYbNd6\[&7dOLfQ&ZDtV'.T@i'.
9u]Ai4)i\Z/Ml7Yn84*ne8;r:lpkOb4C>T]A^_c*;MX::;qu5]A&!!T&Xhj6lOj&sI+u`BWp^6)
pFt3S0rKu2>4NgIY!Bn3Yid4>'BNC'dASE```@U;Kk_!t^iPST;<mq_o;(c=BnH)XWm06JC;
#LHgeWB;>PX1#Gcd3o[Bk1E*1844/7#\rE7_>EaL8g&EreHC?k$BcBf8_5<.C[8oN"&J//;&
@oK=7O?Ij.`]A&N#LFjNbs%B//:8:]A+=1*^-3"M2X>$WX$+kWqN;B7L3G<QdSC(TUg'bWbKWL
Iun+`(U(nRLd:2U<TCU7M6nk!=hXB\ahp#bnT#<+o&gLp:An<)DFbqmqc'#r8_l:OpmniBI*
1Mr8"5nqVD1YVKO=0qL&8:DH]AAA3?'N!W"Ym+XN_8EH?u5Ub@!!Wgm$]AA)ffqJPNKC#$m+hs
g/X^IZqOtrBN8fB!iKt7c.%m.s3)rB,d:B2@`]A&NC0*T:L3;;MBEF;84V[b5tk$--sR$#k>d
SZEQ"5@9+1#Wj*CKC[b.K*k.<B[]A]AqO/Ae`/B)fJ8j&V;3gEZ]AZnO$PdJY9V2qH'<B%B4?p)
:(chdB*hZDh*Zijh[%ir1k8^SC)+<_#%=W(K;XRBMr0o-!hSE`=TEEe.s6[m=O]AnPcYK[T&F
4kgRteTG3-S=)Xar/`3N*WJm1/:F"_A-mQW`(/PpbP"#=XGs&uO,]A$0_cd5f4oK_L3(%u`/W
A"I.&%NO&8qNpbY4'c&>*YMKE6/+LOG.'50hG#2r$)0gLO7(mUTMi5/Pts,[6GMp^s>L]APMF
.\-@K/gTI]AE4X/:33^r-d_1'a-;DHLYc.SI\jA!mbVQUr#"%pBSrGLOSe`'b;PH08?MEe*3L
[8]AMdX@k@&B*6X(bc/<?o*X7K5>Bom0=]AZ_=aotVI1qM9-6l3$tO$qiGh?cJ#@\UhH>'5`pE
\GUG.>>&U.-.`ijo\8H!#=+jtV^bI<A(%^Vl0B%+>L(bbu2g)f<>bXN,q(W+89@I)?R/8q2:
"lp`GPK(Y!$\/'2)]Aes&SI1$Xr-rA'?jn7Na^TH^erfh1cu#)F)r--h]AaA]AO$uoiA>J+7n^C
iIB@>#4^(h&(07MoQe&OmUGKFpZj@>#64s"E(2.X\:i&$H:N@H;f8p$$`1=)%'H40AX6C7OV
g$GpkTX8/@9Xt"&XZ)<<FV&?G6&'_/Hl@_T7;-4MJKgfFteCDF>qra!b)t8Sql_Y]AkYhi,<p
)<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="386"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="79" width="375" height="386"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="237ee0ee-c719-4379-8e04-7b557d802411"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TITLE"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1104900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[11544300,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_remind" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i6&dc^MJWO98lFfm`?*a!4$[@AUBo74!=1GdcHR-0^^bQF7V+Tdr[4.'heGm#[bW2WlA*l
2Hb/l=)4'EgO-MF\cg"bA=1g\&7/J,Oirr[t]A5Y)CWtq7Q.mIQod;rS5`^qVae*7dIcl#64a
_I"1#M.ff&nB_hPi-b>F7h..i4i+hDdGLt6#78nTO)A+O%I0LI$hKS>]A:TqW/=,4Y9m@%1kg
YoL1)^:>mEclH0dH2Saf[nXC6C-HaM=5r9Mqtrl.)g2\%B$uS#=$dlb.RT^.OhfT5M3Q"Kk"
:]Ak-ud/ldgp^RMI]AJY9r;;@uFNL2Ua6\TPCG7Y!(Oog?p82"C1k1^j2dbkTmmJ,-Tsb`N#hM
VP$oh5b:d$<kusqq79dh/WLdupH:a>q^M@dDsc"lFE[_8\XsaT:7=4]A!!(FZPa<r@Wf>C:0t
VPgdp`Da5!mg>B^aVa!I23J_;(eW'D=.Jps`_BZC5)[hmTQ)l+P3jfm3Pn)&dAbr7+e)k<Fo
FR#K2[rnrW;X&#>$r]Ac$[SS\@g#slZD>sD&uCD4B"!-#a%X[.N;HN8?,pVEEbp!iuk\:l-1S
51$HCTWW:@pF0:2<)qJn>&*2Wjmmk'`s/jiSE.C;S3\pXs>B((_H#DJ2h`6,.Hs*Km'>B,[3
,=P1VIc'Mri(Clp0Rn^G$W.@l)?o/m9oXAN9XP.f3kiRP?#+Bnd&$)&W5>fhY0]A8;[c64ATE
*&9Z4bp4goL-&l!.e=D"VnX.l<&?NaA</JtcZ]AYm)ohF%=t*a2DNKpD(+,oqTj1EXFkoCoVC
>"FhZhJ=D5(cp]A6)a2NW,_49o>)XKrd*_0!%48>dOe:8>iAb3c&"W]AsO&G5g!j0O!1/Q[;GT
Kfp4mh[![sNf;+jQdPA!E=jUes1;QoC[OWY@p^h1f_kZ0-WQN4?Yrskk9$S:nbV5_(US:c2<
<ecX*X^<sXD?@Lo4G&`p_'hB&2fND/]Ar't!%EMV6s#%unAsi_ND<A6QSJTs>(sNGM#Kge4S_
''ToQ=-pKXd`ap9S!LV:gI]AZU:-?&u+_?!c=F8D_&PI9/5Q$F;QV#\ltoTP.4)+(gu]AohT>4
VkZl+Sj'.0RGLS(5d=M/'@#UCK.\Oia0"ba"[=K,LdU1L6Bm)qn2g7\Hfdf%7T9Yi-bYaLPl
'1mjtiPSXbQOBP&5=Y"b,7lio$YhZIl=-gk\'fa<_D6P$#rucD+`;Gf2fhm)Ga"('4ss.rKc
?n<LiWUukhO<"eUV)%NB<LO?qbphum_,LdpXjDn8L*Cgb<QpQ=f@dFP,\0P'ME2%0S=2FDbc
FTEn,l:$U%h;E%oA[,BJ\5C0XSt.;a6"ohShm$G+R@6:::#N%Ki^SYh>;#(Zc,pJGg(u%<)L
2_3qCRm\@Z'1hF#t+!hP0bN>KXSd64;MF'K)do*h`oH"`sgZl.VY^+^P$&npHf+_eYI_=Im%
f(]AinOm;#up5:ZSdbAW`h)F>]APgI)%o?t/6#*Gha/nS/cU!0mJaZ`<jNgq0i)Wo`Fq*/YA0(
g>`G0S'KfR*2_m$13nfC$JaFMF-/rbS!D<VKm0;M1*QQ!GMom/MkqH\]A,<O.<nD6L7Qkofq<
]A?J5;?'WM>1<luVd#_aD=9`0r5UF[)m*[0]Aqm0J-.'QPk1@D/[Te<'m>71(9"$9A'E)lTbT\
W^445tHG]A]AGDG]AJ&paeD/5l'o27K]A?c&P^dFMtpN.WV2S03CN:YpQR1u0Q<0J=j2&Z)5RG#q
c?mGEB_fXm]A3J`Jj\\2UM,3'46PJ$,kgB>"FNV4#SF=e`K_4CWs/[88-0LR,]ApU?Be1Vd%#a
I_c;OP*]A6.kMA(V=!]AZ9[7eAWUsJQq;oc=:/.Ps:HM3k<2X-O%_'D(R[QCn@:Kfhm'0fC@$%
N@);7-pPg*s'*EUC2UN`PaQ$V8sAT4e0=GLidST>L1Bntg1^cZ3#PXbKZ:eBlL74>ZM6,.5C
rb+4r]AgT=KSU@DPl;*j@nk?q1g3*YE'H/F)Z25rb=3XMPs@ificnEP!ehB[k8[&#(AZ4!Km?
D"sf"'!"OE]Ags1Lql&?E`i%qo#u(9cCt?l_5>Q8-ZQa5LfrttLpQlm[Kn2JD[FO6b@\Up>(,
j43jrF_Q4+uR>'W=4j>^LL0ekZWic^0BKGhdL*>t[DZkZ3sMl6!6'i7#NV^<#%&<aZ]A#\A:t
hQ?m2E,ZL`GjOsm_i3n>p]AVb,Ddno"5^=]AJ*Oq4JK1C`n]AcZW?G$j_uc#Cd"<=]A=5gICWO3O
G09Jh6Pp_4IT[\A,^nBX_pb,hs*bhEm^3;ogS0P9WYD)a7.lK3$78GHXT-U5U)hToVo>;D=P
pDf#15Z,Ihb>]A]AbR.rb_4]A^&BLARo'FAmCR5f6B-[c\4RZSc.P";/MTUP-)F-fcpujMdVXHV
<skHSiJ"=cXR%Br2n[Arj<!o9NL3-.#&geVV@-Q7o\A<Xr)9L*M>^/lpd/B9UD9haU1AQ`PG
j;7:7AZPuMF3Bh+YB'4LF@ABU:p\MAZDH:(ZUAF8\]A/YX"sPo=C=68*sLgbRVbR"0Kg,EpU`
dFpOZ`TQkq"8o^\[=mgI/95,d6gpTU?"hDqi_UYPDTLmrUt>qr7]AY)aLAk(e+6Z91nq7oA+\
4OZA^^AMNuG@4+Mo>?4-lEo3_2j4\6=?Yal1uQfc*N<G+1<KR@kV4+Z+P%oTjen'cL/6nFt4
CAC,@!3)WZ?lG&.a&D'i]ATnb7Kjrr&\UO3D/0!J'@8L)Lc3$VBq<>,I$J)'F!/c3,HM:'Om+
.ZnggE,FAAj]AIjr#]A?*^3)(<Hb[C`R,,uI=;j)9+UM,SSbCcmVK8I!O>$H7m-.!Pf0LL$p+=
UHLOa4.m^0;-I$8)R!.6Pemjr`mO.Lo7pLSB(Jj^=L7#$P,J!.2!0:S_=Md/;5kV,V%nU+PU
f?4\>V[WS,&jGMQ-Q;A-9.Arsi$3k`:>U%^;J=!IjkoY:V6.!h,ejqg@-;KjF&O,m$/D/c/r
'4Ng`uZ9N<s$:,E>,-G`JJpQ;B<U.?2cSVJ^Snr&3t!:lIY!Aj+b5Y^:8*0I'jE]A*0Jq:\jR
)`*3"Jr@JA:m_&9=*+m0qqp7I\Gek65H>>cO88Me%.Xg\;UQ/aZ1@g':ojj`h!:KB^3LAM+`
k9p;QVNl"bEa9a@->$Br(NE#67Fmnj,'e&,$NkA.J>aOiImaQ7)+%?Yu4Q>GH]A#W%98.0?g'
a%<5u)J&PbS,8SkmQCK]A_]Ai#-/<Q_LJeLZT'[1,u\PdgA,W']A6-t=I*8E(L6S#YV]A_-R=EV"
DN_2kNTTZP@-p`$b;^);(91Xu0d;39,@7@Z0lQn^5^pZS^5g=3CAqBB3aV26]A'C^S]A2d3q&T
1;>.&cNt1q;$E6t-$u=-)**b?s&t_=lJN@MVKk[@ATYm/QZ:;Uf'Nh?9if.8)5t;AP6I=PQt
1h+uSH.S_#Q2an!3RTi?=6'?OQ>jee3^[MK!(_DdPHa*u+a71*X1*uVdV47t\<a3O]AWTT;iN
^$9H.(_(hPp&8)JuW<DYOOdc(cs[Dmp%%\FUAL:G]A9`smeMEI*5$-I-kPAX77;OgUeN"fCTs
i(&a%l:E9-X$eZ5M@PU4E-A-2K:KR5"`Xfe5e%'KOD;oA#fech;oSDM4FiA#/CF_/V0()85$
?ZGOl_=[NAT:(O:OD(B4hmjeK%gn0T\'NWg:_J,VP=2t35@QRH84gF;m\"jCb-oWH+6+Um1:
^>^KHcfWb6%62JL]AhZnFR,=k)qIakooPM$;mmQ*3aNE/t"1AM=+m:Ls`t]Ac+Dg^BYgm1+Mgn
+(e)agjU/NBX[U.L>.VO"g&n_68/;JjiM,2sFNNll.WgBmS&G>t"SR&!b7a1@9(YOD!B$gC.
4]A<AXX5SKSF[nAVLo]Ah7_>p2A!MC7Ffce0'H]A<dlY_//oW2+1Po=_jDEVH,q$$5,J3U9+!b\
#6PU/j*;'<`4(_&Ri9tS=kX?oa?jOs*U9n3rlAfXE\TYOg/*9SUOoXZ8G+r:mCh4*jL]A0gY[
L]Abg\5k^j[>iY_<h%_?VWpUQTl5ZqT&qk[TqN-JcimF,6C]A:$"IM!;(rjGet;i?=O'=c"3jV
"03EQU[B#7Z+`jN^:cG41<5f*B^BhE.)FUaa[`+:\-erg%na0e_ktUA#IaA?hn0,`GNt&^dJ
IF=(mGZ-K&oo'le,a5lIj-FEh*5`DJt?]AMDW!nIJ&G0@#G"#D6/:'(/J9H-a\GYrLj:%SE)b
d4;/C`mSm)AT^5jc_Z+0BHCKSP7AN&/RgL&PaUN?[Am;#=/K!LYoC)TVt,@L-2lAr@oKiKb%
u!G@-/H9YQRDJgXnI2cPNq<+%aug>#E#B:WO6>csTokt-:IY<RCok,:(OdQ.d3,7J_5BR4)<
@+&O[>')2O!5(:[j6\t'4RO)X!\OY;]AB8eu>i-th9U62NA&&SZ6^PN*J.(O=%kP`.`J_uO8t
qZR*1.hIPtM_26<4K1oiZ_,J?+"$.o(I-=HL7<!^pquJp6T*)WTp,."luYomMWefRp=Bs",r
^$#S5l7\>3>HJEG]Ar%eE,G]AKB[/-[`LK2saMDT::`8C+b"%.3<bF]A0(I?e)==iSMe6NFV]ALW
hJ-mP)=lZ:,hZ9]AG:^6PN24@he_H*<M=)m,Ja3ALNi?dk<m"ffC`BTLKVVY#Li&RqO`^H5HK
URr6))hNSdWd,>MkU.Mh"LkIie9PQhFGT/-tb;o%JY5Hnjs/Og#o(2h3n4#[Y;<@EF%HJm;H
_'cg3BZ$O76`^o8McOn)VNH[C>G4l>B8gCS?[#DjA'1lV)GN]AZ4\XLWi=;DpctcWk'Gp=dgh
9&SFT9aU69-h5&u<U3?n]ATE."]A**_T'KJB1q<)@Q":C#[pGj_.#&)M),XPF6RehF-_ttTB53
dM#!r?q?gbN]Ab;U'h,mWpVl1ql_/pJpo#qK_b5H*."cH-FOj<)J6GoVZJ[b2S?mfq]A'XV%81
(O8J$U_2M:[dM+35I\NE2kHN3`Q&Cf_W5)AXhik]A69(&?US0DT*F&G]AVYtnc+WYRCDc/hnTW
48B:C:=H*WWf!XdV.^ZQ/&MD+QUib?ap5`*)pWE%>lNE?sXFHCc[*$Ge(.[/+<$5^t_FihC`
SCQD>!((>59FRmn<%m$p,D8Dc$&YoI\VbOpg,(4e&F%VC0>r]AhqY)BPRlAVY$=dq?N@)uai#
0[WW&CVW+`","l+d+hDZ8J6(qP2,nY/!AM9`&&Dd;B_+oZKq:!3"oO\/^#.(VeDBHdDDq!XW
R*"ij3hQsKl)uu;t"L'hI%3aO-If<7)o#h("lhOKFW9jN]AQ/Z[!Dg:aQbMST9eBj`*4-5OI2
#?s"ro*[Qq2`t]A1g`5HSVD1k=MenLRBbnf)6skO\W*RW^dSL1nC6Y$k;Nk-7^&u*p0Jfl'I:
[>#J@0cdo*jFo2Wb8!FY_e*kPkinC*h;"ou1,ol:?C!)-H&"2;L^bQ@il=8j@noM8PY!']A/F
*$PCPN?6ZgH<:^D@6L]AIoroLg_g5DqlX;rXL\7kmf#.M0i-/?rTAq^#Zrg7l$Q4n.)nG:J<C
<[Lk>(rhJj$gFhTD1_<fGUWrSGT8q("(74'!ObWCQW[*n@$3<0-<A4fV$FW#jKaH;osj;*Cp
Lp#uDgh(>DQPtWnNP*`A"pDqsl^&-mPrr<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="79"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="79"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="TITLE"/>
<Widget widgetName="DATA"/>
<Widget widgetName="report0"/>
<Widget widgetName="SUB"/>
<Widget widgetName="ID"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="28" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[=$fine_username]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_remind" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1699702547177"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="c72bb774-fcf1-414c-9214-e84ce9eb3cd5"/>
</TemplateIdAttMark>
</Form>
