<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="Embedded1" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[指标,,.,,金额,,.,,同比,,.,,单位]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String,java.lang.String,java.lang.String,java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String,java.lang.String,java.lang.String,java.lang.String">
<![CDATA[HeR?GZt&4(D9Fmj/^;L!%J1&\/1`cQltCn&g<B5aU&!An*I-YS/:+nAkK/`e:V.B+S$MEqlV
cN<E6CK!Z0KH6?%h:2Y$:P"@lp^gY5RSTN`Ztq!!~
]]></RowData>
</TableData>
<TableData name="para_tab" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HT#Hie(1AAHJ4uCB]Aj]AQDUGeNmE_YIi-O,6n?c^!1U&-+7"ccr3ruTB!!~
]]></RowData>
</TableData>
<TableData name="para_tab2" class="com.fr.data.impl.EmbeddedTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<DSName>
<![CDATA[null]]></DSName>
<ColumnNames>
<![CDATA[name]]></ColumnNames>
<ColumnTypes>
<![CDATA[java.lang.String]]></ColumnTypes>
<RowData ColumnTypes="java.lang.String">
<![CDATA[HeQ'jC&\[GQb+WZS@"anLnE#Nbk6AGhO!UC!<~
]]></RowData>
</TableData>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID, MODNAME FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '分支机构查询' AND AREA_ID LIKE '%jyhx%']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240601]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="tnm"/>
<O>
<![CDATA[jyhx_jygl_zbyjdt_cfzysrgc]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${tnm}' 
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)
较同期：当年值(DNZ) 比 去年/上年值(QNZ)
**/
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID IN (SELECT ZBID FROM TAB)
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}'
)

SELECT 
TAB.AREA_ID,
TAB.ZBID 指标ID,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,
TAB.DW,
DATA.DNZ 当年值,
DATA.TQZZ 较同期增长
FROM DATA
LEFT JOIN TAB ON DATA.ZBID=TAB.ZBID ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT AREA_ID ,AREANAME  FROM DIM_FILL_ZQFXS_PAGE WHERE TABNAME  = '单指标查询' AND AREA_ID LIKE '%jyhx%']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240607]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx2"/>
<O>
<![CDATA[lrzkhs_20230912162605]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)

SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   ZBID,
	   WCZ,
	   BRANCH_NAME,
	   CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  NVL(DNZ,0) = 0 ||  NVL(QNZ,0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX
	   WHERE REPLACE(DS,'-','') = (SELECT JYR FROM RQ)
	   AND ZBID = '${zbsx2}'
	   AND TREE_LEVEL='${level}' AND BRANCH_NO='${pany}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_djg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
			A.ZBID,
			A.ZBBM ZBMC
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID ='${zbsx}'
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="SX01"/>
<O>
<![CDATA[分公司]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where 
1=1 ${if(SX01='分公司',"and tree_level='2'","and tree_level='3'")}
and branch_no not in ('2097','2098','2099')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/07]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="typen"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE('TAB',SUM($type))]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth;
window.objTab = typen;
window.obj1 = "";
window.obj2 = "";
window.tabnm = "tabpane0";
window.url = location.href;]]></Content>
</JavaScript>
</Listener>
<Listener event="afterinit" name="初始化后2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.tabck = function(obj) {
	if (obj1.length > 0) {
		const ment1 = document.getElementById(obj1);
		ment1.style.color = "#586170";
		ment1.style.fontWeight = '400';
		document.getElementById(obj2).style.background = "none";
	}
	window.n = obj.substring(obj.length - 1);
	ftname = 'Font'.concat(n);
	const ment = document.getElementById(obj);
	const ft = document.getElementById(ftname);
	ment.style.color = "black";
	ment.style.fontWeight = "700";
	ft.style.background = '#FDAB07';
	_g().options.form.getWidgetByName(tabnm).showCardByIndex(n); 
	window.obj1 = obj;
	window.obj2 = ftname;
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=下拉1.select(simple_name,branch_no=$company) + "画像明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent"/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9".';qJIt<b`"2oN4LMTH7;t'h)sB_HX'p=qKtoU*;mV&g;paO['0Y<dZ%OTi)U2+VbaQQ)
G6\!_M,:LhIo%,_8(6JgQX.OI$MFDGY]A#\3E%B^#Rrkk=:!fZ1k`A2>:9qcYq,ia1D19A=/#
*)ESlW)i$T6mG;bkA*^C=IJf4=KZG&OYd*^.gTu7i_5p7_#[DT_4Mm&tDYPF1\U)S^*S!fcr
,^cT7I-7fpN`(RRiT]AHSipHZKC.Zr?i'fgZZTXApc\P=.ii!appB@-C"jY@6$dr.nF?Tb>L/
XMIRNk3,X?_P&N.=OL<Td^c[;Qj-n;'/eWS3+G'UVOCpZRH!jEJ6cCt@-QCT#qJ^j`o/mN6s
m]AjjQk3UCdO_Xr*Tp:Nfe42sCACfJbl2RtMYM)0`B,`t<CX7US5^mDXlfrt4TM[_NlmZ0,rk
G^^m+Rb)>R:8G!^<aUM*09s',ig!$a`/<O[S99BsrNj>UUL"Ju?`PETYq_r%Hff,dh*hifAA
g%kWtH#Egu8\g]ANXD7N/^?2[FB4?[8S5&"X*eIiNQ0+as]Am+LsMVgmco>,M:JYO!`XjRP0j=
]Aon'1YN(P;Ll$]A97j-!dl/.2N\f]Ai[j7Cs4#Cd;d$':4?sR&;Oi`8TkC>FqBEhJJ-<!&W8en
e2%r,-Z8(ob-W+(KYU`Ks%p0mIC(R>65DG3Zf-JJ//K8<J!dBn41Vp"9ha&EX%F_Qq\TOq.e
SO#Z_)@b0%49jW8/5qoW#+p0`PKYr"%L_66MJ6q'4?mIciGO+^<#R6-VL+TIO6)%<!a:FY;/
25.PZZ;+KYjtI^njVtEtVher7jpU8!u;&1&SBj7&(N'Th/'i,=kc#9Y373P0Cb\anNZX%qp]A
R(u9,c"+V-7lX_cW4JpF<1f^bjY0Df_T7'`Zr1&TG^3;h\9GVV#Qf?82;Cjn"!pJ[.+A?VQe
DAk/F]ASWA<:"^-"qeC?jbI\+aaSEF@S+r4SUI'so94KCQl@g.AV4)Ta<\#lCOq_ghJ74Ia%o
KK8?S9>,f6IjSYIN&R)qJbm2f81@lp/pPKLlY]AB)\kARNbN<:4QID4[1>mkLfK`qRp:[nQun
4po#WOE^@uf_M]A64JBDi$,ck,,<t#VY/MRZD8`-mHm`"A9(WMg\<C6`Qe-V26s8P=8mW5$`H
fCu(F1%m&55P=cui&:h!)rk=a^Ce9kOcHD,CS!1*aUB`cK?n0eA;md\i!`SmseqL6eVt2tt0
D(T;hIc?/10N30mfAC!O#N(5`@"_J,cY.8UPIIWY/II:]AdkGCjlf/d!VF;PHfgqmljcP439D
BERfg=n73=qpY3L4c+:R<_tf3Q'M7O421_4V'EA`6<#djRsgWl0H:T^Gp.O1\._>K19l4Y?5
T02f\daj4k((b`fP6&2q.V%i(?+M`(G66pLWh!`hFe-^PI]A%s<W[bXUM!$QRqQe^U>?C".tX
]Ae4N?'OPPNBQeOO@s,oJ^T+MGU8RbZNQj26#dWaerU-l`s)C8X-n#HoT2'>a*nCa)$>u9:6u
]A2!Y!_O;YE=!&Dh]AFQ;4c,BpF<VE-c5c=GbZs`D%U>Mn6I._^@S(0_&`<?(RKN+3NkQMc5k5
JQ/cE6l#=mmO2+toU?EC3,T?o%^XsW*Qt<;?m<%jR?@ifMo4*XoK]AUCskaSieJBZU+okC[!3
!!)l=[Ccje8T<d;R#k\M9l[PJ8Q&F,T!oM`,!M_JMfteK"F:U$2sWaeR4.NXbNn^\Mepng0C
IiPh`Z_EY0pm0qgS_`$5Pbh=^LqOfgl5fPn/U[LVh=D5BsG.T9KlK7GcnOt4/1-HD6+F%=,^
#Zk`3qqQ2O@>7MrPKV$Y@b>pjY&8:"i[Gm.ncZn-,B2pKW1[bJ\3BHcbI>-4+uE#sMGP@13;
!\@f)MZmZLSh7K/V/]AA[LdR\RqTXOupk'o2#?&QYR:k7n9J<O\]A-.b*FjgBMh+sCFI%]AP.S*
'i0M[U3ccl8!nCCMOI+?L]A<'.-<$t\b1jXgu@+`EMX6co/!<:H_TB5fb(T2?rmRng<n;PJ2Z
Ul`LqZh(&,IpJ6LAX4-ka1VkP=MO.1L#+db>C8IRNXeFjA5Y)W?lOrS-l75(;Mb)2D"G#=4N
k$0a/WB0ktJk7&.&tT1Nq=2BE:I#ti,*8uo=CHq*i7'((-kE[%E<r:P+Jq#ksa@kKZ>Ko"dp
jB6k*'pl]A9OYQHJEE54IjF+b@EUdu^9CpV#47?-=g60m)/]Aq*5.R\$d,NMeNAI\Hj!^7gVB4
[6LYplfGe%"C_5B+8356Jp#&Xk5:!q/+A7=ZO(Or'TZ,+H)Kh:*S<2dF$o+E4uMZtNUpF3Eu
u[7*f;8'qI?/"``2afnj#K;Ph88/S(IdBnOPF_Q''Xo6C/Ja3RMLRW8RJ:Nj"3)TOZ*uc)3X
L(XEQtu-aL[_--0U\Q^-NOb[O-n[@+=`2#Zm6k>iRU+j4uKt42iPCIPqMMr@E/%eo8WiN*O;
G-:ka]A-$Sl_@C@MDr_I"Y="[&H+E)N[+HaP0On!3ZrX`NJ9Q("2q=%.Guh:[3^Na6ceZr8um
<`<"U,P!%Jf,"OiMh5!F12s9V+ML?u%]APX^JopV91P>qZ@IE>87Oe"Jf#6KPj\g+b+t9&O_+
@#fL'9/^R'[h6e+;n!.CmbQ*uTJ,s"'Jh!/ss6%*u:S<UdVqgV8a=HhH\GhFRH1=)\,IONF+
8>K?u9bboS`d>YXqLZF,F@b=#q&B%7hhFA^&iBj0(\0L8sX=l,a38PDI.o'"r>ps5tYRslHF
Ed1?>o^$\rtiGbVdW87F5D#MViAGbj<;SC.]A>JPctBE+'BU)="oF6`;5l7@2q\t+I-bGH!F2
Be?bDeWYL;]A"mF6guLeHa,g]A]ALm<qZ!qMa5$ebU@l%Daa(E!omRdZ:dF\&_B,(s#>u#3l&N-
X,$2bF%)KHLG_4G#n&)"j_>HE!fb3%Vg#6K!*6>1ZYZFX1jq%Znubt1?@/o^6Zk,\6Kd@gM^
>:L`&sYkiLiMj@^e(?TQE%(V,ore@1:XnJs`8ZH!ngH4fK$r#""NhnMQ1T,W1QXQ+tei!"nZ
Y?:(4\lX"!jAH-&r1qW9`RtJs9.(*Db`G+\+j!Q;Qj?5f%g^r_B58mAiQYSOj)<&$c:(;ksm
4c("7_Hhro3419a<4GL!#^J(/0+it70mUH#$]AG46')f;F`nkZFso73[TV#l@p)$HK6G<G\j?
!=A^)eu@D;Z+R8r`7<qjf3<sG,6Sd:(]A:Igq\3qnfN1E4<Z;?c[q`=.emTho&*2DAj=6+QPe
fC=>[.,>"?3,_pcR?hiq^Y%(UJ`)&qh%'-n^GF:H<]A@VHjpBE>K$W&))FUo'>,]A8HdjV\"8#
>(q1_:6iJacW1*/P7q'RS<UU5K:EU;:2knka&*D0-3TpdX,rq$p%^V/L-.-rt!/A10H*<0A+
fPRmVI<SiI\&eoRSi_hGk%D_RPlKq7P%9Y$+4QO-QAh5B&R#p:$ggbi/jae]A/4KEqHoN\gb(
u.8*Ai&9X87l.8cs83/8^j&%gp]Ai.dd37q+[=NM2G,8PTKO"/=[2)hP^\,3bV7$.Wf)N.dKi
`X,`tYE6XFWd`o:*0VQ^kALc0GXOTMOMT@chQ&af0)\R)Jq6GI.OjfFC[`oQQqAW>#GHKajc
i)VmpGJ'OA8tjo>SY*e!e9BjX0f=trK:!YL^;l0>d`hMXL3Ra%s$hU#l9ubjdqNpZemW\D2$
kK8g(<rbh"n/o^=`2GfZWOKB_%mPgOFqklXJ>6@M!98m&&tdVa!35g<c@$]A01',<Uh$SQr$S
@d-('DF+^4Ig06#[Ba@hM8p!=8J+tJo6-[240!qWH"XMClin3t(PZeWC6Vjm+]AQR4]A/Qr2Hf
UUr]AQ$G<f@l'rEH8`*E;@IJ_c-Lo6=+BK$SK!+7-\cFXP4*&'Tga<npjbemBfcWbPd-"*UY\
b,;S[9\%/G?YQAMK9A^,r$Vt'u]A"_ZJa+o15]AOCms*eKM?_1QP3E?Sm'rn5O;RqDY1;,&"\A
k?aP(Xo278J7,F4OXk)2C90]Api#YWCd`FhZ3"kli>/WW<eC.EAs3QBFK[%0t%Ys:<4R>0&@8
r+-joSKCpj;fdO.0/:%k1F7!^T@^a#&FMO470KOb_*EM4)nhK:5.k2rc[?-rtgCo5/=3NlBG
@:[F@L*9T8C-X$-Xp0Ntue!1lc8RiW\bBV)VVB(VK]A$Ea8l'pGfGfg6+_^,2G&6Q-0,$Z/V[
$,TA$<3B+%A.7fBMjO,?;:V_9mRgS+)4``b:6ba6.1O[4J=eh4DAsRXTl&"$W"m'0c5/X&Z:
3pjp<`8D0As`QX1GcBr)@?I)^sN+e(s))cS[sfti5BG-sf#'+[9OrZc]A.o^63l>hcuSW$!<P
arat(TqagClD.aaeYKSdl3uPRVZ/B*&7r@h^g?0ZL!rVqr=p_c(lsC6ITq80[2qQ<k$;-\C&
WmCo^Oa$@h05`O,H.<Z;IKg=]ATtSQVHhRCU?M,gfNY\R//N8$1g77It*g6#F9]ArSrfY:gI#j
iqpYa-;2ci'pI>p^P[GF.5&b1gNF_"Ok-:f?[HR/H,:dr'btQb*rqXk;rc4(6cWP>C%mo=Vq
L$p/"0VfKadmsMWdGVT[,eSREkF`@Ha->!59Jqf^a2Ag'-F:brW4Wh2&:,"3l""kf"t_<1_D
TPZ1EJrfMF%?J'm%kN%-ot"@VnDQjN6C,uJP?]AN2jigs+bG0X'pI@V45@KF[90ATH9='$:L:
$^5u09*+bnnc&R1Fhcb__7pjO'>1DI]AqVS%l2T8+XU4\#:pU(4fKC>GTmTSZGV)"+J-Yk6(\
]Ag\KO+ZTrHf'!GN:cc)1-YO2$CTLO'WRY2ZLKsj9%VT3W.DZ?uLW4mZn\q?BC2lNq72F[b73
iKiVl@<bR1fBSBK#KiVl@<bR1fBSBK#Ki\!CE71g-?FS#HE:u4H&Jr4Me-@L"0-#c$*.G86A
nQ14S]A=t._V;sX)_5VKXe_5hP#g"'>"L5Y-*<u.[$"J<8m4e:q<pg+XqmTDLT^GXY^lFp)nD
I-c1,lT!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="46"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="26" width="375" height="46"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="234"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="437" width="375" height="234"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="2"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="95"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="279" width="375" height="95"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="2"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="79"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="200" width="375" height="79"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c_c_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c_c_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="63"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="374" width="375" height="63"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[533400,1257300,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3314700,8648700,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="3"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/ZQ,=R/=J795u@aLE!p+V@>O=_$K1<<;0fN,gs'2b]AE!@&.$KQ1E62;5"%K]AMbR=p
J1E9aFCDXJJ>E;@"+1,QU)j,mhucaYsOKfU6gY2)G9HS<NR,hC$WT^3f<qo-5Q2`<=qCm.`:
D#."Wh^$Al>CcTr9[Tm7BWT-*f!eY>R'!a%WHTYk_1mM!"&_JGbWI;YYjQ@n+M_FP'Z*ZXRr
J-p_[eT9rg>2D?c*8D6R3S;-c5dDNqG\AhhNUH&QPUX5/lNRV1%4cuYc^f>+&]Ac95l&_jCp2
W@mAnGld0",>V$Kh#ZY>#j(EQ*H(e^EnB]Ai2%;(c+\dO4STFjFT&J+Yc=#(JI&>AYA0h_X^"
;)E_45"/Ajas5)2,d/t(G(5Og>\"TiX^A[K#!3YJ)E5G)?_W=>&YM`.XMo6=IrkI[V6NX/O$
rt&dd@]AEC+O2WP&]Aj<LJVflHf3u=>EAbqDAmC!H1g.'36/Ws7^e=9`lS'+rU:cqVQn=2Ge0j
j`.^b;e=DVF2J<Bb3NX_ldCaeW_56W#USh2Ri/=i;FK<[EVoG0H1Rl"&-T3\Z%>KHB(8i^M7
W9P.[TK,#:8nptE94fOZSl'\m``WF7VTJWC]Ap6+pQ@32W_f>6;H2[#_4kLZLJ%%&m_(oJ<>.
m'WiF2A!#;+;067M)H#i/;@!sa-@JA_]Ak^"5qmj9/lk=FBGIX$bD:+Dm=n#Kk&1t0@Bf/N2=
7AOO\W"_oLlB]AUlKmkH$m/9,cTs51dEAWmBTD:=b62tOI<GcuJbTkEGWie-7%--M1+lQ99?&
;!8@4M!Gj/sin!qQbc9Cg]A\2bOS>3C'FH?s&[B9_)<WreX%7-!MUBi*o+Blobsd+`ZeUY_B-
47.akTcF?pn_d=eY?tXrJ2Cm#Xo7-@9h;_VB"i(Z<5S7R5%f&V9BYhu>YItRYCtuU(U3_FRU
r]A*>!f1l0#'LV-Pf^ILco!e,oWo7G5:U$gWXQCeN'3J45nYsD5m+*NS5+>'CoSM<G-/e%j7G
=\80IL8s2%/R5f*+$S\(sJ"6Q@?\>.=%q$D;`A-LP'=tQ(/$mkUeRc7N!7[q3Z[.rh-*MT?9
[k"dkYgZ&[AMY?O`Q4iV`b61HAX=F%2LL"k[6Z_;Wq95Mi4u]A"`4Y3qQ^DsL]A3SF#7>^SMn2
OM1G58qee>!\!077#3:CZ$&<5<areQM3)>'&8b7X$b/5GE]AoNK#(+:Z&#eUjFi20FGD6@STT
YPR6TmRsL%^oM%#3V%9n&Ni<M+2M8j")S6r.$a8SeTWY=G<LEL_&K"k/[9pCpV)9mL=<%@?b
U+[9CBUe4T6Bbr6>e#qroH[Lk"M1ElAO7)^Qpf'8;\aad(T%[jLSA\QsHc+1HUjbjsZSD`uI
N#E9F(Rh;,+Q/Wm.'[]Ai-_VF5M`n-q^W:l.c:AoC=j=.>Aa-?_=cDO!PJ-97]AcqFO4(8]Al+i
XbZthKfq051O3.mhB@M$3$t'L`MF-AO"BXTh_.gsKf:7`cSua;HG?PI`b^J3C"msX@8'(uUR
l^Q#L)0rBBo^MKGRCbqVc:bTqQ@/B3Id:'iN);Ni2ssa=)pCYF+L<P0rNg&u7gm9C#`O3#J2
BEJ809Pb5-(gmCB$;d;ai^[`6<>8:k@%Q0>Wi7+Y;Dbk9"LT^L01%+Di[C4m1%DokPk>[_cB
QP[TEKbV&qn9tUU6j_3@(QA8f,11rT7I0<blU<?(b5.P:tA;LK;aXb\eV]Afb:U+C(rUN5kE1
egWss>glfNE,=m64u1J,NM=A<CEY"+QB=Pm^K>GmnMlA%KI;0'tCisKs.>Xos',D#`E&Ts_\
DsOCTATFoGG%lbcNOIAc+%U>YoQdL@(eZk;PX=&RZ[Vmk[rh=m&FH*pc4DZC\PshgiDbr4dk
b?7k`hqWT6KiM)V8jZ[L"d(SZhp'&%3u4[kPjPAD]Agf4LFrdZ:j%%)R$BVJ3>//h4?AI4l;^
/%+s]AqX\ebP2*,/MA>Df2=(UOP!DkZ.^bmD5QW3GL\(nBfVgH\;$smMR5^hfWN6d0]AW!ttq0
3k]APA^7AWde**oQ%%CSR=^LE-YsU'(3F^JBq'dNeGdD(gINfu@*&`Pn;7Ze9Cqs8'=XX&W<:
8[gfAX[j-KJ6/3sc-(RH<30rC_l)LAcEZBHA5"OIs,*RU573$Rfh/\7.H<`m\$7&@WDerK=N
dI7,ra'Dp$-p#PZjU]Ap3CMVL7D6lUYS@Uf5?ZtP:+$=e]A5[[Z9M6GJfXE7q\O;hPoY?JN/4S
Mb[TN&=d-;t0P,"7'akuq(l$W4+DetTt=7%2uX<@epIm-j?UQi(_&-0.UCMY^DFm*!kj@WFs
bq6h5X,58'&Y=/[%-,BSj-5sk:Q:P#2a^o\Q\C>C,1gdi8CApP&4Q?Dqm#)Jno/H`3KUt0^-
.2MZ0XQ:F=KJkET=1sK*m`d1>(g48C]A.64gd5eGNH2gc+lP4&X5u,0J$eGrLh;@YZh^J8lC7
7<Pi@tmM..q*in&FlGs<R?IkSO61fl]A0-[MMfi<SCa<<LM!&d.=JSdO%lJm=0/Ss@Bc]A+&FV
c.($i0A^X8J[=D=Rh"@\QLP/i7tle_.b[A9MVdcD52QTT/'(XbPi\g2e]A%"3F#\$\O8=$6I,
W.XhY/o'c]A!R"<9/7>R-F:lO6g^pqV]Ad>@L=&4@a%Nn.`:*WFr-?:@E2-#\LH6j>YO,99&qb
V0:"DU$E9*oWK7IdO:%tK$E9*oWK7IdO:%tK$E9-`IoTLcXP&MiKdS"H^OE#gaf`CgaP1mP[
B+clg\TCo@Lj$'+9Whn5mTdgJe3SX"<e%:#XT)Ss''095F2qk6gARc%h^E=#d^RTkJ:*s"9
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="72" width="375" height="47"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="2.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="26"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,3238500,2362200,3009900,254000,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount type="5" serial="3"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[AA分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[公司占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[环比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[XXXX]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=range(1,10)]]></Attributes>
</O>
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[指标]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="1">
<O>
<![CDATA[8,888]]></O>
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+d8'%jd>:=Eu>fq^D]Ajb(_*9%1rRrKsGeQ%bX&q5uQU9F+MbKhTrhAXZ;\RYil61nT_&'G
XASP#!PB6psX9'Ef$O'EeosS?n`rkM.k;h7M8:LHen!hu7Kdrk<=>rVcH!8N@7^Y5Dc-(OCC
R.h<`UImQqLZ/tmrfb/-0^@U`-4[%E'L\L7#)2FbnXf)h&S)dMaBu\O+:2tSVK)S;'mn,S5H
!Bm;48TNlhLphP5?N9*`?MYDP^)D"_rbejR@DLtooG-9.1.!_5&t2NU3eBWrN(cB%]A,c#W#Z
7:`&FXRa^[X7^2NM+Zs3p*L*]A23#o!hA;g%6U%u^<EfqJ7geSc07gU6b1:K_KD66r/7%"pj?
ot>e5=Xo/72o\EqFE\DsorD_rHY/,OA86>8B6nma#GA<bAOA7XI]A\?8]Aspc$NZhCZZE*Bj[,
WFuN5Am*[`Kr:Fb5agaC/dK'dkLc7/7P\`lm)JMZ,7uH[,pM[6@eprlhW5]AdE7><!SC,op(J
e<=-kt!p`fG7Tj5\'e".3]A(!O2SR_Sm>Wu$LjoSnkVt\Bg@`O3i."m=&F?B*WC8FkT;VhrZ4
*7"#%ukN<V6`RK*%S-kc)2kM+Q(rL@(-OL#<psW3`5+RY;:K-(Hn[c*\1#SN+lAX>?bHb,m>
2tb(T9-$0m@qVW+42C]AeE)`:Nd=KI`b6&3&&NH8GqKCMa5YrVd!D)8RCtjAM*7Y3LC)pLHK*
"*>Pi9h*IA!$hg?-,CTHIR13);rnii8?L%Ob.#S,;F3U=J(nCF7b0/pDVms,P7M-.\b<+lT"
Mj8!6oSlVL$=nHFFF"gX&h;R0s*]A'I1%#h9%DnW-(UcGJ)<!=QUhI8ecO\;"KmhD+1^d(&Sd
b2jrSuPpZa,)A"YX2"l<NmG-r.:$i/YY7,BA.\Y1[O&#aD"t*),Z1RDiZAQqU3qIcc.R6^H&
_Jq!hSn-#1sJ.\EAc9@[.[1om$%p.nAR^t^*VtrH?uc1ekt.7*_%leFjHZZZpGJNJd7(VLak
i?/[8BK:C&@Tc4c8<-fm9naQtcA]AS<9XB%[0Q5I6@V8/QilX`\gu)d&'7gu?p'^D2),UBk]AP
H%Q3VRA]A9N^h+-;k=JDKZG:Ij2Y]AEA.2f/hEK`AsI$q$#4%ii,A0SBjCqEcJcm-#_Bl?N`]A$
seDK#&V5Aiq!?kYn#e!t#8U.OgqD.##?*k5r*jIR(-BA]AM_S3Rn-C5Pj&2j"'!/"H5q")6X0
[bV0l5hE@JT^\ikeG?aA[jqIg1I%"eM.EEM-H9J6Sc#tD,b`.>Gbd)CYpJG'hbDNN6g]A(Z"_
oD,WNin;;Z<qOSi`k#'?j"QB"0,1\>bHl3%G?W-T<+Eq4`(?BAm?%Gi#B-G25P;g(9$Ak=g;
B8^`cf@P=6=ImdTYL^+pR'7MnaZa#%Z;A]AoKMIBiEP3U%6k@1ZFF92hMN9IfV0A+pd5c?&VJ
Cl=+VnDId7h8O/GJfKK0q8f66j*8SE`11:mW2Slg;8-sJrVPo)j@aZ4H]A*?Gh,$0Gef:gJkA
RA_KW8micWpM5EQZI9SODYrbg7Drl#X8:ke46kbnDG!hIlj_QWDbAB"7/hcI^W+(eON:8\mr
il9(Z?+j<2In,&3l/YGF@)Y!I3>gA?l]ANm^m&%RA_;4ZV:ECZj3'[^"lMf;$f9b2b-r%ig:1
QG&5;a,M?9:I$]A-8>&K%+*UP(ecX$-*Mq#jI]AWrb>"B`CA0ILUa/)piR[htK'g/h<^h-C:WR
bT/W="/S%Ila&c5#DfduV=<rDH!mI-)R=t6$@gn6N_I0@RqY!TriGEqFF(CLc?\&4u&e.="\
ChcKMS0$c2Jf^o3,8m"!DWDrQ[(1J?@aWn.g81[BOU4TY+[.&-9P`M`f3LBKW(P#/Sa;D<5?
VE!iZ:7mHqM,bU`*J(8V[%6$fon7Lccb4<UXFTc-4r/ddmHO7!Ek!nOV-?=NpW&EA(S-)2),
?-KC7.3H`!F'fKVjLt<UC;FBX8?=K\^4)?GZX_e8*CRV$[LYLo\PKLj`_N:M%0.RTS8jMHA=
j6I7a<=B*C1p-dC2DV,j:N`W[s?t8C?Zr@3$6ZZd*&u9=oBR75PT;Lij;<]A#&`c/LmeSU4<L
)slFj9]ALDQq9d&37t)d3,]A+"*I'kqNde=XiI\eT4.8D<N+1O18Wc<22nqS.hB&Z'G;(oK!@^
Zr2Fe5CJu>>gCQGXNP0Rl`-pZj901mll4&nN,^7<d8P]Ai/F-I'0]A`(VO'IMmmU<7b7DoPnf;
9ih0(kTIlPBTD376/M;3:2H-*(*d:Z@d]Ar2#up%cna74!\AL:nM?++21jsf)1MoJTEDEbhDo
j+1Mp]A<@EK.oB'JEf505HME@dPO6g)9*#)EKdZBME`$lR;otu>RV3\%W1t)O65#+8_NBOZAW
l-B/2B\<Fdfaoo7kHlq\FZH@/Q6fLZ?inSZ#Q-1-6Z-_'_t88hd(q:J2EHM,GnL,Dp[4oO?%
lj,Q_.$W2Cd7h_1"<-\unTSa64XS.cSrC\ONoi^SglSSNAP`JKkGf83tugga!dG\a#F+mtA0
Z#/9!3X%ML/)h9hC8qctnL;#kAg)uGL*9:@,,16"8I6!d%5%t;N"HiEP5m1i[<jmbrb&"b(f
Mf[;`.c;<nm#fqS'PfKA;W-L2eNNUZSRr/u:p^7BQh:4=V'%;R-D2N&VejAUtICfp\tS7)+g
^k`GGg8b>K#>YUPcHX,-H)m0[C4jPasHtrnU$'';kQT-f+O%:JZP?7u@]AVhfXQ^T`ok//,AF
\``^@]ANNg?Z<fX&/X2l#aBY@!a(=NG[VjJ-(&4'3fVbZJW5E1"t(jGE8D9QUC?f6<n5,@:-W
4947?BG]A4RJkn4b]A2ndPuUjT?]AET#ITPGa6^cb/U8SSUa(]AGSITJ%B^-S?-^lfqL5F00C#DW
O6@4Kmo#q800#L6ht(i3P-*p>$6FK:4E._`JPY,NmF#r?/PM3#DZ[reMHi1.dVRA(Gpu8f-_
Q5ipKkiK"G7,"JI**(LR^>F@r,0G.n1^4e`q9e;&!`)KC8u0%o#n6$^:-_-,"Z*Gh'W#X5nY
MesX'"Jn]A/)dbm4L?:a&JN?AUtG)'-:YOkbkeRX!/XP(WLcOL;$'3t/2-k=Y6K1qJ80h\3RD
W7-KnjF7ol,-:LV_mpL[X>E]A7[$",%WD?ki=L7!jA=+>]Aa[nS&['[\&49u6a&X*EotJ%6SS#
,#86R44,K*k!bA0n4"?8-qCX."qfG;";,I1C3FuQEs$>Shn@A2?n>c&3"QSJr.<sM^Wq*4'3
6Lak6&`7I.T$)M(ZGP(i:Nh@Ingj0EI",+r"5jH]AX`jaR*t;R@_-,>(!h%nn1M70;i%pBuF9
e!DY*u@4nL=XnMAkXT'BP3i+-YcR`);_PZH=^r81,2KW%!K?NkR=\<?<-N[J5tabOMg:Q<2J
cY"+PEF4rNP6VXS"SpCMn6M?-=3k_XhnnL]A:YX5'[R4S"Nmt[&BnL.?QNMi"m^fghgNhp%E8
DEePOokG!Dn/sL6W2lVN!R25j-W!iEV^HfS9gGW/9NPT<o'C>_1E6c?7e7$raE*Hih[<t+>g
G>DSZa5$h-9bDuGau@OS&OiacNq`'Yk>NeM>?ahQ6YkcRN!:3<$Ip#5-=\D,HN=<<e/4a;#=
aQNi>D`UuS=OAB>X8.Jc.(%HIEC@g[j*kb0mQK//,FmYuQs"TSrFeQe\a+XUU.O0eR(DQ\QR
W.&@"'M.ZQs<R5oaTJJ]AoRo>B\f<Qs4)Y7^,`h*1q7^RZHBt<K=OuN03!hMnMqpC]A1=sQ_<E
+8=n6(4ZjBY'kC9$phc.olieLW(fq7sHlWXd$*CtjX<;96FV[V5ZSB__L@0-Q)"YjC+%""bq
Z5W0\knBC.#D:1@JBQ&':V8d5",Y^&]AA0JdHd&LAoHC)_NJD3crQ<(A_eK(;'j1,:\XAVW<o
jKi;,=a_.l7>E!`JBnA6f>IWX*864Wd8O)!$liW;8E<J[_).,Xp-:$f-]AIJ$O!7#$mnQd`np
<_Jn%P4=A>`GD9mgkls8qg_D+LFpmBWs,lg1)j%jjGBs*^$u,$I]A0-Odc[B\@HH.A?rJS2CK
hl[);j*\]APQ?Ja/JrL)_-ReAT<>$Q*>H#CsM]AZJhJ"`.^>NP>I-VB9(,g[fTLj.QIAA_UUc`
Y]AEqYO`S$sc!/DVl'T4"YGpu$a:0<.f)+#$Vj1HJ6ZjcQ+A3Q2/L_j=.R:6)ILEqXYU+ss31
XT>qN,@d&/r(dG3?BW6'.p]A@@;[<\[_4Zmqc#1B;]AYQfe_qA=F)CkqE=mI@-j@gTBZ@#bcF6
nRNA.dnC<Xg^?_^W@Z1.N8M=W=j%qQQbHT+2Qb):(IPrE1FLIa>]A#ik;@XLaP.H/ndjRDl:u
9!-t$[5)7#!LIF^g2-!pEpi<dLRp*J]AVbGDF1`GQ@_'IQ;<I=V?(r5A0?/Jt&l,=Yk;iC:gT
+O0h1ODDRff9d!C$\53(!.':)t89Rj;)/-*id]AKu%+"'GjL%E<o\CZC5Mbr_YPo1E<B9ojBs
1dg22;WSDW[X5*ToV`To6X?.<qbY3ukYPAL;U/k2i>)"=]AmKC[n,D4aIA-aB?er>>9hl=rmi
G\O-3<Rr"e+i1BRCtp`j+[FF+7e1(3":4:Kp'Sl+?MVccm%[%Ic/[T`f,'(R!?Ou4Q)R-=ZU
#,ld2uGVumQeU(2aB=1`\,eqFNfVrX>VB6J+(B/\0jiq\_T[>0<1=_sAI)r<\P3SK&%Md:_\
F%mhm0^U5^?#f^bVHT[l'ggXCYW0p([,0`/R]A$P'7()!_]A\1`QofuG*W?(5dH1dW9mc\0?j:
D(*7sL+Q)s7:Eb%lkhQ2:K6)J1J0^D.Z+i]A3h]AY>$O7N;0Pg#,cP>mf<'2mVg17(.I+BeI^W
o_EBZb*I&Nfe7=MQcrO\j)Uaa(<ktoEE8Kj8gY+$2Gu:BjqHrVGG;5l[mYaLC5<b(`*upho/
Kc_n",!$Nj$.\4'Y?&_o>Q^*aTic<QQfULMh'JnLluZ1$I)rH@9eKY4_5?iq_;n:';?JGTd!
DkXp)Y`1q1i$s%&jCLT.)")\`^,Y.'SB*h:m(M@2'lZXaOplE6DdKZ47kG104G*AXurc6M<)
kI>H&M"$e*9BUin-AJu<?oZ9$JDYF7M94P:D)QTKEpH\Q&-4sGch&+@(uah1,E@i;%']AkGcK
iqWK*/Z*nQPg,JKXn9_t%P'T9-2uEkhD:g,]A#0D79DH?.&C2Q.$4i)N3.X<mP-/a+&,?E(sr
B'CT.J^-,Zfiq7#<%>Wu*UHP?X'g"PFC5&g_L.TKP%GgOuhe77Ph:5/_I(Hr>SLH9RiprdYn
pbXbs49-Di0Ql^[PE@<+TJOV(*HcXJ#NbV9IrCi;tl=cXHiHi`5LCX@CU-;Y%_h;8);u-n;k
D:&q>80GWIVV"0XjQHd8Rh#5uhX@?N>rqWEP5N1GH.l,F"Q/:T\hMuI=^#Hd5mlb-<[Y&Cb$
$H(Ll/-#+5Yb'8Y1N%:76-BX?i*fqc=.@Df+1B@'p0d#)9$PrGE6=nRJ^2-ec7]A$tSI6Xg#H
M;PC2I'iH/n1A7>Q85dcK?T@n3Gl$Ven`Ib[;<flf9$@:2U5]AaTA!1]AH#,\S27?s*TA&\W0m
Dh_=Q2J;+]Aqe>3Hr7WQ.sAgl-s'[T8E$qAc\CQai[>LI#U?kkHQ9h'5gY?0$JE\4nim@hdE\
I7TRB4NH+OP>Li<jo@mbD-AK_AU6hUo@$ITH$;dO+!<g$:;RA^CD;&h6BXU#*&'+8\nG&[2Q
;AD[m\j]A\REZ>uqq3E;\391\]AjkaVRHui.G+a:[[:c>m>f4L8b^31.#939t]Ap2%@qSS_D$4_
f+2/8kHiQq"%]Al<8Vb,A8\KXR4l"=e"E5o3jpk!U;6kFG/Rp>m%RA@"i*O,'hZ^:lW4=U/lY
EQn8/01X_]Aq-TlD:>-U_L,>D2;eJ\o$2WSSPcpl(c%P#Ht+Zf0<\cJe9*"h>C!ZUVA^h`Uh9
8-`JY>I%ORK3)+*O7onk="Z25KK\h`hB#+k6<o&2oV<(mMTK"?LA;LW4mIIZMp@XO`9W94l]A
_M^W#TM0$BC]ATh8d4eF4g835,N[CD^]AirHSop@kQE<SI<<HX+TS/@qKqYra,?h>3g*.cIS'm
T-+\muCg,;a9nC2'hp&WUp]Alo>A3`fb(c@'N]A26UCs9@nmLAC!cXo,+$kNsi:;IUQhXh"p<'
DFMil+6:/s\N]Aq=L1>,`g_3]ApjpNbI3RjmS+R4qd.4\B?j8%)&i(<?"reU\Z%eThhb<c0TLo
K@"CU;B@QYa'gnAO]ALSI\5med&u6"fV;3Y<'.?B\^;%3i?caj!daUGn279BpN'@U*R=8RNUV
``j4<X9%ZKcSuCM.(.!a+f8YOd\4qo3S230\3qCY=881OB^=]A=I_m!TC3s$@cbTN-<U6A_S2
`!")8`GYW_6JDI!ccTdW]A+B^LKD0@=,P-";[]AJ9-Ol.5#.bO2O2!Im\^sd;+VFM!17@K23NB
:;Kj7Cf;:-_i)4V*%Le]AKC(<q`52rga=k&cLZBkXhPR.HrI\c7qk+%fO!lP:Iiff"tQ]Ac7>)
a%RoWHr(U/`J^?>a^^qkT+T5V8_Cj]AHE_7"6St]AuVp5[bI&1Thh:Tb)mm#&N^>@;AlniNtn_
0WhDeG`4,X[pfc*DJk)&aAL&4@KWXX5_5-a;1YP1N67HWbh#2g>QOS>3ch(C/#&KP;gJb_Tu
FCX11JO]A@Ln$7$bVp"m+N-il.OSE`SH>Jf_FCEP4@JT5_'\p)/IK>3i]A?D"R>6f+k%1e=)_h
AceQQSCqdo<<0`SkP<q>Y5DjXr#t>k-"nR;rqB\D=l=2QSH"u^32[sXXn9+OsYr>c7^-2Uof
#_%880PKUmADFl?qQp2u1>FUm`\Y-57$E:pA4fVC<\g^Nta?Nk#LJT[0I1eMVGkgA*#p)W!L
mOqu21qWE`lmHGNg2u7\4o]AVPVT/I]ArB<\sk4%SBC<!$ohhfFV%'sRS)XOqbl;BBl7sF=tSc
0%Y*a0s_a+t[#H.qV`Dtr4dbM;nEI-8`BPk9I\!8j\SUs6XfcGP-F+L\_HHY-^.=5H0&)`3H
NKA_uSKJSLjj&I*XI"e>%9SRl49!&J>W]A-m=ruBG`IeN:/l.=D=oJ$>l)nsce*Rm@qe3P,)5
onH@*Rm@qe3P,)5ookfJ28*0s2NCI?QXaEi:G94]A^cm+pGL>&oDIiS+1n/uB_epN:S3*4,B]A
fBe<)di8*WVcW?ZP]ANms1P;Fg"C*N:9+jSq$H3f^nd3t:ELDB9f^!8:fMh3fAa%K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="119" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="DATA1"/>
<Widget widgetName="KJSM01_c_c"/>
<Widget widgetName="DATA1_c"/>
<Widget widgetName="KJSM01_c_c_c"/>
<Widget widgetName="DATA1_c_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="671"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[370389,304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2<>'总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" cs="13" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="12" percent="50" topLeft="true" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" cs="9" s="2">
<O>
<![CDATA[客户数-总客户数明细数据查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="10" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="13" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" cs="7" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format(now(),"MM-dd hh:mm") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="3" cs="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m90HrdV%0UlY14iEk@m11Nd'm4*F\?Au0*+]A;tU)=u0#SZo#0Y+hn!aU,?#<2I/++$`-m<"G
gCt'EftbA>YTjQjAqD-somU'EW/gch@"dJ&(ZnoAU#5ofhj1F.IAXk?R2?MpI+G_7MpTr7o]A
9_;XFfHZoDHi33p"q"aC]ARtUK[i/**(B%Yt2h]AC&d-q-]AD@qEu3`t26Nh^3afOWG!n39QfrI
)U)Bi?RXHn#HrSrp3-C_jPQuFiUd0XoPQU3q\CYY<OrRe8la8d&[cf9"Bm3dD0UVCcNYs.<5
WHVY'nue7iL&8_NIC4\Kn4VU?XT6`7>"Pg^Clof7QdPP#ajV>`9D7A_<-&dVgKD(f<r11cHA
kTcR1Q#ijFi\KFYH2#:OhlRjSZ\bKu<'e>6HcL(#-Z8qL\^c5Agg>\/5)otsj6U4gD=E-a/.
_N:W<BjTjF;uUi#e6g!<9OP2KQ2%:M&Dr2K^+?T(cQ$"%Thk0CQ%h\QCrnbD%W%$iJi\*U`G
unGCWec+Dk.ipDusnhiP-s'Bc[@9&mZO>HL7]A;0XNZT't=[*0E%CiN/'cX27`0[mL"quVdWI
K&Q\b*f?\HFA8:?K?s3.Lf7ah@S,h-El\\SL[#TXGL<Y1F'sPA7TMr0(a+)$<>9Pl^rUUCBD
JMSgTA\]A#AQKC'06fQ!6o:r6u9M<+E\E&*XN5IW\fl^r_Y1[4(K,",hbBKRiEVlFO)SG-GuO
-Hh0.nf&C-gg*o/Pnj]ACfDHZ%YcB/>1;sUb^kfBU!o-+\4Ie4bjbI<PG53gjJD)J/mnrY`oI
Y,f01.6#M9(m,#V^OP=#Z&+CNL8u?bsU.Zm%uV1o\l-Th#c.OBZ>>$F3rB>XU^Ti\kMjnpZT
j/JMo7%*C*dUB`>HboofaZB;%4L@b=I\JM'Do"/EZK54NHLrR7l-J+ueM>@eZg:\8[JSU(nr
ohp^*SVT2o/&aZZEBXF-)pWrkZ68$-#["iaeifH`Dk?RlUeDJKe#@-GcA%86BJYSPWh[:jn?
M[(FWlnY14e[Ls0V@VDNP'nch=_r*@p%bfjmLE-u)s%O;rZ6\2,tI/".CIfA8"Y9C>oLo+cU
b\@/_>HLa<FA"02L,tF.&>liEB<rs1\]A+nj@l\cakq(;o@(nF&k_$tD<@8$WU7G2q9d@:q+&
[hiXqUA2`Dr7WgDYddbJT]A&OEJBPKKWO*9*5K2Ph<FAcoT5q[:RcVlY(![/2'EhPV%rhah8H
2mRC@:a?ZgDOW<BPU;E:um0c;n$`7eUd1*tZB43>'8k<NgmP<L'q#jI5!iPc69]A`tUNOL`Q+
7AV:0M_[6dk%!M@urjk@ql\["tqY:"*O$j[_?jY[=4B<.'_,dd4ZT39ujPN<@KfOl(R_CS`.
6/n3\GDX"OSXJZYN%&O/#*.q6Xb#CmS9%ap.'FRpiaJ[:NL2N;PdC#D821FO/BU_oqd&-%7G
=Q9"L-?.,iRli6^$E!G2CGO&:pP);m:PMO%$fqE,Rr(78i]A;`a4WD_U]A^Bl2n4>"-J>L4Ll*
Op$7V$u4>]AA]Aa.5I0[8i,@Kc@7Ujf0tBK+:!WQLJPAe7I&hk<qc09g1CFJL-6_;Zme(nQFT%
U^-)+.2MDB'8epks,=a05do@@[<ujq<MJ)tJQb1jgI9Gd)FR6LFEC?V'8#O\s:G5j*eIOJc/
\tuhh;030EL;;,bFcDgZMB<;F$1O^*5L0A$i'_iDT7tU"NiQ#$mdufk&*0E,!C[E3Y=BC*hu
tE'I.9PpVfr?hJFWo9?p#SSnIcn#dO32&BW$iW=\*OZt4dM]A>J+/'M-Hj'G)hUoiN!EH6t">
S\kh$Z'7tr[s2qhY,5%T@b[)+!_$-uq]Aa&GS+)ni=eUqD[$hj3"u4g*BoOdU5KR-T$?G_u1A
KcE21a2&hcnu7e0:^p6H;;B3rq,3/d9M7-tKl.?D$odc3lXWaQGOYQ*R$omMmL.MBhZ5'm0_
`e]AdSB:[M2fjTTlRHD+gdn-RCALe!O-I8A_E'n=QN*4c"j]AT2#1ZNAso_c6Kj88+eE1?p0B'
9Fq(q!+!eI'F7OLj(tTZa`Wgad[Bl&mM#f-5(/'2%el@2Un,6h70TRGT?PsPTG,u:3,aCHVs
PHmQr!^9od$QiX.!qb.!;[>c.#AJVTFWaFu0u>_\@p8UPIB4E'>Z&j;eZ92oHPBF1VO2"a;L
#,`&n:W2O:0YC[i'XLC?R!L:OBh@)&,T^u]AZ>*e`cYZa[UiUmVl@JKcl/_YR@LPdm#Aeb_OU
)_@)Q!J7V&pJQ\"3,:Wf]A&9[G*5+\1hgV5J)6s;r5b4SBRj6F[ocIHcj%Y1[?`gYKo2+q3/R
N^dYuUg=E5U!kgVV'WnDghQ4U0[A9^L^Rj8."1CiBDTchlG34,%GX=I%[lAi63:`YVXq$6Eb
gLI7fY6[jELY(41c<=>D<TTCk=l-l:HkVLZ`Y>5'<kqfh5=;[K;.nh"Y*V:/>FZ/T!rn#Q+/
is[7>L`2nd\^/d97-7$PW)0pj5sd3eGd^ORQ?F(]AJ_NX=/Pl/Zbj=E]A7Q=&+-!62/r%R.o6p
(s#P]AWAR/bJ-QP'Lna"b[IE-8obt*7^Lq>3W*ucq3_!6e/Rq,-'UK'ss1[4AHYgZ1WQi'i'R
f=#_i^7(=EIqK^pP9o<!844qKjg4$aQ?$9iO+Oj2fihI$701<'(#s[8diO=,@m6UNS=_WoHY
,9EkjuFYJMQ;I.NqjF^kS(`bVZT+k/2kZT-U8%q$B#W)F0NtE5AL6K2SoinRi>E*YYJGMF:U
8l2tN18NElpL,Lf8`YZlCectDW`]A7(:&^gDGmqmG<cD+QX2V:iR1hR-E%+^=`PQDNNC,=lhc
[Pf@&p4hO7!\CkWd@B]AJ!ugs35T'D_o['5ZW>-=)[td5\A-Y>V?ccN#DIGE2Q%Gbj7p-^>ie
;ZB3XO.Up[[YD_)]A/*n0RH0gs]A!,40Kunh8F?HaZ?9iu3)UFh`9paIm+$&*:R'!m`R+HAf!>
7MB\&7_a1)^TrNl;5kIR2O-``A?sB7'Rg`+GSWp)XT=0_)RF8MXZk_YB'Y@86`%i[\B;QYNe
9`mA%)*Ar_"8%su&f,N^9"Bj&R0kb6[q/Y<"gXsnd28dtB*deYkm80g+KMhJglENPb"+k>TF
/(VOpb)Q^]AQ"b<.`kq4i.^_%r]AADA/hQP5fjd2;^(#5V@Nj[X1fKdV#[8g-A:5T$.m(aJ53p
M#+QQSMA8gmF%qu>T?Q"%W'7eVD^3Yl3"J/h23p<8CE!+`*`9E!CKX'pAbsWZ[[p5CjgbPl%
"#!EO4VQ=g7LoU\nr9[sH\(FBAatGmY%huK7ck>K+OBgpcMoU?2d4>d"tb!tO-ci$5<eS;jl
Y=KX/p/sMB.ZlTFrinBks]AoaRPo'HrX_ob!qu:'XQb%`=M"<p@9kK$p7"^8T+Ip%)%G#9m8=
M=lLAJp>IKZ>NjGHSg;_6blTRg3UeL0LsXg9R#"<EMAIL>%FS:e]AkV]AX>Gt1dTYaJf_VIOR
^0YM[['Vu/<h.q8L/"/VUbZNrcP(;4KeNVh81g#11_YBj\!jNI>g0t;RmUG^cQ2)&JeOmXO\
o'Z%j[(&eER;R/&J86]ABXKDmEb-2N!s+:]AsI0XRcg2`&,c^O[.0pqu&?tZ1\%&3HnfSWDrM'
PtZQuTML$MH9$.31@AE)PG$jkg^>E[$di[<Ua"XX!9`;1q3=8TLAX5"8C=U>Z,B^Dhk%F&]A"
*iO6D5e)!FQ<J@r[L0YP]AppFm8Lk8=J>V)Me+0.8^t>;p>&g?7*s\Fe"$1?+kpWDMW2RN$u6
X-Fp!X`MR"lPUt]A*6B&okBg1/!'1&dnp4_X%9jq]Ah59?(%',oZkXW&RKc*_:X[d<2ng$S^Ik
2\5F(^ra3al^ZKFHZO6ALSk7e"3!E^k:k$Yc]A&'#3ic,ds9kk6HEUTs)8ePI.R9<!2fAq@EW
a;o.l2'm7'=o4M?oSpSR>g-TSNf@)Y;,'TcTjb_;DY>7ED/JA$:G+]AKOU=aKNJY5:Y(N^uHS
K:C.#GmRL'N^Mqs@@f0;F]AQ;D\F8Uub(fVt5-$Uk0jH26_Qk(O)7/^%DWhWXZQLS=:uGN.<M
OW>.PI"KE*RecHWalT4\%Y5.b@30Z8MurR2oZ*Z9p/iOQb>7+1NXES3/H34Xam@ZM$7d>1d;
)h>>%nD5MWqX[O`k.=C9g]AC2%;b[gs]A%*52n`SiM0YYAG,I\]A>+RbsKu)A0Rq&$'WlW"AW3=
-#VhLXJ.30/Kg">br[0V+.%sMKQW4JaE.#.k'9>Jk(/QM5TF9pXTc^V)%Hd;P.c>4jjW3HoO
PkgjXo+\44VSH8#P8H&m1V1Kj(l`9,E-h.VsUgd&S5m@'c.$Me0+<=dYn>IuT<`3'PTef<tQ
>6%W4+Jq'+qso^[Nl9c1kfiPc9,*1JG5rX2?QGcpC%-b]AV82_T,27FXJMT7$\[3X`r["S`bM
:^.39e?m\c*lRQ%$7MbSJ7-,U>Aj@XDJf*LjA2=UeY:@15JO78t+H5^:_]AA.96,*Gbj$!S,E
\>=<u%>"V1$g%BG\)C*3^;iD,T@")5P@dUT=@Y^1I_O1#m/l>V@[3Qi]An5P0%o]Alh$)%aYj(
&JKYHG>+^`=p2*B#[*U88tCTQZP8dJrb5tHJAj)W]A9nqU)U^[T"kRi5!'>@k:4GA'N$Yca<r
K)9(c\drn`iI_Kf2]AFa+rSM:eV]A9NTp$<+saM=h"dLP-4Tcjh0N?@nr.FqtP^$$6a?YQ>AEg
ra#spY)*6Q*#U;VB^7-CH0!qW2:0?)OS/IlT3MjMn=YCno8A$XMSiT>h!_MJh`N$6IAjQQAh
:'G(;_eqSs"MmlHcr.3(57]AW9Uaa%/KCpq%`(Ss29XX;:'8RH)mt&e7,T4DHJ`e/Z%G@.W'h
1Nr(F161NSM&Y/YIfp-[RA5k8ojK`S;7g7]Aph$MT5g$"P@2m$36@.SLH+'9Eso$"F2d\tpcd
QI(jOrp=kT[G7V\fYt&/4!Ur=")J'I[TbALn'D5T%TRLPeIX$a/(&_r1h45'/jP<hNUnAJ*D
%UoX1m^-7]A!B\#qhU=7pW7ns5Vde3WIeY05&aIU>OY/MC3Pa<d2@LE%g;R%/:02^%>CJN[l1
O$#[aE[\C&#M*^]A)WcjWCBD,K0POZi5Pp'bJ_[?PriQHX8!gWl3=,ZJ*C&g*nY/%PS+_R,`S
^&I]Ae'X&43uZT*SR2-g7]AP@TcMj\*SR2-g7]AP@TcMj\*WDZsX/+8:Di<O@UNP(7jnjS;s&jc
\pbKeHo\$_B3#kt2^)[gYE3Gh>PBPALg.=A?-gd_#[$,[]A:>/9$Bd`;DSc&LWmL!e(J1gK/a
B0/Eh$)Y<s0&1^rYk~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="349" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1193800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,7950200,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb_right" columnName="ZBID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象3">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report0" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象4">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象5">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report3" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0"/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1" paddingLeft="12">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?i/qe(5AUZ*ZsT@`:WqJE;W\"[lT<m%RNL$<0ud2>/1i:j)lGMl6oBV/9<a66`?NZD;;F#p
CcjA2/UB@L/%sL&`A',_QG+G_sf3+)g;dqu?UMP=[t4H[k4MhmrL:F6BG!k]A(q?L0Y`T1c.,
&SiQ&3U%+DIqRc`B<=>M`?(Zkk_._Ga/f/C>cAl:Y![TJKq(e2l:`7@68`'`)qReU'6T,ff+
10n=[<uNNXbOmMTeB-Qhu-Gf[/Xj:T=2qH2-)%MfC!pRV2t)Heqs$>fipalYmXL#nkJp3kK`
D`>ocH@p0p9l1D^fQe3jVcSl^$&EH)8Q,8\b^cH/hUALFNB^J]A8c^l[Rm<_V)Dr+ad8PQP?;
Ve_Q>5)3iX@hJMGN7ZD5i6FKZJAM-AR9ARiWa9`fU</6Bb^0T&"4OJ(eo76Pa&_S3C7.8,fV
=\:*?>($<RlE%1_t#qHg=;u<W-i0MToV>WS/Cgs3]A6)OIs05(S\%K^*?7'LGse7r\6ES4gsg
_`\n+hb=t&ge(;gFli5B9a4=2f5DFl*U]A*qbLTk\n$=WKl5#0U\FQ[q<0%pgJ&UJ>hVT^`i%
]Aj=80,GPA0+;NO),n79T4TBQ#.kd3c=[F="n?\SYObBB^NN^/7J5UAas:S9#f`2^9ekQLs'0
)'Y@Q5DqosP)5;pt6l2/en,3([h\Xl"%W[V^%I5rT"Kn,*Ig;!1`67O*X8@,'C/7ms!gs["U
;gUX@:^qe?U.C%&CtJBkX`b+`Rl;/,K-<`#S,`:Fh(ita[^&q/pQ&@fSWP&=im$,ScFs$o?^
;`*J7&9)SHuj56Zqc4YpSn^'[_><m&&Rp$eG+*%Qt4^Z;?-`H>*>u*Png*-ip;(SXG/Q6((Y
\M=SKIn`nK^_!rr,.>gnBBbU,0r(*ESCppL/S\PN,=Ei:2',`]A[&X)e6UWWAb:/eg\1YY4fL
)`=&2,kd$)!O":oDs/G4=se4Tk4@1[01-s/FkEiYKh06G70+$W3A`Bc4[8h2pKsfQ]Ak2?UO0
)@Ephc.qG'4mnUp5[VL^\GmU3-Uk'eH%CZB\<5@2-6N)H7>4sObR.;;6E69U]Afe)>fMbtK^#
/<H*P)Lm_=JB-(gI9.?FOr6d(<TTg2c-D4hJt8Tn2US;%R#9#L)-tA?e5Qdg3hW5_\KjM(_2
S\u*)r2-"kC>h-HfK:hj;&-N/R+)5+>$KAFd@V#kCItn5uhe3f4I]AEnkCZ(j2`m9C%1ko<2;
OkYG\b0OO$;8+II^mHZ,OX/UOC]A*hZkhtAnnhKp%F@6UAFI>orNA8apHE_7(IeuJnLmb*=,%
j\g4dZGf`AsIHsI&\%[XOMNO8FsJrlL!*QK)b(!]A-W"Em5+63md=`;6P+QJf8Z_@:*+bKm1!
:IJklr:;asL%L5`@28Gj:r(/'Ni<&F^(N+4J[nLGE93nE@f=OUcL-aN(P0+^7Hbe_.l/`=5*
0Fs3g4,Nt6lV4=?4?=Zp;\<N2l,HAh?Unblfu?gr3kY@0$'BdUQSeMpg"m&R_f/%'HL6u<m,
CIK';_DuLQ)msUEL.-P((lsR$X$GE%BtpSiGBXRhl>AO`&nd'Ku)n'2*UHbRAZABHH;&X?-8
1Y2d[P,51$[3Beqp'J*+eHD:i3_1_o=GcTu+KA_,.(#kbkL,K7bq1uklfN8o@'.527ll@XKH
b(W,]ADUY(k#ur;JaC#0qVaR]AW_\nPjA&G(q>?[*fM$RY'3gmC]A9fR>[dZqTVa$f%h)QK)[+K
Tj),"LA@M&&p/'*!]Al4N,cgg&2XI7*)N$e[!/YahO[Q7Z#]AA/dF)/Bk%GR^X`M>ApJ2Z"J@+
,*PgcdDGjG]AKreH(@U1E?ln*J_B@au:g4XAOsSohHF_pS7R.Zf+tR[s[Dci%pUONBj:Hr@,X
j^U/f!EN-Vg1^$5X^YlheTndFqDPVg"&oXj-B@V9DK0S!pmr9OQc+PR89ToJ(Yp7#YXo%b6:
+;Hk]AB?TERN+$AcQQ@D0iY2JMD-R_!r]A#0O]A:6BMl)&I"PQ77RJ>(MAl&IFdUo's?7n`GCW_
hYnj8Rlu;%EgWHD.PGq989F-;:E[<B$k]A'I.Tn:5DU454MfKHK_G=-cebGIRrrR9?R.H!)'j
Hqj[0p-[3t4nN%DkJjA5?!QEM8S5K#k6>P^]AAPXN55msn7:DlDVEIVR9X*[adCP([ZX=YgjA
o@ZJ2iHG\7:N_<Ff-\;<$?<eJbt^(6IHm)2L>TDbK0o2#h<K8sek+r=(9r;55pl%,Kp3j!,8
DU[Z"cS8#cg??RoE*Z0!ig>$s#;Ajj4#E@7()Loi:*k0Gp3S)4?!,Ug$79RTP@bL>*c:0OPj
Ic4'Rfp70/kH.e5GJ0O1HN>@nWAtP]A`M6N?h("*W17!X-_K=Ig<Qbe4afflfQ_qZ^GVq?jH+
Ktsi;M3J1Q7aQ\GR.3!*jSJW?_B5J@cTEl)cqql\M1HMF^8UBo4\EM'uhVj<B#nMTAG1KAdU
DND"EOj38C#b]A)g-mSh!@3V5d:l]AQp1s?]AZFWX/1i9:t^"jF\s\,SVaHX.$_1rJe3;Q6bN.S
l=o"5?q/rVdpq0+-<:&)e_p9)1TXa3+D?#!F()Oer@YOEYZ4AL$Dl0cl-]AsHi;@>+iGPh\[g
`bVj.-mpb@=R]A3?4(FOG*O35+*:8>u7d0(0p7.hCFA[?DP*VhtHd+Fr0d3a^uuJ[\"W#b+l/
]A;WB:L);4CpX7ko"a,O:4er&t,nNug3au&fO^AHc%ULoAV%h=iAl<7j6]A5@ASE0E=:Y(a?Uk
3OtU-FT<XKB+k3hZAS8>0Ft;$u\"GgU]A"Qe+Ce3[C";/N*pf!8?r]Al^HHN+SO[gb?,DOpQbn
-*AWWDqTmU"61,WG*i945Ua6>'H]AmI5j/bEmGYE-3[$0]Aa)M`RSY_)@L7koi%03G.i2Qu^./
l[CQ#oOO3&ajnSR2p"ME/&nMp>ZP,\p]A\MS$KVo4OebaR.Ae;P>h<c7/YoaJ(0jj'KKGLij0
`PR$P.r5g%\NQRXZ?/^'MW`hqa6p.*Wjp[:7X4Gh/S$Yo,I[SX/-d0u7J6WVrDr\#W.Tmqe6
b%"K0]AGQ$G)ram]AG(.iX^68u\Wf/X38,XH3K6P)k6ZD/qJ.p&;"?K>n\bt(aK((4dp&pcC]AU
!$$:d]A[(kj)iG@c\J?,"1JYTI;Ko1R?r`._/lNr?>lP+CQM10RJP5.mTM`>S4L;Zjb'"oqb)
,-nUaT#pQum%mP]AqNaYMkfG!:i;E0XjXJ2CKr4JL!.e'sr<\%)rt)aF/0PMX\-2JN>FI#;=R
lKD7I51\;E_;o&b3bEYGH=_W*CfQmB3qn[h<8(f&A@F/n+*aSSaVGDYpDga4o_cbEVBO5d3m
4V"Ge%JUk)WtSNR=b8'\I>HY?$)[Nc%1u9]AGJ&gFE5G8^+">_?m6/1;'=7n8R%eQ))UQ/-C9
2l+hi#c4-hR(OhlT38=iSLHV-PPRn^SBr60"oh]A-Tqt)>N=)V^-=%FtU_Ruq!L6ah:QPEI5I
c,^K^KX2-2S_\)i4LM''(dJRN+MK!o[_P41mt<X%(JUI=ZBE\o;PjWmZP-BRDYcalR[;08[n
o70?0UnNUp`ogF=27O;t8k>Hh94,NaBe5Y)Ztn.HC;>/k?7U8apNIbP$HAo0^(6+"HM=3m,r
`;*B.*q2]A=[SI8O[Mg%*1%<*)s%"`D"2(hD^jfu&>lC_ng`H@'VoHj/DT?Vs#U"Wt!XKp!!!
3EKJ,fWV+FjFn#U"Zt$dPmD58^A<kAEIg+Valc'_@$5`atpk(PFa9I(mkSd#Ct25AFGLUV>=
`6pLlC5QjJ?"@PKX6pLlC5QjJ?"@PNQ582Z)PkLG.b>8PqjSHR1am.oQDuCU~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="286" height="348"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="89" y="0" width="286" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d6b23a5c-88b6-4adf-b325-7e28e43e0a14"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="5" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[266700,1562100,266700,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4038600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_tab" columnName="AREA_ID"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="1" size="96">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="CopyOf当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="report2" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="AREANAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_djg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8nX:;YemUVMh?^juK?CS(H,/MW8ArR`\j/1Xg32:,#CPlBjPCjYU;cck(c-kqZhlPa="C1%
"Rq(Ikc.5tER6/LXQE]APn!^WsC,H+:&tN4FPO33.H>5TAT7Uo>#:ZleQok]AbYCcpU@>T"[1f
98!cGl5Tlt\0D9i(!j)H,^OK@"Yu"d!^&ik/"@_\Um$i'Lkn./P&XgG+*>5Z*S,.FobQ#$>>
ON*K(D[`k3#eD4:\j"bJ,!^M^rKTU6FMU;;\.DCJ%dZ+mZ!\J*NaNlE*XMlI`de%SZnuST.8
iAUs@D=_h3;IrjC?Ie9*Ij=GEQ(#K^'RmGG_Ld?9#6JFj%X*^]Ar2dXB<Z908%)#<ce(16kt5
lW)84<sp@km1[Np#MEPW%C"ZS"U'/BYGWB1`Ot#]A!K6J)fJ)4`!c91cTa2Nl,YtI<I_sQA\b
rA5@/O1m`H7in=@okN+OG<MU2`TA1EshRCb!4o0*?c+6OBI5k2o.G`CS:Ki\;S&O6u?o$7$W
6JA)D>?r"#_rd2fdndS?@C@YXm4Kp$InVQFFoU"O#jF+JGpL82CHP39hCnE]A%Mj&Bf'bQK;h
$'o':\-d'O?MK/nb<;Lhr"nErM(5=&!"1S^)aZWlX#St%m9h+Bl,CqZ.:cT72IN[Rsi1fBa%
]AOkmmlQpph/fO\PVgYcp1b(;q*.]A\IICHftP2.An=_gMk.Z,Num.Md;/GWX3=D,UiFZ62DaV
>InY[HckAlr`mfn[6T2ro=Yg\`Q%4JT\L-*[M$:12gXVsB(CqZO1FR9EdEY+>8!%o<pNPRVS
\A!q7CZ2EGq63E:hI.2.9:eK6t0KqJ2gEXg5ooJKngb(goX>(XojAgGDc[StBmL%gemB)ACg
"oVkN5p*(sBQ[S\P'lAKU<1+*GL*K.<*5tma@M5sQDOV$me1CFMAJ-OZbT:?+UPD&aX0>160
l^"t6JQ-kFpjb\T,Nf7??'SR:A%Uo!Q.HdSQ#.SDK9MnB+qRX=2C]A&0u[_;U!=Zd</5&P!>]A
2IMYW-YTS=8#?MmAMd/K4rHB#4Mi^!T%7ruMo:Nq0.^l]A&t`1'^Ws4Ja4Bmc`n"=JV;=%M,<
fVO"pUd`g\[j+=;YN#)%!Q4iCe]AK;gd&ns->q6J1L;!W!56-9,RsKIt_RFTGJ[`V'Zo$gD^B
8<^C;sh4A=C7d"j2ok*\pG#n0i/FDcua[J<"q57Ab\_G;i'=DWC/C#:P!c>^T4-cJ6*Ufa(I
eLH@s`9Cp+X\lYV,Y*s[=T.^LJBKneQagGa#iqG%F;M:ZsmjZ">c+aq-OsT5_bQ_]APEuR-<9
Rpp6rtAN5+G5t@o[qh-cHuC2a^ZUef90g[)F,p<JAT5L(&qP6$rA^UpQA8!_BVmAY4nU(Z&_
Q5*WG>?BdPbUB\./M#na^Rh]Ao(``d>.*Qi(4K*hSGU`K[,hb>WFN^W6)pjhg&2@5ZPLPGoYA
<G(EcAC!jX3II@R;m4tR-VQWQoLiPRIYLj2gINsC331VJ?#8)%:C2:8Dm5:$#-%S&_%^$%h^
e&l5^iA\lP$[iV%)X*-L?2eqEQI;B,b\KVW%;+i6.AD8'(gSadR/+/MZDa(%,l<^E21jT[kq
-3MLNLYP@j5_(l(r?)kpP_-%;7P%bbma]AAgP3'fA[=VhNgo-#nDiZ!HGB9STk'7$`96OU_9'
N;$[=e]A'-Ve8/N<Q=i6,&F7?Yq!]A'8*/&V2&)q%F&&<C(nl3/G((6ZMS1jlm?Z0PSjjt-\$d
8ek!"lLT%4QgV<nGMlP075,`#lBDK_?[<uDd.)sb!JmmI(3:.RE>8jo4DS24cg?J"hLkhTXJ
k5r\4C@Q.7&ol4Gi\s_=A$1/-LkN)I3\TNsra$W97]A@b^%TL6bV?$lnc,@ciBM;#hedm!_>k
9YLm0?lMAU0apCW[#sqW\P(+,P'd(?FAeF&%$?$-VC@]A$BSQAnFO*L>->IR2R6&0j.@/.a4h
`XX%Ml`>(Co%lPZN`dYHZk8D<mB1*fRm:Z?CYUoOP0VmZjKMd/nd_eEf?YDOHWXrfDZbA)P>
+QI.4`%jHK6scDTW=&dA0/Rs6h.d[A1FqZGH+6CUV,FiTc0kg8cQD'fT=17M[MHW<S*g:a_k
geQ\B2+L890Njm#_i%*KJ$#4pNVFm>DoiTK4K`UP!@A'B,$C74SDd8gN>oTWXVN8ar`-<7=N
G[f!a2/O2>"6[6OVd=)*aQn`s941a"7#<Z@_&2LLM[`:E);cGC-Ckh+T:0ujdLZ+%oSpU7/l
I01C75,e8q@p/V[/'65d<,Q\o[cM_R5Vpkosb4hK0eIQ"(Qs=Mo))c^9<I,V<P^aR5_rJ&TT
"p#5AFAaKgt[O-N!Gm33rZ5cG_P+-R9l?2R8q7ERjTMfe14!I(JVZ''A+Gl[SGO!KO0D':LO
!2_\:]A*+Fg7U`+!G.F,RsbEj!Kb['88E'M?T?AF`8fn6Ob.hJ0n.l^k*]AcG*,2l_)=rX_LV.
=o91@ginMIURYoE(B?H<As*+]A0.(:&S#_jTJgm^OJhHbTQBY2b<N>A6lV"$>;J%bh$W)e_;2
k!4ui(CUip4EDk;$RtnkY&\nlN]AQ'G\)L^"Eb_[^*E=1OTDs!C\R2CR29l`#S&?nZnZ$)`)T
M`R*;t9OrZL;IaRt8En[!I(k^64r*4<i5E2#eSko?;LF-FhX5W')gLboH+eU;%`VUkp#AEu+
:p`^mK"Qu(8)/!,lE"*Hp8`g8Aa@>8Hdp3b\g60tB&=m4Vr,5*-oS\,[S[[Y/$f,LL^-$"f7
GLQKaBYYZ;e)+C7d+$3j*KU(&!k\,1-VQdAH#UHhB8"i-0oG/RK'NX^hOSQY`79m6YkIE2"N
XrO,nO>5lGCg>f+A!@BOB63BM@TRnW[/"j#dWa@G@NFO;rf;nZLE;`$F$9,C';#;*]A2GNTAD
eho.@@=E6GRBe;,e^?B46f##spG*)r[oc-m5@0ue7Qeupl[3q/;_^#]A(pSd*OThk&^Qqs7Gg
+eK6ffK]A'@!r&baAr2c]A&PAga.>^.X7DU<nK<\O>BmR*YDo2jWW?E&fn_>2S;hO9fsf7H=b5
!WT:#BRbi\UOdNRT2@j$*8.)oq!Qe6S$oF_-[-2>d-=D/07D3O)eplrrF2hZKE'om%FMj<Nb
')hm=ggWAAFFWs."(Z,*;==^66q:6!a.;FHXRVEMkZ7&[3^2<l>/H5de6VF%Y_.7.ZsZ#ZPM
X6BQE[*=s\7fej35%#kL5u]Abbi:2qhc@ZV"<'^t2cgf$W+8Kb%1WqrL"`SrXG+Ds-0^#Ss@=
20p@D==b2C\&C->*TrZc"9C1Une22=)TV0Ec\2,ODrUT&P;VZ;>aUI!cJP2SDq4*$D2i>bp)
.kcR#(WGkH/fRV7&rP^2HB/L)9Rb,)['J_n(qL8taBk]AF_E2]Aa5,c*Q]A'8'mf7hMCKD\[P2U
%A!2RDgirXdc3\qYdN'C4(c&7\.>4Z!a=*h=]A98i0QnpL(5<]Am8+1et(b;D6u+T'?Zb/-Qgg
/bd6m^Z8ckQJo0MeEi$"Ze*"M)1X\]AL&HYNMg+JTnbA(L[;FY??)V#\!^?An.$n.CrSo[;3J
@G/8cX#A%L#j`2:jb0Kb/W;'ScYhP=HrARM.-Cb5E%oLulL\^Y_1HBjFrB@n/N'J>4`_+LLV
99cU9:d3tW?cn)]Ah!)nL#E+!H<&aKeX1i+YgZ^X3pah5qk7itfPcK]A'EV-=j>-"p/gSqNc'2
A5&>k<$pfjS`u*_>0F`m`:)dMindgat9FjX_NQgWJ0I&[QRAf`<tCFibDQc08Q>+[1*.S=%^
/9)*CPCd"AG=chFYj]A[F-5IhVp7ZN(`g.q>UisRYoQR1@rf^uU49Rjql4>H>HJhPVd%.W]A<3
qBn"28CLVDA;n7R:IQK>#C!E(Zh^rPRR0<8'0&)G^AesE8EGU/K-ipmJ2KQgdBm6k2I'/TDq
ZtMLu+S,XmB8_8/(C`6cJo7Yp/W;u`U-QL/)s/<Wh6lUMhrAO=l#1K"%/L%!<c48qHW]AQSbh
]AjV?U,i3>Gheb*@GF=oJW4)5'5.8qbEtP_MQ&PrRo#bPp/QN05Oa]AA3_YsZ@f`mYR'3abd!;
^^/(DGKXU2k+;HXS&_7lU2ToL%r\T1'i)QTo>q6T_YB41%@:ZpQp^6T_YB41%@:ZpQp^6Tb3
q$YR`TJ^#9=Nd`N:;htaL1Hl4RAL2`EhsnJ\;IEe%I/]Ab=*:9Pm[$,[]A:>/9$Bd`;DS[=T(d
SALf4)$)0jStdp"0JB-?i'>%9UQVam"=`.s89J:rrr~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="88" height="348"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="1" y="0" width="88" height="348"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="349"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0"/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[609600,609600,2057400,1524000,2133600,6134100,2133600,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[266700,2400300,3657600,3695700,3200400,3124200,3200400,3124200,254000,266700,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="2" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C2)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O>
<![CDATA[一级分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="3" r="2" s="3">
<O>
<![CDATA[分公司类型]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="4" r="2" s="3">
<O>
<![CDATA[完成值]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="5" r="2" s="3">
<O>
<![CDATA[指标占比]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="6" r="2" s="3">
<O>
<![CDATA[排名变动]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="7" r="2" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C2)=0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C4)=0]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=seq()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="3" s="5">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_djg_zb" columnName="ZBZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" s="5">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="3" s="5">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="3" s="5">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A4">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="3" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="3" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C2)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="9" r="4" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) <= 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="5" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C2)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="5" s="6">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="5" cs="4" s="6">
<O t="Image">
<IM>
<![CDATA[m<Ieq;dU5MB[T;KoHF>#]AdXt9L5tndJ:Nd=,]Arr-8FO6E+`n8$]Aa75n*bl=fLd3(*?"jemqu
a9;c'hFL5.pRF\bDW#n%S,UFmb18f6p4[_FO_.!6?u^WWiG'%f#JjL?QJ01\</s*WTU#dSP@
#"Ne\"XJDM]A019f0,jo/lNplmWno]AE=o8&p^8oH:IdfAS]A.dq`dhU,'Lc1#B4.m"8jjLSYP&
I*bIJsT*GK*eL)`R\.W.U@8?4b!qIqb$qa^M*$=o9c9'Z*SM:EKb-R*l8L2ct7a2J6'n:gFd
du9B<Q4L'q*!LmA$grV-GO"5nJ2,QikT;1J]A+`"=+SJ2Y(j,N*_E'*LS=jkuPGYMeD>;VB\r
<dW&iQd=a7`B<9r&`JNZhJ\(Z&rlN.3QkLkT\\kp]Asr'n_CKoYB#jhCa1e?'%]A&ck,t]A7tRH
MPE*eTWm#garH`k122('KsLOd@K(%\7(OGMu3tq*^.Re#$AkMZO1(&+9<61+59#!s=huBo\>
tP8%<FS]AE$(gCY.?4\D1<9JTWMQOH\EY#\E'"/UAQcke&\l/>630?/:#j$JN@T`jC3!WmcsP
XENB#tfGL?e6/sPRAXj,QR@%OsF]A)C5a5UK[7"uPn[8M32Q_u#qf0ohZ&uWfhPaLR!\;o'Wl
^Z;9jG/A<gLnH=it!+(VQl?9soZIc4'dQ/mYe)7+d2="3k7@uMK%P+4IUgjJBr%nhB?6TgoT
jdFC.._.pS%fH%j16V]A,7K)Xl817M`Np)_[[c.cWc[V#5iEnA3ceXk[EBO+2@bC4tE0*W]A%a
+g3SN&O&HJ7!3%_n[q>JL.ulm`PI"9i[E6]AHErB_+Dt-]AT\!Y1NdMB#+KKME!%l8^Ju==3eE
@6i`b$iE^Z2Pcq@UeW8OT(G>e'^>432SYAE3:k9@!SDp5\:U2*!g!9TXB`G@g/c]Ars)!(-up
lj=Zn?\;d6jWJ^0@>N@fPjD[bp>C%L)J%,FWER_ofV\OVXEj1S%pP>RIa1l(4M`5gud,ck-W
srgb/Apa*!ACL!A-]ALSrJ1.:aK/!1>hbr7F7J&l'bVaL[ERM@0-+#2:F,coG*@[1C4Ki1-rB
K\S<W[X71P@a(T`N9'HO0[/*;/*l`ko=;A%52cuYo*oW0DQpr3_#*^qgOF;gQ5/4`WA_OfQ#
85ViCbf8#Z;s130WUq;@i%>d'sF(0a2*bp]AX\G/X9.##SkJ<[=a4U:jUr>R@ZUVMi3X\g2cX
4V-k(D&R1Oq8moq'($kLDkTP1NK#nAhT]AEa"=TQ=?I5^VG/f]AYqeS9`W^_?>ZQLb.?<:+;]An
Z"AgcJ5FQ$Nje''=tiMVNOX5&_IX+B\Uo<0TCIp8R`nFDci;50iHZuL]A4r]A+tcL[Pqui@.Uf
SSk8dr"M7;Id1[dlgiNcD9:lA^DHNf&G!jEl3Oj'>$n8n"Z&3s"39G]A9plDm]A:TM`AbglD,3
:Y?!ES0CMALYnYE+Tc<EW*1HPPu&Y`0K=:u9,!Tg6`6oETNM/a'Lm3km/7_cQr-E#^btkF]AZ
onQ&^:NJnH?bqOVWq)"R\;7$2fl&&M@PO>0.ODCR]A:95R+DVA0s<Gn8c"[U]AS)eqeZ&i&q(%
Rd8*L:XK=2qOU&N$,+KC9^LQ"TpjfN,S]A]Al@Sj)si(Dr6uSh?K<N7>F+5ogen9`(OG'2<'2+
/_U]AR?3626LDO<3'&f^:0sn]A<jVUYd9]A>>)l9'UZWDr7Je@*Hd+!*,fO7T<Z:DD!JBP)(NVe
AcqUCS]A)g0ea-826Fn9;7blNgaikj3P3dNJm0$jRO7c\D+7$a-^7Edi(Pp@qu<n1T!K\9e^k
b=KnJ_)(>;s.;*fR1B]Afj:1O<M/C9YlqJ?9ms_ElBuWV7,OmC'NGfBd%8P&mD!p@_F%fJ$2_
k=>/&5BrC>G;Gr:9_%KDV<R/$T^=`NFGuXJtuRqmK(O%\W@XCW4lI,.s.M`.DPF!fNOATITR
,MRPM\X<A_X[L>lL=2ahP=((apc+k1Ni1<lH8Vc%`TN9Pr!m@8L-p9h-Z$VKeJdYh/GGVbRG
($G.5e'g0Yq(NTmfRbXNkf@HND#kb#$DhH*9"%r&:FC\F%#Lt9fT]A3HO=^Jp0Ol,>a^hE_='
luE8'NNI<0K'A$t6)pH-EsP3\A*Q7b9P!d)i"EmspSWh(+*.UWUd+Zq[#$5e.VnR/?W?q;Vi
[&^aQ&0$>!`r\Z).<]A(/3qlQSmKSEf]A<9E3OeH+AO%H>R@fKAaGNDF*Xt]ALi/X48-b1,!6EK
'g!bKA"3%&1^jfns_k^0@jq'HOr9k5@%UHlE"!li4b#IAZ4JGc.B]A9?id7%JYJRB@c-gVdkT
G=11#fCEA3/6aqV>4hEu`pW^Ldk.cukjLmqt/Zc(sr!VU(n34C#C:lWGCW@[VGQ#E_6I$KJ@
(3)UeecBZOC-IZ7Fb>>Kr2qrVVC^CUK/<QT783#g57't'?epAgI%h*_R'7@dWJ!'QGQBSJd]A
Re6$$Im/;I!t(4trR0#IU)rR9T;+Ra/sk4d)5m+Y\&jpJj1V9(*J>1l2*'4U:oTn^uA;s8mi
YS<N8k4:4ro&+IKl.k1gL]A:".YZH&3g%j.)o6A4JCU`N5LVY>=QEV[urGYt#;8@+7D9Lmhe.
eB\O3cS+qF*"[;/Nb5TVW\Gea;8[U89ZDPuY8(o9G`V0lVk(R#lKp`"-M)^hK]AT!V9,Fj\'Z
'DATD69>]Atofl#q>2i#tq]ATqrlg%1P!XmJJA4+T)TW'ik:!o9Tnr%dp%8c6TD.QQXP?%]ANGM
.a.-2N-<>N[`*lN4LC:<]A0fYrj=D%?%XVV)ArQCW%)^AobSOr;"0Zqaj;LR4B&DZ"EW?NW9h
)LEdnN>HH+>e<3o!]Ad8m)!bO*_*:&SRq-#ba.EpCuNYBu-gb04\.I6jm#r:&+YqY^-"[Uo-*
nQTSVI?K]ATe@S_haJ6N!2kAO6Z1.'0,suDFq9r6qc=Hl!o4$NW8@aiS,&nA^Mh;EF>s3&Lkg
KtOrp:6qV]AtR;\E$RZj%aHNd:eZYG+ueTacG`6DF-#8g#uA2''X-t+$KWIL<BL^B_#E5@'N_
t:JFf-6f^[,ce;;"4S3FPbLG6*LG`V@a@/UlG#>'$=*#uO@qWeoZULu'4W/65i=',dV^fhN^
H\?tH)TD^[BQm*9jURh<aj5u;/`sB974Za\bhF6U^m)0G%.%TmibVZSL60I]ABYMbG8qF/llm
,#7D>Yip2^k&afG!j7\BP]A]A6M*VK?FlH"2Y+^W"*Jdh(]Ah,0m\^jrf`8a5.Xb^(Om*m7P_)H
]AD3X%^,9Y-j`.&?8R2Jib/:3)Du8EpD4Hi.aQ]AjgEDJ?GNMZF;>,m4%]A\([M?Tmg1*^#7jVE
[@48kr[H_SB.(W1o5GIKWc9"UZpb@c\U^l1UP2e]Ahg9@R5:'9F37me^K=kKmnRm")V!ggjE&
]AiopHDb(1rA2>=amn2K`g*-95Oh6T!ln[kJYjS&CR(*`E$YCgARc3_5;<**@;<mFH5^5:u4#
G`uB'c4\P9]A+D9j)2SbH!7%86'T"1*q)C,W:+mhf\V:=8R``+&KrnH-cHr2kPgKJFGNh4Rq3
PNpf#G!N?ULF<0NaKZ'ns.;P)o&&*mAj,=aY>>9*JcalP@"Q%!C1)o_Y06C]AR]ANI5`kCX"gh
N=Hp?F7cA0FK"*`h1tc=HB439A^BcbCX3]AV&>s<;6Bo@R0B3'&fJb*ZU@>J>^l[kW-H*%jVb
R:`@F+YB5>n[!3XHbl=Y64YnP5L7]A*g4m&SJmZ)hmUI8/:3rRu7EEGE))0jT+E:RHM)k:+rN
XT>oJ!f\UfFDI+NW,1L.&M,PJ#%gko@0O7#5p^g;M/OFK<GrDP:F-;7n$-ilId&X_&i+[AQV
SOIbJLsZJOumPKY&iN*XJ7?oY>b.+8nM=m;uC!L1Vr[:[2<Uj("GR4g^;u_2NEi4@S9[#oE#
;r,?4bORCF`L"7CXH57"D)_Krm6hbH'Jf[KRD>1PegM-oQ*J!+h;<1Ybe>3,>>G&>=E9@'aJ
\,q)1_SJoBOYtM,KbZR=HXA#tef%aXKPrK,H;Og6/e_i'$bA5S2cE7)SKL4qe>$/B_%sK<>P
I$m=a@#R4:sN<4RS'!U8Fs-7!-,3kV<l+6kiaO*TZ@YES?PE%?KY*mJfjmOE)j:hpfqXIQ7)
rAoEns=#juZ>Zfob]Akq\66Ed`'5Eb_Rr:iFY$R+OL93>K>C"_d-8Yg;(<OGre&'&5Np^t8I=
KJ'j2G0q.PI<NAI8N\fPFJ1n@&_1#^3FR/STc>;)#"djfFrom#>=C(d/,cAIOkC`%<lG*rk6
A^]AB]A7Tp\3rr>@A\2PaOK&F*!*-S/aL3TW)G[qIIAZrDBaIB2F?/Gu9I@&88Un(4`\,@+s7/
gFLA5'K"US_P%Rn1\@XF_aWlf_N:+d;e8KVD"2m*IYJ+#-qcC396Y/Vfg>"Or,n!('@9=Pk-
\IO_A*#\mW:#XnZQJb!!W]A-OEBP+]ACYkYn%H!V/C#MW6lr20WA3,8hKRfYU7hdhNJ`Pu><9R
$[gof<dP`u%K.Q-o_^6#D'kW-A+**)S-@s;d"nN3qA^B<Qm;04]A4nHJB:Kp%5Mma\Ic5.LFk
tRJ]ACs_',.9#N-"<\'`I04-a2p7hAIETF@"4j8s]AL>p4<U7!eVhbb!MW8N\7uc-P8(uAL=W.
'@DM2Wn9P18fe0:24l@.-c'>b8GX90ETZH`qh-F03m7_@?O%?jBN]A>iR67hF!_Y#!C+a8Oe\
d-lhPbm]A#hLP)?HjhQTUOdk&UCmobc!hhAlPWhAJWB:+Gm-]AkY<)/3_'Y>%c(Fa3dp(%U3;'
brY3Y;BDY/h(4(#FRD/LZN`_em4OJ=gWVQO"D2dk4qfEh:r)Q==_tS&R^5,<lqs7?p<`O!&)
re++,fE6P?hl50]A>m@k&0hem,_-JgE$04LJ`3e4$'3=#4./&F+Ik(=6*atLXuRV$KB<-GBqK
DkNB5C,5%^s>RL@c6gA0%")+b,T,Tb#h0=;tK1eM.:qBLm_fe63?b'_pEPs$3&45QI[P8p-`
t#]A7A3H?4]AF"n*RD>*o\\c?uajCRbd4s0+t`+_3NH,ALfP,dU^Y5"mgL:jcsguIrROc[&dbu
3![^R=ghV"5:SeXMicQXlTLnr/\m$;=>rD7e'$":5gO$gl`V>gI%MmF)3rSHf203<(lFGC)<
P<>-&3sug`.am/S*TpVig#hD<c&Qppgr6P=;H<rg<#pkAT.X;HS3Z3!A7JFo.+#L&?W=pmqF
phl&hW@)bEB%rh=4(!>o(r6$9sQmkh%4S>Kb*Y_^J[+9kIOrHMnSj!'f>hBjM"gXZV%oM]A]AU
n(0mTdmf1!O9r<,@\_Zf$>!FA,8JI_nf[YQ>NME'9&sgAu!J@MpX"-=PKj*e0KLZ8l\R^RN_
?AZ;Qiqa;&7U!FJMehm9`Q\A]APl=)ZsEo9FAaD-]A3uNO$3LO0X2cXEJo[]A4&n%g,LIWOJ"1b
L!fJrD6ij_S@sgtCS%[;.<<tUf5qd)\G:bHD;AdZ1e+8@F!7[S%mODN'&g*<\Da4bBVCq7B5
9cW7ei=i1N@f_X(E,tX[+e&'62cW5Og-#kOCX)r9Qj(W8ioH+5?=1P1'#1M,b5tYOq,0O@f0
giU5&r.<`XL;p$mU]An%(oepfL.e,>'\NYS-p[`9Flh=$9FjrEd%f[X_&_\HBeT\H&(?-Vj@D
i*QiqW^bDBd<d`)C8K*O,;_$LDSo>ij&[RmSb`Dft_\1q&t(:1[7-?o!P\pJZBTaEZL>9$Mq
H7c`c>;iC>6>gii;VIHG8[;"=\`%qnr\.7_/+^.58)9*Fjsgae*P$iBI_em!U[G'i4;N^E.k
Ep]AkB2N*X5QZsZrXjgd_<4(Zp<ELWn+0DF?Q2&i!Dc%9Slb%m*5dH2Hjo:j)V8*cb]A.]At(-X
$.RoE'5Sj32=RrONJ96:0U'pFXMD/fD9MV42[Fl=t&Soii-gEiT_1XYK=15?-M\1.rhDdJ\Y
Hp5""+^T0@n\X0)X#i3k-MSlu;:&/?8c&hH%'sh!@DWLIBTqu=U!Bc43eui]AJMdd%PB)QCf;
-Fd\-Q0rNs(Od;C)l2Qm5fQ5>(0/`*$#mA<]A==%J8A+\hNZpLkS.'XG5pR^9\:0W(TIq>8Z>
p:.a`B:oLli4+Dn<=\;jafiSE72WYYp`$d]A_O*3WdRP'U/%"h#\p3J?W^\(3TRpJF.IdjTE3
LS<NTag(r/T!YC.T!P[j"=7ff^&epRCnn&O7r0q(PH<\W>OHRu]AqJuRQ.II-n&=H7m,*YOh*
R[5\_W-H=C*IP^49Nmr,1'4KlG%OE?Q7R5'H%UUqE<F9N'Bt!g"BMFM:AXSJ-OagYN>[LmJ&
L_5J`!/hJ6@+[5JB)f9A&0%FaZKqh+p'8\@/BLjlarOiM=lKMT/c!"9)I@147d&NpSLL(aPm
3K@Y<`u$]A]ABCdpgCkEPWp@Y;VnqfKU'9phh-+Poiou'0Q-$6C+H_FuF!(TIk9PM]A(0&=i]A[X
@M(IXMAimVaPZt]AG!s)&(38l%>a6h-UUN]Aa=VdX*N!dI?'(o;$:U^P1u@FQXP;&B!AlT,+c`
_<=^3&K*WXO&NG/k.hl`Ip)$QiC5knZSHT,d$0@Ndc'onYQbHcZ+8E:&bc%##-'HGA7S,E=Q
1CmXc1OH,SJ\KR#_%u2c2-NRholTk0C,M+`skXb7BYb=Fr.?ioL^lI`*gACOaiUU/1>B0NQ/
sj2!)j;at02Bu.nY-MG:\F0."?=A=C8U;d"jl'elT(glroMM2F(_9>lO>D6q:Kk,5O[Is4-a
]A\K.hqO@YAAk>8:6UU)Wl88%jDJN2/ZYi)Z;aXHZ+3.(-%KjHA'tSr:XRZ/#K5M`Mks?<e[&
UXBe14@;)H0D9\,)-BQp8)3a#u\&$:Cbpl"g)rR7a.fMIQl6GB`+(>@$2@l,iU5G9.c<NYeE
C?ek/7'>)mpQ_qC.aT@Co1Yi:gc<7:G8pbE>0E*iE\h#'-[VF-1tK<MW_T<d2P.lY37'\LG4
"JO\#qd0[D2cY-OXufU,WFYipk!'JiIf3:n$3B<2&"l<r4>3nr["rUYb&@+:oLjq8.UY"l6;
L0]AKP%BnKW591(h4l[t\kMf%PANJ/R38UNN0+nh&X9)G8Q2ZFWn;7>b;n$gQmbr=.&DVUIU5
=Bgn@?',q%afjudUt^CEoG>:C<CPoC@;j7n5CL[[1b>JG&_-o8Lgulmd#mQZh3`YYtE-jf/R
1VH-GmhY1k!>SOO]AD5qj<.a'1Ecij+i+]A`;j0W5!4Mo[ajH-MOKb\I;/^J;tf?&UDqpb2"O-
A'Cb4+u4(-N=Gsk+r"?)%U0.dp+cdL2Ykis8=S+KRJ:E?9/Z4ng@olj-n<X#"k,11&#@CGGX
J%W]A8LIIPN8MY)p(o]A(TWBKSM1@nrB,?8M6Wai1i8Y"-FYi?+nG'$q09A;AR0?(rPV7O!IJO
Tfk/m>pH$?)cg3!eBH]A9?6l#QDM`BPh8$m18Wa'Wie#/!K73PV!H@"JHlg;eNk4aI6Dd&MSj
O)AfL#tuh\t?4I9;=X_CEJ%NjFa5En55S.4p[pSVe#UE/41^O!l:2C&;&4"cf)%7-qF/00a=
IN*E;fh?u'eS-2GRA'aTB2*6:W4C^:QrcU`;h,R!`)RR>BOel*O4(j^%F`=m0MZXp?dh:h4#
/*?rHTf;rR0`<d(pWF'+J'=Lrnkc3k&:-au#\KV33B`$&'-/Lq1169X]A1bjQ<7j_d?%lKFQQ
dj`'g0\sDA'#dHdjZPb_lt59>_g:8Ws?..<\0dC"nVSj]AJ[ue`]A3(XQ9Y>=9&,Po8-R[RJSl
?_#9<)%5hGA3p"unr=8~
]]></IM>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="5" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="5" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="5" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(B4) > 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="6" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(C2)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="6" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx2 = '总客户数1(户)']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="112"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="104"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[`-c8uPM*6"NmOLCm/SUI'bqGpcpg9TW!Kb#"VmF.5mBc3&-eO8OuS$p&mJO)$&OHn6^YcUTH
HX5LaWfW5_K,G8g8p6KM5`^p[)kbF74=u]A'HHp_mN`ppUbY:Wi)kgg2CgLT!bn1[q=qE3B=W
m-\"="?;>C'[d9X'C?[D([SJ",j<=q+SiCm:&iDHn?a]A4B1LoCS4"IH%]A_"@2md7-i[8l(M]A
8VW5a-ei%2(69>Y[K"FVs;1Rm3[VJj'Ls%)16^Tq5Et?NRgg49h%[=<@arDR*u*Rg"DR(PcY
Y.GTeW!l!TPa[tfft6LC9t9F*GXBMsf:/oW90[cUVS?qPIKG'EI$[,g:G_fU'r$7I7'@$G"U
A)CDqf6$]A8Z^kM@86th4_`8Dk\]A`)%g(bANVl5p"UD=?C&uV=<*Sr(ipQHAmMn:JU+]A)O^3W
7-`4MMj0P2t[f-3Tm;,eXgH6J$B$ZC]A3)kGm/dQTKMli?ZMDaWcaUGa>]Ak2\V?cA+G8B>l28
lESDBcWkuXL59Yd)HJ<ptYZagGj5g$NW(B:%RG<]AP_lkPt:PpQB[2crkY&b?g>,[^,\$"![r
?umUFZ28HW1;YhDrWsqM15@mCgERMW^$3^+bq,jR('[EnbKI-2</@cZs)nK`Qdj4s0=)c([0
mjj$j>+SLKb`LSZm:DJ1cCiLMRK"sSBIk,MA2r7=p$QV4+)d0+GZPfhV"a^%==Kg^QDUW&3b
!iN3J>"5=g"!+%K3\-g;Q?;YGFg,qhVZbS.!_F.iTd-,"NOr/Z4GEEQr"J5pEMMA/m1$b^f,
_:><@[loZI>Im*hFZmmiZE+H:-ka2/Ho?VMj`U[Gdc2!-*A<<,U^0MsKd('E^R,4""Z%o3\e
(^g]A1<(J`AU'D7Tf$/M&sRG$gsJ$P%^GjY<CoMk*KIaGpcY\-^gbMSqm>UK1l@=t+K0/pr1W
OF#99NhZW)Kn<]A+s66n)iTlK.4Je[YH4!Z#-hg'#bdk.O;cE'^d*]Af6aO38E4lMX*)T"&#tn
J55i(XhYjk=T0RkVWF0:1r06qJO=^&c-5P%chZMLnu7CSu`P\K%+`#@>.*m0^9XeBlmEp=k&
@?jb<"<'5+M.AW=Te&5n(Z:@-?`Jm>bS&0f<VGl2dj'VBb3<"r&njq#7XZk8!!^0jZ9&6?c%
iN]AM"#[?^u@k4k5ic(=8;rrmeaX^mlPI23W2JU#eB+JG)=b.F/8R52:p,Qm_5S,VNfH1B[f4
W;>@Y"PErU4je(oOE@`YLEQZ*8lQIHon8LE2`((i/ff8(!K'!WB=$NuPhh.+)n8)[a)4AV?b
=n3pp\!Do".n-oP%O,K&ZhHc5fL%F\>&lP\aM%M-;b`fGZh4!JRpBa5l>Yo4_SI`Hr!VP8?O
>UR<3,s3g""!9h`!59#^j;3i`:Vb<a&)'fr<JKBFHO.SNZ0)[N4;NjP=$E>NL(6+LN\:)c#D
LVAK<m#@YG#0jZ>+Z>cjo#^ie0fkPh7MUq.AtngD"3!r':SD!G#CZHm#X!^S:9USdC]A#WOJ%
V(N,58`uf]Ac`cG*OdL50N#I,%QKAj^]A\j7dGA:\4moQL>uCpJ[M'ORW#km+^;"PP+0p<eA_E
DlVV+T^&Cj^oZ?B[Yn&nROJF]AV7`;.-kiiom^Q/N\[WR[?F>W<gIo5n6Y7'jt_TG<#J2<4k.
>qfPDYQC6&LlLPqC+_LTRZP)h[1!kNA\o?>V:WnIukRYA&3Ml3nC+bI"9s)Qd/=j914T#$J.
Q,R]A^s=ib_6;cPkVpXSHRUc["\^3#VSaQ*ng-X:h^W<jXIc'iWFb$I:]A?"/5pL/*(SbERqA[
!#7&6r5=l8p%i&HaStm?jOfB`?=;Lt&S(pT)<A@mqs&fT]A\):;W_i`ghprQR4U98P#Gp,<:n
X.H?U]Af*j#+_VHjWi4'@ftakI>TYFZ80/G^-6e9]A#qh"!p!#\gXG$3q_Hl@gqD_Q8>@:Ok;i
]AY^V=tJdYq4<bfqp8><5RO?8K(qe]A9>4&?*o0%&:R$dKm'S\HeUV2EVU_+6s]A07^N!3Qd$pk
=gE="bEF_qtdZVB9fTL4Y_utD22P,)ANA7M3_Yd+p.;oIHBfUIFrH!Fgdg_6WCph=g9ZWrbA
N\#Z<]A"<;:K\W>S<!_hcR5:?m$<b/-Z%%t]A_T@qmZm@HIP[*@ZY%r1QC2DH&oo)Akno2c>hu
p5:A!O^W)g-nkrS<qLcG%jGA<R%=FR\A\THJ86uIH<7'm1gC.pf6uU-?j"=;h>AtD0#,q(7"
kh_^H^:1A$AN>E=bu4oos\:FsFHhpQ:UmF'Sg:l[HE&,3O:BRIGDBjF);2AEjo&W)'#qerNN
,8t)u\!^Ojnjb&AC9MB$=2smG5[>3k7b7'KTkpfES^Ds.r/s`C0ksnJI^uq\%J?oBoNd;fg`
Lcso-P3>'>7<a'TAlbXC.?DC!c&$bekY<uEX"@Uh<%?6:&5-RO-To'#Q1/2M@hfORa$>`D]A,
"i1<,lO>ef\3$lengn<)1VKX]Ae-RN;j*H`r/$!-N1^rnM=7<#R#j;l6#Y+koM`^P0d-)[]A+k
op#<E),7;OP<X]A-S?;"eX#^2K.*)cW(rKhBW,Or$gL<:!+RG[k`XQ#U5/mXgZ)]AUL!kPQFi9
M=K7SaB"l<l3Y4ecV\S<)S:34s(Ihbu<0U^-G&^X!r?FAPNJ84[sO),b0HaR5:LC'o?ZY-BO
oS0uQVE%Gr*$BgT/3-?mrW':9nf$&0n9<B9?5L8JU,iV.VD_2?gcUTAY@73Q8m'DB09D!54:
63B(T-`";ElMq.G_RS6/C6LP&IYkPite]A"G]A(Gu?M1faq+=$i9r%(%:q.0q#\j&-85V"h4M5
lBKfHPn]A`B@j=kW3'[SCbR.=oW>dUkq1XNJu/CSD.\/U76`?8(1,3:`Q#=&Q9iP_./4Q"e\p
or>;2-E`d(Q.SaVn4ID>nr'Ga]Ai7I``jMb8q8<P<fk"i$W#C,4K^Q-om*,n!]AQC44m]A"NK1l
RHm_cF"Ck*r'>>(EN'ge(h`jm9l`RfhZbrcGe$gRN=*#12MX)BqoM@!Dn9-W.UPm#lJ<r3Y]A
(\7h<lhH66b!LcM)gu@$\35A$$BX4N]Al,EDFq-)AhGC\t6_6(\LH>X2B%lbgcCE<fU%Xol>1
6pN7pGq&\G@`,4Is"h4ZI[(:+':WK%\77P5;n3TDmgA+g5f*S#/sY^!_2g]A`YKT,LXsHNS'C
\bOC3`]A^#!AQLZfRdpfAth`ordT1-t8e0018SB(\a]A."9J!q91AZkPbtdir,[93t(:Fjp&s3
1qJ8sdWE-t:Fom^n6bYMIu$PXGPfQTF51:QRasJ.le;4t1\G*JiO\ruCO&:uL\S4i`>^D9CK
3YtK@=uc49YVGF:Enb]ApW<@d^NccC"&jIVOQjZ*+#U6`'EhROu512"[N'_rZ"t-VJtni7\>X
;hB%Zsf>\?s+>$2Q8MKWKR(!,EQtUJ18s:*gqB#.'hJ!D:/<`92?,/'Y_P5r9*:G8SA2QkAN
s3<R"3(u$'HbhX8l>s<hAGQ(.$RWE`Y`'V9&>Rm,cm:m2TkUZ^4\U0a2QTH#ZE[-PmY.2+sE
NIT$Ki4@Y(]A$H;5?76ZA0bA<LJD(s5UKT!)`_WX@S%#4<A?RmKG"2n$)NamgEJ"pIB!c@OI6
)$P&$SiF2A[(RQDlgBJ!+n4umgg:VEM*$q4_nPjX[HiKAq$jbYa!84b!)![Dg(K='\7SmeX!
B/UrcBoql^Pq%qHh<$&2I`,_2I5oB[/e,]AGGWse*N_V7kD;9H=EWn20qGbe^#+!(\B.KaRQ:
I^pQ4[+;%dMZdp+A`RUK]AU'!+E.Y^U6l3DX!DTmt($^(nrVMRSDlc';'9<lm7l)43)i@FMQ!
_'^pB0T_Yo2EJTA<B+9T_C1j<`#X33%En+I]A"R_ShbCcHK9m3R?T85=o+ReZE!/&2P.JnQ-`
f,'=??:5;hn-+#W_G2ch06.p^_o+3,JPe6ZIEl8(MOW5Cb(3mUJZ>/XOermVO-5=rYl"V\=6
4d?Ylh6jSM_!\TGn#FRBLbdK9bb[hT6NBRtA;k\U6d+'98l<;Y>g$'BAq;Up:MCTg=o7s?VP
_CdqU)XO0Rsp,Y'E1<AlH:,!j#f4k,48LFm'6QI$lAN/0bmG#h]Ahcb,G0%A"T5c>GDXe)G*8
joVXV26m-43V`7-MKtm/\^EC5fE.J!B=VT1QX#Kf1@.2UG`S5^#Ih636Aa<AOKKO5.MUjfV,
C&gj?]A\2`<#>-\;c6R;Q2#=n;orVJ[SE.rO_EHn%*t9S!NnC8q6f+%1?bBp+0m?:47SM-Tr;
1b:`1h<_r&$Zo,:!=hQB?BZ(X^u@-,scaY4A,>SP:,/W/rL:2\TJ4+lP?X^RGWk=E9KQB86h
F2Vuh8-<&XT-K=!a4"Wb32aC<=ubL+)'H.dne(XR:gD!lZ*WLU;Trm#J0&Vk*OahZK:86Y=M
4U1`;Xu?+V?N]A(Er^85ZWi1+D-Hu31Hd%G6Bms9"^$a(sQ)%e5QQd"MIsX-QhC5A>(A5Y3EZ
)*[E%pQWD"$3eY$N@?#WNhMKLLe(tsrTo9hlab$%$V1e8Z4]AI#794pF:PqN\E@`6PZVa+nf@
=>8d6hMB;>,Xcj11,8rjgXk!$asn*icg0_Gn6[WBe0$928agZ^V5[M&U5K"0=jlhi#r'>k:t
s![lbV.KqT3EC!gu3R$Kus45HM=e:!ZB;DQ99_;XcWH@Qah2J:4f%o_W84`<EMAIL>
l'#[?rdD^XA&#Fhn/3<un^eB5,a6dR':2(l="(OPRhZP$k!M;h>G2kO")P4S:lL0o2bu/DB1
i+?L2KpU#FAL?jF*\n^1=MqrtC.)XbbEo9>1*%$?\2Y@^QJIa&hs^UFjd&Td<%D#7k-8dcs)
2rR]Atc%5"]AJLoBfD2^\CLguKnn\Apad.G>AA#;P2tJVKIAc<=f37&,e*WShs6TH<Y&XM@]A`Y
'Cq0j*'[LP-8b*TOh'>'p8:S%>Yg?_"!(,:[qOJ)qk/p7Sj&;9)+S:XB*@*EMGIaSZX6XFc@
!nPm?_,"[>o1QsBp\Jq4H+&j3>@hIalM(:6ZR1Hdn.,DmS]A:cRQbT/csSonY^H*q)"+:i^=b
&Fnkbl=fCB"I=#_;rfhE\]Ac7V\8<67rpj't4WGI]A.H=0E*k,+;T3=Aa"IlY6IGkl:MT&]A#I4
L%h_6O&5*cGqD,n29MgS8CWGpLGu;3A1N&f+f`O;kA3E9p6G1V%_hDj;$YIuE)<-gfmm+<X>
Da$;,4e7fF]AMYt^3C`)#,S%nUsS[%E#6X!&ci9L+TlB*\WCpMTtS\@]A,T(m<2#</Gi7K`HH%
KR7Aeoj^)b(o66[(p'G*8.m7negGFOrLAIk[6oh6Bb?AV$/2X]A3%eHijI)^Y"K'bO50aBC3S
Tnad-F3I,k=TrM8BJ"g+P:dp''s&+j=V2G)nJ\k2"l]ADh.tBr.m".__N;W4dS),YBR*$&nf(
6JkCMGl"(lp)sc[n?"Rp?Yl!s]Ad]A!DDe92E6p+@1')8JEoX4B@2KtkBXfG!;,[Wh&PVI.Ka@
C1<K'H%Q:B,s36k0t>IGiL/CSDGt%f$+SLBpVW)Y-5"ceh/sJ-/"f0spMBfsa05J-UO$&j8_
dRi8Q7D[)TUXM!-<-H\%rZ&V5b'hNf\6!Ef+_XU.oITiR7hTgt2(Kp%:pM#K2X(!_L#cHqIk
X+eN=T!'r/OVjt=t8-NO*s`6+4R+Df8i*caG]ABtG<'8r!IiSQCX04n[DksG5<dl.Qp&eN_<K
#"*9T\BSOg\?lj(e^L[ai>PVI\Be+R#HdKb</<mqT[!2DaAQC[+NS(U!Mo0;a0kS[5`.\Ol/
Bg\iOi)?sd+++Od+sWhs!qloOW'!&UlCJ*K,:NcY1D(#4]ATrX-n*a=#;i[&FdMV'URPpUC?(
HABD+<k&XO509#Mq6YUElIpW]Ak*&8Pe7".):Mn-7,&"[L[*)ffX#o=E&?t&RQ.aa?0t]A_ang
Q^nNgmAuOJi8U%n`E7Z7*r$@/(4/<)u1/IPq83aY*-52qZ$P6%"$Pc[g2X@hKD6Kp9BG]A2Ko
H\VO,DdHp_Uu_m)K\k)7FXi5KT#(SFA5IYh.J<&&bb^R,s60fq1m9XhJkk:0Q)e:DNdN"I3`
1?5?>@#dQ,F$84Ma74_J:?.d^'qWo/4qZncm$r1*>C-M]AT(:6mQCN=O?Cj#7Q*@kS5?fi(VM
n%E2$O:EdBX/t/'q!$b3]AoQ=^cArLW4qBL;4/]AKIJ+SJq9<.WEh[Q%r1@,?2n`<9!/Xc/GlB
MV#,Tc9PC2D"s`hJYc:-/7B2Wn6Yd']Al#qq^7`%RP]AG!`aduBQ&iNXMR&M"nS,ZTID5cKj"^
hkNSGW1Z7F%!&8eM*p)gE)[rVUn=mE9^W`(@:*'=(/'6=O<>QiARd9NWePpX[!'<XQ.Ht4"<
q3UtdX?gf[mRCEJERT@VMZ"3d's1I_ik>^4N;o2(qJm;m*S<L!)Fkn"F(%.1f;)8PK`GHTdR
?^qR'`oBj;jRUX#.*"=l>3r"^r^8-M`#/qeH9JB/BSG)CVe#5#l:(-;>j)]AOUNSl?\7>a`/m
X+-lJNCKN?M$s54r8>qKBO2G$U'r9<8VuWEQ565Ip5!'bZ(:^M>@msTk>gNmBJ5R]A*1a0h11
qC?_<>Jlmfjgo,#sCg[f[[R%=h1A-^EA@.-,Z]AGO$%mQ6#e:C1`/K>9aS`gebLA>XNYS`9Q5
;(]ARsF=`HH%U^UU$ZF0h_Nr4_5nc?5fa3BGW8PF6S2IkCrg-uNPl6YX:b9*G6cotmoK2;FG@
mrq361(-"iAt@p-6OO82g'3*K5f8Ig'`lt>&s.#:K/Ci]A\eT"nOZ0ngDlmO6g,_[7'mfu<^5
*7N*+Gq"!2^-_`+qN9^hV]AR!b00CM=Gu8*%8ooH5Wlp&eW">A>'\+rMcpB3tF?H/Og#3.kQl
*C"t&$/"4kV;G+I+9hT\%G&ej)7J_FH-<FUd=@2!e`7[IVgnVloiIa9AuE/o[3NNn1m`LF7i
mjgeJn$a[Ets43UTA7CXqg@J%htFiU]A75gJm9UoK0jgq!]AB>C%L>[WG;[ug.E(mF@^CHGr9H
9n>H6&WJdR@@#_d5:X%O9DJ4p&$qa0O*E(Sb5mOg<ZP%G;a/%8NI@D%FVKk7]AYE^`GMMHU]A?
76\J(;AUG-]A]A.AcPqJAlA.'0Ynu@IKlA,,C0*dG9BRFf_CI(#??s]A_=$^5IR0hfJ=b$]AR`NF
LH)FG9"U0!>fbSFEHRUcCo/CrG&3o1:SGiFo*U/$)]A7Kfu3-;TH+`g]A*u8arV.A=G4DPO"j_
heqdAe>;oT6+`mF,Y)n\F=f.C;`)+O_&Us8kM!HAF:>hs)4n+t>]AF`\;PI7m)"ljGK3A$]Aae
?&!qdNofI3@)jB`$RUaDkIA$.aImG]A"XgC*ks(nG).u$I,U<6(@5,bIq'*NAB/g=<)&ASFf1
:!mZ:rM#mS3@>q`Ral+=t?&5*(iG2NpTUhM<(8]AO>o(1KhjlhcBYD`Vj7PJ5Or/^Hlg:!Ibj
fF*Ci;%@[hOQM'O$P+S!%p+uA@q)lAY*bRC^h]Aagi!90ok8UEL<P]A+XEs(n6qq6Z;+L>V=ah
ALO,c`fB`^:)ql]ATY%X0gnJp"b^hKq<kb"-JaC/\Z5WDf\#(N8b*KfNa98hIO!ceRCLUCPCf
bG'`A),sc>m:]AA$TLgD:31CPg>n(`2!sHoXfTYYtY/fkH#9D;Nk(8(/gXIs+e08NmV%upXB'
:,T=!IcI^WcCYfc6MY/'6Ht4!1#AG"nuq6s;[H4-h9VHkE1)o-r(+_Se+n!N5NG[:1VSK[;O
@bfgSs>e\pUK+OP3>@"Q6gq?uC$;[e=S/n7=2_lnl#=.,qHDKg2>S*oJ1lI`W'13`0btn^60
qCLR*H2#]Ak<&$gbnq#*X"\Q\,4D:M%cgs'Y^FL-;4$U(m?%bNpl!j,NR5ne\NX<<'<>)nJ_E
e'<ao([LW:_ZP5Ip'S?,h_?:1gGat'%0K3>8LDUt33*TLtJSO[',lh:)J&#UK#p"gtX[7Kgj
)&9f-)Qpif;r2PHV)kiKBK#:XSqXU:6SgKbQn;4cc<N<?2=ZAa!SR:D+j]A'<Yo[.WI!3]A<.s
8iBBG]Af,5?`d@5X3#L<cmkug@]AQc?trD4BcXLcbih#@p&&tDTSf3'k?TINm3D+TPB(\YR"mC
f-+Bt\b/GhL:Oj,@@Un@A<Y83D4?e$g0D1a\'U@%1G\j4#aUK*%;KcB>ft7V^6p3/B3@f7JZ
gOFS3kc]A*ihQ<Mc,P0q?"IC,5r<Pe=^8tY=Uk[pYd1oE"l1-fFRI.^kN;q%%@ct,Cr,]Aq]Al[
h=Yk9;JI2=B,$((sk2Z]AXs[M;Qt,PW^@dP&F7o_NYd*/d\RjnZ!"%7\)WBS<hiW)eFu745'N
LJ*P)\mjWTpM0=VeL?.*D:[ND*'7?6rjF>##IWX3-Q;.5]ADM6(g;*@5T"@.@)FOrL2qcl3Fc
3J>*jLpKMCHf-Gd>Z/g0hCu%9rBjM::Q[^6ZshQ'=pQZBH+`/hAn0R!Fm><"D7e?M<rkPWR4
.3^V5.]A`(F35JD620db*[nc_a8ous2fZal2P)p8+)3B72b%I:AWM<F%<+)%YbimZKrqA':H<
MpgdYiAl!dYL7_FCchIh9Ontpg!En_S2CMrh]AXNH60+@?tUef.ACO^1BE)Ym;2"airbBm=O*
MFn"<bDpZ!k_[(+=qol&5\Di6/HE/M-=QPUkRO+3A"5dQr0SC%5Qa5a;/>kYo^<8"mr;B^2G
gAPu\\t.(Rf7Iu9c'6jP6WJ&?>0-Ds2O=cE;I28aQXCa#ZQhV+RP%k)?gt(/k[tNe6UA(K#(
=*mWV+;L;5tmhR!:uMc$+-@4/&QApV5<WJfSAL`_8M^Ko'XU<&;P'F4PL]A)%dMQl+T+jWn+i
&\FB;#P:\h?iZ2NoT:>e'MrhAS2E+ATk%XQm<o2Z6e3a_\P87lNIP5H<Y:C!97RoXA%N31sl
eGJADISY6RTcubA&fQLbI8B#]A@;#S_k]A.iM1JC/WiQ#SeX*qm+LeK?oB&;m(ClLAlbWs8ZrG
*+Dk'(A:cm$b^B)UcFGX,a+6K[s%$"%[.6*Hi7ZAOYRKf-qfl=$uYs:UeQ1r@P5'Hn/lkCak
Cm]AAFqAO4CEtrEpbS*^I1"3hZ;I$VtS_T/rGJpQET=E+5pJZ::[=HcZ-oZCC9+RPX%%X`T8=
.X26`M[F0Q]AG1L=jpFOO,E*9/crk8p5_R1!7),X#$9B&['HlJsCDjT_-Z[6GcY@[/5P6W+]A3
l,,-/i#\W6`GT^<d0Z.GZ/GA-eImhfAKpjGWFQl*<c,WO>fd7b$9m)h'ZEL#0Uc:=N(!AhrY
BTo)>)aa:IfPaa5!.D1h!"4CY).TgGRRJlc\2t^"]AJ-s3$k:91O[ih4XUZp!J,+)ERngTWc!
*,CG,h^'HAd8j#Cq0mO;;Wnbn$.HQVT&#ST>r#H=OV0Bh?a'G'nK)hsS3M0%=%/C@c15q4Y/
-ScfZW$0)i9;o;DfaF7l!U"sZNaMHmFY7O_k'(&-9XY?9YUgEjUt44/^-<Ze0*Ad:q=o?RUP
pObk0:E5a#"m6LrdW`Jn\X87Bq!7f`rHGO*.g=K_)3lHJiO(1mW0&*gL.#2`.!@H36ZKL?%'
<Z@9+f@^d4gMg[fkh%MI=hU_&V4mX-$\#.KsC^eOYAiab6COW`hdX*fbeW/\]AlQ7VFaV5D?$
8+o&<\WGa.@OIEVgX-RLqlWNl7rD]A:FA``XY?k<_6M"&_e5]AG=X/$reXARIg<<e-T(=tRqDD
%bn!J@mU6D_g8s<I5E-MfW*e[sjCASQ';Z3lJR$4dH9m(DP6d(/&2Y-dN.2O:IT>3Cd/3&R!
%lSgFmGKiUlrsNTQTp'or2>D%Np5YBeQ@lW"mu0flOr9e[KYpgV+LG1\0-8Wn)<r)26l>ch1
m+b2W3O]AUl/f2;$j_Sl^gW)U^Q>">AcPR61F%0Xr%r(e_3d2OqO@u2"UKBG8hGX\I[taK=c6
En$cJ#[j(KobChJp;8`-.a4M5(NVt/$MoUQLjf/&C*Gq7>$"AK_n9KGV2=a>`WdUpJ63A\AA
$-Uk*)pgmpi/?4<9.hYeV4kM3jg4OPXGnrlDlKcdK3YYU$W^$/`QTMY=TRW4=^IXGXUNWb7m
b.b:<#amhdOPS"h,Qk<l"$5HN1D-K"4#5%uK.$bW1E_g['L*]A7./*Ns'h_K4-c&=@W,>$o+s
?:rFTkWFOk1l4i(*AFLYQ=*<$Lt="J?$hKBn+-9Y/H@tf[Y8J%.+?K2Jgj)"8D?HNZV^A+SM
oH'VRXWl07^!Nk<kguF9jWZAM%kA-5kG,p%YC'M>J2Z?DP1S,KOD@5W$g@.h]A=Aa@j3NEE3D
ZUK9CecjM&$fJ(j<`?-%7[8%uR=&oA0/YgV00n)oNpQIWYZH_]<EMAIL>*)(q\$K]A_BG`Lc%
s;Fn]A<"-UBQ<LRTcOI7e.9^T^;a>:.okoNEV;qVZu/VmfQ$i_qI:>9gu_rjA@/?V:PN2[H*J
DT&9h)lZe@UacclG!Db-s5QMM*cq8g&d3JNSibccj^d"4*s%Nj+2FVA2dbhGp3R(*V<K;F0A
.(l]AkKhc/$P'DgEeBL&VUX3AD^X'q!HB#MXPCHFcV[;j=A?/10M[sq/Kl-uU0Y$.Kp"B-F:S
hLWe#r#!WY52@o)]AZ:p!<P;g;)O9UUbO50=\/*9-bTXc`H\*d:S5X/o12AAK[nsRI`IVJMhL
2chW]AfX'"N'?.hY[Q0+ssLW,#e`2VIG@PWcZQORD<a-m!t]ASCeX/XjRLaL$/tM8aVae?B2ld
tFaO:U=OX2VE)XIRE,l4!q;S%P<a,l>;=<9l!Z#_V$$aSqYR1<nK_O7ojk&8&k'LG+p%&3R>
FqB3EdWG`Jace<l[E'HkYAZZ4,SE7AtN*s:Si+SD@*$3c`t(8uoRqdXY_eo0!!\%QoJXBt;`
(.I`)ii0f;rr&"^a1MU5.9ZalO'ed<#C"$J\4o;p9)'_d@W&nc.l/s8ClIan""q?h<5ZF;h'
^GT2rClrU!=XprcaF&'+[$!D8CM6?&.ii.g1)]An6_SB(:@'+9E);UdfX]AneCc(#C4"I,R!(g
Eers&NPA$S@7I=S%/QBTIR_'U,D*(IZ9%(rPg1=N'S+G<Bd!"N+c5r>H3RQ3ALm;sir4@RQP
[M[caYt_lE(3`7hK*o?B-\EDFUr9fOf/jU5klXKS.8i:,B;2eq"n%Zfdm<Kc=garAu9X23`.
'Ra,&u@'j[Pk')WoO<bn)R9r'&ck8\(XC+GI!s4P[pRVFl1-J^9@8PkSC1_qK=iR<Sq<i=WE
NkU4L=HZ6eBE"SmT<@Q%BeCadrL3q?pk9B.X2uDZ]Ao0T$3s9:cRZ\@/JI$[shu_itBW3a8#/
e/'G1/_QCi&*af1!QdNQDqD4.jGL05J%8A4L^Y&)(3dq[deZn[J3oIXEN(R99.h^\s/.cGp5
8jk8B8Q5c=SR;2]Au$tY+\-rFJr`RjYiG)=Kmo%;=^1/0hLE5$q:QMX%o94tb<OAu.JUNTEUd
d^qOF-[FS_XX6;HJ5?PKn^%0b@"0@Tn^dJlY>M#hX%eqi""3f4*SYh5WZ:_N.9DU%`KJ=672
e%'2;?*=qA&No?M-+_E,#g$%0k?$"ei\1Vr5?k-pn,h:^(@foMp3;SdPFAthja0bPXOW&P"9
Z+M7q@Cgh`JVu@WbHY)1iFk9D8j$KJp0kB\8.=rD.'0-q1cqd&U/,j^brTuaJ,e*YOHkl+#Y
l*M%3J(cZO/!8o*:=,c-2Flmj659G8;b<QC&e+kI$c]AOOs6Zn?&cUL42d&0D!*JbIT*jNL+?
["3#9&fi#=I-<iX[l/@PbE<M^DoOOJU@'KZ+l=$8u_`JYL4JUd^=)4g^,6X(_H=`S9>hb=W^
5p9g_M<P,43`^-U\@[2N$*_35ZS1gPA`]A+Thg@/7\N*Im/(NQ<(^)'S#i.Pq=CfC5\H)k^9>
S#ghUTfHhgIOF8B/.(FM\F%Xm-C8L=0]A_VW5\Z6[t$2^in^>_K(0glIm1Qe%?4DquSIca%p,
f)=u/a(aF@W3OM^q_dCsk1t?k+;82[j4dGrQu#F8,[X0^.C(U\QVSF-;n/bfX^c+D=8^YGZL
e7Dk>0O>f%M.4*1cnGQ\?2p,7s9/!nm<n:G8=+<*^X4&uaF:Csj[_eC`sd6X+73Ph6eX,U"M
859Kp-2e^<'YibNI9.#'UqCmAI*siEA_r.C$N:,r2+H$LFOa@?'o#7-c,?[68&2X@]Apn2*JP
F>aUW(l7,e$e%%JaqM8j2ba[Fb,QQg0.1`n);*_eaHgon^$lQ!-L=3SKs]Aso\V%(25tIX9^s
uMggjc6"'%Gm.c&p";Pq[>mlli)5"1U+1j'fh6,MEr[31d2S6QRAhBatc_fr!'HHl6YofOOH
nIq?mc0_E8!A;[OQ[&r$aF3p;lGJ335Q01&_(aZg-P@t_,JoBm@R</NU,8MRaj0R2GI6qWjQ
dS^5G.^WI4[2qqQ8(QXGa,m)<.LgjM'=RSu;tgQ$Mac.b6IY;BGCUDp?"q^IiWkBj]Ajhc<os
D2Se.9o4Rf)g_.DPhrsX/2-0t=BFCLn]A_Jh*k?hMY?Q:IAQg@*@04-'8I?i*J0i:FiIj68bA
!qN:c1BC")j8N?*Od$7=CL84IE`j$IPX%Xn4;,M$_g;SOq+,]ANA^Ho(k;\>OVORA]A%).tmCN
cc9pmnXm1K>L@sr-`lQ.)fB^U#SpLMW,V=l4q>V$P8)QaEWYd^VU/1<lj@n3B-19+G7qVn\q
5+SAPQTmT-aV[4f6g<V"[OV9dfA#bP[Y4gZnk]Ae"\'HAGT(e"4:nPLrc=`]A_pcToiVR*<"_n
KZN@GfMl;+l3-TJtmOAC5/`pS[Kd&N>=g(4>Hllr29VSdP@HEg!u'R_R#f4[t(im@46,U5pW
CfqnCN/bH:e'kj;39e$X\b6gSNY![;9Qh>SLQGaU`7N&Th,7MM9!.%k)7glba>?OfYLd0-lJ
jL7)6J%ef+e9\Vd`Z4IV!Z`X%egq-f4ZN@a92;9[8?raFA=1$:B'tl:j#Qtl7c[2.k&tB(eF
be7+3$YRZ!+7'!ea!asCDP(eb<j;CLOF-JZb6rlD([4mIog"b"Hi);<h9\5:((F2T<_j"Yi,
j_l`6$iPJP]A0t#%V&qa5*,=VmS:W$)U2GE@R2.T/US+@E>.-ll>tLHE(^MGiJV$&uTdl9N*i
-F!^N^i)aaKss#LQ)`>q2P#8X/i^]AH61TOF\r)Eagr-Nno8RLrP)CE;c`$;jO%Xn@P"Y!?R)
+Op`!qX"6#okbH:Q2<fA0%YLNjg[`U>J<.O=,7.CCVP@bf==d(+m\V'':;o#KZ5H3(^TALS`
S&Hk!oR?+Lo8)nU,k354e9Q%oiBO>46fC%8<'lup_EVlJeX5joUG/rCL:kFje0gj]AIA`qg)=
l>ZA_1Fj*.3A4L94VOkSaZK#=hnpk!!:QEl@6Y.1nUi&CA3+"nKAT5tMMc."2B)O^%:'A&N&
X*R/DpUBG>$PVMb[VM\^4g<<,'8IG>6!bm5R;2ZN`VV4s./f7@(H#%T9c8O<X6J7R4EWgO)$
1u[5Pd0cn\llYX_2=Z;>/7#*d8:'m.4XX!#s=CP;RsA'sfo#%X+^N7)djiCHEE]A(-14cV1t;
X6f'/eD!^!9mL<'<S_X4HqtB;MPW9c'/sU".4pU^hq90rh:6JXGGp)-;r-@P4$D]A)OK=qFY3
'sMdJ`<""5=pS6?srRb[!T1_'/mjgOs?P\%BaV6A@bThkoacCNm\p@?#6[thJjI;eEgl+Fj;
3.m^Jc_=75Q+R36C>dH"='ERUlG*NE'AD&`0D$'V=_3)BbIl^JUpP8R4kA-X2$AT%(1^OVp'
B#Cm8mX33\B`c"(*?jRO&u&ESC\s'uGUAEVA?)9Vq"gDPau$)1,cKC=#cB>fF&$CjLE?9BG5
U\6^s5h;N-p+7E:K]Ab[-]AR&5h7ApoIsrrJOqR#p"p1=;JJD8P$0[[/BCS^Mn[14UJFko?(3a
nBEP]AFH__HV0a(4)aUU)@G'8sGS-,Ees4plRm2aJSThY<Kg[\DWi`8i931`R>S2b08&MsaUe
PaM#H9/++Gs3[6cHKH-TfuHf-&.1YSUh9%5LO1Z5Ui+X8#Fob#J"lf_Z''"lGGC_H#1he]ALY
3O'$[1pVR2W;Qi(D:Um[o#A_)Wl8*@AJ7a3#Po<^Jib<[T!$Ee/I:P7d4^g<6p=BB-B4.$Qt
?BuZ361aC]AH+dPSQqgKt'1s!EOTpA0Pu=&s`NP;!@#^[NQO&Xl;i0d2^h,2&N1il'YgIdegl
8u(3ebEiF>f&EV`_M21Tuu.1a>OCU2Q$riVhB7JbKrdTbPLq-B9k3SPR5043Stf=,h"m&<;B
N3Oe_U=PO]AFY&6po7B2kZcgS(3&AU)]A$+#]A$*K`gOIX&b/B90"ja+DHd%I[6?^,k4f]A#3a1d
#M9cggH++?pCi$0]A4Tt17Ye@0qA_/aSLh$SCVDU@DQ'*AP)T7+7?M6jDZ^"R-]ATj1o-)j@"K
iY($J>J/[m,P4\[b%Y&?<C&<?F7!^kFK2INP4`Kn;n@J`q]A.KjJRS+-1q1I<WH^+-=+PJ1dV
<-D@ahJ%shR26'+ADK825p9UJH'<P$pFN\_->PmBns3X$HSV<#<EMAIL>@513XOE%Y;n=00
=I%rt`fF.V!!9R/dI#qlWo8s2oqB:)j&/<lu=#/NYjuk!s(0J0F8@PTK>2RlaJ/OVV1e)'(-
AC_d2_BtM6?@(\d8Ac5e5"=JeOO[hL3gKEs&.1..*DqgiB*o]AT8^2Wh-Fk8IWj?b[sZ@D>qQ
@2/J;==XmZfLS]ADdUmS@FU&;nHQg*@6>t-@M"6HR*@S)V@-2D(in+4,gr8B[V/&;Pg#O2iL.
?WUIVHqO&El-H@t+3"Y<5J#?eZK&71Z)Wu:m/Xe_"b"/]AaU.0r&0d^;Z%oK%UkDLB33X;$C[
`$-_cni_[Vk4VMtmW^CV-;"-nPY+Q@>9>pP%93/)T%)d):7#tH!leHrBL&M[e"D.tI+3V#[F
t]A-;%$MtkcL_firmkG&)JLKKp'R>:q?Xh3q;^'?i?]Air8FUb2BcgR/[(aN:$0ImoMfY34$Y(
K%Y^;MH-)_iEdNR-@uXrGh0=GA\MRHr;iL5n1@SuWPY9thl9uU,\uhb8h@NY9lMd*3#[X,FB
",)>(=_6qQHMoS7_%_iFjTd3#X-_>SqMmJ]Ap,3<\1u!Gf/jk!k_cF]A9VQ')`aq;_H8SY>SYk
g@SPK"X9L%7Q\i0EWiV[DhNc=3N,A&aNd!"n;"pm/RV@%,<^gg0WaG62X>0)V/"Y"Df@UFJ,
s#t!G=tk>qL:Jf*>p4SHBWbR/E7dj-VD^*W2]ALW'pFmW;cuS_rG<Sdf%$/QVn,oI(^NDuO4E
)I=%u1l1h-mPG4Qp$o\W=6(3^?(,aTHIqI7^N/G\QUsCC/q:]AN*DEk4Ir;<*(A_Xkl*B3qHD
b?QYD:Y=CiU]A[MHB?M`EaPL#0+bi!1loj@l9c<ur4XZ(pI\'jX%"\2q3p31ZC$A.D^4@P?M]A
gU-Zic_QenK9&QSrW\6qfC?GR`\hd;/?-Pk]A@9t%.G_qZ8n;u]A5-78ZLIDEFDk3QBc!N$aC9
9E$B[C`)g_&K#(9Ve>"j8['XFudfW$\!pbOMJ\L/iR^+;C-E:k/!P1DWUp9um1_6>Sk@QBND
l)4l`%lM<uPWbDffE+u,q/WN!*@oJE1HQ[)oerP0N*h$l]A2uc?LrMU[\LIWgDN-YiL5Hsr#S
7@.,m*F-!lCL1BGU@l3%Zl6rlF0$1RE@ZXYT7g6k9L&$OH#r5fPpdm1D]AX*&X#]AH>+_)o[CN
j<%3CCdR\$01e5O]AdDLk<1XQssgja\<?4m^U<"^.6IAhb.a'+<H,Q<,#DP&6c.TsuF"@j#X,
6*FBqWpnM-tECg!ml9?j\c]A@.oo#"f1WL3Id-o%!;;q--ueI0TY_9'#kjOq?(s:sbb/&<d6/
t$\A\\f&O,7;A<Gl6:A0e>P(YS0bOQ#0".u6n,^[]A_*u%;hSS/k!BZAA>TGf(aZE$KKkZjOF
[ZF7,Q#V86=''@q4#YqgK$KUC/F*Q+U=d6e6N&3>LB^U=jS]ASRp"n!qe(U+o%l'8G?9%cBWm
lCm-qC5'<7!EQn:,Gt]Ai;)fN$j#lIFjWP"%X1ui<7>>W3Egk6J)Xi]A5GP!K6.6`)-r:_*+9^
.L)!8fn5/HYqYV"K8rhei7N5n=6Su1:<moR&qq=jiNO1:?Rja*o5CR;+oKpF9D]A]AS98k-bq-
IOJKT&[@X!m94CdQDLA,Fos2/2&I1P^u]An&\bUf30ndt78!*'o=&P1Cec@;@Jj`P1N6huM@$
;C!<NWmGEC(>""8h4HeHBQ;(_gBg1hh.G!lb(oki:C60kO.YLFUQi`[#EYo-%o44HPk9.[E5
C&5)q6%gQB64#'k@sCt>TS3/8bP96a0_^^&OQf*%(idJM,0*q?oDV%lJ7+0\^.P$KMiFf2CT
`;E+o*3i/a$>D0jafg`1atML,cg/9VtL!GLc/.FjT;BAID"[m#\K%\O%)&Yd)/9W0OQs=?Wd
Jd<=.RkNR'o4>]A;BI-F?C<&XdQ#/H3<E(5V1fJa7.S%OIb+s\@#&&3j61-KC6-<P,r\s?JTb
pq1PRtQDCL!D$BnM4$2k5=>dM,ZdghsS_d7_Zm#1YO&<"bX=(![3X%,qu?Z<XYdj?iSno`*T
u7d*d/nHQAtt-4ta>+%)Z,dHmkbXbi"o)b,:ODujn0eO'!(3_IhMYQ=ZHR@MH_hligY\/%E#
]AUiI.r\iK[Zfh7g4`r2Ph^UI,R)u[XDJ\lfFgMapU#l!WHWp5;g@W9'F5,p9O8,IsePjm[[E
\04/Fu.[,ZRl^dl,5l?2CHDXI"n^s&iO"`f*!n/,!mt?MXA#BCKgu3W@&\N!3RX>q$9`M2R2
sX+JOpotaserTIN,0]Ar6?@5jiTri3`jUK6ftIaq9h60>pGpg2Bu8;_n0iiDg548R>ds3Ht8G
.Qj$F-5#9R/6h$>lUeh[/\Ch3d&8%gl;?sb4'DerJ+d]A3-+Cn*rO5%Q!jMr:/3a]AP.FQ&h_g
\j./.,9f<0(]AGq2GbkGS;jOK>L9(aIVt/kn>dM!S*En#t'CS5&kN#re+1jP2B+-ADF?jYXD-
(:?fq>CTJK4fhla@\o!o?1RGmeEgk?H^[p8Vo,9M*(Qrt>d(X'7UC$heV"W'=a#7q^Ashj]A$
X9p`Nqm;mZsHn-ofV(:B9N]AF:>p,U&&`B1bRWXZKcIB/<d[c^M1d-hs='_&BK^XQ*=rt&)!#
$;En9'H6)hH?SlHiVX*!(h_qX837@JH`;Ql"s1lIVX;KXIrk&[lkKm>iqA0eo)&ReV_B56F*
sXX[1&:jadBV7*`/*r_Xh`[1XB@%IU!&u*Ffr5Hh@DN^1>VkZ9Y]A*bg9([HcJ'[)]AW:`)TAi
MkQbI#mb.%5n]Auu#GTb[<bFB)mAq@/Hr%GUeGIb.GM^s@f4+j>oT/Y@@kX3);DDrsER]ANDU_
o?+]AELJa9b[XPGPeG-@ajiMQB@Ut71r!c+[46!godK%$]AW7*AZ#$"sPY.+r<C7]AT(a5)&^DQ
1XJ]AdW4101T8%WID>Cb-ka9C?"I&l!sj6DsQ4D[j&GVqQ*f1N_(PW<_.+<99!PH=*O[*k6fl
+<0'9<+q4e9a[\WDeL):,;s'gISCE58+UihDbsP##<30MZ)oZqIM#5%P0BbOhjIZ(Af(ec)^
AQW/$VuM';Md`7Al?M5V(1<WA#&t\A8G.;8D/]AnHS*"&]AAg`M(tk*[QX[bu<:[jAW!ar_`*K
:,/nYum]A`/0#K^9"YWLZ5>^382kf3P_C*pu6/#s%ruo29E&O>D#1l3=r-P*_e=iD61O,>TXN
?Rk>tdih2>PGY#HR/@JH!ogAYnKf=+hU-WC+@e5%n>6'KMuha/s1Q`pq;/)ULEDdQIo6,&?M
W=UBsd`/hiJ^jM*oGZAE^nhEp/2u[6$-nR3i_Q+As\O]A<<kVlCl`mXG"#?jSJUY]A!s&u5YD-
`on^iJ-q^#jUe7HV?XK'i[-jL$BXb`r5bQe#2lD\0W_s&g+#Q^Yr6ng!iq1C_`n,qmSdhh"F
`$GB!'TO*ir<0JppNE5L]Au"5^sY.(P69UD7iF=8kD1dflaWSCPaB\[3$Z5t_4Pp;SiLq;IMf
^/]AQE'P:%mhe5U\MUg_i7+(g?qqEiDf5]A%1"g_?R@Eb,(<7rtr7lCh^-K3?(V2WI>h:3TOdS
W."8I<[I33H.<Ndh"&AcI`fo<-c4gq(m-,gnC>goj1+A;Bl4[EU8U/FMr2YM6KXP.Ob[5KMd
/D>I4YQ;;!QANQccdrj-!j5&#CVnbCKFME/ZeSdE`PO=tW7aM?uY7RFu]AYnZq)2poBCEj-Q\
C,,CH[VD%6br.p+:`e76EE+<%%f5O#O^+@rP2UW1r455)<$oGJm9%GfUBN7hG!qt+B*#'(KD
[,N2Q.4.n4r"onl<ENdfs)SZ2q.[l7Om[Vlof3u<'I(B8W<Rp`6&c*CE$c_/KM4<NOqN^*1t
D=>:=qJoGYL:MX-FoZSV2]AC$8<p\37"Ls):g9_kK1ge-TRb:6Ci3KN1&lZSRF?8PVtOZ),Ot
5He[#="Qk*[7):%,F.9<;Wuf1GUO'&I2Z&l[6O\^Hm+FHb&oof4@?.]An<!@2l,&!A95X<r`V
cX3o"TpudhMNPIHP[KGc(iYHsgHl<8%NuR57tCi=%XQ4E0<QDg`Gh/sV9#e^-3PqM0TJrj!i
J86Q%(2#`F179.Ft84Nf)qD1q47$(G&<Nmu(kb-=_,Wh(qAEh)E/p#Xjmg5r8@iWe.\1JT3<
4p[,IA\KP1Y%8'/cM9DA:%csdLY'Yla?M9jI"QkV3*F5-n'bt\'=`a(!oB'2X&8D9;>6TQ7g
=nG'ME2io6e]A8S.@WCABQ"a+,_LP]A$uB?CTejZ=[f?CWY!p\]A8Q"cA-8C^JXkV\,.6@qZ2IK
k+KP&<Vno'DqM"aBVSnQ/PUGp6)_)fJVj\[h$qt6JtDmMWCfd(Q*_JjfnF="H'>6#?@L'8.X
(!iK>V-Cn>OO$lHa#9(MaR7K<b+>G95?pVk@laPZVO&Un5QH+!QO,.X>nY/MEF045X(UPjTr
K3uRbbYm2[STSj2u_))Ur^(Y0ReZL.#P,,MK4$;st\KC%E7&HD+?2Ic$8G+ik\bFmOMg[+E'
*%t.2^')C`a1FQ%pG9RHDua"ir?DTIn.Gl3rBp,mNo8E;s5]A&hn`rXh#ULe`qK&^J6;2Frcc
U,*$,RRj@6g%X^2n_rf,kleGR?tq=c69G1`JYcPK;<6nSW=Eh5MDGo$2L`u::_<;lQ->6arm
Zi/<2]AE\C1qYdl(ZI8Pua6<Qt"29=ka[#;Z\rZ"55`gBYetogiQ+ODZ!.:06fN+I9=WYo3AI
XkKl32m.T]A-(L;E@$B:c?9Jj43Z/:%R&;Z_Hdb`p..LE7Og3WTP8gVdLc>^)L99L^W7K535X
@KgSb%6U@;!MBL@>)'K?"*pI5/jHB]A3?Eh?"mL4N'Z'*YJ/NG'DDKt="fQW"CMeqo,DhKpT0
*i?;*#7B6C%84fg\f(Tc($+E)$Y"lYfB8PIRS.1pGm=kI`+bt?sIC=NT<KA^1pcUFj&e0p"I
Pc+-(X(e'07m9Hnot'"7&foK0koo\MWeX2pZ#BrZ;$Bi(<-#9t5q!2$[QX`#52GYLSEd(p[[
M`$R&]A2r:O?P@PNG^H7U=P'Qs_9a4FgW_3u0S[6\7\f.(40a'b[eJ^]A`@j:kd8jlu>Damr#9
#cX$h1)#Dd4%>'a2QVZV(J=rU>,WJZuEg.c9M>'cWtc\?.8WA]A=.@o2=mW;*O4u@btW1ah)6
KggljZdsosiHHb>S\0@B\E*HYtSPG.TU6gX0Hb/)raX?Hc'=b*s-2323atp6PDiIJKOu!44-
8-aF5jcp'mMT55(S_4S`l2uqcsoFg*F]AtSC<5>-e`t7fDLtii4G]A(jdg1<Xf[+&0FKoZVBoO
js!C2+Tj2f4=FkJ3f>NY^r2B$Ln[epYapQLQ&;?u9`4S^M`Ygr%h)VeW@Wmug<6N%Ej%ef1\
F?,]ADIWC2USGetoUqB;I+nPBm2rJb(X1?*HO2V,q)JeQ[Z2&B,8U"h6-)*`fPH%pF9Rj,D$*
d.9&?fJN-FdFiQl@)A?R-ODo4U".pSL#g>-4_t,VHKBWM=s9>,(o-CG[p@f%[@6YZWd$R-g\
lq3NQ47IdK>k>`CD=$O%JHVqm:Srso]A4=0,9feV@Th,O-qF4$<>OaMfBrEhe-@\m0?jgm_`#
A27dlu]A2m-uODU?UucO-h@V;NHf-JeekmS@bH>nEt>9gC!d1WNa\=KJI#dnB5EbJ"LY6!I=+
L!?Tl6HW\qQMQ(nrgfXOq-D+nr&Z5_^K]AIE;Z_0f6d^*_@#kkF/HF/M"/,dWs<U3=l8:haFo
YY,foiF85sILf_.@^9V>c<TK00M5mM*F_c<iQ]A:AK1ug"MK,A_kbHaq`deMjImU:)e7'4.&_
6)h9H45u=AneO_0Y9Pb,Rb?U$A:Xi#&#13IbB&pU.n\O8njKiQ0Jm`eCS$M1)NW@:Pt7gRqe
`)RMT`q,%SMl(%d!h$Y#CB&Z37KL4kC?@Nbt?C8*9;8:Sr4+8;1/!PJ"DT"[1n<->Z]AraBHd
To^2X)M/UH-$?YH/6_bDn)Rb6l7m)IGIcY=aMm_hD&NrYD9&W+ARRL$Y+L1*ss*;"l".\#!/
!^]AcotO-SU=b:<*TbT%-X/-lZJTVV772UZ2'PR]AJSEFs_o@=`=oncs<(2p<Ma)Iio1j9V(aJ
!'AN4.jZiq@m_;=ebK0-`G^(RF$MMc5X.1em?_Rf*KR?)eW2dCXaU77(C[ggLGlQDZ;p.7MH
b9Lm/$')&$Lm_+3P#bUJPh=X1K?<L8Xn#nPsA>/qeM@2[=Qa(qUkfjijb4/^fEuG$?r*4C2*
0AC2]AH%GcinA[&`.+fs&8+=h#V=0mML+m<NG)"ZPRT"4N8F"^=KL$kgI[PdR.K8WW,NA"UOI
D/L[##Ibn29g$ElWHhdPSrT5k^-g(SQ1l`*8A;r7TW!%T$:8]A3L(YA9iA7LKHJ9VC/<nOh@C
-oH<@,<W^f`$=ER<HEB`VP4N?nU:.#(A$Ok_t(8VPjVOlBEZYutsbbJq>4kri9:lP?F^)tg7
3PONGDND1EFIeFUnEOg)gZV,qMX[3%e*=EW4U?+K*;6N'c<$Pbq:bD>Z).>?"qfa2*.ViZO+
[eE10\m=@YYHMMgA76D-F:4)U3\WAT]AhP,-9+"TN44@Zh:?/A-'\4Pd`GhRn8-VLh>iGCYS-
tU<\M@#OW-:UQ4UZT70?Q*ABV^UlGtQ6:KWH3Y5K#J,.r&pl(%!QZ<[&&o[Cq,mU\<3iJJ7:
p=NJ(.4=3B]A*^l#lRjm9D,E[[robpFmH4I2I5abmh*l'>+Y,P.d5bJT!)qG/]A#J4N;Yj[ZR5
ekhKc'0p<hMhdbkW8C9bioNW'AcC-;:)B!eLfEq4[B+8Qk5[uOPpm7c?)Iq*5d0\)jR&/lN"
5Eo^'Yk,e-DM`#'.Bc"bl\C0)=Rhj%@ItJA6%&@_Ubin4-u3]A=p3Bi.m<LW=jM9l+Es[3jn>
Q4oVh-i0K;XSnR*F/!n+#G23\Tg(F_"Bs7eJXsl5p)uqp,U4U2HlT]A;['aa/6M0;JIM#K>Zg
kr=9Q8W0.G5A<cBu?U9HV2<5PLFllk/jsE0ZO3VF@EYJ>LNf(WV*C83a;3u68<<[esTL;tWd
oJ=D1-B/ho+E!GL6`#S.t8WWSrf7i`pN#mbqhjp0)9P!^::*W->L>q2;iB]A$:oL_)M2I^`eG
GMQ3EYi5tUQ7oJcpmnaYtGN/O%`S/j:*c@W^;NAn4db\k++Lls*_!?IDO"ZN:bCr[C^ZUCdb
56II/m9Mo>F2hA-9uF,(2pn>_==6-?SIsCF\CtU8pO&oa6mrbH.E71/%q-eOr_e3gr\!:3gV
TqF:VJe:^A!q^jdP"N+"*[QaC"[)mJGp-lT$\$[`(,<R]AnL:9:!l4buT2Z"<E!$nAsFhqX:T
Um4'hifkGO-ac]A"=$[H+F3?=-,m^^++?0VB[`AtopIg32P$gkYiV<uoZg[JLXYQ'Tn;)BXND
#=.bjU[u>TpBL;#B5nUn]AV<EYl&B%HhZKa)`$c?4o0E<oWXKel`,Q24N?&E%o7Q/<Z7REqVH
G.LJJ\nqECBPjXG@F]A-t=7o$KT@G8hbos$!WLY"LcW=*j28lm'8&c_'"X'u+d4H_d$=k5#.S
V?:ha:Fc:VoD2g.Z#A=iCMSFEFo8]A#HB-:[rAO<drO)*DhjUC!E#33)J3PurEQ6=[bC:S]A9:
!(GpUk,af5!&,S8c%,<L(5#_re/_;;WsK9%(U9i2ZJjRBhY>rGT.dmWj6EjO*r'.k_lLA,3>
%nB%5@X^;n,oWF'U+*u.ONHK\uHN!HuJob;j7^APUqYYA-r]AR(oD_:Zhs/5V6o&a!M6RND=-
2'%,Y?`+<lQ#)(Q*1J+]A;NdDrJk.eRq.bD(>RW;la5VV>]AK&K[5KSjhUU`Y]Ap;=#W'%/(YPW
Zq7mYOSgJq=@k?D&#5(7-iZh04/h#"&eDf;/^Ci&S&]A)MW=LS%KUSc":)[)AS?GB(LF<mRr'
Z[#mCI;QJ-_\9'E4TsDYrjDO.oA&WVhk=tZ%IG7)S?\"]A]A32egrVbDngRdlKD]A.,sFn_rTiG
T3A<hFU"^<a,0HuF3pd0.G/`SY1/6uR>rZR90gm;'%V473fP+5%NXk'H*2l)f7>FO'_a^A0"
[C2'u)a4]A%cd:gKrQtaYp]AfBjRWqPlY?2g:,f?Su,(CRF%Epj`Wbdr*k9f2U@[,3_'<P/7qB
*Fh4NEhm"oo8:gGF(7,g6;.t_3Xn<>b1)r[#_tk>M<+Z?V/"pD!I9.h,U\Y9Q6k8BQ:H:Xln
k^]A<5JtIK"3(bhBYJL"k56YQ%SsN9S8.bBp'CT0E2/@Of(Ze[kPK\&=mt'GPP^p1sP2f?]Ar!
qUuVG"S:m.DUV!jDnhIAAm3F+G,irV7t1DPeaU.6NrNO#H(Ei\g?d5_5Bk[RD=P76ml%>O9(
C(cGOjSEUNS9(Cmjfp%4+^9bfRT58a>$bq6Nf+AOC#ngYEXS$2aNcjE\CC:AK)oZSg8>Lr;<
aII]Ald8VVtrQ_=Z#:7F1FRr>\@QMKf%f/GhH46756K1#XiLFHiEZ-6m&WE3G\bjE%EB!oZ?q
[;RMh6u)>BsK9lg@*e=oftkSLije'H0H/V^'`tHaCRh^A/7FBVK@@A!p^hTpS!e0Y?-3`@+]A
F_pL23ACrW82>$/@$kHI4hh9.sCS72?\[g%);MXkICj9[9b,lm_;-eh8"Beu@j8kt"]A<ZO[1
K'!*7]A*:AO5U/a^#C[BlSS-KX)nb=Rs-pWa7G+(D31><p?etRT[I%cDYO\===m;Ln:Cc9p/U
hd@F1-!A.IPaP"G+tk^hqA`Dk>kCmqf?G^Njg*p(:[8mLW@T><D(fF^Dq1f<R!SV$_d)S9Mn
-It#Q?H."X/-d'0Zkm!"TC&<q2D^W<1?d)cW[qW,7oa[X1$^CUiI]ANDGIcnMZmPc$H9cjE7F
m$g=4A="RQYrjfl'=O@IG=]AZeXu#QZh$TB[hr]A';'`gj08X_mT>saa1BYhFk?$^`JYg_Gi;\
`E5]AFNMF#&PpDur'1#_YI#)&O\+W.Ulb>!RY^^n:aL&d*c<,1&e%hupRp=S?osC)thcWc_;,
W7FhJUKU0qNbTTb3i[=%l_rB3XeT&i[>nuBeN0^Q<9pL6_5m7Fm64[W,*$%1Db5g@WA>eJ6B
#`7W"plb91PKPRli/\.QJt=.S7&oA*8TF.83f!T7V<ZY'r"hS._7j~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="284"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="387" width="375" height="284"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="671"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 

setTimeout(function() {
	tabck(objTab);
}, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<FileAttrErrorMarker-Refresh class="com.fr.base.io.FileAttrErrorMarker" plugin-version="1.5.11" oriClass="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11">
<Refresh customClass="false" interval="0.0" state="0" refreshArea=""/>
</FileAttrErrorMarker-Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[分支机构查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB0' onclick=tabck('TAB0')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[单指标查询]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB1' onclick=tabck('TAB1')><font>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB0')><div id='Font0' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB1')><div id='Font1' style='width:10px;height:2px;background:none;margin-left:50%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?q`h'5&56UUJkk2,tE5\.mN&>M:8F,0DK//R#P[OGY68Mi*$M!$U,B_3S(uDL,-^J`'Cub1
/ld(Ep**4X:^q>d?qc5XI4O;[?eS+itAjL[L6&hm6tXS2kY^pR02d1Z.qUhjM$'gAUg?XX76
a#fQG#-]A\1b5^[CI?U*5V(9(pV%j;KBVC.EGnOb"OnPU>(i_t3dENaa?ri1CsnK\M<r9SuCj
5Y37&)Dd-5D7i#Hbo?#^;'6sQ]AXj`>W*B7If>hgY&ol$'=LD)2M>?ds).#u$mY\`XdK&)G<!
3qqbjN$:.pnG01$ti<$KC**$eD%:j2L^4LB@9RE0?r-F4_LQ"u9kNZN<fq9^[&Ya-#5:/r0,
Stinm(Z@cBANn(VCU$siT3Cs$G3Q7n7F5[RBJq_R==?oO$"N4\a:%9/F?^mmXRk5O7f1<Ioj
kB#=sGUf.<YA4<a_79gTm:t1O)Ho)^:6UF\bR&4oDYg]AD1K=%n4khpOl8T30gPbk6`ur-\mR
a$X`WM(bIDJS,JJS\>@DBj9X/S15u44$.c7b-[/&R'T)k/Nbqb]A6fTi.)-Mu%`+&H?f4f*X%
if0o>\F@0p#kYI'ooX8RK!#q702P*$8_D46/si8&3G)fHqblP-bo'@0[m;#!,kU5;On]AX3d\
Vt`SfE!Q)=@g.jO"?C,I'ERa2/,8V^Q/,dc&=*4FsC:&Wj86GTq+(+@@:Y^Pc$8)7JoH>.sp
rO]A[d=Fq[tTL8Nqh@pJ[jHlD#;ZJi$R;X9k6$Hhkq"m7!o)A6&V]AAfka.5L8i&7$nTDH"+"F
Gm@]A=HA_W4T6h0:6QW:I9<npHF.5E1Gc;U_:3uf]A8H*^em-1SYk<]AOB(-c;JT!Th(MiZV]A$^
-=1_N:Oj48]A`jthrEm-nn7a<s;5;eE%P;T+6]A[Fk<Yb4mr#1s\Cne%As#,p)JM#YiWE[p<\1
;&5XRp_+qa"E(AB<3JYkkm#E!GS<XX/&i"k%JYc66Zmqmh+p4,N9SsZYp3<Y6Y2PK1&2hJi1
@5b^G*9<r.77]A'i=Qo/rH3S?8j7oMAJb?uRS[mHAr,aNWoZ+Z%JmJ%BEPORG1g!Z'e2-IAJo
-/Bf7n4'=,%l.^+]AZ<!_]AJP3EQ;=2n@Xc!k=%tr<?RDYucsiWh0K[aYjH&qOdc-&C^Ti5Ffe
K.'`Mgu*#'j:,oA:S!g#NMZd@+RE!=ojOPG]A6^dgpd-)S)Z4j)UT`iJQACNuZ2bF)IY]AiKsM
h]Ar[0Z@p?*NMuoi#K0"1<ltX4![?=ae7"uD5L[:glBp+u6(E7.+,2:&nFPS[j`fF[A!hCXaS
G`GW4q`/i_r39F:4ZstElTl&>N?;aaGU]A[^ZUb8$^]Ah/o-@4X\aX3#*\.%:%_=X0e-<@6*=N
nF@983m,l82975jta@tT<Ib$D$HW?mL+XmWfAM;M<;]Ao1ms:7SliL.@lHac-X;@:RE($6>!/
6'FM[4DGoq5g'9rqn$V)3KVdICB\OTV8PF^GV/#]A/2joab.boYG3&<fI?.L@GV4`>lnEDI<'
e4kin<sr+`Z>jB2EeJ^MV;7TEU1%Eq--ARXPIonj#?r`G$3'7RXEWls35i!Z4RDo`'W*,<.=
_HPSF/Pg2b6P,Q,sCsO+h<N"mj'fQKphOI<.M7ZPTM,GQj3]A!pSWr0IUM:=@#^`,BO2T8S+3
QIBSg[D^gF&["%&]AKFNC4?bO"Ni((De3;)itH"reN&9U&\b]A:m_>EjRe+MA<G`Y(Mre'5^nF
T@0I>Q^"HkVo2?I/'d_8'e/6HM,>3lDHQDH=i[.mm_+X8+dlOTiB9PC>u1!@6SWgrsiOEM.g
M@MbT@eiC!<"(q(:;$OUgD(hGiKbne4",mooa*`7m6\94!Qd2-8AoA+F&f"UJM]A,4CRd>DZ\
<6CGY+??dk,]ACX#8R_H/hF%/\6gns"n>S^4)0`ml@I#jK8i$'RY"E=mrB^-GJL(TLJb%8?!o
DI&9u57N>t"/%o/sr8r3!>FfkMR0c\'l6&4qX)7Zk;7AU0_%!1[NDNDG4hnq,3]A<AhBmuWdi
6TfHYiB;\nttK[T(O&mQf\]AoOi7V5Mp@V7PdErg7&IPuBdp8nQ*nY[?Q:0,;"5_VeHnmmbr[
NMF]A!8HT("LD@-OI\QKdA;+p^Xc'glQ34A52*P8a+3X0M@p"fbp0_m'r;^+a<S*?T,8/`X8t
)^V%TUQr;YGu\PeBH``2q8c(4Y?tffIj=Y[br1t@hQl7LrCiP^[]AcC7.&]A/]A$P,4OpeV[eBB
jmi,6_IRYh3MdKBosqhtfe_P"uqO>De:Pcu-HM0S@<^CAN46f'09rSB.Q23NM[NeKVTpn-+0
7pPKNWh@Ybc8-"W&5U)pS1n2Y5&5\f:WsNO$Udam9&cQMa'gp&5NiE^WYoJ>NXL#6:UN,t]A3
C.C1G9$MC;N+N@16D(PiI0>%g1;/COBSC^JK-t#^_lfK`%3W*-Gco/+hFdjF0E%b+RQfc9m"
XXTZ5iK]A=\RO.HYZ9;+8XIZ$4>"Nbr74BR.P5)UNX2"u)8K$W'^OQm/_#eN4>,/STfs%[g(G
`@=6-d4a/1l3gW;[hjmZm4TW.EHq=('(SW-o#DpDkU`(1\kV+mnlS1UX)n5&3K,JJ(?<?EW<
ud.))t^)rH%^mC\]A`JB@8H<Y02>.X'*l/elqb2I-H'SBOV(8p_-1DrGa#<8?k.P)P)69`!uX
aB9R9o`BJNn!><XkY%b!sD(+OY;<"U>;QmFX#Hk7JRST#-\GGbTe@*`n1;Jsb]Ak(u<'9D[Zk
*Ao)?8#;.YBa.'Jihr-gOfk("3K&(%>H0,e<WXkMqSE7XDn-J;F!8?>i<hPT-c-O:GZ0k$lI
eIAhRsC"_<8lM^f]A*XZ<k\k)?ri*Ig,B&C,3^)D0jB32Wu8dK_gmB>SmrY<LTgm1JFOg<i,%
eb?T:geQ-$bnk6G^Ji2)IC?TNH0-e`hq#R-na#UabnSgkI(dGm.[,s^Y/4\<`bAm./MhOF;\
J]AX1L1JN-spt5?t\[^lfl%C3"gY5Kp_u)9U(Ol/9ULWdK`h0;=l\RGBW3["&qT3IY,^Xs4C_
'gciaCJn9I$r2=6WNTCjOjjLV9=V<$_nY(Z,"!'ga5u*`1CCB06LEc>\^XD3.-\L%.-2BkLh
Q\Tam)(ggF!?e!:jk\)8>"$.LGKSA9F-2O]As_^imp?$)]As]A%=5(sS<(h&(07MoQe&OmUGKFp
Zj@>#4^)#rL_\h9^iL]A6$ciZh0dCn&6oL'Y_un(5<Y+q,IC`STi4op^]AJlT\3R#tnC;&sfbT
,qNI293B%DQEZ#g/H86%7dDJoLO0dd1A,b[R/>:Bmk=V8DZ~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
