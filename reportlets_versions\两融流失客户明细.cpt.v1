<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[2024-07-01]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with 
--所有需要计算的日期（交易日对应的zrr都是在今天需要计算的）
tmp_zrr as (
  select zrr,jyr
  from cdm.dim_txtjyr
  where jyr = replace('${riqi}','-','')
),
--框定一个范围内的明细表
tmp_assetdebit_mx AS
(select * from cdm.dwd_ass_uf_his_assetdebit_di t
          where t.ds between date_sub('${riqi}', 200) and date_add('${riqi}', 11)   ),
tmp_assetdebit as (
select t.init_date ,
                t.branch_no,
                t.client_id,
                sum(t.FIN_CLOSE_BALANCE) rzsdgm,--融资金额=融资时点规模=收市融资负债余额
                sum(t.FIN_CLOSE_BALANCE + t.FIN_CLOSE_INTEREST + t.FARE_CLOSE_DEBIT) rzfzje, --融资负债金额
                 sum(t.FIN_CLOSE_BALANCE + t.SLO_CLOSE_BALANCE + t.FARE_CLOSE_DEBIT +
		                t.FIN_CLOSE_INTEREST + t.SLO_CLOSE_INTEREST +
		                t.FIN_CLOSE_FINE_INTEREST + t.SLO_CLOSE_FINE_INTEREST +
		                t.OTHER_CLOSE_INTEREST + t.OTHER_CLOSE_DEBIT +
		                t.REFCOST_CLOSE_FARE + t.OTHER_CLOSE_FINE_INTEREST) fzze,--负债总额
                sum(t.slo_close_balance) rqsdgm,--融券金额=融券时点规模=收市融券负债余额
                sum(t.FIN_CLOSE_BALANCE+t.SLO_CLOSE_BALANCE) rzrqsdgm,--两融时点规模
                t.ds
                from tmp_zrr a
                left join tmp_assetdebit_mx t
                on a.zrr=t.init_date                 
group by t.init_date, t.branch_no, t.client_id, t.ds
          ),
          tmp_30jyr as 
(
  select a.zrr, b.zrr q30zrr from tmp_zrr a left join cdm.dim_txtjyr  b
on b.zrr<=a.zrr
and b.zrr>= replace(cast(date_sub(concat_ws('-',substr(a.zrr,1,4),substr(a.zrr,5,2),substr(a.zrr,7,2)),29) as string),'-','')   
),
tmp_30 as (select a.zrr,t.client_id,
       sum(t.FIN_CLOSE_BALANCE+t.SLO_CLOSE_BALANCE)/30 as rzrqrjgm_30
       from  tmp_30jyr a left join
       tmp_assetdebit_mx t
       on a.q30zrr = t.init_Date
          group by a.zrr,t.client_id 
          having sum(t.FIN_CLOSE_BALANCE+t.SLO_CLOSE_BALANCE)/30 <=1000),
          --客户前180日两融日均规模
tmp_180jyr as (select a.zrr,b.zrr q180zrr from tmp_zrr a left join cdm.dim_txtjyr  b
on b.zrr<=a.zrr
and b.zrr>= replace(cast(date_sub(concat_ws('-',substr(a.zrr,1,4),substr(a.zrr,5,2),substr(a.zrr,7,2)),179) as string),'-','')   ),
tmp_180 as (select a.zrr,t.client_id,
       max(t.FIN_CLOSE_BALANCE+t.SLO_CLOSE_BALANCE)  as rzrqrjgm_180fz
       from  tmp_180jyr a left join
       tmp_assetdebit_mx t
       on a.q180zrr = t.init_Date
          group by a.zrr,t.client_id 
          having max(t.FIN_CLOSE_BALANCE+t.SLO_CLOSE_BALANCE) >= 50000),
tmp_lskh as (
select a.zrr,a.client_id,rzrqrjgm_30,rzrqrjgm_180fz
from tmp_30 a
join tmp_180 b
on a.client_id=b.client_id and a.zrr=b.zrr
)
select a.zrr oc_date,
b.up_branch_no,
b.up_branch_name,
c.branch_no,
b.simple_name,
a.client_id,
c.client_name,
c.client_status,
d.stf_id_kf,
d.stf_name_kf,
d.stfname_service,
d.stfid_service,
e.stib_enable_quota,
f.khsdzc,
g.ssdbzc,
a.rzrqrjgm_30,
a.rzrqrjgm_180fz,
a.rzrqrjgm_180fz-a.rzrqrjgm_30 lrgmjd,
h.rzll,
i.DICT_PROMPT,
case when j.client_level_current='0' then '普通客户'
     when j.client_level_current='1' then '大众客户'
     when j.client_level_current='2' then '白银客户'
     when j.client_level_current='3' then '黄金客户'
     when j.client_level_current='4' then '白金客户'
     when j.client_level_current='5' then '黑金客户'
     when j.client_level_current='6' then '钻石客户'
     when j.client_level_current='7' then '黑钻客户'
     else null end khfc,
'是' sfyls
from tmp_lskh a 
left join (select distinct client_id,client_name,client_status,branch_no from cdm.dim_client_df where ds='${riqi}') c 
on a.client_id=c.client_id
left join cdm.dim_branch_simple b
on c.branch_no=b.branch_no
left join (select distinct cust_no as client_id,nvl(stfname_credit,stfname_pb) as stf_name_kf, nvl(stfid_credit,stfid_pb) as stf_id_kf,stfname_service,stfid_service  from cdm.dim_crm_khjjgx) d
on d.client_id=a.client_id
left join (select client_id,sum(nvl(stib_enable_quota,0)) stib_enable_quota from cdm.dwd_tra_uf_crdtsubequity_df where oc_date=replace('${riqi}','-','') group by client_id ) e
on a.client_id=e.client_id
left join (select distinct client_id,khsdzc from CDM.DWS_ASS_KHSDZC_DM_2020NEW_1D where ds='${riqi}' ) f
on a.client_id=f.client_id
left join (select distinct client_id,ssdbzc from ads.ads_hfbi_tzbjs_rzrqkhylcs where ds='${riqi}' ) g
on a.client_id=g.client_id
left join (select distinct client_id,rzll from ads.ads_hfbi_tzbjs_rzrqyhll where ds='${riqi}') h 
on a.client_id=h.client_id
left join (
select distinct client_id,fundacct_status,DICT_PROMPT 
from CDM.DIM_FUNDACCOUNT_DF  a 
left join CDM.DIM_SYSDICTIONARY_DF b 
on a.FUNDACCT_STATUS = b.SUBENTRY
and a.ds=b.ds 
and b.DICT_ENTRY = '1000'
where a.ds='${riqi}') i
on a.client_id=i.client_id
left join (select distinct client_id,client_level_current from tdm.tdm_client_tag_kqfc_2024_df where ds='${riqi}')  j
on a.client_id=j.client_id
where a.zrr=replace('${riqi}','-','')
${if(len(s_yyb)==0,"","and c.branch_no ='"+s_yyb+"'")}]]></Query>
<PageQuery>
<![CDATA[select * from ggzb.tbb_khhf]]></PageQuery>
</TableData>
<TableData name="营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[3591]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='3'
and branch_no not in ('2099','2098','9999','8103')
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select max(oc_date) 
from 
ggzb.tzbjs_lrfezxtsmxb_dm
--where oc_date = '${riqi}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2362200,1143000,1104900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3924300,4267200,3886200,4076700,4114800,3581400,3962400,3314700,3581400,3771900,3733800,3581400,4000500,4648200,4229100,6400800,5638800,4991100,4572000,4953000,4267200,3924300,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" s="0">
<O>
<![CDATA[两融流失客户明细]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[分公司编号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[营业部编号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[客户号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="2">
<O>
<![CDATA[客户姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="2">
<O>
<![CDATA[账户状态]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="2">
<O>
<![CDATA[开发员工姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1" s="2">
<O>
<![CDATA[开发员工工号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="1" s="2">
<O>
<![CDATA[服务关系员工姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="1" s="2">
<O>
<![CDATA[服务关系员工工号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="1" s="2">
<O>
<![CDATA[两融账户状态]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="1" s="2">
<O>
<![CDATA[时点总资产]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="1" s="2">
<O>
<![CDATA[新股配售权益]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="15" r="1" s="2">
<O>
<![CDATA[近30日日均两融规模]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="1" s="2">
<O>
<![CDATA[近180日两融规模峰值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="17" r="1" s="2">
<O>
<![CDATA[两融规模降低额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="1" s="2">
<O>
<![CDATA[客户融资利率水平]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="19" r="1" s="2">
<O>
<![CDATA[峰值资产对应客户层级]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="20" r="1" s="2">
<O>
<![CDATA[资产对应客户层级]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="21" r="1" s="2">
<O>
<![CDATA[是否已流失]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="oc_date"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="up_branch_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="up_branch_no"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="simple_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="branch_no"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="client_id"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="client_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="client_status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="stf_name_kf"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="stf_id_kf"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="stfname_service"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="stfid_service"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="dict_prompt"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="ssdbzc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="stib_enable_quota"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="rzrqrjgm_30"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="rzrqrjgm_180fz"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="lrgmjd"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="rzll"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="2" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>10000000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[黑钻客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>5000000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<=10000000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[钻石客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性3]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>2000000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<=5000000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[黑金客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性4]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<2000000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>=500000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[白金客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性5]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>=100000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<500000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[黄金客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性6]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>=10000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<100000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[白银客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性7]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3>=1000]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<10000]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[大众客户]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性8]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[q3<1000]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[普通客户]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="20" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="khfc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="21" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="sfyls"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="100800000" height="42768000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="2"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="riqi"/>
<LabelName name="日期："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="date"/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="349" y="25" width="112" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelriqi"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="Labeldate"/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[日期]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="269" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_yyb"/>
<LabelName name="营业部:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="branch_no" viName="branch_name"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="130" y="25" width="117" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_yyb"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[营业部:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="50" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search_c"/>
<LabelName name="月份"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="487" y="25" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="s_yyb"/>
<Widget widgetName="riqi"/>
<Widget widgetName="Search_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="960"/>
<NameTagModified>
<TagModified tag="s_yyb" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="s_yyb" tag="营业部:"/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.report.mobile.EmptyMobileParamStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="960" height="80"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="1" size="160"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6709E3A17B2712D6B23823F2C27AC440">
<IM>
<![CDATA[!CI4,oncL?7h#eD$31&+%7s)Y;?-[sJ,fQLJ-Z>Z!!'o#@ESWN$49?m5u`*!m9L*Y>t#XNpk
Th6-o&H1pkfAe<.%)2S#hING@l#bh6o+\Qe'RfXbf@5&Q0\oO=KdSc[]A%.!J(B2&VV\uUHp2
^YuFrD2740f2R^#-.sCAAF#Q,Ys4M1_AaeH%B',V5cFNNm?acG?c^Fh1s*:2cAkk?4+sJ3T+
sJ3T+sJ3T+sJ5*`n2g'1XaCuZP+L['!=D$]AG>a1b2i^S=$(qZi:fV.Euqi!\2p'4[1E7"#Cj
))E-Yg5okg[)RlXfWmtg[pj&>=]A;VMp@!/BaIJce6'13GhZbXs$Ws"n!<QnkYIn[1$C[X"Zg
R&r@XS2(KM2V.*?S3DJMMYs3deQ'ZcW]A*f@0bVR'HnmP'(P)#,K*1gG35$E-)u3LNjlNJgB2
S!E3+-fc2%>5q7IB:^H1qBl'@aVD*[_p\nc9Z:;e$qLKktA`$kVmM)EU+eJh;pX(P,s)#N.H
-`3DtPoIHjt%MCaaL)POh[?E)uI[E`67]Agl2kRI#B!B?u4_Q;^/h$J?Z[J$E[9ZWQZ8?M=s"
NR/mGRq2(8-"&]A9,lU:(M1JFQZ8";g*`./?SDdOplL;;67VVCW-qT*^sK8]AM]AFKqG?V&fW-q
V`^q`#5&34jX2_kXIPJ7MkgtJu05lc?4:T+H!-(?S88=BAi\WA]A2r.KG0[1BA?F56Le$ePII
b,2(4VtO\1UdD0m5eMhJhNMg/n5O.p%ZN*ikL5@?38R!hf:d_C(A.Jc!t_PRljC._g:tfTjn
a.8Tn_JX,NR1EC&L5;<OX;[D,-J0#!Y]AtTl.qQjg^a8!Ig\66I%)PRC[uRTI%*r0rmjS%;VT
"9hmmYLeWWIf-a7A&>!qZn43QQb]AV3t0.Y5$f"`:'!6"-P&p4Ipni)bui]A_4,SB9q@q94S:>
m"92n+j?6$SX)sg@7>h?f0fmcRdo/8c<7Ghh+mOG*lT;[Z*Yl#lOd=`rl>E<)q[J\B;?FS(p
i=4"CKEAI:j#4/qg[+<EMAIL>?tLm"R52Q-CZVII/nVcA*bHldQgq&gf92g3/+$;C5aF
B!c!Ic67<o;kG3hIL5Xe)lp+AS&l's9RW0W!1:?Kbj[g;`l=TJlK6TmFYL<=\oM:h3qOmM^q
XRb5X#hGmTc+hMlQG(>^rGn6_X=PX8R']AjTZM$%SA7S&4\3U*5_At(k<SKEP;^^ks$%7dq]A5
2fi4Aj;#<ID<VsBp^!jCe>qXI@P=+oY*\]AIbGOU4so&=Ij50*?lp^DE8iJP%r]Ak;0[dp:-\'
VP1"L+!&iohbAJQDmIQYQh_R-XHXn:piKnY2rt*R&ihYq`@-HFX_Z@i-^*/bEh2#5$>Ec6J^
m(k+P&*f+1UL6+:mp_,&&,Bu]AfbCu:Qd]AokXY"e;-okVNj+VP]Apg0A/u1!16f)>Aif^/VD>p
Pqg1p(]Ap797ds:01+s#kC.Vo7mYpIJaunREk;nFp>?e58`C$`,Y:)`nDd2@8!D`D6<<-Frfp
"K]An'>=aa)/6$20paSZDlO?fp6t-0Y43AZ7[q7a)'Yt1@@\d)CMR:'6;+$gjO0]A9V+jg&5m1
81u6SQmul?-m2%EWB)$`7p.k8c>J4jn*F?h:kAADu(C=:d?E/3&-rM3Q1!eA(:G-^[o]AblCK
^!F-lBU?P=(L%;O'MfEW)uk]AZbIBA5iMG@QY/T9I\XS_qJVTZq"0!\+SB#\k>k-?g><Y1?.A
!1^9J@nqA41IWY9;`-KV58>+*@)CoQgk[k.Gi.A%B+6'X0oP-;7C$8[k()%!\[0S$jj>6*TE
.(MVYYp3;QE7)g>h8(V0qLL="j&u<Ga:0VkHh#`L(S-^CRf8gRQJK:QNu9R>W!t5EG9:s$.g
M't18OcngX1)0F%1"?Bk6KU=.pD>>"*>L;?=SaSE;7#?sXk(6/F'Ge&iIRj:&R>I<^-eV4Y\
/#_6-XNU]A`ucK;,7oWsl^gG+N+\G^7+A4%foS'#e9Dm+B8`)(n#*S/LBrAkJWcNE+'eAh9Y^
<K4WW)`>WXn6cfc`Urr/I'#j&8LUK-XQ*1UnMqHlf+kppo+=f&R>A>^nnIX_#OEcFnlF"l`1
D,_B:O@nc7f/g,WM%I9%]A$g".@N$.H\ClYkJ0So5Yn0>@rZ:'9gs&7fkWEa"eLLd(I>&Cc6,
i42^+INALgQ@e51WJtoYefshIZ)eDEZZ)6Ik9JF+ltZ\WaFV=mrk3@R"":V\I30b*DNFr6Tt
aXHeg=X>J_AMD"$ljJg*h=5:QJZZGi'G?,(>=&-qb>X%8@*.moS#i\R->m!'&ugq2:uS8WSO
GjHUr*!-`]A!!(7aFC"\2s%P:<<1:n*%l`pSb&Kc^qc;%$HURV<llJpC674ub.=]ApMK`OgY85
bWX/MY$iU_t>p.,:!*6M"\fa#!ej5_Xo-L!=(>br92UjjQ/&3KWBSBo5N"6huK.74e(0P631
J>#;='<hl\d>Qb9HZHLO6A![+UV$R!dem-:EEVnI\WdK18gIcoD/rIKe0Fnn!R.KPV:rI3(1
*XnDF>XiRPR/rE#Wsa0^[(1ZN**t`5ETj`QlP9JCC&t!r^YH/BLI3(_#"P<iGVknX3Q&1=LI
9)Co:Z/F"X(\'#(ZHlT:uTS:S?YIYTpL0m,AaLW*]AcP`lJ=6iGuV=:KerZKmQ?WEs"\bj4TE
0W;`Zqd1--uG:.Z)DstJjC10RV!;,G:]A?_"\E3o2r1n-2^rt2Z2#6I]A"U_]ArGI.W7#>QfjDI
4_N8"2?S`"E79@oM^5c(T]AnJ?goM%hN]ANbkOh=tdVs=b[6Le?BrR#h`N)(?OcA(U:4Q2W/Nt
*Y8YPiTqp,YuT]Aagu;Kf&<jqXR3Wu;U:WrnV7GZ4^]Aj<`=8##Cp7a_',OI,D+/d'%:3(HG`<
cQ"U?W!-R:pgGkb'7f!aE@Y?S9C84kJEPS9I2fUR13Ec.5=gm6Qa?1a>,dSoG,s?@&MbI7VE
p/r!cU\A1dZW4gI[u25W1cR3f68kQjWU"#!B<M_4o&R6!ik#d&Al4*[jp)cTtL4X1`"/83s@
nm=;6GL8jp%S96rEs"[hb43q!AKj)MdTU0r:,lksqVU.EpgO18H^78B57KOB!;Z+$-6BaM.s
1&<U,cUGS.A5UD'*qGh*.r7RO7Q)AUU5Uh.QX^*92dGr9VC9sT>(U9V4J_,(uE=Cgt35+(#C
QQLaWe$6(;6S-IgR;Ib13>!$&NO\-III"@)(#$SY[ER]Au^ALcP7"D2-72%(%R1b8BXta;ck!
NeiLILkpkCLkpkCLkpkC#Qar+1WVn[%&g;-z8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="宋体" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="d636e8f2-d956-42e1-a45c-87e25c30606c"/>
</TemplateIdAttMark>
</WorkBook>
