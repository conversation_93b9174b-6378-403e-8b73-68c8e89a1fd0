<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="glzb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2023-01-09]]></O>
</Parameter>
<Parameter>
<Attributes name="gGlzb"/>
<O>
<![CDATA[营业部达标率]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp_date as(
	SELECT least(MAX(ms),substr('${date}',1,7)) ny
  FROM ggzb.ads_hfbi_zqfxs_ddfx_glzb 
)
, db_jyr as (
   select ny,'BQ'  lb from tmp_date
	 union all 
	 select substr(to_char(add_months (to_date(ny||'-01','yyyy-mm-dd'),-1),'yyyy-mm-dd'),1,7) ny,'TQ'  lb from tmp_date
	 
	 -- to_char(zrr)= to_char(add_months(to_date('2022-12-30','yyyy-mm-dd'),-1),'yyyymmdd')
)

select  t1.up_branch_no,t1.fgs 分公司,t1.score 本月得分,t1.rank 本月排名,t2.score 上月得分,t2.rank 上月排名,t2.rank-t1.rank 排名变动 from
(
SELECT up_branch_no,fgs,score,rank,zbmc
 FROM 
    ggzb.ads_hfbi_zqfxs_ddfx_glzb  WHERE ms = (select ny from db_jyr where lb='BQ') 
) t1
left join
(
SELECT up_branch_no,fgs,score,rank,zbmc
 FROM 
    ggzb.ads_hfbi_zqfxs_ddfx_glzb  WHERE ms = (select ny from db_jyr where lb='TQ') 
) t2
on t1.fgs=t2.fgs and t1.up_branch_no = t2.up_branch_no and t1.zbmc=t2.zbmc
where  t1.zbmc='${gGlzb}'
order by t1.rank]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
<Parameter>
<Attributes name="tabn"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}'
AND TAB_ID='${tabn}'
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[管理指标]]></O>
</Parameter>
<Parameter>
<Attributes name="gGlzb"/>
<O>
<![CDATA[营业部达标率]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="typen"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=CONCATENATE('TAB',SUM($type))]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="v"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$type]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.w = window.innerWidth;
window.objTab = typen;
window.obj1 = "";
window.obj2 = "";
window.tabnm = "tabpane0";
window.url = location.href; 
_g().options.form.getWidgetByName(tabnm).showCardByIndex(v);
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[0,1905000,1905000,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,381000,1446662,6141492,3275462,3275462,2438400,381000,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="1">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="1" s="1">
<O>
<![CDATA[机构]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<O>
<![CDATA[能力得分]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="1">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="7" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="glzb" columnName="本月排名"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__413A4477FA5DC879A7062C04A560B7BD">
<IM>
<![CDATA[!>5b,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$/%m<5u`*!m@.sO>ZDZ^ca
GpuSCsI$h(A.#Ta;fW;1gSq6pi\[@*oMs:lA5@(srK*01KI$-JEQYU*!3I-u'$]AgF^#nF7-^
t]A:i,#"CHs%D.B;l,_?%R[.:*2d[@Z7rh"7Ep1;CnfpQ"2b^*(Ucd'hk\bD]AQr6ICLF3_=T,
pb!38P2mDP*Daf,q2DWb*j+'&?YR?hr$_M6G/=E\?=g!?BO./(ATZ&RM@J8PIJtN4MY"?1\g
R5ipVJ$ZhbEldC>l%3mZ_c6*+5ZJns1AIi^h]A7=Fs=m]Ag-8h:i1M)S.[)Q[h`E[F0!)X`0r!
WOm4Wh1sp!.t.<]A'r"f'LC?t)8t9i0E?E&N=jN0op.VaK[J6P7a_sN'E%JQ?1W1:n2NM>ifA
AOAM@lJ8[LNq[*!"cG)gnpD7AtG?J;h:dh!-D8%:Hih<FUl=T\38CXGcPZVI4R-`@>Gu<@pc
HE#Wb4c7Op3!<$ZJrM$?g_Kl/#Qq_J;O+QC6JhCrk.f'l",639@\"Q_"W&s'CV^-K(=dK096
s(@?!672X`;ko!*!4S'COiJ1!Sj3pWg5M%Ln]A@7!!_>uGEa)f\Nf;Io!=pN^\2g!k+P]Ade$^
^OH_%=8cb[FH@9HpqR;srcEbVXLmh:4[]AFSsM*-4*W]AKu`HMWuEO*Wg$W80V^M(F^&>$3Ud&
O2+1,9M(jp%SXSe/8)\)c]A(]APAnu(4!H*p&-$?S$SJN1a_4o6^;L!/NH$MBO&:sEZMN7,H=c
qo2%1[o**+p(^o@5[6f`284F'NXBflJ%>DSVBer0aX0=ZA8K\"k3e;e%Dq/ZJuaC>Gj7H7o>
d>:8@klekT:EgHuSBFNhoElVGdE*i[VmfQH*:``$d*iY(oY/:7K&)_=*\@4>JF#6R\X&O;ur
0cJp-H0.T<1mAXqB8YlC]Al:c,b^EOEn?K73(?m;;nn_!1VfN4(/R<sF[?HPa1M]A1:I)r$q-*
j.\H1!lh[nCYMJ6\'k_IU3V%FWtX;+bc?>t18LAX@O3,1e!3k0e\go(+FGh?eb.cslDYu[k+
i6rNOO25\f1cU-R-;3duQ[pAXebi#K%QK)j*5I"rEEB=k3,uXpED?TIhn#Dp8V$\@"DhrH;Y
47m!:DZ/!Sdp!o9?_1CCP,PHHLW-YP?0]AJf=d$O.dan]AlChga8n+*3e,Ml?&P3PB2$Yj$34r
bi6E8QE8hl<pQq8@/]Aj6$!aMV[_.=1ap!\5>SghK`nTf^dRnYQ95JYP/5^-^'!Gua)>X9$M'
M[uP4b8CHg/Y-H#D[=\;^(fE\>#dJm9`!XpP`mD*M,m\hCIaigeiUVe8O87&?Gc8B9fhlTf+
iegbZFa;lOM-c>0;oi,JB7iSAnb%KjR"a;oV+5m4HQ`OFS)c5P$>9&A+!9Z&_ca+C;G7!SGo
]A\iiZkWrM`_/2\gjYkEto9HU$ZHFIDSD1u6;N>@J/%opCL[-_MrBo83=M^[t)km#B&Pr*+>g
4kjS[?\H#gO@3TWd[PlP,t7o5ZZgE8bVZ!cFLo@YSBhgIuTL>+cH>\FKd\9f-*Vlbt_8nt-=
$:7N7R3'b(Y6bV#1\0Vq7BKk[F`2BW=$#XPnXukPZggBuNlGA,HA?Le)S87\";;jaHH,Hh1-
N=T+NDtj$$?nWIQm7p#`-uAQc?6g,$>]AoJ*nPmoEqWak'OM$Vmff$$qc<P!c:@JPJ(<^>q('
tEgUS+j%GqpS3$kGAj"IkF:*C(9*SKE4`MK.E^<i9DD$]A=EUGW6Z;_Yb:ee24:KBoB+&n4tc
17tGab8WtErjS;Xm6MnD)k,_P>Q%em=L!JLUG[UGqj*Scl0M,EUcWa^1Ri!0!JRE$K@PK.;K
,tIb3@M$c28X*N!?V`LYH(iVe5q+Gekj!m&Kmt<6QeIaU6s+B5'XZUXokFR]A6O1[.p^=T`qL
AVtHu\I@M?ab?=3jRXII8#I"UEB65,=@A*Dag4gBRIj&-;L,e#*W!7Ke?Nl"%AQH$-IC@"Il
`,\uU]A@fa@@dcb"t@^s@g$crfBHQd;26LtfYO6A`=8uP5,AFqai^o'SdJk)la#s[,c!o[4HB
J"ZuRL*CS52Ccm$F:cK$X#HK,T"Ynun-g3q.DRi,Z6e/55$c@FHl7@cH@?bdQWWGZUFi7S:=
bH<KHCXE,9)hG'<U5N?Y?(pk`?Z^?poPrE+_("n)T,*;GYCu?Zq,_5c%EYO'GD8/-\HpKSk7
Wa'Ec"8!oN7I<Sqn+$ij_(`VEJn!e>ij05"&h6>D1Jrd9_uD))ur0,Ci+4)JRhWMEAC@o.Z'
Z+"t.`n8:Ybh>I*a1sL*?H.q=+]Arr^5">1%Cac4B$&V?hCo<39=c7ABIZ%<@bd>J)_D0DL0o
#Cc[8FM>_Rg.>7i!tdR8faH$$ufoNJGr6!?/7>;gD&8X$4amd3j^YLmh?biV0`5]AfVcNN2@g
E[g<jI#S,G\MIZ1j&7!eK;gA^3b+!mY1%NmE[3ntUg:EqrHH`Z.1;\$DDjduBL#F4f+J,KB$
TJHYGG?E^PSFZc73=Cg?^dHb*o7%#'"G*?8@?)M>i4:9slJIi9b;!P?b?TKF462`K4L!8V2c
?]AYKS,D/LYfVB;O8U\.q5/sTge5\i3O's[mrN@[_nQHe-WXR-*Mr1j]A8!,gOtHC>_gJ*G''j
\.GbI=5Jl`0F'dRQEa4ARMGLDoSB2kRQ/uVH8;J/=GVm<8)Wqq_XAujWLYGRIe5%MZCCmOQF
1"[3F+?0u2rJ?R__XfdKaYJKl./u7JQbXZWA/PG5N%LJ'Lp^Q>s:HZ2UX<%^C9%&T7BQfM)I
tmA2^[1Z.M>oPMSB!&mkjMTa%kf!%1i_:-Wf=$2Ug>`sli^`Un_MQ'TUeod`"JB]ABc9ktY^"
*fD*D\BP#o"pNcdhiOK&j4di&"54?GneV9G)^KC0#C$M?l?a+)4m_LjO`-W1:4&9dJ-"UKJ7
F3;:+,3j=_'3od,U7lXu3R_M<>GNm=aDBg;#l(i#'8Ws#6W,J+ME"1gZG@nJn^467"8b1e(Y
Ei$_hJ6hB,n9$nuVQb!<3!uRkLq4Nt!B4,:RJ38"gK%!L-ibo=IHk`PY&X=`"1]Aj:dWKE_I1
=#KMYN-pgid8@:G"Z4S=JYs-E-0;nJBQktn&B]A9>pXAYr:_>9#=O*LOH4F4'#/W"533PYh[Q
ZL.g%;gFc+'<i7L;H91i*FP*Daf,q2FM^AJMT(Xr0kOK$_0!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__C6BB7A0212D9D7AF5B063C7AE49C5CB6">
<IM>
<![CDATA[!@eH,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$1UST5u`*!m@8N>*5rr=a0
?(oi`[U]AU@dFDJS5]AhOe>P7,=7[jVj1Em/V19,X&R5KD:M%N84AB(R)J^Smd0;rmDEfSgDBa
1AfY@oPR+9QH%]A(q+B>EiU4e[dB`c3Z+Ur.)*;Z5:?2OQ:kIYO'cJ=Vsg;iUKb^Ah;^\uF;N
fZ6>.8Fl?;k2h]AV`;XE:;nOEpp6r^gAV<?&9YpZb:_P!P?O\NbODPU16\^Qg`<?/T:u!(P:]A
6(kj)&nomsIJg%;=9T`rjrnLF0>LeLgei5BFm*&Y))I2jG0'eQp5$5D^3.KC7LA$lI-eLCP7
K8[Rbi8t/d]AFpJf!gWSO:K/DqS'NEBB8(.Di[8l8s&sXA&->l?gW`n<s,lI=5>4-;RSK4,"5
TMiq=eu44K-qblAg)(Yf/#"`OV[FSe!\GA?)XE>`4?Z_HQpr*>j9g]A&#eja0*_jf(B%;&HH$
ir?^)S!ZV5#P(5Su:>2BE"E98!XpO$P"5HN)&RX'ncS?J(/bCdeV',di!46PhB[mNOT\tL6H
kkQ[<>BPn0TNo"c^%Kgf>)_/:]AU]Ao&rOTuqq->t3!+'/`Zp5lin3pi!*h%GE>S'!(H-3mZPM
`,S./XRJ<RPHql9a8R64NB]A/a=%>V_ipO/0&aPpa01.2^24U%]A`)-V</"BSj)dZo!(VXoas)
2\1BeF2SmIcKoDL4'+)]AeN,[t0.X'r!N=8e.R4:jL]A+q)_+f(7+h5g^#N0l#m7?3Mb_4LJ*0
HNqE+D7bPEjKk0kjp^`Q8nHVr/8>Wrok#!*9\%Eb)"DD(!g5E;7YXRfq$\K1c3;HnKIV+uo?
O$)mo0"2HeQge*+O!-!1`MC/Dg'mq@GR8\Zs5mICm+5hSVE45r);EV:*'"Emm\%%td!3UtEB
hG&?;:emtiSZe/jeO,n!-_unbHB?3Ik/ed"I+,(pRncO_M_('Fm#GW@)3&_I1bD?$GdUa\5$
U`?81#^F$U\@QEumm_7HqE6"O<bC]AL3"^fK;VMRVe8^q(KC=qir;@=e;(.Zo;=WC2eVQ@O9G
+PqqP(+fZ.NG(e*!o-HK1LPofY:IVShcbPGWb$/5$2>%)nGg3BcmC;>]AT41<_tMef"P4'eb6
i'3e(k8:ee5K=5VjU<<WEZgY-!.l"t*!3rr@be(cetMmlA+?Z7V7$CM.*]A<<KB[V#o'!W9'Y
Q5_[Aj<@7N(P\Z8c\cg39X"(om)mUQB33#XLXYNp$hP->2*L5@d4:b^P3QHHZaa-LVWW9Mf,
$)&J%Wpb^dR4-(.@Li-_0UGXAU1l++(YGl=KbV&J3u,+eSR$=Tr']Ae*<)V!)'Xeqr!hK@s/?
DhIY!s@S#ZX]A&R$.BX-c\g`!_=[/N9ffKX0PPh%G&aoka8aLR8+C1Ep_:M6n07CP&r7W`8'k
rO9$aO"b@2R/5j]A8.U,IGJ3S_HN(N5[Hf0.m]AVNaS6XG##W\%<G(hDh']A<jG#=nN_\N\^UX4
pjPk?lYST%#OuNKJg.Vu.IXnt3]AjTeb3]A#[<;I"lPcQ-P%jnoIY0[jr/Cm/e=HO`M>YT,6aF
TLbGNkrj?9UEf/6&iUHqNgS^p"K4&gpOA@O4*EC]ApK.9&8/Pggj:CjOa'HCSkT;"%>/>e7ha
*#uuN47k65hM.[D&^50fL.=*:3XX#!G+\Z@U'2!Z,[-&e^;UfEqm4m%Ei,!\oXA50$i&75nD
:g`c1n:kTQH]A*Z'_(F%M.*NYEH_m*km2m%!"\7/"MQYJ:P-3`sR'X\05VgD`foapFJmU%W3Y
YbdcD7$k5`o)-cadC8:pjpX+\):?%Gh"Bp-`E`HpVC\Llp7cu;inJoj*kp`lOW%=RHPsqim*
g:!m-@YmUiH[@-ueai4\/Ja=e^'5,C]A7s&dbko*4RljGV9,'YtarF8AW,L]AK&1uj,YP9@O#2
/W3ceS"!iF]AA2;&ZdD^q[.+R^IPeA%8D(iIjHdn?a6OJ"RPS;M+oSmp&RkFTZr&t+-bF"?_[
DbA)"m:]A)"t>`RnZ]AC5Ft-HU<t.YC-;6a0>*+d:!91lsU9WN"aTh?/#rN_?hBrEo6sA@V?uO
@!>$`FNK0*;cS';OTJG8XH=u]A?ONm[LQN./(Okf]AT2?=$R59r,]A0,smeV)_F$R]Au`kq\C<r(
gP9@H%A=MgCL^Mqai("$o%'\2I0VLakn9[]ASVn1%_MW@M&=uA[j"@tV8LQSpd%6O=QR0bhs1
p`(of:h%=rG\_,5-#bG`p?8Oo.L?qLia3GNrpd?2?,I[S2]A,E4hTNV.Ce\DsAg'>B"M+I:Ja
2*@WsMS;i,b4BIDZk%2f[>n*c%2qq6BaOX=?!P&&:)C&@g';uMfo^O]AP]Ana8/7g]Aa`gj0[q8
/^=q-LG%YKFe7b$Pk,K'nVY>>o4]AR!Bbl_B7TRo4l`Z('HAoa=!3ZUT";S18g*ojj$,#5i7h
-Ci-mtublH?@JSB-2L!A-fWOtuMr(UaQ)_i=]AF0slXT#HFIBU/ql+tU0$qCOj\,/HEOAm5^W
E//%`6^l!#poII'()P+.R1^Vnomp"H"MUu#@'qSKK$uK(K4]AtDTg3O\:l-uYn9kU0;bg]A,i2
t.D9,n&P9>P@aq;ODhcSGQFNV%Gcc&4]A&JUdWF$$i,_(UXaQNte*m%MOIXeqee9Drt=3>9&@
#q(/W3$@pfcF)hlbkcEA"L_CCDoH?5iL>tL`!:D\Fm5jJZ,>DSO>gN1\?k'$r=c(`fbPMSsl
&W3]AiLk1@BYGM'IIkR!N4NI[T[d*K\3.EMC(:/chA(#uWA@YOFT/<EUp[<5_jY<@-g+Lu0>Z
p+Qc<]AnD[`a"H><EMAIL>/%L&$68IW*[55J++Gc$p>X:XW/Oj+=WMi*WJnFn8;6CX4
b?Ac@A8gI#[!I2bHVSdtHY+a?21h%'Q\I^nMFt<:LZ\CgJ?,k"E$;kq+p)s\6A*d+^M44Jlr
2aHT/YX]AV-KYd1@ea'U"(nXH*.Hl[4Ee-RFOte2G5p>FWUAF6[kYl!s8o3S/_`ra7j$>4>":
6:`M1D^L4SL3U1kTANm+&$OiQ\Y=F\e!QT\_:5T!FU0oG556>4gA%S?*oFdp75g@\/<,hZ*8
*7!Z"V;.hQ`RoSb0Dk<,l]AgfLsb>_Sp#B;:&Eh65Bnuq!Gk:"rsE!5c<k.mi`:0RfBKHiMf[
327fl4o%*(R=(pDuT1Uq?b3qo,rNpQ/QpASYe4a7-/^iHgf/l$DD;k2h]AV`;Ypr:F>pY&*d4
+$MI\!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__941F354D1F9462B64D43B5EFBB8169AC">
<IM>
<![CDATA[!UU.*pPD^A7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^#dd/k5u`*!m@7s.*7YeEf5
#3#+b;l[#XQ<B,!^emPa7Z[UHFePHltO%5bb!<.$YopLuW`(B@dGUJ6M`"J0Me6aj]AeJll(a
H#m%"?%>h*<d#-6Ud\]Au/5iIhBa:n/,pWhE$a0'W!2jmfmY4U>kbg,!,pG_8!cJ1K#Ma]A'e;
2S89U(jFP74Ci+.PBg623M]Aq.<UPcWHZbA!U8/&PWBK.T\*(p(#3m>mm2@&l6(UhUP@-HZdW
Y74V9A)/T+rLqXJM`2ltqD%2t[*f;$tp"Uf2bK,hQ;!ar16XX8!?LB*^BCEd^0\-Uk\=r.6J
Y%ECu>`KkI"PnuU#=g9YKB2LfPklWi@>ODH/u]Ao;VptZj$Y@gnBGS/kh2tL5\X`C"f;tb!rW
\>hNBGgfWM(8kr"!c,!$Ju.]A>G?H?N>B\(fpasCg`clVGNWd\V+_U\es@7q)49h.&gr@YCUI
a!npeIF!\jJ-,72:!g.oFDeJssOTt&Y"W+LD]A"lJ6W:a%*$F16L2mYXV5X,ku3B8HucIE(jR
/IW4RIa<gXJjH:Wc8)qm>JKWdfmcL)3,`dT5PHrMrbbT)&2(*$DmrXm/l:Z/23C82>^Mmap@
C3Hme*\?@_f%U`GhgJ]AR:Sj<jhihe;(0a#h>A)OqMC7XL:#m_u^@!`_/0fi/m"0#2TiT':qW
^Lq1!%pdfuZg,BG$3KQ[!c/5".Gthk^srE;%\-]AFeIA:Kj?(0-GenM_i$?Y!NYda:E3_-Y4Q
'rT2\,;?2Xa^tHNH/bOPWA)7:/%N'Q)d?0#0=HlS?8H!4[=kS3ZCTXWh^71#/fE$V^6@%<pM
?A6#\0@T)#%E-6E^<5?bP@-[g@bBNj1JPh.<hkU/647oA?<:,55);q@l0!<$eNAh2@nRf6TO
^gaqXX8"0ia%)@f`&CJEF)KbdqTh0mKVHd@hIKA$IS2?4"LsFq/>9!&P2S)!ZX\%!P#Om$^b
#nAfk#3K+5k%1UoK)rjgM)+1^#<GX,KPbrWNV_:@L]A%oAA$7hj\8Wk0/IUtWW?LW9Q&bl_kl
i6DWl\E,;mCK4FibkN`&BV'Bc%6a,)f1,#+7%jYE(c57\Ek_GQIpYK\I>mb5aZ+57quMKQgk
lk1)Qtf)e4UN+n2KG&-`U<<3Ym,gdqWJbPH&oe!>TCB*?oIY8Tdg8*\^WhD6hZH=\E$md%74
E[G:no.0=A.S</7ga4("e2.+:#fcn6q>?Iiac?%\<W_ui_.XE&\C'"Vf]Ar8:SGZu\Q<MWn%e
Fm-N>p/(l2Ut&-H`U+O`t1hTDY!Dn#7QV6<8tbShY)8l]AT%;>%sn*,VU!@V:gYBL>LBNi6Ld
=WFgnC)>XD_Y:K8qMh1Jd6k66Xme`1eeXd%EP/=#CQ:CIpWj0fWbl6!3o\"Pa9GageXE'@U;
X!1k?S(TtqSJ!>17c]AnI;lZ`^i;Q#+f*hnEhGrRlN-Jn5N)n([\A"7"eZ;$P@Jk`po1kE;Ip
P[HO"bqh624WrkW/8H"(msR]Auu_0Wo:U-_!<?<HT)#o(P#2bs#[Adb-<nHpHS$5Y_sM3V::]A
[Jce=UL4!4dSc7C7H)d"jicI8U:+@eMO1;k:1o#RW'/GlAP`b9Yg/O'&<$s>9<bIP5$BHd]A(
92dYT\q6>`Y,;mDQ67.$st:XOgtO;ek;Uj'amd^eO,U\a^Z&QU8@IGU;:2@6E'X-T\si:#4k
fn5)([16lJQeZloU>-PnOhirX5NZY8iWU!5^MFeR!$bN<SZQZMQF6>>h'Diibm96s6KS.3+I
=15&2JL?*6Pq?UF9$^U";"VOa2/pe4/q$HPq-K`AY&3p\3?8MHghpI^Rt8$O!4C[.f@@0='8
R/^mQFW=Z[>'f$5QlMc[#@Bgk"&eKF(sE)B@U%BVZ_M%K0%nq/Q>tEhERGNo'"&^P:L?_rr>
jrH3<^a1GqdKOq1EOsX+=ZcPQ[Es_0U*'<Qq'O93eYN<or=4g"a^W\aFldCe*8*\C:"fE0f,
R.s$S]A':Egb?OY(FV7t#>a$=s6=>lkE8Z.#`u^kXp[o<m0D6(0Qtp_&b@J;*'IU<322skT\s
j&)8i=,$F(OXa5gNF$+#e/3,fem6:VnN]A1d1gJV'WsB63f/I2=*0U"?k/QE9e1,7kq%\>*nb
iF11)3!8\o^Tje>7U6f]A\2H497f#&-$QosDF1*XY8e-C;Cf8;pCb_Kr<fYqA7i1fM9:<20Ho
@]A>5&]A""fKCf_E&fASV9[,SJ,1S3!MDuF+4i1t<C^Be!4ImS]Aq4_-#EY5PDRp4;o1hYkq#SN
YO.q@o_)ZLG/[#,#+,/??>B;;Kl2c)>qjVH/KPMk%r1%hp'@`Z>]A[iMD!tK[]A(V@Qb:'0k,.
EoP"/ArUB-OG_rfL7u4E<,D0g=+=k`6t;]AC*!#>(`I?A%_QG&GebA-m?CaX\$+;YJ^3TdfB0
;f'<c2ilaHTlES/g4;B=CL.?*1qmB8VahB+SM6^1V5!B6d'<I1%JGlYPi32j[?HEh8J:clQ"
0!,fls#G%+7f2G\kA`<<eGr@ElpZ*fP;n=j#CuMAG>c1qF,6CG"D;Fn(Lm==J=q;uZmTS>`c
lde\>^*[ns[s:[QEjHQf]A*fN;<f!R^/O"b,@dKg1[j\6>\pgi&4u\-"979(]Ac#]A(H[,InO:E
JV:AqoIkchm2N/r$mQBe%SF[Ykn!=>s(\t^<^2;pq62uZs@)H(LVk<*lc8f\ok57*Fl%+&=!
bsZ6XXn=;Meh%Wrbh9ig<KZt$(SY;3It]A'k#:OT=jOpu]AG.j**r'4oWRffa[Xlbb>XCTlV_$
&[aKYq&[&O0DV'nb]A8l:IDPbM-+q>g^?_C*<C!@e'L!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="glzb" columnName="分公司"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="glzb" columnName="本月得分"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(LEN($$$)=0,'--',FORMAT($$$,"#,##0.00"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="glzb" columnName="排名变动"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(ISNULL($$$)='true','--',if($$$<0,"<font style='color:#51A579;'>"+FORMAT($$$,"#0")+"</font>","<font style='color:#DE554F;'>+"+FORMAT($$$,"#0")+"</font>"))]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="5">
<O>
<![CDATA[详情]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand upParentDefault="false" up="C3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C4"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="6">
<FRFont name="simhei" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="0" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-12485642" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCNU"dp(EWbX"@#5t%Lsm3l"]A6pRR$!0JUc&4?d8.7+<RP9>`*2(B6#?<!$C5tYf;#bd+e63
n0b)F>hb#U/754Qh-bkIu/Sqn4e0k'lo]AbfH+>?i.V4g2!nb>AO1n]AORb\F5Xt;%Lrsi]Aj!G
aEcCB1g%i\e-i4Pd1B%L`a('q^c"j3UW=),NX[c]AW>4)d[6dq]A,5C0_Uah]A(ASWSi34a#_I9
U7-:hKF`VMVuT4kb0EXI[sQj\+i/$=iiP7]AokHtGCRK<'p5"]AC7#=m8PVp*9+>b_Z+otiB/I
SSgNugu]Ac;@^?#Q]AJ/n5d4Nb)li9,Y_rY3@VFa2'm8V'lQRL1&%,N;I=BhDa6.A7jb<`R<Z\
$77U5]AtM(^^lVu:afIb^=ZR<=gAH%7mIX(2oF(uFZW!\.Ti-gHD^)ke7k3>p#.6"mV&?aZp2
aKXF3=\t7PVF>-U\?-.@.,P]An3AWW1uUQN]AYCR=Rj%RS+q8CGmP:MKB#[BF8<QJ>2q\WHWlp
E:kN@2aN;:&l((o/9E)\TeOU-bGHu+u*4g`$C#Sc8]AIJV)dJ@3Ep6`53s1_[hs7Yuh`4>on1
cbF3]AVZ_fR0KY:#!]AoU^S59r+6@`Fc3L.N'j0ANq,nHR?9EQ]Aepa#O36mG8gAeW'O'hh\Iq"
lH/!!n"/kP969:'N.-]ARM=27Eo.&0[NrlSZnshN2&`"u.0kOg2%3g]AVJ4P'eMWjOU`)DUEDt
)N*#7S2hL>mjRKF)dqA(J&h^>H3p,+:Ett.<0q4N]AeGYE_0'T\"q?BUT?LPfM+#]A>#.f@rDp
4Z5a;Mnb)fGt?`mO7/Cq@8$W@>Vg/TP-5UkZn"1/g`4Y@*=cFe5^283JDZV*1_?#=mt1\`T.
hq-YL<ac3e?EVN=pK-;2g6c:h`HmpFQO?Irg.fQq&khFgks2HS;,=^/30EaaECu<[LWGI&FG
3-c&pki(RT?HK%e>2lrc4:enj3Ni2'DZ0Z8Qns3dQf3\nHO?^WJi8m3S<PQ1h55Me^Wb3dGX
IdWBPc-7V!!K'%\?DfMk(jFGl]AfUP$QjFAq'.iU%P!j;Rf(n\U0*$$eF<?0fpj0,:n/%I':3
-:s0(,Ao-O)6YU8c15<9b7tnrnhD.R/^7PCPo2+Pa%YW?d/'&l.>!U1YFDdR[`L%fo8+3#XD
,X'HZ-GW8]AjH2r2U/WE^&=K*6q?W`)4!"i<3Y1s*&GE42iqVlm2YS<.&1hEZT>1IIl'^UM*l
hd2>1g$e'RSe<e2\"(i_l_hhlc\JZ9!523Y8&%7DVH[C_"i@W=?M).@6U;7KbF";!o<du_Qq
5(dQT[fYJXCFjL=so.%c$rnKdFuO%L-i&W%@VCHdaBQYTsClX(pD6=<h0#\#6&.$`Z'o0?V8
Dl;l7q^X4[cGo1>#8cX:39fe8lZS%@c(0?fK#b;CNe?;[;&TY)W+?G-O;(dfG\acH;10Pc(p
b9hIn[m"\:q./)%UpDD<ph`E"*'EAlXcUlM@'j%;1&T*`_P67KLZ*S^J\eJo3\:OU>8hV\eo
XDdi7=ponp=)Ed.\=&[uu]A8FP]ANJ9e<N6lhZ]AIE!`&AEHJfRa-]A!T!g;3UN]AJ?W2@A(TL&kb
gn<F@:)=s>2nqDgk&Oq%MS18?B;AElDI8udKNU+dcV4<:nS;bb2roEn_$.dGm5elbi/K\'J>
8OcuO<?1Up5D887c)_cPW93YA#L7]AQq.!-K[C4&L1K7VCfJ$5>Z0+CqkWs()j&'<Q%Q;WZG0
#R,Qkbl")G9B.']Au_n.*m:V4;msS$A9EI]A*6#9+s,@C,dXgYo^8U5G'fa:4uuR?[?SU^q7">
\2Ge0AbUKG7HP>qG1Lp"EJ[F8ogu$GGmJ)iUNZtUE%(9do#t\oU8p6T#C(@f*#\jmi!kk=B#
l"FFjPlHitmc-Stt_hcD(7,!ZBYML2$Tdl*(hJY^+@<KPZ(;i3s7DbXep*\iF]A:*HUou^=,q
0j;s7FeSoR`#@V_,A;':'>tSStjJ&?QrP_)d%G@3!O1di`Gsk18,4pO=4k>Ug`mWq[NY0u%,
=DXD6@QpEb[5GP9&[C.o@A?YZrl+3LjWuE)p'I(^F9-A>R<OkRE@AZ`c=V<$tgr30e-iW0b$
I^]AQh-W*\#0r2JCd>0i;3#<[le`<"r:"KdCj8b'so,=)ZD`LYA@RD()&VK=cI#*boPVnrs)-
?_sE&MO@[)d<BB]A<)+a]A`-F5]A2mBTmo7-\BTX((+UDV*`BW%`Rg??C=7cR([c^Ej482O^\\K
T8MAc7@KH#FEl>e"A%SW#bL\U<(k(rU0LA)G<&o1*t6NS^;Krj@[[ISoZgDLmc.[D2^XpdSl
tY.sSO=@#+5`7?b=^<+Mf<ljBl=/s`aJHUb%0p]AlKa6SWY3P_4UgMLAApb&GTNs)P4s%#,2f
Iq[b,DZ*[Jf@tO'LA/Wk"U.seir*r<\pmOZs58'7?^NuEU'.iBo.p)EN;mV)hf[lSm`+pGU[
#]AA,G?YIq0XdPre3]A5YkOH:mtcu\A$rEX)2pJpk1d@'>2jZ^&LClJ]Ak17_Wr_u&qdO0_k6+7
MJQo'GYQ4BGe>.Fb2<+WB[X/=FdoqPVDA!uGBhQbFM;aX3'[t%:bhiIrS_gEp@?=lb[)ei0p
@$`pQ<S6Eut\aGa.90d7DV#pJB1jI@%h:!C(II]Ai7cZq\-NQGs]Aj>D(DjL6D`GpD@fE#>p#r
EoT76]A+pI,H4F#a&)g=@W'@lWqG<^\2IOE#<_uLr-c]A;GFBo4o?r[bF@2]A?aMI\0YK?XhF1g
=8WKTn6-9f)(Ur&?j4H0it&arUMa<Pka).Q.^$4B^S$Qj-.1#bqcCkV#$MQe[?IV)bNZnBh?
#e?#@XY)?*4do[V=-n?\O3Rm)Vd1W>;eQIA]A^dG4.n"h"#(r<hDCL!IJ%Z>_l*,8J[T&Us=k
K3JFK4"ij;J4k'9[/OeREG@L>+;=rU]ANHbD&1GZ+ZcE^gWYuiefBO^MRB.$>H[,.9"P(4n8`
M7ZHR'IuTi$5,RhW^s?asi$fp[4dX(h-!8GI#7?#t]Ao%,c(.(U;joP'l;]A>JgLc>,RB<X@Md
&\-E'07N%X%"`TJ!e_E!--uuO;m9&RO)a^mBO%Zlf5\gcU:-ITenP\+DijXlQ)EBJ$Z54C,G
Sf\a5I.P]A*>CsqUB)bqEK>h6C[u7P1(JM<b><@+-3,2##Is[\2o)O-p1TURNX"iKb=Goe*b_
O3YdH.gNWn?cBB71TJ:b`g)J50NKerB;5e'HIGfE2;A-WkZ^(]AX5qFl@nf+'\V..LK0CYHoC
5>kM=R.U>nh6tUUnCA1Iib/[E_P^0Dl*ms74FM'CZKPQh"jRXqo9k630aG;aOF/.e%+%7*Xc
8fsS^-p*1^7'N^r-Ja9r>oO!_.(*mK;t7=0J%*%M-tJHZ4A.`c>3[47('El;#s9YmnHHf-d1
2<m)*t;r2DNP%&I`9i>m3Jf*oQc/nKpNE,Wp%JqgbYI<jD&csIVXEMN7b.2KKL7XmXZDPVJ=
c)='Xh<:&6[>sul$W24ijK`7Z^\[JVl"ke>9Y46%HC/PC\CDg,a`Qpf7R5h<nkT)>G#Cf7`#
\Y9VSnp/.4lZm[41t+#f0Fd-:+2(GE1u\/sug'ts'O&ud]AL=dD!*UUlRa!irfEL@;3H'KmR!
\]A/jA2_5;$F+&W$Hg&Y_gi(hQp"+2qo.bNA&0FPq#rWB<Z+:duB.!'>2.j2K!OP>RoR?ml]A$
rOt5B"Igp1.8^OXq*5<LVkXMieniJ>W:2%A,m*)Y)E7l:?p.c:pU;/EJK!6/ONgYRLCs1sH^
CSJ'"gCWUqh@)#U'E6]AgAch@ol@09BA'Z,#&nX3[]APrcM9,o,T11*VSBM6l6m&hU!G3ai>BG
L2O<K/.$X\N`R\b!3k.;?$F9C8ZX:Z-1^>m\0?)k[7<kJO's`Z"#XXPBiNEmf"2!LkhgN]A5-
rZ44\#Z@3$;\_&"H%M!$c+2d.J^+slk8<sYB,6eUR46qVf\YI0+hMm]At/d@lOk,O^&:`G%U6
Hr*UKmJC)?\n%'cnF=F:OW^fnAQj!@p'Y[55M368(!8\2IM?t;N7@N?C<4aN'ca%'4-R[&ij
PE!QfS5F>C^c4pIR/4[F3%*CVF$U!/tPm,TuHN#>$m:>M-TOTU1<<qC37AQ$r$9G#4_cg-D"
?buk_SKD?)LOo;R8W""`Okk9;K@<QH$(6Mf8ZLS0C[J1BoQAO9r5&f,DH5$NW=[j2Rp^.k5+
Rd?h82Oq:VgaA.p]AT(E`o!s[j7c,]AhM9'XSL-.iLlZH72IF,rS@_1pmm?g>(DL05EWJ^(PGJ
fQ1@=7-0@ERA:a.-t(t,8D34Pm]ARRFfsTiQ*TliSsl?T2#kDu64ThU!Bm[H@Y;Zse<nci1WF
+[*OTrtkk[RPkNg*CQRPZEiPK]A?`XT!Y0WMT!iHm[t52*aXsaIcZdMMXCJa9N<eQJ9A=e\nf
l]A_VVASnXWtetY"l.P`=*,IAJJ)KZ.u6t-311._?GfR<=C!nd_^)`6o!^rm]A;Xt]A2Wcs:rb/
-QblSr6eY&i6`LthAk?h6a1bV.G0NQRXu%I=+D!`4XB`/HV*\gA/;<9DWL\Fj2*2ub[Q(@_"
3`JED?]A'=h_9;l2eWU*[l4b-S)1Pg@;:2:p6_HNn0S@g9+g905XmLdlMLBGbD$P\#G&^kQ:G
DM:1CBI%O%JSb8]AZo_L0CU/#2:0!t-kr^),dKhtAVsG@-6El1IiO?(VpN\g41BDf>J!/J$-%
=7!&=[(XDC(9AF;L*9V1Y5lT2`S@eELnNtu"aOl,?5@R28e7M[?]A4Ord=<"ho6q\R">t/pmd
n*_Z-:6udi)o@Unm4cb'TT0a&DG3`T&W6(ZgbW!2j`fkDZp67.CVI?lk^U:Z'VE,VEX"T>1<
l6_!*L#=62H\%Wd3X?ZQMQM2SD&Qo>$i\fCBO;L:/(:(VV.$Tet-D&tVFSHI&@5L4EGq.<M"
u5#L_9-sfRC]AM01>c<@@p"adORD%Z!qr=GT6_:Wi5n!UJglsFCDIdf%@m5HC%NO3EHs;eA&a
62a`g[MA+2VYc36tEnC(<TVoUFR;jC5JndbPq%UYFu?$1*"_q+Kl'&YVH[._AE=KH%Jb^,\,
PK]AD9%UnB5B>O)3H`oPk(#u.Dd-&R.GIO;6i^]AAR5+j`%6J$*gD.;D'[oLS,Ni;;bAZ>'eK<
SU=+j8mu2TMf"1Ej*d[^^lE_>U9(\Mu>fY7J_pN/_2WaWZ0:Rh88#baqOsh!S#9Qt=)PD1?K
..l$;88SE4AGIDsN2^q3;WI4p<C4lO'oL[p1)1Ti;0Ni7d"\a"5&Rs:$<I\j/>88^o@Bk><U
ZH$pLWZSHk70IO)[!32%tBgglXLGuo@F,D@PSPOfcPC&6)b5j@De*U0+NAt4ZhqD^3"!\JUd
6*[H4rs?,0]ASQr.\`PTP&&K[ptCJ!f1?<HgaJDf8nAe#9_t^hMhrYo$(JR3mZZW8;40/>T5@
E^'eM^Yj:**0!lFh,p!"7^ue3^C]AWcWGu??`"!ValY7,bD]Ag%O\"W\'CCn'!)7/d#oIorRa:
9mZd@'/<3b+kkC`SHAJkiHB]A"*Wp'`D$8(cOO`=l19_T9?hl]A<NPtgH(Tsk8VQ'UbZSIV-4f
/2BWpRMRoR-o9KB$$iNG73A=?,q+oX15p`E)50;6?F_'>Ba>`'Fm#,uc[3UmS94\:B/-0=:\
PM?l,!qMHH*&IPWYucVV(tS`D6dZO:]AGQHD7&)C[oK_FjEu"]A?*b81U$cr;`f/UGF)LO7)ne
"/"MXr=IsWcQFpriUGN(Ti";EetI4EQC';Z.#DRGVc^X=V=D^aX4W_A0rJo=)=%g`6t*rJVX
*cke+a+gXf[;t<oe#EY37f@K"lp*gA[C$q2??(%sHFB7qfHt!@&Cfg_*TA-O`_JAGJf/'f59
u`^'`N`fnk["+99p4E&Ws;Sj_X9Q-3F/?R0*0OR@[6hZK0`b,6Aj*m"XA4&Y@Jb&)N+IPH\2
3"rOo4jG>("6UFV*=LbLq8^rsf8P#$sT[`MQ9PRN6Y@LgV9AI$*)Yg(J-P)S`lYg%[\5Vr3!
Sp3QB6KKdY1+uoKh]A^1o%_pA&ET&:M^-ZP;eC[,7E9;uE6AOWr0)Gn:C8=<NG_nDjTP==gbS
Op<#&0p`>;FK8J[b8\p'0>lha4iFO+-sqjK#$W5,rSL\YB@.f.2p[r#DD+sl&:)`,$)\8d9$
M0Z)Fp^E-49Aj*K0FUB&@/r(@m.X'n;9QPSo*uo68^m"o\Jsr(VWVqBE)$So'r:aT2&8VOW*
IS+g0mdD8*X3M\dpXPKk[HX/.+8pTagt1,Nt,\g?P8^l6Z+]AF:T4.<c/>7!Ie4T\C+gMTPSj
F+9B$CN0&jd850u.K4$?5;/`-EW,%OSW9)Er2gD_gJ\H3g1bIO0SI4GuI]Ai';!3AKWEQ/2)I
UWDJ17I-,$KAKe&Xn:)9O4HSp31gU[%k:CjiR!V?`^/5B%H2:0NY0/%jg]AbMAH><?QF-^%90
l`c1/k\B2\`VIhF)lANnJJh1qEO:#[k5q?Z9i43W_o^;%t4iB[f;Z/Pk$C43G3V1sRC,q\/"
Jn/JOr/3b&cW\Ko:liKeTuf;ArNSt;&Bk_]A9&5Abq[k>k23$iW5F-Yp4(IoU]A\%rDnCZHpBM
=0tgVTna4s8M)JeOFHK:N1<@,kGI]AaD!kPleM-!+#1&DXM0BL=$;ef:CCo2>pJh1n(o7)XIf
8RM_JM%i;BG)2cqM3K^8@E8TTR_#<?npSpS0ai:31%;FV@OuEbn)uCZ*LU&SUhb89b]A#PjaH
5.`G3\%F]A@UHK,l[63ajEa;h"#U;p@Qeu\8[mBqi4EAgY%4%lcF->b!0'/pl8/9[:%+pLkPG
Qc+rbWfWIE#$ZbMe>]Ab%X#'0bQD+SD1H?.D`jpB`_Qs&i>BTf-8r+G6r&#0Qb$eD/u3ouT0Q
`6.nLAK(?(.@I)7nk\iO.Yc]A)2oQcXNY&+l-*9[T7ia#(.usFS^0#/b'f>"8$*Ag!e-OlSmI
+'qH>J$^W7sgr7nqc&m$`d]A3hG,KQ[3WS.a5HFNjL\Ko>ejf4Z"i"Ym6(kRS.46J_9t@jZU'
kMGGXg'/0d6!0?fdha<=>+bK4nT>;]A*d5?ul*oF<.GTf#\;_,XqnJhPuh(c=Ep-i&>eHiRRr
Z#UG2[iD:Icpl4?H3T[&)>#3UEg!I$-RcFcT[14HSkt22P)<=6qEn3CCo#!2Rck<O4),BkDa
Yg9^+fQcWiCda"U'Tn?r*hM;E.qIr!*oFf9^%HK.`So)sEW3T-B[3RJ#Z5BQ,m+\KnVh#L`#
lVD&d)&i5@U;p]AK'O$VSKb1P:\TM`XHgZrd43p-$;tZO=4Fgn4q\@FT]AlP@s;AB-2@i551Q+
^\h`\m2^2s09irjNq-W#PLr1!T```uW>!K7sJZAg.1]AWPS>$C"W0p3neM!Yl,pt&_#>ugo9[
-U"?[F!T0jTh+SC'!38>/[[h\oUH/4VJH->\M=bk,5dr6:gV:EFNo_FE@/Rjg%-0EQq@GtGg
d]A&RYrIeNrX5eWLk<!M8GBKabLO[5l/e"sCKao,nng7%9.Jf7ENB)6]AGqn.B7ouuQta)(*W-
['PP;C^2^rs%%?XKD/K5LP=tj"c2F@7dGprN<'Tkd1%u'9lhRiZrfH?[DXS0S6fAF#Z<\rSh
Y<LLf0(?H9FC1!/O=_M.d\OJIqAtYEALE(HhDS^SNrHKtb-FL_UlQ8rLN#O-(NQq_krEqt9]A
q/,!jEUpH?bXF#s03DplhGLfcHg#\;u7EXAmp?.I%;#9GVS-]A=-u:S.sB1I&QU3k/0A^9JK4
Zos0JY#KNJ1$DCB;5QIc1B9RdIZ`D@5+(Z?GIQa?@UT3qCq:pG>P%6ZJJO7]A(E;lX'&$XP5[
cX1;X`!j6;!_1hS[\1/:\BfBRc"[?9r!8Ej/2'L0VT/5Y%udMCe$:F]AAG)!%UGt&/,]A4(!=W
Z8N9oSLEp^_,)u@L,HQT]AI17oTDFJc/'V*N0bHBA`.5<:hQP,q^E"Ne;C^r=iXMI%P';3,lT
+Z\&l"KSD*4=JiNFFCp.j?IkJZ%O?5%nC^'RgZ(Xi0>B)+XP>Z"$!AfrT.]AmpSR$,;:N'n+7
HaC^t&]AW/F+6gQP]AH4FT0I0]A&0KlGLR@\AFHM^S"+J"U2.EmHJ6GR0"H6R^S&+)'(:eunQm%
[VFPc27H?t8gS^dX'md5a">'6tY0]Al46B``=B[I8e;k7iHSajO8f?iEF/M3dYAiV`^VRqf%a
[q_[MsK>^NGCliD9C#d80Q+*-K)K0?M98'3G?$O_OWHqP"AbS+U,&]A5Us@1f4.n)O<kYC>\@
>5W%)O-S^a*1=mJ]A6WhrI+$<fbY=<_6Ji?a<K!0q'ugK'Vbi+s0"S?b2/R_80+#p8h!Y/uIG
Yp#s-EGAu`,L-J:`>=\W)g>TXiF51(Z_bLVU\Zah&.7`p%\mIQ_E8A+c@kU!^J'&.+$;_iKe
;erf_-W$qGE7%-pJ[Je]AeM="K>,6HtH2;NFL!@:QUr#UMQ!-<Qe,ADSa9`2Z+pHcWR;2rq_X
TCc)Erm'UBBna8l%HcaO(",89Z)beKGN/hkA\'KT<CMiYM$#/Af[]A)>kab9p]ASB8CC!+]AEmf
;lmE.[]AX!n`pdi/q[8UoY#t"+KY!Icn"9-1ac<ad##2]A:E2b<NM2cIhM?*fq1SGRqa@L(6TG
23bLV[-Rq/X3*ZpZdGMHK2D^^t\WGNC*i#,7^I;pJg33@)+2ac'9gE*.HM%<ZCQ6kqD<Vc-A
p<qgp[9G=E3#'ZQh_4"VM_6>iCE&]A<2#M_Sc_P3X[V")2MV<F?Kn5W0\Oc^@gV'AS]A]A:;u[u
(S\6%5g"A'BM%53!<8rN#Y^qsEcV$`8rf-<ChQoe<eYI0fM20CnAD.igTC&?p_0#\mL*i+:k
'GWu>L?r7?;)5,O)A>4u8<7%Z<7KL?Y[iiIYq:HFmT1MB#ro:O&_S0U$KIQ2:RL:RX`bjD49
XQ_0@Oj%A.;^CpBJGj5ojUq@2Hjj,V3FVf-__/3V"*L,,a"8<ecR(dHRR+#b@diCdje)a%u"
l;M;."h*"B4,oA_W5bmeA,."p821?D]A%jdc0eq72_Xa@`>fqV0U-5C1$]ATn,,]AU333-k&mb"
p3qj4gJYkhX5kQ!LKCctnL*S_eSPh7T!=9KVoXaeE)h2n-+MNO;MXB8P;)I0d&FO;RMnd]Ak(
JZ(?mGD6HfM>jJW.sOi=IieKe#';;MA&KB%s5:dQXIK;<?DS'90TrYoj[ZmBG<CPpP_S!#U%
KI_5r^Zph8-lgfkGO_B!Jr?\@7;:SK77@%9$aC7kBej"=rs##e$W7@B9+q"bAHugI,L`%t:q
9-:BDeBE-2l;FNO`Pd`(QjqX1[-0@?W41Aq+"SdA_f2A4NgdY%,u`DIo(j*A?4*2,+Oa9;=S
s=<V_`N0l@MF$>I%1kGr_j@#S,l6Te_e@*-bu>8U9VF`0Xr!HCrK$%3Wr;#Mk&*jftm+^OaA
,naB/VC/Pc]A)Wl=9VIj&!EIbb@;$=<7ndANG^ke]ASbbH-O:/)`q,O%5RBQG&0ST3L8basgAa
Lpn$;KGS*tB.bEh6rAh.VM4(Cp"dCHP<GT>Cp9rK=<U\(7YncF7CpY"Bof-5FNf;dh/]A1(o9
(r7Yl%@J-JZ'Xb)t#&VTm[r^ZXlqB*_`i_1Y]A/3`AKt0qcftNa>(A;Kg4.$.TDet<7kg^(si
n^A*+Zq0hPboM,lG-O^VL\s5ge?7t_BkPKq*K+,E1eHRE`4>+$BFo&:p@gl]A:F^t0')V`;^W
nVTOZ.AAV2G`,d-R&8=fT@j-Zd2Ch^)WMp`m\/,XJn8\CPZGG*CPEhlsr/_lqlBXt_--m+u0
H6Sf`S>0H)"FNZY^:5R'O,d@p]A+$u^2hW\G?"5=G&:R4UF^CQ<m#.68rk/6V)Png%j!-F@e'
/]A*>\ZmplV98&s-?@!&F;:f@NRkj4JV9KU7&^Cdd(FNM66eS2^Ho0Eu1<JV*6,O#1`bkSs5L
mjh\Zp,>A9caPI0!\NKUQKo"DA81B#%TOaP-UGBD;*m@aXM)lj*=W6'D>;I$cQC&_8A8/J-K
C?9L:Mbc6R-B6pS]Ad_c*A[tDYgd<@YKRcdS`i@r&hCKb%@^-'>UFPF7k7Z]AA+>HbGqd5L9&&
K,G9bkbY9>-2%'M7!24&Xm[&qWE:+))UT86`M(3_mc=EL%g=SftB$d@U(Fo^1,BWn8:/Jo2i
7pNr_=T.S*Z4QH9I_8*/,!NU[l5`c="b5=9&t#(phIPgQgb&hH3qf@Q8<3BV-fA;e4cQA/:n
HUn2i%jZo5Iuo_?h_e*PS/*P/SnUaCl@./OW<19"739pk4bgYI.H@;FiprhH+Ec`+R>=):bP
%5X'02!hLF)i"%B>TutFH0L%ZEPkQ==PSt0P!j3g+&`3B7Q]AM\^`OJt/Vbt@!SPU%fflU)l>
[,apUOGDkFkn@^GfKZ&#]A?3/RAg%P)0D7_Xp=N'T2Y/f7tdRi*@GVr=m7ZB);J.9La]AkQUKY
:i9S:2!T4%VE<L^p`J\:m1W"#eVC0V:$g4eEd5sj#fRp3"sX"Dmt>2eoiWOchS;.3k:p_j15
`OHQeIO(;MfJmD>c'af&9hjah_'hfXZrU]AhX/FlDk2*(sT0[.-DcePT&@N<fWZ7i%fSsLf+1
pSM5lQE`_"h;E6_c=)lJk(8lK'h(eQ"TpTWrALdagB^"l&:XKe9P4@P7ASbCf2!]AQ7.i@&?3
.Gnj6MfA"^lT8:2aU09)=@6O.O%%os@ii:0eYX+0ZIsdAl_UEiR%-T<28#2CC,0*8uR/*PEk
="lf>St;@)sAA=<X\'\F^ZmJ5tVJ.i]A-r[NB'>qM`2:e]A07=8-*I%8lP?K+;[Mt(7ZGs[5Y0
)*lsu`!Uc/'hfMML2;7Enas/+#K/!d[KN>Kg(IkdtDHo3jps(EqD:UofeXmccJiu%<^I/QAl
<gTDb]AU'\?Ub@P3HW[0ur#e(i6%-jbkde>4EOqY>C:M,]AXl&"$0@5LJCpmIVbLVWT[-tRm54
5EIO;..EA"h6m&b,#IUq+'Sar3?HJE,kF#?GGii`fp*[;rQaGpgfY%oIskQHZu\R8s6Ta<0#
=Tcf>B.58h'OgbUG8`YCI%aW-;9bQPjIlTXGWr?\kOlZ7O/@Sh$oTEd^MMc#kCYO5i&Sg*k;
S=)8eu&&oG7td)i'`.FY&Klu(8Zi'6DU)\;hI8>&)Yu-DFZ+>6L]AuN=$pjNG(a(4kd/1FB(K
4hfF+99*3S)F:buP`WV8S@lg>bJrI\?(f(;n^_AD[hMqqE`_HbjCgXf_3O&-3;Wha,MpnZ->
B)d!PK;nXU_@^Hb9d@VWAU9MG"HnW0\?Jl$,C^[rRJo5mMq&bahd-I+qAOE[;,d"-n_7/%T^
?eP,mZM^%WD",UF7+!97,6nTi0klUoeGo-/_DjGh9M"WXH?^rZ"QuMH%=!>omIt>C"dBBrS'
=/+4)ZS"^%S[abC2(kelsh4G^NhR54RQ(_6:\LM_.7mq,I(+KOdL2A1#72G>$.p8)#X/Zu@"
dJMeTqGVbnd[bf1@Ci;GP_L8Bj3s*9"!\c4,U+_b6!nl<0h>"I+>"%4ikjjk'2mZ?Man!2uu
)BWTab\qSk,*H>UttUQer)s'Y6"KcfjqleQL``k2@]A<2k`HJ\IF`aLFlijot-XghKU%f<kN#
jOckjdU93N:2GA'FeQJ1@kG2S;]A""Bje$,r3&o353jo9_G<YjgHlV@UmY0rd#qG#gn!]AQ7rL
GK;o'<^JD8G)/W@DdqWo.;Z3?Ut!q:O/g/"\ZZlf<0m_-HmQNkHhYEcKI[G5kMVNFaFpKNCV
HPIq,/,p#;'cF-5H9Y1HRp@t6/Q.@T;_jit?q)%Y5IjW?QBstQE3a@HXQ&iV?_&)4hG2WgEd
%HtD)#$ND0fru!^t3tu:H-#5cbiPY#hu!RLA-8ACkalZEao`ug=e(Ld+pluiR@NPE@:,VLhX
?l(U:'R1'DbgCPnG^in'(<qc)C8Qq>=Jr.PEpa15n4Trbk74_^ETanDcHk7Fd]AAWQ)Y[i&Nm
.lC+RWc*)#.1q:W!]A.h*3osMF[Y1btTdb#eMU22Z94q;Ii(kJFc7+I+-gsuI"OoU9jG;\Qc1
RP[E(D0s]AR%4C9UC*:O]AfbR.d(:>5Yu[cr]AcOm>,h4P1OjqghCl!kD(WrLc.LIr@dETB&#i9
e?/L/t\^P!/3>G\'$VPEG^-JsO1;(;6hRt7WF:%(HkZ@ApNZGoQS2$?(UTG7FQ?Pqha]AA[O]A
eoS<:Oe-!7-[i/fOV0rdc6e'c,B"^,P,`qHVD5f5Ag(O,[tP`j'5'XX!up?V&#4UF#6)K;,M
[k[*3Naj6@Yg.`!$.;j,_c[sNun9>T#rj89sF&E`2ZC25iR;_\g8p^U&8:k(OLD1l&o\g5po
qM]Ap@k=C;pj,NRpl&)S2j*ef1$G8S+lUYE:L9IfOYtHk/KUndi,%1ChH.f8]Alc?%KbHjq&KD
GC.HX5M'riG?taX]AoRRe*>\?OtH4]AVE'kV0)PApQq/a0+;+'HYqOKLS,a-fT^H5a!*[X\F1b
!BRe*HZ:89WPqj+8N'V;iD$;`?.kO&G\<!j5\H&%?aC,somc(Jn)gk+t"/8W,!>P.gej*.`R
TuItVfh-J='f1:'NT:]A=,(^qG(Y@dJHFuA[]A.7^b)0KWE[U3:O_XlWK=!&^F`H?6KN1r><p=
pIZLH)aY2hSmo)Ys*QPtqI]AtUR7ePMtUW.q8"b]AeW@N)]A!TZC`RM!frZAG)Pb"^j:m1K0.p`
cl^E9K:6U,A2r6F(i4,jrXI4Y]AM,)t'26h.1$XptSiKkcL9U&T<0(4RJ$Fo:e(.,^S9oE<"^
>*D=P#AJ81k6@iec4$3N(u/3Y]A-K@)pa+K5-PGoiAfA'/upR.-"bP4*.#+K"KTeW<77Af'86
OQai_;UL(XXT*blWpUq^/eMUfoR3jkagXd!-)Inf#m?L)j?S3T5<J([%fb&K64]A`%cZXLF5P
a]An@qXJM:buA$@IaNAu.[TiPpPJcsn$^Xae)SiuKA_\^F(WK^,\OO`9HFZ.HiT`C'IZtrNAY
mt_:I]A;<GH5);D3nFO-fK87A=)2N*M2E:FqsT=OE78X;`[k)T7ii!JCO\[>_oI\C#qBTA$Xb
1b-U5),+eI-m?U<8m&eor`?'T.XpocH>1V$)rZ#Zb9d,KO7Z#bcnnuLIG#@mQWTb1&M8,5:`
<8YRe:giEq.6:1.\UgqNaK36X.Zk_YPZ]AD8k8>\l:`K^:-B%l4JaNnS:R<Uj',gno!mG+I,0
1AFJ7&P>_>iLuLh^NX$oe'`)RC6Mq^9PhgVApH3^T<K%TLT':R%-nV.]Ap:[82r/fN=UR4XGS
]Aj,I^Ybd&Tc:Bo&Zp1<iE6.uh8n,MRqPD9Usr=?2;rqETHa(AP4qq\5bmO(l5.ft8_L>gMo-
7:H!4Ch8uK&I/+X6j]AE8g+7>7ia+3Y>M@_3p)*\'p6l^/`$S-T=n.K]AV)jK!AQ62)D/Hut!C
I09?^H]AV#uVeShTO@fmF=M@XWa*U&8d<\6,m?*gf&cA1IU4Yn_\j"S^M&nY?@5"(dqMOtXjo
8="50nqANs9!TOjqQ[[<D3h:"u-C)B!d'`Z^!@#a4e.iP>akO9aGMD)nl<]AP^F)W/bpHp0`H
'<iTR#Z=9fq#=-Q!7.[U&mZs_45/Ot(dG4$K1b/tEaNbf=V@m]A5r0Glg7Y4^R6]AUl3WI*l3<
)42Je;L^qBW2:KMkJ\4OmOb!%`l0TG.'ae%`jp^q%_<).jFe?)F`+CI,CX.?BLjd\dTj'93?
31V/%P2I$$)*mc<&P\OsA(k`fGn_E]A=2;KVms$p_fpG4A'R67EYdV0+\.`hW=@UF6Afm8Hr(
j1:ud1MIIVc;b>$[Kbn0P*[4i$sQrb#ASm`6Y?!sZ-Jb_ih,B)$fi_GZ<Q319sfK.K/!]ALV7
]AlC5c<10UJUijA>62""`\*j5'0D$aN@`A)^YgW/NBgIHNrA[oO%tio*AP2\m\Fg5@(B*V0=r
sPYD)88KU,#:/;RNK3.(\etXZ2dZndGqNI\jJb#?W3aKih/XA[g0c'`%ag#(T]ATO2EPmO3PP
F`7&WJ8,Yc(9Os7=kd8n(fMc3^Tg\FaSoZ/+ME_H<CuM1Gu/.I_:!L\[j"*r[+Xh0=bLiK>m
$u=pTj"&$\?O&2&STiGPnZjEn5:/*5*RS0LKqN+E6#4t@8B7l#.j?/)(_q\I7-p1X-kBQ8"h
Gm</RnS*93:R*U)>@(QnrAr>e`6_emXfp#s,[qOqL,1ZY3WMesS`MD<&Fg)k:=.F@RZnhn\0
Gq7<&&W8ht7ZEn>t@Q"C!,8E273LgUj.T^$E7\!kMfQIFEME\#>Ib&`u[sl!e/#^r$kA"'Wt
f0\WG!DHFj%?m=M\-66K5F[<KPe2$A*hkkoEL'iU?F-i![+^:;lZ*mVu;_Pb$FCFescL.Ru0
t2MRg?YWDqhs`8^9k2&BqDFOCoBRCI+UVl^J*XFV(40U=O5`"hM8FG6E8A%8^\4ld*3THErt
U+q:,5`KPjN$b'[i"n42$W6O\gDAIH#T7iGTZq%E(>qEBr%bgRb$*FYb,WT`Ngp@Q[\85eLe
r!#p>W85C?^HU]Aio;gRPE=2/'M+2Om<8?rm6M6s:%E+K&$Fb<;'d[r$@&$%R_*2DFbG!*jrE
-Z3n)r]AjRL=`5.;F<iBBX+;,1:4@#0e3MX=;'6qLc%a3!D:8kn:8T,[)j33^qS7qV95BfBo<
ClC:iY"ZIK!-lB_pkQ\Orf%ndO)5?bdaGC4mP:>j/cIgph5!eM_Qm:?pY/=+:,*PX>"g74X7
n'>1EFLjFb4N:F`:jFPfC9+h!CQRX<]AWFn+7O[as)%m+!%@hEStI9J5ZR#=r8tJ*/^:9:Krm
KX2KrL!aB4n2TJ#_[dnYit/rnbu+H3@H$NOZV:VH*ebSZn+mj-ZA]A]Ar/7!R*'Gqk`<-9EmEo
K\MeNaX%i"78;d68-#9B31MbhD5gA>=VfT'GonTo'Dk=p>.uV'#+*sUNk)=_BI:uB)]AI,j8P
ONqE-d:D(jmfWg@g'i>D[O\Gh>`EHG+n#pY^m-AHq,`dgWf.L6:'rnI+?nFH?Gu#b?"5_[Au
hE'TNLXQWB5SEJ9%1.#?GhPiXs$_7Wm_N)=giON"Z7(lRub!-iWS*iF<qI)u8\+iX@UXo"aI
A-^^nJH491h<HC*/IDmI[,0Z?jZAS6d_f1Zl5;g!\#11#4MHcXkXuaA%DnEl)[$p56?U4._j
guSus3S4d$Y6Wm(.#[>P:+fK0sqO*iY]A(?4t*!%RCRaXp3UQIPbh6t-J3XD04Doi<#@8Ti2n
&M8/?0,jBm0aSkkCgJ"GB5/.Q]AMlEBoFR:,l+F*SUq+@Nr&V^@8s%eAP7`U[Gh4MtoH#R-%S
tGtX5UUO>98nh7@$JHnW95MS*\\=Cm3E)!Gom!-In4;TXZ;/`:Su]ASKRFRf,pmN`QsifiG([
$>s^+94k<Nn//3ZdN_aV4X[o.`D`@.Xjo_=X)"`C]Abt4[0I6%[nlDr^7UYfcW&eR;p(,P(RI
<AUE=8p>JD*Ic2Ej]A/Ydd2OBe\Qp?aHf<Ehc`,;%N2)k;nQY7"H>Ch:>C4S?HjR,+2^&m@jh
f]ABJO0A]AFDKY(R_e^VV'!!+<2\EI*s%8[8ra:RPj>Fb__)-dq%[hGOrZ#<uNP1#N!R'%o7"'
2;'Sh.6:>TRQWjkAgdo?f[a"M1+%O90_AJ(`(K=]A,qc"mO"ks7%-&\RE9/VEKsh&XOQd;hbf
Vl_6N\6K)(WaZ,H]AhFWYaq5RP28-q^>5'h#S7=GKWA@`SV[<[3gp<OX_is(k-Hk/g!pJL!&$
qLg=OYeq;rYAV:U8T^q9o)F9rMGPR*!<hhA2O)@YfTRfWgBn,jFCZ=CZ9^kWZQSn/O!c:nR>
JiA5.VkkM7U[6@:GdJchn0*]Ak]A'AuY=8+0FfP[3m?2IE'uQpr!*n,43sIn'<bkuSm8`V\c3o
kWaQ^rWHmOZQ8'@Ycip#MhU03JY+$GuI44.ubaEp.miN<'enkRd++EI:tC/bFdII7BFpsVPi
G5FF^(&,pZ2U6Do:Q'aoqJ]A?h"`1etj)hSbVViO+0ong?G!M^Ec/9,J8IJ8CYFFbH,]A>,gVr
MF.()`ZIn.NY-)fM`ak8Yora`F'd>lBQrN:eM\]A!BWH0Ga'HBWN@b`ei4FQs5<0_S^cchC5i
1&iPbdEm.p-Z6%tV=7H[iZfPX&06aZB#_&V!EB_/^L=0HhTM5+ol&hZ?^7a+q]An<WPGQ)RGW
d`!\8/!%1cpm`boD+Q8pBtk6!E$\6[ak#J8`$B:#>YfhGOdV?gCa0VqsNHo0*ArA)/SmRdIr
TTVS5.hZs8t!E)F:2S_JP^m+4d0d?(KlN2H&>%i.OhBW+H`CQsk54Q@$7bl>Nh3cumk^0_-F
1lG>*Ht,YAXI+4h23;WsrIC"K9V/S_4.:#R8m!)U#7:j?<iB,4]A.a56:rGSch&T;\G5#o:7-
j4LD_3df'#!=:f6'Zt":%'@MB4326Z@2$`L48Wj*maEmE[#q"(r99&ma2i6gLuW;!1L:^@#W
Q6f0]AC?LD0-l&*s;l>K3n5k+sfYn7fEQ;8AkJM<r`jC/bEaQ>*Xc/K[fDEgl&=@1\]A[X[qHL
tqH[V'bhE@OJ+/bKgT;J2Oa-HhM5nKO7B,LV?l-4AleF>V'SlRdK5d'[ftF[]A`#l$ai"S<KX
?U4M8C:-=e&;/N6uN,4r"\WL@7H-]AMg79!t]A0Y4?TqRF_\.#/a/rgYZ]A/Y(Wnjf8"AXRXn;h
6h='DP$XYYI5IdFBc)e"':V+S%\r!rS8g*>ek1a^_H->UoFoo*[0l!uQiLDXlf@_lSPj_qcg
uCBL%RRY9;qG<[eT1Sd?ALLhCAFL"l/KmeD.0=/d42fYE2KPH='[5i1>'d2"!E`n=l84>!ao
B_/n)b@Q5u;U#IZ;Q4LVcgo>M;dM`0L1RDGF;a/Zk$:##8[,9W-el);B\CTS7miRlHPbi6NV
Xu84A3Mkt6A[KVl(a!!0o6/FiY-1dp;N)\i2]AQ$ESS$dTk^mGF#]Ar`559f8hNgDLhUu:qqk[
h1eGP,*>nR.o+m(_p>hj;JQ_""c$p-GfmS+/DVOM?[B/GrHJJkDtks!1im[K7<5np#Gf\J(a
]Asc[6'ooJs9eTpLZX#c:8u,YPK!c.VF*!Y04e\aeg8.=LfNN1]ANERD@To^oB>&ke**0Z+RY-
LctL^MRhmF3Z/:p_49$!V:U"^P+8\GYT2><-KugR0Xc)+P>^fhsG_-\?OQakTfoji%!jE[/7
RP$MER*O[9u>C%6HICNDA*7oT`1HXLgDTp>pq^)=nKE97n8&d!UY!'J"KaA:IW3r-1(#HZ0`
uA0mhMRD3c[#[lOVt2JO91R_]Ap&U1mZsfTL"1"eh%HokH-;4p\OSK@QfgMkc6"X,>Ua.&GnX
tiRem??HqeDZFAPF13,k%"S,*Y96Va,S=a0biBN<fPOd_0G.K2s397^d4n"Ug^3a8q=G<-YL
]A2.PlVrDgDpi*pr('lSt@5b-W$\BH[PD5</1mcZn(W;%Z::4Pa/'>-"K&]A(W4P7PHr4q=ZGT
"LG3$IU>`6?WY&.T3!2pLH!rDr71"UMY"r0(5rE.=C*.m)QZ('@4q49pkZGB`39oh\^FpHWF
jZVceL)*\haFK=On,lLYq@VppH$?1E)RRKC'=@,9']Ar,Rpm,5mK4Sh*?*:(RXpgNA:<g/C)(
cq#h8*WcD>*,>c?"<DG]A+9#@6'<OkH3N6-(9:QP)(l!HE/0O]A,/g[`\1#(,hYAtZ.5:&V-8;
,%Fq1o;--s/7MKPSa<adA5q[(Vqb5,k\#4raA/O+.Ud8;s0'hc*Jk$[aO]AIg<BJ!"RP7@IMH
Gf`#VC=qNkcQIse@"=p$F\%#H_Y9Vd!ibWKh!)riD4r>%-(PQteOXU[?/E-&#P`ji->;+er2
/foj@Vra_^"MQYQ"9HCR/Tcmn8Th1L6Onh"38U/c(n9#^Sj\8)7We0bRmC+Z[#/1q5pRS:G#
NEt.-tlucRHU*a)#l=oSjk%te.7#Sa<T>$;kQ'/recfp'?=1:$4.77@eO?Al+XFPV^-j_?7>
G9A<%"VAL(dbSmTCm:mqFHUgKYR6=_X0L;>GpMo=t+eXY+fKmPBh4+2p?k/1(EYdm1[$:,`/
KP;Ms`f>\mkdYN@etme60TbD_tCeGs5(7PTeck4AIqW`>Zn.##HJ$bktk*N*UVLpJr54BSX*
*r!PG6?0VV(YqH-a?ob"bZ5?66/GK\o-q[K0g+O9rk@_Ij`$e-5%%b<GG@]AOR5LK<Mm`j!\Q
c*I\(%J>GP_PFIe=)O]Ak*g^qV<!^j=)U]A*&[a5q;XlX>beYkI;'3b9t^;_AL'Wec!&fmD2k;
V>ik*q!D>Ep&?To6ZfJ\V?ec?G&?L9$dbiQ1g-;8\a#=JrRYQ8M4&1ERLi]AU6*N=K5\r)Uh#
'B*C`]A]AcDWUu^ml)YTDp^2h0JnOVUb&eH_Fup)71m:]A=Ydl3D+\`H,6W*mU?6[jjnLlf%p@.
A.ICj!&*cp5b^&'%gYOisfg!dK."T2i"HjPp>B>"XA)gnL2=RYFQ@ooPQ*Ems&]A)TILrqG-"
pFm1r'h/HE!=F/\n65iZ.HQ=ji@nB032/dA]A#K++MY8\.$r',-Or%"g.c=knS.!.g<YYMh\-
gq3Cllkp"IniKRIkrcVRd;m7M(X!>7AXl]A5l(?@`-N85#t7a!@X"e<+Og)Gs3kKZ9o[XWueK
3Z@o;+d&Y&_0"Fr0\>jWoZC2]Aemol?M9u1Ku(;)2RlH7:oW=E8GX"CjY_.!*AF)-(IA$MWJN
$W\EW)-E*Q.)!,hS"bj4]A!H^W;\^lK`"RSE+D%0alUkra//q,Z\0W)iUKEHEK"D$-8F5@?b<
qWL@&WQFb+,HfO/@h/8n1eS$[eR$Xm8Q4J15X2<_^J^]A]A-2,Wa6!0(M\^GBhqaalj7u>tjEt
[8hXmK*YQ1QYt^i3t'O5l1#j<k]A6+BeWY?S(kt2,9n/&74Q;]A.3_o_lO]Ak^7*=n<&7@*j-ic
+Asn`BSoe7X_5m%@mt3hMr)6=VB$ImHc-LUVG_#/*"/=-BTqom'CT/E8%52IT"PARdU.%#lB
urI3/LEjgkL+*sU7_-7ofT,'r;pB").M4]A<Yb\`tFpRgpU'Elf)ed@28(Z^D>nh4&1Yqm,e<
QskG^TL]AY+%`gTs-mk(V8%#[S,]A_%o\&RIO]A&o<qa<;!Mr8/'Mn7rOr;Lc6IN=D0&8<tWg?@
*<\9R#,pO6"X1rX.H\Y'4WahI@@du[hVf3j>u+)rkhfM^UF(m>rd>Rb;_O[9DkWIp1t-kubL
bT"jMTFg7SKU$u&rU#q6Z!R2cPiL"2[)#X''u^^dGhh*fPKJ)TmDL?#977+uMH3\'.;]At%bE
A,_8"l3A;h'ZiQG0T@YK+.7H)b=6rF-b!/*8l3`2ZcEhcMXUX]At?2!?1@DdZPZA`eVA\m@mo
m67\IX']A1kZg+`=,B7!r$V>^t`VDbs4YcB.rgn:$jB#_f\jJXEA)*ur".t!bmagai"prSU2J
$9OAZU@IMVqbUe;5tP;1IL@Yj(n\*XEUC(."Am4l7O=Bh9jjc4'j(4Fgjg(MK0`:I%6n=njT
i5WG50<[?J^<IbMG$A1,2ph;3Mq*L5fI2fjDW*NmgE:>VuEq3CD#OR_YP&aS1bejmPgQp@0a
&4JM]A)LWRJ>t]AOlM=j@iPbnnk5W`8s\]A8fPU7OH/?o`e\EA1<IS\5S54WR08<:-GJ7']AY^-e
;QLBqOAO:.r(92;Zk.FTJ4Bi"]AMkiI>4K.W"^SMd5(S[WM5a]A-Dr+Z13#]A2+6>EjY9JrTgCm
!_kBM"V\t&qdt^LmFR1H0bT6mi#2%:\iF3$J<GoDj(/Kq:F&A&'SJ+$QEFL4.+D4oGfLm0+*
pq<YL'nY^=+-%_b'IabTZOD6nk@!C)@Nj>lAKd0ab<LH)kt0[G?_6i[]AYiE>e7I)@Ebj1M*g
e.FN7D%.W#+$:U.k76kGlke[.^)#uJ>Mn5'9cj_SZp*O!hWn#p`[h+HLI.t]A3-i$3%Js3!ph
`h+QkXW7cp1Ja]AVdApc#dB\\DdcP[^3a>3b:LD7nR?I[<#j<(AOljMqb%nW;Mf?Z_n^[F>?k
8G?2+otRC&usM]A^"q9'NRst:P'+,`SP[pH=j:DqJ0AH@Nc=6Eh`WBNU,LnjVJ&'`_NXJQNcD
2(,/+HVY#"`)u\HIAGB&)29N4.JC_Hmf,l]AR+k4g[^PI4)+e_iqeX<(#gFPLOWn0@f2u<fE:
R1@t&[Y6t)]AkQ6P0">2I5ZoRBD<hJjl,KL<:nKfdu"Y6ir"1;p2&RXNA7*88d9G9<hFG2*n:
fGNrsM>_V[9o4Z`0nq,W2)=U='G&:S\?09Y$hDul1UHWbiN]AJ?>G>ELgj=0=ArhYh@JC's?H
h-"]A+I?6OF^Y;1`--(a8:$e&L?8<T(Jn*QYjW&/?4as#b*okY^gB$uP4/@@Thg]A)-SLf2f>3
@W8Qmp++6KSPmBFDFF`X!V(oXP$CnMCDF$R!uIbC@1UhcAeC?=3N#fnus8D*EB5X'b@dq9]Af
#9'>`tS+0D5mB@"dY)HqA$o50[)'n2%Re"()e_S^nME@P's72Xlrt%(=s)uD+&'NI)3MuVfP
rpBWZT_R/gNG(Wrl$4+@B6tQlq+bE,f$3beXCX6%`;$os1B:4,NDdDo@ccG%GkE]A.]AL;)7g'
!NT)#RR$]A9$roQtD%=1a!.hfY\gA(BFVYb>mE9'P-\20.eZTM1/"C@&mAAN7#b$6Q2<7aGRg
WrJ"+Wnb?!ptti4CifNUrJd$7"U+.EIE6a'qF7pf?%3co]AI^*a=C\4UaeLe*R\5oVC?M,Kq4
C[4^<sZ=\Y<4$WQg6g5h."2!qo76HLi37<$;<=5V;uoH[IK*;lA_!%e.QsCcQh`R@<=Lrs=u
2%TG:`1\j@AiVI?1%%(rUKr*3]AjY:r(k1G/dl,l)qp]A&6ErX\VJ.#m26a2-0de0e]A$#7'bH0
"fA9E`f&]A;?'+Crb('B1@9Ae8f7TKArhX@$/bbamdNFo0-AJUfAYfcbO?UbES34O5<.;&:S'
Hdh&1-bruQZN^W56ln+$/IHi3p>pmkCcNo8Xei9>Cu/NgCcrt?I.rB!52\Fk%MRi=W<SPc2*
=)@X;EbB%q0AR@@%Qh!QPb+,m!>3(8IuF:Ar@RkMoXF%&dGL\57=U6p(B#i]A>P,DgDpdI:^I
<X1rYGGaqHNU$k^O>l'2q!+1EQA6W_tjqAb91m^I<X1rYGIiM"6rLH/+=\Wk,G+PFf51c/S%
I:]ACY~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="586"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="586"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,228600,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gGlzb+'得分']]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="9" r="4" s="3">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<X+]AdV7;c1M%q4Euae=^?(hYWL8N(Ue-QFc(nBM.[Gt6W21rBV4n$NQB#E09kA&\3a)lJGX
P!A"&P_TR;&BN@>ZEX:dI?WA'I+,^&,D7p$M5Fp)j/BrntB_EBj2DIa>@OF0E!CMl]Ak6*bnp
O[f?C.[3(DNq[!7!0jjfog7\6&nEg%5!.YIULLWj#,SeR`DU']AB.U2'(h_g^jB)9T>s2+,ir
2@c5,h>G"kH?q_Iq[^2YVg/O$1<.X#X$ONm*S33GjtF)gpl]AhJ&E;+A/ACYI960mioq'O!fu
qG"JV4X*YKNVk[Lk@q'YUUBQ5bCZ67A?TiV@Ig$;T!\T^8P3>?EpC4*)A<F-W2*NQWFl9]A6+
6T-G9_<Xl^EidDHWn.=Q;jM,nXf2&69gE%f!/imTDfd.o1RdH5!<<a3aMf,Le"oca$552(2$
:=Of:N$CY-%*nksKK?31RDr!'#Q]A&A0k[Wj$+snXkfj1r!`nI:C_"%05mJ9UO!$5L&NJrZB-
sb#>tJ:[%iF`H=YPrblEI2eu\BHI*+.r<)(3a"%RQ#jU^/CBj.Q-fJH'ZA]A1)NsJ_/-8&LsP
j3ufTLa@O`h&%kRV&_i96fCXM-kVe2DW=CEa/b%^hPO9Y6jSJEf=mIBUeg7Pp?f<pen<s[DN
V_C4=R&_gMkj*s_Ebq1IIp?u;GkS3J%1du72=om,[?/P53;>ru9@Z@1Q?Fd1euY&D_Lp<Ahd
Z%Wh8ijc/)-=\S4qQM&]A0$Ma;3"jPF2_\ac@S#KMp3Quf!WP@]A^,B`<7H'GdYajseT3Y=W1T
3Wq/PKp>pFY5S?28/kOdn#m0S2I0G=mgN>oN4IpEBA>F#92kK>OLYc69Cp3<ANc,M#U\F^ma
@--0(@`QN_[QH`ERr:jKdr'lGGO>m@hb9Dp'37T&7#(.5odl]ApXE&)"75Pp28:0oA*ls<L)K
1hBQ7uni:Sts[R^$mRs4Zu"L18/j=q=MfO)hHoBDt)N'FgFb**SYXSd'cq4:m=(@4omkmC@&
38IX<_-q-jTXh%Rcor3>'r:MAYlD=Tl49E<sbc\uRpL9Zi'rmHbVT(`hn6'q)*m1WaCia_#\
V,Aa8*fM4bW&oEc>ja<&n/`b7dW%A,MgZUUS8gA>Qmf&V=[7,R^mH\)?AqQ"c-5;';a22'`Q
#<=),U"&KO':LY:DI#c?"jVA#<>.RV++O%B4=+a5*&.K0Q3sgl]Ae6F\('.[.2`a,H=]A*j%Gp
iGl^WZ@9*qS%QiFH8sc0#2eJ6jc3rGGi)1A914Sg3Ya*l4b[mcDSI`iuq[Ni8)lA(!HX'RAU
OB&XlT0gJ@oFlFB$[sUN#(_F.L+fF4pi8hOuur/5-3DSZ%).Lcu*Jpoinu0PoehMR6c#H:`A
I2+]A*Y<5c)2+^)8`Oq8>cJM(<)T$,bkt`g@lrW`['.YXH@H9lT(gLtg-C)c06,J0/=0`8SpK
d[o=ONc.f[V&PmkF`o\8c*`GK-3dW$0i9"T$!-g%LKMD!a=M=L@U:Br0SRUgo0YnZ(2bN%Zb
CmPKFV=J]A8a-*W-OP0`EsNu>i7</0^2:^f]AU0JT_OV_3I1.ojiuq"b?hHVf#()rOMW-m,%2a
_p*NL&<Bhf3HO$%[H&Qo,WsIk*',Bm>UY`]A_r`nBHZ9fO/2ES`p&N);;j1sHPGm!P2Pt6+sd
Kr`rR._8RCuE15n+R&Ef-Mjs#)rq5n<,p/KB1SZVstlZ"g-FA(;VOYWPbBe0QLTOe&U>L?TS
ebl\:<e)i(H>]A:NCsF'^l0M$&;*[b%omc>kSgqnXs>&."LpT0t+:/X^/H-<gpAU^`h9@BE5W
B9&,p;H%3Y)tu]Ag7*]A4Gc?VnM]A@t?$k'h\bX81o2.L_hiFQ8B;<OOpX@Htg[BbQoMPn/I:2E
*^9UkMj&k]A(oH([eU2R2M"&#n+o(+IMg.6.7dU.J@"C4mQucNS(X0Pu2^R_HX>#SK>7XE+PQ
kdrEMO$A_/\F*"_[>T(9ZLkfXrG2HT#2HaU']AY_^NX\;lI*ckrI7$1ulqG80f_E[c>'Ic2sO
7qt?nAT3_X!&JIm4PWJD[O?V@=$tQ(&k<M6C+MJprXW%0",iPJ?6)//tHpsj^d1iccemt2[@
LEN-erko=,k%H#\4KZf%jl\X=akqF$S$iGgUW;^27O#Il^;TlBKr7E8WRe^1.i;aE?H$Jn;3
=sMEVFI65$5,?jShEI:FLDnmdig1^0R[SEW?c7[7+&5CX)9:S`T"1kjm*^+DQp^9B"1_);U.
l)K!Sr4XT,JR]AhcH?:0_gc]A>[9>$*Yi.nG^0@t6P#.@BS!B4L\UcQT_,qjn!#CLKqR?;N(#9
CP2B]A\HMLM_au@Z>=b1Acpt(Op]A&Q,)HZc#-6k?D$H_KoUn-hZ2@Za_]A5jER@$Qci[fms\(]A
BWIoj'/B<J"u6ISiek.A6;=#GY>89$EWA<AdRDQYY9J4i;FR9>/Gt;>:(kon0kQ/!'"\+B>j
$ECe\6YD]AI<@NEjXgJDp(>D#uP7"k&V>rFbKgbHal5A'=tg8nT\W1I.cERQ>=?5m+:\d8:l-
Fu%+8p\>7"Tk;CZ_++g?:%'m^h#jBhkp=P!N<meEiiWNrJW9;i;kj2_`6nDc_#`"TOk?;8q?
OpT`:\stl"E6?7_#Ep,2Jt*bJc&k'QJIWG3WMup5OfnO2T?&BmE3aK!4GdG.r&XK,Cc'Qp,H
+Ng4`NS;<Fk!5iB*!Cd_Bnj5(W[_@Nc5HjfM":M?(WpXfVb"S`#I(+1K3UJa?*E<ZncNcI!n
_W&c`mFqOn9Ok.-CRfue<k3IDFl/^`LGl-2s6V)=.,+$QJ(/4r1!jgAT(A]AX0LaG4bG"g]Ac^
XTE"R7f%"(W:>[;mq`*G$FmBGC[l#=cRB4nefKI1$kfo`nAA7n*YYj8C5m/PRt@6J[>@n9g-
X%-F('l@`-.3UcmrMs.nOmNX&>+4>"nrRmkJAm04;X3`5/Etq$:O)rd,3Bm`0Q/\lmp*5<n;
\:&]A;hNF/S%[SJCdd+Qs#o$K8i3]A)RDJrNpdp1IHCTOrtQKU&:C5UmEh0+Y_0[?3Co@kW(m0
/a#B^]A>NuAkHP1d=7GV\G<2r9RbseAM>+DkIeV-ojf]Al*C[,GKmb\s,rI@`<L.25VFrn!Va5
E54FU7tk_1GA_#<#s`0PV`oZ<imXUE#UJFZG[/H(>Qls"%C'rEbghCao]Am<&nfi;i`lq@_^N
##N'C:NU@<79D"Zo(\;"qG:$eg2p<61;lsEJ-^GEmK8\'iT)Tt#/1^0QTj&!\2rF&GjYhNSb
odoo*WB>.%I^%b<8BTDuME7h5Wu`)q,M?7s\lW'ILaA.[;a7DQ+[cRX;L8`]AVN^r5-AIW3r3
:_t_l91KH9ABpB<m;ZfJ=aF>%V,Q3anKh9.[m^*Q;l+OZm^leai4SEDJX<0rf;!>CT=]Ah3<1
HG%,>9[D98_a;u7Y(G!pWbtgui[#>PT.C:^=8=Jp,2JY=P`G*AY;G]AE,lE>/,G@GTi>Q7B;H
0J`SWXW<I]A_l5KjG*hdEf*J"7FOZ]A\M!8S`PT<WP508!hI[[D\I1]A&d@B3CqRiZ^3pKD*W0I
U[kj$f"W2N60r3^TlRn!79@F,`2j8RKb&71?'s1pC/,^+d/[m0(-K#fB4fo?cq_-NRL14M2W
qVrAFnF:,njh6!CO97UAF>IM*\Si4o#hAgm\D;[>E[W`]A]A*Pt:r,M-gbA&+k(I=4/:uTE8r+
plBUP@-rCWSl)g4=Sg$saflIlC0uMYPmj+UY"+>OoGbr=8h"KU!YG>:^+">(!+VmU'D?Q=`[
G`"o<LB1-t51/kIU(S+al/XKY2l8>#[Qji5?Ot=En`de64[5t>p$;1X!lfn;7oA89pR,8p>(
(1`DYl`=p8_Vr/ndYii8_dNAe==-Z?_$hS1VcEq*$r5d\ts`h+Mr;fqHDi]A'(S5F3EcoBm:F
!$IW.N3iO#icC*gXf;r]AombHpnm'&Fu]A7N,XEh;)*i\2_+I912qc__"s"$??E!F'_\\B^tT3
d=9/;:EPfmLc2ooDY/9UUltl*:YO^]As$&2d2tQmW,N6VB&4L%,ia]Afo/q]AoqUL4!kr2Sqs&r
4?N>:\bL@I2J#-.7Jm-Z$l\la0qlYBG5mX]A4]AV14Qs(g@ckcC@cbsr_"p8[!BgKa$e;ab_5_
G>jVI^2mV#2j<SX,r3=$.D]A8k(6H#]AqH/I'KX7kZ65e\HL[)7sF&/81SC/7FLUM4UVJe=AD!
nBq-P`_Xs%6/=:9VSD7hqaD2<YbQGS:)1Sp0.GSK<S.Lqb[PodURl*i^T%Lfg@fgA$$hEm,q
R2H!b"V7P=FVeT_WspBagJJrX,bOD%<%+pQGS!Z`Y6"l3FpD+&:uQ7sTrCW3PHQKM#h`IFno
hWD5U<@#Zu0#\rEVF__iGPb7(i<MRIH0]Abfl!M"@e(l,kfSUp8pST2U<G5=5_\*`Md5kIaBX
tmZA.]AjFpA2)>Lqt(%'C7rH3ZI,pmI.%QeU1<Tqr`so:16:27a)^Gbb;'/#N;7==6K?FUdf?
Dq"#Ms\?DY;JeFlcp2)^kVq!D=^.dl>-[sb%YeW/45KO2$!EU3oDGFY:F7*Hrlr3P$:3UeXl
(B?-dUDh9HV._DYoY"UU$>qJH&:3e^`$Z*#2br+p:oT*n,dD*aC>b:B'^@ff6q.(P:YW*\BX
@FO*c!SKf?3,<QcmF8g#=bSC\L:?,Pj0r,:3(+o,]Aji%NoYF]AF5=k-DJ>6@22#mgt:R+]A_&-
oG76^9;WhHA0Xcf'hMNIDK+V"$.X^D5G`"2oQRa7MV$XO5q5CR#YW%jP]A8P*GYpR7okljn,V
CFPs,iL@%-tD=LP_b]Ali>:Fa9%&K\cegJ7_uVPT9[@XCelTZJ"Am'#-0LVU&V]A^T56MSi.V7
2Cu1GO*QZ'Xitj3(56j&KTO!4B9uGaA\&>nT#)CG#WqMc'8':[=3C6<.6pc_ke*;_/l&70[$
=#>Y/.W$Hf0=97OZo,6po+H)7AbXL5d6c<W#NYi*n7u.LqH3%-F61ha`9urR1HVLEE5jo?Hh
$5Y[jscIYC`lj;N[dY/7n0egG.@D\0+ZjnjB4YDTB'!h36%B!^TZMg#:mOWL@BBYcTq41*`&
:giY*;S%e3::&fHbC9fY.H<2?/C[!,.%JIGCZ?Xo(KIf\K,-[0_/Ge1RTRfc4^01$e$FH>Uu
s6j-QUbuKZ1*ElVpDXcTZJap7d)m&ThLT*(!+%UXNqu#P1AUr8h%'ntr?3S&";/;!dLP&`6%
l`W#pB^?"X/]Ad^c2Ru64^DiU*S4>`WV3&Qq\/d[P04?+1KROa@=OlMRcY`[J[ij7c&hR\"Q=
t%MO/>4$lR>H;6c6E!ZCj;.-mtL[YV#tW7'=VPb]A[7m5SC?dJ&dT$e:6X>WG;oH/bMNt8Th^
tLNb>^>4d'A&,B=Khk.LP6Tdq&VEQTUX;\^<"pPnWj<LZ]AiXab<-^V&bH5;JgEg<WEQ=b/6>
[?W?1qL%mRDf927/O>*)a\K0R`d_IU,k5Pc]AYXTs8F=mEc"L/a9sVUWoQr5B<e#SrTp?R3("
##^QIA9Q]A<5urb4/f"O4;!Alo%j@H:r[)/d$K%Vh3/P\FI_T/T.q`eg8d3#C$tPiI`m;rS+j
?Kp_=Z\ab7=hqf?'%!:[U(nkGMBGK=lI.\Z6p]AVW^#u'1\E]A6coP]Aq3RL%O\nJ=^JErVK<<o
"=sQ!3b:C0RlnV8i,nG"QQ7#OrPeUD[@WQLOiM(&VtN?3cq#g2HZi#6*qlL.#4nObS:T^oIh
sr?PJ3oD/N6kbk'hX^3s#L>+*"%>A>.^Yc1AVA%%h&dQE'Lmie$agCJ#tQKtdOMMY!>d.==<
j';JHfGF[`g5X77ZIMn`Fo)HO+%mjtBkh8R*cJA3D)ZgY4tsP9"m+]A9f4V"^EsC%t[ZCdcD'
J@FP]Al#q9`.$?h"@Pc:q&.FY0'X/=&NEJm%'#`*r\FB_=5%P#U0a`:M'iD\i)G]AJma@M6+I<
<D%CJQAp#l4d:<@!\HWld4!'HY29UacXQ,I)Z?43u6ZVEbq*bV1mE8MXpm+&?n!:T`9R;RQ/
B<oe46r4g"k7%5imJ<T]Au?jC5!?imghSqpc-08*N]Aa>7qLcDG'kHG,FOB6665MCW^mCsUerc
2fIBL)*Vk9u1iq994#@d<#,@XZal;cS*pGQe3q_YcT.E=K4236+[$`?uC,F:_[M_b+3bd*r"
)4RS^5TeTj!0*Y8@X3Y7]Ae?m[:I=hWA*!l?qW?gml$+Jn[66i$?M!$UE5J8Z02OT+oWH]A`VU
n9R%1_q;Ij,i-l<W<^HbE^>h]A3O/p9jqDD24:8-cnDLN'ci+aW=JuC1^EshhS=KHaU<DAp%W
d&q1=FoempqQY0%/BOBB36)PZ?1[X(ASu4--'sTuOH5LFpkT$Of[B(p*agrImS83NhoG&RZS
%g`%.+c.D4qE>dUk,mC6?bp>N$Z/rPZ'$6m+L%S;$ZWPn6>H)\%7O9AJH?haG5fk]Ai\kPc-Z
["qr:]AR0tSm%X\D;968N*k*st-*`Nd+%7"8iih<1%O6I;*Y4"L9=Z^)!5ndN>UM4t_hVn@3&
4R'Y0L!&\>XDt2Fo@mk)h;G)X46KZs9]A$9HOeGh<)&c27dIJ^J=X1:+l\LR7LJDBGC==nRc@
]A'2fTI#%*)s(nZ,@5")+VMV)Oct=f;IZul=HO6/FE)^SHYL?nS)S]A.>EPO\`IPgYhN"l#F^b
JTplSc]AXkaP7%_u6+,f`HPu-G.N.j;U\S/g!Gh/a&&6]AmEVo:s`85Y"dKC97\C?j\0\.q[a>
[ong@@2t7L1:EKS8se9n1(;W@6Z43G'p9[e[e?U`#d1omu`fs,QCg2M@dJTQuN4WP&#$5IZE
_P'1"!Wa0+:ESX#ZC);`;9`l[+D3J`/p;,rqQiT]A9$8Li<EM*j*M4KIU@^pMG2$JFRVH?]AMW
3+7$tqLpFhFGEhnmt95e5[jAEI$8(J8N0,t[Drfb1JL!U;'90,je[;#qQR#/T%YF$Id9\KHt
[]AAUm!R\#E]A8-H>ZKfjE@n?0`V&LF5pQ^?WI4k51XQBJ*3.dNRI[m/@u3!%:ugq`b)4)MIn%
HJLX%lmZuAQ>40G5"9tD(_b+qFD)M!6BuSQ9O5ED!c1]A*[%Q^A[-g&Zm[]A(6iMGpQcHMCGb-
0DpoTm\Ss8PoHrF4eF<=S5`:7Md_4Sb980P!VO;NB.lfs,%>X[Mf>T)fY7`jpe?NFH'A^7QR
6SkGeCG.774Lj2Ca_%n!.:;iQL'ZT!#*T'>ls+MQ72):P'YglPlCpfAWDG)]A1<;A:"!Ic%n?
n4h7ERVr/U[G'e/d)!K>Sj*V*6`j^\@EPH?FoJsjcKFpCH"R*9b.unCgoupTQFu*@LL)XN)^
'<jOrf0XbMppNl%8(<1.%;CIcqa$Kq.8oQY'+/(ulITWh4@'U6`sU1L(Fu!d8Nc=bRR;#^@p
)Ef'Eq'#k:N@&?gsf[kUKIAb>9ChlBCYkKhs8G(Y*$9$c3!Y_.eE3VVo>Il%>%aShSmm8e"j
R#IR5W3&>_V`8P?f^K^pbAiX%&>\+.7[PM+8\E/j>acgIUM/P(>`OHSNRCLKM@7T#pb7#Xn`
9dmTd&DnUZhQi_;I"=&-dG^$S=h=q[Hg1Ks3lIH95)Y7Pc.bG38OF3io_CHhg"crRM@4rC5\
:$_GJn8m1.M9giFmAE#^Ad72BYXI(B]A8jhm/BhWq'm`F4"9ZOu[RpMVq^(F6)Z#e4lt:IQeh
V2YihYqLfIGtbFJAog3a"UI_ju;C11aXNmY.,Oj:oN2$a!UjQ"U+!j(94!2dYf6,bqL7iid0
ip9/V%!5PS[WW9P@qt[f0n.2oa:BLU;?.Y)K!:XTi&lukA=:$Kd-*2cbYS(!R8luBM@3r".Q
14ftn?SQG[.?*M5`TVCs,Y?1!rsLICH"Z^J3j(qEC8L3mahFl1;6ks]A>"!Z/O9!S01OL;DdI
e'[-=o6,umN:;O(3-<Cp1gPuK&08QAZR<$bO9<Do)]A,umN:;ZE4c'.o_Na(]AZTIdQA)2t(6V
hhS$+0)~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="611"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="108" width="375" height="647"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc("+$$$+") style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m94@/;X(c9G1/;8Aoc>5a-dX4fhU;Em)p*^ct9Sj*4<nQ9SLV[c'/nS7?Jpp%kBNXPf_8"SQ
%'e.c^iGbD\ni2CdW$OI;aS,2eVPJHO-?55UilkPr(FIW/TJlX&n&htMDgkC0"H>q>)?OPcs
chY_!o$]A@fAq"l,AYZM8gr]A^>&E!<TEG0[tAp"0l6Dbm0o]Ab:tRU+g5IMR2'+;h?MGE=\+ao
R)+ok&3I-BS4&^Z`b$!fQN%`G^eaA_Y:K?XhNQH4l"W@RX91$6\&)o##]A#J.t,]AI)`E#Nq?,
@8[^U;&Im:NThY%cj;Tt,_"L)0f;FYW%[rtV.4q9)r%6u52l7t1X]AgAV6:f"SJN&AXMja1dO
mQUT&_#1u%]A>`sX<hTB0TYK5&B\RVcYNUm2D[EtH"*irEU":6/-Vi*@S3/C(og8iXW-lVF^+
WDD*Vh&bapq%K4'!)u:YofiFST-[J+gG%]A)dnPNIcF>_IXGB%2HAAf/]ADt/C5Es`[c2`4]AJ6
:[%8B<!5<K'k$t1Zj/D6VLU,+\(>[&AkW[*PRUR1(cqIbm-[Qkt8;V(g;O+g@BHenA8&:=%P
?7E+0A+HTf/\,Pk*moLdO+Tt/kMfLUW;d3;)<45-l04tGXo:7KSh6ma,5Q!L#D#h,Z.V%6dT
759&7X;]A3OQe2.1\6qQ)Ehf?[jboY3(D&:;Y/V7pc5qX.YZq"*h+"1"?l;1^]A"^>6>?lg;X?
WMRRNQ)]AHaK.jq(MBA"b(`p#Nq+!LoC%^bo!]AsBH*HgnL]A]Asssd^r@VHusIP^)I.n4&G&gMA
M#DC\qAiWu%FTER%H\daZH_kPIo&mXUk1D\]Ae0=K*JoKl]AfR!J+o:VF1+G<CTSB:XN@PdaHS
)SbNro^e*)Ps0^p5W5#hIA:GcA/e/q'KlCNa!Z3O><;jaTcd(T2A1.a+RAoC9ODDXWEX.rY\
;[#o!fPZV+.)Shkn-</OC[h".@Auq12RsPR.4:jX-J6oNLa520%Oa.&r0U8^<1T)<05$$<Pk
A7R[eSYTOlDs23'ZDH@D*96mR$F6"Z4o/eO6)M"I&Xl;WGj$TgQbo43#]A&K7!bjq(J#"_%fN
EX!O"XOSMahf*Y'6!gN(N-(>P^%5dPlZ)?-]ACS<)a7Zm$T1a>N3`gA:`T'Z)ooZB!JE`mj5g
%\'<D'<ql%oH]A@Ye3DC7!=q*nTbYQ.WIV_u@GR]Ap@HV(,@XmGcVA"O<mNlCXbg`i&0/8b%[)
blP00k,cI"!2V]AH@;s#4_MdtY5DYPEPC?i)>BSG+#C"iHS]AJW2XqR-S5:hhE>Np>kr->/RI'
eP:%V?A**e%%n!;_FA?Sof(69-u?.%?KFZA(0jnijVa=4XmSf&l?6542p&nMQuQbCOBGcRjS
qS`PPf[0O<\RV4aDjVI^C#m=1eYoE)hfe!J^oq/JH1ke0ajFi$SX`7a+9hNli]A^cI*Z%kD#L
#FJjXMK_QkiI);mPrngo\DD)<`j(\q,>u#M*Si[8*$E4Qpr^bj?67>Th9+O29YW0'p`R7?`V
.]A#PYD^FrJ7Z`#&e?.ktKpYYHd1N(nKYNK1kHRF44:$F8*qMmT(b/]ARX=&Qsjt!ndDO),:q/
4O\XU=@`_rD@Q/(hIHJC&(UR]AQ0E0'S-quW9:XPKf(n>%_/G9dSYa3fDMo'F%4I)!($&gSd6
7hlJFFJap.`RE_ZY8;SA,)c"(?,MA?ounoLVXcs(/DJ-Fi"Qt2Z#gFQ1@4Z.)9FtpQ5^hfa\
\b5\;\BAS4DRFSa>JDWRHYnRrG3C>L>tp'`E[euY,/q0*h37dip>Z>6&bGEt#3f"THeA\Z-<
.r&j9ggouCm<n_3MfaIc*ICT]A'OD^k$jF;@>seje<M]A38Z(\-3e/:6YCVDg$`]ADi4_O?BP[H
H`"ps6g8>jViu$;nD*=!p<d%Y'_^AeSMN\1S/SWH[=Sg,ah%LTWdbn3ERp/'foIJ*s:UU;Me
3+dridV"(4a$+31g$\(4H(.NOfqa<"U_8ok^3c101J9#$8^\ZE3j=]ANeq)2&9d!$t.YWY_N#
*h1<<:Q`"S5-"\W2T[7]AYHJ)6A=?B?C_0\q:@4o=Ns:]AP1PQ.4Y;Lrh%JpU[.bt&kFW[4kh:
V&WC6qQ00.G'QT<!Ik4%BdN8!4pm8@9FgN@6$\R90KH>4<g^JL8Zaf,6fg81):)"J:`Wtt%.
^;$+nT>h^Q1Q4@LeC:nU6IC\JlQR`.2G;%YeTTk,oiT98fjQCWP*Rfi4k`d%(=0U.SLH`gm[
sF$,T6*^n<>t>3HjnJP88L@^3_Q-%PDtkQRB:H;$?l?R_8Rk2u=R_pOH9.H$4m")`N'H'&2*
f;%XaUK?."'CM.^d!:/C*fY8Q_fj7+eV&_]AJkX\dof^(T.+M49[J0usl,PoPF<IS1e"A<T+5
1ME4.i1QpM-/cg8'lrDG]A5IoQ9Lh7io+u<XmDmfherLnLKs$JGKi"peR]A_*:WpH.RPJ=X5'2
r'i9N,!7RjFt@"W%ank3r"6@i@4K7$[(:e5k"N:PJhSa!)V./n%N=sGQgR%<?-2Yj$[Vbs4\
]A$T$0lYI)P'\)W?mYk^H]Aq_<glL$&;l_J/0hulGeq;&`;Y`KY_$K@ol3=TN?^894!,mWR#=D
-'^"C!s#lWI_BiSu[nG8+e=adP#OM._2[]A7C);hqb,.6k*=L1UfYC>u,8#N0&.Gra&\8@qXN
2dms>&F^;LrWB^>SQc?Rcc918)U]AF)-5pAi6d4Br<nX;mpr$a0M%CCj)Osce<^2XCJ3(f_O=
m(KXL65Pu[kcZL+#=9j4lBc6`Rl^-N)u1m9Dkbj>,Ya*DaU'GYtuL6p0'Vj=R0?PW1*8sPcV
m5\S",Cfbs#01Bp;%!a45G5T?87h\rliKYnt1m7Sg4B6SISInT!Zm2Uhi'W"`bj[D7!K^bGg
rJDC>^,.T5CeMN=P`tEj"XgUm.cpnKLiaVKaI>lkT^sJ[Q1%At(e]A<WYl-N:"lPu(49on6*P
j2gdD\TrnZ+fJ*gJp[qJARN3=HmB%!-:7Q[tIKdU>N2Lnn[!D-#$u[\(kfV6"k&r@ME^\X0l
dMLbkGY$JfF:)p1R$<EMAIL>[S-+t5312]A!C`haMEn(c#3Qk[i(#mDmphkUEij'I%So#
X;kQXrT%]AAFqC%CF)O:]A'q%$kKp?WnaTZ?"QhU?)3lK."WNU=8XS?V3`PC'K)UGs^'>Y7/8;
5b%&r2UUAMPQQht6toTRlIFFkE!)0K=FOJ@&(1GdIAs.sMT^ac6I#P'@T@?QlP;N6Xi:RA[h
,dU_]AZl!riU,L2a)UBdX8MR&j",nilmu+5S>,lCt%@0`1J46<H*"O"*eQP_A57&p=KkI4d`0
M;k7[mbA=L@g/6b?2$\1*1I2.4G*!sDCUe6OZM%8,NF:(.c%"YYl&6W=jH@sBQ0)[/Be`72M
n,us41?@5.PmE5p-]A9c,jnW:#Ghl"N66Jt'2Mo'4Zf[a)RlFP@qZLgU7]A@(3H^-umY6:FFk5
cNK:,jBJPR@t:5j\#,+hL;f7*nI0W`o2qGl]AEXZUe=4e\lA,L/^AQQ%Z;"_-X'O2^H.-]A5$Z
\$DCR^NePJ'*JU*uaOejD-AF<B,Bi>FEpWT:)pRTY5:)O@`P^Mlqou1g*Pf0X*ICEuGjS2Zb
J_kN6G6RkhEAj4W46'pZr7pWC7H;D+gkN2>cF%Ju%]An7??AO'#2ABHqMta\-WC)a#<SJh;F=
/V-(u`,Yjkn8&:hS>;ZiMd0h@9([?G/[H(TTGL7j*JO^5hoP"Pn*InVoB=<fM^RRYN@B?8pi
8qYlA1YSjD3dC0q[QP?m=@gsJ)Z"WcfUWUVCXPVUg]AqTr0JM$M3icnr^XnBoV:b=+B6%_*s@
uKos*Z:<#aBBA@bO\)K6GjBLYNl*+mFG0GX\`#k?4Bn[pT!')qro5^qHjIZ"X/GQnSQ2!cR[
J`p[gF4RF9,@9;n=b8Gr!5c:PTb3?1k5l*"pQ?u]A#[lXr;":QM(2U/:]A0MTS^94qt@XH8@h/
\Kg'/U7WCS2Z=imO/V)NTB#Hr?EQR(lk6F.k8ohm82t9bEJnZV9H;OUMk[71XBou!9j]A)>W6
!E2l?R)q\D\igWbf=?D_CpDBI?sqW]ARCKR2rAS%j"Ie?ek-n1,BGo70u&ARt--1Tcd'*7C"u
/0N-cMk4-Cg(*q,Yo-tW%g4T'<?XTipSm:8/^GpOJHMntRkNIpUJ*U85Y%@06l[NR>#gD,W4
AbSj^a'pE9)hsn^JRafNE\E&K\lm]A+A?ul7_4';k=n+q4%,@oInJA^+bJSSin&mbAuZ?sl?!
u1ab5V<V70*q12$$0"HBu2,mM`32RA3$UGP4k=kn3s`IK2%?[2DU"#INS=Z,'GK.2F2S47)[
6'&Y`]An>&P;*r/%mI/:RN87Frct'>cTV+PJP!s0HgGigU=N3Y]A#licZn"l]AnEOf/4=d">VD#
MJq2pQt*^87[&>X1f9C2d=:DXY2nQe0]A?)Nn#fg)Kurc4-u,G>Hu*QO^@#f>MD,<e0]AYZVsd
srPP50KQ12b5e36XhAkH4LQk;OUaa+;`/43nU)?p]AW@>)6fZrXR)nY/qS?U2gn+=KV+%J2tH
J>7%e<LnL[--(/G&'3$"[<RsgDNe6jF<]Aipq&),pA>0BQF+PkEfC2tidX#(/AU?E5Uk'no<M
j`?f,U'09[Brn2C9N38IsL4,X2b:$3?$h]A+F/6SgPd1GhHT1da&U6SgPd1GhHT1da&U_uCft
5qa2QgA!tQjnj?2oZT7\ra]AGWH4.f9V9&:/K14Fcn_K\RaWl83QXOLS9U\O/BdV*#R5C+>dS
-*$1MJ2\UmX''B(GsI6\fjrmuAu!Qu4p).*.bQh<?Hi"T~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="755" width="375" height="25"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg.png)'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="TAB_ID" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="tabn"/>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($tabn)>0]]></Formula>
</Condition>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("TABN").setValue('');
_g().options.form.getWidgetByName("TABN").setValue(n);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="RELOAD"/>
<WidgetID widgetID="84e1d24b-d351-47b3-ba45-b3372f5d8693"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="RELOAD_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<FRFont name="SimSun" style="0" size="72"/>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="tabn"/>
<WidgetID widgetID="8b6865b1-24c3-4ee7-9b24-14f368e8e8f3"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="tabn_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="20" y="18" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('LEFT').style.background='url(../../help/HuaFu/icon_arrow_left.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",-1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="215" y="17" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('RIGHT').style.background='url(../../help/HuaFu/icon_arrow_right.png) center center / 100% 100% no-repeat';]]></Content>
</JavaScript>
</Listener>
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var a=this.options.form.getWidgetByName("DATE").getValue(); 
var rq=FR.remoteEvaluate('=format(DATEDELTA("'+a+'",+1),"yyyy-MM-dd")'); 
this.options.form.getWidgetByName("DATE").setValue(rq);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="right"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="right_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="329" y="17" width="30" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[ 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
 	document.getElementById("DATE").firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="date"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="date_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="12.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[='']]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="215" y="17" width="144" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[4267200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[管理指标]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="0" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="163" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="52" y="17" width="163" height="30"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 
setTimeout(function() {
	tabck(objTab);
}, 10);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ImageBackground" layout="2">
<FineImage fm="png" imageId="__ImageCache__E06A636B9F69CEC5CD01541A3C3A3E5F">
<IM>
<![CDATA[[8^:.;caZ]A%%;u=&?,\`->I+k`=NXH_!Hq,&?.EfOH<EF'UPa)!Pr6DFGU8L(/h"R=>]A8o(4
r@m!<jIsBUB6.1f?T4KHO\1P6C,30E1b27t9AV_`dn`Zer;PD4f^'@j<KK"@8![pX&gM`%V8
#ceoV7hh/gRiXo^@PW-iCn4_e[=5+&#*cmD@5PCITRCk;cCNIPc(PCMDAmskN:G&8bY,aD/H
ZH[>b,Fu2f'p,EdMle4r&$&3Y*70MZPuRaDL)#i0p/dkpS[Y\aGEg2ppu$UZLDmS5^7LSl=I
;Cl*1h#R%!:7CMLIHqFi1B,ZX+Q89.R(h2RhV/SatPkt01!Ht#cd3BNT]A3I=%4gEQ+GE&A='
S16V_f,(Q+GUKU;D!W?FXRnumpR%9^5AYN/F28S@K3:)=>c+NqSa.7VeL%Z'kF9>I$G!`eR%
oX9g@TUJ'341pGs#)8ItQ1BVp.TXa`X$mm<rpH[$-,\s#IUJ6"$0D<*e=K?@e#GOK8-(UKpe
MSA0fReKI5TP\L)n[,?2LdY\-seY(dCc(L(gKAD6LQ#YfHJ!d@`an@74n2B92f9TKtpQ+oZV
(Q)eDp_C0_MTbN4#maRa]ArCK*>OR<_B.gZk;XOfn:2>1e3k[5$ooVEmd+B8R^_O[/r8^fd,&
iikg;/?Q3]A\QWHikiCqR,<:5X?gYO*FP7E8s#2&O9XarS+Tg9[ikk^M(5#P?EjKoJDWZ-+f,
<p)A*3oh^6002mQ8g\oel5=i;C8(OsVpAfE*;O-"^FWH3COGm>"73FVTu0.KO;4Pf@Z2>14J
kV?0S(^3B%oef"&.4TbjE+:<EMAIL>-r[W^^45pjDgU/GZG[HOZ'kju
FK:FPlX)&uH07^3]A-@35Q&cVk(imH;D90l.=*@VQ5oNE)a^^gVKFt:F.1=D?d;4mE^SFL2?C
5r3n4;X^&><qE207q1]Ag"l?8R^YYZmcGY[;0IoNDe2>D1^?:kofGe;X:IYtRI@2[OIRAa;4%
CU&Y&S0FN$RbP(j)fVbF*Hf`L_[2A7?]AB\i]AQ=`X]AG9X5&GO6/&^/W\u((RknEApnZtCZd_\
e1tT,?A;Te8"`M(%>TQF36lYiGEFlmNC_Wn>)'98NaUoj3?^"HKuL;f8YKh.p[&"CoQAPE:@
&Tq]AI8rtK0CU$lu\C'+)kFtS%'!-Cn>KL/fJmDUClE&;]ARo64<l/3`Am-qE)p\.hDjXg<_0d
r)U8oPY6ge;<G-qnb3(Y21.R1Z(BG&3?DWnYC!*M3#o9(siQoS*8RhJe4!\^]AgA#Pn<;-tZ.
OW-3L9`KRjG=Pb1NTss?Ju8lDn5<si`HOqB8#t;q1:_TG8#Kk\NVVp0b'Tb#H.1-92(d5Ml7
AN4+0I#Ua7gL2`nQ)")S7p54=FJ&=:`i";4O@)6kL#1l&aYI]AkC$5ijH1-3u&9GU4@DM?;\%
d3a(YBEZ\QC0@;.-iIl,k(`99bYMYnQHuZV\Xeh!kmFrnK'NJ1'ZLHZo<Lis'g.p2d&HCL)L
?ioYkA,!fMYdh&>.GuMM=df<tkrZ+H9oE.4"5Q>O0l=nF*ONd!%%(9:9ZjJMc2<19G'6XXcj
XJn+/#AL*4lIkX0TD"3k(?%Ug_\?:AF(X^!RH$XK-,NhV^8l'DB7B.!mIU]AaAL/!^VbJ5njr
LiA&-QP$qd3@1@:fX0A]A)@8+1.L-Mj!3O^LGCI,eF0g6;I9ed\DFjqBGEU3kPkV~
]]></IM>
</FineImage>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[0,0,228600,685800,228600,114300,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[647700,2171700,2171700,2171700,647700,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="2">
<O t="Image">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n,PJfSG.h8FGe@OoeN#4^B$p.d"B,[ullj5iB_<qt2U<$SaOU/r
\QaG1-="%W_gnP+*m^dHmh2@NM3&)*D4'>h1n]A1]A[R~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[tabck('TAB0');]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="Image">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n,PJfSG.h8FGe@OoeN#4^B$p.d"B,[ullj5iB_<qt2U<$SaOU/r
\QaG1-="%W_gnP+*m^dHmh2@NM3&)*D4'>h1n]A1]A[R~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[tabck('TAB1');]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" s="3">
<O>
<![CDATA[营业部达标率]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="KJSM01" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB0' style='width:100%;text-algin:center;' onclick=tabck('TAB0')><font style='font-size:10px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="3" s="3">
<O>
<![CDATA[人效管理指标]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="KJSM01" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB1' style='width:100%;text-algin:center;' onclick=tabck('TAB1')><font style='font-size:10px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="3" s="3">
<O>
<![CDATA[投顾管理指标]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="当前决策报表对象1">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="DATA1" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
<NameJavaScript name="当前决策报表对象2">
<JavaScript class="com.fr.form.main.FormHyperlink">
<JavaScript class="com.fr.form.main.FormHyperlink">
<Parameters>
<Parameter>
<Attributes name="gGlzb"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<realateName realateValue="KJSM01" animateType="none"/>
<linkType type="1"/>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div id='TAB2' style='width:100%;text-algin:center;' onclick=tabck('TAB2')><font style='font-size:10px;'>"+$$$+"</font></div>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="3" s="0">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="4" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB0')><div id='Font0' style='width:10px;height:2px;background:none;margin-left:45%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB1')><div id='Font1' style='width:10px;height:2px;background:none;margin-left:45%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;'  onclick=tabck('TAB2')><div id='Font2' style='width:10px;height:2px;background:none;margin-left:45%;border-radius:1px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="4" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="5" s="1">
<PrivilegeControl/>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="2">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<NtR;q\W4D/5%3?pk,0Jn!Ke1'rhg9NRXPdFJk^5_OsGN.q<eL(FNQ0SWUT)4_#0&<J%DM$
tP"J-[(55_',;63n,oJ<8hYGMh[m]Aj(00?G66:\pn\!:@7nlF1tRgY<Atpf877]AhH=R!k%4c
#"f5-F*I^p`=MMLp/,lomW8$Bk'GPBOST6]A=q,Zq5d&*8(I8O?a]Ai36;VV]A^=ft+oZZ0q6[g
Z!EqL?sD9^^-j-W;']A0l!7FWQ8d;:rj9RF`),%paN_a'=$P<TVV[.&WkWCocn"]An]A#PcNP5`
XkkE,qVLC[Z'MAY,;)t%oOBqEOuk*oT]A@b.<-';/Z-Up^=42CU<4TV1eWd)7o44UH4t\T__^
0Im@XrkqpkQO."RoB_c4'd4'0[d`0I,bcuN\ilJNX$@?rQ1)FPM5de4_O]ATY/pOQ2Vrn"$#i
bTVg9\Lt'rHI4P/sP%^A[U:V6nuLE9`CUqqJ<Nm,n/d,5[O;',[sj'Q<eqs4_g+Xd-@Zq053
I\B5o\<Hi$0^T[1?VQBG%hD1"dFaK]A3"t';ITQ&H[qnWHBMbhV`g^ku`4%pNu,`k\$=!#%:n
dRF>F3Ut*&u[_ifDIj4V@."mUSZ!E[KLFFbI:k>7aPhRC7:pQBYYlIFf\0@fm;X-&FoG;%3G
JO-^8;C9']Ab^MtQbG9[qR!jLp\c(#g%l^R(VEP9U)YAfS[#d0u=7nX*T$=1\0Mg[Ziar;T*n
4cEt`<0d[JKMSr?7;2is7G]AS8WA\GZlMg(+d4F#DV-sj$O4@b/DbLED<KVH\$Z3'8T-3^:.Y
h8I(>!sMBa>Np"tKP)H@7rG.]A#o-p/=MshL't`-N@3k6qP<iT"Z;rJL]A#+N$mnPK@R1f+.D1
21,oN,J'T=FDbmcdjBM1]A)PWGA_EpdqTd?,]A:U$9?0'K]Aq?4PQ^8@"*86W#YGEfdrjk<4cJ3
G)K&AM=pC`oo<$9tld\'"'fl+rK'&qu2"]Afsu,A*Q[+p:+jL/?&Uagf-H2i+rsRgYV3l$^ZX
(!"Yp)L<C!/AkGWaQ49KPWXfE`-+gR5YWe!9_0tP7j0$%(MRSK@hn1(?0Fj$k@UtN+Mh?:??
eq3c5GBO*2="i8bN^3kE9C+qRo0HL#/>7.!_Yk"(:V1aukXV4;6dU0o,(';A,Ou;Y<A:f0BE
Ci25l/iOq^3.;P&HoSJ*uAChlCS]Ai10c_8(_^THn&qp=L'W9]AoFnO)rDifPjB#/b:,,q7]AU/
oI+0"/nTiEanB5(=r*hAA/LsG*5Y)#dLu$ZI%7gTN[GaiGnlITp3Kg%t\S!m?`uqQ^NqE$4U
DpptS7V8QAKfpuJs)#6mNb6/B2b/gjf^^!M8fut<m@c&]A>3Y#00bFh;bFFoiMWd=pBA_rp@^
bPL_T';MnVH6;sX%uY8.i2CF`p@?7J[VHb7aTP/)\mqeF`7fZ,0hPs(((e^]AfmQ;CCKC[SM1
dcdU^Kd%8$Dq,jJM9bKCJPYGS%J`$jCdf+CC0a_PqHQ8p@$#!qEils&++/D[Xb(8L/UZMrA;
=GO>XhF6o%ei['*pK^n9W`&m%so^6L]A)`H%s/t2@EJm'=0R@GUoBCOG"_o37eO+[ls6=YuS7
)@\j\X)_%h6RJ3.9N^qaDW-R;\*U.SO&gMs:A[3U+[l#;(FrW&(QDdp_N"Qs:$[M*3I'Kq^4
l;"INjYL0:CCr5s1QH8A]A0#k!WkhHX20CVB8*0dB/bXX.8b,tUXdU3Jb2S^7oga\\Wg#;PnY
k@DOltDSaf'%Rd$6q=.+]A\?WfX$`hp$?!.X/g2J65m?@ZB\r47p;eZ/AGX1V8-ml/qI?_qb%
D[`T]Ac0Cn-/^oYqE/LIs'cg)]A5tDrbiUgS=QN2B;TcWfr',;sTULRp^_N5Nt@,pnoaerKoMc
K=;8@,iArd*LiF>#f>0Z;=*45r0>oqLK?gYt2EU^>(+E\[KUfMb,uSN^N]A3o^&]A7PJf6+.p&
fJ<RM1Xg$4+BF2'1HsHq+\bV$u=/Il:SfX4[,JeZOm`H2o_93nJ^7Gt]ASmgTTERCU@^31G[Y
I0q]Aq;d&Xp[Cp+$6bZKq.H-.\Y%-[A5_2Sk1=gCgsqYi'J?Aj(HtPd"k*/1[dm0JG%mo\Z\;
g:%l-r+AgiDX1GIF%`!bu)jeUW[-jUCgH*R!C4QtdboJ0n_Nd28&S3r`Ed`fKqhRg>P4?C6N
NUQ,JL^0?+"bD!&Zi&^^.%9Jjkde\M$]APVf$W;pl-r@[FE(Rf]ADmNQpZmsj+3\H59)Q;QXf]A
P-g8ZOH6,`dn*j]A1HD`u1$lAG6>?`YSZU?5LmU9iFYA`"@8-jTC.D`@Q$8G?178a83DS$+=Y
<<%$=R_5OLO"%<[>MR@89rV4D>(11h]ATi!%&jBHRO(#s)\H^H%Z'idN\P3r"W/lbai>6DLO.
MXLo<QgA4SUF@jn)F`T^/"'jm_6o:h1Ms06T]A*jb\&H6M$BRlR.daFEO"00DU)0Nif9-`*B4
_jOgIsRl<g2mMP%:m0X\l<*u,A+W\e3m<h?dbPj9;<qNaB_,R!OnAljs"J%'k_?Z/K=d4q7%
3J'c^e!Npj3@j\[CF;S3MWjN`S)LeKr4medpjcK)22ub<as)^]A?ei#Rh]A*+hlnO]A[gG/OcH^
>LeB:Q8DXI9GFOa-PnUh9lM"\O6*0RAPJGkgJFpLEa$XT2;'g[D_X2rm4c&,f3_+RJM@@`PG
eo"_aL&rq5V5VMck*apXUdrK(/Qm3]Af.Aee8kEJ<!:/qKZIb?fPO'i\>*Pg*f&R&#5mXBp<M
g?QO&:^ZrP%[^@7I+?0#RZVS#e4\g`2F%u6^Vk'NZgq7gf[(o53%^$5OU;En^HhjlHV=$"p"
rg&Q<j?(Nsm858&&RG3QDriJShL>'N-<**d2sM"'*+/!"9SBG2s);"e#,_?,S6%OXnkd#gV^
4`*=qApP=OcGpg3]A-J.a'8g\b76)LTGsm$pj@Z-fja8'7X4H*pIqrbN>nXo[Xfb.jIPGWW[9
5?KMgo!4mPJtN>C3(a<iXnKh+K@,$;uYV>,_V0$l:top?MN=KBVKbfi9oK5J,P4ri0dJ7!sl
lf3SZ5U9N,4$QN[V!/"d=i(hVsN?/)0HE:kI3+:a#8q%8t25YYm7lFKj]AQfWA7WJ;kUZbH:G
Z\FccbX%gmVn&HmPQT=f6XtG+^#6u\Acf>s-?GH[QM,iQkMDXeT:U"r<6]A1UN<jASN2*Bem.
p^E=o/QYo*,fdk<^&N"DbgR2JkTd4KYbKitK2Q#K$Kn2k(U<'oUC0\Xnh07,m&2$/N(dW3d;
VFY1WO\f*JdgMGaD.V"\R.;bSCU%K]AU"t_6C)>UB(g%0TI(H;o==Uc#*#^$4,X%mh47b<X3(
-tkEY6uqEat1h=hB3s37bEIE:?rf%LMGga[5b-p#)9-Qq>gp2,q\_>3WRrncVt]A0`b(Br&^A
t0HPb:O$>R]ANr8&(\*aZWYnPj33M".P;a8LTr>X&00bkWUH5V@=OKE9oS+g3kYA/nm%-`"7$
7D]A#mO!\&d9]A#?IhtS#3")97f-0VM"RK+f&LY4-Y%*umFn>cN3l@q6DV8k[knM4;)uqqJ4cQ
7D7FXVTDRG?/)]A_`:CCotLo@AA7cd,eL;$D$%g:nqCg-^&RHr\9%_o0!X>l@Qb?sYd!$Ss`=
dnA*9*),-sH^V&78"`^p3?7DeIr]AJ>B1.32)dG5bK&Ig>OFUj5l-nBpM*`fG(f1KXmUis,nM
ooP9+EHT7!a>4\&-37]A,S4R%H`0/7Ja7F`3s78A_0gaGW/76QPrhNc&/I5gd\t,3m\^A1=pG
T+KR3=Q#p2r/mF\Ap3/jt'es2e;-V=YgDU3mq3RKRQS:,RBH=s4Y7JL2F1M!dIXa3OMXLbX/
"b_YBKrr-jeDbQ0aQRF?l:N!oA;gL_8`sl`Z\R(DXh)cFPjb%i[>$Dhj_H[o'Na'&Zce,#%Q
COCc@jg/,;.2F-YrH?Gj1j@opU2Oic%G7b!0@BsL[JP2_eR*N-ZlV\2df'UqLH"Wn'W;0+nW
DKj.Vnfi(T9^@"Q3)>9U5,eF.ku((":Y]A&&K8IX;`U6;SFlg4/%^A0S7cP)B9hQ!T[;;oS1\
9)Q&S)L[9`dVJ?e!JO,V-2#Plh=8n$k2IS6?b,DPX84S;1euWcU85D0[[TYsqf*%iasj6g,U
DHmHNN\l*>O7iMW-OqI5SBpAjH,Y>KuS#2KI!u:gAQoig#EQ!;D`oT5=(c=P(S4jjNFM\6\!
I,6k^C/m8_Z)E'F?Sjmi2]A;'nRWipotf3d8:E"*hVqL>i]AL_#V.uLo_]A8Sk##:lP@)3I61EE
rdeLA/GJ`87;3,re6+RsUT@69Q,=Ta`feZ^Za3Pa=[/s2%t@W\P(F:,4tl$H?6&$_c8FYpn]A
,sOQg;Mn*"F%Mckk)CAaq!nbu;$H=?`E.(JMtM%O>Aa,Y3H_P$#61/i9i&b=d6iE!9h5!Rra
*6Td/([`1W@gnGaR:Xp&S._M*r#59V`;8'22WO(a`&4RqFFXp8(!)m_i$KJYA]AcV\FojlQY2
Pl2PR+k\Gd!+cZd5(LFq5CO`Taa#0Bf7o&<nRTIp:iMBHenT:EGJ,:UR+?5g<FET)i^Rk<\R
S@_d)aaTMp21OILf'9(Iu39]AWB!IG5BGmnOlo=hR"E=^5f"j%aG`&g7RE`2kW9"R#QYpaF3c
#Y:UM^DC$Cqr!q=e?iR]A<qMi0b[HcHs`M7YF]A1b[E8L.n)>+JOZ1c4IB%LunPS^mDpuWAg@9
C;\IaV=OFsn0\df,IU&+8)lM:U>L!]AXU2PX-Corse$79!hY<LVaIU"NfFbn7XJ]AK4?^$0Y'q
MVk-&YU4%YN\CAt""72[,TUbGN9"Opnlt/[JN;Uh7K9$"h=fB.ALBo2l[cp4E'%;T)"!6SIN
dbUu=;"M,D[EXE[Ho7BUHlXu[Jre@RCl%pcjL(X_U@)98QR'iN![-"6lR7.D)'211JRNLi%Q
uLn3)s9#ZmROkNUl!Kc6.+'3dW0r"Rj"V"E2WX-i@#`=3)jS-ZS"E;?Y?c>dHnd(7:@X33pL
>tH:QrTHN.jRXW&\bVa"sMBCWQ[>>^;gM9'>Km`-a,YBDR3P]Am)l/Wf)aQ*s'u+!a\J_qDK)
_#7snlrpK,*\*L:do#FOR&O<f)21@o<]A)H$5@/)CLqp@_\5pR5`I5gO269``?Y3:C$9+3ZWT
"qB?s7#nI2:pq0RP9p:f2:==7P760dB,,#Q4DVN7Qtd5VkeG]AnT</Wt+7paYa*Pl'7>N*j2f
"n,=g2RF.B0@_/hI;n>oEG[4;#C3e/5SF#nQoCO`.Q]A^LZZM5sL>Ac4tI_de@f!6U'^iUeB3
6-'b&1?1]Ac;BK$FQr]A-$n5j*Hn"eKNe2"NA6>2Dr/5o$US/0A2]Au_T9E^u(fuu_Lb\kq^5^r
Ph.Ap'O]A_BU[EDb-ifW'n\g[OX@q#VYR[I=Ih2e/1\GG;bNo*pW)'1c7q-A^NhX6MG)>YB8(
`RL=\Q8]ATY5%%=#<B:ST"8M*O@a*BZQ6#!lAEXgY;iN'.O"/>\6J]AXQ^3(AVWX(]AOW'^>eg(
9uiaP:p=dp#3!%9ZDej8+D`Cl^NB)V"uHlrL-T(J%-n\?]AF%"n'V[K>U)s8p&'*FYjSM'C^1
HJsRV8'60c01g^tpF2ODeGQ.c9Ms-@jbB7rKjg.a0AAbiqd,JDqe"8B^<k:EQWp$8[*^,BNd
f0UU/'_OtRa8aU4)K7'pjkEVE$;bYO>VW3+q:ZPeF^Ef<N>5:&#[U#S*#"4`U0A7\s`X`R:p
onaQ\:b?$G5bZ8([BOEQRuOOU>u&LDD-9?S<69,ei`7N19TO)XBf1&"WN*m/$C"_8I.Ei=FG
(Z9bjip9s;c"fcW"Z"\(2DCDV'J=Z[&hgRE5%#[/gL#BD:fTQ"@XEt1YI%'>fVaVGn67]A"P?
]A+aIk*cNgLq?]A%5W<#eV,akf?mWZ".?f,k\$lq++I4>CeEe>.63DmVo49<=[kln&B1\!7ZBT
=d,M8R`W_eCjL1No/K$aq_s8b#l(C:,5)4P5<d=%k6W.tNb`N]AQ6C=,LIe:ru+)k]A/b4"[s\
+@-d'WojH+4-isB*+<u[<JdL%+R?_e\=V;6I)p18u58\_`]AH!@q2jdHEJai9h<>Onr2D,FpU
kE]AVGZV<C(fdR^$L#P$ChGXmo:+kVDDmIioMS+k"9N*)H3R;%DUq>([nFXP)X6S"=;u[4_@N
KnmuZ]ATtRO#AueULre>okl:(MZd>;sQB"8ji%=rh:h!+0><o+<mJ$3&0!0n6^.*kcG;[+Yh&
+(\T2)^A'eWd9r:1-_b,=eKBoZ7oj;[8iIbea/=J*!`H:"\+k$o&@R$IeRI?!?99e<`fkI&!
!YAo[egRcY\!bK]A44EIHaQnaa96WD_B\a3)eJ=5gr)5c0"'*ABP+obOCCZj*qM'a4#U6aD!k
a6ebj3W^@,2PKe7u5o2-!)qRrt4VD1UJ;^Kk'L9P:`V)nk0N-g)_h4Q]AaJ8Th[n.([A6qR6Z
1Ph(*3Bs36`NO[%L#b`@/!JGc/l(<Q<GaMdZOj!;$?ie]AVed,M95W0n'VaG^^\@u3FrOl73B
`<ksM_s!f)jWQZTo%$H)@udqH4`"at@\Bu?;BKJ]AIu-,:1hc@pf/$BJgA;42O,23>RJq[/H&
iYYBl)UKQ^%,qrPbmcFLH/k%J2SHO&"EqgQ?BG<++FZYC(.ij?M$m<hapa6bgYjec!i_8C#O
CW4>m5BP`\t4,Tr!fKRr4[2Y]A*G$(ZLl8M:-()HAJF]A4MpY>IF"`[G7ra0%^sh</?pjCm"d:
6BJHPkTD?X!XWQ_;&OkTW<`AI3Qs1\*qBML6@Tul,\L8``5(DnbPcG7@0^M5:@p"1@0i<5mD
+Y*h/nA_8T%Rp4?cWp7A/A\VKj+\pf['R/)DB%'58E'-/V(V]A5B[JP&fnSG<"i/lrt#Z;Cft
(#@h-r$r3\W+^g:jp*p?f3g/F8Y9uE/`ke8#-nr^UjY]A:F5I#=K-0*]ASme;:E="=<6h5%l\W
h9g!Y9/\dbEt]Arj[eHQ`^9Q-?4kT=#9Sa%U8b=oQO?tEU=YqgI]A@k-&Fc=Nu"l65.rto%l.l
sqXW9o-]Ac:Oe"p9X^_K5]A4)HQ2D:FJ-qbUR<M8f`X;i:\=SqAS:4H2M.A1de/JfNNG(EI=/f
;^9>"]A)*"#+Qn/&]A8qhgGr-;]A,\]AQ<Y#eg(@,APlh+eD73)/o#'MV_Z5A44Ta]AbWLYQ,+*[c
i4=U")C_0E:5chON"89%"VSa2fO`2\IM+KTS_rc^['S7:!K1j`$3rpu4=,7/_9m"#&A?Ap]AX
O>U1F]A2BdO&B(q*nl=$%X3Q\4@3'1"h/-168f"0FAeqp5D[5t<G)W!=#3b8?VC+jog4$$>)o
;gXhnt9LZSVS[(j(<mLU;p%j4I.pfKOA7s6LVA&arj^*co$V\Ms7(qWa>pdZ##6$\mje.ea]A
`&eV&WakUX,CIfWQgI.hh;]AYOjMEMKX;OLSo0\9-mOK$Y$JKL9H#RdgiN]A:-%JeM@Rf3e!!r
T0U4I3YC%qi[J3-"l3DJn?Pofr6$l'brTh71?Y.pp2lsr+aQ1\dru_)$tNdZh`niUbXOk0L?
.Qo.^1,>h\)Yg7[DZF6'JGn"AD^ATh+Tq-!MHO;F<s3)k7"ou+oNs*F6c,AqZR\*oe?GscJ)
rB98o;p_)Y^naHcChM]A9@.L='D2M2F3C-[DN*_R,#'h@-H]A8Q.Q0e,KK_%),o$SKkG;^S5\@
10f.fn_:4L,,\'7d3VMs?_Y`N7UNFE"Ofs8KE&)-B$9@Ep:*PBSX9Q2FXF):uhp84o-;@$Z2
&JX&)mp3K;0jol^Mc=QSQ'Nqg-7We4'cKJJpJd*qi3#l]AqdC!^lbqJiV4gngf$[U`00IrB*)
"MIMGnOV]Aqi#NIIoA"oinB.m7D2O!Phf!;_fgr^BXG[!*]AXV7i."MA'/K[f#_ifM/[)O!pd-
Z#[@dUnOUNE*DXfI([L4;\k/4Yf:aX.6gdkgur$i2-Vf$r@mjdJ:J/us48+6P#Ignj@LcVnC
@3iV#<mFh3jOJ(UdJp32s*l,mF>&>^pj`6rq,#DE;-:M$VY;[DN1Ih8kXOm>ih(AFi#5jQCN
'`c_)8_,fAIQPK55E8Y/*!*#1qeJ%DK_\_e;P@M<Lf$W]A8;ap%t1W<hWIYT<BOig9!7OJ>h#
\ORD[SF4K5&/T0^kQ0&QaFQ4j_-<q4e$.jiG_DU;@E)!hh>SF7Hf7[qV2;t!C:&)J*\?7qFn
;mDdqd-G";i^#]A8l&]AOr_E~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="61" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="url"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="date"/>
<Widget widgetName="right"/>
<Widget widgetName="BACK"/>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="108"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="4"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_khfx_khmxx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_Agscfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="glzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="56ab203f-325c-4cf0-89b5-013f9f94d8d9"/>
</TemplateIdAttMark>
</Form>
