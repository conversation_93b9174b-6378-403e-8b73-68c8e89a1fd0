<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT DISTINCT ZBMC FROM (
		SELECT 
		     A.AREA_ID,
			A.ZBID,A.ZBBM,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBMC,
			A.C<PERSON>,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群')
		ORDER BY A.XH
) M
WHERE ZBMC IS NOT NULL]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		 select
		 branch_no,branch_name,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2," and up_branch_no='"+pany+"' and branch_no!='9999'","and tree_level in ('3') and branch_no='"+pany+"'"))} 	
		 and branch_no not in ('2097')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="company"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)  
		AND A.YEAR=substr('${date}',1,4) 
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')    
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	   SELECT
	   CASE WHEN NVL(DNZ,0)=0 THEN DRZ ELSE DNZ END ZBZ,
	   A.ZBID
	  /** CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN ( NVL(DNZ,0) = 0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ**/ 
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   WHERE -- TREE_LEVEL='${level}' AND 
	   BRANCH_NO='${company}'
)  
SELECT * FROM (
	SELECT 
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	TAB.ZBMC 指标名称,
	NVL(DATA.ZBZ,0) 指标值
	/**DATA.DNZ 当年值,
	DATA.TQZZ 较同期增长**/
	FROM TAB
	INNER JOIN DATA ON DATA.ZBID=TAB.ZBID 
) M
ORDER BY CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="单指标查询" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="ZB"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level,BRANCH_NAME
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' and branch_no='9999'","and tree_level in ('3') and branch_no='"+pany+"'"))} 		 
) 
, DATA AS (
	   SELECT
	   DS,branch_no,A.ZBID,TREE_LEVEL,A.branch_name,dnz drz
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
SELECT * FROM (
select 
REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
TAB.ZBNM,
GS.branch_name ,
DATA.TREE_LEVEL ,
GS.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
TAB.ZBID 指标ID,
NVL(DATA.drz,0) 指标值
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
INNER join GS on DATA.branch_no = gs.branch_no and data.tree_level=gs.tree_level
where TAB.ZBNM='${ZB}'
) M
ORDER BY m.branch_no desc,CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END 

 




 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="11ab8167-9496-4e2f-ad1d-f6ca2475e26d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="company_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="下拉1" columnName="BRANCH_NAME"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[BRANCH_NO]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$company]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"^7dTO1oVWfklbcK%cPSH'QNHkQn1M&K#Yd1p;@O%?%4`iqX`VR/;U/k<\Jp-gEP$4NQ;Q
5bHBIP,L=JDN6Or$ALje29po:<5==l8+L*E\6-fKB&a^\?RQIc'nQ5Kl^?iH;2D<s&GQF[pn
)4q.KC#;1I,X\es`I/gGl!3dt<ir%Q!=PO`PNo1R1qg+tBhRGZpouY(JbfW_m<i)1Q:KD.=f
2V0_jF$77/mU!;ZJKfCm6^oBgY5L_J%U:2o]Af/#Y'Kj6jJ-'8qg5LV$uP5icn[[[F-0MR;:R
sp3-J9_!b\_-W[_ruX(6EdLE$G!4]A$eB@UW<AXSn#"?=aZ*=0eter8Ui1EH*K'a2O_f0\&;S
n6t)_!5Kfh-A/b7j/FcoHN9^Ee8PPF*Vl?7r!i]AD7eT&&VbI[LB;h-kbAM;#e6`YT<.epJ3#
HH!@,&F`2;gsrnr9<AWgV]ACBdNsq8UFiY!8e%^>`'(t"oiMe71-0sSd0r4_N(Olr?oL&)sDc
9eJFE(n_KL(]ASq+`*S#BoT\Ta\;XoNY6C9df)MtmNT^SdA.,3XJA9&n5Dp<<&)Qcq4RgqL.5
>W4!eV'8B`;DsV3q_#Hm%L<rJ\p703BnA3>cG2C<S="8mLY^hK[e:-#r/ETDI+%A!TM)8puN
m/Xj:bL3aNrj6rGo&ET-$u%aud;D%<k%aQpYXTj<<uh@CAPQ97.A='#I+&scGN<uNAi7*L%m
U%.R@%E!7e`KH,M>]A]A3L8I5L=7YUgKD:<QsQ!mTYXit7hq9^D,&$@%0EOu*_;3`8Zam!#kL<
RgeltfeTco^90!jWV;&"2X"A7tegroHJpqkh8#Oh:Rq"UN.>S;>"+Q;@.0g_T!>HICW8R(bs
kM.<^2PZ!`DdH?9Tp&n_g).Wo!IX@'rWft.8XZ'Fn.&dq/'!!<ahKD_lN2fQ^f+HDnigj_T8
9Q+3P<(XI4]A0AI-`m"f8,2YW93?#\ZH$;J;NTiWqTKlfoZ:9fK=GmD?I*f2A?8_EZqn4Te&u
PqoU#I"okPq@`cmpQh:l0g;,aqH+j,7%8u.c$H/([Rlo/@Uq#[VdSsQh2$C#'Xb,[fb@nUjp
jmHqZ^##2i4'E2L5A!sh$q7MD3E8.dO8-#NM&lQW3M.S-_6nu=BjI<H93R-@I(Hu]AIcV@;Cl
@2!:Y?EKg&edNl#ts=RL4ddENc-eDskJarogJ:lO-^!AO]AO[4V_F@P%6%D=X3*ed2ql]A5c8#
Qeeu"fd`b"6.NCl[:.aYS_+)[G!C"M^Q#b(5/k1sWlB^0,Hr`1:74beUlY`'3O58\tQnpZ$o
Iu_Z]As>25CRtQKNR/&hkm>WW9sYc\Nl;BrPd?"&fg1!h.W`;ZfZ?0!@ec1:r)d)O#DsFF5$4
stET&Ot=O+#CCZOMh>['<IQ[\PPR+]A;L)*;Jeo9'_%C=+YCpIsDe]AdH>561F']A;HVkg1`:7G
-p,lHjBUD1K?Q\EX=q5.q0)W&JZE)l55cVr;p)lHMqOZ//:^P1cYe;W8`R!:Ph!lj#Gl!-p:
9L4;6l/=%@Z6D.LR/EehsV`mH@epIMs[kY/S#oUpT\!JZ!1riHW@9_/;Va8SuYV'bm781s1_
1(Rc#^YfWN9iQX,eUpJG3[Cu+ig6P!@T8RWrR"+'6.:]AEuNqpdmgZ/Ok;eP0/\ahPe,+U&74
#ifWXi]AQ-1^G0/*+`./r.(5i$!h?81B0_PMA3mS"44=>>[>55V;)dVHC_62=^]A)WW."5ggRG
"/B0HaHnS6%r</dXkOR;,H:Q+$O2U@q04o\pUj4E#q5Dl0^c1-dN"'b1tN<B-m$0Mu!ZMWtn
^i:s'A4uYk>p_XAG5^OYXV<CEhFpoiSZ,#`88Ur5B/Z8s(dI^[N9EbW\+&HaA8)e/B]A7-o_9
%)&q%9K)cBkT,WXK80+PNoiai&=ZnT+Z8ffBsq]A$e(-;%g\uFt`ZZ'uu-Ukn$M>Jfmss_m27
8.st6uDoMqrcY73Y$R$:J]AUC7Ok@!NN5I$-Cn$T(T7eluFDP4kKe;6-g2pLIqgth!kjAFC@H
#Vo7GBOp/=tthd,KEg7J357P'To*oRqW0.c+ZA,GHc5EO$MLB[)Xu&=JO`h-a=GGh;hNe:8a
COLSIGO9kO#u7CPg@I(_/A_mRTg<Lgte`Y#JT^<CD&L]A5pSbQC!41D3/)&`^F=UGXKJ5FTe5
d,d`HNQMq\UHG2hJ[pZFfA=H,"<bX''1YM9f9YV&.hLA=a$Q:W(ZW&8;AILY45N@DXn;44$'
%9RP((!:&Q'=X0D<YZk4mlL>rD4l4eF+$+92-6R'NU.N3ZOGC(*D2\uXP\kZihfXNVl4pAJZ
CFIT5W1j>etW;GCaj$#<T-K;ZlKY/tso:!Md$s1J!;;tb%KN?6HF4/Ib:BOr1NWAU?)bfV2K
0DI\1+:abAE?bc7qfb<\Ta[t6#+V#7D'KVaLW(=SsuE"2NG4INie4u4ik*=XA=.j7+ceJ2K3
Z+]AaJ^VJ^'(T2A>.#EMT*\oU+24iE4kJS>A.rQGLAP6,/,q9H%]A7btu2UIMZUK@Q]AA`s15?&
_<6;o3!QOk9'd(-\m0+TE>_:bhW3iF$?e95qBI2^HP#PX<`O3tiE)Y(fD?4DE=VN[=kb=%Nt
]A7GIpV0u3=\Fm7Y'bY)%s,30jFisUF(90n1G!lZ]A:mJ/*u+ZruHFs"\4RR*Cc%tQIi=L,IAW
")Lm4**7Yu+jitqAEUCi(a<K/p(u7gt=7Xh-obpRhFaiu77`lTplIk?`5`%A>M)^1!UttJN_
B%@7/[>@_hB?/d&BBHj3t;Qt3mG;d%kfGZAHecep7S]AY24_F3Bd7FnfJ+5"q9P'7rOutObhd
K*A/H?;O`uEA;)`c]AAldGsZ;-eWUg[_bYi5pOU<:,B32%f%+)-:hnMe"Y!i\_4<4PIWM"*3K
dMVKYmO,idHBqX1QrOqJm#,9)Mid;4HT?&K#3M#oq3MeR=Sg:o1Y%L$#R?6CjNY5Z)1Lc-"5
CB+dLU;p$+\75E:b,7h$KVr_SALLA?XAOE_WI*J:*L)]A;["R5YTuJSgN3@dcJ/kZT/R$S\1/
3L8)iDIkN=aJIp6?c,_#9g1IkWp&<'Y.>QP&dBNZA]A)6&pj%t7,&16MYoL4&i?R^b>=F9Dd"
!NV.-Yfj7Wt"1i$I5P<Z/8klUKI8WYcG_'O"ch9(lOEL'PO!6-=kuao^sQB&U/oY[4]Aq45O"
3m1,1SV99aY]ARe\D1o:8QXKs4>ql>5c3!`teNGSQHf+=kN)_+r8%ia/]An2d!fZm<n]AY`Vt@5
'JL?S8QMk:S%#>.MK/,t7`lLqO?%_0$f*,&oZNPCFqDQLYJ9*Jm*bfd5nNADbt.X/?-($m$?
m@Ye/r)fDQ30^\X+;n$@EYKLVT[r#[W->\)Hc88?EC=oC9noaaL/rgVSCh@gD+m>rEq\0ZLX
0\L#$b9LW.5O@ATY<7]A7Y<6mlO3ZHhJ;DM(TP`-\'=`>HdQ_Mte6<,mXV*2d(M%bm&OdaJgE
ODQSM*GW2*c:A[LDJLabktT83;ntX7:R%51Js0U*DOs!\-ggZb6IeJ$et*=F/"u-*pq'd:E(
=.J^Q3nQI\H2(755c$?`+&H+qt3)*Bu@\9!.od(Ac0;s%ek>Am#'7[_JpZc11p;:qbKN]AiIV
MDdUp!eObjb%GV"b1HOpIns:6#N)/CI*$aWn$?dLPift\^^hkd>ih0LL.Ful0Ng%#I3unJro
i@'r>T[dXR1_<46Wt(X9(s6bX7sAgWHKljkO/!5m8Y!F2PaB_j0o'"o;:O(EA#D\;_;+7NK9
E665"]AT>ma@OddDF4"Ns6fS<'5_AH)W<$^j4%GQqO19OTi3U\eJ!=^r7#E'98n\UK!*$G-Wf
=;'QBJc]AnYIJDO6D^,X97OkP17W)=$f`WT'YTNIC>\sE[oD]A95V@HH8n2!$l,h<?<7H`^S,V
1K,Yr-18Q:RUI\pJ#7-?9PVls^f#$9cI@`e4H$+P.R(\napT3]An->>lH/7!&#8OgpW>1<=d8
-i)D\rjAQhJ4'\BLNVJU"R!kl9>M.(q@p,]ANR+:is38!CORLr1bE]AkSWY$9k5?BMN'_Ht\U4
mT=k9+L/Y46d'(Qsu<*Ql9X1ZgQGWGG$f$tng4[:EugT&V`KV8G:94A&p%3#EC8oLr463;>Y
b0qA8[V!739_MT!l*k.$c\]AkNZK%r4-Hd9i2<H]A'T`h=q4.c#REG6AGcS1+dckOllr'`1=PP
Roce/*U'J^;=-H\QAM5M-$*aegg5"F'8/qM0GT-IPLB&>\%Z7Ekh?Z.ja.:;0Y9s)p01XkHl
XDQj0Cc2@rO3\mi$G6D,&@q?M<XZEnWmpsknOkjXEQ6Zd.b()FM/0%pXe[$1<5%5C!%8Ai14
Z0eG*)+M"rV;j7X4stO'k"6/FX_g[<:hO[CZ#iUTFVoJk:gP!..sT$XdYffumuXSECBS7(*u
'BX1EbQif:,7q_n9-0cEBX,e$p<)%Y@hMk#PcOaEW01>/f*:i?d=O$h8/59ui7&2[gC8]A,rQ
*/aT6nCar[u2l5p%k&1!P6N"X2s-U&=^TH]A`mpaJ885Qcf29N"U-4_+I?CjL5RjP6H=J5s^*
.M8^(tLIjFjP6Mc@#3KXXs,@-D4aR9jUj7A#FN^"?VI!;QL"T&U*9';oXj'D9KQshO5"6c)R
Y!auKL>Q1O>6%"#FdmtJ)o7e=8;7+#=)^pf)sHk<EZ9V%ibOU><V;"'thK]A[$Kgq1+q+kApB
HdICacHR&,:*BItD6EXn.DA=7SZ:0$0ZPQ\CW2WS/:;q4Y;NL0&4?ruZ+_NjL1[pLb6Sf&Dd
*f0k3foJ&37sEC5T;^gRO'n'MY4K(edU=kbi@eoDXZYNZ%'>_Z?(O@".Y;PNDS,^5BK+R^0<
3<YTl"2.-&:7kYK)),>jrEQe5NhbhWD#d^V.5WW1eajgR?j1!<3[D&6]A$YN_.[6BNY1fYMI#
f\6=-liCe?:V)-P<52pb(nN,/*B&MLj,aPjLsT4_bS*=;:EiX3q?GZL#%C0p8>'e$!d@nLiZ
mIeBmP)(sD&h[+09lkA3B?3*PoMgW/-/\?,]AE^[5[V>oGSakil<L?/0nS8GpT^<M[$/OclDp
fnDd\IuFCLIrG`4C;=ffPTm>#pY.BA<@Po,BhhH00;bs#T62h[bc8O?+Po$?V#(()=5.jOd:
g-Ec$ZYA,5=Kp6TtV8D%QHm/+k8q@X(V6k2#^=rWa",HOoLcFSY/JQg&@e'T]A,J&&.eD0Jhb
A#4_O[LUH^!#[4pWO'"Jfn]A2_?M96dGWi`g?bGJlmk@+oP?0,5A&0+a2ZR0kN.pflb0894gd
XE0/>H*uUrF@_ogQ$*7^p[IUg+1?<kt.Brb0cioaU<7?iMqlZ#PCi9Mu7/RaapQ7IH(e/KqN
LkS`^oI,D&tde3"?b2-:Jp6rf!<a'i'.4SI<+fm`bVemgZSq+-FMKt\&o84uT6:JQN.#26=h
`^SXl_sE5\Y*NWn<+`M*Z%T.K+A#62l`c70IW:dq#X!o'fSa`kHtWo<2*:(^`1RjdRUhV'\6
*'S0&k\X^3daOqV8-.$%_&R6Lj$pHca.hC2E)*3SaK8mb#n$:;cPirFmif9Knt^/6bQO%V9X
n*G:1No;.J8o[RJG&0iC8?sE+FQ1*dGMgW8q$O/o>e5'AYAWs"ZWu\sf^97#IUq%4`(.tO;a
h81Bi=Cq7K@!/N*983J0f:I=%C>YSEEM*[)KloD[no%@`!n_f7QT$q#]Au-'nThOj1'(X3!-r
64^dNN1ppYGhq"^4$.[>]A;&0G_%o#,Y'QQ=`In+Qb6*ppS/DjQm11d&7)=Zc?[8EHX[U+U$6
\ucBcU:@Q;4mVO&H2BlqC;p;X44j5ef7IX4A-*HS1_qq.ZI1r^RBJkgl+'Y^^2W4gA%RI8Sl
B;<Xj8.O.O(c/''ZHt]A(e?>KH6gq\-I^'^XA#gh'13haHUai$@b!kOXP8p*SlK5'J>;Dkn?C
1=ImmHFmCs7Zuf+lAsN)Wk;hHWJ,9M"i]A\V7Pr6@MYjR>eDpiGF86!\?\YCs@+K";R6>3%s^
BN*GVN,ebGS&L*ePiM'd:5V1:2JHCb`>,cHsK(=XWZq>k4%26Pm-Hk;&g>RfFUG&M(),H\&G
9RG4oah4/]AU1WauR!N3jXe]ARU#=@8s;9hNn%O3\E*[hs?BtQ=kE*^ajdOdQF.?/hZ]A"M2,nX
s(H*q>Arlsng26EnaWi.HX=s;O%=Mfb*fLq"8I<j0Jt*+H#;gm;IP";b?()#RrjTS^Y"[`'j
LCFl)sK9fApW53JO)AlZCMj/Y0r@9gIajL?Pn)_$@I-;*!H;Hg&sj#Xu02NUEEUJ6Tl%^4`U
jIOkr-nnSpa\f%Y,3iu#ZM3o[MV7o$^ob+@$[I)FF[(m"dN)e;D.$HsdY$UT&kNJrqgg*8(D
;IO%b=;81Tl1@`s/]A"*s1P[VG)p_-9nf/Pg]A\jNC#_@"5aNOL<F)9?7f=c=jIJ:s;s'RUJaU
F8,Vm:CKJZ<q,oASU[*.L#hmM8F#MoJ)BUQLfjIDSfX11nP\H#Ol2o42^#&X#`PiR3U$aj2d
&9mZ4UcVfP+8oX:b4]A$_L!UVleJk,dn?%([p%']Am$7SpuF)75AKtQlgAiF%INke"<;?#WJV"
hj2q3h-\<Qk@7L(9ZG$Z,d)^YV@WYogL3B2%#(dK1mQghPkTZm3Ed<\T,C8W8YVLe]AG#;*<5
(<U)r2<?ao5\C097s1GsNGY1uo@!EgR4P>u2P&`IU/5+K,dC-7R-RE>%)D7"t3`/%mf=1Y.l
=fK1l:]ACnNm[iS2;JUeH='U=`Rh28_-(*K1SSKsK)Se"]AA&'Ddk)G)%NG3u;.J6sh>4Q\s*M
oDBF+Kga\%6862WSNW8@n]ADQC-2Dod=.$tRqedN>5o?;iJ-H6hg&pAK]A8^6:i)mcZMY3p]A.E
IV2lu0-Zq-_fkP<+&3:4/VrnSNo?<TN+--nJ%B^"Sk"[R,DkdE)4Nd:rCeY[")'of5H]A2<<K
-+_+R,OMWl?uCe^-,TYWEM?_BFD+QkX$co%+2q/,Ub:Fqc#l[s_fqCW;*V*!u2?@+^PgC**e
'4@l[s7f.3e?B/-h9Vae230l2tFb\YpFquhf>'XQGfbl`gcaI)SFH"Hq"6MkhT?^3s#i6@L`
/Qnf?U+jULop;uXC,T9)H\Oa:CFa.>nf#%"lf<)\kq+)@nbR_pd,q@0*_Ln5?kI\[M&)Y[De
[=!1n%0P^@pRS!M8'gfitr!5Riq;\/YGojV;/JD[gf`XDk"UF*%`C9j;0-EG7'ZC7jAX3#+^
R]AIj:a_J,Mr;W+27i=m*^GPV=q'=AA24(]AdJ%n2:FgX(b9@2OH-2*7$)/;tWW^/M@#1]A@b<.
ep^%B;]AOW!:eF)H;?(:_!D2!EWENZm:_.`9pbJ*B=7B/j41Q+9)K~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="104"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="104"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="1"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,2224585,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBNM"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mC[X>P?ShUDF[Yk;=Pdr?i`]ARJq.qH'bmJ\kpc`QRZu/$((cb26BDs^C1r_FC5@Ji!X'YXW<
"H!:dPP@KS96BF$-(>r-nCch"fcoHO"3]A_dm>lH`t-VZ^t0'TBYTREpPj"CX]A.T**9L")Jn-
G#&!nj\HDW&lAFAZ9q*@&Y?\_JjHDFNn*OV,BoP.iI<k"\7;e.GM[pl"]Aauc9C$u5#k>+lYg
:)9uhSkCQjertc*^'t;&3mGc"OCC<SqQ9uS+g35^<#\c!_QQs:*^%n42HqOSb2ZV2&g?4U[n
;G4R?:M]A&O[Hg?+=/T<*/%\1O2Ygg%g7h=UH6#9PR4Bh]Ai@GJ17g*<20d\[gk\X^eDMD16lI
WD$6Kj2R_M_.nn?s7qWML_C79nn^0(1cYL?HdMP]AJ%h<W\Kb"XX[CW"kg)J'Frh[`@Vkl1eH
Zh3d"a3f.'JEb*c/%->1Pt=o@\Pk42S;t:VMae'r#RFB:8br4pFYrP:a\#WW0G*#X>Xil5p1
X8(2X;\,FXl_G2m_QA2Vrm__tQGucW'.,)6)$[!iplSFi<hYb]ALqE(s1F_[\mdhath_2E3s2
BNZ^q#,K3cumpr$/`Dms1$^D8*h!Qs4;)Y^M&Bi[oNsKb;O30>+jF;rV1L(G.ImV1urnI!gU
L..IQQ@jLh5Eia$BhUY56B*</Q4?T``e9I_VlmhBp^r,c04F28O&%.cu^$Ynn*I0T^rF'79I
i=c'#m_)]A&D^pVgHFK+4rq,<Gq<4Elfpt39<V5(9e!;Jdcgh@brb'?i*,lU)jFb0*Cf`9^^T
J@KBA/CGpghWiAfjh7@0;"1_jeh&E?/F.h,1[Wi#'XO41:.jfpu\e\Jrr^#!_\-f.7:;6i&1
l[QT[Po_s-TJFU?C=3lEm[Tq6QXoo6tB/b"^ZCt<<4D^IHXtW;CAm:S5_h)tAH[kqafNr"/W
2a98o\jq!s'u_#khUSPS^_lJ-[Q1-bMLaA<T'El*Y8P%r4Gq,,Hp![6m%HD3\b>S.]AaGF.hD
NJ`k`i17?R.L.p"V=#80*snE,!uR,Q/N-aU5@nju8P*d_\^Z&@0J;uX(H`fF`hWkFpNpWID.
&Wt6A:RP=N3;;]A'Qn*/E.rnD*eZ>0k1V_aiAigc7%ACZY)RM3GA\'=4gk8AJf-b^*8br*#8Y
X?8:.T(8%?N\pQZk.O57ds"="Xs@mi03:Cf+*(+'S[*b81d"L;Ft:#tSD93gX&4.A0)R.OE$
2k#^]A[XWI?pFOWm7o_dXHm"\fui%%,[n%''`1ep?UGEDs<VV9D4b_3=J)48U60#;%=\nD^C%
ISn1"/kpD5/ot,RpYg="QRZ"JYg[KU\=3=5OSFB=ZbrUG-rl9AuZia=Q2D,_@FGC^WLB;:67
nl)>0nhL8bK*doWD:XG<DH`l^(c8A3245/&"l_\V!HAgNJo*c8dcI'-K6(t3kbcf>hS&=h%i
_8r7NB3Xg#f5DP!p?Rl";-R\G'dOT!hPFQbD6Q:N[#H[LZ".Ub#n9_-J&kK0coPRmCWE2"W"
cmlfB2u[i`'ph(Yd/(S1euJP\nZ%s$(tlh=rb]Ae#1V9bC%[4^]AB:$R4So#aNtC%iWtunlV,n
g29s^V1=";eM@RV9OOl/]AmfOpH'FL*.QVM&qEFgG!N@VOp[rc5+7`84Vg^Jg1r:6>]AkaN%,\
(!^0SA8!Sa]Al4B)P:<(*O:[C,cOW#mZ%+mRYA'3<[uPM3%5e^0F;BHFUFX3^ZT5f=#)3`:>a
?0&5)dP<_n2?nm-,$W*6@9V*083P4=k9[:#@W#Fq4]A.?jZcGt/D?s7[U.a@+^8X0.E:XgbWT
IXUZ=bp;'-MNA9^9G=1Gb_T1MNO'9uZgVP^!MGSX\8;-9]A_E[H+,b=ZYEZQ"$273#XD",@,0
L"<d+*gar7VNeegT7_?197.,s-1mXI9*#77<:(ja#bq9gUjoA5RBr%IBWVkK;otGai^QcNd1
bLLg`FB6.ktd71=EYn?i;^_JRMA<JI0-Yc'*V>:"hoOrId!8^WbB@7\-ES/2P@o;(CcB#s(R
49h;gPdqk`Aa]AECf)QbGe@O,Qs3aRZ$<E^--j%&`aIiN@GN>k).h_$/?Gkj?!`!U]AD%LhnaG
g>Q.SfO$Gb"F&mr12ir6pSiR<d_Bm9&[XJpX-n`3d+\qfV$;A&K3+W]As43>LJopoYEJ6%Q>%
9E%cW;gEt%^$A9tRH_8SnVmBd&"HG@.BUH#<D+sBPKAX7pF&)c=;)#AOPKaV#44HF/N!3^>e
W1iKT$;]A"V[<jDq&'+f=>+cHHOh`krZH\:W]AZ]AFZ3pE]Aup*NA?fUKBb%F`'\jcVcLh$Ha#nL
]AISjo^JSfgqGmS0Tc&J0+!#e/0d@m+ehEVUH:nQ-;]AVe**J&Ug$cU"VJccWp4pGIDVES=u"E
.s'PL?l"G_KLj56'"M=ob;nI:Un.E?:t3cp[[E=UH<\JBX=Zg_?cX-lN8I4.H]A`b]AS(tV]A&H
J*DeTQWPB9q-2HD\3b"i'-DKM_r9q>k9o01%W_GSXSB$8gk_4QI)ZY1&eXK8K:`c-,`n5>lQ
L>A(Ta=M@CmR=/;MrQ6j+ZtGgD@qY8_25B1g?f*s>]A#^G[_jI6!67G5$?>K1aO?Z<N9gpW*/
u:$c/!+KER;C#GfHYFX8oPO-tM-[+c1P#Odap>K$oSS:r.kF[Ssk3V3`k_IO;BSdg=b>)JJm
-/fd>Phj]A[P!39[K%5gWNiCS`H9elKLb-Q"]A<DoaE&2X&A13.K"'dnhF+<5^4^#+$BbW&bIq
[6ETZ(nkZ@</=J<bG._T*`#Fs,Q#)59lJ)DmWkE%u$85/!L^cQm@cd(5gci,9tI2b<.0+8P?
`h'9"'@9sm95$<*e"8j$7Nk[DYnJ0Y?)L3,goS)dRCSl\+@R<#9=<>@8f<0^nLij]Ai>#R$.o
ZS%f]A)s)32%OZfmY8T-rrq"eI#=lDm%b9-n^tc`(euQ)@HffN`,.F.6_E%cb$,NT]Ah^uP)n]A
JG6rg8VROt<)5gl%[\$JBRk)E(P^$g#fdfsj)YpRm%%Soru65uY!CE5<@u+FJWQ,eqJi<k+E
\$C)37aYmni=p;%"ksXokrJV2Na4*E2U-4Ya1$KFJju-D%)1S>2#b>aXJ<ZHA]AuP-@J]AT%r\
DHJ8YHYod<<SWRFq'p$fFgX@klq8i\NFcdk&"6#?;me4F:ni)!@$R5Z6Wp3M%m=DXB5uEPdg
>U`37B%YJ'kYUSjdW)8,8;:6hL?6Bsi$Gt+'=gb`I,@C'fD!b(T$aR%^]A9Fm#>O:j*oAtiM!
O98aKCq[;84a;RgRq68Q!J'0qgMo)[Bs?.%KaR/6L#.0<:bEp/B@""b!XGio6ifds=E>SSWa
g^4KRQDA3%HRM_ttUrk*NaR&%d./9SKR6(gLS@efJ?96u(DG:g!!_mQS2fWU0)^8f(<KCP#[
9O$01TnG2\=$+"%nYS.,/R;JIHbTF!T4P[7F=87UVi*YHLL^7K#5.N#DCS^rp?:!WG2hd7=J
WCU<h5_AQb4t<ectIS/1&4g.\K)H.."HCm#VaIN6gA<#NQ]A@5VEn)KKosC)>mF-;M&V#GUj5
)E;n-k#P^!D\Bl'4dBaOn/UY*\P'9>J!]Ah#FpBj\J96._\M^!m_`7$Mn!2Ih<b]A6E^_@8b]Ag
nhc^2,n8;jNeu(":(Wu;amuUcRW!s^UX\q6HbK)Y]AQaH\iQ>lZE>Y]AA:r!>ndiLM#SR\A-"R
=5)+%Z4`AZ:VHDt[=Q(6GTso]Ab-`F$$qLWCmlX+ln+@[c.<h1gRWT>&,sSfRhBa:#Qm:#-cF
ICSd';Fi(lrE*6g?6*G(`Yq/JQ_O0VVph-c;PC&4d%nL'j9HZAm&sW0..3;oXd_W]Ah-mtqDl
V+,+d[-hfV+G9&-sNER-esfnnAni!3u?[$olFBLUih,+3W&WV!>3I$'QCqLM-Fc&\c%(OX."
+-im/m:%(PkM#I.!H3?.(JpV5k(K=Nlp[pOf1]AI^`R#Ef%p/&-9]Ac6:9nP87?3jIFtPpf21-
):i:$FFI_RST'RHcqI$SZDX@%A2:'J*NpFZc"(LXb+&%;X^'#P*M$\0)]AZ*P3F^"]A0t]A]A;.8
\D?AugAAW3\M<*#?s3-:#pJHJQ=jeNr_5ac@0]A7YBj<+dr7k$]A[qj(p^!85`hm<aX;c_hl;H
.h#$6;ffbf5=IFq_hlZ0PUaiPKo%d.[;`[@=8pgZe/61uZn#*3*TViR,Ec8I]A`;K[k2O\;Tm
j-#T_7Yu$1SF8JrA.A+?Z?<&M9Vm56j[qqI$N($Dld\R-sX&0S`OBup%,i!B[eQ_;@5T*e37
n.^X6a@M>CuUN@d*$^!5p:0FX.mo:,qDa*`ka>r>(\ZDDr@!=%+`>8M/cA-!G&UV59dM[@1j
&%*)Jb<6>df,+ogFk;!R!em_iWccS--=G+93I0sZS6'b5_g:ZCO:46W;IQQuGpePsr-PaAB@
Y>-Nq\l:*;<mnYHpZH5U832%o?E>LLPKZp:.kJW@Qp/>CEp%]A>sktMMstZZ",7LI\<6FP%Pf
UCOfjK)1+S:jVJOr<&<pFN)AA9C?o'78Z-dMdAnC<jClB:(4E:'jpf*=Nd:h&IJnJeF*P\H(
T#,60Mdq->6=fM@e-),-/qm\PfaAp-PmZ=l8AOFP51=_a/W;nprh%3I`&b\EO&o1.tSi:SMf
cA?AF3o)C._YFcs[3*?C]ARTrdC]A=Pd'^">p>F>#!C):G'n7O95?">Q-#t%Vod\m&23BjP!h?
bsjO23(WPD[5c7N$P2T2Z2dQE9OYn#gGK0q#O8AnGVF!q("O41%5E0KjAM+4'G/RoE1!UdV8
k^[.SKXIm9a9!D6tS0l/;YZ/Zs;)XK7BUQfllmG;mon_`qLp0/3Tm,S6hG!(K-Cn$n>a;b^c
u/g.IE(AMi[DYnDg?p71KY.k#-!%HX*ThTUUc":Uo9H&spXo#e2fC%m.b49Fp'Yau;mM*O:I
,=U+jgL2d<M)Z>q2+kf!u(R<KXQ)JH8;*1/'`BT2"m[BHgRBJDqZ4i">^LKg[a\KnD`&`%.!
g-(!!IcbsTa_,d<#c>l<b'(0!_OmheaAXD%4%A8!e4TP3lgX!'I/IYb-ZKUI(a4!hGlSPUgB
\L*Q8i\9C#0"K_bQ%6Ts@8C'X94\36&70``FHP!8E&/fjeb*fWFG4e>=8'UG0*.m>A9bRQ/*
8tQi1"]A89j'L1lW+^:hI&%BSX\C*la.Z4m"3i:D8)0'*Hm0NO*UqIq)SGYC0$*PJ8:L`/QLu
V?GJl0Q/jl5!0RbDolrERV*X".6EBZocl[[mc0?F@G_"3hS(cK'Wdq$%7X(Y.<jZ4>GW`+3k
S4N$CsQUA.>i:Zj#A#4TTBg]A$TVbH7BFsd4auks.TWp;pMn,[Kl_EOTjJZ*\T;=Ng9)k4!02
nggRFiK3H:m8CW40.*EOA;'b@3O-.a2e]A#V;*5FjVrV='LG#&j[VRE/\':fZUm.K39\=0%Jd
O$OUV`IH<G2gVlLWQ=49ZlaI!.uOb@M@u]AsGYrj:$U>X=/P'JB?4:1M0(=o>b8Lh56''S-e^
8(:2FCKfRf8d"[D^\f5nC<().K&CZBCA3'_9r[3s^qIY]A.L@i6uO!E)5_bAW_K_3VaEJ"[t+
,AEGct#:osn%@FrdhQKMebdo_tq9Y6\BQpYe,@u+HMS7Gs<#S^>DMRkTmK'L$e;C*m0PDGC6
t-nEg4=6tk')-Y<k+3#]A9HK41MhoBWW-"I4"G'00F/`09dq<>H8huF4[I;m5W"_L/)A,Sb]Am
(#a3VbKneMegqW*VL80t5k&B)LP42=^<$H\knA`%:QMPY>V$m^X/jUbNRA>&83ghp-\@@0>J
Kj&1bmiHDH`pURV=pd@hn^5SqM,I+h<Dr;1K*5A;2KDp'@EQ>u:?:%M<099XB_oPj?&;$VrG
ZHV3ugnX8fN$D<qJL0dR4k(>>tqcru-LL7Q1+K=6@a1]Ai>:@!V`86iOsK4GRE#V(Pq2_hC#Q
hp3-FB;HP:JMcMqV'uce?4uM[30G`W;;JVMlZ&qllm9U%i!JYr,SS;9rZnNf0F<Dg."gW<Y_
qdtqUj:R7!6MU?I-1U9qn(hO.LL1MYt\<,:^a*L)+2W059VM[kC[b1&lqXRlUVCqC@/Zq8P&
7LJ:kCIQkoKqDnP.,Ynf"G3*NEn'i-bAFrI2(8Ys\A;,p`g-aFCOp]A/lSOjkqG4I9Ms;1B8H
nt@HH(Vc7Gh5H@-"W3-a)Ib<%B5D5eDW'F$Xet_J\u4(%705u-4ZCq"U[lO6iuaoCeXEoNGe
p+HI73)OG/$XFPQm`)A6Z9s'jVW7GCRrJiju,jO:9(UeNp$[1KU)(+$!?lIb%Y&-g<Lr7/I!
V*)06j[:cf!p+kq^4e>W1lG3$R1=>ns?JVY`%9SHY\osU#'mVnDT_`/$XV)E+=nS-!N<&fgE
;XI0d?ue'Z>Y1kVl>M2GlL@%:i[mnG>9dY@K'P4K9[^qI/0q^F!>sJs2/_`o^T,[C;CA=6))
jFW,t_Wa]A'Ad(`oT'0536UQ+W>]AmLY26L(;5g\)0?0MDD<c:%C&O>K;RRC,'[^[RTqPV:hpM
rV?9*;Tlsb_a^[ir#E9trA)#gR7AZ<C"=QH2)jqS[#Jli.t7Z5<$jn*NX_#2IV`h-J/RPO-t
V/eg[e6gn&(`Gh]A98k8L7^Tm?2J'_<6*Om<]As>YO>W%WsK!S$8_OUQlZd-eH\^uU@#$)oVE$
'2Ht,hB9_6Y`9(?0kGb.A/`)E`!Of-o`=nC0ENr_<BGV`Y#[O5Z[LC(dLQS+mBPDk$q%cj$o
:!hG@*U>Uq6?m1QoHPc\JkqCG$9jNLMYmrk/FhS<;JlD%PD6ZeU.F7Lj,k4-Sa>&EN7b[7Fa
PUL.=&k7+A/8k3iOdFK(+'lDu!kL@UhsV$teX`%7"\IP+>a,le5GR8DbkC3F3d"MS.\IRNT)
88a<!e7)D5o`W\E[t/a7;.TfZBK\U1cC)ViJ$$Wg*1-]Ab)3puZq)Nb>+8#aGcME-rN/MJ\d7
H!Z7i2I+Yn_I`<n<NX>BdtT"6YBfmpNu+fF(B4-d>;aIT%u#PE>$1fKYhGL,9b8'VioU?p+h
10oWbP(5t?DnlHMTo,j!32X[ef0fJK?lYs5sFtP_Q+XEh\)nfhfr9<p<6)ZjYhG@r/&e\83b
0H\WjSNpQ<=>m;-2+!r[CV%aI,7#OH(gcR4TKt9mXJdXh%hs'`7R7aeemHcIG.RVV1U2%\[#
!I.`XD=4ddOoKd'Ss+&4<b]A+DrH;'ja/bK.%p.d/FG2I%Kg30$egeKN"Fc-:6Oji>EDQDWIq
WVs3Gen3c6l.R1s97,"]A]A"2^.\mQR-ShdoMYgJoJ:pdJ,C_=9K7Bc*N[@XNj$7YaAV\+`jG[
*<V+3:n,!roE)Yo+#kRYa]AurT+DU$GH;K\f.4ndSZ"(SaP*q(-"egWni5Fd%J%i.ZiA1H_E0
3.Kja4P'e0)^2())edtNla\&)?Vkp5LV`/J+kC;l&G]Ah.Z]A@jhC>cVdLZXDR--$@A"Oj*d-3
]Au^BDU:F",QJpX*21&.Y&,NlOQOVR_1@AioLW-J\2s]A?c,s?$8d(tu74@A(R:*L9O8'nFA:T
A+(+)\fcik9g9L,d?N(7@$7sFL93b`lm#LYOcn<LQYpCAP*\buGsQa2m9PeRPgP#Ym+!qNGa
kG_;-]AC_n9e^_+`JX,b,S9S^AL#34JX:,GWd3ZHie&dNJ4dk]Aim@NMgj6UbHKZ\Q=#3Of(gh
rht3&[%bO#Ho[XnV?W<uu%,6K#?4C2<]AH>3$$t)QJt,[CgTcXas4\4gHdRk<^YonOlt,:"$3
dLNb^3)n[`P_<$@">0UobI>Zl;n&X"X1nm%5n+^YQn9e,`@-6p=&DAtfXHO_Bhrr?FBPR@[2
coHb%INK!a7hLdq\,>8`0N5\]AN"P*k[H\DX#EhF`4AlLaBA)<;gj(i!5_#7j03=(>XaWmaXV
Adi(%?n;Z6#NP4,,!7;hbtG'YE0pl%._IbohKc7Z$Jf$q]A36\mpuF(o^17=LbR$)k11)%&WL
Pf2:XfO7"JklD"h_@4F:4[3=Zg@+-a*0(ai2K8(QH;OJ+8:fbL8YWeJ-%QI:4B@.q#G=+p)+
_t$QoaRu(UJ=C3!lM5NLp.[6FS9ADQd#:XW<a(UB,X_ZEMa8LC:I.KPJMm$L4)gO29qZ@[hB
S.mGAGb1P9Nla=X+7rXC6mJWk^kP<In3Et<]AZ.9YP,1dL;=+qEZq=bYk?P,o(1H"cNs4mnUr
m6!D$2C\[>>ZN#+tC[_aN+X4]APt6Zq#kfC%mURJBBT_52?6/ulrsP4^J#>FecY33qi(E^J:o
Rdb,6H<THr8p91%dkk\:ORVCfl9_ClTg;5XplmNtP41LIM!-f72CDOLW-E9e:FLfBl@o]Aj2o
"^VC$h2D^fiiWn2>7;A1:[fH.!B=1#0u%K&C<>kj_\dlimKsE`ldn5ge>[>^hK"pS<FL@k^%
4Xi)q\#RhHed'@"KSiPRS1>3.m33niVQ/`mekCg=Y<6^+2X+%f2Bi+!^.sq,QkU$ui0l+JMt
p(>elE]AfUbb<51'r>%<bnL-jA:Ft#hs#pKl,)ef!r`S[,<SgeYkQr#CY,m90,bLh.ds&h`Xk
Ck2t"aQTHNDS9?o^4L>(]A?6ACC<3cXhp-^h^S0JUPf3#D)#$jCR\Bs8\$%s@IG2;0a$guf.a
bqDSmZ$!/\H8aY@if%0dA2=oo8_3h?.E%d6cI[$%u5^Y3Ei.MRr,VeX.Bfr/)""**4:n0)#6
BM(NRd[?3R/"WL8BSB):.<>]AQ7Z/-q]At`_pWc6L'IO-K*gb_Uepj-8gcf14-$@gq:Z%n5=rA
2]A=V\DPP@uj^5j_/Ss;Q;"<a"`_0R]ARD\EsjD8NZa03.a0-NpsZinfqAtoR=c4nWebp4O%i[
G/qD$F/.h@0X*W(JH/]AkIAQK9p9=+n@@Nfm!h2XbHRtOrnOsUDVjK5;4PQ,NXI#Po4c87Nn&
m\cS>AqPDMp#X6'K`-q6RWrIFErgQ-^X+=oDl-_lbtq?77Hg#P*QnGZh+\eJ4ssc\s*$"DLb
e3B94.eTpr+2#_&lHbssGoSlmunhdi?W)_Uf*%Ck%]A4K%)mVjuF,]AQ/FA$`g)Fc[IsAJG<Sm
>T;Db%.3IF-t]A8V3oH@G<%a+[0c&Ed'9LUg69Do%A_eC+d<FoiJ*+]APMl#.($3?>k(gYl'gt
Cpn7su#4\5cH$G9)^L)rbJ:AD#X:!#1]A(_ifEa%ZM^&R`X_IgqW1H9(E\UMl*XB`b4tL^#+F
\\KpFrg1>k>$GYiK\^Z'LX;;4!!)f/";hQX%oJ9akT.;EDm20T#$EupL\>/AM+'JuZGl:G,[
kCm,+V`Fi>L!PqkOjl4qJRgMl5^TAE]A/qB::Ie(rpG=UkB^/`Eo<)M.k6T1X==l4bgo-%ihW
'YD->KS&(b+]A4?\*lgHIE!J6HN,BZk>j@\ps>rLLj`gRqVXY8U4D"<.B\!8N0reVW1jWr?;:
qeKVECNf_QpTOOP^Ur52eedd.T"YC+>*A9&=4'6F^:s76f5Q;^pAj]Ad06KX%ggt&li#CE?jC
englghhtG=&<APf^fFS/L>\c`fkmIbqZ6QG?]A<&9lCA_!]A>`1PaR`bn(BU>KT1i$1./TT,US
1n'aC)VP54F]AAj?fQ!K\b!Q2dm;j'%')q_;N%6E->J+"03)Ca8J`HgC8hQ,oZqgcNV9C!K:F
"G!%'9X>`fPKAS#-S&3@<3la^VR/Oi$<ReDK_QWACA+!cT,`[=CtV@VX7@[q8!`l4b%U#JT!
/[cA#=QL7\E[C]A*5!%@[Im9@YSCSZS:PMr(/s6&N#`HKnSHYi]A^#PKbJi(i>dQP#K>AZMkU3
LF@Xm\3D*BV+X[2He)'_D>THB=W7AE5S*Q-&9n(BpR<2dKYdKML.&s;5jTSm3(9Wp_ra(__j
rf]Apr>Aeq6?5RdB4Hf=$q-'ReT[N5'9[KnS8eKoU;Fod]A#(31@T)YHZs$A>*C:**:X-ji"8X
&RC\%2Yg_8r0)8<:oRbPf6>e0jU&F:YU#L>E:^>BPj\<6-]A2DB,Ft''N52$,Epn[GLo:$WAM
TNchDn3@d(Y"hEO>,GcN_UAX1$E@2rUYPnI1cO#=4Nd?O_9Ya7V!"!4e$iEm9;3A1YZ&]A+Kf
Ao4AjtC3ofAC7h\L$;m1,Bp:':7h<S71mtf:#o;Bm,g3eQCWM-^AYE<^c\@gF_YK0:-Qnh96
5_0]A^4bDUnir!NEgQ*8D2,\+9J1p,mAj8+=UU&!]A#U?F2T8p!G]A?t<PX*S;69Z95/9YOU^'>
&SVq_FotJ0e(0k01HO,ud\7PZYH:<WhtiWSU,HjK@c/>Sdtdj***ES5Y==BhG>P\kFk,]A#(<
$%i/>+[!13Wa]AV,n;+^>!nEruB'EA.R'8^(#*nH#XeJO4VNQ^bY'6%#&7g$I@qC*a<#$'i">
j7WTPH61]AJ4_+h9I"nXnZ2D:^QlS%&m@6A;OIohF-c*^lg*5rmp&SiVGdj!U_=KX<hr0ig(?
Tss&Ko\;C`1&dss)=-'ZGs?==>81`-NG#Xu0*j"c'd2fQ21>c-3?jfjiSURbOp.$;kRYA^@[
+k#ma4"k-"1uNhnIZ*6--$)4,6^5+h:E&&>+:pqR%.1^1j_Pu;G'&-k=C\9;G2KR'fe;5.Y@
;gls5Df5MMQr=k+^,*F+2O1Yq[u)?[X23Hla^'IV$-HZ3M#aY;!`dqMaflo0CjKiV67t'@dW
@MM&9>JY1?[OE0&?[;Do#h,ulA<LskMk7;<Al#l.@U^IMY>\0V=gs4P<K;AZ+SN0R##mo:K%
<58pXOOTj:u)FboH8m!Rlnhl1O;U;81Deq9c*TVe^rtHg:eL9n0__s3'_#s3G/`4PQc)JIU7
CdpO;.R.UV4\Bfd;"?&ZXFUbF+UN6;e>fap2W,`1<JT#5=,%20Q?*pIi;a<N^V(KBjq^9/6,
U2sKS$\O"@n*GnB\P8oj&&Y3EX*d2#mdhM\J[JC26A:g%F%]AYSMr$q;eVY+pRJ^X*q`Y3kI/
!j-g6.^"o&WO"p_VbCmK2fk6.LL$OHn*9/>pWk&)"oOZU9G_dlh*bSP"+GO+QM#\orPPd)6Y
-:Tu=-7^"RNN%iVkcM4-,SgQDWqWQdLg1["9IVb%,55';MFtIFC.!Aka7a4&VKZ-?K:OT99#
,Qn(F=E0h0Il&mGi4P_oCR,*4P#1M0Yb3RcZaF$efEnohJ$nr\aDpA3B<;fhOk(`59n(_%&H
e$2YC236T.4TX=iSXSaVPlS@\"m4"OS`0-hA:Lr;A+qdkTSJnJ17h5.kQN=Q'C1FIdhaL9EF
c$6oHOWT;8$;9j.AfGr;fm"lH07;7:0RCA6Dt]At=+G9N40)d0sR1X[@[XF(/Vi$B:!"@`]A%B
p-IfQWbbc)WSnQ"2N#62-hcEjdjXO*3o<l*VcJhE4Z/h.T(FZ"G-Xh?8S1(tUaPY=7u^9J!A
(jA'kZ#4?1VifWZ5Zk$S$s1[oW_m7A$#[%1DRi82T.)8up%[Z6]A?TH[:"S_H<g\IadagUr'N
K'Ub-Rm>sBeK_VRjF?8iJ:AOOAEr;KKSBnbTh,H'Pg#sTOr<nq",8tPnaGG+`Q%!7C"=sPu7
h['RsGpk,sRTMbTCF/o!><'.mHdf/qrGpqg;+\3L+Kb-i.+'e4+!UnP0)`V&ZRJ[Luu+%CgC
B%HPLbV48YS5hYM'H]AUF>W[QK_(IZ=G3:YhZhW(d_79Ft?^(J^THHtA,i-XFmoElg#tYo75J
cg<G0`/39q'5/kVZ/^4E:'[N.aHY!$CguE&Nfen.Wmh6t&QG"V5\#PK-RLKdMR&nZIT*V='m
;%q&8ZD9rNf#I*>[\Ms0P/5"4s\cWedIlK5/@n(B1q_%hV.B!mogV)C2U:+Yl>S;*ljf<f&6
D!PCPh3*eH@/n$9iSn,HM`c"6$[NMJsbf%Nco3VRl,E$YmSq-m:QrKb)I3MX6I#"MAEl3Yr&
TPS8bLS7j4pIhnfs&"P8Js1'Q8+QK`FFf=`+r)q#Fr=QHdm+K\M$>6@l..>>NlP&I/58u:U:
JiD/hi#ogs$J1Ke\Ns"%BI6pN0*/pf>VFNnrj[;XnFXs9R<=_f?d55-aLLZQn3*qC<*Q-q'#
X6&p<J#Hbkmhn;4)q%lu;?Z'C_h9V*FU"*5*Sch_Sm.#=1Ml'7Pa7f%e@o'75GO=KZ-6B*%?
NfM1_CGFni]ATt'>+090;klBfiQehfTn51cu\?39^D7=jlW*%kb(57HC<+fnZcSm?T7a!A*d/
,\t+OfZQKi..6!MADsQm;C[Jm1SR#2k6qiG>Ea\b$*XP9*J+7SPo"uT1toX:A_\on+ZXN.=Y
-m/)d#%lVj4=e"//lr[*kV&?B_N72;=p]A8MBBT[-eOGe)9TT2'DeY3YOG8"8+X4:cR(mWs+_
0D9Y)?-qdK%R.BPAB%GI9go:51MKjp)JZAKPX?^U1P;YC!b_))\XALfihUl.`lH+A^iCu%fs
qVpG/&5O;PeOuln/p,MalV%W31B#CN63GDYMZsR#Lq`XK7dk;s4(?24)-s.mJOk]AYF6#>LV0
SG"p17URCps.pLqKI^8q$k!'&EVPTMZ`9-.&PPu/FZYI95A<X8_?eR9Mop1H74V5[.2aJ`>V
,UH_Q9_C&4t1!U^\l8`m65LK9m_5_;T7'`3Vo16SIO#h'FsU(S@uu'f@8Zn_O4!ar>A<tYkp
8-9;M>0Z`#Ctrj-J=lDY0^Y7us%&S3a5:U5>2qhkStCsX!De*S/d5rc5_XK5oH5Z_f9e99"L
r8RlHTg(J)TAH[.aV4rW2oTrPG>@^5<B?4#G=38"em!4t(hs't'R3#?7Orqao`rCtI?Q8IXJ
$RUP>.IZO_8"Kq<g$CE-20XNVb4Sm\?'E\7[LqmIrG[`mI&_Q%cHAFEsBD/=Obh=aW6,)3qj
M\X]AO-NH?pS)-1MDnkmfg(\eAq;>R8M7c,m!FDa#tr^f*HZY/ftZf:'OHn\q[!@Pn)Q[VluD
CL`L("nAD!u-(-FL1ef7Y_<^>aK<&-5e*N_j1,]AViIUi<#K'JoeDeJRY*'D6?l9Tq$dn>`<M
/EnhR8a,1JqD2^nFT-/]A*hqLH>Fp;c!>[R9)Um=eKJ]ArCWJb=Dbm0p5pF:)/0ulAnDUqef/L
`S_/_`ak5(A!h&M;i%IL.O9aq:qGZkoOMZI>k922X&faVOlWBAEa:$J9#[aH^87%DgA(7,1X
6T[ZBN4R`BPB?X2[-iL:&]A>Q"m>"4Eot@1I1bsXKH=M?VguU!MXL!E&+a7\<!$m/.O"/B.n(
j-moNi@p;H%&\W_IQ6ncJPoEO$lKmRmAWhj3o9HcW3!GYFDobO945S=VXo+/K`JeeQ6ELXpJ
UM+rfm]A0U7HkD2l3k[K5'`LLMRJBI#SG]AK)[TQj9o=GoC6=@Vf1k8#.9nkd!Ck<m3?nEM[%g
p=lL6\tL9gQ"`!>Q+^Y1CnqE3>Ske&s4S]AeDBSt'YbRIB<"'+.oi=R0U,(nAJf(o5GL'3V6:
>13RBKX5n!h@DU`(Z%0/C+cJ`F.3\q$*Uf"H5cr#K/FuAHB6BXmEghJPV>TG=<M8KmbOoKQg
8dV7FIjbD1tm,!;<=cm6jR3j&k,5<a.u9WhPm01lp]AZhtSMp7_q#%R$&TAq'&h'dX8McBVkS
5Foq>_eC#X>?BtBnVV*C^<Y>N!<S7^lNCGlN/=*0hNRL25rH6&;CW8W,Bcj?+2M?2d;hEqZL
R!j.:E%e9<jskl^]A(""%cgEnHe-.Fn['TLHEolG*N%#@SbVmD%5<<Y=\.^^\3.T'e,u.T]AN=
I%$Z"J4h7\o6?T[sr]A?D4'd-;A,/DeC*rH7<Fa@3(=!?iV>_kGGo5^D<$WFJc]A5&MTB&KW/U
2`DK[bI0]AT"t`C=nopDP:]A#9H@NjF65<l%\Z27.AX@TAH$QNZBr`VD_9!jfVF:#\)qg\^\)N
D4$HqT/\B<>UG!a9)C%Y,qu9&YPA&i)HBY8-/6fGd3b.)DVmpKU9uO"kc@^+OM#:a7ZfgP2-
?Xln#!GE]AQ*A+hlngANPXh.L[`m1+M]A$TgGK0%&4!J1'Oj_7=pF^IAF.-ojuso@!4+8^>5jP
]Ak8S-RJs03+-%g\J]A?]A_sP']A=BEQ!'D3kj!SMKINg8]ApmGXQ)<LWKKBK,?tH^Co!Z)$.?M#)
6KQi:*F@?%ZRJ<lA@`IuS$:Nu\c=?q>KU90Y%`t22k\b2F6!BW8lG+i7jCT%gAW>+bA6oF?<
d<V'0K'd*n0AZ4-08YHZ+F<je3qjg-kN:p4:%EU$%'D>B!Ee7;D`5e]Aaaa1raJi&Q=#k+<7T
@B/J1"i(r`&2JXtbC-&W,t4ROf9sH@R(*R5.$\@hW;eOtb8u<C:d0_LIcG<jYC6q$s-t"8#6
]A9sHum;,M(JdF%\A%fh0J8[k_lMNDa."mbCW]AFqd3,lqNcfo7L@2+Vq+P8B&-'GCK7V[_XCA
V\V1BCk<\ZH<;4O6\6%kO*ihmi9fVeRMC?G9'NZi^7CnT.\oqf:[m`S`N.ReXdjs>X3X5=qY
?n3sAX^!h?<cjN9ocUD7#%kOf",qt-s<gB_,!fJJ=W=`Z'8l0%$-fAoRa4E&Q5?D(;9qZd@E
r2<iT<:niM:OX@g#UWheIh4pEb\q4E1)>uCmS]AFFkur%u4oWG7S?rog!h`NXXkG.M[r:/9C-
J(J/.6KUofA%5c=4)gGjH9+i.-b=r*%i:/YF)BM\NqrIW..!Y5G5QSk[os$n::6"_,"G8+Pi
3OPMh>bIQ,f8D`\[0XlUVK*m:?aBM/Zgo4=RZ+Ho@8O\?,`iC,Y\aOa"Fo'gC&F17H;V:`*F
@,j2+6&4\[W0$"I\lk$[EB#06>uDp,Oml6JlSKKFCncR4BP<S7A.'gn[brW_Ql+94_OL-oKL
GU71>")T`[o(+nR'MT9$ds61^,aC-+<$r$[<$s):'IcH^5Ai5;S4oRGWDHlpOtB7(%p3Qo<;
C-($KPtUg_g_KR=C..ISo%d>bBrJ<HT=i[,8(anSZC!e[-'=)@F\ll]A->hAa>??9AZi88Eo$
>k88Us0bS2iB,N2'8W5uD[)#Nn1gP^ts:>g;O!HX@dkb3<+f;_65=0e!(i3]A`@r\pK7VA=p&
9SYI,C52ch9M8$UFm,1AW4u2q-EG=0ofpf6U`a8@e8tFJg6T*MGhTi]AN-CiXj"+oEpOWXAPV
6@a&7.V$(WDQVNB38-R!h"B/bA@fP<Ta%cH:a!N?"F`-e(2Dt!,1Vr@MZqOfZD]A?bu"%1mU%
)0Q/:.<81=4Vm)gp[li['VpLS7iP<Hj91=a'g$1Ik(C*&tl#1.(n`P-@'?!af]A+*XX:7jLk'
4L>f11YiF]AoFkYj1FK7R(,lbVdqB5$Ke[QYBVGu%k"UA&N8u3n<d;_kTBkd0G#^Xn]A]A;`EWi
MgMLF<C%1`*r$;f5YgE<1g0[jEUs\bY;(CC-4,<be/.HC()3(=iG?PX/Kj1E^b(Gj,c8.?t6
8q<'?=_87+7K&i2<(2qK:VG&0?X\*Rgii;u6;W,B\$+.Y]A.3*+YY/CpU&(%W6>nWntN.7ahZ
?#SsS66-CI0KmC4eaeiBH63VK'd^S/Zq/cB("D(qCpSU.+rHarQH=4j[O1&<TRoCg3ieK^T1
>9[r[Th#W?Bdo)&->X@hu6@nO%)&]AGi7[B.<-V?u+E)%_,94&G+M3>9ZV>EnRn9WGjBTA\)/
bM+$@m-X093fqmO_F5d%?m%"G0o<,BEiWT\S\chf3-6['Qp+I1]A9$<J3IFVgQ.PcQ#!oHm)=
*9@j^fMjokUsP.;K]AYEsTa,EIU)*GQnp-T'9K+F^)lHp0ftZZB%2u0uM2>QbmuH>cLKQ:CfQ
(&JZ$ACOM(b%efb\b<ij;*lt]A0#<-at=X`&,GS7P$E1@R$DH:K"o)6goR"q`BQ>W<*L;kq@3
7n19O8:h(q$C\P4c`W+0<1LX$kRS,^ll5&U*(AS:,:'4TYFs5q+276m-k]AR:j=*mF[8Wm["V
KX3@mqOXsrL+dG+X#(UK&M,PX"R$I='4iplbV)X4J5<uIU&T,#ZY$W:nib?9ll*PEH,I!W)+
EH,tmS5mtiA349'S7I.TIDK7o[p(@>&Ic>eEt/C0S4P[^-mWYZ[iVb85jq7WiHPt_!m>?j-]A
;d2Oh)*7ETem&X1LHMs1DW@f!j7]AmI!Mt5-)$l@)\JO_j/X3O'LVNfi@%2rE3JUMLQ&-*71Z
UZ[PiO8<E$*:6bZSIQr5gXdM%"FSok^/d8*Eia!\dMAi7QF+@B+p4E'L%sgL$HmtTi7u_u.f
+[7@L#`!;r8GJS+R`Jc10G1FrE4^o)C<]A>:ULQK"&LOX_:5;=8YO%re-./S7L3:*=\(8RRqc
qT*$'#1\f6;e-tY:W;j^H<dpYl>-,DAB?E'Se);7ue7;.jIgUBt.aO@DACG%3G?K..BrdX?n
qk74V3u-Pn6ZCi4[*)L:)t]Aj!DVuUoU8"Bq:]A_7B`u/>`lnj%_8RX!bc3,SVOdHoI?@;9.Po
:ul]AgE`*me>:ricXeV?Y8264PAht]AEV;Q\NS\D?H$'N*FNU7-Y+!8:s++WopiS`K/9CFCjfr
cD6I3T'"SfalVs^/5.SJNa!fAG488aQ\pOe5<X,@.kNJnNAc;G]AbGf#AeE6u`Do^E;h%\C=i
;Qr.&"3^63@9"udpEK!+1^L.hS-H]A,n(innm1#Ak.9gVs+H_H:>lhF_IDuS47M`U38@<*\?j
+HR%c1?AdY-oqlIP!<I::I`h]A?\CjRg:Wk_+t*3]AOukLCY'hETieKa>A!AMDiV.^D$rKnYXf
l/0*oBS9^N+3G,3P6:*sKHe7:a@N1sf_a(j[aIlo">MuD"Rrn7N/@';c,\*=A?nNk"Fg/oOH
AAJe4TS-?Y,VOEO9f^oB.qe:J+2bbc=#KHeg)VR5(`d(&1Raa/Vd9dqrfYL5SeOQbqanXW7q
4jP-?2*'\ll:6%\d=p>Ic\A7Bh6Ac[p^.Rb]ATUt.9^n:&G*Rkn#Q$,fY5XsNbTQ/PZh2%?rs
1N/kan?mMPDNo\'c([l?FDifi/IEG2,\C4YY:VJ]AA!&s-Gq7IeHib.MI?<6>Ca=?KKh_ZMYu
23Rha&u#BW*A)1Be'K!20@=!ke'nN5@+pB.-KYbNV(N(0R@-TuX;f10H8a?&<.<KcaD-;*Z0
G@ac3pAqcABmh6,89uI7n]A?tSGS.!'I8sLnKl.*f;qfGp?sK`3dsY).Dh4,S[m1^i!VfjHWM
02hI>XWVD(B3qZAr3cH#l+r*h:(45nU5J\X+NN>=@B_3&,FAH-$W^V&)0%bE[O!'[uX'LodD
(1gfCBTU8_iJO0._b&G!1k@DCM33-j=4Ws2Nmd_I\IF'=2k4Rk_@\W&]AWFIp1p2Xc_[Af3*J
uqVL:<'<PK'R0S`^')(n:AN[@RP7oo2s%%Jo+e<M,Si7*?dr+GCCN3QJACc0L$agI!9&=S*)
9IA'J%.=P?1(g?[5$4t*g1<cUCWQ%qLMi&WSIZ)6GCda2D<ohVhh;6`3Q)B9f(Oh$?YKfeNk
`RdG[!Nt!?Vj#MsCs/e&V%KR&?G4e)--<QDiYnJa1S@B*;6&Pk_Bff,Y17f%ZanQuaCX>opb
ntAhUM"(nGV0DTBkfa9Pko[DSAI-%jd#UapUu-7otlpq^Wl/o\ZJo#(A):(EY*aC+N0E)jjS
"e-"i;'^W+8Ed`;tN/nROlXI6^YDsF.5Ye;1RGm$1H"A0.q85k9s(X#L>MZ=75Tg]A;*SS?9\
@Dg"o;q4^&HH$DiDa/"A&.o5PT#57gNb6e]A*:,3?24t%=t[RZUIg/;hY5.l6_n'28ONKVm<D
1E=rk,Sa5=CR?'o%plcg*J#`#a&k1-=7<>I?rk*mDKN@IjUo%K!*%\bF@WaC.aKFHR0%fBt^
oS8K?=YK>Rd?Ud@!"b[DPF2E:BZ.6"?3)%FmrfFD?=?Qd6)+QS>9f%*E87T!-67_O&U6D2Ms
;Q.&#Xjg),93)^G/qP,rL+W8m\rpMN5K%jJCTBGCS\bSeGGhGET+,[9uj:TS_"(3i#_$qGTH
EI2DlqJ1Y(1EdBUo0(OF@8C:"cFP.WMa2lFYcH/"!0Ye.Jf(6p6Gj?n,6LC4ado2LPQ@s"-d
BL^bX\:)bS9tpO^_<eER-bJb0Juu?C&MdV%V88d4dD!eZU`fJH<"po,dc[Q@H53dNCW.t=gq
/=@E.n2d&"7@^4uI'>Zt9Y]AS",7%EJZ5lfaI,hXNWs&(2]A7ngk>ND/X4'lf]Au^lho5#S"Y"=
]A5lLq_BsLROT/\k<ip?Ij.%_N?KZhLd@_/V^ZbIH9?7.u9EC5k=%^\=8;i:/Y21\O9)&AGKC
GctCS9AgC!;]A@=R_qT&DH4-3.SkU$GW$5qkiQ#(MX_Do*3X@P?D+]Aq-LFo6Ea]Aa%b$bt$s)L
\%>CUDcOM.%(&:LkRf5uMaNKFVQ*lAQnR30U7jR$?aj7lAkc*\jTO'I=2L-]AuGZ4Z.)n"5Lg
,0Os2'd1E+1?Y_E2KfYd@_$Xp71?MUoKne/'5mb+TRB`-$8SSTP3/OMjWmlU>!#Bq)`_,\/-
33YJWQ"2VPg[Bq`s-"iO.W<G3Z?)Pf="!cEX29q$/dESFU%mMb=*kKN.-\Ful,W%e#;fD_q^
JNn.H[i/H!oj;MkNQU'IE?#VR'k666Bi*:ugY91hj&4s./#llG]A;64[*5(iF%4kmCUr@R9kp
Vh-:TmkP'[)[([\6Z$(a9T1Sa]AY,Ougcm-NcM"Xc`t^CjDR&kigE4bPT\8+GgBfolt0>e$F`
]A!71XNi=g9Dg*Pn*l_ojZ*u2IYFn%5kp71U[A\HE^K3u<-d5ofj+lb98+_0Dg$e"aRcFr0`]A
.i'3hh)>JL_qA-bcp;7]Am7A+l^NAoB(%)`/68CNINA-3Rc:SpNCbLI@F=]Al1-KgISjD;,Y3p
qjST'YOV+MmU,GUXDnk<@Pp`+X.gtMY+<rUAUr9,Hu*]AN,2gR!lBY9%6!PaDfE1r"U]AirH6<
A$q:,=U1>QI9E>&4\J/2$c^),f9Oqj2&[hqpc[s[`J!_(ghsOia@rp2?Fu7d11f,^"gV[S.Z
l*`;VtpRa!Um3UQn\A;XLcHqGX;'kM)Q$r\tf5*eGn9R5[%3as5m'M9[W8k7uY0fslEJ0SjJ
TQ'123f'o!k5S`M#K84nBpZ.nDk0i_G<`c'G^8+jc^$+:=Q'oH6]A^#YD_=%1,V28GQaAqf!*
P?se^#`'(b[*[i-c-GR<.4)HP[@a^]Ac7c3(%>trio@Ke7p/:07npm[!2l_K>?mVQMc4=Xf#t
j=):r$hg!nG%"Gf"!=0GqI&#s/"8V[?"4(3-:EmJ_.gVe82s,W5LG=:B"!J;d`/"E=82`bO<
)tW6U7mWdA'+R5%8`%Ic4a_jQ<m4'^WV&=AK;\3A_AYaJ*mRY<VE9_@>$5A4-%2Ueii\I>Du
h9860q^95;r#hrr&fh;_!DM<8O\,>j[h(Fa>.t>L[Ze:kLDHed/F^qs`aCWs^n*p<Ocu)*>X
SQU;2r4Ej#Yk)?dh?[*[-Q*#]A3%\"1shJ4AReh9=uXE<lA<A:U(a=RBrP!uV2F#hIURWEOk_
us45ACkk`A,LHENV18HM!4'_26U?"@MRu&#G?AN^K-*(Mi?5W153(*]A%R<uE5KBTr&`0']A3,
4a:RujGiL`:sqPu^^rG?iFJqD%+DJ/AE#ODE+J<_0/rE#-ZB\I#\<XD`?+W9Rq[t#IWq+XlU
!QY66L[b9=qYFuP*s&j+A>MUn(F]ADm-&2`7/gnP,-m#u1rd/+n5ZD!Zo]AV.)jOo,WLR&(rdQ
W.9b3%L8TAHR',lQ3U$b5=iRSN4%F*^>6,?I*g_8+K[Le8XgSfmEOjSb%YE6j%@Sen3Y+*L0
^erur&8u\T_+B!5@>uU7<s7pSADQkU-<J"pGQK+ajpfoVCEig-J7)PuSHi!WJ%4Q6%.!c)(A
pijHoNnjGCQ:p[1+`"ukuBA9*9)b"ci5HqK"l./`GOK9>^#l069D8HeNf$SU%,>Y'ff(UVJ5
r`McDia!VGf";dlWfSNA&IIc`/A.8]AT&)qb!MH)Pd@%`MNC^[fjSIDR0-#NE_)fd\P$HD0=?
/Xm734:Rp"0`Eeu_uu62gRQR_DVp.&L.pB]AJ/OGtf^.8^a"0?l".p6;YNi?B[g?Ll%==-B"q
`k+YKn1F"ONbPNjkqo*+8cO0@F$TGPqip`QamD(liFDpPA3Y7*&Xjb3*cO8336kqh*$7c=0Y
um>@cEEJ]A/b.=CUTZr2:ZkTs^6Yet6`AKSjJDlOS.koQ'ib$_?T^nDH'djG]A&`&J7Q/`V&YL
!a/+F%p\rLmsMndh%M;^O1.MA>N)Yo$$)*a<Q@e>+ed!:(W@LR@'nKBVBaMX&XuG@@t<ba(d
>r!Fi,.Vd:%ir!@5KJ"NQWVA;[/I^bN-ieo;X4MfQ_!]Ad9)HKc&TBJ!_fCQcQ%+L]AoUguV?1
11"QAR3K8mGfRLtZB@s^=]Aefi$[kZQBl?ZB.m/4#o93>T@>a0>Va#.ccG^lS.t-K6>OoN'qm
nrss)Gp=0E8PLIieQ.hkTrE\;(M4l8I./W7t.XTl*6RL:Rh:)sT?2D5c2c\"R\,h1iKMFW9U
(e8;/]A^I6l[_Gmuul^J,tee!pI8)F8'R3VIsLXq8'\o&SfU#E''O(E-`D2<%bD2<%bD2<%bD
2<%bD2<%bD2<%bD2<%bD2<%bD2<%brBDV%kL*?%pEF,%)SZ(?^)NP#3?@uoMr_M@G6RQTjWA
WcpFgtp)`P\n`&>Ol`&>Ol`&>Ol`&>Ol`&>Ol`&>Ol`&>Ol`&>Ol`&>R5*u*2qpX8nE3?2CI
[!B>@GGD7N+grQ&+grQ&+nkr"fX=<70)Nf)c+j;9)HSQU>g-9aq<3;.~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="164" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="1aaf1d28-933f-418c-9e4f-386608efbcd3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="ZB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="company_c" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="ZB"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ZB_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBMC" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_tab,Key:ZBMC}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="ZB"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$ZB + "-明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="4" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='line-height:20px;float:right;'>口径说明</div><div style='padding-top:2px;'><img src='../../help/HuaFu/icon_info.png' style='width:10px;height:10px;padding:3px;float:right;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="qyid"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C2]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[
FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/口径说明.frm&qyid="+qyid,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="4" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9+4,P1eQdRLYsUcKlEib-[S*)5kQu0uYtl3geB\1o70caJbr1-A[P(!id+KUT$s_Be%QY1Q
\T/+HVkWGp!j<5U=;o7DKQ\,-Wg!Y1_^9n*g7lO8$6:^$bR-lZq'&=R0,YT>,n>2Ap?h!WW4
i:Af5#/-)p*HMQ6u:>\G(m!T<C*(W/2m'(\#nY?.B#t<:sVE&ap'k/GMqqEbJ.SR5*UoDng^
=s6c_/ICaZu*nhmbN%td!13=0r%BkT65rNS@7#4mk[FoI[;oNU9UJ.&Uun6"P,/d3LsQK4!d
>SAmN-t-B,oehm!RT/s\)FEn?(QL"MgZkPqrq^^JQeV;84CnL&Wb\eWUsE;kWTDtNGqr>Bm_
SMrd>?3L=6WAn43+S>AMa8aO#VsHnpg=BSXET4n5T35gaq0"-1[2sR(4,.tIfn0!c^0M:)2Y
b7_laCscs51=&-UL/S1\12*Vr".)a+'s;D/%>Or3S7e]AA5kNrl@2->+66@<d2#CV]AoO`<BDr
2[\9e,#$n0JNd42\iX3HIn_f8Vk-a>k73K+*!mRXYiOVCNlZQ8)85:.s*$oU74Ra8@BJDTkd
#DT'P7_La.RMGWb[Xif"PJ:m-mN&_%>p5-&hOo3Dgm%r]A#OA=oWQ,,p=9sUGCB2pGlI^+>HC
DQ.<a[\!03I!qM^"5k^%9KK3HH=V7AP"lc@CX)"K4u5fsboSCaSgM71U8;sA1>'W+?WROrU0
KYOqLLE8ZO'>)=!D&bN-;fY9U,u!*R',UsL?'?_*3]A'Iq/8S:,.#`VApP2HjZqFje<33T6\s
N9mI_]AJa*e%g&[bbl[0T(:u6V9Q0;H=e/l3;;h:eC=K/I\PHWd#:H;e\!%$]AF!($^Gfo>+h!
fFgC#ZmPbYEU#1R+R&%+InZ=p[Dj&QV9iKoOZ"Xcji[GM!Fr!jcCDY^[ifRLe(phhsl8L8X,
uq9#-@5J\n/(:8qq7ECFEMV"<Zp-c4.Rm79P;8H0+;DrJB6qB^$K\$1*fFh1A-EF**1SjV)f
"(_)?4M-)E_0[D#Cma"36D;)4"?,O"[MNm8:6%4uTKjS,[$VW`&[.L4K<U-@,UcD-W7.Oq1L
"h/S"6/a,ic7lOpfn\21-$P)GokF&6\h.*W/>Nkq<&a@O1<uO^L4VotTGfm7la.IEkKK>%m`
G$8CQ3TP"YLmS8PGHDjC7,u";e^s:u$pLll9;NkKY_%$k\X5+_L?gO&Q%'3otfU9lS8j-V2+
!mird1[%83bfP*46+oBtMnL&JBX'7iX.*%`XE=T$c'"dW`g<?YkEi@)hk#CW[3`ZQ%-ul.9X
@mK)Am(UmG.LpbV:&RE8CpDkne02X(a[))fDD'&ARuT%XPhe$iUrLQ_6=72\)BBV.#Bnt!;o
Tj*`A.%M+k=-KI5llVfFOaEf?/^jQ*IMAX`+`qXQiMq'3`",WQjFk&9)tR2YAIQGi7nan0J\
7[Cuh`MZq[qshWp3VAdr[n%s7PiWr=Q1Xn<@>>n+E\j=jj&9D`mr%ri/@G%>::R,KG]AJ?*Ts
[OI=s1k9hWj2h.k?&4&VFq+Vn,r]Ao9AX(@j?3$Y(u)(iLR2i)okt/I[YX7<O"5oMG5cW5]A2R
8L`_82ka(9qO]A@VIlIjU'P\<93B#4"j!=,4j?1fD.krPMufY$sq5Z\PRKZf%5mERj[G<?DC3
4TRD&c"0uq`S5@ihWTl^ic0>H^rlT)]ATeMFki8t$(!u<D^n^X:r92HQ=e"#)0)k:B7D%-d4`
icLqa%`qV8j)YO$Hl)'V#FlMKXNMNngeekO!SU?0iO0A.?&)q`Z%g1cdiFqpD,j%)F:86mQp
9I"[6L/&tka=DbOJDI7:&iGZ5ndZM5(er('\l68)M=(L*+_$,(`obuq./)+K2'F$\M4,]Amm3
##W%To\%\csE1=F(:oii+YG7d8"5(2@HUfgL\RcV.5=^NaaJ)GHIGq7Bji*\%qqKrXPLB&l>
T\5FEi(V^%Kgo)^l6TbBBLjf&/Y?W3;#\>b6^9p%S$2^cWrcn(>0m!Ds+UV5Qdp;9Cg)"?"r
<lL_Q:;[;i>:03Z3c]AC*-0tZVXMOJ&*?<,#7bp#Tac8JG<rVmq3:l+Ze`FWN%oZW\GA<Z.!A
UF,G#$=M9b]A!D$llkkN?C/n<=L6fUX^:U`UtM\b'b]A^QR]A>43tT?c&MYp'"7Rcl@7FER9n#C
Y:Gb(JsdcB#>5@^X/?_N;jnc<5B3DVJ<1rM7Q6E_#l&;A:I5e(5p(#foK_@/ksbU='V/9!.I
cAa[ikoldpRIlijji-Q?K/rl40aN-7_m>:f?ubq$D^<kEjf,lCFe8g_S3ap!-+%ce%/6eXBB
:b*\_B$bA1#ffB`pMCk5JNgIJUNfBV=S@LXEjM,Gla(:>=Yj/sM8<\\$2oqC_!_IEq-@o*"d
QdasX"";#.gY5$Vg_rPkEp=Op*#d/hKg9nD-gUC=5f8!75D?^>*q9Pdmb*=ChNai9t;)P^'b
>rr8Ro9DCGJ7UH/6k#Nm\`_tVbtag^lGeU>Hb8BQT!Tb1=R(:[LQVQ5nlU?c`\I/]ADk+?HGs
/[\+U7fY_CZ5@"Sd^H1]Ag;k62,L0S2C"N+2pY?&qF-VKPNr0gCcps@[fc^[X8X`2Kg-=NNGu
Q_4o]AA`:Z<oU>HOM?]Aj%c@VH'S#RKbuFZW..;k/dYES,AW"oR^t,a]ArKJ?Qnnu'ni'[l/,r*
=Z=;p0I8jpomh2d.\C\58JtW(Z\r?5&dEL=F'>WqJ3nm1%)D('JeJFBr]A]ASb?(T)!#iN6_CP
-'3YrF6-QWg2pE7SbJ<U>`SQqe(STTuQ%J$_AkqpM6dThSXKu065L)eKOFX1*j=iOA;WH4Yu
4.BYrF8ji.ui;=+Vd?F%k:cLPZnLsC$\eJeQT`>*":oks$T1HC:6&l9lqPLNm0\/C6+,mMo'
Y,.N>,O):VZ7c:Tj7pFLC9J6-EM'f,^*sAEo<UA"-6i=sk-b<fe,_lRF1Q$J"=u$M^aA?RSn
kGE5KOpXfNJ'*5+D"N#$&fKEK45F4>V1DY'SceDh>Pnenmal,Xo7X/4p_dVp\$25+G=5;B6:
N;VU6QGjQh21q*&P[/#ULNt`C;d1h3.Vfc\g%(Et0KrgP>r#,umMX`6]AQ=6D4$h<9)9._YuC
GZ2qi81M]AY(%>YSkBGf5*#;^]Ag).E0Gl33%f7]A^duK3cqKU6Lg_01/HLD^-C;81EQon=&2#=
#*g<V':3!<Z3(7,</AagXO>6Q;R1te6o3m_!PY7'g?i%nbG<:5=`CU2\&Gd:$Z%Y@:gOVnj"
#ndj"Qf3k5c+gJ(j`G<pmEcr8m.,8SR3*]A8X7\a%]AM-PD4q^3e#n8Wf0GYICN^Lm(L9N<qZS
jg3r2Op^0^JMlr2Xm=['aIl`aMTjXT;-+BD>i>/l5%b@'d<K%iFdgScr*sG*;VgM$@VeNSfJ
"'SOI*R+>#(p:!3[ca7klNetF?iq`[N?[u:r)`&Tgd[)5%JFO+DBs[B(DfFoXB"U/P8XG`KK
Z`"UT6+5QM->NX2Y7qM'nM%mW'i%niJ\Y',KR-3[Z7XQg3)/)msf?*P7Xa36^/aB0-I$Q#ti
C9B]A1V#(d;(Pi#tBg@<C7/iAiNe5>S%<K=>[^KhlDATf0@!>aUoH`]A<YN.Ir$.rcBHEj/0i>
nb7'!b*DShfk@7]AoT!ug:lL#BBD0.R`^>g&1LciGFAT5n%4N"]A_u\`Bg^C<<]A.iQQm&*b4hZ
)BdB$8&-GSj0]A8Q,69]ANC?_E0'fCn#$E2ril"oZ)B]AuNeO:>U8E`OEuYC=HIe[q*lD?M^5[%
eTKqZRr8*Z;SVuo$7F;tC]A1blEPc>T5o<(tXH_07s5q'4(B>+^5IAdoE?gp)A2a")!9&f002
X+[Mf(sZ:H,tGp-tXf2*1*f9#su%[)"Wt3[LpDkSRSRr%F5=jU^%8(]Ab"'&U)8T4'n9DUg,t
>a&9`@d6=NQRE$$mlGj$SEbk/^]As/-MF;a7M>Pms->I#>/mQ0`)'k5&9umFQ97l$PPhqp&->
`!9dshJ-Ela''4e"\=X?VBqb/oXH%f:G_%rHO2`jcYjFE_%4%)P$bthj>3J8N@qBjO*]A47/o
>-&3AFf)S.L04"=Yah:XK'Ce*nU5#3-%$@@Q]AkTK3>E^p6:>q9\n[1FZejQ2Y&Bc!9k,B()(
PY!,XKP\cI>Y\aKiIJ)_`nW\j52,Q=dL<=DD@8'&1RfB.BKIiD>%@5coQ>qgFM^W_?p<tKR#
Ze<gG=\4K8L8T52l!"YS%`qPf>QQ8a^ROq2RJAo%q-V[hL8H,5!XY_Tt,`T@N]A:sA,$0-;`-
F_>uTO;R!In<4$FaDn4ZU9oO@>QB>$4Ig1T:7Tr]AMT*4='prp1of+W4s]Am^+UbAZqML_Z*DY
]A%?VrqOo7`BfXg?]AbI)_qle?q7)c'/>%pMW`S\Xs/Uq-W\.eSeVt74J3FJ[mlKe;AKHho9hr
Tb=hWGRs6#g#7qH?#B=_7`KiX0GeVD1\+Qtd+>n<:8:bqfBQ%2\J3Ct\1!QV"(]A0SqCc<s#p
6m^$PTC^CL.J[&6K.da@Qbt>C2a^*?KPAl"I&?"Nj,$.bk>=;\6qLmr%D8VBrVU*)!Xo*U=:
(r`W\?%soX2j-kUl%?FjM]AV6e$gV-oAr^o41j@,HI_Y63#uinJXYmC6s0G:U0q5a+s10k.>p
t!R9+`LikW1$3l^!ABC1D@8^@X&Kq9k@,5n[qK0<LM2?mHig[Yj).1g@mEi%9CJ'm[^5a;N=
/1[hi-=C*t/um#?XFh2ZT]AG9'SE7#c)qgc<+&/gi:;)8!8l<?PpT[$gNBFj`A-\j4YmXa7Ff
9NN>4:0?Z3=sn1PH]AOdWm"3G%1!A)n5.sUAPOg/TrF;)i'e<@)4d1cWc;U[L5L^dAShMei5*
X^6dr8=JmB>GC+7URlmI!Q&BJ39$]AMpmu"'JZ%o:>h$6c,q_/iEX!8_Y#4+$/"#N7F1dD."5
Q`i."#N7F1dD."5Q`i."8o?V>,J.fo"PIGVfTFq]A02d:!J&3LQLF;D:QKn5P6;^E>eXtUll#
aUbWm(;")/keUk(@a#1>aT8-<QK%A\P3O9X,urn!@7gM69tc$I\YQdH+`n1ipb?a'DI~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="224"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="224"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="0"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3467100,2641600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O>
<![CDATA[公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/3XPl:VHY'i_f#pH;'p>SP29Mm3R(pYMnaDU?39!ejM,^Y8#p+Q\\9MpoY@2R`q=_@qK)o
IWnHd2Nbh!]AH2]Ag(\$cL5tkkBu`>mXMC:fCdjhO6-1b?9L2$dcK==[%E87Mu/t"'iQ`f4\3l
uHZoQf)R$-1io8+d^NS_EGt@'K.6,3B,At]An`?i4b0r#U>mqpg&q+B:\q5`RTVbS-OHCh@Mk
K9Kh=5d%)p\tg+3HX<F8"MrJ>EZ6%mBpi]A1H+DT<gB0WZj6M1B>$Aq6P`$AM@G`5Ph_5!3lP
c8l%.oe!]A0?.RKOFnB#)E;*F/XChniC?5%N@E:+h!dDgO1qZZlBH#HihUGDP.+`uEO&ET$:u
?V\I^C[I&rRuIkAp@kmpQh@jup21*GE&2&)IWk-FP0i('4!Q80kH4`4G&+RsntB!/2f>UT<<
)p`h*EWRP?/6r^%\[Ng\0ONmKo'N$Jt[V0)4r)D]AZOeqcb6I*.JoI_(LK'gU7"KLIs^ERo1Q
tg[;c8?2`Y]Ao0.-[d\C`J]At\IhmF:UJDL20V'%c36s1+X5>;,lZ\1W3a_tnjlHXd>J*h?P\n
BVP)XN6YCrVq"oS.U8,13gThR76Ug/mpPVs3#/aN>^Ne-l1_OIm:NbL.aLIBkb'*ILNl3Jlp
L?(APLds##X8*b<5*gOW"@C.#_^9NZRIn\\pS++&e1>Gh2`^OJ*[a4[77]A>37981Tkg4-:/\
g]A,up8Dls>M>D;hJmSp`,.D.@A0:YL7'(,*F+!UX*#7tsVhDH6`/1XFnF&R\i8q&t1_F'K!5
<[P+#E&Lk:%310;ntSIVIl<^NYHk,n:o0N"UO4lj$/<n\:\sMZ%ZSVC`+hrH>*Knnd(\rmPY
okB=o4#*)45*t-h]AN'?=T(VJDW(IcDnY79a=`kom9kEt%QKn_La>OTH;=#Jc[SGC,DFM,UOe
*qF,`=U51LC[bCf[PpSPY/^\VDgM.%_&A+q6DQqDX"hYT(U"s[iHl.b%GM,lli2aYJoV=Qku
`6UsEY,PV<<`d`ARGY\tH"Ll0D.hkGQTPXi'5:%]ASsc>U3<]A'MkkJ)AQ"%=(gTht7\on/S#m
3F_1*9X7'\AkfnL]A;<aZ%D6hk_N2V_=_ga80+/JtMMPHeqbLB46cg$A,9k7@R3?L(=a-S5K=
C_jm7%#f5OYmQB'g;?Lc`FQlB'6fQBttFqn3lS;a`dIZ*c(K84CWAd@sNdB+F@qMWTr:VDM2
TSm6rpgFssW<4^jiMNn)'aV.T$<MNMP4M7%.#M0Ad^$VHN$=!FAM67/]AXJisIRlj1*OP[A-]A
!'S@'u2kW=()&:BfsItWH.:djDZ#<cJ6e4>HTqV<k16lHG(bni7;UX102Q+2[k=&n`VJm8Xn
dIninBMd6K"B+m`"+^L0r3Hk%'I]Afu^]AYu\hEe^md?'>_M32K&NS+uhLUS@eoq%//FXgK,@g
e!-b/kjcPsD`ij^aN^3Xp@IiYOrC"0<]AWUJ4hMEeW*0)9q:$?H<Q8q6X;aP)f^#,@%[Q#[f$
uSMlbb?lI!;=9-]A-]A]A>B.eVb6V^+G/Hb<g=_4<,uo_t%JqQ+Ugq0qfIPO8!r*HI0u@?m:K%S
Skk1S6WTPrIlh_VFL25tbf/FJ:O#Jo8&?;s9*qL*cB%2DjlqnfPj'IZ.[c_6O;J%EEm#h-UF
2kj^Z:6eK)E+k'Y0KKmc$,NfrhF,SZe,;f#qGN1IT,`>KB5NGL+\NRG_nVp@'PX8YD2L[b5E
Bq2*=ErYe(N#DB7CV&%Zs*(f`?#++aieK%Fo1CPsu`]AqQoJ3rSPJB1DTT"q<l5\W,%'E=m)7
Sjh=/l6rj197756mg?8>lMZIA3<pB[gdb`CB&Ni4'9#@ln?#_n<VO7a[/TI#(id&_a)Kt(Z3
Pq7Cj\/44>]AuNoQ:2b37R9eYbFM7ZCRR16gd+d.1t)4HQe(HgX*,VeH/jZ>b.g-T=6ZI\HM7
TgA#WcaiWrC.e.-ca9gq>HW-`2lQeRuAQN!#3iD&iZ+,6F;,eQmfU)<E9mq(jZ?Bc(a6n^ao
]A0BlS;Zb+.DDLVC,D=;3ssB!j"ni0!23TEK.sITc,POm@)EC[Y71F9U`!sRCVDeWs1K\L:\;
Mp7m\[;0tL@Gb@Yo,`Nt0ta7nc\VoX4ql`oW)_8Z@a_aOq@$Q6YJ`:EhTT7NE1;G>LM)7ZSD
ns;.$WW%iPXX0;,l0qlK/X\\:oPY$&/n4+sQ?5.T_^N@aX3$t"B>U[k^fbZHlcH\\7G;]AW_4
0(hh/c1WV8ODeg/DHM81E5lo<N)=E>,Sk7i<#&0YodDN\CA[RKs7l+%#0R'.VH?;eJ'h]A?4.
7k=f4C(:n2I2:U:;<,[P#PY>_FgUlBB2<rk;FVg:uHRA0?l2M7SA_J4$d1q8\#E]APPhn=A4B
k"KBM'M42ACnA"FuDjC,d0BlHHdDrNK'^F4b*ID(rkPAF07eNro)T)B_LBc!Mn#If/#uqUQ*
)O,'h=[KD8ai@W&k.1W42%F0[*9eG$#>\D&*t+H)-V5_XSDbo$joGV:'aipK'GA+gt+CapcT
UbY&PD"0a*a7RmBefH1`qYE=`G&LX`m0`:3U1E<b&.HZY$P8qu^=58D=a?%Agu&_l2H9!r5`
DMe/&8U;kMLp+`"8#Fg(XSDVP?^X!VT.%SsF_0o^SOQ(X7Ol]AO$qu9hDO[.dG;t/I"M?jR+[
l^@Vi`(e^8b-b0''NR1L$eWiA>3:Y'7U7$=i'\nYK/iH8($58ls!cRaZ4b(Q&B'\NmB;tQ*T
U^S28g./^#0S*MTCC$c'K)P_"#mKI9+>t_V/!ZnGep_;]A!eIacT:m8m&i_#>)SB+a'c"gmBQ
q?h20]AY@k*Y[kN&WM>P</UmDqX]AX)Y"GKZ6g_:HE?*^"\BZ7%]AoY?&4pk`?Hj1*e""7Lj4Ca
;ZI1"9t*q"'u`'f8!,;-Sn*@po_.o$[;WV?@@+"+@%*]A_W;/@l6n6hH'ja[u0AA-&?V2k'&i
"ea(28[<V6.^ILQ/AG!c`%flbG7H@It=75J16ZdV0-W^<B\Dn2Q_Z?'"mO2:%oVCo&11Bdi.
`qe4f7jORL"nBr=tWgjpEVt1XD-obb4<Z,V[?Umo[Eb3KV;qQlB!(J3'^;UJqpW'053'"Z[I
T*oTGgFl4@\Fp@6j[1;LFCJT*H@1IXuQsk=&=`<jTJR)dr#?Ci"PIiZb#dV<lcP'U+=^A'EO
jlI]Aa'E]ARAE2U&tH31L9N*"Dg&mgZA'%@r)B<&%:'OLTR\<DbTO>)J)eV,'Q.B')Ht9Qr=uH
LnOsD2\AR%!+@CTSt2qJO19GY7iYMY-Z\9=^Ai:fnqju7$#`0<,%j)X3(lCoS(ON-;Z1s$\d
WiS8VmHJ")*%D5%F0hOdI8Y[:/3$Z17(J6-:ImRu_$tSj]A4"36![Oge,n4/F:P15C#*J!"r!
F5U,p!d4<H']AD/crrZ9Hgoe-M1VbJT`DF3\.Js6VZkX(qr;@"TMa]A*XD@#'Z$-+MOp8")"O2
3lAgPuP<PbB4hG$ndK?2.;ZLgP1H0<cuA2('aL14=\.H#M?)YA+\=l%*@6)kEoW#n:`<TqkO
Q:io6!A;fY#U-PH<R$(^hBm9W,#+2".j$co9ahQ2t@?5Vg['O::VO<W]A$J=.J0[DU_DiI7RW
bVtgfa=STO`A1f@nDBT*;VNXFm3@68S%N@A[&g>0N85'sO.T^!8fP/k]Am3q:YQ%8VrhFrg\N
3Ik3(19XLSCeCf^c;lJR]AHMKKVGl&3q_!SYdWYVapS$7noU*]A!PC:UGgo4_ht!VTW^SnkRYs
s@W_E@Be3M08XpLko*uc?):W'1(sI$DJL)Rm\?tIe&9KH(:^k:46kT)h-^]ArS4W`u_%^JQN4
0>7fmf#Xmb+6b+F66s&2[Wjl[sGAfXguTEgd;,'V^r9%'17R>>Wh]AlEumMKn!S5[7haXKY*9
+t=FJZ@j>5?"HF9=Y\N4aX7G1U.@r?^$@ui/n450@?).a61k>oa#2Qm`Ih:\9APJ*HT4!ck[
0[bkG9kBF]A[4(M/KXE7tYj=P,N(jLJckPk'G*uQ:ZWU0<Jd+jj>unG8"o6NcJ,EEJ@:ltfA1
Ku+6BCKYdSBfGm+h'XR^AmlRX#MfDiXE,ngNf$9.-N:4*T/s1h-i=OZURr%j2U')rJ$#aAa;
9;nG8Z[pRMUW8?0^I(&a^'e4Y%U91`q#QYMa@=P!oN%pqeV3<;W]AYJ:X-d>Qg$j7h9"Q?I+o
&uJqUWOG=E;[?tZ/eB'iUt2:1iWhpDR5?3o\S14]A#LX^:Qn',k$sUB.@+V/J5Rq\L5;;L#>B
?f,mYq1H%jBPiC7.<)V()keg0,C.m'laA\ZQ!(BBUL!rCEs-YrWS)>of>k3lur^DpSKf90["
hTIkSMYeUa*LQgjTTK#H#7Zf"Rl_NeI<m63kJl4aJnW0!!ohR2,M_:WR_D\#JA0"C7a*gmc_
es9VtGI.c;IDl!!?o_AA3rMQhY.UN+1U2q_7Uf^jj]AI:OrDtQTUhbr;,P68tY:h92`?qMa(g
[>W$86Unl/8l``V(m"jmISYB6'4iq&E)%fRZ<h[n!/^;NCel0hI&imeTfAbMr8c9N!Y1A_Zm
fU2/([7mqb?s![\143_.CZF75%6,&.sAc=pGKJIGBOL!R<b8O-35[a5M.Z]A2e/joOGE`/Ha_
2n)Dh*D)*T/cJ"<Li#3A_5R>JU5kf7>IBD8T)/LPM8e,.,19?-V!@sk&ZGIgL/RbDI]A/R\4*
6:/Vu1RTB3&2Ch/.DaIKY;orS/P;X/F0P0f"leTknDU$IN#)&I!FQ?:!De)-Q^j>I,\4cC>R
l5AEQS<i`iL!KEaW(I<0]AOPp$=Apj0abRYXU4/f5RVh[==*4bPOM_Hd<26>2_40h+qQP'7;;
:s7HF#E)G[BJAcAOj@C2#>6(aD"7R\Poe=0]A1r,hr=^@3I$tDG_it2@-n*kZhOVRD`76270)
M<H3*$$%[@5NYonhjj9+%3^,pTK>>6U"&:SbdKDpuJPGr("ojm2Lq4N8nR=0sZClmIUI3EV5
J9[sHRhe`)\AK=HQIhn9'[*D'7h'(;[u0-B'@RpRdYe[>K>UA3-2]AdZQknkakedTlVnfOaGa
$Fm==mQH\R:QpN$fArEN4ndTRjPFUlA%jaMQ'dC50&H@Yp?E]AJ5saS$2fAu<@p@5e;8C]A<rP
#,,>sgL;-/'VkD"aTG&0UkCK=:IP-+KUnAJA7i,Q&Kmn#rbm4Yr%#Q4o\6"=skD]AVMV<h9@N
T9RS@,B:T3fe$A+"jBV?WZt".%cc?=p'k_E+"3s`^Q8V,I/e2/N#276t"V$kC,Qo(Y@*0BV/
`K>1?FdMY`".Nk*l*pWiOm,_cKH)ANSE'&>juf]Aqs1%9(8niE1r=P5%j0*'W3@?S?n8c:'N_
ilH6!e%#J`/D$C\7A,_=Mjp*$3t),H18akL"ChD**pV+iuNCfUM5;7TJQ+q-Yar('TRSK1f`
*$,78T`)D(_L!2O1r8bIY.ub4i#]AieLst=KLm\8?RQEGXns;>uk"U:gUbf?+,uLI;e&_"4[i
<KKX2Do<P(&G2PW$g)$qlR$(au5>Y(^OOM0aZD5lcoVk$]A-Qn<B7\Ct``Io9+d.lte786om]A
\-4#4o:hF4fU!-3)VE0t[`e.dIlXI]Ar\;rF/gPe+jbu"$1HAF_q/eJIVm/"f,OSN9":qn;[&
,29uAcV9q7UeOk#,Dj&k=Z"31qG?@d!NBX$*TUWqZ%o.qf,*?Y;)8BXQoM5i!>C)'EP9LAM!
+QZ0A&DaW`"+NnL7ekGF.JLdMhEZ%9!JW0/cTeiJV?hAPcH3ds5gkAh$O*uh(3^<A*TJ+?fM
4u;aJ^nQ,K/d+jriV;EbTr&BAEOJrOhXa\0R,Q:l_@flkX,miTE:f2r?#2J(4d]Ac.EH#25-Y
h[@4tZfZS!?fHn7L55/-Qal)?45<LTYPH/&3PDIUU.;V]ACu\!s<%-'OO4LShc",X1>UP]AZ+j
Y9.!,G1qAs+Z0'Upb9"kj[75F<2qoNL25c(3>n&sPo[_pe8>5X4RJMk"ck@cZeNp/a`P'ArL
<jcINfCC5p'F:Uh^tDNGBS^f%+f)K.24&9-:eo#OGPGI,&'^[&*qFJ%ZF;+qgA:X]A+d""OGq
ed74l(W<7JG$96AAp&eK\p*"(QXn4\53**t7Gj%YKF7(&E="e)p<Adi*^I&C@87u\-M<)!tm
]Arkd@@T!-S7#MAFM2i#)aj)HnY0C0.]AYB&c'e,*.H:^n&^AX1L+P@)@`n^n"/5Zh6CP=J)2'
?,PH\qj-"@$Z9!D1LqSBfjMdJ\]AahWAF(ZS&'.!<Q-J:TdbqIp`Ce`s+X"hR#pR8^:E&<\0Z
u<%eKO7dp$C=sYK1YV+*SP&2<-jq]A-_IOg+Q]AfU2l,kt4LQ.kp)g@;_+prTBfm.A!(0L^tDi
Eiu)3Q_#\)dI??3RG52@sesAPF<97*V*n&IAmCWan?G.,]Ae*G\i(3QNq/1b@oF=4n?c@(?Yr
m7)/YrRg/jkGXa-Fiqt4dOjr_.&400#?IKNL/MrT).H8"a:Gs]A'0AVHUjSZedmaLj3$hPppS
rFAa/dP02:p#Pbl4CB#6FF&usf\=EB8:f6c`[?NAbPnmn1'N30$:1lLbor^XYM5^OY/Gtoo'
EYcE2T)TcJ*LmB\1OY0C6]AAN&U_qG:Fu>,VM`**NL3+/p>gYHm@6I>GhTK,FRHaP[C<7mFD8
lQ8Iq=E7$UDfum_'qCTb*$RpE:c`%0:L4WjXMrFFTlh"dnS[)%`>TpbK2m:B[>f`M,raBnA"
b)F!;IP5F4uKD8'#%e#F%(S*s8?4.?5JAIIZJEn!TTVrc5NSg]Aj)$KE(/hoCMVWJ7P5^,8]Au
;5^-FM$qaL7]AFTORr_=gg?+PRib,i/E>5;GqaZ0lbCD7kt92,2]AqmQuEg9?buLPLrm=J*PU`
qrbMaNtg!u`0>@e=&p/,G0j9\G@KUi.@W%5*gY_B^?X8KlMUF7k#m=<1R@<ljdZ(bk+R)9&*
1N2Mrst^.<EMAIL>%U(;tARN%AoAUKj?0^Kgk;aV+YA8Fk?8-\tW]Ag@UACMo
mJ8XD$l[.XUU?+5H5:^ZR023)()HkXu(%QBI%6!(8BBmbXa"31U4f@[e6_WRmo*nh&l@mB+r
crPu3e#Y_Ze'LWH9^tV;&4g03#r)MauC2TO7[;H0si>t6L'njDmnR9)lqk:d"`^jI\^$Q5b=
UZ\g#W;Q[$B1PNmhL7XGXL;kN!c-5<p&M+t;L<T\Y63aO?.%]ALbL;Qs`uG<^%%!)g/L'%U$8
A/V_+2JF&eW4Xi<_=+gP[T`+JDE6ge"^l'U.cYl;oBW6]A2SG>2.fFE=hi3*.i\\+JhpBbZb`
eo(`jQWaqus4r=SMEb&cp/rM@]ATH9djLNiaLG5XPVetPEUg,6-hLBOgQ]A$#;NT_$HAh31r97
[\#K$i<%;,&7h[p&E^()1F+5sYH>9;rs%HN067a2\<2]Anme4Lh-(bA]A1']A=fuTY;`>#69pl#
Y&CV.&a^iIjt>.k<DO#qOfIm/%=!s<-jMZXtS>.>u7S!6.;d4%C<u5fCkQ'^%AVU46^^o$CF
o5qYm]AR^lJcO"SC`_\?NW^q<sn$,rlCi>U6r#;X=%S\eo`#@Q1o'A^$@a-I0h\%Ft)VITtd=
m^/KF-OjRgKWVIB4Kb@,N^\[]AFj1p@<AYitPmR3bIET?hm`'/t?i-kj+ilh4DWh_G1u20JJ5
+6McHWB?H1q)VU6(78'Hm.<CX*@i%[9fKPfPHd7M.#uK;dTIC7d)$:(Jp).U)7+-mJPZ7]AU`
rLMY>oj)6aR!C?u!i.d[?6n1a]A'C!A`hO+WdF5C%m8Eh1B%rqc>VN7"lh9sfF6EW;CQU\.Eb
/R,Bfh(f[n98-d.H)ic3'e(CUhLWmWlXA6-9qta<LgD.OUfI(*MReZha.?gd@%ug-?.?Lofh
V4DZ*Di7Osshg<<BO'1u--_[/=>-3L]AH@se=H@P/kgRRCh]AUm8e$&1J[6;dLLF-bUW,Fs`&1
Yc_-Xc5BeX1Jj@_0*58@c$k+U3+*&)!efTY`-U)i:^n<le\@q@h]AG5rf6STL\]A):\mR3@VH0
gH8B!=IG^&-&S-jdODjAaWrScf+GXF8S!@>egaaMF8U_]A:u*R1\0S-u_SnI-g)1M4.^"Oinh
h=GcaS!'D##E"Cb>/26kbFO99'e:o9I*QF;XVruB?HUudQ,QLHAIi,!7rHYs\;IQb'Ztl%O_
KA%2*4"@KC[c0>X(ShR2qhhli=#u>44%d:SAci`?8cc,>9tN2f1;m@P".Qk/qtuuP/'L]AX[`
F[*%Gl4etB4o12<51I_Z'07"2i`nB26C$-]AokDL6Ne8M=lfOHt&L+NuY[?8IiJJL&%>e#A<M
qu*h!p(!(5I3pk"Y/Fd2h2`XA'[7)*mD#6&b2T'jmEN,L,LM^0C#u-URqalef#D*f5.DsF;J
'bOpmr7.S*<\'I_,-u3lLJgqUAC#0#M5`/:]A)M`qi?18uP[Z=fp063jYZX5?p9VG9EOnOoVP
\7BrJaZ5Y?`G2iI59iVlBHYBf'ZY:.6#[h>'J\-tlfF:534X?rNIq@Q,SSgZ=e\(L_r-?M'4
Z4\Fb.dcrN]AEiFmlLHE<68J^!:o[kW=O"LXcBtRS(I"s='TLMr'2XX6,qm_,iDU5WY@\KiOn
G%,X.:hp^qbLaT\:j>Y?#%)(.TT1>:[rmr4(ngG/?DoDXMF:>_Ib0&`H/24uBP0+l[SW$iGJ
.8o9e_`B3B^N[2%m>"B0QYf*C+Xu&09r(;O63r/)3N6Ar0lB[=%PCJu!/IZ4iXg,4IK&Zg?F
1*UH3en+AF\,2Q)rgZn<0A@id\ID-abB9]A#HP9cD621ah$WA;:nE$&1@7KY`FS[1/lT)G&Z6
3\$4(<htMIR-^Z`IN2&U<.J3C4($La`k\s)R]A9@EpM_>FGeT:H@(ei@Q'=i)Fd?n!`<@C%%j
*jA:iX\ZQ7mK7C1D-rEI[SFs0o#sgGZeqY+fCUJbp,#o93^kuU*jSs(X/<Wi8c)L==(IL1IM
`]AGe1-<("G$-R&sSZSLJ'mi<D>G:&gUOGaT;crs)LBRUtW5@7>1qIAI_,&-s<U6l_+M1q0ja
NJlla*C)%\aXYrol-\"WV6A2S7h&GIg-2Fg!C1M2[G6I?Z/3DLi/Ft2LPM.Q+IE:W)6iqRJ%
-_=_P[/c"&1B-9N[-?fo@+An>K@TE/#je/o?up&Be'qlR^l:.ld2[MdsJO4"@e=lKXS.h,IV
VT;:sV;01$2B7p/KC?uI5Xif^)e]A^U5+>(%EA';#;k*&<uBYs+MqDs'c+IN'Y@_3YrR;?.@N
?D/%/`l68iJHnf-O(aeY`D(F*jsR3m(I<Q?kWiCNY8<KBLZ$X"kJugQNJ=8+CtM\[HER;.bL
l;jZ+JuS6eld]Ai3u'**-(14f!6,-DV5Sl><sjmXZg0.FZggl^UD/ERM-V3"6+A3GZLKN431O
7itZccS@t)D5s,0fr]AGV<_$m6.70ge8`VS=?jheZ\]A)QiF84!#]A&oso2`\uGNeMBpR^WA3YE
*?2H1G0Zr,V,;NXYQscp&mKrj("0CO=["i#CEFgl>#tkroanq5X8dkdR./+.SC!4a(Ud#BW5
UH,d>4lh\:h:9)<[J7!VuJjXUjF.YXDQm)0g&5B_gp0,>u<[9LEFm.@XgqoQVnN&mgZ!+\*g
JA7:W)A@dSmT<VhAK9>mtlr:e<VVckKR"T[XFi(KAHUV]AnN'bZ5aVf.k2Ur>a3G=o>:NN"^\
=SK+MB'*^*[,h'NJ&/"A4UXH7Baqmqd4AK+b4#fe+[`,Q-$TapZU_&IMd%ZEHtJsFs+d<;#:
MEVN[Vc=MHdG&l51KVYG06gFELSgD^lm?r:X1YJB1139XNS+Ur3m-a!GG5)FR$o8dj36mbVR
:V^U)Oi!/5%P,@.MW5c2N>:#en^TBFf&g0.Ttd7[e9L<cW*$7EfrWmToH7b<2o^"5r+e*!$?
\T>>R(A7ErGhPI*#FG%'n%.-%dPahH/>_$?c$/K&0>U/gpO.8a!PBma#(o@$DQpMpgY1f'c/
*tNt);.)\WE-bM[QLuRe\Z6(jOfaS4:b^<3J1s)8QJQ>oG2V;"&@Pg@'^iq5?)UFJ+`BVi1A
o?/@rI*Mh.R#)'=iga=>]Aa1X9%fRr8"<`V4jV\VU3.8!1m]A;u3WnU?=<<c=o>lMf^fTIH2ZI
T\is&r^4]Ars2IiIP(qOnB*[9cQ`5%S41c#k<O"16b[W^1Y@\q1SH=5I0XLZ>96;Pj"j:S)$u
h+Ts1:cKNmR"uU-!>)\$"K!<,e\l344EhSZD)@7!S"-0:s\Y4E\MU4)cjnY*Nlei4:?9dnft
7XS0Ep@UJc)f@!F,3,GmqhpLqc0-!b^!m*="3>Hl49H8e9s2!Ro29O?8GS#u`R^UH'`[jjt6
e]A8Zme)9&8D:;2l^&fW`h;$-X\s4=D9mQq^@eAi&M&:id8ql^<6J@JiDh]A9Qn<AE\.TFi5us
*eGu&m'^=(2r="e>oC-R./I0b0YGMbo=9C=ufJ@Nae4BHB@<hWHu=7(CLI`mpFB5f*AClC84
+^,V5[Il?V1a."E+*]A>ocSE"lJ(ps6GX)<EMAIL>+#c<qNl(IM%4B`oDTF"TSqYo2<i'BFa
ODiKCbmXAn0,_LBP?>bp=jaUBge/GdN/7`GVb1Qf&r!8c=Q;9f_c9UL?.=#Ih>\[S%Mk$D=L
JmOq!Q^RIaW%A$KWEf[:u'*1N=FG+n9HAjaGHm/['maCXZn>_LD0h8j0O,l[!FUSTQ$I<TL6
nG4rX[\?$5[5?plamSKAa96B"*g$UoVjdKJbmVj`r[Ja*SI/3Z<'8W,./*<H<1VH?;dZ,H,3
GnCTWW/K2pEo;l<@o^/GO^3'\plBNdN))872\`h#@\aqefM9roin_m4O.;?W@/a"Y.cUX-=?
+jRFA,5Rhb(N'LQb:U3@q$dQ8qT1g"`\>G;NR+0k:9a"p%&PN]AXO*S%I;63#2ie,MW#u'F'(
XWS<rh+tWjdY`/FKF^<d,_E>^\(Rd5rZZp:JU'D6>6%IM-kcQTdF#YM,6=<hn[#Aqd!@gV3c
hVs%<<O^GfX;(9sE%j*u'ITL3Qn]A0/'q<3q@VXgDRK*h9rdE&kqrJ%:W"ni-)nN:Y@>6&O77
jG%WdI&<o*Y#K@*1/Zu@5T?cMI\,/?e,h0#ORF-o80K*gjrr8r1"g)mBboQnSo"G&RjuGG$:
qP(9m6U>C:00L%XC4YN0#cTB&65kEH\Z$">m_OeXd@c%h7^\A@Gb,G)B-#-WpRI:\?@\aQT?
hWYj1*c<ms?A'W&nHoUO2,Rr<hJe6rB&`=TMb>s^S$aBKb4UFgbsD4rX51AX=NC44$NO<gbl
Dl:p/pQMlg!c$A78Nb6tlktT;]Ai'e.>UQW%!d2pj+0?$Ksiuj)JlPrUmkjecNL]A7Co4O.>Dh
On!`gH?i((>iMk^j2M;!mm8S*82C4biPM`eJk^nmTrq0@AI^PEJ/o"p@0::j>Mt/!6HO?g7i
>A%;D#+gfc,n^.p*)D,a,Mse.J6!<gED17)1W^H(8:j=4TkrMhYRC;!\L-iC"@U8bh*<`%20
.6$b+DT.nJpLcV=SLa"\9XCk@K5!]A#,R\@cU6?Y2fqBRsIc=jl,oH1#&9.K*El&u7iMk&%O;
op6,k72Q$S9AsW9`O.%FJO=8sqJNr_[GF?]A7@\eP)L+>=D]A;nb;uHR,;$igd<4q#mBMhP(dX
Gu9X_'h2Q,Q=<=qcRaL@7$Ve9h(R)WbY\bco1F;%mb*A)H#:L+c\2g1N6OS!n6jYH=2.:d$g
7Hi6ST+d5)Mg*Z7(rNl1RpkpXL&:>WO'pXpt;/*g!@prd3U^:E;Pe;[<R$.SnIOM%R.``-Fm
&n\"1tfHl`eoLY)R?EE,hZUoH@ZA,S"M(Cqp/F2p-4kXnKnGS\9@IL=%R+[guZW"EoC@a`%m
@gpSC#DE0jh5?WWQ)0p]AKkJM+mCVqP4):7]AhmQb@*rV&&RW"f&#Ig=bO>@]AEd\dB-?\fUUa7
XQ-+Xb/PnIC]A0CL*g2*d3U0mu&caa&E\0ZF_N+]A$IFPc8?STK_*S*Lr)c`E0="j#<8;6%1'U
fF5jNV#)IE'4?9s9$@g`%"EU,:\(`3hZ>`a#'E4WO3qiIt;uB>/g*D]AMd(S<ksgLtLA23gNi
RX55jm$^gc'Wc3>9oGXSj?s;$7*qcE.:Ck;_-bs=a*Ae'=4I@VR=)(a[SU`6STj66*r:dY3+
#CkL#5I8s:VEZ?0Lj&ZR5Oo'P,u&rpM%1r";RID5^)Adl^__s36Kqi8X(SI'$\Q#[(5!@7#t
&[LNkA`6q4&W108:H7N,Pd6@`.*c^;+VN\mUokT;"b5>-6rUT.D5l"q%Rju74miYcnoYDYV*
>32Kj'P^0*"HUC=>\lI5^Q-mYcGmA:NJ;:-NVN,km%d-1,ml&&'bfgo;o*,77?=!<6eGh!fL
(RiG.jurn^]A9tb(WRdMP#M9I.VbYS-'Ho8k]A:4CV(Otbf;&]A`5Na8S#W*iDkCc;PoH=%7X95
E)\_j9`d__7EJ7W]AM&b<0-+r]AGEECS]AoL&7A[9i..7%9n">3D9^nW/8Gm7W%!qT<W+.jURU^
&HKo.U1V-/&L-$^?6W@M&kbH:#7n%XlWtRXl'.)Lu/p%=&AJKace%AaB2Eka,+jU8_N(oTum
?E>fQVV5I'o5AU1Z"!S<URG[@:F0Y9/uT7/q2S?(bVcU(+g+F0c3fc"R-kPID&Jan,Z'!`$.
:?^mg\Iqp[)gcs.e+NYaF$ceQqm&:J7dnuN<g):)CW&XTYLra$M$DE2a53HT]Ao4V5NTCr<[/
;apL<L?[B.>fDoflE\[dSTW?o*;ZeL\.YPJ`2i.8O1G$j@G(%ipUETSrAGf40TC8t+YNCQ[)
&Ma*\#mb\&OUB9Q^_8eWk4YlWSLYPfn42!/Hjl4Grfk=aA>`-[!qRa@c=l"?4/P29Nln82+>
b^nk]AB#qi5GCo&dg0<OPtCK)Q/UPR8P<eh).[U`q"XK;.15Vh02^\SMDV[G?qaThfsHSerGA
NN]AmmnVkL.:]A1!jSRk!FRDoB4G"?YH\TT64!?83C_"j53CSXmOK%DfRM=.+Qf;A9)PVr/a1g
\;6;`VIlolEKEW#P+./tT-%`I,B3$M*j[;]AJ@qa<-\$%,l@[p=!4q[1cYn\an$SO!:DlVil2
n!DK1sddNJ:D8.oWXF7_(U>B+#=[q!iE4+,VRq&-f#_e-A$8UX*1pH%GG:YuTT>a,^rh;TZ'
9->Y46:gH"-6L6nt/H`UkLi<($<)7c8pi4"F[a:U?@J(.b75Vb&b+G*n)#$YS#C7DK'e)r3\
gmc,kmo\_F_+2hdD=n8aUG=TFA[A"cbcn]A7Z5]Ad,%ck0')K$O2ECit*m4bh[>G1gCAi1AcgV
dE[lCTa5p7b/o@>YH5FF*;C_$]A>%LB;X6mi<9-<SP_ZZuMt5JEQE$HgR(?%UL(q8r\nMJ)tF
f^^$O`nh0hh<4ehS%0Q96.gKCcr+800Zn-^TOW`^iMB0:`rKT)U(p$fk&4;WHB4an'fi.PWa
dd``N2@=NL`JIOb+fi-VZ1#WpqA$l'!L=f#@;H-FZ7rKD6,uZml@"oP(0$OOBr8go>(QFF$4
b3+nQKn!9tTcX"NSS0"s\P"h/[RiC[Tn')BJWIM0ce0[eSP6@uNO;0EG3gomT]Akh:Ni+/_\N
3`:b1/rZA6&j"5F>I)[!(UJM48Y8J`).+@J4:27?ejYUY;9tBXn-\N0S*''701n-?4]AiuekA
K%LYeQN6<_0h1d"d<pSi\[]AGYU-F3tHRL]APB4(DjT!D1[rPn:BT15^e!G:?b.+",D%1c&Tub
K19l,&&o#ZSIN7-"SnbP_Nf7ngUSIA.kb@[.GQbSs$jh!o`fbZ=N9Sm(?fd1;U:ga"5<@a5=
1\`e>>V"PWlf.Pb#NKs#%!6O[.^@*mniS7NJ<gFo2)GdY$$L<L)Nc`(BM79A3C)\5J@bI-YA
&4L#$/\q!-N'YesDf)Z3g```fg<8E=_M'1=n2g2'TdlAWB&!)-p8Q^>&[I!alo\phcKqNNp*
Sf6?<bOj$[RU$oNtEZeN4"NNXOi,:.hf_qO9M-41kioH0qH\sO]AXhse%bI7X_E]A(B%nLW+lC
Hk_'B%DGj:@H7gS:b,S5H7022D/HI2"DU;7/BpmkbBYcC-<3kMJ/m^sP\LAg@$d:V1?#/7?\
<7ePV-.s2mZn9aBh]An^<=gskb8PB77.=:bqJHi?VB.hH'!>CF0>8%X*N!qAqEgn+`VamHC:H
h;n$]ATPaqkBS86lH3<L.@,r!,K:$j.(S7"3u45igt\r*NGYdAnqH_U>2?:\di;RM?KsRFM2e
$I0NJiHJr2![1+^%WaZ%d?5_<Zj)be4Wa^h/4=I;a4@`Ie[W?D=q+^>6\5e#HgV[dZHFn;Mo
q81\fbU+&#dc,c9G$XS"]AB<12Q!B;?gOWdQQpm*B0i;1J_nEEVUX^r0KR0&@7t&c-hBkZX'?
"XnlT7f$qTgQ3Z44@e=%LhQ9=g7r')gJTC,&JZ2gVSok%9UEkR6Dm?ZPM[6,!2iY![#HiSCY
L/feD;5JPYXI$/1W^Dm\@h.1F8SX&`hd7?m(Xf+c0XN7WX&"dB/B]AuC.!6drV#55F#;6*NDU
1@]AWddcUf]A?d@K:3(@Q\T[&7`NnDpWAb\`,UlPE+:*!H>J7Qc2&!EA4Ee,&,0Q\lN!AX.N+"
9&ghI*#Y$8g=^K/XE7AL',,oau>3d`GRoYV_cb)Mtg-M\kVs"*0,:/S[Z3%?@%0_]A>_QCMq^
p0cZ;t/(KNO\=&_jp.Q7QIt*JApA_JCjUe'=o:-.E\>#!?Hab$K*kns!I]AYA1&\j84E<m.<R
(a!oS)Y"hT:>QT:]A1Wf\r`(oh5hCD<I"k>dPfaPJ=^$68l-'lrTQ&(7h8-WER`CY("[d%chh
k,TBZg9FjT.U`fJB*>$Ts)8+*rmMP-VkF2V!`HD3nAl-#\N/@>I8d@54==IW_NNU(#:_?l7`
ep?-4RjEI,,]AMZPX2M*1Z2m`>A>:"/Ha53gV:6N/\c=n7k9W)8Of$@UKu\#eFc;/Q]A3@0nb$
&5N4DdE#!Np+:>[$X<MXZ*j-A,IY1R9ht)k15$$j#.7pg'Gl=NT/bj\f;[q.>(rF2hr2/#I?
1b%k]A=^*qpkc"`s2[#shD6%qr"bWDJl,m8BkeadXq6(fOMDbR9*-,`\Kq_C=eQIBbOL0VRoF
*Hp,U0*`@+!a(jRN;3SJ&Og^YQp.W<@$=OK;h;C&CNWJapFC"c.k`feM]A0Y[70c"m1*a-S(?
Y-cXL3GGudTKMWY'bs/cZ2^d(oTkLDTMOPmN?\5SesE9d+!(tiJQOfl=L.UkP+1h9"AI?MVU
PZ:rN*Tso]A2t1J<('#?IX-$%*K*?Q!t71RW9$7ASl:1C#Z(9,9LAT()cQ;mbN5eXU*R_*>(3
57H&XAimCi?B*7b"-OU3aUn]AS\$u1HtX@Z($kGM&2h15l]Ac`E84l"8D:]AAk%tIn[W$6fC"19
TngQ<M,pjEI?llY=LI&c\'-4`]A9JA^Y6;Pnep(i59EO9%"[m%DGOnuH>A,n8:RAU!G#7IY#P
.MrZRa)OE[ogC'IMF=B\J\kTN8=_,<mpi\eA?ON>\(/m!Y.IY-d?!2_K?dR15*qDgK?CM[.i
0$sj#!Qq*PSS=kT\k)2+H34/AHP+8&n^r_b\?)8=XJ_Cr7EY8BKH/\a(HU#k%e+=o.d,.W-p
[BW-T@*(kYO&.,(7DK;4)P0!leXAb3Ph^.,FXpOfaIL><.\:bLt%s#Cg&Vi4PAEbg#aT@-^p
HiXZu#e)mP:D^l<#`(8I3nh!,Ial4kIe?gZ$C(pfDjrQ=^MQt<AS7h?@IEha<(lM@/=L]Aa'M
pFN@;cb>c\in#eZ1s9Z6n%C=?C@L+)Gtem>pZ!F5YfcWUD^\Ra@'HO4U=JI+JK!RQL@Kp2)r
%_i]A,eJe^i:uI#W"qkuCUN`V<lmkDu+A:Q,aO-/cB,os4REd1?BK]A!5hO\N=7?C?3jDUW8CP
jV;QG?JOhu=5A2e-h')%2p+p*cIV<6A5fuTS:UuY<EI<$ZkU*$ht)5'?1paLif!:oja^=`Y_
ah3`gfiE(i1^*Zg5\8K1<&'ps.CEYdUam@u"B\ZYlZ(MRjTfiKl!h[]A@+6]AE*h7=Ob@-3%3$
-##TI-I<aE/`Y.@nNq[$T@2!M(F"Z,:dc%ptRjA)4&4s*'F2r(^g%B`ZZHBE"ZC&?>Z7f*@p
?"]APdr7(g@)6[IjTcR)ZUEY'<`D8:nJYL[JD:-Ze#YNKj@Ff,Z[XK*<iV_Lhj.9]AXXXfD&f2
U]A8NXkC`7.FRIt?h*R2*0=[YgXQkJ=0Ni2OGf#sKH&k1b1a9P[Qg9:8.C$hjbsd7g)03#Mb)
'/Z4Hg1N:ic-mD58D\('RDR)fDcZ&OiF=i)Vo%&9.Jn1ks3%'_Q_/gp]AVZshoS?Vt_M3-<+9
WY'9q&'n<jdg,!804t(`N-_#0j=4f$JdE5=AWX::,EZi"M)j7.(g.Tk[(VeFGRC!S+=,LNIP
sMMgY(dKCm\Up2u="BOEMdL1+M*3qnVoZb+?X4rQ.V>-*?D6XUDY`k89a@P,8]ApTON8OL@\i
_W=q>\&t6rK>&OGR5pFF#,ERH9l8XbXrqYYNMb1W2)gJfD3n7d*U#71/9chQ_9VAg17_Oi=i
'^'U8is1E4,*dq&?1772*r5Yu'i1"!-##;:bDS!uV%V`)Ni*%-cHqA"B%8HfbekIRgY5/bj-
Dt<Mabe@b/Fh-mIYsML<[,sPrOf184QT*8+f3hB-`=hsG@cQIV^oc+J:tdAnZ,<-NA4bh+F3
#n$.=@]A527_;eB"(FA%b-VI<(/U&X>EJaa_)&-bA>PsY18IO5A+jI%Nm&,?^J3CE,&#)ISCF
?Uf`:3p+4_23&&PjjY^k]AQ*P[/cItDNqIV-I+k>P%K0e('XJjX]A-W\nDWhdfAnB=\fisEC0V
`uP56:rfq&e?]ArfX:WrA%kk=d:n+]ApXFG!`;HGSfXX'M^u`<k[IM44DpJ9VG3m&I5*RG"_TX
AmM%iX;_\*a]AEJLj_LV7*3im;=)b-e1[KDo#+:+(NH`?Tik."aN?M1@Bt,-(5eS6h98.Q3Jc
N6YLlgS5%8C`GZK4cO,'ZH$S7W+]A.p3_<>8W8fmXZ'\WQ:VmrU)TJ4[;_ll)m!Thdh$>n+]Ar
AfD=I)@CI9B?9,V_MH'-;WX'QTYF!^m-sY-+qh>'(T$iRF*n7Id!dT:=lNi.%K;?=]A3qjam'
iRaTX4\Q#miUc:u*lA2Z?Au=#6C;^\/!uafqq']A;a@W_T#r0[29<E9OF.5e*`k1eXm-S!!$g
AUb]AJV[5'\EDf?L%I[2nNW[Y[oQ13[)9/Y]AI%@I\qSI\3u([P,Dpu&J9sBe.>3U6K[._d9M(
VM(1jqd]A/qM?^nS9D+ltTmFoqLc.4ePk@o7(b&emX9+!2Qi_\tBss6RhKVk#>(R6/+dkh05q
\W#U7a9]AT*kS?<,UcTZPRNA4!%sdc(6O*T,5B^Y!-!mkl?mGn0NIjR$%A9K`8(@X@$B0Go`f
2QQgH+68PV0=%#pcF@.VkG##1i]AgLAUd`Isn;,L0tmY-\^A2,t6*X!8nR]A\Ae_(S3ee@4T3\
bL;5nD3I0=:HM]A<>21*NsC=5hargZM+\UuU&*9K>-p+cuKo';YC,.jkj4#9lb7]AW_0o<,o_f
kXG7!D&c"Is$2[QTts2gZ0AA?;!J?COc6GdjKJk!4+MU!=_4i_e%-rZ[oGCRpUT@a@K+qeI7
W<-[kO:LX^O%9\iTeQT:DMedDI?h1QFC+r(q\HB5cAm&hPF;[V716*DX.;[;0>#P2]A5IEN4>
5<.D!6;1%f?%"HMn-bA.[Lr^;E4UJ0*3Y*MPk82L^KS.GMbfT_T#mFMi]A@!Y"U:Qge4fDo\`
]Ae`Tj52lX]A/W-Y;b&_1uMQY3_%[Co]ADLZEJR1JrR"9,M`K:uN$Xg_B"*B-hCW`_D:0&QK+X*
3+q6hZ9N8Z<RZf/XJTU8t!nVQ=0:_Zt#Q.@2R/"#/s6F05iJ.R'dj(81?:0,;*Zs=,I[N81K
/cm=J<!X;.i.L(*94>8aV5L%#em9Z7_KT.Q3h^GUBe9=^Tk7H9652sg_(A:%+Qsmd$4^i+p5
&HC8n61.#uiYnFLPk_KL_obPs)IToDq*ib2#NYc;o#[a#Ml0(Idc68-4.61u-\@Io_t&iV[B
iPP!4fh+baK!)W`?iOqno:0A`M]A,Y*Ti16,qth,fW4U&iZEa[u?deV4mge-+D1"gkDSKR>pZ
U/.it1?d3+T+I:$Aro)=34Tpk7PKlF'XK_l&e4`G(hfa1#tGH<G@OM6X!tNr[<1m&T_6K=nb
#01AV8(oLRY5a8Prf2G@'q%'$L#cX#hrT5Ah$U1&gj"'1s:00K#S"IFkiPUF>'Ch,!a!F5,d
XJ0MSNCSDfJLri#&at4]A5g*0mH5<urp0:1eI#.6O-cATPKa+nS9_RN(6`7,?.`8uLaBOgcA9
RpX3$Z1PVk:.L1h8]A;N\ZAK]AO5d3Mi-Q_!IA/J[\>97KY:)i0OWBqQdAQq@GEJA48akoA$D]A
IahbfNP8p^NbA\$VT>FWj'/!T!SR@!ffq94k]AO@0HZQu,>94WfR19.jI]A9mdEqc^?5VG-d&&
UsUL3\Z`7W/)rW=n,uOot,D8[RQo<pOfKQ74Rh1n3LB*'`Y,WlPp=B,Z$t\V/t+5QfJU8]A1u
:JM;5UD4XjY42+K/"F?UaUt>On`[n(+O3)(@%2+T0YPb*6WHEXW1`#.TQ+(NYXE*XLj*Z<u.
52u-2:Ht<q82hS\=B^+Ts:eYmC[\1KF5bVC1&8M?H6nANS<pq;B4Z/VGD<o-Ve_/4X[NYcCo
C@7pDGXe<b9uB]AJr;s'M*6IkQPLR"C2%No#*emEO5EQSrdRX[EC([Pl?\2b2"78TiX(Oe,Kq
F.,hP]AeO^9=ltDBl%E"rp$Ke7m2H-NS8\*]ASIN*kmRoW_h]AKT[;S%6l``%%"PZVfsoaRfVBb
r*u-:%DJdghRN#k&1OH$ND8<N2e8,88Q'G(KmhIh%.8FLM>7^j?i`7aC\n0:`N]AfeB"2a/P1
l6Q0]A)!E30gRetPR,!XMe1"#kO!D[2:[;d!]AaR.0Oo4fNAK@OYr3/mf0Q+f#Ma:C'\0JM`jq
P?UMgdl4)<6LPT>Vo-.m3SlMl(?Hb3P>'lR@o]A`D91R2^!4160onU),Lqb./k9Hf#B+@lf*7
:qbCnLEqn!7M8%=*`/klCqSX+UkXRdAaCa`f^8QZ?Oj`K=!'XDCsBhG0&Ms"%&fj@?Ug\FP5
1Zh^QWe^-lMBO:-Z6AGJPSC):!)4pU7+KaF#+O7bOH>Pb+`QLjA7Zn(iDj6c0Ns^OCU`rMC;
NB\6q\EC1D+q@8pPKLQ*]AIaV*CL4A66+7D<KBBJ.%oH(IpK_rtlj@]As,T\7;4`0MVm\m=.7.
cR^F$uaAQZ(5#"l*\$[5rC`V9ch>ZXpHX]A_Bgs5IIrSYMAhokC/BLuW2V/!u9U:4(T\rU[^G
=MAR47LY=n><iJJ2"_"4./Oj,J5ljcIAXkRd5%qU=s3Tr`CUr1iQj$KW%<'&6(F?P8_\el%+
ZT*_PHFQ4TY^,j2e+pY*46@\*bTH&H.BKnUtGkupg6MJ,J\5qIfEH]A^p803?C_rCCB]AL4?6o
79BFlj*jXg#gecVSG4_ag8?H%@:K$.iZ$0E:DtK8/oRK1F,SIPa3!gO?r:T57\n&Z;D[@c=K
U3Q88)*>VkdA[i#`!@*6Z^jWF[7)#5*!PXl@WsXerdq8Ka42\H-.FSYF>]Ai?Ht1,1DhfY^AR
mi$dts&d[*`oO'Ve[1sfJ2$U,;E(9p=Obh`H5fc3JCT8s:llA@!M*Z@R<+;0o)DX/-nFLLd>
V4+T\Ec92T]A#W8$OXY]Am2C?l#025od_>bk#Xf%MA*Lfc?h8;CnYpS0j3X.?:<RBkqh@AA3h=
\X4;!a!?>_t*cqgaY?*J^XpiB8NeV?r>5R^oZr0nWrPNAB-:XGk1E)^/r\*3Np43T299DX4l
m9%Y8emGMYM?9Xi`bO[;aNFP`=iZN5[LAqN2KO"b&W:OoEBYh@Ah#^kC=uQV]AB&DT>-f$U_f
)pFLQEg&.B2*DK%[Ta6g[u>%.12F'#$CgB\At$K+@=lehB0OUT,>gW'2>OiAksGPZ6o"&suu
6P.EpIG:gT.nVN&r^&R."e_q"R%5_2j+$\j(Q>f/br]A5f_[X*jLj?N6#Ol43Zmd*SG<=jr.(
iN-(qChjC\8S`1^tbF[juY/OopPd4n_ec?r5@c/n;:qj9oiKP1a6UNaCZ]AUGY-mB0lhY+dOZ
k]A;sOn6GG.Pa"VfC.:*Zjtk2!,gaE+=-<Nje!ZWmOSig4(lHBZ%iBYZKE$B^ircbL)0WXO?!
3]Ah^'fS>'DaUcn<*Vp7OH&&0Xp!7d7=PJiTVa_DRWEU=.''Yq[L,>Cm_<G(QHl&gRNs-EU&r
X'$"IoPWMXV]Aso3X`7e:2CnX16Vc3k0Mlnk(bo.I=7N=U"dk@-To0U#`tGra4ls?Gi89?G0O
CC(OYK<*1F>*U.XhLqtu'I93+X)b_E:D5)!_7eefoS;c31reZUBS"g%k:-U\ZXC9GL/d/8i#
m8C>Yt(FApObcK*9YKnj2"E>ei<oi<;,sHQ>L/-Z$[%i\XqrCQZ[OgRKhQp15AmSi.LE%5OZ
EY'T3tlP2Gr>gI\Z<7fpf`n8X4I_l(RT.(Ze'!Djulg:'\+aR6r"18[FZ<8aU"UI^X=c8njQ
69oe9+sYi)LLHp7-pJL@Z>q<b9(U,RWco_5_$D&lCbKB;[rY*&NlK:G4='Qa=H;(L^&?>RPA
hRiQHi(R'ZU0p]A6aJ>)AN7?#6[,a`T]A6J"H1V5E)0kZL5_sQMD(e_%n<012GP5tXU.<3.*Hl
>ObYtI`fn$L/&&8>bSmUS1N&lg.M9rT<e)Y1#'.EF(InoeVcjX!(1.b0QS1RZCFH:p*YV<]Af
A95O+1JY6UoE]ANp7a^,o6XZ]AVP:cOSAO!:W47FV$b5'b,GR:]A9/8W;3&RGrlLpN&55"diSS^
OMl%)qec+j69W2=Yjo?X!9P@6Up%jIH)c\d[$M,@UL+@VUAYYB0,SkFJ-PG13FR:I>SBf>e\
!"o>-]AV=gJbQbbHGMh)(kt\,fToi6RC?3jNE[EUe"+?N'n#\ei:@lWS-hX<%j#*EF=(sDcn(
$30VO84`Q[DA+.N+R0,1U.t[^J&PPo'#M['_<O!0<ub`WQ_mrPR^Nb9]Akr>q<H75<jY`0?JW
c<D6lVMJ!*920ug%,D[4']A['?O/LB;sVEI*e.Gs[T$m*hh/OahGBFoQD;7shH!QJV_]AW<KD1
gMHZ$OnoYH)LT>EFM7aIEer)Fr4841,I\\KM*A6ek`=P*GKq2%.9CbI2:_5l+[jpI=7le5\q
GVEBle'a2LKc=b7]AM>Bh@qRbcN;_'0O1/J"[l3Ot$d`_[Sm$X&TSCT2@c+68MS3\(\_:02O?
<Ss>+%34N_L/3`D1!s!qg(_7Wf2"M(9E]AhQaa2jE\RI)m)sOb'#H#'-i_qK]A/`G*P[@@@3ou
QCP2ORt!)^QBtmmqM5SNO$0I.p"srZa9[Who'TV.u8$!a)gK.!0TN3\[*ZlL<i9/LG/6YD"T
VHp0RiNq_I0pUq4MZr'r`qId.OYd1Bs7&p&m^,$Rb/E^Qn@gPl_kaKLe><,doDcMY\liitr#
(!0]AY3B6Xi=b>&Nk`GT8bZmdT!REJ&a(ZGG&:;#\0bX(<04lWcBWdpa5)%^`uX%+1CFYfq6/
t0PVRl@7+ReraFMKYf'L(F+Fk$PocjR(o-A-s&0j0%aK(Q#`Y`"Tn%ph02\mdkN!8*JmrZ>/
h`A<(h':)/UXC?`@jqd;]AKTn9'K7bgkKF"Cf5,j1j$^+K]AjWVGG2Qlt%NUG2:SXP[Q%R@n9B
2Cj".fQ!DqPGsg"uF6&!J!Ki7o)KB@`^C4UT<W_g2\"F%/;OkF/>`JVQ$`!ETD+L=q*reYY!
&jjOI<r0OV0I=1f-@>q`8Cu\Uefro+h0>&.A+JD)Z+-JqHAlEHtR]AnKqq@9c5B'(LRO^7*2l
>Ae(U?Nd[C$4kqYHtr7.bd!CZRM<Qd!.RCAJ5#mg,JW<Us_DdhjU!KS?d!01F7/^RrQkWBb>
KbEk+n*fj\ROG$QFm;H"53aqh<_@To;MUe/_ULe<Y3OM":cR[CXY,%+Q:&=aFFX2mH$gpt[Z
E9D@mikt*T50q.<SF:X8S4DLm\cAfcg>[4&SXk(M/U4-kPpH:3g.L`AG_qth%.4aoc,hC%BL
Q,j9lX,0N1BUac]Aet;o9u#J(C6I(?b0T/!gWB4ji+.;?+a:@3B]Ag4)8/hinfo1UrtoTu4B1^
igV;qJ&W1(Ub;hj%=IcZ@HbqbT(H=!YGZFDAQ6g$_V%('K%XpLJ`f7&M1YsiLp%@\IU3kRm1
dZ_)P_T\/p$,n0fhcU$6/6/?\laDEo3@Q&:YXD)*:SN`9W+%W%VGLYf[eB)k9ZgJV!ALQ<+C
)FRB^9U9&!tr$A@s<%=/HT3s<q@^[t;81qe#Up9$:_4gsu$<P#IJhZm$(J9$2i`C;!XUPkfk
$pLfq;d8Yfa3(,1WSPrY(DSiX?8LbdH_jfq]AdTJUYM?@$U+pZ[)kZSO9Nl)4Z94BJiL\rdnV
&Qh]AB,<Pl4gc0D>L^Vk94/;:0[<ZbDpsJ^+o(F_YgUM9KKWi7ld??W&&FR&g]AFc27n*+b3rC
6Rl&4!I&D8H"?T)4;2cU&bnltpNus9q>M#RCOX!f?%Np5-S%"fWd'D+FND>%0W2Do[G$@[ZS
J;k]A@<7R`]A%)]A5]A,dY\B)ct8P+*SoDTT)nTCmb7Ej8'f'h.*&U"a5,*-3)qSt=prAMr-#l3%
0BM%)@O9:3MtalI]AcRd,Z'OCj;kfJ3iDg:4i/oF-:Kp=+q>H]AH.%CU+ga)'f.O3ZSm4Qmt6V
44nSk(cq2D0XNgM.8EQ"Es>,m+N=nM]AEoD*p"KXO-61&c`Zoc3lrs;&Ir&BN#_ku==eu$BVH
q2s?J4/TF0N2id^rSpMok7X\Vu]A%X49'EcY.!?:p#RiD^pLmLSL%URo2AQU:cNJ7RG(")MXo
`Uiheo7WQpJX"TkH4dBeD=Y2mtah^J*fQ:h]A@1R@i0j&Yhpf?(mdX!:F/`.;F=1%5p[[R/oJ
Z=3R(ql1IWl2L/(mjSfG#bCh:3I=M'T5n*#nd?Gnc;8:'-E%njS69knuKp=(7UP7W>f'DE$M
pVSlp5p'p<r0?nc`Aibi&m0Fi8:4*.R,P9fFXNCt0=7Vt_P"u5t,HjLa)g&a/:F/o,(pLhf`
bg;:<3npm'NgbA_NZpZ3NkCmFjbHGLGg;Mfr;f')m+k!8EkDr515r[rr4ug&I8p0!SH6<gh6
AeGkU`^-^Si)=:/YVoMG^EFCW9R,7_DJ\T-8;c=H0e$l?AT^+YjF)p5sH[:/$SZl\Eb#/*\Z
Y:$IE`mOY,.,ga'eAU^?ZAV]AWkAkOJ*Uin1=0W@.sS\5Yql'q]AG0QX#CTB-hHpA@2MrReN!N
h3Y!9<a?N]A(i\Qrl?\T)1I?+R(q-q=[#lu;,aM&N?CRl>K;G9GF!E=9.PD8EViMBQ&;t.Ohh
)tG?E^aJ(V;.n^dbQ4u]A`=\]AAoCf1,J7%/''LR>Mp<GM3Nu>?Y/tc<fU!7ME*prjH9n)Ke8E
S0C5)R5s58($`Yk)PTMll&]AE%"hPjq7UBuM=Z=!?^"JJW+=j7#Mop\:Xt0\g8i,[BQNS"^"D
]Au$k/lH]Ab[h(6(P+&6S,JJ`@q?Ph+G*qLBtqLM42J+arR\HXiom#<%]ATc.h$>1g`GJaF^;U^
bTh"s`C!TeL'<+jqTS&t%Qhrq+CK+MuY_h'#MU6%[\%Udf%RGoCI<O\T7i,mFgRj-IdL0O;1
b\Qc==O8"6>TbBg`e7d9P',[KpV*#bUfdn>D4>Kf:TMpOuD2%D^R]AZT2']A"olYc!c>)Eo.IQ
]Aip?3]A7.;F;hU?*M9W!V4!MaRu83SQ[f,IVGZ''!<HrOKBLDD!LFB\#*j$8S;VHWcnAFALq7
bb_r"(du84pLdQ]AiDdJ&P=`BGTWO\1YNCYh$u%U9Z2S>oT?E&3RJAjE\g$57Rl^J%)m+qO3R
hfU6PS5CHA3Lr@og4Dqn\&`(9F&OBQ)98h^#^a-6u5^lk2.n(^!C9qF/s$7?"<9XK(+hCg3D
q5qaq4`@V=rSUR\RTk:5]AjGo3Q.(1OP5QM@meTgSe;=XS$%4:`6Lr2SV<T,A6U_cTbq==k/g
Z:_OQt%OEY6n+Ugt>@S$HaW4RMMh=8LGLW!N]AB?Nh5-3SS-6(Z`&d$:*9>:l,N>'rGL':3f6
_!+*uk,\.U=E1bk&6nQ>tOT9BCK5MIBg;%5>qi]A[PRMDL'S3!<CC%WDH89gl8"O)3Q8CCs7N
T6Y@d8l"GUGT2t&1Gg)2Qlm!n7h\)1L$m4*(0(?6As;*_YJ7Pt@9C0!#fpmq%H=lr*5UCi-2
ZY:o9u>F'/+_uX<ik?F>snoKJrL(f&B1Whb;UGgJ>;^oGLf3h<nH?bD1Hr=$L4mdjtA#qN96
NhY]AsSFhEN$CtM62$Lug.L8i_Sm*66@>0^\m7/*IGo$dfG&h]A!OEZUjKq["7F.ScE1ho(tV1
"qric]As,%!Y;'?,Zu:RSiTuZSRd6nmnT!&g3B=S=QNVL(>IDM.M$V,)Z'V]A)uF^-eEqBg]AmG
aumA&:hjoG5X_sE'cXRd__</$8d`]A?!G;C0U!V#<`_pBgE7DW;<#fXeK*00""58cJ^bBD`_'
nk"LckEdWK"mu_3ZrgASh^A__X/^33)Cqkk(tQ#jpp>dP_V?)Z`qK4\_W8Uc5_W_t'ed,]A&h
MmB=0<Wc+::BS+IoAK4HJhloXM1d?$+f50ZS;ro+l_Uh2[c%2T-\%Qkl9A]A-[uc/*2B+JtRm
5"'%QB>uWuBk[M+5\bJoWoCH*\UkQp<-4`B`jj(TpD!P9&IEc?aB7Xa4EBd2XjNN>iNX'8M)
46]A=4OISppc-H;/I/I$)>%D##MOnK/EidZ)LZ:d,IhdPQ\;.Q+S(sTP3io$7aeuHfnC5)]A@m
R'[i9L[&-=dLBl1fK;t;]A!Bt@1d@G'&UgnQ'uH]Asn1[%K7hTfr[(Oge`XnA3le9%BnsK>2]A`
j'MFY@W@e+QtEN38r421(Sne6)J\)7i(gB1o(o/9Q06KKe=XjqGP-[WJO'SYd8LR0>Z.9as6
CPB:!E43h(1nXH%6AbO`9\4bP>%.!l>YWZQqd]A)VL_t3>F^)0D%Hk2`p;cBbhsHHJiDki6;>
L$>F&a(0JgRT#Ck5m*:13*YX#::j_eed\0E^HE4-X!(eg1WZrF,$+3,09G!,oA(NbNYjP/L3
5s(43R]A_dr2Z="$FNs+]A@K\5^.sXAohUYFdXEHLhUE(#`14@,OIPmNOW`\d=TBSBYU9599>0
Sf<rMe:W]A"j;gHYPP9MuAk:<9'!4gU_8e1`oP7q6VeXqEsX0aAOuiqlCWbYI<7P_3AK0*9L#
Xj"jSo]AK["I1f`DDZ2@-Pnt'bei,GE8ZlFKqKPO,Y=pk[5[K_F-m`^NPW*,]AatZ0oc/Q"WVp
iisLWm#5)^YmKdW599]AMWJ"rc5>:K$t3$dZggMIUS%gW!S3u?gIX)=0!49_=YSm/KY.Tb#2=
E+uI6R1p7,<8K2no_@Q<,04(U@W(g>*8G8Z^h5_f3C7k0GYm0tmVJKJ*8>JlWh<*0l$!\ClI
9e7rY]ASaKfJ.jnqcGFX/1=L)0J?D*CR9eW`uH@;N&#C=C?=+F2YdDqB+UJBr\r7e#?J'\'s(
dH.p0Up<j7/jXC)2^=MY>FIoRh%Okos*0/N9u*ij4V\kKP4gjblB=5dhqq7Vu2-hBFE?%*#d
%)uZ``aB4p?jt%_%)uZ``aB4p?jt%_%)u]AADhpijbaC?f3R()q==cH*'dO<=D*I`4lTa'+I,
nd$'6KRU5#h\p0Eu#@#%K=k@k\*s0Eu#@#%K=k@k\*s0Eu$:5HHUuCYH;B*94%pAj?+nDQCV
kN56?i_&/-If`)79Abe\$O#VN'anRgpXT;Yor"u%$*r~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="284" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&dmk=sIk(j-i=Z\h3>>?VC(1Q&7F6Qi_#k6qB]AVe='!8r93TQ!I$?f>T_.DSf>8Cd%XI
>e5E_(_2.k2*<#m>p3Jk\',!KdMD8K'nq0APA)s7D^A:S)q]Ao9l$@IHR/h?fjq;O-bWfL9Up
c6!Bj.='l52/":sj<0_r3k@nQ06,&JQY0J-.(@[]A"G7(*>>:),u5Ib[Zda5*h"m<53c!WKf=
`;olFVepafY5E[WsIh6mAoSafc091NEfi!"nPF4(,rNHce$W/$R778@r8H\/1n>mKSsTE5mZ
2&_T+\?ILVK&a-TXu8n`JL45%O=;o/U1qo,^?_%6cb9R@b0:lu\!IIdbb[DgYhTfXmSV6-sb
rnVkF'sQ?u-a)clj(I:_C0aWZ(8:KSqmrkI>>*l3X(^Z!cS.8sW"s<p0NM01qi9ODdUm'4$=
-O$EA.-GH]AE13^(OGb.+T0BgRI9[>M(F*Zf#i"g7_Qh51pR$=.8l1s'`A2pU8O!/Z:9"IY3K
O\oFqD!A8!\OZ90:HI*`;l<-$e5)d1f*u(!2(Cl=]A]AgmoWR1DK=(JERol(EBA[1rX;N@9$Ie
5IH5r9h.+jV<EP4I7%oH#r`7qBF^ueZ0fiCQ]A.)I#iE=1e#[DNdVrs@`f,3,<DlCUYR>$nEg
?/r4M,]A1/O4O@D\<elFhEl9IdP'1fC6<?_<BWKlp6a^mLSe(ZcI)(R9s$C7MZ>/-M[D/D\EE
lCh;5R\6@iKA;m2Nl2r6I7uZPnmKOe<dadK7UE:"=:_IU=]AM=-[!^ko-d)`n#'7NSJqZg$QE
'qV*#Reg.gE[n[-SM<`1(,=BrPil1jNOb6^Ma;%AcM#q?8V/]A!JghR_bp=G=Uh[@R"d\_FEN
3d^eL!`C#3o?)G\YP>C/fmb1X-h2_e[@tQM1QJKsQZ:cY_$U*X#>aXg<(ak0iFQT8gP_]A&DO
(l*>QleX(URE8Gb>,D.%E$/7/]A9ioHf%`S.Q<!qRH3O'7[]AFI'Oc]AX<-![P:rG.#r35p1>VJ
AZ)i"mk9Rg:YeXq7&fiN8/?>ZSp^IHXJ]A7tYCgb.KnD0Q7"_b8QVPg[VZ>T.KY7t4cZ9L>p3
1.7C,BP-iZW[6q"5l&oXU^?K<%/[hbo9[N5b2eq9Z'1AOJOmTUktGO1fA]A0ACK4R`LFsf"&W
C>EEFB'a3[)6)_75?`?@bB[iU0$5E+URQ)E#ochPr:`FGtLth.%*NH'UgGQs4Mi29&F83/0@
DS[d!2ftT_-;jdQ6nQQq6e'Pp;86Psp+313#nJ7X![>PYJD*_(>ON_M2Bi$m(S_RIYjZPl/I
o8C0jNTQeO7OkV1L$BRaX>;+4^O$Pq,CgFFL"\L<`a!'gMO,<U+VdZ/3#[kNS:di5$95,LF9
k-7jNe+4'YWf`QEg7lYOe=T)=W*r)+q<h=!p([hrG>)1Rm+f5Z15Gq3H(b89/k6An$tAhPGi
UA\R+?Lhg,+Sd*rBEqTKAhf/&[J_=^BHH\i4Q6_9OAuauR2'O-`g^lU;B8m&X-5QYoqg1o@Z
A8H'gE3^Lnt$;pr';IB*J#CDj#Mso3=Y$]AoG2fI,-Q0kJDhB1AOV6X"!@,7=2,MiXc^LT!F3
C8.sq*Zf!![Q^lVW@s=pXGMqeHW7kGc=.P.\cg.Hb5NRgNRr/#/1ZEHk(Ipu8cu)Y$pIWcn!
l%:WD8(5?gW*k38eMV1CDgHSRdit(A;NU!0E6;Y'TkeoMIm?7@LSjfRYotY?S0ksB2BXk!TL
?IoNg2]AT"0BY(ncSZbaF<NG?=QB3NI!FO(]A2E^>J5+Y1Dq2YlEaOW>?b:4k6*LouQd#-q&Wc
K-D*GSh=nfkIntI(k,0a_S'2)T,^qqE.aq/2OE!jP$UhBbcjHIR`o]A!6"k2T>>\ff>Y<3u/o
FR'qKW;XPGsCQ0cbNo)UAmQ."35HmYq5Kc1>>1\\.=i"OV0O6%O/74&Xacn[&a^giQJ!'o)J
9\Y'6>btW]At-XcSI3r*+/4P>ZdQq>$(k2b`dH<KC_1i=P_SolZ%\<:[X7O-l[><CuKH>+Dh9
D=PRVoHL$4W<0-T/k=beA(Z&UGB0^@0NQ]A=_m3KEabF_McoFIb_2"MKYO+.Q?(',-_LpqO2u
0QFacL(D)GCU9MQ]ADa1ZJ9K'@Uu'as_>mmD[m$<oPLI=p*^'2GW5q0E%tH%'/cPhY$!o(acl
p9W)H?aeL_3IXB#H\[;+-b0so%t:1[U5[qo]AJO1WW'#IaQnY0!GDmLiRPT&BLR`WSQ?i`b`=
[TeE&[ZlMuTu*cM_*GBcf=OeGa57PX>X,k)H+!?Stf$4qIA:6cp4'TSs'8PQhgu5QM$&:d>$
b'Mq$q"VacurFW&]AZ+n.EEUW-C$PYP1U0%80m_T#$L,aCd!(#iAF#OpIe;$)Yr98)(0Rs04?
iU9G^]A4QmJ,p'd!!=(R!!Y--!UTQ:IGU["5$M2C8`X[PT/N4ims-B9'H>lkrYG]A'jV`/OZOf
c6V#hLL!!Y--!=W?9!uSfR"u(Q.$t'&;(r-.F42WP/&XLal-:o/0hki5FJ88/8!=W?9!uSfR
"u(Q.$t'&;(r5!c/Z8$cE'9ub(,B+bFRsN!m6N,u"9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="单指标查询" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="eb964ad9-cfe6-4899-8446-421fe3892538"/>
</TemplateIdAttMark>
</Form>
