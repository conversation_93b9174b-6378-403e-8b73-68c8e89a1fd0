<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="pany2"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**=========
						《《信用及场内期权--重点指标》》
============**/
WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标  DIM_FILL_ZQFXS_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, DATA AS (
	   SELECT
	  DS,branch_no,A.ZBID,TREE_LEVEL,CASE WHEN TREE_LEVEL=1 THEN '华福证券总部' else branch_name end branch_name,drz,
	  CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
	  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX a
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+pany2+"'"))} 
)
select 
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
TAB.ZBID 指标ID,
DATA.ZBZ 指标值,
TAB.DW,
TAB.MODNAME ,
DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
order by TAB.XH ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'"))}  
and branch_no not in ('2097')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name from ggzb.branch_simple 
where branch_no = '${pany}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[
			SELECT 
		     A.AREA_ID, 
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
		ORDER BY A.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_dzbcx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx4"/>
<O>
<![CDATA[grkhs_20240527090420]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.ZBID ='${zbsx4}' AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 	 
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)  select * from  ADS_HFBI_ZQFXS_JGZBMX
较同期：当年值(DNZ) 比 去年/上年值(QNZ)   SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX
**/
, DATA AS (
	   SELECT
		  DS,A.branch_no,A.ZBID,A.TREE_LEVEL,branch_name,
		  CASE WHEN drz IS NULL THEN DNZ ELSE DRZ END ZBZ,wcz,
		  CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
		  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID  
	   INNER JOIN GS ON A.BRANCH_NO=GS.BRANCH_NO AND A.TREE_LEVEL=GS.TREE_LEVEL
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
, GSFL AS (
      SELECT BRANCH_NO,FGS_TYPE TYPE FROM GGZB.DIM_PTY_FGSLX_2024
      UNION ALL
      SELECT BRANCH_NO,YYBFL TYPE FROM GGZB.DIM_PTY_YYBFL_2024
)
select 
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
ZBZ 指标值,
TAB.ZBID 指标ID,
NVL(DATA.wcz,0) 完成值,DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长,FG.TYPE fgs_type
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID  
left join GSFL FG on DATA.branch_no = FG.branch_no
order by  decode(FG.TYPE,'基石型',1,'突破型',2,'成长型',3),  data.branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_left" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=value("bp_jyhx_fzjg_zb","AREA_ID",1)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("pany2").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="7417de1a-9ac7-4ac7-8dae-41504a9f711f"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="gs"/>
<WidgetID widgetID="731fa7bd-95bf-4422-a2d2-cabaf5b1b846"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany2_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="pany2"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="pany2"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="gs"/>
<Widget widgetName="pany2"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,152400,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="13" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="查询机构名" columnName="SIMPLE_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=N1 + "画像明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<a+\P%jf:;b^,KQ'GK$3bDr7l'T#"V4"c+R;3.oN*<BP!cD\0ASH`PAQksCQFddtiki&Nj[
LSSMJ,ThZK?b;#_!</oH(bkhYI-I*T-C9E#]Ao>cbmlDI:(<CT@\AF2r-R]A@&L5Pa4b+JQ78?
.5N]A=hg$1oW+(uC.Vr<<fc=P29hjcZLe2cM;QbGN:bGX7_o.J2*+3kmTo(/cSa,[gBGOt7]AU
HeaVPi?q(b[ufR??,D`ot>9Oqjoma'Q)Q7IH$TQr]Ab<(`=GE'rpVUj-&?8(^-jk;He`OJ<o6
_n8\cV@!s[!I3hP^3G>o/!LJT(b*PGHZN,..Pm,F-Ob/Iqtf;J"?1od'@mPD?oE7OGu-MR@o
p$YDo+2iV&H6R["Z9nilfg:EA]AE(a8@E3g[ogG&;Dtea3fG`3db1.;HmSV=bOM,WZb1cmW6`
1ZYX6b:d]AO,oclh<uuBnboE(KrIsJa`G;9[XJM$I?a1+h<"hf.pVBhj@)jp=@DcI8m<0!pdL
Bg8UNBoN1[T"LTZ0Z"l#%Ae2@LZQdF3hpP8(B,DA9CRc[b>cU5m2ft3pLS=s0WGNSBP>\130
4_V!B('D?qU!@a)>/':_mDkne/,/FL<B#An1`'-G`sN3qZ3\JbP/8;<e4>Vo9/uC8t/R=([f
,eQpshp(_'DIa'K@rBpT@LcBm!8_`CMHMnENB?Ght1Q?FJX6V6"ncoZ*%dLBsRF.'/I'i>^6
=W@B!4_YhP91aO-nY%[77pr*rQ\qgbn.us2"]A>]AQOcFip^gkdIM38Nbd?J]AqRD"K)W-:eueu
KX.Krnj`*>\HhMj2^oUQ`3TYIE"kWh`NUGfsRCP!h]Ac(2Ll_LLY)+Z%Cip=i+Qc6KXi2[NDN
Z<p+Ud([a)d43_R+n]A9GfF_Z!obDKU-2.P*B,;UfW?n5"?HV$4qHCXbO>1q]Amk,/IsRE-ap*
3u=7Faf6R5:[J03_=OidZ_IOD%8>`7%iUt37a=15(,J0jL2AdirN/I:TB?,I4FYKGi72":W'
RN86GPp?V/]A&+lX6K^!Jl]Ah$ER'&C,r',t`m_B<mb?/<SXc+$A-K;qjk>ng'lEr`"SbkqP21
]AW>6R[b0IuBAdJdY-=emLMt/Afh))&_]A::pfsL8`G?kkKJ_')XX2jR/Ahuh(LT#jsdM]A^:qr
7"hiECB]A-9!PP[96e6)pBaC^'en)Ed6T/:.l^Oo:EHtSFfR_4KX\s:8fd!]Aa*fQ0(%5Ok('t
107p\"(Z7l"Be_ESlKFa*_8>,FionE&RsZ$C)Raj:PhoM1piY"1HX\c*\@mK1:aoNr(M%g/*
?tL"1nT0)fl>s0/VW+l0,Ub5:>KC2nidIY:2pnT#gTKpjdKg;(Jf`S6R_T\D:8Rf33Y2RWS2
Oa@_S+<U7KhdiH0c?:7J2=C"E"MX7c7J-\^q3Mj]AVaAH1rU#eE=*4!Ue+p`gq+ZYnqG2@-#>
HUGp"CN:]ARE.IV[AA\cJci7>2DCFLq"F^O-;AjcV8g\F5>!tk4($=?sCo`rYR<2iZHYf]AOBj
GK2po)Ut_@-'_3dYSG[(lV<I4@-HD!<d48Km/tW(4;"p9,6D*.JtEhf^]AF;FQe>]A7lkpRA$I
c2*VDq2!m]AQlCMCp'b<`RBsD-=;8]Ag*jAk^>]A;2RULlo(Ce?jXtJ^MO;b1P&sN.G<8N&*(O,
gX.88I);3F0<b8%>[Tt.O\rAJdZ23Ael),p%=BM,XeYU%InX6r[m.mTes(@EPk1N`_bd<*>O
@(6)0O,FfB6=Ar3'^jHk_'DP*#Fad11hd)`JF7,GNZZ@XsG-mXP@i[26I%oN0(I1'sn4Qf?M
->S\&ih322)TDW#fKaNN&hZsEVB..CBN1jZSM"+ib)jG=%W$%ecJic<iKajER<Dotp;+!L]A)
r6Vl^aMD#ib@upN#_=ecGt+lk,J.%DUtn,eXk74.,QfJW(*m'mgBSo+or4N42#X]AdOU-Xn%S
l$kh.VOS-EQXCLVHK-\5*o[&L,ctZcF2Q.8F@WA.&(9I#PM_'mJqSL:.TL:T7g%&iXQ8D-N:
Yq%PgkZg317R+A)>p.m*TJ03A(9\B>+[tm9c=R!C(F;<H'=M.e>$,+2@Kt[ZFHoYZ.7a<T/G
"!jBc8lBPFR!UY/PE(bmF%HJ/Hf)]A?"93Ch-%:9X&g5K_lZ:n=$HMkTf3X"Z;Z5>41P%o/op
mJrFQ.d=SK)67F?8@bKe]A$e@uLTq#d`h*TR<"/s,3++_gbJ3OX<fpI&Ag7bdL5d_3["jr=lO
q"S^?quOA%qXe<7_<!#%*,(/NGgpc@8]AHe!QQ%TujH:BUs6]APIU).h\lY[cD2,*MPg6G6"]AY
f(oEEO?$71`&QB#bgdm2%'Vn8r1]AAY-#bhr!Wr-+uJKRhdM*[CI/9d%=EPP;GK'=5/_Xi;(p
_hogoASE?DbUA;Squk#!/j"ae]Ap\<Ohu.,I,^;g3BTE*oYjF<>0nZ9dPSqeCIQPYWbq_SJ_.
d\RAD>HC-Fb9DtMQk=sf^SRe>6RL'Xen_7A96`%/#PC`dq.qr"qJD7EtZ(95m?r8r6M<'B-W
a-s]AXmOY__Yf)m_C&mL"p[H?on-cn[F)&O#f?>\MjZM+"SZpT?+5u"_Q[d!P&j*CSnU*cIf'
bH`h]A"1R;SIrl=\1h&C@#s59GZ:7K?sE$k^-[8R3B,F,[*@?]AZj&19hZ#4_*+Rl1>dO0g,">
.S7:3A/4EV/*7B<S\_B0Bm*DRe_1UB#U!^YS;"IX87pYDDOgI=q#l6cVdZo2_Vq^\u_3Ykhj
5Pg-Qk<;'VisusB\[Il0,r40R8qMsnZq9SK,659_59IL`9Hk)mqADLYZ`KnR7l<3U1rc=.BH
:Ki76TTN;[JMStHX=Op'Yf*!^PDWKrjbG1e@?S$(;pdO"P?PdgVpS4\kkcHLSUkK5rG5LmDt
R1[kHW7Ml+s5)q9Ico#]AeXeN_Kuk*7d&Z*X"5YDZ+FKY.o2%R2Rr"Kp;u!K7g&/33N/nF;[7
U\]A30:EW]AiiOp]AR_53$J=@F6/?RdFqbhOPhMGZ'-qNq$:&nH?b>jgei>ZMK7f-OZJk:m)[_F
.pra\jlsBo+V=_QL/'R11prs;gFk7o3rUh-lo!]ACJ[6'50Y$a)2e`kYM%\%E2#Sle^X1NlII
@Z48%E-m5eHZbrrHl!4.6sM2'nm4,>tZ^:7\a!"d%s[_lc7eU9n$AOK8ig0$)lP&3D(mXh'h
U'AR9at7Hk"@!Rb2_VIkU"KiR)r2!/,7Gd'BiPrb";OnPR0]A#Mp2[#Q;`-WZilf!dZKX&J!V
r73T.NnTXpo7m4WL"i+%FoN_mD8uTc">tri.=bS?3"!%_p=HpWN?U?Y2,1@E;`+-SWRQRu$r
[:5^c!^mEm.]AtQK3`g!M)E_n6-i#R5VE"O&f<5Ge.GVBqpP='jJ8C&2'BtI5DeR4Y/]A8-@ks
4S0"B]A8g2-IH)"CE*i,W_PCi`4/7C3eBZ9D*Nh4V/5N::2`lOHt2]A$%\;2l![USHYTY,L<J$
BuDDDCV<K`qnY?Rup*3RHb@5>/#N2Y]APl5MjmG\di#gLYp-#Y?B^bK(0piIO-*WaRGNM=n0i
t-PWoTH;MJMpj,=J$E`\AsHb1K<h0CE@gIeU\]A*Mst5rO4Uj"JgKD:in[%PGAN5Q\pm"#OT0
$@nT1G&*FF3iP7n_mf!r:Y14`C$JB7KD+o*<27#!IZ,./>lebfG^0-igVG]AIXC?4XlI@,6^V
3KHN%u4IZ!p(n.2#I#rD'7#VCK:A^Uj'G4;0_O8E1TQ!s4gY@^H'O0^=+-:t_m.C\qm7)(36
E?8YH4#3Z<o\7Tn,^s@u:asK2c)S+r'PAHU&T3\FC5j5I^,L(@NF1F/Y5jtsXd*XM67@d&f]A
jCDN/ZZsMqYAL?GY7)>J$iY8V,tPP.0NI28(V=O0VGLb:PKqgGeP$A\`JC;n*U>gjE:lC\fJ
2Q9*&b8-b?ZiM)c%OZ?Bp2`(Nc"3$>1V)l3o^FS2&g$dVkMD;Y"1cfKM%%r?9lZi(&J[^9[Z
U:]AF&""h7sk$O1n!r^OP+T#O?l-6qKXkq@,ARJtHKYh'A&0LSU5?=-.<e_OAr3F^K.>BDZS*
!3'GN<?hIaP1b%'FqK'up>slta!c:X3&Rio9/XFkP32r/,$[+_*=je$,dN%JX\`4ftf-f^Q'
TcBN7'RsWn>c46A5XCbljm`q+^ZrgM%T(bUuO/7tOJUYfHYUj=>i`BCPB]A;.J;W.,$`O.Af(
VSP)Q52'HL<^X`*sU8T4mKe_W9G8Cj]A7I)CZ^,a`pAT##o]AZ]A@(\7m`kp=kX4@tk8c;^Z,HO
L&R5+pYpa1Ga5G5C3@+_OTVOa&.BCSGQpQjcsm@"p$B,B<1ddj>FTMoB3`\'rp0NsVAjt&Sg
3@06)Y:_8S!A2hdQ3J9HCU1>i.L<3?<O:.??+G4jR84a!>.*T`$=gX#0kGLfXlFr_QW]A(B<@
WPK+o9c+:^LVt2Kpc((Yack>DNJ.7Ft@.)Mn:D+\hQ,[)nZdI7ZH4qhn*2nZ%cciG(>@R-J#
(/rdkpRneATH\Qat<pVo?(EO;9&Q3Um.*7dta[KH!QU3+^hb+5IZXt#`/n]Ah9G^decK'YPd(
D7WfOhuNiCON6X/YMKN(:!Le_H_6*hI/0Q$``)3D/l8^9uj?;2F0LW=T!HP<Vf!HUr_7_-@0
cTp3RE=X/2Xrq8255Y#Hsnp8O,8<Ok\nFLe?ap'p2e=#7%nOrhLnbVtJX\+L-[<-4kk)%d74
ENuce$u(:Ud7_StZf-1bWa%>H;_F5nTK;0c!a:oS5YIUqAOqfiL+\KUiCbD`R7;^t@2@DC$q
50+R(>3]A[LqEc9_qYN*Q9XAF*)1^hlO>7h,MW4kUgC4bZS*TfbK,&$05)`4+URP1d@jA!gpC
kYjob_=Uq//:^6SZ5\]A.YW`te8E6J4IkNQ[m0O(C*;0,+!VR4Q,l+B?DUBinb;<4hEJ+`>/C
]A=e"=(fV-plhQ"Y]Ard>o4bL3r0;()f<8:e(Z@ER%VKk:ouSM\8gP6+/AM1h@TkN(=OBtuRAK
-\V*2?@.N27&JM".-*uLJVlALUUA+Trdajg42N-]AP!59CBT!B_*bY%fDc,EIZkep[@BT(!fI
Md$f)B3$6`s/>;^G(=^G4f>*m+Vo5>O++-;j@o8$-Pc]ANerV:fW%ZC9&1`C&]A&m"tYfO*!IV
dd`KDXYZ6`Y#@qhq?<X;8AcC^?]AgN7k+;SWAOXV/qT)=sAFt.(K]A%#/)cb$K_jlCItWHXQV9
D\8anD[&^0_5EZXJ)fm2<SOg*DX^!r1oO/qG9J:(qJ2=:V89raB]AU@fj"+Ylg-B4lE'88H%>
1RW;#nO%>n8Kg[D,B!dZ\Cd1^FZEu!bDLZ,TQq1VlY?f*K&i'.3D">:t?%Q2%]A;mfht1GPY2
>@7foQD\0U#7$c==Bi_:NP^VbTE2>hS.G`OP'r:D]A(^''PH*X\i(G8)'+(8PgGH"Y78U3%c&
A[_bSVYMR;"Bb;e?DmIN8i0DUabHq_J1J`Y1WfN!7SajY7ZXX']Aal9Cmj77XeY8s*BXuYA?&
B,sq)lGk!d@XU="SBSH\X9`<G^I#IeL\g<--Fo#ZuOJVf#T0%L\i'#ssgCqI=m&3=3i%mH&q
:d?Dht7M)(mD\)!3Q0DiIEuRFfM=k,B]AelAKYpGSLg/rbh;R4(#eda@jPa!PuhsGC`-[aRt2
J(WY=1@@^9oCQ0$jG2.Vo1>0E(<::lH1[o.>)E`?mfMhWF/eHQ#sHh3(?P-4G-//CS_odMnY
6=na!BYB!<m7-`.`i[$Wc;nKK?Mi2$j+5NB%/;>kG=@'kA=03MZ4N@%E/W:Z=6Hn>dnCCBKO
DYA[V1`*u`I^P4obBo&=[HS<6@`8?!?-AQ[4Zgm.#*4+(fLi=73BP`h!4>W9.(5chl`Cr;/i
LEWZ7%bZRJ[`li9Y"71,E6JpAl79X9o?l)f2u\om@#OIt03?,?[4IeO(b.f,[CRg=OB41j/l
#;8UH*6aGk09n(^]ABEd?<0tPDs?>J@U0/SD<Z_E)+Wou0SfE^E:F"j"q(lD9S5=,_5co!J%Z
VL6fG;CZVIUUZ`?`TT'*dOlENs\Z.V&f*nI-P.fe@ahJKe1PAJ'0W<=Ta5)']Aeuc]ARCHk:t%
P;Zf`jN(cSSubtTq+q$Ej`>?hIA(.[,32;ha7b'T$#>(e"%:,2)26.=>ZMmJ#FV<D'TVmp4'
ilR'=>B=7TFuR>/r6TM8*t?>I`rbnp"1U_(=U\e@2g-suJ)b^2n`\fJWl,Sc@$Xr\EOPR_Ha
Gs27```X*"Xm@L7OoKIZncAQ=teE&lBaHR(f9*X44JYX-n]ALcKDTkPL+ZCM[Rs@Z#pEd1&N%
E]A^Xm$pcOf#"d1<?,HjYIqY[!AK,lXBH)4jm6.JQ#M6ZGRg?(/bFMZiUX<a`8+#REN!P7:L%
HY,Mos5B.!HVU<qNZnYc,!&cjt0p@lTo0L`jhnOANIcdr_2j+,qehpDtInLW=K(fIRe[_9Pe
N!X5A$ZMR0#jfhBc>^EN=:Xi1I2F=JlHp#/(<)`D);&&fP;_HF3JL<2K/em0?ZhXBm<d9X3t
TPfq^JW.D8'<u/_k:l4W7jHm@H@n^R.W>H1'^s=$fq8"c=lpA!W%^/M?XE]Anf2mTTmMAZhgj
SL)M-)XU5?t^`*)sHRJ_!/hI%e':,ef<WgU=XSJb5$79L5>d]A\Ao=c+8P$frddF1mPN"ErL]A
d.kD0L7\0>oYoK1un;@\]AKfSIa/hTn_LM%O6BHU?a_ScQ+,2-=.r@O+Si^+/n8j(i&Nb?"IZ
7\/)=cbfd6NX#TmW@>]Aa-^!BQc-h=N+'pBX5F---]AZ9,:^0/%SbqZBndhnMa>#U%J5F6/fqD
kIp,.lc;&QQg'1<#<J7)^T[g%F/>I,[p,^#/2n$t_S]A]A(At"YJ$g67m*+Y`+4C3)Nsi[jaM<
6qp]A=Xr)85A&Bo\f!k$()Q4VE)oY0f=$9>dJ]A(JXWaVgU7^mse,Ci[/a21IV^#FTc+7:IVFt
#EPej.1/M:r*.6K,JQjQ8LoJlMt[N@HRN'`("/[_#OuLlN[ojm.+/CG%cZd2W+5CI;6-''*p
F0&JB&MKVt3BiGMna&eM&_jb&,]A8:ot/pcMV:Q!\lnT*@>Wst`,UlY3!M4KiOfA6VHHKl4G1
nN2WD(KE3YZk&iU(ZtiT"3:SeG5CF]A@03PdO]A\>(XUUU)",NCpPL((9Q)h6K?$=s/M:WoL,g
]A[b<%sLl`)p/.#Vn+c9pD;J30P:UZ$s+Vq"'$B+qj;!"i_2,oR257/1h+(U9^V6:Z-[Bi>.B
5:b&_Fs%O+Nh_1PT`Uc$AJ![PqMD)F`:]A)D>;,?&CN*').g!,TH2'R0S5U38KWe;!U]AR?jNh
Do#XLT*aTI_e=;@`K1RFm/C*f<%=LQj'83L?\IVnm+,6?l05CC,e*Dk3l8!,1VT^hO6UZZe!
e#1tT-e.g4>5DP$>aE0=894mV4(^sDBFO71fP&i-<F@"DaUR.Ngm3+3"1l>Dt=HNun&.HmEV
\CHWX[uF>1_BQ2<I59_BU,>o]AWbJCi+p(/'oNh7T29om4m_Dr:FM=Z'O.CQ4M^PQ7VS'>+:L
Ag4c1co4b.0onJ*ProkTA+^\2+1[+GCeB(H'fq^FCRU,[M4^scQU]AT_/).d'/!I@s=NE'=7f
:!!h<j1hNhiCcc>?Gd:o"qhMB!S$m"$f,DZRAoHOP$^6U#]AP"0i)W]A"TP;]A5&b3c`Bg7POpY
&CU]A#KJ0%+Z1@1!TmpA`;k1kp.]Ah6-F$B`6dh$<-LQj,/Y4ap"c:!N(FR/Bc5p`*D.,R[0D`
.lJif^ThflM*:h>[._6>GT42QkIVZX]A?5arbV]A:G,!BiR%edIK"q/91<fOLgAF+:g9DncD2(
TFr%8$\Qj]A+BG:/g3>)h7qh;KdGXu<._M>dJ2.kGY72ud4/5DTW1FCBLISt>qog#eRR28N%F
*81An0Cd2B`0=L<o2JgICH4.X&!K:M?'6^d6)6)%2B,2=D4DJ*L"]AHo+*Ee[!DSc`ocB$OD@
99f]A*griHL'.2H]ARFf&Tq!g8=./WJ]A)g7"2?8C]A(!`Tj#'1^/5`Q;l[^#hF\DtgZ45,5GBD"
%>81+j\T?34D@as?G\NbNal"ognA+;gl,aL`+>N'l/:$Tn!nNu@Ru^)G0Q$g5opK3Q;E5=>Y
mGWmfecIf"7A#@FE4VIlZpAT:12HM0&7266t)Zgr$IZ,ipB"k\M3@X5l5RG0d^nm-XIEg0,I
H)e0-',muZ,704jNPnJVfO!N+%*N0a-*6&&r!mcU1rMbM7edeph2\R,Yg@@5]A9Gi[\,$'`O'
h6/5@CZQK[D3`PEU_qJ+jBjA]AsJqmU9PhknIQ9Zp5`>Rjmk3WOQO&JI#\P$munI\@3tjD=r3
dKDQt?%b86m3T,JWF<5LUUK1Y1ke8tq:,;/-N_k%Cgg<ViZSD\f9r$PWP^K>/I7X;%[e-s%e
7lF`6(D3X;os@"G[8+%U+kKeMM-%X[jU7kFK_>jJ\ur3GJ4&g5sS5M:Gn@AL'#nOMlM68H<L
XEF42&;5/Ycc-8+(($=q3V8L@EVof#8"V%URS"]Aou(DOJEF.`k+$`ZLK@rlMPgPNG"2\9j,M
0AirT&poH[hTC]A&Ge/6W]Ad?_,P\*iTkaH8jG22A.s*<+X#*J%7U8$.F)qhs7g".t.N"26m\"
E>W[U,l;>&kT-\&6JH+UnmTe1u6ie!/A]AnTGN5Fp;I+g-2M2k%[<<&`,3"&^7>bupI#`+Med
k;-gpZNC6W'=jR(95pt.Qq9E2U`S.7%<%j&;rhjs$8Aj!]A_G4b/KA$@cRRXFR$>ZO]A<]A3:H'
k#mN;""%oD1I4+pUM%K54!FptJ/L^W!igU\0>@q%YDdLNfQOVC9`P@NU=+deb_[DLRkd#I#B
=9*]AV0ml$`QHDj;K<;FH3;`[0=lJ@CW5CJ'$'E3[\Hm`!b+0qa%3#GK!rT#s&1j=M''D,DPU
mXuel[,f-%ko\[<BNa&b>,,I_c\)7IB6KebTuMO:@D*If@R($RAquK?hZeU%/01/SH`&#Y%d
JY[;!(,/HMY<=EWs/@d!S[p]A>f8..^V*aPCMXK#%UiN\/,"&%\T89(e&`^op7u_dec_3?!/^
Yeke-Uc[,FR^"XMa8:\+*`H7m>;S/^ZS3RNkEmb]Ao4B`S.N+H7C5o=\(dqkO5^dY)j?:PNkM
>-.a^'WFDL5uNKd->aY4B4#U=/!HWMkT@Ubb6(Ua?b*=PH`>)HJU%pp-X^8lX&3Q':.pN&[\
c=M1n]Afu9T^7<'p:DQS5*Zr@rY.%D=cYis,!(XrX#k.Y`G)!m-B3RdYQCj8_neR:57hf!#u&
)[+,%s$Lccl75,C)+]AH[OhcE0Q4Lq-1R4Sm.kQ]A:9q"ji(Lul:@r,_A3*-;h+&G><7Mcmhu.
"hddFU2q\I76E=Zm<VB/+aqYtpj<g(]A%-!F;Q_a*-m\`1r_.MOo;[Vd,r\T'cD+Bin@Nh1+#
^4M$qj>7l=Y.s>,"#[r4#ob85JWSe\K8hcWBhN-N"XZ+gM+I[DJ5;*eDdnE"dDn"s?N#nr3N
tJtEP!.RUj\$n0mVGZLBm1JZc0k["=+i=hfIj+aaslad]AB^S[JpduB%=3%%MW\b_'D@*8kqd
$fd^nUFne0j"'#G,ktfSe.=%ELqMult"\p,mN\bj?B/PUIO"E%<p!A":*0"gGnC-UqcR:i2C
/(N.`=JLLVHkc-f0C&093l7B>onX/nR!X_cEreTcG3s>IBLOOB%lT^)!G=RUI@J\<VJT/6i7
%EHY00OM<@Q5rp-enVg?hg<36rt#1Ym9iq\r)7FgMWkX!h<?IEO7q"!4L1FD">*+1-68'n9W
-Tcq[3>StaAUtJ+kEcAZL!P3o81f=L1!tlM_roXH46&I-SRi8:]Am3_lD<BG^&H2Id`kuY4Oi
:Q.P&V$Z]AW@\=2*7F2;5)aK1E*uH?d5ST+s+tj8/&:2;s?(j5[/0i;SnIkTnZ";B^(5NSfX)
JqW!a;TsfaVS<+94ltJOPlC$?\4u_09pPXc&)^hcNn?Bi`Aq'35qI_-A%)JFLrV4gklB+L[e
)VO8eTPDG:ErA]Ad0B1ga[5):neI:`aJ[DuE</Xs@"ecS7ZZ\&X,3!<ieobtN[U1JjQ<Nfbr1
lPF^B?p-_O4%oAXW,eeF]AJ%]AUG'NP2'(Q1/EhW)2.3ZdB-iMJ:EGHDp%EImut-C'$_OIp2FB
2u#!aL\Ji_8B>VNB*bN?U[0=I6@YpSBG3bDBRi8'>>2Jt.c6#F(=ON[?1FSWOMr>l&^U><R&
2u*\_Th?HKmZ&:)W=NFhq*lp`c#k,s(\54<VE@EC'r"fj!cl!sJmBkD?LA>)?0ZYT]ADhEGpJ
is.OIIG0Gs6jUYc@)S$]AZ4Y%</_k($_NBY"-'p6l@,dsi[_U0!FXt/$B0qcrc+&d4eZ!g:rX
kD-$s87"5B_q0([E7RZ04>Uud-q9CqP1/t_O:]AIR!T%`F5c_ukDt\eplus+&'WSq3`h[.h&N
&I8Qk/sf.1<S#gU90aF&U'[lZPa%`n(>jBX^4Ca^>U!UTI=AE%ta[k`p7l]AO!_GOG&TWfM&X
U;([<"3TUHZ$.;]AI]A#_$0*akF&duHrlQ2p9T0ShEdM@@4EjC$,f>\L"jCb53LT4fJ=G=DCMQ
%Qt6">]AsI.G=)IE^5VZ%q<Y[5&u1+QmMrb--JOo.S,7?]A,5&)-c+&P[Tl_NuSoF0e7B.UK9A
6acl>hQUW.s+8d7BZI=c+6:CR+kUFrYEQbFB1&B8aLr[tZ9$Dh5+ON/?97#`+FGWEYTgbmHR
Y98::2)e'r`,-#:R89O^G4,oZp959N%HRhq(a_p_JBJ">o>Bh5.#ep+8V'OqTPnmm%DeE5f@
EOYS_k`R!H"EF9?D]AFX[UPG^-=4]A^e=k=Vc7jm[UE:[6X-4h:<dsD0U55e9i`gC=uk84k(;X
Hds7k9#=<fdHe)7$a?pXT?ifcrZ-n[;Un0;>=aA2l^<`^;4OPgYnlq^_?_;O.jVJf3f>#HNu
a6+^Q!WUKDDNn7I2.k`]A)1RA<ArhVV[@#Wmeno4.(iI*GG>f*5nT]AUk/'XV\'POa3[)k3lOo
4Da$<RACKG4"\iA&<pul,KTqaAVH1cFCg20[nY0;`S>M6%/G%R?YmX5A,:6!_V:d-YXP&]A5U
32nnB9,SRFoRm0XK\C2-[eUX!<LqtDgUX,RUP0oDETa)rg@5]AP#@=,j"4^@BW%WUdM/0P>aA
*P5isDrms;-n7.rEt.tOb5,S9/.`Ki)d`H\C5WnW[n1S14M7d+`qb7D_fhs*VX]A77cWN@7O7
mE[HXK0C;K(YU?u>i@\=/,mqJSqJF&`)=2S;H?2.PW2=;hF;7ICq7<:HlP'&`bYbmk3CPuO0
2^DFV$mEc,Gg+_t7uD`9lldO$bB(o!9Mtima1L`V-gQ0eI9`,>)S@eR?/27HIR6@gN>#Uq28
RgGU_bJP%mSp>?P&f-FTTlP&,gk&RigH\;D$)4l3QUXE#sF,I!JS*%.GLl)=>'GI[(jd'\N>
$&a-mCGh"`&<]A=fTZ,6D)TY3ABhjc3eQ2[LS"?eTem=<k"T2IC!TG5;879:i!1-$He<s,$2A
%3HSW1R,s5>[j&Z;&KZ>Ab,+`T<_C7J-She?V#hL5&W^dOgAp.MW5&!;S`[;-&JnIZDc96*/
[i\7#=a72h>4h>1!\ooYAu\n%M6Y]A/kg8-4$pNZ7C[<C"(cstXc^rYm4X1"Mp!LqkD+rRo+%
l"l$mB6BIT%8tK(]ASgkCj$Z0a&?Wi/^UqoZi,Ea&t%-X6<V1AQ$E-"p$bL"KS^^%-Io7K+Qe
%mC5,^EXhWQ!U9*`0]AuMF[a$0gWaaN1W;%'s@qP10YDSQuh_<OcC%K%pY[!\Egc"/Dpd/ZCB
[_$>'C2@[:6kF`'^S4&]ATkSJ7pW_5YBUs(:XN*s$L13Ui!iPLc*R0$:1(J6cD]A]A(-M\>l:WJ
W"#`u%(W<&_2>s38mX_ur9,';V-qWOPUQ,NP.HQ]AS0&0Sa]A>LhI;Nd?qkJIK0Wi6\NKbT3U9
`09_<"Rk*f!fZkU=$7Zb"[;5<OuAEd/uM^F?ZR7D6AT]ATMLgQWdEAl,Op:`%`/#JrL`G3Jli
V1"5K&6aQ9;]APl-;@XIQKWqT$(SQY.PJ`ERabL5W*W#cIpd+hjkQ+hckpRlf$\tgulLViK!^
_K^U_9B32XB4-?*C.JDF0W%ff]AH?pd?EN:I8iWlL?1R_G9\sS#g$nopc3E,I4QS$`(Jujcnf
PI%l7!F%dTg0p68RLC_b3M_:5"lTDfYiQ1Y%h^3H9\sMJ;DE$CCIm'?1^<?g4(Q`NM8f]AFAa
g7eMhlnG@qceDCm@BdGQB"A\DKtYja3L6_T5@BeJL.<hu,Za,HiR59+Wp7L*%i,9)sFL^T@P
?s44!VP+r)Q7]A6A.DDOBM,\KdBacc-;]AXg@NG&k@UF$r8+#F]A08`K[t$e]AF8:Vi0OOo>1T@#
o8Mi:AtHLF9B:T('$j;+unA2P@K6Z>lP@\XSoc(#h]A1H>_LT'd\*goBDmE9ET*JDboEWJ7h^
gHnC!Dnbu"k2a-D@:#ApUY*\/'X_R\cFP5C!U,qaXq``kF;QZ,X)K.XX/LUF/B,OEO<_nR:?
]AVFu[16iX0"t#3Fm+@NiG-[mWbBK?e3g8)p<DFUl=$=QHK:LR8jTXO&>R)KL)pE8FY",T)*8
Z1SGdB;h,fNlZs&W9FLjc>$I:N'SQD-CTqbUD5hK=Ye)edZkW,9\/$PqG\]A*\bG("7G!3n1=
rK.u;ZXa3oI@nI-D*">g&\>9Ge/?=mVjlu+3E<$.#f\%G8_X3u]Ao6K*2Ke0j=%NiX@PZKh4B
\&F):'&-`95Uqcn4Qh:3WaMI`7?MNlN:mgtD+D(*P0ml0Ws79q.]A2+71F$C#6USV*Q*_cPJ_
d_J(.0?]Ahed]A1g!@Oef?ahGHE0R`4?EZ2>e3nKoV-l5q&Oi-S6KFaK3"#6bRmdg.Ml*1j,([
(>%$RX6qlc!)qZpZ@8Lq1L!$ir>IOQ5uatHiA'j[=I$i%7N$.X3KH=7K4?Skh-q#cp'bD@_\
?+k,I6p_2[0YLAb_IVb%U3#IQ+%>jLeV]ABA*;R>@<\Ob*SRR3'O.'2.sg;/o_d`anXPi8"44
$?P82$U5^5$QZ9J$S_^YO%1pEktTTG;%a`^8*R!W(.%kj%ZiXAD1JpMomct2$@$t!(&i@-ri
_qI!\$5'kK,mVqYdVTi<!N's6$XFi3e%4b@iM4[S-5<N<gYlgZ9U?+lsm/+lsm/+lsm/+lsm
/+o`Vm\JR9K:&4LFO%\!"?PM='+9.@Np"O[ei3)NUVK1\"Dtl[0PJcH83D9pH_rNL39Fd9(j
=iSf%[4fk0KDf:PJcIcs$kY\BVbU94\4.%!%]A-io'JZsCOY/om'H`+o4[prp[,u]An(X.KQlI
H.a^K4W*%$H_@<._T-AT]AOEgRgnLWNtFQlIH.rKk@g>+"]A?rO[&]AWClpW8lthsWZ3@@a^K4W
*%$H_@<._T-AT]AOEgRgnLWNtFQlIH.rVm;$A:Z\]AY!<@f"3PtVC]A??^Qi>q(jLfL*Yf-^teH
oH8@qF4-DuLg~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="273"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="273"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1695796,1524000,2360814,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,6096000,6096000,3048000,3048000,254000,6631912,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<CellInsertPolicy/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G3 + if(ISNULL(H3),"","(" + H3 + ")")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',format($$$,"#,##0.00"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3)=0,"","<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$),'--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3)=0,"","<font style='color:#"+if($$$<0,'51A579','DE554F')+";'>"+if($$$>0,'+',if($$$<0,'',''))+""+if(ISNULL($$$),'--',format($$$,"#0.0%"))+"</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="7">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="8">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H3"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="2" s="9">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fgs]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="3" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$yyb]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1" paddingLeft="4">
<FRFont name="WenQuanYi Micro Hei" style="1" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="16"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="24"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[[(LZDgL%EJY&mQ1<E2o'eP[)=PaXod6VUQ<'5^T(.tSm@eQ)oBbr)PISgD=::<&[!O;Q[a5s
utQ71=,,3R@NpHd;4Hk?RKKqtZ$5PO/8$HgS6/Fl.!%bM.Aug?clTO"i@DZ0U]A1^<a`>.$0;
Mi7QGpN$k516LHQ_IG)u\IH*UYg<q6N'9C8l-<Y\*cupQopZ/r1[om3Y#M`VAC5BX')lkd)V
Mi?Jpg;[Xna;UPnEN`f?*<cXQ1;a\6TZ04>9m,6#/FlS]A/sLbW$`J2]A:4O%83&it8\*tE$4N
@2)D.eZOh5Baf:7051B$6';g(liF]AAg\c>DG['tL6YGk=+!cf=LrBpQBm%N&OHP=uVl7UOh9
JW*CMh0N,njrE"N+f:fUZ(IaWo9"e0b4_2?T3P-o\%M[ukUQ1Fk\?WSahujJ#Q=&)8tXe/mb
sdj?>I\;SpgD+kL[J>D;JXAlX*GtDHuC<A(kD)d,<CA00\k(giMGnm4;S@eXXjM![@Sh+pSs
GjZ#1/*'MDG;h$XrU^(BN`\Y>QFJa\/)5H?%2-?+R"u<per-0`Fl*&LYs7%!-dso)d?,iIIJ
%eiRn,N94WH\?1:OdEB=q6mc=\`44b`a?cF)g1B@o)u3s'O=YCchQA5*+/ts)#LMBeB@Tmn_
+\s*#.UX#'WSk.em&K8HSfhSbZe3fL$q27@VmAXT#&:@=1?H3lh9n"Vm#s82")kPfP0Y"HGS
Fj'%bjQ_`JM_j2ZJWYYGhRK^X9V!]AH<\'>?TIWm>Z-b72`RRM-D0V-#T7lfo2;\R)MV^n1S\
DDo\Zn8RHob=(F#lV('Oume?"b9.0qTL;[B)rMd+<KEb/ef3?_"4G?-]A]A]A3eN-se&.=[-c1S
2S";l;P-6$RoCC*A/i5Y^gal=M*HNN%;:q./TmqR8D&630<6=j7`hSIbPN@CRc,pU)b&89lA
q/6nn/[n3l"?H*@/n2Sk8SG$MM4r6DQ+^@602Bd7EjRW:5Vc-1+2Q921GLt'bhRUai^FNk0J
+r77Kg:1@'U"kSJIgidre$mM-f_BS"M`%6M7'fmRd=qik?Kq:lrqDMsHo6Vk)l,SWA=JmU+"
Bb)V$1YK?^E]A2tfROLC9bG;ob@`=907YU3rCeRKT*.@-b5"VKNh0cIA6e!`>*!ibNCI8[jGC
Q=#T675nogTQ^5_i/3'%dR\cOALg'<mc43V2r:8Np:KQ0Hto#CF0LogsKcl^E^SE$9PeKj^F
<&N"Uq$<Oh[kkc=%$8I+^fR`Z3[IUa%MLe0B[6Zt1c(c)*7&<U*n$I_*Ysi)X;V&Hma*3SZi
6fr5^:eFHlRYTMCiUM5T(^(^c3!sa*Ad!plARJ!ng;X$2_kXLqSI,EBd]A\!C`E=jE^BX631T
cmkHBJTV0'E[FRRPeOk:m?FiINA3t\e2&2]AH[_Ks\UaAg]A>H+PX-#Po2$\uD9%@G;o>r_WiT
9AXqJQZmKK)6%o!O=IL5hF2f_1tR)DJTKZ9B79'Cm4!8NLK7=OBCRku`84]Aa7<)+=I2#`K,f
fk:B,a=4#q]A!gNQuSO^9U2A!?dfkNrD3(nrr_Sd0u'^<\(ZHI$6J?4_5DIY84@:l:4WTi42,
BN4Nmq9_C-jMGnmY5G"d>jLGDO;,.cJ\0@5$F-UsXBiNN8'sHtSp5EC$qf4O&csgj3\%5dA9
?>sB8UE:Xf2s@]ApicKQO'CQ_&<o#i\+cd'T[0t.l-<RWV<Iakdh4I$+A@(/#a-6&l$U6OX30
]A+j>faR\U@..'A<bfTBcU8bH(_Ck6r'pI-J=)3qO(ZQ\>CX0(c?Vq/Y<#[<i%KIeo`t!p,b-
'eg<S:4sq?mL$hL4k[I95^UWIGt62G@X*sc@C=eH`3dJg;<S_YXnu+]Agr%U-E2]ARHkF-'era
+$Ugn>OWe-u!mBZanLKP@\qeIl2jJgbK+g9C.5%KCDR,oh(/-"+-)q;RM$`M)BI!;Z]A&(QhC
NkN.u]A]AmNp\rm/JHa>I=J<qb-hDBkW$18eRbL??sZ6pun7HXm[G69(e2M\Bk/FC/K,Pbt7n?
XDY"NqjgBlLBL$h.*2YfaMnQ'"9d.>2>-78s("DFgsSUR;_J5['t>>]Agq$rg&$U[>Qi"P)RD
Nb$8b:RgptCF*1#MC>W<kbPHqNT-10M[KM0$Hpm0Q4/53d>"L),Ob`GSYGq$49KX#D)>k9SO
s7O[cA_O!gB7k2\WE#!qcQ'q8LWTA6!3*7OX5\!B<J(RP)ToTG-QYZA1QFm.f9NQPSp#;LBn
eu%*q$UB^jVf[>f4[q`-G=j#@:I+]A(VYJ_no=mG*+)FDCIg<c$$3B.YpR1D0ua`K&rNYHC3r
!CfgP-.&;4+Sk?H.^8OE%DN1u?g)T(@06>g'MG^GdpLLl#o`q:.D4nnC@PtJd:>FBg&eAWt2
?6?hW%M(F:VO&2%p[piMd?=0:@drUHgl+b7&/=rjBVS%I^3r\>A,%*9?>Dnp^$1snSk^]AH^(
2gUaT-e_"lWVp^HXsEmYoV,`ah"[:X3X<c+^]A_gP?ni6+Xf.%4u=Eoh@S'j\3BanEqYL.UH!
Bm9:9mu;&iM<XROjuC!;iHrsnj%\1Si&Um5@\L6/ps-(=UUMGUO!d,5>h\uK/KFQlT&*Fl,r
)#X4P3%u*gHZA>?4,#_aZETY$W*Y2R\:"]Anl>G5WNj.Q)Ji1YQDWcBf=pYl.=@Dn<<3#Qb(>
&9dob@`7&J2<uO&GB7oI7`2&ID3iE^UB(;Mh/a$;ed#ZNuF/$]AmGKq=@Lsc(hG&a5\]A;m="@
[4o#Yi_Ip0@V=4gNh<8C5$RJ`o]A0P0YGND1->!Mi?/0f;GGOWfa4`qk%h$aWidqtDKu,bTt)
aaU]AS6pnV&@e^diij+n2QAOA]AKr?V\^P<p:SuR[u7Nq4r*d&scA%p^lu2M"*?(Ck`:1E<o*s
VtemGJQ&%JU]AIWe4=P)5,1gRp6E6bAin!nuZgS/;nBWA4'Pt##[+)&.PO=LmV^I>R?=Xq^;e
Q'e>2Ag=K9,M%RJ2J,fa_$K`-6)>8Y"s-<:q+9ESV9I;VTu03X@>s;<)3;9i]A+>UlTut)cgC
Y)"qU:Z=7O^TQ9D%(/%^\><@^!W\C+E>ob;YPC.qSHCO3b,WL'h\V7bfoV2+#Vp\0=DSVmUF
b&RmP>bAB^@EL9?M7\]Al?uEb[q$Ln@.>jN\qA0,f9AAc:E*8C8[4WSQ>;^l7rdq+,mHYMeN-
[\ZMSd+q^-&s=MG'rhD?m.Af.99>kR[?dmh49*80sjKDr8@qPFQN9m!Vb'j]Ae4=E$nHj9=j`
dCPnSf>WXG\rE_uQbq>(Q"lih[<UG3P;u:`fl.rO/1.A@-=5Gu5>'-8)4b$^92qqX'eXXP&7
%$95<PrKY&@ihkgRQ7e?)]A4j=GBR7;*CB1X9qkT`.JKOX"U`Yu7]AP*),8iUe"[l=\;m=)[@!
kO*%<8-n5t0F#-K7B)8iiDfN/>2CMkaf(YiVqf<Aj.!F<\B:Rf.MWh"j=$1WMVta''JqD$*6
3]AW/Y?`nVZ5@=QK)r_i3Dh>r5VC*n%r>oFhZ8ul#!WTLM\<htq2XPhh5-99fD7-58+HhK9$P
L4U?mp(o6J*a7jhRG1l@\Y<dbW@#'7gk:PIFR`_VF(EEmdL^'2gBpJ?>.[a@j@VfeT/Spj;s
Z.s<b*K"b[l-NC!eOk7]A(\A*#=MWr$8m)s-(*8Q-^d^OtU'.J-.-t',b5[11<7iG`aTjAb#M
7n3_?k'=CLlL@j\//E[$gn_\gMo,<Rass7T2ch6oo@S-oGfkV,h_s]AaP:mYgY-@#>pi"i".:
4;lBUD!p-jSrgAm0Y#c-6:!B6urukOdl]AnVNj9YQllg(c9d(/40`7/e=NCLf^W%GR>Md>FQ;
):[G4t1D8'!LGAcgCsa\9lM<^AjD[GcpTOU#gk1D^QieeEh,8QJC<S51R(nGEiqUkY#]A>g\$
6u]ACq?*hI`8ffMuUW.aAbo)DA]At)Kqrg[6,f*-]A+<TM0>D*'nT),IWVe8eFd6f<+-C?U(7kq
hbe4Rmd37j]ATYH]A)g7T3j_'S&J`e5,VPe^"<@d$0Oh0j`ZSP<s@T9bQ`]A&Q54&?^H\*c!\Uk
q]A9\n6Sa,</B]A6E(pSa<ul;#n>pr[d5iP=tO:sQL3Z^I^%aJViF-E]A:`GJr)5[>K:CPiY)-G
VoliFYLq\EeMsVo3I,D48nI:>L^<JlfeE<URVmg>1&OZgC#9ne/Nl-t3%IX<;Xug8b6pj*Be
S"A:1&ST^ThKiD^QT,rp$Wp!([cRBr4Ct-q`^bCA0/HYFL7(W)BDp#e/lq/i$-D@%MW12ci;
S#Nf@e.H,eY[E]A`SOnSFZ^VT8>hD[[DtdVL=Q7rn:$\$+?JgU%QrNXFDrpRSFoR]AsYM'hYg^
\XlbZ6gjT7CFqg6-Za(NlK_q;J9CJZR-chQUV6gp_Qi/YVkam]ApkLm0f`&/<1m`W"=\p]A6h"
LP81WV'4A"\(Zm1J+O`1at84,[8,X)cP@hn&%'B7U:dCRqiZ,I(W<"-gLFJmX1qUb-%uWe5C
F#Lt4POa=(5Qp;)rG3I%&cI",pY4^jMfNIZ:,]AYB./U_j6K]A%u?/d?:(n`h7V@(Au6764F#4
SN'4+^$D4(nC`[Dp=CR@H*4A0:O#:Ir'U2R31+VXC_k]A"ZtI_=UoT=Ht@PI48\`X3J@Lj_^"
Wm9,tK</M,V2]AUEjae[Ra]AI9uMR!.8F,55+c`Z_!8b/XA7i,SR,n/8@SdI.^*)P/<%;-biR@
/<^=RpDUpre)V7oZ\tK7B6"YnPU"4H(&UBn<D=g^),tjA=unqhXT5)O?oPQ*gtMpjc5+Hd:s
4,=0ptDF):[M?2+'7lpJr[Hrk=ACG#Ylh&bAi,fh3FMf%`!fQD\#jJR-/&5'Tlt_=ZE-;r"4
p\4c5t?Mg,jGUg['=iL+51>qZZ'k/H?]AKq"jonbCmI+paeFNiLk]AVS.KeD5B=WBmg@>(Q/NI
s>]AYTW1Z[VqDSNQ%9bofF9bK]A&&-eYE-+&jTD@TGtiAPZ6r,aA>7o_SkK/S+NiMc<k[[Y2a\
Y[TC!FJr^UKpG+]A[&".ZkQ:pis1R*m>\`gq=CnBacp!bV%5Uq0d49R@QDB7d[.@Ul\%R#]ASL
VY4`V6Ungn\-g>#[je5:CU67t$%AXW48%:"GgPFnSoi-<D_Qg?Y%@3-r.iEYco_/lpG\'oDe
f^n\qDqJcht>sAPGsdnC8a!6\3&Ya.56EDOMt&\;dVP(rqPIhgXjCE68/F.DCrZdXuT*9qt]A
XmEjL^D7hq`eYG!ZNq.34lL\o>NoXP?1"`c9/YRWVpFmRDSeR-\O(L[dPf?$sd?.MSpA53iN
$Zid?f9V*!Q'!)EW[hl9l#)[U:.K]A>-Y8#JEH3I(HZq9@,B9r6tX5&=sh#HnkcI[OG;N$acG
#/:7*p_mH4K>oqfN_S$_4mJ8KJ%'`0<?3Gh:^]Ao!jJ>^>]AHoK%Zch51W7mEO-Eq&]A=0nbdY/
hlo.`8t'5jCH8c93'sk4Ul?=>Sf!he=jMOM]AliG]A0aa;emU<bn6=imR[d3_<!dXJZ9/B!'TR
jJo4"Yj)"A>_8c4+FQ_NjNM'('A+9Pg]AJ!95c.T=K!dT,QBQX0nG4;iX"9K=SQKM]AO&^e/p/
pNfW2N0+VpUTA*,R@d.a=[-.Et;G+V\Pi^mB-Ngk?(A:FXn?<[]AIJUJ\JX$DN76BKm]A"qX.M
==_7GIKlm`2D6I,66@UHaeX0+R.6eEW_(h^\)]A+^G"`V/WHUElVII?^cr>2hkp:IVZ*N?9C^
u9=^=KP`6opgm6e8C:^qf@c^a*)L?G_Aeq/.p\b+)dRZ99)2sE4g=$*-3b<dlR9bp[s4<+s\
hJek@[=MT=3/n(+)s_3a(CDJH=TaYf"]AFl=8<"_;%:d\<87\/W9@mWEN.]A8t+>)T8PJVe2l4
8$*[4=GKQsAh-3b\i2\[Au8%65L.V7cr*bh\VP._[djL@2)hHNRTqUL+5,M[uFUet-nNQ`'k
66\;,\N_-\FDo9&aHRea^@-Whb6"SS+/D7HTNeaL5YiI%mL%MC[k"0&SB\P`/E;e+YoG#/Tm
@,j#:!2.sU\5N,g3F@7XaRR?f7T*&!&f=c-n:3ie'q_d7*C'W4J_Ys.SNAc]AVp2_Cu>Y?^9<
V>Z.LmnfT'Ce1$Ns8I+:VO)2K+"F,HKn0f#HNpN0d8>X5i'%:6),Q.YhLSLD)Zr3-FsgM?MU
+_c.SHOuh(W&M[JdqTQHd?<H.Jm4)?c\J#Jp?Muk7!i$eKUY(Da0:=,);4;t!XF#;Y_s7Q/1
O-bOU+6"Wj#mELXfGi=-I0?rSp<#Xj^0KgVm67^lYqel@J?9aU1*Tk?3Q+]A`h99X19[#=0Id
c6eEU/Zm1(^WU$OZ2L02q>M&3DIGVM[`-"]ABJ2<B!s.>ahp2Jq1?NbGUpS[nt-)gsZp9Su2!
3,F^Su\=lfoFW8Ng=+;7MTgS4q:UF4EgJNiKhDR[MF9BCpZq^g:>PAW1o`:L"ebN$QmOE\RS
ZZdL2r<@rR#@\FS`P-9OM)+FCoe&oj>Uj=p9To\Q3uhRCb)l%DO`"\Rk&m`m#RNdt0+f6?@a
i=.2FX-;4Y2Dj[O)NR">QF%\QTai'&MW)P8GqTWR/?ER.88*bg.!c=]Af'@:e&e!oH(>MO*&A
n8^&R^2?^73PD5%Q&)Wb6`j;LQr7+rf!Df"^V&@.MA&U[mkX8den0[pKV.]Ae7Vi=`S?T]ABd\
b'/dZVTI8N\+Y"*@-8NJS'?]Ag2[OC=<QPVVN\]ATA&b$=;#Y1BV3n6@,@A7!Hc(%!cKdkSpFT
.^rlC`!)2+3%TQL_`t_$^>>>E+;]ALO<`2-S[EfZp]AsBH<Cs<@2+OcaWU,*Kod^2reJ-s-9^=
$@iu`o^BC8?A,TSRoE%<TEB=8$Z[k!J7+:O[]A]A:RMQLUkY!(*lg8/p-ud07pl6eoMt$Y((<c
(A%-aIoC/]Ar=$'G5]A&e\IKp)THrVbMV;2aU#S_g"U-4B-TOl^D?!U)H.<H)oVnVg:#<$V4!I
<Ff=3=./'*2rua5_)4NG3lK[br:XGEq[5]A^iZm7+#3]Ae>gf*UaJVT]AaUa(,i&k^>h!I8QTPB
b]AT'e_+D%E[21Fm>*]A]A4F:ha)WXB1sk\G!q8J[kSSFu]A1eV*W7!>[\I=:8o/]AlR3P71"?De1
OYs%'B^u';J_68>rMc@_e;CUK^AGunk-Z1]A*4W-q.Z0%J3:ZlC8`gEh1`UU4N/ma(d+CUmmk
;--@,i_2ZHgu9b.D29'7`I?<s.uM^7=)BXr4/#E8&6$>4oK7DTrMRP>DtED1RWWrg,0N3de0
o=D'pk>Cash9CqkS:/mn!VpALKXUL&c!Gdb!t@(HYsf-l'O&@:-Ek\E3d/K'F'DE$?X=^Y4p
*jA(S%282a1:#e<c;Cf=&$G7^C0*(#^OqNS=qA;q_%c;"9./(-#^#-.W7E"$5:M:6%+8T]AH7
BQie1'@a!upUi)Ot3KskJX$'(:2c(u;[NNEeEB6r?HotImm\XA^>1:dc]AXe"*mL)7:A_cmgK
0Q^fcB&X7dJ6.^L:L0,jf(kD/WYC/%ODiWU3EhW/8l=NNPW=)_kOZ8_l4ao#S^*A1H3+25Jg
dXcpbc9DOgTaI6j&lY7p!4F3-XtnY4t>Lf>nRrHi`?;5_jOc40P/\rN('4&%:;Ri(PFnXh\]A
f3R97Y+@us)X!JKH_i$TQI(5%(E=:@@.--5IubbK)a!o9SaT-+?.)T<30H/rT=p%H)>bR+".
!P(RH$AZ@d;F^+nmBTMgs##^3[d`>"BZA'K4G-i&KKVS_/^;Ak\s;51Ju&]A&e?)hjl3SkO56
-K^6[(Y",Le9)[HICerLi\&UKfV'5<N8XKUdG\J>CgbA'KORR*%/#m`E2pQSr[2pE(Bt*Wum
#Ck1?&J%ehf,+NZ[ac#k'IfG^D6N,+3F=?2S;%4dhWfX@[4\=Sr[Y2S&i$Zl=Dg_S[%fblu>
=VeZqf!;V3<=Us,jab%#+ur;Cb4@Og.QGgm9!rj3BHNqEXcL[[4V[!BDmrtsYYScR'H*WnLi
MO'1oENlOF6ljL?rV:*Hk3]AER%.9>P/7)\9/nNY?D-/0,ip9\n7h+C)EVFoR)ag@5T9&<(m7
CC8#lS=EI]A<4CStMfqR,!7eVf'dIn:DV$9_"SOV-<9UYR^o&1b5\0YMO\)8MJ%4lYcW>/20a
^]Ar<&^I-]A3l'$eAQ,&(c(7ed!iMh;h(OEe.(7+3pl"^(B:Zg+p/=059b2\';o-1h,T$OBo0o
Bp)^)\U`;<<]AQJTD_2lrr*a17VZ]AfYsUkVP3!\8*u*OTeD%/[F_>IUm<0UiI4I7K`*]A#C9+!
bt$.XKE!SX$BS+Ge*IWM4r9V%C&D_,#a3km_qj#:4P0#O>.]AufbO)C=U!F%XkRKLBKljN+*E
4e@-*r&e%^5]AMbu\0]A%gF6l[]A)&-YLQAI'Vl2T8LYs0)hHuZq$1\^LehUXpg5Q;67S=bgZs1
<_XOT<l1WmCCR>g#(U(t8NB]Argn@Xnr(KHIL#/db939+S>\2,;=6R2)ZEE+3dobei9i.2$(N
B(^0hOBrm3)GN?<<(h?<-^cS8ZDthWM?dE"h%dLJ.i9W.qIH+KVP@J\U7DEG`m]A6#-D.l_-T
2/QGT3[%r4D;N4^h(F(B]A,U*0k+4&2j1SrmLIplAd2HiL#K^pkF_4hASJ;VW`ZT;dW,-0hl\
^u=/0#3jr^@,7k$BXr,\@<$JFRibk>CJhCp\+jDrC]AfKM@Uan2"']A_TK7K"oA9i;ChaN_Jls
l6F<':FXs"S_"`*[8is<@`/1ZM5CQ%9s#7_rX7,+hufmq7[4(j/aF9UT0[DH8f`<YcYnX^#s
?!'*Pds.LHX*XkdPB.?Lair6Z'f=-)lS&'bNGI[t0AQb47+js4N*n5j-R1\hI2_Ni]AR0h]Am+
X?HL!@q!&h@H;bpaSE[t!?Lg5<!p%RlnmR;2jmSOu#Hu6.H`WQ<kBW!=grikf7%Gt>jNt6Kq
kO%c$[X?M"dP):-#$`?q7fag6%ZYtFMZta.DOlFT__?@,5k9-P(-cR2QT!uoC^`%d@=8BB7=
7@DT(u3pNc+t-gu"hiL?A(=J!Y*a)WaMDHae![:`%3%@%.&os&]A`7F=Y[+Fi-jf\5#sC]ALaq
`4C'?W9*FWjGl9%ZcTTKH-XOB#5^&DZp%T*H0TfC?7&)?_\^$YZeQ;o_\56OkAm;;+.Ag(Io
:7sM[VKdp<]AUI#Z#cB`4i"8G,XO;6H@;ts4'R".sJd*s5:<_XqmC9.&=M$c(XRg>Ckc2UqIb
eAd(!b!/bHAlM?bKd)_Q4Y"O!OVu?_!/R+2u)9UOS^Y.YD1"1tXm>0%VaM"J=TP>m%Ze@`[@
4s&:lrf.G-l&=C,Mqu,g1ikh4p%/<Xf!;1NctE4Xt9^CHr">^m!!FZqo@:PDa3B_fm3J0GZ=
6j-&JREp62E.jcX3fYq[q*88?mKc,(dT%81ri:ue-9G'0J+^%!,gkZk]AFr4cJ"h8%0rHAO&3
a]AXj&/AYoGQFD:;l6lB3L,-;"iD+$'Q\d\&[TgLqo>>C>m#kV>_]ANHR269RO37C#0?/\240B
GSOd%%>Q:um$AVNB.%+Xmt8W+P3fT2f2R!r>c-Wu<p:L2U\c?&S(NLo<g3@eqaBT/:rt6r2F
um`AE0I<U^GFeQrd9b:hD4CYOAdH?LSlLq2<>ab"h-Kl$?Z`Q\a1sr2:$k%Rc18IL6>/Z,Wg
`"Hojb9\!).i-g%JoLDO)g4k`mG.^dl(OEo_a6MI#AdX=\d=Ba6-:>G0V#J\PH;[8u6V'fho
a.f/_C;!A^U7UGjS-]AM/c<B5+Jj]AItm47m9O+E/FH&#j\u_)#fDj;#Jh+'c_9o&TJ)qLm!Pc
_Ko,Lno]A#T?Lnf9T=%.=>)V-o4<3=2I'tFlY-iF.XQhO4C[;Y'*$GPl]A`,9^.):*"LOCG(o(
>W8\`jMJ<'dHC8\rrpULLpl17:HU*+-DV=j^>7eNJ%&]A*j1gZA4J/E1!YsmQ2cG:C'o5YD%e
S@2))+\$W#u/N,t+$F[_)E3fAWMZt5^%9@dlHT5@3%sU=*UEb0_8S-Z0%a<(s!W>G2\'-ud!
.(8NYnAP0IuRDr0K#mj9Y8\>Qek:"9BXj"J0cuI?FM":h2S&\)hK-7E]Ae&u^m1g_"o<&JR(3
uaogF,Ua^PI8h?F14`2(hFgfmNs,T&J:!r33$JuKft!C/HX187T$`P':ZnsL#.=\gLi1>:C*
V95;"`5$VZV7Bt_j2`n#5#;39T'=8:b/![T*=oaBi5PV2PjE%TAd:ebn$\Oh]Abq?jk<"57)<
K(iP=FF@+lj#e"@ak&*i\PiT-"*5'eq8e+O\l8_98_'^RdQug*ITt?@;S%s#jg4*HjR]A@F^T
J/phV<0hZMI(skKle-7*MdL^d,S'9lf3etp^44B0e;S.*,7)Q1\UTs1DH$a0mJ:+imoKLVW,
1Y,"hW/8jn"b+`!8[L/19CcVg-QKfD^#mGaZ):%9t/+tP5<91g1nmneAR/r^I&VZPYA=Dc\Z
J0PKL/8(Q"%i.&iSYe/4`?%C,cWlCc#LN//*>J<;JD\'2q]Al_RgsfW)5HqL+JiU(?a<l#+tb
6IGhS)N8cG&8E3,$9[^Cna#-DMn)r%?O^7O/oFP[@hQljTFmp'YlU?dgW;n_$>l2!me4at1S
>NDJXe/dqW3+0depBO0MrT?ElM')^P(pSU="5I0!^mJ2%p>7Y'eQI@+SA"L_cJP.[o9io&C(
^n5KH'f!Os$k$[,-OHG4no\48hJ;,64Cp,"bdbXsaQcRQDH(YmEq##')Lp'eO8Go_`Mn1XsN
YAshnj9?!puJ,"5H%&`D!$B60*pgk%LEMJV*<*K)Vs1Tja`/a1-'R]A5m)fR>;f)tPs^9h0?Q
+$0Hfeg522igWL]AsUXSbXA5:@t[;+$41<CmJ"C1<\HKa$Ks0r+$%]AR12)9QuZ!?Ui`$h)H)c
Vk!8We&"hMa/N8e"!kaI1b6.'DR4`9a;`$bk5.`=13pm#n"/=,ghn2]A^E+I[FnB]AAKZY`(P.
OoV3<"Rlc"9A!,\o-gWE*%')V(_%S*6<fkEP]AKSKeQd(4=-(2or?=Ue[PNC@F*^`"1[5U^[T
j9\dIc\5)@;XZ'/,472d!NiO?31V037W\MgMP;K<bhS?csElNjQK$\(]A^_!Hr^\-hu#P',H@
eu'VrX$lN0&JK7\dN/:R)95B!]A'K?>/Vi^`m9IcZfPUmiiCbo057.8L-PaBSsTi=_*EPI6"<
CgE_7gI.$1>^)c3FHFQYit?0b5s45_g=]AYRVKZV+"m%//h$@2;/2D2um1<#Q#A^P<`&9%ONu
pG31ge,dth^<_X?;.%UqeM"9>cpprJ9ed2Y[B0YIDn6OnQ*-#iLo!&D1!l(t%sqljACd+b_E
C1h4`T4W@t?CB\/7-Pl^0,e%X/TK>b0VU:'k!N=KL5K\k:6g>>KHqOFD's\n\/)'@kh-?]AN+
5%^TM[pb)qgT,mOZfrD\'G&?YmCj5#NU*$hN@)gk3BI60;!6'oN=0`A)[!Y)!f=Akn;=]AS3_
9grQa'Pa3,`3mITRA8!HR[O#@nO]A@%_N)e?:`-RUp\U^;S%6%FWo.bR_$m)jIWN%.BgkVYcr
X`MuA/i>0V![WmV,Hcf4Z.]A3,(@TjqTKLmrH*UX+T54e+("AH[b.rO.4SbEso/)<2sU?-_Sq
RJaR=6=tnO-ai$g9NsLGFFDqnX@FaQc#*]A8JZpcCa_Me_e[W$?)/>*Pnj_/"OM;f#,Suk$CN
->@]Ae!V2XjO>:(YkO=$7b]A?c#jf,^IZWe5o)'nOG[u[8$?qc"fp^D=06l)SCrOtPN(Q=O5(W
`<_ZK3<kbnM=IN6=6dcXJX$865f<W^A$lR0$!f1aPmZgT?gnS+)F)S8O7BUQk$g^Q2[amoej
u*V,4(=Vk`pIDo[$k'FTfsb//dTed6$Vbqne(2ko>anT2i1">.jEA0+a]A3J.N9$f\tke3$,G
SGQ0%k1OnV4.?1CT;C]A@kmP*uu4]Au6Z89Lq/tIJ7mBlp1Hh=OkVL#]A3enX%E^?C"ZoH^(GOu
fI4([9U'GJ4IO)!\?X6u..CL7GH?7(\P(?'lm2\t%,fEuNrb'5h/M5dgDWMsjfTX:IO!e2L<
b7aCJR8SD1`?Ore`Vu;DL_+#m4XudsSMUhhaN2]A9@3nN#7InVB95VHd.!YkNqK5HAC.Z,Dg4
,^Cm1O1<BV:JJi#Q_#=b'J7XF^IgI:V^,*IM>h[\RWl:".CTmW!8?Eq&1s0o8)c[l<fks.o2
HC$ihF+DP2CH0()rgP7)g/P"G!YE*RrN9a&#4jJi4C/m3:ep6H3k40bF.*fUXcp45e>90p>P
Y%6%3Z>Z7mOc7F:KJ5?C34HXe_mr-s%ie4J&-9i?ENG/VM#jW9ptplcF4TTW_!05YL46UOY-
'8L*Tg93I_]A(]AOW.C#oId_39T^[gJWOL0Fr<b-l1inejYKJfj\fLQO`-bcV%jSr#gT&g;Sma
;60K'g3XE+sg<3BWaH<'9\e:\mIL*2j-]A@RU.(nTs$J90Xjs0P"4TNd3s(%f9k6X)=9b3sPs
OT$!,odKJCZ0lPMN>di>iXamEp8_ZF8NY%<fM,4^/+;G_+6=P+^fU4=JGR04P34Zb"6fsBoo
QC`K7de;p&l[%?L53bS/1h-5H=c1mCi"QGJe_W*fuG8>[<$Q2s4[s?#hW5<6Z78"XH.dH;3f
V(;42s%AMfJb(_B'+g`/6:$b2+B2P^Tse.iY40#=HZMZ_\$rFUmhR1JbTNo@Scp>Nn4OFFJX
%F`urh]Ad:dD>q.qgK_)_HSB2MbDjAm5N1!#$N*j3Lrd9c6QS0,%c54Bg5BM/GB7rd.uh(YQ8
N^gFNNC9eo9XI>96,MCTmDa=A-o]A&2"Xh<A$X[(*g1dBsOrBZqk;lgc4bA\1k;^[*1/,bd(h
q/DgrJgD8GWb"CP,Oo;1BTD[?dkOmbsAfPZ<arjeIBml5'R@Geqe-8;UAiCS/^kAPAVV31d1
biRYCAF4_)Li\j_lRi#6VbbHDbhX15rKNSCsL?qp.`g9(o@K/=Eb(J$@V5.1eK:D>o_so*s(
hO/*B)W#/lLuGU/KZ#_:?.pT[7_!\_R@cDQcZ*ig?fP=>,32-7$jEhBB2[^dB]A&Q$3Bg.q<.
md(]A'gX7rl!b]AOC^gT#Bk2oDE,?_\1pTu/&Dg[H4I!(AqU,q(qK_S2rHEth$l47&=BKsCmRS
2c#,C43IBc"3Ck^5b7gZ`it@p6M0MhR"Gjc(MfQV<.JJ]A=\UD6l[tN0[W@I>%>_>9lk1Q`&Y
8hlR?:-"5C<r3g#6Fmq=uY@=!L?$S>,"6]ACrJ$S5,<,GH8jKoQ8^X;^\d60`dLa)FH.$]AoLb
=bDUL0YU:'I1q4Y4\;IChCPqg,=`HOinec[Up7h)oS`Q7]AoZA+/V,/?KSQS8?Xm:H51$%"j'
>il^N9P9ZFG^N/[RQa:W?h"n4TpM[Pi8cfeCA4e\MqY1ZCSS_f]AnM<E#*e.R2*6B6LC3p-Kk
`-N#Fgs%k=gsl#)drc'gG/;j:F\X20,]A=m7^h@u9Os2YahRj;gk&GdI0X"&_YB_<'Ecj`)*P
$t&mAJf_X%^%b20[7HX>'D,6=ASU]A'b!!K?O]AnLR/UZc*Tn)O]AD%<n(I<neg*>."]A"14%keq
V(Y^=c_P5RhAR&s8FG`1?^;?C>&j\6&Z5WNUNcl/UQ8Qs#^Q/Nf8=D[q/MX#,[$>Nl:LGl:6
S?**fBs-j,fa0m0N1l@PWSd2_)fj]A&pM(^n[)XaNu2tY]AVdlujW2(tM&3Tb\tgBG[k3[dKt!
K<3[th]Ar7\W"h.<VN:0MUKf1(XHL]A^IEA0DHR[@L\,G]AMh8+:`HQJbT]A4Dn(jPkMrf1/-/O,
9"_2^TB6Y]Af\Z"La'Sj=IjC:sE:[[HD&#SpEVHG]Ad#UhHi[4L!fJL[\1>21pe@CY+K-bAY^^
]A/1#j5.@DN`N0m;mdj@.W]AdmIq?1mW`(riS]AugW[@[T\[D__5Y<S4"/RF.YBFUp(0?aDaenn
kpQe83'SANkZqT)f1M0Q"f2#$0Yp'Huc^PY,^V1Yd2o[*h[Se[NVLb@mc(?;"b_$+YS)7tQ4
E<X!U7"Asoq%!%V3!WOYs9lYN)W[Ua>Z:fZQeHha\7V"?!eO=%F'D(.mLc(C"^\hqD0W!aLn
M28uc?6+/l`p3,7g%CrZP/d3(m:Xm1:7fIsgf%'N:1?IooA^.h6'Ac:2B7ZuPjHNEk/;TfC4
Yq<cR^p\B/!Q>:C,V@Jf#CO8anm7Y90<dB/%44hG`5t5X&e;;:I)$"&_5i4O9/T9p9+u%@+J
:pI?A1%%@Ye#S-2Hu>V(E"7P9Vs]ASKBJ*g/%\Di_D)(h:sPjSF(H8c(\<lHIaTLm?M6([eIU
b$\?5he4nEsL>39*LDl_0)+Z@N+$OXSpqDk/q*?F3MW>0ellZSsUn=#5=Pif41%>Dk!IO&/"
.Y$Y`-/=F>eA-Wg[C2dmZ(bXDtEaFH>@,:SKqXpTZ(s,eX"p'$TBt7h^Xo!154*k$(gQPl>+
<.Tg18lEJ02Fl:OgV=W+(G0`-`W5F8eEf(KldCL>%%$Gp(5bg,\.A7IS)>4BhHW`-_t$Q4@p
Vf2D&<!ONblslKZARM-7R7'3I-B5ml\I&SAFuoQ=6D"UQRh*c'fYXc5"J8g#,NUDF6Yl;$0h
T^Ig/*IcPF"PHKjH"spLs]A]A6D@c/>ajLFYr[qQLWF5eLP9#H"kK<:p@jkr3#4\4i5>_Pj:gg
ARVGQ$Y\hdU:Uc\h%*hRC;qe?)6b]AfUS[M@@q"aM.m,UbiM<'2U`CIh=)7M9kld5sX>@sG0J
h>GDj-#e`VkT1"YVC`Ze\.b"9h=_ZI$i@\C&s8h-e0I7UksKjLR^8gnbce./AM!9G`b9ASC^
*Io28P/e'4lhDF3?)%)K98V2dlV0[=Ce'P\qT@cbp*)Ze\7$d/J>'?YiC.>,@?$:]AgZJ+X@G
f&*L/%(i9Tau%9]AponWf8)/eAY6lXV&a^O_#8*28[bQNn/AA)[&YQ9,:a.'7!ac&S>=11K'f
5UL^8^VZ=<lPa.kr\T:;YYCfT!r!MPq(eXQbk1F?KWYc5F<>;P")5MkG"D8uEGt6YdN%i/5I
[^RS:$p@;t0pl-fG7]A)rOX*7$)50qpd\KeI_0D.$u+=XKRo(ZfGR6JcX0A%Z(+.S1@eY0^XM
5YZSBdq69TY3j`!Yp2b`8N@lIE>q^b?^ITT(JiJn:,Hj-)bF#(F'4/6)ISaV-r4Vmpq"ob_S
-ggp3eG;u(kuf@Fh!Y3kdZ1gkNpr"6Muo,l)c&Je2W5,T)rh+if@heC1tSh&N0gAIc`Mo_(T
i;K'kRpdn74"@f=oD<YQDCW"tAe!5U?0%FoiHrc36'0dVa["U7_BUp@I88IHV[6gUNf=78e3
&t6&@Wa,@"ac5o\*S^BMjU3(%C8,YjQ)6N0mQ?5(BeT#e&J4j'CBh*-ZVHL2Lc7J`C>1`(bT
fG+G,ZJHk`lC!%"[[-=jTc*N"714(fLl6(B'efoV5RJb5`9,WiQf$3ub2tKI&Yds@rO-sX!Y
B'AS5%H)V,**O*d=5m=klj[0-;S2;DLG%3Sp`=:?#aQ(g25j2KFd;kQ?pj;iRo>4lq?WkS>Q
c-kkA41quKGHnAWRP@BTiio:>9(Vp_HuW"YX6mLgh;-4)jI8c&`--4&L2I>.`]AlD]ARp=(\`"
cgg';n`fJdo8,0H4*`IcCqmFfqU!cuIWYkX%Z[tHJVB2D\&;f&bG#0;nn.16$o&<uD\>p%8Q
9:pCO0P*4,(RtV/9\F:7D>/P5u'1DB_)-4,K,=>gLlBUN,oI3*TXS8GW]AiZes\F.TZaEVN*c
K:[bIX^%Y%[jmL0oenP0KSVK+9;"eccLkOG@eRB"S_['/"Fu2'#jO0EG^j'U&RS)3-S;YgOq
8`2[YK'u:&k0/[mH=P^6[!mb6U+XpQN[sNJ%cifN'CGS"<MXT&*C:iqF'33Q<*Vtk6_peo\B
b;aob>Y5r,=U&S9P%J^(p(2hP:KR1?JJLQ_mUSHVs>59lGjc-d%-9.CZ.ET`^]A#q1*fia&8?
RU`kD48W%H0N6@[)%Bc`O)"?i^WHYF?61qE1phkD_gDLNf*HAE&Kq*&OHJ8X<`9P)ZR<?>WA
.7ajSa%..]AeFB(d),3)3)6R;u;-#5'\Qeq1AYK!@%"IL7ach;V4_%6HmtXR+X'8J)#o&'?Z8
1N&pT&(pa"H++#r=+\e;pZSQGF7%o1&2VR%E]AP2h%Z>\/6]A<144_KTsY2VJVS67$@kgaMbCc
4A@56/''CjVf8K:8RSIT^+)kQE4!BWeEu,J#_ZH/Zh(,+,rd+74D(Gf'D`)P_ZU0-F9)S8<S
YSCG]A&dmRRuQ\p"?;GA<B0$LOUYF+clg4.3A)e;R[,C,(CeIBo\g#mI3o#O"_BO\oC!0B/f=
l3k\C8C1ULJq.SJDfY;l58Gl[!'LJ5bY_MVRNZ13\#1\th.g#O2HJI+rXW/!TF>1#C?k8N=P
n*"5+0h#hN]A4.npB\Ng7Cag),1Y4LlUr`\U@5fr4lgr'Du[\6%^i_2rXG6K2N-IILb=dcn]A0
IiK.(cJ*Yt`ZMQSZbjk<kNP2NAD;NCi;T:<c18S`2H>e']A"WkLE3.5MbeI_.>0Usk89(d!S'
b$W4o]Aejs%[CRh@a]AeM'<6*k;&ijd@Oj8I_ML?lV62&^Fl]A3Q.<@[>Y;1R<]AR=;0#[(F"LuL
oVVtu.l'1Lr'pVZSVe$aod)Ygc=g?bl7l5$MC'FFf]A[S:]A09-CH8JhWH`#%fm\]AN.[<S61Na
h'DHC$i=2qL:6#SoK#``g=PAgoZTSJO)[iGHb1iH976ddW.3ji&C5`7jSI0E[qOM>WBD/Io?
CCE;ib:Hp"]A><-ZUTOdBct]AUdaZ-;cLs@8l!@"rD'n[&47PM>cFrAW4&G.(Z/J:Lp-4V\J"k
bXSdOJ`<Lr;ER.?[fS.t=h`)hDT[1pFL]A%HlfoM_KW&jNdOqV]ACe>b,Ik/m>Z>;@hTUT)_ZO
.ds3!ae$2>Vl4aFRk^5I^%T?L&['5M+H'rBY+/5Xe9ADpNS:pDR`O"]AWP^>@rN:MK3i;rlEl
8D:J#>rPiYr+b@kTF_K!;Q=[n$DU0mHkhSLf4q]A"R9qR]A;t(p91PcXi;t)H(``IIp=o_X,NY
%MB3C`\GL*go_BI6(p2OJd_5G=Z\\)O&?gj..8c1C+>U4IR-*?N^8b%Kha)PMo?s,YK&<.8,
ZR/,+kAa=Sq41(9nInCK40+DBso)q2BNC-\nAfP]A:8?<G^!s!oDJpa`hTE5?K6>6GIT([<L*
N6,U]A?OZ9nWQY;7.L:S1PPEW5ncTBk5lu:pUlWWd&?CHnJbk<qp[)eQ>AUrsZ+;/3<11[S0e
^,<ejJ4GC>')ui);B9LIa(mV@BO>_FeR'pHohK!0j,;Gp#a;.A6AUEYTF2:@?mL@h_.uQe?1
/M%bj?ZXAk[QIi!Eoe?6TZCH"b[D;>mU%c2ea?uZWi]AXGOS<>/Tnll,a_kY1m=I:J`If="&k
Hb9.JaTqDXGASjLECoi*q1\^1rY%Ccs!#Bjm!o)bMBkP94Z;94FC?(]AH!s!@2p'[<qG>Jq^5
LAWk-!^p-OuMqX%I#iT6,hs(>Gc1s5=.s^M6,tJ!'heKh#U8XB!&s/GI\HUC<<K;UL..TcuE
-)cQC0\8%HnJ9>FIA,"'dI+=cfq$9L`^bgFKE"t@&qbRaRnNZA#Jlisor,@S3==gTSbK%GQ/
V<.KDUAd)^"kXW\#pnI<;9AQj=ZlKjL_j;RoU?&=\"Sb_=Hghka8T\`o`>P>4JEMh\;rD=Ah
qbQMHa$%A*$TP^*p$mC<"Ao'+Y@E$fhTZ415O8<!9aIrQKJ53EfdeZ11a#H]Adq<%P@(WZ\D\
OKPan\fUbBT9.'VjB?PM]A$/q&.VQ)AI*UlsEs]AbGJ9U(#BYWSbl^0b=J2Y9+f!:QRZl4=A(F
lb338WE=,s:B1n,Itq]Ab:kJ"q3'snNGi9g!H4FCTaFAa'[[dYt"AX12(X=ERsQ02n-bZoN1j
t%,lHLX_E\g;->P*/?,dKIS_?sI>?MB^04#l+4?]A90ljY=lJnV)M\V@(5bd@8%7tHj6ENX36
PZ",[n:i$>C)PB>Dn3p3IOBfPM-?kA8P%ffum&Z)76YjMEQ"+c@7(bL!VD01,Udq`Ge#UL&4
W$5#O$?YQegANk?]ATk<ADsGC!h!F(Ps>*MA7+@'t=3YjT34)[B]AR9Ab0B?rUV:.3^35k%q]AY
`m$dgDe2NXJ@*iCZ@''Ma#E6F+%(720U.M97DO,e5Sme6762q3I>g\U,r@+=+5d4$pf7>QYP
K+!HueqcWK;97D?M!%V?'.dg1&'.IBn4So`P4\hXfLdCYlBn73LhETK'aX(r5[R<jf-XFE_b
"KX:0LnI7\<$OaJQX%VcP?[j/(S6K%'Xr6K.$S(.l8Zu7V*g]AM]AS4&R.g$1EWl*11'(D[6h=
ZRZ_S2#WNS/RZ]AA17!FKOmQl&/fs;TU5se^dp,XYUsb=W<YhSApT%94jSgW2&"2k-=R<SO;4
T,ITHU;YVar.%,4`KSaL+#1Vg-ffh<>&,_q'>j*XH>R-Yb$fX`(1[WENBF?XAMmkZ=,`#cAn
1."<O+rr+>/#K"enjP$oZuub;A&XZJJF^4UZ7fhR2_obfccb8a'`>!`FP;M@?fA<Z!HFaXR(
UKqcb\DG@L2*l-i<3JqjTNSob`G*DYgUZCC$^ulIfjFp2W&YZEF1e[Yrq-Z!4h#c`F%-9,7,
h/TJg2&3o<(qs;B1I<0(b)*")`Su&tq0.4O.DTKi0<uG:_I"h%lZ\lYU=JF20F4]AMCot3D1=
Np#.UZ+EjUJ,`%#`&0[^E+i/1[RmEYDchlENY`[UZ%HD\+S@^%h4?/.',G\=0[26EF1FQ,MH
,_,HB**)*0)*3JEfMoos+K"[j7L+)FV&nR-[&mkJi]Ad[I1-$_[uWRG+ssk%>,U"Tl;@TG+r@
"[&r8n2*?,CR0%UWd7"NXWFZdIH@e-'Gr.Op^NnpO8?m.,8[<Bq#=Lc8Dc(;YQ;OP=,%.lMM
+@*7MO%Cfl#Jtp5cmW4mkq,Ep`iQ.5RX'?4X?0>s,jda&'=dHf!8!GqPsM9r4=W%5Abk'7U6
t/F2;?=d&;Z_K,75$rR&`A6iFL4Z^oca.OFhh%[L9BS=8Q\@_.:OQM8jp=sGP%E)"\19J/m4
esmpD5-d#)h#/Gfm1=IY!;k=eIIt&!T<Dlpt2M,Xs^aLR5f9F(q.)4=)+9),iSAlDe;ee..[
\g64)o>YFld2To75us4ir8K;#_0=L[3K1O&cYbPOkO.p^pu=M]A(DCHuoLP9rmta:>LS`q<JW
@mZ",Y`$^jAp&LiH=hjQMR7RKpn^pPYp7s-l<gLWIJ"d4:G9i6g^Xs"&c?P4GEpC05%o7Q+m
Q7h95%*5EG6_MjI:4Wil&B>V^kW*SKLj,f)/&6lWc]AZo*N(O9LZ5`X>\KHpqg92_c(F$h_Z^
I3kj@[)Sj[&Hq>M>@^3OV\RSR98!$H?S+E#@g@R4I^K5/;8LC'DY1a8Kif:&2<>B)9n'^G<$
VX;al[d[N[PN;T(+,h%<7iVR?%ir-EV(PK4F>b;#%>i^#m*eb9mBrtHoCfDEBnJlZLLLmf5a
DFQi7[d/&J6_%[K&gb>?rV$)tD9I9KT9FAR8.$/!aY.,rq2>*J;F>2LRL);9Uh-X`ZY5L,l#
^1HtuX'`W2`W*38P98k\.Jeq=i*:fP<W@DQX53$07GC6^U:)+EXFmb:oL)p,rQ`AddL:;?p"
iO(Y62@LK4R>r)qmG_l1G3k:),`m:+rq711jOh9IG(MSEA$&T*18,!k-84$dOEQ5C0#1dbu%
@r<&2ngf`1k`kT%h`!G<%gnfN^T5e2+Lm4:O#@E!d[HqeicF%W=/(Vla]As%"[a!d^nRrU:3o
@Bj$J"eXM3:1^tP'esp-`T%F<`j_;>NCa]A4)1'<EMAIL><9Lh.+5W)C-fYoV+7sUH4p.^`Fk
.Zh]A]AVT-er\7G*`/_kPG]A4!?i'no,Z5Ji:4#I^&PS[[8/D):*-R2<"C4*!o(S^<DPPbqOVYA
hhG'4g?e^LV9i)A23b<h(9><6;.>W-;T,i=jsJ]AO%l82T!7hBlM4J>iL7qWOp6aC6*Li;7^.
Ft!rW^pANigbN&Cg5Pj;=jN0n`Xn\NG<:%`YCaK(iH5b36ueZ)q\W*bs)uIH@P)Q^msi(N9$
I>K?a9-:U=h8T8%!OO5<@6JdkY#;:90bs]A$dm]A<XIcouWSEfuo(NG6f9onfh5ZR(R,%VXS[X
0!qDIld=s]ALdIN4ioL1,&2n(Mc(2pg#EA2Pes'm9RMWZICH`Bji]A(XY;C,19.Li#gK^c6'UJ
:Dfo@V^0!IYMA2]AL-q$fpl5?0R2r4=HqN1;PFU`sY6MY,"d3djKga(^\"n"e4=8`h\GXKt!.
8]A6\R$mbI>o\q=B06ugudDtZ\"Y-J-mQM*o\+[Oocg-/!R(3gV6=GZWX'.LP4[UHNB,n!onV
uP+P2:DImIJ8V]AnkpAEfY$+GaU]AhD/LbY&6Fa>6/!85:`52L0`\p&6JPerX8BY6dR8rVpT4S
78mTh2r"EP-=HLl0iF7ZDXZA4sbCtU'/U,/"@j!TJFQI>4[9+hD,l:55>Ii'2im<b>=12*O<
t>:mfX$2KJpEuj_S(r?ZN36>hk,3RSHhZS,`eaa*?"+`k,A?QJWR:`(\7u(UG]AG*Rrqn-TKl
mCbU63EhfRBH5)Ub#9oUKhr.6fR5KHS92$N_j`tgUfQg76`9aoJD2p/CHI!5_kaOI>sa)fB\
e`3.@/IF!+C]A$c@EhceG:^Fg5m;A58Mo*n)/m4Y]A^7V>TNK44^Q.9,97tSeUY=K*boPM6trL
SM5Z_>>pf]A/ZaUPr:(<:WpW;/)-FhIAK9R?/Rg\aRbWNT9hEMf3GiH;'QgCAleSG`J@]AmM.o
(P+3CbYu^[l<.RHSAbR[U_(`t("":CA_tuCuBcCdm4TX,(k=-EPF+*hR)Dg!QjarO6B8'\[^
V[9"I5?q[A+O0#j?R=pG^l`oIqoZhO(j=qh:$#Nqr&p:hPAA\eJe[uCuu`0qk)FUJ@cRn\+A
I(AmmO]AH;>*&Y\-E:gZT>8YdJln]Ae^V8Pih(3R;Rok:0f13r_S0W_)"ht7X]A9b[ZC0n;ReOU
m7s=9YBuF*#*_qffLO(<e_iMsC?^ksX[J%dS[gI6)009t1a.;n:]AtkI$SVP3a1?bkpcehtp[
DA)\WN*ZZEL,#2+g2WpC+h+\>0PdC:f?/n@OjGnDH^LeFTNEc.P2uE_OJL)oAA4[^J>\9o3`
.<oZT`L11KF<S1ol]A8h3l:F<0&/5IMPo>a)2>RuRN)CIG71#eEAV`LtOkKinLI.D1cNan4(\
TLWA57#1j9j=pU]AWlgj+)=uDrq-j*1k%I&R]A_JL%_[fWOGS3`d)S('W+]A/X%M*=Q)K=\NM)i
aCG./XnY['<gV,K2P%'gTfPCCFc]A?i9CPlBqce:3t[NB)*p9NY#&&(82Z+"Zbc%$DM9mXVj^
5$L=[=_R[kGD-/M#6]Agel^dVB`h`iJ$.7[@7gSWTj@HFTF?Z_-?h))-U!/b=U3ekt+DZOP^F
4_G/Tt_(p++9L'7SiuP`hH4,=eJ3Ku"DOWO-@QQ$otb'VJIY#hs3"Yn[_Tc-<!nZos0meIfb
"@I1Z0P$kp$E>tcdB$3"IF^&!_C:P]AdXj#!_bt2M^%]A%kf-tdb$<c60Q>Ju47857@B77AA@M
`="%"MW7]AbkLl0T(hnb`Tt+\pmf[@Y&b'UG'JeN![fkAF8&ZoV7bQcL[,FW9`14il@4egl['
#m4F:Ac(.3TDG[":u#3f=+Z:8Ej$>0OsP$WOF$OC'R2=QgKqMKr&UN_9OT^10Ka1a&kVCKXi
'KcoVUcuu^n#u:13S-MN%R5kp79s2#ETlf%a0Bs2NWIn!&5QD.4VXLH#56lgI/U`K7IZ(1Em
%^doD]A,A%r&&M#Ye&5P_2^E.7,6H?WqQfKKcO)9?5?bMs+0S?G+]Aa`^WY^9i'SV4tb3t"0rY
)GA_iJN"Op\3$Bs6&kb&BfM14*le'Xd57Xike'<">U306"-l\A9)*l\<a=!O>D?Jj#6T]ACcb
gD=Q*YP*Qi&<+qML1q`h2VYFpmr-P^L"VKbdMco7:b6IP-'tWihafm:Rs(%VL9WfHh:\ZeFR
7q\!T]AX'D)<soCGtg3E)F&'7NQ7fT@tJK-^PCIY70_FQ%A!`FQb'Ild1:]A:HV<2!Lijn%FS)
9.FEVeA7q\VR02$ODU3;",QA7D6.Z?V5Sqj.rFN.ICn]AW<G]ADAoUXq5YW$B$r;T4@^Jto3lr
.fV:G&$\G.uP=&DG9*O@"UQWhnOKO#(F=f.(N1oU+3#C:(j7gC+<\e;a0#G_/_``TNZ`<s/?
rC95dbFoF1X;;8$1j5o2.:')ACq"=E"(T+^>>oXbA[P^g'_AR/h%&QIV4m!MR*,O&Z.=1]A//
&6:.RtUOt[tKmUS+Wo&33UiIDu\@+p3`*LYohCg7=7(LB$3g%cZf:OeWEGsP,^^Z.c6FrrL<
?J^S-nem\KJ89(%@8a5-a`K&CCFU0$/b3I1-u81PVh6`a5d[h%8qF@ETq%h'bKfsI1l_a"h9
s4[mZagE'>1&Mf?Um$7>otUK!/L5]A7f1_OoM_QAUp0W+EX8h`tMuW8O<I3nuUt1$Y8WG=Qho
O>30%bZ_:TeVVs'_`;!l'('3'Zkon2C'E:7*Uq>?o(hAKH<b!#P4,5]AkoLId[iMRfZjff0G&
a=G!#dpV_kf>hS#C!mU6L*BV;anukdmQ.Yk02D9LN7@5f]A*BO^ihfV-EFbY\;bBLLBnd\R&3
rSd+bZNt"cs=QdDln4?\4Z1.#&s,-g$Z*l@C\RF56h?&_jc\aid-eNJhkSY9(U540W\?V]A]A+
5ej$q_jO1aI'FX^c`2Y?&:YLN_[ocLS'a1[O.F$C<7*dF9GZc^>CCCYSa%n-%'*D/P9cPs!9
EYSE4]A#GrtXuA\5]Aoj2b8,.jO3r@RE(M5e+oYm*^;rRReS5D)js7;<3kP'Q'DY.;bKntQTE.
'mcR?OMfep[.^I8O(Jp:Ya#+n=6Xi\e((LZm&!\Ih!);]AiG.1G+>A'2^$[J8>C6Z<uufr^\i
)NNUK:odm$p;VJW&Du'phFo=YHs4#A$F9i4?K`/H`qG;NcqMtE,eBrNUPWhc(/iFB3?,=k[i
qU:YXTMh?J+Qo,j]Anr97V2Bm(I^K"%F3$ms'"RqrP#$Tf8k!&J!iLG?L,;shf&MfqsjJX7Ar
bU!*VJFYaTbUR/;@r7OjA9dqTt++Ccnq]AQnJfhdZ'Cc[]AO*XS,j>Y7\:h,`=FZ/H/Ee$C?!A
4\5FR3J/qJ8X@X+F.J0UrHI/p.J/pfWV`<2c=np-kPr,7F<5GmlI'd]AJb9[O?g(32Yfu4Jm?
r%]A,(tnZ@4MLWo#pI&0928l2?##A95``OO-*1$MK):LO2#.4U<XB3Z5nS%W*0ta)quaS*l\*
$0?(V/D.D;FMS=[R%F:C3a45D1Ea!-lQ(lRi`JlqrL":^5;OaV64_9&<4o&r@pH`ipk+2P5?
-"u%d*sJ'jm!TOn=Pk@Rd9KfPB<lbSFDeSJVUu+h)bA'dr.8\fj*M4VQ6$EraX6m]Ap??Gbfj
_Nq;;.I$R;ACU741+=2njuUuab^BKSUblf$(@F&-92G33rfmW*[F*\6,3bY+&oR*_GB>d+,b
k4##:E9LK/5A,/"pEA4%O+mQN;*3[a`aulUj>![@ZaDi@ojY]A&qluEC\9hIdZ;L@j0;)[\AV
D!VY6?ELQ/7)6<\-u4F\^pi4h3DRF((q/(2qe_24fXbh&@l_5'hAfo[l)T0(48p.BY8=([7e
0n?f7)/Qn^E5r3p>5m#b(W*:>[12m%"'q$L9)?RHP&Oh68kKWJ]A"G"+b7'ZF=la5(CS:9J*R
F<+_MaN2[%l//8D-:H)T=dpWk<Fc;`Q-PLk&]AI<XYe=h1#m_D1U7(rq!kTr0iLjmpu`t6`5l
-X6!j>tS'c\j=+j]A)7Vnj,6sI7Z'\C)&<QL#\_mp7r<W@PjQ+e&_`OoYQWGM=-?L>HQ#o)=_
oC!r+?_s+&BCl=8/hg=O[lR+7H-@s8#G"53.RPti0I&K*T"VS;o9-0C.Vh4dTO:gjpc$/sa7
8FPOKtG.ou34MY.InRaP1\71X7tak8ba=:)0,=MYr--p=6<Y6MiGX'lnYT\UI$IHhcX<"s0N
&^hVE6C>iDJC]A`p6]Aml,-h"GkiO5!=E=#cLif#'D47KnroZkXbmg9HJo0b&W9Y@a@[E-q"*8
fH\`b3S8QX]Ami[a)hU[<(N@A)\_3nVZ60u&(`T"Q6<n`cT`6#H^J:%Fk<?8a^k+Xr^Tn#=T.
6iG.iITVZV^X+sCG2$\03mT"%E_p'*-MS0m2&Au:/`0hn]A5fLG8$0=oOT$IP**rSJmE[1E5_
Z1.!CS/_gsnApsN6O*2b(oO=5MrV"&!Q;KMD<HTVF[l%.<3#'C62#K]AW;g-kS6`n@MsjHAZt
Tl$9Drga,CT%)=U[T-6M_dO;-gNW9=e:bY).8'ELZL0'/t%5!Z\^Xhj$V.3:O*2@>aoHG@*"
o)#cbd>"\ao\;`[9f,u[8i4Ad\d<\7OE3%^\=iD'aDl.PX;5nIXp&$W5%@Ukhmt#T*rL@cQ>
]AZ-.Z4*=sT4EH-e@Ita/3\+G%bJFE$tB/?d[<:F99?Q]A%6.d.h>Fh9X=HkV_e&9=Q+@i!IQ(
bO!q,8?l`ZAg:Gugs9Y^>BYYbeG)er5Q%^elA\SeC2L`Q=S43SS6iFm(+qRi6oBdPD'omb?`
5OX<9<i,Dj,@>Jmo_?Q&70$?fbpArJ'1D5$P)^$5c7nZPN9TfYC'gd8m'T&rHF_XJSC_h=7D
^4s`6CQiZ+DBhP<"9BAuU3F%_WD[9H$*)f9<3<jIc;;VgY2lfAucUc\(q93R_USL`i9f]AIg`
Q/u?H^T%!;9L@;c9Xu'Z*.aE@s3Qb[=d[JXo3tc<f>%U:J8V.1n9XkM['G(8BEX]AmJG^-rn%
DDE54q-m9Xu@8K>qlRR#[b8`>l=@lKS7qGeZLlb`__bI<bA/IZB'!bRiP^2@&;]A(:Dr]A^]APJ
lun!!HC!Sd)hI7A'rN##OSPm\Wl^Ft,sD27tMF&$"/F+]A$#k[fY=_\PSP$akJgFi_d!]Ahk84
3%d@5%/]Ah4kOc$`>Ydgkp,T'HIBVT,`+RS0kU$tXfR6cJQr[NpYiA;))!Zp\?Inmk*AX2OSc
:[==__Bf)2Hq[IMZ2MHV[tF%/-qcr$gQ@2'\>AMrU+Ae9rWf]AEd`@m9F!KdP(b%QlG\SpkDO
md_K7^U$\ru^5U;4e%CTGHJ-+60@O69UA*.Jpr1E1%.t9?jf1*b$Id%u8q&j-o@%BO&GOJ5,
).MK/rTIN4XTRGe:K+*mUQHres;^jL+41&N&,?l)ddUn,-o&<F3*"@&0`B@LnVs.J[61*I-L
rdNZj5/Nld4X1CruLT"1:@J(D?"SO+SKkF7q\rk3l+MQMg:oI&JmbJo5[F*ZoH`Phu7Z+RCm
!limKg3t[akNs6GV-qU;Q#ne,NBSMT(P'4m'g$>U!LkE^.=#+>:-UfA\GEOGXmDX%1fHc$=]A
\R_f@_TuE;2Fc^$O&!.FfDJ4iFc4.:;auT1ZA6L:X9pgLbl:hf;OJX!F'PHJ<WV[n+kZ&Uq?
?.p:81W6eWiE._f53'6]A-=5ID:PHN>MV8_@*NNaHk`2*Xu>rKed$B2<NEm:boB,q)VW:\[::
CqYD44LG=eiJc)oGj=IMU7lmlS40bclk$2=0_pSg%*`65mP!lXQh]AZf4R-F@aooaT2pHC&)W
lM>^s(m+!H#$E*N4=)L/>M.TW`SYI4#X?$N.XPgeq^s+>R\2fV*NH"6Imcp\>9!W]A*X+lVp>
c\EJOqYNqM8c&)pK+?ACE4MDbD"cMq2EP7+3oLn>CB\L6/]AP7iN2Ch5`-8B\s/p-Y9Cqt%YE
IBR+US3MNhHRiFZ(s*jo`q<Zal2<f)1auK_i_Qm.#.d-Os@Te1EXU0F)s!p<SpQX)^$t^Ss:
lM/'#J%1&:bgJ]A/hhO%$OH[5&=kdC"$24r%ZS^r^c7G,L+p\M_<UN>!.<ot#8;G@UN%*Mpp1
P_t9MG<^<GM,GQ59)]Ac`.eUmp_A:IMX<dWH+L1ip"(meNXn3Fj-kgMH?-#K)rB4l\$gMCVjk
Zu5bp>1Mb!hTA39J&4F"BqcVH.<gZKV`O_dg-O,k!LGdSFoe)_PTS<@ZkF*EHc3CUG3X0@:6
iqU$*B^F]Am>Q(DW[tN3;"B-VE6((f/S:UcO/;u=%p6*66T<a4()B$+6Pp-<%dMJL(cRiUmCh
aEe67Y`D8T'M'>LE2nI26E11&uG`UM;ZES8'A5iM%ePq!F'om2LmA_?[V$;<jM3nqYf%2M4r
bH?%\p%a5^Ko9*(!!*pDt@U1?r$SB6j<VF.(iNLA;0!:<\>,^W19Zq!O\]A(=uT(I0Ok]AXB2e
>L^Kj*+:3(\*D5/%2?>&;AS3]AVi3CgVXpjr_X),dS_B\2baE<]AeStmh!cY@BH>DDZ7>8<Q2n
^:BD,XHHW2%9U:bW!qVuAI?ks-PJKOu)C]A\]AbGSGQCVi"`W5*e?6P"Ji4ft0NAR9[84Ye/d'
rGbFe+0)#666(#LCl0:fr.5[L877AL/C,u3,T8lF7SqM3L"<O-b>&;6@\-6l:Vg)G,ubISD*
As7@.bqeGLk!;JT\!\5f3c`lGit$gg]A$69sVS[D"1Pif>RL[bmu>#XXSW'Cf`bh3Zcq'2mC]A
[&L++JrU-1"*,-S2=ue&-Mo6*^i"(g4*OZ)LU+g:j4ao]AV+kac')6oG8Oj`TFE0-7C(A[`gd
:G3Y'GgXrW('MTbYIcRfu#aaG!%\'[oUY_>t#QuqUOF/T(EKKbn"#:<O'K3C2O!(7hC:_D[B
HM7h/!bL$Q.5duI6*ELG#]Aoo+@oa=JFriB5`;Cmfhj5NYK(k5\R:>fJ3\Bg4b5pok`pfJrj^
p,^2a4n6n&<I=\#L%Ed*5bFY&e@oIh86-"uW9:FM(sW8d8Nhm8mCJ7gQ8?JhrUI="\s#l1/Y
C4OFd/0t=D]AJQ>0),R\Gflh)DBiP$9gF]Ag33G`/c*f&Y:6)-G]A=7;UcG=/UQ\[Q0[$3$QC9@
Z@mDBS[oL59P$@%C5XNQ$^k9C2jZqDVYHD4ufn34)l<b59O/O.jXX3;tY\Ih3ZJK'*VuS-@J
b/+la!2+:"^d$!b[$1@QG$U7Q<`$\Z$a$a_%C:_&hX:loYC>ZZ^iE^c`[rJ;C7Y*_m5ho(90
t[^U6h,)tH(lJZIlU4K:q6k5.'5?FZn>/hr5a>Ni8A)1NKjO49oCeGe_D(Z1$Ko-"S))kA]AS
'`"Mei1+\b"&FrlpILhr&$V!rBSFYf&)d#[p!G89,hk&7qK09elf33qQLT'cW.(hUXhH3D&Z
M.eU?"FalP#eaUF="<f!?*>6>sf'GqlVB4^9N>2iY$>]Ar<4=klJOOGI4c)([=au&(NUaMXHV
&X)-@qTBNP]AdYF5Nn+bRb"C*5f)o)E3/SR=$&D"#`ZM3P5b'G<'79'*7rI.4>\i4*dq66DBd
1.`D7,3KK[S8X<gFOP<aF@Fa_VYO87G]AHh<F!f/2fm%+$Ycics!-(;IW")jP-3)F2e+]AkAco
VuWB2s(^O9EZnEGg.YD1Y0n(s)O_t=PV>9qZu7XGAULULU`//`.4rL'<1/iOlTIWXuaM_j?n
_Mn7acb,Ju/>TP\cJrsk\77hk'UAJ,1g,D%!(V*YPiWm,7"=3Tj@rjPa6S)UjrR1!D0sqi!]A
*Y?71[uT*MSL4cQAc-1@HX!,<`E9jYL+AS-^1N4;UkBGn`[$('6LrSPi[K&64ROU]A1a[qL"\
5315O0(0,Km4&/Pq\4bPp;;D_oahZJ-GWsHX&6M%s')JunL<SmF5.D3B`<;AT`j+p9r9Gp&&
,#d,-Jb0TX"U>h';`Bef@ZE1p$9XJ>k9Ur^fZ0BbYLI:/mE&C%^q\J3fRXOQ%LCJge8+M3TD
`A<1_"\<Go;67JCJ6VD#[;IGN?Aj+Us&(nT\\YaMrcVDh+@*1Anbp6tU;1)L\RS(A\;9e'1i
[,=edT7\%7[:!+D=Z51dR%$]A_?U#9qaeCa*5I*$El?C'XqOm6SL-4%Ld]Akt(P./oOAP/]AL,[
8Pt.UTksq\]A&gF!k.AO6kc<0D[A<fhY*o7FUD/"`q<IF:3d57d;X1I`k[eE8f:7LbbqB`Pu]A
W6EJSQ*LQ0XA*(Ecn!0h980),>hR0-e(IjVGQ:/]At)<Ks)i8\)@gmgo?G9,[<EMAIL>
@BL$@]Al+gU3t77dC_YRjgl%e4)mu0<mr,$Slf0lT54m@Oe-FNPC&@L(@k;N^[b;,IW2hD:>G
6WfbR"/=N>6Sm-#/`23<Q=\)C3^2VU>acUWeF7oldsH-VVecO/^o/tE]Ajk0/j@k1bia4hbc?
1*?A$eYg8q/]A@)"b>h_L/)"4,ORl[^2LJW*pd8CGBOAp$2W-A!QPMf(po@"h7)@?RcE&Ec_d
nea5';7e6@qYF;(d:@l$eO/)C**/C&Z3*39+Nfa0V<4CqfsqrNi]AfN1H7,qE^+tj<]ATM?"=Z
NfoN8[F(p=a,BiH:mML@L^oo!bg!8^S2e@V)PITmf9pW/NhZD&a'L48\k*ITJecj-3EtZ)Fc
K&B.k&\3*/edFG'om0tp1*s*?j#*/D!0mXD`hn;&$"DF5-M`3[mO3ai5q9nT$o&OpUpUMd*4
QK&5N!p<Eluf5$/=V\mhLgm,L+nghgKu8C5p+@dVldgR5hijqP%SY<\QZlCJm-'q/'Am/QKZ
14G'kNCLXK"3CLQQ$Z.eF*eP:/AZct>'Bkb+"tq-\[qgrfeWq>HYncHSdBnAF5%1Hpt\j)'(
;Fjqp2u@dk2SgYmba7V!R3eF;s'%kOt4Dn1K$)j)DQ>rcuHjb6uMhWCcLM^8\KX#KA2^H>2+
/bSa#:,?_X!H-AU$%c?YTG]A-4=I\C?38oi8jm>>Y_C:ka1hb^P#Ojos7FbSenaq\uDPI&m*7
%BGUWI*s]AAV&t?_\UhISH/g'hj;?q0+3Qd7b,qE7[D(UjGM4?_AVAI2'FhH;cM=K5@:NcH#%
UBfoe5;T\U+;Og?[jmL1!I4mPrDp8'63V\hUDIL]AP;=)[)(>[DNB;gLH0n$ap303'[pCBh(+
.I^H!VI6'fO\(W^6I96U2nB$I`FpQ_cALgi%EPS8K[GI00>4O[iJkJFBeinY%7Pim,SrKtON
cl64]A;+B!C2pLI1O*f'oI=,/p%</F)S>.W`R*80t<]Af!8Oe`4SoCA-*68&jQ`MX!c^uHX%ua
t#1V[-p=1CfQFGACJu7*4=]A"=>^Q@'`9?Q#\3EC\ep*;UR,X1Pe</XS#fJ*Z?RaIZ>M>6/e>
O7kr;Ia1,V+?P7/\94o:IJs:*1Zo=8O%9Q@\\)8>9i6kNHLRMj#XA2S1"rTkgcQmjNOaJ6`,
R]A>NDY%(Phr'$4#OOPubQL#EEWu,'bo/+pCPX&m$m^k"k85\)]Aa2c%8Uua?@pQVTAilWW2h)
03gc5^,;DHBndqC<mA7)d==93]Aa$L5O))<!+N$d1j8!J^F\>NoHRp@U-OVubcUZ)cA8AuU#-
_0`'qOt4%nFa`ZPF/(/5m^/lRuate9&BjQ2uieppRrg'aj5I_Pp"h.!0;W[eYDII3EKL-q]A8
FS?P?&PB?sE!Z5*5>TdI$/bkLC`"#p&M[-\A<=8!fhSWT.c4c=D`*T9A[g_P.>#[oi:&#+$@
AP.<`I^I8,+:^!k/*"U#Yb8Y$kn>_S%\B91U1(?m^-p>T@LZlT\SYMr*8N+6/<ndl!f3@32n
?F4EX5a2e2Y=?tmZF&ER)27S0aeENES@,BjRZC0/s3ce0O:.mHb=i_]AK*QI8=A_PI+@6sA3b
o(K36cGBF0a)Ji)&FH3c=!$#r7D`6XT.A?T^,,Se"Yb@<(q?Tu_K>+W\!qZ9]A[VhAIJ8+,=U
J9rH)h\;=n<ZS=<&*Y.q4L49MJefln-oE3Vj"_#i$LbBk+LSr[a/"<&#FFgNpH`@@ab\l+D(
<OcPc@A?Gd@EU26"BuRl*SRTJe\cCC=Gdho7%Bi"7M"#9S2?V_[Cg"3;I6q2G/[nPp0raZKf
(+e=4E5G`B@J@<`$UG8m642:^u9?BcR;?k8gm)2XEm=IDuE(O\/0SK@;OCTVC!P>WoD1R]AjA
F6g5L)Pb5L2XS8=`,o=H*(V%:20)_<Zqeq>qdWFBVn/qsBOB=g"M`a34FBQSnEju]A@m&93h9
jB%/m$l_c!Dq;=nS[h^E^+TAirs$>%9;,Rrd/Bc@)=OUq*6gKs2DG>*j&8td\UcA#=O/E3K?
\&QI3TeJO[]A(;&[X/e)N!(NZ)1uh+=am"@M4?"s3^dF*dZWO0;!p'[Ydk359-\GA[Dcn+USB
#\?1`ac"`h+R.M]A2;HUtQZ:U/Ya:N(.&m#+mli<5.CtHI^@eE*Ne/`6``/*btDM,)aP&]A_aS
9+4KXh1B?On0`71psONQdFp.SSFK]A!>F!(4758##X.^dm2)L3/LH^s!Qp2XOn&?Pl#dna:7p
oqU.!3t0uF#T&-fP?=h/;>JuCCjAO"mq_TVmYlQ\RB)6Ie*/TN2JmpVm^)p3b3^6J@$8-0C2
Rk[\]A&l%3JNaB5G'#APAB`IGB,18nN9#-nT"a+n+.W,j+=<+<HBpe13G(54sW+j!p"EPd+Is
JZp^,4i+iT?!(&QulZ$'oWa1\[.-^hKFf2nr%kR(A6^aYr^K-es2(NF1IJ(#tVT$JJi=TlgZ
CN6BD8$.L,uA7u-FD8*mpa_8R#!GA5\^&aoTpuRrF^eu%0JQ?K%2>PgJB[LbhcS2eL`Qui$Q
@^<P1Cci\n1*Q&h229*F$V59<-]ASld'X&/W%=:GSq(#A[-F7E\r-)D>ppqKo&a\%6TVk#9mK
D*UuG*$A1Qu2GJ&6sl#*m*a]Ap7/hqsE7dUs94>0;uH#NolOY4^D-2EJdDatAtg_0Shr4):HU
aukdbA,\U]A?`\9OG+S`RZgCtOjc[pga.IA[=`4o/I9h8#D;PpP?^Bc=Jf:f_6e#i^Fc(=NS%
r^XCOU0Z>A1"C?0Zcf4pT`[;VWI8V1-D4<<55EPg\K?/sFeIRA_R;4'$)DLY6]Ag8@4:2;]A[.
Uh66@clCc`_#Ed#%!SuG6:dms5N?\thV?5h5T+4KY04G=0?FVRqf5ESZ`5o$Gm5um'Jo0XCT
`PcL'=+00,&%3nQfG-d5PfdIm4=71e\"n-[c4NeX$sbBBu5FdqUP?BlM%i9bKIa<T4WMX7uO
.u28doT"$ZS/HX$XY$Y>J&>'Wj]ALMr&R_,F05KsI/??9MB1Itb`2WZ_DFTuES]A.Uo^5H4qF/
YOLojT,a`c%/+_I$"Db!A.Y.^22D2t]Ak@t(X;d^^gS@*KN1+I)Wo9KcPC"E.j@IP7"P8ZhWL
>^4<`oKtFX.\Q8Z'-h"E:!u=7NeF!bhq>LGUL'fKa]Ag(8JGJ2Z^fkX#-8A>(3T=:W[**r5#@
u+[k9CDjur'aK[SoZ00<\9jWEr1&1Lbk*\*aTN`5Wa<YfE+L0B'2!uHN`)?#Nl5hYj6>FR<r
>;(Y%1.B*^'afPmsq^>oq&is>9k?e/D;niY;/G]A<k;WQR9Hf+W4"F/ic9BZ^=XlO8sf`>8$=
ctkg6Mm5N:#Tf!Q.df"t&)#8(!tU4L*]A&:IL#=4YWcJ_"E4_t/*tg-L,gZF*nJepKp;ILE+B
FiWqAA'q`CD;33N>4"92Terl;B3aO8N[BWkIF:i:(E0>Q++g!mjb[]Ak-7+_j]A"cU6OO0C^?"
K'@WDoM1%Z+j;o4A"U/(T[gMt!3fQ9W"/MQ:T?,(Wk=3rPCl\]A:2t>mXST4\!+CoMtH69MoO
WU52.:Za2ecf'?pZ"l.CuWup6B'iMRR3r=Bi6qM,<g.1Mhs4!/s4h4KIn5*,1k[Sn1XoE2Z]A
sa-3;e#e\d.NXG^!Bp0j_4a-c;o%&+oVhN7g#R\XXtBXP1Qlrfn0KfEul#["mfN^<Ye<V^^B
[f/_no0*K!M+?dLq`'h$^<dEYoJSUaEP>cnd8Hklj?HQB$NrFu>FVhI<0$17=Oj;EStHB#@c
qGnu5ZI1)4r1is#RT`)%na#_r??[BtbsYiIbZ>%EO!:Cn=@qp(j<T(ecCjLQrZhqMq`Bnrr^
B]A"[3lI=G4FSermEbS@rO]A.b0$@CR;&%bK9PhDi"kl9k/1tOcfL=E`H/[*jDAcq,dOl-/1`K
?Nj3n/8kpd-o9Ehda;t0sWlc*QJ(AQ8X4e!"ahYEo0_u*%SqB85lWiZ?3/Bpm#NsbCgIng]A0
naD"bW2\"At(U]Al^kbXG"rD&rM42SM_(l#fn=%bDS@Ui<C!i%f#ra[)h`K7.ME4HOP'[sh/J
Hl4]AU:$,78^"Wm?#?)Orfk$f#D?f+eUR-+-#k#t3;S03fsk.["'V:/n@U3d\c#5`N><k+gS6
+$Z6TN$.f3c[O8J(m3%ZC=]A:Ia3.hb(%M?Najg1u&$BL.@>K*KO%d$2+rTc+.<mKhMR$QlT#
n`6S80D/N2E=j+el90=0`E.+[J9!5#@,iT'E5P?eO\4TFK$D,^,GYa1&Y3]A0;a+<P5A&=8*D
4;EDNBmPbb#:5VqTT9#aER5?JG#OS<5,9mj:<A^tXTRlY^>2-^T5!*?b]APED&IK+@7jM&mk4
*+!J[&no!QP5t+FmGel,J&Rgm4L?<H6#DD*f7d!MZj%)57n.Y%:<a#S@mB%%jA7]Amcc7#l-E
Cq#I=h(<a.afSl^HGCI/gF2EfYBDKV6c9kpi:E8C1W'!(EZ&aEeb$<6Jo$.h$1KBA_).T=(I
:3C;UafB*(Q+Q5BfqIHgdDYP_+.hstH7MDm1Oq_aB#i3'Y+]A+Vgb^+rX':@c9FJ\a/\\d0ed
.4P@oMPD;UbR-o]Asj:c\^,$in>G4qI/0U-(srRo`XQ,SuRMLckd(Yq2G6a<RJ/.)YZ7D->[m
!Ll\FR'%%3AdSR1:oVi=!q<70sfq-0?/%^bi=h'\S:P99R4*ErUq1l'3Mk]AcB-pO1V2i%*j$
dj-&4"&Z!3Z1>i(VMbT"1#Kj%=+\9E.o)b8ouNNapJbX.oKYP$-)fpf6C@*Rp,oI:&)jMH()
<DLm_HhX$e,CC%K5VN(i18:tV;gqgjAS*O)(8N>t/c<9,?YTC^FJ`6Q[`P3^(rij0c6.k?>H
!L3D\I9q.P5hpj'X`&:`/"Yb.F1Q;&L41NH!h,uW4WeTfc#5HA=Qr]ATqWtO!=F4dE@<4frEf
RW5lDnR$Mht0\h4\1A^M[eFq2rIJ!/pAmWq!-(4;!lV**-+1)TR$-`AZo?(A\1hs,d[roE)J
q$##Bf_-0#Jk1@e]Ar@L'9Kj`n>ZhgNRW3ELD<Jm<L&o^?fqb9B0/SkS=MM/(:i]AS&O_YOeDW
8kb/.F^'M2gUd!dqKX1OYC;*3T2s0'AtVWj^eHZPuQkjIG+6]Am7?"?D4%+Z[F-!i#RCH=_jt
S\Fk0P\%rh-cO9st#"=^cAh>@(:%5K6bS5*hbR]A7YKe$%@>S!D`8q8(I=,:N<9K1i4"qf@a;
+#4&k%'p5PP7*oD"`rE(-"]A$4o?ZJH4J8C%J%2Mj2rb[s>!"o7Ks3uRZ"khV^fA&t)N3JmWn
]A`:N@U%lH8YVL*>m\mh=nsA-@C[B\:!uBf4,O_[9#a8URI4ImSc$#bYR!-1rXQL`@ZR/"BH*
';(u")3S<P'5%]A]A9]Am-MTM-^H3:99dg#^N1M'4m17I<A9]Ar%5%mZYotslTS$&N/#oKp_N$Y-
]AS"%Cr]AEeoCpA5</oe8h7C!=>JL%1#smdYTHUqr^&)Ti`'T7tI7DKL<hX.3abrkFW;#0qSkK
Mj>]A-KE=aJ=X<UpIkn4?rU,&++3A\Sa+%QThaF4)9Uq3.D?3.kDW1BrcIaYhL<="qPAOZ(pQ
U5$Su,2F.JhL9R/eC'edkU@LGh+_!9MjXhQ<qpgj*m&/XU+b4Wj"Pni&dj(`-n%[RG]An'[jt
h:N=gso&ZXP"IfYI:YXZ^g0+kHdS?.-B3rP'H'[5;&YU.inI?HA.^(&ifb\kOQA&USTEP"C;
BD7QWpMh3+Z0"<<SJ:CD_m"_ks&F<:t#NkPTjjIQc[(NdPl3JTGJG(%5ZU9QjN2ELSM0W3Ko
OTs'$O0^Z%__4&TEt\1_RBF'/R3't2FT#a@OTq,Z:L)B97BfFh?Cf4_g&[rjNVZt]AYR1qKHm
CuI8q#-c),Lnp0D/S_BR\qX["sMYp6_Jf7(6S'9A:/i"NGq$D]Ae"q,6VsF"ori]ACqV!Gn[5:
_#=7d!NSN2T2LA.lTn*3)N*rOY:!1;N34Y!.p$7uFOR?B(n2EkF-E6-j_)=U?i30n!i$P-,l
Eu3-XJZQl%A3C?O_p7fi]AQq@K]AD4FnUrA25\X)g]A:_\mCG.Fg(XKF%ODfOK<:aPI7_#E//P=
5G3PZHHfucI`q^_MUtUf5SC$'-.l]A#"7d?$6np4#IL==dXqgNrl&AaAZ%s/DWil5682t:3dh
Hb:k?C(X)+(E8rl/5/]A\rtMnHM$X6q4:_HLB+^HF!]A(:k9%b;DG)2hCK1+J-Hr',0R4'UWVK
P[m(U-JnO2:!<00q;s%%&t+$3hXRE#S3.rK[B/<>I#)8%OeY!P0MD49<#_)cdhW:pVG#[+0g
2*5/L(8ai2@d$H%]Ae49p5sGO?0Rdu0<7Z>sUOHjT]AI3CBdje/r7]AXP<IQ#<Y3ueKrM)A"%U[
j%+$;bfp,:gnp0C6h_RTpX\?#Wj$%(#,=n\kg)MJjl_/%[fJ3?JV*m!%a@o_?[7(,IL(X1[s
CDYfp\:1ud[BC]AcceM(3(aj5_9Sk.)&a]A6k9J2<>QM[>$*Vft`ID`0$371mOQ5S1$f3VMh4U
+/VNTXq.u'LilBX\bQ=o3Q>&i'*(d&^b<sLUr3Fc2ljK>Hb88UlEYNRcgWNAq_cLE#]Ae#(og
TG?R5b#??[s!;1#j&UV!$,8A!):h:;fMfH'o#boFIMERhLormYa9s+#&BE\qUe.9X''iYOec
YA/Z1T^dLrOP@4VZ6.K%NbhgiW3Q%ZqASkQW>+,V0k]A$>.rRP]AUNV'uaX6ctqU22cG4I04pf
/YsO:/!Q50$F+7O%,j>NPt<&ft^j7W)D7bU[m,QJVKZ!Nq/)VVs?2GX+<IXgEq1jZ>0tA3$g
O`S2.\&le3\mYF5Ak-^*7V^nd1P.NHfnNk\mNBnT"k_D(%#ltuNF15!Hm8Fi6<k`pig>$=`l
B,W-f=s5peG2-Xi&'FgC!/Je`_88ZnQbt>7G4*L0hVJdgb`A@F0F!>Ak/O`Xl.)f_lUkF,;s
umjL]A</DGKXcBoM4j\%YA0NF!N4KPJ\`T=NI6Y-S)q)T/t!npjUQ5NjuG2.3;1Xq'UEg)3kl
krcWmAiPT]AB@0RtFV!PnhJB=8V9S`kme/UV.3j:u&9kIVeT&V.ho8#T#Ra"*!2#n!9(R2G:O
U>D`i]A3]Ae:B2R,B$*4]A%NoG7^EVooCXIGFk<c!4<;A-VaH^j^=i*+N@k/8'A&]Al`ELSk5)#Z
/iU5h$:sE^Cd\55ak)SZI-u(=?;H0^cUC,^9qKbnSjh<)s6_NJ#]A8P`l)IM<X#^Z/j;r<H_(
8\SD$,B/)0S\G:Rl#158L-L8cE2u$8deQ6d900l-+300^p!F$6k[QUE;$-jdMkY&,R&Ak2=)
-pC7R($S:rt`W*PIH"m^'&@XnT2e(:bOlUj\&cboo6.aZ-95of@prY<NTp=)q91aU[h]AW[_2
)^d5<.S!4:l?cKo#VP1eX<27pKZ*4.Xq[51?\V3YHgq3jW*)f,]A-$?L*2NUg(;J]AqrGtl(@"
@'POVse:#N7PDUJ1!O?60QJYDCG_X[*I*bPu+l,4?CkI8Sb?pqDJ&p]A6'qiCrJ6(ir3/es1Z
sC%G6u]A"NDZ%lG1GIRQqDdlY0bTDhr4Y+Q@l(F<ltLC"+Pq5,q(:Bf*`,Vk#,7^dem2iF^72
>$'3&smE6;@R[KIGc10esFh0#B/?3'>!3e*anZPi:AuZ[:VDb4`71F]Aj"P7Ug,U-W'q?dMl%
'kZ<]A^Z_A3-c?Uoba?hRq:LBM8cY7qQpV/D4s^ShoIiRAY`_ADlg?H"UB8/o<+4-NZF$=[8,
9Z(jmMZnggU\"NU*./H7N]AsW_D!i_n<?DE*O@+eHH<")_dA#okcSfoG,+36Fjb[CW7o44D!J
Yd+@ente6[YNTnBSCJ'G96f"(.ldd_4&XiArLj<Zos:4#(i'G-b-G*GN&JOA5@jX!WQ8r&9s
uGP!cS?,EdE<bo>p=QFe\_I(h,q?)WM-ZHW1@$DYA0QqMNi#3El:UX-g7pj:XB7'O#,UWSc5
;13aUE9!AUS4^..?C]AjU>4HJWi2GA']AO.2>#45/^SL5cYZ!U@cCc!QG2\'A0b+S0?>g22&&7
U1(j(#8l/WGdYKr3fi]ARZEX4H4iZHHm#b#e<Xa2Wa_?:uO5;_rFU:AJ#.34M'tc#mqnT:R$k
@aPB<Z1aa14[b1?[,o51>_$RKEHO.e5d#I5N5',u5"$%QqtZN_;nBTt_*YPop,Gs'UHMC.5N
oWfr1L*XZ"*(:/Xtp^3>"(\Z8ooj%+-fYW-0eN'H8gYjH\1[#GFqKhs*RR2k$+<rHt#`J/+'
X'HgS+#A_I=GK8l=lp</K!6^R,Pa(Q!Wi<B3H@=-u&HF1,\MH"$FV/r#Nk;"LbB6]AY/lfGPH
NMD;K1,t6p1k'TOk$2MrAm.H2jeO%HLd<Br9e-#!$,P2AOZ%"c>"s2IU$emiO)?USOT,E@'t
BT@OV_ZZU.8RkPU%Z-sgfME+?4HT+cH\c-N-UTO3WojfFcA0he(/QS9dk#YEK4q2+VpqhM:T
GoRBWfS,(l*MeDja-g'F]A+C_Ljd:[r`\4]A\Zs.pFUH/=/J:+&qMe]A7;>fd-E(>J7Fflj9"ZK
J'@gjV(O5YQ;8\9:T&9ldbUL=(7t\d<F>`qX+[d;\-rb"ScpP^\9HnC+mKHd!d";6:i6pBma
2I1tUdOuLAoY1d(F'u<1=1hrAb.9V`"j?UI%1]A$e32:?aejD/B92eb69/+1BpjBH!f.BuCN_
e1D)f&f;YTa#N(+0Jkbb:X.VT`Z4AqTD]A_Q'Xun0(PTPXRm;;.!Xn(i5Vm\N/mh%.=IXo%GR
HtM?74ui?:U^2%rTMmmHAPpF=6"&RRrl01QREVY2XmPG.K(+^[$O:*523m=)<2da$=rSj%GG
#.?UF("W_G.l=8n/;CO@Q*%T(KEqmonnG!I!+9>_/&TK>Dd+&A]A)rSlZl*Y=4`(ZXT#5`d\K
V9i'mD[qIG:b\1Yf\(l/Frm9%hD7@H5QD(TsR-=a"P!\ZfaqDJR]A=JJsg_R;#8_q#C3iB90#
00`/T8^8?,CQ$H\OBd(ea%r,e0O@sDY#[g9tc'RrF#4ZUI`nc!gWqgcs`U/W$<JNeg)a`XnL
*.nRZ+JDIB":q%h=."?j4'76NdlIi%QAnKH*?2Ej2@_c`@!hCmoRSh/?(&dj;,%KAMZ?UbT1
l4aKiT>/'moN:A4l+?=.XDbGVUG:?ZZ,qVJK?[%k=.L4^i`@@iLel5lsQV`qP&B\9n7dlqBM
c=?@r%T:/"ptNO[XaX%^I$?9.pa4VB]A<([sof]ARl]Ai)t3%u,kqa$Kb[!TNWrEE<^inj.gOeJ
qU9c'J-e=*jYE.hR=GKk&=Uf#*lF1Ll.3/hJ/?$g<JSHr49i(@sl)=6_@O,Nc3e$]A8M3f&]Au
/]AdL;,dhEe5`C&'AA!lAB>Ed"%`ZDJQ)2n95#GucX+dCUI/PM=:A&b:fe`Gl=Rs`l_IR_'c@
PHmtmQN)D\54Y_#+9ML12/ALjG=h)eT4qB8iiX+>s*5B:!,_N8r4YZj#G-uHDeK0c1$2bn^o
8P\6Ym=j)Do+@5\njq;M]AST`;@7@sat'WW'0DCN4-ccH?GiYTss);">?%KW__`(#0dPTeZZn
"AIWMC-.aY1OZ&UUp&08@S5M%V`Bj-'tGH6F5lQB,6Rp"Es>/(!LRM;'PlP*Qbf^H>6EQ%62
0OTS2:_WkMP_Ch5NUX!bAuS3/i@RV]A6>VlX5!lW_Ms`V*\:nYO%-pRV0T41S?[(N;+l\l;s1
ZXN,$eeu']AJl</QeK(1/"mu]ALt$ql2KeN!tmc&'Y+77i4#/Aqq4,d)-=;<(8l)GbaNSFP^JD
<K=+c*R#s3t]A/7G2a`OMM2X$<k_hY0D@J$g^6-[n;jD@'b2^(rb:\=7q>BZCJ3bS2#^pHi^1
nD8FfQKdJfe"PPGApFgpTW.ORcu8oC*U,CZ/5Z/?%P1HJK#PM,dRaN`?8:0f4gWbH]A$\0;iC
fq*>9NfAgL5#Zo\=D`;FkdrQb-0VnS69;i$!rMYOq;4N_Zb7G1s%>BIgY$<?L?3Ya]A;CL>Pf
u[o0''BPn;iNm?q8Ve#XWRH9jL!VMrg=-@\.Fq\a"!4<ob4MM@HU3FPO@Lhm.&&ZT7mcc[BV
s[KDAD"0jO57bn(6'f7MMB#qo"_NU/h@d>n,=Zoitln#r$?erYC1"9V>W='t%pF[c-Pne*o(
-BGh9OH]A?MkT.V,pR\!LaXiEjA@%S0#/16k5#V1gosEqm?7!&5h9gtjg\M"0mG%_da9MV'@b
up'"B+:$OPCM+alG6D?`;c-Zrj8ip]AD=p$;1Ul)5Z&Yj5&dY@paT7CP'?">:?KmM2S+=e5NS
P\AcZ)4[*^$4gL!'QmU$$;)<?kS9p+I]AQOSSKG^J+=e^@9I.!`)Frl3PWkU22EJ!n%Q-YFjW
1=LLgI^5r``7#Jggk@qAo)t-Nn[oeYr$fKb-)fR("W]A1A*e+nepZVU@;r^<:sVqZ9>%RgV]A&
^T.PAh]AU7&`F58QR?=<_P2R06'-u!ta]AYinO.r'-4h![8?n6M#(X^^^h;(WBS[)[f7J;8`"[
a<k(Cb4#Ug=j,QX[SJ.=`mO+[mXG;VX:BSY!H6VD-J6YC7R5VZ_`b@J6fGCXeXYAQ+,p6I\m
>jWRY<KaNQ3UHrggTc1::YZdaY9`,^b8enjq)kNgSnB%TY[$R!eWS]AK!2bl$)9a,X&..lgZY
Vhc/_Wu3NAZloXm-+lEE@Ma'J#q(\DkL:2'pOC`.@HfU]Am)O7PCck19n(79]A<aDlT:J"uoY?
Oa(0CERi(0kq8-uG6r\72>Y/!(M!(Y0a0#nWWg-857ea-H`g21O\"d!+i=jdXq"<&mq"beE`
@>:j24]A`XL^e?U$[#5^eOpSo)Ue!=U&o`d"Zc#J;%MNR_&K_;XZBIQu*"O8?j+.M)o)&77C`
r6-/]AeV]AUB\rA.+O9T3^>:'qrTGr!d0h%&ic@G/8;+:RM\2J;UNc"0'q1R5A;_-3IEd2AJUH
$"BGrQdlB%m.>lnNp+]Ap/ah(_L<6W^%Y.Bl<:lKXSDXW@5,g$Pu(*!AhJ<VAOj$pH'U9KK]Aj
cq./X6SmspQ370jg.CkM`JMSEImmkFm).H6r==3+ZJd<CJekdb2IrdZX"+Rh*RrHMml%-YrS
uNeVp%[F0c8Z);MH5<p2@CiN!N^(:9PRi5N#"<[gqBLFXZ5AJiD9GorUDTHMp;c:7&lkaE;R
,I7j$1_It:XKMn'Nk@l2^jXkHg15l$[Oe,/=N`^^pCOYu>GD:[X$H\p!_AUi<,0bi%)^E-%F
+Q,Kfb&Z&\lR7m:sV_%8?7JH1:VAZ;kXr`73,=G@)eokS_7_X5D"MI.:g+u^>EYRh\^+U5#F
OMF?7[YME'256S-,BG4,l+-$Gbn"S#O,%GoACC$G0]A?VaTehrI=>)!lV(S)BWU&@hR;5gsJd
rdMXrqMcW[`'Elq78I=qXK))\`=d?-3-I36FR>-1!TNYe/'$.#o2bD7*DLCq7pl.0XmHehXK
TR@<Sk1lC:r<?c;4d23!%"rYCNocR#APk+nhYuS%H%sD\]ALj?61\T%\?@N@B."@`e+l,*\Eg
ra-tg1=AXTNN<\1i[%a>*mS:-R6"K3<e&V8QQ"?,]Ag"ij)0Dk(^`p:od\3k@,%tVLHl_!a<e
c3lRk/GU=USWlA!p5`:6B=N&W\==8+A4;13M_'?2Ri@K>IbJlpeM_igu96\RhD\;6nfJ\AU1
]Ag^N%37B>0i6nHfhc>h/#aS:97$'<YEGR<7%=ZFZc#\e5dcmtg8Td%bM==*L%#rY&XXH*:cd
6T8PrKBbm(Qc2'E^Z=!@!=VUp'>-IQP_QY`7ne2P3/,i;?ggF]AZC)pTk)0IG6)>A2N]A_ZFnG
DR4Y\#-HS<=hI38faG3eA>%jn9\(%fMs-[o`jA$)g3BAS^K+k8X6X(3[*C8;LGN`i*/&O._i
GSZ"(SU0lH9f(eS`roZj/g=Qj;:Io&\P1E<cOTLVEJUd;i1';OEFX5/IQ/RY>-*$n*[2Ig8Y
?q5D(;ZfRlO16Qih?'d*_/[NlYU9,I$i8t>Fh]A]A94c^P]A<""!\GV)f5+S,c]A$<EMAIL>
/R[D>1aYZVl.o@Z+dXu=f8H=9p%+Df7I*D-L1q_:0.PP<D8;kaMpCg=.&XPgO.r0%?(B^OMV
.GSiQlWA/`D_'J^M/Gk)hE6ul_@aOpQZ%!Kne<nY=RQ`j>Qsj=&qGmC_41)oFs/:^&EV`UJp
,6#ou0\eNA9qAMV)84$i3Fj>]A-7[*!jB1H(^kd4TF/i4mk/Yd4'<_>mKuB\9HYRL6ZuSq0X6
j%1lW+mk<=e6M-)*#9pDp\#DCOSR-cb=!,#\f#t,u^J,oB,s"KFgn'POh0eO+nO-%b>A80,!
DTFU_B5dC@ZY,@@2/"`)f8m=4fpt9Jd.EN\e6`&3u-X#kDCkKTTp4l/?5nNhg1>"HTg0if##
M$V!$FY2sN<VDdCX*8)?j3;=EEO]Ah8J3Gu\DIg,IUN4/,kp9'T+L9gF$4>@B<6Y58F)]AQQCQ
kmUg*O.\*XNcl/G2"I[UMqr\E)$<GQ(VeZn'r_siT&c+)#M'aMnsS(PmFD<]A]A:DJEnkb_>]AQ
[m$3lPlN6--RaXRtKFgGU<>9^R:0$O6=O2RKE?i(%QO^7O\*FTrNZ;B-AZ!C;Y9pJTMn`cou
)<[['ae[MOE=[Q"-J@TuSDT0VqNcaLOb]A,e6@hGUt0_<uf+:.5@g/\QCB]AJZ[("OnF_l3K]A-
'E=fUbZLe)KHJJrNdXq98ZkLJm8/n&^@i5.a1m662K/A"-84+Ng,'rrt5cfkoo&r5TCBI[a9
j6P+u[b)4kZcFe,$A#u!1uH!lrRlP'*@'bD1)AtB[=&?Bn"\&QO?JtE?A36R;qC&`=!2LrJ3
')csNpTi'7=LWIND<0?cTusO/VTd:e4YR^`<d!j?k2B[iitQ"u)@83fF1J9h>NcAhEOYbcAh
%J@nIlaq=fsa`?.m)AlioicM.V(=O+k:o;/@eCoR<cJ"+@/(2ZC>AD73`AT5`Q/Z-o%u_]A_X
m.U8b>;lMa_\j^K)ELXQ6A;pD%INf%]A-Ij9N@h)Q'*'.r,Qr]AfgM.S!t\CF"O):"6IF_m'HX
eQ.C"8XPD?ANW68KMun"@$KZ$pL8C*&)UWQ*,-ni]Annm$4_'[iY7CENKd2O-aC*LU,gtnEOl
/_7[qTPosH&@s"[*Hqh8%A(_3T/3VES2pijc6]A%6;N^Vlk[jNN%YgB8BT^>$3T>IU@/EFkl`
[7/V/;CbkK!2\uD<^%`DgBhAAPYcW_bQK6,clU2G9Z%-7:,)kA'?O/XpdHS]A(9TMNp^hJ58Y
PP?YZ+q`amUi5!m3KR_Nf&\?0Aj28C!DF?0U,'c(Y<=iLO&VXiG#@hcR_4l+.TC??kouI<pM
n\_[J*$be$$nBqSGdp;T)B3opX6U'df`5IGE;=7&SrKQUpQPH"^MU&@Zk&ng^;4LNB;9FG)&
F=Bm%'W*b;nt"ekf0Sup@0-U25j<-&D,R)BjB7nN#AI09grLhW'dG$QgaC'6,M3B_?Ad,+bD
%)?<%YFFf5pb6Jr9/>TQOb0s!.k)bUXP6[%n00[G06'2:>K+JMi%X-&!q56S?k90/ins7#Il
e]AH@ur@ujC:+F5o'!@O9ek2s+.10sa#kQZ]A%:Q0/UakpiQ4>7V8^8paQ)u4?g46eU83MZPkY
-G@k4p\3M!@*4l&1+ua"ADK&0;`\J9hg6T4<_H1Z(2fr+V-4RK0-QF/MSW(mk"+X-A"V(#W>
YT!J/rTnI;:?Io?T<GiRS0X^QGABU9JdYfc_?^+B.HW!.MXV1d6LoYP;HJ]A[q\/)/.'Mn#,s
#W]Aa`9s+#T#d5hU"AA>-T1&F8h`j5b2_`S&%9j&Po6Q";6':afE"hoQ2R6LXl`7M8ttm2Z[m
W!^Xo%HqU+1<S#2SAIY@Beq5sbYSf&U?CPlH>/ZO"noNm:Fm1[iA.V*IJ33a(hC[FEF3l1l`
)#G*V=Mt)G2N%3m4BZ@8AYTXE2!,.tZQk\^WO]Ap!p8/hsNE7&iVkn.D9M3E83QS^IC9H-4ms
scW.It$aBkBRJH<]A$#`&`iU^Lg)2p?EZ&dPg#_VE)D!H1.mYhn.H9kPC-g]A<;qmKXSTk;=M)
`lWUVk<4=pEU8Gp5Ysi]A"4%0f'HNGZtOT*End'IFmDI'WKT+0bj\LF^e-ttY;Sp@/X0>/_s/
Y]AcCfB8K/i2Xjhe`h#@aZXB;,^jR;%H/Z>YH'9\;9?mMVe&L%&0hbCl>d<>,#$L)ljc38ANu
W!iO9V)7Ifeki?M)'+AGs65_a2B+>J8'\%_/r_,_n*E4aT,QBm4OokUlPg"2d%V>Xn.:aYe#
6JnL#$c>end-Y!:SH6h8NPEnCKJ"&4:%H*&ehV99Zf($fbFs%3<&/!/S'J#!/B-2Nf%!_)D#
`1^FHVf^V$g-H^A+'^Fh[jIP^-4a(cR;M@M&t3:o?kgj4IR%js9u4*go:1:C[Df_02lCB%cD
=a0*;h[Ec`$:]A'auY8o*=32L7,>&)Bt]Am1-P%'GtfHV7^()W+5$@!ZTn[n:J*HRm<erFal\T
#cR(YZ>D[%8M%JqfIniF/'=m6Boj2Q8NUp>f<3WdOX9;mjRf]A'eY%?mH=;c,i8_#5sB(hD#\
iBpN!JdORNV40b\^u4=Qe;@<3Q`)BV[KI9>o%X?95#Ms3"[n&?e4Ukm_M"X0m82#X9aJV$lN
f=(FMO>mt5c#uI*KFe/R`q.SR@1l@.HbqKE^n">eWP`]A_VKR+L4]Akc$m4jOS6_4]A\p$/T*.J
5@8G/5<OP&:+bW.5m^VBJm*Jt_fuYCrFZe?+DE"VS?@,\/0HmRnc/s(,-j#$UgI-;kpRKre=
sjd108[_-9A:Yk0e'f[Z<s0['A@83;$]A<*$JLl-#q;Pc,h8-FT&U<oorU'MASJ.e(]A!<\cE.
`dd)=sPN;&.0U3bcjbb_ncTt!X(k.@0Hp-&kE)_3sW0q?N'kOfCf(KInmL+mcV7O3R>@1-SM
Y@A]Aeq7.Jm@ji7@8H5kgs-@&SN]Ap:f+:rh?-&?]AEY)hajR,D24h[HLF#pk"RNJ^2F<)oFUlN
@mAoC!QT6CD]AMXMpFB0o+KR_HeWd:>Dh=h/i<^b.S;BO`UuKe=U\t`u]AJNi<JRP:(K48$OV)
_',61nZ/b64$"am_P+5Ob)Jf+\&Y_8>nhP?.K7AXU$#UU=oCFQZJkpt-M.hcJgK=tmFqf<VD
^0jaC<_bpfL#=8pe9k^>/?XB2Qfgh$@@;t^H+oc+&QSpo(S$7h#UN@@+m(PC7!WF1q]AjZQf[
:T4nof&ZWODM<hG6_enMVnUHjH>qjm@/Xp)qCBJ2F-#K"2Z]ADF6S=01,0+I;T\K$)$DSg^RS
]As[U/@dAjLaRO?[%T@l8"gce=NDo/MI2<&"f^El:thaT=M1EK=Is=LH2Zou:7).etM(0H=A:
497;K4XdtI'\G+oW#2lu7,:pKI-![p]A8-&;8@9B%`Lg2^p7)>0_;i$[j.%]AQ?`$KUGC)L+a'
F(+]A>u_9'+!>`pWc*,fM*T(M^6!(W7hcc/TVJT*0X0XYOt:8b[35>L+!RCoAdOsIK=FHgt?#
Mj+,.$YB]A)P:J4\c51uX8;"N>AlO/sJ8C4!:a$>:n2jpd6kI9<$!i8.DOEh_Hc)ck`!>\o9m
)H;CjrB+AaiDi6T6X5F773Fic).7M'^6t88dsRk+BP#F2MtMaUto/kO<N#8gZs.l`[[6)YUl
4Dl2Lpa"]AcFlpZ!t@K(/rcaHia:F.nM@$?'VF+E=HYRIm.1-7c42BX\dIEp@X7'$Li3*Ds08
=pD6jLoQV#o>58i=5hX#JEhf#qrH\_P(bjQ2c1p8"7bIJo&7rI?,4XUGS/MQ2(3-M-[G`[gk
!o>\S1=jK55=<'8:SimDP:\i2]A-VrHh2$JbN?W![r4E)S(>lZ.ZCj0U?]A9X.qFg%S$$V%o3;
5L:`_b,:a3s$YXm`G-jF[DIGgM&er,n;,^YbX#cMUkdcqr,OVd.F1WAK)eARpW:Cf-9rS'TM
#hoP28)9J>PR%]AqtcD3TFTDiJK&_EK>.aQ6<<S*p*I1pad)FqJ+K]A_IKb+ansc6C^(TY+QS7
D.rTr0HE)r:dGRAWF5&]AEJN\Qm8pB*pf&s$N"BqA-Mo0hF5nqXCIik:OuW(Bp=P$$jgF7fCb
'DI4P(/Ii5eR=+O"g,r"l#+:n=FR8i!M9NeiE[iPSJt1=qSTd,B]A7>KNQ1fkq>nd7UtJ4W&o
DKTMfd;8H\(.bn!9'T8hGi+r%N,Y+QC;p=j-#sTt1O#%[*Fk"E<%p15*i$\I"JdA?:8aXk>W
`LYY%cA;"AI,8>R_Tjn^$p3HX)b^*`M"(lRN]ASY.Gj&rJ@->t9nJSdVgCa_F,Ftf's1#lS`6
Sk@AYH1A:,OcG<b2)o8E-gfAatT'i6f:YPo)mYIg3GKN3*YD%i9Cd?dF#R1G3k*4(rn7B6QW
.Bn[qm9Z:Wu:Hs(iVi$)b5!Mct__(9\l%_Zmd0ZZa*>QtS1PH@[?\Ut]A.&u'oNDA74dk7L_9
qq[fRB$LTi_f]A[P)\AWS#6k^rVoqV9G)g<'f0r6kb!'N7=/2Se8+"8I\OqUP=s[1Y:<0:.02
NdlSoG2(mg5p7-Xo-t_JF9tjpI#"0@I,,-OfXo*_3-OFuG[,&=B4N2TFJ6a14Vhjq`6dpb^(
eN-irUM05Bd5'_9\pQdHjI0!67FtD0:p0]A(G=1ij6?rf+r_rB>JKnUT\a-3rV\l.G_Yr^9'X
j9%T!V2DCn2=J#T.6@(Ci=R!A@38sEu@ceE.319;P:2/NPWDWSbq[c(B)se4.lC]Ag\<^RS%/
_8YNru#@6cZfOhD>%!F=Xr/gj$M.0P%<#Ues^&OkdQ7"mA>lM$aTScr8V;*CuB%[-a)?=+^Q
b4/oka4J4W^DQoQ+]A/PLEa]A:a?"s"L05cZ,_:eW@9KY.)ICF%E&90u$ocL*>XIePSSqLY?MV
.WZ@*28tb]A8mSnc_DWEYCTd]AsB-6WcVkp32OOt;C9OAVUON&5VA2cfRmVpqZg3V/)G)p;9bi
2YT&sj#_PN8Q6Vcd:TnbbbLktN9\r5/!up:[.'+j+'b.M@a66#KF_9Fh7NY3T6-]A$_2M*<Q^
_6qc\5j4!X@rGX^;(97r\-Aa5tNE<bgk\qK'UWfj#>r?l29.uXsM'n5,$6.#8%s@S5_Ml<rg
`$nDks,!n>rg-7WB.$9Q/gKeHVD9a!*@WC:m"1Jk27^C0kNle6\k&gdieZQGbs!s_WBW/4dR
^DoA2Ss_tPM$&(XjQ-oR]AuM3ZZYF<B@"rTbqf'<&g=Se$\-6o=qsk.sfUY&abJr5='e%ci]A,
`cb#LQJum%2J><VkZ'lb0&J!aA`fL"@GEKE>3D'doSo9PKK*4.B%\aJnshMH%t$PHHuEM;19
rYHbRW#74dBT+Kn7jSO>0S!oT,!,\t!]A^r[*n&g6UoJiD_8Ba4"(&mIdY]AO(#2d[2g@duEfY
pIpDQ06t@-spCWWu03a%E46f=hWtP+G\S+?uRmo%B[U:IKSGFBd58)%ApF2A8m]A<6`)0\J:b
?10HoDE>gSs(FCaZ\<ls;kEP0c&m)sRb4%QmkhU+Lg>8NApHUG2cElc5AM)Sj8F$'Un"a_DO
5)&L8FhPNnmJYPBR[O[%^@r^+HDXf,#:'D0dPt#q[E,/\=tu"HE!H[,pOd$rJ\$N-k2581/&
ru%`u\Rog3Eht_<8Hj#)a@Ecdfd$PHNLUSoKemEt3&ESs;rfKKFbufQ1X1o8RAP>.E8$O8n"
uWE.s?'DeU6I/`sr)J>0Vhu3MXl.n2`+tp-E"#V(O!T>tA3UWQm/QBb3Yn*h$j",'E$2scAp
FcUY;N<p;rqO*GEsN\N3FA/?;<Nrf2R<Q2h).T!`;-l;Z1J.+8*eUG&Hoc7Vk1I6qn&+lT6m
:HM%r[]AY,e!MqD<8Xla?B`B*XShRSN`Dn&b+gDU1!]AVe!E&!A@.,*$A'Id,A&dNBT$FE:8+Q
mlNVIKVA=AepF;aFslq3(%Y3/`2MDUMZqg5J:b9U?9J(-_W'mp-7SGr'/t[Ye0Fnhl*n_1_?
M<'!REI#ZYXj\dbq/d/+-SKQs1hR!h8:H!jEn\[1jG]AY!JND-&qX&o/lP'#!>]AtDF:=V<d7s
0A4kHpb.1PNCh:tbAT17>rh'lk8JLl/Vi$10>[]A1mHbboPe%tC[PIN8jl/.S9?P=hBlJ/fCO
EG$;8I(c#`>K)"LYE5D&RR]AE[+CB$)2ncIF6WoJj2)!(MVeVVg2#gNaIr64j`Q:(CK&FADX[
0fkfoRO>n@b0&sH[Uf:MfSbp\a3RW0E3Oc7@12E(cU0nO#>rb32dCm0<K\5W5;=u\'@8J_2G
,[hH`ID(?XOfGZ$=YBm64kR+;P7@q#*E&"^JtB(]AM(OKf2?mV\\%7Ku)WCP&@SRT7-4Rh\aj
o'0>^7*PBt5r-g("mBs(Z=oGtg]AJ1nQh?>H68lelGoYiXHTnX4FbDk0WJK@Yk[d!B:M+rQDR
d*bJK/J>#`FK[SnE2ES`$LA>f)FuIrFE%:IcmH_.<5I\tG#27R+E+PnP#;+^h?SRNnO)@*Ye
a"3giZE)]AM<E&dm'tWr?rYPAEI++5b's#?(aN"Z?E8'8@lkW$s%L9oQY#L1ITc]A]AHNAp+>]Ae
m\,6;48M1^0F\8n`Jk<m'AK!Na&qo(W$GFD0AqMJ7s@KT6ZpmH-b9M;a#G.4VW,IUto[_A3a
kWSS)C;)IsdGm1Mk-0DY3XM5-QVqs1Jj:X($hF-TK>)ChTCp`034%G]A2cHp:.e-&j>o-#S.q
p`"gkCr^*K!/"6g0`)c_8L+?]A[c"f`U:Yp).Ab:OLDn"ousu@ll\1a"cumUQ3U:Hl2@WOM.G
"oe-Q*ecn)VMf,#+jljJ:Mc[aqM4NJ_!?e("Ld.m"FOn<%QR9[(\<tq^R$adT(/a.e<1f-;\
XX5GWgmW=kq$N]Ah1;uu!Of;*fLN_ar(f[,p-2pEpHLM!XA\-i,EKNKi8?q4S@MrP7&i/<b&J
^(MK\`b9HSJY5YMGA<*!V3=XiN[]AqT\DF>$0o<,Ra\m*+k.I)Ia0[4Dbr=-p&CS*)j$2&-\H
VOOq(i0_X16$9D)(U2DE*($-1,D&uV/.JV<_eQmaT7G+SRW5g<OeP7P)CaN+5:1rf^FX>n/j
VsRI[>e&"-bR8khEig*+)q%)uc_T>^pb/=6Fp1p+U>638L=7oP.=Ifl5Qeh)EN`H2.C%9'h/
)2^d>#DjbRR(R`-G7oXYX.H.I(FhJ%bbfW6iB@Trm<Le@^&#%YrXO&2cHu*LsN81G0KePQ?h
M0K.Bg.4-'pNHKCTK+P=2+4i>KDk(E2#L3i.lbZi^61@mk3JSZeQ]A6T8&=NIP.l>_a',]AKn;
VDn&hj5HqEZu\[Li7;m)D&*IVlB8t63A+"6CLM,GfTCt;@!9iLWK*GQJRo+o=X[c?oZ]A;H_9
ptb4uqB>cFQHE,N/=/5Q^3`&tWj,'1=ME"bk$@\G;FXiG1nbGJKM\.A[RkD'Rcf4af,7B+(0
0&nnEt!)/=l#d.)pOV+qP@8/`"9hi:&mA@=XTp?'&YME$Lg$a&Z:eaOgBca87e,F8)*QDcH5
+(fCrU?AaVAnc=/?gjn"F0?JhgCtp%64VY%aMR/KinI[\@R!eXFVYB_?n#OGL*DTRH@!C1kK
8&7'(=+\(-cqjdb0I+I13#4t;8Kk`q/D,InRVY6-(X>+7!E,C\#sPgG##amMZ,ATC78^32Tp
b,D(GIZ%);,.4Bm/Ik4IPBj?F,5o-i='4LFHSL*h1m\$0ii2daYg:_B\X[OET5q4I9So@g<j
(Q%Q=_#@;N:#NC,B#+A,MG_?E?_,h%*eMKsV.Z]A'pJ3',D?^KFI"(J3b069Y95k"u8(d?G"k
dI3Us&eP!a56k>G#1(Z@jCPLjZYb,K";fjq-PD3T#ROilO>t9Sp!hHkf"Rrn?_6jt6CX8l=m
^RICZRf+i<So<_8?O0qB(aKKU\N+C;W=O';f.2ID^gs,(RV!JaToQkV4%V]AX%<BZpda")K[F
\UPX73[FW8jtfLge<&I5VtS%F'4dS=LpuHh=)_=dN),k*jN:8pJk7LO&54k*Lr9&*bj^#7`+
hZ4dZGC$e]AU]A+\b#YASh0MOC5Wm6XWB"V,_F2'88!nM(H5p3;;8:a_CcT]A0*O['mL/GBEBB6
pLgpLJ75.H.FFe>s3*@=re-=EHGmqchhsbm7:rSQM:GcgeV3^6jPCcB3rB.CpJ:#Dq2tsY[m
OVPbu+G'">kQ&rRQIF8_ho;jZd0I'>cA]A-CcpJK))k8q,d"]A/"/(.L0>3s4,fHF??Jij>6fU
n3um5[?@R=5j5X:EUJK?L_!CE.i,7J6^=pL0nG5m%V?:.SD?9//IdfAP[O1EnY=O=;KLgbeK
LgbeKLgbeKLj&]ANJQ+m6bbG8?QR8uAhmn8fc:%3na&bZF4<]AWqX&'m&#aohe_#_d>JGL\;9n
2B+6E?rYMD\O(4en^&Qf/OJONrY=9KCj7:6^0";ZPW?kHKD(4ep4s%fkXF)/^'ob5)^n@)b#
SioqigbIl>!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="333" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="d694b6b1-c4cf-4c5b-b7f9-bb08b7028fc8"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1657350,6610350,1657350,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,2743200,11578660,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=report0~C4]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[report0~C4> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O t="Image">
<IM>
<![CDATA[m<IeU>[J35BL/BE+G`T#U(\ee(l[l!]Ab>NB"u/.^H3k1SW0sT:ke/=!cKLfeS'cR<@bf)=J&
&blH1&_"5+Lg&FnWGu]A@K1Ch<R)+H9,f*G?OA9!!#W60^'5O5QJVbhiQ[1ha=QAWW<)8Zo[E
IJ`!AXgpQ4>k1]A#La0/ar`6mlbM;fjW?cQucTG=c2+,3f-S_T0fiC(^/Nbui0S1nM;ZF`9>"
_?]At"SUf8M;th2j]A-F*O8aK^q`F6td;9mj!;M%fKpk(\@!<*n3c8WrAmDKs]AibU@mceR)QfD
<)J"3C8PUp#P!!'!F5Y?Q_/I_go8feHh!bgBpYl\`f+//`8qE)fC!1>#c&qH,2ld9.nnAUe9
g?[P1*>lHel0N>-6bqR`Dc8oP8=H:&CS+iX3SrgHOoX-2E7Y.dZ'<'&^03h18a-]Aror=[JCD
n=lTiY'-:qH60`>,8-J":<geu;X$l'(I/mi90ThClb#$bq%[7XjkY^0\3t/[.8*&eGsnT]AS0
;0!.$"kS"3Rb+dEs+e^p)3_I6WJaX>1'RAT_@U/.@`2nLROdDopl3LR+!-iiF&o.\\%D*:bC
/LmA*=dNT!>*@pA]A0hP9+Xu&5't"FP&j/p/Kh:fY0npSiCB#%!:52H7g-d/:S0gN.]A"FRs,7
^"Xu%!0fRG#U-]AF3.j$-AVI=3G9h_t=CjkH3ZEoW9`"p$p9:Zk:8'X;23"m_Z2Z^ZJ\,.t&!
S,>VBDZ(Br;EV!l1=<s6LTnee&c[[+s1J)b0Y!J&"j]A<V-B^5[Ic0WMHl*Ss3OcCqTl1.08p
Dic-`c#CA6RXl4-UYMfp@B#pu>%OW<j=.`HO1BcNqScD*&!:O``sFO9#WSXK>NG'd%qmok@U
KRC3G9.Y_hd[[LD_rq`rub-i426OP$FKH9g!O)X0"C0mkA8ju0uE*GnM0<BQ5Zp]A,$"%Wq_X
`.&=al\GR2h*p%f8,pp[J'WaYo.s[-V81Ck%uU62[,#-/7(M;4o43l[>NG6^T$dQ:>jDsakU
=!&1h@+F?dN7Gfb/CJs=L]AT^d0Xc2(QaK@P7/W3(RWVbQs"mOrg,9o\P?EsDh@clHhXH$+@:
"9Wa:Bj9UJBI8ZI3BK)gf.)KEf:Xq^:_"b=EYkneLZ7j,Ric=ZZ;Y=%Q^stQelC:g>[Z6L6@
_:H0T2Ot:A+F*6G<7$rJ^-U9A]Ap:c=fb@&6h^clNL$,2@1#UI;I#YZ&N<U+:ElJ`o'BMcE:!
U`Vf$8@a]Apg"=Zs(Qg$k[7A_npb1T[fGF1a)Q?G5\nqS^_C"N$>fDs"GN2>PO*mh;^*,P\ls
)iTV?7iem1e<Uph,/PccKFLP8b9GFa3#uP2+'pEgTYXnq&M7;<'X'B1QD7"S^/CNEEm%T28/
Jl:M+gf*DcG?jW[,:2nhhblM'uL-4<e*;u'H?'P4YY\Oahr.rPX^,fn'/,c^aufh^?p]AK-4T
WA"2@)@SeY?<AR[Y?@*lY.*skfCiO`PG4QQ:JI*G"@dDTr2/73#YP-o=-P+:I/<V8GC7nZ$:
Bj6%?Z#*ou/5VQ%T>,JSSc,Rn&i+O2J05<Y>`(]Apc+m.lB)L$ck.FL=iUR2Jm1qYXh5"al`"
gcS""LN0Qfrj/j^*Y>3]A-_$eN$_[]AqafK$F0*YS47LfBd(GO.n/XPJrI[h:oX2sB`^,$&P5&
UooSMMO&0%d#a*h]A'\ch0Zd1;rnsNHJH98DJSn@=L:.K5D`Z/n+?<$W;Q]AaM)SFn>[+`t<n!
DDWLKP"!C$j@_(4K:0&$jhM0:+ML=Ee<"I\Y;Rq?f=\_dc0G_k@m50o##1n3"ia[MO3bl8JI
d&l=t,_Y`3r-&@noS`\AEi;?ZF6H-lD"6`T<;.YmIp44oWfGC>IJIg\_7ZIo+2UaOQ/q0:#J
a:N@G+]AZ7Qhc-k5ahuBHgGeU4LNgEGm1rH:=7RTV-r[83kgiWh2(Noh-lNme!1S,4jhkb`3m
A^&B2rl.lQJ-V1+<$lQ'g1o#u,1.1pMi.J8c`,b@[]AfePZ4))qh9ljtE8&+W#`qp\Pd;$lMg
o*(I:`BaZ>/X,=YaY=NYJ0Y40PZ%9cfViAjW1^AT-GKS!&<lV]AgVRU/Z*tFNr0c'AJ<l#d0F
iV)>*ct@W=%idAUV\ej9C(GC[5a4Dd=I%_VeChS=p+"n"gBoq)al*H&U#8%6"+C4QSii_\./
=$<OW-VX_p:MF#BQ+\"C\ms*!/#b!#d%URF0J$cUK3VS]AXf7Y_i$REGpNsR0dtSW$Uj81<qh
PnArMeF`$rEg";)([Dqr_^0Hg;l:Uh@"(gk662nb^]AtN?mqAC!Kp*s1dguhULk31VWKW:[e0
#(^13C^[*!?2eS/?.:12rZ+,aOX/hb]AgmTjU7Ms_!$/Z<0)c1J)JhHRK]Ai9%"e25aq3V7Gsp
(iMBEYD0Q[2/NY_Ui]A&.`r)fU%R1mlZN3uqRmo.Gg;?.jXIM$-KK)V`nYU$iS>qt/Sjl,bZ.
;1l-f(sMIkf(:A8QeMebG&Ar#DU%6i.YdUo;#G^Hdjp.lM'p!#GJU%SiTN0n7u0>FP2]Ani(C
V*1B<3(#"S@Sc'K91I"U;Z,`i2#<D=@H[=VT?fGHBtm^r<Qm"SgcKDlcWSO#`87f5js+%;>#
"1lA<C#-(0':,^W\ZNCE#DU83M:'Gdu?:X%>#\IDjNX`&d^!eh=jB,.JV&%"Xa,BE4b)LpDA
+_XDte@7>&D%="XD>e^4s4$72"4[.OtNq!l+#[Eh%giI9mQ#LQTm6I^I3*Ppr1(*DLbY"b&p
mobqS`4)!4FV$C/m(hTRGEk@qmhVLHDZLTS>[l>>37u-l*j!B_@!LAT/3?qo0MJDgo58%ABB
HH]AOVGD+IX<sY+Wb$Xp[C/G!EN.rlJhIQ0;/gI^U`M`;-'oa&k`N6Y5+S5#/ah.b>Z&,KVe.
=4KRHqlNRq_=*f%o[GE=O>35ZX\FXn%+L.hp'HoV^3+)6"Ua,/:DcnG4R4E_'7)cXa&K'W+K
<Pp[A+HWQQF3g0h%^$#7UUF1uuEs/sfsKU9;,]A$bS3c\WkV7'N1q;Ub&<q*5,SuQ2o(l3KIT
pQD\ad<QgFN0>i\sne6E62NYi^#+*1%8%u7Wh3.A4Sb@CpkC":07lmY0VOTPN=]AB9/oXf$)R
D#92Ul6V,3_:`W4\osC:a33?<9\[i&RD\p<hn6:9H7tCq_dJl5uH-Dl3ZL<ngaFOd6Y*pq4+
^f[ldi,oiGs:.fL3'>\-hs3r4"gZafl<V.rjKW%b?VpEMsL-S2&MA,9aC/VK]Ajb^nkup'\c?
Do#1=8]AUP-&'#B]Af#`U%s6]AVVE;llm373Z>f.,U_\]A!S\@'G.;?I,XUQ,mI]A>PP>:La4I`HJ
"mhP\Sc>:hpC9]AG(I:ipW0_-QC)N*l5.[>l<'LGRjW3#u-((J+@/WD<3pQ7,uSpn_>T"m@F"
M<-*f>kVo$?kurHs:mFJFBBJkWgE`r&b]Ag<@(f'nY4F8<om+YN2os9,9Xqjp_0e^Ae3NfP+\
Wk^!-Imj*_8F``>uiZk6QeXB047,u$pj^c357--Vh0"*Qqb#.FYF<^^)J$WD(%(61mToO"VC
8\o;;p7S]AW/Tf"mrV1=n9BBe/]AA%#*7uGecgW0#SF#AQ1BA;WFKim=C,N2u2]AQGK*6.^)9%T
KnLjQTD#M9G;i"'@[`A#LBll*HbT@\:#<@c'kgEC"XQ]AVg:K:JSI7<-+#DC)59&^3aB<dMMb
<BTnGPe?H&"M"KbEc)pp%?$<d3p?Bi**=r0;47;/3oX?W*GVUeU*V[-.KnQnTltf3@H^h=uq
oo(d)+2J.SXbP;/>Dr#'((:sLU1#^lP2:#Z$>n<%1-itCei?qRL43CqF,pPgGd@<5#<kGqp)
3`<Hfe:XmNs`m/b4!cO4j3OOkEE:%@oE.cnlBR6pXKUXN3;5j"qRa_Mt[I5,+/N,NkU"Cir[
AcBH&3]A>,L[eB5o.TUgSf(q#QmijJj'F?_tDX4aD8#hmFeJ.cFp\-^q920/soKVs7m[o)\1s
Tg-PeUao2d$aW2YQ\ZJ*]A>((#!*hM'7[`&VldDbZn/Sq;GEuU?4X9$"_"rTN97nHeg6GaL)R
m%[<8<U23VXQAbd<UFpK$SRY@6fBl-pcE+G5ms@bfI0GMll3n\0?$6i6gm6X5CWh;\K.D(W*
_%/N.o6PMP?@gBMHaGo2e9FK)HqfSV4!itW.4n*XJPU<*Vb@abEeBu#_[=O.><`"9@"8`F;?
_R+$m)G,UmR6CHMs=oT[=4_R2\2-u7@E<pFj2m:q*W5;X$k6=I7DbQ3hBt0_JgrFIXN,VH#f
Xi]Aq%i6H0\mCLWLd*Q.a`5UCU$rb\q9/\XJphQ;-9tC3";bga0/<^YRDf(&)@9%bb-m0_1Yq
Xj/fe4^YMQ*`a%+Rf;1(EpuD.A0memV[=!%<DqCY(doHQ1Qe=n_DB(G@41icV;I[A)EiKJRo
bH5,?H2#K5-3>Sggd$MigQ%c/&r*PI@g?inZQOa%R9LZR?H5h\"%n5MBuP<G6oHr[,YdiWN\
.EGQ;%rAZF<IaFZ41S[iiWp96AbJ^(:QRo[>9Nd_B!7V!?<7n>GbJ]A7'X'uK91[s\Gim;_/E
9N!@#PsqAan5Amh*-e#s!mJt^4%rhTdp=`bf1po.f4l0eXo/CjARP\nTU`XZ8He&P.%:rMkN
KKd?FBe!(fmPE.0pBmU;)&.?-_d%&JLN%XFr0'_t5\et;GL\YP5F[G3f?Q4!=tY&\G4XNAc'
ni8q\Xe2]A%<m%lkQgNADpJ@7`%V/_cU7M4+V7_,gX'X-37-ZGkN_BfHF"o7e#3tAg+)%Gc`6
%>Je76.$qQI`Y#,6>:J5DE6VW+1jWlG"tnjA/4-#Q[kM92mFZd9MO3qGKDdK*C]AL&.d1F/k'
2-=P@5QL`VFk5<(Z<[C/irl:uET?co%gTdFLf!FmVQ<#M8h[.+<3g6W['MmqC.7Ws9Al\UK"
k?Uc\\\1s3GsCOT@g>?l*"qmD*?K5j6G;A>="0m1!dScV\p>'MTGY4=Khi;IReol:Pl&kqrK
n/;nJ]ADH2VU>CR%nRjJ'S[*Uh@sA*J.`dB2M8>Z954;104l?(AC1OU:@@bKf?JHRSXD]AMDDa
@:LBK2l<JLYX&DXHXDI&r'%h1\5%Dr$!,3`S(>JSZ>T&&U$o"R!D`3Meesnh0LXupZ]A/r^.]A
me'=j$F*p<;iLQn#m3%a)0:"0B87>8kIi$n?=b4a5_jaXnRa2`&lLfQMAeJ'#JnJffe?<Iq+
eAs8YZM+VU,R5/<?o@(X)5'Rp<4lG);,9+aAE8A0bRbQfbKO'"KVZ>uVN7[=^"'*"4'@JYE7
#:k(s0tdi:NE0oTj<WOq4PJYroeEB9>2+,ZHmQT9:Bt(F=?P/[HhXH'3q]A=F//s8+RR"$C\n
u`I>]A<fAZ'`^mPEaeO31qF_$'qKS\a]Ar6ttQUf5lrfqclp_K#M7Kd\c5*eI=$fjdOLWrNoa:
6JcL#<r261`M%dQ[We6*hQssT.?:^,q7,R5+iLUbO[BK*rH_9/eY3&+.1`V/M>DQX-7[hu02
>id+HO`cn6"amidABt'cVJ1!N]Als6fbJaX1_Z3$5M\*l-hq9:#7l7WI"A!nE>htEMLIbMd*1
Z\()PZJllFdM5_#GSEq`,rao8T)<K41r[dtT1SZ?#*osbTb"GBun9J)hEK2k]AgbJdKGdF>K$
s,HX8M<.>k"9NEo,5m.&Nsbi"=G%$$LdC16k./cB8'2\n=;A.?UR*L7]AkB`9ceVrXcaRV9.m
KS(ZA:_$Gksc"]A%-ZZAgk;XUcaO<E'b4.Mbss@[>W.5XABA=C*p#aIZ>bMRF\)2V`fF17I4*
/*cDpqPEKL7Ea;@XDot,.AK!5TO>/XWT$*om+bKT="+q]AIi0'qNW:ME.#9`ol#n`SE`Us)?F
HJNhb`JM1'Q7TY?ej@jLp`_cfLG`^/[H+a1kaLKed:3"?2l`>?g3HfG9,s:k()!Y0;11eE^0
b/hDPhh1NeX7#*.95!V'T\O=i4Y<bZ':>.Xr1_<ss_eEIONp52sBf"H0D(]A@5>@Y%F\\]AWW8
sVaP@;Z5u%+L3W0REk)**9V,;pH-4Qe>r%*;'ma)BBp0mcHN?,,'&5eAfe?$2Y*8KdRk,L_X
MSOOSYN?2/_acmp1933Y-=:62k:s2Dc9n3Asd39@V0BQ<^)ED[i(R4=UHA_3!Ae/Q=E`R\+F
L\ad9TMAp]ApO&X*PCkUQO`m['WZSdWZ>5)7XNcj@nTPX3Po!-Ibt7W55JO1=jdhS"B<(4"X5
J.c_VMtYNSC`m01?g8lQs1.Al9OiQP)N(mQ<l@$iXJliKaA?6;J+pTN57HXoQZ3KF`$fH3$Q
ao2eQ#DdAb&S#UJ3Y;5cHjl)7)4Ped8T_=\a3fdhhZ=*qA*k*fKG8t9uL<>)[cn/1JS<%n*-
aUA#5.R[!\KaE.PnHeZP5gX*e?lqjlX!jT]A<rV,TVO<I&+Mpm07]A?Y?Me=F)6D%-E]A&N;,!"
@UB[Q(10!&uS`XM_VO%LnNQlqU0n)?Lg4E7JRJ'VZ1%sA`a;.6?4^SM?u1opl>+>`g.UtSVE
h\gM+,qbF@BY0SlDm!99l_.UNj&+/H3*joKkXm7kj(>kG\M8,;jO!_KR&M!O[@"\)8LU!@/u
d6C0&5Rm:p,WFHq<ij686VBm;N`u3tOUMrTX$66rUC>%!'QZG8(+R7i1&N:AO`6?P9LW&g)9
41I>si\1]Ack[b"Xajn]A>[/Amb=Sl-p$@)r2?hU@G0,JRn9*+i35S:WLg)VceCn/%]A!%JHsRR
5EHX-)[en>dOFV`<kM[mQ_DdI_XQeJg%s8k6h_aTHGW^.k)WMe160^;aF/$8Ti*?8&'_]A"+4
d7'72un+c?9r-dW=G<#"i!DD:V5:-%ao_ht5ZLHLk@Lm:C%/ptN"M[tk3I'?B>.U<=aDAm8B
.fSM(6epj>AS<oJ#0*=OrFC4A$"eRpY((96)8De-q@nWV).EN'YCTq\<F/WV4_6=LP;NdfE?
reH(]A]A-p92E'c-.g8p_G5Md>KrGL3nc5gW"QoK/<<Y^;Y//f&PANoT0ALsc_#K[;Z-'J/@l]A
=D>hA)2VI9jA>&.0_7eSIB#sAXa*C7Y-'S=TP2RRrf4Y!EONMt)Vu?J!&/M+0@%OD&Frc(=?
?X5%\6noCaQir_P!16Og\(lAo.Z'EHP"@&kpOs]A"V#1`!t->_2PP>b3aS<`NL"PHXQtBTA[V
Ui(Rt$e`D=8H-OM4@f)(hU3T.Np@?'c`Gf4p0:1nr"D*@B=dp;1:-r\RdV@9XOaMo2T9TMX-
Zg4O(Mbd01Nn0/HnM\VOL[SdAm;)_O\d-'8bPut_92Ig$/O@[cinGq[]AUEE9>0rrZ;!o]AIb=
,Yr2Ud#V@On-d^YpH]A`0d;C&/XC,@j%S9<?^^3eMaj<+e,?N1ljQL_%$^07kkOP<$d:2REEd
mGqX.XB=g(tQP\3I"@)!S/mIK:3mn.$L@j`AWi4bZU*`ZmLP.cUXZ?\cR?#0:\oD-'@AXV!b
NDq!rm#Jt]Aooe>/j\1Sh_'Lf%[Wk.6u1&'jUj/<GBTJ3@emcf.rb8GL)4VA#bGoC(!<kWP=?
3HTu1^l.7TS$crR(=*FNpFl*a?Zfo[8^f'f@p)>.2kUmk94iE+)#P#SW$-Cq]Ad:+XUVh%Z8b
?+n65&E9mYlP!u49U"Ibh/;&&CEq=^I3g@a=Mkasn3A`d7XZ_u8Z=aV'.YGZ#K9C'>I[.BU%
WlA@Uc8i.8m.-fj&j<2s,7s'G>sqX`@\L"tq.?YCn5;U/7IQj9kH0^BGF3Cu:1N]A%80-Y:=2
L!SrKC\,XR.XMk+.q"4$qLUr?QW(P1oh>[H~
]]></IM>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCN[0P%jYTm*n^(M`Km`XZG.&23q:']A>@O9R\`H@\Q;gtCZ^5+^pH.7BprVDd=NgXTXg..:g
$r3:l:tkOIDmlTH`16qP$\Y7F1-]As6"[akP;g+f05h<8^;ael^[Q0hfI(cho>,.Z;[7!ClXC
1MOK12%7EWR'f5[[qcSfK<'-K2W2LsX3nS9%(R7!K=1`S(&$@cP=&oo0hsl]A0B$^=J?'j]Afd
E-0$cWdBKH,$@ABAj?(>4#=h3pATsq<qG;E3\\S5GHSqHtc#SOmS9@OfQc7H+bPHU#)A?$gh
Rf)qODf)Yi>4LTKY[kuZ4A!uuBpO`2`2=Cbd6'`_lLPOt^1I]AOTn3<0DM+JhHfcsN,FIL[.[
"b3H&h<Qi!E/*BKC:QT2&/?h2\@kDYF^N?u%-e^Th1P/q?CUGl-Eeo2P?JsZZ@k7;CQ=?5?O
Zj%V6$BEDXseona0&-)>NJU4VPDY;8Zc3DI-2gG1D_gQ]Au^[f[e#^oS^[<^X;=TP?4%,XL%R
SY+e!jdV$0`Y3L81W!"".OsdF_!jb$$fA]AeAcalt<[a.c1Se7MMGu_teIA(`XgI`L@$5Jhd@
uSs%<u(FGoU;mNp;n7QI]Ap6f9O:_JO'.Bt^S26!10<R<:nRFA<TTOt%Y($n(Ve=`LVN0mnh$
bM>YdrSP:m`5X+P2hErEk0</SqY7A!ecY%t]A.PjRZ5g#fO)S7hEU7P9P5KaqA%AZb#0L!Bd-
H=Ss&j3X,fT2bZ`]A-eEp[jk'D[Ttfgn!KkHpijB7Zj40)NU_FPn)(Dt:&If-2D-<Sq_+YKM&
kC#[!W6t^!&c6BCXlrJ3mrO;L.e^YJSfr^FoFGk58NGhrU:D&/LM\riO-KJ:BVg?UMO\L&O3
ToSi9h%\g_<E;SW.5QA'Hr&*qZV[%LeX?N6J[he`!ApjVcA$u=BU;*SpdeY<fIl<G(k+,.F[
"i/G?YfVB`?08l0),s^RLNt9<O$(\Wjht$'pXtqVB,XX0-gK/$Mg.--#Vr-TDi%X_o^%4Sc;
k2q6qJma[K4s9P-:?ZAU4eM]A.&lfL235m%S%_UYS9rD__VBOb]AsEi-W+G@,Fpg<7Wosf6H=>
jViJSX+T-=CL?r$pY3;'1UeD'dR=DmVG\K&<q=ZT\j(8#ZMq%Ek<mdJ[!OCeo^0n1B#DV1GP
E9(;aSjDo-qkKr1X=gD7@p!0"h$q"b(l<AsaUJ_mK&HSnj:?s)#,%C=:'Q*4Z9I]AtH<ZQ(`>
^YN4Wnffe4!7[mZV^b\nuK&Ge`_9l24mJPqm43K+h8kDm'f.\$&]A%^']AI[p.]A7?7r^RM-$GJ
]AdTXn#m<ErA*kT,1g;&pX%c".EdrQEQ4fih$L]A?Ii>'sP"HnXRWOChd@fi)"!MMLV-OiM$DW
;bbA7>HeG8D;?FF%Q@L=3!\@!#DDg[DWK<W$#/1O)1A;C2Jq+:hoqP^E0[9)3K(%X=X[N>SE
ZVDJ6DWuOhgNrq'dRsN&fH\MmcV+PFQlnBRn%8lHZ\'hPW=&QW69b`XfTrHIs5l!)&,BI(=G
NS23u"DB.*&mb:oRq$YLp>5-s*HL"f%u)0XhGrg5hgU6*?u`J6f<YNfFhQHLS!&mI</)W-7t
%)Lhp<*Bd+jrT/eT#WEc6%1VnKH)ejG8l0n`DqC_LbLnN!k`cq_`pEZ9[ok0]AOOJ3S%Z/rL?
TeY=\6k]A:M"5lkQY>0+:Sm:BL$3"V.H?a.2SdeOYT&1DC0J:>X47,nA)K/PQ5ngZWOi08f57
(FAZ`u*r$>]A7hRF>BNM;6*JPn&^eQlV?X_LFa1#d'//7`XgVo2D)Z>XBfd+o^%6RN?VPX)Y(
9&[\YP[tZ&.2@LSj"a,YnW8jHL!7-=lmD6]A46)Q1I3E:-Xh_k1^Zt`h?E>#F6K@Ng%jaL%g3
amaJ`,PNXIj\b9"?[I[.#S7AGlgK'3!-?$_^__:*-k[>/uXfDkc2H*Fi_o*)`N7b(`S+6LL&
c,`bZcijM7Sd9L=*fWlIZPj#;0PnF+KI%oUVC`V#2^@&`1Pl$atgqJ3hgk*WBOFu[fXtNNdA
@`IeAeu'N5]AjK$`p?i'+1&`J'_D;&$ar-ABe--$[M"M[Q9t9n,'L=IEZcWEI+J;79%I*Of5,
^i@'LcChZ;Ip1LS[ufi7=?Wbqa.e2>7oXF9.q7k`i.nVs_10*_o.,I/$-5&b"X+T/DRKNhL&
f<T]A6-6IQ>kBLKL(N<(q4`gKN;Q5mf*?EY$.=(^>I$f(<17UAsABD.H^dqnU8jX35muprP+Z
sO-jP0$Qo5E<S]A4#Qpa2;qZeB_pse/!7@Vkgf)_q\S%K^VlF87^?BJV=K',RQqi\"Qb`CYKG
t$?f$42X$t"htl`q^^Ug\I3N(uKbm>rA9VKTqVs`#D%")EbKjugk,1p(iB9=r9Nl6ls%p9B.
+WTm$!so2M.`0Tp<Db<GV@qMNS%#_H`^8K]A$9tB@,*.kX-tk%[*,#`;05KHj_^<Lo&ClNd>7
>ObDgc`"u^n;dG1.UP(%g2jq)@('(L]Ati5]ADI(dI<n*nqtNXX?Yt;^P'^BHu1?Q@iCjiQ2='
CO1j+EHog5aX@[5N#atH4huni)9OXomBLY2]AjW?52UUZ*4/fMt/R@C'#\4l[$#%S[$NFJ`pb
CJt>Si<rR$J7<NSnGY[55,HJTf[#iO1eD>G;k8iGs.5\i5_!F+U!9dr+hq.`I2seX7NTqH,P
B8sslaAJn^_D5ihtckB*iDt7ogiFr_=!N[i+WSo9?+2B)B_Bt^tTi.00dYdbQXaS+XPcJ872
;;2_1Lcpmh$4k*A4I?*fWPG=T5qb;iFe,?P#/Sn0sj;;CWnP%kMk!ZD>FD"lX`\-I('ltoG:
eCMK5CGT0u0B(ke<0>A<*OjG5K8j@1P`CfTn]A"pKIf%C9mX>t0*B[_)0cNK)kVN3Q#lKg?Y9
.7M!o0B?rl1B3EZ]A]Ak"Gl7J2cCQ"V)_(sK+7u:R=N;Y89hn<]A6f]A\VG$bl.UV:6'AYH^.S^<
Wm'NHI]A!\R*-Qi1P&LQW+X.6.\);Vh80V'!kTFb1b2-X]AbDDZS,g<6R/PHQWa?aUrPk/$S@I
L/"3^"3K7BWT46i[grr%@@T_WFD%n$Wb6n(`5PsiU]AXuC@XQ2m4D%:=VHQh/!Pdn!N=9PFGQ
]A1uh-Sn>FX-DoS2!^L>,a]A9887SU,2(CmH_fT?:,nA_5#j0,9=#5':cS.61k'`ZJO8HrpU7>
\)9r6Ig22:CUla+_-&Aj>]AM_%i1.TO-s`0=i8c44SZ^L$(s=H>MT?5iT@iD/Np<G\.QeU&"-
i)pM@,.t9nV(/^H)7>/0[92oM%h%]A0Y@qlSFKJptJocD<Gc2$Ki>CcUUp![&]AMoH]AjrOF[5Y
;%4H&T4lT-RZ?=c[WW)hff2(+(nK=?Cg4-\:SH+T7l&7*4K4EUArT<7;dJci9DD-Pa5q4$p`
6RS$iAAu9>`W2=A.Pn)"`Gg]A!>CD6lbRbe#gNLN)4*BV:4K@fFP%`+F.#R('K76Di/BV'\f;
5Of[.8gC`mSY"r<<DssdPFCQOR^_r#.s$ZdnI[!TD,FLenV6XHdp;@.Kr_W%Wc<&*G1T-493
NK=:`',D.X[le,hDF;OCAQ2+D+_emF`]A^ar+R?;r#Be.;dp6mJJ\O/>Jfc`2W;hf-J\Hlc@j
e=2DG5p=mceF_j#$H#E@28mRS0+[bQh!Ut/!;A/!mGn@JdR42)oUJO6"n54M9c1@&AQP.Mj^
g3BH`oJ_U>I"LKXK##5n(4>0l/=e"hPc(RW"rSXSBuGM"=C>F@doXc*@OtdSZ]ASC,SC8J;gX
:$+9@'GTZ+rd$)oeFIL99;'*$_'ENIQJE[>_N=k()s%+P_h)=-+SuYGsNgTU\#7TZ1_?Y=J!
-TAKIEI'+`T17V6hQFuV^kh9'oC<"P_e$^LfY-9\Uh:U7f1d1^HfW,;n*'aX:mDO1@50rIUE
2r\KM$9UW5VuWMdhrRW(\DC-I=1DF[<XgE,!?:#bYfA-Ag#.dMn&AH?dM0.7j14\G3rb2+[^
N^"n<T7$PWo&L`o9mCSQ#`PWHB*Gotq>tC(`1BFkStr1-O<hZ)bupHBU55A4Uh,#??.[-tP;
[EO-fac.3()ap*@\GIbtn162pHCXQ@S&T/c:4;;'Ki8YlU0Qgj\>+FAA*c!TQ`n2dq+A.h@Q
ADE'mo`:[hWND1SWC<!k"VBZCJ>+3Co=83plrW>p^34a\]A/5eMWkHdE=-%TB\*Qk0QkgpD6C
)]Aj7P'<IfSTbf5oT@)5_%X,o`[jbn5$m)ZX9GZ$0kE[N//?JBn>_Y=9pOor-WUUI/n"dPYp7
T9iS((@k>A$Zi/@uMOJj'C)^K2>M0B@/CaZ.n16El(U(Jhm`=W9T`H&#GTYd'RJjLGEA42d\
J/Z9)dBasDOf0R3^B32>k#tU=HF@X]A^YbWOG:$`FK#pVY:iM?_?d2)Lk_XkfjX-Yirmu>>o7
(R!nAZ,ej5EhF=+2]AjQ':>^$ATfEFsPJs6YRf;YF+$e!Tt3NI["@Gg>`VX:Pa9A,QcoPm@NX
gi@I;&Du+$Hne*Ob;:X[ro?6C/3BW?'^8lI"Y9mQaL8D5d9ki/HFQK?/K76aAm?NdGhTC*$A
u;;1\ed^Y]AChMn(c9Q@QMlpK(+<eiWc3N9=t*"HBee;tS&;;."DbpiElL2>`!UK58ifAQV?I
nfGc+[ihue'LF":5XS-J.gGY?_mEZA.-BspnsN32\ZN+uFN8X5@q0gbDZOlX0PpUHSC7@?)r
I9ueeXU["U,.I_6;jP+K5\aIeH7;XsPKagDY$t6J=ONHK!AI<Gf1*[>hC\P(6Df*HUnlcj\?
a^>9dY-B;`tm&Q)dh6eY08HgU&Q4PMm:jibW#>W6q\:T'0FM,]Aj89T6CBqi6nUdbh<dh'bAK
dUd,[eZ$8_(O\nWOTuCn<Sh9r/BB<hAX;bg!eLR@?,SDf<D!:C/n(&bD,7@#ra=qZ7U?ZqON
Wp/M*&*6:Je\U%ZAo3.NBUC*)g@XXkO'YeU/BjbJl3I)%gIId,A#\(WVUR/5pJ=]AW3Xb!i8N
EHi.hF/1(^q6I!V*@o)pdT'?,q)!Di6s;_)T@iHU@>DTd]AE's3Fa8_1($b4<&9&)nGi2mn^Z
mVdpO.I3X%-)EO\kGces),1FZ713^%$Z/lD@j9M:l!8^/pt_U=SaSr`&e50P_J)!eg-jhi]Ao
:LGmgKMcd5iO(#:n_<KC]AA!ll\!&/KlQ:*'s6!nu##=aZ7gEf%il..XhNgT^s#nIfekG.V5m
e"KHl4gur]Af]A.f44SRH'nh2qa/0A_=-<'J.HJ2lEQG?re!fs?(]AP3eBbp?'8NW?&ngan$(#+
^J:$/<T]AIL]An<oi1*mF=@r7sbnbna05YW9$MtNBc4ZK+CQ`87lk)FlM"/BIK<iB)$@@<]A^+%
e*a@H(80ShZ7GXd?=At?;NY&gUp4ALAO%lE"0]AX4rH:q.c9\e5tT9OA!VatGg66E5l3GHf0N
%*"ZXR]AJPbp5j&%T/R?/4^)b"^c>IiHrbPdc8^B:d>.d]A"M?@V;7::Uq<*M2jGKX:;6pC&=I
j_]AK_>#Nlu<(r>;<p$'bPK]A$UuNL!h7?nB*4<\:jL#4/"r#:/V"\h7#kp^L=-BrR"s[@8an8
VR19l?70ft2QR24@[Fu9IlspRn?u_MoEnm!6?((:31)*Km<&d&2Y6SO=[N--)kTS$H>3DP15
-:hB/o-JYSkhnX,mLibJe@W@Dd\/IbA<IQ/N)oX=_"DOVs;<A$Z87EUZgu6I,.AYW'%kgWrE
Pe*3e3_d@TJ@1Q^XU*>)[5`iam6#N,2f%Sb27a*&.IfPCX`3&M6)8agV@#c$mMf$Z%q+F-HW
<a%]A(:*Y>.bp_8tOFNEmLa,Lan4>b2?9e!J*`9R04*L8XbZk9@b!X>%f6]ATq>(Al+KEW^-An
mAkdTb1dgCq]ArSFG`NOe?3Xg7m2=o<FG?4P<K$?ZpN5YmK`MD=oHB^0J;[Vhi0OhXHWbY2+t
G?I_ica/EA!_D',=ol7'NCXe=LTcQr"&mD$JSLe3F2CTu]AHQK65&kW_$A#2u,TSs37!%VKK%
mQU7Tl,dX%nDPu73h=f*b#X1,D71Q71D5^7pc!/q*^@I!Qg7*VY%.(o\'j_F[9+T9/hbC.d#
[M;0%4]A]AV[JdF,W$.%$ifp@mXU_n5iReOWdBHE)=]A_A-i6\Znr@uh4qFrQSUhh)Gnp14-FQP
7Ti@*3h*LNh(;+ni:isD%*JEabQB3bY%,nO(nZF$+#CV4k!4\rjFRN%9%d%%Cid4q[tlJF!4
(Fr"O4g1Wkk#<,oXji+8+<J;Y3,nE()@E0NLM%8"tNj&G$*<4.'e4g+8]AX5SUB6M<nUg?>i;
"=!DnleHL\P10?0F11L%gc:,VP7hG\I\_]A.7fchLr%Rr&@+uR3Id[hJ#f\Z(tpB;oL24@uJ>
3#@^KC&@b[,]ARa0#)5fc'N]A-`6&Cig7[l56uBs"<"*GHS#[:kFd^ZO\E*X"k)Pj_bpkBk4!_
sL9M>tUmYas)M@Sh`(YgB[6GmlJ;*qbB05=hT1%/;gZ3i``8n<D!*WNCEn)D-J%2\q8FcA1P
Q&Q7L\-"6j:[K$m*knq4,hr+IH.IZgqY.Eu/GV`D4Cu"g6N5W1HLn&4:5AkJ4D^^I&S0iRZ[
5%\IX_1Zo,D:;Fb8@gIU94NTrt8hCJXHbTBmj\@;C$n6V%`1.!=L+*EO!I`(u:<AZ*PY*MiK
e>ss:S'1tb\S+c5W5H&&5\-gpG`sPo1T]AETTZbEuDr4HoO%be_G_+SCQj?_8ChuA6WiT/sN6
J$?H>[llFd=c\n5EtP1]A\`G<\uAj,$k)lWLS)^PV$De%G-Ae/=Lq_\bZTk'qhNoUH;kOo13B
Yg8Qg]A`;)TI"5+,oVns>D%%NQ@/3-]A_lM3Mj3+`)cB.'Nft;p>2DJ#O.O]Au9#7GS%([f/[%"
WW6X7,=jpB&"Difn)kLD)XuO^5s*NY268&h%E.1@Zh!V^TK5.\*O<TORnp/JB!)OM$VU%#CG
7*KJP3)N8<ddH<bCC>/8><M^QKC5lQ#\C':GdqMK!Q3o<=^;8]A7>k_;r_g*XhYcVX&:q<Bc*
EW/F@r$Hmko\akSsRrnk#,dGr8k>%%.PMXqo:cie`_nT(]Ad\iJ1;1@2F/_!"9eD4,_o5YpBQ
/%PM4<tGChtO'7aHGtq^^m-L^VY$^M4sZo-TYKPV$PCAgDp3j*Z\#g4]A^+!*/B]A'rG2`>JLA
-:X,W@VmCm?.OOZ6Lb9"r%V7>lC(qG$Cj&d<A=nB@Q#"''2!MM]AcH8L1*[B2k<;<6BXWV[mp
@]AXdX%d1%.@9JS6'i"<AaU.k,;^EkIJ1F4@U)l/NlQ&!IP6Rc1R?F8E"m?=5<_pQh8^*W!_M
4OZlp)VM)IQjaG-,c8cG*]AV\E1g*:p@*pU3E\"T+q6oK4E&M*7H5G6APN$Rd9F[WQ=nth%n0
un_@7L%/+-(77DJp$gR:OS=CcS]A$JKq:KDYB'fNi<]A4/[eS+r<)!kCPX^RVW_2=iItq#:,37
`7qg8&_[.9A;VLU-`>H,^>B4n7$.KnB^B)o85!";S\qO;(0#.$[4ArT/qcS.*0SCf]Ap;l-M,
]A9A^ll^#C+jG9c4!^n(nUUTPm)YN&%qKSm/CN<p(hB>tN@2YK7/rqLn'EGsOXdQHMuLbZN)Q
2/8ql2bAf[EGJO2.^6eM:H"("B\RGUJ=%/0.e4H*pk2f5<M>8,E\!:`!f[Xic_UUTr-jg.8g
8WTI("U!Si0a5">(t@W&1&Y%qngRNW\sHX5_,/:)9#_25j"igKf]A)Tc`ip@?O4hXDGKDWS'!
9E++[*&%i<(^Gl'.l=f8-Pr3eV<o6*k@pfG#Gk8RdXip3k&l;?Ubg/OR"^KY4HJ%UUQVpg\m
iG]AbIU''#;)mH*'(=e(1`%.iNi9`NeS9S+4bD?L\+TJsqs@<(q&$7U+YXTa52<QY3P+%rPa%
42DGSnFnk:iE7H60g,9p<k#I]AM<T39F]AL*77j5\$Q^em&SIVu`g-KR\:Z99Q->8q,@3*3+A#
)d+Y%kP_XtePD#*rZKDdMASOX/K$LEHLdD<HCfMcSN0]A4H`st(7dagjMp>+qj?Z%]A$QB$Qf(
@1u;XlY^A5[oRo2%q"88*/2#cjrfV#tOh2Y(7rQUbnV"MENsoOIufoGluu')iY%H-"&Q6Oe&
4C]AGQS.;\o36)qs*W[+5Zp3pW<LI::I7Eoq6"4k=8T*A,/"@@Csp*[_C.E^Z5#6)UALE:oYZ
O^u%.=?"qcZPmNIAbM7SU]ABqT=ka.%rRqSRhaHm;KMCI8G9I"j+$W:+5Uf0#:fbe?[[6lqL<
:uLDPa]A$#u[)Qd;>si;iW0p=3ddE5$eeLG$Sr8^S@0n5iib5PDOHmj7u9Q2fF=5'nU7s3cW4
DdcAgqF8f$,k+:"Qgap%?[_cYpE/0OFhFL-1US-f0<+B]AJ)`GhbRA+,jQZL6K*RY)qnPGSer
L>:ru5l*IsdLVhft4Q^\2-Ro^?*[s6dl+O+-i?]AmZp2:Td.Y^BbJLBdOb*o3WbU&IN*Ragj<
OphcW=:-LE.2C4iV+b!V5JeMg&#-n.)84kRnlU3Rl+sN,V)NNE"Yi3VrmP8@eip\E=dh4'+N
KF:)jGD@B"7W6[T?7)H#r.ZQ/U?#;.pXT+aqKlRV#5Cs!?f=/EEL2I-),47^NWZ(T+h@uX(%
51("9t,N9JQ&M.FY19!A/ug$l4PYZFYU'95gHd2H7=c^H56##1IqKu\K]AU91Kl&iof8+fJW/
m.n64FmC`6GL2:,4rR^Koul9)o,cFH\UF*trTWH5h^i\;>>aQS0/UJ?>s'o'`<jgWI*V,$hp
9J$QOnLX0uKu\om7f0&MX#i$/L5PN`>euOdT8*5%A-YG<IN:i^eQH:3SED$e9u-4:Kse4uT%
d7^Ic.__^6?anH2phJE_n_Eq+*EHkN,l+S"nGm)\p2pLef40Rb;G>H-H*&KT'_6K<WF.RmV"
H>62b'R@HbE&QE@2L4<d.>bYG&!$sX.=@$R3=Xg]Aak5L,DR\]Ar0Sa'?/dedBo"q[0D]A35Jr3
.2C'=q^,H98j,eXYHY2m[RDcWB*1;@p<*uBZN.._p5mT^Yj2m,#gp[UF,$Bs05_RH1CgX5`Z
"4CIB\&;RMB$hJTHYe7QS#%k"(m_QO%?1SRR*e]A@crhaE.#sSD)kg-E]A'nK]AE5XV<;=1<0,P
W%KA>lZXj<aS*2polclPJ+'3qb^0.A`b.,b8>hIX+LjfMRra0>C+uF9&g_Sl'VnV2@A@3TbW
C>k^&g\4c#4oTCs6anJ%Fl;f7+=HGKuqim?VH'9AY7snZF0G<G4j<WO970(G,"A>fRj`Lr*$
@Xi-0s5cc:;>ehO"JXL_(!q`dWH?ZA!^*SZ1UB4X/fGkB$EQRb3W?6JaMlt^AhL#79J&`i1c
V9'n.+]AJ"FFCOl>bp!3cXbD79L]AZAjc6G!D9fd7Qk\(gJ?bl%$4Bgh)a?D$A4UJ<U=t(*2RL
qplg8jV"lkSlHD&*Q<g['uaDAq4(/@-m)0H=EfajgT^3!O2E-rN]A6sajdiMoO;EiRWag1im/
2U2]AD+O`8'Uu(d?m>2KbupOo3n&)ZGkO]Ao827W5l,U42t(.?N2<!h/b7\+fn3&33o;<9V^Z0
oh)RcmD3jmgSX<B-Q1>0J0dOlZKKq<Df)mo:F1o45ckK0*0;PuNZ"<.3<oFZ.2Ni9%;t&]Aue
dn.aA&tIRa/5e#a*?,AM8lm;Po:KDp/N(*(*14"*<@HW#Eh>Dpk'anrpY@9/n60ugdYA!jZV
9r4mPa63O`<fm`Gn?$"+=]AHG#P>\&\q%el$7F5kUQZ)Qj(-^FtWr'f7-mj&U/gOiGM`H!S\%
]A1Z\Pj1nq5eX*o,e8e&"_'9US@i!jnCk-1#a)tqc5a&0[ViqVpiE&2RM2mj^'5T/MUjM>gfl
3'?P0"^><]A]AR,_\.&8/5;L>"H99@(,]AFs/nRcc^]A>,>U5Z<4G,6^[&O`[S]Ad672$`Pk*YB5a
S>]A%$0Vau?ljGPQVWi!pkerLF)j5VmDJ$pdch0_6<:eN,KBh;eR02p<3NUZG>M=s6V,dWOQW
aE-2^@_X-pU6utM8o:2qn6W,I(/.4\U2r_[JPJeD!iLi+fBoYCLQRWDplnk.MeKRKN6CV>g&
XqWboAT.r#6nCdu*0"Vf/AWo_XZ8fo=!F,K)`Gq?l::_G3:D/GFjedVt!s$7\!eCF[5bk6au
)7JA/DZ]A1WosCr&TCGf"W]Ac]A.W$!5'V;R746+O(d^[PQm6S^2uk+--9AC\NeUQO/[4>UntJN
J,&f'0/(1mh?^6Pj<)Xmb&4WYg4^h?P_nStZl!UW$/XCtO@Y(pW)8!cktJCJXn.8[\*GH:._
XNPZ@s3&VtP1:QCF,W`GOX5KT!WbOhD^Bmh'JO3kS&MAu/:ibg7os@jjfk+&u+YdKD@Pn:U2
/k$pTMO2CeN:GDDZ*TFme,pf,u+*]Ag2_&n$gQFg_E)nj!ZF>[4_/-dTX2eZPh5hLI[^-n63m
_(<OIP^5KBn<%;!R5d%&2$0(H5Y=1'hc?[Ua)2)M)8WJDW'P0P6d)iP9hJOr/5<O.'6YfVQ&
6.PP?n!6i=\[2IAe_j3G6K&0^br[D&puPC0`O[S=knr#VO\YKh*rjf1b5c`r>KA]A5T,X<2:t
i&!iISARY6GlKmt<J(H.55S`gRVh2!@2@Cm9.'Ut8D^WQVdR^"sTAYZ1'nArg:#V71+JkN^%
s4_4?Q#Zq[u2F_Zk=T+L@[sC&G]Akc%UBm@:Y@F3r\RS4d86nW>$D*fAda!ttcMLFY5MlqFGP
EWo05F*38fl)D.g9CD+$2.\oaeG=eJp4U<A\2d3#8A&#GU`es5r>/q"tP5V>CF?E\rXJ.K6s
MA4Jje`KP+_;"O-p$]Aj>aeE;3&XpuoE!83^5UOLRfBc!kp%D2.hU18rW*nHWWE^*fKs*@3H[
6p;C!Y1rXQWE^nc)qk??j#j,^1tN>H5<4I=O5T]Arjh>A5XKFD/D4@2C-hng#Qhen63Y4CI6!
2Wa0h_VDaenD0bj;LAW3s\<RH:PaX]A#'7Om[[6.iJ7bqO?R,Wlc@Wpm0+YR&40.*#ZTJeJtg
,S1BrSp@Lk79.FgM4X7F8MjFE8je]Ao#f?=6\^+8p.IR-?1CSRP"GA;Z_,[@htF+(i88qZoQA
JcEPHCX?\F!NH40H)skW'>flYm7d'349Tdc>D^k/BrWtU5k_4`TOJP2t0BeOq:C_B$HqM,mT
)GiJ\rpBp!=3Tp,fu5QU@[(;D!C":@WKq1?X=$d$]A9(26B<\`SjrhMtp$_)e5T;&a?(7['fL
rXk32p:J&2.L$RcQRK9\H*;hbiXWH1VV9\,92O"<r=iL*Uh*[_^%Qi-H"`F%b$tH6pkUfj-(
.^]AR+QF(CN4f^QA=S9?ld!^9mESOO>?nVmPW#0^3d5k?^fiqBdY+Lj53$0#i,p*q*i%UOj^B
RI'mntX.EV=2\VEnfPA4_D'TfnO768.qNYt89tsaMRJ!"V?Ja&fk`VnW`fbnsgolUH:e3Vl-
?f=qYdqAmcVnGs<`qGCY@PU?]A3kQJ]AQNK8:HNqd]A#h-]AmeFAKN_F-\6(+rHi4pf%OE8nd(uX
picNu'pclIFck-Jt'29C[c$!SDn?^*gN,+>TPC=YLQlp)QV21.C^1/[2l\u.8DBR,oW([ABh
b#Ai==70m"4suAHn1Vl2^sI>N$T4*m*7h;s9T*;)$pN*WGf_P+T+DJBE;Ucq'(7UDGr5Y:CG
Tro1a8>Sj*IhT<O)K!enr(t&kgIaJP:&8EeAIfV('@42:Y48:[nOFAQkcLH-C)0mhH`$&U%c
ql("N-qdm20GS@@B_ZROGlI+I,;MAc8JbnXnHk2mEX`mMf`lBJ%4%j8,L<C:V>3j-lA^L?AH
J5.b3WlK'Cnp5VDZ"Ogo6F#Kpk`Xm8nr?C($PrsJET`4&j`#VB#`)?Q[b,!,V_))o5&?.:Qr
u+B??t%6k2RAXC>!I5Ym5F#E@^<(5iQ8YA6>gp)?&_]A/:^IO9Ycm.n%fA.%dG\&LSo<[`YkH
&b'3\8\*Un+fD/feOUiITGkJ7DZd[M-\?j[9!,___mIn:IeB+$SOjKueso_O#S;7310O7]Aif
e[U^:eV=RO_aKM=Hi@M.'1EVX'qM5=S;fXdkh$B'iF9q+co44-#[7"FIG^R`.[\;=WWhoE!D
Gf)sD0SO"TKO>N#Li/h3e8:B@3T0*0m8!<3qS:YO/ZXWgKgm<<"$@$&LeAZ!SUqWtD4R-"$V
lLiI,Qo)[P@ofun2RNDWI,#,S?ihqG0;b>$)MQ$M$RAD==mG!_n7stmVEp$gQYIfCMH8,?fX
:^6_6LPA9mtHFFAMEKEb5IhBL/VQo'D(760mYhqQMemo#u$T%*;W\&hVrj.3LJYj3`qAk#nF
j"73Xhu0>%S?(/MfY.clm]AtPHeFG6@St9&)FsL_^DmR$sTNacEmDiSG%Au/);]A8p/_Va.0-L
*?)-JEYaJVga]A6;Ud.lZo=JZ^V&[$jbK>kA"jX46XK<^/c=Yh&1WMDSN(O+Y&B^Y[e6+=qq-
Hs-pL4fXNbI$t7,)RP;4URG[QrlOQQjA&)^:0b_7\WHba)jRXr^&7u/dLh<Ve_]AlZAQ?l,!-
LM7b6A)RZaZ^#rHMKWg9DtNn.fn]AYj_^W%AXKTo0K:NV#7pMnB9-7B:g@'P\h]AgsHhu-NrL9
cB'&b:r;@K!IMa(J:]AHNlpX"DUV^::5-FXmBGNl7@?E5)RcAd*#lbJCkSJ7^KY5W(niE&`0k
[Hb;igcOK^I1;;ed<HZC8<g$:B@^*?d.oDW)Cu`H`_8DTQ,UYXnrpHA@L:M=kM>E2+=FZ^#_
k)>Ip@k.5MWAZ[cM0mQj)\M&%B$"[_QT=g[Y'FJ[6bGQB8]Ag$g3Fm+gm+@k?&N;B2NS8S\J-
P;^FmA=B6JgaeD[JM3n)I!Jo)g@h7h(pKXP`'%!V6*oS<@)^1O:=J!3gnb2H]A`o*W.jHZ*km
lLeT_']A_;0#WiUkU&EF<1:7--h&-kNBqj!i7T`;Q<\kM(7<BU8t^h9%D)h>k98oVN]A)#%`fI
[e\[VJ147,4li&1b0q>n(=Pn2bP[:eMJ3\G&AN@Zl@]A/l-$ot/R2cs9:,cAK/<bo)4NIW@=s
3JLcq_0pW,\>Y-L:?HleV99_ZDd%DsRGLHs&4nZn\L$@q]AeCHmfi@E-.QoknkpcX9#8Ns"Sa
`no4J\I\mjH?oNI^uMD.pj;8F6J;XadW<qLYKi-Y]ARdB07JXrf"!ML#I5W$XS-AjdpbF<,RI
sJ8tKFCG\sugpa'qd2A**T3si+.m=fPeK2q-WX&TLiI14g&@,NS,kWeWB-7u__F"a!lTJ2X3
iaBC<U)06n,[Rbh$F8"Qp.9QRIE(b*HDZLp-F)hA%mefff-T_D/LcC2%)&O$/_TFq-r0u"H%
;-7Z:I3-nu8dD-+S'2q&hnNN%Rqk,'Ma[T-@:"bN_&Jk1K&lj9I*;!?:l9.fi.SYI_#>iO\E
>#%JD#CO$i[-c81EU\Sb7P.V9M@G&BeGH88)obo8=N:>9L-0`dD%\#-7i-OYjIU`sDQLHk^+
Gff37Ku:Ur$-ucT3Rh54URhd#G92C*;`Y#.BE$f`C^4"N46G6%M"9?Z870<lC`[S9!JTciJJ
SW9^UREV$oSSQ^1/nRGl4G9.5ebT7.M6o?g*podg*-C>t1faO<jQ"uPV2!\e1U$nn3dYG'[L
)G>Bg'_JF\MieTcL&-Ri>NLW^NoQk0`'JkD9tT:*h\9hBa@!B"WYdCkT5.4@2@s(#h0n\#;\
eX@2GP:1D(jGrV"Y+G4S->9:V]AbUhYUM=pL[eOI<.bAFf)Hh^`COn="M+LM55gM9pbuSN4t>
Ol9G<%FUK6VTq3+dFk"K.N23Q`i;W*bd$<",:+C4e72'O'm;u[MmTdm6Imjh4ji-45IU7$fe
UZ:iRrkQWG1h,#0,g:75ONnCuHZ1@T)UXr6!QOg]Anq@?1m:N+5Sf2U/'<[MA+PXOBmJ6SSCZ
oQRQBIB5lDBaBRN(99e5_2U2K;P_7KjBlAJ5+/&$q&4E`<G@mrJ$"KH(_AA/,Qon&/)J712K
D!!u[b0'*m@6\dM_P3k)!9Jq%B=Dq`8*UF>fqJ$fbYRPns'r'B4#q:1Di;t8N]AJJ/bLm_/Qs
u@RngnmSE)0kOtLHc/HBGCGYcZITIT\:HUQh<`:iKGFidX[*"i`$((<[R.c)'9]AIb/h@:A+C
7pfHdM.'+P>00i_1Z[sp(9P!eN7r5h459(kXL01d@Os(Jph#8N@(=`pG-Wd'\t,>Q*`h6&IN
VX_R),L?SAK4SjZ7uSMloBp=3K$O\e%gLDk:$'oIgcIp,;lAM&sWN]AJZ+lSfD?Q%Sme2>c_X
DrKu,URGs_nd<#Ys(kfBjq^f\8]Aq"C^(41]AE5HGOgSXG+bGTQKIbglR`2%?cpYsj=e<I4h#>
!f@,8&AY"^^(0jE^h8uZTO:L9!QP,.kNH&RcNKomjJ7#jSgoB1b/H=Wi1!8:C_Uh\ssKC.QX
aqB2"U)_bk3gW)'ihF-0%D9U:kO&>(c$U5Fo,(R3:<G-l<B&,"1=Ne&hOgLWAmdiBOE*K?Hd
GU>%@.s<:`,K(sa2J1-==EKVO<1VqBADG@hi?XP948?"!;O.b2J!T6"cA[VN7rr5Eb^pE%$E
&L!"B[f&"<h-$*]AE`Y<@&IiHgi!3D60e=q([Y^Vnkk#\DM=5^uC!8/lVJP?3!E&)fOXP*d<2
#3-'B'?Ccn&+Al<=X(^T*ThVFLT@MiZH[,Z]ApME?[/+Zpf92[SF`[*&t\]AP8grtahq-b5%&M
Lq!DL>X6V$>U@ZY[]AaIV`tD_bkVBZQqg&:9=1(Okga-[%"XRD'TH*m,6d8kLBA-sCkQC_730
>q,M#ag4U[!Jo6#=,pBh/@84[/0R,%f#"(RPf6fm&,NJ!*H'Ao4N(1P2jJAVdFjL@3u5E6;9
1"$HQZY\9Cb%%!1U&[%,0N.hLe^g[T$:.Hblrh<MW"msj,9I)g[[,[Pl"#,8b_UM5]Anf-JZq
OB=CMg:;h&W4<EdLZkPO\Oqg2@),Z)TLQ113LPLkT>jRa^SYp&qi+^VZ!D'KC,Pa?3TNUcQ]A
4G$Y[8]A3<"r/C/4"p5h<:)"WSQpulm$I^6uIefQS.&40DT[Wj&VS@Zmj+XUVlJbQ*bDk5",#
+<U)7,iU]Aq.HLID@XNS&Mlr$'tH`.-/u^eh;tF:%/ELN-[\U(G07UIk/StO0_EN]ADl62td;f
XZi)^o,bUW,aMA48X7TOJ<$;Mth!7aIhC&R6&kb5W$3R:!5Z\Ykt.qZJ?Za&2%5<@G4;:`!(
"k>MEOPZ2Nim.<[4me[r+FPC;C1O>OV.bOf=Q,`^"I&7fN')#YosR4HQMq52?;PZNj@;_1_k
9^V([*D;;L_+ZQfk>0la5"0EWJVXE(";ZjFdi(/nD2F2^nrhP@2gm>!9jg[qUZ-i7[:!-M?-
5<q(P)Don,+>p`06S*ZuW*kB5X%09@()%ei:8Lo61B$<j>p_hd>qp75@6u+X_]A,IF2ou=Z1U
<"oh,idAALp`O[d=npYlHM9ibog;r-fA5(IEVI(XiNo1JgHDrb,d+<C"[%:SX!V$^tqee`QX
a1VL]Ac7H#:qmRJb]Aa2XK?q*^5?m^Tp>n&1:G.,O:<1"TQB20I4>HMrmX_-Wg+nL-EoeP6j$(
PJ4:hC,`G152!"4@;%W;ke#n*S,U+1F(Vm<\A-FnhW^mU\T=Kt(5J+\g3]A4HC=r;`>men]A%q
8(2oT1>f-(3S<#o(#+rnW3-H4]A#HdZUX8M%#P.8RC06CEB<H8?jQ/a%#E)D\!fJdcYV@;u=O
:Hcnai",YrD=V!;mWs@8T(.JPP[8+p_fs8s$jC*l5%Q)8dAj-Sns4"\7MV0,d.VS3<U%Ic_\
U5VGgig']AT!"32F!WK6k;AlA;h%.ZUrcVF&cG;r\.S\P3:GXF]AFnZN)W(/p5@@DjQ+LFk+K1
UFntQV0dFiq"G&2KKCDqK^FT-\NWYZ`7$7.ErR:6Pt>J#Og"l^<U1N.uQJ%c?>=</Q>=.#=&
piPq*8nU7T0\+gPQW3r-ERPM99Wea%kkU8EWmYuT3Zro<R&c)aOIV#MCe1_/$j*lMmWY]A`S%
Y1aK&Z9XTR46\Bl)AjArXZ16mm@MK^0fjIaH.rlB5GYW+dtG/Ns.IiYU#ZoDjqh:2N4HSY/:
=]APO5N*V!sRNu/RS/9G:Y9^4(0R+;6)4ParA3IIl[\f]AKhC'F9]AeVjZY-]AGL_XlJ;UqunW!q
Le@X_'=%TTJB(0\g]AIoG)8m(5YH;bh2UPPqN1[F`?ihP.\Dgj-TO?Vf]AdQ<q?5M;IaVjtT`a
,oDJYJpEf$rr-GR.OeDLdR>7PDZVAI]AT7P6k3l5rR$L?_54SulR4GL^oCA'&NCH2?"VCtR1?
Fb?C#f$bSB0QMJZD^?A\]AYk&8ef'PHY+L42V(.JODtSLKhX8neOB)[CDIOX/V79@kfX[?<i4
Q*Dq:?p07E,@s'D(KMf&2t^BRmV;Y:$h[gfH\E*^$>8O.@+O[/:I-A!NI!HZmOaZ)rn$ir]AC
O\TehU>7&\N2YgQN*CuB5Vp.kE:g_Co[N<B_B8CV`??49PAC6>f^J:#<"gf2,0"kT*bJlbLo
LbN'>gGOch8shIL4P&G`Whb&Dq\?r(9%=l*:XJr?b=^5ChRSqYc5:cAYi'GY3"d183@\6a#&
K%NSYeX#_RGVaRM31.^Q*Ced"a.,I"n.H`8:F%">'#Q$T1(-O@,'@umiA/rA+0?^sSbM\HiU
6IZ7L2pb<:[_F$YCi!HgXbRr!f;-0c'L6l-W4/eM-%h!_rJeJNFePPiO]AoWVpmZrW^i2Ci+G
?#21G[+XEDAUKmFekIOk6Y-<NCMo'dUC+?[E]AS(<0(Hi5kKH8LO#UnaZ/h)M_u,\H[#*L24>
i/*;c)AKW9I\S'Ia6-P6h5dF&rH;O3rP8Bd'UCi:aZaR"D+02a]A>PNJ`M"YecF!n82mC#FgC
H40q%86GB`o,<`RBk,WSc-Ye/;UWs2NafIA^%@R1*meX0OSoF/+:<c6^;Faq.YXe)Yo+"c_)
P?6*i[9&,a'C35MEF<=BpCbJt`6U9%5G0G"pdDJ15$EC>2V5Dh/HAXa[>\q'\Z($;6EFUVLL
32_h$_SfIT>>g/`/+?^%Q*al4W!tr8K1@\s'0.Ek=-b<^^-QW?njcC_eIbLY-!+gF#cdn!!^
W"TfGde=O8/(uhRD2G@dlV$nNEn^B#jq^VsdTBK9a$%`@?qKPRes<<'GT5@lhBhO+Tl"pU)o
,=W*G>n%1Q)gHiC`ChK[d0`A%c7uR.**U78?Tmq,r2#-tEnr67mnrnmU[ehG/-lFqr,(-gq.
"@1U>H[ql@*dmWJ^U.)lIr5AI-CmBEP4$Lqf),qDcQY=d8=r95OXuc+,0WPSTSs/rapsNS3?
cl;=aqrI(AF>deoqart;")7A[DYci0s904=g\Nqp=$q*4?7A(CVBYl@_QFBV6`692',7K4Mr
s/SbE=.g?lrbmu%:Ah;'s/W*_ko1`m"b=@+Xo6QGqHm5@g@NeTn,M.<nDQA!T>^_1;=jUOq#
C5AY"5^!Of,]AZV4bM,^0m79?[ekOKZ]Ap:,ZZi&Isu51KC\1QoE*5qmm#L/L<P&Fl,n2AXS8?
9\nnSDrmPf/P*`l^o_YgQ`dcim@Tq`t_J+_Q$E"V6/"BX"X`.J&[)-O2s.<@PN#ZS>2>Mm^D
oo9R6TW1BVK?)BYen6,+2lq#5:A<Zq68AUfX0;<fX0;<fX0;<fX0;<fX0;<fX0;<fX0;<fX0
;<fX0;<f`*G+T7fR5V@FSQbGiF>$P%QZZJ=Eb1WW)@5FbOc-\"*n%/fq=@H=fJ[+Mb<[+Mb<
[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<s$F!HYXnYjPJ_ipJBs5D$`8tbs7_Fc&:I/A.G
+"jPQ,^.Fg<IMHLT/Y!r~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="155"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="542" width="375" height="155"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[647700,990600,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBMC"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[ZBID]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx4]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount count="=1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" cs="2" rs="2" s="3">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(GS)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue("");  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gs]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand/>
</C>
<C c="12" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@/B#dTQUjSS;\P1NUda&tREaRG<PP8lTO\Q`&9[b&W'U8'Thp'Ggo+h(`Q>YYlq%bga]A;&W
e=U.T7f5>Yn*n<"r:G6j+R2Z'o`_cQBpZo@NFk?d@UM^A#@Un,'YfHgg-HmQH?$6`s1!.lXV
1;,L23EHZ;&;,Joos'>VpZ@q4HAY>b!F)c3PF!N)JU+c4p:D@MX3pEQ##!ZtN]Am%#deF\duF
Of\5,k&6%bdWfT?\ceWgc;QT`6&99%;Qa$CE'V<ON#@@.tYN]AG&-IU-^S2Rg@BLaMQ`=ooAk
X3(5)^sRlsT+N,rU<Ta$0?:hKmi+>iuM6hkVBO`Y#?+aLg?F^<5CUNj#cLX5:=.#h\S]A9>+5
/N:Bokmg0JT%N:9fotFI4C7P6$0Dg(qCo5AWt0$jiqLERa(=!kZsLRs-I_"Dd*-pkLRLU0##
VCk7gh<(;S1V9\;08.qp'Z?Is<*.s0VK.jC/rX!=!bsC8EqkIAkeH1T;C:h"0[k[^:rW2Y1<
H7dVtf_me**R9glUmc[E"J`TD,UKcNK:\l-?5nhpubKGfEEoHiOIAj[MEhV2J,cSK;J#[N+?
h:5W+*<&>3BB*CjP=9;qa\%=\"3CK73*D<mmF-SlZ0FH@59`iW49?CoAB6T-1e'o]ABCf8s51
66N;@aQru\<u1:3QdGWgmDpQ<*iN_)EHmaIFPPpV\%nut#b#G`_G`th$QOD`J,?G9eJC@dRp
-)>hJ>;`7c-8%/r_T<qC`0H1ar*(<biU2q5#&['#:]A,$'k^c4D:KH5O0D>;*XIg`W@;26!*&
;@&+RS>e^"UiaXg'^I:TtN.<WU8AiCHUf#q&aF\IH"2H7;!:+%A_Q'#OK[_ot(Pi!;L30NQe
)SV_R`3WV\n9joR5@Lt@i[mJB-R]A9FJX-9oD,8PKH)h&\9_rUN;?bWn6/OrKqHdZYaH"iCF>
dJU.4C)b07sdpk\2b\QLaR&5A'i1HcCIeH#-s8YFhuUt&JUULR-fcD6rQ(<`XF6.>n\3]ACdq
l>MRJp%k<.hB.klAlKO<b0BQJl@dIiS7<as$u.WcV'5(h<b\!oN;9o$k9m]At"d)#!$]AK8G??
U!]ADG3CalUSY4a-dMHfb-^;aCP&f#<f5*+I670Lp-itSe(.r6u-*Qp8@nkN>-6CK4H\[^j@M
[\eYUk`<&m@p#FSb@cg>?afa+n"!mTnH5bhWe:E!HWVB&-'THEb3Eg(Oq[]AoPtVeL=*lRQ:/
=g!j]AKWD?TnYic/@\YtL'500?:CJ-qgPR)EA5FnCWU2I_eJnOFMq-\i#^k(`6!LfBJk;9"Qn
a!"K92a)6YG8OI`GR"I$p7c90;+3Sj*]A.aN8;+OAil_?MKuf1M8+O(\u..C=MMMK**$`(:UB
KH\[L.-kI[cNd.Qc1F/?g(YGO$1oQ3\M>j8%%;2SL^bQ.=o*K/qD[Z66kK*FqdQVJ1P3\+oc
V;YV;^1_bmF;P7</^E9f`b;moY76c0E9gYp<@)?96Q\3MrqP?E4Oq)eRs^>+<G..KqML_8lY
r5OT"#jk(joo?0Wpm?$XZsIaO!"$<QE>8n$$@ncp>RW@H>Pq>67Gr<R>0J3F;:ABc1]A3aY\m
$N0g!Od7)<on_\12TBCIjqa3d[/ZjRM8]A(7_URjChXK]AE8N,hmt=56R:m%0$Z3WJ[5iRfEL-
MM/+&b/WJB%\7p[KW`\:iL_fq]A,?9]A8)l4LlG8bjq&cXhX5nC-3Bl1(p#M!o"`SeMEYm%MeO
lC>rQnCO=EL%G3<AgKJ"_[(Qoh$@ZK@==DEJu9Fl;m?8he.KtsliO.!b*#*k+M>>st19W,[S
-klSl7TQ3G(?^e:0N"S4U6nCpf\ui/<emd+1E2$Y[]AGGL91I)?#((sF:#P02mrZNjnT7Pd>'
RN%\'@'T%[-iJJrLE3GZMlV'l8rf*8XI7N=cRO]A%u4?&78&*:51YRf[&EH&_0^Piq3PkJ"(%
rc'R"MR)_IM.[l(+S9fAO[.8VYAq.lB._7t!I<JG2Jg,#JS=er>Ugk0#=85<(%hAW"h.,Ca=
<Z1>Z`A4593^YgmDM*T2&_0n`Sq:tUSU.9\pr<lbbFB6\+i86?<b7pAa78PVmDb#KTV_B\1C
D,D:opVV/S:rUR8s'QA<9=T5!1,QQ!Ha,OiQ$mV5AobF:u$.'#?Q\E)c9O]AT#W7Q(3_OqE9t
Im#l5%a2eV7j&`hdPb73WMO2CVpe7#b=rHAb1PXS)qBDi5;Ff^C'\T))sAJj.o(1&iR)YM&q
15t64WY*7>DZK?RKD`$0F*`H?`+(h?294)5X8,K*,3)_g'Zg-s6PZU;PqLQ9g%$"LM9fe^E/
U3kNGmYs[^-GU\RJo!n+ZRe<)'8!kMi:t_3NG=:[L,Q6gq/^MtKotf9X4[md0?UqEo+V,%<C
&FoMUR^Su^cueqVoO<C(>erST!N79*(+:O_=#UPSu@L)3Vl1+5a)=BRTj^8S^q7+G6N1SRLs
\%D=gqq(P.6ak[XPAll*Gik^d)#1B#R`H?S4o>6LgSQ+=PSHD,)!Cl9V2&%nD"RjZmMH]A6eN
asf3lF-tJcb>u*Cg)V-Nl]AOhp[R*PC;pSJjfS/EA_\)V!#9Cg"*l[7*Cb3;)>1"M@FihZ^LH
4$sZX4<'D#3+hr#Dr5,7-rM,4(T`1Q+>rKBEukca*pZCufG[)Fe4=:A-I]A1E-U:Mom^d)N/E
nf9k1uD=mI?@Vf%3K@_6R'4Za[pgUSW.lKMh(H[l/W!]A*qo=R&miDn4"``f<sOKPEbhql'Ar
H8tE:<%!QHL#8(%<)HijE.:u8(YSr\%'#lH"B,33hVEt5&KK:[^hkV"5rJ2E"pW&e^U(7\hU
aWQeJhr_nsn/,d=^q-?WE55E9A?Q9c%ffP;=(&S2-^K-Pt[HLUq(Ydm=L)eKM^UWh%"6%)o!
lIE%F[=?tG0JDf0f?MBH?mt,p/(6KfXn.s0fNj4$#CIL?U2JO=IeDrk#]AuW).T#`Qb&UES[i
<U=5Sr>FLnmgI0a+`GO:s[c_=Y+rZub]A-c"pc\>,.:<P[P4=`i8P$:J"KLKff:hZPAJB%8Sf
ZJbSaE%Lih^kiCG(@Y]A^prDY"ihNBp9B:^%Pq\T)<?F[/*8q?oWVYc_3_iU'P*BSQc$F80*%
U>Ndc=[U0`,&WB>rjCf8##U<J4.Hp-,Xqk$`S!?9>dAH=gd+2_sB^C2t_&jD=O!QS)%?HH*6
@1!pCK&e`PJ_Dmb""cXsW$B\eg_99"J6qkj+fgJusFXU;(G<I3"aj0IWYHL3:_?SCFQ:\*OQ
h=QA_>/2,%G(^7\RQ>V2f`*tLLA,AB(a&S)`W#s^*i'B,)Oi/S4r>g.kYdXNqYC*@BlC,O(c
K9nAkV'fLHA#p>tNUhZR&Gb72+MVWE;&-W3&n`d:W3>]AeXu6eecLr3B`W`Fa7_*R/Zg&s1CC
m$oauL5?GZ2nM@Fu[:"t7>7Y6cK(3@H/5j/&Yp4-2-p#*p,[7%^n@IX5BJ+sdJq(%#13+1_@
(B0@/#!(^Dk-Ie-87WY%D'Ue69Y?bq6M`+[>tcr,LXAuhV)rkElRQ!0![$SELMn$2[5_u$gn
C;H"E?+oYC.ppmDqXPZZme]A7?';-ru(<?;sij6+J(T9"A*&8-LhR*dr'B@8gmplkdcATYVVK
Zi!ef)!c5\f:_D*k^f(m$G$l[juVaBimk3EYB]A`uo:/D,k+8^d7Jk."KbF9CFbKF*EF78DPW
JfQ=$>k]ADRPPRY$]ANEH=.K7rl+/dhJWA$$Ckc/.18B&31f@0PQN9_rqr/G?*&DIX@(L@3,X:
`$$3,DfDh,ILR(tp.rt*Ji[_k]AID>`:B)M$)dFRPNhXJ<Bg\auj]ACnIh*p)@%4-hmce!Zh&8
@_^SYtK-e,JCb-h21^qF\d;Cf]Ad`uHE$+hSJobT.t./2CiqQD%*A^J)HjTg\Z55Xg1UF?'O$
L(;OBqKA4oL9;L>*\iMh4R\,eBu.P-GIaYD>[)%.Vo:.tQ5nIpt2WWMT\O4i)Hm!:FWZ%m!o
3V.tHgAdu,3#R9^p1(ED4f*k$oF5M+S2+3##]A(=50qOJ9W=H&mFr&5C/S[ql\HRs9R`-_%o?
H2<jUSLjSa7qL[dtHKff3X#J'=:5PhbTdE/h@S4X8EmN,6*TPXF37.:JpT'1a,8OH3E6T'>E
mkogl$79K%t')HOfj=$VmC2&[-^6c%LN^1.I9iLK90Je.e08Da7l=n:GE?n$D.$^3._r^8,9
kg)Q@/Ko.c"=46-=p"WCaM=A^mE(1;S&6.%75I0'MUZ(451>tfVI4"_)bT0m<UI,fYB>K0-W
Gdnek.b/hL]A*:'+@2AH?4B>lF?&APf:u7ua9@4?^(l,<`6*5&lac`fV$Ta2nd)!O@D9s,aO=
'=RUlhb$cRRe]AF)&uoF_1O*31nq_8'<p<:i0Z$gUMVgq8>Di\[ZgO#='$AA/GFd\j)T:L_+[
;lgK7kIr\4/KJi4SQ"]AHamNK]AY!l'<%"/n0J#TG'BQ&l=D?T=(*-A2W+Bmd^;V+eln[U0%C/
'j9rgj8BRe`P2GTP#a["3!2!cQ7l9&g_ZrSd]AbTWm^J"qqK]A1\I&PP:d=hDeW'3ARr^Q)U#(
qKHP<$h5l<%Nb2B'%J3`STD!SQ\]AE$M!jWp^s,n79m2KM+B?eS7fUQ9Z0aj=t0IkOR+ipUGd
J%$d?%3@VON1"a+Rhe.p!pY>oO[1=DA(TO+5*c-hE\mlHl3\s5fg=\[\N#a#7f/M&-1iqkLC
6:'BK_Is$i9:?TPCW5r(%L89g8/P^N>rF5_rH:@iO;HJIM+ePHd`Rom'=q_to]AMia7e0-%`^
:JfofW"FocT'I_ABt*V7RlK+S6;D;2483+OH>-c8>(]A\c1kh@T1tr`!]A'BX)dQd0@AR>nq<h
D]A'sldQ"l]Ac`d\iFj?uccocHoai!%*H6<sjq9tcR.)s0!I;':H*,+(r3/h`\=j8?D!\e)e%b
5JnaF]AuDs)+JeNIr'&"\Z':fYW/]AJ!fjBB.?VGP'hi-FkXEGW\J-i?@4#HZnK;<PRCa>0Rhl
eJMlU=ZE?=?oFT^7/pOb=_IbKU1'uKFMo9W)>>LDKe\6HVm=o7_U\b;4Cq$;\fFJ9sd_.+,T
X$'u0g`VfG*%;>fa1_m^/24QA6c41b-5>h9&=WFu=\tHJU*97Q]AD:7G;aMA5Dd7.CohA\1=j
X7qS#R._0U0jYS/nfLb5'ge(#ocs)M2.eKhW"fI2;rni6ds:9G9'$j#Y\J#C:io%O]A^p2aT.
/flM0eoc!Prdk'fG[ii+@>qGS!K*7^dcS64h%$hVVN+mNqN+VA&cek!7ei@Qe05p43jlm.c`
ABqhqK4"KX_h#bF)[!'dh4RKWqeCj/%gk*Qd5>+=_lh>g;XlR3L\8l)DD@+:p.&4dp6=c7oA
Y*cKHBqr94*4ejO[7.%1;l,KTPYUa"pbC"jmg6l(+fQ#Am/A$>.oF0)E-+eOuBFP_TC;nnCl
FHQ/C%jtA[aK.5]A9bG6,DjI2d/)j;p8<"N(Ji/#lht`SJ3j.@nfP`bW[V!M-G>IFgqH;V<q@
Ss`Pl5uiOku%9)T%)4<KY9(MJZrSG-d>l8;Z:m`EY$0pd.!Qer\=H<.gDoHHsoO]A9X&q9fX>
8m)UE?(>%_S,HX3*b(\IFfHf@gLa6D@NGeB>GeSE'VkQ2/*dbnI04Y$XXJtB!E0t[U7gP$*9
gUii!JK\-+c#X*`Zi&MrB\bblbo2Qr*MWkhFjGbZ9S=)O[<[hN8-OZr+Qspi%Vn<A]A&HnA"M
lPDkU7$<3ln?PE@Z9aBq-1S:Wfp%PgF;VYIB)<VO^_[cWkXM0J6HH&TV1SIpM-NHpUp4F?9Y
$(`GnMNu48j@0p6rQ@a2=\Me,A@FSeI<iGLS`(N"-&gRnkk_/\\:+3,<X\;AjO``6Vs5-5MM
6X[)UY=KT49@\CDkoa&S#?X[->$9K2OhaYK>@d*);]ASaS-F^KOi;;V;<a,1&"-#g6'c=q^cj
q-Xuhr2d+$TBga_kQ8^G:/&M0[AGqmH9/o+dXs]AgY.,Z(k3rDB&S%$?g-bcdBi+F(1f'_K6j
7AVb.Q7%D>9<[V3n*pF'<qlCOrQ_W.q5FU<+WueQhUGq+6_fJ;hNdnYipV*A5uDWKhpOh!H*
g>ostduUWG-[oplLs^X7b,m%Ym'VOb/RUL69UE5%7ZDOM'pH'8iG`:VfMHeH*X>L@P^G8HUJ
8aPbVXGPj\]ADTB3bG3.$Z7FS6nJmlPH3k8\dBo:i4J(bPQ+n@EfDa<K($DD<TZW7j,Y$mN[(
hbIQ/*CG\ouJ1C#;qt>.V[NH+T:SWT7gpJq+sd#RCK`J05s?#QfU.+<Upq!ePs\s%gIh$@g'
a(%6X#,K!3Xd_)CK3+JqAOIqj.9eLpP4`t"E$GZWM^]A4eY!"?XC%);iO^]A4eY!"?XC%);iO_
#>i=6=@Yhn"25UBqfB7:Z)2Q;5MH]Al%[XO\#&E1[&(sePD2_3PlM"r!!Zg2#%.E8?iUC=!!Z
g2#%.E8?iUC=!;><(?0nQCqr(=rHPCG`1P_1O!nXFP#%.E8?iUC=!!Zg2#%.E8?iUC=!!Zj2
&[l/lj0/5*s%(f:rlJ`DX$bRD-SPK-~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="348" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[565265,1562100,565265,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5932967,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="AREA_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[A4= $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("zbsx2").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="12">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&;s;th'JBs"3a+DS"^6s=T;8-+t\.%<Y:Erh2Q.'"$a'F6)m#U+b6"]A7!P[K;Vb7$(^J,p
Z>"U4>WWOYmH$+XB;S+X&C>*[p"CAt4.qJ,=\_alIfpI.$s(S=FYLG><%m.=3#1[;'Il'GQe
uCl^V,-n#i^]AtaDadF)]A@ZpR-qBpILOb?lmZ*N)?_Ei45GVR12e^28g7F&"mFkk)<!c$rlu<
H8$;9(K0('L#a%Hpc/!c-Dkd-\%Ff=$(dVE;V)K5BKnSE8>Nkfe2SUF0,(d5bf*iT(,++]Ai[
AL5pe`WcX9k?SFo9b4rJ3#s-uT0ZPob7g-%U*6X<u&>Zu'F)@ULqk$j.=/%g?LRHZb@V3;BE
V(q`"WO(51hcjTUdVVe:Z;pb:>c0]Ar:AH#Bs-EJYj'07=da2WB*q`ku@!>a,0;3@7s4:&7>q
G,fMe^2MW"aXQ_2soWD3ETXq1rXqO"(N.oUjN9VB6t^W]A"+5\6ZJIF,6)>*F$89@#S,gS$^n
f;8sG7G!(ER^o-NZZcc+X#03%,QJX?:[BfWl1X,:IQ.L/D\E)NHl".b-a&O4_8i@Y27=,E!g
t*NOa'-Ig0(i]AYQIJb'Y`>q;9\;Gh4g8Pi9S)0T1bWJ1?:C'`C!++6nKl;>H!H^[Y=\Q:rUo
G5<kX'L03chCY#CnoZ+S14@N[Gci5f8KLs0CT'W[,7:>lJ6rO%7BR5On7%#nR^eiXdi5'$4c
BBiM5Z-e"GUTTnKR)D;Fs4R]ApV>S>,OPP':NY.kd%r)hG=kOBQ31o>6;;4coYidqJ_(j0TMS
JH>YsC/siruqH>a\^7,4N3a'@-76J/;t%1d+q*b^>q,d%9'\&:Y+(jd_M_$;i8uVNlLld[hG
89A/Z><mW8ZW!FSW/j)kTkR`10=&`'DRpc"t3ZqU&7AV,=H>=lH(ugqhAgjMu+5"BS'+1^ch
Jrhc=_L>;@IB+1&^GieSj#l)Zp\>9ao9O.X?2F5<tD.be0@q<>f/LMj-lk0M1D;fe`W9CLBm
RCNil;S?f8pd+t^=?VVPZ)?+G.bWkd3a)VGptm2DLNT_rVi?5=dE242RUp$KdQ&FsZM>pjDd
E`)%lTn+X*k#?:G/W,j)ZeYp.c!g3JMh4l6,a7mT1@]A"]Ac<5^L[&+l&AVl62Kegdn)e14M4Q
"BW;U]ArjF]A;hYKVX<r5+,-f5a?1;-kgg!g'A'k#Wbn)!hUKN84H8L"ok%Uj20_W)n*ATrTN*
"N6j8>\eOBt5:fUkdE8gZ?Ps_@etUNb,9:?W9u^%g52nr.*.A)Pd+sJ9XKb#(ci'q[kl8h/+
]Af9,D(ZG[ehH%!C?)`BbGus[#H.6-__iL0%_R58kJB*9`-;q>`+lLra)riC_rW^[*4BhZYqR
VqQk^OgK.aWQ\Lg)K]A$e'7F/5Yc`$7]Ah7"s-Q2FulrPmG*tf]A&f4e0>HfIdX[Q4$]A@kP;9I[
PNQa?0D..Vb!e(8-.r0T?G1krh=@_$k>[Z`^L#I0""l$Z,=3NjcM]AFf;(`>[3q)$VmB*cb=]A
%2lTm";0SBGd!NS7:.1Vqdl@IUP\a:IhFnXY828oWfqLWlXo:jXqXlbs\dft*HA9Aad$*"I8
Fd%doAr;.&ugCt]A8Q616jb(+lkXi$ANk"@]Aic=nh<Ue_qumN/NZ4KWjMVI?6cFc%h5UFTS#^
:RDD"slMTf*RM*A'.Oq*u$icKA<EE*3!8&mk\sI(]A8Y(4/p&-FlU$+[sZUI$coIa4o%XDcs2
l_i\P=\#3iRNX1\gD-NIL'JQ")jM,A\;k!1?*#c3@p:P_BYdio%pfC:h54.UO!ak!d[\t-Jk
UO%ZJ#*4X`T+f1;,(#rWBIO[0e8Db`E4;!f;KP%8^[<:e,V*!+&q.;%BceL7ODHFX_&6Z*r!
pUTI_G=&9aoIi'6XY^fZ#AO[G*fYB]A2JEkZ6r7cBPhM@k8lWqkIepTqeg(_Y/#"1CW*&DVXE
AX3?ds+WJ#+---k(`NBr7QBgsGGoi1n2oB`t.M\R@1$-u3a0SHT3G.>U``Zpn_&a7r<L5k4*
eVZ[Q+Y&7j-&?'<b%;\IcA<-?@G;3eQ,J4Z<$htjHCS@8BhO'IqLZQGLsDF5WMgfI;If+5*%
ETF;4,21#a`2RpLSP,qNAu7sJ*4+eio#PTlhW=fkFfr>lN@$kg#mTSq>nIgn!hkRtAi,"io3
qC=.P`l7>b-W6ng]Ab%Y%'_@C1p;JF\HaY:<-[W/Vmo9jIZ:Xan_!@__\S?dp3hc28qPd+6YA
Eas<QJXO#1'oLie01dD<7S\h>*!]Ab?IN=!t7gmLs4bHJmllc@QO*ajLVUH32QWO*ch]Ap(j_r
?Mb<YrW(A6f'fU++P+<+@ndD3tV\_.HigNkO=tHenM!VjB]A87E5I2Q\jM/=g,5qNrd8(dHjS
P,e%@ADTsUhYJL@V,PU^s1J+f5i4_:%?%pKFkjUZauXBiC=^3D_\OZL9!.@T&7*!bdg>*'ni
4C;"!0Ug5ZUcAOdS1O>l6Oc^fi#/]AN[k;HI]AhH[#T4*M-Sdq8q)tW:Yr^ZpiFk:$_M(MJWq-
;]A6[p]Atn$4GZCJX;M,n#@pk=29U*'5%AcBhPI,/SOrY$5(J>"T6:616fY9?N2plprQ"MX6YH
4NWmuI(>3,d=ddP_6!)>>0+dX"$Xe1KTK%rppf;C^,A]ASY=\,g.f%>9g"F0TZ\,!N"JQB0<V
e"P4+EjY=?K=7V&iPB&_1?>&kIA-!;tq0dWo6+;JZ^c(7BBq*JA=ku3Q'E[S@@1QV5n>,I@)
@Zn6j:6Ttp!,mMWiu=3&j8`kFn]A^3YGhU.l)f7;ajV3#<7I-r0O&@Z;)mE7^'*1I=)FPaO?\
Hbo##bbhW"8_2PHRJs5T<6\MhG?K$iPBh7rHkT(0uAqklO%6=8hV6J3)K_C=)EIGIn1Y(+ON
m5YSp%=mZ<S!&N:BKOr>=\WBricGF?@W(ZAN(!VVOQpVg5lW69:@E\!Ykh'kr-+F'c\>K(gR
=/_&o*PS!m(X&asjZi94F-36.+Jr`Nk)XZ6@LJIjV88-\8SUe:-n)\Q+15kAE0B9I<8q#N@)
5=.Ep4)SVctJ=L_3c;'\4MHA\o\j]A6(/\AM[\Ca`eLKb<I0[m"2_T6!J7/qPS8dST-Z>@dPb
-p><%"h^O^KNl?b:hTecTpugp4n_'m.Yg@!G*$E@4YY*(Y?270.1RM$[Udr(&k/@6]A.af=I<
Wh*?74i=1MNZ/qpbu9CVk_lW4h=(-"o>4Q)(m-ur9U>:A#EQ\8i+g<PKr?R6ID9W-WP^f[ID
0:-^T,YhL[=kS1MP(+.jFWD7NRn@RdgbE,sIMBS]A=LsBmMqA2/'icr57L6[qQ'D]AVUl"+/(=
Srd<\%rr:]A%!dkN:5+a!964B:>08rJB8*\0V%87e>;3InD:WWoV%MJVQEEY7JY\@l>5/=lsM
^'m8T8l[8P,.f[s;]A<pIbHFuH62f>FlU'4hSbBC2[jS?O-E1_N93/7n@jP;W4<XJB$QdVbmD
t!srk2W\IA#2]AVe9BJ/!<j065QM$&:d>$b'Mq$q"k5bD8`S&iGJ9\H#\;K^-baP+qH(aUH1:
=U>!?qNPdEQ>:^Gje4F$oD^]A4QmJ,p'd!!=(R!!Y--!=W?9!uSfRp(,*5Tf"_[afji<I9UoD
Y-\%*;5/HFGYUp2#ZQlPnrlWP7XqE=$t'&;(r-+U0Rs04?iU9G^]A4QmJ,p'd!;d"1rab+J9:
oQ^@OBZCrd>.kWr`]A;!!=(R!!Y--!=W?9!uSfRpA]Aj6H6@XLEQT9(]AQ@[sAUh)Zn@:(L"o~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="1" width="186" height="347"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1562100,381000,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5039832,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AREA_ID]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("zbsx4").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFmV&;HX&+E2oJ_ZB!\(=f1MdZqHl;3LX*HjGuY)Tr+*P!i=@F)R98a<'Y4]A2H))9c=EB$Q'
8sT.KpM$Vu4AiQ=Q^h$=o+15X6V@6Y%t^g\h6Jm$VoTLVoJU54>%hhn/WrGOE#.r-_EmQ<>b
k5ThS)]AY+@hO<p_Zh`idn:f?K:C]Af4keG4@"+,\A`&`J2C,VJ#73ANN)c*Im#J=b'?'tu0FJ
`uI=3jSW<Hin/hCWP?S]Ad^Ee<@_oo/[#4t2Z=b^&oO-3Y.O+W<Z`kqc2@Rf6AdV(MUfC.Y\_
df-q]A?20]AsD<QTZQc`E#Ao)NgV#C<4L5jV[^+=8(^VZ`pZih"gZ3k8&HO_uqjm)2K\4(M);P
pZ8@.&Us69M\\ECaH01?4\8iA]AjfRZZkbq62$XLD0j^!K-&'&e.)F;tDmB9e8sWYb=gP,M_8
d#JGN<hO36OAo'Q72&fLPm^"@4KS]ADbsa##s"0f`G=35e`n:DcEFbIiZ#N7[(&fA\4qdf%C]A
9ig[XmU9F4&kK#/Ena/N`Q170ZNJ?L^n>P!P2Y$iV!C'beDhi%gX*tW(m^"E.OsW_:)pE(Y:
M!$h>9r?/lJnS,-RUY2UV2:h+Z[qb0K6"@ZHogB_Sf*W92I#NrFeQ]AhE"B+Xhlsf%s?[)gLf
n4<pGjWX*2D?\@U2b\-1LXHE&49nf/urph.?!d[k$Y-JA:pr0$N>cs=:#b+:4IQqGK-R,,(o
OPrn_9[=3c,kUu1ooJqBS5]A,.g^6e;_-_:t!C<b#+ZHh8R:f%qe-C\56ue=C*E(q:(!.DuiA
VsE7is<KU=8Z-aes'tUc`2.YY-D1biF<nHH[7MU./1R97u.s38)"8dL:sa^]A-50I:obN7/MT
@Wj%G[WK'ff@OC:-aW0s?$2%Gs=<J0Z09:s/F?&-KmLoK<gYo>u'ktZdG&^%;93J)L805:Cp
KroO\/oY:]A`qq>9['uq78@#s9sS1Gmu0ZS%6Y(k)mcl7C_+PY<8r4+Yd/_!0h!V0DJmQe-2&
\M'Nk5JPP6\"b-TPQ10cQuLn9@RGGBQuc1*2/Qbfl=KhsJ!9UQ"/eCdpQeZ0uDL>=I)"u&B2
ZJk;alCJ<(m5]AUB>$.qT^(4$r3U_'7LPh8_TPLR2Pn.k7K[B&reOk!4BVX1)@54M+qRc\3BU
AE24Hq^$F,b3a8'LG:l45PW<iUe1X`h^Fb+%9on<MM9H]Ar%I98DEjL6'+OfIDVpVC$!Z#WbF
W,-2I74fW+LLBZCEa4`;4)KJi[".]A0N/<t8!+n$LC;A:+&Hs4<u;3tOsnh4lLgpW5mI=]A/E9
r/]AQT@!5@Os\8)!g7TP6b7cXE\R:O:fC#Rd>YDpdY3Rc1/3OnNTR/L_39(jBY1uF=BCM4CT-
L6U\=&BmO/[BcsI[rP9pm=&P),c1'`@Fq_1aE9KLLG*QIbFcqPp%>=6,_V*RY5E,<q?:Z6Sg
7]AKHn*S%^%QD&Ba[Y6Y6NFSO6HZR9a`)\J<>^@Pkhn@j#^sEb[N_l<0:eeZPO#G2;k1&6EJ@
:Y<![ju<n@C^/Z@g'a.(u@SWJR5_9o;@Y8$$*6!)baRBl,@DKr&t\$XpY^HXhoq??JINn*B:
4AgKIL]A'>^B;r5*j*VYXoUQ+oj/Z.kj,U&D\F*t%jf!tu(`5(R?fBbhf3X*R[V#?)hGjWR>X
Eg<.LJ+ecEYPoudtsI_RXl9qlfI2YG9W95JMlqk)"eQ11%cM!7oQNnCMfVIQ2^?o2Z,Cl!7U
n$F*)N"De^0hr&R7\.48'L`'%Q/Pf->)Lh_d$_@X4lSDq(3E.&P^C?)A25YdS5>AYZA:5C5p
:1#]AbY6fEb;3#>\j/PG*%<ccd%J<K,@jT^lf9>LbCo`2P%bG+,Vod1q5uVu[PFd*]AS^NYpT#
_RFSaKIbef:R?@G+]AjCJA'tH61V:Q4^RFGTi\'!f4+VY9,LOj\a>1ekpqEq-dt)o+u_lk($'
jq>6L[jL6Y!gF`f(fE<aoh*h%D1hX;&L/f1dR\?Du7n'Ji:0(De'UBgWnMGH*E[W`LX"tqDJ
n@bZi+g!KkjBNh_-jL2Qh_Iin%#np*IKZBSI),:jWDM&LA[F\8^X.iA`*&D<YW]AKA=B:R3j>
=OhR=HE00e$84PpX9:l=j3K"N%<->cVpSm&d>r`iJ%.9pW=Q45ba*OtCD54hKqBW?7UJHM/-
7b5M#FJ?F`Q,JqM9H%ZWZjP?T&o5&/Ga%J&V[:LKGffT9HEC:.Gnc!V<U(Wp^o.)WIoX1]A$t
>io=+qrZ>rK$[mX"*j+A,a%cW`07IEVQU@CK79WO3]A@%%06.&Y@@!2p6%Q*PSd;;:?7j-u!5
fAgX7sQoF*q:".SHB7nsPkP&f$/B*r*'+8(+743A55;b>W)FgapGnu6nlDNC"CGfoV'P_SAD
'p<mImeQcH&RDp3V\<0e/fsj3Hu`j;-$_`UD8]A$De^oeSF4$70p?e6kn9Iu`Xk@'D=q%1Dl!
j<U"hQ6Kp<D7%[ul,-LjBCL@<pUL6<q(YF.L;KsjbeFF-b\M<cIcX6OF[?b8?[W_6:H,bK2p
]A)`sALl:_1iVZ_L#Lpf\(I::O+7s$5WkhNb>b-7`d,C4oWNaV%U]A9kO*Ga58+q(L8lfLE$g_
=/Ej^_>X8gr0'rqB(*DV7c"G\F8*G8cl\Br^OgqrVf/XrmApGL\YcLVG:D:Ot\AH5LmJmi,2
[FWj+FkMAAia*m7CO=$&S!XU-65QV6;O?`jR,`k7m#pbW3+4;fs(R:R"B9Kk-X*a2Um#1b/T
?ru8F&PepofdF15el[]A1fXYq@"87^(gqjp"rL`u!<sBKJ,oik@"A1ffrgI(J"'TNG.u>=TH(
GOs)Ic%L%j@@1PkYQ\b0Uu1HmB83sM6[klqu4J,oik@"87^(gqjp"rL`u!<sBKJGm;diP`g&
RGV;r$psBJQ,*jDZmoI6!<sBKJ,oik@"87^(gqmm:YAX<mk]A$O[Os^"o?V'YY?-FuZ1%Z$~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="187" y="1" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="2"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,826935,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,1179870,2566219,3238500,2226365,2226365,2226365,2226365,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="3810000"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" cs="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF($level = 1,"一级分公司","营业部名称")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="3" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF($level = 1,"分公司类型","营业部类型")]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[指标值]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O>
<![CDATA[]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="BRANCH_NO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',$$$)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-10373889" hor="0" ver="2"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="FGS_TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="指标值"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',format($$$,"#,##0.0"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="3" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=count(C3{LEN(C3)>0})]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[]AY&Ad'Pc5Y/DVVK1l-6t,MaJo1dcK]AO>:p6#YI$"+?=P]AcjBj&6^Mm(desWqF._VXcZAN$^$
GRHqu,KjjcY$t]A7,,,Y1\/r?`GX7@$4q^HLqJ58Q#33UIWDPLHk"$n:)=UpBu6<kWOW_XHYU
sF"[p%PsfEP:2Bmcm/B>8VaLj=kDuf#ZO4()V*u*SQ0DtS/("_<-=f<8,9S,%F!'>+IB4=:d
s^##G\hEXA3Frg5Eq`$s6dPq:gZ.ojICEYXj26mCoM]AkXD+FQW)P\Onai@#b'%?<g+[ngFB\
P?dCXf1ps7u]A_%6f^cg#s_6ICFH+u"Ee=lYDU>,C/*4j&]A,BCs'Fo.o)$F[G=@YtPj_'\0n:
EGY%-g]A73:jhh@MKR+$0AWM'(ImTH96t!lHkd8+?UsLhhoDX00Qet-0'DB:(a:j]A0mj:=F_U
=93DanmmRs_SO?U$D72W,rD7tQss5/pcHhCQ=;LNp^4)CA"N\FA*9P:4;@Q)2(3FJRou>-!-
VCakXHkhWq*UP.j`&!oRJq[M%`"$\jf?i&rJ$"uk\_0\BA6!0qZjM)9RLR9dt^V;s9O/+!R4
U.:Z'Zip@d=(geT@2:V)EgA0RO3qce9MD[#]AC4MO)nVE')iY#qf%K.Ln1COBpH()l#2RH)1G
efQ638+.&,Z.4gZ9UJ!>nH;BH=eNA?&g.m>j+I1Z/-@R$a,nEKF%h!spnLLU4s3)iNdWCqmk
a=QA\_r-=K@:QeQ\A+EH-t-F8DRos]A&g,@u4WHqPG*i.M+q3B'V(!2VDX^*)dr-VrQ=D?%hM
=DO)t(UK#g'ir;5YBdirAJ&rnk"EVB^[1-%6)Dk]Aat\`VgBMn=.5rk';bqf7/UC8.I>)o^@2
((22B0qH,eYj<OL<7Y^Bfeau"Ndbb2&!iH5_ORf]AA4Mce68=5G,UZ:T(jg'jE'Kg)o4r@9".
.).i,4j^!!&:t^)#i)F1gK?HD7W]A,8d%S4LuDHn$iK8I$jaan?D>m*s8*$<?&K:XYBOFN#IJ
8Zr`k9W^QR"C1"9+l,nJ:N=e6^/#d@!Z[d8VSk:uj,G]A-HDhcEH?n@,(?YJL8#6o)Gpd[>73
ZmoC[/0h)h.0>A,>l4MD`/K.F*K\uXW%:LXP1'e(Ib4tQcc7=@:[:q1F8\LL_R3J+]A_*#ApJ
_S>aj1ah?\%/8Q)o\nnDBFlALKp_A-gV")ZmLr,>HH`Z+,*/n`RV>_;/mp$q;Ll^^8]AglHo"
GXqBm=Va-E9oZXAYWRidFIc2ZW"2IuTU?)"&=mF;H*\e?'$c7AGhgX$4!@]ACU#u"#sK0'U2F
o.2qIei-SX$Pt4HgfLjRc,ad]A9UI-W6nE>0d9pc>FWikoju*m[YQO!&XV6X_)K_+VTR548Dd
QZ`kEFms4rZd;;`mQ^1nS44"Hft8lR"M7'O-1g0]A0B,8?PYQR9oJn]A!]A*N$"<dm=iO#;XZ\j
^eJdQISFjXpOUuEBRe=qo[RsCS(PlD)&=^:$dI'pS!m>KXG2U^*0mr3I([[rc@EdX(+g[UKs
@@t`u`\a7V5(!a4A8r^VYL,.SWio]AD>Ve47LHq;bp37n]AoISb+c3/Y0_?h3f2l;Eg$t<#*.G
)A=5^WK*KZ0k1t7U=2E]A:d>?1L@QHk$[fDQ9s%*gX:?64OVqG85A!b2RB>FDVrog*;9([XS,
CHfIG!(2NM#HLgF"2l1?n2t0Vch?GmFY&$Uo`;\ZBmgNUfi,g^ENiQJbWTq6A:LcoI"\kifF
NZ[G5,bllQZ^@@[B-V(.\5Uh/]A7BhKP<C@.XDK-F-Y#KoW>XV(@Z=l=Qtqd(Ah%@s3;s.%`:
Z=WRii\$(A3!3*aP?r2DHVVPjSoj+kW>7K@Vu7"N;P_&_!0/ObBjURWVSB^g.'IgN/NM&rUi
*uZ>G?MCQgcX:[`@)@rSjdD0r0i06HmR]Al?^9YN%q4_*JSCsLfFoXPI&;PU7PT.Pm1#Cd`lr
W]Aedn1^C?a]AO^gH+ru8H.PGh@[@0E@ef*N18al6#ag&iSKEY1.WmOFssX"']A=)l*AbpkBg@;
E+!pS4>B)-IK[O'pG=qP1>lQ'&03%7Kkf-lhh7QJFbb(h:#Q@;4d7Sh%Q'#^Ki995QmX6k_^
\`0t[U&2oK-VUJR2B1@fT*o,kdY5Ft/(PFa!d5hr>A^W(RY>BEdgSLs&;HW(NE:mIA6d('m;
]AI@"@?U\Qa=Ye[*S'eLfE]A@q6-Apo-8^%DK@K([.KkpeOF1r3Sa8\Y%0,NTao#Zt4N_tHgA4
Jhs,DhaLqH-]AY,(:S"2;b!pqm(iBkJN,Wg/K1Y4W!3g]A_It^NUU26UC!4W)5#3HZ*I*llR1F
AlBeYb!LLRFY>/3aR]AM!DS^(A*!DpS3/XfWBFWok%q>[9K'0,oD2.Zf^l:lW0T[TuclhR:Qa
E7I-HX?%,'-enqB)<(MLe5<BcVq9mcc=T=^%TSH.*<n=;mUVI/X>*,=tec=<]A!r\:pKH5PqO
Sr%s`K_PlHd1=@Y?Fi.R<bk@RG?!ZT3eRQgUS_*HmHU1U#P96O:$g;M=f/Is#.XEMR'Gb=u8
@O:CbfiA%0]Aa$\M"j$/h=iBcMi]A>AD$/gYg:?SsY;]A#/5/=9K:1Y)NYOc<_Ecn2/k-:3psJF
&3mF%N/A,_SU<;#=hF)WdF]A9N%ke3o\6Uin9hpL\YKki2A2_NW4G(.C<5sl.nol&JPT1R06X
7JCL*V>)WD",f8m.T;'*5qV6-7Gt2++OV;j1R>U"ll;Gui6[*nDF)T%G)0,o7poa"<d^'e5d
"YUN*6-;c(f1A#;cN)^3QIGB7NW8U)!/[&q3&kEaZ*qi+@trY]A4(Xk,7Yi"@*O#@9/!dQH!e
>^18guG7\?MDcM"4]A>)A.ZM-hcu5]ALafqDZuU3uF\X$-Y/#k%G1U\I>2$aHm6&?1!Zq\Jhnj
,70:fYo3U_k[k)[oaW749$`l1a,A)o@hSP*:BMM_QK5C[A"()SBDqJ?gT29S"mS\F;hSbZ[/
Ug/fm!FSHIa(\R,]ARR.3*H=mK:M]A?B:tt3-%cKlo.gH=&4'"4@dSB4`9_`E-053]AXSuZ*9V(
]A.[BN&K8olI+npsQS*3/`*.hd[_/,m(rp!hp'tSo\@oC#6qj!nBVc@s<5,ZGdn00RTIs@Fnc
\H:9KX\_qZX[Jo_Vf\^bFZjgi0gndA;N7i=-P0)C(K#TG[t47^/3HHm^I%Uh:\m[Ub61YbY]A
sF1=XXR+gF4.HgZ3b@dH*Q5U@KOple%WQQ#\>s&fn]ACG>)tp%@u8ht<*G.KklKSF5n@Hr$g-
M=?YGml7h?l'Ub^EI<.=d31AEXJn'!0^."M=D-#gR;^_sO<8^!e%;J#p,iW7/gGZ0J*9:r$h
-jt)@F(3CWh+%+0ZHJDa.t)Vcr=0d!D'3n=?0h>h*Fhde5O"0k9p,<Z$N>FN:=V4s93?+C`!
CW9,pm)NKE1`"G#C2bb;u`(SsQ<C3E4%'OeHC'A\Wi$e&:R+8G%bNYI=%m=fD><EMAIL>\So
rq!PO2)p"+^iO9f2t,+NM)^4g^P_]A3g'9B.%T;s9/Sb)QIGro@.L\bX=mmhHl#NCm8.1`clD
#1&Up=M\!X2=#NtK0np[''*I-TpJ&)#VCCoS0kL^`k2g;/P)V]Aj^g0T,DO1gS,ms_&5Xg7.l
i^DNSA!"9FY18"`3rW:STFkV?B8&(1jjPNbo,Wj!;doD6V!X$J6-4s^E0OrIbNn"Q"mY]A?GC
)1lPLg:-mn`oAMKHg>%pJDmoY;=m^I(&5+-BfAB?NV"9>mmoYiQo^YQ=ud,\HJKOd@I2Q*gS
1F$njH:'K</^JhE%%Unutir^Me,rbnI)-;<b]A`5D*]AV*6MLLlDtEIGaY-GH?!g<jnQ?/#:&s
6W`kb+cqNUTJQ=USYO^[G-OSi&(&:*&6u.&VD(hD"UbDoBF(X[J1]A@-%c[Va`HFcb`jTlcWR
mj2F*[$8q4c-"h0=I5MH'cjE<GF2AhV-"HI#N1MT-k_fUPJl&)5'aNE<&=a6WDbmg6"@f>A;
q;U#;Y+]ALiGsYB`0/(I'Gq..l0MdM?e?7pUfPHrYWP@,p3Jg-QF:K,=Kk$X[A1^K8oc6T6*Y
FI=F_drC^i./X.J;]ART.5;`b`T95Q8!cFW68!T]A:11^"<;'h=orHI>jXNK;3\feUe:!UMsW,
$p`Ff%B!<q*O%;!GAG^es4rY8#oN"'20KbSBO&(*2GR9`b*B7H&DFIo0&dc2p:.T]A6%b`QY@
,/.4<;eL[Ba@er`WkY'5areplg1g,db$2>bF.eDSa0uJHeR;u"<2QTXR>Rj=00.p%J>!)[br
mG$9<6uI'(#m)6B<FQt)2F%;Qcm%W"+U`Mg0&,1gUZl5*($fGs.3<hT/QIe2uZ=jg1D_=,pZ
:mCfKJN'Y8c+?pnbFd&IDpM"Gk,A!,n]A[WM(mB^>[D#'>gbMZgSO=6Doi)aLXq.11l&'Ft1W
,2^pL"uK<Tj>nM<ia;D/NW(\'Ku2<Mt\3=0%!BALt*!N]A7NbU_^)4;Y\'W2U0:15)Yd'n1+J
#MsP"=:q3.$SEYQ.I:YS#\]AQA#gS9"JW.N#J*]AZh:Q&epVHt(^:;B!GD<GhRN$[P*PIT/rD8
5u"b*o8\/7*oLT@X?/IBhL!I]Aim76%@5Km/C(DT^b6jFQ_=.63!R\0n?Ak^7Ke/,0/:Fa__>
m:(Mrr4HDcS!%[q46ZCM5H;ht\uFZN9^B:C(C35A>(gKIVA([8L2_X%/TS>)4jbG=a,j:"St
p/l'+loB4/WosP>I$663KslkaFt`I#N^,FC<>T^lPVA[m9]A8XgVQYJQ:O3r06M[]Agc"I6lN/
Cm.W>[.M.RkFYW1N=4*,3f78a&!?NtbCmfOUOT)!,ta'</V95sq0*8Uu>ErlD;FY4,#7AqSV
:c@2Xq[sA<*_VofFAS'hC7o5kg:A0:d,=I0XoX^62%.EH@?Y5is$"/mmPj6P[`I4a29mg,+)
R=JMSLPbU6n5P[:"EjKekU"f9Y[I,C)U-\Z#D=8U4)6a81qo_\IlMhP)C'dXZ3-I7ju<j`Gh
pF37;4C7:Ck5F0na`Cioqlf=[;^qRF9SP;*g.86np1+m"*K%[Vd%mk]At/XR@uV1N)W?V$=DG
W!P)Apr`kHX>"5sjNrO6IbpYhNZDR/=Olfeq-Bp5K=b)DQ@AMR[R0-'2T&EV)i@%_.W3taMX
P28H_W(L6hahAk[NNlM-74%q^F@]A;F\q>=#;/,&7?7oVA\p4`9lDhi7TnV33u'@`/@nWI@HE
q)N/&C,:U'(k.;TMI?"W8::jQ)oM#k5jZF61hi^A4[R4kpR%EnGQ7T!Wa>sp?$V*:Yq13YJq
hQTJK$u^Fh1@R!:AnRW&5T8Hp1gVq=MW3Pjprr3/Okmr$U.2nraH6;X[1j]A@#0%\J4FW[NLE
8@m&_r98p/:GD5SlcB6uYtd.ViqR%XZ#nY&DQ((FPQ\co$T'I.G3RE#;mn9jW[a\,pI]A/;MD
C]AI/$=dPS\Q]AEBG*riX_W'V5,)pJE]AbV<Pk#M"<fJr^_9_^B<l>&</m[O>fRgRi"XX3dt#*/
h1<URr?r,Ng#FqM^EpmuT(cGD,_;dT%+E6*Agqp*$6rMf%'-gnGrVi7rniWqc0mRPKLKe%DR
dPR*UJ^ST=KOB;49M"`*=G!BJqH;FB`O8jh^3[Yhg5STIp`P'CH:Np;McOAu\]As#!+*8h:Tq
*?s*!!)^!h^+m#i(C+:4g%,/nGUHZB6nu)Q0KnP9dN^Ulka6#=Ejm!<?cj$H7-m4MsJ8!8`M
si2@)gGJ#?[Blo6d,)pkj@aCD8ZX#kHFd>1FLoYjoa;onB'R`cI`]AN3th/4ijG<!c8u4KSt`
S$CFmN:r1_e*JhXnh;A3#?%t:OsRt6I;n"Mm$I88_3A[(j0JZ>/l;(s<mJ'6AY*]A-&YK]AWPu
ogsmSCOX""8IT]A2e4TVlG'4ZJ^ot+F$[ldt+@BHNl\d?$E>3'k;;"s#B(00d>+7p<,i%j7?t
q!Nht*F63jNcA@IiU%-r4O(#og.`q9"BJb%PKuKsFD_a9=CpsY*_I;k2qO:]AY4M'cl$Em:k$
g*,![7n4(NMRn6ilM?MClefA`H.^TK)2@oA*M4m:tXu;Df<]Am4mO)bVqRGEpM9<K"^!D:IT2
s"&,Y5k)a?Gbk]Ahh=d/WZPi`'3@5Y'G)eP=bP;nQ6+P@`a>0@aFfQ;EBXB[f!W&qdoGGI5B"
FW)7PEI1cOlM&Q'q$#WK<Qd$IGcc>-pPc@2C,IZA;V\Zar[f`MQ"(`NJJ-l<iPjqJi@K33Jh
4YT@4=^Q0l%I+Q.)n;@e782TQ.*.D>fa6Ba2=>IIY@JapuD@6dIBdes&khim!uPR\V:RQVX]A
re?jasZ)g*aS5;'nRAXq%kR<!a/8Ysq2t)'69LlFBk@!#nN\"DXL)?dqT<r<fN%4I?7.RAp^
s'0cYIHpBNrigTp`G;`qaL"R=CKA2;k(ua*R>(k'O%=1H10j!6s%7r*kFgS1);eZm7.'\^RP
;8Z]AnCaJ8(L##pdALp:@i>o=+kKk4d6i?h#>OmF'?=QNsJf7^U4O-Po-<T-4e[3:j!%cmmG9
HR-70]AN76q&>&ig`uWug0>d6^+!/GcX!l@$T_K>0FtA^!04)GogMb;^`EW0'>i$1p6C\._)t
%ZGD_*nm@>Ui>=/$dNAdp]AcKVuob`Xp=a5P*bghB(W,KWUsP>h!;Z;11\?EIOhc1+90QRAQ'
Z30Ms-H`Spp;HD;EDck%C0SZR7nn'(lHm$sD#f@6D'$<c=c2o0-P8d[IGlO``Oi^i]ARGC^&O
H\D&g%!FkV`7Ark3e0YODsf;FjT6O&-Rtp8mlQ)/B&[Fm;BB5&jYV'>rXOODI6JX@O/)*qU"
+j2hiN,CaDkCh?\GiN7a_=G!A&O'g8(@2f7?L+aNr#&t1(fA:PG]AqN)ukHb:BRU!c/2+7k[I
SM*Y2i_-#6#P0;V8Pm(&"E\u8+9D:=5/Y6/9tZ=VbA^taZJor;/"(R]An/k2mp-P$pS]A^rID$
khWfg*=C3F@@qihI@CWnfbV)uSrW:2h7uf`eX[h<&qFj\BOOkQhq)P@DlI@Knj:Zl:E*)oN"
#MLjA_+,$+o=9;s?;__c4/'[J%=O*<p!E_^Q#k+=3:<%)nR#Ga\TE:;c71*0?lH!!;NH.@WF
:o>c8TDMMN:EgUB1=jHLO-cBLmIabLBjNk^`EeBU/&`R812Y)1J[hOOk8[T("(3T,j@&u0h[
ncfO0Of:p9c,(h!FtG3]AhD/]A44>YiNO1D^U<=L74FbT/g:"[juEB7=K8]AfljhJEIGb=q*f;9
a<XPZAKJCR-=;H/mROrc&#t_oI(Aic)I)/&,Ut-#G4a`br^s$05j(hf;o8-Hn<#u54.NJ?i:
sk<9bit#@2uK'ia.ipKHebVn#l5TLH3+[k%UgfC9+/j&cn1A9)UohdQQqap-JWsl!=hlGURe
2j.f2r,OAte1_Om_nHo,2m/8Q2AoR2^kG]A'+f!R+]AbAE#M8DN@nc&BR/o\LZb5-EUQhbd3X^
IK5%kA8s05?d=#3<.P0-Wkh,Oo_`@CSNDHNsK\`o,mbjN5?V[3NCReZ2e<K!R1i.fQ=[_1cH
O&l#itc_NbiMa+uSWKW.Q<n@kJ"I<QA<+gJQ5'[#bB`)'cR0^g%'hsA5>gg>k2&Hu4\YQk>M
dM,/A@k0<dT>T^gJ90\:`2t(M(C+(EVI]At5&Dd'*OF=nu9p5>Z+sjhd.sW@!&$.d5f/3Gjea
7[nc?jSjH;oJQ"39]A1@@IqV<0@b-2As<;KmFq@Qo3Tca9&B:Z7Z"u1e;:ZPsqB2@P%#LnRZ[
8SJhtPG-I8?=RW^g/\DQRfoe3WafF6HP2C7h"Q?H"Xs?[Y$U,4j_*VhVdNA5B4FWbWFgW[n9
&2u^'fXDk@e59Ae&&0C&F'_Y#GG"U;@C<O]A=Vd:N<k+hT"utiFibRb06Xe;SH<cABSPk#)c^
mbdCC>_m@Y?jIS7>ED]Ah/n;/5]AeX)5BG^2^gB#Ug\@+LNEp<2J!p_7FubU-:3tQ3hfNOq:m4
K:"s8*O6qX"@?>T,M,=IIqNC+,bDGG+U*bp`Z9;3KHhA[0r9O6QIq?8k$fF'LYH6c;tl5FFU
#`<<B]A3u8WBAfoVDld]A3,$ZVi_Y+PZ7JB-cpmip`Pad7&K4rrY$%O?m-\8XH$4Z(l&U6EXu]A
^lLP[3R]AmgWP>O$RQeetMNqkSIr0]AMai(b7I4:5ojn"_\WY5W,gYUKVT]A4G+%8]ABb@0RkHBc
ceJ?n#9)e(orYL-sD,%UlGt=0uaOaa!Xt?R-<d;*NCdpOH3H<c+7NR%XGB'aX9k(]AaG)HO<8
c(Z;OSG(Y_A*B*\G1\+-&F,]A%9"p"2Z/gJ'<L=mEk2-pp#]ALu]A1GT.%EQ*RfQ19U&><<U)D/
>1ErEOJi/YQiU$i/@mKJBlMh;Z>gT_VjiRJOe>9#+VpmY4ZkaQ?Sd8_k?SoM&Ml&[<]A=aU)r
3'Bd0L($Ri[9h<]A#hVa"d%NKte8\$=+DA$:dEcN[HJ29W`;R=[O4sH#8n\9pBIKdb.(%G)Y'
Y75$[1Fi_.lJO#s,ceRMomK@8$NRgH$=+^qigg["]AFBMC+RnQ-.DGe"/Kh`"o/E=(+@1f:gD
Q"AL!SKX_rMDI.Z1-UI!@UKCT@/D5h*g9PJ!9t03mgKOEH9Y'9sh568l9LO*H]A=LS9YgJW%J
=K&F4b*7>$WGj15D=Ua$%\XXG%@rQ#-K>qF0X(%QO>Ae$)dRs#$/?LJoL>Lk1s$pj!TJ:tGL
UAC[b;ePKqN^_&+)hqmPQU<aaiuJo`d]AP9-G'NXch)(?.+k]AnI7_=J[gn=(@cZnWhHN2r^kG
3FE!tJD?=>T8?ksHdM`:-ftl"h(Z*j5Z[Bgg;Sf/VO4N]A66;pfUk^X!rtu7K8C@n'1"W:Vb*
uW%Hkrg.u&+48FO%RbX)V4m8JV!@ChhFk1)'`1"tAonMceWS<D@39j'uG%73$&c1;)@Lb)c<
uk5_kW_ppP1Y%Z;9GF$SuD9T=,BhsS!*Zo1>R]AZD%!JKe*)2OOt<^"/4HDSGhEX^Z+fXm8S=
-a]An/Si#J;lWhjE^Mb?O8Kicli"f;%Go:#jg7QhuP\c2EJM1hP\(Ln]A_V<r+u+NPg14nLRD$
/%50[>I?Kdo0I%(G1H)rZ#t6uoi;<3&RP2"n;/nYm3W:th%AOf-%p<4TIi2)$CAXA3-1#"OX
G*9?[f>9rK&tTl=kn+U'`F'\*%Wu@F[Q"WnQ*N`;h/C("MWY[kSgII%>h^[<\GcZY^"9Y)]Am
r&U8O7'N``f3*q\T\KJ@\/tfPhDj-!tMp+p(S&H2=rFGCedNNEqVY3(0DD$BO0;D3gDDg,,5
!U<\F5SXATCK3eY>^T1;#6@2a5-s"ds\&cmQ)#Q?lO7.hfc/2`74,'W+?$#FC='6q6$0W+#9
QW)r7d3,V)k"JgM.m7^k8F,qP:,\f^kO-V'ZN>gZ3/3sBffKi]AnJpBmdh%TG]A83d&;>*]A#>&
*Y(lLirN_c/hB7;pEV+@Zkgs,8`k$tpnn-]A-KJ.jcJ,P&,j&o%^X(,3%R_:*I#'=$XgX*qb,
/cKYYQR"""kT[:Q9!:2/S.!6#Y"X?\44S'u,r(+I*N"8-Eut&<Jr)$f%P*F$gU3C_4tY&VFO
7rk!l`_K:1rOu=ND6N`,U:0U)Kc!OR3Lj=JTAnBi`)nK<OTsWn2,9Z.HGTe(]Aq-'+^h:1q?<
hukQaCE;)>"$cXVA:JX)(:G]A=BI\dROZ`]AoWj;&rr\dkrYRTU:&nM?/P*X!-cB=,&)6d-G7n
qNdHrZ=&QI.g.;468Fj>R,J`glpkJGN'?n$u9'9\4/)4MTNf'gH]ASK=huUdT:o.?m8G:XG:G
`KPKXaT4ja.oN7flGO4o9P#8^Dm12IeJL"-l.=V[F0GSmU,Y';HAPr`F^)?j8P1Y)d3V-c)C
[VK+5hmjo!#YL@:OJ/P[0'#>.^f08=VoR4L/Md-76A!8]AoF&XFJdSf^jhT.TB/bYc<Ep34k=
[lpDLhW/'EPq=5ispS.k9GQ^`VAAGJ+XG!Y#!u>mJ8CdBA%deln5'4=Ke\Bii=!a]A;=ib$/+
Gd;TV<=X9P34/PoAIM.`[6BY)lS70grk([[SIE?1&ECrcGHl''iI38:3budS*9M6d@ajL*ju
P0$'0H54%SZ8Z.13m"R!6nDfcPm=)OCTjRVt1rVk?q2KJaod(NHi0"RHYN4[l@Fi]AedEa+$4
P=A;D`AXfDr:VY+q7Ug31fc0oX=NhZ\b+>J#Bl]A3E>&k='JtJ*DR5:19C2=:q+-Y$qJ'A\BU
L#+ZpUSE#@gN&9Dlld/URP/J_$=g_n"7,m'^]A[_^KPlk$EU`hj_IkQN\>MYh/(k6Dt0\_p9Z
)Pcq5Fb-1P]AKi,r"%qH[W4K(5hD5H1V%]AVL/n4JEqSQN_^q[U6I;'n9Ye0n+g`sSS6XDO!L-
BCYXSm5+-Msa4$'3XUlO.eO'fN=kgHqY?&0mAi!Thp[J=,7*')^kd)Td@Thl&$63#FiuYi!E
eRYYBqKN@nsOqhR7s*JlTP5u7k[iT=%Q^g&rWG>`+'igBEF:G<.'frfHa8g"7i[Yh)G1s`+2
g>[N'G)Kp/@RHeu(8>2ZI27)n#q^a=&2`I<Cc,$aZN;2nmmRq>XNoBWm*`j/F#>0b4?>o&5a
Ad&n"(Ts\dUZ[K:R[%aoIpKWh'i0!;.kWR,`^X9k@;G71oC#T%FgP?41G`_t9Cfb;At=;"j$
?OXJMf+<qgOSd^teLW%93#Gdjt(YA+D&&taeCZg_5787g.;=PR3YA"/Zcdk:T3K[Rp(@"tqF
lN4_?OP'-$3A"AX>Do6/_4"?aVPCpM_\kiJT&93gdV,XT4J4f%SM?oL$R9Y%4'uLlaeO)/:r
P_"X_Q\E0MLWf?1n9)iY_-BgLI'\+h9DmeE/lGL8h7Y*p+`K9T$L+-u[!b&cq;2P/G\o1s2<
E'uXBNT<Bi\IQi[fn_>:SZ7EpO@b[B<BaC?]A#PPY46#j^_$Gb!cNjDrZDCb;\M0nLKaHG(_6
-o'+1^-DohRZ^hGoS@Y]AjM-:Y*&VgMmh/eo$@64#h5q8@%r#X]As,BEQ-"5mQ)ACM*s*B/=f%
b\<qN_RG.7XDg8kb[._Tc7SSXs8d50F(j9g&d8HpN]A.-n<ZG;O(#+)Pe9F:NlVCetp!%]Aq`4
hQs8U'OK%_qp,3ROZ'In3>7i5ENSOK4R[NWD9M^$SF(p3dS@S]A<UB9KngiUfeElCq,Z$q`i2
=Wl.G;k#^C'Hg(h5#9[3:eJuW%K(!q-3e\?Kbm)EH1Pj-#s5f[`5gAl/,l'l&egs,$`Xc]A!8
p,O@.N,4C[o'q"nm/H]ASn_9\nPeCpj>>E)!_HcF0-=7c.FDPR`g:^?[7l8^W_.+98;[U?_gs
5jm#94>t&NA>(UXjp4RXrfgAKM*:]AtHmS7LKT)iQ*KB=Z?['8FlD!mm&O;g<E^=TU*;\a,[2
!KPB!%ip'E\a%E3t<@r[1'cR+jF36l8"L?6+_"g^2m\uuumNG5+OKDpZ&CuT6*I?LaiCW=kc
)"h+MeG9$m@J'0Y4]Ag8jj^HB*^ZK!N8#X4n^5(.2.T!LnR@k8D9unZ.`*LGMGU%7^fGib(?1
bJd##&t%u2&=7n5c0,0jIO/5>EDNVJCj&r&jj5jq+k;pB02aOq.:EM-h\6>GZqhD(-<;;&IZ
*12qQiYD,Lj_8Esrh>_#eb>;0^`R:Q[80"p"@f"%l%!^aZgD:R]Ak4$a%EusUpq!u`"d_D+?+
t#S9*%=_@5cn(IM8G=Ud^?1T=DN^kSM5R0$(WIa#R+L,&*=M4fTe+WF5'OoKi;piU\=ZHD>d
m#u`:lV)S.XVq:-9JpR4UlS5I'%Zr4-jGW_Xn"4l&?a9#f3cU!@4rU/@Os_-5)oeH8`UCHp1
d.(#fjOQFm=iC2mRIoPK`Io)q)>a2X;3`C,OE#L=H<q\`/7%7\^i>+TPdj*RN*!j@Z0hd&0Y
Qb+>h1Tf$t:!Vfi.MBmn32.f.&rEmZ^HY&NaMIKG_ARjq&]Ah8C!pH[,4s6]AD$hKO;L<*tdFj
2o,WR4i(OE?J*K>k/>:eK"^?p4m;=r>'SP(NpE9%O7)48,T+[?#XV0E>t8,D\E':Vj1mS4>"
:nofD_B,,o'E8">';'Ee(PjOeLf-<CSSq%td-;XhjU=X6C-J?[+_N;04k!DH,E7A&bU[2sLP
DTRMPE*;!k[-.qa07=&Rk\L,tT-IMmo9e9\O\'dugJZW9@57E]AtI)h&ChAT74M:I@Z_Rc<L4
E'g5?i*Ni22j'=)I%<r-5bm-(MN>AES%>cA4<FsJ?s+=4ck@$E3_UkUujoNKji$7!lqG:C=^
9hK$XMr:peW(61MUMi,]ArBjjm\)`h(@RP-8W&6"+/hf-'eOrO;/>f7fQqLi,WFW;RQN-B^9)
UkLZ\=AN2d_>*6rm"p+_LbDg'h":Y_jd>+[C"^+Cl7GA@LIVEO3H=S26BP!jA`LSp@Z4`Ikb
#U*SPRO;,FP_oLMoFMC(t.u?G,ZcoK??lJ7*,4/4a9>Ta70)"+<5XaJ'mND@+XAr@S/k2pAa
lp7b45IhPdB&eGPN0T_\mS7hi?Z4$0NEk(9tDori2C[^@8S<<:IZk&N$\4F]ACkUW:)9]AfO%@
%02j9i5t6?5s7@]AOa3-<B*_j8:$45,mqb1$ZdRu?r_3r9%oTV\&JG.Q'TrfbBK`7Rb"U&0QH
BpTbX=M&<Yhp/etbD0UaP&0p]Au372.nRc4Tp^9mg*m:^**:-`ni@8t[PlW<Q5D@=#XMhfjHG
=[JUoL?r/,bkZeQ)G1n!ORk0dbcf!V242l_o/[$IKon^FB.N0jGeiS*E4eW66A7P]A#3Ph$l,
)p(6^JRV2bF.`Uunb\"JKYa+mo#2el`BXo;K&'TS\smAMEb8]AZRh"<DTDZ"n+=M[ugeG*#63
qG?\a#c'8/EEa$8QM^\?TLt(`Q7qV+TGQQbe66c*T&4';AII+2hHBgeYr%S%rga[EdP4u@.M
D:4R)!59>[FYInrM^2KVAmgMIN*!j-ad=$#B(6i#_]A(d568qlY=PEK9+qfE\[fMc"sVMbOJ-
PK()g)4EFLXR\#*%lH'lFh-Ktd:Y\2n&o0mr6#>UX)6N$HP<A4R+`Mf'D@T$bYXUp!$Tco3%
jq0K4q^(F*<-h5.HN11VY^<EMAIL>$l#XlTh\?iiN+[Nog#]AHtGR++TN,*'7a9mT1b/
NSJOV=]A#0Dl,rZ%60a@g2Kp\1W30VPrR-TQ@3c+'b&W0*/kAcG53jX_+rY\I$FZj'L'1>ZUa
9;<tpR9enGpQ*dIf0q$!=;0&Xl2?u!R/?01X`a'@4G4tQ;,d?Gq?f)NW"mnhFL%hUZWWP]AQI
5_tBVD=l;5rdm25;p#8UQ!5G"`:[O^,+7bk]AQLT=-!-=8qg6$_o\mC.e&WTHrS?IPfBt'&k-
1Cm=3DE0S*-S4!)E2&o'k0A5$RMql7?8STWh6V-?#WfMl_)Cm).X\_*P&@>*V537f#(@P"SL
p=*e*A1&$OTpg'F]Ar&:uq6dVR20Nd?<k".s9Vitt<Dmr>\QTopG%$PQK$%f3IG7:mRmQ/FLq
B-b#frWYc[YNEoI?8PIsbF#&d.64e5\r#'qaeLP30&MI9LcdnC"1-!)5h$SoC!iVlr;i=J?&
*1%j!OB;mW:LGNsJHpR,Dm_GICU($Ji1l8)C5Y5l1_kbc8;_)1_\s\L-'u?I5pg2pU0q5cm@
k4@Eh6@\rh[)/EgT4J1m;1Gp8(C1f<!AK9nTBNt%2lp/!SX_u%m8-0rR1\eRKJ8p2Y`8IB/?
h9&N&BRY'`ZedS)$A0)`<$#Q;'6U*)dfPN0F9NQbFHPk1V^&;i_KA7o2Vs$:n,6U1P>ru/?s
MMe(3o6O18)DW5MU2l2Z6*8!Q]Aqb4IR:4BeA[[fQPYSl+8"8O=Vl';op;cFN5+\3XL(pK;.6
D1A;?"1s-m<_h(=k=7phi9K#^V+C,H(u6IKlZ<QRE8h(mZ[maMfm76K>F/6UD%AKhooOS@27
@`[fU)[nO*+EF]A>+]AFGG56H=F-c`7`/I7Z6LPVnIY1\OSCoq*Q5l-"&[l+j[p`=>_).e$0eF
Xnc,Km8VqYWP!]Aq:W_Zn9Me+pd9X!=$4*%<*D)t:[fANR%7&L+/X(c%+rLbl?3L).aFAl<),
hD%2A'8lk@eg6Z5;IO7ipUY0)%;S]Alg#b_XP!?.<Z'U5$ErI0iVYhbWXUqM3HNWNdH^AjgYP
>/erl*aM/l1q@>Ppgqt;4dl`-TDU4`=Ip8A2h/!I\HljeZX0e%F(+"6WraKd%cgC2rn8/BhN
Pq@n):FC;LeoD4MuJ=b,,=Y`o":1i@bsm/Ot*bc,,jI'+b+;Q*#/eX@@W?`FI2C)FDlBQ?oW
*0P"J:F=)$tXjo<9#q%S_43Whs`>%bc^?]A`F0.Cu'PmV\OI.LK$S(-uIpY&k%j$o3Kc")"8.
CX!*W)UIoRYOJGI>?X//4hG3\P1uH^37Kp[nh:3k(lWnc+TuRlgc$4qrrb4BpK#G5.TnhDl?
g)@p:0;h'*June>b<r'BpQ^T@0kUFkqjjF)K=$(MBoHu-9aZj5i1A-k4Q#I1Tu?:i/eOtG4d
bc?uC\h"iD*m'8fP,001E0sNd^M*`:I*QdqS_\GA3D[WulK-=o`[ZSKPC>JGOjA&<;uab^<(
Oj$<$I3Ml<t3qU^%d6%VbA`^gkH!mR46rm"DKV+BC;_)>Phl:2-*49YBHR*j^+Mp[#]A%VY80
mT'@NGKZ?T@XT^&l!tKcS@.Q&#eS0h\d&JtL3PUiN'ujPaE8+Zj+WVr.7L*g,Q2CDC[-0e#/
%D,35^o?YBAO*^n`W'2Rsu*[o>guE\^@;;eUq't=d&N5rq9rpTb;oXd30[]A[s'YFb<j9<;:O
H#59E@,Co:I+3'Nf)p,79b`o'GFd'#Ga]AG"G"*MDM0IsI=8p'[K/k;6Y2jjj\hT7.#hSE+*Z
Gr*6K.BkQu]AE^39gYX_d.#:bs,KgQT,CWcgX;]A?uG"O8M[Q6C4.En7U,Y6`KL++&*T1oUf!'
0pWA-,H^8APVBn(,#>'TQ'BI/g3;SW__>)SOjkiB3"7i">QZGV`9bm9Z$)E>jNVMCr5p/.B$
P3INq[$Kl92%jQ'0IpDkmNbYq9c-3AMf3r!Q;;uapV2\r1Ugs&LMaj;\'nYA1'mT>0@;3Eq^
9Vb=ieVQT&;Nc9O&?=q^-A#q]AP,MOBm="U@".^h/:gRT^@0!R!_/^j'0&9+pRgDFkk@U9`Hk
P[E:<%/>Fe;D3%soQ*LDg&4`"t,KnN4DAI(8\`(K=Q0`X'lG;7@WNqMSZ\(nH`$g`E3cKAFX
h%kDtGGSZm4#-)Xgfo^gR9q@*$g7a:`O"fqC`]Aj#R*4`e-[ta``E]AcKC9cuJ>^f+sOCBCWA&
NL]Aa)0BbZWaF*WV>A"qu3aJ]A5)d?dV#rSe:7UfH[oQGo4^:f9aJ;DX,A:Y@TY*>]AJ5q3##C1
tInWI1k.;m80&J*b9l=Qqq;8.GnhVn7'j"#Y>8Rj\C!Q,3oki=J$Wh0bB;Ui$Q2%)+kq7&?O
tdK>rnDDrnnjZQ"6NQIHJp4/\i%,_o,,U'&uK![2=o6]A\(9:T=/"oapIJ)=^9E)mU-0s^K5Z
C;AK!4dT-:S(\nJed=nCX;M:0tMJ$^XN!i2#n>Lf?h0P@Z>*p9VNoOpL#a@_lFr6dRoPeT6H
4UKYo&W,h(=cZ?s@n@tf3J2qdp1N\^CTN;gFEMrt9<CW^#>_DnUjgDD^?Ve(FmQ#Epns;N1(
ST^2cKcSK6"s=A$FHkl%]AGPdp[dJ;Q!3l8Pik+:5>abq20((L<:+T&5AJT&(YRqC`&>cho%1
BBjD3FB/K[FqIBu9^#eoJCfMZ:@N*PNdl0jHUd_[hLK;Mu3IPaogi-7T[B:\K#tk:gF5oCG*
GD:u@J.Y!(,\q,iU]AdZJen5KIIMD-5*t":r\<Is/`W/OjWbdprc.0VBl&o8nhTs,jtIl\=aa
;d1Ol=VB=0=E)AEoZ)8>JC$>2RQ;+:bI8HiM_OW6+4Wp]Ali<f"[UO2e@3lKm3Y-oA!k-[,W7
MSh&5^\G_!Iehs&*r<L>VSAK^+1'6Gp-:PnDP>42A\ntM0*Gm69aU]AlOS34/oB?@,iB6(F=m
!3ar6&6!0mKU0Ht"cG@PQYQ',B']A1-o#E=qTt/>]Ag[:L,Z\E.[A`$;_06O;0I#(S[7%W$2>[
X;3<#8@W-WG(#BuBLG"hk2C\I+j+L<+Vd1:"A6;Y"0V%ATS/@D55U_sFdg/j=pmbg;&TkET\
p^`I(lfJ)%<j/]As)b.&h\KGqWIFe_;(),\#($<`>4TG$PuJS@iGXW(s-,)7l1e$9r;WhLK-U
ERQAPSWg+LjJrI6u%B&(T&R(XYHbp:*Wn\.]A3k$^$Q2d>i:!Pm?Oj`n<90,LflgKe=?Nd+(4
<$j(h9`0Q^@=sgsEps:,-NjUrU1<Jmjka9jZ/Mo:hkIm?B`)9[rBpc]Aq&_\Y4=nOT-m`emiu
p]A>W'+t-ZhL"oL4G_g5iC27f89dUDi_I-cQ&!p%<@VhH;>LW6l2k7`/m&>G"rED#Jd.jmOgH
q]A=b##)&MA'A:oKfKe@k(TT&o8"]A&UalJ4Sr]AX[n<$fR)0:[^]A9M+C0HP#ujr$Op<k<N4ah&
Ze0S^-teTqDHjO4-QMG^;h20fc;-'_td>cam]A(er$sTg70p#Fe=md=o&$WTKi@FR\`T`\2'W
a1e,3\XcCkd'=4(\;S>gJ!a)VUl_i(3(n=-9Kr.T0I=EV[&f.NCJJiY2M(u-E,iI@r04g]AXR
q6@[n^j`Iq;$NImh,&U>hf[(o,h%<W-8GuG^'P&kTUD/t5).@rUV)bW=b$.Pg5G$%r/Y:_i%
1_T]AK]AH8RN^7l`Fh!-\Y1[nhTZX,Po1p"b69J(.(dI_EPE*ukjVJIX8k1UMOjt!$Ob4:?k(3
3g8ln9<tE#ifHF0+nJ?t$V=Mk*s,`u6&NuKmX7#)rVfKGA0H@l;kiFbl4n*aJU)J4&>>M50U
dDgtoAD^+(lghim?G)c^h=j6IR4_Lpl@IWjpD0kJEVLMjo'BlgCQ.V5!EEY7<(]A`=M_jhT;)
gR</f__D:>*YSqb84K?TD)&[N@iF<&a_3"XUN:\sBp"70k!&!Ho)EKBAJNCJbpY74C93k9k,
qg`XKDNUAJ_Sa8Qm,jfmk8e5hQ>)Ut:9DIKqmnHX<!;9DaQ4ZjSZ@[/%kG%]A6haR9OoF"re"
foQ<!BPMR1=\iX+'YL\k+o=V58?bjY%2lhO-nT*p_F*EabcMC#8-]AD10tnM<)G?nQ6,5P6f;
64p'M_,N(!o@U_;D,a=me7)>\X#:\!*p=&rINi6YQ.$Edq8HF06rrT!^jo63ahq'bED#_!E,
UE*rd;URD_LF^.OraYQEG(jX:(@bs]AQMd*_PioQD'6^MY96/$FX,.kT\IB#AG0dJGT!Sq1N#
D8E-h,4cWA*R/Hrof7',"c`9!r?,/2TTV8IBYFdZ[U6)!J<`9k[VYIOVc]AW"DEfD]A$mLttDh
BaPO>JE+[2H2F6tLWP]ANh:_Zk;56p7_ibL.Ac4`7]AL>IQZq[R#Lud0q+)bc2ML&u?Igjs"mU
p]Ai1Gg9Q\MopmLhK>5C;8=As$lt`NCd@]AR2*Jffcs^3f`&_a#1j78MSm[CcHEqY^eK&OH5+o
9Y9d58-#@f9qX/SLbknQV\NA).bI9?gX01'T[f"_<]Ag!H<GhZXj+H4G8I]A:V$g+h#A[G8:R9
OkNfJAGs[U]AodiI.VL96iI_mc.-#%2K4r$i2QploLcW;@@#^r1,SAGlG$@US`.9:ZGRKDB!X
[ine-DsFp><ljLK3D>aKhKeNIkk&qrMV;L4.lk`;9._\UnTf?j1I!N0);=mm!(q]Ai.mO-^=K
:0'/DX`-aNYH/K/p,rk`4P$URfk^d4>%A+;*_1G#8>+>(ke6q73[0*VbsT:^F4C#pHen'&O?
QG!.1no.h`a'#5.aU4hR4KI^+HL+qk!6I,?LlXjkF[jgSmFtmK?_mqY=t^lW9t\!&Nej`n%c
9n.rtpEj3jXFFW_KI5a^)nqiIonh0OE^AHKB]AZN[hEA]AlE0qI\S6)bmm_(A/"+U-DaOg&V4<
O+%;TqjFl/oVS&?*+^>%*Zo#Z^DPWqNpN^15"6B^4Pr]AgXa:Np3B@U_1MIbE8+(BpA\B)c`=
L0;E,N/*HI3)88>daAo(`BHJp(*EP\HjmrYJFG4);Ia_%hXpCPTknG.icG<.0@6d))`D6HWC
pD.D!L_GpQ%DoF$i8LC[ap8l^(]A:tt8d'9<0g0uUo>tpVjRo!%(7/DMKC]Aet`R[\u=mtWS.%
E\+:^T0K=`HBg?(5s&/:KSRm.#5-PSd%bFWSi"26+rN_ngHmete5gR'7]AZ>tapbh9/$j6hp,
$R?:D5qcP6,DQ3Dt]AQW12j(?W\+4ZWp#rL5t,EEeoM;;?mgt)>?:%G*t4%S!54C&eo52uPjr
+VYHR9efPm1Z%TJj3d9mf*YZi7[=PBUF#)`L#4LOAsbZ`8;+cd%8ju>R0(>"q9iF>L1h]A9g@
m'%89;nH3#Ln%["+`WNNZJ]AGlWm<-Gt$C...f)p&O2qn?foP&Jg%f3%=HT/[Uu'uhgk@-<7+
N3W%nHe1L2<>rEqq6+?%b-;V6:7&UGXfT33nS+5sbG;:]AQ<pr.=8K]ATDHs2?:5Q-bd-.o=*_
XW^.+i``p:9'opH1H<n.4$O=3p1Q87H^]A)(usR"kjEGN8/oGM8[Y1FUQHti5L5E3M03B5t(J
Mj:u:Wbl?c\iTFTU?]A`u_&>Gj<St2h3iSn]AYnLmk=:]A&oJ<Q8tp@c(;aU_"VY[Q0kSHS$9\a
d3`4gr'd+V88Il-\jF/f;5'Xl`f`]A`osA-QB#G&,on:l0$q22^fJ%E08@QgVLCl7f_;@ChZ&
<779+&M6T4mX9=#E&I0mdVq?9:ZQghben%6Sfku1/Efp;C3afYkTQB4D00[E_VN:/iZ'4'ec
H=OmW$pk`#ZAYH'-^(9G*"L$:r8-\k1Uae!q0#!.gEP1T?eQ3#,ZTpi$2-V>4*\\e@DUuHHl
)t0q[M`_@_OmXj,A?g:ACuqI\#6DojNWOV!T?9\\L=Voiq+gaN7o92&f89#Tlj90-@VQ<<F?
59!8D9biVIArQG)MgZ`B4(QN?(JK2Tg@b?unWJ1i/(]A,@,c=.]A!RFu36-MR2FWVTHO?Wi]A95
o$AJ<&/b]A(+D/J[<<IsF'k)Kqn#L$oaiu#p]ALUeo)m1'!&1o.oB]Ad7OtT_ujES[ub-k;)/p;
&gN%g9:Heo`.%U_YSjg[655HadBGf0_kIh13d@m3u6+le1j3Z1D7SJ+kKCA]A]A<+177DnmQ%3
AgBoTfp?1'1nCAFcgICRB!9^c%-lroi3XOp%<C`j,2eFAis,EW&0mg9_CoHHaDRP]A*ap\Dr0
8\s`-j,TW`.L$]Al)2?Fgcdjlm5fU9=C;QS+A`TQh%.%def\u2Dq*QZfkE#lLCfGo(M:FrYDU
[ofsR>6c!0dFKimKE,tIYgB3>0qif8-)JK08So=YjSj,2gJ1tHPOgK?TXVu7rR*0m;O^Cas.
ShP7iRR1q$auaV_qN)5JF1Z3*"EY$UHVsalXdo+e*VRZS*J,)T*RoN_k)6T#hQkms#UjFb2^
5G%X-lTX.S%tpGCT,H"9uq9U@^@g[rgiq#IFCjDT'F<s%*SCRpW/cRmNH$,m:Bp0O62PYkU$
fUXcX`I7P=lQ%_>s0c;pT*SaHRL0]ADMr^K0=[TG_5KjB4q8EJhZlldBBag75o$/\%`GY(/J2
iJK3m.L+%Y;3bYD/iVKUKTrW-Hh4=)U9_Z4BOcl_j@-r@3"cHV@fS1I.)jg_e2dSGc,1m/GK
5=eSU\2--d_BLkRnCr[;eF$1!gofPUgjXq%%1#E=VD&jg?aNn>,IHLulOp/^U>:bZ'*dsM(-
6g%[0EOYMrb)hTerVt&r`F+3M[<(V?_[M\Sl!^LP-1sjC=TiUdJ;8R@Cg9d!Jqs+24YCB!d2
Y;J&5O_3tiDi`&H%pL"lRM\RJ9;BC?,FI^7/biF/2hE+8W`K?);YSk)"E+YsXK&1QoqFt3'"
e^^naJiV0-mk_&$eQ%&AYSjD+f-imBP+.7@>Di[5qq`s;B0D6aps9G)ELFL)?p%b\1mRE@kR
R[F]A@JU^[Yop'fZpIPPNJ/M(A5aQmD;`hE2l)NADRM?&o0t<H"KPOY8gENIj07>hiV1F\c/E
f0"qeX,N?4tTE`./\'/9'QIF76k4hbH*1$`G?WWfa,,[Ej2.t.b&S-]AP<Q+G`aK\[77&k#fm
RlGu-Vi\s`7cI%nmWfhVVX=h2<:A!$lB7t%?)^=EI?2:UG7R1mA'psi`Zle4k5<1ejSG8IfX
"k8UhWk@Z_/pd"7hRnTt6\%idWYNs^%WgAK:Co*9F`4TST0ET9"=Mc^oEG!X#]AZt_L7V>#"Y
/6P?rd!g@q>sCbF>.#[\10/<CMNm\A7V<^ZH(-CJ_$CTWhsWCC;npLqs)?UXj:T:T,Q([>!e
$:gpRn0@lIl#m]ArUnd)PDL[4M.>Jqn)1ScjdN%S%7^=Kk`T[3YnE@HI"///\3$@\uB?TN(9k
M38`Osigh,C]Aub;ScM6nSF$0,\d.blXES-f/Z)b9T<<g'Y&r58!s7QfC7%3U3c=PiGPu>)0>
.""cp2lb*L$"n:7DVsbR-kOo^[2Q\N-_D"F=tb?#KLSo<(B3d]AASCk3[aE5Acu*bhBEt8l5#
qAcT^ok9lfKGD&!D9l\hYTomY``).gJNnmDk4%6%\RK"\EB.2FZbj+"@W;urbB-hl>,L<J*o
-i'b'>l#P5j%j@B6]A7Rtps@lm=M:!GP@mC/V\\f5+q&n&?-F@B=J`jP`ce*]A1sG!_\fcOk9N
U)[C%co..@ZG/p;N.q2@PHI>ep-DIoVkXh">ZX-W[0gMB7_UE#MLeCOS;Q)cTR!5cJ[_co#8
P]AGj9m@1U23>`VO@*H>$K:k%WkKg,>l'/5_bkP/K?m_/akebX8'.,Kj)lhoqXRR;Z=>\K9,=
*6QLjl^_udSUffkbYh<I8B0*f7E\4(pEd,=K[rdk9ZYp*mn]AMa3^dN[eFcsU>kqW_>FL"*\[
dAlPCGJFj0\rBV^.p5)H6*=M_J$Y22I1@#3*@5b"Ak]ALh^s0K(<mR]A`n5_4a#j'Lo6bU^jtT
hS7s79kY[*WF4O"Dp50s=?j#XfBYnpYb3^md,(=r>fuge+6TPIrJ!?C;3R'3FPO!J=cf_.f^
Ja2D)#%BXHBe1j.F5q0Gm,SChAS.W;Ofs:bf-mr*"9Qgn8P_*hM^!31S<h\@,JV5/T[_N#g_
gI3@dCgm>Ibp0-MD1-djfjn6ptfOkG62:-=U*Em7M;atd043c;gKH9MlWl<B@nOh,s+;A;$.
E2DW"Jlqq2T!`i:&4j!<hJ3Indb#kMD",ZUO6_nrp!/LTAf"LK;M?LMcgY2XG:E#glV^X!g-
jq[S@`pr6$D-Tmn3]A8+bj9am?1fjQhj@SJ._01a2o./7bECKsB>tGY*`F8D`rJ*:3g8^o?_=
)pZ?6+Fc-^EYO'mg;ihnIbp0/d6deXa-j"mD=-^hp&&bS+0XJjaTi/:qq'HC#[f^AbKqc.Os
aA8%h;iN?G<\8F0=BEPf6!OJ.p-3aU_0Ls8'GI,,et(@LOMkP4alHM_]Aj-g0e40Bu+I>h"aN
<rIa<h03J!NYKd8G^eX)_S9kkr*G5AMPAS$Qf$TkHR*8XPcXNgK!Z^D\VsGH!!OrOmrYOblr
p*d0iXj5HiA\#lELq,$j-9)t_gAg3SI-V!QMSa=rZPs?L$i_9=HC+mrI<PunouS7p]A$F'P-p
I3[?S$@q`:NLb*'+C`ZY02pLQn<P-#I1$R+3BfU:(lfu#N;Z*.XXb`)ABUN8KZHp6_@rqa?X
J+:j=R:j(6kIRhnrI3YuF5L&GD$0P9/+YUqS4<+;Mb?;UD0s^^n%FR(@-#4%(_?$t$gu\7d;
$qEX5(ZR8s;@?M^A(]A&A_l?,Gtmuc5a@u$Y<c?\pi_p6NtZeN8Rs14nKDQ)!:Q#//7RW/k1H
q_S,h9p]AjFX7*jcdpqHWX`_kp%cr]Aqs^+7WPdg9eZq;nEl7'e36^@8KX-$=)QjFL94IhD1AB
sCD,4jAl3/&3)7n3]A+Ll5&+/r^UW6KC91F(A/h%-J\\$NZ/8K$?(oj+Wo4o.>@H,jEacOA*!
>spcr[eRKs_#iorh>TW]AEOp5f+le==PYFF,/5$s998AuIIlG,ag.0TO]Aq`DcXbGD*mFn_(YB
ghSSqb5a?#C'NNUrHL!)6?]A26Ccop.-OcAG>t`nB*hJ-lg-2@&IcIT``lj[&j8b@[iqC(>ok
2hrmfU%"16^$!=NJ.3&N-P5IlI7rM%)I*`OSZJ3)WWJR)B&AA9XnGnuc&DOTjE@:8cUIoppL
YJlXi("Z>aA)PI4j@p()(mjpb78S&E5R^&V:J5AM#955UCno8ecrJ#18-Xhi*L3oQIL]A[t#m
Aqetgmf\;m5+;:Zh2fu$%&ap+iD&JcWI^[d4O&<F5<Y41r.+Y'Vc6$ie"i>ni'`?`5ubXZoq
9_=J?-"o)[uM)6OtrDEpW@[=f_7ZK9a4fT*_pWPuREmrS,<8."foPo4Ru2h)X\l[QrlVPA4/
%#/%E^[=j`a'S!o^KH3I!IZC-hO*Vs?S#u1#3;7`B^mVIf'RoVb(A_sHU/J5rZXngU3WV?!d
bp#8_f>4Z9_0+2SpiF?Bj#'41)]A`LhNm^!+/UAN=$Xm(WB7a30[iiV='kO&p1?=_`jmdmQD[
(h]An14pNP`broBtq-[>C6>i8prY'-Oe%EC4";['t\o'tmE#A@3;\0S0aJFroZ(Wh-^QGCp0a
b'!,:`98SmJoO>L'fF4WUb@dW%Bt,Ai@1t"BA_KY3n)Nq`l-A*,c+p!-CllnNd)Q`ies'b?^
Sm2tVEW?au_0<NQuhD0"h"Xg*6tOf0i"2M5e>Ujs]A43MAWSr7:AZJCu3PY="hE?*Ce5??^s(
pi-s/N!nANa*J*QgjNedBQ4c#<+m/qB9N_W^$BiX"2B/WLN5VKepB%"*0hY@X\=U;dio.D)e
Uh:3QQpZ6<0i&!&+2a/\+>/aDdN/o:4j-a0^(ir_-3TW4J5]AZ.cR_IhF^:oKS-N675;p?AJ8
_llQk(m]AjYdIB]AVfZ73l%kU%Ig1Lpr(bp_6$ASjSmS_4n-r;)onV[^P?'>o1VOm>g5#ZY.b<
'PXX289VHE0`gh'X\C$34l9'(e8m"@F1RK#.<1nku["J0*QesIJU;Vd2J`?Y_#8pmuhFtSlu
[g%!Xq:C:dOjO9WOq9N7:Q<)BqdI8F9Ti`Tci+i&g5.7a&5Y.-pe;urY(9t?;\nCO=M1!X,l
Wh&!lBF;W=N[<7H"K"i>0/j#3e('7RjhfdfCRj`FpL6[qKb(3r4:+[9=+_A"ZL'/BFk^,0KI
c^Lg/6Z\S0r9=]Ak&l_<u[EOjHFX4X"0#.rr_t.$fY6a_3q-_I*i%J<NP?]A\<:,A0A*0PguIn
&JQ'%D%/<4[:"=4L`c*Vc#rKtMVO?@I,1#&'\_$o-X;$5U)UetN]Ad'W/GKBnal7f#Fl*J[Q,
n2@dM&?jL86H[=R2i[4doQm+pduI6E]A2eA!+QMohY890f5V]A#-7PDCV+'LB@ta`Om_Ho@+mq
igo,n*h:k]AGJo=ZIZe)qPN]ADWS+=tY;`PBWXGKj_KdclU=V!9r;skuq&^i!fWI(e@=trdgM4
>7:eCbbB;)+u%>)pZ9p:Ap@+[dqsVc&WPKh.5%__P>LRbm7IM(dZ'Pp;%on*e_@d;VRp01B7
TroWl\jpPU0j\Ab=BZKO=W=-"GXA6cVNOc%_jMoW+Uq2OZ6R=0_1Y58<>tP-/eS`(Ou3AdXb
D+i!s&R5X(aqgmb[$J*=gkoBpc1<J;2Yf;A\UMh%L_B45L2&a]AR=%!NT1;@jrPsam)q-jhL0
n1AV,oQ:FW!qd/]APk`G#^_Y7nh3p]AX\MVdZ=BWZN[en*RV,K+T!;X)l9O]AU8b^U>$='ioQj*
08F&;ud$La4WKMrR4#>`)I3c^-<B\kf%P>+f[Dkp3,$V'f*Y9q:d%W.EuE)ZVV_Ur<J:$SMp
j%9j1AW6=[[;MbBoMY]AiR</'d<#e9$:M.N:,#9U%p7M.]A*Q9Vnia.`nT7&r=VPgQ.[9dUnQI
K5%Yk^<of%K80+R4%X'^bGH^GQT]Af2mb,Q1G[0pMrGlof7eL/QI030Q[i.#>*U?QEDF14QM4
0=^?+#.H[Pf;B-Q;?o8Wmb?TZc(k4mGroBGLLHJm]A=uQfDHuj`]A+B8[[VAFpXmeV7r5YiZ4E
b>O%lY^,BLRjSa3=Bco1"8>_kd>US.UaXsVXG!IfuU'OM^!b]A5DGUNoa2.$*L?(H.;T<6qNR
oqp96Q']A@d;\Ke>V9ij\oraMqtUIRd#nR#J8AGH-N4Hhu,)Bbtcg8hd[2X1p^^K@#.gbeLVa
+H;6dq'gk^VtplW#%j#E_%=[ECq3eDf&[EQ8i<:VB56n=?gYBC5'A+%_'!`-D['6/ZdHl&%G
mPS`UZ);CWjJ+kVNGd,[JT=6Rf6,o4LcGaXq"Xkrf#u(]A$`VSU$Y'?!A%+C/ABo@t,tNZjB"
^=r:k;I_esWnLGP'iHW8h>NPU;%F#c.(RjAja'CI&CM)u2kWmA%JuK\P/TCX=N'%1hW^"8rP
nrW#$,c0o_1>hLe_oINGl#bm.Nu:prGt>lJ53U(;uo5pD$nJ1GG6c$-N*.#d\"mu6t5aIJ^=
AXq#X09Nh*IP"EftXdU/"o1SaY=p70RHeW^e+R?F)U-`ibY@X7P]A]ATs5l>[sii'%E(+ApA++
2]AhFP3"N:!G_&-5n)&eZ)kg`31$>,JfEqN-Ct[0E_j5"aAT6K_3:gj@?OpZG;s'9h\o"rIL[
>Sdl9[(hR%jUn$a(R?9Dbp-rMZ'^I@:LApG?,4=c#Q>"rh4C'CNJ;?&SF&\[Y!-B^#pZ'SF+
]Apd=Ij<0RBHcMJ`g,^i!h9Ab^%:o.ei3tW,EgVo5,HUpAH&g"kq:Xl1=Cot41eu3JLP?76+4
k/CoQl"2Y4S5,HQMCbQ`PUNjWXg]AHcR5BSD((hD]AfO8ZWP&2(;43X>>RfeR-L<4$_udL]AgW5
+-'7D1*jmrJ9"@>N,+j;n4Cms5V$V1+t-ld[OM@YJMQ2[.-;'4AeaC*Vk:Nm8K9)O>4rT8n6
%&7TjeP>0^%[8I#WENZR^.g+0E-^UXD\ql,=ee$X3&Fl;RQb0V(Kk[YAPsD09ZHB7!Zh6V@/
66C&KJ#J%,&HMf4lDNrA8RBT`6,J#hFt8_khS$gasF.b+I,U<L4X>b94<MN>i$cRbYJ9m)[4
Zd4[M8V0*pJCre_Ke7<4Wi8_6<;N"1'IDZa>G.#W:S?TQfqF'YtdNto<e5Q%`X1-lF+XjSQk
"Q160STJR2d?j]AOb&^":r3XL?*,D"pCnPb3DSN2@@jl_/oT//aa#j"Nhl[.=up?]AbM@S25@2
@j'/Dr!TAmlWY4Sc`XE4e(lL3Os>P^-%l,QK,[r7o\M7H8jVgs3H^$^7;\Lj)X9K$B)BDJIn
e:CH)`Z0YmBdtOQ/#"@2pu+((jKYVGkC0l0Sldo*fCD'V'!\s2=X/As3[c4M_/l!XR-aXM>?
tg<rg[;LigWk9`_,`kou=$Do37X:M+V%Z_DU/<@o3U6QSk<MH;(:O_I/#Ar27M35P<)aM5HJ
d+OkX'`m9lI+4gI;>#hdL0^S#V0P2C%B,#1MY([)rgdB`#*2f[o?&l8ho(XYf\d'"u,$%PL`
m\K@jO+K$>8hN8a:@/aZDQ)Mkfrp7!EG?'@t[`92L7qBpbVl&&LZCSK73B*4*F?k=CK*A^+i
J.>WV.j7<V5l)4nB!2u5>U!tri`U;bsp^/X-?:h%',#)tk=5sPAF2'\$K.>h+?*R_I/1f>N)
80eq&4rm^KaLg;)GO7^.i,@(BUMi:;TcR'K"DjJ^og"q]A:u@^B-_eETQjSOI9ejnU*aJ_$,I
r;9AAe3Ee"+VRg&%J7,D9:CW9qR\jfdXrIA+9KkbGqf*lJs`rcMCYVo:sj<1aV]Ag%[JGN1OK
1V)tVmMfC9WZV#s>9T0JFl-Zd*T(47jF;id+eW!O-;W_nnEf/atA^of4h\rOplWd`OHH+m51
-baqS3tGrCVk%QCi@(=DE;cH+HLNNMr,sTS),N=/Nbo;64Ngp&R(o[D(//+FIS+gr%BhD1#e
&XBbVf-9EgC.^#\PS`!oK7..6YW3/0a^W7i$uER\fE3_sEuQ&0b'Fj3C=>qPs<(q4rIDpX#o
(Y^5l?1r(boB1Lg)l^Em?4m)s:M4+BC6-;nbC3.-d(4ZYT#lH*g$3$]ALS-48m07?&hnU$h5,
-hB2rAZKo6XrcKk[YWq$i>&/WI>`VVlT1Fpa'K#NJ\Y6)f6?$d3sr$N:Ge[QbYrrBU8*<'nJ
p,Kl2MI^P$SaY7,e48bpIs4tb9$FduJXc00jd!e=6M9e%H7D)?;SZ[/iAEq@MSc3>A"k(Fc*
4+\<%CK+0<Du)oY;^2%HnU0]A![$%^mb$=t#$L4q]AM)&?*)XeRgSS;XqK."nL8DLfG/d22r4<
*XC4P[k4@sC!/,"ANKH2XeXb7Nh:$r0TO<%%lhe$o-jAsYN_B,IPkFGq@&U^]Ajpr"3(]A3U@b
#7c3ONoZ@+o(F"hPrSAiEhG>C7abZ-q:E5^Wu?7O\0/DA/6Z6C7f$AriHV]A/;,tFm<<HsH)p
8qaJe6DR9Gs7"]AlWZE*N(hh`!XSM(*The<CWrMVNelTR3Xpl7:ba6\oJjDEkhPuEd+3/pI;R
e8s]Ap*ghb@S%HcZ;Zbt#pK.aI(Y?T0CGb#86,Z5]A@eG$GE<A@(O1<GB>okrU&<?cJHcK\ps:
E>,LjBV0]A5'(g.<]A)QI";.a(AYWIm]A/JprM.et`D'ej=nITXcK8>j.KWs<aNT/s4&TdKt'Z@
?3*i(7_'L1)q+XnG"ER4d-DLgE!`"FN4U,J@$h\N/6H\(<"!8c2k%C_d4AJM&>Pd?(6($@Vf
"BFZAHbf;'9%KtINXB]A+m&mGlkX9cDEKf'kG'$`Hg".0"h*(LX.-IIY7sKm(T^.R16L+6l`s
m4X-n,:n[QsgfOqllG2Zu!Z#%e-geub]A93*bKrY:H!cLJm.G:>`'O2k9"_rXA)E;r!sO4O^@
Nr3@qM/2OqC4Yd,-5u6GB#1bWb;<q4kN*Z`LH&^U[V[<a\>hp4BDZ@@XK+MW9V6D02UETkFO
CIP$lFh6LT"Y]A)S+k6M00B9dmc01r3%i53,pH@^[DM+<@Fl@QJ*6Jb]Ad/qVN[m?2;INFV@kf
1I5-]Ao3O9Jn9!%MtA>lbAoEW0lZBMUTOIC2@a<3H.*mK3VYc+&A["L:j77]ALUBGg>W%`lAQ,
FUs]AtP-[n!l*&4ihbJ)*)(TrGI6Bg\Q:86")QGsNq@+W>ZJ"B\O-uqUPc_8\C/,/]A@@27XA:
-qN^qU4cCho*q;L?']AW(:9%4UJQA@+eBIPtMdnm9i'>,$GEiRBVR@l^>5k=/]A?O^E+d3lW8l
:bbhj3QPL<A%QJt4;fSSZ]AP:uDDP+HF4Ub68jSMX&Z%"`*j1*5r.X']AM&%#uCbijpf<6X4ri
g5tGTQCi"Zs^hh=9fm$m=jK%26PDilJefu/7-TBS!o_hf#L5*X_4c_dP#6k%4E$;nt15<3;5
#'!9YfBK8T03+XKNl4)i82=K#MJR_0AVZ2#-PUB>Erg7#5CRaOOu50#rX+>V*P<lWZ<GEmIS
+aMBfEZq-h,DqWmelGidZ5NWchU)G*_;>%?s)bk0J/#H$^if(]A]Am0@roGI\Ta4*[e.OJ*\r>
)LN73?bJ'`Ekme)KfGde3cl.J?d[$e\+Yma!O/*$,(n@nQ)n=_nQQZgcMD@sE\5_hmRYds<?
K9_#QHS@-nHVRFrqIsmVi!"$`>.n\bgHX>.T1.YmhO7G!$F@i/qlmX3CS.Ck&QqajoDnE@Tg
/Mj)-_ePX92^-dbP_,Ga4CTn9_Cr<Z5r'cUaqlM]A^lhnJ[_=G@i*V$0;uG3$ijkq&>L7KQH.
E8_f=,]AAfCLA\/OVO/`n!`'WUs**0Ju?0mM5PRna+7EnW?@71h:VN9upG-8ZSA;_Xm5_[khH
=?^G+?'cICE"\^)beW/oLX74Tk;O.aPLku>hmkC+O^&iH4bONo\)QI2U[hE?*4IV/%&G&XHa
8W$#_$6$C(p8L=W0eaa5YC2<424lNB!arDgaM[b"Vo0Qa&6:6c,LIaur#`rQnFM9LYoL>i(1
IF%`5rPe0[@;<`H`.bONhnLCMM1<,ClpEF\"T<O,!(GEL1)nc#u$3TK`H2<6TaTLVf/m1W3V
chK*aoT+Ur=tm7$_^oK'<J*oG8TuAh&0D1FgH]A=Ii2m3J7/K`c[d@Dq10:Qb\3eVC0lM[9)<
3Ia?%d:QCcSN42:Qs:;!omE9o6HS9c\o-.9,h4Iprk`,A]AqlJ/FML:j&.kF0E?I[k=AG2fsQ
Y,$aZXV->nFUcg";;20;&RKM!nuM"+e3Lf,$q@@qqAkC!]An(O6&d8#H&6\QE(fj7Ef6gTaQ7
24irr[7?lFaO#lh_7LhCg'2F'R01@D+]A"a6bUuL[?=g,1")MA!3%t+[U70HF3'@<BDMln$.I
gB$s0FMf$&]AR2\`$ceKA,*5.nlK4L^fk^7ZY]AJFg%$)_eF0QL`Mh+Qd,iW[VAd.%W>h1oC&k
^49?`sabLds`c!rBdi4l[e%H?n$#RA*?Wc$:J;'RQeOU/Top72;:=D9B]AtK"8d@Q]A"KLG<m>
aTAh9K$2l2EQXoL/\5fJOjnGR9a\u.cm4btu_%LE7P>k+!]A@D@Yb1.O<@8"-'24WBqM>j\$,
-fZnj`nM5,=Pec8VQ'nagZaH[5']AePY'r$0L6V`AE;Q#g9oGZQT=-+SM-]A#A`m)5Ue7LU_l>
Fo8OpLjrj2#ZhDTI(+:/%\7]A]ANg]A]AX5,Y*#L]A0pldsId7SW4OAa*=Y4:AM6=r2A+)>FP/NV?
REI,7V#;-CQF!>p\`&Y'D*GqJYlG!5(d3`/;,0qgim]A(P-bH9HD,`o)GChWfl?UrGua8#.WM
BCsl0j7,iWA-'a*,Z32[$966ZA9P:eC<Y<pXQcs]A'f(_,,a9tY0(iL:$BiT6mbOO-\Uj:Sr$
pP#FV#,>Tdc.j"^,]AnWcSD.GDn^eP'tLM/PO^1(I<sA.sKF;_l8"gTFoMDKFU4QQEMCRGqI%
L6`]A/Hpo6/M5?^:R80ig]As!pjoXoH95$EmdAf2.!2+]A5@"n=74!eV\^1(l/?W$BiR=)cm?%<
,6m,jCk2iY6lU+#?a`E2^Xee.*_CgMQMtft)II.n5`MRmiZMPSCrSh*tOJm;tW)gR#tPoAb*
Q@qX)482n4O`n4-R%aE>!>W..,h,c$_iTkTuoN>[;G!@\7881jq8O2YJ%D=s/YSej/W(5E;0
A8`,lo*=4T?YQXlsfSMm4%<khf\?&@9#:H2W$fn:1(c0YUNCV5>hTEl7Cbo5V;VXk$.1ED[G
YSiIVq:H*#<^^<QgA;gZ`b_tNRICpq*\W,eG$G_?'@S6YD[7hR*>2OUA(1pC%gT?P,o*C?j!
J#I\)+H`eDf7e5;-7e.'FS_#0+Oc%J4qF0t4rcuqjqCkE0!/ug6iPa7J=a%G<'_Wl*Upt\!F
<&<#lpTgL-dMdlJIfM"HoLg4Z*@/&_Ydm:@?L-!%&4j?#gRc8.)\cjo"m(<]AFNI?5iTua*&o
BQu*I!%&CpYf/4=V4=JarCIWGB"i2^O_%jB;[0Uah5B+s=#9qVP5.>KKDJKEF/?p^r=c4]AZ0
FWrT!;%@=;s:F$:^J;DSE?2-Z-WSY"Wg"U;Rt,nS0=)VKAVt%,Im2q+&5XQ]AXU)[c;E4d7#+
;(EM@D>`A?hmJR:1\1bHQsY?F$()>.%j3;Poo0S+,\Ug76Y>NGq[.uq"O-p9XJ,NN/.Rk+W;
'RN6J\ZNE(L/E@THGi+sHaDOaCFGt1PWt>A-YeU"CJ(^.j-AOn7H-ib;ED^oga)s,OKV02?B
_9j,>oQjkds15"p(G.bf:</JL+JWXDlo+(Je,GIPM7IfZg\o'PY@T2lRtO;OB/eXf0Qp5'.!
sX(Dk^!a!HZV?:OeQ/>;eaOC."%UJ,24;U@l8,V4jq)f>]A)Z(E\9GN%'=E(ae3_c\;eDB:uF
;;P^Rea;50b-aH:hSY\@V,rH>?8mp,=%Ufk5=$9F'#2-`SaSkMjM+jgc94p/i`rar9XM>)X2
R)Z?YZUgKj?ImVi#BI@hV2QR=Wa=g),!L(?,pO$-L9F[QZKOk8.u.,^LV"0i9C#P2..nF%RR
+h.gJnK8?D\be:YDq*g!N[qNA:&[s<A[cYDAi.=Gk0YO)-?]A1]AO<.Aos!L8!TLB\lm1P^F.)
"do8CSqg\AaP>/ZE1N9@-`oILrR<kjMjlVZa3M0<r2P0[IV6A0bm@qgDDX5+$%O]AZbVr_Qid
)eG/X9b\WNdHX*obpGc)kJD[kO^$B9=:HDS)U(2U8U@<eEVaV)4V<s+S)a9m^PAjn1ct^l=$
=_)pIl;5()Odh?E'Nenc_nD2K[pq@f:E.#bC;pH[(Nq'?((&c]A01#G/ARMj#km*POUSlD(=I
C#^2a!?I0?IdH7]A<Ml\\h%++s/..$(k<gto0./A+rALK&&m>l:Ca)7:EF58gB_?">n$IDA/S
?SsW9P!(U+?+K;oJZ@E"2.r,cXphmaCY>Xcr:!?Yn2q1.Co/RX58G>"Eh3'@P/uUrCe6N7O_
E2>b@VukZ'IMRjBqeNr#MF<*C9_>[Em`S[J:cYNBZ?cF,4`S"Dqt?kMQ5Z=-p[]A,b'Z,Uc1/
!`OfJ(f)OIXj_m-PNu`ZJa+`HYda<!\cC^Ll"+KXWnolo_]AFS:p&]AQ%Oc[Ro,F2dC7Up-)[D
,n_N&?8dR_p0o^jY?$QoCe3Piqo!"-tSLGm9mA?hpq:*g\8u]A(`;p!GBP"<p3,o#lGK+;1ZD
9P,H4#6:I04E^GP,Ol5Yos5Sd8+G/\m8-<Q0;ij*\pVfeT6/s\_"5mh&l_tdQp2G,I1n"Tup
m@Q'$F8qZF(O;+3iMYCfL7'"@D.$Xh-_JeN#dkfIYn"$@'5R#]A[tJ,chYbD.!apd%1BHNNk+
cc)LV'Q>b+p=>MFro/l"MG5Oe^IqAn$7,5kB2I%da&=:N<>dC#eKI%'dXR'B9t4&VVo*GcIi
<EMAIL>&$-")Sn4GF9j\2mSH[+`I,K@dnc@jG4(5IXNOf,,9!*-;HBhj<Panc,!I9EsW
mMUG,OqGTB;m7pHIoRH\%AI^RuIZMhU1$QAa5/Gj?l$7mNoK[d'=12hXQY;Jl^EUa6;PU$-F
C`]A%`L?66?b1.]AMWag@JSX&>*6[i3RcQ(XmK6NdX(W$c-Rd*Tu7Z#1JMs7#=UN]A!Rt]A?1h$-
ZusoQ"Dh8PZ0MXZ98Ijr>>Es9M'hep?`tBBR.JJ"_#KF#@.0a5o?^IdI%<3ueqKTV0b!hIjN
g`uls:J]Ai*E'?CiCqZ*BP,+HDt6:KR'YkTRN)?;8&>D%p0h5'@Opr85m'i'+V@7GbTNR+1rK
:9lh5qZUM07*P$PklZ:A(MU/]A7<-FZ;+#N1BkCk,_V'e3,F?Bh%/kshV"o%s.g1`)RQCJeI>
tVoGiV,"9+7D+&I!1AN.4/MF&kSPa:tg'eCNaNV(P[<%]A%\&HSNAKqjXFFg48JA3D[1-u,Bc
hIZ.!TA>morWDF(1hGL$S$Un<WAiR1`V<DR%9BBSX]A)?@oX5CDQu?<$nS9l7lk`Y`4/G<MQ'
XJ#-Bn6spC4?0Ndbtm&sUYd]AUFV@Y7c1jH1H;f4F4&-.I6%]A`QG@Ot=L;3m_!WH:XY"P#ks6
Z;D8IFk!K;Irs>O)o!#,5.;*0p*#Ze`U#73l>0@5uAa\p`Tehg?\Jn',(Y^8Tjcd7Tt8+dh(
O#GHDr?:uuCCJp7!H[X0?"SPA6E.qS"5J4A\)fJWD\TmGNr8MmJM-bPg1jB=:"]ArW3R.n&Ej
f:jKY3?T6`$/9e#]AA<2N<eWZ?EDYuW4),Lf4$3smP"8e^C%,,;1m+bSmDoP7`m*4(pncaR"i
&O?*\ga8.W<j0Z1#k39V#H)Q?t\E,eL%43I5[_gYp*)NE#5>S(Qd;b5%E>JZ25'D%E]A^'%[Q
38^2..>N8m%/I7SR4NS2pJ!ZEl_V@BB\-7E?qHiU)logBQpA_3l1XmGLBn3pi^7X=fCr:O=Q
7'G)et!urBE:sq2MoW(@F5J1#u`Uj#4E^HS\GHZ@"ilN^A&>LidUPiEMVtl)%&AAL!7<s!4Y
-B\m1\\aka&7L(qiS0:O*gKHrA<C2<7_Tn1i-5)GZi8JsXHD+^!FfQ53*mfT+r>`6(=aq7a+
#c5?0m]AB@1!%;Oj5b"kJR!ubQla19NO`[K)(t7D4I1fWraCAE,n[iF>#X?rnfCL$amMt6`SY
hj95Rh:E^<UdGLr"Tqj)k_[rT\a6Vd"ENGFmCjl4i>:#NEi.c:V<">6oU=hFR8+25GS]Ac#2X
eF#Y<\WQ6alDhMpA'OO3c!1C[-D2H^XW3G`l=um&+ca8tnp,:a%+DZX2Hb<%=`O7n'i(m&$a
3*mR'1ZAO>3Ks>uCOf6h(a7BL3-+@Fq.Z?0]Am8-<!Ers(DNta_rdl^qKb^f&rJljL2>jRu4W
Q&!irX\LWHsf%OVhYj7*.Ih@H@[`$Sh4T:90b+9m8@]A:P]AeCjh-SLi27:6*4fa"WR\/EItF#
ZLU:3gKbKifXrUQXf"=4gC39Wt6hG4:k?f_3rg"Zh'HLIj3"BL^XWoJSpTV/J=AV.h5Tu7.-
e,#cG`_YFPCKrT]A:j\^r*9-o^0K/LM4;\NN#t/S<'[&\aZE0?UtD,=ajqg0!an%jqnUe3M.m
6sS\49n_>SZ*3hl9TBtAKq1M*k%#!h]AOW*#U5Wb3(MA?3^f-)PdTWe9WrL-g$5Tta\eZ:0E9
,l0p;?^Q5,B8t6DQ'#L$tmqEmH(ZMhiod<mbKdG$[1:E]A$+-@oKf6"@SskhESA2)E>0&DgrO
T:CPqJ>0[/H8:Rp-E@#>H1V"uYSj\f(=Q?\EC#S=Q,f%2_k,_Wk;0\nBI$J'NZUr,&:&%V8#
X3h:o.HFaT[(#Y`j^Q+QZ8,[^E;E-f7LL-O?D.C2Q#97ec$,5Tu9/YC#EbG03\`;6VL/ZJ)S
W,A2iE!Pf6tKqgn1"7g-2eP2N4N^L>dA;%q_=>LHFF`_3\h0MVOW(o1\.,b7nIJLB-7m4+t3
4!g'F^=POlKk'2d%M:(G.SnXgk$s<<q3F:X/*kW,s&_QlR*10^O$5".qXPh]A>,:uMf_L+I8f
K1&77eiFmi\P1($=C;&RbdVF@E2Ir/8&`cTZ'm@]A&qL8b_+)rGS=<"hHa;]A"07HZ7O0NV2Wf
.,Nd6(\k_98jqh8W\E`qc&4-Zu">B5J*3fqXVi.R.Dj?ADL'6uTK8JK*K4%$*s43OS277M$?
q7q>kpfK4d>8=/A`W9l00*Sp#*/MG_@t+PNq1bia6J=g%O">#`lA:&3RemWVlfqQSpEM/ZCI
YS(O<)G7#/+gh5`UK4!Hj:<orDMK0*&9_mg+Q12C7"n5t)tbTR#'%QH4(E^M,NO>rs<8&qds
ejj\ShZOF1QLJ(8>"8;%o5.Ni&JiWMB2\F/o'Z\"p$0Ip:EVTk3Ho>R2Mr"!b,c4k4LVQ2mJ
4k1K_SVs*rQ_`94LZgHCHVG<h(gK'Og<mh#BA,4OuBC%"^H;qtILO<3j:Fmk$Nq+<DHMpfF^
fP;*2.ou<,EAZUNh8+LMUB@`S)l1J^drE3J!k-l6[n\>$f83R0GY73)HT#Z`KQ/R@7r(sBPc
:hCLKaT4'pK_>2(CsCLO`)ZcLSSf:Z0N'+9Ghg=1N"q."sZ5b47@Lm4Pi[I"uHOh.!\N9iC<
sj0$Df?i=8V_h_(73"@/s#cY46IrW`::OYri^'`7+b^=bIBB&DsEIC!kcc+_jiqe"aPRshV]A
p<C>)2u@4Er#_?/AWMQ.J-(#,KA78CE('C"j`e_b9YeCQIQ7\$TV#Rl.0)OuntUd96!7WH\O
8([QkM;]A^[3,OJ(Xa/qhOP\[XZs0n&'a,6Tg%8qIWDK#5k<kVQH;2"oA#o!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="386" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="true" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx4"/>
<WidgetID widgetID="d4ed5807-5003-4fb5-8ccf-40d9a6d6ff29"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[grkhs_20240527090420]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx2"/>
<WidgetID widgetID="fb849881-b9c8-4ff4-b9c0-076208fe0ba8"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_zb,Key:AREA_ID}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="n_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFZh\'5&<#nT/TpTF?\0X`O[M#Z6?o#\=2JikW!dQe<o/S)nc&jpHaC!Kg!!ELXUt<\n'9Op
m(5es%!8YdBW/A=KX4%&(<L-&@1GQ*05FJ_T,N2Rec"qen4(-;Aj5IIb).e*$^UEo.44=Ba?
\+:)Gu@Ks%K`4U!d9euToUl@fa7$-?=X8r:0;m0rVII]AD?aSC127r5^4Nr*D%,]A8k=,t>k\<
V9@=;V_MPQlM93@!rin-;U2%AOFb<dDucg,dW/9c\TE7=/k7hY>VX>P"4h8-kH:k4Xm`K13f
%-`AE8DnVs_d.EXXeAE+D)7]A$O@UujD#q8TqARYi]ALkX`L*%_^Zbd<84^m)!@JLZ;1L@>?-\
?_CKCA?f>4?.a\;krO,Nm7Hb=mS;A"Dp\m#_=j!W2-O#33+L@Q=eK0fJ=N)f:`aIuP;BpP<H
ZcF08d?S>DQPsR5/KD1("tklSj=PDN1*/>1-H.?V[jop'Hu:ALpK@rV_rS(c`U*a*b@*kAHh
#(8Pr&A.+ls8SG<!Qp6p#bLX+6]A+pqq_o<(+Z\#LPkapF:5+#;+]A6u]AHP%)rj+91ipdG+b<5
4-8oro:aj#u--=VW%M9prt'D-?iLjMGgZpk,HaFJsD+>6PF7Y]An2pdqfrWnlqj"Gl:fNLDrX
j:5nWiLKpYX(l;knU,?1i7YYlD#*O9.F<QHG@2l?&j[mqF$Tu164>(19SJ)%1fr;VkBQ/$J\
3A2ac;^$SEQUDWcbI.a.Q650HoQYN-mJW@=CMNfqp!ZKG$.BO!9)XhD:51,tFQi$1h8I/5S_
G/'rj%C@3Nb#7nHH?<$N'*O`iQ5+m'BL&RqS=XN=&V9VZ")SLH(3%c>7kTYC"JJdjJ@Xf5*m
=:X80-C1">,IA#]A$P'_d$^$l30\#F>^r>#<:n\);tU;cpb6`4X1PK`;F1Qb"]AaOJWb5t?&8.
jm9tS?(21>u^BZL9-Ec@\%qHU@l@IRGE?*hSfM1Lg:.58oeFAG5q8)L.bEUK'K,X5AmI-/*(
rRFu7tkhpUkJN<PS'm2GuAqX/\-^4sQB^K[pGo,KZSP-Tk6\9Rdeg!Q2@CTbZIqk%1C_:ARq
:3P2KrO%a`D;s`#otFXGD#@0"OkS;Vp^[=e>oWCthbco3`Ll2#r!"YSIpOb?;2m5P#:F!S03
R=N$2Hbt!/Dp(5/t?_&CZP6LhY'a+;WDaUQC*Jj<+[fa?^;VOFnP6+9spJ5n7psKA?<=1=E,
7Zk;UmLZA]AA_mU[e(>`+4-)tDTd>E,p$G_/m!sT/N0OV\H!<WN7(bf@5J,t!$fa`H<F(<\h+
j,cq+k:)7Or&qfa=&EF#?&elp)Rk6M9S\$^jlFf"q2>&@).9n!sT/N0OV\H4]AC\ch8ag>F2t
KJ%JQOYhgoWE@).9n!sT/N0OV\H!<`&q@-,H?gK7A>[egR#8$'Lf!7?q-m,%d~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="FILTOP"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_dzbcx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb·" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_left" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
