<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_zb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024-07-07]]></O>
</Parameter>
<Parameter>
<Attributes name="pany2"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH RQ AS (  
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') 
)
, CWRQ AS (
   	     SELECT 
   	     	 TO_CHAR(TO_DATE(J.JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR J
		WHERE  J.ZRR =TO_CHAR(LAST_DAY(ADD_MONTHS(TO_DATE('${date}','yyyy-MM-dd'),-1)),'yyyyMMdd') 
), DZCP AS (
   	    SELECT ZBZ,ZBID 
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df 
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and TREE_LEVEL='${level}'  ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+pany2+"'"))} 
   	    and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
)
,TAB AS (
/**
1、从指标台账表配置年度区域展示指标  DIM_FILL_ZQFXS_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW, 
		     CASE WHEN (TRUNC(MONTHS_BETWEEN(TRUNC(to_date('${date}','yyyy-MM-dd'),'MM'),TRUNC(sysdate,'MM')))>=0 AND p.MODNAME='财务指标') THEN (SELECT JYR FROM CWRQ) ELSE (SELECT JYR FROM RQ) END JYR
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, DATA AS (
	   SELECT
	  DS,branch_no,A.ZBID,TREE_LEVEL,CASE WHEN TREE_LEVEL=1 THEN '全公司' else branch_name end branch_name,drz,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	  CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
	  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX a 
	   INNER JOIN TAB ON A.DS=TAB.JYR AND A.ZBID=TAB.ZBID 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+pany2+"'"))} 
)
select 
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
TAB.ZBID 指标ID,
case when instr(TAB.ZBMC,'占比')>0 or instr(TAB.ZBMC,'率')>0 then to_char(DATA.ZBZ*100) when TAB.ZBID='bylzgddzcpmc_20240622191229'  then DZCP.ZBZ else to_char(DATA.ZBZ) end 指标值, 
TAB.DW,
--DATA.ZBZ 指标值,
--TAB.DW,
TAB.MODNAME ,
DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长
FROM TAB
inner JOIN DATA ON DATA.ZBID=TAB.ZBID 
left join DZCP ON TAB.ZBID = DZCP.ZBID 
order by cast(TAB.XH as int)
 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name, tree_level from ggzb.branch_simple
 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'"))}  
and branch_no not in ('2097')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name 
from ggzb.branch_simple 
where branch_no = '${pany}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_fzjg_zb_right" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT  AREA_ID, 
			ZBID,
		     ZBMC,
			case when DW is null then ZBMC else ZBMC || '(' || DW ||')' end BM,
			CJ, 
			DW,
			XH
			from (SELECT 
		     A.AREA_ID, 
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			case when instr(A.ZBBM,'占比')>0 or instr(A.ZBBM,'率')>0 then '百分之' else ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} end  DW,
			A.XH
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
		) order by cast(XH as int)]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="bp_jyhx_dzbcx" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="zbsx4"/>
<O>
<![CDATA[bylzgddzcpmc_20240622191229]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
/**
1、从指标台账表配置年度区域展示指标   DIM_FILL_ZBTZ
2、关联指标维护表，根据指标ID获取对应的指标名称  DIM_FILL_ZBWH
**/
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.ZBID ='${zbsx4}' AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 	 
), DZCP AS (
   	    SELECT  ZBZ,ZBID,A.BRANCH_NO
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df A 
   	    inner join  GS on GS.BRANCH_NO = A.BRANCH_NO
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
   	    GROUP BY ZBZ,ZBID,A.BRANCH_NO
)
/**
展示指标值：IF目标值(GOAL)为空，取当年值(DNZ)，ELSE 完成值(WCZ)  select * from  ADS_HFBI_ZQFXS_JGZBMX
较同期：当年值(DNZ) 比 去年/上年值(QNZ)   SELECT * FROM ADS_HFBI_ZQFXS_JGZBMX
**/
, DATA AS (
	   SELECT
		  DS,A.branch_no,A.ZBID,A.TREE_LEVEL,branch_name,
		  CASE WHEN drz IS NULL THEN DNZ ELSE DRZ END ZBZ,wcz,
		  CASE WHEN  ((NVL(DNZ,0)-NVL(QNZ,0))=0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ,
		  CASE WHEN  ((NVL(DYZ,0)-NVL(QYZ,0)) = 0 or  NVL(QYZ,0)=0) then 0 else  (NVL(DYZ,0)-NVL(QYZ,0))/NVL(QYZ,0) end TYZZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID  
	   INNER JOIN GS ON A.BRANCH_NO=GS.BRANCH_NO AND A.TREE_LEVEL=GS.TREE_LEVEL
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
, GSFL AS (
      SELECT BRANCH_NO,FGS_TYPE TYPE FROM GGZB.DIM_PTY_FGSLX_2024
      UNION ALL
      SELECT BRANCH_NO,YYBFL TYPE FROM GGZB.DIM_PTY_YYBFL_2024
)
select 
DATA.branch_name ,
DATA.TREE_LEVEL ,
DATA.branch_no,
TAB.ZBMC 指标名称,
TAB.AREA_ID,
case when instr(TAB.ZBMC,'占比')>0 or instr(TAB.ZBMC,'率')>0 then to_char(DATA.ZBZ*100) when TAB.ZBID='bylzgddzcpmc_20240622191229' then DZCP.ZBZ   else to_char(DATA.ZBZ) end 指标值,
--DATA.ZBZ 指标值,
TAB.ZBID 指标ID,
NVL(DATA.wcz,0) 完成值,DATA.TQZZ 较上年同期增长,DATA.TYZZ 较上月同期增长,FG.TYPE fgs_type
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID  
left join GSFL FG on DATA.branch_no = FG.branch_no
left join DZCP on DATA.ZBID = DZCP.ZBID and DATA.BRANCH_NO=DZCP.BRANCH_NO
order by  decode(FG.TYPE,'基石型',1,'突破型',2,'成长型',3),  data.branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_left" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[	SELECT 
		     A.AREA_ID,
		     P.MODNAME,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		left join DIM_FILL_ZQFXS_PAGE P on A.AREA_ID = P.AREA_ID
		WHERE A.CJ='${level}' and ( P.TABNAME  = '综合考评' or P.TABNAME  = '经营概览') 
		AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[8010]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH GS AS ( 
		 select
		 branch_no,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 	 
)

  SELECT  ZBZ,ZBID ,A.BRANCH_NO
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df A 
   	    inner join  GS on A.BRANCH_NO = GS.BRANCH_NO  
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
   	    GROUP BY ZBZ,ZBID ,A.BRANCH_NO]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=value("bp_jyhx_fzjg_zb","AREA_ID",1)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(_g().getWidgetByName("cardIndex").getValue()==0){
		_g().options.form.getWidgetByName("tabpane0").showCardByIndex(0);
		
		}else{
		_g().options.form.getWidgetByName("tabpane0").showCardByIndex(1);
			
			}
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("pany2").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="7417de1a-9ac7-4ac7-8dae-41504a9f711f"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="gs"/>
<WidgetID widgetID="731fa7bd-95bf-4422-a2d2-cabaf5b1b846"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany2_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="pany2"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="pany2"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="gs"/>
<Widget widgetName="pany2"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,152400,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<a%VPA.KZ;]AU!M'N%,aMF'RZ!JD#aKr.0jdVBi&ZnS0e9#e-a7CiUuMNJgVTd@o2&0Vae!C
BXMBE9;'":.TeMf[1T1Z._MpTAYb?['UqX;B`arKR9I]Ammf1+,IH.2?)jUM$!e\!^BsJ0t8Y
Tn=A@kpCH-`DfB"\mfWd[Dd>61A/U,)Rb3tklQ2o&Z0@AW//Dc>j"#DA7sA:"r-[Lklh/a(<
G_-:4E@.@@)!<"`U&2!lD-`4Y9.WtL+\i1UR97'&HiN7G:5f/F0;FAB;oOCM4)i9%bD@&p$B
L[!bZqfPaN(pR9d5@1%a$9BjgKZinZg)r2epe#a%=\I+`c\B%C:gS-Yf0>Fe*,r#q&FVas#s
n=qhRnBn"$-9Nq/B[:Vn)UCi5Y6p*NF^@4Oj@F<X7G8Cn:\u*f]A6hioEaRYV!m);u(`9@IRK
u,LF8:8DH%3tXbdY?8.)6)j+7ISF]AR'L2^T$\%9a;i%J,At!.D&1'k4e8fRqLoA(3m2F2=W0
Bq!?#V!$Qmu8HCc$n^$^:pr8M/G(rFQ>_q%*O57e"5"<V\(Nug[ENoXr^?Y*"F%*r8^EAXp_
:/kKfQ7W@#QJ:?^3'-9UK*:tPU_6\!rR?'eo]A=Ogt4N`cS/.Zi;[&6Sa\;=3V1t'J&&7kE,O
,;^j<BtR6NHG/Ne?sMYJP<*M)%kktI[+HH+d?g$IZW]AC4m)7FmH%DW"b1pWn+LEZtjfdUpm?
E>/3A+!4q<fRNkpmG(T(rf^eR<]AW*,i9*YTYTe$5:XHFq)<LO*5HPf4;SX!mqNEHfAad:?4a
Hm>ZcX$I%5Ue_7ot[qQVL'kI8O%uIKI06T8IMYQm=_ZP43W?U%R)V.O$6b\3\$mJ[8ETg<+X
GePGZ;+mMc-A,`1pg$o=,Lp16PlbI,9&m6OK;d8WskHSA"E=$-\'r.LngMOuIRXM3J27*r7F
_4DugKZ(os4`nkR.5/YgR9,$A\dtSVDn`nLib:n4d6VZ"o>h]Am%S,*PV^b8Do=#Q[4ArWrVZ
ZbVdL2\hdfB#Oljq.S$1W:oqQh+Eo4P1E@qJpQU\#C(H/]AQb]AR&+[3RT&)jM"(BpoHRDPH(H
*7A/^W0qb:E=Sm^:D8XAS9Ou!_nej@)rRdq)8luUXiLBMf(BUo%/LH"+#)9bQ+n3[S)-B-ce
g?g"MLa<X@[O'-a6d)<FjbZOY(A144BaK/)-k_W%K]Aj^mO/gX)[=mirSMtJ:l"h+4U_V\.NX
G*cD.M`11F.20qtfQ`X):#0b:eE)Fr*\.4%0AM&bZ4h:A"n=@$%AX3+\#(,bo<O3q1n$lQMW
DGXYLZ6Coc$9L]AGJ%aK)lPCN^t5/gkbt5Sf;[jeZ%S(%>rk&eP*]A0?GEET[29\5f>E\"[:+h
[lrs5=8>,"""GeW)+VW*t<1sJ+_]AP_QU/X->mJc[tqf_#AHgIl+Sb#Y2]ANFS#`_p[1\9&0_m
/Z5`S0bmB%@P>%[(2Dce)bJ+>'PjamfUf@C<tWj[GGi:f8V\Yra;Z-lSD7@3:*r@==kO@DX=
m)o:2$Ic#';sap34<\Bj!ZhY-(Dcqpan7oK<G!8f'DiJZRj6K3GQ(Y,/i%8X_&t5Y\@-L-"2
<6$IOs#"M_JHRoUYc#PT!)56G&Pp_,%N*9`.$O(YlGSDYiG)k@!C%mkkkgukT81h3&HrFS4Y
jU2Za6^TN;KKS$_MB/a]A0Z%dL,W;SM-hOe\+,=&o9QB;9e22]APa)Nh!A9f0n\0%>equi9W&m
qJMs<CBi',ZSji?f5pM#_Y"5VaE)Jj$mbUB?qrmm]An\RPJ)WY%&(VM/jqjqQo6"O?P/n`i!r
/[pkoRXp[H<;P>N:^o9OV=*`q0C4s7_\QQ)"uk3Qa.H[a2=AK![;;8rk;4>[SID)tf.I[D'"
,@omN6>XR6:eYcd#m3?LQk*Xa<be*Eq*MJt/R_K=$WG*S5HF+7q57_\:sXh(,L6,N"q0Dc^G
082`RO?LgSO37C7C's#15#S'327h6$Q^SPSYUK?S,EL(DBX5'=D^KD(*$]Atq-)cWo.6nt3>M
Kmtf"7BEhB,911H$pkh>L)_+G=[sH./`Peg:UC7D3<\)\ia9"3imLP,krq!-F*+kEeh9QLQq
8N.*(Wg[OmFn^rPm/ip\5inVLIoJ)XT\MXZn<11#GdV<jWlB@37Z2</?5PrCGteX]AeC$W_UA
LGW.5AInN\?d@oC*t#fW\I@8#L<FiKi`loSf23+l2o\3#Mm9ppc-14=L<5;]Ai)glEK9>lo"R
tK<5'Kmm(I@T278,BbMUTPMPI]AHsHhKnTJ)-$QK;.)d<%5Lue0>nV3P:f'Lp7[k420mR;o,n
u.eG6iALcZle)8<t2A*=b#/bjmR(9WrST<ATeV;b9X.,BAa9ZnuX(dI'&pRth)^.saqJdmZS
Ih$VEu"As:.*fXjYcus"aI2^1jf,'-$5^9Huj9\jO^toUc6e+'Kg1j2qCKA*cl&"Ys(6`]A:[
Ub_Q`\^YH<m5pgkIYfZM7o`j@S8p7fV:[Q/#0*eh"o2:g[LisiE[ZQ"3:FjjG9Q/ck'c5=N'
KUq&^+1Q\8nCMA`I[ffI2j;D."H/9+8\Pppd(A`ZAA>o!j%j(^#rchS8o6d(.iCt,[g'#T.I
&P6E09d6rh\k&^YD&^U@_ENi2('%^6H2[.+4UDG<KAII^'S'D%Y!1A5kt$k5F<)`c@"#X$@9
5lDR=Ym3F*C+IS@o)mQAAb98o)iDcBQ!R*m"*K_-p<oO#SZbtGRUrg3Uh_rBMjSoA`!*agXL
Y;Y6`*WB7OWn-BBJ$7#nZ!LUS_=Q=!Y0BEo`IsI1mHt[Ol\2!dRl*K!m-9t)TVk6a,WS/D"F
\R"UCBXU6dX.9FJCq;)%a`htb5P58d.:ZnH,UjAK8tOfUe&VD[`(Sn<]AGY>))4qR+*W^cSOJ
.WgMf`5jSBQNr^VF`rSg1`PWF`-OZ*#AZ6*@8!H]Ap>3b!?*Zpd=#1X^P_$&$.9G?NH[g8n1*
cN;F#Lh7\?quMO]A2Z5i,3+*?X.K]AO%K5DcMTW/5<+n<VWhuY\FsOk5$QuGs5AO=ZR1Qe2cc^
s!/S5gX,@srnm<Q6*#.(UiM^+,E7jUF:r*+&m6=1b97>b5@7rCh>JE*A$Ze>mgp#M/&Ts'II
Uu9Zo4Ic)0q=+1r^PgT2%#N./O%pS2)+U::rK>f<okFBE"FDgW%D802/Ym_oB,bg6JcY<$CK
iHGh%NmQZkB<MCJij90Y?6%*H&9F_UGGjK='p4T4,"h'66crtHl13m`>:1OZFUEks6lT&g40
qiNOh5+2Fc?Euk[+("DQmN-R-O/<`mkcRnbrqAHs:=sY^S!YQm-,F#?YHp#l:^k)1I:d*VL\
s-.CBUokrVV.?U`eh1$=jV!A5jK\<tkMXLJ1.AObT'7]AfHp?F^s9M/s)6d=78cM51AT]AY4$/
U"h9.1&pP;"r;<8<@Qe]A`2kn/IYZj7c)8/a^b6a_m]A^sV8D@D?m-c>+fif_[#Zm*W=n]AL!*9
ONpo<;9.-]A.3.nG?pg_L`D'c+#DMOD(Ws@b]A!oOhb0dD7uS`nC.3ShMu7N*%^H9=Wuk,JQ-8
3TkmduEh@uT7VB\Tq6H>5.Poj?Tbg;`7>b=d!JBI%EY'%hOiu`D,qWA(Ejj&2Fk]A)ocfCSd"
X$aD\g(BB^^Wt$/g/HbQ<</M-Su_O5pkcTRV(MsqOV@:>6lXS2%aAS(H0.QG2>d.5Z6_i8Wl
RdB&jbt=l*_/`P_o(%+7:/7'omiR=EBk=p_N:<@]AuT><UJ5rK8PtW9<Q^%E/r^2i=c,"Nc/9
UhHUtqqrMga4N2Y-b5=,.pHS!?YFRau?*Vt$gDM%n&_uLgYrlPYCT*2D+b?H%\N;FWqgP.s@
quk810UlN=H(l@YKfToHM)>SFoLXmY]Aj'D-sbK5G$\F<&7[HY7t:G)#VGT*0l,P,3g0bcGM\
eLPNj-`h9>UKPoRTBGLCmh)IArKY<[Z6\b.po_d%)*&f'%C8mqq3\jL0TdPUaB;nV5g[>GW,
@c-kO?WYfa+bFg_XAX<;gmAUiGtS4`%Rl"7KFm$jV[U]AjXEC+m5XhY7XgZSd'q;8%7fc,Wq:
K/<c^j&/<[HbN=.)=&?b6g*\OijV@KZ$R[Y;kE:R=kg^I*)!XNEM7Y0RRBm?No5/e-ZKU\UU
jc'GKAQH[1q.Y)r9U@\]ALl_,o#M5rTrUFX8g]A!=/^k_*+f>#uE#3<R(eFup8Wie[G<DY?CdR
8-8jDAroHekPM$N;*PF<udn#o/32Xf4Zc?h&XXF,tUn_lgGF+8W$&)T8S8sn.G/#)!td<KCl
*8S76i6?cuK>Er.TiAY\rC"Zbh'V+MO(Zi&-j@a$(gRrto7r<+0k<Q[)be*&;'b4_t&\Ank^
NIs;B7BC]AcPFB"t\-T"_%.n]AIm<mrAIKBbfQBG;_25s`@J%dJI4K5?KZdL*h0K7TiY!Mb&4Z
dC(mPe7/::%6l2nj/=*")kO4LnUM="!S8P+jQ$<cjbgk-R/lo9I\pHo"UR!5[SP,!"P=qu0h
oWLJe-FL*8mlpH:Y8P*j4_`V5e"MgbRCVhSCXNMR=rm?:-pWN(>'>E#QS[<bl1"=6.pE`Zel
Ba-'kMraSPi-N6"7J<6Whu4[X1nI$e3'\I`*:kJUc2ss^Tj(1lq*Z&(?p!0a>Qb]ADgT39`$N
6*mR0p,.T.e,2MY.1%1QD:F,/^i1[Pmi9lK>do@DO#mT98JT@A2egGbZ^?eVU48$OH23prH-
-+<<?f(D<ko>u,$H#nDj>@YAGi2gG@BBH09h,69;LkPJ*;21#2/TpaR14Dja6Y5A`]AoW!sYh
`ljSq#7XWKP[:HY5sI(*+AVg6%t)/dma1\QT#5@s"kGkCq1jmqmLP]A"\3I[S]A>;'iH36+IX/
=:c?94]Aeb`]A]AMpZe9qY8O2Xt/XIuH_$Q@^b/HuMo@Qq94(!R,gMY_qIh)Q=J)A8o[4?KZ,iM
J)u04fJ`.F#JIG>5Y7)O'>8UU"_B.WuE9,N4Va_!PhZC>8<j3m!uLrEttE-CL%k[W^_36W+'
TRgmo:I`TiE5ipo]A$l3K:to19=Nd=UkYb+@C5&b,1iFV%,deOE]A67OVJt)t<1D=mn[TDrc<t
\e\jkDea$,G*4u2%AYt;Z7M\=c`%7$hGqkXdUDV5csLsI%M_AYoJc;^(_oaN8EY\BrJPeVc-
s_sIa<kbVPc#j9of\24eo;pBltq,30_tY0V2CY)D&N)HSC-5L5dELBej$1\4(=aGUVEq9Lb(
$8DitSp408J1Lfp;/)q*2q<8O=:NJOic6V+"@\RW^`.Q.984u6ph*Kj/^[IqX;N:)1d.;AOX
G(V;-]A]APeh>`L<C8FjNGQlpJo\5j\8?g.>gm>cim='ErQdIp:G5^&Z6>?k/`j27N3iN&k<pu
%tk(PeNRh]Aac@Gq!(%E8-h#,ncR/H01J^4qd&nA4B"D_"5Ff,mM6l/N5Ma.`V-VWBiD1;u`9
DX-6aE,'M$a_P@rY"?I0i-ZuJ2!J#>1,[ME%7k'&e`?\"U*$:5pCm2kT]Ac6*bMQUCW&cA*]Ak
?-3<-`"6Y6ra\CIGWa5S]AfR&/4Zh`k^lAq2*<2d'ojdF+>h&jQbmr3;qXLj`P_pEBV@ucJe,
@OWE!^pi`R[EAYiC4Tlu4-'CP1Y^&Jrp#H&a:K<9m[&8+V_]A,MIeRe]A>cj)YqH]A\P\Y[8p!r
Z4sJXA!g_I%0%=E(GmE:e84F-e_uJ`LUNQn]ANbU.4^DcPasLQ]AaDRrc/[MpO,-um,e1h6kh4
F[Qk<75@r&)k[![[NYqBJ\!NHB\-@l`5K&LEQj*_O&H<3W>T>pu9,gel_(W(At8L1XB[1@lQ
H55rm*8PHpf\@,D)3'7OcIV2G)%DKMV<nT0'De<*KX;Bfp!1$L_8;8U@i7!f70.stfIE&q@=
\^h`4Gr#!FuVf&^?8_HVs1PI-'qg^/9g%DG*J?#1M[fiI;R&^C,rN%8_bMWY!$r9k!lHaYDS
+"3?@-`+YG]Ae>Ue^(QNKTBSi>;j5>&OV'9q\iAN:5/uD6-C>.nI/(3;7RnkKX0:EV7,D$pU[
=;LUn;f+c;M">cS6D8<g?;`gkj.h@PK6ldg(\>;k?e+l<<EMAIL>'bCmg4IX^>G1XpX"s
E7UhIfE4+oe-n<D_%l-pELn8!D`S65U:=uuWa,&Vqh]A&DAk#tT.JHiQL46Gf:FE,_A9jg(fB
M)o3H;U"(]AN)S>sJ`8c^rASiZ;$O8,8s7;3c/ZH:BmsV1!0S#(HF_`NE*Q_g<2ig[7.%c=BA
(r"ZPed'X-oBm$NPf=k8B(C`9!PAh&8KPiG_XrQcGm'XIk/.cB1\7!kFh*,tPuD4(*$;l0G*
7Ir&>->NP2`Pc^NRU;[HA,R.)-nMMSM=]Ao*D&1G^FS3-[DpYTuI(]Aps+AAq?G3Z[_K^'N4(g
AVB8)`argG7BY#.k$@Y3o:jg^VI=GLp2/?YUK*U$#i(FjbQeFSYRM"+[9)"J]Ae4D^t@)8CN$
"oYE7834r'0EL2!de0I;\o1nW76GF1em?^487Z6e#=&)<W5o@RFR-bEqOH`c_)n?[uDPi[m&
b`YJs(Ig]A"_$2)!_mhI\;mV[ZYS1H`O-Z$i$:*]AELW[i7"5RT4jg)!Cq*.qC\!rIOq7[:e1q
g0$ZY>9*Yp\$MXA62o^sS:)W)Gh).tuAVmCeQT,$QOQ4,`FJ%n5aB,\sP`nZ#P_6uRO&_OQ*
pnO$uH76Q:k(*srhp)23#3[T99PB@.ZUC?L[/]AE]A_$;dia-Xpba%'WBSFXai+*1s+S.p-BN<
Gln2bU>kF%Ub^#B`Kg3nY:UCk[=LGC9T=W.EF6JbRf;0e?ViU^/j`ZXUmO/)dh`;2/Pp/S#O
@qq3c_V_oXY%'uU0XqW$6+QV%K2+[?NbBq[)GD,hTcr5'N/cAV/\d29K$J7KVREV=&p,KdUZ
JU>n*)qJo+FHG8YhjkOVh_RZd"@dB6qiNN,7>mY0_%>6k'9cPk50b?6)m=*hfPWI?^gaTA0_
)8`HU[%q.kHjp6%Qh7-+$t62VW:j`@1NGNWHi_;:jQPA>C$lR.2Y`i:?R4Q)t2l.]A8s0]AFq!
h[Y6j)EXO:;OkO/iDkRZI]ABkT%n9sumpU!4HB[#_8D-kdfgU]AjW<rs^t>,`"82m'CM5$k]AYj
o\);G'8Cj8uO?q()0_QC7X7jBpG>?jGk7a5!k+:A,darE*MBb[mq2>##7T8--%j`fCZqZ*]A^
paQo3&%d*Uoc";=@(/r'c\%^Oid!"PDoJ@9IeX)7SuG5aO?XWL5YL:.=bbl&'O"GXqZa'T9+
U$f2cJF'Q&C4It'3PnKd=IG^?=rg7>=u]A[gI!TM#Hrq:+DM'HYL@.o#`4-LVs1hj`+hdO-aZ
.P;=scR,d0\"XKBtj11mb`&3.WR<HQAe>,k*lr#P**s3`-$nfF(/&oL`M_O"cUEf=2alCS.;
VqB3N2l$Horr"JgS:sOW2HT&d_DmZ1he/0ClDC'[DLnZY>(6;jV66KDHI0G7kqm0U<QA,k4D
J+nbLeI0YkK#[_dHfKOBdkMZIQ"2_/3]ANNIR2jNpo.&kBih?FRPBs4n1X&<,Hg1n@B;<A/^7
BY.VKgo%eP3iiPOo\Pb$f?jEXXFP)Bs;P.4!.qu(0b`e>5sIC@;J`TaWtaZF.\:W.V#;^gK,
0!&;VCWlmYZVY09r95YkH[/93V6*:XS4-#Wqn:Zj*1]ArqnJ^ZA@3XZ6.F"gb+mH\Y7\A>9,Y
:!i^;("uW63%L;%ZZdF[+\#pu3EX+i%%6#jOA#e'f=4aLh^L-qEt?a>Q)[-U9OsS":eO_WN[
GbLI^q>0pM;(Z:?_e@lXLY/+1KeH''&e!^f;@o'oipQo:17"U.O_+A(sjs'sCR/#8s.:sK)k
=%ot5]ArrTaGN_e;>L'?dZq]AaKD<pY.n7K+(1l_:@\p4H+6BbpRus2jH%+dO.6gqn$D*hrI!k
%;f=3X``&C%^S^TF77G+Skr>jld$*7P?lLF%joT\F\MmC]Ab5e/sBm`T%TiaC@)4rBJ3/ooQO
^D*Rog-.',&]Ac;:r]A51JGKf&*j'@R]AlDs5:S!$$WL/hOZW3<-#WZH_J(:b#2Zh>W_)(@%Ma?
o&WVAC4=Fb,e0$^@9G'\Tc#?"A0>/;JDB5O3;_B*WpqQ[^%eL,o@&!)'Q=)Y&C2!FC\_s6r;
$GeC/gKf.4t,upRK&b[P\E]ApmlkmHn_XsF`YUEE(5IH4R#FR4;qfe2ETC?X$pm[2U2UsS4TD
ZA\O@\%@iQLu*1P&s89_S<&U]AfIknG*_QbUoT'VLMWRJ%c:F)P5FgOqPMPbPZ1T_[AZ-\AY]A
::GahWrC5M)$6DL]A"DWCWO%sId\X=4a1P?=kSXI4S*O>Y9m#Z7CA)/MlQ@mJ5.*@C+BF3Aq(
kkQ$A<97*bo7?1,hEMM45303)ZEO;A#]AS\L):_YKJG\Af:,gWIR3;NG;hu:HI/T"e$;0!MaD
>@L59m)@Yet5I-0%fhV:T*%ep%1.9i<&7l."7(JccRZrR)l(RjF6d7#JW^ks.D,pDDA?X-JF
0kd6@II7:6iq15i-5!4XBZA\!*[=h_qI#Wsp@QNIe%8,6,&cK\9GXbBJ\mspRjKd*^KESi\j
QDp0QWcUf*_>%2X%f5>V9]AQ3peTBi)QgId8-?g7J"V6NLDf=o:-GDdoWEXGd6IR*p'=KRW+[
VGp[R*'e?a6$=$D$?C>L@:<[@F6jK6]A+VugER,Vf_K2^%bpe[:U(\;Wg)eO?EEG4#4#>e-2I
^m^5Sp=jr-,+N\h;SM9+*#02EbY9IpP3fA,m.HUUh3iShlP\;@k7J'V69%_:DsJ0!8dq1_+s
o#s)C7K+*QIM,`slk-3CjPt_s>B]A*o.QiOOaDJ5QHhKkrgXC"*)l#=k#qhlLVFc_Wcb$l2N/
A9KaP.+u.S6@qdS8W6m%kRFeA^PQPJUQ1m]ALiMDQ*qYdsih/a"%"Y_'Ddsf?1;DcQ5,"DAhJ
M\pE]AH?suCQJjt@.h$NfSB0eSXGfe:_FrSZlhH=#oZ#[mT0LceS/2E>KK&l/1546]ATOP9@s"
3]A?1q@>>nSqu!#&EIVB:U=k-%/=bdj%?@71:!+5Vj_$#\Ga:8$#R1QdQjfr3F_Js"BFM=I]A3
7.)CeN5dBh2<iO.dPTFIncs.GZ!Jrea+M`E9X2=:l%q\^JFM*Epo1;ZQgQI=Tgu"bhbHBC\9
Wi,))r=N!G]A9O+>)9tD1EjleE^-hD">.G+PW%PBdp,2AI^1[Y.L[]AB?#=<'*5j\L(Y=uD&e'
.V>#kCr*d]ACr;)\BQV@k6eWEdYi[_8JOsSt0<K%Pg#%stTq:AmS<sLO4>i)nPQNs^m[`TC<C
Ip0S:K^+L9j6,TdQLFYG2/c"l*.lU#+G`O.=7FOf>(`D%9SO@n%-m21,$As@IGmhE8k-s[!g
>`W]A%2/rEerkpZYpqKDh!G5e:tU4"37-GPh47cG%=KqdEmD>8KgK#*!cKqKp,a2^gi.s3k7c
J)&m,bUosBNl<$'eQs38bQ/oBD7>hYp'4FTC.mt@X;+.)a?eIKn5:$fAI@++!RsWB/2HE,E$
?W"%U*2TjD/cTG=#"]A/X9OF7PiL3+jukmdVM<NZ7&eMmH,YG'+V@&?U(Hr4d4lU#R(tYkji0
d`,uAW&$+@Ca$:r6qV8!C_1Qi.l+i*gf%.l#Jm^$s4CM'e3lElUK,=U8+Xs]AZl&PskLSF#2G
7^78"Q,aEVNo0L;Mqlf>j:o'/4o:dL`8eM'\EF*MTS^E-2?\KUXYOWG5pd"Wo_T?$MUbBgGg
1_3U1>H!6Mgd89$0'#Yd%k"87A^!dMWMf*UBWqXaK[Ek4!/4ZU]A1Bs7[1rEV]A8>b8(iZ7$K#
//HOoZGF(l*gnObXL@m76NJZ?35$h.\iH<%RD*>"]ADu261nRA[BQa9>AoQYQnVt\aZ$nd0"H
iEk;^R0_,-_Vn40_eV*?png+iD=S<\Ba1&=t`b,rqDp+\N*=QQ<>4O/VAN>Nfar-#,B5r/g+
-`mEE<`:2:`Ai.6ooEEn]AZ2XubJ.b,@ZY[&Y5De[lY[8rkl5J`:ERm:m8p1B(-9>\@."ZRA'
'J,:9&t3Y_RD[LJuuL<nUtJg"DTCP8`4EOQXc1n/:MbZm`n!FckP`![(<Ab<']ASS)-Vm5LX7
b/lnK*m?SC(U.-0!,h=kn'E@hW4l=C8,CJ!A?f@tJTMr/<i,g^7$0e-M]A=Y*SDH&L^tQRa4t
fS*''POjq4XYY]A!Y1'@CkT)5.4&V5P'rA_NH-s,cep6^k!</i2#um@rq)Mm,YTBa:,BJ:<'W
7`pgmXgp=6mC;^)3-hKo9Q.c;Bj,f$r=F]A$n8&[H,OTm@3sg:&le#qZeWmLPVY='bCp)9]A,Y
88Dn,I8TIFf__3V>B=@8R(UH.?G4.<uTq'p#9fSC6<1BX/#U,QopbGNeU2nA%G7g:SHP]AFQ(
XOOpDkiqI'agtSC$1?9E\dpbAp]A=s=.i4p-)t!<FP^8&mkaK%cb6s0%PNeU]AriHr3W<IeDH)
]A;G1:WsB/SSCQ4Ka(LnE#9&jp;D6]AYPRY-u+U=5s/1p`]A#$]Ah90#LYBV7Y#gnd#G-<h9mK)`
DZ0)*-^@rg;`HYdYbX2WJ0lW4+DF(2`W,:T"-*>u.gu]A.gAO6M_T]Aohs0Dh,hk#Z.OL^>OYI
`q'XFKdkJUu5_OAUBfpX6%iVr+;oZujQb`sQj;_(FuQ2b0TR\+T/W^SQ8a#e\XD]A6)3dI_o/
<W5e$QbuL$Q?58HDN;d-KJ@g$;Q<YuQXfSBg??d-c6nQ71qt'ihVH,b>Sf_q#^hs(mDMD7'I
H).B$pQ9+lWZ/\C'"J.TDl"7<4rC)eRo,Ph@(C3VP!\384ih#J_t]AT0`"IcDpLEHO]A?CBk0?
=CH%+6W=-1s;Z-QNU4(WrQS@sHZ0fV@HL@9$@$&KSbFa$ALq,t(5pI>78*D`H;'Q\]A*l0<ep
+=LcZnS4&gY1-R]A]A@.Ln]A^2I&Sd=hej<F;SfJl;&g5r-i3#8cCPA[j'i$XWK.\7Tl;I!-4G5
6<9>]A*Zcf$I#sX?IsN"s4$[^mVs82m@FBlu)g]A8uAQkk=2O09RCMIg.(8iXERY\K<ajM>*CA
4a]A4-jb3/Jg-O)a<:%d<iJ\&D:4BKp,I,:hhK'SJYq0cgL]A6C)r.o%rMK-9A"JPJ@2.>_sA(
gB:W^^q.AKTdMd(miA,3E\\OUGF^I6\/1,D#cTAR7gOYP*fSeapq>[V9V4BbjNq>.d9`*@W#
V;YO]Ad)4TRXJo#Pq\EkhMlYUq1EYgFD>:9G^<F/QF7N.sj8.<j[1jQ*Ot=<hh.9m%)'+c)qg
2?W]A_:4Z+#6uDeqoRguB]AGKNT]Ar-^4/nAMknasEpo5hJ$F_mFp"(?6/D(U"f>"WWAKAQSPfC
_R99#NJG#l3lj[W""9DBT_"nDCE:X'E8oiK'Q37HI$'&mSr4rk,6`f;lrnR\%uo[WN"Ps.l^
aM5+G.5DiB]A?GH(ca&6PsE55%PM*fU]As3VR3b=lX2GZHQ/^i.7"3\*K\;`sZTp:c`ZJo%5UE
MO`'O$\!fM'R=jG;oKJlaLeqFK?Jb7YBs5+Fm&p."e65(mirYi!I)+5H*$5S[E`HS,XYV(L,
'UY&P^9[**62+I[9$1m't38VE_Uk!WKDG0hZhGDGXJctE-.OK/`g`=5Vkh'*$p(MXH&DICSr
]A`,]AaTUOD9]AN6+$\Z&_6C=2%@naR_da:.l&)4R4L^]A[HEe'FOJRWaQOahEIFQf_M4LQKO)fu
DtufZ)$C$HRn_HMu.JORVKED![BsJ;`5ph5tl)MA^SuSc/'f[#,.[R\Fk:C1)cP12:rG55OB
r9#QU-SoX--Y<cig1Z#qX0/JDUHCG,t%[c9T?6#i7g@\!Jq@$G%`t:?S1pcImN0LqI$7KFA^
KAcSA7k$es([AN[Fq?1dC\b37sbp_`@#\9ACG#_Y`HCYrR13@A6`UjjrW(\V*"L$E28VS5"Q
LBE%e;hi5^(DORj49P=%s`cE1PO-V%hHe!m*Jk.4=J.*hVAKs`Lsl2kb%Cl[02qJd`V8R>SR
KPoeA;Aj%uKdc&o9:5lmB"@i-cXn3*Q'rH+;<c[9K9([r3b)ZQmZ6l>TqJnWoh\`62+`6@21
I'%nQQ<q[i`VB]A62UcYt6kiPCj>RgQq%UZ7i$Wc,6c"jJbE,h=V*P1W![]A&0:13;@^fk9!:T
Ook^=J\Od."3]AJ;Z[;h>m>3_`%<L'SQ7`@[TT+4dg3AP_/%Do@iQ`8ekN4Z?8H+m-OUB%$SC
/E$"ZbhiV6%9PCU,(WTaSe;+7le'@VBi70,2THq1'a=31AF'W*S!Sb,_Hk)ge7PJ@JO=&`I>
h,`]AQttZc?*)p8oTEVrkW%4"JJ$eRRE=`_;f1Z0bKI?>8BMMdpC'/Cd[j\+rOL#2/rk4<:,Y
aGJ;Af9gHMd&O8Qn(+.8Sf4MocZ722L)Vm+?-iG[#58Bq+*bkLXL!NF^RIpb'bGVEp)(QB3H
6B(#@t,#c?rc'4P/^WCfPcPQoAj+l>3LIq(YF]A,qD`Ii%_qSSj7RUF(2Aa;aV]At$V/aaN9,a
#^6^ji`ph/<EMAIL>)b6ndiA`1%>UYpVgXSgMb]AtY5Ea^67S5KNM!SHF?TmZml,lTac*iB&
Hm5_K[#"61Oe-i,)YZCiCV#C"'Hop$#IJ8?u_Sb[",?B&WUdEE^^^3n]A&Nrra!>K]A]A#jM12G
@.ZT^C=W90^q[`UWmi(Q;8KVDaUi6^b&1JbADir0-ipX,,YqGr7?iWZUtIN/HV$SI!i\pCjj
hJdg4]A8!SWPY?q3FEBcm7'e"O@u!"Url<h?@IRg'Ymk8cq=4JSa3js]Amq"9F^(RrsGI)q1mp
gotbl8n_W$o:NG1kI,aiG)_]A_A/*Qk20\*J>0`[PAXo=</j$N<-lT>I@]ACXfW"rhW"$"q6sg
8'%'uo1dk6U`du\P_eXM.\Zfi13,tQ9G\*)ihY@A5jb6f%N>@P</LfiD68Eki/V[CEEf)qO6
%m2oG4ErE@=2Bp?eW)ThYVJO(#SD`#U%Ee,-C*I]A6K.S,']Aq@8[%L7!_A1\F//eJ3ul<_kJW
pl+F7<G-e^EAlHqRC3f!VZd1Yi#Ft*`30VG^4-7TA9n+40iYn5CNZP#]AV>7r_58Th=(<JY[V
A!N`R::6b^Z)ERP()Js7#6qLIiSG%(G6N-dD*p&F@Kl\LX7>_bmKFi?ac>2#n!s?]AXc4X2+;
-"0)W'7q]A/,,7?.Mfc-Nh.jJ,L%kME@OfbL>G(]A"J;i\+or#.&f>4d@o3+fF"*_:1@S^nb9F
n?Pl@h-P0o\Eb?s^A5pRuY13Q@1,uqYI;rb.kOr]AS,lE7k?>4qidC46m\EL3]Ak']AdUGi*Nb\
iXLo)LIg!DF,NXl"EY?Q<G(!dk5GbUdUn:RK)*G0qDWB"BiEN`cp9.U(Fp&EV?O%aQ71J5_V
9R?s.]ATgTIIs.*Kn#00B-r#je6q'[#iL%Jf`U<7=jkX>knRk6&+=RE!io0/(:ASU7Cm!`T]A;
fP8K"`)pLGNV8FV+&,C*d8F7A]A(o6BiG*MWDn/,R?$pt%]AS[D?SBaCW.W\6>:^LE3&CIFqg0
3rTT1m1T@^F/ss09nR<K4G;I6'`T`$>_>UTrO2%_/%&1Qung**:,jid<aIKGW=KouTho;&LA
ZH<93NnNlc?'c'mAiod;oA&**pD>X)_(X*\F*]A5>]A0"S\JT4KnY'YX?/a/DuYRjYP7S:%fF=
+54L5JP4Z*1)qrE=.*Tj:Yu:D\Dfu1I(\1ni5DH(NWd1aK5?!m#_'5alQ,#!PmIG[V4HnKp>
N)r)?Gko%OXO__JJ61sT7>WG<M"i@Pd6_a>0^@O.<1j-7[\J]Aq"6N?5slSaF`&fWE&ra#PYi
;;t9VWMN&;T;tlWCg6C.aonq9[0&e)ZEe7PBo@XR34"pf%Meai\M-gW@!pE[=Vp_u).s7ak;
9E&m7uuT#>4RqUtqRKUUslc%Na&$.SM+OjpAJPr'")(#E'=[mGhP\X8,&#XrH`6/X*!nX9VN
q/Ir[8;*X%YA-X`A-7AG<%SURH7H=[WG'A2S/m8?=L_?jcdaoI1p8-ThJOWuanEL=N$:KY>F
M6*iCff&hiPNgI$n#mO`"#P<deqsa!)hjSN`*&_&ETE:K%?IuL("5S7(*mPEG:eO=<#5Li5i
'cUA]AWWFm.n!l*9'Hc46'5ME&E_eUlKakJ\3H:<Vr<)LGVU>r\:9AdL.pX'r&D#poWdFL8@2
e"QWnlJ@g^M&`R^&4biBA_-"-4kt4N3^Uu?<1GLn9)@^7_HsQhDDqP+RCj-V'!:j9Xlae.hn
WS$e(oUc_bs42/r:;Kp1c=_k0->:-?`97/eohn>h[lfDE\nQB=%+/*?k*o;UjnbOpu$O0k]A(
:7DlNpnt4YG/dWF4RE@dThU?\+Nj?"#Ti.&(4Zan#c0,1&bCKl#2tN$*,FY+6/r`Zl^asJ!J
)/6r=()UGMM9/b&a]A;9!*(3,Y(FQ$SDRT+dgj1-[4E.H@=2X7p/:h\U5b^7biMdf8JVMUhFI
eQ"sm=>Fht^lXqRB^o+<:8!a7)dOrkI+3@irj9tn!kmG_f^IR'kV7?k(rIj?D^fg"=>Sk]Ar2
,+pqH0/2o5?#'OX7R\jO9TlOXn`.=>JR3uuA";#).A57Det)2j=rj'/QddH=[9srR'^<4*TB
;/(CTS`>T4#@$!^rNb9m%::?tXSL9Q2%T?FA7CN`Oak!dg/jn)g\4;qUm#lLrlXR)HQ6T<4F
Mm'oYX/M^4e1Xe2Q*`%KP\%Y%M!%7Wl0hT%N`r&(LK4oL0kj1a>cQ@!3.59+n!-Q.M(QI*.n
XW787BNVAL%)'ZX=mO?NQZpF7I'TB5b"?ZmK+@9Q3o,N;L>-_D+\mO4SMBRfJr?O&;X/jP7,
%c`(qUMYTHEKL6Zq^XJ6!,1r5`;_Ph"5`]Aru$5/DShP\ubTO'HGSCg@JM.)56k4OT+u*+SN+
[]AqAe*r<Hm/rep:bj`QPA#BR4Pn74>c:*7V4=5>7k$:N=J]A1<ALNZso'5h8K\3\'W,5'\+As
)32U4)^JQUkg9j*8`^)Z[]A[o?/t91u1qb9CeSCMK=k12Z?m$mt=ZDjQDNq>nn]A8,>]AfV;W/=
4YA?#.0!)<AX4u(g,4(M.DP9uMCkPg(\a`jA:K^Wmb&/Q]AMJ(`.)=73Z%K7p94Qm08n!=RP-
K)=7;Lp4"Z1t1$o\Z9sh/MeRD_:BI9!j%_JL#?eLFh:P2`=:D`3<"4<EQ`4hGL\!-Ds%c3Zp
G,d/*Vi8]ANiSOceUU*mh6kjtPtB<P;&4c43rbn?al*;cjg8SA`cf%lU-KA0C"&SAR4;)i:@9
Mga[.I^\aZPC;*9^u0q7B=#V75FoHpr+oXgN=CpFDe8sG:B,N*rYm_[;i1Nla<+1bl>=1nGL
$*5VLn;GGl"h^PM06Ba_.ZqRe.<P?u;5%kO,k\Pr"U9#W5Xt=_s_MmuqeE%A;L9>n5DfhjQM
NGFYeMode^DDK>\b_c""#';XA+96O\X'f!kGgD/AACU<>br?a.8-1'W+QWe<uj(sZZY!R4^#
;[sqkrb=ff=[3J9r:;geUm>9r&X3[q,rR)LXV.i_ub5_.XPS^bTt5MY5;JF'`7:i:,YFbB&N
:*$%$\_F`143mbp858GHj:30d)9WaN>)po[oWF5oCKV,q`9g(a'U'*6j\31"K?T))fdibO/;
Kr*S=kFs.IpiiJ>aPE,mhBS1$%EPgo3jXNk05>T;-G7F1;9?<hcrjHLmcs+ZZ<SF#m@Z_86I
T63]A6C$tS^6jjhno3=M>V@pO-G!$cNKe*mMmTMF)p1c)&a[[\/P`V,mC;KKVZb#GFrYgcWJg
MTkF5_;K77dh^"U$QQJgD*?,^e8OO'fOmDiRLX4oD9B&8[:'`>':c\"<B4rr1*fYk,"l--=k
WM$\Qe/u^]A.ijC8s3/,U2c"Lb.OeNq+]A]AK1CbhTPt$lS?&cEn2]Ajc\i%(g%R^;ZW;u@.5"J'
&i4!S\PeU<GHPlU=Ha0.Fq?gmr4'dU9_ijU_C.=8g9'^E?BpH6!YIV/'Epg/KthUCO@AH1NX
b]AD!7cW/oFNA'R(eA%r3;?*Bi7K&/@(\KHH?duihs$*OPU-k4fJ'rY`Vt@V_X9jjXlLCclrj
_mXn-Atk4$W=NZnK9L_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_CrbZ_Cre+^P"P
Q6VK[dQ07s,T!gjk"9`V5qQ'"2s0$V:n\;ZZ[VnAfT>([3hh4Mn/RB2UU?pY+WPNKY25n$4l
hCD_~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="273"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="273"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATA1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1695796,1524000,2360814,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,6096000,6096000,3048000,3048000,0,254000,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="4" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="MODNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<CellInsertPolicy/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="BRANCH_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B2"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="0" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=if(LEN(F3)==0,$$$,CONCATENATE($$$,'(',F3,')'))]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="B1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF($$$ * 1 = $$$,IF(G3 = '户',FORMAT($$$,"#,##0"),FORMAT($$$,"#,##0.00")),"<div style='font-size:14px;margin-top:1px;'>" + $$$ + "<div>"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3) = 0,"","<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D3"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C3) = 0,"","<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>")]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="E3"/>
</cellSortAttr>
</Expand>
</C>
<C c="5" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="8">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fgs]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$yyb]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1" paddingLeft="4">
<FRFont name="WenQuanYi Micro Hei" style="1" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="88">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="ColorBackground">
<color>
<FineColor color="-135192" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/(#*6YcS$o('S&0qg=-.q,Y#rYnI?i^<3\)7g>nA,ci]AA$`0h-GaX_-I7hG8P/CD`ILXHd
CR%kM5t3n!iK+\sr,f>.)rY^2$^brZ>^j0qZ++JqAT+.4HF?J:d>'E;0&#&b8V8n*hG,MBBU
/kdsVcOI[XR-iE\]ANisPnV1"s<^-b>;Q]AR)cK1\(]AO0_DN)GVII!/HTOO7iiT$ssGC*=d&hk
/6nCS=EZ?<*okPRU2fF2VE,QcP]Aa-c0`rh_YI*\pTesdJA?KhHuEpI[<)i;%X^MQ%HX3([cc
I-b/g261`64S9-"bE,H:nUG8EAo:`k7,g81ZFY>Gc8No*Qm+O?2F7G_<#H%<R"E/=q`#0lNQ
-h:EDHikUIUIO).h-GI,VA%8G%"NXH\D^H)K06u6(*?*onYDias6fFmg0\bf6QR@7N*kHgH[
OpEpUkoErk/gW"f:g'o"SlKn,`+<m`OI90>?1f+C(.807h0#pJFc(+s1inhd')Jh1,9>>&er
^k?I@!&!3ENAt[bJRn"OT?'&A`q?[mg4D;H'`GSgGSlf]AUNV3#dro9$b@I#tXAFD*p%&3"?,
#?jU'G1:/[NY:(&UI=soIp.<;9/k#O(MWEj+BR#g"Pooh`(PT*A:G)KZFH^-i[[7'gWF)&%,
EWGO#h'1B@ZJ[5O3<Ck`P``WPY:er)+#B\6isg,-H=ePTB#`RE(oCoDNPo8+T;IDCKTEGria
V,5usi*`g4?@HTaOk)W'ME-u<3EnFZBWj6n>>(`DO^qOjO090TPpMVr*(\01c645q'"_!n*Q
q+rI/nSF.%HC89[^,P=iJ6'jZV1hecGt8OQ/r.J%QgY7Dg<0s-i!W/5;U$Cqi.Pb>hfN?J$@
Oeq0Y6)#WN*cN%iXQB^APRb?"eIYEsB)B&'1?]A::CA]AlDX]Atl4\./>\^a4*Nf/(&3.U$I?fh
>h,O<KrY-b`64,BOr35//+(uI[?e[P%LmF.fK;:>AWpd:XDriXo+>(Y&^l:F;0ZbnprrHKAY
<eNtalM1UXY9RuWUf10%nWT"ABNPZO*!isqQ\[YfXYh)]Af;Y.[Rb3^12Q>$*5K^U(4JIXt\u
9hIfs;H?Uk*q*4\R$&Eo)40HD#!lJF3!]AYu<QuiYbPF'>`Enj$eCE*ghKQ=k>OP\E>"+ZmgN
8`Xkt&d-7k[M"Wo-%PGB/9eo`"Se88FP;Pb!_<ccT8H/DcEb3q]ALD.:7AWn*"G]AbBE?!F!Qu
n&OsNeWShfB\nc*HO<O_KO\`I=Z>i(a6*P[UZKU)>C2;q"?SH4omHE9=<"CAeM1oV".:c%^q
Ah-RFQu%EHu;:n-3$<<bb49#\5/&4KC0o!c?j^a%!mriH0D@d%QU=X<L-'-S3`tW6cr!%hI<
;)YfB?eJV`-\JYq[J@qZa`r%t5&#=j^<LML$P$mpcsK_b9($DU<.jU1nEPrUVLKL3%2C`rLV
+,5V03B=W?'573.+J#Z:gj>`W'tQ/rb8Es#IZ9fTIXM6FPGG(/7:.Sg$XtClDBlV]A_I*.7Mi
Vcl@!8@*^>F0)J@'i\8j@uaKn00O7N6B\F36)Q3Sc\(U4lm8JE$*=j5Z+B1Sn*=s,*iX44Lq
C)*=G_,C,90M'&Y?-d,Q<^Z]AXE#m3uiCtsiJlY5sE^>@%6'8d]AnSk_>+I*+S-o_uCEN00h9&
[K/(@f$UWp=lLQH#n^HVVPNNDlZDG>65(tS3J.',dZ=!O0T-n]A)<6G)X^Cc0$!*oN#WpJR!?
\!G2uIOiSieidmGhkeY&[jdIYjR7DY&0?\ELlfWOm:8$Q`,RG[.\@%`aWr"U25@^m`;HOYH%
R]A81hgg3]AjS2C;9S0hW3A)ks[iqRD^@3ut8"`Q,0^/%?EnaG6WK#j"27bJtU/[!T#,GK:4Jj
T_k,TK.,.RL:u8)srfO!7nHA9+02>RYsBcrWQlOc2Xpo7GD\fBj(aI]ADM_l%'@9bFD<=&aX%
#'/(QqAlsd4%P0@ehV&R/<j36s+XA5Rd:3j)=G:$KT'4l<r=EJ\4aP7N`sJ.Pr.Wd8<J[f4N
bjo(1>r(QiaiG=M6#,J4RR^6SJNW8P0f"@h0lZY'>)RW>l_fNjOu36cKh3Ffpp9-FoM4^pAB
6fEP>3p4#?kO?A5.'4Ast)i0f8?VA2tii)mD;4LUD]A402!=dd[W%0.pPe8'-T0848*Y6Z$UE
[GO3f23iPdp/+joB%da$8k,s[B`"a!VD()P><5_5RXS<a;pIj@i/U,=UI"!Kq(IN[MTgDX2H
`^TF_j*B^GkY<B8VY(36c4f@`t$8$Eqln%mVAeUs'O"\N=UWF7XP=P,Wc)bkn)R?34iPkt%+
Jc;OVYenMlX2Gs`;(;R5_.M?nSHRkti(ab6Bd8cu25%+B7luigK*7r<?a[!@%_6V2^$%2SY,
YZ0C3CfPLrEP&[O>q\7@'BQ5o-$='5iN&r0T',K9YE^!PX@nR:E<LD`1d4IaTmaWcsgEPo0^
97kbF"?8]A,%`9&%u$c]Ag/2E2;$tF=H+M#)>ZekA&'=@'O6807K2rUUM[:?jp@<Q=POf_6ac?
;,&9*V?-RHBrs3.Ul\9JliL8]ARt<#=>Wg[q'MR^2=@M1DiBA(7mr!:ug9aW?Zb?#riIKd="6
JD/q5T12fl;K;AO2h_RJ@E4p?=+CAm&DMMo*L6)b\^7)&UN+RY2D;OT"B8:\q&len4bTF?2J
VnM=]A(J<c]AV$bhuAqS(WAV>h/ulufBO`nkJT#P0BRH%p6!a;>1k#hG2ZP/8k:pa/-XZ<L#G+
O4k/r.@I6.BoSi9:%k7;oa0f='A0)>^;,F@7"/C'*a)M0J#@^bE(GlPX`cUN-?;:U?+@!HEV
A[A3[#1PJjkn3V)!h()B_7:Zq,$UKX\-2+VT6:dVgu[h(!lWd#g_5QI5sb!\PCs,Eutg*Lr-
'SgsG'*rW//+.hI`?E1GLT"lB$WRc'<Z%$Hn(tlCr@AU8_H*4)-VW`.]AO.pIq&3:8D&l__pH
q't.]A-*qY&_jMTDk'l8@=F;0Q(^&lQjs2kYt&&?%3MnFXqN7rR,5$5%@FWjH=?GXQ`*KX1"=
E^M[B"(<u\'Oc6b>,aMlc-jdPSDp<2_@[XV`o3,g`8O_nJF3/kL/f"=0/90tO.3$Z2XYYIe[
4XVf)D7OV@Gu<@UKE$6Gj1S*h,V@>@T_nZKt'q%a)g>7ZbOG+Jk3J>+$ETFO2XENOgWAtn,4
<.9/1'W>@'SC2f)a?*C"HJ)QlVs/Rm\"@Fi$b-Gf9/[,,6H06.\HQqJTXd[eR/ikSZQU^sM'
>PY/dD!nTchE4,f<92nFB/h2RZJiKh!sL#MCfM</r,I&Fe>&%mmXR7,h&8X9`)GG4r&2(f,M
mXKo&t#ejQMM\lU)YfJuR8Z"W%WMjO3`QQQ.H%'eV/<a?GsXk:B`7`goUlU!QDhhE7Y4AG]AX
-TIkKu=4\sA!![KeMb,funApMn[Oj:0HiVXO$lClY;=Kbrju*oEG>\KBqTAN7*n_?AL,%J_T
$bO;`.t8`"ble93#%=QXG9YCoa.qUH1DF:\'KRYAld*CRVL6:7r25U;!4qXT<)0/p]A/)6#qQ
mVni7gHTKujgjDWj'em:&NT:C1`J6B3J1M2'lj#/?eMu?QnPBOih]A,uGUb=<&476L^i2_q1[
LqY0tDoU7+(/S7ZFtYD?"S!A`MiKu8/43UT)Nq?k6jRgJOs.D=TSUZ[j#SYmocb[XoltL166
o)7q^'XJ]AC(rjR?DbU5>`EdaWF`Rl3TOddu_,5a3RB7($G?IC817</L#iSrkt.'1elV,Q<#p
b^UplK_g'i_1HtDL'YP-mp[u,jYDnlS"<UTZq\%cBiZ8$Z=7"=Jpm(TM?.e.Jhp[U"]A.&UES
X!Tc\b#G7cCdspVn7ZT,_mcr"i%TW.CJLej7M$uHPCL6ID&(f#2jDmpoCU'-[.9N(nDA>=5F
/B*'Lk\L4m%BSE9.Cr3Q<sX%.sE<!;clermE(W\FJ'@B*+7O3`tZo*K^#o%ru]A-'1!A+Hk"_
cPk4%Q^@M\ks;&*$H3%&@M[)\kPlZCL*@gB]A8]A*HYo?AG:;0EuHLJ'8ddf'hZaQs1>;M$VSX
<>61a_3%UNi#Ych<+`HsnO0]AK"S>iHtjN%kLjC.<[7I+0Nsfij(7@^S5&,;#Y>i;fa@T%HB`
OU%f&s23nZ4=ooV#H%i%J!>QC:^(8+@(_uC.7[b"ebQD$`HRa(_$:LC8NO8m2EIEdhYBUI@i
u?uiWM^$MY5.!JEH/R:R<D>[+%fZ=<TBunCpMglQu#`a0_TgFb/]A>&=74K#VeH':$T35O6_t
+?iRt2nr6tJ]Ain]A[mo?=[6Iu+Y)3aa8Ngd,9EJ8G7K47-Wj7qs6Z7;"V<\:rRi/5%BO%7jaN
UStj'*PZsd0SXo,N^u@<5O8H;iAqZG-M)60js-"iMhA_<NX&4GGA9/VaS?nM#tMH")EBtQXu
`:;i;bo&a_*>C#U\SU/u3KfqWW\<'aO[oXOq>#S*">aHj^S!*'B\)1td7\.;:@^DUZ?7OT$e
X.AM#i\sSG6gOU8#fP"q8*)TJT$bOl2b"L$Vbthn%RXXJ"_3+:iLPrdcRV#M+O4f>%Xl?*\6
7K*&A8KUtY0M'?IA=Ua\%F%6k9[<:REcr?:n^62lZ5K^;\$+,rAV+e;h5[0CLnES8Dhd1E^d
cJPe6\YaVNX*'T)f)UM:klOC`d>j+L<,;e<$?Utc/O2'r$#NF_^u[#(^Kp[1jUb-0mk5]A9a-
\C'B/Kc!3#<J7[oiIX=k)=/\a.X#QI]A+--'W;4uZT9DYF;,@Be@b=68c-hN8O!;p3b5282@q
cfC=rTKhT!DC)pQ9lmoJXR`GlZHt2!BfNUgW&@ppFsc(k>%NCs&C.'dQ/K1gP7tc4-WAj)94
<lYG0&XWMY"AN\\cN9YQ!*6BB#-8Q:4mC!Om`&PpEW[t$h\FRTDNP$3Td0'^CFM69eg_4:\>
'/9lS1jE/Vr,hXH7T)S3#0QF?8;Q1?uQWudWEsqjVCe6@02+@WX!+Fb#9M-^GI=H7rq0+hk-
Xt&;"t\<(iQSV8QXR,1cK(gf%9pE]Adj`(\#]ARr.8:iSQbWb6]A"N#IB&j_e6%BT6M.mEr.I1!
ILHAY->Q"Y#g-fP^YXS8?[&I*qeJgshmJ'n`q:#:*uVf`s1^=:`pH:rO)=f'Op:XO5<nFoda
&o\?Yi0:K_"kss1;af^TI5]A/lBkoT`28Qjk9MOG2Er.b?A.8,_Pld(<Q^$P"?q/^d<>,F3tL
%..W<X[Q1(,Z6&J1$%+ZA\SVP"odFB=l$,XS@eDD*(Q.cO2SsnQ#=scs=`#Pk_lbYg`#J2,V
`M9PZo7q@-`XZVDqa"4#:LOU(KgZ1;fX:X!lE!qm`r/l5<[<K>$O+&Z(QVd4bHT1:_Ki!OW<
Q1S?D<h$,%/?KmYp+/J]AR$@Cp`r,C`2\3:[8rs1r&W=+QR&`:n7J+?2-LUP%6C*c6CH;s\7I
dnm[5OqY[s`J4qH_R;1HW>%VCEjOX%Bo@]AqrJFQalsu]AW#Ch/(7i=8A,>)gOlhrM_^U<,nm6
HA1O^d/%eh;cK(UkdPI-s'nBk*?4RJNp^?;12*F-b!PUpiGr@7aY3,f!>YlknR`)X0Bo+6p2
ljq%Q/9&Rh\!sd<NJ^#F\jmdEd.t(Wg3bRh1K\'s3PA'o@#4)J8d6dkTf_6C>b1sIU7YSQ5d
ZmhOR:Iu#U24!g)[4E]A0<:0Ta:;p?+htZ.a$c2*hA&?eV&8n>;+P"W.M"TlPDK's31N7&kX$
\g396_-[#+OF5cTENEf[Ck8_!&\eS]AiXS`(M.9n&CtX.K`b3o:qGr>(<_[,LQB>sXpgXc63h
6JQb$ef>758-_9&":2!2cfk49TtJe)O_"rKIK'#>B5;!=ST-j)MqQf0"/0VXX0"f>K`!G3HV
gEjVDW]Au:\X+``adgY/C3V]AFpA7_`0I"'8U?='VGHs\g"&J.,*?.?+PkKR\@u:GDlpMqRCo%
S9>=Hl:8MG!U+L>G\@jLQ5ZW9S1'fH9CS@TMEuM<n]Au[^u_"99'k(h-;!sg4'bDq]A@b&'!BD
(]A>R9ri=-002,%&FN,c`+]AaErR1/.1N?o,=$]AsMnT1+!VXCGd]A:;$VbY\69R"!Y0G:B@PEB/
-2^WUk+^()gCpX!e`5V9N[VL5"I%0X3&)HKTgN,-b2d:cU0qJ""'W)%nUj$&fPIFcl[DHXs`
?S-j#H:l[1.4p?&R_$VNI^;hN`>D5OC]Asl6[rE?9n(>@j38QUCL7rKa#>[sU5MAMF_Y>A>A*
iYKUXjcDS*X\Xpl*hlbGOQrAAUOm"S?#KNIIPtKn&!nc0T24N0Oa'mrFKK-8FZBb=Jp"Ja/6
<Bqt2Pb]Ao7W1Q8>dULX8`.9!@ajiso+S]A<G8iAJ'C6]A/4rS9Ki5[\%\$O/c6dRr^Y]A#^=h09
[<32(d&BjeR`]Aj_BD6r>_[BG#_oNR?7DHcV?%fn]AItPO1o!Bl?$n(G\rOB<X5^D^A3a8bg59
aY;\QcC^Ge+(qs3.N&Io$/BEGK;]A0tZI-@?f$j\^Po;YWTDp:0YYYoIA;"QTntO6mc#_>?S;
Bdo_ol*VVu8'Tl$4l8Y&il?X*_C2R5o@,[Xe;t0UOFfjC-`eP3"d!UQAkoT2,6h^@Wn=/ep#
l\uk2IrO@_[D=65crEPT"OR``uGD&u;L##_7>_M/p^"YhQ0-]Aa*\#!C!t9G!:&;p%Wjnf!in
^Ea?ZLZJ#'nZM>Z!Zj7&j>pT'0DJN8EKgfS!hhX9ub]ABHe8*7HTCNd'Fo^i!.Z<Nr%!nWft@
;[(2`s0#UYs7!0I"DC7#s.2-4$-t_2Mme$+.a3GPU4F!^]Ae"B8W#NY!mjbBD098)>n83\C@T
5'>X'b#c;hF@.`'tOh_n(+%\(Jgr<HLWa-X]Ac)c"W1`hBVP_MrhC6dbiu/)p,%JZ$DaIp+oF
oiLgEj8d>bb(`HX8'D8VY&',+WCcK80%;!8XFHd_%Xd?l@e)_K_tVQ+_7ke(4Ldt]AA0-9CJn
BK\HqK\Nq8"`tc4CRL*[,msIF>uShHG!IfqH<V2gKt:gHs=mIT(X#m=J&qKT<5(DP'YV9'iM
_=DLMUc$1T,CS0jeHK`01Y_8Yb^"O9@,g_u;rh./_B5!mDMr*!MlH6eL\Z!mi;Z"Mt'f3*ar
&9`J@2K3@/>p,fc-[Q%6*:>[`=Y2_.Dn5K>l-CE^t2dX&N3S28&K\-0h4,dUV>?>c>N>H!uF
o[*;P0(]A?[SJ"?10_ji]A=RC@(@MT=399h/?NZ$JK'Ym$E:s>J9&+[H$c"!1;6/XCkPtgu"b.
V5LjYFg%d5jVtOG&dO\QH7bGhr*lUu`RN*IR.rfCcDK^I+.=jlqO3V#,HV#\)\p-1m6K"DN*
4;?;]A7]Aj.9b*,(a7Tuaq!R!b_5Gj2Kb-u[/O5b#Rf%[OltIpgZ6lH7`4W^oh7\X"LTl?)M3-
kZ3b%._%-CPf4\M#O1k+mStq[gZhT4cTJOJr_qAV:#JJIb(L\rjUD5gZj*o(pXJ90[Q:&XJ^
@j*oO;!o.@]Aj:Bb$lB]AQo"\V-PP7X%]A/.CIgZ7/#89&sa5+9,D0J?^@SZM"q&3nYVkYI=0@n
S7Q&"qn0j#@JQBmjb`JFaJmBm2ocgE*blI-;skHB++aW*s5qD:h.Q1@P'Xit-EZ'u.c,MT^-
02&CJWdb6_N5(SBEt2&0*Z4s7/Yj=DXVc:M?mLSH/tWGNLT[[!#[>NSXsECVDYPn(8mpKfSg
uk6^SU]AYNYRYW75cJ8;>sh@CjinLjf%kcN9&9!lOKW?)AtV0H?jb.@4A<!D6sWuD&UlDgQM!
VCHD"5-h%@OoQ!'E?(T(f.tgo?"OYt\ao%tsq&W+ec!OjQ0r.'YFfrGG4C_C[-dU-7B.OYRO
$?k>@E^PCVche:hA6U\^XsrjRqaPMbr]A:S^@t=`7Z>DSD-e<44iB]A%"L&be*ure[E%@7o0aW
)2SCWm6p8BpQ1%fX+SaM^Q^mB>&7QF0I_k&Z`#AMk`N:H<&Bf@5Nq&2t06fhkRJ)TBqPg/)\
XT$[uW,OQgD2<b.G)S]AC-9>rc>EL3"]A*%8:+&f$]Ar;ij=D_dgp/0kue;p@kC\$Lf0#^#Qg`K
ISnDF)kq_UBt*%qC1q2Xo>RAkZhBXL)2AK<h-3rfSjnUCh"h?hNt<)%D?aH6BF!?R_nS=V03
Qp?&X_/nL!l9f-=IJ@VH8pYl7+F8JAU<^bOeHBB0O6nctUk8N(hHtR4=`96:RcA)DH\*%\os
"->U&mOkjZ,UePmF*]AZV2Y4aT:89+gT,?e13!b#T0GteU;[:-/"pWKE@g3<FaFA`1Z2e^I/,
$+\SGCT$3/>6SS8VLb(eV`LAOq<*8@PIV`X#Wa$f(%e,()#2Cl0&1NRFM;nO-7[VH[K"JI:D
?"JA#JoIFa,lN<)r?4O:W`b2H[k(Dn^`&>+To4-4a5MK08A6DXJAH:XPTWq>Te)QPB:F(6rG
KZET>qbj.4L1*IAcjuc7d0MGGM7;%R\!l;X2"J@F&:FB]AK4BcXelK#/8mgQg"eF6(;K7ZM+)
@Si`85?L^9V=sHNR&U%Lh?fR::*gk:3&lT\L$6?&MMBLjgB!j>Q$>2-_MTAJ\J']AfsKM>=W[
i]Ab$Y2^S7`O[j\N69e0`\T.jb4d\4g9-pmFs#bfXP\$q9mC9k'_JQ)p0Gfe$"Rg66MIMTctB
$kg,<>!CM=2@>1(TeEuK>AKGeA7b+(-l^?D-^=jOjFkuj@1@I-;g/8f9%n%JiK)i+[UT/76?
cm1/R,jP?TqAr4a>@MOgMga-(-M>2$Vf7'4Qc<^0BmDZujdliu[h5C_B4eq2;&nhB0jhV0kK
U4eqo0mf^:Vlthlks+M1*FQ`>)#:\Z:Ls\c[>!`p*?ue5^o?8?\lo]AKK;n1a>f<4pA*;D2\)
MCa2:oa@Q6(lerKL`-2-)$+i]A*lrs*E4>:L3oh/5n]A3`7$.(lY+$BTK5D;<T<0%/g;M2H'.o
Jj\*hO^kcF>7Sje(s@dY4#%f1$D8d[bhoAZ6j4PHkg=*3+q%clsmU]Ao*Ch%$`jAp0mX=;\OO
)WSihgU1jlII'"!%rcIoFP!8%)YJ[`IL2qppWpFbP2VT.O1ot_;RE274sMNo&M<]Aq=G-n"L(
\#//DX\NNSfaKhMhp^.^e>fiZF[=1t"WD9mGQqQBRiZ)nG<U:dNp%p8Sg,Z\)c]A.V>))>:lm
BeED"uU>cs[H5osJ$c^ACMMW5H@#C=HcVgIfhDA(Q7^r&>2)W)7-?j8TYmZ(L*^\(X/AC=X9
"4#>qX#Mok*oK]AN6T<o12g22hh]A>.5.g_skKr?I"ZJ`Y/eeiY=/Fs+ZVF.WJ&5.7K>h8WOJ!
CL7R@BqPgbe*(ORPImBL-qCif>iUJ"0E=cF?m&=5]A.W'pM4+_O*CN@aJlr:b9kaonuUsnCNJ
bn))-u;H8?q]AS')[1.F>(ZXZM`m5PnDT"F$L%q<4PO+#0>:TuE'hYUsM:f;2ID9r]A/:f'A2Y
.KiT[]A4Z_ZQhqgW3:R]A9H@ClHs"m#=c<%I$-GXFR;6*5SN>tQMl>Bc8g#G_CYU$"RlQi$Kd<
GR^</XSGdVk\&?%=-_dJ'V6o*HE@g%Y7A(A#^FKL[2'k5@lSEan=Odd61,!]ADVg9C]A2'=6Jc
bCHW_"\8QSqndD<<EMAIL>"I+*9X*e&6Yfi,U9GANYKmHWrQ5T!s<6JeCs3eGcJG,8T[h
j%F\,E85giup$dB_uX!AUSBeS#ZOAArY\S62Q);8;+N:6[;o(N%\=c;1Q)uJeljR1XO0m'/W
s1_/?`%&B<"$5Hni7f_Lm3S[(9Nh4n7La\,J7[6'hSQARGjb;C:M^8;:0Q+9n/3dm700*sP5
kt7p><@aMu/&a\k?)bLW&i[Pe6CFkDB??1(9XA#;%Z/:aIa@_?gN:]AdRml34^^)qq?X:USOT
W2D5k<]A+]AWZ`]ANCbi[dUc_R)WZE!p6WAA`GMkm/*:Pj1*p3UfR[[@7r`&*m-jA^.6mK8WCKE
JHJ`[q(.hA"Na=a>2DiXEHHmeh"&CPfT<6D)T*^L3/?Ui*<$>A$B8nF6"8KbaI`jn.,^i8ZU
0>oMC"5</B1g4Ic.01NDVs_J=1<25Hi"RE(6/@.B*]Apsg3bT2E=Q=Z5`W_"X]A[)iYC4NjGm1
+u,PqS(MOdN/@i/14pZs%rSbHoj#IB=r3L:a4c6lgKErtdSE_$T^6:^=K+/.uiZf@n*(?QiW
9Y-#hni)7@eWohRJ.kQ42c3gu:K7AIA_-iG_c[Zfrr6EoWUHA+G71f*@@shh&39A:=\S2OfS
Xq4:>/gaeQWTN53[a8"nrSlY"ZKHN0:G\[_:I=/AW+7T[HF\Y'03bgD`e%Df6GmkRDjbKd8A
0V)e5FOHZeLhgpL5/G"HPkN*H4Q`&n;F"MKn0T4Jj85HI=`:'EZ,]A7@]Ar3@s+J"Hd`<j&J]Am
[WN@R"WL,C3>sAH+jAYGJT'f.F^G\D0/1C?`\TqUSA7Ss._c/d3qU\mE&'JH[UHJA&;g0rFl
qAXZKN>j0c&WpeHSbI)oRJL7+2JO^r3=oOr*gC5T5(ZqSAmkrk1m'j6/FV*C[sHus?kI.Vf-
a^c&i^;fee!8Q^\W>\@GjYWm:I9/fm*\?ac'O[l[*oSA(`[375I`1iP-OIj_Ldal?Hc!TQM!
[P$A`(^bGe)e9,3A_e7C+*NIS^nA0_$S%66\$,F2lo&Xi37f-@nt:RI=_6OMZ1FNqjTXr.SP
R5+Nsbiso*\Kdh9k0_@C(V`:%PWOVRGI'';V%_V5\HF:WRBk/)XVj2U1"=![I/@hn%)c%MnZ
;8!,^PBi_g9K.GD<3"tA<T>t?B:q>L^V#oOXd'-hlL3\^JMGTL]A15tkH1W^G2BYpZfqdd5^a
t/OajJ]AI9(E:Yi-[,_3L%UCnt40ECRs@>4QZ">CVOjC^q;;&J:%';djG;L<j>ofFE!=9#csG
6O+lEnc6`_C0-22r_Lk=6i3K3.'K/SSJ$n91H^tu`-jVR=`mp`3RC?#"DoX51`Vk<n+bmp:#
6W./.R.Q]A4\!M1TdYlr&KL$rO86?I1Y-r$E`hb_Y=!U?UegE<]Ao9GbSO&:*lLuj<8T7gO?&f
!ZLh%PLL^XeU]A.L3dW[+$'>46ddSM$tDQuP,:JYcY.!<EMAIL>]A2ZqUhHgX[]Ai,BB;j]A/
)od\AI3XTccG3IoGjd@.E:hD/Hi<\UFc4[9D7,0%!4mLp\V=bUjK$H9LUG4$2G&IBu=GZK=8
uE"K4)Gh`cYps/46nQcZVc>k>e]Af67YcRM]A9K3j=Y;dtDf<X&i@n:M!0_K1bAC!i&/u6S:)D
F7Pf(\P_jlgTHbU0hZ`0eDDNS(;aBg0-:`!$;J@u3GCcjo231#a>CEhSUlaAI's#)Wf/"hP7
;_Gm#I8ge$i3X>Y*<&[,5WS3<[Njgg$W-F9lP]A#Eg/Y5knS)&)Sf%8a;^QcaEDW>m+[Zqmo0
7d#$hM0>h'f"/-KHI,g#L@UTDGq<bCQ;SNH!7/(7;gPt9)H[c-uDl/IrO2<-45--QO^50sGj
1CJG.<]A:!;Touu&0H2kC&PjR^rK9FJ[a%V`Jg;XN;4gP&K/"%VbTV!mFQ]AA<ZVXsH185USgT
38/Sfrn<%2)cTTop-UFJ]Aqbd7`#>nPeP'Sd_,H#9auE[1H4O(V.$APpcVZmgsZ.N)tQG"1AL
s1,e5(2K*4i#/2Q:9DaU,^$WB?=Pea46IM`97N&1bm2CQltWIbL)4G"P!K_aI;LKS6E.34[m
K7hR=XVrd.;Gm^;EekLT@S]AN##?G44_B\Hoo#u[[8:Y5.G[ekiLAmabou\e+Sr9N!o*L'PGm
5e0$)r0>u=cF7Y60TBX!4mb)Mm`+7#Xp4`-Up(,6'NYT7\LuI2qEMY'0Y7-bY#:.oD6u[d^r
9"c]AGAk/LVW^G=8pep&r=C@)"(Wr!"Z>nfAF(ICN`uA)Y2LIDAAl6ri\AbZ0'#afnj%>6=X*
Vd@$)e..J:>R/TT&05-)q#:>@D%BVh_.&Z0mScNU(PR["j7UYLU$0/B(C<O;/l8O5k30U6Zs
3MrXr&5jD(-3A1:)`d1Z^.FYA@Uq4+"Yjq8FsgTXkRFA7SoZf9*HU,;i/#Q)h7Jo8SOl\Keh
BO%W":VG>!@=aoBLq,#<DF@ie;EEaNGDFA*#nf>E[ZAk.XN<_+B5KPg9A%aeAQt-cN&&\UEk
/4h_Ds4a\!,(u8Klks:sc#m^bhFG,2blZ!\58E`93:jcCc)q;+\I_+hnpbE4n^=P'[Gl#(_W
PA;LDp5?0m.0&!Qjsb>&Hh!OW*df&`-V'q1ARi%J&HbM<bH!<rQO.Mb=M?tfk-Gk5`'<rK*7
-DloDaaZ<6I_Ida;f7d3g\:8*1#,MD%ceOgVYS4U9>T3]AIrO,TNs=>^`bHFJ.5:.Aa[01sP:
"[pA%=fq!mG;h!_kZMu&rU6R.FJs[7Tf[kpeIVYYYqf=+*\[RUS%<=6`a]Ajd2!XbgD_BQ-5P
mT]Ah[/8R)#9RHL%I6"^1t"$elNA&@?FdD"V?Fb9d%'Fg1@_\8l;HaN4ZDaXofa_%C_s[![:f
<OsG.38JaB=4E`tl)Ng83<m'Id>]ArU0T1d2FY^%O;rZ8r>i2:^XY]A[0KY\g\(CLbN=)ljF-I
JBJ]AG<.PPEW]A"tGl3h/\E*n*&XC1,Ur2-,M`WX&/Ebt\d#9r6Cofdu5+u6EoQq;mh;HZ;JU*
[*5NV9PHeAL:*?OQIR+!+]AI*:+u"rA[p/+n:2207$rke9'fCk)?Oip:OP2TkRIUifWbnh&Ke
)$uPl>GZ;TU2$^eE#%uDoE\jiVIDm?r/j>eT.+s6L['Wciu[PWU?)J%!u3%kb:C$cpIq8UI3
1JFCU9X@?NTR7gGfEeEJ^UhaglR7Dt:qtWg.8*%/\#!G^,G_+.Oic]A_c=>\]A_N2:)eMlF'*X
5n+nd?Arh!&2_9XJ8\9c_3bnQ^';?RpZ4QS`G3Acq_"C`k6#S<E20bK[$-Q,4pfgOUFIBLo#
F7<=ke#>iBe@(n4.jcPE;>,_=VEQ[H^&cG-M&G*Z;O/:lD7jO`P8e$J;1NE20sl>9na=L3bs
^o%C0>RBY.FKJV/9Vea_F&f6=]AI3@!LFYGF9N:QVpF+TaZ9s6ERM+0I[<oNFT'N;tEgUoIRa
4-*)hnDl9)mYupQAF?_+)*'7&aB,I+lO:']A'5Vckj&Ve(bZ$g/LKnD[-@LX_YUY*\nS&8.I"
C^A1Yqus,nO;e$.V#Kgn+2Q3m5dt7p*$6*u.l"`?hF@_6J7GLI$Ca9H4"O*rgQfBb:VK=?QF
@n&3JGa%A=&KQ\gP)>scH650[hW7duNAP$ME1n=eeZK>gICj)=_nV'o_fXbf.1`rhML">ME#
>c\*2(83C0O9F.=f=CjSE5-cD,p)UDN/KSE@cm31Wkm*P!9%R`WU5Z(P;Q<9,d6>A81e$k7o
@VJN7#rK_XM>fjS(+#Xd01UMKjsOF`;:Lsq'l1ae$<o?6]A,n9M-@%,A(:o9B8iX3WTP=UqpT
7`=>O$Jj4j+a8(@,fBJ!f!T<=a6P\--h4B!Vn-n8PQqh2+E56Oerg*o/'!-<bC([`mhrM`5a
Di2L.!Ecfo_>9"[D!0`;uLP'[m>gq,OBX3-ARt3^5pm\+)V%`11`Lq%%+(1!4]AaCG7i@'>Ns
->mRkJ^r<&Rh.^@CrS"$8';n7;fik>u0:+GK&2b5Tb[dX84'e"WP>DPiq[2LH_CE$]AEW!VZG
Z/afrsO&XqF0[hcBWS>)s's5A+k?rG/gVefLG2SHetYo'cp&SF`k!fpsWiIQ-\9>Y,WGEEep
"Vl56(2q,hJ.rm#fMIGr*!eh9PFDn:Z-_Fn>,'$>=U$%djQe?W:M"1=iENjJ.*V#uL/7RidC
pE#KoS><!tS*5U(5i5qh(Rb<6KA:=B8-jPIGrh275mWPc!%Me:r;)KU]ANgD/mVRHOKE1dO^$
cMcT!UB\!N01WQ<5I&fqh-[\`gE/3dG",F`!dA1I05kn_?.2U$fgM/5UqUK)cNelt5)*X52N
62b1TD)S0VETdP\EUnX0))ZsUG5CfaI*HGWR@^1+d\urXUe]AQDI(5I;:F'0UL-D%TC+C##$r
doeUA!mVA$rul:s*W-Wgujsh\"N0>qp7^q/#;s-aLm89?(M!;TF;RR[et"q\*otBUstT]Ap99
ZKd(LrMq0uO@CQh0^nY!!7a>Z<]A7<ZGJ-i\Z#!&Q5<MBD7.@e`0Tbo.:)3VCEf%j@^6D,`.K
0=L"k&.#1&_E.RBPYTqno/Q)$F7u1"M(q/G^,b"sKH=I47N[BEV7Kt.8#(*#@Gg=#j70_dra
Xpea7j3H2'f'd%q[bi*MT55[\a="\@3OYoR]A``E-+QML15']A;X6tC6n_CRco8VYVXBtS\<KV
ZM7g\O1=6e#-9]A2<2>VRdF%F.FkCke[j#s2D0N8>Z6gc?tBG[XQS7ctg)_?;.q.q:s+SYkN6
!\C\UMetGklN.>-e12`DI+aZ(gAYPE*PXNLTlHT(3^S>P:IC&g%]AFtD]A[_Sh*'i.IcTB5I3n
%EGb?Y`a`2Oi/`?<f77Oc\#G78[j<TWg,VN^nB=eUnQ<FlA9k0qL0Q23\:#:"0HFN]A<'R_o#
;B%t'`;ooXhXO6i9+_%Xph5@Or8\S&j#=@9gP9\KGJ1!d;_9iN"]A7DJOmSXn[GOB)`+TT,fm
>+Nl)3R/-m_t:2\W4I]A)Yh%TAL]Aq..Z9/X>b4a`T)[BfnB./$M/2k5nX?qHhhQ=&sZ='aH*E
h8/"F&qRkPAmsOfb_UE48[TiCMiV[]A^2(1XgLK;lrIuBqKA<KsR#)hC"Z+U%mRZ5XHZQ&WD;
G5&Xct[M\%$XXioA\eU:,A1\Y7Fa3;p,!e4e*t'o<C_V>a:Ce4@:m0OCDk]AVcPk`G\P&%58K
Y+8pBai-*ZLT@Gb):Z$54Um^?1bc[1N*`3UC!iLA7!j2OX!=j/aR^\T5X*VZ.EBtpIZ#7PU8
<W?dS+\]AW*-n*jMkTL_0_<[^?7k7m@A'g"3h9j#s$mo81>^O8-Cg,:Q?st1kCW>C^`U&&S"M
R()R>mn^fU&0GgsujHAT`)9DVl!Bd<GI'kZpEdBqXr#j"':BA6sKKMr$=NK5>k:qVPD%ET%o
=Y)ghRs7P&MUPgkEip?7f.N4ad>gPC1fL%e+TMhRW=Cj?C^_LK5>cQC>/3;gD8XsUl:8m6Lb
NhOQOQVtUS(e!$l_0UDd=t$XXO<N[Eon?l1O&S*Pg\hZ"DRJ^FR-U9UO!Uq\$YtQ1;ca"`,'
@#XH,c=A4?!UL"F'@MO39QdK,JH_;0G&`MdKP-]AL'ge4*/,]AfXe&+r@emN'jN]AZ<b;:G&[1>
?Ifd1_Pc$[`6?pEkOJbU/Hni4R<]A?dMMBGA^B)>T4jIX[iF4'+/i8K;:!tcgihG=Wl@,4X5I
hg9o*]A;s-+JHL6IXOA4p2,cW;DB4-]Akg9o'tO/hcZROfl>l\4PP`#=&aL,j`Rqh1t-:aDmBu
1oLSntpEci=?cuG6s8HRLF2?-;Z*^'TF#Jo;(nBuYT`EJK5-1k.+bAn0&!_IVJKn_18lNA*h
/<@@-6'birNS^"\/[QQcC2,]As5hAF$0NFgU:YhCp.#jo&KU>I>*f#KJ6Z:(@8QR;11-mK%E@
Tg_H7(s:[rcqBk;3FqmmE,cJL[\/AWT$e]AYRM\:4`3ed&B4r.Vg=#R-+"bC$=p?2bQipT)"+
3ZU0#);$stA#_t?)#`8KWlef)4]ABK83lYd@Pi2$(FF1[)?42m[m<l5$OZ.)p^Rb0=(PHS/m.
U1IWq)t5A`fn79Obm*M^Jp:AQ7=#]AHoU]A`u`b-L/nc(I.cT5`oD3B17Lq4I%A,=T(+5Ph\(U
%i)*]At!l8[N8bEc0Fe)/X`uBg5]AJA(0D0j>f@;XN:7@N-Kq?",b<t71ma7NQ1_11p#pH`W4Q
KD+3Dll9e*0ue"Ws'!FAnoCH<ne\)i%J^3@rbnuE(8N`bi.2idUdeX7s;4AnalrM13Zh1=?g
o_6KL&kV,_Cg-]A6l,EDWg"d$6E-_7['hgsDm[9pV@ki,h3A9]AW[-@Q(tj&-EUXfP,<>FKeaK
,GY9sHqm<KS`3o>p#G/qjT.!_@=rr4:<hDhZi_gaOXc$k*uF;;Anl69V^aU0V'_dFiV&FnMo
Dc#g*[<*M\fGEqJ_1FnfHAoSF*06NT:tPa<kq26kcn7i\5;cT6?[^r0).q%ZJVt:!o;f@5K7
k[55mgN]A!iE=k,l08)Hllqbf>Js/T,"rld<"d/$K7ahD08F#o#ReXi"iZ$_h$`iF!VJHn2.m
F!9mh6/YlD^K)3Y(Q9KWZY'gEsZ\Dq^VjTmH)7Nfl9'#RRd!KAZ%Wfc+ekc4K#sgo4V*kCE@
kbP*1]A&k3'H\n-[FE*s[O6f;QAn5jW$GRuoLKPh=46m5P[g89\#^FPIVaZ?<2A)@%99n.1B4
6$ac,>fCCU2O8g3gA[4+;tC<]A7aZlhIZK4u[/7sj3jhtKWN%gjfeR>C-._2?,sT!_Vb,Hj4g
*D4PH-g$6UURFJ$oQhFg^%PW7hd@)6Un)/K^2@S(&N'f0f/>V;$:3l?jY6XsDb)'Q4MD<YW!
ul.:YRSp3uf`E+4(4S@oF2+`QIaC^ho;%3i\Lf^1B?13E(f'a'i0NkMTi-MU$`Nh2?7FNcdS
nu*2nA@7:[@FYKIeQBnbGB\8-QpWYB;3t$pWD'em_EGBqp=#u$&!<EMAIL>/)?%L+Z?@g
I)d41I`O/;abWg>Ug0Lh+mH[H80R8PgB(=Q:,lthh&UNl*R1bt@Ce-cO:8;06Qrgiih9/kks
b]A2>5[VAkWpVB7ScjQ<&,QTXOpWiglF$mdJjh8@mHAZ8&CY>3K<C#:/85PX-+^b8>X'n*jBh
D#=(7bMt%cb&PmD99)H]Aq<E3sf7ni9.hh64HMg/1CCkb>l6dku.1!?<hSuiD')H=rg9P)/T2
Jn*=>l?aK0>,AehUJs)2@4+FVp]Ap5kE0T>X@ReY+5pUb$R-/AK"rplDS.*$f;g#31WeNpcOb
puBM2ZUDL('VZRn+IHEa7)R:piO?<P)?aCr8]AW>_aX$Ag,t"r>7riZ<&FPp-(JCU[;r%m'*X
bdp$G3tT4N4GDH;\,f-<uY`:51$NQ`f,,@,kt5Q'HQ0T2F@HI<ZEL6F\/@!A>TMmYg>bO393
Cu2\,85W<bbDq\;[/DXl]ATdl-D;e[F>+.PE1`aiFli)]Au]AebO($[H5.4]AO/l@5n5^)F<7KZ[
cOGY\rT)!f)'md;!%&$_r$,%-TYbkDL3(W([`L/84TpXIa^G=$ggg<9&?r=phu?1SbCF)Q/n
CF&f-\Hr<7Z%10m$;Su@#GMJe!*--3gh98Rb#pMFd<cX1OEq'B$3`3uHA<RiS&QfhP!h#Z+<
'MBHP5u;e?EW@TlU!s1pi5;8F._S?NHYMQ?H`,:<ne4L@e:4a@&[_8l.Yk6r$1ZoOaq2AN_J
`kj2hp>arYo>(Ukhe5WMg<Ui*Jes&DiW4$`WEA*7f%&mHMHoXiAVItX=/`(lF*/e#j(m92QS
&=kLr&KPQmO,C#Ba&^L]AXI$h\9+^?4'u<!Ohs)rtQfAThL-R*T):ZF)>E#aFPF_DYPF'r=2t
k8F.QZscI6![un26S6As+l,IaW=ZmT$"=^9M@QJs'L-Hg9=QDYHWOR?)WNo1OcG\t%ZOLs?m
[V\_(GMFc%>0E&3cjMM#*mo5t:ht+$#O;"#Km.g]A\PjU4>V3(Z(r[&YN]A03A1J%Bt+Z_XUCS
pId4otJJ^AC3YNFk-'\]AR#!UK]AbB7BXl+mP/1=?\M8YRCqhXF?f2_l3.b-KY6ZQ&;kf&Z+T_
oe]A$L)JTl0[laVa$i8,T<JoI<LA.=?kW(T._"]AYH`2"+,qi1L]A*Nn_hI9*XF,O6*g3XV[]ADQ
e\9H":9k;o3QsfbW=.2o?XD%<fnpp;*B3MbUeOSW7<#bQ%f"LfaCa`bMP((\VPC$)U<Oab)(
!:<"1g0<NPPMV3'eR9-;cHjDP!0p4))'7l\%TSl[*?79`h5(>>L[H4=lejYZjr=]A<-a51-)X
LVFe;+b\(4OIGJkF63$_QSD$odP_ESaXYs&U#)e_t>bQl.l(FHF&?84\I`M<FduXK3AXWMtW
SjWO\=Tagc&A4=%nQ7)i4oVT)2Oq2<KGQ&+Y%auH_s)cqgu+(^L,@[#X@B)+V[(iSB]AsqG3j
*+?28R2<Rr+3)Hl;qO/]A-^)`RML9bi5#k['g$bQGRKlZ*Ks,;g?KR"3)YC87V&T6jBcX5/MQ
#OJ.e(h@9h['9ZnkjGmUAtaSd`M=61\T@FE%s6=?'C+&0V^1k%qKB4%b'V6@/ZC45SB36iI(
-d0"$EFKi`O'`Gr$Ici3*oBE%jB0KY\o)3h$_#\pk^E/ZG)[83Sq$Rkh[A0t&\-#X+f3IhuO
V?.A!sa"N%3f=B0Wig(92;C+[c#5@K/DO0,/iO+PY9\,6([bqPAO'686_Y:`8GYPXgN-s'oi
fgqFI`gIq12X7:HN08if73&j]AM`Xc8kV'@>!F<)K1W0?1sZ&T8O1=8hMb>A,:`3NA%*3<KGO
#u&dGS^,BE:kD!n:i?p7SOgK(kP/]AtLK-*)dFRXQL1)14^.7)tL19s,2>)euqUP7gMTL*`k6
(73@Beu)F`dM'Z,b9APD3*Se*>C9!$2UHsRF;8U;j+\go9j2qIS`C`[^rVD6fT=]A:P+oIZ:B
2AH8:OB1WZdUa]ABL0$I?(@\UCH%[9Oj92J`fq.0IoY:*XR4T"$8<q0<E[9%46#W_s+sfU0G;
.3?t,<[-Yh7?"mn?.m%#%B8q7PlEf?<3H.CYA&WpIQ[:MU>Ig,_BWE<rHaek^.W8jZ;&)Ter
OjA[b:?O4>TCr0<e=6>,midP?,,i#PAOOH^hXShpjt[.ra$::8AC=Ij,S?2Z@it_'Csk[p\4
WLMeeMN&hoXN=@$Y(9._)MLNa:QXkgAMk=E#/TCCDjqPeNlFh#9=W38n@Wr^kqf7X>E$t!=b
!i-]A^)_AGH*1m:@Qom6?CcT^,"990#-o[p_a\cXg[lQ-/O,+0R*k^Iu=$H2:Shud<1<^LUL\
VYVM.MmJGbUou;r^r7<A@PHiQ0`'n06C\D@cPI]A8s<B0c:i!ZLpL>Hf2D-Qh;#WEuWm'V?*g
Pa\7+i9T+,CWFG$lnKY//&gm>EZFm`Q8B:=![hae%S$1hK4&&s?gN*HX9oO`8(nGK&Y$Id`_
o"X0JCn\QrH3b.TdYRkK<k]AML^?'?b8sh>rg6!4J;5pTbrX#<?m.*K7*t5]AgDejUcJG\WM<>
\r,dOi.Y[=p@_SRR^Fg6hg4K<Nf8hn100q(t6U/JqL(\6pH!SO[M;S$D%?PA.D@s6g0*FH1*
MMQ8edIFo!rVFS.9E;Ptm/&m<P\D(/%OpG-;R<C]A:lYS7Jo%S'@)@gP5N>sAhPWS5TCaUCqu
0`=0\OKloesqF;bM+SX2HI'1q.tLMS&S,o:b<F9$2Q/+D:Mf>o(#;8h:L0&g\?mK7Y?;Us-B
So-;F?(iS\T<:s4#ZkMqm`T,ErdamBu6D&OrVo@a54H*LMgrS!P3p*;Zmi-I5lL0(8dXJ(8I
l#d:!K3QN_%T+&lrl@TSZQ^r4gBT88t<&%et$f+QC7.BH%lfD!*<,7;E88*L?5Ar.SqXns*F
!WRP>%nnsmG'eT<Bc-Vj1)#T0"YrA&)/>,k>,=22Yn)&@VH5Y=ecIpVjVaUc6=3$dZ&B#sdT
9L/?"%n<GJP'2(r"FXW4)Au8]AC*YE9$-<ijFRd?%;s3S(VlI^;AFR9;U13tu9HFIR<@uErO%
Q>,f]A#[$R\Lm2;*F*HD0Y%[Si4VhY*"]AD<UAW&r?SGVFlC>7/R%FbXV]AY('jr!+;T?'.b/\0
.cN*;7J),,HnS=Ho\B)6_/1M"R;R>e]A#s,!s,T(oUrlt[ATrtNb>KF_Vg%V5J5RNH52mtC@B
s7_W,Uq@"P8A8fdZK5/NpZY]An:O^VkY#1"$Ia[na5$#h]Aq<02XQtIn<2%C72kBNqTG2>CBkb
Mh4aY>;8r<[%U`o0Ud-O\+*:Y)"6a2)Uj01j*19"IR#tc!7ERYeSno&Egk%Ge36KAQlgES(4
%V)FQRD,Z-9B)t2>oF,ea0$D(KJ8hT2E^\8XYk!!g8H&ZCbJiEJ]AYBUl:<9`GE5XG/QF[3M\
AZIe]AZ(<&5)tT7d*inCSqB`Xh2DU?d3TX_-]A,O/U*I_rp[GrlM=ZKntNC/Dq=?\@Rli;@s4S
uBY)X26^;kQjWc!"ZsL^Dgtk8Z>:j48Jum'.:*:!(.IP,"pP<879b6Vu1+e5=Po?A4K9g3Ye
18#VW#)AWf99KH;n_f_>ZK/TX\`Ss$oPYD=Z)aL=UYagq85DceZ<46pDA%+))@#tE^!DIo%:
8la[pET\YSc$'.7abY:%;+l7FCcP5MiM/IYZR.$Zs]A3.rQMSlTbQmH_Et*V4.LJP4[TF>s,#
>X7CUiVkG<QpgajO\^@sDi^EP)a?=kJ5'_)obnLW?mYK45PO<2eRC1[Ar*'igJL5)np>Zq_j
[K8>k.*cX]AUs#nfB:K,/UDh!"ltF+[L^QV7*&bBSQJWAH'd&V=m1Fb0,M'8i%=FqtKR@a."G
#gcJjNaoZ#FXNgg>BdXpA1^e^Q9U&WYrBcATNU1VJ%oR$bX?>&!36Z"#Qr9Ye-+`>n8(tJSU
1Cc^-?:\,>4n_nqW+:)/l%`FhnqkZFahE4!59ao4$h]Ak44T_a8?7ld()SSYT:ubWZNj;k1jT
h<8;K?4nAL0@qambtN),D5+p),!ZW"cU,GZ57*T6j[PZP2;DX1JNCdJa`+d@a0)D0'/c5qY9
fD3+rje=8e9r]A]ANLg4X-V3qT&H.hD9G&fEAVNq*)3Or\ua!R[dWJQ+M*7MJIKWil-E43d=dY
[SF]A@cojnaQdD2G`!aQV*_m_bs`P[Neqql/$'pBEh%QAR01*-"tr<Ys=dibDgA?VpY,QNISZ
')CJbPf9!TC'gKgK^1>O_12b2LcJ@D`_bSOTOLD=0jUPsA@K%f]AMN&af3'uU*r8KTUWDLY;j
Cr8h@-C?$TD7YsJ't/.YK+W;h3RBj)TbM-I)W(n[Pl+mZYrK.-l.r#;sumL[7%8>c^d_Ul[6
.o6a'D;)bV,qI[>9e9NrVq+7i,UTSTQrM^WJ%aX.BkfIN7b%U884AN6D5TTjsRaY/gRi;a[@
IlK5KP>@q[Oc9+Q)LGd8_H_c#Y"npIVtn9kc+,O'HcbgBC!_=rcZ8s8:K%ZJQN35W'&c%^Pk
)l>=3[gQ9F5,02g;,[UgDWiJutfjm5>'q9((QuC[L2i%#B"F^IbkP/EA/e>I%PbK8hhbn@s)
k+:JcA)XIXeVOFT#cVD0EA;@@<pK+`oO(;Zr_iO>5!!!q+/<=,#EC*;E<Rjlg5,@f1kVL??M
EB\gT?T&>k#(OC<fdF!)7"==d^7Yl@MS9nq66G:6te$'2[63:^$>7F_t)O%jh93BIGUi'WY:
=R_'Hn3%*3#C-[3`c^'\nEVM0WhR(bt2!eAjlm?+tti'K_^bB)NS[f[u-Op+4!rP]AQ@SjrIp
^AMEGK[#g^C-b=nGhG*kPASsS[uTq5J'YZkorRGa[YPoeI>K.i/G'05CI/qo,p[&fHh'jEk0
mLTP@5J4:);p'2!E2Vd..5[Vf70YoH^`(cW`;W;0,4lSR,/j5a^q[q5iXnj3$uf%>\m[=NE\
!?p`Mdq>Y7!42[NPQ@C-28l\,A5_:C$3[cS%&OdlT7&3BjBhCl^#Gjs-kq.GXhP<^r!YHX)V
&(;*PY_F7'$<GTqk!DNpY>X.pNc1^qh:*QjHmRR?>IfSRGZ5)ejkSn9099eP47o3gu;feJPJ
bs,mN0%r.dg<^6LQ.9HWEK-.]AO9)nZ/2mPjEUhGuflSl\OfhqQo?niYR]A\!ZWA;)8"i^\L!B
MFI0En@#0p6X:5kDB<RM`]A\+3rFC=XDu=0Xe-3XQJ*$'GNc2.`LVI8GcD1O57R]AG&/dYtBm$
AU2l'Xg;n%R<&Fr@JMn&aY,\hJ87M#65j]A9WerGe620e%b@4ju&5mcIr!\PJCfS?7WV`@Z4"
cViJnZ@VUu2[ucC,5"h*&SR.1?P_72UBrX!2m.)O]A\'/68@b"$lbgh'R<VZ6)2&;ja]Ae@Lb`
?aiZrLBY0O'UYYs'4W>*t?9NlY6$U[8&\Egn1K__XhtOF:Z+aG1%C8mR'=Tfl1L=/W4ZB4FF
>d9f)%GZS_7>U6Amp%7_2`/Tda-m(,<ss)WStpf%]ACD^o<(Qro3?0V^9@n[X\D"U:[WR,MnO
]At4Nq7IgO7:Sdc#YQoJ->/C`^=turth.4"ZNE-J#`F#47i>QUL3cHEQ!Ncbh`1j.K79GT*-V
]Ala'q_sjK!4::JAa<6rRncG`(j47!8]A)qROuQnT9'h]ASpbY+0"F_Hqtar6bq8N/E!E.@hLo(
!buodgda.P-`)WN:ZBX@c>!B.@?_9m48%_5_Sf>:)<tRq%%k\U,foMiuY`lsZ]ABM,mcMDl[3
0Cc,au64t98T-,M5>O[L;7D#PhpBDXh,..g3[nBLZMesHUC4IL<__*f9$V1am#N=,j9HVjJ\
2p)ZQq.?&W4@IXe2H'2\91FYb2*H9X;-*o6N(O<iqFH)4I*0]A0k?!g<Uo;lj&]AEA*qpn>50m
Pfi6Zei]AK=Yk9UBS8rgb^g$P_ns@Z?m:]A@U\?NIWZ1"\l.eW=R(0S[95kuJt[WlP4SaPU54Y
&MtTR86Nb)klL0\3$Ap<(%kaeO9e^e)LM^BA-X^FZcSZ.O>*f8_QI?-S@p*72kLqHo:0%B&J
s[tm"EU&Gi7N=YmW!"52!_ErG3;[qhC)f'!Lhg!9GFdp%("r%X63?#pT9AmY`RlC3MKT4!TR
#6#qM^40'dA<h=B2)UF1Qa##.e_qh(&",-VEW$!FMYn0dL);!mOn]AGcl!/!p(C^2\d!D):"Z
f%hd7j,Uo=W2=kroA'8:O^WI!%dFMXkh=3)c3?ZAjA.9<C4LS(p'[J%nICQe'A<)nAiBSsL(
V_knc4%(WZd>#'UTX2f$F38eAVpZ@;B\X4ADjj(B,Bm!u$0Jk/HH4Q&L[NXD@V=17>d9,^a3
H4!dSJJ2O\iuEkb[8gp?9M#n[r</6kB6OH;C1,)e>>/\me]Afo&17+EK$\_03#\)\]AF_P6'Ym
*`+-(r\hQsn,-hiI6[b*lm.^6grA",b^:qTS5"gtLN=bBEd-JEJ[AD,KI1%X]Akc;lKO*hL$/
1=Bl.gER$#DU?1(G#.(D,;k.`"Nu@rVB#u2ouTR1Mf#8,ZL&mJ!9?M/X("!'\)h(3OlX6hS1
V<'Z]AYk\%e@VQq27:-`0\mpg^?s,0#pd8U)WMb=1e9f(1c5#fbY\lGbI[)p;HVa@tfb#[LfM
]A?Ih`lZDR&=oGL>W$FbX:(+V9E#`mcM.W9CJ^631F,u%@TUD$/4sRE0jE7HK`joU]AH!E[4[.
3?5q-dq=NNh_L&3)nIc[HE3f/[O?"b)fT,.s;9J%AljX>?U-qoR<K5qM:CdS9P!pe`BDN,i:
j%]Ak'uAO]A'/G]APt"m&?1pHO/7fPbr"FU8=U]AWm.C;qiDaS.3rM?r=1omIBf)D7-0ES_,a_>&
-)">:bo5q0-KUOQD..MPL,b;&jN3\Hf`ftUjdr99Z4Pc6bi[qY;1\dIsO2EprArDMWHe?#;7
rhQ43]AAnnUha,EHVdjjI*3<?'<FeNKeugtuM!)NN#Il</l&?du6OX$m*SPKU0-OtGK`j"JN;
#k'hh`8MtHB#]A_8?n*4Ve;oiA_`hU*-/">Z=;H:nb*,RpIi&5,.0%1.AQ7K:a9s`KC#?8,EA
#]A+Z095%>0B3BV^'=!Yj&dIn1b_Cp:^I'ehh1Sc8=#BMDl9$gm!3Pcq@0N/ed#>@s!7cZo-`
%(cjChKBgPp1kO6sNZ\t/[2hT=)4+def,L_)GB>tlit[]Aq,sN1Ym[/+/k.SU58X[g)A64LMb
>B>k+?=&GG9V1i=/929$Emjeh3Ac5Mrn9D[sZ2<T:GDU:A!Gnm=d76W">BNQCs-c$'YV0a?R
pa!1:f"<GW?3JsibHm1J'oZ''V"mDCRXk\7Mi<qf[_SO''^c[]A52Ip7HV!(hfB_48LAkDVdo
b_)c]AqK9#(8clbo`mUO%&l!YRaOen:]AqUH3euP5%!"U\L=MIcVZCd%7hN[Ul6-ECRcR1V!,-
-opUC2MZ4K$`he@pJ$;\Jq;HUk?63;`t:DOpIgYi*5Hs3'7nN174,$c,Hi@+4ph1NJqaE69Z
up%D=6WW5Nc\f4g"-Vrgoj4A(U*q_0a;H!*-!^B2d%`R*ics?kf`=KK+na#Rnaa'*9r_9[i^
aqmc@ek+EY5F03q@,DG\aEtiE@XuUl^lu.]AC(NgO[D??Ypb8i;RCWF^`^KVj/U6s%sTU@Mj<
)a9lN#RXk/$nQR[Y"[o=-^:5=,Z<#Z!1X^RZ>IsKh=.dYq>Nd`hbVS85A*UV1X1qV*:#LZkU
QW`LFrYc@F!K]Ab=(ttHB'Wmt-&B5u0\prFBL%a;GbD'qH_7]AVfU]AqS9%sA@J'04e6fk9mRKq
uIL6F*MLT'\4sGiClCQhCAo;#d3ZCI==^6[p*LBikt0eug_InoEG#=HT`r[sWB`6gF(B0S/b
0JE@d`_=VXKqO-*BnPF+>n`0:einYbq2Z1"H7UFYKgrU?hm>;ZX*"3;fQ^oIBpUm>WnZW2Xb
_u2Q/F'BDj%,#>fgdXhNNjd]Ael%/>jrV8l&Cq:<JR=?'7<-.T]AoRl=Kc;kY=nafebTqi2&]Af
5_+Rh&@N"HEk3'"R_5@ZNQ]A6f$RBcp*LUsLCr0aZ;4+V;K^0:nih]AANF-(?-@]A6G1^iCNt5/
B[C_&3j5B*n<m/e<ZcLJ?"M'?Wc\jOL-_oSPGI!hDcTf:Bqu*KO!tUe*o`4-.Fo.S*Tr9#[n
6G!&C'JpqG8rp<V,C=0qOG<)m2[M+%.3)"$NE/`0fqX]AcCGC4]AUuOruc6D7nMADDaSC8(YiR
%UPD3GC$XN4Q79NK:UU)JgU5oN"^15i*.8*jj1@kl.X792R]Af`>U(I/Igh?p=8U95l9.9ciV
X252?5TJNc``>liPA<RD[HL8@QqT9nOH3'/E/-_)54954D-]AWTUqs_/D9`*ome^f&J0C[ie-
G"YW(hO%U1"B@b?qgF:n7fP,S[,SIgVI/W$j]A9md0Q;N:C>s#nn_bIM?>bEKD[H%RoqAOo/r
:"=J]A=cHgb8g3R$P9kYa[WbhIFl*Fs_HH0bVjE)5V09b-aKIVH#u#bYkmuH[;9@7%062@K=U
l?^LPB)MT4U7Cs%JhNaVd.j(?T%EcG#nsZjd)*5H<C.HG[/qoSa:lYoIJt'R&K<dss%tM,Mp
J4O5ee-0sa<H`U_!!b^Tu:-P-k@tUi?if4D-XdL0%)#Ui&HZ9O8)qFaR0gd+c.S/i>M4_f-_
(V*Y`SFCaSsoQUAB@;]A`e)#0b)kUI.7;H`^(hO<TeNNS-As/*!AlY8Q*sFS:7iT938/TkE)R
qrB\&UnEsQXO,a8KQaZ+)MX2^"L:'E9!(5YfaNMdY=OE`f8?R8@6>$2'%EGEHn]A?UZ1mB$co
/9-%q[AS36^8L5Hqd#Q`.j/k3:Cf4@c0TaBH`(N*nMRWFWN<&;W\3-5o>Wf?$,0"b<g'c<Bt
*sIYoiY9M,Il6:U2bgL;3T2i<MgEh-1U3D=e7XAbi?:rtGc3[6ZB-T,?oJhn%i0k4?FAq(a4
.5Q+h+A9X*T<?S(b-D\4d`%a%@[;Ia';41!(=9-0U:mbGqL'Y'O/Dh\pdY+r"<*4?o/N<"`o
"r5cn`->dN2r9DEAXTl"4EIh9%BLmC^h\sk[B'GAqVh^62c24DUfnJ]AkJV'6Tcc/ji9tYrV-
J#pk/)As-51S9u2'5o^]A?\RYr'$Zg?KsR2inb]A6bT%UD1u+fnf5L0OH[$-l`[DBhM8!C(_u2
K\r9XeXH0_C)\a'^Ni1&_).fan)^3?_'6*08J-2sH`gVF&CXg_.Csk4c##p;b9E_U*a?GLjl
t.4d2-%>P-.ga1W+kKA"_9(]A#ZGr&:G#Vp1apYa(l[T!dB-IickCcU>Z8?c7J8t&UuDCS?2D
4s,-)i?Z5V+AQgb_R6P.g<RH+:<qU*NP-2%9<=kMU=#[%YO6/>,i-i9&(snQ<`aG<@BP,;Oq
H=Q7MuL\`HL"n@^MAMn//9`YYYMV^,<tY^8.D<aDK9-XB%JWhEOXg[P*VK9KQ>#=U,r66/:h
HrF^CQA3qS'+%PBc=^r%$eg[Da0X+DBN=oe&]AM!CptYG*9en\Xc,,S$;\-AM=o&BV&-AEp9!
n`]A(`.R;R.;LdU=.(a]A+#9C^lER8_TFIKSNpq5FIW.jSf']A%VAO`LCu3g38Wj1<4f-TD)%lY
88odUsL2e<L"ieYcn/^&`*]A11o-<,Wimg5(:-Wjj==#c'7R;Y^//L<T=qSLS._R24F6U;D[h
6BQ3[XVtc71F?Y)g%@%N4<.ga+[M8>:A:*l2eC2m!04MOA>`Oc!44]A5tL<kEfR4DD>T)S8E9
rkb,A!>J+*C`mHlguT&iEf+gQZ_?I^9fl#OIt/DhT(XHKj;+hRS5Nnb-"gFS,/Q49bm_Ho,/
:Sho[,!XVMPgDLb0fK'J6ca.F?MYVmUCE?;jhkFiAIB&XTqDY-I;kP7?g'7QeQ7rg"<)#rmK
a5KNcdKYhOM8@&+a="@UK]A`nk&@%LprFZQ-@\4T#>Ou:nVtX(nB*T^pXrXt%qr'q!,cZXW"m
ug4(os_2AEYM8$RjESs*7!T0]Ar%N\!RGB;S',1/'GnC\_cUI*k0RPi7_KVY?h_^A:9H>>V&3
@>T!b)H:F/g1pg7.kB3r$p)C(p@Kd1$NHWo#AMkBs)d?*<K@GUic"5;ob5HZV!t0KkP4,ej;
.1qGVg0QeI54Z`l`2T>;j.C8+T-,89-PRSWDqiq4?SZ`5OKtc0m"ch6fEf\.q3fWRACf'1-J
tLB2LrbOuaY-RbQ4+48c[CBL'\P.g?hirKE('\b"8">%Z$[/Iu&'7c!esI]A`XqZG`AOc>rU`
-dgD-BW5bY@<L!'[oQAW;t^%"RO&qoQ*5pMH,s5WUX2'NgpFtH9]A7:mSXlqUXhI9YQ\/:Qh\
<(I=m?VIj0D$"6o>2LJFnbLD4bWJ&0rsI0jd)AG[B\!J9&YP@S3^t"WeZF:I_hOrX+B$$O^I
_!EKE)=aBXTh^CBU\o>)r3C[lReUq+/o)@@s!<TPJ!Qbs]AG`omJfFPXbg2E1?-@<I-\3m1jk
!b?s?L0bL@0f@;ZF91\'eB$)!Tgo):!*.=UV6nH)%,E]AVK,X7^"RAfgHHB.km/;i?71F-6PC
0mp!\uX,T]Ar!$d)M*[3e7B)&X97<R!(^$S2=/^40-C%Ko5R(4(Zk<QOTE$cR#6DmUucWQ+Jo
9:%I9U$WE(Nq-WAe*fkjQK3=3,=:;BdmS;%F+4pi=.YQn#Ac9cl-C"+?oJdK$XVhBAWQZd96
roqoEY_$4aFnt=,[MLaSSIkfVD2,YrJX0g6@H=9dr'1@s^lmJYZ_ZO-$cj'F:";'!jTo-%uo
8nQMsc;[L":eZ_=U^.$9='09*:#86$g$F?/(QHD0H4YqrS0k-3m;G/MtcLIL0:an"FY'j<P4
8Vq7'm&1qmk*h5Wj1m@r#@DoBE8030lc#QQkuL,bQ?T\]AO(L_QdT=48,'ONf^5sBFHSP<1Oc
Y;X(0l<a`Vu4Q:q$O$dHrHZ"4eqhWC\-EFcF!0)[4`5='%,4t+0)f()[`Wl8h8(_/@Vr]A=F6
2W]Ajk?+dqK_nQiuWb7B!Bl)Qb]Aoh(9lu3O_*^>*fH\]Al!G(DAfdB);"7dX:/e<6)mr?]AOJ22
iod-K]ATmA_+2ce8dk2-:ML,pZA7qCtjU,HFSnQLmLVi]A*uBDoKK-+el]A+f'E"$.W#f8$Z.74
UcmnAO%m0*_]As,L5q=C_E>N&K'qr2opb,N19AuV-S9(>N2*j6\-/mJ^%Yr[uMSRoG)a]A\:L*
J,S3-PCq,k_aY/m8empKUBD1n7ls]A]At@(%!f9e(:)E";A5^ELaHmD?EseuJ<\3-gmO9AjnPL
W?HcPBI-1&(KY7-1jW!t(n;E%t2`A-1S,`5A<cPAk)acf8L0=t2Ljr_lMop[ZO>$GS:IH\fr
BIp&bItUDA?f=%[-:54[hc2>u,<Cl@QS2Dd4]Arni?l;VlA"oK"pBR66B2-OtrU:euH,LZ9HI
+5^E&,j=%*`_H:%Q/I-n42cbDiurQO.O#oX#&G%p`ZreCA$P\Gb.oLfCLrktcKRa+sOT09NM
Q_g-GXT$%J+!4/.-:;%$1N]AiJ='#A+>@WD?@bOLI.0g*?!6hU\eOk[TX7eAqCPe5%F7T'7N_
\U-QbN\mM.h5_\H0XNjGh]Aap)hSL6JOol0$u/mMm>-onG3&E?[rSHi+D)7\_m'[cA7F(]A`22
M]A'K(OPN#1qP/N2!dQ/g$,KV[n'6Q78Od<5RN_(`!Q"QRf)G/)J:35%V(B`r?nelu*Xqj:E-
chpkko<6[+$U'nWls3]A_53!GQ6bNPOLSl_Zi9Z]AAj^cEYPs!7UpBp'1aF=taWUo@q<98HW@Q
h/J&]A;j@&(/p"dL(qZ(8o5/qk^ZQc.I<`Nqo?a@D7XePQ\0Z<'^Ff:ha\g:]A%aA'k'PJJ@E;
n8p#mKfP<'0!YE3\Vs1,S`KNSRS8$**l.\AR=ZQ:od?bn%m(1XFfc[fHATRRG6;N4X(2hZpf
r<Z$p$(>F!AhnCM7,O+JtK+OZT<9=drk2PY:$10ZtO50TtOmTWd#Ia9Zq[9k254cs3>i-?Z;
A-P*,Uk$Uo_fdC/,&-6np2YucRge\fB&FY:a;5#nZJ7aGP4f^@WS$palIfq_[cdYu5R6_Yi6
\'>_SOV>^;O"6/gHCl1\%/lo,'i.XFqp2=NI(&i$/U0XN48T=TnTBf0!&X(oTF1>%`,uF<Cq
F6GaW("4,t9F6WD*W_ljLe^jVi81F4l"NIp4oM;U+pd1qkI'4a:\AW\!:^j7a22:kW(+rQIt
FJtrS-2\%0RE7`3(Sl[PBd.9EpGVkD='C^G`KJ^qW<?s3ifZ+uZn_$DbB0l(e5eYb6,pR!i,
s.2*M-s8IMK!oTBYWb\f-_0I&FWhVQK\9=PJh8H"<C=rSF1$5`q#*Jakb(D'\'mM3A+"pLF!
C81a+3o<f76%'<Ai2gNF+C/2r"2NI?+Aa;h4&3$tZ>)VfcX]A/D)eH+s,>(W?-!"_[<CnJcb!
`7rL'I0Sf/EntK"e.=^1O3se%R]AN44JPkWdNJ(Wt6;"=mC!_qQ;4,=!rfGd@%c3=?:8`^2MJ
V%.[lo7[4-#B#r7gg,P]A_2ao&>cZ^^_?&TqWHN8R\AqUH\8MG;g>mNSg;tOFd-_:RQ7B'q=#
1j>Ut^?&B$sd$'&<qgJk`%Ci$2&WL?O"kG3XG39#[N$sgL1N$p(L<!:A6%D[13"d*r1COJG5
Co7tKt@]A^<h%!S]AS@BZ+RoeX5KbpO'GG8p"5^t08s]A-6Fs_</\)Vt,%()bE;Did(Ng04`hs-
'.or)lkn6idM-G-%:0PblV*Lk&bNo^AANooC`3fC'[9$UQ_rQ,qil7/cVR@8]A5AW.ojH6*;n
Z.0`8aUAC:Q'$n<WTJhqMq3o==B^fk$@Vte!>eE'ED/8)ckO,N#iCeLS>^FDC3m59?a?&_%(
;cK0V2ae4QU`TCG7<aU.PVe2:X1QS$U3*e(I)tLXm8]A!I$h1:HPIb@<+qGlbYKR<42%df*Bh
[Xl6LdY]AFtfL#s0T5%rXLaDR)[0f:\R>;s+4;Va*,=l\T)NcbaXPXpV0T-'1#Mt.S,]A`VVJ)
0(M+ikp2MVJ::AAM4GnP7F[3!3fl3(_2IIY%jJAnigc6DqT5<l>G$&_d#XT^3AhUdJ8p'3U,
;KfjS\TSHm2j2TROr/VD^0ikJ;^kOmNNFmb'#EP^3(2-tJ8bJpU@qPG1d:P>Cbl_`TTrC5u;
hgIYGED?bn9<rpo[&]AhB7^9Bp?F)%!AnHVn1fMt_mW7)Y\=X$aa>f:cfM#ZC_u3<.8@?^<S+
GKF*ljSJkLMuoDF_*#:SX7Tr>=83HHJ)d=^=2@hdAP%k\^3,MPtu9e?nQWL"b-aK>c8F+OQ!
A#ZB)gVAX5u5[M1mmGtXY#@%#3JlqZ^*kL^^DJ\GH?5'WS3JTSVJ2Z*,Fl9]Aibf:0P0$mL,L
Y6oNQ:Q@8J:gk8@R.#-YNSD"EI4.H&+HW6q>B$#n5-5[2T9$X;1#O(.$3`ar*Z,-[u<ClQ*g
^<KkeE-VIpgVYj2-Pldi>k`cg\9oG>/kn`VL?[Re$J=_qBdgATBnrm@7ZFLtA4d`hgtf/e,V
:tCM(h^WUG1+90pp+3h]A3pSXD:[""*o)jf/4ZT<s7FM!0310@29k^TscF\n+7H,<o6-oQ]AfL
-)Cpa\MKR)tpH47@3'TaW>npt_*ial0-^:OWaug?\FmoeODf^rf]AciUhOXbpT/K?"4WL1do$
$s3Jl(B`>A)rV3;Nl<D"fI:Cn0[%=tIG1rT(5^EVtK:YBTTP)V-kSa"$H3QNbSjC\qB@&Hu)
>">`#!IU16-l3:d/RbBM)BnMoLn0gD_qXn4'KKoW_fUldZ:=+eU7DC(!+?iIs35%':oNVKP/
.XiL01/GU>Ndrr3QC-.uQ0:#hnq,CG)*KK^fuDrgLS~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="333" width="375" height="364"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="d694b6b1-c4cf-4c5b-b7f9-bb08b7028fc8"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1657350,6610350,1657350,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[0,2743200,11578660,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" rs="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=report0~C4]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[report0~C4> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O t="Image">
<IM>
<![CDATA[m<IeU>[J35BL/BE+G`T#U(\ee(l[l!]Ab>NB"u/.^H3k1SW0sT:ke/=!cKLfeS'cR<@bf)=J&
&blH1&_"5+Lg&FnWGu]A@K1Ch<R)+H9,f*G?OA9!!#W60^'5O5QJVbhiQ[1ha=QAWW<)8Zo[E
IJ`!AXgpQ4>k1]A#La0/ar`6mlbM;fjW?cQucTG=c2+,3f-S_T0fiC(^/Nbui0S1nM;ZF`9>"
_?]At"SUf8M;th2j]A-F*O8aK^q`F6td;9mj!;M%fKpk(\@!<*n3c8WrAmDKs]AibU@mceR)QfD
<)J"3C8PUp#P!!'!F5Y?Q_/I_go8feHh!bgBpYl\`f+//`8qE)fC!1>#c&qH,2ld9.nnAUe9
g?[P1*>lHel0N>-6bqR`Dc8oP8=H:&CS+iX3SrgHOoX-2E7Y.dZ'<'&^03h18a-]Aror=[JCD
n=lTiY'-:qH60`>,8-J":<geu;X$l'(I/mi90ThClb#$bq%[7XjkY^0\3t/[.8*&eGsnT]AS0
;0!.$"kS"3Rb+dEs+e^p)3_I6WJaX>1'RAT_@U/.@`2nLROdDopl3LR+!-iiF&o.\\%D*:bC
/LmA*=dNT!>*@pA]A0hP9+Xu&5't"FP&j/p/Kh:fY0npSiCB#%!:52H7g-d/:S0gN.]A"FRs,7
^"Xu%!0fRG#U-]AF3.j$-AVI=3G9h_t=CjkH3ZEoW9`"p$p9:Zk:8'X;23"m_Z2Z^ZJ\,.t&!
S,>VBDZ(Br;EV!l1=<s6LTnee&c[[+s1J)b0Y!J&"j]A<V-B^5[Ic0WMHl*Ss3OcCqTl1.08p
Dic-`c#CA6RXl4-UYMfp@B#pu>%OW<j=.`HO1BcNqScD*&!:O``sFO9#WSXK>NG'd%qmok@U
KRC3G9.Y_hd[[LD_rq`rub-i426OP$FKH9g!O)X0"C0mkA8ju0uE*GnM0<BQ5Zp]A,$"%Wq_X
`.&=al\GR2h*p%f8,pp[J'WaYo.s[-V81Ck%uU62[,#-/7(M;4o43l[>NG6^T$dQ:>jDsakU
=!&1h@+F?dN7Gfb/CJs=L]AT^d0Xc2(QaK@P7/W3(RWVbQs"mOrg,9o\P?EsDh@clHhXH$+@:
"9Wa:Bj9UJBI8ZI3BK)gf.)KEf:Xq^:_"b=EYkneLZ7j,Ric=ZZ;Y=%Q^stQelC:g>[Z6L6@
_:H0T2Ot:A+F*6G<7$rJ^-U9A]Ap:c=fb@&6h^clNL$,2@1#UI;I#YZ&N<U+:ElJ`o'BMcE:!
U`Vf$8@a]Apg"=Zs(Qg$k[7A_npb1T[fGF1a)Q?G5\nqS^_C"N$>fDs"GN2>PO*mh;^*,P\ls
)iTV?7iem1e<Uph,/PccKFLP8b9GFa3#uP2+'pEgTYXnq&M7;<'X'B1QD7"S^/CNEEm%T28/
Jl:M+gf*DcG?jW[,:2nhhblM'uL-4<e*;u'H?'P4YY\Oahr.rPX^,fn'/,c^aufh^?p]AK-4T
WA"2@)@SeY?<AR[Y?@*lY.*skfCiO`PG4QQ:JI*G"@dDTr2/73#YP-o=-P+:I/<V8GC7nZ$:
Bj6%?Z#*ou/5VQ%T>,JSSc,Rn&i+O2J05<Y>`(]Apc+m.lB)L$ck.FL=iUR2Jm1qYXh5"al`"
gcS""LN0Qfrj/j^*Y>3]A-_$eN$_[]AqafK$F0*YS47LfBd(GO.n/XPJrI[h:oX2sB`^,$&P5&
UooSMMO&0%d#a*h]A'\ch0Zd1;rnsNHJH98DJSn@=L:.K5D`Z/n+?<$W;Q]AaM)SFn>[+`t<n!
DDWLKP"!C$j@_(4K:0&$jhM0:+ML=Ee<"I\Y;Rq?f=\_dc0G_k@m50o##1n3"ia[MO3bl8JI
d&l=t,_Y`3r-&@noS`\AEi;?ZF6H-lD"6`T<;.YmIp44oWfGC>IJIg\_7ZIo+2UaOQ/q0:#J
a:N@G+]AZ7Qhc-k5ahuBHgGeU4LNgEGm1rH:=7RTV-r[83kgiWh2(Noh-lNme!1S,4jhkb`3m
A^&B2rl.lQJ-V1+<$lQ'g1o#u,1.1pMi.J8c`,b@[]AfePZ4))qh9ljtE8&+W#`qp\Pd;$lMg
o*(I:`BaZ>/X,=YaY=NYJ0Y40PZ%9cfViAjW1^AT-GKS!&<lV]AgVRU/Z*tFNr0c'AJ<l#d0F
iV)>*ct@W=%idAUV\ej9C(GC[5a4Dd=I%_VeChS=p+"n"gBoq)al*H&U#8%6"+C4QSii_\./
=$<OW-VX_p:MF#BQ+\"C\ms*!/#b!#d%URF0J$cUK3VS]AXf7Y_i$REGpNsR0dtSW$Uj81<qh
PnArMeF`$rEg";)([Dqr_^0Hg;l:Uh@"(gk662nb^]AtN?mqAC!Kp*s1dguhULk31VWKW:[e0
#(^13C^[*!?2eS/?.:12rZ+,aOX/hb]AgmTjU7Ms_!$/Z<0)c1J)JhHRK]Ai9%"e25aq3V7Gsp
(iMBEYD0Q[2/NY_Ui]A&.`r)fU%R1mlZN3uqRmo.Gg;?.jXIM$-KK)V`nYU$iS>qt/Sjl,bZ.
;1l-f(sMIkf(:A8QeMebG&Ar#DU%6i.YdUo;#G^Hdjp.lM'p!#GJU%SiTN0n7u0>FP2]Ani(C
V*1B<3(#"S@Sc'K91I"U;Z,`i2#<D=@H[=VT?fGHBtm^r<Qm"SgcKDlcWSO#`87f5js+%;>#
"1lA<C#-(0':,^W\ZNCE#DU83M:'Gdu?:X%>#\IDjNX`&d^!eh=jB,.JV&%"Xa,BE4b)LpDA
+_XDte@7>&D%="XD>e^4s4$72"4[.OtNq!l+#[Eh%giI9mQ#LQTm6I^I3*Ppr1(*DLbY"b&p
mobqS`4)!4FV$C/m(hTRGEk@qmhVLHDZLTS>[l>>37u-l*j!B_@!LAT/3?qo0MJDgo58%ABB
HH]AOVGD+IX<sY+Wb$Xp[C/G!EN.rlJhIQ0;/gI^U`M`;-'oa&k`N6Y5+S5#/ah.b>Z&,KVe.
=4KRHqlNRq_=*f%o[GE=O>35ZX\FXn%+L.hp'HoV^3+)6"Ua,/:DcnG4R4E_'7)cXa&K'W+K
<Pp[A+HWQQF3g0h%^$#7UUF1uuEs/sfsKU9;,]A$bS3c\WkV7'N1q;Ub&<q*5,SuQ2o(l3KIT
pQD\ad<QgFN0>i\sne6E62NYi^#+*1%8%u7Wh3.A4Sb@CpkC":07lmY0VOTPN=]AB9/oXf$)R
D#92Ul6V,3_:`W4\osC:a33?<9\[i&RD\p<hn6:9H7tCq_dJl5uH-Dl3ZL<ngaFOd6Y*pq4+
^f[ldi,oiGs:.fL3'>\-hs3r4"gZafl<V.rjKW%b?VpEMsL-S2&MA,9aC/VK]Ajb^nkup'\c?
Do#1=8]AUP-&'#B]Af#`U%s6]AVVE;llm373Z>f.,U_\]A!S\@'G.;?I,XUQ,mI]A>PP>:La4I`HJ
"mhP\Sc>:hpC9]AG(I:ipW0_-QC)N*l5.[>l<'LGRjW3#u-((J+@/WD<3pQ7,uSpn_>T"m@F"
M<-*f>kVo$?kurHs:mFJFBBJkWgE`r&b]Ag<@(f'nY4F8<om+YN2os9,9Xqjp_0e^Ae3NfP+\
Wk^!-Imj*_8F``>uiZk6QeXB047,u$pj^c357--Vh0"*Qqb#.FYF<^^)J$WD(%(61mToO"VC
8\o;;p7S]AW/Tf"mrV1=n9BBe/]AA%#*7uGecgW0#SF#AQ1BA;WFKim=C,N2u2]AQGK*6.^)9%T
KnLjQTD#M9G;i"'@[`A#LBll*HbT@\:#<@c'kgEC"XQ]AVg:K:JSI7<-+#DC)59&^3aB<dMMb
<BTnGPe?H&"M"KbEc)pp%?$<d3p?Bi**=r0;47;/3oX?W*GVUeU*V[-.KnQnTltf3@H^h=uq
oo(d)+2J.SXbP;/>Dr#'((:sLU1#^lP2:#Z$>n<%1-itCei?qRL43CqF,pPgGd@<5#<kGqp)
3`<Hfe:XmNs`m/b4!cO4j3OOkEE:%@oE.cnlBR6pXKUXN3;5j"qRa_Mt[I5,+/N,NkU"Cir[
AcBH&3]A>,L[eB5o.TUgSf(q#QmijJj'F?_tDX4aD8#hmFeJ.cFp\-^q920/soKVs7m[o)\1s
Tg-PeUao2d$aW2YQ\ZJ*]A>((#!*hM'7[`&VldDbZn/Sq;GEuU?4X9$"_"rTN97nHeg6GaL)R
m%[<8<U23VXQAbd<UFpK$SRY@6fBl-pcE+G5ms@bfI0GMll3n\0?$6i6gm6X5CWh;\K.D(W*
_%/N.o6PMP?@gBMHaGo2e9FK)HqfSV4!itW.4n*XJPU<*Vb@abEeBu#_[=O.><`"9@"8`F;?
_R+$m)G,UmR6CHMs=oT[=4_R2\2-u7@E<pFj2m:q*W5;X$k6=I7DbQ3hBt0_JgrFIXN,VH#f
Xi]Aq%i6H0\mCLWLd*Q.a`5UCU$rb\q9/\XJphQ;-9tC3";bga0/<^YRDf(&)@9%bb-m0_1Yq
Xj/fe4^YMQ*`a%+Rf;1(EpuD.A0memV[=!%<DqCY(doHQ1Qe=n_DB(G@41icV;I[A)EiKJRo
bH5,?H2#K5-3>Sggd$MigQ%c/&r*PI@g?inZQOa%R9LZR?H5h\"%n5MBuP<G6oHr[,YdiWN\
.EGQ;%rAZF<IaFZ41S[iiWp96AbJ^(:QRo[>9Nd_B!7V!?<7n>GbJ]A7'X'uK91[s\Gim;_/E
9N!@#PsqAan5Amh*-e#s!mJt^4%rhTdp=`bf1po.f4l0eXo/CjARP\nTU`XZ8He&P.%:rMkN
KKd?FBe!(fmPE.0pBmU;)&.?-_d%&JLN%XFr0'_t5\et;GL\YP5F[G3f?Q4!=tY&\G4XNAc'
ni8q\Xe2]A%<m%lkQgNADpJ@7`%V/_cU7M4+V7_,gX'X-37-ZGkN_BfHF"o7e#3tAg+)%Gc`6
%>Je76.$qQI`Y#,6>:J5DE6VW+1jWlG"tnjA/4-#Q[kM92mFZd9MO3qGKDdK*C]AL&.d1F/k'
2-=P@5QL`VFk5<(Z<[C/irl:uET?co%gTdFLf!FmVQ<#M8h[.+<3g6W['MmqC.7Ws9Al\UK"
k?Uc\\\1s3GsCOT@g>?l*"qmD*?K5j6G;A>="0m1!dScV\p>'MTGY4=Khi;IReol:Pl&kqrK
n/;nJ]ADH2VU>CR%nRjJ'S[*Uh@sA*J.`dB2M8>Z954;104l?(AC1OU:@@bKf?JHRSXD]AMDDa
@:LBK2l<JLYX&DXHXDI&r'%h1\5%Dr$!,3`S(>JSZ>T&&U$o"R!D`3Meesnh0LXupZ]A/r^.]A
me'=j$F*p<;iLQn#m3%a)0:"0B87>8kIi$n?=b4a5_jaXnRa2`&lLfQMAeJ'#JnJffe?<Iq+
eAs8YZM+VU,R5/<?o@(X)5'Rp<4lG);,9+aAE8A0bRbQfbKO'"KVZ>uVN7[=^"'*"4'@JYE7
#:k(s0tdi:NE0oTj<WOq4PJYroeEB9>2+,ZHmQT9:Bt(F=?P/[HhXH'3q]A=F//s8+RR"$C\n
u`I>]A<fAZ'`^mPEaeO31qF_$'qKS\a]Ar6ttQUf5lrfqclp_K#M7Kd\c5*eI=$fjdOLWrNoa:
6JcL#<r261`M%dQ[We6*hQssT.?:^,q7,R5+iLUbO[BK*rH_9/eY3&+.1`V/M>DQX-7[hu02
>id+HO`cn6"amidABt'cVJ1!N]Als6fbJaX1_Z3$5M\*l-hq9:#7l7WI"A!nE>htEMLIbMd*1
Z\()PZJllFdM5_#GSEq`,rao8T)<K41r[dtT1SZ?#*osbTb"GBun9J)hEK2k]AgbJdKGdF>K$
s,HX8M<.>k"9NEo,5m.&Nsbi"=G%$$LdC16k./cB8'2\n=;A.?UR*L7]AkB`9ceVrXcaRV9.m
KS(ZA:_$Gksc"]A%-ZZAgk;XUcaO<E'b4.Mbss@[>W.5XABA=C*p#aIZ>bMRF\)2V`fF17I4*
/*cDpqPEKL7Ea;@XDot,.AK!5TO>/XWT$*om+bKT="+q]AIi0'qNW:ME.#9`ol#n`SE`Us)?F
HJNhb`JM1'Q7TY?ej@jLp`_cfLG`^/[H+a1kaLKed:3"?2l`>?g3HfG9,s:k()!Y0;11eE^0
b/hDPhh1NeX7#*.95!V'T\O=i4Y<bZ':>.Xr1_<ss_eEIONp52sBf"H0D(]A@5>@Y%F\\]AWW8
sVaP@;Z5u%+L3W0REk)**9V,;pH-4Qe>r%*;'ma)BBp0mcHN?,,'&5eAfe?$2Y*8KdRk,L_X
MSOOSYN?2/_acmp1933Y-=:62k:s2Dc9n3Asd39@V0BQ<^)ED[i(R4=UHA_3!Ae/Q=E`R\+F
L\ad9TMAp]ApO&X*PCkUQO`m['WZSdWZ>5)7XNcj@nTPX3Po!-Ibt7W55JO1=jdhS"B<(4"X5
J.c_VMtYNSC`m01?g8lQs1.Al9OiQP)N(mQ<l@$iXJliKaA?6;J+pTN57HXoQZ3KF`$fH3$Q
ao2eQ#DdAb&S#UJ3Y;5cHjl)7)4Ped8T_=\a3fdhhZ=*qA*k*fKG8t9uL<>)[cn/1JS<%n*-
aUA#5.R[!\KaE.PnHeZP5gX*e?lqjlX!jT]A<rV,TVO<I&+Mpm07]A?Y?Me=F)6D%-E]A&N;,!"
@UB[Q(10!&uS`XM_VO%LnNQlqU0n)?Lg4E7JRJ'VZ1%sA`a;.6?4^SM?u1opl>+>`g.UtSVE
h\gM+,qbF@BY0SlDm!99l_.UNj&+/H3*joKkXm7kj(>kG\M8,;jO!_KR&M!O[@"\)8LU!@/u
d6C0&5Rm:p,WFHq<ij686VBm;N`u3tOUMrTX$66rUC>%!'QZG8(+R7i1&N:AO`6?P9LW&g)9
41I>si\1]Ack[b"Xajn]A>[/Amb=Sl-p$@)r2?hU@G0,JRn9*+i35S:WLg)VceCn/%]A!%JHsRR
5EHX-)[en>dOFV`<kM[mQ_DdI_XQeJg%s8k6h_aTHGW^.k)WMe160^;aF/$8Ti*?8&'_]A"+4
d7'72un+c?9r-dW=G<#"i!DD:V5:-%ao_ht5ZLHLk@Lm:C%/ptN"M[tk3I'?B>.U<=aDAm8B
.fSM(6epj>AS<oJ#0*=OrFC4A$"eRpY((96)8De-q@nWV).EN'YCTq\<F/WV4_6=LP;NdfE?
reH(]A]A-p92E'c-.g8p_G5Md>KrGL3nc5gW"QoK/<<Y^;Y//f&PANoT0ALsc_#K[;Z-'J/@l]A
=D>hA)2VI9jA>&.0_7eSIB#sAXa*C7Y-'S=TP2RRrf4Y!EONMt)Vu?J!&/M+0@%OD&Frc(=?
?X5%\6noCaQir_P!16Og\(lAo.Z'EHP"@&kpOs]A"V#1`!t->_2PP>b3aS<`NL"PHXQtBTA[V
Ui(Rt$e`D=8H-OM4@f)(hU3T.Np@?'c`Gf4p0:1nr"D*@B=dp;1:-r\RdV@9XOaMo2T9TMX-
Zg4O(Mbd01Nn0/HnM\VOL[SdAm;)_O\d-'8bPut_92Ig$/O@[cinGq[]AUEE9>0rrZ;!o]AIb=
,Yr2Ud#V@On-d^YpH]A`0d;C&/XC,@j%S9<?^^3eMaj<+e,?N1ljQL_%$^07kkOP<$d:2REEd
mGqX.XB=g(tQP\3I"@)!S/mIK:3mn.$L@j`AWi4bZU*`ZmLP.cUXZ?\cR?#0:\oD-'@AXV!b
NDq!rm#Jt]Aooe>/j\1Sh_'Lf%[Wk.6u1&'jUj/<GBTJ3@emcf.rb8GL)4VA#bGoC(!<kWP=?
3HTu1^l.7TS$crR(=*FNpFl*a?Zfo[8^f'f@p)>.2kUmk94iE+)#P#SW$-Cq]Ad:+XUVh%Z8b
?+n65&E9mYlP!u49U"Ibh/;&&CEq=^I3g@a=Mkasn3A`d7XZ_u8Z=aV'.YGZ#K9C'>I[.BU%
WlA@Uc8i.8m.-fj&j<2s,7s'G>sqX`@\L"tq.?YCn5;U/7IQj9kH0^BGF3Cu:1N]A%80-Y:=2
L!SrKC\,XR.XMk+.q"4$qLUr?QW(P1oh>[H~
]]></IM>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCN[0P%jYTm*n^(M`Km`XZG.&23q:']A>@O9R\`H@\Q;gtCZ^5+^pH.7BprVDd=NgXTXg..:g
$r3:l:tkOIDmlTH`16qP$\Y7F1-]As6"[akP;g+f05h<8^;ael^[Q0hfI(cho>,.Z;[7!ClXC
1MOK12%7EWR'f5[[qcSfK<'-K2W2LsX3nS9%(R7!K=1`S(&$@cP=&oo0hsl]A0B$^=J?'j]Afd
E-0$cWdBKH,$@ABAj?(>4#=h3pATsq<qG;E3\\S5GHSqHtc#SOmS9@OfQc7H+bPHU#)A?$gh
Rf)qODf)Yi>4LTKY[kuZ4A!uuBpO`2`2=Cbd6'`_lLPOt^1I]AOTn3<0DM+JhHfcsN,FIL[.[
"b3H&h<Qi!E/*BKC:QT2&/?h2\@kDYF^N?u%-e^Th1P/q?CUGl-Eeo2P?JsZZ@k7;CQ=?5?O
Zj%V6$BEDXseona0&-)>NJU4VPDY;8Zc3DI-2gG1D_gQ]Au^[f[e#^oS^[<^X;=TP?4%,XL%R
SY+e!jdV$0`Y3L81W!"".OsdF_!jb$$fA]AeAcalt<[a.c1Se7MMGu_teIA(`XgI`L@$5Jhd@
uSs%<u(FGoU;mNp;n7QI]Ap6f9O:_JO'.Bt^S26!10<R<:nRFA<TTOt%Y($n(Ve=`LVN0mnh$
bM>YdrSP:m`5X+P2hErEk0</SqY7A!ecY%t]A.PjRZ5g#fO)S7hEU7P9P5KaqA%AZb#0L!Bd-
H=Ss&j3X,fT2bZ`]A-eEp[jk'D[Ttfgn!KkHpijB7Zj40)NU_FPn)(Dt:&If-2D-<Sq_+YKM&
kC#[!W6t^!&c6BCXlrJ3mrO;L.e^YJSfr^FoFGk58NGhrU:D&/LM\riO-KJ:BVg?UMO\L&O3
ToSi9h%\g_<E;SW.5QA'Hr&*qZV[%LeX?N6J[he`!ApjVcA$u=BU;*SpdeY<fIl<G(k+,.F[
"i/G?YfVB`?08l0),s^RLNt9<O$(\Wjht$'pXtqVB,XX0-gK/$Mg.--#Vr-TDi%X_o^%4Sc;
k2q6qJma[K4s9P-:?ZAU4eM]A.&lfL235m%S%_UYS9rD__VBOb]AsEi-W+G@,Fpg<7Wosf6H=>
jViJSX+T-=CL?r$pY3;'1UeD'dR=DmVG\K&<q=ZT\j(8#ZMq%Ek<mdJ[!OCeo^0n1B#DV1GP
E9(;aSjDo-qkKr1X=gD7@p!0"h$q"b(l<AsaUJ_mK&HSnj:?s)#,%C=:'Q*4Z9I]AtH<ZQ(`>
^YN4Wnffe4!7[mZV^b\nuK&Ge`_9l24mJPqm43K+h8kDm'f.\$&]A%^']AI[p.]A7?7r^RM-$GJ
]AdTXn#m<ErA*kT,1g;&pX%c".EdrQEQ4fih$L]A?Ii>'sP"HnXRWOChd@fi)"!MMLV-OiM$DW
;bbA7>HeG8D;?FF%Q@L=3!\@!#DDg[DWK<W$#/1O)1A;C2Jq+:hoqP^E0[9)3K(%X=X[N>SE
ZVDJ6DWuOhgNrq'dRsN&fH\MmcV+PFQlnBRn%8lHZ\'hPW=&QW69b`XfTrHIs5l!)&,BI(=G
NS23u"DB.*&mb:oRq$YLp>5-s*HL"f%u)0XhGrg5hgU6*?u`J6f<YNfFhQHLS!&mI</)W-7t
%)Lhp<*Bd+jrT/eT#WEc6%1VnKH)ejG8l0n`DqC_LbLnN!k`cq_`pEZ9[ok0]AOOJ3S%Z/rL?
TeY=\6k]A:M"5lkQY>0+:Sm:BL$3"V.H?a.2SdeOYT&1DC0J:>X47,nA)K/PQ5ngZWOi08f57
(FAZ`u*r$>]A7hRF>BNM;6*JPn&^eQlV?X_LFa1#d'//7`XgVo2D)Z>XBfd+o^%6RN?VPX)Y(
9&[\YP[tZ&.2@LSj"a,YnW8jHL!7-=lmD6]A46)Q1I3E:-Xh_k1^Zt`h?E>#F6K@Ng%jaL%g3
amaJ`,PNXIj\b9"?[I[.#S7AGlgK'3!-?$_^__:*-k[>/uXfDkc2H*Fi_o*)`N7b(`S+6LL&
c,`bZcijM7Sd9L=*fWlIZPj#;0PnF+KI%oUVC`V#2^@&`1Pl$atgqJ3hgk*WBOFu[fXtNNdA
@`IeAeu'N5]AjK$`p?i'+1&`J'_D;&$ar-ABe--$[M"M[Q9t9n,'L=IEZcWEI+J;79%I*Of5,
^i@'LcChZ;Ip1LS[ufi7=?Wbqa.e2>7oXF9.q7k`i.nVs_10*_o.,I/$-5&b"X+T/DRKNhL&
f<T]A6-6IQ>kBLKL(N<(q4`gKN;Q5mf*?EY$.=(^>I$f(<17UAsABD.H^dqnU8jX35muprP+Z
sO-jP0$Qo5E<S]A4#Qpa2;qZeB_pse/!7@Vkgf)_q\S%K^VlF87^?BJV=K',RQqi\"Qb`CYKG
t$?f$42X$t"htl`q^^Ug\I3N(uKbm>rA9VKTqVs`#D%")EbKjugk,1p(iB9=r9Nl6ls%p9B.
+WTm$!so2M.`0Tp<Db<GV@qMNS%#_H`^8K]A$9tB@,*.kX-tk%[*,#`;05KHj_^<Lo&ClNd>7
>ObDgc`"u^n;dG1.UP(%g2jq)@('(L]Ati5]ADI(dI<n*nqtNXX?Yt;^P'^BHu1?Q@iCjiQ2='
CO1j+EHog5aX@[5N#atH4huni)9OXomBLY2]AjW?52UUZ*4/fMt/R@C'#\4l[$#%S[$NFJ`pb
CJt>Si<rR$J7<NSnGY[55,HJTf[#iO1eD>G;k8iGs.5\i5_!F+U!9dr+hq.`I2seX7NTqH,P
B8sslaAJn^_D5ihtckB*iDt7ogiFr_=!N[i+WSo9?+2B)B_Bt^tTi.00dYdbQXaS+XPcJ872
;;2_1Lcpmh$4k*A4I?*fWPG=T5qb;iFe,?P#/Sn0sj;;CWnP%kMk!ZD>FD"lX`\-I('ltoG:
eCMK5CGT0u0B(ke<0>A<*OjG5K8j@1P`CfTn]A"pKIf%C9mX>t0*B[_)0cNK)kVN3Q#lKg?Y9
.7M!o0B?rl1B3EZ]A]Ak"Gl7J2cCQ"V)_(sK+7u:R=N;Y89hn<]A6f]A\VG$bl.UV:6'AYH^.S^<
Wm'NHI]A!\R*-Qi1P&LQW+X.6.\);Vh80V'!kTFb1b2-X]AbDDZS,g<6R/PHQWa?aUrPk/$S@I
L/"3^"3K7BWT46i[grr%@@T_WFD%n$Wb6n(`5PsiU]AXuC@XQ2m4D%:=VHQh/!Pdn!N=9PFGQ
]A1uh-Sn>FX-DoS2!^L>,a]A9887SU,2(CmH_fT?:,nA_5#j0,9=#5':cS.61k'`ZJO8HrpU7>
\)9r6Ig22:CUla+_-&Aj>]AM_%i1.TO-s`0=i8c44SZ^L$(s=H>MT?5iT@iD/Np<G\.QeU&"-
i)pM@,.t9nV(/^H)7>/0[92oM%h%]A0Y@qlSFKJptJocD<Gc2$Ki>CcUUp![&]AMoH]AjrOF[5Y
;%4H&T4lT-RZ?=c[WW)hff2(+(nK=?Cg4-\:SH+T7l&7*4K4EUArT<7;dJci9DD-Pa5q4$p`
6RS$iAAu9>`W2=A.Pn)"`Gg]A!>CD6lbRbe#gNLN)4*BV:4K@fFP%`+F.#R('K76Di/BV'\f;
5Of[.8gC`mSY"r<<DssdPFCQOR^_r#.s$ZdnI[!TD,FLenV6XHdp;@.Kr_W%Wc<&*G1T-493
NK=:`',D.X[le,hDF;OCAQ2+D+_emF`]A^ar+R?;r#Be.;dp6mJJ\O/>Jfc`2W;hf-J\Hlc@j
e=2DG5p=mceF_j#$H#E@28mRS0+[bQh!Ut/!;A/!mGn@JdR42)oUJO6"n54M9c1@&AQP.Mj^
g3BH`oJ_U>I"LKXK##5n(4>0l/=e"hPc(RW"rSXSBuGM"=C>F@doXc*@OtdSZ]ASC,SC8J;gX
:$+9@'GTZ+rd$)oeFIL99;'*$_'ENIQJE[>_N=k()s%+P_h)=-+SuYGsNgTU\#7TZ1_?Y=J!
-TAKIEI'+`T17V6hQFuV^kh9'oC<"P_e$^LfY-9\Uh:U7f1d1^HfW,;n*'aX:mDO1@50rIUE
2r\KM$9UW5VuWMdhrRW(\DC-I=1DF[<XgE,!?:#bYfA-Ag#.dMn&AH?dM0.7j14\G3rb2+[^
N^"n<T7$PWo&L`o9mCSQ#`PWHB*Gotq>tC(`1BFkStr1-O<hZ)bupHBU55A4Uh,#??.[-tP;
[EO-fac.3()ap*@\GIbtn162pHCXQ@S&T/c:4;;'Ki8YlU0Qgj\>+FAA*c!TQ`n2dq+A.h@Q
ADE'mo`:[hWND1SWC<!k"VBZCJ>+3Co=83plrW>p^34a\]A/5eMWkHdE=-%TB\*Qk0QkgpD6C
)]Aj7P'<IfSTbf5oT@)5_%X,o`[jbn5$m)ZX9GZ$0kE[N//?JBn>_Y=9pOor-WUUI/n"dPYp7
T9iS((@k>A$Zi/@uMOJj'C)^K2>M0B@/CaZ.n16El(U(Jhm`=W9T`H&#GTYd'RJjLGEA42d\
J/Z9)dBasDOf0R3^B32>k#tU=HF@X]A^YbWOG:$`FK#pVY:iM?_?d2)Lk_XkfjX-Yirmu>>o7
(R!nAZ,ej5EhF=+2]AjQ':>^$ATfEFsPJs6YRf;YF+$e!Tt3NI["@Gg>`VX:Pa9A,QcoPm@NX
gi@I;&Du+$Hne*Ob;:X[ro?6C/3BW?'^8lI"Y9mQaL8D5d9ki/HFQK?/K76aAm?NdGhTC*$A
u;;1\ed^Y]AChMn(c9Q@QMlpK(+<eiWc3N9=t*"HBee;tS&;;."DbpiElL2>`!UK58ifAQV?I
nfGc+[ihue'LF":5XS-J.gGY?_mEZA.-BspnsN32\ZN+uFN8X5@q0gbDZOlX0PpUHSC7@?)r
I9ueeXU["U,.I_6;jP+K5\aIeH7;XsPKagDY$t6J=ONHK!AI<Gf1*[>hC\P(6Df*HUnlcj\?
a^>9dY-B;`tm&Q)dh6eY08HgU&Q4PMm:jibW#>W6q\:T'0FM,]Aj89T6CBqi6nUdbh<dh'bAK
dUd,[eZ$8_(O\nWOTuCn<Sh9r/BB<hAX;bg!eLR@?,SDf<D!:C/n(&bD,7@#ra=qZ7U?ZqON
Wp/M*&*6:Je\U%ZAo3.NBUC*)g@XXkO'YeU/BjbJl3I)%gIId,A#\(WVUR/5pJ=]AW3Xb!i8N
EHi.hF/1(^q6I!V*@o)pdT'?,q)!Di6s;_)T@iHU@>DTd]AE's3Fa8_1($b4<&9&)nGi2mn^Z
mVdpO.I3X%-)EO\kGces),1FZ713^%$Z/lD@j9M:l!8^/pt_U=SaSr`&e50P_J)!eg-jhi]Ao
:LGmgKMcd5iO(#:n_<KC]AA!ll\!&/KlQ:*'s6!nu##=aZ7gEf%il..XhNgT^s#nIfekG.V5m
e"KHl4gur]Af]A.f44SRH'nh2qa/0A_=-<'J.HJ2lEQG?re!fs?(]AP3eBbp?'8NW?&ngan$(#+
^J:$/<T]AIL]An<oi1*mF=@r7sbnbna05YW9$MtNBc4ZK+CQ`87lk)FlM"/BIK<iB)$@@<]A^+%
e*a@H(80ShZ7GXd?=At?;NY&gUp4ALAO%lE"0]AX4rH:q.c9\e5tT9OA!VatGg66E5l3GHf0N
%*"ZXR]AJPbp5j&%T/R?/4^)b"^c>IiHrbPdc8^B:d>.d]A"M?@V;7::Uq<*M2jGKX:;6pC&=I
j_]AK_>#Nlu<(r>;<p$'bPK]A$UuNL!h7?nB*4<\:jL#4/"r#:/V"\h7#kp^L=-BrR"s[@8an8
VR19l?70ft2QR24@[Fu9IlspRn?u_MoEnm!6?((:31)*Km<&d&2Y6SO=[N--)kTS$H>3DP15
-:hB/o-JYSkhnX,mLibJe@W@Dd\/IbA<IQ/N)oX=_"DOVs;<A$Z87EUZgu6I,.AYW'%kgWrE
Pe*3e3_d@TJ@1Q^XU*>)[5`iam6#N,2f%Sb27a*&.IfPCX`3&M6)8agV@#c$mMf$Z%q+F-HW
<a%]A(:*Y>.bp_8tOFNEmLa,Lan4>b2?9e!J*`9R04*L8XbZk9@b!X>%f6]ATq>(Al+KEW^-An
mAkdTb1dgCq]ArSFG`NOe?3Xg7m2=o<FG?4P<K$?ZpN5YmK`MD=oHB^0J;[Vhi0OhXHWbY2+t
G?I_ica/EA!_D',=ol7'NCXe=LTcQr"&mD$JSLe3F2CTu]AHQK65&kW_$A#2u,TSs37!%VKK%
mQU7Tl,dX%nDPu73h=f*b#X1,D71Q71D5^7pc!/q*^@I!Qg7*VY%.(o\'j_F[9+T9/hbC.d#
[M;0%4]A]AV[JdF,W$.%$ifp@mXU_n5iReOWdBHE)=]A_A-i6\Znr@uh4qFrQSUhh)Gnp14-FQP
7Ti@*3h*LNh(;+ni:isD%*JEabQB3bY%,nO(nZF$+#CV4k!4\rjFRN%9%d%%Cid4q[tlJF!4
(Fr"O4g1Wkk#<,oXji+8+<J;Y3,nE()@E0NLM%8"tNj&G$*<4.'e4g+8]AX5SUB6M<nUg?>i;
"=!DnleHL\P10?0F11L%gc:,VP7hG\I\_]A.7fchLr%Rr&@+uR3Id[hJ#f\Z(tpB;oL24@uJ>
3#@^KC&@b[,]ARa0#)5fc'N]A-`6&Cig7[l56uBs"<"*GHS#[:kFd^ZO\E*X"k)Pj_bpkBk4!_
sL9M>tUmYas)M@Sh`(YgB[6GmlJ;*qbB05=hT1%/;gZ3i``8n<D!*WNCEn)D-J%2\q8FcA1P
Q&Q7L\-"6j:[K$m*knq4,hr+IH.IZgqY.Eu/GV`D4Cu"g6N5W1HLn&4:5AkJ4D^^I&S0iRZ[
5%\IX_1Zo,D:;Fb8@gIU94NTrt8hCJXHbTBmj\@;C$n6V%`1.!=L+*EO!I`(u:<AZ*PY*MiK
e>ss:S'1tb\S+c5W5H&&5\-gpG`sPo1T]AETTZbEuDr4HoO%be_G_+SCQj?_8ChuA6WiT/sN6
J$?H>[llFd=c\n5EtP1]A\`G<\uAj,$k)lWLS)^PV$De%G-Ae/=Lq_\bZTk'qhNoUH;kOo13B
Yg8Qg]A`;)TI"5+,oVns>D%%NQ@/3-]A_lM3Mj3+`)cB.'Nft;p>2DJ#O.O]Au9#7GS%([f/[%"
WW6X7,=jpB&"Difn)kLD)XuO^5s*NY268&h%E.1@Zh!V^TK5.\*O<TORnp/JB!)OM$VU%#CG
7*KJP3)N8<ddH<bCC>/8><M^QKC5lQ#\C':GdqMK!Q3o<=^;8]A7>k_;r_g*XhYcVX&:q<Bc*
EW/F@r$Hmko\akSsRrnk#,dGr8k>%%.PMXqo:cie`_nT(]Ad\iJ1;1@2F/_!"9eD4,_o5YpBQ
/%PM4<tGChtO'7aHGtq^^m-L^VY$^M4sZo-TYKPV$PCAgDp3j*Z\#g4]A^+!*/B]A'rG2`>JLA
-:X,W@VmCm?.OOZ6Lb9"r%V7>lC(qG$Cj&d<A=nB@Q#"''2!MM]AcH8L1*[B2k<;<6BXWV[mp
@]AXdX%d1%.@9JS6'i"<AaU.k,;^EkIJ1F4@U)l/NlQ&!IP6Rc1R?F8E"m?=5<_pQh8^*W!_M
4OZlp)VM)IQjaG-,c8cG*]AV\E1g*:p@*pU3E\"T+q6oK4E&M*7H5G6APN$Rd9F[WQ=nth%n0
un_@7L%/+-(77DJp$gR:OS=CcS]A$JKq:KDYB'fNi<]A4/[eS+r<)!kCPX^RVW_2=iItq#:,37
`7qg8&_[.9A;VLU-`>H,^>B4n7$.KnB^B)o85!";S\qO;(0#.$[4ArT/qcS.*0SCf]Ap;l-M,
]A9A^ll^#C+jG9c4!^n(nUUTPm)YN&%qKSm/CN<p(hB>tN@2YK7/rqLn'EGsOXdQHMuLbZN)Q
2/8ql2bAf[EGJO2.^6eM:H"("B\RGUJ=%/0.e4H*pk2f5<M>8,E\!:`!f[Xic_UUTr-jg.8g
8WTI("U!Si0a5">(t@W&1&Y%qngRNW\sHX5_,/:)9#_25j"igKf]A)Tc`ip@?O4hXDGKDWS'!
9E++[*&%i<(^Gl'.l=f8-Pr3eV<o6*k@pfG#Gk8RdXip3k&l;?Ubg/OR"^KY4HJ%UUQVpg\m
iG]AbIU''#;)mH*'(=e(1`%.iNi9`NeS9S+4bD?L\+TJsqs@<(q&$7U+YXTa52<QY3P+%rPa%
42DGSnFnk:iE7H60g,9p<k#I]AM<T39F]AL*77j5\$Q^em&SIVu`g-KR\:Z99Q->8q,@3*3+A#
)d+Y%kP_XtePD#*rZKDdMASOX/K$LEHLdD<HCfMcSN0]A4H`st(7dagjMp>+qj?Z%]A$QB$Qf(
@1u;XlY^A5[oRo2%q"88*/2#cjrfV#tOh2Y(7rQUbnV"MENsoOIufoGluu')iY%H-"&Q6Oe&
4C]AGQS.;\o36)qs*W[+5Zp3pW<LI::I7Eoq6"4k=8T*A,/"@@Csp*[_C.E^Z5#6)UALE:oYZ
O^u%.=?"qcZPmNIAbM7SU]ABqT=ka.%rRqSRhaHm;KMCI8G9I"j+$W:+5Uf0#:fbe?[[6lqL<
:uLDPa]A$#u[)Qd;>si;iW0p=3ddE5$eeLG$Sr8^S@0n5iib5PDOHmj7u9Q2fF=5'nU7s3cW4
DdcAgqF8f$,k+:"Qgap%?[_cYpE/0OFhFL-1US-f0<+B]AJ)`GhbRA+,jQZL6K*RY)qnPGSer
L>:ru5l*IsdLVhft4Q^\2-Ro^?*[s6dl+O+-i?]AmZp2:Td.Y^BbJLBdOb*o3WbU&IN*Ragj<
OphcW=:-LE.2C4iV+b!V5JeMg&#-n.)84kRnlU3Rl+sN,V)NNE"Yi3VrmP8@eip\E=dh4'+N
KF:)jGD@B"7W6[T?7)H#r.ZQ/U?#;.pXT+aqKlRV#5Cs!?f=/EEL2I-),47^NWZ(T+h@uX(%
51("9t,N9JQ&M.FY19!A/ug$l4PYZFYU'95gHd2H7=c^H56##1IqKu\K]AU91Kl&iof8+fJW/
m.n64FmC`6GL2:,4rR^Koul9)o,cFH\UF*trTWH5h^i\;>>aQS0/UJ?>s'o'`<jgWI*V,$hp
9J$QOnLX0uKu\om7f0&MX#i$/L5PN`>euOdT8*5%A-YG<IN:i^eQH:3SED$e9u-4:Kse4uT%
d7^Ic.__^6?anH2phJE_n_Eq+*EHkN,l+S"nGm)\p2pLef40Rb;G>H-H*&KT'_6K<WF.RmV"
H>62b'R@HbE&QE@2L4<d.>bYG&!$sX.=@$R3=Xg]Aak5L,DR\]Ar0Sa'?/dedBo"q[0D]A35Jr3
.2C'=q^,H98j,eXYHY2m[RDcWB*1;@p<*uBZN.._p5mT^Yj2m,#gp[UF,$Bs05_RH1CgX5`Z
"4CIB\&;RMB$hJTHYe7QS#%k"(m_QO%?1SRR*e]A@crhaE.#sSD)kg-E]A'nK]AE5XV<;=1<0,P
W%KA>lZXj<aS*2polclPJ+'3qb^0.A`b.,b8>hIX+LjfMRra0>C+uF9&g_Sl'VnV2@A@3TbW
C>k^&g\4c#4oTCs6anJ%Fl;f7+=HGKuqim?VH'9AY7snZF0G<G4j<WO970(G,"A>fRj`Lr*$
@Xi-0s5cc:;>ehO"JXL_(!q`dWH?ZA!^*SZ1UB4X/fGkB$EQRb3W?6JaMlt^AhL#79J&`i1c
V9'n.+]AJ"FFCOl>bp!3cXbD79L]AZAjc6G!D9fd7Qk\(gJ?bl%$4Bgh)a?D$A4UJ<U=t(*2RL
qplg8jV"lkSlHD&*Q<g['uaDAq4(/@-m)0H=EfajgT^3!O2E-rN]A6sajdiMoO;EiRWag1im/
2U2]AD+O`8'Uu(d?m>2KbupOo3n&)ZGkO]Ao827W5l,U42t(.?N2<!h/b7\+fn3&33o;<9V^Z0
oh)RcmD3jmgSX<B-Q1>0J0dOlZKKq<Df)mo:F1o45ckK0*0;PuNZ"<.3<oFZ.2Ni9%;t&]Aue
dn.aA&tIRa/5e#a*?,AM8lm;Po:KDp/N(*(*14"*<@HW#Eh>Dpk'anrpY@9/n60ugdYA!jZV
9r4mPa63O`<fm`Gn?$"+=]AHG#P>\&\q%el$7F5kUQZ)Qj(-^FtWr'f7-mj&U/gOiGM`H!S\%
]A1Z\Pj1nq5eX*o,e8e&"_'9US@i!jnCk-1#a)tqc5a&0[ViqVpiE&2RM2mj^'5T/MUjM>gfl
3'?P0"^><]A]AR,_\.&8/5;L>"H99@(,]AFs/nRcc^]A>,>U5Z<4G,6^[&O`[S]Ad672$`Pk*YB5a
S>]A%$0Vau?ljGPQVWi!pkerLF)j5VmDJ$pdch0_6<:eN,KBh;eR02p<3NUZG>M=s6V,dWOQW
aE-2^@_X-pU6utM8o:2qn6W,I(/.4\U2r_[JPJeD!iLi+fBoYCLQRWDplnk.MeKRKN6CV>g&
XqWboAT.r#6nCdu*0"Vf/AWo_XZ8fo=!F,K)`Gq?l::_G3:D/GFjedVt!s$7\!eCF[5bk6au
)7JA/DZ]A1WosCr&TCGf"W]Ac]A.W$!5'V;R746+O(d^[PQm6S^2uk+--9AC\NeUQO/[4>UntJN
J,&f'0/(1mh?^6Pj<)Xmb&4WYg4^h?P_nStZl!UW$/XCtO@Y(pW)8!cktJCJXn.8[\*GH:._
XNPZ@s3&VtP1:QCF,W`GOX5KT!WbOhD^Bmh'JO3kS&MAu/:ibg7os@jjfk+&u+YdKD@Pn:U2
/k$pTMO2CeN:GDDZ*TFme,pf,u+*]Ag2_&n$gQFg_E)nj!ZF>[4_/-dTX2eZPh5hLI[^-n63m
_(<OIP^5KBn<%;!R5d%&2$0(H5Y=1'hc?[Ua)2)M)8WJDW'P0P6d)iP9hJOr/5<O.'6YfVQ&
6.PP?n!6i=\[2IAe_j3G6K&0^br[D&puPC0`O[S=knr#VO\YKh*rjf1b5c`r>KA]A5T,X<2:t
i&!iISARY6GlKmt<J(H.55S`gRVh2!@2@Cm9.'Ut8D^WQVdR^"sTAYZ1'nArg:#V71+JkN^%
s4_4?Q#Zq[u2F_Zk=T+L@[sC&G]Akc%UBm@:Y@F3r\RS4d86nW>$D*fAda!ttcMLFY5MlqFGP
EWo05F*38fl)D.g9CD+$2.\oaeG=eJp4U<A\2d3#8A&#GU`es5r>/q"tP5V>CF?E\rXJ.K6s
MA4Jje`KP+_;"O-p$]Aj>aeE;3&XpuoE!83^5UOLRfBc!kp%D2.hU18rW*nHWWE^*fKs*@3H[
6p;C!Y1rXQWE^nc)qk??j#j,^1tN>H5<4I=O5T]Arjh>A5XKFD/D4@2C-hng#Qhen63Y4CI6!
2Wa0h_VDaenD0bj;LAW3s\<RH:PaX]A#'7Om[[6.iJ7bqO?R,Wlc@Wpm0+YR&40.*#ZTJeJtg
,S1BrSp@Lk79.FgM4X7F8MjFE8je]Ao#f?=6\^+8p.IR-?1CSRP"GA;Z_,[@htF+(i88qZoQA
JcEPHCX?\F!NH40H)skW'>flYm7d'349Tdc>D^k/BrWtU5k_4`TOJP2t0BeOq:C_B$HqM,mT
)GiJ\rpBp!=3Tp,fu5QU@[(;D!C":@WKq1?X=$d$]A9(26B<\`SjrhMtp$_)e5T;&a?(7['fL
rXk32p:J&2.L$RcQRK9\H*;hbiXWH1VV9\,92O"<r=iL*Uh*[_^%Qi-H"`F%b$tH6pkUfj-(
.^]AR+QF(CN4f^QA=S9?ld!^9mESOO>?nVmPW#0^3d5k?^fiqBdY+Lj53$0#i,p*q*i%UOj^B
RI'mntX.EV=2\VEnfPA4_D'TfnO768.qNYt89tsaMRJ!"V?Ja&fk`VnW`fbnsgolUH:e3Vl-
?f=qYdqAmcVnGs<`qGCY@PU?]A3kQJ]AQNK8:HNqd]A#h-]AmeFAKN_F-\6(+rHi4pf%OE8nd(uX
picNu'pclIFck-Jt'29C[c$!SDn?^*gN,+>TPC=YLQlp)QV21.C^1/[2l\u.8DBR,oW([ABh
b#Ai==70m"4suAHn1Vl2^sI>N$T4*m*7h;s9T*;)$pN*WGf_P+T+DJBE;Ucq'(7UDGr5Y:CG
Tro1a8>Sj*IhT<O)K!enr(t&kgIaJP:&8EeAIfV('@42:Y48:[nOFAQkcLH-C)0mhH`$&U%c
ql("N-qdm20GS@@B_ZROGlI+I,;MAc8JbnXnHk2mEX`mMf`lBJ%4%j8,L<C:V>3j-lA^L?AH
J5.b3WlK'Cnp5VDZ"Ogo6F#Kpk`Xm8nr?C($PrsJET`4&j`#VB#`)?Q[b,!,V_))o5&?.:Qr
u+B??t%6k2RAXC>!I5Ym5F#E@^<(5iQ8YA6>gp)?&_]A/:^IO9Ycm.n%fA.%dG\&LSo<[`YkH
&b'3\8\*Un+fD/feOUiITGkJ7DZd[M-\?j[9!,___mIn:IeB+$SOjKueso_O#S;7310O7]Aif
e[U^:eV=RO_aKM=Hi@M.'1EVX'qM5=S;fXdkh$B'iF9q+co44-#[7"FIG^R`.[\;=WWhoE!D
Gf)sD0SO"TKO>N#Li/h3e8:B@3T0*0m8!<3qS:YO/ZXWgKgm<<"$@$&LeAZ!SUqWtD4R-"$V
lLiI,Qo)[P@ofun2RNDWI,#,S?ihqG0;b>$)MQ$M$RAD==mG!_n7stmVEp$gQYIfCMH8,?fX
:^6_6LPA9mtHFFAMEKEb5IhBL/VQo'D(760mYhqQMemo#u$T%*;W\&hVrj.3LJYj3`qAk#nF
j"73Xhu0>%S?(/MfY.clm]AtPHeFG6@St9&)FsL_^DmR$sTNacEmDiSG%Au/);]A8p/_Va.0-L
*?)-JEYaJVga]A6;Ud.lZo=JZ^V&[$jbK>kA"jX46XK<^/c=Yh&1WMDSN(O+Y&B^Y[e6+=qq-
Hs-pL4fXNbI$t7,)RP;4URG[QrlOQQjA&)^:0b_7\WHba)jRXr^&7u/dLh<Ve_]AlZAQ?l,!-
LM7b6A)RZaZ^#rHMKWg9DtNn.fn]AYj_^W%AXKTo0K:NV#7pMnB9-7B:g@'P\h]AgsHhu-NrL9
cB'&b:r;@K!IMa(J:]AHNlpX"DUV^::5-FXmBGNl7@?E5)RcAd*#lbJCkSJ7^KY5W(niE&`0k
[Hb;igcOK^I1;;ed<HZC8<g$:B@^*?d.oDW)Cu`H`_8DTQ,UYXnrpHA@L:M=kM>E2+=FZ^#_
k)>Ip@k.5MWAZ[cM0mQj)\M&%B$"[_QT=g[Y'FJ[6bGQB8]Ag$g3Fm+gm+@k?&N;B2NS8S\J-
P;^FmA=B6JgaeD[JM3n)I!Jo)g@h7h(pKXP`'%!V6*oS<@)^1O:=J!3gnb2H]A`o*W.jHZ*km
lLeT_']A_;0#WiUkU&EF<1:7--h&-kNBqj!i7T`;Q<\kM(7<BU8t^h9%D)h>k98oVN]A)#%`fI
[e\[VJ147,4li&1b0q>n(=Pn2bP[:eMJ3\G&AN@Zl@]A/l-$ot/R2cs9:,cAK/<bo)4NIW@=s
3JLcq_0pW,\>Y-L:?HleV99_ZDd%DsRGLHs&4nZn\L$@q]AeCHmfi@E-.QoknkpcX9#8Ns"Sa
`no4J\I\mjH?oNI^uMD.pj;8F6J;XadW<qLYKi-Y]ARdB07JXrf"!ML#I5W$XS-AjdpbF<,RI
sJ8tKFCG\sugpa'qd2A**T3si+.m=fPeK2q-WX&TLiI14g&@,NS,kWeWB-7u__F"a!lTJ2X3
iaBC<U)06n,[Rbh$F8"Qp.9QRIE(b*HDZLp-F)hA%mefff-T_D/LcC2%)&O$/_TFq-r0u"H%
;-7Z:I3-nu8dD-+S'2q&hnNN%Rqk,'Ma[T-@:"bN_&Jk1K&lj9I*;!?:l9.fi.SYI_#>iO\E
>#%JD#CO$i[-c81EU\Sb7P.V9M@G&BeGH88)obo8=N:>9L-0`dD%\#-7i-OYjIU`sDQLHk^+
Gff37Ku:Ur$-ucT3Rh54URhd#G92C*;`Y#.BE$f`C^4"N46G6%M"9?Z870<lC`[S9!JTciJJ
SW9^UREV$oSSQ^1/nRGl4G9.5ebT7.M6o?g*podg*-C>t1faO<jQ"uPV2!\e1U$nn3dYG'[L
)G>Bg'_JF\MieTcL&-Ri>NLW^NoQk0`'JkD9tT:*h\9hBa@!B"WYdCkT5.4@2@s(#h0n\#;\
eX@2GP:1D(jGrV"Y+G4S->9:V]AbUhYUM=pL[eOI<.bAFf)Hh^`COn="M+LM55gM9pbuSN4t>
Ol9G<%FUK6VTq3+dFk"K.N23Q`i;W*bd$<",:+C4e72'O'm;u[MmTdm6Imjh4ji-45IU7$fe
UZ:iRrkQWG1h,#0,g:75ONnCuHZ1@T)UXr6!QOg]Anq@?1m:N+5Sf2U/'<[MA+PXOBmJ6SSCZ
oQRQBIB5lDBaBRN(99e5_2U2K;P_7KjBlAJ5+/&$q&4E`<G@mrJ$"KH(_AA/,Qon&/)J712K
D!!u[b0'*m@6\dM_P3k)!9Jq%B=Dq`8*UF>fqJ$fbYRPns'r'B4#q:1Di;t8N]AJJ/bLm_/Qs
u@RngnmSE)0kOtLHc/HBGCGYcZITIT\:HUQh<`:iKGFidX[*"i`$((<[R.c)'9]AIb/h@:A+C
7pfHdM.'+P>00i_1Z[sp(9P!eN7r5h459(kXL01d@Os(Jph#8N@(=`pG-Wd'\t,>Q*`h6&IN
VX_R),L?SAK4SjZ7uSMloBp=3K$O\e%gLDk:$'oIgcIp,;lAM&sWN]AJZ+lSfD?Q%Sme2>c_X
DrKu,URGs_nd<#Ys(kfBjq^f\8]Aq"C^(41]AE5HGOgSXG+bGTQKIbglR`2%?cpYsj=e<I4h#>
!f@,8&AY"^^(0jE^h8uZTO:L9!QP,.kNH&RcNKomjJ7#jSgoB1b/H=Wi1!8:C_Uh\ssKC.QX
aqB2"U)_bk3gW)'ihF-0%D9U:kO&>(c$U5Fo,(R3:<G-l<B&,"1=Ne&hOgLWAmdiBOE*K?Hd
GU>%@.s<:`,K(sa2J1-==EKVO<1VqBADG@hi?XP948?"!;O.b2J!T6"cA[VN7rr5Eb^pE%$E
&L!"B[f&"<h-$*]AE`Y<@&IiHgi!3D60e=q([Y^Vnkk#\DM=5^uC!8/lVJP?3!E&)fOXP*d<2
#3-'B'?Ccn&+Al<=X(^T*ThVFLT@MiZH[,Z]ApME?[/+Zpf92[SF`[*&t\]AP8grtahq-b5%&M
Lq!DL>X6V$>U@ZY[]AaIV`tD_bkVBZQqg&:9=1(Okga-[%"XRD'TH*m,6d8kLBA-sCkQC_730
>q,M#ag4U[!Jo6#=,pBh/@84[/0R,%f#"(RPf6fm&,NJ!*H'Ao4N(1P2jJAVdFjL@3u5E6;9
1"$HQZY\9Cb%%!1U&[%,0N.hLe^g[T$:.Hblrh<MW"msj,9I)g[[,[Pl"#,8b_UM5]Anf-JZq
OB=CMg:;h&W4<EdLZkPO\Oqg2@),Z)TLQ113LPLkT>jRa^SYp&qi+^VZ!D'KC,Pa?3TNUcQ]A
4G$Y[8]A3<"r/C/4"p5h<:)"WSQpulm$I^6uIefQS.&40DT[Wj&VS@Zmj+XUVlJbQ*bDk5",#
+<U)7,iU]Aq.HLID@XNS&Mlr$'tH`.-/u^eh;tF:%/ELN-[\U(G07UIk/StO0_EN]ADl62td;f
XZi)^o,bUW,aMA48X7TOJ<$;Mth!7aIhC&R6&kb5W$3R:!5Z\Ykt.qZJ?Za&2%5<@G4;:`!(
"k>MEOPZ2Nim.<[4me[r+FPC;C1O>OV.bOf=Q,`^"I&7fN')#YosR4HQMq52?;PZNj@;_1_k
9^V([*D;;L_+ZQfk>0la5"0EWJVXE(";ZjFdi(/nD2F2^nrhP@2gm>!9jg[qUZ-i7[:!-M?-
5<q(P)Don,+>p`06S*ZuW*kB5X%09@()%ei:8Lo61B$<j>p_hd>qp75@6u+X_]A,IF2ou=Z1U
<"oh,idAALp`O[d=npYlHM9ibog;r-fA5(IEVI(XiNo1JgHDrb,d+<C"[%:SX!V$^tqee`QX
a1VL]Ac7H#:qmRJb]Aa2XK?q*^5?m^Tp>n&1:G.,O:<1"TQB20I4>HMrmX_-Wg+nL-EoeP6j$(
PJ4:hC,`G152!"4@;%W;ke#n*S,U+1F(Vm<\A-FnhW^mU\T=Kt(5J+\g3]A4HC=r;`>men]A%q
8(2oT1>f-(3S<#o(#+rnW3-H4]A#HdZUX8M%#P.8RC06CEB<H8?jQ/a%#E)D\!fJdcYV@;u=O
:Hcnai",YrD=V!;mWs@8T(.JPP[8+p_fs8s$jC*l5%Q)8dAj-Sns4"\7MV0,d.VS3<U%Ic_\
U5VGgig']AT!"32F!WK6k;AlA;h%.ZUrcVF&cG;r\.S\P3:GXF]AFnZN)W(/p5@@DjQ+LFk+K1
UFntQV0dFiq"G&2KKCDqK^FT-\NWYZ`7$7.ErR:6Pt>J#Og"l^<U1N.uQJ%c?>=</Q>=.#=&
piPq*8nU7T0\+gPQW3r-ERPM99Wea%kkU8EWmYuT3Zro<R&c)aOIV#MCe1_/$j*lMmWY]A`S%
Y1aK&Z9XTR46\Bl)AjArXZ16mm@MK^0fjIaH.rlB5GYW+dtG/Ns.IiYU#ZoDjqh:2N4HSY/:
=]APO5N*V!sRNu/RS/9G:Y9^4(0R+;6)4ParA3IIl[\f]AKhC'F9]AeVjZY-]AGL_XlJ;UqunW!q
Le@X_'=%TTJB(0\g]AIoG)8m(5YH;bh2UPPqN1[F`?ihP.\Dgj-TO?Vf]AdQ<q?5M;IaVjtT`a
,oDJYJpEf$rr-GR.OeDLdR>7PDZVAI]AT7P6k3l5rR$L?_54SulR4GL^oCA'&NCH2?"VCtR1?
Fb?C#f$bSB0QMJZD^?A\]AYk&8ef'PHY+L42V(.JODtSLKhX8neOB)[CDIOX/V79@kfX[?<i4
Q*Dq:?p07E,@s'D(KMf&2t^BRmV;Y:$h[gfH\E*^$>8O.@+O[/:I-A!NI!HZmOaZ)rn$ir]AC
O\TehU>7&\N2YgQN*CuB5Vp.kE:g_Co[N<B_B8CV`??49PAC6>f^J:#<"gf2,0"kT*bJlbLo
LbN'>gGOch8shIL4P&G`Whb&Dq\?r(9%=l*:XJr?b=^5ChRSqYc5:cAYi'GY3"d183@\6a#&
K%NSYeX#_RGVaRM31.^Q*Ced"a.,I"n.H`8:F%">'#Q$T1(-O@,'@umiA/rA+0?^sSbM\HiU
6IZ7L2pb<:[_F$YCi!HgXbRr!f;-0c'L6l-W4/eM-%h!_rJeJNFePPiO]AoWVpmZrW^i2Ci+G
?#21G[+XEDAUKmFekIOk6Y-<NCMo'dUC+?[E]AS(<0(Hi5kKH8LO#UnaZ/h)M_u,\H[#*L24>
i/*;c)AKW9I\S'Ia6-P6h5dF&rH;O3rP8Bd'UCi:aZaR"D+02a]A>PNJ`M"YecF!n82mC#FgC
H40q%86GB`o,<`RBk,WSc-Ye/;UWs2NafIA^%@R1*meX0OSoF/+:<c6^;Faq.YXe)Yo+"c_)
P?6*i[9&,a'C35MEF<=BpCbJt`6U9%5G0G"pdDJ15$EC>2V5Dh/HAXa[>\q'\Z($;6EFUVLL
32_h$_SfIT>>g/`/+?^%Q*al4W!tr8K1@\s'0.Ek=-b<^^-QW?njcC_eIbLY-!+gF#cdn!!^
W"TfGde=O8/(uhRD2G@dlV$nNEn^B#jq^VsdTBK9a$%`@?qKPRes<<'GT5@lhBhO+Tl"pU)o
,=W*G>n%1Q)gHiC`ChK[d0`A%c7uR.**U78?Tmq,r2#-tEnr67mnrnmU[ehG/-lFqr,(-gq.
"@1U>H[ql@*dmWJ^U.)lIr5AI-CmBEP4$Lqf),qDcQY=d8=r95OXuc+,0WPSTSs/rapsNS3?
cl;=aqrI(AF>deoqart;")7A[DYci0s904=g\Nqp=$q*4?7A(CVBYl@_QFBV6`692',7K4Mr
s/SbE=.g?lrbmu%:Ah;'s/W*_ko1`m"b=@+Xo6QGqHm5@g@NeTn,M.<nDQA!T>^_1;=jUOq#
C5AY"5^!Of,]AZV4bM,^0m79?[ekOKZ]Ap:,ZZi&Isu51KC\1QoE*5qmm#L/L<P&Fl,n2AXS8?
9\nnSDrmPf/P*`l^o_YgQ`dcim@Tq`t_J+_Q$E"V6/"BX"X`.J&[)-O2s.<@PN#ZS>2>Mm^D
oo9R6TW1BVK?)BYen6,+2lq#5:A<Zq68AUfX0;<fX0;<fX0;<fX0;<fX0;<fX0;<fX0;<fX0
;<fX0;<f`*G+T7fR5V@FSQbGiF>$P%QZZJ=Eb1WW)@5FbOc-\"*n%/fq=@H=fJ[+Mb<[+Mb<
[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<[+Mb<s$F!HYXnYjPJ_ipJBs5D$`8tbs7_Fc&:I/A.G
+"jPQ,^.Fg<IMHLT/Y!r~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="155"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="542" width="375" height="155"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report3"/>
<WidgetID widgetID="3aef05a0-6595-40fb-bb6d-bbce1aea7e25"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[647700,990600,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBMC"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[ZBID]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx4]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<SelectCount count="=1"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" cs="2" rs="2" s="3">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(GS)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue("");  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gs]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand/>
</C>
<C c="12" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@/B#dTQUjSS;\P1NUda&tREaRG<PP8lTO\Q`&9[b&W'U8'Thp'Ggo+h(`Q>YYlq%bga]A;&W
e=U.T7f5>Yn*n<"r:G6j+R2Z'o`_cQBpZo@NFk?d@UM^A#@Un,'YfHgg-HmQH?$6`s1!.lXV
1;,L23EHZ;&;,Joos'>VpZ@q4HAY>b!F)c3PF!N)JU+c4p:D@MX3pEQ##!ZtN]Am%#deF\duF
Of\5,k&6%bdWfT?\ceWgc;QT`6&99%;Qa$CE'V<ON#@@.tYN]AG&-IU-^S2Rg@BLaMQ`=ooAk
X3(5)^sRlsT+N,rU<Ta$0?:hKmi+>iuM6hkVBO`Y#?+aLg?F^<5CUNj#cLX5:=.#h\S]A9>+5
/N:Bokmg0JT%N:9fotFI4C7P6$0Dg(qCo5AWt0$jiqLERa(=!kZsLRs-I_"Dd*-pkLRLU0##
VCk7gh<(;S1V9\;08.qp'Z?Is<*.s0VK.jC/rX!=!bsC8EqkIAkeH1T;C:h"0[k[^:rW2Y1<
H7dVtf_me**R9glUmc[E"J`TD,UKcNK:\l-?5nhpubKGfEEoHiOIAj[MEhV2J,cSK;J#[N+?
h:5W+*<&>3BB*CjP=9;qa\%=\"3CK73*D<mmF-SlZ0FH@59`iW49?CoAB6T-1e'o]ABCf8s51
66N;@aQru\<u1:3QdGWgmDpQ<*iN_)EHmaIFPPpV\%nut#b#G`_G`th$QOD`J,?G9eJC@dRp
-)>hJ>;`7c-8%/r_T<qC`0H1ar*(<biU2q5#&['#:]A,$'k^c4D:KH5O0D>;*XIg`W@;26!*&
;@&+RS>e^"UiaXg'^I:TtN.<WU8AiCHUf#q&aF\IH"2H7;!:+%A_Q'#OK[_ot(Pi!;L30NQe
)SV_R`3WV\n9joR5@Lt@i[mJB-R]A9FJX-9oD,8PKH)h&\9_rUN;?bWn6/OrKqHdZYaH"iCF>
dJU.4C)b07sdpk\2b\QLaR&5A'i1HcCIeH#-s8YFhuUt&JUULR-fcD6rQ(<`XF6.>n\3]ACdq
l>MRJp%k<.hB.klAlKO<b0BQJl@dIiS7<as$u.WcV'5(h<b\!oN;9o$k9m]At"d)#!$]AK8G??
U!]ADG3CalUSY4a-dMHfb-^;aCP&f#<f5*+I670Lp-itSe(.r6u-*Qp8@nkN>-6CK4H\[^j@M
[\eYUk`<&m@p#FSb@cg>?afa+n"!mTnH5bhWe:E!HWVB&-'THEb3Eg(Oq[]AoPtVeL=*lRQ:/
=g!j]AKWD?TnYic/@\YtL'500?:CJ-qgPR)EA5FnCWU2I_eJnOFMq-\i#^k(`6!LfBJk;9"Qn
a!"K92a)6YG8OI`GR"I$p7c90;+3Sj*]A.aN8;+OAil_?MKuf1M8+O(\u..C=MMMK**$`(:UB
KH\[L.-kI[cNd.Qc1F/?g(YGO$1oQ3\M>j8%%;2SL^bQ.=o*K/qD[Z66kK*FqdQVJ1P3\+oc
V;YV;^1_bmF;P7</^E9f`b;moY76c0E9gYp<@)?96Q\3MrqP?E4Oq)eRs^>+<G..KqML_8lY
r5OT"#jk(joo?0Wpm?$XZsIaO!"$<QE>8n$$@ncp>RW@H>Pq>67Gr<R>0J3F;:ABc1]A3aY\m
$N0g!Od7)<on_\12TBCIjqa3d[/ZjRM8]A(7_URjChXK]AE8N,hmt=56R:m%0$Z3WJ[5iRfEL-
MM/+&b/WJB%\7p[KW`\:iL_fq]A,?9]A8)l4LlG8bjq&cXhX5nC-3Bl1(p#M!o"`SeMEYm%MeO
lC>rQnCO=EL%G3<AgKJ"_[(Qoh$@ZK@==DEJu9Fl;m?8he.KtsliO.!b*#*k+M>>st19W,[S
-klSl7TQ3G(?^e:0N"S4U6nCpf\ui/<emd+1E2$Y[]AGGL91I)?#((sF:#P02mrZNjnT7Pd>'
RN%\'@'T%[-iJJrLE3GZMlV'l8rf*8XI7N=cRO]A%u4?&78&*:51YRf[&EH&_0^Piq3PkJ"(%
rc'R"MR)_IM.[l(+S9fAO[.8VYAq.lB._7t!I<JG2Jg,#JS=er>Ugk0#=85<(%hAW"h.,Ca=
<Z1>Z`A4593^YgmDM*T2&_0n`Sq:tUSU.9\pr<lbbFB6\+i86?<b7pAa78PVmDb#KTV_B\1C
D,D:opVV/S:rUR8s'QA<9=T5!1,QQ!Ha,OiQ$mV5AobF:u$.'#?Q\E)c9O]AT#W7Q(3_OqE9t
Im#l5%a2eV7j&`hdPb73WMO2CVpe7#b=rHAb1PXS)qBDi5;Ff^C'\T))sAJj.o(1&iR)YM&q
15t64WY*7>DZK?RKD`$0F*`H?`+(h?294)5X8,K*,3)_g'Zg-s6PZU;PqLQ9g%$"LM9fe^E/
U3kNGmYs[^-GU\RJo!n+ZRe<)'8!kMi:t_3NG=:[L,Q6gq/^MtKotf9X4[md0?UqEo+V,%<C
&FoMUR^Su^cueqVoO<C(>erST!N79*(+:O_=#UPSu@L)3Vl1+5a)=BRTj^8S^q7+G6N1SRLs
\%D=gqq(P.6ak[XPAll*Gik^d)#1B#R`H?S4o>6LgSQ+=PSHD,)!Cl9V2&%nD"RjZmMH]A6eN
asf3lF-tJcb>u*Cg)V-Nl]AOhp[R*PC;pSJjfS/EA_\)V!#9Cg"*l[7*Cb3;)>1"M@FihZ^LH
4$sZX4<'D#3+hr#Dr5,7-rM,4(T`1Q+>rKBEukca*pZCufG[)Fe4=:A-I]A1E-U:Mom^d)N/E
nf9k1uD=mI?@Vf%3K@_6R'4Za[pgUSW.lKMh(H[l/W!]A*qo=R&miDn4"``f<sOKPEbhql'Ar
H8tE:<%!QHL#8(%<)HijE.:u8(YSr\%'#lH"B,33hVEt5&KK:[^hkV"5rJ2E"pW&e^U(7\hU
aWQeJhr_nsn/,d=^q-?WE55E9A?Q9c%ffP;=(&S2-^K-Pt[HLUq(Ydm=L)eKM^UWh%"6%)o!
lIE%F[=?tG0JDf0f?MBH?mt,p/(6KfXn.s0fNj4$#CIL?U2JO=IeDrk#]AuW).T#`Qb&UES[i
<U=5Sr>FLnmgI0a+`GO:s[c_=Y+rZub]A-c"pc\>,.:<P[P4=`i8P$:J"KLKff:hZPAJB%8Sf
ZJbSaE%Lih^kiCG(@Y]A^prDY"ihNBp9B:^%Pq\T)<?F[/*8q?oWVYc_3_iU'P*BSQc$F80*%
U>Ndc=[U0`,&WB>rjCf8##U<J4.Hp-,Xqk$`S!?9>dAH=gd+2_sB^C2t_&jD=O!QS)%?HH*6
@1!pCK&e`PJ_Dmb""cXsW$B\eg_99"J6qkj+fgJusFXU;(G<I3"aj0IWYHL3:_?SCFQ:\*OQ
h=QA_>/2,%G(^7\RQ>V2f`*tLLA,AB(a&S)`W#s^*i'B,)Oi/S4r>g.kYdXNqYC*@BlC,O(c
K9nAkV'fLHA#p>tNUhZR&Gb72+MVWE;&-W3&n`d:W3>]AeXu6eecLr3B`W`Fa7_*R/Zg&s1CC
m$oauL5?GZ2nM@Fu[:"t7>7Y6cK(3@H/5j/&Yp4-2-p#*p,[7%^n@IX5BJ+sdJq(%#13+1_@
(B0@/#!(^Dk-Ie-87WY%D'Ue69Y?bq6M`+[>tcr,LXAuhV)rkElRQ!0![$SELMn$2[5_u$gn
C;H"E?+oYC.ppmDqXPZZme]A7?';-ru(<?;sij6+J(T9"A*&8-LhR*dr'B@8gmplkdcATYVVK
Zi!ef)!c5\f:_D*k^f(m$G$l[juVaBimk3EYB]A`uo:/D,k+8^d7Jk."KbF9CFbKF*EF78DPW
JfQ=$>k]ADRPPRY$]ANEH=.K7rl+/dhJWA$$Ckc/.18B&31f@0PQN9_rqr/G?*&DIX@(L@3,X:
`$$3,DfDh,ILR(tp.rt*Ji[_k]AID>`:B)M$)dFRPNhXJ<Bg\auj]ACnIh*p)@%4-hmce!Zh&8
@_^SYtK-e,JCb-h21^qF\d;Cf]Ad`uHE$+hSJobT.t./2CiqQD%*A^J)HjTg\Z55Xg1UF?'O$
L(;OBqKA4oL9;L>*\iMh4R\,eBu.P-GIaYD>[)%.Vo:.tQ5nIpt2WWMT\O4i)Hm!:FWZ%m!o
3V.tHgAdu,3#R9^p1(ED4f*k$oF5M+S2+3##]A(=50qOJ9W=H&mFr&5C/S[ql\HRs9R`-_%o?
H2<jUSLjSa7qL[dtHKff3X#J'=:5PhbTdE/h@S4X8EmN,6*TPXF37.:JpT'1a,8OH3E6T'>E
mkogl$79K%t')HOfj=$VmC2&[-^6c%LN^1.I9iLK90Je.e08Da7l=n:GE?n$D.$^3._r^8,9
kg)Q@/Ko.c"=46-=p"WCaM=A^mE(1;S&6.%75I0'MUZ(451>tfVI4"_)bT0m<UI,fYB>K0-W
Gdnek.b/hL]A*:'+@2AH?4B>lF?&APf:u7ua9@4?^(l,<`6*5&lac`fV$Ta2nd)!O@D9s,aO=
'=RUlhb$cRRe]AF)&uoF_1O*31nq_8'<p<:i0Z$gUMVgq8>Di\[ZgO#='$AA/GFd\j)T:L_+[
;lgK7kIr\4/KJi4SQ"]AHamNK]AY!l'<%"/n0J#TG'BQ&l=D?T=(*-A2W+Bmd^;V+eln[U0%C/
'j9rgj8BRe`P2GTP#a["3!2!cQ7l9&g_ZrSd]AbTWm^J"qqK]A1\I&PP:d=hDeW'3ARr^Q)U#(
qKHP<$h5l<%Nb2B'%J3`STD!SQ\]AE$M!jWp^s,n79m2KM+B?eS7fUQ9Z0aj=t0IkOR+ipUGd
J%$d?%3@VON1"a+Rhe.p!pY>oO[1=DA(TO+5*c-hE\mlHl3\s5fg=\[\N#a#7f/M&-1iqkLC
6:'BK_Is$i9:?TPCW5r(%L89g8/P^N>rF5_rH:@iO;HJIM+ePHd`Rom'=q_to]AMia7e0-%`^
:JfofW"FocT'I_ABt*V7RlK+S6;D;2483+OH>-c8>(]A\c1kh@T1tr`!]A'BX)dQd0@AR>nq<h
D]A'sldQ"l]Ac`d\iFj?uccocHoai!%*H6<sjq9tcR.)s0!I;':H*,+(r3/h`\=j8?D!\e)e%b
5JnaF]AuDs)+JeNIr'&"\Z':fYW/]AJ!fjBB.?VGP'hi-FkXEGW\J-i?@4#HZnK;<PRCa>0Rhl
eJMlU=ZE?=?oFT^7/pOb=_IbKU1'uKFMo9W)>>LDKe\6HVm=o7_U\b;4Cq$;\fFJ9sd_.+,T
X$'u0g`VfG*%;>fa1_m^/24QA6c41b-5>h9&=WFu=\tHJU*97Q]AD:7G;aMA5Dd7.CohA\1=j
X7qS#R._0U0jYS/nfLb5'ge(#ocs)M2.eKhW"fI2;rni6ds:9G9'$j#Y\J#C:io%O]A^p2aT.
/flM0eoc!Prdk'fG[ii+@>qGS!K*7^dcS64h%$hVVN+mNqN+VA&cek!7ei@Qe05p43jlm.c`
ABqhqK4"KX_h#bF)[!'dh4RKWqeCj/%gk*Qd5>+=_lh>g;XlR3L\8l)DD@+:p.&4dp6=c7oA
Y*cKHBqr94*4ejO[7.%1;l,KTPYUa"pbC"jmg6l(+fQ#Am/A$>.oF0)E-+eOuBFP_TC;nnCl
FHQ/C%jtA[aK.5]A9bG6,DjI2d/)j;p8<"N(Ji/#lht`SJ3j.@nfP`bW[V!M-G>IFgqH;V<q@
Ss`Pl5uiOku%9)T%)4<KY9(MJZrSG-d>l8;Z:m`EY$0pd.!Qer\=H<.gDoHHsoO]A9X&q9fX>
8m)UE?(>%_S,HX3*b(\IFfHf@gLa6D@NGeB>GeSE'VkQ2/*dbnI04Y$XXJtB!E0t[U7gP$*9
gUii!JK\-+c#X*`Zi&MrB\bblbo2Qr*MWkhFjGbZ9S=)O[<[hN8-OZr+Qspi%Vn<A]A&HnA"M
lPDkU7$<3ln?PE@Z9aBq-1S:Wfp%PgF;VYIB)<VO^_[cWkXM0J6HH&TV1SIpM-NHpUp4F?9Y
$(`GnMNu48j@0p6rQ@a2=\Me,A@FSeI<iGLS`(N"-&gRnkk_/\\:+3,<X\;AjO``6Vs5-5MM
6X[)UY=KT49@\CDkoa&S#?X[->$9K2OhaYK>@d*);]ASaS-F^KOi;;V;<a,1&"-#g6'c=q^cj
q-Xuhr2d+$TBga_kQ8^G:/&M0[AGqmH9/o+dXs]AgY.,Z(k3rDB&S%$?g-bcdBi+F(1f'_K6j
7AVb.Q7%D>9<[V3n*pF'<qlCOrQ_W.q5FU<+WueQhUGq+6_fJ;hNdnYipV*A5uDWKhpOh!H*
g>ostduUWG-[oplLs^X7b,m%Ym'VOb/RUL69UE5%7ZDOM'pH'8iG`:VfMHeH*X>L@P^G8HUJ
8aPbVXGPj\]ADTB3bG3.$Z7FS6nJmlPH3k8\dBo:i4J(bPQ+n@EfDa<K($DD<TZW7j,Y$mN[(
hbIQ/*CG\ouJ1C#;qt>.V[NH+T:SWT7gpJq+sd#RCK`J05s?#QfU.+<Upq!ePs\s%gIh$@g'
a(%6X#,K!3Xd_)CK3+JqAOIqj.9eLpP4`t"E$GZWM^]A4eY!"?XC%);iO^]A4eY!"?XC%);iO_
#>i=6=@Yhn"25UBqfB7:Z)2Q;5MH]Al%[XO\#&E1[&(sePD2_3PlM"r!!Zg2#%.E8?iUC=!!Z
g2#%.E8?iUC=!;><(?0nQCqr(=rHPCG`1P_1O!nXFP#%.E8?iUC=!!Zg2#%.E8?iUC=!!Zj2
&[l/lj0/5*s%(f:rlJ`DX$bRD-SPK-~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="348" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="af1249d2-3967-48d9-bb6d-0c0f137a3343"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report2_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[565265,1562100,565265,0,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5932967,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb" columnName="AREA_ID"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[A4= $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("zbsx2").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="12">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="微软雅黑" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"d!e,.mD;PuQ*Vn*B^e2aGf3B>;NX9Wd!3MirB/0mi$1rIQ&k^c+N;1sp$;3?,>Z`R%S@[
3;ugL7@SD4#1mdr1,h\R3*ms'Fl4s1LYjH@=YBp\0#_YKsW&opGF\aCX`tg>Aj-W2t.?g$,_
uapNcThl<jp44r0RhPd@Q?F`,a4VEgI(egk;4W;8-1>6_g8'/L"k?!&GjnWehCHHTUs,-jUY
`m,ip"*I9lX/F?93nsig\&bAfBK*4:K^t.]AmT:>:!6=K\?X7lGofW%rLFqQ`Rf(^O$m4UN,a
O'3mr>Uk\.:K*dQiZOMIHd.<WtkF[+1nDRRSUc6GMP.&)kFF>,9c.ijhS+N'\BLX=3ZHeYlD
<[6Eb/c4AO(EKu;Q78,^gHk`/2]A@(?U2o+:UVWM&Zu!_DSq=?92l&?5`U\1bS*S+Vi?&fX2^
c9K-N1UJIu_RFrhkO+J"3coDGVn%/_,UMr/L'UhR3Ac&+I%YkAT%T<ig*.rX8CGCr"5[mfAB
@mO@(>=l!"M2JI66]A*)YnHoM49p=mG_:JX*)<p*0b+18fIP&#C*(Gm.;WHfHPk>=H)]Apb.S"
RWsHOTP<*TGhYLqn:P!)m(d)`Zl;7b-hJ=r1LJSEUc1lBT-bD5e#jqB#:8pCJ@^=:!]Ao`St'
.efpd9(m^>a]Arm,4+#I-QFDbM$SW&)+>GcIcm3[Z&13mV8."o`.iSp0>l7J3%t]Ag(9i7uPkm
0V=\hG>X]ACrb?S(s68`;+bhC3+bY5-2mCo0^+["&p/H7MK+L1JhEXlL/fGL!,P0h;A1("sjI
L2+=G52K'-?U1-Iu!h('(LRNX:c3RU8?@[EN@]A+!K0jke-gbMU@T,6'[PJ,eqt4W527="7p>
dJ5&aG"cR@D,`I<YXKY$e`H5Li<':,^"*N<p0LgG';=._sHn`l%bZa1'jkcHjp9HQ=MP4Pbh
n8bj3Dh]Al\\ZPOm/WC$]AGgHfl,&reWuD\=1^q=<empe^N_#u)CpA6;%;U[]A8p8$tYPu5B#Wd
9+`%FIIU-U\)q?9#GOCGX5+g1d;5a$hkGj:a%,R[b_"f)eO8Al<JBADHR3O_r<4erp@2<lQ=
Qs-,9*.Q%EMIIk$9uj@cnD)3:^@tccg7U<cacQQm$j+n0D`9/P<gQNd[g6AJeqpP;p;Qcl]A-
=t:8CN);ns'%Kg^6^p]A?`[h?[[1]ALFgKTI,!l'mdX$b:mP.ub1N!o=jpM,?k$g0c&JeN4q]Ad
_`o/Bq2M0ZNK!g..#N1tNgXnBa4m1s'WM(2h#TfF96kddS::1j/4p2U9UB#$cM"dk))#b9q0
.[u24^Ick+>a=.@^a8!YquYKjP)`V9BorD67Z9lg#!6LIOrdmierJ,fc[u"0M@BOA\U#D-;G
MsK!%&j3LnAC*:L!9;)/RoQ\,[sJDZ%R`HqF<M.3!rp*^1s6C`?B1+&-&U?1&#7>A-_St#[I
\]Ah6#bQl+?2ss+f?qZ/,YgX+JO$>7oL)!j_4TiD6RMu5[9?0YZ/mQeK,2<AJe:Nm]ASep==M.
-<r@l_5L<l@7SHKG8_7Hnk;GV]AjZVD&Kk3cK2X11gX7h:UdeT.<76!ob8b,>EDl/n2\@eAaB
OH?E3"bLa15Z_)tOM_0hP6E=Ef.bs6mkW.;S%o)Btg-C%pW@G"pprDTO%_m!M8>^6PBl#PpU
"Xcp-d#MdMjT$Lkdds#X?ol288:1"0c@UO9uSc!I\qt3))/,)m2B8Z7K-".Q67aD>U^c8ZA"
[Uq3i$5r/W"m]Af77GM\B`BS9fO**FSAA9i)GW-D+auAnK-q3cHgd,e,5`aiqAWnbN]A$/9Sl:
/?5:&]A$VGFSGQIdK]AioE-]Am5+Mra3FLq1+?3N60t[2;.48'qs\L]ABQR;"&G?EfIb1EL:W=7@
h/oK@l+SMoKtt,41QFHcee<pQ;/a#phF6*?,5N:`16i:&k!fAMgkS,?i.GN>,@!_1A-FME?P
efT-sQ0m<_LV8^oqY@eaX@GLtHh:4IiGSSIq2Jc._CX@bGXPTQuT/.PmrJ[=uq%q?hep(7]A1
(/V%l@e5Q;cA(YfM3s*&9PX*c(>eC5B4,HX8!gk8DK4s+7,G+m`uLmFbRS,k_C+B<gXUCp-)
W8'!JX6n8673%TZ9JDY,ch`"/@>f6^unlI\59jB]A7mS)21(/+`lMn(<Hefd(:j\d"mU!mf[p
hS;&lT@:4WlRJfPT>]A0]A0d6'c4]A'S-[i#cB0lioZqD*mZ_p(04:,Or/Mm(%+]A#fiRiIiI%mD
1E0_g*J'*6kBl0Wm+`L*$6/1']AI)p)-O>%'[do,9YDk?fG(>d%[oV#I(RU:a2$[7mH"ck+kQ
NQ_^/!Z]AlKCZ(Lb[2+(lrRG+/bLgrfrJ;!i[/r.Ig5gDsNYSldGmBk3-LjEQ`rc[<1'-k_;G
N<Z^:4p34CCHiF.R_n]A*fG7`U=G3HIqqN;cgTJh!9ur+9#o%)aNbYUU$BUtl=j*hLT(e5THo
p;_o=0"&4`K@Qn!^LZW+717,6<uKPHN<,_s+()?IO=C4#V3o904/^UG;1;Z0A?U7n?a3;b4&
W*!'UEVCmW<7%9#%tor_h(R8Hc<.k)$,&V6Z,q"3bqD/3&Z'TbB[n$4XM%;VW(7Y.17e$@20
;!mHA``-cF,d\mh6+$\30D^X=HCN6,CI/-XaXGYh)5S:DdCac`m*@"o5(-9<9Q@if6BfinYX
%-hclIF0`fIbj5lm8iXXLj^oermr%^_OA#&"oE4*g\<<Rk>e%U(D_L%p)q;]AjasNb'+o_5Y^
CDZ>jsu[F0ECAFCi]A^<T&1a*MhHbc(k^inMWS'P`$EG,m+Y'cQR@l7VWrGe<Djr5:^?U81\)
(K"2>@&0g;NG\6M0-UF*`?"PM:AeK2OI\2hliaQ&:*<hJ-tRqW&K%/%Xg6d1@Lp<h%6bjYt]A
>`Y>'WQ?7<H2.r-#UWs(L%*R9n\+qN9XcNC%pf;T<CS4UaZsr";<L`W),`(BJ&KD%%Q/o5mB
"*C*%-qR;qBWmGgEsP)cBqI.'[*q9Nomu*`V.r_eqV-lWb!&T8D<@$uZu"Dm9\`3Am.WB4g/
CIi:faW=<\"r*lHS"=VWJq15`Sn@V%<'d^?FD_e"LP1dG*E//Cb(RuO=3E_6%PnWfOPNjc'L
7h5m_HH5s<=a'8B&U:)W*gH[LYCC?;^2lV]APu11PBO"1M\$P%U`Q=ihV=L!(q90Z#.G)uSXr
jICnPgtg&'\>Zm,<]AJ*",o?CG;Z"#XQKk\1?;f=!'3>laH`#^>D@@]A=k%`Km4A\IK5:[N>I#
\u]ANd`7%u(c=(=*StBVF)%b$_P!p$Y`.9p6Np)ua'%:pZj]Ar,NOS;fIm]AMT-.,,5Tjk%&_^&
S_*nq8?k)s!Za6?G;]A]AU2/,VQ[PP32AhZ'8ekZ+B^q*?r5?Sj60D.D@25qL.9rSang===!);
k5G)7_TbSY,E^Dh^m:)3CDI]A^ZJoTTEHe5H5KA>@DgBZ1^X>O;YdJ6q`c9)PEi42r&L-KPpR
Ku]ARUphu[IRn;3@7Mo,k)tBrK-;"6d-4H$'T>8E=#(>D$@A)^BgDJc"J5;<l00q!$AZa1*UQ
Md6JJYZ,7u#jcaEh#.7l2%?3.GAShCgacp1X#6.&&!c6)^"'tBik_O2J3g'RBCSf9q$UI`tC
lLigc-W/#4]AV]ASF2W'/>"M55E)'EAAIFj/Gd^\[:b]A1Ac/i"r(PWQkk]AdZJs&=2TcC24R27a
rE"F%)J(,1Fb/g:I'Z*Anf'=ZKpJju>`paNfqmL[sgBhuehI]A$/1T]AG<VhMc_264BMMmp`OB
&5I-kaDg&?sWZ8*D6ja$Rk+(g.X_6N("+PkE^jY0cZ)10J?06+`p"*m3)D_r>O6LA)Q:k^!p
>^Y$]A\$1NFtES%gISjhEZft0$\&:\=XOP=2h31F$Ik3E5BMpP@1R'a)5aUP\J+u/P,A&^Kue
_J49eLWC,:-CbCADOr2"%VDYT]AQs3/;9os+,;#N?HF4F]A_5\k[?GBNp9UWAAkeIVtDC5^;h_
A8Ba6^%f^C6F+n)]A.a$/LQYei=El5L%le"YBYJ2u^a%Um<)5#pFM,)7,+`mr4I-.M\,ioaN:
s*=#nli[@9['nHtr8U$H&sC-Pe,qXnnu&s4C*6WHOu$V0BjEEiNYmk1@;HgjV9dn*>n'a;B9
*'0T/eR+GBX@W,DTcAt^<'F+Qa]AMNNCK@<O&[cR?e?[b3Qf"_/V[J:"u6riAP7ioS>X"HRXI
\%LW,Ffdc94,adMRkp59iqZ:/H5@):Z'g[/A:IL'MdRrjF_)WH5h1,TdK%7P.kM'!@TCiF#`
!G?B4&(FfLiC[,kq6qj,s32=n$5ZNEu.;ccR\`,'^]AW0i(R+o1!<G]AkA14jquraRKjlHX[2.
;^?Zu#Q3a)H\2e9P`01W:6j^68)<3_q[H2*!,;+aDHO4Ko[8mrn1?-_V99f,rX:XKRBo.2)A
G3=(<EMAIL>/LjY=(J@F8im^4ETp'O$ddM9K+#g+kBai?F,Q'7&IpdW,!?92#;
l<_P`NlbNa"B680q['?b,W&M3r*1XJ)Z9+GcacMNS(i<Xo!oPorU>S?W0H/LMB1'WX&Rh_"X
tPU\`1-21AtWHu0&F@,a5Bb:YgTb+M>L&U7&bj5IKhe-1hF=KUShBB4!B;<BT/4ei;G!p0L=
YXi0/H?CJft2WKB_9O]AeO&]AUI+.'!QZk?$>Ql<7,dYjG-u[!BJ8*B%q:`Mq2O=[K3d:5tA`B
0q<)-3KXKm"D8)!NciXj]A8SF@D0u=T5AP8I4CmGs4S+3""Zi18Vs"o!9]A1Z[bJjjG6HnqKM;
,#N93/M::->P"`Z/l$JJ\$t(fp`j]A08'(RV@WFaA;OI<+Q7ja%]ANW2?G1C.uDp2^t$1/\pmO
YKl/^@hM^1,/FeBY?P41g@:<*A<'\PlF[^b!9!dF[L90=oq"ohI[hhZsrPI?pQ^FAEPrtV^]A
YNS?7KKqOl!R4mE-fj,j(<<]A)(4`r$Y&Fa5KQ^ZRl4*1m4C4F,$<%0ZuPoNnoEHUce%RWY;Y
XZXLtTKQ6@nVn+T<;RF.-aC63HST^R-JkTrrZDDeF0)RK=I25WpCqg*3MCgBR(n_Re/DR[";
/qW>8n\t,Z`E<]A/dc8u6aECa3<m_13CA2k-hp/3pJc<p6n6_o/S2_?r2*=<lV2/c'`@^dO-V
_>_.o6p020s3O%'YJDU,4ZPOR*<ZP_:-T\eJU_ef/njlOX;c"D=8E=9t+I!sC>%liQ@*k;]A$
2k!fp&N#\)SEJTkC(k%b#Q`d:AG(h-]A<8I^TZWpL[,4Y>'+,HO;ROG!A(W$-Go1M#`q*T,>j
1=c2Zn1.b9I`U\P6%et90=D=I"TmKlsmI4%pjHODW1^^hVPH`7uop1;_<[=[F"/6r[iB=Hg_
s)0%5gGX%Ep]Aju*C#N?PaW5!qfHdo,Q-nGT^A960np9if\9Lk@<6XQqBfU=QbG3)85K1J+%-
e(3m4]AqK*r=kEG8r>t!6JjiEP<LR[efbfXgl;o:&YOa;HC9S,Olp"BueD9[2j46K66N9L^N.
8HVMVPl<bC%/%T"Cg::B7.DqRWSPaS;\cmpYm@R$B$3^-5seWls]Aq]AM/)Dmt'Hu6."oV[[0L
40S`1h9<QHl'aU.<Y+e2SkC#$-GAQDb[0*e&XT(B@&GM:nK4U&_l^4]APr=T3;+h&6L'Yh8J;
&=guV]AsQ*deEB<['#2Ce5kC4NpgkpgsJBF&i]A2$O5rX7oK<Sr=Pl8A+,ur^oB>;X5.U^r94N
kmqo>R%G9Xf_S*`u'FDb[b^S`9><7SaO/Y,!.pcm2)\O<HQSH0d<O<`*OhJnT=L@tB2d:g/]A
s-]AteZNRe^IGKC!kc_#Th>DK&]A:8f1*e-Deqto!H5WL<$^%%KA2dB`e*j4c9;b32KO:;R!<6
h]AD,Z?#^>:!68,ud2W<'hm167#St,ud2W<'hm1_LDd9bA!BUT=.hGbPS\(Q5QWk^[u4EU7r7
kC#A[H_MghCpt!c0.7khb,aG0g<0BjO8Ld:XW?db)P#RT:;Fr?0-2I#jqME_W8qQ_O_?+b##
!#]AM49"Q\!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="1" width="186" height="347"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="d0df7b60-4154-4c76-8860-1735f04c78de"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1562100,381000,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[5039832,381000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_fzjg_zb_right" columnName="ZBID"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AREA_ID]]></CNAME>
<Compare op="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$zbsx2]]></Attributes>
</O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4 = $$$]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-200728" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="64">
<foreground>
<FineColor color="-1745897" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[


_g().options.form.getWidgetByName("zbsx4").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="BM"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="64"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"()dTP=KZ>uD5S8tVa;eqht7Zp6a,ugb`139LtO@d80,V+'J;%[MDbCM*@>#\\&SB<+^>Z
k[eF9L(:&3$8%6NnVL"@:rHQYG;lk'EW^IBN=^qgf8_r.!mRQM]AJ#S9rH3kNBD:PLZ9B)&Y$
rg>d1gaCkT>s8.&7Y(b<nk-^?!2Puh]ANe<2!@NOsX@,VS]A/5-D^''d.b3TDXN,1"p6-emCY'
Ru,?G\P%'M/cIPh^a0sXI8PWBHco4'iDS]AjS?`MNqo1Km&"(`aXZo6LLu'ucTXRLrV#X5[4L
d9>L5apcLY5P-n0P8DaRdD$mn)nQ:pEYW0.r]A;;siYjp?+69q;EHU<uC>HQ)J@fo\=2r0lq[
O3$iP!KF7b]A]ASRt#\c>,>[4oK^?X0NZM83]Ab8/$s6O,g\Ht6c0HN+ac0(@<t?=.O6r^tfB[>
e=*SGh=B;5n"Yet-3R7?EY.C]A=#C/OgSgk\:kqE21a;4j0^X3Y'm?4hrUEb]AdBhlgh.6M%NN
`hKSJZ"+G-/oV_>LEN[jmcOeFO2eJrgI^\LL99#;+qmLF&fC@0rQg6hrS&I!em?'J;pY"MfH
[ar?o\Z`S\i"nJmL88I9>:%fVu)G>/rlA>Q?B&P[%bN'WN=FtM)R&CY3=%@XB(pq<Zrqh!X9
,7V81XU4/85/iY$?7r,>&Q?,cMoA`XRO2D\:F.<hp[McuNr##5=mF+5Rg#kjV5PK*It+d9TB
ddmC,iMkN:96h*DQ8"VqZ^KTG)VKu"G+;5e%*&YF0WFN<`gX7l<"rQ#b'A(7_<AUDX&7XXbF
eFi:e@RJRX@s68YrB,1Ds7TZOa72mSMCFPj#NsRP&Fu(m@SP:TTYc?(.&i8?NntNK?.[pB5J
?Q!NLeh#`eC`r+bp"C,A'JOdCW,ptdP.,M#!l*=>U=*\UX#qG&ec#&9ldJW3gB.$m")So;io
.`@R:c:=\R`>?k%eF/i8kmlYC57JnU.C@^3R*:`=7X"<kF*I9[C$a+,MeSOPe+'9$\4&KFs^
6B+eBlCeDi.O\"-*,T?bbp%V$AA=C5gb2CE)Cl&(fMc)k9<=5rGc3;$_mj6%I$9.&ZX90HSj
a_GaYY+JRpr@R2ihY&Y/\p-(,1n+Wr%P;\cH4T/Fjem-e)Pm_#RBdqrR?jd-";HT2#kBdCq%
Ht8`MQQ389.PS<8)#TjO+B>7U/$4UkZuj^Q&Sr;./AOC"A`hA@ccb]A=a@XhJ'*>mD"n>`+sU
`_Q-(g71!7cXPiCU>$+]AeA<RR1I;oQ7X&[?NUm'f)eamQS-W&E_k<>UKQU7*4Nj^2:d1^&EA
TA[Dk<m"&7ceo!-1r$)FHpB-Dn;MYG^W@n#b'SoWSEa4^RF(R)n)g)#5G54Dtm@DApZfbTbD
gs1l%a?Z8@g"fOX4bEg/Vm9^p<F`DLI"eqB_hCX)`5!M#l>%M0bs4IgBkooGU.4UEr;cP7%Y
/$cJrX9t1s["-4'V9ua;$J/6)P)3TYGum-!AT'!t&)'dqI%;P`#*ls,3'$fIAIYO3WQ,[!32
>R55k@.<>ASKm(doHqUSVIrI)p%A^PNS"op"Rt\B)88=t;\"Q3CiY<lZSEeSs&H&X7m2VY;!
9B^_oF$D:)W5)AFnbem\%$':JI&53CtRYp2U2V%e<13URYSgHb.j#7k_L8bfk'/T>_7kg&;k
Sk\YB0We&YXaq;AK>u]AF'=KR<>S+1FiOLaD64I]A"Su4c65OD*"\>VH&U'Q;#n=@XP5?BWT_8
]A-$O3T0QT2+sHqWZ&DU)<4m10RO\`Wc[3<P;56o_`!7f$)uZF*@>LYE5SkUSPc*&^A@<-?/=
WDi#Db=WWN^l;fL.KY0(<EMAIL>?Wq)FFTXJ8=E?)fWl,A=/<6>5"-+?Plo:Q@PG<X=d
A60)m??l7+h4S1@+OXTPju\$Hh57O-%5aV+FWd7^+d^nBHAuN1>ZSCi."of>!TY0u]An?i;U-
5#s?X5c-h<K&86LsdX&RD1etq$m%I'I3'<HRHdJY>>\:?"$p=F\8i!]Ab$\1`T[3Ql2<*K@?_
]AU/PVs;86Dg%VTWU'HV/7]Air+_oi.3Xob9ftAe<'b&MV_lj2N+1+1l*:&)C[Nf[?;P:55q9/
MiMLB\<;+N(qe(;(.WT-B`(g;:sY1h8O+BX4QGN!RA!/ZJrd[=<7%##E"<K(ZQ9QC3.C=\j1
nBK4[N'NhaLk/:@e47[n<l?B6PTt,ab['';:/P47Mt%LG+S\BDpWLb(_fSTb/6<65%S%f[Q>
6cmBShhb_5J(d?CA[F"KSNJ=2ii,Ki=F\+3uEgYF-ek"B)JcguJH+$.k?YS+Qcs0=Do]A<KeF
@OG!-`#F,:3X++(U6+;b?,8R^_>`/6?Od$j9?=`Ma?Bg4kP;d>M<`d9<SAdj=DMXH]A,4kT^h
_2'O%j9jo8b+!jLtpc!LrLA*0KQUkW3%L*[kR#BlXkp17";OA<OcP&IP"1f'6SukN'dmlE/#
Cf0)5S-kRoSHfP4G@*iKh;N/eqUE42)LX*:%iWRXtVdZ0T09Oi6Da6XY#DMUPjdgI4:5nAO9
Rj7#-T(OH(6ML2>BN:"\B'21C49L\KaKSIuTt+pJ9Jo4'KmW]A!gA<anlYg]A/s&9)ufQ?eFs%
+hbh9MYJW3AEfOA]A;"$CPWpWKl+gP[S;J-_S]A"V'JnR%tb/^I$"%S.ib8Jcq`f,N$92JT4G7
]AqFCld5u5;LGJ-W!`%\^78Z@`u8W0Ypcl^TKho@)&T@Z3^+b66>Y>Uden#d2Cp]AMJp^+2'0o
V>10T3*K/.;>"+pKO$noSt+;/#aqW8u?$s)tUiVGV,CVFIQrBDtl%+`WCh\[,0e$nU+q!*KA
NPM5H9^f!3uT(n2)H%OqCFUcKROASBT4RjE=2)j]Al6-AndoW+5YE2VdtHIh@X[%+\ggOpCE:
+-rn%8f,ga-]AqB,g<d>$jU0b[.JIUq-JRE_imoRrIGdL8gkhKo,?9BXO6'k*8+<F30h5-Tl:
-[O:<G/;hCF&.d"[Y/9IA,0h8rP0>V_GRL>9:@7DGJ<?I%E>LYdC.g\q!ke9b"`0RWKJ>mMl
KX.4X?a5S=t6Z"/7A7\-38aV/&TB)*L@6iscs"/U5Tr#_krKrjBTBZMm+ap_5dQ1deVqG's+
/>PPM?a4E#gtEcLEG<_RTu0)4<iFD)C4oZ</@<eDZ=S21j^fpDZL2s(q(Y*/YWr)iu*,5#>:
#>O<jRW:IB5A)f0<DN)A[V8'p4V(?MV-HnT,`fjPlEDgA0X^%GP!f$;DI\:4bd.6[K(3PnNL
2QCN%'OcDZ%12en<h(HY_phTe5,(]AVQ>S0Krenbg>L"k`G9fHE+fbqV<K-3YI)37l&i\@09\
KBQXAr40#f<WRbTlC&*)"+[+_tU[>5g9iK#?DMFc?2,o+4XVoO;_"X@2eH#CsmTHo`#u9#/f
"cLkT^Yh),$Zo-FXZb6FBFDd-Z>Sb9lZ7s:ROk@ZNYM97-Gd4=j)N<f\k=B+$$!g[bq<AsGZ
dK1h5%89B';?bY,Pn^i-uT9`Uft;23d%.fKT1RL'mLAB,U"SAfar,3NF1_sA_nXj\[;A4P<<
'R5^q+;p,eTeDF\!G3o[BDmFZJGF_X5UYbJt!5?n[NK]AjEt)p%8B3O4.g9#:'dWV9F\@S/"i
CFJ!c*+uCE92FpFlZ7e?/EOK$+d,uLPb.Pom;..D=+:ljId-U_\\IAIWgT2&8#hh0Eh:3D(]A
3_+_&+5^$/JkZOR%.mA)cXZE(8ht+%\P]A@0YZ-2D\itP\I#GWHbgDE*;iC^tduel,/Y2Wr%E
a>SH!^=Y^e%DS2kAHORW+f<+ZR:SJS!MNdYm^\m$.=!YWi(cc;q6,!*!e9Ge0_QYfBr5c5)c
.p?Q^2'@qOhDk.oPaO;fA_cDb'i]A4Y!%kLUjLq7<SeZn-JHB,ibOlbh&Q5V[AD7P9mqrUki,
\o]A2TiA1@c((O6k(C]A/EPP:0$G;:Ym5>"m5L&kbfK:k^s`[*9>t#(JVOFqAT/H9$t:3Yq#"O
1FrTPVh>XG7O=#gHB[^FBt>]A2=-QBC3PVhQp4j[q&"qTFjX>;4rum7u?:&_0k=_B/M_*m_Li
>eEJ6_#15Bh=c9Bs=Y4.\EWkf(*-E(OZ3W$>F(pZ$rjS7#16_u6iHI@Wh'&@%4\1fj+5$R-Z
mS(E3i]AO@dca5DK:8n0Bqr*;h:f#KK@=3N!+B&/k8(]A#'9PW-364a6K;Dom4*$6DG)4<g4<;
cu_bdXVTmas=>:'9>"`l=58?r5SFpM4]Au/IuIC[c4O?$Zg7*?R.B[(8SV,u=<eZg:JqNf@K\
[d:<n%0:oWRk\HRgS9^3#K(9(YG)]A<-9(9@jh:KejhpkLOk1m&?`dOn'&V#FIsRD8"np0^]Ah
G>/8gB;Ef[/(V5>8caAR418DL4pD:k"kX)gH<]AM,NqUV=mLJq3Dl\iaW=<?ejie<&?R7Tp+C
;?]A-b<StbS80c!JoA(!<2dN$qnDt@3tALCc3*?O6&n89Wa*3,&JT7<M]A1u`RQBr;,_O$'12`
!LI.6/!cfK%V4IH>aRe6;LBU'"HLJ8`0Y8YcqSV&ql8JXQl.S[n/uQ7SPBR1\=lKdDK<RmE+
,P[!AIe!Pj--sHB5`o.H5=(<FR=/a/a$W?T^atGbr5LpnUY<Jb37?[>?]AF5o-o[Y.Cku9i!9
+J*]AH,<*J[6JSr8]A/Lit6TO*D;@g8\%8T_`6T=WatL32b2,qr?/Ycf4Q]AJb%h)R34I`&\c*-
$Ci4Kk)B-@gVl$1]A+!u"`[T`hr)1/Oa9j,(1pU[pIV$2?+,]A6:[g6I\bN@;br`6#m9.8I)#O
r'p#1$4.;#[[%Mhd0ZS`6O]A,HBQCLjmN-&[(PXk^%Q^>#9l[C.^I[c0Lug`X$W%fh9,\!QtU
^q0b8\Z$^GW4$3G)s(3kXEl$>6s3MJ5e=;rMOH\Jugc+Wtc>5,becs:'k&&asAi:f^(jk37q
(&enDHq5Mk8TJ:cK=)`Lq8V7?d12Fm?>85hi$B(0?fFJX:5Do`k7QJCE7b*.*X?^Rp/us5WZ
_Nj3^;>mnQZN3U]A>`*r;_*hsU>rg3NF9br#'!!!Bl`be;$RF;t.E'YakD@:0&(H@QAqB?$QT
r'_c0U5KDGr<r#lH0OqGIge,kV4R]A4,h5O<1tLV&<aHOSM<kPdR[hrVW/dtX+KA43IJlVO)'
\-EqYqt#]AE'ZF"o-8l.2r5udk1&\2pQOCX5HP*J*aVHIqJ71gA?)oq0--EgH580a]Ar&:r9Ic
PEN\?ZBIEJ</)VJJrm,6cAGX6(^-+]Ao[ZO(k!?.U3)P@*LTE,t(!?.U3)P@*LTE,thru$4Q5
MW^8,CL$f[[*)#FBsj>FCDO3:1"Md$N*V*e1_54#FOq!@Ch>*+:Y,t1lqpi5T3/qBcmk\JM`
GmdQe^A")/nX+-Q3tNk8l9nZRA:7p`h*4Gpi\3VuS~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="187" y="1" width="186" height="347"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="348"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="2"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,826935,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,1179870,2566219,3238500,2226365,2226365,2226365,2226365,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="3810000"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="0" cs="2" s="0">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[序号]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="分支机构"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="分支机构类型"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="4">
<O>
<![CDATA[指标值]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[较上年]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[较上月]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[趋势简图]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand/>
</C>
<C c="0" r="2" s="0">
<O>
<![CDATA[]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0" leftParentDefault="false" left="C3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="BRANCH_NO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',$$$)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-10373889" hor="0" ver="2"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue(a);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_dzbcx]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="FGS_TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4='bylzgddzcpmc_20240622191229']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动端弹窗1">
<JavaScript class="com.fr.js.MobilePopupHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features/>
<PopupTarget>
<![CDATA[text]]></PopupTarget>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
<FRFont name="SimSun" style="0" size="72"/>
<Style borderType="0" borderRadius="4.0" bgOpacity="1.0" mobileRegularType="auto_height" mobileWidth="40.0" mobileHeight="10.0" padRegularType="auto_height" padWidth="40.0" padHeight="10.0">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
<bgColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</bgColor>
</Style>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$zbsx4!='bylzgddzcpmc_20240622191229']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF($$$*1=$$$,format($$$,"#,##0.0"),"<div style='font-size:10px;margin-top:0px;'>"+$$$+"<div>"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(AND($zbsx4='bylzgddzcpmc_20240622191229',LEN($$$)>4),MID($$$,1,4)+'...',$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上年同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="bp_jyhx_dzbcx" columnName="较上月同期增长"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<font style='color:#" + if($$$ < 0,'51A579','DE554F') + ";'>" + if($$$ > 0,'+',if($$$ < 0,'','')) + "" + if(ISNULL($$$),'--',format($$$,"#0.0%")) + "</font>"]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="A3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[C4=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="3" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=count(C3{LEN(C3)>0})]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[]AY&Ad'Pc5Y/DVVK1l-6t,MaJo1dcK]AO>:p6#YI$"+?=P]AcjBj&6^Mm(desWqF._VXcZAN$^$
GRHqu,KjjcY$t]A7,,,Y1\/r?`GX7@$4q^HLqJ58Q#33UIWDPLHk"$n:)=UpBu6<kWOW_XHYU
sF"[p%PsfEP:2Bmcm/B>8VaLj=kDuf#ZO4()V*u*SQ0DtS/("_<-=f<8,9S,%F!'>+IB4=:d
s^##G\hEXA3Frg5Eq`$s6dPq:gZ.ojICEYXj26mCoM]AkXD+FQW)P\Onai@#b'%?<g+[ngFB\
P?dCXf1ps7u]A_%6f^cg#s_6ICFH+u"Ee=lYDU>,C/*4j&]A,BCs'Fo.o)$F[G=@YtPj_'\0n:
EGY%-g]A73:jhh@MKR+$0AWM'(ImTH96t!lHkd8+?UsLhhoDX00Qet-0'DB:(a:j]A0mj:=F_U
=93DanmmRs_SO?U$D72W,rD7tQss5/pcHhCQ=;LNp^4)CA"N\FA*9P:4;@Q)2(3FJRou>-!-
VCakXHkhWq*UP.j`&!oRJq[M%`"$\jf?i&rJ$"uk\_0\BA6!0qZjM)9RLR9dt^V;s9O/+!R4
U.:Z'Zip@d=(geT@2:V)EgA0RO3qce9MD[#]AC4MO)nVE')iY#qf%K.Ln1COBpH()l#2RH)1G
efQ638+.&,Z.4gZ9UJ!>nH;BH=eNA?&g.m>j+I1Z/-@R$a,nEKF%h!spnLLU4s3)iNdWCqmk
a=QA\_r-=K@:QeQ\A+EH-t-F8DRos]A&g,@u4WHqPG*i.M+q3B'V(!2VDX^*)dr-VrQ=D?%hM
=DO)t(UK#g'ir;5YBdirAJ&rnk"EVB^[1-%6)Dk]Aat\`VgBMn=.5rk';bqf7/UC8.I>)o^@2
((22B0qH,eYj<OL<7Y^Bfeau"Ndbb2&!iH5_ORf]AA4Mce68=5G,UZ:T(jg'jE'Kg)o4r@9".
.).i,4j^!!&:t^)#i)F1gK?HD7W]A,8d%S4LuDHn$iK8I$jaan?D>m*s8*$<?&K:XYBOFN#IJ
8Zr`k9W^QR"C1"9+l,nJ:N=e6^/#d@!Z[d8VSk:uj,G]A-HDhcEH?n@,(?YJL8#6o)Gpd[>73
ZmoC[/0h)h.0>A,>l4MD`/K.F*K\uXW%:LXP1'e(Ib4tQcc7=@:[:q1F8\LL_R3J+]A_*#ApJ
_S>aj1ah?\%/8Q)o\nnDBFlALKp_A-gV")ZmLr,>HH`Z+,*/n`RV>_;/mp$q;Ll^^8]AglHo"
GXqBm=Va-E9oZXAYWRidFIc2ZW"2IuTU?)"&=mF;H*\e?'$c7AGhgX$4!@]ACU#u"#sK0'U2F
o.2qIei-SX$Pt4HgfLjRc,ad]A9UI-W6nE>0d9pc>FWikoju*m[YQO!&XV6X_)K_+VTR548Dd
QZ`kEFms4rZd;;`mQ^1nS44"Hft8lR"M7'O-1g0]A0B,8?PYQR9oJn]A!]A*N$"<dm=iO#;XZ\j
^eJdQISFjXpOUuEBRe=qo[RsCS(PlD)&=^:$dI'pS!m>KXG2U^*0mr3I([[rc@EdX(+g[UKs
@@t`u`\a7V5(!a4A8r^VYL,.SWio]AD>Ve47LHq;bp37n]AoISb+c3/Y0_?h3f2l;Eg$t<#*.G
)A=5^WK*KZ0k1t7U=2E]A:d>?1L@QHk$[fDQ9s%*gX:?64OVqG85A!b2RB>FDVrog*;9([XS,
CHfIG!(2NM#HLgF"2l1?n2t0Vch?GmFY&$Uo`;\ZBmgNUfi,g^ENiQJbWTq6A:LcoI"\kifF
NZ[G5,bllQZ^@@[B-V(.\5Uh/]A7BhKP<C@.XDK-F-Y#KoW>XV(@Z=l=Qtqd(Ah%@s3;s.%`:
Z=WRii\$(A3!3*aP?r2DHVVPjSoj+kW>7K@Vu7"N;P_&_!0/ObBjURWVSB^g.'IgN/NM&rUi
*uZ>G?MCQgcX:[`@)@rSjdD0r0i06HmR]Al?^9YN%q4_*JSCsLfFoXPI&;PU7PT.Pm1#Cd`lr
W]Aedn1^C?a]AO^gH+ru8H.PGh@[@0E@ef*N18al6#ag&iSKEY1.WmOFssX"']A=)l*AbpkBg@;
E+!pS4>B)-IK[O'pG=qP1>lQ'&03%7Kkf-lhh7QJFbb(h:#Q@;4d7Sh%Q'#^Ki995QmX6k_^
\`0t[U&2oK-VUJR2B1@fT*o,kdY5Ft/(PFa!d5hr>A^W(RY>BEdgSLs&;HW(NE:mIA6d('m;
]AI@"@?U\Qa=Ye[*S'eLfE]A@q6-Apo-8^%DK@K([.KkpeOF1r3Sa8\Y%0,NTao#Zt4N_tHgA4
Jhs,DhaLqH-]AY,(:S"2;b!pqm(iBkJN,Wg/K1Y4W!3g]A_It^NUU26UC!4W)5#3HZ*I*llR1F
AlBeYb!LLRFY>/3aR]AM!DS^(A*!DpS3/XfWBFWok%q>[9K'0,oD2.Zf^l:lW0T[TuclhR:Qa
E7I-HX?%,'-enqB)<(MLe5<BcVq9mcc=T=^%TSH.*<n=;mUVI/X>*,=tec=<]A!r\:pKH5PqO
Sr%s`K_PlHd1=@Y?Fi.R<bk@RG?!ZT3eRQgUS_*HmHU1U#P96O:$g;M=f/Is#.XEMR'Gb=u8
@O:CbfiA%0]Aa$\M"j$/h=iBcMi]A>AD$/gYg:?SsY;]A#/5/=9K:1Y)NYOc<_Ecn2/k-:3psJF
&3mF%N/A,_SU<;#=hF)WdF]A9N%ke3o\6Uin9hpL\YKki2A2_NW4G(.C<5sl.nol&JPT1R06X
7JCL*V>)WD",f8m.T;'*5qV6-7Gt2++OV;j1R>U"ll;Gui6[*nDF)T%G)0,o7poa"<d^'e5d
"YUN*6-;c(f1A#;cN)^3QIGB7NW8U)!/[&q3&kEaZ*qi+@trY]A4(Xk,7Yi"@*O#@9/!dQH!e
>^18guG7\?MDcM"4]A>)A.ZM-hcu5]ALafqDZuU3uF\X$-Y/#k%G1U\I>2$aHm6&?1!Zq\Jhnj
,70:fYo3U_k[k)[oaW749$`l1a,A)o@hSP*:BMM_QK5C[A"()SBDqJ?gT29S"mS\F;hSbZ[/
Ug/fm!FSHIa(\R,]ARR.3*H=mK:M]A?B:tt3-%cKlo.gH=&4'"4@dSB4`9_`E-053]AXSuZ*9V(
]A.[BN&K8olI+npsQS*3/`*.hd[_/,m(rp!hp'tSo\@oC#6qj!nBVc@s<5,ZGdn00RTIs@Fnc
\H:9KX\_qZX[Jo_Vf\^bFZjgi0gndA;N7i=-P0)C(K#TG[t47^/3HHm^I%Uh:\m[Ub61YbY]A
sF1=XXR+gF4.HgZ3b@dH*Q5U@KOple%WQQ#\>s&fn]ACG>)tp%@u8ht<*G.KklKSF5n@Hr$g-
M=?YGml7h?l'Ub^EI<.=d31AEXJn'!0^."M=D-#gR;^_sO<8^!e%;J#p,iW7/gGZ0J*9:r$h
-jt)@F(3CWh+%+0ZHJDa.t)Vcr=0d!D'3n=?0h>h*Fhde5O"0k9p,<Z$N>FN:=V4s93?+C`!
CW9,pm)NKE1`"G#C2bb;u`(SsQ<C3E4%'OeHC'A\Wi$e&:R+8G%bNYI=%m=fD><EMAIL>\So
rq!PO2)p"+^iO9f2t,+NM)^4g^P_]A3g'9B.%T;s9/Sb)QIGro@.L\bX=mmhHl#NCm8.1`clD
#1&Up=M\!X2=#NtK0np[''*I-TpJ&)#VCCoS0kL^`k2g;/P)V]Aj^g0T,DO1gS,ms_&5Xg7.l
i^DNSA!"9FY18"`3rW:STFkV?B8&(1jjPNbo,Wj!;doD6V!X$J6-4s^E0OrIbNn"Q"mY]A?GC
)1lPLg:-mn`oAMKHg>%pJDmoY;=m^I(&5+-BfAB?NV"9>mmoYiQo^YQ=ud,\HJKOd@I2Q*gS
1F$njH:'K</^JhE%%Unutir^Me,rbnI)-;<b]A`5D*]AV*6MLLlDtEIGaY-GH?!g<jnQ?/#:&s
6W`kb+cqNUTJQ=USYO^[G-OSi&(&:*&6u.&VD(hD"UbDoBF(X[J1]A@-%c[Va`HFcb`jTlcWR
mj2F*[$8q4c-"h0=I5MH'cjE<GF2AhV-"HI#N1MT-k_fUPJl&)5'aNE<&=a6WDbmg6"@f>A;
q;U#;Y+]ALiGsYB`0/(I'Gq..l0MdM?e?7pUfPHrYWP@,p3Jg-QF:K,=Kk$X[A1^K8oc6T6*Y
FI=F_drC^i./X.J;]ART.5;`b`T95Q8!cFW68!T]A:11^"<;'h=orHI>jXNK;3\feUe:!UMsW,
$p`Ff%B!<q*O%;!GAG^es4rY8#oN"'20KbSBO&(*2GR9`b*B7H&DFIo0&dc2p:.T]A6%b`QY@
,/.4<;eL[Ba@er`WkY'5areplg1g,db$2>bF.eDSa0uJHeR;u"<2QTXR>Rj=00.p%J>!)[br
mG$9<6uI'(#m)6B<FQt)2F%;Qcm%W"+U`Mg0&,1gUZl5*($fGs.3<hT/QIe2uZ=jg1D_=,pZ
:mCfKJN'Y8c+?pnbFd&IDpM"Gk,A!,n]A[WM(mB^>[D#'>gbMZgSO=6Doi)aLXq.11l&'Ft1W
,2^pL"uK<Tj>nM<ia;D/NW(\'Ku2<Mt\3=0%!BALt*!N]A7NbU_^)4;Y\'W2U0:15)Yd'n1+J
#MsP"=:q3.$SEYQ.I:YS#\]AQA#gS9"JW.N#J*]AZh:Q&epVHt(^:;B!GD<GhRN$[P*PIT/rD8
5u"b*o8\/7*oLT@X?/IBhL!I]Aim76%@5Km/C(DT^b6jFQ_=.63!R\0n?Ak^7Ke/,0/:Fa__>
m:(Mrr4HDcS!%[q46ZCM5H;ht\uFZN9^B:C(C35A>(gKIVA([8L2_X%/TS>)4jbG=a,j:"St
p/l'+loB4/WosP>I$663KslkaFt`I#N^,FC<>T^lPVA[m9]A8XgVQYJQ:O3r06M[]Agc"I6lN/
Cm.W>[.M.RkFYW1N=4*,3f78a&!?NtbCmfOUOT)!,ta'</V95sq0*8Uu>ErlD;FY4,#7AqSV
:c@2Xq[sA<*_VofFAS'hC7o5kg:A0:d,=I0XoX^62%.EH@?Y5is$"/mmPj6P[`I4a29mg,+)
R=JMSLPbU6n5P[:"EjKekU"f9Y[I,C)U-\Z#D=8U4)6a81qo_\IlMhP)C'dXZ3-I7ju<j`Gh
pF37;4C7:Ck5F0na`Cioqlf=[;^qRF9SP;*g.86np1+m"*K%[Vd%mk]At/XR@uV1N)W?V$=DG
W!P)Apr`kHX>"5sjNrO6IbpYhNZDR/=Olfeq-Bp5K=b)DQ@AMR[R0-'2T&EV)i@%_.W3taMX
P28H_W(L6hahAk[NNlM-74%q^F@]A;F\q>=#;/,&7?7oVA\p4`9lDhi7TnV33u'@`/@nWI@HE
q)N/&C,:U'(k.;TMI?"W8::jQ)oM#k5jZF61hi^A4[R4kpR%EnGQ7T!Wa>sp?$V*:Yq13YJq
hQTJK$u^Fh1@R!:AnRW&5T8Hp1gVq=MW3Pjprr3/Okmr$U.2nraH6;X[1j]A@#0%\J4FW[NLE
8@m&_r98p/:GD5SlcB6uYtd.ViqR%XZ#nY&DQ((FPQ\co$T'I.G3RE#;mn9jW[a\,pI]A/;MD
C]AI/$=dPS\Q]AEBG*riX_W'V5,)pJE]AbV<Pk#M"<fJr^_9_^B<l>&</m[O>fRgRi"XX3dt#*/
h1<URr?r,Ng#FqM^EpmuT(cGD,_;dT%+E6*Agqp*$6rMf%'-gnGrVi7rniWqc0mRPKLKe%DR
dPR*UJ^ST=KOB;49M"`*=G!BJqH;FB`O8jh^3[Yhg5STIp`P'CH:Np;McOAu\]As#!+*8h:Tq
*?s*!!)^!h^+m#i(C+:4g%,/nGUHZB6nu)Q0KnP9dN^Ulka6#=Ejm!<?cj$H7-m4MsJ8!8`M
si2@)gGJ#?[Blo6d,)pkj@aCD8ZX#kHFd>1FLoYjoa;onB'R`cI`]AN3th/4ijG<!c8u4KSt`
S$CFmN:r1_e*JhXnh;A3#?%t:OsRt6I;n"Mm$I88_3A[(j0JZ>/l;(s<mJ'6AY*]A-&YK]AWPu
ogsmSCOX""8IT]A2e4TVlG'4ZJ^ot+F$[ldt+@BHNl\d?$E>3'k;;"s#B(00d>+7p<,i%j7?t
q!Nht*F63jNcA@IiU%-r4O(#og.`q9"BJb%PKuKsFD_a9=CpsY*_I;k2qO:]AY4M'cl$Em:k$
g*,![7n4(NMRn6ilM?MClefA`H.^TK)2@oA*M4m:tXu;Df<]Am4mO)bVqRGEpM9<K"^!D:IT2
s"&,Y5k)a?Gbk]Ahh=d/WZPi`'3@5Y'G)eP=bP;nQ6+P@`a>0@aFfQ;EBXB[f!W&qdoGGI5B"
FW)7PEI1cOlM&Q'q$#WK<Qd$IGcc>-pPc@2C,IZA;V\Zar[f`MQ"(`NJJ-l<iPjqJi@K33Jh
4YT@4=^Q0l%I+Q.)n;@e782TQ.*.D>fa6Ba2=>IIY@JapuD@6dIBdes&khim!uPR\V:RQVX]A
re?jasZ)g*aS5;'nRAXq%kR<!a/8Ysq2t)'69LlFBk@!#nN\"DXL)?dqT<r<fN%4I?7.RAp^
s'0cYIHpBNrigTp`G;`qaL"R=CKA2;k(ua*R>(k'O%=1H10j!6s%7r*kFgS1);eZm7.'\^RP
;8Z]AnCaJ8(L##pdALp:@i>o=+kKk4d6i?h#>OmF'?=QNsJf7^U4O-Po-<T-4e[3:j!%cmmG9
HR-70]AN76q&>&ig`uWug0>d6^+!/GcX!l@$T_K>0FtA^!04)GogMb;^`EW0'>i$1p6C\._)t
%ZGD_*nm@>Ui>=/$dNAdp]AcKVuob`Xp=a5P*bghB(W,KWUsP>h!;Z;11\?EIOhc1+90QRAQ'
Z30Ms-H`Spp;HD;EDck%C0SZR7nn'(lHm$sD#f@6D'$<c=c2o0-P8d[IGlO``Oi^i]ARGC^&O
H\D&g%!FkV`7Ark3e0YODsf;FjT6O&-Rtp8mlQ)/B&[Fm;BB5&jYV'>rXOODI6JX@O/)*qU"
+j2hiN,CaDkCh?\GiN7a_=G!A&O'g8(@2f7?L+aNr#&t1(fA:PG]AqN)ukHb:BRU!c/2+7k[I
SM*Y2i_-#6#P0;V8Pm(&"E\u8+9D:=5/Y6/9tZ=VbA^taZJor;/"(R]An/k2mp-P$pS]A^rID$
khWfg*=C3F@@qihI@CWnfbV)uSrW:2h7uf`eX[h<&qFj\BOOkQhq)P@DlI@Knj:Zl:E*)oN"
#MLjA_+,$+o=9;s?;__c4/'[J%=O*<p!E_^Q#k+=3:<%)nR#Ga\TE:;c71*0?lH!!;NH.@WF
:o>c8TDMMN:EgUB1=jHLO-cBLmIabLBjNk^`EeBU/&`R812Y)1J[hOOk8[T("(3T,j@&u0h[
ncfO0Of:p9c,(h!FtG3]AhD/]A44>YiNO1D^U<=L74FbT/g:"[juEB7=K8]AfljhJEIGb=q*f;9
a<XPZAKJCR-=;H/mROrc&#t_oI(Aic)I)/&,Ut-#G4a`br^s$05j(hf;o8-Hn<#u54.NJ?i:
sk<9bit#@2uK'ia.ipKHebVn#l5TLH3+[k%UgfC9+/j&cn1A9)UohdQQqap-JWsl!=hlGURe
2j.f2r,OAte1_Om_nHo,2m/8Q2AoR2^kG]A'+f!R+]AbAE#M8DN@nc&BR/o\LZb5-EUQhbd3X^
IK5%kA8s05?d=#3<.P0-Wkh,Oo_`@CSNDHNsK\`o,mbjN5?V[3NCReZ2e<K!R1i.fQ=[_1cH
O&l#itc_NbiMa+uSWKW.Q<n@kJ"I<QA<+gJQ5'[#bB`)'cR0^g%'hsA5>gg>k2&Hu4\YQk>M
dM,/A@k0<dT>T^gJ90\:`2t(M(C+(EVI]At5&Dd'*OF=nu9p5>Z+sjhd.sW@!&$.d5f/3Gjea
7[nc?jSjH;oJQ"39]A1@@IqV<0@b-2As<;KmFq@Qo3Tca9&B:Z7Z"u1e;:ZPsqB2@P%#LnRZ[
8SJhtPG-I8?=RW^g/\DQRfoe3WafF6HP2C7h"Q?H"Xs?[Y$U,4j_*VhVdNA5B4FWbWFgW[n9
&2u^'fXDk@e59Ae&&0C&F'_Y#GG"U;@C<O]A=Vd:N<k+hT"utiFibRb06Xe;SH<cABSPk#)c^
mbdCC>_m@Y?jIS7>ED]Ah/n;/5]AeX)5BG^2^gB#Ug\@+LNEp<2J!p_7FubU-:3tQ3hfNOq:m4
K:"s8*O6qX"@?>T,M,=IIqNC+,bDGG+U*bp`Z9;3KHhA[0r9O6QIq?8k$fF'LYH6c;tl5FFU
#`<<B]A3u8WBAfoVDld]A3,$ZVi_Y+PZ7JB-cpmip`Pad7&K4rrY$%O?m-\8XH$4Z(l&U6EXu]A
^lLP[3R]AmgWP>O$RQeetMNqkSIr0]AMai(b7I4:5ojn"_\WY5W,gYUKVT]A4G+%8]ABb@0RkHBc
ceJ?n#9)e(orYL-sD,%UlGt=0uaOaa!Xt?R-<d;*NCdpOH3H<c+7NR%XGB'aX9k(]AaG)HO<8
c(Z;OSG(Y_A*B*\G1\+-&F,]A%9"p"2Z/gJ'<L=mEk2-pp#]ALu]A1GT.%EQ*RfQ19U&><<U)D/
>1ErEOJi/YQiU$i/@mKJBlMh;Z>gT_VjiRJOe>9#+VpmY4ZkaQ?Sd8_k?SoM&Ml&[<]A=aU)r
3'Bd0L($Ri[9h<]A#hVa"d%NKte8\$=+DA$:dEcN[HJ29W`;R=[O4sH#8n\9pBIKdb.(%G)Y'
Y75$[1Fi_.lJO#s,ceRMomK@8$NRgH$=+^qigg["]AFBMC+RnQ-.DGe"/Kh`"o/E=(+@1f:gD
Q"AL!SKX_rMDI.Z1-UI!@UKCT@/D5h*g9PJ!9t03mgKOEH9Y'9sh568l9LO*H]A=LS9YgJW%J
=K&F4b*7>$WGj15D=Ua$%\XXG%@rQ#-K>qF0X(%QO>Ae$)dRs#$/?LJoL>Lk1s$pj!TJ:tGL
UAC[b;ePKqN^_&+)hqmPQU<aaiuJo`d]AP9-G'NXch)(?.+k]AnI7_=J[gn=(@cZnWhHN2r^kG
3FE!tJD?=>T8?ksHdM`:-ftl"h(Z*j5Z[Bgg;Sf/VO4N]A66;pfUk^X!rtu7K8C@n'1"W:Vb*
uW%Hkrg.u&+48FO%RbX)V4m8JV!@ChhFk1)'`1"tAonMceWS<D@39j'uG%73$&c1;)@Lb)c<
uk5_kW_ppP1Y%Z;9GF$SuD9T=,BhsS!*Zo1>R]AZD%!JKe*)2OOt<^"/4HDSGhEX^Z+fXm8S=
-a]An/Si#J;lWhjE^Mb?O8Kicli"f;%Go:#jg7QhuP\c2EJM1hP\(Ln]A_V<r+u+NPg14nLRD$
/%50[>I?Kdo0I%(G1H)rZ#t6uoi;<3&RP2"n;/nYm3W:th%AOf-%p<4TIi2)$CAXA3-1#"OX
G*9?[f>9rK&tTl=kn+U'`F'\*%Wu@F[Q"WnQ*N`;h/C("MWY[kSgII%>h^[<\GcZY^"9Y)]Am
r&U8O7'N``f3*q\T\KJ@\/tfPhDj-!tMp+p(S&H2=rFGCedNNEqVY3(0DD$BO0;D3gDDg,,5
!U<\F5SXATCK3eY>^T1;#6@2a5-s"ds\&cmQ)#Q?lO7.hfc/2`74,'W+?$#FC='6q6$0W+#9
QW)r7d3,V)k"JgM.m7^k8F,qP:,\f^kO-V'ZN>gZ3/3sBffKi]AnJpBmdh%TG]A83d&;>*]A#>&
*Y(lLirN_c/hB7;pEV+@Zkgs,8`k$tpnn-]A-KJ.jcJ,P&,j&o%^X(,3%R_:*I#'=$XgX*qb,
/cKYYQR"""kT[:Q9!:2/S.!6#Y"X?\44S'u,r(+I*N"8-Eut&<Jr)$f%P*F$gU3C_4tY&VFO
7rk!l`_K:1rOu=ND6N`,U:0U)Kc!OR3Lj=JTAnBi`)nK<OTsWn2,9Z.HGTe(]Aq-'+^h:1q?<
hukQaCE;)>"$cXVA:JX)(:G]A=BI\dROZ`]AoWj;&rr\dkrYRTU:&nM?/P*X!-cB=,&)6d-G7n
qNdHrZ=&QI.g.;468Fj>R,J`glpkJGN'?n$u9'9\4/)4MTNf'gH]ASK=huUdT:o.?m8G:XG:G
`KPKXaT4ja.oN7flGO4o9P#8^Dm12IeJL"-l.=V[F0GSmU,Y';HAPr`F^)?j8P1Y)d3V-c)C
[VK+5hmjo!#YL@:OJ/P[0'#>.^f08=VoR4L/Md-76A!8]AoF&XFJdSf^jhT.TB/bYc<Ep34k=
[lpDLhW/'EPq=5ispS.k9GQ^`VAAGJ+XG!Y#!u>mJ8CdBA%deln5'4=Ke\Bii=!a]A;=ib$/+
Gd;TV<=X9P34/PoAIM.`[6BY)lS70grk([[SIE?1&ECrcGHl''iI38:3budS*9M6d@ajL*ju
P0$'0H54%SZ8Z.13m"R!6nDfcPm=)OCTjRVt1rVk?q2KJaod(NHi0"RHYN4[l@Fi]AedEa+$4
P=A;D`AXfDr:VY+q7Ug31fc0oX=NhZ\b+>J#Bl]A3E>&k='JtJ*DR5:19C2=:q+-Y$qJ'A\BU
L#+ZpUSE#@gN&9Dlld/URP/J_$=g_n"7,m'^]A[_^KPlk$EU`hj_IkQN\>MYh/(k6Dt0\_p9Z
)Pcq5Fb-1P]AKi,r"%qH[W4K(5hD5H1V%]AVL/n4JEqSQN_^q[U6I;'n9Ye0n+g`sSS6XDO!L-
BCYXSm5+-Msa4$'3XUlO.eO'fN=kgHqY?&0mAi!Thp[J=,7*')^kd)Td@Thl&$63#FiuYi!E
eRYYBqKN@nsOqhR7s*JlTP5u7k[iT=%Q^g&rWG>`+'igBEF:G<.'frfHa8g"7i[Yh)G1s`+2
g>[N'G)Kp/@RHeu(8>2ZI27)n#q^a=&2`I<Cc,$aZN;2nmmRq>XNoBWm*`j/F#>0b4?>o&5a
Ad&n"(Ts\dUZ[K:R[%aoIpKWh'i0!;.kWR,`^X9k@;G71oC#T%FgP?41G`_t9Cfb;At=;"j$
?OXJMf+<qgOSd^teLW%93#Gdjt(YA+D&&taeCZg_5787g.;=PR3YA"/Zcdk:T3K[Rp(@"tqF
lN4_?OP'-$3A"AX>Do6/_4"?aVPCpM_\kiJT&93gdV,XT4J4f%SM?oL$R9Y%4'uLlaeO)/:r
P_"X_Q\E0MLWf?1n9)iY_-BgLI'\+h9DmeE/lGL8h7Y*p+`K9T$L+-u[!b&cq;2P/G\o1s2<
E'uXBNT<Bi\IQi[fn_>:SZ7EpO@b[B<BaC?]A#PPY46#j^_$Gb!cNjDrZDCb;\M0nLKaHG(_6
-o'+1^-DohRZ^hGoS@Y]AjM-:Y*&VgMmh/eo$@64#h5q8@%r#X]As,BEQ-"5mQ)ACM*s*B/=f%
b\<qN_RG.7XDg8kb[._Tc7SSXs8d50F(j9g&d8HpN]A.-n<ZG;O(#+)Pe9F:NlVCetp!%]Aq`4
hQs8U'OK%_qp,3ROZ'In3>7i5ENSOK4R[NWD9M^$SF(p3dS@S]A<UB9KngiUfeElCq,Z$q`i2
=Wl.G;k#^C'Hg(h5#9[3:eJuW%K(!q-3e\?Kbm)EH1Pj-#s5f[`5gAl/,l'l&egs,$`Xc]A!8
p,O@.N,4C[o'q"nm/H]ASn_9\nPeCpj>>E)!_HcF0-=7c.FDPR`g:^?[7l8^W_.+98;[U?_gs
5jm#94>t&NA>(UXjp4RXrfgAKM*:]AtHmS7LKT)iQ*KB=Z?['8FlD!mm&O;g<E^=TU*;\a,[2
!KPB!%ip'E\a%E3t<@r[1'cR+jF36l8"L?6+_"g^2m\uuumNG5+OKDpZ&CuT6*I?LaiCW=kc
)"h+MeG9$m@J'0Y4]Ag8jj^HB*^ZK!N8#X4n^5(.2.T!LnR@k8D9unZ.`*LGMGU%7^fGib(?1
bJd##&t%u2&=7n5c0,0jIO/5>EDNVJCj&r&jj5jq+k;pB02aOq.:EM-h\6>GZqhD(-<;;&IZ
*12qQiYD,Lj_8Esrh>_#eb>;0^`R:Q[80"p"@f"%l%!^aZgD:R]Ak4$a%EusUpq!u`"d_D+?+
t#S9*%=_@5cn(IM8G=Ud^?1T=DN^kSM5R0$(WIa#R+L,&*=M4fTe+WF5'OoKi;piU\=ZHD>d
m#u`:lV)S.XVq:-9JpR4UlS5I'%Zr4-jGW_Xn"4l&?a9#f3cU!@4rU/@Os_-5)oeH8`UCHp1
d.(#fjOQFm=iC2mRIoPK`Io)q)>a2X;3`C,OE#L=H<q\`/7%7\^i>+TPdj*RN*!j@Z0hd&0Y
Qb+>h1Tf$t:!Vfi.MBmn32.f.&rEmZ^HY&NaMIKG_ARjq&]Ah8C!pH[,4s6]AD$hKO;L<*tdFj
2o,WR4i(OE?J*K>k/>:eK"^?p4m;=r>'SP(NpE9%O7)48,T+[?#XV0E>t8,D\E':Vj1mS4>"
:nofD_B,,o'E8">';'Ee(PjOeLf-<CSSq%td-;XhjU=X6C-J?[+_N;04k!DH,E7A&bU[2sLP
DTRMPE*;!k[-.qa07=&Rk\L,tT-IMmo9e9\O\'dugJZW9@57E]AtI)h&ChAT74M:I@Z_Rc<L4
E'g5?i*Ni22j'=)I%<r-5bm-(MN>AES%>cA4<FsJ?s+=4ck@$E3_UkUujoNKji$7!lqG:C=^
9hK$XMr:peW(61MUMi,]ArBjjm\)`h(@RP-8W&6"+/hf-'eOrO;/>f7fQqLi,WFW;RQN-B^9)
UkLZ\=AN2d_>*6rm"p+_LbDg'h":Y_jd>+[C"^+Cl7GA@LIVEO3H=S26BP!jA`LSp@Z4`Ikb
#U*SPRO;,FP_oLMoFMC(t.u?G,ZcoK??lJ7*,4/4a9>Ta70)"+<5XaJ'mND@+XAr@S/k2pAa
lp7b45IhPdB&eGPN0T_\mS7hi?Z4$0NEk(9tDori2C[^@8S<<:IZk&N$\4F]ACkUW:)9]AfO%@
%02j9i5t6?5s7@]AOa3-<B*_j8:$45,mqb1$ZdRu?r_3r9%oTV\&JG.Q'TrfbBK`7Rb"U&0QH
BpTbX=M&<Yhp/etbD0UaP&0p]Au372.nRc4Tp^9mg*m:^**:-`ni@8t[PlW<Q5D@=#XMhfjHG
=[JUoL?r/,bkZeQ)G1n!ORk0dbcf!V242l_o/[$IKon^FB.N0jGeiS*E4eW66A7P]A#3Ph$l,
)p(6^JRV2bF.`Uunb\"JKYa+mo#2el`BXo;K&'TS\smAMEb8]AZRh"<DTDZ"n+=M[ugeG*#63
qG?\a#c'8/EEa$8QM^\?TLt(`Q7qV+TGQQbe66c*T&4';AII+2hHBgeYr%S%rga[EdP4u@.M
D:4R)!59>[FYInrM^2KVAmgMIN*!j-ad=$#B(6i#_]A(d568qlY=PEK9+qfE\[fMc"sVMbOJ-
PK()g)4EFLXR\#*%lH'lFh-Ktd:Y\2n&o0mr6#>UX)6N$HP<A4R+`Mf'D@T$bYXUp!$Tco3%
jq0K4q^(F*<-h5.HN11VY^<EMAIL>$l#XlTh\?iiN+[Nog#]AHtGR++TN,*'7a9mT1b/
NSJOV=]A#0Dl,rZ%60a@g2Kp\1W30VPrR-TQ@3c+'b&W0*/kAcG53jX_+rY\I$FZj'L'1>ZUa
9;<tpR9enGpQ*dIf0q$!=;0&Xl2?u!R/?01X`a'@4G4tQ;,d?Gq?f)NW"mnhFL%hUZWWP]AQI
5_tBVD=l;5rdm25;p#8UQ!5G"`:[O^,+7bk]AQLT=-!-=8qg6$_o\mC.e&WTHrS?IPfBt'&k-
1Cm=3DE0S*-S4!)E2&o'k0A5$RMql7?8STWh6V-?#WfMl_)Cm).X\_*P&@>*V537f#(@P"SL
p=*e*A1&$OTpg'F]Ar&:uq6dVR20Nd?<k".s9Vitt<Dmr>\QTopG%$PQK$%f3IG7:mRmQ/FLq
B-b#frWYc[YNEoI?8PIsbF#&d.64e5\r#'qaeLP30&MI9LcdnC"1-!)5h$SoC!iVlr;i=J?&
*1%j!OB;mW:LGNsJHpR,Dm_GICU($Ji1l8)C5Y5l1_kbc8;_)1_\s\L-'u?I5pg2pU0q5cm@
k4@Eh6@\rh[)/EgT4J1m;1Gp8(C1f<!AK9nTBNt%2lp/!SX_u%m8-0rR1\eRKJ8p2Y`8IB/?
h9&N&BRY'`ZedS)$A0)`<$#Q;'6U*)dfPN0F9NQbFHPk1V^&;i_KA7o2Vs$:n,6U1P>ru/?s
MMe(3o6O18)DW5MU2l2Z6*8!Q]Aqb4IR:4BeA[[fQPYSl+8"8O=Vl';op;cFN5+\3XL(pK;.6
D1A;?"1s-m<_h(=k=7phi9K#^V+C,H(u6IKlZ<QRE8h(mZ[maMfm76K>F/6UD%AKhooOS@27
@`[fU)[nO*+EF]A>+]AFGG56H=F-c`7`/I7Z6LPVnIY1\OSCoq*Q5l-"&[l+j[p`=>_).e$0eF
Xnc,Km8VqYWP!]Aq:W_Zn9Me+pd9X!=$4*%<*D)t:[fANR%7&L+/X(c%+rLbl?3L).aFAl<),
hD%2A'8lk@eg6Z5;IO7ipUY0)%;S]Alg#b_XP!?.<Z'U5$ErI0iVYhbWXUqM3HNWNdH^AjgYP
>/erl*aM/l1q@>Ppgqt;4dl`-TDU4`=Ip8A2h/!I\HljeZX0e%F(+"6WraKd%cgC2rn8/BhN
Pq@n):FC;LeoD4MuJ=b,,=Y`o":1i@bsm/Ot*bc,,jI'+b+;Q*#/eX@@W?`FI2C)FDlBQ?oW
*0P"J:F=)$tXjo<9#q%S_43Whs`>%bc^?]A`F0.Cu'PmV\OI.LK$S(-uIpY&k%j$o3Kc")"8.
CX!*W)UIoRYOJGI>?X//4hG3\P1uH^37Kp[nh:3k(lWnc+TuRlgc$4qrrb4BpK#G5.TnhDl?
g)@p:0;h'*June>b<r'BpQ^T@0kUFkqjjF)K=$(MBoHu-9aZj5i1A-k4Q#I1Tu?:i/eOtG4d
bc?uC\h"iD*m'8fP,001E0sNd^M*`:I*QdqS_\GA3D[WulK-=o`[ZSKPC>JGOjA&<;uab^<(
Oj$<$I3Ml<t3qU^%d6%VbA`^gkH!mR46rm"DKV+BC;_)>Phl:2-*49YBHR*j^+Mp[#]A%VY80
mT'@NGKZ?T@XT^&l!tKcS@.Q&#eS0h\d&JtL3PUiN'ujPaE8+Zj+WVr.7L*g,Q2CDC[-0e#/
%D,35^o?YBAO*^n`W'2Rsu*[o>guE\^@;;eUq't=d&N5rq9rpTb;oXd30[]A[s'YFb<j9<;:O
H#59E@,Co:I+3'Nf)p,79b`o'GFd'#Ga]AG"G"*MDM0IsI=8p'[K/k;6Y2jjj\hT7.#hSE+*Z
Gr*6K.BkQu]AE^39gYX_d.#:bs,KgQT,CWcgX;]A?uG"O8M[Q6C4.En7U,Y6`KL++&*T1oUf!'
0pWA-,H^8APVBn(,#>'TQ'BI/g3;SW__>)SOjkiB3"7i">QZGV`9bm9Z$)E>jNVMCr5p/.B$
P3INq[$Kl92%jQ'0IpDkmNbYq9c-3AMf3r!Q;;uapV2\r1Ugs&LMaj;\'nYA1'mT>0@;3Eq^
9Vb=ieVQT&;Nc9O&?=q^-A#q]AP,MOBm="U@".^h/:gRT^@0!R!_/^j'0&9+pRgDFkk@U9`Hk
P[E:<%/>Fe;D3%soQ*LDg&4`"t,KnN4DAI(8\`(K=Q0`X'lG;7@WNqMSZ\(nH`$g`E3cKAFX
h%kDtGGSZm4#-)Xgfo^gR9q@*$g7a:`O"fqC`]Aj#R*4`e-[ta``E]AcKC9cuJ>^f+sOCBCWA&
NL]Aa)0BbZWaF*WV>A"qu3aJ]A5)d?dV#rSe:7UfH[oQGo4^:f9aJ;DX,A:Y@TY*>]AJ5q3##C1
tInWI1k.;m80&J*b9l=Qqq;8.GnhVn7'j"#Y>8Rj\C!Q,3oki=J$Wh0bB;Ui$Q2%)+kq7&?O
tdK>rnDDrnnjZQ"6NQIHJp4/\i%,_o,,U'&uK![2=o6]A\(9:T=/"oapIJ)=^9E)mU-0s^K5Z
C;AK!4dT-:S(\nJed=nCX;M:0tMJ$^XN!i2#n>Lf?h0P@Z>*p9VNoOpL#a@_lFr6dRoPeT6H
4UKYo&W,h(=cZ?s@n@tf3J2qdp1N\^CTN;gFEMrt9<CW^#>_DnUjgDD^?Ve(FmQ#Epns;N1(
ST^2cKcSK6"s=A$FHkl%]AGPdp[dJ;Q!3l8Pik+:5>abq20((L<:+T&5AJT&(YRqC`&>cho%1
BBjD3FB/K[FqIBu9^#eoJCfMZ:@N*PNdl0jHUd_[hLK;Mu3IPaogi-7T[B:\K#tk:gF5oCG*
GD:u@J.Y!(,\q,iU]AdZJen5KIIMD-5*t":r\<Is/`W/OjWbdprc.0VBl&o8nhTs,jtIl\=aa
;d1Ol=VB=0=E)AEoZ)8>JC$>2RQ;+:bI8HiM_OW6+4Wp]Ali<f"[UO2e@3lKm3Y-oA!k-[,W7
MSh&5^\G_!Iehs&*r<L>VSAK^+1'6Gp-:PnDP>42A\ntM0*Gm69aU]AlOS34/oB?@,iB6(F=m
!3ar6&6!0mKU0Ht"cG@PQYQ',B']A1-o#E=qTt/>]Ag[:L,Z\E.[A`$;_06O;0I#(S[7%W$2>[
X;3<#8@W-WG(#BuBLG"hk2C\I+j+L<+Vd1:"A6;Y"0V%ATS/@D55U_sFdg/j=pmbg;&TkET\
p^`I(lfJ)%<j/]As)b.&h\KGqWIFe_;(),\#($<`>4TG$PuJS@iGXW(s-,)7l1e$9r;WhLK-U
ERQAPSWg+LjJrI6u%B&(T&R(XYHbp:*Wn\.]A3k$^$Q2d>i:!Pm?Oj`n<90,LflgKe=?Nd+(4
<$j(h9`0Q^@=sgsEps:,-NjUrU1<Jmjka9jZ/Mo:hkIm?B`)9[rBpc]Aq&_\Y4=nOT-m`emiu
p]A>W'+t-ZhL"oL4G_g5iC27f89dUDi_I-cQ&!p%<@VhH;>LW6l2k7`/m&>G"rED#Jd.jmOgH
q]A=b##)&MA'A:oKfKe@k(TT&o8"]A&UalJ4Sr]AX[n<$fR)0:[^]A9M+C0HP#ujr$Op<k<N4ah&
Ze0S^-teTqDHjO4-QMG^;h20fc;-'_td>cam]A(er$sTg70p#Fe=md=o&$WTKi@FR\`T`\2'W
a1e,3\XcCkd'=4(\;S>gJ!a)VUl_i(3(n=-9Kr.T0I=EV[&f.NCJJiY2M(u-E,iI@r04g]AXR
q6@[n^j`Iq;$NImh,&U>hf[(o,h%<W-8GuG^'P&kTUD/t5).@rUV)bW=b$.Pg5G$%r/Y:_i%
1_T]AK]AH8RN^7l`Fh!-\Y1[nhTZX,Po1p"b69J(.(dI_EPE*ukjVJIX8k1UMOjt!$Ob4:?k(3
3g8ln9<tE#ifHF0+nJ?t$V=Mk*s,`u6&NuKmX7#)rVfKGA0H@l;kiFbl4n*aJU)J4&>>M50U
dDgtoAD^+(lghim?G)c^h=j6IR4_Lpl@IWjpD0kJEVLMjo'BlgCQ.V5!EEY7<(]A`=M_jhT;)
gR</f__D:>*YSqb84K?TD)&[N@iF<&a_3"XUN:\sBp"70k!&!Ho)EKBAJNCJbpY74C93k9k,
qg`XKDNUAJ_Sa8Qm,jfmk8e5hQ>)Ut:9DIKqmnHX<!;9DaQ4ZjSZ@[/%kG%]A6haR9OoF"re"
foQ<!BPMR1=\iX+'YL\k+o=V58?bjY%2lhO-nT*p_F*EabcMC#8-]AD10tnM<)G?nQ6,5P6f;
64p'M_,N(!o@U_;D,a=me7)>\X#:\!*p=&rINi6YQ.$Edq8HF06rrT!^jo63ahq'bED#_!E,
UE*rd;URD_LF^.OraYQEG(jX:(@bs]AQMd*_PioQD'6^MY96/$FX,.kT\IB#AG0dJGT!Sq1N#
D8E-h,4cWA*R/Hrof7',"c`9!r?,/2TTV8IBYFdZ[U6)!J<`9k[VYIOVc]AW"DEfD]A$mLttDh
BaPO>JE+[2H2F6tLWP]ANh:_Zk;56p7_ibL.Ac4`7]AL>IQZq[R#Lud0q+)bc2ML&u?Igjs"mU
p]Ai1Gg9Q\MopmLhK>5C;8=As$lt`NCd@]AR2*Jffcs^3f`&_a#1j78MSm[CcHEqY^eK&OH5+o
9Y9d58-#@f9qX/SLbknQV\NA).bI9?gX01'T[f"_<]Ag!H<GhZXj+H4G8I]A:V$g+h#A[G8:R9
OkNfJAGs[U]AodiI.VL96iI_mc.-#%2K4r$i2QploLcW;@@#^r1,SAGlG$@US`.9:ZGRKDB!X
[ine-DsFp><ljLK3D>aKhKeNIkk&qrMV;L4.lk`;9._\UnTf?j1I!N0);=mm!(q]Ai.mO-^=K
:0'/DX`-aNYH/K/p,rk`4P$URfk^d4>%A+;*_1G#8>+>(ke6q73[0*VbsT:^F4C#pHen'&O?
QG!.1no.h`a'#5.aU4hR4KI^+HL+qk!6I,?LlXjkF[jgSmFtmK?_mqY=t^lW9t\!&Nej`n%c
9n.rtpEj3jXFFW_KI5a^)nqiIonh0OE^AHKB]AZN[hEA]AlE0qI\S6)bmm_(A/"+U-DaOg&V4<
O+%;TqjFl/oVS&?*+^>%*Zo#Z^DPWqNpN^15"6B^4Pr]AgXa:Np3B@U_1MIbE8+(BpA\B)c`=
L0;E,N/*HI3)88>daAo(`BHJp(*EP\HjmrYJFG4);Ia_%hXpCPTknG.icG<.0@6d))`D6HWC
pD.D!L_GpQ%DoF$i8LC[ap8l^(]A:tt8d'9<0g0uUo>tpVjRo!%(7/DMKC]Aet`R[\u=mtWS.%
E\+:^T0K=`HBg?(5s&/:KSRm.#5-PSd%bFWSi"26+rN_ngHmete5gR'7]AZ>tapbh9/$j6hp,
$R?:D5qcP6,DQ3Dt]AQW12j(?W\+4ZWp#rL5t,EEeoM;;?mgt)>?:%G*t4%S!54C&eo52uPjr
+VYHR9efPm1Z%TJj3d9mf*YZi7[=PBUF#)`L#4LOAsbZ`8;+cd%8ju>R0(>"q9iF>L1h]A9g@
m'%89;nH3#Ln%["+`WNNZJ]AGlWm<-Gt$C...f)p&O2qn?foP&Jg%f3%=HT/[Uu'uhgk@-<7+
N3W%nHe1L2<>rEqq6+?%b-;V6:7&UGXfT33nS+5sbG;:]AQ<pr.=8K]ATDHs2?:5Q-bd-.o=*_
XW^.+i``p:9'opH1H<n.4$O=3p1Q87H^]A)(usR"kjEGN8/oGM8[Y1FUQHti5L5E3M03B5t(J
Mj:u:Wbl?c\iTFTU?]A`u_&>Gj<St2h3iSn]AYnLmk=:]A&oJ<Q8tp@c(;aU_"VY[Q0kSHS$9\a
d3`4gr'd+V88Il-\jF/f;5'Xl`f`]A`osA-QB#G&,on:l0$q22^fJ%E08@QgVLCl7f_;@ChZ&
<779+&M6T4mX9=#E&I0mdVq?9:ZQghben%6Sfku1/Efp;C3afYkTQB4D00[E_VN:/iZ'4'ec
H=OmW$pk`#ZAYH'-^(9G*"L$:r8-\k1Uae!q0#!.gEP1T?eQ3#,ZTpi$2-V>4*\\e@DUuHHl
)t0q[M`_@_OmXj,A?g:ACuqI\#6DojNWOV!T?9\\L=Voiq+gaN7o92&f89#Tlj90-@VQ<<F?
59!8D9biVIArQG)MgZ`B4(QN?(JK2Tg@b?unWJ1i/(]A,@,c=.]A!RFu36-MR2FWVTHO?Wi]A95
o$AJ<&/b]A(+D/J[<<IsF'k)Kqn#L$oaiu#p]ALUeo)m1'!&1o.oB]Ad7OtT_ujES[ub-k;)/p;
&gN%g9:Heo`.%U_YSjg[655HadBGf0_kIh13d@m3u6+le1j3Z1D7SJ+kKCA]A]A<+177DnmQ%3
AgBoTfp?1'1nCAFcgICRB!9^c%-lroi3XOp%<C`j,2eFAis,EW&0mg9_CoHHaDRP]A*ap\Dr0
8\s`-j,TW`.L$]Al)2?Fgcdjlm5fU9=C;QS+A`TQh%.%def\u2Dq*QZfkE#lLCfGo(M:FrYDU
[ofsR>6c!0dFKimKE,tIYgB3>0qif8-)JK08So=YjSj,2gJ1tHPOgK?TXVu7rR*0m;O^Cas.
ShP7iRR1q$auaV_qN)5JF1Z3*"EY$UHVsalXdo+e*VRZS*J,)T*RoN_k)6T#hQkms#UjFb2^
5G%X-lTX.S%tpGCT,H"9uq9U@^@g[rgiq#IFCjDT'F<s%*SCRpW/cRmNH$,m:Bp0O62PYkU$
fUXcX`I7P=lQ%_>s0c;pT*SaHRL0]ADMr^K0=[TG_5KjB4q8EJhZlldBBag75o$/\%`GY(/J2
iJK3m.L+%Y;3bYD/iVKUKTrW-Hh4=)U9_Z4BOcl_j@-r@3"cHV@fS1I.)jg_e2dSGc,1m/GK
5=eSU\2--d_BLkRnCr[;eF$1!gofPUgjXq%%1#E=VD&jg?aNn>,IHLulOp/^U>:bZ'*dsM(-
6g%[0EOYMrb)hTerVt&r`F+3M[<(V?_[M\Sl!^LP-1sjC=TiUdJ;8R@Cg9d!Jqs+24YCB!d2
Y;J&5O_3tiDi`&H%pL"lRM\RJ9;BC?,FI^7/biF/2hE+8W`K?);YSk)"E+YsXK&1QoqFt3'"
e^^naJiV0-mk_&$eQ%&AYSjD+f-imBP+.7@>Di[5qq`s;B0D6aps9G)ELFL)?p%b\1mRE@kR
R[F]A@JU^[Yop'fZpIPPNJ/M(A5aQmD;`hE2l)NADRM?&o0t<H"KPOY8gENIj07>hiV1F\c/E
f0"qeX,N?4tTE`./\'/9'QIF76k4hbH*1$`G?WWfa,,[Ej2.t.b&S-]AP<Q+G`aK\[77&k#fm
RlGu-Vi\s`7cI%nmWfhVVX=h2<:A!$lB7t%?)^=EI?2:UG7R1mA'psi`Zle4k5<1ejSG8IfX
"k8UhWk@Z_/pd"7hRnTt6\%idWYNs^%WgAK:Co*9F`4TST0ET9"=Mc^oEG!X#]AZt_L7V>#"Y
/6P?rd!g@q>sCbF>.#[\10/<CMNm\A7V<^ZH(-CJ_$CTWhsWCC;npLqs)?UXj:T:T,Q([>!e
$:gpRn0@lIl#m]ArUnd)PDL[4M.>Jqn)1ScjdN%S%7^=Kk`T[3YnE@HI"///\3$@\uB?TN(9k
M38`Osigh,C]Aub;ScM6nSF$0,\d.blXES-f/Z)b9T<<g'Y&r58!s7QfC7%3U3c=PiGPu>)0>
.""cp2lb*L$"n:7DVsbR-kOo^[2Q\N-_D"F=tb?#KLSo<(B3d]AASCk3[aE5Acu*bhBEt8l5#
qAcT^ok9lfKGD&!D9l\hYTomY``).gJNnmDk4%6%\RK"\EB.2FZbj+"@W;urbB-hl>,L<J*o
-i'b'>l#P5j%j@B6]A7Rtps@lm=M:!GP@mC/V\\f5+q&n&?-F@B=J`jP`ce*]A1sG!_\fcOk9N
U)[C%co..@ZG/p;N.q2@PHI>ep-DIoVkXh">ZX-W[0gMB7_UE#MLeCOS;Q)cTR!5cJ[_co#8
P]AGj9m@1U23>`VO@*H>$K:k%WkKg,>l'/5_bkP/K?m_/akebX8'.,Kj)lhoqXRR;Z=>\K9,=
*6QLjl^_udSUffkbYh<I8B0*f7E\4(pEd,=K[rdk9ZYp*mn]AMa3^dN[eFcsU>kqW_>FL"*\[
dAlPCGJFj0\rBV^.p5)H6*=M_J$Y22I1@#3*@5b"Ak]ALh^s0K(<mR]A`n5_4a#j'Lo6bU^jtT
hS7s79kY[*WF4O"Dp50s=?j#XfBYnpYb3^md,(=r>fuge+6TPIrJ!?C;3R'3FPO!J=cf_.f^
Ja2D)#%BXHBe1j.F5q0Gm,SChAS.W;Ofs:bf-mr*"9Qgn8P_*hM^!31S<h\@,JV5/T[_N#g_
gI3@dCgm>Ibp0-MD1-djfjn6ptfOkG62:-=U*Em7M;atd043c;gKH9MlWl<B@nOh,s+;A;$.
E2DW"Jlqq2T!`i:&4j!<hJ3Indb#kMD",ZUO6_nrp!/LTAf"LK;M?LMcgY2XG:E#glV^X!g-
jq[S@`pr6$D-Tmn3]A8+bj9am?1fjQhj@SJ._01a2o./7bECKsB>tGY*`F8D`rJ*:3g8^o?_=
)pZ?6+Fc-^EYO'mg;ihnIbp0/d6deXa-j"mD=-^hp&&bS+0XJjaTi/:qq'HC#[f^AbKqc.Os
aA8%h;iN?G<\8F0=BEPf6!OJ.p-3aU_0Ls8'GI,,et(@LOMkP4alHM_]Aj-g0e40Bu+I>h"aN
<rIa<h03J!NYKd8G^eX)_S9kkr*G5AMPAS$Qf$TkHR*8XPcXNgK!Z^D\VsGH!!OrOmrYOblr
p*d0iXj5HiA\#lELq,$j-9)t_gAg3SI-V!QMSa=rZPs?L$i_9=HC+mrI<PunouS7p]A$F'P-p
I3[?S$@q`:NLb*'+C`ZY02pLQn<P-#I1$R+3BfU:(lfu#N;Z*.XXb`)ABUN8KZHp6_@rqa?X
J+:j=R:j(6kIRhnrI3YuF5L&GD$0P9/+YUqS4<+;Mb?;UD0s^^n%FR(@-#4%(_?$t$gu\7d;
$qEX5(ZR8s;@?M^A(]A&A_l?,Gtmuc5a@u$Y<c?\pi_p6NtZeN8Rs14nKDQ)!:Q#//7RW/k1H
q_S,h9p]AjFX7*jcdpqHWX`_kp%cr]Aqs^+7WPdg9eZq;nEl7'e36^@8KX-$=)QjFL94IhD1AB
sCD,4jAl3/&3)7n3]A+Ll5&+/r^UW6KC91F(A/h%-J\\$NZ/8K$?(oj+Wo4o.>@H,jEacOA*!
>spcr[eRKs_#iorh>TW]AEOp5f+le==PYFF,/5$s998AuIIlG,ag.0TO]Aq`DcXbGD*mFn_(YB
ghSSqb5a?#C'NNUrHL!)6?]A26Ccop.-OcAG>t`nB*hJ-lg-2@&IcIT``lj[&j8b@[iqC(>ok
2hrmfU%"16^$!=NJ.3&N-P5IlI7rM%)I*`OSZJ3)WWJR)B&AA9XnGnuc&DOTjE@:8cUIoppL
YJlXi("Z>aA)PI4j@p()(mjpb78S&E5R^&V:J5AM#955UCno8ecrJ#18-Xhi*L3oQIL]A[t#m
Aqetgmf\;m5+;:Zh2fu$%&ap+iD&JcWI^[d4O&<F5<Y41r.+Y'Vc6$ie"i>ni'`?`5ubXZoq
9_=J?-"o)[uM)6OtrDEpW@[=f_7ZK9a4fT*_pWPuREmrS,<8."foPo4Ru2h)X\l[QrlVPA4/
%#/%E^[=j`a'S!o^KH3I!IZC-hO*Vs?S#u1#3;7`B^mVIf'RoVb(A_sHU/J5rZXngU3WV?!d
bp#8_f>4Z9_0+2SpiF?Bj#'41)]A`LhNm^!+/UAN=$Xm(WB7a30[iiV='kO&p1?=_`jmdmQD[
(h]An14pNP`broBtq-[>C6>i8prY'-Oe%EC4";['t\o'tmE#A@3;\0S0aJFroZ(Wh-^QGCp0a
b'!,:`98SmJoO>L'fF4WUb@dW%Bt,Ai@1t"BA_KY3n)Nq`l-A*,c+p!-CllnNd)Q`ies'b?^
Sm2tVEW?au_0<NQuhD0"h"Xg*6tOf0i"2M5e>Ujs]A43MAWSr7:AZJCu3PY="hE?*Ce5??^s(
pi-s/N!nANa*J*QgjNedBQ4c#<+m/qB9N_W^$BiX"2B/WLN5VKepB%"*0hY@X\=U;dio.D)e
Uh:3QQpZ6<0i&!&+2a/\+>/aDdN/o:4j-a0^(ir_-3TW4J5]AZ.cR_IhF^:oKS-N675;p?AJ8
_llQk(m]AjYdIB]AVfZ73l%kU%Ig1Lpr(bp_6$ASjSmS_4n-r;)onV[^P?'>o1VOm>g5#ZY.b<
'PXX289VHE0`gh'X\C$34l9'(e8m"@F1RK#.<1nku["J0*QesIJU;Vd2J`?Y_#8pmuhFtSlu
[g%!Xq:C:dOjO9WOq9N7:Q<)BqdI8F9Ti`Tci+i&g5.7a&5Y.-pe;urY(9t?;\nCO=M1!X,l
Wh&!lBF;W=N[<7H"K"i>0/j#3e('7RjhfdfCRj`FpL6[qKb(3r4:+[9=+_A"ZL'/BFk^,0KI
c^Lg/6Z\S0r9=]Ak&l_<u[EOjHFX4X"0#.rr_t.$fY6a_3q-_I*i%J<NP?]A\<:,A0A*0PguIn
&JQ'%D%/<4[:"=4L`c*Vc#rKtMVO?@I,1#&'\_$o-X;$5U)UetN]Ad'W/GKBnal7f#Fl*J[Q,
n2@dM&?jL86H[=R2i[4doQm+pduI6E]A2eA!+QMohY890f5V]A#-7PDCV+'LB@ta`Om_Ho@+mq
igo,n*h:k]AGJo=ZIZe)qPN]ADWS+=tY;`PBWXGKj_KdclU=V!9r;skuq&^i!fWI(e@=trdgM4
>7:eCbbB;)+u%>)pZ9p:Ap@+[dqsVc&WPKh.5%__P>LRbm7IM(dZ'Pp;%on*e_@d;VRp01B7
TroWl\jpPU0j\Ab=BZKO=W=-"GXA6cVNOc%_jMoW+Uq2OZ6R=0_1Y58<>tP-/eS`(Ou3AdXb
D+i!s&R5X(aqgmb[$J*=gkoBpc1<J;2Yf;A\UMh%L_B45L2&a]AR=%!NT1;@jrPsam)q-jhL0
n1AV,oQ:FW!qd/]APk`G#^_Y7nh3p]AX\MVdZ=BWZN[en*RV,K+T!;X)l9O]AU8b^U>$='ioQj*
08F&;ud$La4WKMrR4#>`)I3c^-<B\kf%P>+f[Dkp3,$V'f*Y9q:d%W.EuE)ZVV_Ur<J:$SMp
j%9j1AW6=[[;MbBoMY]AiR</'d<#e9$:M.N:,#9U%p7M.]A*Q9Vnia.`nT7&r=VPgQ.[9dUnQI
K5%Yk^<of%K80+R4%X'^bGH^GQT]Af2mb,Q1G[0pMrGlof7eL/QI030Q[i.#>*U?QEDF14QM4
0=^?+#.H[Pf;B-Q;?o8Wmb?TZc(k4mGroBGLLHJm]A=uQfDHuj`]A+B8[[VAFpXmeV7r5YiZ4E
b>O%lY^,BLRjSa3=Bco1"8>_kd>US.UaXsVXG!IfuU'OM^!b]A5DGUNoa2.$*L?(H.;T<6qNR
oqp96Q']A@d;\Ke>V9ij\oraMqtUIRd#nR#J8AGH-N4Hhu,)Bbtcg8hd[2X1p^^K@#.gbeLVa
+H;6dq'gk^VtplW#%j#E_%=[ECq3eDf&[EQ8i<:VB56n=?gYBC5'A+%_'!`-D['6/ZdHl&%G
mPS`UZ);CWjJ+kVNGd,[JT=6Rf6,o4LcGaXq"Xkrf#u(]A$`VSU$Y'?!A%+C/ABo@t,tNZjB"
^=r:k;I_esWnLGP'iHW8h>NPU;%F#c.(RjAja'CI&CM)u2kWmA%JuK\P/TCX=N'%1hW^"8rP
nrW#$,c0o_1>hLe_oINGl#bm.Nu:prGt>lJ53U(;uo5pD$nJ1GG6c$-N*.#d\"mu6t5aIJ^=
AXq#X09Nh*IP"EftXdU/"o1SaY=p70RHeW^e+R?F)U-`ibY@X7P]A]ATs5l>[sii'%E(+ApA++
2]AhFP3"N:!G_&-5n)&eZ)kg`31$>,JfEqN-Ct[0E_j5"aAT6K_3:gj@?OpZG;s'9h\o"rIL[
>Sdl9[(hR%jUn$a(R?9Dbp-rMZ'^I@:LApG?,4=c#Q>"rh4C'CNJ;?&SF&\[Y!-B^#pZ'SF+
]Apd=Ij<0RBHcMJ`g,^i!h9Ab^%:o.ei3tW,EgVo5,HUpAH&g"kq:Xl1=Cot41eu3JLP?76+4
k/CoQl"2Y4S5,HQMCbQ`PUNjWXg]AHcR5BSD((hD]AfO8ZWP&2(;43X>>RfeR-L<4$_udL]AgW5
+-'7D1*jmrJ9"@>N,+j;n4Cms5V$V1+t-ld[OM@YJMQ2[.-;'4AeaC*Vk:Nm8K9)O>4rT8n6
%&7TjeP>0^%[8I#WENZR^.g+0E-^UXD\ql,=ee$X3&Fl;RQb0V(Kk[YAPsD09ZHB7!Zh6V@/
66C&KJ#J%,&HMf4lDNrA8RBT`6,J#hFt8_khS$gasF.b+I,U<L4X>b94<MN>i$cRbYJ9m)[4
Zd4[M8V0*pJCre_Ke7<4Wi8_6<;N"1'IDZa>G.#W:S?TQfqF'YtdNto<e5Q%`X1-lF+XjSQk
"Q160STJR2d?j]AOb&^":r3XL?*,D"pCnPb3DSN2@@jl_/oT//aa#j"Nhl[.=up?]AbM@S25@2
@j'/Dr!TAmlWY4Sc`XE4e(lL3Os>P^-%l,QK,[r7o\M7H8jVgs3H^$^7;\Lj)X9K$B)BDJIn
e:CH)`Z0YmBdtOQ/#"@2pu+((jKYVGkC0l0Sldo*fCD'V'!\s2=X/As3[c4M_/l!XR-aXM>?
tg<rg[;LigWk9`_,`kou=$Do37X:M+V%Z_DU/<@o3U6QSk<MH;(:O_I/#Ar27M35P<)aM5HJ
d+OkX'`m9lI+4gI;>#hdL0^S#V0P2C%B,#1MY([)rgdB`#*2f[o?&l8ho(XYf\d'"u,$%PL`
m\K@jO+K$>8hN8a:@/aZDQ)Mkfrp7!EG?'@t[`92L7qBpbVl&&LZCSK73B*4*F?k=CK*A^+i
J.>WV.j7<V5l)4nB!2u5>U!tri`U;bsp^/X-?:h%',#)tk=5sPAF2'\$K.>h+?*R_I/1f>N)
80eq&4rm^KaLg;)GO7^.i,@(BUMi:;TcR'K"DjJ^og"q]A:u@^B-_eETQjSOI9ejnU*aJ_$,I
r;9AAe3Ee"+VRg&%J7,D9:CW9qR\jfdXrIA+9KkbGqf*lJs`rcMCYVo:sj<1aV]Ag%[JGN1OK
1V)tVmMfC9WZV#s>9T0JFl-Zd*T(47jF;id+eW!O-;W_nnEf/atA^of4h\rOplWd`OHH+m51
-baqS3tGrCVk%QCi@(=DE;cH+HLNNMr,sTS),N=/Nbo;64Ngp&R(o[D(//+FIS+gr%BhD1#e
&XBbVf-9EgC.^#\PS`!oK7..6YW3/0a^W7i$uER\fE3_sEuQ&0b'Fj3C=>qPs<(q4rIDpX#o
(Y^5l?1r(boB1Lg)l^Em?4m)s:M4+BC6-;nbC3.-d(4ZYT#lH*g$3$]ALS-48m07?&hnU$h5,
-hB2rAZKo6XrcKk[YWq$i>&/WI>`VVlT1Fpa'K#NJ\Y6)f6?$d3sr$N:Ge[QbYrrBU8*<'nJ
p,Kl2MI^P$SaY7,e48bpIs4tb9$FduJXc00jd!e=6M9e%H7D)?;SZ[/iAEq@MSc3>A"k(Fc*
4+\<%CK+0<Du)oY;^2%HnU0]A![$%^mb$=t#$L4q]AM)&?*)XeRgSS;XqK."nL8DLfG/d22r4<
*XC4P[k4@sC!/,"ANKH2XeXb7Nh:$r0TO<%%lhe$o-jAsYN_B,IPkFGq@&U^]Ajpr"3(]A3U@b
#7c3ONoZ@+o(F"hPrSAiEhG>C7abZ-q:E5^Wu?7O\0/DA/6Z6C7f$AriHV]A/;,tFm<<HsH)p
8qaJe6DR9Gs7"]AlWZE*N(hh`!XSM(*The<CWrMVNelTR3Xpl7:ba6\oJjDEkhPuEd+3/pI;R
e8s]Ap*ghb@S%HcZ;Zbt#pK.aI(Y?T0CGb#86,Z5]A@eG$GE<A@(O1<GB>okrU&<?cJHcK\ps:
E>,LjBV0]A5'(g.<]A)QI";.a(AYWIm]A/JprM.et`D'ej=nITXcK8>j.KWs<aNT/s4&TdKt'Z@
?3*i(7_'L1)q+XnG"ER4d-DLgE!`"FN4U,J@$h\N/6H\(<"!8c2k%C_d4AJM&>Pd?(6($@Vf
"BFZAHbf;'9%KtINXB]A+m&mGlkX9cDEKf'kG'$`Hg".0"h*(LX.-IIY7sKm(T^.R16L+6l`s
m4X-n,:n[QsgfOqllG2Zu!Z#%e-geub]A93*bKrY:H!cLJm.G:>`'O2k9"_rXA)E;r!sO4O^@
Nr3@qM/2OqC4Yd,-5u6GB#1bWb;<q4kN*Z`LH&^U[V[<a\>hp4BDZ@@XK+MW9V6D02UETkFO
CIP$lFh6LT"Y]A)S+k6M00B9dmc01r3%i53,pH@^[DM+<@Fl@QJ*6Jb]Ad/qVN[m?2;INFV@kf
1I5-]Ao3O9Jn9!%MtA>lbAoEW0lZBMUTOIC2@a<3H.*mK3VYc+&A["L:j77]ALUBGg>W%`lAQ,
FUs]AtP-[n!l*&4ihbJ)*)(TrGI6Bg\Q:86")QGsNq@+W>ZJ"B\O-uqUPc_8\C/,/]A@@27XA:
-qN^qU4cCho*q;L?']AW(:9%4UJQA@+eBIPtMdnm9i'>,$GEiRBVR@l^>5k=/]A?O^E+d3lW8l
:bbhj3QPL<A%QJt4;fSSZ]AP:uDDP+HF4Ub68jSMX&Z%"`*j1*5r.X']AM&%#uCbijpf<6X4ri
g5tGTQCi"Zs^hh=9fm$m=jK%26PDilJefu/7-TBS!o_hf#L5*X_4c_dP#6k%4E$;nt15<3;5
#'!9YfBK8T03+XKNl4)i82=K#MJR_0AVZ2#-PUB>Erg7#5CRaOOu50#rX+>V*P<lWZ<GEmIS
+aMBfEZq-h,DqWmelGidZ5NWchU)G*_;>%?s)bk0J/#H$^if(]A]Am0@roGI\Ta4*[e.OJ*\r>
)LN73?bJ'`Ekme)KfGde3cl.J?d[$e\+Yma!O/*$,(n@nQ)n=_nQQZgcMD@sE\5_hmRYds<?
K9_#QHS@-nHVRFrqIsmVi!"$`>.n\bgHX>.T1.YmhO7G!$F@i/qlmX3CS.Ck&QqajoDnE@Tg
/Mj)-_ePX92^-dbP_,Ga4CTn9_Cr<Z5r'cUaqlM]A^lhnJ[_=G@i*V$0;uG3$ijkq&>L7KQH.
E8_f=,]AAfCLA\/OVO/`n!`'WUs**0Ju?0mM5PRna+7EnW?@71h:VN9upG-8ZSA;_Xm5_[khH
=?^G+?'cICE"\^)beW/oLX74Tk;O.aPLku>hmkC+O^&iH4bONo\)QI2U[hE?*4IV/%&G&XHa
8W$#_$6$C(p8L=W0eaa5YC2<424lNB!arDgaM[b"Vo0Qa&6:6c,LIaur#`rQnFM9LYoL>i(1
IF%`5rPe0[@;<`H`.bONhnLCMM1<,ClpEF\"T<O,!(GEL1)nc#u$3TK`H2<6TaTLVf/m1W3V
chK*aoT+Ur=tm7$_^oK'<J*oG8TuAh&0D1FgH]A=Ii2m3J7/K`c[d@Dq10:Qb\3eVC0lM[9)<
3Ia?%d:QCcSN42:Qs:;!omE9o6HS9c\o-.9,h4Iprk`,A]AqlJ/FML:j&.kF0E?I[k=AG2fsQ
Y,$aZXV->nFUcg";;20;&RKM!nuM"+e3Lf,$q@@qqAkC!]An(O6&d8#H&6\QE(fj7Ef6gTaQ7
24irr[7?lFaO#lh_7LhCg'2F'R01@D+]A"a6bUuL[?=g,1")MA!3%t+[U70HF3'@<BDMln$.I
gB$s0FMf$&]AR2\`$ceKA,*5.nlK4L^fk^7ZY]AJFg%$)_eF0QL`Mh+Qd,iW[VAd.%W>h1oC&k
^49?`sabLds`c!rBdi4l[e%H?n$#RA*?Wc$:J;'RQeOU/Top72;:=D9B]AtK"8d@Q]A"KLG<m>
aTAh9K$2l2EQXoL/\5fJOjnGR9a\u.cm4btu_%LE7P>k+!]A@D@Yb1.O<@8"-'24WBqM>j\$,
-fZnj`nM5,=Pec8VQ'nagZaH[5']AePY'r$0L6V`AE;Q#g9oGZQT=-+SM-]A#A`m)5Ue7LU_l>
Fo8OpLjrj2#ZhDTI(+:/%\7]A]ANg]A]AX5,Y*#L]A0pldsId7SW4OAa*=Y4:AM6=r2A+)>FP/NV?
REI,7V#;-CQF!>p\`&Y'D*GqJYlG!5(d3`/;,0qgim]A(P-bH9HD,`o)GChWfl?UrGua8#.WM
BCsl0j7,iWA-'a*,Z32[$966ZA9P:eC<Y<pXQcs]A'f(_,,a9tY0(iL:$BiT6mbOO-\Uj:Sr$
pP#FV#,>Tdc.j"^,]AnWcSD.GDn^eP'tLM/PO^1(I<sA.sKF;_l8"gTFoMDKFU4QQEMCRGqI%
L6`]A/Hpo6/M5?^:R80ig]As!pjoXoH95$EmdAf2.!2+]A5@"n=74!eV\^1(l/?W$BiR=)cm?%<
,6m,jCk2iY6lU+#?a`E2^Xee.*_CgMQMtft)II.n5`MRmiZMPSCrSh*tOJm;tW)gR#tPoAb*
Q@qX)482n4O`n4-R%aE>!>W..,h,c$_iTkTuoN>[;G!@\7881jq8O2YJ%D=s/YSej/W(5E;0
A8`,lo*=4T?YQXlsfSMm4%<khf\?&@9#:H2W$fn:1(c0YUNCV5>hTEl7Cbo5V;VXk$.1ED[G
YSiIVq:H*#<^^<QgA;gZ`b_tNRICpq*\W,eG$G_?'@S6YD[7hR*>2OUA(1pC%gT?P,o*C?j!
J#I\)+H`eDf7e5;-7e.'FS_#0+Oc%J4qF0t4rcuqjqCkE0!/ug6iPa7J=a%G<'_Wl*Upt\!F
<&<#lpTgL-dMdlJIfM"HoLg4Z*@/&_Ydm:@?L-!%&4j?#gRc8.)\cjo"m(<]AFNI?5iTua*&o
BQu*I!%&CpYf/4=V4=JarCIWGB"i2^O_%jB;[0Uah5B+s=#9qVP5.>KKDJKEF/?p^r=c4]AZ0
FWrT!;%@=;s:F$:^J;DSE?2-Z-WSY"Wg"U;Rt,nS0=)VKAVt%,Im2q+&5XQ]AXU)[c;E4d7#+
;(EM@D>`A?hmJR:1\1bHQsY?F$()>.%j3;Poo0S+,\Ug76Y>NGq[.uq"O-p9XJ,NN/.Rk+W;
'RN6J\ZNE(L/E@THGi+sHaDOaCFGt1PWt>A-YeU"CJ(^.j-AOn7H-ib;ED^oga)s,OKV02?B
_9j,>oQjkds15"p(G.bf:</JL+JWXDlo+(Je,GIPM7IfZg\o'PY@T2lRtO;OB/eXf0Qp5'.!
sX(Dk^!a!HZV?:OeQ/>;eaOC."%UJ,24;U@l8,V4jq)f>]A)Z(E\9GN%'=E(ae3_c\;eDB:uF
;;P^Rea;50b-aH:hSY\@V,rH>?8mp,=%Ufk5=$9F'#2-`SaSkMjM+jgc94p/i`rar9XM>)X2
R)Z?YZUgKj?ImVi#BI@hV2QR=Wa=g),!L(?,pO$-L9F[QZKOk8.u.,^LV"0i9C#P2..nF%RR
+h.gJnK8?D\be:YDq*g!N[qNA:&[s<A[cYDAi.=Gk0YO)-?]A1]AO<.Aos!L8!TLB\lm1P^F.)
"do8CSqg\AaP>/ZE1N9@-`oILrR<kjMjlVZa3M0<r2P0[IV6A0bm@qgDDX5+$%O]AZbVr_Qid
)eG/X9b\WNdHX*obpGc)kJD[kO^$B9=:HDS)U(2U8U@<eEVaV)4V<s+S)a9m^PAjn1ct^l=$
=_)pIl;5()Odh?E'Nenc_nD2K[pq@f:E.#bC;pH[(Nq'?((&c]A01#G/ARMj#km*POUSlD(=I
C#^2a!?I0?IdH7]A<Ml\\h%++s/..$(k<gto0./A+rALK&&m>l:Ca)7:EF58gB_?">n$IDA/S
?SsW9P!(U+?+K;oJZ@E"2.r,cXphmaCY>Xcr:!?Yn2q1.Co/RX58G>"Eh3'@P/uUrCe6N7O_
E2>b@VukZ'IMRjBqeNr#MF<*C9_>[Em`S[J:cYNBZ?cF,4`S"Dqt?kMQ5Z=-p[]A,b'Z,Uc1/
!`OfJ(f)OIXj_m-PNu`ZJa+`HYda<!\cC^Ll"+KXWnolo_]AFS:p&]AQ%Oc[Ro,F2dC7Up-)[D
,n_N&?8dR_p0o^jY?$QoCe3Piqo!"-tSLGm9mA?hpq:*g\8u]A(`;p!GBP"<p3,o#lGK+;1ZD
9P,H4#6:I04E^GP,Ol5Yos5Sd8+G/\m8-<Q0;ij*\pVfeT6/s\_"5mh&l_tdQp2G,I1n"Tup
m@Q'$F8qZF(O;+3iMYCfL7'"@D.$Xh-_JeN#dkfIYn"$@'5R#]A[tJ,chYbD.!apd%1BHNNk+
cc)LV'Q>b+p=>MFro/l"MG5Oe^IqAn$7,5kB2I%da&=:N<>dC#eKI%'dXR'B9t4&VVo*GcIi
<EMAIL>&$-")Sn4GF9j\2mSH[+`I,K@dnc@jG4(5IXNOf,,9!*-;HBhj<Panc,!I9EsW
mMUG,OqGTB;m7pHIoRH\%AI^RuIZMhU1$QAa5/Gj?l$7mNoK[d'=12hXQY;Jl^EUa6;PU$-F
C`]A%`L?66?b1.]AMWag@JSX&>*6[i3RcQ(XmK6NdX(W$c-Rd*Tu7Z#1JMs7#=UN]A!Rt]A?1h$-
ZusoQ"Dh8PZ0MXZ98Ijr>>Es9M'hep?`tBBR.JJ"_#KF#@.0a5o?^IdI%<3ueqKTV0b!hIjN
g`uls:J]Ai*E'?CiCqZ*BP,+HDt6:KR'YkTRN)?;8&>D%p0h5'@Opr85m'i'+V@7GbTNR+1rK
:9lh5qZUM07*P$PklZ:A(MU/]A7<-FZ;+#N1BkCk,_V'e3,F?Bh%/kshV"o%s.g1`)RQCJeI>
tVoGiV,"9+7D+&I!1AN.4/MF&kSPa:tg'eCNaNV(P[<%]A%\&HSNAKqjXFFg48JA3D[1-u,Bc
hIZ.!TA>morWDF(1hGL$S$Un<WAiR1`V<DR%9BBSX]A)?@oX5CDQu?<$nS9l7lk`Y`4/G<MQ'
XJ#-Bn6spC4?0Ndbtm&sUYd]AUFV@Y7c1jH1H;f4F4&-.I6%]A`QG@Ot=L;3m_!WH:XY"P#ks6
Z;D8IFk!K;Irs>O)o!#,5.;*0p*#Ze`U#73l>0@5uAa\p`Tehg?\Jn',(Y^8Tjcd7Tt8+dh(
O#GHDr?:uuCCJp7!H[X0?"SPA6E.qS"5J4A\)fJWD\TmGNr8MmJM-bPg1jB=:"]ArW3R.n&Ej
f:jKY3?T6`$/9e#]AA<2N<eWZ?EDYuW4),Lf4$3smP"8e^C%,,;1m+bSmDoP7`m*4(pncaR"i
&O?*\ga8.W<j0Z1#k39V#H)Q?t\E,eL%43I5[_gYp*)NE#5>S(Qd;b5%E>JZ25'D%E]A^'%[Q
38^2..>N8m%/I7SR4NS2pJ!ZEl_V@BB\-7E?qHiU)logBQpA_3l1XmGLBn3pi^7X=fCr:O=Q
7'G)et!urBE:sq2MoW(@F5J1#u`Uj#4E^HS\GHZ@"ilN^A&>LidUPiEMVtl)%&AAL!7<s!4Y
-B\m1\\aka&7L(qiS0:O*gKHrA<C2<7_Tn1i-5)GZi8JsXHD+^!FfQ53*mfT+r>`6(=aq7a+
#c5?0m]AB@1!%;Oj5b"kJR!ubQla19NO`[K)(t7D4I1fWraCAE,n[iF>#X?rnfCL$amMt6`SY
hj95Rh:E^<UdGLr"Tqj)k_[rT\a6Vd"ENGFmCjl4i>:#NEi.c:V<">6oU=hFR8+25GS]Ac#2X
eF#Y<\WQ6alDhMpA'OO3c!1C[-D2H^XW3G`l=um&+ca8tnp,:a%+DZX2Hb<%=`O7n'i(m&$a
3*mR'1ZAO>3Ks>uCOf6h(a7BL3-+@Fq.Z?0]Am8-<!Ers(DNta_rdl^qKb^f&rJljL2>jRu4W
Q&!irX\LWHsf%OVhYj7*.Ih@H@[`$Sh4T:90b+9m8@]A:P]AeCjh-SLi27:6*4fa"WR\/EItF#
ZLU:3gKbKifXrUQXf"=4gC39Wt6hG4:k?f_3rg"Zh'HLIj3"BL^XWoJSpTV/J=AV.h5Tu7.-
e,#cG`_YFPCKrT]A:j\^r*9-o^0K/LM4;\NN#t/S<'[&\aZE0?UtD,=ajqg0!an%jqnUe3M.m
6sS\49n_>SZ*3hl9TBtAKq1M*k%#!h]AOW*#U5Wb3(MA?3^f-)PdTWe9WrL-g$5Tta\eZ:0E9
,l0p;?^Q5,B8t6DQ'#L$tmqEmH(ZMhiod<mbKdG$[1:E]A$+-@oKf6"@SskhESA2)E>0&DgrO
T:CPqJ>0[/H8:Rp-E@#>H1V"uYSj\f(=Q?\EC#S=Q,f%2_k,_Wk;0\nBI$J'NZUr,&:&%V8#
X3h:o.HFaT[(#Y`j^Q+QZ8,[^E;E-f7LL-O?D.C2Q#97ec$,5Tu9/YC#EbG03\`;6VL/ZJ)S
W,A2iE!Pf6tKqgn1"7g-2eP2N4N^L>dA;%q_=>LHFF`_3\h0MVOW(o1\.,b7nIJLB-7m4+t3
4!g'F^=POlKk'2d%M:(G.SnXgk$s<<q3F:X/*kW,s&_QlR*10^O$5".qXPh]A>,:uMf_L+I8f
K1&77eiFmi\P1($=C;&RbdVF@E2Ir/8&`cTZ'm@]A&qL8b_+)rGS=<"hHa;]A"07HZ7O0NV2Wf
.,Nd6(\k_98jqh8W\E`qc&4-Zu">B5J*3fqXVi.R.Dj?ADL'6uTK8JK*K4%$*s43OS277M$?
q7q>kpfK4d>8=/A`W9l00*Sp#*/MG_@t+PNq1bia6J=g%O">#`lA:&3RemWVlfqQSpEM/ZCI
YS(O<)G7#/+gh5`UK4!Hj:<orDMK0*&9_mg+Q12C7"n5t)tbTR#'%QH4(E^M,NO>rs<8&qds
ejj\ShZOF1QLJ(8>"8;%o5.Ni&JiWMB2\F/o'Z\"p$0Ip:EVTk3Ho>R2Mr"!b,c4k4LVQ2mJ
4k1K_SVs*rQ_`94LZgHCHVG<h(gK'Og<mh#BA,4OuBC%"^H;qtILO<3j:Fmk$Nq+<DHMpfF^
fP;*2.ou<,EAZUNh8+LMUB@`S)l1J^drE3J!k-l6[n\>$f83R0GY73)HT#Z`KQ/R@7r(sBPc
:hCLKaT4'pK_>2(CsCLO`)ZcLSSf:Z0N'+9Ghg=1N"q."sZ5b47@Lm4Pi[I"uHOh.!\N9iC<
sj0$Df?i=8V_h_(73"@/s#cY46IrW`::OYri^'`7+b^=bIBB&DsEIC!kcc+_jiqe"aPRshV]A
p<C>)2u@4Er#_?/AWMQ.J-(#,KA78CE('C"j`e_b9YeCQIQ7\$TV#Rl.0)OuntUd96!7WH\O
8([QkM;]A^[3,OJ(Xa/qhOP\[XZs0n&'a,6Tg%8qIWDK#5k<kVQH;2"oA#o!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="386" width="375" height="156"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="report3"/>
<Widget widgetName="report0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="true" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="cardIndex"/>
<WidgetID widgetID="834ea768-5a17-4190-9880-8463e7405bf6"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx4"/>
<WidgetID widgetID="d4ed5807-5003-4fb5-8ccf-40d9a6d6ff29"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBID" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb_right]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[grkhs_20240527090420]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="zbsx2"/>
<WidgetID widgetID="fb849881-b9c8-4ff4-b9c0-076208fe0ba8"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName="comboBox0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="AREA_ID" viName="MODNAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_zb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_zb,Key:AREA_ID}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="n_c"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mFZh\'5&<#nT/TpTF?\0X`O[M#Z6?o#\=2JikW!dQe<o/S)nc&jpHaC!Kg!!ELXUt<\n'9Op
m(5es%!8YdBW/A=KX4%&(<L-&@1GQ*05FJ_T,N2Rec"qen4(-;Aj5IIb).e*$^UEo.44=Ba?
\+:)Gu@Ks%K`4U!d9euToUl@fa7$-?=X8r:0;m0rVII]AD?aSC127r5^4Nr*D%,]A8k=,t>k\<
V9@=;V_MPQlM93@!rin-;U2%AOFb<dDucg,dW/9c\TE7=/k7hY>VX>P"4h8-kH:k4Xm`K13f
%-`AE8DnVs_d.EXXeAE+D)7]A$O@UujD#q8TqARYi]ALkX`L*%_^Zbd<84^m)!@JLZ;1L@>?-\
?_CKCA?f>4?.a\;krO,Nm7Hb=mS;A"Dp\m#_=j!W2-O#33+L@Q=eK0fJ=N)f:`aIuP;BpP<H
ZcF08d?S>DQPsR5/KD1("tklSj=PDN1*/>1-H.?V[jop'Hu:ALpK@rV_rS(c`U*a*b@*kAHh
#(8Pr&A.+ls8SG<!Qp6p#bLX+6]A+pqq_o<(+Z\#LPkapF:5+#;+]A6u]AHP%)rj+91ipdG+b<5
4-8oro:aj#u--=VW%M9prt'D-?iLjMGgZpk,HaFJsD+>6PF7Y]An2pdqfrWnlqj"Gl:fNLDrX
j:5nWiLKpYX(l;knU,?1i7YYlD#*O9.F<QHG@2l?&j[mqF$Tu164>(19SJ)%1fr;VkBQ/$J\
3A2ac;^$SEQUDWcbI.a.Q650HoQYN-mJW@=CMNfqp!ZKG$.BO!9)XhD:51,tFQi$1h8I/5S_
G/'rj%C@3Nb#7nHH?<$N'*O`iQ5+m'BL&RqS=XN=&V9VZ")SLH(3%c>7kTYC"JJdjJ@Xf5*m
=:X80-C1">,IA#]A$P'_d$^$l30\#F>^r>#<:n\);tU;cpb6`4X1PK`;F1Qb"]AaOJWb5t?&8.
jm9tS?(21>u^BZL9-Ec@\%qHU@l@IRGE?*hSfM1Lg:.58oeFAG5q8)L.bEUK'K,X5AmI-/*(
rRFu7tkhpUkJN<PS'm2GuAqX/\-^4sQB^K[pGo,KZSP-Tk6\9Rdeg!Q2@CTbZIqk%1C_:ARq
:3P2KrO%a`D;s`#otFXGD#@0"OkS;Vp^[=e>oWCthbco3`Ll2#r!"YSIpOb?;2m5P#:F!S03
R=N$2Hbt!/Dp(5/t?_&CZP6LhY'a+;WDaUQC*Jj<+[fa?^;VOFnP6+9spJ5n7psKA?<=1=E,
7Zk;UmLZA]AA_mU[e(>`+4-)tDTd>E,p$G_/m!sT/N0OV\H!<WN7(bf@5J,t!$fa`H<F(<\h+
j,cq+k:)7Or&qfa=&EF#?&elp)Rk6M9S\$^jlFf"q2>&@).9n!sT/N0OV\H4]AC\ch8ag>F2t
KJ%JQOYhgoWE@).9n!sT/N0OV\H!<`&q@-,H?gK7A>[egR#8$'Lf!7?q-m,%d~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="FILTOP"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_dzbcx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb·" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_left" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="494ca45a-42ad-4308-8a13-7a8b5e8760ac"/>
</TemplateIdAttMark>
</Form>
