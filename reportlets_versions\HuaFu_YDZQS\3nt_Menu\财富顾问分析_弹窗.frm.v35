<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in('cfgwfx_gwfxsy_sjyl') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, RQ2 AS (
		SELECT  
		TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR,MJYR 
		FROM (
			SELECT 
			MAX(JYR) JYR,
			${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} MJYR 
			FROM TXTJYR
			WHERE JYR<=(SELECT JYR FROM RQ)
			GROUP BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))}
			ORDER BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} DESC  
		) M
		WHERE ROWNUM<=6
) 
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ2 ON A.DS=RQ2.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   AND A.TREE_LEVEL='${level}' AND A.BRANCH_NO='${pany}'
) 
SELECT 
case when DATA.JYR is null then TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy') else substr(DATA.JYR,1,4) end jyr,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
--ORDER BY case when DATA.JYR is null then TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') else DATA.JYR end
order by TAB.XH]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d4100abc-37e9-4c25-b085-ccba13704c88"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.14"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1837898,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="JYR"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>("+H3+")</font>"]]></Content>
</Present>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="微软雅黑 Light" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9=:(;cgTN[T4mnS?2ia/(G=GW$"GE66/qNTLD+3TE:hn+bV:t!:^^k"bCE!dQgc"6t@>bKK
&so0g^O9&0b%>J0Y[3&Mi/U`.!1$Y'&@05OVeOOWXH_kLjM:GJD&em^7*,"PdnfWi`>OeC7M
@3IKa\27;NWj_s8T^ltMDc?F#<elkQRloc(LQt6'L4mPM-)Q[M;f<>,Fe#L+S$/7'M)Lm7rp
PWcN`,>SB4Qu(\="DVa3,kTubBM4"+7S%9I^e9dn!8)trj]AO[h+A@@X1s%kn$Vl`.?a:e$I.
d-:+eEu1c"m8:70KDB:QiYhrir=gX93;P"AdJ_X*C7<fGS#MIrAf"gF67lqRho.;<0a<"7)o
1=Mk2q(;)X%g5nDi^FhI`5)>gi_.&'kC."WUC2,iIK[[Z#k^obOf`]Al'qq&=JFPe8`J<*PD[
'OD(7G6@5=3peY^cQE=>;kSPhWkW+7def?B+'Vf!GNH[C/2QlsedHTu?uiPp:t(]AnPVPBC-R
@JDgUo9t\a*YLf[92#A@&MRUYDf%<nZVRWb(T+6b8H7cI(`l,uYQm]A,=WJOfJ@pj0"bLH1Bh
^J<2R^_e6S]AmLk)GY.1jBL;eAA!^#o3KTVF>lK$G=;Icg,*u_l^6%@=0)pX/B$F^kZ?ls-I+
`XfV8!0hXTsep^U'NXnj87qfL;tQ^ANN$nUA7ftIC"*=Z(t23h\*OABQd-0N.]AfAtFLq2L+t
$@DJRW&sq='k8C'2_Ih<(4rr=HB>X$J'B1b2;.!,S%T"6g90$YISao;'!SseV%1e_SP*>?L!
Kpn8:Xuirag(NjWIOeoiTgKB_a.-3i1@pZ[Nk1Y?"n4s8EVtU!jI`dPM-</jgj%<r>nDFebo
eD@gfG""cdN5[qh>Y06.ZfX5oQHb<PU>\MGbQ[Y$.A>2;A4Tng]APk!k1FF@,fg#5o!-c*gX4
h<5(qtGP6HT+>]A=>*]AoP3:q\+@tDNUugm7HSMaM'+l`@RDc:saSBCspX)/mPq4DhL<K:b:l,
dM1B&M!mWo+R.$(+tQf%.s/:i!g1HNn2>C&cpi2OQRiO%B+gcJWDotg`2,e:uY8g,r3JD9=E
Cq0kD;Qk(0Olq>cj"'PE3ZM9kS)nDIXOS&H9p'#j@BZhNVP3n]A;f<hZOFGuTZm2@`>h)YNPC
Q^`\IluAer3rm7RAs\&YCD>No(UO;#GW>_siIdTKPr[Ec:DlL*/EYbVVAP*cu:N3c#EN9dH0
2\RV$ODKC9#hEGTJYo%6__)#n6nq;k=\KWN7&uZ1I:s@"&H+0i(mL!poo*F$]A2i>M_R\B7g5
m%_:O([3=JrP2;1S"AtV_&9l,!#*`pU@uF,!'H1]A+d6!RaidI>-/"\q=OrV+`f*V0Sd%t6_<
#UF)mhj9oL6F:H.mR.!^FL7DcarqnsnOCZ![+:7`@JCl8Q0c#S>k!d`0uf28.VKp[,aQK79B
ZKMIAFhH=-+*sPIk`;00"TDru^ON%^+.(GC-&T<Ol-iVkl!n,DV`XVEDE"(G=k]At-,o1$Yfg
Kmc4:NCCb:Y&-g!uc)-9[rQ\]AVX'#;Tl,Yl#;Ndml.]ALIYD7biPs<F;N(d`O8"U(+++NA:AY
KnKM_KmP-PCG:=1?1)Vbr/+\SD>HM1Z8e_JfOb8sceMVmj5/o\JGmGl:Ra6S^IA-;AKFU,!B
\bf<%@3NU[V"22-U.kR\Y<uOo?bl9oYB!soLN:1k.>]A-%!6P3j`%;1K#6)"StYI#kA-.1M91
SKgTaqNXN+O>>On/Kd(_j$q/^R'P8o?'bDQ"tMnqE#@oEgB3;IUO,F4OOVqV6,[4+#QadMlN
TiH;8&W%S7VjQ3J,eZC^gHAK/;u%ogDlCCL@7'5p3[]ARNA*1pi"a,R1isD@6VpL(?nSu[AAt
Q#f0<focJ"'kjYe)M>[_"cK;t8;J+2r5<fH<R]AI]A2%:UQt'J%5&)C5L6YjZI1ekK1?6OY(uH
co#lp:Z3)Ce^UQNI'B_@mAF<hte$aPP5m9s"L8K^MSl'Xc?4fjc:YY5,[S"?-akBA2mBE/XR
*GT>^G3)6=Vi]A&.=ukuZB6)5<LZ"h%'"Z':*?%Ofkc@qgo;+@Xba`_J2oK/NC*J!'@REP*BK
eG*$f:306j!)5*0h1(0\m3UQ'P-6/,1N6"Y"r(G;R`O@,)b3d9?i89K(dWu0!jPF:['PhKSC
Fa!L=EmENTF.\3[eAlN-C%ai7Uct;smEuY.:XI(2T'61QF]A$ZaPZ_`odH"80im<K_Jq<Nq6N
t]A.;h:*X#jeui1r:Ih#r>^Nh(0o(MCL(B5`d=]AG41<NoSPddU:0XhFnVH6<C/DOTU6>[Umi4
KA?_,-;N*%$Qa5Pr;(b;,(aJ9T3=UHpTtuP9`8D"LQf/3:X#R7^I;B%;)<JeO^rfG;[%\<hq
tBn]AilkHg;tS:i><XRTD?i8!2.9,A8e4j;k'HUS$._IC4(jmUI%J=TF>D.14La7WqV-'6ApT
T/a2K&gJ'q7H10hiQZ]AA:h-gO/FP>\,$F.%WAq5RJ)!I1@YaJe<6*@k_j/"5$1<@aD-QMdN1
,I<jG:^'?EEqL:=I/,;;R+%IcmYsqaT]Aeo1V3t>Y-aD#sm1GJpf@=6`'onDlpI3#2Gn5_YIH
6ihiCX"_$e_XT2g/&7_.@[cX9m[D+r*#kjU/mf6oEY-&PW<OrBp,6gYqK<DPY%.gIX@RS\te
B7_!sp'4V]Ac"$/JSfn*j11Gn"(CWrpVS1-(9Z'Z5dNJj*tJnMJb0+hjSJGFW_X>tJrL9Uqen
#]AVUouSm=cNZ-\BAN5)asafBne.Yh[c)@$Dcb1WkZ%%qIj)1eJL"QR"?%&kBOu!5$C">@P]A]A
$UAEe$lk9\,UYSuRPArIa&d8X6*[+U)iGbC0IT=rT:L<Rl%CEX#Q!u6VAAg+Ct/:LXnf[SjX
-f8ZBNpL[t$L"\emO^uS8gY>80<K?H9mgOsiH[1"OqeSAd"[MD4%IZ;Ys0WM^Z#Br[$L!`"Y
rHrdF9lWY7o99A><[>H+c^'*nncN>ZH9or^i!RCYm4TM@S_"4&LT9O+(NF=3Y9]AbFVqCR-h5
nio#>fa&acJcL*N>rq4<LSfC:6a$m;f3uDXj&5SPM)NG:/5+$cc7a3u%rVU"%Hc`dX#2GOn,
Hl`epJ(>^.4#AF[p`RI83@#f0+[`k%J)E2fa25[-St/ah\j;a-dlHem"2G+&,YFAO8*8%4T\
bOCli0/O*!jrq<55rD@^RG&;X_HP$33d'NB1$4E,VuYZ,eP;8S"njNcB6]AVi/U)F4I[hkY-a
,j`s9HCO?a3o+3Lm[KAUnepu>1P>Qk_gu<nlCdlO.:d:"+_B:R`s#BDGLcH2'88?V2U?iS:5
Wp$cCT=oV_7u6+o_MA4L/@OYEOhoK<BU(5&T:q[(fW_G(duR#FhGF$MG(G;eeK85kD,Q=oai
c%0+WYs%O+$1&m;5l4*^'L\-WWjDCpS@NKbpK[G=05-:ckM&\Nkngr1tJs!X7ETZhH-&\jY8
03uETat%7A_K:O&\p>cs"g.j(9g"_A;Y^G)`m)qA(7l.F,g+kEnr#lN5'5XDEBnO)!m.Bc&M
BM!h(c'it,jTrn1%6e!$DjhOWgscd\M%4@4N`!^fZ,Q"$(Rl^3JE+l=)ZP91'S@nj\:'?sB8
20%0fBk\_>2eP;/,*+0-U'M:[%*%,qj0o`5<$hP9_G2rfd(=92Z&$eY?5-u8hFVT,/WPY%Lq
XgQ*HDB]AEc0VD%o3mq7<&B4`loaPO`t_)j7Nf/(ISpNFk.-jQD/DG+)e$6`'9E5M!&HRe-Pj
r]A;\L_idNDGn<OAD)(25p#/'>PiJT]A7f;K&%r>cs9eV6R8[l4jUPu!4`qCp6P;Ho2hJ<o.E)
&g'W/#D8+@TN50b0MZ<m'U@RMr[edKG\Eh\Qf&\*/:PcLMo'D.G+a`/?[@N4loN2J8ITSd%r
NfU@WRO8X<=.*m!aP7*\5C^K'\eAs*o/C1otl9.l=7pTCoq)4$+rqf?`KH&h(FF`S:+$%bL@
I+H5h?:?"ZX:`saBP7JG\o\9EQ2m9#dbi!uXo.,3#IhC]As85,ZPe0r"EH4d'`2=W)XD"lQfD
/#W/3<lp%%Hhp,::uAT[Mm*:`(<(R<0[`D>'lY&mNOL$Np&lWZIe]A$L%:K.,J#gGq!iMF<EA
X)m-lCFsOZM":8cP"``$6ef4%'i[YeKE-6X.M*jH/$9j0oa$n&D%hJMZ[c*H/D8:+N+',8u[
@eE.D_3on#_q[T%A#[Y<dro!'C<,p<kMW5?`pj[@d2`*Y[2?)/s).EW<":SWSEsSiXqqD9MW
!RcPFlb++H>"[2(n:?.>SVOj*@`HN81(:'o$%s*[WHNH.'`2XWfY)5e4OY7pV9Z%7Q(\G(;"
`.?YqWKX_gkc_/H3assLmF[=gF&1.%*^pC1oo,]AjmAh\u%X'C&DC42PDRsZqC1p#Af'Le>f3
\S'd--5+`HS/-NHCB^,#9=)?Abs.qm\j64j=HVD@/<JcPgj0m6J`BcTJPKP`fZm^4Zh;L4a[
h,Cht/S%"*;_n"-+GfZa$Ci8<f#Lb4MrBme'bi;QCUNX=<#<gGXeIB8N)oDn)q`O-"MMPn.[
]AKC7KhQR>'-nsdXn^RjcQ=;UiA:N1IiitZ[E7,X\6Idfi]Ac+&CYb`?j]AK'1bkFPEL\$G]A,]AR
aQ_&rSY>No+:8m/YS3XP7^:_@R_g=d2mgr#JuA3J*=\&`0q0qXd>a4cO3m#J^@>GJ&:;WGeQ
>__Q6`+`WlmIOJ*>KI*XTTd+#'SLo,R[Ig%,Tc="NGFno@30`j+)aJ1SYu)$[`ZaV2Dh^H;j
!VW)*Q_?h;h[%F1;r8^cl/KPo[u[W#!>:@@mq=&Go?#E2/G'5Ir\k<P3.%p>&0r,m2):fr@N
20a4(T1&\bJZ[ZUt2jn]AZ@4H_Q$Y$GhBZL&7bQVJJggfSrA/M_P`%/S=Odd6F]AA;;_DCJ=JX
l+"Yk'a%qjYu#"fg5X`n\3h8RNHHW^@S5?a?##,7`9HuLB/qNl]A+&/,.uR,]Ak7Q2EoqaN\?:
sH%<"-e5E#Y7-bj2]A\S$mrV@7<O(8)ko-Pn<hZIskRd`43L%W-rDc7hiiF"\64(JmU#+$/i\
"jtEg"%d`c6qP9C7k5#9j/jm[E0,4\'Kp"o+Y`:I4Ub(OXQ\f3Fg!^l%gqcpWA?+l]AcBi=PY
X+HO,4+GK!FK*.:a[(n2*QPKcj(/lR#gDj-tgN2.<+@joN_>S@,]A!7OTq`07:V(YP#9?,pt#
W/DUI0m]A[>1N6p\QB%*%pENP?Vh!]AnfDS]AW(@`#!*4D_7MN!j=CLem<u)JHqI@)K>0$8LqYB
^JhKoSF@t5'J>um+k$'m?g\%/l#P/;mPfc[&s=4_1u8rS5s##EYS1:6au*>@o2X<Y\oEGq"m
1ro3-F8gWsL_q:-.VZot9Boo"@m#*!"hRjV*);b5M&"'B-l^gA<2Qd<@%A#@XGjFWPTl(s;j
pNKKS,i5%kI*1bk>.07s["-X,/+[Va7.jWVdVpj"i\jA0D4qFR)Dq$Y2Em3RlCN_kqr/b/0G
@uGXnZ'ROW!#2#^0(t+P$n%&4Pe0qTCY]A""g#WSm.YZMHlO9pGn_AO3_+:!1^pq[b]A7J>PF)
Q4;TXJI?3JgASk&V.HHBL`(&l+\jbT[@SBO#g"lK2'f(Z(]An_F#*-?,eD'jR:hC+(M48#A2X
tR='CN\o&0c-6^Bn6jS5njiZ]AT^70k7X?5aj$l5]Ae5Hre3fbPBk%YVAS_B[s+[+k[DLB%;n&
shcZtT+UWCmXJ(g)B#)N"a?l`sC%Z#dKeLB9-B5>Zndb.l0Rjt,N;r9c03&?NP[PQUFKl[k7
IX=kr(Gnr;TRq.2Bd,:A=cWWmNKG:Cb*K=M\klF`R:5Ek@C0tI%f+'*p"O[L1nD+Hae71U;M
]A\>=C3`(Yc9oIA7ooj,<ukrJ)>Q\/3C>RTf0m\e"EQ1>\T+i,^=DlTgFpjN'Jp%.B-k1SIKn
QI/TbWX^U(]Ao,%$trNnsUr+Z_7'<`QmG"r'3ZVI4S``RXFHU74t?,IoUi]A$_O.+,3:\+'c"E
bpm/geB_T&S51R+ao.4%,K)<2euMZU(VCE#IEu!3)>_m=:ugsMOE[=m`4XII$RoWm@9GkDO=
:(a!%SFq3j5Ro]Ad;#k#j\4&lU-IcOnp*;c,kcIkP0l7(g,ioYn)LZ1`,Z-41iDY$-L*gYe"4
6Rh8j]AB%*RSNNDF>9^]AWXiAaDcUb!6>-f7OA@(qjNdX5^g2>>Pej%KBmSJ$#<rD:<(%(g[_6
#a2Hc.oFKIb4b!Z.E1#ZpMq=RsVs@<6o8h%1Y!nnlji2S#CMO%:hK-J9Mfb<8XP(kI*X%s/u
W4Xd<?Z9WsM#[RDJm5L\kNEVD`AKTk9k]AjZf6Omr83iC'P&dUX;KdIe_'?,\2^[/uJ:3Kp75
FIJffTHZK=]AbZMBPAg?!cVL':RiCnhQcs+DLs$>&LIAq?RHA8]AD.@a1=L&$BeE\`dr>bAOZc
r?in%je-.Z69#?L?UBc`CA%"aeR`@RZ0=sO;5O_Y5%.C.93HOJ0e8`f?(NPn/Aa1V4d&d\SV
"d52[J1EENHkrOIj(fh3:fo1V%?r\\Fs2]A^_':?+(1sbjoDAui^QPMJ<c)Up\kueV,]AX7!i`
(F_7Q=l:k[sekVpcq%Z3ut&TG[o6fX]A,s_Ipc317=!tH%P^lU4.;?/GE79E9"f'&IoV6)^Ht
%E4RJjO.gN7UD>Lp`st)[2^K<SfE56)R9ti8ZN8-51&<>`I>5'oi#XogmSldo5SY+bgPGA-)
EK3;&[i'I>#N9uSg-I)4W(*I?>*Wck[8[Ke"Z,cPkVe)X-J8HFqeMHQ"8lpJ\2k+a'6==CjI
6nra`akTk<0&Dg:dN%D9Ln<IT<YUkm6%-smhchiOu+))I^@bHYWTb9Q.J\h'MsHPrXa@4GSo
(d1'T-7cS#YjKVNis(aQ_hWF6&.TK$Fs7Z$$cfqAmVD-r=C>h(gkJaTTaA"FCMlu%j#,n]AYg
b!Z!4skYbraHH*F,*mf/I8ZRfOGRR9i*i?QdJhSuf8p@0TOCn<R?,LC!usQ_]AgPr,U9s38pq
r&/mh4ceqmN'[@5K*&hLCP+_1,+\WnhUj0<##)M$a-#!Se8SYTN4<Q6IqdO(L1\=(Vpp7PT8
+MU_ekl"K?Z*_^-J2ThK#)kLerhFSmYGAN>QR[pLd8dJ13S"d_<DJn$3k;%=&\Pj<;"_(P/i
!]ArEE_r?V.9I>BoYWIucmo57%@k^22c]AEn%e@Mj76@Zpu&XpTJu/iJsR'p&<bX5l#ns,fmeo
m.,0$A3mb"[EAgFMf5Y%rG9X"CDLP`>P!+ZCAU3ds&ch1(t;T8DZA48=Ze:DT<G99.^Fr5,-
/d7ZAP_-X2lcrMQA#&a==KQr;Vg?Uo<ju^<"&5VhE)rHhkS#,#J43ibU:BI@`kJ]A#`a)!`bm
#<.QMq"fag&Vuftl$<2R+:^*ja'rV16TF1U(J1If9&smb0`%V*-p;fPpJ\lhb?26,~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="732"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="48" width="375" height="732"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="1d53af8b-f2f9-402f-bb13-bfa82d2c81e2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="数据一览"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9".'P%hO?B"mQ"S8=Y6EQZbS1m:Xo>%rj!TG]A7q.\4RQ/HHg8'.<EMAIL><lb2\_OWMl4ZAh
'a8.#MQ,nc3>Q,cIN9Ukj7$:f,duH[gh4Hu&-BmJU\d[9tS8>jq@"c[4RDF!%RJ^%mkIqkfA
-2Z`u09h16ar##K:B;te.FhRJU\bfqK!8oVVo\fJAie]AJ/kd$k(_j]AckLK^lGWVlV4H@s+Bh
6:eOrbh>LbB`m4LJ8"WrmDlrs2hO\>^7j5jaM">&LlbP@t&F,^3]A$&oqU->F]A7JLD2E=c1qJ
U\Pid"FFI6!^L'unoc-(9M&Tq`mL1X\Jb:;8T,O@i&GVC8+9;1_%RR,nK2mQ-3gNK3R%f?k<
fRp`&g%K]Aa1MH,j!8uPS2]Ag]AU;lAL.aOK%6"Un&[qm^R"a85j%2f.iCp*#KUs6_&eG0R%"_;
;WHq`;/f^'=W@gIAsU4RdU#47Os(^jS\qdD48FH03MH!!!&j:NV?0-FOlt!%n_(Xc#sQ#<G>
]Af.JfN?V:r,-NCe_':4:M"4*pn<U0)`!9d_]AGqFL=cF3pekp(s/CK<0QQ'i1SmbGdg]Am7OB@
m?T(*hLKr)H8P@R.g9`hs^&:8LZ0dRr7$8or671c`9.JK5?#o<'-LFP*Qn2BBuCV!toaI-m-
pHlKeg[NMXQg]AppW&cHhpP-JkoSW)^A4[LEso94KCRF$W=t2TKYjW4G_YK"6I=5+Qs4IgbA/
j`^'YCn4d\/)7VNgunsS6'.qn&c$G"WN.PUBVo0l-H*/Qf^.=5.`kjKN.t8s-Z^<oV2^k]Af2
jjlLri=1Aa49<6S\+?kC%BA+L7S2T"em,<6/I%p-,":/gZtt4MPK)]AY]AbM*!@)S/lZu[Pc0C
]A+Wg;9EoK@KF^/G*HB[V&lp>=#L%2'fqge:H<M\l;]A8hC(V;(-_T%)gpK'C6!'JZTD_;.3sL
sg1$WMYMS\[:NSCli*S\=C\NpPtk[mE"p=/m5#5*$/nGqF_#1[c[otB@2kZVK.2lUHjkcL^K
Q3gG82"j:B[pLQmXBB5>)K?=3OOB:ZkOeMKdFBTYE1k9%V,8R.[*A;6q5Q#BGgd&q0hf4ID>
lAam'q3(78?!B0nM4`K5T""'%eYX-jrLpJ=<p.^+\q/=CWD/P^6KRIN2TNg0#"'>ZLaW9KAY
<$;>,/1Sm8bKdj^=7A&0kXS+IT!oOJ2IVPM(;h[*_k'E.i4_#_<>WT$JOp..Vm+Rat:R"Oi]A
!@R$l3]AX9k6b^<tHH;]AtKrW-J1rG2SG7m5lV>\62<Xl`OND=u8_D<Jps6-t1>TC2S?etpd##
JNV+lt*:PK@_R+85U"q(nk>j`)s2M.F*hZLFqZXWZdb/@rN`6O.n+^;3i=TKb'7&<!)%"9N@
ER\]A*i<GKK&a#-Tk*=Wg\qUh.GDHlcXJIog.m;)I'?JfG[P]A<uH'Ag@6sJuIg+Q82Qh%ZFKo
qFWUdA<"o&EPnSCg2NRX^d:1SaL,,q]An*"n<%o:A\>neFq$TB;LU2.!O)k)=kIkeV27.!t*-
F/prp2U$1t[6lj"O\!T'l;8l%a>tNUq!654L1\2Bf]Ag8F@05$pu=fM8Ds*FU0`IDXhmN6dRm
6*ad,-*hrXF>D*Z)=.HB_UD,498\Si2llkFp0m%l]AJ.cUB,PbE"fB'Z$gE1fUD&#0l?Ef#7G
M[gf:+ee8,cWkLoqMAXXiqZc?H<`b:qof,'4r`Kq96R;$`ge6Ad^u4Qd@&lRi9$u8tZdC]AKk
b:$#^pL5blB-#3U1\5"qgIipX>.YY\)QD@$X9(;lfC?\79'd@o;kbl08LI80t2GK#/jJT]A@V
(MSM,Xtq1[`O[B1[e/A-+Ua<'TNi>81,>A$pD8JV?[,u$q,`kH?Ds5[6c=mO:<RQ2IH'joG`
><n3)ESPXdh9Rm+B#?_W6=m"bk3CiF9O\,>9LhA(6$Z.Himo6E6%93B.JmmNCOk`KEOtK/[;
[*Nn;5FMt\gKE4B#>IJhX$_:i,?Fb5!=%_2A^_5&1+`'2-]Ad3UYZKaja_b\u$KY_@^3M4o*P
L/_Qd]A8t-Dmt7XetKM`2r6pTGQ^50;hBIfP.<"#BSPb\P%lT`[jm,Z`qsVt:6)(>i7FP^HbQ
c(c)28)SFGe`!G(H29e[4SbjX`j;\GN-?p1i)^nGs^5p3I&8nP)gP*%gS+DR:fH_Ss!/m/%>
>HiV4l!Vq`W;H/;Vs^$HKm6r&aSp4r8/ZVg'sjJKN2;8^gG[OD]A!VDcQc>+T7,5gr%2*D;_L
C(rgdqDMI^ohMU@ogbo#=Q"1u:5e^\J>gHULBd1<]A&#R6@sbbe`?;^TW_(QXuA2I[JUTLMte
6>j/F9ZVc0.9Ho&/]A3CbjrV4]A!G>VQh#dCrs,OHod%(]AHu<nG%tMq;PW*s.kFl"RoK4?`ata
c37h)b#l+Edlu,5qgWDZ4[anQ![M0Q"kYLKTnFl#)/ZkCR##`%``mp<h.?4%o[;]A8IZ&uEYP
g&oXat+?!s4,.2Ki)^b0eX'62BXkY/(m=5U8=[p"pQ10Z8qhmCT4`dV%r71W>%*bO^_]ABrCq
R6pU92E1!nQaJuZpHmp2,g-NQT3U$=/KRMQ<SjXk$&LjRV(&TRV$S,0+:NpMp>-!T&J_t.[+
PJJcuo'Y05kS.OTMbqc/BTYE)C]ApBY-6T!W$&[>CPN@hH)+XAT(5d7%MMt_gOEt[A-CAhK"j
dEMrXEf,JILmH44Th:VZSV\</[dr(C>(hs>&f4+q6[Q3>)+)R'"o'W/a1)CgrY?;Ib:6KFBo
>5n,q"Bed3#@=3"SY)B,`0?OB?`s4HBa%Z5/iMfF>:NEY@'Hf79=Jea5G\L&Kg;VaaBfU+nT
79`&ru<'O$`EIDkFichU>LZ))>uKY]A5gr3bS<rbhAIG-e\[UT(r<Tt(oXmHh9\T6WHO$EQ)p
51JNgL-DU0h'=_S[(L"_9VreZA)u\-4$ngEM:?F'7&"kb?pn./#)gM:VU6u"gn\B_;hN%sFB
oT^)W]ADp`:"dY!:,C""T,NuEIRQmdn_@GNfUV&?dPEg(%/%`Y1#_(^@,p9(WV2AZkoQ#597I
9$2LQ?WRWnIP*P>MHVIDW4bMC?nO4\?V-R9VU>*oq!gf=4k+@H@$?+>Zmhc6bA]A[2%&ZNu>h
XB9R_'I?)Gq^"n\LN`bo.dS(f3]Ad>Dhb>]Ae`Y-8bna]ASo'LUUelG^p/CE@Y%s0AG-A(V9/dk
]AOBGU4F-d63$#mZQ7'DL99`nUV0\3mq8_p-18+D_S\jVle$4oc0AF>H@VUhtRQ/*gb#-$]Apk
KH9Gj(=QhhZe#\h1V3*pIhp-k;+;2;e?6.3ilor\:J"5"5HIsna`7rW%:U'iB2S8SBVoNH1+
l96\J1YXm[eUE>QtGSWqU5]AEXZ$@lbpcLD!]ADHoug+<HfZtV'Cc5h2N6T@#hXF0JNQK4Ak97
B=F*@/i*?3cg:3Grj2%m[GskoZIaf)Q>iQ&(U9eO)=]AnKH34.9$4k,dXD)EOa&UQ9CS_2a.V
./!AJp_<A?V%NQ:s2)cDRWe#BH3PK3$/#rPr$rJF*U$*745$C'=''^kRH6M]AT*S\kJVZDol`
j3`k*m#k`&IZA:]A_WN5Nf>Q4=28R;$m(%g/^^'!ifXo+]A(UP.leEm-?to/UuP:qVq0gFLXp:
f?eV$$>Whu=PrVo%VTG?s6BC\F7(@To\SKdCuBS]AT*UU-?ln%>cn.VRTeE1mlf>*!XMO)Ml$
J&ns/;(ubfJ>PJ!]ATZ6'c:YH5cX"a:CA>g02%qfQH1;1N/7J#ef!@EAit_RP6\cT"Df_Z+<[
W584CS'Y9bYFLpa)1@LlLl?lZn]A*0PG3?IEj/tCeMFjCt?Se-6J<LeL':J#mP=N@]A9N$L-Uj
HJ$u%<lB*Y0S+M$]Auoe(>F%hW[ku&lXnlSf<K%X,6&_a`.:Wec]AIHMZ30c2.eEH1+af))$(a
#"Vp<r2^kNnZ#,W;9R>7h1$3L%nTOGI!,!.D)6n*.erJNT^oP=i=7p@q#l:*.,ElL89Hc?iO
3'eVO9D=%SfkfTie5%l:kjQTo'6m7K<Gq&2j'B[B=Q+\RM)+%hDAI)Cie2#,T:)')Q't9E9I
&Yl)Sa9;V;a1N&EqUK@><mI%H#LZ<H@#TkTPCRW?uA[aMX9$Cdg+DJe@;*Ri<`SN^J3Ck`nH
bmImW'qFp#'bKf/0JgRTS6h62e*FR&O>\9Rmn]A6f+gl.m![<HP$Nisqp>IJi=?o7J9Ng:(56
dC_t?$ncCQohj%hX*/*6m/N34e7\K<#RDr<)jhSRdP2;[j]A3_p<R[hjK:+U@qD2^rQpS5UAE
2h4P.-8*EK>@hn-)taPEa'$^B3)BmY#F^AXX=YA)dR+"Ie-?^*!_jS(=>,3i!CP;K%uMIB5D
l:_J==Ucs[6PIdGa8om]AbFXFt]A\]AodFTQ+V1-SGe&3%Laf3nKB>mN%idFMe2(O-T[:XJS^$8
X;6pOXE9RmAR=.^dp.RDJSKi,mstZ>D@9.A(B@B)[fbCKeBiN?ct_gEG"dHa`F+'a`J<(F-n
S^5<b\"Cc?Z^P/MdP+!")<;D8_fruAi_TK.#]A!io2H!se+L1SS!XR7S]ALO%rhH>aY4e\/B08
#XhZEINl#/?(j<Ec.P\[:gcnh`/M41+s+0(@=b@h5!X<OlroG9O;K-0T#BWMG&.cJAhr42;A
\08`]A:aCAF^r>VC7fKp0)oSODHmgJGZM\#qJHR:'o&)\bNk.M,9UTSNEo.rY[UQ&S8h(_b"s
&=XFRodW<M!WD-mdOQBt*mWf;OtTg7@WI^b?7Oo]A\aW6%8^hIs6>U6@7B\KNZ<7sU!1SN?f<
6At>]AVrI=L/]A(%oR4M9e,1VlmmTnH[UXZB\Q'b*^:O[B3\IcRm1FD@0lt'EAf.6#ZA>C$\A"
1cDh3Hr,.VGQ=\<tUn-J[U.kFnEf.(\c(Gt\e)`X!'Z3EI'sG<RH^rlT0B)$(A`O0qj#`ji?
+RrRds9M']A*IO?>r?g'>.L(F;&53Yo2=_RCM$_`^K)0qc&f)(96PShk0d`/;;iBM6AtVG9_.
GU\19;i*MWLj[LF&1iZnr)PGS&l0&P3J1EY9K33YY=q5q38_m1ELkqJLY4jm]AHV0Mjs2_b:.
2p#OC$K]A[GnH%/WJb4Jk`[6l]AOk]A-i/JVr(>:$eP",C@drQ`Z%cf^E+V%G*L<]AO@Gj)??QLu
%Ok(,6XE/9,-_>>t/i)K1JEElB$u9:-&XV.%9>)*Fb_fu>:e!AaIH1?]AFWm*a4T6,7)(hYs5
JEu!19?aC_R+R6YpamNG,q,mU>;\R>M27l(/Mf6GpCZ]Ad[43Z=tg\Sci&Hr'D3(<0nFQu:4a
NPDIVhu:_"9=!AXW:"u8VnPJ/&a/Sbrf6^>'KdC'snMh"'<?dg)eQ(l;4P<[Si<a(aYhn&CM
VB8L]AsLpU,gZ3bGZ=!Ca@EPBNslAMlYsa&#WY(ba)R_mYRtp/u%d%P/+_3LTutHj^4h*D.l`
OiGk'T,q@?UIqoAq5nk[G4n3[gui4%@>Y1s8pgJ'Ve-]A/Zn]AJqC<hs,;I:O:8HN0\OXq9Xi<
d%6_[*4`Ip$-0.lq+YGEH4?ia;7'm,&6C:9<#]AR4l^O+,YOd4[u:b1>[&Gm/2]AO';np'U<!R
GOadrHBs@'Gc614\3R5]A#4DBPt!'P/<L4*DlK""4Tl`r]AhmJ,H0%;M!@HT\f5`t9f3Tbra!C
@FTZK?buP\+A2[NG0.8gj')OII:,M_>KV/p0aBRMq7O0*Y'R,Wgisg!]AJ;H3/WG5T"EJSrIr
HaFmL917h^KM;T7R%(u'/^2m[FeBb%)g-r'\(e."j^Ml44n3Tla,J>J1]AE"b-3Yr=[XpEo^1
9Wd=PlMGuI$p")s$Q8)6$F7]AsM9n$j6MbUJ<Z;H-ZR7"EY>R+XZ/EjVQ,Ml9buY-OUT4f]A82
78Po5m)^br$DR58a<S6iJRX]A!THd3GnW6aN_jfK=^n=C@-jpBjJ*OQn=/%M@Le"))gsQ>iJu
jLYGij]A@P%f,)37b\l#P8otuA_II'KNg9A19m==&8@Gh2_[+6QrkVmsD#6"N`W0OJ;=UaDLH
04WrT8<VRB&p?$h?%/JC-6g<b@9`lOD5X5IIm?R@*),M.Dq7;T01XrjWg_"=f<04pmP9gTAa
s#"HW;;=_5u#/]A%,2e(FRHV.N_=.UL?e(-.s5QBa#4E5_pR;[(2cRZirEB(pnFoNF/55^XHK
dI*;0Ytqdo.j3m*:7<XEYr)1Q[::)^gOnh'/1p#]A^(Qf4(h-#H]ASsa$55$2uIeq,D@WL`KJt
:o+4J9&<WW:jsf?+h_\W[iS0!MG&&jGu$c6$tAToZmF/nQ>RL$oDD7!W?]A'io_Ne5D$7I2;b
4d[J`3?C4PVA+K81@iGN0eg&CTR*)jU#8'ssNHr/-\VXH`+;ZR)J9NA-[5u\a(.@p2iY3XNQ
n,Jck)e70^:cBB@_)Ut2qO7c.iUV$5BBQ7GG%F":_LtF/^::,m>fcUThtF44uSQ4*3$.'`7*
YcQB%s$J(d`-.5t_.QRHE6"P&0hPtC@gk[L0GCYORPU*$#KHiF,9YcOD\L7m!>lgbms%Fhp$
']A]ApTH;pKAVVeh($m]AOWf/_'XR(UBfKO*J[&2\S$o!-L\2OGd1`e5gjFXQn.(A_J5Ocm1U0)
eegJ#sH64'gEbq"kL;QC9tCp2#pos+OKR[]AM]AWrYmA4R9O:6WXhu[/2)ClS35?80Yt$?YjS`
MJGNgA/F0VeXp]A/db;nj@`5/IqhKJLB3AKaRMa3RKa48JIe8[V3Oi.DA,C50fD,h!1mb2P_P
;Ri;olW*#]A8D@j*ED6Mh&sYW*QDW;?IZhg0P<7+iX\<\iA`8)N,Z;YUR$r.VASE43-d]A#7[Y
T%>8DcMM8L$fhu%q#2i'AjHF>]A//gNfj#fa0@,f0\m.-=M7CTf&]A-Jn70*>-/fML_SI_&>7n
$Z$X?pFFnYK9,MN\pab@*[(W-^P]As*F?ot5qPu4M!5OSTohZ1l:_34q&+ra0ohZ1l:_34q&+
ra0s)17^hBhTGmckU,22Dh$^Cmrm2;K&j!8t2AJ)'Jh6MDo5&Fee25,BY"Un6)"H;[Q(8Nj%
#oq\2/PBp,&l:F.<-hZR#"*.%2FZ)>Nf0lGF*BiJ'ORtoUjo5;~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="23"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" cs="2" s="2">
<O>
<![CDATA[数据明细表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="112">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&;WeGK:@Fh9*!jJm$iW)SX"`.U`c.bo'f&gKuRN4#o!1D;7IKI#6;,"n4FbLhPGA<4Q7Qn
_a`@#Qfr#%Pm/kdl.kT6#J)?b^ZrBAVgqEF*REfCjU*?I.g+omFJ\r+W7IH`+eW5!H^:L'C^
snaUJBk5KPnYJ-tk<0QG(F3$;QEp577`9AFrie-rU'3)ftmjCJHKtd3@gU>p$=lJJ,f2l]A0-
-Kir"7aPr%!Mh<IJS'SDZ[?o3s*/=f_TW7Mtjs4]A,_M#q#^d5Sm8q<=7;0_BJ)(Z8HYF:7%(
lHOE6]A*A`L'>S(gd*+_DO1V=N)V61M9O/BZ`Yo6iWG1k<9)O8Dat)p3^n'c8)PWT#Tf^:&k&
b0Cq#g:nd,l26Xn@]AHr>.^sf1r9s'3*=LG&cKV3I-c1(41f@dL]ALLl%_nWh8C5g9kg8[$iZg
ENUU]Ae/sC3@KEeR:J10E1IC[I2n+)XO?9_XG7<ElEl>Ic;gU;lJh&K%jtPs8HB/KX1cEWKk4
]AHHnYK^U-VD^%S2rlMA;83GU;)[_/2k:5<J!j"l0T?SW8S^AIJHOqLnP&UfF%n/3WtI"maA1
\Z>LG<GNL:f/]A4)J!7>5>U1ib"(<X]Ak+>%O3Wk!a4U)W``@OPIaRgK*&QHq7ortL<CgbPF6#
t/=dOu<J;rNVr:j0NDode(U5d5YNNNbWD^jON2n/:Si[XL$ZT`Znln)#5O)9\b;`CaJ.cSF%
=S\UZrs1dMnB1)M5.\l`@AN%Mb%Re49em@#XK'LQXKP>Jpb&%BV;Se;j!mum-8^`hm9"E<[<
$bXDqcc>NFSr?&KOknonX!7Adnf72]A*T>#SM=JVcG\W0=[-X(E<Ym)s]Ad6&?SXYZ!6E*leU8
6/6mN.bgoqb1(Xo_6%5ER-7::QMN:6>=IFN.o\<unRGl,V]AA)BP4bK[s\ls)`AhWE!$3tMk7
LMn3=@9&iXr8M3Ej<]AaQ1-%2/0cl9%<_\?WHTZ!-gR6F'R'3`8gEO24@:DMhKW<K59Jq[g!d
5o;J$@`;W[unZ1<%]A.?#POEe,OKTUuFL$""ql5k-r+*<LG*<*,5M;RCNcL2=^6Ke1-s$\M\n
JjF!B>8cgJ<P;I;$f<;IW44'51CVp#%st,sNT5fhD1/g``PQuhOa"6HdGo]Afk+eKbgsjQ'C.
=h4lRu3Z%%ur?Bo(,Umu?V;$OKIto.5NC'4[lI7V_X_/s2AQ"`O[a>?mT=E6%OUkK5:<Z+E!
o'8uprJ;mQdrt'-OX/fLsZITY:aoN`<Io%8M,3AFXeO[n_XH'jIZ<fa11K_<ZadeW2'C6o:4
hK9'dJO.5nDCR2Zre)1185aURk,c'C2[;2I?)=e`s7ja>XU2)@U81WkF]A1':U0217I9ae;c4
f<<2;q2kE3u@R`nO3JJWchFgg/M\gCF<?-AnTniI"bm64sC+4Ea)l9J;*#2qpt9N8=NFCWM`
k^"'2)]As"Uqe<YVlt:amqaZW5NA]A^u*'J5,i7-aTC"<a%(P?#NE5V._i(?!4CD-Q,EAlP(>^
aA+kRY_cA;oF@BloqnX:!1O1BQ567K"4V*P_*hU@Dhnl[,*3+9Z[KMgG*p`\B/!"r.kJkfc;
Aiep8Q[sd$I1UtdeaZ?Dn$d5UD2A7U7d"sb;!#FFnm)6E)d\6.$@i(Y2d?N@Fhb0YM:WF29(
*c=):jZL:<n;%##OM%^Bc0&cInQVV;\s#1d_8@Ba!1]AEd<*U%5VuFh71+J,Gb,6i\&C4CB=E
lTrCWPk$_k)8%.ih:CccYOm-efqZHj%mP6^PSrik-uQ-A<-amTD4Nha^,]AgY0=4W@V?Je1W@
r'A-9F&b#&4=Qg06Ya/9/GLJ16Y[_e:r'HlN#SHik\Iu,/D+dq;!5us5Yk$BXla$U[9"8J??
UPHGbL:SJg31\M:PDq!__;4;7Vqb?+*h@<@P5@SdlN04M9F:XC9lA'kbk:;Os_(1K1mIWGR+
Z()+$cq_n'Wks\EfO*[XIf%!?=c!)4a5hB<C#Fq%Eh=hA,p$]Acd4snk$4Jf"&4Ph<`._,4[N
Kt_H'OK\j2=I#r[TB-<d#(nCT)F1.IYTIAhhSi$><VMKPikp:NAoMA)1BagH_f@JV*EkCTcY
AS$ct04HV8#c&3\629H)3/8Tds1E0KWi^8d.pc;Xia]A@7adVf(1l>6'J,i-9Xd!LqgRbH6UD
QQaenQNIq$GTC!_L0nOu[a,cTQ'*JGZQKJ>Cb0U-lqW>1cMd!2V-`\\gSZoB=fS;.XF*]AME>
TG`Z5\jd:4_et*Ls$"fO6$_+`duR,;ScYYbI\n\o`Ru.Eq`qN(S'-L&jV2Sd9%c(@P%g:Ke&
)RQK1qh8\/6&HXH(D(km_\.Y$3hH7\8#?>@U*Gcci4CDksmb`L7.u"Ql5kQaGNEEH4\3-lR@
ZiARE1t%+W<^O=]AC1#'_t6EuMh.3;mr^O&*+^OO]A:=ak:6BD[X<gLH_NjIt9MB,Pm/`Zf5Nh
QX]AQ\!*%#)E%*,K=5p"2.ES=aOgTAM8ret26.*FK!gp\6X;+hP=VE?[H^X:R<I_Um+$"j3]A-
VnMPF"lD=e6?=PD*+@-04)=a=>G08c7?65o<]A?bY\&8A1/Q(#uO*T]AK9A]AiU;hkIr@_%!Lm`
uVj&IUSK2]A'b3Xt"rq+''P"M7M>Na#3;ZW^0[[C6/+4#^cJ2&`#LS>P)]AI6HlK*^l7tZ)o&L
bS"9'QIk&<p_u<3*_78>tddOW[Hp9;K?^b"4Ppg:7d-:<DjMP]AqF1k^Ygd<[C*D^nOP,>aab
%4mD;U7=!K"l*h?/h<LS<E?(dguC</GlX81fDY(?Ckme.IqHSg)"SSMY!G8[`cp1$,=1H4Nu
0"h@&+C8]A[6^8)9<b-f%^Fd%ncmANT\hY4dom4'0%GX-7Y]A5KNZ-p[URDA2JKs5#Yj#F(5*P
gj0cM./J$PCRi_0FoRkC@$Kdrdj-IMY:+Ur>.PlYkq;Q05c-R_pGl2"?p*Wrd;P.ujN,IkKD
O?iJF@$bSK;A0G&^D44YI9VPq/"9ULCL7iog(5U03;*%T(DM!\?Y$^[8Md]A!UsNacH[WLo2d
]A_oC*]AF/!?7Hf[ptT#P>Z4eKNS==G!Q<m%Br)=]A<L)PX%]A-"d7l6\ZN3V['7c!>0P)B<J)kE
[8=0.P#Q<Kn.u.ZP`NO;?*<h!'=,Y%+8i5$=r9E1<??1#;oqmXOB8!f`,iVAFn$B*]AMJ-.]A.
RK3D7sCH["8E,\oG\b_hd)g5FE"5pkf4GT;7?%/<6,g8/>tpE1p9d(9k_PWO':W0O@4&!B!>
KaT5C_]AgU#$rE;F([g?7iTPoWVe@qRGu9,ZJ*"\DFSp?gR[sBnK8;`Q]As)B5bOFQb_]AtZ;Qu
2X^N0\jlYG.1$.fN!$Eu+a5l:02b7^aIdr'E/2a4dH8/`C$ZA6Nt9AkVMGN@A\`$U;.9V%m`
[Vn24HT7bhlBGHo<qDbX$WVRqk3JlEZ$$4-.(lAh^fY)EU^+kBL$7?r<=(Wt$/@M!h<6gJ)m
3nD!DMWPWdO1X+*&>iuo!p[0/DN)q"G31g_'?kDcc@o7EIc3oMHV)YMlLp[@p/&QIQ&>DfUV
QHOWje[QY>e,T_!t9#T@r1TRQO/`^kp[P/QT@YOf0BI[!?e;Dk@=M%qqeSfGtbi6VpmSeRXo
XOg8]A]AUcg\R^N<HluL]AN398#P7=,n$R`,u@/q1H</uhlN#T:9i2A?-c>JtkEm6o!83==i8Ld
Wrhq"[9cV!Do`KQ2K0egn6o@`2I/&n5Mio@).t^kOBKPbTsnJHSak6ShtgL@-/6.fPoKS?C-
@,V%JHaTa>q\4KEPcZIie/]A)oH;C5!)+Ml4$s1ma!i_R^';m-g-;:W*hR8jJtrUj#+YEArVd
60)1BCGkj>'6%56Sa*A=b!N_W"j*JH/Z3H'ZErM6@N6^+GTQ%qZ>mLP32#h<j"/6:!R<qYDE
g".E6.DbW3E2lG#99GiPTOF8YBYf'@:;C<g=SQ4gG]AhXHr<;>Pq(!DH'PJ<E84a3%id>sEIu
@Q?:M1_VG4]A>(@:CDZiIffKQFIiF1jc$NehE\j[Ve@0b>HH&]AiUh5_L>l;ORgi\)3DSfA@1%
C#q+h-d/Ob^9[)qN85_:j,"K4J7^>0tpO/(S768:)(Z)5+S189DL`WSN&mW<Z1OW#?T;WPCK
MZ)W)qaO!kj(Y0Q/E&0)45(R!\E:"IXT.#lliD-ljZ`rt<:U4ci[k[7a'c?<?&bf5Y;B+?h.
4.X>H"AL7rq=XiqE&-)75o1W?tl'noj#&3SIhmldKO@?2mSjg1,3`V_ed)Lh;-s%34WW8l"Z
*SR8W2fUp:Z*e02#a0:jSoRO$<.O]A8HVF`"[>DY^\7qHFs>FRt?(k+:El92%lOUQig+,880K
]A=op;fd:#-lh#5hO>6$NrCD?f0spU7mSosbrP:FP]A"K,)[ZVX7o-BEEn7MK?AXn_`4ZE)Tf@
dFIHeuK2V(.?4!.:7q&i^EdN0]ABkOqLk/P%uoFJ)>b4\po1*2T.VB@bP=IQMZVp%N^&O>rhW
$-@;I,H%n]AcN/3p&c"XIR$gG:qOq.';VW3D0lV.H+4((Fa;'a[52IDN_,0B9PP0*2^,8/%&>
D>Q5PhWQ:LV@1$+kGT(:KB%(?ptmp.$oRO7"RcrLNnbkObm$,$J4]AjKLT##QDuOD8ghpoT2r
IiSikB46G=Z'Kte]A1bujX6j5e'[:!b5'g7i1r9TW;:KfX,NM`j*fHgEn$'ipYQ\m3qs478Jr
F6P1*Li>Vb<m;pW67$9D]A",.OTsu(2;.:%T5h]Ag_g!>AKjg<Tu;p/MB_T.u-fW[HY_333<Xr
o%jCBSF$A&t[A"u9Z2BcT'Oo##:I1pBjmA#6$#L!2\J9I2pX94dUG")5Zm4a(dV?RW14't_%
^-Bh:2W7l@6,0oMK<e9!!,d6Fsp<USULZMc6f))A-9.Kof(T'q'6&HC6_Y#i-l1Z$7Y(IDN(
Zsdu2hsWh!E.H`7\'8gMj]AqO8_;jqnAE#TI!PR&WN\k8_7O)MDa-lCemnMMH>Ri5JhaLh#/k
h.Q<de^mn=5QT9;_V#?5:I%lM]A<_o=n+%l,NKh9DsZOtJ!Y%%-S`)5a/-Uss%/7Pmq:HP#Gp
^<kandT)V*(]A0ljm%WpcG5m/#D9DYIHP<;9m]AdXM:pFiC(26)tJ$D(nKkAFlIX^q[lIEskC'
:VKMaj\I]A&Ne`6.+N%@&o(3$QkOa`B(Z]AhmEH%5?7jNpSUjhI1:N&FH9>d'(S4'LWfkEps7J
R(p4_JCI-)>G;F\c:FT)A/aWU4,q&#PH<9YN#ASQbLW)bB97h&NDO_DF1tYt.;!248j_bUW/
3eabhK:jn@TSf)8'$E0@96Tfm&]AfS["p*FI.-/6QV]AQ1(5k\m5&Z?fV@?fX6AO-"@Cm?r9F`
+b,)?)9APrg/\>Kd'";-!@BW*rr,a#/ka-"uk$>hVLp7Z+1-'X3"+'atGX4^6M!+#LeDVBtu
%.89*6=oolVoRo=^m`MLeM_tLbY4&moRR5:nVNV8I_sTcX$/F]A.!B,J:a7b(g3IOBWugiJTr
de^9URs:;Zh2L@U[DI"0LnTZ;44%U;9rYN^QgG3PjPf"&+8BL*[9tA3;4V0W"BMi:)b,+#g)
Obb?\]A3&fs=\N91uDQqj/-^OS?I&7D&&,RgG=?oBfa55%t2r,sT)\ke.k*m>M%UG/8&sfKcj
qCN(/E]AYVALb$(-K\U-SA(J+C39;EXj<HTLb#:^QR(<-\^cfm*E*F5@X!S>5JC6Q#7PLtf36
urJ7U(8pA8fBm=6n\cO#uenQQ:Q"<*CX6upRjf'@OgaHSCp5U&Uo9/'/\42sR0R08We90ZhQ
ae+OGiBnP:S8%NmnJ#qlSd'>cq/j><(d4pt<E"7CDD%W\&dFeP=8C.aC=LPrE$+^.A<;-S^#
Q!/(<[3,od\-4UWO]A[8,)k=q40M%V\c8#Eb1D?4=6t%#-\nTJ_.m,Ml-?\=%9)M*N^X4;`&5
rLC@p]Aqn3M^:$pGEfPK=q95Bae^Y:#>X-kX0@&R<fZG5t+^7D=d>1[2TR!kU)*67c8g*Z(of
F]A#H%6d^>_V'.rH;%c(<)=3Da8<[8(qp\$rIiNR&,ZfY<N",S[ga%N'a>^0DNcV'99.Mhl73
6H$om=O8ba"c*RG[$dsmG#%El\8kSG$]A]A64nIc>M+'*:[hFFlbJ1getVZ4[\5eIOO2YIr!&q
q'eR!`GC7Q(b!WbBsO_Ho"L+d?$+PX4\iOQkhZJ9#eT&?&%63bG;J4HRi!bT?E!C1:qL60g/
6HPOL1g!5>phEW;l3m!sm?cWR0p\[-SIL`o+@eA%o>%;G`B%QpuMW%>LJ)U=BU)OBHeuLhG@
uJg2/f.4<qeo)6*BOTu^Kk%fCN!#)@r:V4cC!k8,Lqg42kT86gRVjqmg^3:Y#*K/MS8i*5EF
g:9U-H<\qoJnim_`1;rJXJ.HLZeiQ.c=%5FucrN7EpA$BOe?l92W+"7?blSJqD(VrW_cjoid
Zb-[_N6Y/&1"RTQsiLWmr:1p4LV[]A/oe*&:GsH@9MOP)o[pXH?=Pj>uS$+E]A7i]AF8+Ucli]AQ
fPk@bI(Zg'U8(HK_KYIU##GZsP0=<Wdu1YelG6s;/Ppt.QFp&rc1`"3Oh0_JBu=[j1EPP<+N
in@&G-a[BjA8lJFYM$ZrbD<&KbQoA-",=&L!J)g"F0&HnClJ1jU]A$JfhU1h\sK]Af,-pFjh!L
2(m"\61LSPaCVADg"ER4/M5GLWCo99L5H!q5Rqj8kFIXlV$ZbF043).RCe!d4VSemc7tD)4;
D8+?ls!biG'rkP!,c&^!m:B-JHkOUJU_P^YG=^79/;WU"ONj?*@n^1*3-*IRnBfW5C$pC[4$
n%fKWM`]AQcI#]A'HYM4%(.6U3`Hpcj.:_m-rjpT'/tPP:XjUk\tNLY7@n`!87]A9!VT5!m?Rj4
Op14kO/TKOr::;Th<h$aTD@f=$H=]A\/4?&_:9aJ!'J$cNM%Ef^f^P;oe+KK!cZ-j&nH+\j%F
WGo`Amg28-X?C2:$@r$DFn0WJ%d]A*1i.TcHn`(KW!@(`+c'n?mWA)V(^;dNEdKk5+Ip-)omp
jUXRY5h?Y1P$5hnp`+E2r*<qGKZN>*m\>iP#Ot%ST(E&>F/@m&Sc1UH,R1[(!9\(e>JS,$&[
UE2Z,YsTB^9`!r#!0Z2h!d:1m+.Q!pDIU&U?;!N36<,k,N<c5;bgs(AqLA!\Aa89TaP<5Xb?
Uu/2Igs_;-VtkH#SDdtk>HT2^EogIY1;DPeCqDg"j\W.-s"HH:</7-njO1,lp_XOI..g8fU`
CpRBsO?TQM\;3;2.UsPbg3&g8jL=Cn,6jcK^1GeDq]A!LPVm3H@rd<Z=A8=&:Cs/VTIWP5Wb[
Xjg:**bSke]A8&4NB5&I>7j?SqI8M8n?/ubgclf=]A$lqpW$<?ZkdVZ1^ioQfG5&ZMJcH+b;jd
lHc%O98bK5RI<k:u]AEhFDC;&gt=%#cUd@@:$j=ek!Om--T0SRTEk6VjPLS5`Fc^FF%G1'*DX
gMjZk!:%&V]AVn;>5Xde'=5o&r[Q'p]AS>q->3:RAS;JbZ/e0n'9FgQfei,Rao]A_d/-c7Nj;=B
]A`05X6ZXEV^tJP"(YYuLk80$YhTN4JNiU2]A4+M/iL1lAVuLSCq*EA3.mHHX.-0[<+4M=`&rg
p9DFP5?u1UESpj/C+Yh>bp%l.We9/Vm$N<G5ai:kL:XeIVMcM)B.d![`7?O8Of"A]AcYGi$9^
S49J,59)JGBXh>s`u@;b]AZMVM\EAO,@"s`fNtL+IGbNR7r-3Og\I5",JW!^'PJdr!E`t+5Q"
e?AHI[OC;DGmUKj1rr+i(1TR<chb=]A>73aT5pfu2l`L<';Rg5dDQd91\^J+\h^o!oM-rg91d
s&tM]Acg%ELCpG=`bV_$Ptch%.br++TfpU6mb)X+bP@]AT[Y#<t6(S"P>,kMI*P3j9,nrj*p\'
ZjBNrkYZ<(fK2i7<<'J[a"?Z54S'52+g0:c<fKd`\hFB_+qe\Ci;G`YP3-,c3:`e-VL.i+HY
=plA[s#lVJe?;$_#*c4[oR?)&[lU:bs(4Tr+Z;E?0:eM81S`;Bm"K-9M_YNXn!#BheQ,\D]An
N8h(8K^UNLq,h=UeKtZ<ZGAJCGBnq1Z_),iKMVan"fG3c'54dd>L\5jXY)dhgOY[%%5%mXe2
5np!0j4EunsQ12oP\31=/di33PnC,+<Kh`l>8l68h+\Idgeu!eW=H=Ro2#9HN.7CBJrKdt(#
_^;b4Bi3XP"tqNPYmlmR@ImmiA1P]A\g>j*Z^EP]A=M:P!S&3itkLDH<8FKs+-d;ceYb8_O/!+
:s@N$LXKecb5pK8bDk]A$Wk&ZdKsNRnZ%g.,NfrP"kQ$8jN#f+rdW*DsF\V9Xp.SLe#>+0GkC
-LbZ-F;<XbFL:=Ah^'VKBgt`O,\HPeB8m(#3JO,#Bmqq%7qcT$lE^1b`APXumqQ1/M*i_+I5
mkdp!h_X5f,-qT>iJXkj(3sF=UCa<)QrHFtWSqmHeR*E-oc&(pZq-Thsq1?<_n>aeZmTbHY+
MqF=PF4n+"dgGn&CkYTn"daguD^8$]Af#qT@rPN`:R,r"'F510+QZ&K,$Ael29JV3>"i#nOE!
e0K1T_"7"@XmF[be=hDKF!?'i-cUI!J'g)p6t*%B1C3s,b_35S)kP^BI;$)6;#5"U!$S\#\o
E)l1b3l$bs\'T'-ARdeE_c90o1RqY8NulSP_E3]AdM>XHD(:D;=Z*_*Y8Vif4VH&\i<)T$0ro
VH3"L:/<gh#u3h]A5/5UOOUi^c'pZH.M;F6$*>Ef5-moH@!(&N!QIosbUW35Oog;GgV;IJ0/,
ruq>A0%X+lAq=^6R.hNNKe)*,*chf'?eQF57\`Gl9U)nE^S>nn/IkkFbZ]A3+30k^Ad+gPe0A
`JQ-M,&.<FZGS*m4qIT/\oFdkiMq4A=N;HG\i!\,m%jFadf@/>hSRl[ZpW,Wk,]At\[&U%O2)
GFWPWkgb%oH8(ooT,AXg0ENA#`l1kErm5G-QItD*nS`L=3OBj@@CBErg:bTi%>M^0hp]AqWEY
e#\E&pTPCLGH_-kVo+C8"4RI?<8R*V+g-6HfJ'2tJE(1#*><CEM-g6qfi9RN_n8IS3PSj'es
dZRW'H_`!@@r.bm6Qt0N^a*/fR6\&`ZdGYC$G5>gfQ0$"?N@mSU]Al[!-OD0'\Kh:n4)2dXB7
;,[GlW$3M^9Gb=%dE,;Gpu0B:Rj9(iaY5k0]A4$Gjjkeq5)'Pkl#0T6@B</?>F\rIILDaY"WO
,nr$5q3C1=]AZZ,N$^LUNW4]A/#WHYI7ebtXmL^)r_:kt8YrkPpY:2SPN`s,[!'?S24`Ugi9ph
'<l9/U[#IU0o0\FUN49]A#&R#QKj`a#]AeDJ^)KH<Tu,'7cYgtf='hB#iSXZ-NW5Ke(B:0+bm1
RHTpglhs4[[d7to^tWN#ngLsIeWk><EMAIL>,OBb`"&8Q>4\tE3ISFdhWH5hMYkiJ@PG.1"
]ACJ`cRu_K+(_>\Y4HKg=5T[uKGp!XYJNT#!rW6`krC+;5(SFD@)ckd[He\>0kcNGfLg4lCji
Da:8]A-#rj'b'/L[d&>`l!$<&*%(\N/-pW*lc*B(_>Y84H,'o&&^(qpE/OHC@NR7K7M[u(9Bq
8q<lB'qYl)-YKt#KT5&+Z,l[UG+mdg))a'(7nLu$(_g!"/j'b'/L[d&>`l!$<&*%(\N/-r-s
']A$pr:Fdmhhs]AYR-HKEk$*(=<?B/tGSgtO@6BL(nLu$(_g!"/j'b'/L[d&>pAY4Vp8!5dp\P
F:>AhRPWpuAPbP-Vf$N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="report0"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="454f591f-093a-45bc-b03a-63d38848d210"/>
</TemplateIdAttMark>
</Form>
