<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="1业务经营情况-盈利情况part1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'基本面单方向-期货' sec_type
union all
select 2,'基本面单方向-其他' sec_type
/*union all
select 3,'期权' sec_type
union all
select 4,'产品' sec_type
union all
select 5,'回购融券（逆回购）' sec_type
union all
select 6,'场外期权' sec_type*/
)
select b.sec_type business_name,a.year_profit,a.day_profit,a.loss_limit 
from tmp b 
left join ads.ads_hfbi_risk_tg_profit_1d a
on b.sec_type=a.business_name
where department_id ='qy'
and business_name in ('基本面单方向-期货','基本面单方向-其他')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="2业务经营情况-投资成本与市值part1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'基本面单方向-期货' sec_type
union all
select 2,'基本面单方向-其他' sec_type
/*union all
select 3,'期权' sec_type
union all
select 4,'产品' sec_type
union all
select 5,'回购融券（逆回购）' sec_type
union all
select 6,'场外期权' sec_type*/
)
select b.sec_type business_name1,a.*--b.sec_type business_name,a.year_profit,a.day_profit,a.loss_limit 
from tmp b 
left join ads.ads_hfbi_tg_cost_mv_1d a
on b.sec_type=a.business_name
and department_id ='qy'
where sec_type in ('基本面单方向-期货','基本面单方向-其他')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="3业务授权指标监控-风险限额" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'量化策略、基本面策略及场外期权业务' sec_type
union all
select 2,'私募证券投资基金投资' sec_type
union all
select 3,'基本面策略和量化策略业务' sec_type
union all
select 4,'单方向投资的单一证券最大亏损比例（因打新取得，处于限售期且单只金额不超过 100 万元除外）' sec_type
union all
select 5,'基本面策略和量化策略的整体VaR（1日，95%）' sec_type
union all
select 6,'场外期权业务' sec_type
union all
select 7,'基本面策略中交易所期权投资' sec_type
)
select b.sec_type,a.* 
from tmp b
left join ads.ads_hfbi_risk_tg_limit_1d a
on b.sec_type=a.business_type
and department_id ='qy'
and limit_type ='risk'
where sec_type  in ('量化策略、基本面策略及场外期权业务','私募证券投资基金投资','基本面策略和量化策略业务','单方向投资的单一证券最大亏损比例（因打新取得，处于限售期且单只金额不超过 100 万元除外）','基本面策略和量化策略的整体VaR（1日，95%）','场外期权业务','基本面策略中交易所期权投资')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="4流动性风险指标监控- 前10大股票" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select *
from ads.ads_hfbi_risk_tg_qy_risk_fst10_stock_1d  a
WHERE 1=1
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
/*${if(len(yyb)==0,"","and branch_no in ('"+yyb+"')")}
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}*/]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="1业务经营情况-盈利情况part2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'回购融券（逆回购）' sec_type
union all
select 2,'期权' sec_type
union all
select 3,'场外期权' sec_type
)
select b.sec_type business_name,a.year_profit,a.day_profit,a.loss_limit 
from tmp b 
left join ads.ads_hfbi_risk_tg_profit_1d a
on b.sec_type=a.business_name
where department_id ='qy'
and business_name in ('回购融券（逆回购）','期权','场外期权')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="3业务授权指标监控-规模限额" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[1]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'量化策略、基本面策略及场外期权业务' sec_type
union all
select 2,'私募证券投资基金投资' sec_type
union all
select 3,'场外期权业务投资本金' sec_type
union all
select 4,'基本面策略中交易所期权投资' sec_type
union all
select 5,'基本面策略单只股票（不含ETF）投资' sec_type
)
select b.sec_type,a.* 
from tmp b
left join ads.ads_hfbi_risk_tg_limit_1d a
on b.sec_type=a.business_type
where department_id ='qy' and limit_type ='gm'
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="3业务授权指标监控-集中度限额" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'持有单一权益类证券成本与净资本的比例' sec_type
union all
select 2,'持有单一权益类证券市值与其总市值的比例' sec_type
)
select b.sec_type,a.* 
from tmp b
left join ads.ads_hfbi_risk_tg_limit_1d a
on b.sec_type=a.business_type
where department_id ='qy' and limit_type ='jzd'
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="2业务经营情况-投资成本与市值part2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'回购融券（逆回购）' sec_type
union all
select 2,'期权' sec_type
union all
select 3,'场外期权' sec_type
union all
select 4,'权益-其他（私募基金）' sec_type
)
select b.sec_type business_name1,a.*--b.sec_type business_name,a.year_profit,a.day_profit,a.loss_limit 
from tmp b 
left join ads.ads_hfbi_tg_cost_mv_1d a
on b.sec_type=a.business_name
and department_id ='qy'
where sec_type  in ('回购融券（逆回购）','期权','场外期权','权益-其他（私募基金）')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}
order by seq_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="1业务经营情况-盈利情况part3" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp as 
(select 1 seq_num,'权益-其他（私募基金）' sec_type
)
select b.sec_type business_name,a.year_profit,a.day_profit,a.loss_limit 
from tmp b 
left join ads.ads_hfbi_risk_tg_profit_1d a
on b.sec_type=a.business_name
where department_id ='qy'
and business_name in ('权益-其他（私募基金）')
${if(len(rq)==0,"","and ds in ('"+rq+"')")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportFitAttr fitStateInPC="1" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="0"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1333500,914400,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[432000,4320000,4320000,4320000,4320000,4320000,0,4320000,4320000,4320000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" cs="9" s="1">
<O>
<![CDATA[权益类投资及衍生品交易]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="2">
<PrivilegeControl/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="3">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="update_datetime"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="2" cs="9" s="4">
<O>
<![CDATA[一、业务经营情况]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" cs="3" s="5">
<O>
<![CDATA[1.1 盈利情况]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="3" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="3" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="4" s="6">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="4" s="7">
<O>
<![CDATA[业务类别]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="7">
<O>
<![CDATA[产品/业务名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="4" s="7">
<O>
<![CDATA[年度盈亏]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="4" s="7">
<O>
<![CDATA[当日盈亏变化]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="4" s="7">
<O>
<![CDATA[盈亏合计]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="4" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="4" s="7">
<O>
<![CDATA[亏损限额]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="4" s="7">
<O>
<![CDATA[限额占比]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="4" s="7">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="5" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="5" rs="4" s="8">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="5" s="8">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part1" columnName="business_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="5" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part1" columnName="year_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="5" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part1" columnName="day_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="5" rs="3" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(D6:D8)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F6"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="5" s="0">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part1" columnName="loss_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0" leftParentDefault="false" left="E6">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G6"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="5" rs="3" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(G6:G8)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="H6"/>
</cellSortAttr>
</Expand>
</C>
<C c="8" r="5" rs="3" s="10">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=F6 / H6]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="I6"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="5" rs="3" s="9">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I6 < 0.8]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[正常]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I6 >= 0.8]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I6 < 1]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[预警]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I6 >= 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[超限]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="J6"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="6" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="6" s="11">
<O>
<![CDATA[量化投资类业务]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="6" s="12">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="6" s="12">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="6" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="7" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="7" s="8">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part2" columnName="business_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="7" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part2" columnName="year_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="7" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part2" columnName="day_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="7" s="0">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part2" columnName="loss_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0" leftParentDefault="false" left="E8">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G8"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="8" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="8" s="8">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part3" columnName="business_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="8" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part3" columnName="year_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="8" s="9">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part3" columnName="day_profit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="8" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(D9)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="F6"/>
</cellSortAttr>
</Expand>
</C>
<C c="6" r="8" s="0">
<O t="DSColumn">
<Attributes dsName="1业务经营情况-盈利情况part3" columnName="loss_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0" leftParentDefault="false" left="E9">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="G9"/>
</cellSortAttr>
</Expand>
</C>
<C c="7" r="8" s="9">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(G9)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="8" s="10">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=F9 / H9]]></Attributes>
</O>
<PrivilegeControl/>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="I9"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="8" s="9">
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I9 < 0.8]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[正常]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I9 < 1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I9 >= 0.8]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[预警]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[I9 >= 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[超限]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand leftParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="J9"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="9" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="10" cs="3" s="5">
<O>
<![CDATA[1.2 投资成本与市值]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="10" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="10" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="11" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="11" s="7">
<O>
<![CDATA[业务类别]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="11" s="7">
<O>
<![CDATA[产品/业务名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="11" s="7">
<O>
<![CDATA[历史累计成本]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="11" s="7">
<O>
<![CDATA[当日成本变化]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="11" s="7">
<O>
<![CDATA[市值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="11" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="11" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="11" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="11" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="12" rs="3" s="8">
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="12" s="8">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part1" columnName="business_name1"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="12" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part1" columnName="history_cost"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="12" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part1" columnName="day_cost_exc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="12" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part1" columnName="market_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="12" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="13" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="13" s="11">
<O>
<![CDATA[量化投资类业务]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="13" s="12">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="13" s="12">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="13" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="13" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="13" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="13" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="13" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="14" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="14" s="8">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part2" columnName="business_name1"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="14" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part2" columnName="history_cost"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="14" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part2" columnName="day_cost_exc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="14" s="9">
<O t="DSColumn">
<Attributes dsName="2业务经营情况-投资成本与市值part2" columnName="market_value"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="14" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="14" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="14" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="14" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="15" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="16" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="16" cs="9" s="4">
<O>
<![CDATA[二、业务授权指标监控]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="17" cs="3" s="5">
<O>
<![CDATA[2.1 规模限额]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="17" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="18" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="18" s="7">
<O>
<![CDATA[业务类型]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="18" s="7">
<O>
<![CDATA[限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="18" s="7">
<O>
<![CDATA[审批机构]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="18" s="7">
<O>
<![CDATA[当日数值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="18" s="7">
<O>
<![CDATA[限额占比]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="18" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="18" s="7">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="18" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="18" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="19" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="19" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="sec_type"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O>
<![CDATA[基本面策略中交易所期权投资]]></O>
</Compare>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-661177" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="19" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="amount_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="19" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="approval_mec"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="19" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="today_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="19" s="10">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="limit_pct"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="19" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="19" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="19" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="19" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="20" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="21" cs="3" s="5">
<O>
<![CDATA[2.2 风险限额]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="21" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="22" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="22" s="7">
<O>
<![CDATA[指标类型]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="22" s="7">
<O>
<![CDATA[限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="22" s="7">
<O>
<![CDATA[审批机构]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="22" s="7">
<O>
<![CDATA[当日数值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="22" s="7">
<O>
<![CDATA[限额占比]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="22" s="7">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="22" s="7">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="22" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="22" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="23" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="23" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="sec_type"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O>
<![CDATA[基本面策略中交易所期权投资]]></O>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O>
<![CDATA[基本面策略和量化策略的整体VaR（1日，95%）]]></O>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-661177" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="23" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="amount_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="23" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="approval_mec"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="23" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="today_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="23" s="10">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="limit_pct"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="23" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="23" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-规模限额" columnName="status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="23" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="23" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="24" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="25" cs="3" s="5">
<O>
<![CDATA[2.3 集中度限额]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="25" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="26" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="26" s="7">
<O>
<![CDATA[指标类型]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="26" s="7">
<O>
<![CDATA[限额]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="26" s="7">
<O>
<![CDATA[审批机构]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="26" s="7">
<O>
<![CDATA[当日数值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="26" s="7">
<O>
<![CDATA[状态]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="26" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="26" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="26" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="26" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="27" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="27" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="sec_type"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ObjectCondition">
<Compare op="0">
<O>
<![CDATA[持有单一权益类证券成本与净资本的比例]]></O>
</Compare>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ColorBackground">
<color>
<FineColor color="-661177" hor="-1" ver="-1"/>
</color>
</Background>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="27" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="amount_limit"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="27" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="approval_mec"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="27" s="9">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="today_amount"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="27" s="8">
<O t="DSColumn">
<Attributes dsName="3业务授权指标监控-集中度限额" columnName="status"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="27" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="27" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="27" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="27" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="28" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="29" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="29" cs="9" s="4">
<O>
<![CDATA[三、市场风险监控]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="30" cs="3" s="5">
<O>
<![CDATA[3.1 市场风险指标]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="30" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="31" s="7">
<O>
<![CDATA[业务品种]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="31" cs="3" s="7">
<O>
<![CDATA[VaR值（1日，95%）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="31" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="32" s="11">
<O>
<![CDATA[股票基本面单方向多头投资]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="32" s="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="32" rs="2" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="32" rs="3" s="13">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="32" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="33" s="11">
<O>
<![CDATA[量化投资类业务]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="33" s="8">
<O>
<![CDATA[ -   ]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="33" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="34" s="11">
<O>
<![CDATA[交易所期权]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="34" cs="2" s="8">
<O>
<![CDATA[ -   ]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="34" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="35" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="36" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="36" cs="9" s="4">
<O>
<![CDATA[四、流动性风险指标监控]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="37" cs="3" s="5">
<O>
<![CDATA[4.1 前10大股票]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="37" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="38" s="7">
<O>
<![CDATA[证券名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="38" s="7">
<O>
<![CDATA[市值]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="38" s="7">
<O>
<![CDATA[占股票资产市值比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="38" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="39" s="8">
<O t="DSColumn">
<Attributes dsName="4流动性风险指标监控- 前10大股票" columnName="security_name"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="39" s="9">
<O t="DSColumn">
<Attributes dsName="4流动性风险指标监控- 前10大股票" columnName="market_value"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions>
<cellSortExpression sortRule="2" sortArea="market_value"/>
</sortExpressions>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="39" s="10">
<O t="DSColumn">
<Attributes dsName="4流动性风险指标监控- 前10大股票" columnName="mk_value_pct"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="39" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="40" s="14">
<O>
<![CDATA[合计]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="40" s="8">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(C40)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="40" s="8">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sum(D40)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="40" s="0">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="446400000" height="302400000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet2">
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="15">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="8"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="rq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ksrq"/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="184" y="25" width="146" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="Labelksrq"/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[日期：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="104" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="816" y="25" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="rq"/>
<Widget widgetName="Search"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="960"/>
<NameTagModified/>
<WidgetNameTagMap/>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="true"/>
</Layout>
<DesignAttr width="960" height="80"/>
</ParameterUI>
<Background name="NullBackground"/>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1" paddingLeft="10">
<FRFont name="微软雅黑" style="1" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-14726787" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-6776680" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.general.date.FineDateFormat">
<![CDATA[yyyy年MM月dd日]]></Format>
<FRFont name="微软雅黑" style="1" size="88">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-6776680" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="10">
<FRFont name="微软雅黑" style="1" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-9578958" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="10">
<FRFont name="微软雅黑" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="104"/>
<Background name="ColorBackground">
<color>
<FineColor color="-16731920" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00%]]></Format>
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-661177" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-16731920" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典浅灰" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="2业务经营情况-投资成本与市值part1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="3业务授权指标监控-集中度限额" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="1业务经营情况-盈利情况part3" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="1业务经营情况-盈利情况part2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="3业务授权指标监控-风险限额" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="1业务经营情况-盈利情况part1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="3业务授权指标监控-规模限额" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="4流动性风险指标监控- 前10大股票" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="2业务经营情况-投资成本与市值part2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1698737469371"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="a2708900-9290-4398-9b53-8c79ce3c386a"/>
</TemplateIdAttMark>
</WorkBook>
