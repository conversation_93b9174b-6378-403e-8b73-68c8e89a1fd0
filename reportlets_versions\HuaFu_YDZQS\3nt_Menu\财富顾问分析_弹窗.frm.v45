<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS (
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.ZBBM ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in('cfgwfx_gwfxsy_sjyl') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
)
, RQ AS (
   	     SELECT 
   	     	JYR
--   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, RQ2 AS (
		SELECT  
		TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR,MJYR 
		FROM (
			SELECT 
			MAX(JYR) JYR,
			${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} MJYR 
			FROM TXTJYR
			WHERE JYR<=(SELECT JYR FROM RQ)
			GROUP BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))}
			ORDER BY ${IF(rqsx2='当日',"JYR",IF(rqsx2='当月',"SUBSTR(JYR,1,6)","SUBSTR(JYR,1,4)"))} DESC  
		) M
		WHERE ROWNUM<=6
)  
, DATA AS (
	   SELECT
	   REPLACE(DS,'-','') JYR,
	   DNZ,
	   A.ZBID,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A
	   INNER JOIN RQ2 ON A.DS=RQ2.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   AND A.TREE_LEVEL='${level}' AND A.BRANCH_NO='${pany}'
)  
SELECT 
SUBSTR(JYR,1,4) JYR,
TAB.ZBMC 指标名称,
DATA.ZBZ 指标值,TAB.DW
FROM TAB
LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
--ORDER BY case when DATA.JYR is null then TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd') else DATA.JYR end
order by cast(TAB.XH as int)

-- SELECT DISTINCT SUBSTR(DS,1,4) A FROM ADS_HFBI_ZQFXS_JGZBMX
-- WHERE ZBID='cfgwyjywyscszb_20240622171051']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="rqsx2"/>
<O>
<![CDATA[当年]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="d4100abc-37e9-4c25-b085-ccba13704c88"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.14"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="1"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1837898,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,3086100,3162300,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[指标名称]]></O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="8" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=RANGE(LEFT($DATE,4),LEFT($DATE,4)-3,-1)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="2">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$+"<br><font style='color:#8D96A8;font-size:10px;'>"+IF(ISNULL(B4),"","("+B4+")")+"</font>"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="指标值"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNAME>
<![CDATA[JYR]]></CNAME>
<Compare op="0">
<ColumnRow column="2" row="1"/>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN($$$)=0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[--]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="B3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCRRA;s>5RlMMV09iP(kRZ&F?!\2Vk7Mlk.:dG8\M27:#@XnGM7\u&9',76H-q6fW6371^TJ
-WsQI#pM!X&QrJ3X/]A+_ShR359h.4mW#ic[iK/R%7mYrKXiSchi1H3GX%@8pmQ3W`?*q/Bn-
u=g_ub9(['#>2W#TL!FV;<^g\*r,M(NB]A^R#"FIlCM=0Ksl$'V,gN;l8K/^6Mhq?WKk[Wf4f
R;jf=2qs)?&9NVmmPhN4$H5b'@HI7c1="YT9W\RGlIK\Z/7KX4?Yd!8Qi^g$RTD\C!e]Ab)YL
u]A@BaQc>)tVRNmh2eg'.V$?LmK3kp`B`j9Ik4iN]A2mDUk'q:JViaHI)]A_MA1:KQEqV&p$0T2
\P:hj%_6NQo',o*8CD\Hc>.:3I/.HIOgrlrWA.HRgCn+6gge#>*WGoN?,!1BZ%5eVUL1d[[5
*e)>iO<[s5h_%0.Ze_k&.o1cAN-B*<)Qq`))5Y[+cY(rphd@7p3G1)nk;/\2sAi2':JBHW3#
)ePt6"565&jpA]AU[_Z.XIX7kVo+5;*"j+Un8Y?tALpMb/!P.Qi`^E1pN2;e3km]AJ!EB.tp:I
\):qOSm:-hA&B5H(4fBSgH(QoPHG!GKf8W0g77=/uM;%hn+F1Il[#>Nd#OBf?s8Ws.Kk'>ci
(hUbikc<XVnuf";^F<qt!f4bM"M)$+`J@L&mh)NT"HXX;3EhtFn`,4O.E\5!@g/'A&$5CUPu
1[ea>Vk_!8K(k-K=6I&KdqTFq/)$A?)HQ[f5ck09-$3Rk/#*ko7a&9`fjq3L2d8?!PS'nR>S
A-]A?l4k7/4RIdABfQk1HEbA,U#7>Gl^W_oZ([fhjA^r^3J4m-e70MO$M6_H@9/9r;OV:Z8Ql
\k';$c0dQE55K(:)ldMhc`>FIH'a]AMPoiG@,=h7ko;">N-rbju#4emW_57r<9#sk]AYZI.)7K
8B@Y<t@'BP&$a1WG('9$-n9/LXO5ZNKZNlL.V=`Ppc0g>qSac]Aq24b^&'er\eHm*BM#1\C[K
)N]Ag7(8(`3.]AW:.o8r%t[U=8>+$@:^,G8`K*rKYM&-Zu>DGW1<>"^sL2;<O3YYGf,VT]AiQZ#
p5IsJrbMV6E,6t+Q*Ou'ZsqIeUR$i/D`8M/:'J"?-Qf;%BJ/t#,Le[+fP\("3e<KPb#KWt.N
$qX4$1nu6*/gIeT:,=)8n]A2.`hgg7JkiR80-d%E[S"*Z2SR;J9kbI31NpSn$1cGr_/ju6gu-
5_E*?t4`hLRe`NnISX#!VD>?&cpk,3,np#n+5nl"Ugi.E,M$1(_B8"=LdaNQ3;C@af50nVG7
r"tkP=#@QP(;7E;*j!bGD1:625=2mTZ?K4=[Ws.8X@(XmE9Y2j=/aB5R?1;2uWS-#16^-iqu
"=pkiTeLhh"R.YG.I_ghO1Qcrq:8=tee7ZC0o$TL75lp=<O#.-@0k?%PnZn"(b=/jLRCOZ60
4HakF=(3+XP7(i.ma0stD3@L#3+=AAD@t2>.l@J(Z-JrCRd9<u`3C)Nc[7VGr#E2'G+CFZ1;
E#',gV5^)@@UmkP5JrfX5`>QBFnnGi]A6<oc?S;ho4l12R#?#noZeka(jfkaGlXW\DQ!UDBN*
uf-M:#EAAS/^K#04UqjNU4i'e?&t8Lad?6S7NY3>D05#8AI5.rGpqTnV4g^ZXI(WKCBKk?XZ
G:)]Ai^KO5S:0I7NVd.pHb[cU/f:<F%[_l.d28Z'G2I_ll%Pa?M.$[2ZO)+4q:5su5^96+G37
UgZ9qBEO!+ZrF5CT42]A*dil/5iPg#.iRUQ2+fOULRRWdL"SOKBuc&-OF8QWp8N\^dt.4ZaP9
@WDSD-iM+&V6iL%[V4fBMlq6JKD@HM]A!Qg:[r>\(RRBhC21nSmb,mJg_`Knie<hEUK)o7(jG
&jj7'H1@+=8FZmN"nUGO:I4/,l0lI/:\LCm'7?"cH*nQb"_a0P#&M_(RYlET:EPr9]AF4/n^,
cF:CdD?FrM/!_b/PUX`<>Pnp4*D]AI<7miS]A'JY47KHqKHR/j.X3[j[Y^>EBOW6B\bP*o_8O^
H,3\1bl'Z[IZ5'N(#R$l?!#=9eAIrG!*.S0\N"?LHjY17\qb4gY6Y*KX)'KAT;qj)M7dRRaT
IO&?#)+6`-ne&`A=*qcldlmZmC6Xn?G>"2%/IXAflG+6u9)T^8c=5GFUj5CV2(.WmuL3;iFf
r;*Vj%qiRo9JS6An1qWqI^qJ@Vc=bt<!N_gT!L=cCl[@;hU`iA<?KHUA?"FUj(Xp;-S-iC?T
g3Me]At.qM"Z&6pj?l*jk>1Cj,XBT8SSG%hi/UH&c:0%FF6Q<2Z:-\1%^Q'g"R3SZREg9iA9L
AqSMC`j!.pgN]AGf,9H[d7@kJUOBYZI!]A.s7m-bBD3B_3c*c>LP>LO1:_5C>l#iJUX]AY'\u5L
Z2V)_P2j]ARKk0c:\AGMdTQg%Lq=$^8bq[g^$[2Dhg/;Z$b,$e`oE&YF_qFjYj\Lb^IbdP(NI
m*\%3u]AC6CZ>lLgP'HK"b;b<ZUkbim]A[1On7d/b)Sk&ohiI0;3Zbs'dQ90@e&=_ijjB^.3B,
$f1lNSGG6+Onj_#!DAX6?k@Xuj$0YPr?2YTn1l_U_4_m^F+g16e+jgngOs3=<9(s2<??!L:B
2%d?j;5!RjAe^lHCT\1cM)+/oU$l\UL@7ZECmmL"8_E2nH[0#;&3CY\`+sCWhl&XiFP?<,8t
t;S&#)72=Co*l1%t>ShNYF,+NW0_Zsl`tMjGaN1P8nm%a6S[^*#TbJf1,2;'4qqYH+nHt2\j
"_Q=6[o>gC<&a0:l93Y(T1eJ^kXI?A*/-2iub[eb"a4`AX#&b6bD'XNA^SV/GPlMJj<N]AaVP
m17EFduCDg90+_"'"=UWWIA+F\,UE*!srq#6ZO/lRg9]A_MiX'-YN0-OGfPCW6a;QZD_L`)Dg
b'oKke4H[*cAlc3L<TIo%O5733L9g*P.0k]A*`!A<d0Pbng.7+ON8Q[m5/4Fb's29f*?C01b=
J?>9u&sFTm)2'mg(%E_ttq@^(Y+[1Gk*W*FG_Yq1ka,*Q]AVof1lehAJ/N54m`CK?:Cklf\$/
%YoG's@grnbd1\2g]A7hWq?^p4$J5*ed9DT(_EXn4<]Al&J4/LB5LI),Fa88bQZcHOrh:TEiOd
Ik@l:*sYtq63@e?-dJ'0)W2%`B3H?)BMmurXfAqE=s\ohi6EG.]A$D&9qVOQOunud[!*k"Zfp
2=C+YZ3aP*!n]A4X+[1:F_uc4?tJp;+9T`-H$/[=7k,fHOT@i/H9V!t6jY<d#a0L3O?"[JSC\
,!M#-L-hBi"eLd8N^q\tZ2Seb1*rN5s.6X5PI[>/7[SHt:q*ekdC#iX5;5=Kkr4(&pYN#Im.
WMt<h':Z'^;hb6!\co6(]A6*Xj+7Q0/JDJhI=pH^hA$6Q31LJ%27$cq<u_J;C0AYEeG/Z?lgJ
Eo/soWTeE,45&K,/]A=!)BPQ19WCUAU"g9k=1afr-#q"gtY+"RjlRf9E;HU_JslNYVYm@H-U"
_8`VWQBYe<bh$Z+s+'nd]ASr.Ea5Xg!HH"HO8mi"AGGcIl;8ZUI%a<-X?dTF[%S]AHT.!j3Q55
6?[017*`rEnaK_/n_]A'R4WJ+P@i`JaWSid0*5<o%D`?umZ3`bY/oZb4iW8N=1(anQ1iJCco-
mAt+`^f2Y,6b.?f%DT!.J(/YuA8,AITq9<347^&FBJ*bV79!uT1jclU78f'4Al"4B!bI40dI
#39^5W7_f$8'LKnK8fFIE(.UCOXo/;Q&<o=+ks7Pjh`DUHsXMP36$rfK`&7tN.E1Dq:igYWh
$U#2V]A$`9eE,;$'8Bni.B?P&_-Dduer>R%``Tf[O')DRL58AHs^9^14/&>sb?_W?#G]AJ4'Ti
;Jj3Q34K2&i^O?*'3fS,e[*,M_bt9,oZMi86Xjo&[HA<g7;^qSr8EE0tD"H&c8Um)*qq-p-6
O\M5Q1c\I4qT4q(d*U')+n=B4S1Ri[pOjgd43Z-VUaG]A^KL_oG$/J"Pt>&nZ#Tp"-:saDUYR
;Qi62::gNtI1IMSk-Va`L>c+(\ZH\8Po$5Tjq28D8n'utM02rIk;XP<MNJ";@bW)M;Kr)G!$
:8(dO/K\/N_!"kR@D(0XVi>9RWa)q1E'_)b8&l(^`qm/M!l?SH1.JIVDY3GmC$Ha$$]Aqoiem
hQI3qXe]At[4&*&k:24-QqbcPn.\E1(\MKa\e6tQOXEH?G):IfQ[K(<1$RF0(gB:R*thVRQr4
C_NeJI#8\@4Hs(DTHAP0L(+)V5oer+EVWb+;!WhAUhf<lMuE`8a`b1`."!e&LoUj6/63>7^I
MYNZ0iNGQ2eCNHLm5@j_K8;4fJW&,r=d:m*4tb0f"@*[3pjBds=__IB"^a+b*@7"8%XYmJNO
Z>&_M"E'\4=KHpP!k("_`%P_T_E0e6cs5Q'ZjD,"O7H;(\\_Qq0tPrZPT0u,?NJRj3&ms-n=
"]AH,VCdRU;;7bQ>>%:BXpn55eBuPd?n-b.!-s/"SfY0b7@<!5uW@*6V*R00@7ZF2U,qDdie<
8)"#*^BT!2!fQc/V(aOstE>W`D;LL3>!B&:C71)`Y@b1#^(E4#]A/?YOZG/..bhhKlBf:h:Y?
,9Mkc\[Gp!nO]AcKa3,l<D+TsZr:JTS]AF0T###C.SieF3;t?Skph;4&!2/+),CPZ3QTXjLKa]A
dH$^,sU'QK%$LSfE^BNNd:\*NBUi]A?q#H&ulFELuQSM^*BIlSJGSMhf2'oj?cM%+1+3L.r5c
d@>lL?Un@hB-l7/%Y[%a7=$q-Od3"YP8!etAIqLof@4l,[qX-gUa!BQF[ERKWcU7jk)j+D70
p-mKrRm'*-)>aQM3NuqS&u3g`XB*=65^lZTs6X(NGtVRI,kG+Et6_<5bN1+"7S`Pr(kSfgc'
+,BeE:RPj[YP6B3e&XJo'1kDI:rGYTje@Z4<]A<q?9]AKuN^aCFV7g0Tm*f.iFdnk=)a.q7/5J
.Z=ua8)RF?A1,$!<hO@5EDfn;32fGi`OOOFUI\q^^ho9]Aj?0M=dl#=Gcg/'TQ5s]AB1eLKam>
_Yo^GTm8+>HJr=Q/j\TcPU1d/1AW]Ao"1VjI'5:l=0ng!o/jTo@-s%:9j)<Xgp^0_@bh$J.!]A
ED[IX6PdM9jrEdU[*kdej%tDY?;tB\$.jY9K4c3'k_Y43@+Y':&j*>dSha8Cc5j>Xl_6aQ9,
]A7`R`1$b#.*,pPjZ"d-YNk-W"'SFL]A33p.&nOC^Z]A/b`'G8Eet&R;;54T/dbSA>`A,r3Uu3p
':IRs02G@RE&M,R+J-5tYC>7>@P$*2MIoeHk"OAm)/*d\7n80:hq)'6Rl/F*a:V,+a`,4FW,
bRZf#/K[KfU3%q=a^VU'@T!M7L+O`At>Nop>nK"5l7.tX`2u5j/487\#8oJ7(ssVH\G"5j5_
,<6P2]Ab(;c7B1Ncbt(_&lA=t_j>ih.Ml0p!&ZX49HE$$uXHojDANpcRBS]Ao.usG*l.\HFHmX
&SbW"N82K[Vs/6(/r?J8h@"H]A6&`L<'BbbY5;*69:M/@U5ND5gHh7<.>u$+^e^-IM56I8elN
9C0p,6sSZXR.^V3U>>fU3ObEs1l<hb:PD&^R8QUDf:6VD[Lb)%\`A0k[Z*H`t&Y6V8EDf>U`
t/-W%tD1'n$kDs!=.WI*=R@dn$dlXBoZN*=`A]Ad%5,&<09!DeUj1Y:`G+0LWa'on4]AXolo^D
j:EUh(-:)H%"o$7/E]Aj*pqGtX^TK>fi/L+<cDZl7$ZBMO8^>:3pW9eb5#jq6&`aQQl,fu+!B
b]A7BW*_gcaiGe#]A)N3qeWWl#MC$]Af?eVa8#6'SM+7.SHN%>GXQ_Qic6:@.^@q=Fkd#Jrij,<
\B^BtQ%968J>f:]AfcAS`WtR+fMmTlMY'V/941h8!Oo]AO/I5#:]APBtsblTn"]AJG2^]ALY7CE;,
r(qF7I.aYFei[c`11ETh$CcD0Ij>.M$2Sgt\SmEDJ9+CeWcfLh4)'k-\3&MDW6m9.6pC<>n2
@)[<EMAIL>^)iRaR5(4#ln2^&pgjq*g4Geo('6'"fX&[)7ZH28<h*)S@Xc#rP._"s_5
j2-p05g]AS<WF*^^L#Z>QJS;^p5IgbY2_&So'+G<R2Ith)Y\oUCS=D<R33Ls7AL+@jfI2mYlO
:Sb$i-5kt/P0_Mr3t]A7Zf?D_/6-&*3T_ZnSbKo4?0>O,Jg>o-VrQ4#rogC^U5[OnW34k"$If
hM@bJ#`.nHg&<QE0+5hhWhD@jqnl\i9!WtD)t<k"ZV.jJq9).T2J)852a)>)@=gI4_rG79P6
I"!ZWo-bq)<JGZA3k$?3?l%"GTi(7pJ8Z^"]Af>97,Ro?CH/kPj[M!mVW/sYo8#I\,j:AKum[
RD1HcmHMb;T_F4@?7K*66\[>)7I.uKH.62K(=-*Bj20s7\c8P^SJ1Go[0E-Gc2pZ6gF^Mu6<
KJ8@XEVP-C9s!Ae%$>llX2W"2#glllMY1g*DA9X9fBRPbJhSLd4H4hCot>2-6AE#ZGRbj5bl
tWoFiDtVY:`]AZGF%0a1K$2FYf&1na3AtS*T9AD`ET]A'oA#A0O%S)TiOQY0A=$,*V+GV\.u1A
cj2B9mWr9^WkgeEcWBiGo:/h[HcieDG>bBjC=g+U%(S^`nfjSBDD^1d/_MU7MWPh7qV]AUFM?
69r\<gju\(kisZ.j%oI<@X!&fUA^7J>L"=ccS\LA52`<]AsI+VfmCT)IN9%M,/o@0c55lJ*fN
crEuo$gliS/m)Y4q*caGH[noi-Cdr8A)$F%KQgI89k0LD@kk8>\:3BL]A6j(@RnuZ[qjWD9"P
iifqY1oH*$o'Ap19)t4+q5F`h`HTKY]AMg*!1RqqA4&Uh?lqo"4hN0QTrIq5\6&bt7j1u4B!7
-QN0E=<bjod]ApL[oQ;D^rY5BERr`[?8$$Ilc']A/aO!RJBlDk70*$_*(%mHh$N/n^N11&[l<D
<]A'N#4FFSRm[\]A<"L3uf%7B;3DG"1Q!,.@'pA\mIhqR1.M#=5U$kSO\3F.W>kKE_>+s8tG^&
O1A$ZklFUZB0r?Y.(r\<`D6GnT<3TVOFo^/_)#o]Aip=Mq=<04SYZ[@qePZ)Trh$E[lDKf!42
.B2>7QXL0VY,4FBfM2aaL>>6%#E,=;.$YAMe]AOG3[N%e=HY5+%2p"1Qtm(:J]Ab\.)-CJ2X./
75;=kko'Q:h?S.CBSJ^bJK\;qnl/8e3qH2Mg53\QPr*.;!?!=U$bq`n$fPubq7nq-Jqr*cXV
UO?J)$"Pn35B,*=.;FY;7?IVMVGP5Zp0=6ZM(T6uJ53NpSl2a:')4"p&@m*>R/J$@&WVq/r8
0;+qncp=8XIOO.FZ(B>Os%V1f_7[C.GJ!-P5C0C)idmIL):#6Do>kTRYHi?KQ1$jN`su$o.-
2-]A-B`W.*.8fY"eM@%U'tQ`nqe`[W?k=?7t0HT8%;FS>R:fb0ViUV]A=UG"^DCJ(^Q`9190fh
[NK)'p_j;s3M^a[fofjo]AA*g;DTs<%+MiGgaEKoX@*ttX2Uc\m$Or\nOCGj$m>3l)Mk/s)=.
!eC!&Ih^!k%6.!30Q$T:+!7)_1$L52nI:Fo&V6Y206Y!;+%h-';)dW(0,]ALO[P_C+&53_N[:
;0F7`[E]AIu7.'/72cA9<Q5]AmAH2BBieZ\dEQ#6<,o+\0Roorn5EJS8S'Wb1^i.c?;23mp&k*
,l2nD,`:@BbmFFjiZXgSgnG_kAB[RVA"=$XZf?J#PLb1";Ws08D8S+0,%ls=(eNk]Ab>_PV*/
/PsW;;afPQpab2QLl9(iuW5^2N>HXo#'2eV9HF3L#7R#"X@@J**n-pUH8-=D5Y@h#%]AX#qt^
"hb%01EU-J:eY8Xl.5BRZ:&I!tNIrX_jm^mWPB^`nSf?Q*-Y/mc4+/sm/dO_u2EX>NCAl/?*
(HR#i%E3fFun`R1%1]AGIF>;e*4$u>]A3`;+7uNqE8TR79e"RrjJ"@%_l1%OkXS@AbI>nAY4\3
)ei7>C`85@[^^ajr#Re&6"`_h89X9WAEW20T)'2]A\_%/.'"B4323)-^ru_KpD7:-tC%P)0-k
&^)TNiEb,%Q<i5]A7TK"\m<SkPHO'kDXG$`ZCt">f6bBB=icN-fKcJIlh@e!O&sS5@h=e`fY1
E9-9tb^&i_"ff^5(P(D-s#3[kEINR2%KMSHb:mh75[)4)hRIXZ7n6@snQjLB^@K.2"(`X#b&
o?%`g<:G,6J"'B;jW:t;WeU86cXc?!/C*O]A?"K>QKdJ0QW(8'!1eS@+6:0C@lQcoA+hc\0d5
e34n3h"Xfa,gmV1MP$i3sLN!BgF8\p3tcZpg;tXNAu>FluLG>g`'NQEk^`c+BlW9A3\qB+q5
3'E_`^iq@(:"amQF1G7PoiJ<j#'967O\V4M;P<+H%c9M`pSbBfa-?n#YUp<"us%BWo!4H6O%
MU`eoH+,I+k0iuBO4pER-Z^dbX/6]AIl?f)kP.:YGcgU^uiU'(/1$t"8g.)$"fY6X?D43WE\m
q;p2-6e$A/oobaaCn33q*!q0Q")h+[$m6>jHhV!t@m>UY@Z^mKk"kXsP+r^2\e^k?"JfUQ1J
d"H`1"NeL<?9g<2Y0+]A`rklFrFlAd=,eSpEN:Lts7a:H2Q8E%+p7NOI"StVuueIb^66Q.ZR\
dRUYTSQZtR>G@kQ(8/m6)4A9hC;mQNUj6',98@/4`t1Si`QI^EG<cnlI?Y>..aXuGfjZgi=F
W#bcM-o&(IR$r4Ge7K3:8jYQk`"Q.2K+Jh4jJ+D/)EEhagp[Ob)ilEn+ir%"UTAXu6s+=+)s
R8$1Y@PEZ'r=iokDq4T(<1.Db\F+QHN.q3f'NM_pOli.Op'9W\''[dm-%6)LD0q)A$+k4_)>
^J[n\Ph\lMNr(BD,E-6<095>]AV(t+sn9cU4A2O6.VoYVOQ8ZW_I=TA/g<9e0Q_":ACIDbRc7
Jgn'bYl/(N@`+X!HUUO.G>Oor63\n`p4j%j>?e\(?]AAiSGW^E2)b.iDTVg^gD\n1KrFtj\hf
.M:lr::`'<_f\4b'fO;n%9=d_Mk'2R%t0`,NioV9E9?SXU+G0eYSta$d-i<nB<bA8Vh!Aghm
R%<ajl!ZZXFEl4#rjc:&$REkYDJ0a*P)[Qm7ZOCrHIeHuth_Ag_uU-sW&63q5;W3LIVTM,j<
Es(AP)8A3:U"kT5&*HBn2<q-8J5cLob9[0s\C.%h4__q?KU5reB[XG?hBKa\21%ZSU%dd'nb
e@BqlWr$)E=L&R)1.Fa.A-q<I`q&`60f=)6F=^"2X)P%)>1`,^.el\+-"PmX)q0dJ>G&E.HB
o&]ASB3LjE9CFN71:r@j(=Q'^ogj+%APNI9dC]AQEdfg5OXrcdL9*=CY2co&_t:Vi0[EN>-1='
]AIt\)IAL?/7o[X\&Aj`Kt6U*\)Td(B;4DYRDkL[GL\`9n2L+`('-%:k0>R3Uc_SebS^M2q2k
U?27.^BoPkY_SC<:%$)I*&b0XlqnScDjID-Q2h-#,,N"R=Vn?5Quo5@<EqBDBg'nk9AT3ljF
q,)*>[/UpW]A$aFYJ%gsaDs%7ql_'5\H1&dsd<5P9Ia$'hHhpTijn&Vi"CFq=@g70t/L:;[2t
3hRJ%Qp$qIc1c'WQa%I(;9ck6F;V]AO[T[EJ`uP=jL;jD5!nJ-DHpiR8?:1hc2Z1CsW!'kg76
gebBRb'0rXdf=g#&#S<A9=Qrt2beX0K7]AiW9!pK)d;7CK<M-*>Q<lhO9e$#77;"q^/$LQ2ep
1S>/\PjK/jMhkBnn`)U_3494(c,W>Q#A#VLRq[qWT<oP#.?\IDqee=5ec[r34,]A$`:VgL1bR
nG=D$6cVT5sfNX-@1]AV'6@-qbPfZn;fT7pHbMp]Aq+DEV`lZX.K&Wc4<!mdj<oH,aDDJG,01$
o*g`#&6#daWP0PT7P/K/l'n3$38DdFXdlmY1pbLDms:^DYV??S$"MUC.8\4_DiEmn*g!B!PH
[NNB'%SK`p?8'\'M7<c'd!1]A3Gd=CZ^%+:j>r(fg(2U/H8s(G^e*a?*%hb"6Afj;<dc<<#$P
62WFE8[`nT?15GUM('ns7-BRBmaFfM%eK]Ac^>lu;R]A*Ws"C)+(6#E/sk-3Z&m*D(BZU.iHHm
!%!?DAOkLASb!+-`h0B!>i;0>UI.Fm38#L6W4"-5pX;aUfRpi.6Ng-Equ\U+loG;'PE]A]A-6$
p[;Nmm1qo>m/Co*tU35aq#_6bUfa2(>'fB)$`4_,t,WE3Lf+=c2hB!dX54`9V?WcStW&e$*2
j:A>AY16+j5a.g&Y8oSsT+%H+BQjrFI<^LjJ<<R]A1\s>!)1<@\!KL?.N`j@gFW?@#.aT:VRc
k;l$$Vto3HZ8!-aD0GT%Q+aKlZt@"iBhA4`6hpSf,gE)JP<aPVJX9C)lqC^U?%STge/UV8cA
AQ$o27L5%fAj?nat#eGJI%_fVW.]AgE:AqMUHZ3Sg7%AYb-5?mhd#@/Qb_Hu8V!`n5[%Qfio`
3FVINr]A%Z,aG4XD%gK#00;#.X_>-(<B]AFnBbkhWHmdB/P7DdgK%Fsf8&`^l`tmsM"6%P#F.h
(fSBJ%2YHm9a[umJ=(?T`K@]AGhp4SO+!d=f>mZt?^]A7JMDLL)_Mln9s@ZTjJR#^:74)%Lo;B
n`Cb$.ak7#.B@[<gZ\8V9P1bGSH%7'ZchFKRqa'o1fX^>ZX2NqLNfn4Z1BdL(/JJLb:eSA9P
$!%"b\:e)8u)!>npT3X%@.^)EW%B.&jr."tX]AA<;u]A?Z.MN'pR27>[?GSlk@e]AGi[TVkG+g"
"Z?t:?n,a-=IQ>D>)BfJVX=\GgP4R"SV*GUSA<XkR:bG#W]APSoE[9.'FY'9hNV^.]Agrq^9**
%B_C.C*]A3kg8.l>AO9WaEtq*l=3<:>e*Q(h;%dOGH%'N+,s9q:R(W8MKY9?r#TE_0PcmrVaj
StiW*$c(T=[XbgThSS`"?Wh(%ealn&N`0'f^lm^/m@C7ClsoZD#R4c;`rWX,6%#@Z)EoIFWa
N['f2fXE+_YhlA)_6D>a-P`\q-G.Ijid&#tCX)',$\LS`D2q]AkcL!FuRsefT(m$\koNQo)l[
9mg,DJgE5?s\PL9@'bC@a&[Mg(&!K2@Leh,G)311["fBLY]Af[U+Y/]A*=Dab,c=t!ZpRI+h]A0
G(<NPD.,bUV-nHk*#;J7P;(&mcKE:l1nG?8e/U2BiSOonoAf:_e2l;7)MpRG1V01TVHp([A9
N[\M?S-,:kV1cL2b/frDS5)[R"diqi=Wf-'5QO9@)`g3'0gIDH1^Dn03\F%37p>DlU1^Lbl_
]AN2sRW!h]AH&M:glCq_'lhPeFG\X#)9Pol`:ZJls9p1`s%PmcRum;`uT1tS&9+0[bLSD;<&*[
;IRitZ8UWA?rs&`'buJ]Aeet]AOdX?-dmP2UK)+pl`KDtF9h42X^gQh(+80B8V*FR/7Pr=%8[Z
QF2f4%:Of58T&JWZS]AA.(0DB:K+&Y!NNfI)<f;N@T8=+kL@2ZapG2T9rLD_@N1.#P*@u<cBV
USgf(5h^TlG!srP>8\jS@EgcAH^dNsbXuLTshs3,Il3_##0EnKI4o/9P1:O8;UP#m]AUZE&]A0
fRjA`l&7O]A[PJFi.B!6HFU@rHL0SOk(F6#O`gej%)ucWj$+i9pC;0)F@P$'0bsOP8JI6/!Z;
Yf2"DV@IdBO41^V=T'M$ia&tZZOU)CG!;:L0"jLnSOqh5.;gMN01iM1aR-)n3u^Qo"'*Vb_g
q>6jYSRuu+')bntT3$:pL86AeBJTNU`pW*l3hsorl,;Oh@j[MQFEWt_BE=osp`84aRD3t#3<
usjKH-Q*4#hC]A&kPN6*hP5bJs+DN9.?:u8(r3"NEpE&P-NtII:RSsCqld"?Fk2[:GWQcP%EV
MSsUp[EWmklV,G7]ACU)nrEDq1"ANA'GSYkP/)Q@?Mq9#2,Q-'R[Vh5J-*VnA45WJ$E=A,T)l
PYL.MkuF'L/Ci0rJ-7[#R4Y9I>*c$#72g1%1g4QB5hfCQAdFeU7P?pM39eu'Iiuq`q0-RdGU
kZ^P.-VF!TMik!RjnGL3eoVg%4gB9?MsQ9SEe!R=HFN$Nh=fs.]Aj/h;<mlu7Mo?^4HBlSa8-
$E1Ein!BSa+[`Yj-BbMd_[F1IO0YXRloP0A$n^\g3K#.<AACG95)+%'H*3BJkk??^UejAD/H
C/@Kn&HY_TXA:#>kJbOV!dGk?Hc);U%g);_7rdTf<'e":5j7Pae:kG%RX*:"30l@]A"LpPiW(
Sh$PFbfZBQ-c!)7cQhOb.W69A?->Mr6VkedCL<t`g10Q*.MeWH@4qPX6gdgP3:M6%Ve;!#:V
KDL)\kXVDHVYcB*kbZ>h'@dE.<3?6#ptX&O$<MMrqnT"Po&H(#lNd'5:0YncXHq710UfU[c[
**T<0OK/;073d>)ATEX9sIUb'4D)Nh\I.+"##GrYT*gNAYDls:G$BD6VBXPXM2!tY0>\=uu$
GQc*EbT6qRQ>c68H_uD%r\^V-E_F]AUqHU0j<$T"+?Xpk<ZWSO[&-/-<jCca%,3imqM-Ln^CA
d\clGb!lqGZo@9m_-t2&4Zkk.mI&;FdUg7PG_DQ+tDl+4\*,),9l!M^R6U$$Lb^esZ)up'r$
6=C7AMVVLFGk\*(?fqS^<%S"Q0M8DgGdBM*Fj>(r?:;Ck@0_Lfnn@;).nQnTZF*DqWe'd*tp
77(*$73AeQ_K'ABNQ`OlUC!(CAu^1g`;2YX!\c$6>4W36'oY#,2_CbOM=!]AAFURFOWLl[<TY
8[\'"SF<@Sta>[2GS5YFN?D7699B.kuLVn,iK1tJGWs+KtX6;i6G\"3S02YB,hnY9W*'gn.,
NprS%?ngTP:\`$8&&>adKbM6;$eR_5W,,D:$]Ao5OH#jY)/eQPC_\f-5bLN1502OuEqPbQL?8
#=ZX1cD1@_Qn;<f:X.3nBJ\0.s8,"Sr"epggV(CZ+,s'U=FW6i3'_Yf1o$3+lLc"ls!$m>(^
4ctd7cY<?b:?=k'SH+(cpP+7<&:=>]Aupk`FSWXr)od3Xdb@eVI'DG5E:Lq-?lFSb1R7$IQ=C
mVD&"_YtXTa5]AC_:V?0-oH,C=b%4h<!+)D\-'D:-24/\)Y!Uka)iMeEc$8ijZ9Q5_ZsSTOfg
=Qa$mRUkm$b1Hbb5:@`P6<74/>Mbebc1%c##U\@":A&kMWus8<(Se2bpE(k=(6kfhVj<JJ3j
K]AAEjC!?CXB*hJ8nbAi.h((P<Hn!%!*W]Arib[Z5n3Iej(RZ3q,=J-*#5#LpJ<ZcXej(B$eZ&
rU"A8)[VijB-H?n9ohDbZ?19Cerdl&G]A_;23q!>b"i#g;`B4TF]AE`=]A%f.Gu_=>Rd_HDf1@=
k0^?KsY(A;JGVnPQ.8jBl7e7*_+jBqJd6dg?Y*lg8jPT-jc0H]AfQCIT_MO>p+s8>9_mh)7mr
miJqX3'oNQ^)i$@a.-XXnUu?,Kp=h@2-!CKudg1,f^QOSTU6$I\foPno7d#lE[aV4/Gei3a.
Rs4"0mgL:R/%QYSfXbo)B&1T"L!k)NRAkc"-f8JH[Br3'U:n'JXprRg\(_2RE@*Q%jHd"AlX
'ct,HTdj/"QEP4n%f`!'lq$nW;nA0ao2NsMUL2QrQJ^hq]AHI(]AodI^EiV%Mn=%ILl8njFXZ:
3M$:X+6foY"e#inR:#%bj3[Ui-"W]A8^k0?G+kZ26F$[O=TESJ?toO>.rO/V?Mqu$n(KG"Ihm
B0`$kO,0Jm$'O)$PX`p#oapoWZe+a,liIXAbj?.akdubsG/u]Am44e6_3To1.iYs,>'D3`W]A#
e=[9(k6C*"<#o>3+V#5Bi^(Wr(h+3q[\2K:kn7.BfH*/+3l0fphKKULPm!`rqTW`!8kt1b@-
$[IcQr@J6+t2#bu<s]AcV&bPRU8^0]A^mZeAi[#io7-mS5Ll/4jN#E*N)IVN)6#'E4YC^]A<2cZ
ff/Tefr_lPYDg&Xnm)$tP6jm@R9!$*9>&U-NE+"f*9gEX$D$HTURE20"4Hm3DN!@h66r6spZ
[&`)&JI"$8\ipZ\6UdI$!hMSlToB]A_i>:p(F9QJ(`m'eSOSj_K7N#^&=F)=TGk;R+=0[^bPX
lQtgk@^Ed(kbkq2%P3^holEF=>D?6fo3\_T]A;V5gK!ZQEs:-RU$HQ'<_<+ISUi,m\8HDGNE'
D:;QZ.j'[P5"Q!:A"(W$pp?Z"aV?G7$inS&N?JW/+[g#11]A!lYD+AaZj3H2,@H[+Fq,Q(O/H
ugfd;PM9X9ZbR)?u@oLstiIUTRc+j]A.idaO+/((k)>ibJXIY.XRGIh0/r_Ljs=Z^8F-@'SKZ
*0V"Nc8[sE5jk8W]Atn)Mo;sc@MeE655^?>MC,jJEq4&sl=YOLK5#f'1?07*noM$F5]A,V0!V<
Q=(j`<dX$qlTFiV9>!\YsJCC3Dr04`H!]AP5sE?l-6'snD9E,[Ama5L%ficbuB@(Z2Llg6"K#
?[9?FAK.sIom0(B%9*M;?VN$ApQp7nUTQe6`pM\r>'$7u,R>,$a&LY.E7N,F32]AS`\>LD5Mo
u>m8]A+iXXMG_gt-lQ9uFVWA5WsQP=B-P''C;?4J0Nl?:otc`BH/TisL2]Al:m0:hb0;Nl6_7?
(jd1id8`4tUp/DU')XTKj$5Yu+Nn3bcPq3`Q?/9Ue>H(KIJ3)TE,K74jAAB&0rpH:MipGRiI
mI`s/4VDjD9FW\S$WXoh94pjD(MGfq%o2u!$4fsk@R>u^:6MkkL9ZT(@^Eu9EF44k<d2]AEBo
e7sj(>lSbnkNVk0g/gNp,,sb^/]A!<oIm99$8GHpOd.::46it\l4k9cC/C"3+Km0FXN+rJ09E
Z4P#LpR;U7VBFr&pX3.(T^<X)l8i-.XZ]Ad_Hn>4H,X-/>'&CuB["mXG%U2?:`igKPFrbn,e'
m-SFDIll<Qho"7;lpO_:Hi3++kRc03d9$I6#lZA1=DNG?cs/,1A8$nXQ.=K!0t`*A\[QDj7/
t11lXYdTEr*OH1%tf^ctT+F8i*^\p?3HU4R0AH-Wpg'$TSdr@9o/9#dtf>-L)Y&I]AUmbZGCn
SOkp&&RhKRIGpN.lOcM<&^s^#GSRL7[J6UbX":-GfFbI:6WMpq+22^^fa0r%WVuuNgmob[MW
s`Maj(G2jRm0B:+feZR+4jLrPA)#H'FZ'o3Zi+-g+hUBMf`Hk-%Lf8"/PBcB<b*PbYJa(=&>
t<st'K4!-p0,?(Lk>rAdpDgc'#%>G;<FesaO*f%Qn2EF&<>M7XDmYXaLS0o/Zpc_1Brh*9m^
)pNaIr.bLNB3/DUh[MMn0E>"FSOGU&cL?IDA*/k+-]A7;\pdL+$T+^,cSt;Dd"hl>7D)PgXbX
c1^+Y@lMMMEU3/9]A^f0dm^A8TC!DMb>-/N#AG':l69)-nI%r8N07btX!K&B%4;md,ofnB[ej
K=^h?MGQ%XQPPe:lYkX0cd:7XCGT3J=e\4-*l7mR.D+NsFm@'i#cP6^p"7"\JAG!0*=L0hn.
5k)dg."Iqo@S4G0X<@$b:HilEij!8D*U;7g\kHjCq`f>/L_%Es;6,18NtmJ/.A;*q5ceYfi0
hV=#.oVslf)=qRVt>`DN>m,$7\qEbae?l7,VqY^1JV1,-A\(?6bl1:;%39$C>T_itms6;%[p
YEQui-3'*@MZj7'U\kVZB'j#EpD%/7W0T6e]A)[p?s6<o9j1:6)>(SKXa0e&c[#1<0Ah^0H\%
VB!Z>k(a7mI>[,s1QE*=gJKgP%TZ0ad,p?kT"lOJk(XT=J4_5S?ugTJ)?gQb?+>iV*@e-=b#
#`\e#-O3Wq8ZmfH:k/>h3(;jr9NmcVTg8W#eNNV1Cq@:ZlpNAZ4k;^nJ*g%_If=[K;9N'em=
jGG?S"Fn;/*Q*>7$&S]Ac"O6ET_O\Z3h^H.i28>Y$>5Bq#?P9HmE>[nOl,o0C5]A'[)r;_(m_N
'.\p+Y3q<0odehp$g+&^Xh]AK*)>Ciol<a[G^("`k0"h"J:JZZ^g?n8GrfEuEu[K9ZaXhapsJ
o(o,2ZM3!)&%>.5$D=#C6_QBfCSU7rf$_tC1GfUh;=6)XQh_DG'\?9[K9ZaXh[tF/$Kc@$>]A
mS!aC5X^fFhnYSGdtC^%6K>ClaF#lDftE6jlniN07]AoCT4&=3@NnYSGdtC^%6K>Ciol<a[G^
("`k0"h"J:JZZ^g?n8Grf`/W=;RXGSdI6ad_Htp/s%uuLX008;YC]An_n)"]AsY;m4<qIFU$qM
u9<bI@HU~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="732"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="48" width="375" height="732"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="1d53af8b-f2f9-402f-bb13-bfa82d2c81e2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="数据一览"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9".'P%hO?B"mQ"S8=Y6EQZbS1m:Xo>%rj!TG]A7q.\4RQ/HHg8'.<EMAIL><lb2\_OWMl4ZAh
'a8.#MQ,nc3>Q,cIN9Ukj7$:f,duH[gh4Hu&-BmJU\d[9tS8>jq@"c[4RDF!%RJ^%mkIqkfA
-2Z`u09h16ar##K:B;te.FhRJU\bfqK!8oVVo\fJAie]AJ/kd$k(_j]AckLK^lGWVlV4H@s+Bh
6:eOrbh>LbB`m4LJ8"WrmDlrs2hO\>^7j5jaM">&LlbP@t&F,^3]A$&oqU->F]A7JLD2E=c1qJ
U\Pid"FFI6!^L'unoc-(9M&Tq`mL1X\Jb:;8T,O@i&GVC8+9;1_%RR,nK2mQ-3gNK3R%f?k<
fRp`&g%K]Aa1MH,j!8uPS2]Ag]AU;lAL.aOK%6"Un&[qm^R"a85j%2f.iCp*#KUs6_&eG0R%"_;
;WHq`;/f^'=W@gIAsU4RdU#47Os(^jS\qdD48FH03MH!!!&j:NV?0-FOlt!%n_(Xc#sQ#<G>
]Af.JfN?V:r,-NCe_':4:M"4*pn<U0)`!9d_]AGqFL=cF3pekp(s/CK<0QQ'i1SmbGdg]Am7OB@
m?T(*hLKr)H8P@R.g9`hs^&:8LZ0dRr7$8or671c`9.JK5?#o<'-LFP*Qn2BBuCV!toaI-m-
pHlKeg[NMXQg]AppW&cHhpP-JkoSW)^A4[LEso94KCRF$W=t2TKYjW4G_YK"6I=5+Qs4IgbA/
j`^'YCn4d\/)7VNgunsS6'.qn&c$G"WN.PUBVo0l-H*/Qf^.=5.`kjKN.t8s-Z^<oV2^k]Af2
jjlLri=1Aa49<6S\+?kC%BA+L7S2T"em,<6/I%p-,":/gZtt4MPK)]AY]AbM*!@)S/lZu[Pc0C
]A+Wg;9EoK@KF^/G*HB[V&lp>=#L%2'fqge:H<M\l;]A8hC(V;(-_T%)gpK'C6!'JZTD_;.3sL
sg1$WMYMS\[:NSCli*S\=C\NpPtk[mE"p=/m5#5*$/nGqF_#1[c[otB@2kZVK.2lUHjkcL^K
Q3gG82"j:B[pLQmXBB5>)K?=3OOB:ZkOeMKdFBTYE1k9%V,8R.[*A;6q5Q#BGgd&q0hf4ID>
lAam'q3(78?!B0nM4`K5T""'%eYX-jrLpJ=<p.^+\q/=CWD/P^6KRIN2TNg0#"'>ZLaW9KAY
<$;>,/1Sm8bKdj^=7A&0kXS+IT!oOJ2IVPM(;h[*_k'E.i4_#_<>WT$JOp..Vm+Rat:R"Oi]A
!@R$l3]AX9k6b^<tHH;]AtKrW-J1rG2SG7m5lV>\62<Xl`OND=u8_D<Jps6-t1>TC2S?etpd##
JNV+lt*:PK@_R+85U"q(nk>j`)s2M.F*hZLFqZXWZdb/@rN`6O.n+^;3i=TKb'7&<!)%"9N@
ER\]A*i<GKK&a#-Tk*=Wg\qUh.GDHlcXJIog.m;)I'?JfG[P]A<uH'Ag@6sJuIg+Q82Qh%ZFKo
qFWUdA<"o&EPnSCg2NRX^d:1SaL,,q]An*"n<%o:A\>neFq$TB;LU2.!O)k)=kIkeV27.!t*-
F/prp2U$1t[6lj"O\!T'l;8l%a>tNUq!654L1\2Bf]Ag8F@05$pu=fM8Ds*FU0`IDXhmN6dRm
6*ad,-*hrXF>D*Z)=.HB_UD,498\Si2llkFp0m%l]AJ.cUB,PbE"fB'Z$gE1fUD&#0l?Ef#7G
M[gf:+ee8,cWkLoqMAXXiqZc?H<`b:qof,'4r`Kq96R;$`ge6Ad^u4Qd@&lRi9$u8tZdC]AKk
b:$#^pL5blB-#3U1\5"qgIipX>.YY\)QD@$X9(;lfC?\79'd@o;kbl08LI80t2GK#/jJT]A@V
(MSM,Xtq1[`O[B1[e/A-+Ua<'TNi>81,>A$pD8JV?[,u$q,`kH?Ds5[6c=mO:<RQ2IH'joG`
><n3)ESPXdh9Rm+B#?_W6=m"bk3CiF9O\,>9LhA(6$Z.Himo6E6%93B.JmmNCOk`KEOtK/[;
[*Nn;5FMt\gKE4B#>IJhX$_:i,?Fb5!=%_2A^_5&1+`'2-]Ad3UYZKaja_b\u$KY_@^3M4o*P
L/_Qd]A8t-Dmt7XetKM`2r6pTGQ^50;hBIfP.<"#BSPb\P%lT`[jm,Z`qsVt:6)(>i7FP^HbQ
c(c)28)SFGe`!G(H29e[4SbjX`j;\GN-?p1i)^nGs^5p3I&8nP)gP*%gS+DR:fH_Ss!/m/%>
>HiV4l!Vq`W;H/;Vs^$HKm6r&aSp4r8/ZVg'sjJKN2;8^gG[OD]A!VDcQc>+T7,5gr%2*D;_L
C(rgdqDMI^ohMU@ogbo#=Q"1u:5e^\J>gHULBd1<]A&#R6@sbbe`?;^TW_(QXuA2I[JUTLMte
6>j/F9ZVc0.9Ho&/]A3CbjrV4]A!G>VQh#dCrs,OHod%(]AHu<nG%tMq;PW*s.kFl"RoK4?`ata
c37h)b#l+Edlu,5qgWDZ4[anQ![M0Q"kYLKTnFl#)/ZkCR##`%``mp<h.?4%o[;]A8IZ&uEYP
g&oXat+?!s4,.2Ki)^b0eX'62BXkY/(m=5U8=[p"pQ10Z8qhmCT4`dV%r71W>%*bO^_]ABrCq
R6pU92E1!nQaJuZpHmp2,g-NQT3U$=/KRMQ<SjXk$&LjRV(&TRV$S,0+:NpMp>-!T&J_t.[+
PJJcuo'Y05kS.OTMbqc/BTYE)C]ApBY-6T!W$&[>CPN@hH)+XAT(5d7%MMt_gOEt[A-CAhK"j
dEMrXEf,JILmH44Th:VZSV\</[dr(C>(hs>&f4+q6[Q3>)+)R'"o'W/a1)CgrY?;Ib:6KFBo
>5n,q"Bed3#@=3"SY)B,`0?OB?`s4HBa%Z5/iMfF>:NEY@'Hf79=Jea5G\L&Kg;VaaBfU+nT
79`&ru<'O$`EIDkFichU>LZ))>uKY]A5gr3bS<rbhAIG-e\[UT(r<Tt(oXmHh9\T6WHO$EQ)p
51JNgL-DU0h'=_S[(L"_9VreZA)u\-4$ngEM:?F'7&"kb?pn./#)gM:VU6u"gn\B_;hN%sFB
oT^)W]ADp`:"dY!:,C""T,NuEIRQmdn_@GNfUV&?dPEg(%/%`Y1#_(^@,p9(WV2AZkoQ#597I
9$2LQ?WRWnIP*P>MHVIDW4bMC?nO4\?V-R9VU>*oq!gf=4k+@H@$?+>Zmhc6bA]A[2%&ZNu>h
XB9R_'I?)Gq^"n\LN`bo.dS(f3]Ad>Dhb>]Ae`Y-8bna]ASo'LUUelG^p/CE@Y%s0AG-A(V9/dk
]AOBGU4F-d63$#mZQ7'DL99`nUV0\3mq8_p-18+D_S\jVle$4oc0AF>H@VUhtRQ/*gb#-$]Apk
KH9Gj(=QhhZe#\h1V3*pIhp-k;+;2;e?6.3ilor\:J"5"5HIsna`7rW%:U'iB2S8SBVoNH1+
l96\J1YXm[eUE>QtGSWqU5]AEXZ$@lbpcLD!]ADHoug+<HfZtV'Cc5h2N6T@#hXF0JNQK4Ak97
B=F*@/i*?3cg:3Grj2%m[GskoZIaf)Q>iQ&(U9eO)=]AnKH34.9$4k,dXD)EOa&UQ9CS_2a.V
./!AJp_<A?V%NQ:s2)cDRWe#BH3PK3$/#rPr$rJF*U$*745$C'=''^kRH6M]AT*S\kJVZDol`
j3`k*m#k`&IZA:]A_WN5Nf>Q4=28R;$m(%g/^^'!ifXo+]A(UP.leEm-?to/UuP:qVq0gFLXp:
f?eV$$>Whu=PrVo%VTG?s6BC\F7(@To\SKdCuBS]AT*UU-?ln%>cn.VRTeE1mlf>*!XMO)Ml$
J&ns/;(ubfJ>PJ!]ATZ6'c:YH5cX"a:CA>g02%qfQH1;1N/7J#ef!@EAit_RP6\cT"Df_Z+<[
W584CS'Y9bYFLpa)1@LlLl?lZn]A*0PG3?IEj/tCeMFjCt?Se-6J<LeL':J#mP=N@]A9N$L-Uj
HJ$u%<lB*Y0S+M$]Auoe(>F%hW[ku&lXnlSf<K%X,6&_a`.:Wec]AIHMZ30c2.eEH1+af))$(a
#"Vp<r2^kNnZ#,W;9R>7h1$3L%nTOGI!,!.D)6n*.erJNT^oP=i=7p@q#l:*.,ElL89Hc?iO
3'eVO9D=%SfkfTie5%l:kjQTo'6m7K<Gq&2j'B[B=Q+\RM)+%hDAI)Cie2#,T:)')Q't9E9I
&Yl)Sa9;V;a1N&EqUK@><mI%H#LZ<H@#TkTPCRW?uA[aMX9$Cdg+DJe@;*Ri<`SN^J3Ck`nH
bmImW'qFp#'bKf/0JgRTS6h62e*FR&O>\9Rmn]A6f+gl.m![<HP$Nisqp>IJi=?o7J9Ng:(56
dC_t?$ncCQohj%hX*/*6m/N34e7\K<#RDr<)jhSRdP2;[j]A3_p<R[hjK:+U@qD2^rQpS5UAE
2h4P.-8*EK>@hn-)taPEa'$^B3)BmY#F^AXX=YA)dR+"Ie-?^*!_jS(=>,3i!CP;K%uMIB5D
l:_J==Ucs[6PIdGa8om]AbFXFt]A\]AodFTQ+V1-SGe&3%Laf3nKB>mN%idFMe2(O-T[:XJS^$8
X;6pOXE9RmAR=.^dp.RDJSKi,mstZ>D@9.A(B@B)[fbCKeBiN?ct_gEG"dHa`F+'a`J<(F-n
S^5<b\"Cc?Z^P/MdP+!")<;D8_fruAi_TK.#]A!io2H!se+L1SS!XR7S]ALO%rhH>aY4e\/B08
#XhZEINl#/?(j<Ec.P\[:gcnh`/M41+s+0(@=b@h5!X<OlroG9O;K-0T#BWMG&.cJAhr42;A
\08`]A:aCAF^r>VC7fKp0)oSODHmgJGZM\#qJHR:'o&)\bNk.M,9UTSNEo.rY[UQ&S8h(_b"s
&=XFRodW<M!WD-mdOQBt*mWf;OtTg7@WI^b?7Oo]A\aW6%8^hIs6>U6@7B\KNZ<7sU!1SN?f<
6At>]AVrI=L/]A(%oR4M9e,1VlmmTnH[UXZB\Q'b*^:O[B3\IcRm1FD@0lt'EAf.6#ZA>C$\A"
1cDh3Hr,.VGQ=\<tUn-J[U.kFnEf.(\c(Gt\e)`X!'Z3EI'sG<RH^rlT0B)$(A`O0qj#`ji?
+RrRds9M']A*IO?>r?g'>.L(F;&53Yo2=_RCM$_`^K)0qc&f)(96PShk0d`/;;iBM6AtVG9_.
GU\19;i*MWLj[LF&1iZnr)PGS&l0&P3J1EY9K33YY=q5q38_m1ELkqJLY4jm]AHV0Mjs2_b:.
2p#OC$K]A[GnH%/WJb4Jk`[6l]AOk]A-i/JVr(>:$eP",C@drQ`Z%cf^E+V%G*L<]AO@Gj)??QLu
%Ok(,6XE/9,-_>>t/i)K1JEElB$u9:-&XV.%9>)*Fb_fu>:e!AaIH1?]AFWm*a4T6,7)(hYs5
JEu!19?aC_R+R6YpamNG,q,mU>;\R>M27l(/Mf6GpCZ]Ad[43Z=tg\Sci&Hr'D3(<0nFQu:4a
NPDIVhu:_"9=!AXW:"u8VnPJ/&a/Sbrf6^>'KdC'snMh"'<?dg)eQ(l;4P<[Si<a(aYhn&CM
VB8L]AsLpU,gZ3bGZ=!Ca@EPBNslAMlYsa&#WY(ba)R_mYRtp/u%d%P/+_3LTutHj^4h*D.l`
OiGk'T,q@?UIqoAq5nk[G4n3[gui4%@>Y1s8pgJ'Ve-]A/Zn]AJqC<hs,;I:O:8HN0\OXq9Xi<
d%6_[*4`Ip$-0.lq+YGEH4?ia;7'm,&6C:9<#]AR4l^O+,YOd4[u:b1>[&Gm/2]AO';np'U<!R
GOadrHBs@'Gc614\3R5]A#4DBPt!'P/<L4*DlK""4Tl`r]AhmJ,H0%;M!@HT\f5`t9f3Tbra!C
@FTZK?buP\+A2[NG0.8gj')OII:,M_>KV/p0aBRMq7O0*Y'R,Wgisg!]AJ;H3/WG5T"EJSrIr
HaFmL917h^KM;T7R%(u'/^2m[FeBb%)g-r'\(e."j^Ml44n3Tla,J>J1]AE"b-3Yr=[XpEo^1
9Wd=PlMGuI$p")s$Q8)6$F7]AsM9n$j6MbUJ<Z;H-ZR7"EY>R+XZ/EjVQ,Ml9buY-OUT4f]A82
78Po5m)^br$DR58a<S6iJRX]A!THd3GnW6aN_jfK=^n=C@-jpBjJ*OQn=/%M@Le"))gsQ>iJu
jLYGij]A@P%f,)37b\l#P8otuA_II'KNg9A19m==&8@Gh2_[+6QrkVmsD#6"N`W0OJ;=UaDLH
04WrT8<VRB&p?$h?%/JC-6g<b@9`lOD5X5IIm?R@*),M.Dq7;T01XrjWg_"=f<04pmP9gTAa
s#"HW;;=_5u#/]A%,2e(FRHV.N_=.UL?e(-.s5QBa#4E5_pR;[(2cRZirEB(pnFoNF/55^XHK
dI*;0Ytqdo.j3m*:7<XEYr)1Q[::)^gOnh'/1p#]A^(Qf4(h-#H]ASsa$55$2uIeq,D@WL`KJt
:o+4J9&<WW:jsf?+h_\W[iS0!MG&&jGu$c6$tAToZmF/nQ>RL$oDD7!W?]A'io_Ne5D$7I2;b
4d[J`3?C4PVA+K81@iGN0eg&CTR*)jU#8'ssNHr/-\VXH`+;ZR)J9NA-[5u\a(.@p2iY3XNQ
n,Jck)e70^:cBB@_)Ut2qO7c.iUV$5BBQ7GG%F":_LtF/^::,m>fcUThtF44uSQ4*3$.'`7*
YcQB%s$J(d`-.5t_.QRHE6"P&0hPtC@gk[L0GCYORPU*$#KHiF,9YcOD\L7m!>lgbms%Fhp$
']A]ApTH;pKAVVeh($m]AOWf/_'XR(UBfKO*J[&2\S$o!-L\2OGd1`e5gjFXQn.(A_J5Ocm1U0)
eegJ#sH64'gEbq"kL;QC9tCp2#pos+OKR[]AM]AWrYmA4R9O:6WXhu[/2)ClS35?80Yt$?YjS`
MJGNgA/F0VeXp]A/db;nj@`5/IqhKJLB3AKaRMa3RKa48JIe8[V3Oi.DA,C50fD,h!1mb2P_P
;Ri;olW*#]A8D@j*ED6Mh&sYW*QDW;?IZhg0P<7+iX\<\iA`8)N,Z;YUR$r.VASE43-d]A#7[Y
T%>8DcMM8L$fhu%q#2i'AjHF>]A//gNfj#fa0@,f0\m.-=M7CTf&]A-Jn70*>-/fML_SI_&>7n
$Z$X?pFFnYK9,MN\pab@*[(W-^P]As*F?ot5qPu4M!5OSTohZ1l:_34q&+ra0ohZ1l:_34q&+
ra0s)17^hBhTGmckU,22Dh$^Cmrm2;K&j!8t2AJ)'Jh6MDo5&Fee25,BY"Un6)"H;[Q(8Nj%
#oq\2/PBp,&l:F.<-hZR#"*.%2FZ)>Nf0lGF*BiJ'ORtoUjo5;~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="25" width="375" height="23"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" cs="2" s="2">
<O>
<![CDATA[数据明细表]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="112">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&;WeGK:@Fh9*!jJm$iW)SX"`.U`c.bo'f&gKuRN4#o!1D;7IKI#6;,"n4FbLhPGA<4Q7Qn
_a`@#Qfr#%Pm/kdl.kT6#J)?b^ZrBAVgqEF*REfCjU*?I.g+omFJ\r+W7IH`+eW5!H^:L'C^
snaUJBk5KPnYJ-tk<0QG(F3$;QEp577`9AFrie-rU'3)ftmjCJHKtd3@gU>p$=lJJ,f2l]A0-
-Kir"7aPr%!Mh<IJS'SDZ[?o3s*/=f_TW7Mtjs4]A,_M#q#^d5Sm8q<=7;0_BJ)(Z8HYF:7%(
lHOE6]A*A`L'>S(gd*+_DO1V=N)V61M9O/BZ`Yo6iWG1k<9)O8Dat)p3^n'c8)PWT#Tf^:&k&
b0Cq#g:nd,l26Xn@]AHr>.^sf1r9s'3*=LG&cKV3I-c1(41f@dL]ALLl%_nWh8C5g9kg8[$iZg
ENUU]Ae/sC3@KEeR:J10E1IC[I2n+)XO?9_XG7<ElEl>Ic;gU;lJh&K%jtPs8HB/KX1cEWKk4
]AHHnYK^U-VD^%S2rlMA;83GU;)[_/2k:5<J!j"l0T?SW8S^AIJHOqLnP&UfF%n/3WtI"maA1
\Z>LG<GNL:f/]A4)J!7>5>U1ib"(<X]Ak+>%O3Wk!a4U)W``@OPIaRgK*&QHq7ortL<CgbPF6#
t/=dOu<J;rNVr:j0NDode(U5d5YNNNbWD^jON2n/:Si[XL$ZT`Znln)#5O)9\b;`CaJ.cSF%
=S\UZrs1dMnB1)M5.\l`@AN%Mb%Re49em@#XK'LQXKP>Jpb&%BV;Se;j!mum-8^`hm9"E<[<
$bXDqcc>NFSr?&KOknonX!7Adnf72]A*T>#SM=JVcG\W0=[-X(E<Ym)s]Ad6&?SXYZ!6E*leU8
6/6mN.bgoqb1(Xo_6%5ER-7::QMN:6>=IFN.o\<unRGl,V]AA)BP4bK[s\ls)`AhWE!$3tMk7
LMn3=@9&iXr8M3Ej<]AaQ1-%2/0cl9%<_\?WHTZ!-gR6F'R'3`8gEO24@:DMhKW<K59Jq[g!d
5o;J$@`;W[unZ1<%]A.?#POEe,OKTUuFL$""ql5k-r+*<LG*<*,5M;RCNcL2=^6Ke1-s$\M\n
JjF!B>8cgJ<P;I;$f<;IW44'51CVp#%st,sNT5fhD1/g``PQuhOa"6HdGo]Afk+eKbgsjQ'C.
=h4lRu3Z%%ur?Bo(,Umu?V;$OKIto.5NC'4[lI7V_X_/s2AQ"`O[a>?mT=E6%OUkK5:<Z+E!
o'8uprJ;mQdrt'-OX/fLsZITY:aoN`<Io%8M,3AFXeO[n_XH'jIZ<fa11K_<ZadeW2'C6o:4
hK9'dJO.5nDCR2Zre)1185aURk,c'C2[;2I?)=e`s7ja>XU2)@U81WkF]A1':U0217I9ae;c4
f<<2;q2kE3u@R`nO3JJWchFgg/M\gCF<?-AnTniI"bm64sC+4Ea)l9J;*#2qpt9N8=NFCWM`
k^"'2)]As"Uqe<YVlt:amqaZW5NA]A^u*'J5,i7-aTC"<a%(P?#NE5V._i(?!4CD-Q,EAlP(>^
aA+kRY_cA;oF@BloqnX:!1O1BQ567K"4V*P_*hU@Dhnl[,*3+9Z[KMgG*p`\B/!"r.kJkfc;
Aiep8Q[sd$I1UtdeaZ?Dn$d5UD2A7U7d"sb;!#FFnm)6E)d\6.$@i(Y2d?N@Fhb0YM:WF29(
*c=):jZL:<n;%##OM%^Bc0&cInQVV;\s#1d_8@Ba!1]AEd<*U%5VuFh71+J,Gb,6i\&C4CB=E
lTrCWPk$_k)8%.ih:CccYOm-efqZHj%mP6^PSrik-uQ-A<-amTD4Nha^,]AgY0=4W@V?Je1W@
r'A-9F&b#&4=Qg06Ya/9/GLJ16Y[_e:r'HlN#SHik\Iu,/D+dq;!5us5Yk$BXla$U[9"8J??
UPHGbL:SJg31\M:PDq!__;4;7Vqb?+*h@<@P5@SdlN04M9F:XC9lA'kbk:;Os_(1K1mIWGR+
Z()+$cq_n'Wks\EfO*[XIf%!?=c!)4a5hB<C#Fq%Eh=hA,p$]Acd4snk$4Jf"&4Ph<`._,4[N
Kt_H'OK\j2=I#r[TB-<d#(nCT)F1.IYTIAhhSi$><VMKPikp:NAoMA)1BagH_f@JV*EkCTcY
AS$ct04HV8#c&3\629H)3/8Tds1E0KWi^8d.pc;Xia]A@7adVf(1l>6'J,i-9Xd!LqgRbH6UD
QQaenQNIq$GTC!_L0nOu[a,cTQ'*JGZQKJ>Cb0U-lqW>1cMd!2V-`\\gSZoB=fS;.XF*]AME>
TG`Z5\jd:4_et*Ls$"fO6$_+`duR,;ScYYbI\n\o`Ru.Eq`qN(S'-L&jV2Sd9%c(@P%g:Ke&
)RQK1qh8\/6&HXH(D(km_\.Y$3hH7\8#?>@U*Gcci4CDksmb`L7.u"Ql5kQaGNEEH4\3-lR@
ZiARE1t%+W<^O=]AC1#'_t6EuMh.3;mr^O&*+^OO]A:=ak:6BD[X<gLH_NjIt9MB,Pm/`Zf5Nh
QX]AQ\!*%#)E%*,K=5p"2.ES=aOgTAM8ret26.*FK!gp\6X;+hP=VE?[H^X:R<I_Um+$"j3]A-
VnMPF"lD=e6?=PD*+@-04)=a=>G08c7?65o<]A?bY\&8A1/Q(#uO*T]AK9A]AiU;hkIr@_%!Lm`
uVj&IUSK2]A'b3Xt"rq+''P"M7M>Na#3;ZW^0[[C6/+4#^cJ2&`#LS>P)]AI6HlK*^l7tZ)o&L
bS"9'QIk&<p_u<3*_78>tddOW[Hp9;K?^b"4Ppg:7d-:<DjMP]AqF1k^Ygd<[C*D^nOP,>aab
%4mD;U7=!K"l*h?/h<LS<E?(dguC</GlX81fDY(?Ckme.IqHSg)"SSMY!G8[`cp1$,=1H4Nu
0"h@&+C8]A[6^8)9<b-f%^Fd%ncmANT\hY4dom4'0%GX-7Y]A5KNZ-p[URDA2JKs5#Yj#F(5*P
gj0cM./J$PCRi_0FoRkC@$Kdrdj-IMY:+Ur>.PlYkq;Q05c-R_pGl2"?p*Wrd;P.ujN,IkKD
O?iJF@$bSK;A0G&^D44YI9VPq/"9ULCL7iog(5U03;*%T(DM!\?Y$^[8Md]A!UsNacH[WLo2d
]A_oC*]AF/!?7Hf[ptT#P>Z4eKNS==G!Q<m%Br)=]A<L)PX%]A-"d7l6\ZN3V['7c!>0P)B<J)kE
[8=0.P#Q<Kn.u.ZP`NO;?*<h!'=,Y%+8i5$=r9E1<??1#;oqmXOB8!f`,iVAFn$B*]AMJ-.]A.
RK3D7sCH["8E,\oG\b_hd)g5FE"5pkf4GT;7?%/<6,g8/>tpE1p9d(9k_PWO':W0O@4&!B!>
KaT5C_]AgU#$rE;F([g?7iTPoWVe@qRGu9,ZJ*"\DFSp?gR[sBnK8;`Q]As)B5bOFQb_]AtZ;Qu
2X^N0\jlYG.1$.fN!$Eu+a5l:02b7^aIdr'E/2a4dH8/`C$ZA6Nt9AkVMGN@A\`$U;.9V%m`
[Vn24HT7bhlBGHo<qDbX$WVRqk3JlEZ$$4-.(lAh^fY)EU^+kBL$7?r<=(Wt$/@M!h<6gJ)m
3nD!DMWPWdO1X+*&>iuo!p[0/DN)q"G31g_'?kDcc@o7EIc3oMHV)YMlLp[@p/&QIQ&>DfUV
QHOWje[QY>e,T_!t9#T@r1TRQO/`^kp[P/QT@YOf0BI[!?e;Dk@=M%qqeSfGtbi6VpmSeRXo
XOg8]A]AUcg\R^N<HluL]AN398#P7=,n$R`,u@/q1H</uhlN#T:9i2A?-c>JtkEm6o!83==i8Ld
Wrhq"[9cV!Do`KQ2K0egn6o@`2I/&n5Mio@).t^kOBKPbTsnJHSak6ShtgL@-/6.fPoKS?C-
@,V%JHaTa>q\4KEPcZIie/]A)oH;C5!)+Ml4$s1ma!i_R^';m-g-;:W*hR8jJtrUj#+YEArVd
60)1BCGkj>'6%56Sa*A=b!N_W"j*JH/Z3H'ZErM6@N6^+GTQ%qZ>mLP32#h<j"/6:!R<qYDE
g".E6.DbW3E2lG#99GiPTOF8YBYf'@:;C<g=SQ4gG]AhXHr<;>Pq(!DH'PJ<E84a3%id>sEIu
@Q?:M1_VG4]A>(@:CDZiIffKQFIiF1jc$NehE\j[Ve@0b>HH&]AiUh5_L>l;ORgi\)3DSfA@1%
C#q+h-d/Ob^9[)qN85_:j,"K4J7^>0tpO/(S768:)(Z)5+S189DL`WSN&mW<Z1OW#?T;WPCK
MZ)W)qaO!kj(Y0Q/E&0)45(R!\E:"IXT.#lliD-ljZ`rt<:U4ci[k[7a'c?<?&bf5Y;B+?h.
4.X>H"AL7rq=XiqE&-)75o1W?tl'noj#&3SIhmldKO@?2mSjg1,3`V_ed)Lh;-s%34WW8l"Z
*SR8W2fUp:Z*e02#a0:jSoRO$<.O]A8HVF`"[>DY^\7qHFs>FRt?(k+:El92%lOUQig+,880K
]A=op;fd:#-lh#5hO>6$NrCD?f0spU7mSosbrP:FP]A"K,)[ZVX7o-BEEn7MK?AXn_`4ZE)Tf@
dFIHeuK2V(.?4!.:7q&i^EdN0]ABkOqLk/P%uoFJ)>b4\po1*2T.VB@bP=IQMZVp%N^&O>rhW
$-@;I,H%n]AcN/3p&c"XIR$gG:qOq.';VW3D0lV.H+4((Fa;'a[52IDN_,0B9PP0*2^,8/%&>
D>Q5PhWQ:LV@1$+kGT(:KB%(?ptmp.$oRO7"RcrLNnbkObm$,$J4]AjKLT##QDuOD8ghpoT2r
IiSikB46G=Z'Kte]A1bujX6j5e'[:!b5'g7i1r9TW;:KfX,NM`j*fHgEn$'ipYQ\m3qs478Jr
F6P1*Li>Vb<m;pW67$9D]A",.OTsu(2;.:%T5h]Ag_g!>AKjg<Tu;p/MB_T.u-fW[HY_333<Xr
o%jCBSF$A&t[A"u9Z2BcT'Oo##:I1pBjmA#6$#L!2\J9I2pX94dUG")5Zm4a(dV?RW14't_%
^-Bh:2W7l@6,0oMK<e9!!,d6Fsp<USULZMc6f))A-9.Kof(T'q'6&HC6_Y#i-l1Z$7Y(IDN(
Zsdu2hsWh!E.H`7\'8gMj]AqO8_;jqnAE#TI!PR&WN\k8_7O)MDa-lCemnMMH>Ri5JhaLh#/k
h.Q<de^mn=5QT9;_V#?5:I%lM]A<_o=n+%l,NKh9DsZOtJ!Y%%-S`)5a/-Uss%/7Pmq:HP#Gp
^<kandT)V*(]A0ljm%WpcG5m/#D9DYIHP<;9m]AdXM:pFiC(26)tJ$D(nKkAFlIX^q[lIEskC'
:VKMaj\I]A&Ne`6.+N%@&o(3$QkOa`B(Z]AhmEH%5?7jNpSUjhI1:N&FH9>d'(S4'LWfkEps7J
R(p4_JCI-)>G;F\c:FT)A/aWU4,q&#PH<9YN#ASQbLW)bB97h&NDO_DF1tYt.;!248j_bUW/
3eabhK:jn@TSf)8'$E0@96Tfm&]AfS["p*FI.-/6QV]AQ1(5k\m5&Z?fV@?fX6AO-"@Cm?r9F`
+b,)?)9APrg/\>Kd'";-!@BW*rr,a#/ka-"uk$>hVLp7Z+1-'X3"+'atGX4^6M!+#LeDVBtu
%.89*6=oolVoRo=^m`MLeM_tLbY4&moRR5:nVNV8I_sTcX$/F]A.!B,J:a7b(g3IOBWugiJTr
de^9URs:;Zh2L@U[DI"0LnTZ;44%U;9rYN^QgG3PjPf"&+8BL*[9tA3;4V0W"BMi:)b,+#g)
Obb?\]A3&fs=\N91uDQqj/-^OS?I&7D&&,RgG=?oBfa55%t2r,sT)\ke.k*m>M%UG/8&sfKcj
qCN(/E]AYVALb$(-K\U-SA(J+C39;EXj<HTLb#:^QR(<-\^cfm*E*F5@X!S>5JC6Q#7PLtf36
urJ7U(8pA8fBm=6n\cO#uenQQ:Q"<*CX6upRjf'@OgaHSCp5U&Uo9/'/\42sR0R08We90ZhQ
ae+OGiBnP:S8%NmnJ#qlSd'>cq/j><(d4pt<E"7CDD%W\&dFeP=8C.aC=LPrE$+^.A<;-S^#
Q!/(<[3,od\-4UWO]A[8,)k=q40M%V\c8#Eb1D?4=6t%#-\nTJ_.m,Ml-?\=%9)M*N^X4;`&5
rLC@p]Aqn3M^:$pGEfPK=q95Bae^Y:#>X-kX0@&R<fZG5t+^7D=d>1[2TR!kU)*67c8g*Z(of
F]A#H%6d^>_V'.rH;%c(<)=3Da8<[8(qp\$rIiNR&,ZfY<N",S[ga%N'a>^0DNcV'99.Mhl73
6H$om=O8ba"c*RG[$dsmG#%El\8kSG$]A]A64nIc>M+'*:[hFFlbJ1getVZ4[\5eIOO2YIr!&q
q'eR!`GC7Q(b!WbBsO_Ho"L+d?$+PX4\iOQkhZJ9#eT&?&%63bG;J4HRi!bT?E!C1:qL60g/
6HPOL1g!5>phEW;l3m!sm?cWR0p\[-SIL`o+@eA%o>%;G`B%QpuMW%>LJ)U=BU)OBHeuLhG@
uJg2/f.4<qeo)6*BOTu^Kk%fCN!#)@r:V4cC!k8,Lqg42kT86gRVjqmg^3:Y#*K/MS8i*5EF
g:9U-H<\qoJnim_`1;rJXJ.HLZeiQ.c=%5FucrN7EpA$BOe?l92W+"7?blSJqD(VrW_cjoid
Zb-[_N6Y/&1"RTQsiLWmr:1p4LV[]A/oe*&:GsH@9MOP)o[pXH?=Pj>uS$+E]A7i]AF8+Ucli]AQ
fPk@bI(Zg'U8(HK_KYIU##GZsP0=<Wdu1YelG6s;/Ppt.QFp&rc1`"3Oh0_JBu=[j1EPP<+N
in@&G-a[BjA8lJFYM$ZrbD<&KbQoA-",=&L!J)g"F0&HnClJ1jU]A$JfhU1h\sK]Af,-pFjh!L
2(m"\61LSPaCVADg"ER4/M5GLWCo99L5H!q5Rqj8kFIXlV$ZbF043).RCe!d4VSemc7tD)4;
D8+?ls!biG'rkP!,c&^!m:B-JHkOUJU_P^YG=^79/;WU"ONj?*@n^1*3-*IRnBfW5C$pC[4$
n%fKWM`]AQcI#]A'HYM4%(.6U3`Hpcj.:_m-rjpT'/tPP:XjUk\tNLY7@n`!87]A9!VT5!m?Rj4
Op14kO/TKOr::;Th<h$aTD@f=$H=]A\/4?&_:9aJ!'J$cNM%Ef^f^P;oe+KK!cZ-j&nH+\j%F
WGo`Amg28-X?C2:$@r$DFn0WJ%d]A*1i.TcHn`(KW!@(`+c'n?mWA)V(^;dNEdKk5+Ip-)omp
jUXRY5h?Y1P$5hnp`+E2r*<qGKZN>*m\>iP#Ot%ST(E&>F/@m&Sc1UH,R1[(!9\(e>JS,$&[
UE2Z,YsTB^9`!r#!0Z2h!d:1m+.Q!pDIU&U?;!N36<,k,N<c5;bgs(AqLA!\Aa89TaP<5Xb?
Uu/2Igs_;-VtkH#SDdtk>HT2^EogIY1;DPeCqDg"j\W.-s"HH:</7-njO1,lp_XOI..g8fU`
CpRBsO?TQM\;3;2.UsPbg3&g8jL=Cn,6jcK^1GeDq]A!LPVm3H@rd<Z=A8=&:Cs/VTIWP5Wb[
Xjg:**bSke]A8&4NB5&I>7j?SqI8M8n?/ubgclf=]A$lqpW$<?ZkdVZ1^ioQfG5&ZMJcH+b;jd
lHc%O98bK5RI<k:u]AEhFDC;&gt=%#cUd@@:$j=ek!Om--T0SRTEk6VjPLS5`Fc^FF%G1'*DX
gMjZk!:%&V]AVn;>5Xde'=5o&r[Q'p]AS>q->3:RAS;JbZ/e0n'9FgQfei,Rao]A_d/-c7Nj;=B
]A`05X6ZXEV^tJP"(YYuLk80$YhTN4JNiU2]A4+M/iL1lAVuLSCq*EA3.mHHX.-0[<+4M=`&rg
p9DFP5?u1UESpj/C+Yh>bp%l.We9/Vm$N<G5ai:kL:XeIVMcM)B.d![`7?O8Of"A]AcYGi$9^
S49J,59)JGBXh>s`u@;b]AZMVM\EAO,@"s`fNtL+IGbNR7r-3Og\I5",JW!^'PJdr!E`t+5Q"
e?AHI[OC;DGmUKj1rr+i(1TR<chb=]A>73aT5pfu2l`L<';Rg5dDQd91\^J+\h^o!oM-rg91d
s&tM]Acg%ELCpG=`bV_$Ptch%.br++TfpU6mb)X+bP@]AT[Y#<t6(S"P>,kMI*P3j9,nrj*p\'
ZjBNrkYZ<(fK2i7<<'J[a"?Z54S'52+g0:c<fKd`\hFB_+qe\Ci;G`YP3-,c3:`e-VL.i+HY
=plA[s#lVJe?;$_#*c4[oR?)&[lU:bs(4Tr+Z;E?0:eM81S`;Bm"K-9M_YNXn!#BheQ,\D]An
N8h(8K^UNLq,h=UeKtZ<ZGAJCGBnq1Z_),iKMVan"fG3c'54dd>L\5jXY)dhgOY[%%5%mXe2
5np!0j4EunsQ12oP\31=/di33PnC,+<Kh`l>8l68h+\Idgeu!eW=H=Ro2#9HN.7CBJrKdt(#
_^;b4Bi3XP"tqNPYmlmR@ImmiA1P]A\g>j*Z^EP]A=M:P!S&3itkLDH<8FKs+-d;ceYb8_O/!+
:s@N$LXKecb5pK8bDk]A$Wk&ZdKsNRnZ%g.,NfrP"kQ$8jN#f+rdW*DsF\V9Xp.SLe#>+0GkC
-LbZ-F;<XbFL:=Ah^'VKBgt`O,\HPeB8m(#3JO,#Bmqq%7qcT$lE^1b`APXumqQ1/M*i_+I5
mkdp!h_X5f,-qT>iJXkj(3sF=UCa<)QrHFtWSqmHeR*E-oc&(pZq-Thsq1?<_n>aeZmTbHY+
MqF=PF4n+"dgGn&CkYTn"daguD^8$]Af#qT@rPN`:R,r"'F510+QZ&K,$Ael29JV3>"i#nOE!
e0K1T_"7"@XmF[be=hDKF!?'i-cUI!J'g)p6t*%B1C3s,b_35S)kP^BI;$)6;#5"U!$S\#\o
E)l1b3l$bs\'T'-ARdeE_c90o1RqY8NulSP_E3]AdM>XHD(:D;=Z*_*Y8Vif4VH&\i<)T$0ro
VH3"L:/<gh#u3h]A5/5UOOUi^c'pZH.M;F6$*>Ef5-moH@!(&N!QIosbUW35Oog;GgV;IJ0/,
ruq>A0%X+lAq=^6R.hNNKe)*,*chf'?eQF57\`Gl9U)nE^S>nn/IkkFbZ]A3+30k^Ad+gPe0A
`JQ-M,&.<FZGS*m4qIT/\oFdkiMq4A=N;HG\i!\,m%jFadf@/>hSRl[ZpW,Wk,]At\[&U%O2)
GFWPWkgb%oH8(ooT,AXg0ENA#`l1kErm5G-QItD*nS`L=3OBj@@CBErg:bTi%>M^0hp]AqWEY
e#\E&pTPCLGH_-kVo+C8"4RI?<8R*V+g-6HfJ'2tJE(1#*><CEM-g6qfi9RN_n8IS3PSj'es
dZRW'H_`!@@r.bm6Qt0N^a*/fR6\&`ZdGYC$G5>gfQ0$"?N@mSU]Al[!-OD0'\Kh:n4)2dXB7
;,[GlW$3M^9Gb=%dE,;Gpu0B:Rj9(iaY5k0]A4$Gjjkeq5)'Pkl#0T6@B</?>F\rIILDaY"WO
,nr$5q3C1=]AZZ,N$^LUNW4]A/#WHYI7ebtXmL^)r_:kt8YrkPpY:2SPN`s,[!'?S24`Ugi9ph
'<l9/U[#IU0o0\FUN49]A#&R#QKj`a#]AeDJ^)KH<Tu,'7cYgtf='hB#iSXZ-NW5Ke(B:0+bm1
RHTpglhs4[[d7to^tWN#ngLsIeWk><EMAIL>,OBb`"&8Q>4\tE3ISFdhWH5hMYkiJ@PG.1"
]ACJ`cRu_K+(_>\Y4HKg=5T[uKGp!XYJNT#!rW6`krC+;5(SFD@)ckd[He\>0kcNGfLg4lCji
Da:8]A-#rj'b'/L[d&>`l!$<&*%(\N/-pW*lc*B(_>Y84H,'o&&^(qpE/OHC@NR7K7M[u(9Bq
8q<lB'qYl)-YKt#KT5&+Z,l[UG+mdg))a'(7nLu$(_g!"/j'b'/L[d&>`l!$<&*%(\N/-r-s
']A$pr:Fdmhhs]AYR-HKEk$*(=<?B/tGSgtO@6BL(nLu$(_g!"/j'b'/L[d&>pAY4Vp8!5dp\P
F:>AhRPWpuAPbP-Vf$N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="25"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="report0"/>
<Widget widgetName="report1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_tab的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="454f591f-093a-45bc-b03a-63d38848d210"/>
</TemplateIdAttMark>
</Form>
