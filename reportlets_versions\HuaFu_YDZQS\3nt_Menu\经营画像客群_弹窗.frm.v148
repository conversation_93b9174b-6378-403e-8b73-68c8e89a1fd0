<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="bp_jyhx_fzjg_tab" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
		     A.AREA_ID,
			A.ZBID,
			A.Z<PERSON> ZBMC,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		WHERE A.CJ='${level}' AND A.AREA_ID in ('jyhx_kqfc_zbyjdt_kq_hz') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1
		order by cast(A.XH as float)]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="下拉1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[		 select
		 branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end branch_name,tree_level
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') 
		 ) m
		 where 1=1 ${if(level=1,"and tree_level in ('1','2')",if(level=2," and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'"))} 	
		 and branch_no not in ('2097')]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="查询机构名" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="branch_no"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name from ggzb.branch_simple 
where  branch_no = '${branch_no}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="company"/>
<O>
<![CDATA[8011]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
			A.BZ,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_客群') 
		AND A.YEAR=substr('${date}',1,4) 
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')    
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
) 
, DATA AS (
	   SELECT 
	   CASE WHEN A.TREE_LEVEL=1 THEN '全公司' ELSE A.BRANCH_NAME END BRANCH_NAME,
	   CASE WHEN NVL(DRZ,0)=0 THEN NVL(DNZ,0) ELSE NVL(DRZ,0) END ZBZ,
	   A.ZBID
	  /** CASE WHEN NVL(GOAL,0)=0 THEN NVL(DNZ,0) ELSE NVL(WCZ,0) END ZBZ,
	   NVL(GOAL,0) GOAL,
	   NVL(DNZ,0)-NVL(QNZ,0) JTQ,
	CASE WHEN ( NVL(DNZ,0) = 0 or  NVL(QNZ,0)=0) then 0 else  (NVL(DNZ,0)-NVL(QNZ,0))/NVL(QNZ,0) end TQZZ**/ 
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID  
	   WHERE 1=1 ${if(and(level=1,len(fgs)>0,len(yyb)=0),"and branch_no='"+fgs+"'",if(len(yyb)>0,"and branch_no='"+yyb+"'","and branch_no='"+company+"'"))} 
)  
, DZCP AS (
   	    SELECT ZBZ,ZBID 
   	    from ggzb.ads_hfbi_zqfxs_cfywjyhx_df 
   	    where ZBID='bylzgddzcpmc_20240622191229'
   	    and TREE_LEVEL='${level}' AND BRANCH_NO='${pany}' and DS = TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyy-MM-dd')
) 
	SELECT 
	TAB.BZ,
	DATA.BRANCH_NAME,
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	TAB.AREA_ID,
	TAB.ZBID 指标ID,
	TAB.ZBMC 指标名称,
	CASE WHEN TAB.ZBID='bylzgddzcpmc_20240622191229'  then DZCP.ZBZ else to_char(DATA.ZBZ) end 指标值,
	NVL(DATA.ZBZ,0) 指标值,
	TAB.XH,TAB.DW
	/**DATA.DNZ 当年值,
	DATA.TQZZ 较同期增长**/
	FROM TAB
	INNER JOIN DATA ON DATA.ZBID=TAB.ZBID 
     LEFT JOIN DZCP ON TAB.ZBID = DZCP.ZBID 
     -- where TAB.ZBID='bylzgddzcpmc_20240622191229'
ORDER BY CASE REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END, cast(TAB.XH as float)

 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="单指标查询" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[20240531]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
<Parameter>
<Attributes name="gs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="ZB"/>
<O>
<![CDATA[股基交易量（亿元）]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH TAB AS ( 
		SELECT 
		     A.AREA_ID,
			A.ZBID,
			CASE WHEN A.ZBBM IS NULL THEN B.ZBMC ELSE A.ZBBM END ZBMC,
			SUBSTR(B.ZBMC,INSTR(B.ZBMC,'_',1,1)+1) MC,
			SUBSTR(B.ZBMC,1,INSTR(B.ZBMC,'_',1,1)-1) ZBNM,
			A.CJ,
			A.XH,
		     ${IF(level='1',"ZBDW",IF(level='2',"FGSDW","YYBDW"))} DW
		FROM DIM_FILL_ZQFXS_ZBTZ A
		LEFT JOIN DIM_FILL_ZQFXS_ZBWH B ON A.ZBID=B.ZBID
		LEFT JOIN DIM_FILL_ZQFXS_PAGE C ON A.AREA_ID=C.AREA_ID
		WHERE A.CJ='${level}' AND C.AREANAME IN ('指标业绩地图_两融客群','指标业绩地图_客群') AND A.YEAR=substr('${date}',1,4)
		AND B.STATUS=1 AND C.MODNAME NOT IN ('全司')  
)
, RQ AS (
   	     SELECT 
   	     	TO_CHAR(TO_DATE(JYR,'yyyy-MM-dd'),'yyyy-MM-dd') JYR
		FROM TXTJYR
		WHERE ZRR=TO_CHAR(TO_DATE('${date}','yyyy-MM-dd'),'yyyyMMdd')
)
, GS AS ( 
		 select
		 branch_no,tree_level,BRANCH_NAME
		 from (
			select
			tree_level,branch_no,up_branch_no,branch_name,simple_name
			from  ggzb.branch_simple
			where tree_level in ('1','2','3') and branch_no not in ('2097')
		 ) m
		 where 1=1 ${if(and(level=1,len(gs)=0),"and tree_level in ('2')",if(and(level=1,len(gs)>0),"and tree_level in ('3') and up_branch_no='"+gs+"' or branch_no='"+gs+"'",if(level=2,"and tree_level in ('3') and up_branch_no='"+pany+"' or branch_no='"+pany+"'","and tree_level in ('3') and branch_no='"+pany+"'")))} 			 
) 
, DATA AS (
	   SELECT
	   A.DS,GS.branch_no,A.ZBID,A.TREE_LEVEL,GS.branch_name,CASE WHEN NVL(A.DRZ,0)=0 THEN NVL(A.DNZ,0) ELSE NVL(A.DRZ,0) END ZBZ
	   FROM ADS_HFBI_ZQFXS_JGZBMX A 
	   INNER JOIN RQ ON A.DS=RQ.JYR
	   INNER JOIN TAB ON A.ZBID=TAB.ZBID 
	   INNER JOIN GS ON A.BRANCH_NO=GS.BRANCH_NO AND A.TREE_LEVEL=GS.TREE_LEVEL 
	--   ${IF(level=1,"and tree_level in ('1','2')",)}
	   -- AND BRANCH_NO='${pany}'
)
SELECT * FROM (
	select 
	REPLACE(REPLACE(TAB.MC,'客户',''),'客群','') ZBLX,
	TAB.ZBNM,
	DATA.branch_name ,
	DATA.TREE_LEVEL ,
	DATA.branch_no,
	TAB.ZBMC 指标名称,
	TAB.AREA_ID,TAB.DW,
	TAB.ZBID 指标ID,
	NVL(DATA.ZBZ,0) 指标值
	FROM TAB
	LEFT JOIN DATA ON DATA.ZBID=TAB.ZBID 
      where TAB.ZBMC='${ZB}'
) M
ORDER BY m.branch_no desc,CASE ZBLX WHEN '黑钻' THEN 1 WHEN '钻石' THEN 2 WHEN '黑金' THEN 3 WHEN '白金' THEN 5 WHEN '黄金' THEN 6 WHEN '白银' THEN 7 WHEN '大众' THEN 8 WHEN '普通' THEN 9 END 

 




 
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,simple_name, tree_level from ggzb.branch_simple
 where tree_level in ('3') and up_branch_no='${fgs}' 
 and branch_no not in ('2099','2098','8103')

]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="para_分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select branch_no,case when simple_name='华福证券总部' then '全公司' else simple_name end simple_name, tree_level from ggzb.branch_simple
where tree_level in ('1','2') and branch_no not in ('2097')
order by branch_no desc
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="zbsx"/>
<O>
<![CDATA[jyhx_dzbcx_cwzb]]></O>
</Parameter>
<Parameter>
<Attributes name="date"/>
<O>
<![CDATA[2024/06/01]]></O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O>
<![CDATA[9999]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[setTimeout(function() {
	if(level==1){
		_g().getWidgetByName("fgs").setVisible(true); 
		_g().getWidgetByName("yyb").setVisible(true); 
	}else{
		_g().getWidgetByName("company").setVisible(true);  
	}
}, 10);  
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="686c1f62-47ee-4cf5-b322-02e511b40c6a"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="2ca8c2d0-290c-4f65-bcc1-91fc7cb1f711"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题1]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0" index="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="a81fdb1a-6cef-4202-8189-6992f0a14c0b"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute0"/>
<WidgetID widgetID="11ab8167-9496-4e2f-ad1d-f6ca2475e26d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="yyb"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="yyb"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="yyb_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="195" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="fgs"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="fgs"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="fgs_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="SIMPLE_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[para_分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:para_分公司,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="114" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="174" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="company"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="company"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="company_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[下拉1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:下拉1,Key:BRANCH_NO}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="company"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BRANCH_NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=$$$ + "明细数据查询"]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="4">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WDEPA.CAAk'g!USB5n;FV6)dKHA!((3Pl!U1mT<OrJ_5TgqePR]A1:W.??f0+j9UM+WU-+Y
"f1-lAZ]A&rIht#e@d>F7-`5F)F'(m^h`TXd(!Mo6t'?hXB=IIeK6Vg/sd@]A(ai`!!)Mhl"*4
d!+;_[enb$+_GBZ>hS03o/9dI"?FUW%SX\:)r2H;MGbO_@d`rs_8C6J/SbH[c:G2XW2o"GCn
I**h#!Lg_6'-'5cu?J\YI6B]A>Ln2LIq3`UqY*.MYC5dpgO`n9,Lltq[Lkol0C)@#"/Oh5(42
Rg`DTASB?*@8-:7;@E=S6dF_)0N?]A'=7SFq#13X5XI-JChgOqJB??:P`<G6%ViM9._+]AL3Cj
LB)jsmCkk%^QV@mJ>3l&Gp/o*""<mGi/b5:Vcu+IaT%mf'JqcP`>TU4)LA;HXF:;bpk*C/nD
M-0HON2e!.\;EEa-oThrps<J7"2<LP=*-o9]Apdi,$\6R,NMtq+po1R&HApo=o5Bs$`7p1,B\
me,b*a9Ra8]A!:Zl=hA$K`1@JC&O(r]AJ7oPLTor20($(PY]A>.*\;K>KM-%ohU0lhW*Qh5c!j>
6\&F)?*an[i&CYhS"TsZM<!0o6k\o^srqNpAVKMkO2DVbq9HE>6cqh]AZ3Fa;R]Ao5eg1PK<XY
BN"=$KM[u$_NrT<IaX"uo9rCoIC977(4m1HSR==CHAVJT0GP24^)8LCSe=5qiTn,\[a"-AX8
12>!:5rc(I-5Il1<]A(pfdB@p38nb6rmk9hmG=`t@+@e1N4sGB"jMDn:4?b?sXH/a)E+I7[j/
^2I!l!JoT8?P>dtm6f/I)<b_-;M3!VGXE,o:@XBRa/->FZ=;bZr-o@TQ;P^'W[(j4dqXm&UI
7G,efr?[b8>^kVN5VMB5P]AuP^DK%\pBEnJ!@+E`@%B'A=NqEd;h4GFT*)hXY(XS%=&"HPLYS
l2"Agt_(%NF)',VlVf:H-J]A$I^f[u9+JW\2o]Ao/%b9\NY-ZcMX<A>XH^21`E>U._P:L+UN=D
UM>;6Lcir#3/8["^u$:?2k(ebY5LpT_tH7^*7L"@<e*srHH;kO1%<$c:#&<D_,X_A``_<7gi
%_O<*BZ_]A/.W%%cG#bdgbsp-+U3_DG,(Jr,@ONSB95q1BJc\'h6?1&m+q/]A@PXms?5&FGPSh
[ddd\%3f$4\&Un0bNTUj5_V(4u%6M$sjHPL`3-M:[)TMscT#SHBla3:%o_kb#m]AiPOKAN]A^4
KA"6*TVRFc`bg1cAfjK>e&WBu5IZAK\_>:RT8&=lt7./f&#NhciP4DI,45LG2eJgmA*2X%"<
f?"a%6==I%O/p`U8ARHY#dpn?JlR%3%-En\s>?P20q:J*c(-/";[*G)l.nGJk7SgJK8G56Om
I_k=cH7]AEYb/68W\qSCmb-S,/5lJpIr'Mc6afWnN@O,%D=1lZ/UC-&tbia75T:>3SXD;3:B&
IQB0l3f./r7L+G_-6gKp3&ZV3ir4)l]A+Wgk1HcnB@nDJY'D`YjM?MC(7EYoMh'MLP*SP-sBe
P;Yn7XZ";J`sGlm8MZJaTN_Kq(>%diS#EgtfB@03C1VGkPS5W`dr*3d=f`hHso]A7p(++CcYT
h<@H.P*5+ptUA5dWW,!1.KQ1Ui]AZDghfa--N`n8Y&S<S@rk[)+hUcDRu=EWhVAN>q\U/&<7`
In.=hVd^`Q"BTn(0^YXHte]Ag*@ejJSmsGJl%XfL/\FFXriXL9n>/kpe7+29[Bs==3Y]A9U/Vh
doU3M?QiCh#plCGj?k/B[tot,R="gof<A*F?'#P>dbeG\(4Q#2>p6!I4.`uX,F8bqplEBWLD
EEn!2c!Os^j!@t@kUi=&h`2`uNR+L/i_2hY2\nt7l\%_Zhf7t9#X!,OhoIZ6$As(+2\siYM%
)pBF'BOYGV9=43#@d88Cq3g,1Un&oJ]A!-Al_Lu4([4gTmqC.F7^KE*\#-I/;",L/'BEih<&q
,aE\YX`3k"V,QhcfHY4U)`s.G<>VP`_qQDug\W.K--`P2_?+lA0.eIDRe2jb6i6PHLOR!b&B
Z)Ojn\uM4KA+Y#SZhA1l_O?K9ooM8l9i_6AmG_Hf%1$D'S42=SEe(n=9C>3@kA;%R^c:>Utg
T$QA;)6AiJbb#hb%/fm-INqU7T!2Ft'WXD>W-MWkt30>)SJL*u[;*!D_bbS4E@=udf4?%"#"
O(1ftNo@5l!eR-tOk2pAD%e</VA*:B-GoZK)dMsZqg4["1s1Z)WRSP\*h#f$W%&C%.lD&$c\
-2]AfG$B+lRk"MWrdp/T052e^=*N=G8J9IJFYkZg8@qXc/1H]A/hMt6?[LBU(HCT7Td_jX^K6t
eW`EZo=m1N.[3$J2>O3MNo^'a7e3SA&/'I9-P--4J!kag"0(feAF<l1sPl+O!ifU*:F4RF=I
Ea4R8/Ki9bOE*[E("ae)j6gNXu+mE+WpfV?o4@2_Xf^"r!=93O\N:';tZ1eI&Ou[B0M<I+IZ
nR'[/ja+_cCM\6=^LDd]AihYQ=`J@o%;7RSF`-]AK3#L+l)i3J%TWO_^YH;\OMIg7<"-6W_1LI
A5T0ujjo,eA_Q7+Y5<pM6=!M!Rb)2qFZkcRoKKpcY5)!&c-Cj/BH<SgLeM$1lS3OjlFJD3ZU
\!WB..\_l<n-plprN0k)oV3BUeV#adB?<8I%m'<]A6cBP2_G&1`dK+5*F'Ih3aK<--hmN^RE(
k?fi*7jV1Y:HKc53_VW2Va)PY*9/;,ne427C'6^%I_FMq!R$sqJ#[1J\%.pMeV\n$5Y"qU/P
Gh1c*Z_-j:q[atUhu]A-du*Kkn9@I-R_=KT<f=aH<Gd\IH*q&3Kr/V>fVWc9>*#DJaTtbA5XJ
Sdh?a8RK?8;&c.L[&Qb17^Y#]ARPpPSubVoGC;/1%YcqYFXPalP@?0hrIXXkZ_MD.O)5-3(70
SsI[*4b<C:HN&5cgkW3mOo-`R^(OTO(.7g#6[n'c!NIu*\65+Oa!F8pIZt=^&*qcu2lSYRST
)oeg,=O)<T,s0$U6MN93^`ROj8,"D;&g^Wlog9l((#0+1%OJn*WCIdBWK*9%J,=`suphoNW[
ApLJLP8m0ZXR8)VY&6.'ZA"i`jSBB3g5gnZ3LL&iX%nJMZJL]Ag&Nb?kOr?Y\/JgaZ1DmfU>/
qV06(gNs0[<n4(c04JO;3m%9'&AP$p9C"/$p(JsF"uhb#XmNKU.8eB]Aaq#^Lsf^M<%E[Xkl3
Ds:[[r*B0,KS1S7/F\7i,R>-iB5cSasmhqt9f&!WhPn'rDYORC?P[MC&knirItrOKO$AoXk9
etEk&Ts)4`X-qeIq*"9H=^u>^5_ICrC*BM*d6n<hH9>5Cl>*+p7r\Bg1e*r-<'o<O)k!`P2a
R15l&/j5JU/<H5bH&k39@`8@\%$4MLgS"4^H"]AI!^B^6h4-/WtZkZE:l9O7%'ib!lcT0W49#
gJiTXFbj"o6#1oD/Jfuc%<XD!<XmRTc<2DY.-[Jmk]AqSZXBVA?5Wjfp9jbA^Q<_p3@4NN*=`
[.]AiMSVF-"?)XZF=a8128W^pi/`K-'114pL-[An5/&*K,5Kl?@nFFdC$2RVlFf_P>9K`ark+
j,_6fKc8qnAhPan#$S1*T!b\Hd\cl,ecTOK_[g(KY%mas1MNPU#1pG[S;9RO+1eK0shV>B''
_!1Mc:VXPU?W%_7V+YHfA@N(e&ZE;/K0`\;:%O<tj^rloAnAoa!Jf`qd3_`5HTU2A57oiB`-
PZDDmo6U?:WP[D*1!pf#=hWEP`m)b.JO8Z6t.9+1E-5dr/_:!!RW4`&(%Xo7fhJ,f)$\1Z4J
!U?MJP-oGt!DsgWs`gdn8/2&*<9F8pscX)t%O-`<n2Xe5c2T]AB*&Zrp'RPH:B4<6g/#\@5na
Tc']A#FWu*`]A2_T-mp:567GM92?h\R".b`-jA:5%G,&)QEVe4)\59D0HADmo/f<mO9t8e+5gF
tPi!h;BJ09P32l=N<=e;WG/3Y#4p7]A&B.a;^Y;kBn#'`L'\Qmd+b4<FtO/j[rA'=LWYckniB
(mftHPJ5<^h<?%`'LY[Y52:7f$<7`1F;>X\iB(7_"*YC2+J"OW+%g1t;*B4j')#6FZ1Q*HRX
bq1XP/iLF)JQ&Q-#4<H1T%NKu`n0F<l96[1*kNUbAbOGUIhe%T3nj99$>)l!*,""_qu-MNgj
#>E;n1F*^^9HJf\X@74F2R7#SCm@SMF\o?$hpY#5%,<jdB8VON?71l\DXAJYNM98rj,dZA9Q
0h%MAk"k?At/39Us=YH7Rk/t1*s]APd4]Ao\e<F<F21,^%j<DPgZ:@FN!eloJc4CZtV,;EkSDf
FThs"XPQlo<+.;S)M&/fC$D;E@?<D:!m[!TB<^hd.LZ\b)Ia,M3t`pT,5VJj;NOne,c<tmor
Qs;WE-#]AX1\+eiVG4L]A*MUj/%oFLHR)kahj8&A0d4JdWV6LXm;JDRUmY=%mCAa?:!/bAY<p3
1F.@J?DYS=Sj!!jCH)i15KaAoF[D1[26)*_:/&Ugk;nq-\"XM^P*@qpr>?['#PM6_$>N'Q\#
CL^K$+N;O[_(*cCJ,=s*78DLA$rX0uS[I0S>it:;:&lPj+gbbcm,1S7_nsUfur@%E;p:YUUB
ZHA'anOe!_pc.h1?<>=IplQ0PJkKl=e:`PR,i#+e7Qk1Z5dR5Y2H:Rg]A?dQIi@;8@Lj+_B$q
oFiFMb4P@?:;R1-8@E3J5KQVJ9<&!c#t?)n4%'M=tU'rKRnG@?2O]A=([Oo7o2J1]A+GZU;N<S
W]AmnK1=(;l6.ScV6)k*U^h[FEM3f]A%ffOa4"&Iu<g%a=WF+B"]A`gI\?"GhW:dhh+,<(JNq>s
=Hi*j,"iUHF-&'Pu&N]AEUQG$>%_8qBmDh!OG3.YZUHogI`,*Y"J^jjLC<,8h.[+35HLc)`D(
`U]A1#MqI;:^BiE&H^4(dFGh+Ta_R11dr1m!4@Nh8CDC_,V$`=5=0Na3]A^4O_Ka?r"-DmmrWQ
$7NO_[csM%mNWI/]A>(<2$#aU.MX2]AeKh,BPE&fFW_/p&m'7(q2\["H>AN$U\&M85e+RO60?A
j6730[TT(aC@522@eWOfP[2=Har5.%D2bH*Eg#7?bD4e'Y_Z'4:93R]A\J[lKoFV]A?E?\Fb/#
Ea9)dZ#PSIZU3ZH9-1@q9"0s*`Vn2#JlLAp]Ar$T(aFt_(b:1o4K0A`'YO,m'lONBLh+*QH\>
^Z;gA"7d]Apj?&N)c&l^A^:,(.##-&NL(j$^K;i'4^`9GE*W15(I.9#^H[&A>.cl]ANZZ2fJlq
\.M?l^n6(VIh(\%C)![S9F)oiSfS:!l>[nm%Z:1kfWqgPm-8d3bQ-fLSbB\i?[gu#1IrC)Ia
V2!s,A18Nai5"UN:S73&pd6@?<\^@Jg3A_B,\+ST+qhp1%N=\_;bt^Na!^g7c)Q3I.=p)&7N
?@q`$hI-Lt4`ET6po9Lc?53#s5R;K*C)P\KbN$+ib`"ZEn)HZ>h&b/..)h65gqrYnt671E;7
8!!o+l[c)O3LYJk<B>^k)E2OF3,FJo=:Feoopi)*J;72OkT0Jbq!DXX4s-<O1U7gqKNWZ7:>
hEXl!b5N>1mn3-;`4`8YVF.8^W^"0(ll$%>U-S<M;;j;09jaGL=qAU!CZmSCn-QIRnks(#pd
>pue]AL;.0&:V=ueRk>.dL)2<'o*E"+@2*-'TUFc=@&!ckE1Q`SY,WOJ\IP+0pWiqWU&QK+2E
`oPnd6KeTS$pF.=4ZWF<FW.$:qCJK;NbV9T"kM9kM.QP-'M7g?30k6AZ4gZq`J]AK1s*^3X2V
Y6HargV>G%K9U'VR!^"o?J9C,h%4_Z*#'<rt2#ucg\'6Qu(4D:?&47A:64QPPGO,Zp'WG,@?
CE0[M@i>Yf,9-E_VfY85XiV]A(9;CbX6T]AC_(4AuWFoR.dL\A8iEhcNV_TA4p]AZc>R>&d"fmc
]ALigr=.Prar'A^`hn/^Q0rt$F"FBL%4Ken)mF;<4b1=AbI?lNASMb."%Sb"IZ!,+e>uJkDH5
@o>&)4gJmm4^p*?DWr1FMGDi$YL6E3F4b>t@chjgs8O!imdXL1uaq`]AlnUs4*eiAih=[o)\'
gIS5oV7+,FsW3o<+^KG?$@n=/C<bBL#:"/*7&3P9653-)0)C@-Tg?`p0\e6I?&9ia/H3aNV5
'"Joq.:B66dN1jkK&O:Yn4Ts1\d7Zqj@h_ia3O,]Al:N`Q#fmGhTEM5WC?F+We#%rq>nBrXou
cn?[c:CD<^PR<u\qPd*p/"*oi4k#g;OUSeJ[eX7@5b_njN/!b1XE[q)@ZOP)qm`L6aLMZ<Sp
V)8D`ce(EJ4S'ler]Akjo6Z,YD#=+)ogk!-AdNFhMCjWp2jY*gdD,<f`h!Z2Ja<XC_)(bL2b@
Y-_:-\JaSNZ!inYV^D*MA,d'gQ.\$b8^KBO@`bU<mi+6"G<%b.N>FNJ%`.U>)A&2+Ke6q4k:
3-*jPT=IFaDEGcl'n<o7?rI_h6$C[T[!0$HpH]AL9D=JGi!'rKLt)hN5!&mKgWP8"=u>0^e]Ar
H2<l=sNX&jtEZ8@#4osQboii([IY8Gj\ab;eW=8ib+2TUa0[gj6C^#U'4^-(=mgNM@U\]A@jN
L=oQ2J\ljM6r`CVB:rq$G4Jtr:71ZBclsElPoDM`.^DORC)CfKFr4Rm$k>_;16:@gN&Y[9U<
_*8(N9WH?;,9seA:j6NVuA88!UW2F03W6o4XCu2c@'T>g$)Q2TN#%m3`Qq@u*i\8^hWG2K=@
53-A!K%\8uX.O^[#;:0r+&F;Z/9hr"l=4K3J?8=s3&u]A>A!j(lR*Y[_K7`[SZj2\`&di%boq
6NsSEqfs5OeX]AMMX7$Q$>45RCpe5b,R/g$\uej!GMJ4i;.c2^@/u$\/V(+'F_F)[c[(VSk2.
A9*]A>+n;J1u/(Sa?p1a&M[kVUmi*>^Rm\7nFc#5]A%)=dZo!+H>-\S$Hpnn^0i13Du%nFuFZ"
q"Y01Mi)NgX#,-Lf:)MMlc)@;QCjB.p%C&B2Hu0%3>tt[5C0Z'X)Fsh\(mF0b5MG2k8.>QWu
QAD3%:^Yi0A'8.o"7ne%3VUf<&YZO7SWd!W`')Z.QC)DtPrIg#-4D6[o7EWK@`Bk<<A4/8ME
#rsSlAR=db+akaZDbfm@UUl%<F_qo+o3WE0R#E7TE/\_8c(=Y*P^20kUlA;:$k-Pg$MP[bbj
`&i9YZO,*OhP_HciTXH!:WK7-:=R7)q7mT;>TeYRto<f/SA,]AnkN[NU*kqW4TN9UE-XH($d_
GeJ&3"o$eRUcs#D;F%-deQ^!2;L?XY^9[>,I_7FI\plDqNCBI!J*KrIqh4sRuHJ/i.sVn_<S
*l0<Gb>(^[Sdt>6!"it(pFs8O)W%aL!._EDp;4B?`N"tAIhr)]A)r`C!n\ZGA)[PLh;,7Rp4h
QdY!.K-7[n;j,7ILgMGjgZ$[+[a9P1Eef7++I'!:R&-nVRiBCYsV(gc@h=m[!dmR[XJ,&,qF
@QC&8QoJGP"hR"TtJ%jpdf!0Snek*K*2EBe>lV"[29,Q]A=kFmI)ak]A&pqH:b8ZWMi8iG7*t7
1Ak:Hk==fIsCMe4T3Yq.bME`V#:Hs^Y7S*'.6b&;&"acnDni<`hJu]A[DCRp\$QI,C_)aV-b?
3WOl`#T6BY"-(U4H*>MCuKL_XT"C#dHr1V1!tU>sSf\co3'&OR;j>;<Vi[7;-gTHV_,9.a$D
3\-i>b.uGfDo;m#d\t]A?:F;oLB+/rT:j"rTe5c3+&lUO7*05.%N/5GjP4L*3kW[\9A]A/gMjd
PVLb]AkSddlI\joR&l:p3BetF+>p&p+S.&\Z&885;frJQHiu!EGs(lW"Ss<19%nb,Eme30p.?
o32>>#plIUo9*`qdL\d*tij3KXM(g>6"(tFGYmU*WYAkuO@41m%!WDcY?Y;f4Gg\=T)XFVY2
bO9HSa+_k=BHR7`\(sSStBA5#/l)2-%5dNghio6-Pr>S+oC(24\QQIQ:0a<Y\lM.*/oe]Ah`5
rKjb5&,8rH?c>auK!00@>HR(ZiJHdZ9@93(@RL0:[Ub.!\7CDW#j+@W,1=)"QS/1VD)8$e83
&E;hk\aEL(CD-<PYmt'b<m?%'O3kN=anRAj5I6$@[UED9I4_VKk*Yl6'/FnG1iC1360pa,0#
Cc/ja+>nKr&P_rP?%lI:C3k>nF=^Qn4XTP7B30[Ssis`3)F*Sn@5t-Wmk23iN^@mO_LRldD_
-S3U;l%pi`QYrZC^$i+^.3k'%-j3$:pcfX\"'6(s>,,:*lp"Wq(nQ,JQYb11K9Vb&3dF4A`"
ErMG9^e`^U'cu\?-&C4LD809'2%DPkDd*PjM*a;eiN_gJ90Qr8_E%4j0G[>?gDi;/5W@DW,6
#5DEf,p\P<=f6qu"gZ;Pq@qaW#.GX6-gh)i[gMg#4:l1QqCBhcUpnk&->D3HfLQIkXgimFpW
Zpr%O:N?603$UaHOai+dT*>[,Q:]A]A/s/\dZdV`'ne4VXj\r&XA$Blh4$o$WWAq_igG$:4[8L
r##BU"t68S$j*0lm:*!bCU"Y[:lDY%C]A7Pl',o^iL9B5rcGEn6]A"O?ZP%,9>">$>ZlLHDVd0
7T%emf'3:;Rs3lhIT8'SP+.@`TWArFdn7ldXADpq<CLGXc0MSq;cD-KlUAF5n$,?G3l3L:S8
n_"oUKd,nDT%=2JReK)=,cj=8rmqeQVJP"(H\ZMeKg#-&^6D[8W1jl<$t,J&1%%&j2KU.31:
jPR^XS;BG5Vg_pP_q4Cm`-UsO='K]Aks?:NZG]AK=ur*`U_Do5IY8I3/E[I##=EbZr#b@#Yq9E
U2'mXD8);*:mR`F#-"WQWE+Rep/!j:&Rsu.Gmuh)dR`9Cb+HEZKa8JFTule[MRK-ufBn*;n0
/*Spn50;ks=_lCDrHXlK"3T4&g8G6(MU&5Ud$lN:p3<=QDR?i3["X>2Ft1eb9[L+%6N30nO*
oZ=XW%)Zl,r`hG9X4^/77-^Cts]A3BeUVKH5t##ZoWl=oC.Vj)0)O`GYDTP#9HNT'kZi6"Fu1
#/<T07LkiUm3qXJ4**#(1uA`93r++BbMU<WEq[#4>uOi65($.>h&.F4%M]AiO0X9`[":e,kjr
XR6CH;u&&-kR!KHd"aOH\a^8Jmb%*VK4)B^_bI^W3g9SPAUbd,N.i.O[n6SA%V.&"HTHkogX
<Cb<-N>@J:.=8fi)]A0JM+8;LUOBL\rn3fbS!r&Xk2^_>#?VY=#i92H(cW.sI7RGp0ZVm3&.&
\)I%O=F<>T[X7P-Eo--I1PlmtduKQ0s&;!8/M-oC^;>Ir;P&KqIL9F16e218<5;<8'p(cl.,
U8,(hoc:-tD`<40KN79rNlKuhWRWtWY4la9lbf\TmQ-UE0$<25<cJnG,f)ss^KtNZDnU5&p)
P\k($_CR1g`WLTO7IBcYSAM.7TUU*Z[Om":7CRnYemn&\iWqH-&c#TRIusOJHZF\OS_5<@\C
\kjYCRO!RJ8)noR:&&P/G/7_CXEUgCkG/[!7nGmp>[q_%)$DItVq=<5CTd(j69BAX]A+U@H4*
^o8.S46o$Z-HJ%=V!n%/GmjulkIMt?H/H"d#<#As5qnk%.m&a\S]Aq#fT;=A4SQV>2UNEaB7_
W)\#_$+5IU1Ma\F]A"pn79jCOOQlKPU5DjJ>C!5OHa]ApcopIdTQnkp,fhiCQa:;MF]A?ITP@C)
#6q%?]AY&e[K6'?#G)tJqYmFBg3jkSX(S6EJe8g0,-mU[:8+?juI]Ag%,g$Om`m,KAR&]Ai(S&W
*OcWR0#>'N7P^G\/P1NRd)CtWH9ff@c`fllQ_&RUtJ+D3P:gZjMsq_eo>+pl=d3pnrb0,0R%
[r-(E>$96'Y'[kn/90-nl,p-q&$M`pZf!t@N!.GZG*O9oRlKOrIILQaE$9<fA;7;&rLpnH?Q
eMk:.1u1$*?GUQ@>!m5E(CM8N$cJIVdRNk,>[&H-o0dq*Id>.GIC`FtP`Q4"UX4:.S<ANT:n
cW'jfItk%Xb3(gB#&X)R>AP(%YJYpt6*EV([.*kZnAt_p$MmjMi]AJkWin?.=<^DY$ceEn`I0
^.66-bFd7mqB2^'b4X3:jr(bec:D/F-oR_O8pT$K7e<?2\7MZnaE<n!JPJ/EYhcS`0D$2F!>
rUR145[kTl83$Aa5PmF]AbC7.lA)maUmug+]Aa;o;7SM=uCXm8eTeLZaL=:0TS"2Z'5*3;.cWu
'-e`U>I4-jqt^M6JfADT'J)'K$%ngikgba+k-b9V1K@'h$Md,hd`]A1JT9b@dWLJm?,FaUHM\
]AF,7aq$O]A9B;H]ASn6#lf79(2_@0cXLX<b<:;]AsCoPkH#s"F]ApYV[*NmK`53e]A(_^d*2Uit9o
De4%fFc12^LanDA2\6RVS``$p`,/MZ/7h"]AZIu?[r*0iRf7$@T$aD7o\XD<"nSgZYCf!BrB!
#Ek0Xt)00^25IsdCZ!##cb2S4V]AoE&=_QOiX=Q#2To4CU6Ri`>o44'02U;:OdQ-DuJ+t\aJY
DAaQrk6WV4Ijh/o_*U!chbM0>j3eR@j0[0:3'[33FFN@]A",+Dl/QeD4i.lmB-EbabK``8ks1
!@K`lRk3[3pR.H@C,A*7>*Qb9K`Bci`!b#4Jfo[/[3)Z8WjbO-h4STGu!fHL:0Q_@Db=W%hI
+-KCq:M=E1l:K**As'Ad"V7Z&2J7F,kM1r[)a.as*X1('J,KL4SR(hk<s<s;=fEW!NM9CaP#
>QGD%]A!tlVsdKQg?pTO[#GSF,VYNcC(5qf9.HP4bhQ:htIQRdk=X4*H4UM^mn(WIHocZ0+mF
VT[g&&aPtl.cM1NK"urU5WNp)uX_l#`hdFR0D3\ONT<Z&!QXLH65:1HS&`Mhp>kJgu6@;6M3
O<j3!sTfaguWT,O+q&f:=1MLB8fGgf^YHK%BLYhO$W?nr%gWTSJ53d:('S`MEHaMh4<5!JRD
>"91@0Z\'JQk<0($NdEeZfW)=mK4m1'ZqS12WL?/pkZIA041&Pb*Q>J\1YoZ1:7E&HqVKFRr
n]A-2Gqg!#m9[F_iWF:kSiUKeT;D/e9^Y-pA$[n^lMql"go!1^g$s\c$Yn4aXZc-k[E-HMpSh
d@p2HC7m'fO="^>Qn]Ae\=FUZ4gMfG"b8>*(^&laD%P<C3q:+o/Td:>c1oN%)Tg1WcH^R/Y*L
(YQqK+ZW-,=RDq;-F*tXLTCR@+=-lG4(#Gh*-cmA8j.03+VT/WsNFZr*_8\VH@V+n?:BUSe^
?t7N&sD7?@Kc<cm$.'!=oO9C?3OBbY_&mD@\35&FlbuE2AejTq9+TlZcSVLfe%D"]ASBO>jV&
foW_5rQ4%p-ApeYM-n^+U+NRR`b1`_Q<kFe3Ll;^L:_(\VUf?lQPT[mYT8V@5Ig$b]AMISWI*
oC1fjlf_![r8H]A<@+,f>*G.1re9QbG*<K.HQQt+^\@4VXEGn!WCtWs5m:`11:Ror,ArDhPC6
*f]A%?(UoR$(pf^aHE^`(@R&JEgHS<YPAM/47.4`_^YtPUlemB0WtbYLOCfgEJWgeWkb6i+$%
Sn`'2lbeu0@m;<dZ$/\HX1I]Ar\!BY']Ag$/#hkHP6Sb:/E"dt^&(IhHee@E[cEc(A7P'7$#V1
Ad3oe*=sJY2,YLdXYNFkrc*:[!/]A:87"SJFf2+Sq=MtuGFWjnS,.JTCF")%-HH^rQDkYQOSJ
"P7TYKgEAj-5Ba5%F]Ana\P'il]AJes]A-K7fj_97pG^V^OBXTlX@?jrZuRNLCR*aeM^BFf!7]A]A
YlrpC0)ZI:/N3R*hKLLeI94/V,-/"eL5*_YrL;sh)*2%&XrA*b%j,nBq-@uN_efjrGFEoG5,
'&&s&YO&73j6_a?S)]Am@4#hkLeh`$nTZG3$-LO)8XkdVU+j/B?K1BC[)\9OKR)il[d9R0m5f
9b7:J[1-AjQ29p5'6lm)NhThdaiW]AohlR(mSq[g6HBIHl=p5Z0s**7KXbaugI7mE'-2ufn=9
;\A9j(t.8M<Wm'$uHmJT[A<fUqk0=$XS'n&U`oV,Ud]A;nu1<U1DCq1?P5(-SXh+7k["Xt$,7
dLnb%$e68acKXQ5jPQ/rTFg6M"Oak_ES4AGBUY2P]A?R+8$*9^l<H5(>D/6*_%;be"$ZjlH-P
dpu_=IdO.k[iY]AX+).]A&r_CjNg^hbC2(n(6U>s#^4V#AAL8d#=7L\YRlQ+UT?3m-bPFMSO=1
gCf%GG1&ro+(&@Y:$/`Jkg_*]AY,T_0k"/*\&$dB3M9C,!d.Wjh0&t+r@DFm!7OlWcCNobXK(
"b514t):me05O/='*IU8oBS2k`IcVt!`AiWi>o=KZ6;"fl1Ipi6,hI.?[/GWFdn#>iUW_rmN
\i4aK-q[T;(#M+';tQg*M8IuG3.1ZLr_0tN]AmpO1D@hCdhhF_7!Xb-@-_iZYk?0t!-!DU<`/
ntG;!h[(BEsS7[SJ<97@i:L:?,YGT5Lo\)D1K)r?=YiGEfYrdPH-+UBC]As7X8=5'm3pI5aQc
h>Oe]A`PoT1htQkc`D>CZh?LI*D/^HL#.MEcE#[R:\%up#)r(Y,_/4-1g]Ak7(D/^Hlrt\94.-
9U4s)G`cC?qFiOd0A*`BS8>(B~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="104"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="104"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('DATA1').children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATA1_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATA1_c"/>
<WidgetID widgetID="2237a07f-0c8b-4bce-9d6f-0dc2cdf989fc"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="0"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,1524000,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254000,0,3086100,2224585,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="2" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="BZ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="0" r="2" s="2">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C3"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C3"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=""]]></Content>
</Present>
<Expand dir="0" leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="3" s="4">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="3" s="5">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="指标名称"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(LEN(B4) > 0,CONCATENATE($$$,"(",B4,")"),$$$)]]></Content>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF($$$ * 1 = $$$,IF(B4 = '户',FORMAT($$$,"#,##0"),IF(FIND('%',B4) > 0,FORMAT($$$,"#0.00%"),FORMAT($$$,"#,##0.00"))),"<div style='font-size:14px;margin-top:1px;'>" + $$$ + "<div>"))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="4" s="4">
<PrivilegeControl/>
<Expand leftParentDefault="false" left="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="88"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@/<%PM,ZuS`?s_fP&^Q)O6<aTk9NT/;KmkoRM&JL.Etc;2TH1"b?Xt.O)Q\*i4Q]AFXSC;La
:JH644s4h)@;SrpDq!gjed@pqu:Rc$K,rqc]AW\g=tg1:]Al9^8,$oU[S>]AFQ/@S1D;,*Ns4b4
#pnbLlYMO-NN$28P^N_7/2)jNU\i&ts&aln%c(<b'cW'jhr7o+qp=VqD\9km#ql8aDjNukUd
trE([Q`%u(\qa"l=[Al6d]AAKV?NfTK]A@8[T>$["c!o0IMX"<7h;*,5*Sm,YbRJ.iU`;)mXg=
Tk,YataP:bV]AhDB3h,b^)728$rr5V%/AapV,f27+\>T#)pncfpUYU=l*[]A)^%1CZL[SLHe?S
JA;,/g]A%n&+a%Ol[tVtBVZ[IeVr(2'(>Eh<-hH57(<EMAIL>#8^@gk+?FuQT$iR=a="0`T)
->jOC.=[jE'TpO%t:NrVc"QW3P%e>KbPmPEP[S'mg;%8jI7n]A>h-`"e?3Vi(;j8S)IAsCQIm
jY`cfZ0@-L*)ZK?^+jena`.rg\o\YR/*/H)npoj@WJ]A5+emT"PMIDRZ1m`5^]A=Y$Y0P1OYqm
k>2lEg\;oIk=Cf$oEoAbS.W1@A2S9]AfWO/k>/?=-`2?6qHi[El0+p*MEodMbj>KKbAL.'#1J
$goFmDYRK^9CqqH,h=o"?73@FUX]Al>t(#fsqL,0c(IEC2*@Msb&"kk4ZoDa)#*jX<FP$E@CI
,l7kU]AZf.b^Q@1\3Q*j@s6C[b<HI["jm*hs>-@Gn1mn4Rrq)H.5L1-shTJ\cReMF@rY`FGO\
q6B^=OiBF!,5h<=r60K%C;t'AaK'7rQ?HUMos"j&bHZJ>Waj\%X1hPM3XYE.!:DDMI$i=RCM
;:L]AU)ll&Tq>C.-n3+59;&g,m&ekkR`_1G&d@mMTtb,_CMK:3l^hOSBKT!_mCHurMt9:haMD
uN_LH'Is&Ybq<DI1#Z*eX8VJIo:]AB?Z<MPG,+_"9q_uOf]ABUd%8"=.(U)a\e%LK1\'8_8C]A]A
n,(-c<8U?WL4'gig\,QT]A*"=td><^MH.)"4JdLHN[sq`0(t]A3TAAmFB1-OtU_K&aH/7oJ@ja
&6q!R8jF?Hl0K3fWT[ZHK[)'o27lW^g`haG5=6;VLpbRQl_fRN#.qbZgSuFG/L4QQ?NZO+_\
j9b=W;i4UE/h[<X@4KL*6?L@a#H!LW+(G5Yb1t8Obar]A2JfJ^scXTgQcu^ob<fsG`X+QEB^h
A2;`'>#lJD).TYS[BXYlh.u$*JX)oU\ai.j?E!k0<=[HLL<f0t-DrNc@SE6JlA<_;YlQOqYn
,FrrS*b`kgR0&"?J/bFh\37fEkPVtY0"P)/UlTD$R_W_X1i<"WA8hqlepJ_?o\>V:&!5()dZ
7D\qZ/;'cP^<!NR_;_*lK7OgK@PU]A4YYP3ZK88caj24i/5[*8n5`A1LFmkX\S([@Y@7f]A'_#
CV]Ak'2lqG,]A6h_RPc_uu8`)#js&->sc@mb-K&'7[HkX_r,c]A$mNE,Q3k1X[RMVH+m7n2Scl;
g3sBQM(tU;iYLqi,^]A*p[kt`D$Ngbl(`:("]ALWWHA(YqiJA-k/?)_7`IkS-`r%]AO3ZDd.Gsm
;EN!m-Uuni"ojuQR5rg?V4t(F/982\?9jc)M'$>bMFEPVldE!Z>6UmNgP\d*G?+X-GZi0,0A
ZjRT?fq#!nY6+8,:-9V_+oF/'X%!A[E5&PiVFG+XH$_8Xtuh="(1t0%RTl>fQ/X21!YsPG[P
T`itASQ&028i-c+e[I`lQ%FQaID$TYG,K6HTNmZS(^rdK0-S/h,T%jbd"SgS^*+g.#.<<<q[
pc?8/Q3gc"Ymkn1Asm1;YIEKmFIIVEk]AEWA7ZCmM,'e5e)b>a!>MXS"$a6e.YNU!I0<WXDUE
_f6H$\EEMXsP3aIrG.20=\qSW.:kAIZp>#)T^C.SjNL94hk:"]A)$"GN&E+ra[YaEek*+ItDN
P61bH5/_!ru&!`SWUDI[c5S0fnaRO\ejlWM'R0e8OCYco:AP,f2A>M90f8WTX$k"7QW:n0ic
%b30N2CgR"nuN'r@ctQ226^.%q<_8hiko^r49c'<-Z*.6bS;nfd4fY6OrK#o^.]AJ&Es07Y)T
6%c+kIL&V!,Fkmk`YD<9GRZRGgEFXD[/8iCA4-]A."&lEt)b"N:%:,VWdr7Fl!sIiIr,mqQIV
FMaoc+>.rP2[=\IN%&L_[`4-.T[SSX_-";_lN=I5n^jke=6&e)*`SpH>UBB)dj1-t!B4Mk;V
aAZ7WFB6!rmk6C_\bYP8mQ#MRPc6*),((nPtmH[e/[$lp`J$]AaHr0HTe4HNl5'N<kVd__`D,
1#Wu!u`E6C4lPX*\[ZVXu(>>Cb#$LW^]A><YV/-u2:26u8imnW,,^>^@NO7Db6;?J;Z^%DrRU
#"E_eVW#.&YOc)CO#p0k_BKa!B0O4gmb)ppFC*tRHW$i(1B/QSjb0q.*D7]ANK-G[GW!0Z`OM
^3aO:$eR/<R;nds)OLWAkIOG-jpN^(#0cd0K_B1\!<]ApbH5gnuU6]AW?F-6\>"_`28!Jr%SF*
WA\e3>VgB6'V">[G-$N@)`QK5Lp+5[DYV22mr$JGeDFB5<;s"Z0lf59i`H9`iDtXqR-e3^Be
%[n@60_5:K#uHnZMDi]AF4N4piZ%Gp2YorOuN@1@C(d?ro3LA5fp(dU0mL1H$.^R3_&EFnYCX
UN)mjLDDO:dG6SF-VJJKI>6LuWC"b*'Li2m!A<pp&]AqtKF1UF'c.KkYKBW4#kP!2$"j<j2k(
F;`9\r)4oXG\<)dRfQaU*1EE0B7(`*U[SoXCjQ0b>C+aVgNDqg2i(rZ8[p]AS.X^+a<!ur1Gh
di%Ds`[o7`Tk"/*@MnLkZ4Z9Em!EJ+Yc3:M<'Nt125,Nq.B4ci1$mV$AmMgGjs[?.@$*;T,5
<&h,Tl7_ir==?QtQoTpu*!l1=A'#.b1Lr`^'^K?5an97fP3\15h0L'p\FIP\_E;A#NY\1Z8+
%0X>*Kp>Wog&XNCpuV`o6C/<I$Z&*$mBB5I[LL`\.4mHp^:'<hn.al1q.EpJD>PBWT>kNV>t
#q%QN#$85pEDtlU@d"P*ikSP55;eZ1q7od;5a+1ganD__/''S>>qpXJ"XiM839]AP[kp8gmWT
j5>]AN&)qQ.Q<1Eb6YL@`I1]A`J<of`D(ie)I");;AK$m[_bb>#KQD!4fH:I:\s2k?-NWslr'%
mZkip=LYQ.]A.J`$p)23COFC\85A01HNpJ<r.r\@(cjIi?nfif/lT@V9-_RM9A,G-7QZTLgsA
S9k!dqh4D_UOl![N+nEA:c0[rKjW1'N&Of:b=47uHGiU[T64,<JK7^$lqcep>D&@JG"##JZ*
,+@ieS<FY:_kONbaTd>jc;Vf=DD#-<p_3o-.4\(.NQ$9@cmuV@rm)3kIF'RLJd4m'Nln8Q2%
MC1d!<6n_F#iB[Dq[h8DUN5DUBdoq@mPHJ0"M$Q5j=a<W`q"&Y#_4MKs]AE4>0P1>3bGI'FRj
maEl>[P<"JM1Ds('J@j]A;,LVSm^!6Nu!Jq-C?V5T+apdH(Q[I*'a;X@K-/n0Vi#42c8DaY2g
Ek[F*Y;L!gFDCl[Q^P[X,3NcYMN`+$-%&KB:tW!$rVW84l=*-7XJ(9b'&X>)+`eP0B$3-t#i
k,kQdV\\HXa7Y+3p7d="j]AQ`gW;<o*8Ao1/0]A)A8SJ%/K-=l7TVhFtiTDOIqXC3FXGaT;PTg
)7>H]A)Q83).0m$>]AP:RTY#:$q*rme@Ijl`oSt_db,m/PajiM6h9Bq[#8!+CfetF^_7hTq?#6
!E9j)#g!gO^kglIGe(f"oXCi9H!d`KH)fUG-$7h(5/E&(eV-mm;c1;3?CHg%,\<l3b5:T%DI
ug!lT]A'OrB+-b]Ai.U??RuGnl-M*^i)iU>Ug2'BAZ9prk;(gcPJJAUR#P'%1r5/"FEFdsb'XF
G'*UNHi/SDrS`*N0.qo3B1Z<&amK,!R>%8Qih_op9YE01I*:,=U90;Qop&"A[.1gD8Xs&(u?
2M.2&%M:]A6gYcYkJdX:!!+c>ISI>lnKrih#PgNNuA\ItnipG^J9;"tpl\Z/G".%g%doM#S.?
IUJ5%7o]AM>X:o+2glra[hSE<DgX/&MXg<J10l*LQQPJ$r0JH:2DT&?3gJAobc<M+DBX8()J+
n$1(fC0?YE>U:F<QfIahg@kZN_F"&NX,r<n$-VL=^d_n\SY4osQYJhh6@C$_N/7hNa%)k.Ga
j;;k3+s1,B5&C&C-R7i,Y/^n'@X9,>8%+D$L>?2XGjF->4mM3`@&?5ks9.0*);t'>0aR1hg?
`C2H6u8opO=9UVuUnWV$1&j2"_0XrXCl9AUAVF@D"+M?YNPd'g8$SmNtm\hYX2]AR*XE3<=lE
ounK$ijGdRrmo5n(tkX)/-H_>T'2uJH5bKqHaJnq=>uQ2@bZ0rFBe:3M/2HT&EVW9o%UQ4_K
hGn9J!5U\"r69fsUZU+`5?E$,c$rLlKR]AS!ET-1Y(gka^]A-p@>r89^FuMMf'Z]A\lIb_X6aO;
Gq;-"k33`A8qtsoKVC)5_CV\Ue$Xrc2bMMlspKVDPr`*4GShi!p+#AC^5BL@Pa;X9kTgK?IV
H2Q/U!*i(J>JKr)qB(AJ?0)uGT:qeX?Y*rEGm!seF@#IE"?d)FbD1L@6]AqP&e@g,TD!WZhRj
3okbRgq_p:\AeNWV2LrfoI#YRU5]Af3ul3+(37Kk2uc1hc6(j_"R;)HWB(\?9N\17=G`GN"7`
^*VOj)f+OK,XFEm+C;pc$h/,XUOu`Ic%6QTZI$#mZ(=kRXMFL>X68(#1GLnM%[(.>V8J+\L8
KEDT(udS5Okrm(D04&nh5J*T*N1r]AmGt0(tY]A;,,2^#R_f`Fnkdo'\8'kS^C\')e)VA?[+.V
XMPU>XS565d7'c9"AlbLg*l#=Q:;pprrEos+).acO73tLD.ND^tp`r10a#*7(gI`copXm3JG
b&C?mk.kN!WD:Z@IRe30GNfBM<(RkUbJ)KiPI.2"^1jR]Ae;`A.b>7o6QBJf7_F"o4LqmCA+3
C7#,)+cijN1<m3aU_Yc-]A=DIb$dFm_$k(($H;M+B0%jFLQ)$A*-mPGf0U"`8T!o0te2`&V;'
]A,:Z[KjGUkX,lBt<;F1/AP;4=1^"8l:geU%dNRH.BaS:&4P`I!>hV^LKSm1`^RZmk'AQ2B8V
+hnonp^IZe(Ca39`^-nX2JsGAGK(>5G"9V*n>?2e(pl3h5r8(gs!<"?qBh8iOW>_0.tSf/1R
PXu#gs[<t_X/_#n8@JUi)&V[eMB*SjnqS9^4<snPKDtaW\l4S=cW(Rk4:a9JCd.ZN,]Aj]A@3)
e8"omT:Ph%a_/%%5NTLs(&C9,:lLeO'A=!Y9.Ei_6i,^fqb#r7(5E8XhffY_VDJ,cg>3g%/d
'>Fl\r<0^#,O'dFY>$;/2I8:E*]AZ/nFYfq'j3c]AA`>-\[pHi%+,UPC!'QlqhODDm&-^NjLrf
;#bS1L'?gXd=V"6#E<6T6``1S/>YpRU>G5aks^!MJckY0SE@J]A%8kf5Ylsj8,(e\dA788_;6
:dT<gU!Qc%G10b;o%=L1qG\p.K29T&/;2hIu"Nl&2%!E+kFLLI+<\J\QYqM8U_%53"J]A.i-\
B5YrN:KL(Vu!C9f;g7KVJe6qp`<W(3o#F625!!M\,R8A'7eS]A&*0V9VkVodPig+EAr?D<E;P
e5r6[qE[u8:_20a7BI"\jZ6X[;lJ^[UF8F[AQ2Q91FtpR597DiUTuXdL!K(6Vl[aV:_kF2W_
_T4D"4d>/g%"d/C[2WX":T50rZAF3UX]ALW$lb@BK,E?q/FH6Pa_/q)saspO95uC(kZDGZCcP
pMffRfFS44Ap4-t:XW>D)CfW=OhFP!YCoE&S[i$Zn0NOJW?QY#[=0n!8V^*$&<>,/Rk5o/5<
\@-H.6Zc':nTWjh')A1)dO5Ckpn,d:o:qWO+B(E$kVtD";A*CV%+OVbcVW*06K3lV7peo_;U
,D4]ArXPL=NO5<Zi,%ej2qmXM^Ae"IBIh+=t;!'`!8bhN[mklODGZM#GW)KPo)_d/;SV>$>F)
c?VqOTE*BC9q)FK!u%eB<`([O?mV++a0rZqc;C)pf6;7@07h3`NLZ#\>)^)kt@<k`JnZ1%KH
%5[tq4-Tufi[F3boY4QP:&*Jo^'26adpZ:Q]Ab%/kI8=S?GK0V^#VHXLd$9HdIhQ?u<2IZif-
;nj@O3D%]AukCoO$UUL3?c>W(<ST?Nig\ZJ+K&9Ic?%;f\2ulX;21L`Iq7[X9.P2OgE6>">.>
O+4dPt[TFe,nLaV:.=<%YGgRcK[%M*6,m$J*kTo^pF6.OKp&"n5\Ek>hOaS9%r`FjHs'gR"q
'7IFGJX8W".'>.";D3?0M@."6(o>05k'cb9Rd]AO/\"e?XGK&TR\AU3!n,\<7qZ;hD,FrZtbN
]AVC>pIr;>dI&:=;k(>*6Gi7n>^pIXa8qCQLTA=,d7O6rFAJ3PP_AD?)/JcuXRDPj0N:>QGK*
gEi<Hbas8/_12'l-N'M%GRb8Xi^dc'_tClLsBb#dE!H'537/K8oGRdG*Y4:Z?&qDE#KD,Km&
Ds>e/0^b#N82U'QMYt[+IQmOY.f9jjCkqo<DctLNeR2>r/Od"IZG-YE=/+Z+)c\';\F$m26N
R9li#*9a2!>!O]ACDc<;sHCNXTa-H1hMtZgpf):k4p=A6oC4jU=!s2@kRY4:KH$K0Rj]AbSB6f
g2F<u?d*tQQeCenAR,-M@KdklV<Z']At0]A!f=#u-i($c5>TgrQBVX?H[XL^(jIa9cGF4?$5sQ
LPp"o,m-fUWP64qhrUbhto2-3JSsE2jtkd9`@.:s,2X/<(2SPm[Xl<i-irVU8V(?N'U1!2t[
".R_m6]AR^7%VIjtm1)eEcV>+I:Z#%g'*NJN`(Pj;"U>^`<klWY>fkm8`RHn<4c4CV[j"9XP%
DM62I%PFB"*LC;uE)`*kGpB,WS1!eM9uh_QlUSo=W)WkqZu78t[@(o\WG;*TGl@4WZ6LR+H1
2BD\=3>%l->'DYlXd$gEIh=PPX:qUr?:t@2+u4i-'dJG"S2]Ao#HNZ)GafWYK6GkFZJYn?l7l
7kSJ^5p-0sd#l&EVH;m-WGg5:"$^;EZ105^LbrOgAo"Z'$R+2c":)OM)s&?GKnO[/l9P)%)S
]Ar2+%U!/_ead'T>DtGf>B"0R1bSe8MB27"5??1`]AmE[/fnM4Egm27p)`[T2`9sRIm<]AfFj9.
iC+daDYYT`G:2tffshZXs"aM&[d;EoS;P`<.%GO:eGU3hb%Wek&);+U*^;j2oH4>M1&,rfq.
W,C'cMWE0SMNKq,+j2[9n[`HJ?=9U<J)3P@#_d>+B(*X.T%;(gCj&HLP9SH,/Z3<GTAX!"\2
Z)q4POb0Ljm.!pmkVm=4)LOp;'Rb0O>EnfGNPT^39.j$(o&Pig\4A3>SselJF\^FCLF(@(]AS
j@L+*CAcNc7rG,q4Z2P;6PF98m3Q7a9LS<@iEtP.T!rNU/4hJLEo#It(mYrtjP?#duL8Rop5
`2u8:J476S>O1nh")[.KG6W7mY<K97XA"hYbmH?qO?S';-U2(Ci[+"<F@[s9ri)2UQ*:in1Z
SE`1M7Y8k0\mN'/2iVC/0<5OG0cYaX5BqJ+IOYUBLpPraQ[E0jdKbsINP-pV(r.#pDBLX\L3
.)-IHBt@7!V+/ru(h9qDAcY>G['-8QrOlnR)h76m`RJ2_W2-KY)p#L:%\J9g)?K=Plu)>:44
a$PW^Du2C96S\'GT=KV-Q/#()Ob^nOa'hGjo,?,P#i)pWlFp!pJ2akM/6'gU;aVnF4&0-#X^
p=FsH#d!At(cO4C\d#4ZX(KcnPLdAo1nZC5^oKb!hE$dUJ#\&k\k,k\t;6OBX1nP8'#-!>*/
AYdkT)@`g/<[4sE"'B2XtE1fXY$*0/1!1h!f!Km*uP,abV:L!E8^Je6OrSnpA5pA0IZm!XbC
m"*'U7'Qu]A(+9fbG%0qr>%g>uOAebVSLDser8/.)fd+*t@hV3uV=KiG3rY/0\P;Y;TfQ$=6K
7d>78RqJg1qP*/&L@8":o??44cVMt5VEFUN*X4prFVtV2gFPXTMUSKdqaBM\L%e,Dl6LQ<n3
$V,/$q;5+.&YCXB=t]Art</4#rVBfb79FI89=%;`272V=G-Qp=)dr,gG(.]AWR?,=@sH\h)M,-
Fq6(E8BpHS"*<X3fn5eLN:6>sjlrRY6.J>StR,h6pjlRG-EHNTe/"`NC8fa!f=0Ur_QQNAFg
+3%odPpjNBCn%J^ua;qFoK$tL?WHKrC8M7[glK-pj>*--W8ML9:,8%J>q)s_s5s`'"_an.T&
o=$TG[o7sLNk[G`k,*@9hA@c5Xu\ceNF=RUF0Bg^Wm#/aZClk<T93[69&!A(U%qp#82b>S*&
pgpgaV-(7_9Unfe$;>]Agn&';Df.6bEg#qpc0Zd9%[rD4B0VR*,"b;FmmR9"Y!M"E7101.C7K
>[)[uVaN]AMN*=FiDFQltCh-$r8Z:RPK>jBCh@)F6i@!Y3OX=P[_a/.M3KY')jJ\2S^k3P@S6
uC?,66#F'0#ZidUWm&_R=WT]An;.-W;=P)j*qOU_kXL9%aFqWoDgPaTaX'$X2]AR[4WBO<S&,E
!Bb7n_Lkt#ug$X=$,<RLeX%^\9UmUlj7A;hiX3Y7Ti5Jk=V2tqY/W8C&2rf$!DX[V;<V1.>&
:KC]A*Jj">pOtf;_jKj.7CFS"H_oq.DK`*t1LDmd\(l3*+1Omo61a`LK?6,6mRQ]AjeM_)1ZcI
-Yl0GN<Z8EnD8#[NaRTDXmV4\lulbJp5Nnn9OQC$5$Ahpe(@WMYN1rV>$nA7+#]A#9o.JCe8"
(7Ug1pH(8bMD!\J#n?Bn"XMXOg$7$Q"t.\,Ck;<bnc%d1*MpAN\Yg1SaH'<,NE$Q+5o,ngL:
2X([M\=@jLsCr0Bj+#5@-;RhYthOPhA>Z_ke\_0P-dBM%4:"5G)'n>FRMN-=CI[$JR[3UpmO
"LT!?[;_\S5:O(08c=\"8+g3%`9UYR#Pt9@O9^(B(6Tb3_;O5T>A`K-O$KU(CbOV'Z&#)MsR
IWG[*?(H=+3u>Jr0&br%6gapnaTJIp=61aZf7JnAc4R;QpWisaWc+*q>5`kQYkSs^=]APGkNo
AUZ[K0Fim>S6<@NmT%5KZL&L%I_%qeU.Z52ZM:=QcAD+a9(PGkI2'jd<Q6B07aGlR=RW1iJm
MK,D0);_;$&`/8:[a0prU2*)FTH\aEmF0C*+kZT>Z'/N?G_)!`;Q,'[,esG,\`WAB;m*^@?&
3U17B0V@B]ARS1f6[U2.k?VDD5Zeos#Nh\NAhZcP+L]A390U+,]A=`B_UbtVMXG!`]AhKg3sKOC,
52-A-?!bW)OqDZBhGW>mIR!-(E;nj9q)-2-O`&KH)eOqR'XmLe:;dKJ7hqK(N)!eGLiQ\E0o
Xs*aD/0ms:-=JM"GdS^)jIA;d"Q<6Baj".!N0*!GrN^O=$FqGG\9UpKUK!%':Z.%#.norioQ
/i%!JAc$)Ra`sOS#l\q@qmk#ZIsi_*S-DXEE`e\VFn=k]A_FAL(?hZiY><H[)E:e7S^HFp,eJ
9,bW$bN!eN)2#"W=:*9_Ee4nmp.d`/Sm&rTQM&nHDjB_=98lDB%=DhubiroPmI@=G3VIh.Y/
O6R1Oa+\ZpX,.,g_W1*TH&<TbO*FUtEF=M\IDi<*Ggh`I7'LHlR+:HR@*B^h'>P(mgb2(p@U
'UD410QLmM'"\e3/eZ/dIT*Q$X'EK:MB.u4GuC`91k@&gVi[#TS"A2Xh./ZC4Q`k`8'^N$s!
&<*Atd:gEWLl/$tY%_0*_O<Sm\B^F&g.rOl<VT3J$#(Y]AGE$AbaA1XDN&4fMGg.5(m4M7/4U
hG7>QX.pLrN*.auB`8[^^6rIeb>0?og.(fjVp`+GeHh1`Mp)FF&p-%9OFA4)XCU3-(r/W[l#
L)jZ\61f$sr.P?=#fooC#_M9n$O$QX/8Lm8S+#"qYD0642H$EB6/b-.dRrr@U(miJFDIN!5U
]A7#qg16$?1Q<DJ0^"teXUVD=8k"^kq@CiL_m-&:96)k*B9,fn.qf78YWoVU%`"h)DXJl"LP8
ILR.q?m%ZK\@&0Z9.UHC.F>P>'X0Bk4N$'7SG9#q*c^P5j$F,aR"^QQDeY^r;Gk'ANm:MT-8
R^f=7U,L9pQI'If;;NO5$UXF,gCU4M,M)?qe>$Rh!-BaG;o$&'BechF2fA*,HF`%+dJ1$iIX
FC.nJ;-''bQPGgG#T,%rj)pB,R?A.g[<rUt8_*)/ibT9hh8>.l+6WT8(Fj627f>ks_%i3TiP
b&F=&ZN2,ZsP`,mf?u9QP,?J(uJMT18JHa8ZT[,!(F#[nuG%Q/]A)*!=OIQi>_s!"+rMJo5\V
E_8CW>-8D8c-bDCh"lPhncCNLk'suh-EXf(!AHP5\^@D$Y0"a*EUUY:S?]Ah?o>?4]AN*T@01#
$uMt"VfW@-rJjU_[cb+r$-gZd7FlY>5_&ja,&@-5.+W.TH&RBp`^OXa(`%Gb3`jEUB+?E!O.
-_35<'slss)XrP,KZ$bqY#H1RQ[&u:+^G!KRq=J1)TbmO(Bpg?U0]AX5sRpO`ubWrP;9"IAb3
IB!1N53Ss+%Dd/T7csLb,#*1mq&1Af=E9tXq]AQ2mZ6S>?#+-Vs8fERIMut"dS=^g/A7.qDTZ
``h<:;/A6,(*l6>6[2Rg(:6R`=/@.hi+_Yte-3+g2ekbKnhn<ZJn#amj4f2p5WPIepd"6YD7
7"p1MJBNAM"Xe$99%(:*os8!/n3VCnrq/=;:[G2TDr^`jsXeTI`>S-1,;dg-_A@Y<)_l`qZ(
_`9_S<[Z2!*%Qjo6L&HF@X3F<#bP41F?Zp?k[AR)$-GlF2T!VYuYSl7nIg?$&&g0m&03Tc^0
M,<Km0A7kN%]AWRg5O1r&fq32.?^Pm;H_ekI]A%(5*Fnc^=N2$tLPIi]A.QSPd?U*pDOKNA+&tO
dm?sHdX2#C-'%pGEg9+j?tO["ms$l27b-uM?:lbOC4#0YO;0`H(jk5'BI"%%7ctbNe_rF!=*
cT!S7]An%"EgIKo.MUU"8#,'Sl\"]A2@g17I8Jdqb=\iZhV>r1dQgKL`_s4KPl"*46EUKpEL)<
$D<RK,=M@QI5:KA[VkuVpF:q46Sb#pciGAEDZj4]A`a2Xu(8a9RCA-iq`AiIjjRZ1*ueJ1Z5K
IdjB&u3*T#*('K*kODnS>)s"T"+@OBbWco]AsV9W/Qo3Y6k$EF]A,^-ji?W(P,=^n^iIEh0,k&
_ZRoq8JJpUkE2ZnZmkmdcd'ThY)97h2T$Jg]AYOMprA<KlG%)TYl6P<_>sX/40c"@RVQ:s[h5
li@<B`0dYZbZs[SI32Y4Rf?:-HJcf#D/#FTdMRHbc6U,R0HER-T[Y)+1(\+3CmoQ6T00hh[U
?B87WACGMbK]A"DOBn.mAZCSf1>Ac,lbM"O"1<63hFc;qJs.M^9?)hOd9[.g9;rNV#9g/\8j(
lK:1o<D)6`>es_+MkKV_G<h[X\I2rZ;F2qL)M%GRI<MV=MB'I_l9=eE)16<NI>L)N]AB%ccOj
icKK1pT>;ljFg7n'F@\O7R98LoG`T'Sb<HKZq^tatT(1GqimSqD,$j/K187.?kfHFt@dTJ#:
*A-n=P+C!lS@eoR*ea+6`tcr4_a\*Y+,pG$*HdE[ju94)4Jqi_[MX!*HVC0p>lZT=XYEBI=&
A/%8qE7iDNFPb"Z_--M^iXUt,cgWhSjk-ARh_BOQ8!B-IQ^E>IS#;PG$E(?V33fRH?okuP43
)kn?<1ms&P0)&e_sh#=B5R`?LseB]AbJ1[oRePS4a^_A3DnOQjlc\L,p=o%lUQSp`pUVk>Hn;
b)3dW/Y<=1+P(a11(;A7s#A2+@X?Jiq=RZ)+Z+*5TS\@(5KM$*(>EbmsPC.H2eUl(1PPukf#
BTWu4`M*cDMXjmQ%PM@6Z'!^Sia6WqFs+GUQ]A.eS-&9id<bjbVs.&?j%Q,<G$rKoOWGUn'<V
4@R#NGhAf0)FjFE>M%s==M'cTiu&.X1lUQN2%^`>LLc#\ObSV`STmGLps7FN,0TWA]A>Wa(qt
e0<;SU(\d8'E_rB6$Q(X^9gnj\]A`DLoR-?/*F?kYA\>bdU]A\Y=LZap-LsB:?^)tsn.,Ju[NG
*hO?6>V>Uu'BLjm86$=EHW'\dlu.8C2>T!h_o2B.-D0)"ZV?dEC5GH%Oq;D)(ITnDuSX[R,@
`Hu"ZeP.59B*n:'s[he>PrDY713_*?Z_L@_-:eS&XcbFt8*qBmbT9,j"c(4Sunh63'(S[]Ang
B4F#/eVtL,bT0:dgG>lU&;N"HsIb^d?iUo>;+.B\D3BSYn/VHR9s2teEu+s9MHni@Q]A;??oV
qEBXc_(i!^&Cd0@]Ag-=OZdY_$NQ3@RkhSNBF=QVG,g)B2?jL6:RX$(=i^V:$;depan$>&MI>
/J%-,d?&ljB[UCj^%2d9X4*^>gMS@?9_CYkL+$4*kna>Y3\g>9?A_'6QZ#Y3C.=[L41q&0;Q
e@&IiG"X5dsuJ+8<X0"NtT_g<44C;3583I:K\JRRQ15Aj_t&>^cO]Ag=M*$$L+A&3_C?hYdZ-
g!g^.u,^B+Z+2?%2S.NoBN18Mk5\r'jbjeKjKMRco-ei"E<H`PBmELuS4e_9tXZcZnq,3r/I
Q4PF*:q^bmM9ZSeL3kR'P2tN]A^_q>jLU=%d4%fT9BDSUCFuaJb8ZO?F'3;-FP^XH!pau6;@Q
D8i.o3KfE`kq^VI<s0(e$,8R(l8^^A3IZ=h*XYML?p#2R]A^X[X)dP!HGX%Dduko!><R%sKqE
+So@OR1JR(j!7hgVt/:%@)1.)Kboff<FV!anuA2W&uB^tVmu7Z,CW<5\7C;VIfZ1d%=U&YWG
U6$%M92rH;AgFktTf@bla8U:iU[$D(Y)QA#^Z.UED`[94WkKM@u)>XL>c=%!^nPd/2!,r;0r
eV;N@tN=9eC<2q*&!FJZ!YJNE"B9;Am$;#:eJ0I%[Qs\?FAVu?i5O'#:fEQEZq.0d4+CU[SH
n>8FS?i!.!9^Cl:FN-e1t3;*I6+jsK9j26Or1gE[Cu9SM?6J\M^U.g*Qr?rr:8+768+%2Gn8
*NfH_gD'_j0ieP7FG/P&E9dYrh1,"f]A2B`EeF;c]AH-bu&KU"TG5W_]As1:BAgrrl5Wf)!C*?F
!Tm`Y$^XA5W?#5HTf=)BL0I3bnloIWk@.naGdH@aU"-1BFmb%igOU\'d&dma7'hDia=4'+%u
6A?+>eN\[!'LDTf`</%>fW4o`)3IP(,;o(qR1YUP9Q(Efn&5mYe*;;+-1tqi>?.?t(68P?To
QgqgV]A%5a"VB2Y9Rg)DUa'OY+PN!<MIO?WTQh,]AW%E6"`0@BI*S2scB^R)>u8<;Q)<m_<-gf
C*_Y*Su[_?u^g'D$)(_!5jeJ`C7O2kC?T.gbENZKeVK/!3>UM9$1b>K;,$/C?g,!CEF,HOWX
8`<i>UoE8J,p`<SitE$G`>?TH4)1N-0-qJI%C?[TJOHeENJ^<@;!EErd,S)JKqaFV'@in=E7
?8+T#q]A<L.G,=S0/+-]Ai;H=Gj'a$?T;QQ(>7+)tjHd&SM",CDP%aPJsf-o$lOR"7UP2@ks4&
f75`J^4d^W%HB;JZG2'N>oF/WL8f]A!^NE;J^JWBDQXU:F'.iA]A%Ld_lNr8(Ca$*N@!Wj@2Z?
GiM$W;prA?i&=9o&V$=6*K';HnBp]A^:5QkOo+.jjKn<UakY'=6`>jOeJn#Z.l#T+GMQrFq&\
&+G4J@<^o?%]A[uGhdZI+BoH6gr'Dj&gM0tgfJ=8cSV!\60lDDhqH_c9bm<0j4kd4V/3aXR%F
@/;=dO@mBG7EaDuX,0FrU$;d.cHY6J9#?%T9:Z$riIT=TB7m!I$O1R->Lhhj4,s#W<kH4F\+
Apok;FY@c2AfUZHXI#cpI*.F7p?$V7<':ZLO8(!9[G(Vr!@A8NrHTa/m3;uSF/=Yci3>T.`&
HTA!Z._cEVAB7f':IO(tran\&cVudi;:N)?X@skG1>IE%#spXL<6hTH$VbP%*nL-ja&b<Q"E
GJ-]A?-MD-osMa5^g5<FO`@XWQ71r@o2&pi`qY5bV0jW69$&dC(F*"mJkTX@[LlJnH]ANC<1N?
Og5;1-gNfs,aaVDCA(l*M#6?g3fSQ]A/aGZ<pG2UD;8F@D"qT=cMHT_=DbgU'6KP`"=Q"W$R*
(HkHY9653)%398DP(g(Ve+]AJB;o4-4,::VuTHQsV?!)7TDh*1\?434WUDT1qMDIaVr(9(*4d
mY;^Hf7Ed$p^s$@7cpaZNG0sWZ^V3(Zb*2cgI<O$9:%Mk5`Vt[G;i^0SY7M3SLG9S3GeRKH$
Z$E-j6ch:m?(Kh<N[f\h_Te3sG=iB_a0#!.O:m6631*l/(#&8ri;"Q0_]ArV;WB6+si[AaVJ+
0=LJ6?&Hp$9ZqTsoWEkPp_^!jnnW/U)f/W]A<]ABtYXhc0K]APK,R3hKF&5?te;5LGi)'gE),gR
CCCEjt61jH-<j3hD0#;ruX$Mnd)(<Rh$<\[T^qB^@Kh42S&A&pIcQG(B8@<g["_np1tVUN*F
:Rj-F]A.\7OGNDAfpB>K/8TQ>t*XA_]A%Y)7PQ/`\$5:nc(Q.Dt??:pm2']AGf_J]AYWI#hs1<P<
s5rLQ43'7_l2;4!H'N'4LsP5`e_N"[5@DurQG("0Sh@^#o/JuWqKpVd3tM<,~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="164" width="375" height="533"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute0"/>
<Widget widgetName="KJSM01"/>
<Widget widgetName="DATA1_c"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab10"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="absolute1"/>
<WidgetID widgetID="1aaf1d28-933f-418c-9e4f-386608efbcd3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="gs"/>
<WidgetID widgetID="2b117ac0-f2fb-484b-9670-4a26489de6ae"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="ZB"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="company_c" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="ZB"/>
<WidgetID widgetID="401f600d-0515-477e-9bcb-e8f0e4ebef73"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="ZB_c_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="1" borderRadius="5.0">
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-3221531" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<IconColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</IconColor>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ZBMC" viName="ZBMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[bp_jyhx_fzjg_tab]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<databinding>
<![CDATA[{Name:bp_jyhx_fzjg_tab,Key:ZBMC}]]></databinding>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="5" y="14" width="364" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="gs"/>
<Widget widgetName="ZB"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="60"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('KJSM01').children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="KJSM01_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="KJSM01_c"/>
<WidgetID widgetID="653ae8d9-abf2-400f-b9b8-98e1f830f1ea"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="KJSM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,952500,228600,566057,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="9" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(LEN(C5)=0,$ZB,CONCATENATE($ZB,"(",C5,")")) + "-明细数据查询"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" cs="2" rs="2" s="4">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本2">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var date=ObjGet('get','date');  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&level="+level+"&pany="+pany+"&user="+user+"&date="+date);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[LEN(GS)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6B290E8AA0481BE5046941BF6091CA65">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[_g().options.form.getWidgetByName("gs").setValue("");  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="7" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div><div style='padding-top:4px;float:left;'><img src='../../help/HuaFu/icon_date.png' style='width:10px;height:10px;'></div><div style='line-height:20px;'>&nbsp;数据更新时间：" + format($date,"yyyy-MM-dd") + "</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$gs]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[='']]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="12" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="4" s="3">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="0">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-7498072" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WtUPNfP/1HLD'6"K1R@2'FZOeG@'MSP/S7(PoE;O&"/6V/C<'h,\Y+t?;T'PRRlSHK)sW=
]A;##`t_a84"RF#pCS)?^PoSq>JU,Zduhigq3!+[9O*mhgWatp\X=?g#&-8_9R5epIa=984`+
Fjlb)/,p^4R'*"0N!rN5XoBE$'KY@C=Vb]A?BA^eZ-54ij?!I_M%imr$4mSBc0I:2Z;omQ#Jn
)!L._=b24AbjN$&s[plPOI?eBKl0r3Vd(Za-5*sm(N-9rB\8[0El'oA<4YCT?EldN@ZUpQ[^
!.6(CeC8rOc!$YPO@k%>Ps(1ST$*Zf@',)_+ZM2sM-9&E63r+:.:+nXcf_M80"ETt9=Ou()W
F0aVo]Aid\]Af;?(M1PZ<u+.i6>0MUr\Fuj\Xcs`Ep2\H`1H4qqaSp?lQ`akQl`7q5-=rA.&^m
nSW!M4'kC\%>\`SuJ4G/3lC_2bjrWV$Qir\l3dR64UHE1uL1o@cKQq*/!'JbKZSj>AaPZaNF
p@O,Fuj97Lqf@hD7C9#a.U>L8!8)@LoU"KOS;[`n_mOU2E/pe;D#Dh0`*kT=D"+Lk7?t@m@p
u3MFZq>@g+9Yf^q5Bj=*[ep:TD78pDQ7s1-U4$Dm+-Luddl%pT:TU&*KLSZ]ALEZ<M(Gf=mQN
5W/)Jq8mj=p`)Yf5MQtc]ATXAQK"FYk]AZ3IcZ'd^7LFZnV!<LujYdm3=@`4Ca]A2M!`@3CsU0W
f^#X%>Mc`j`_o%mTgcogf=-5!nY]A./c_RgQC3*+EgLOT<Ct58.KU4gf/U9NHGKni]AG[N&8n#
<"h7sM3\juYT4]Ak3dCDqeY("!p:@1AZ%8,'sI['F-6.KbEC"OU`DsW#/c5Ha7+"27c)_H&YD
UP=<P216V,'q3AWSS&`BunoBhmo*%mN9[-NMNML4VZKe,9/ti\;?^Sql@an=sM*aBNc[n4s4
uG+`BQ'#!DkNCdH[rX!Gi(?!k(b-_H\:%8TZDdKfUZW_=@_]A*7eb.#4>A8I=;s/u3T)Lb5tE
\iQ,E*r<J<FN+ZM=C]AZBE`DlUlIFc5=g=_#[!]Ar'-Ws(Y0"9-lTlM1Hf/i.aT-&'u_>B>@lZ
R>WsXc!=V("j2]A%G3P/[J'oL`^hI$"W^A:bn]Ab]A.hJnWr.?,7?pMGm!]A>joD>YeZ\?[1dJpK
FJ7q9rJ`1*hJ8bOUqm]A]A%d7:J1PpCll^'\L0@7og+4Tm8]A1/(\XqJrJM6832`W3`=87PbN[5
]A1pQn#P!7Q%s2![D2TNP@,^;SZWLLIE7pXnu1SdSJH6=KV6EpAjF%D]A+?^nMpFV,6gSd^oq@
0hlPRlWl<@,DoQ>uF]AZ;D_BD/T3aFI1tK8VLe[#o#jb,Bke4K:k<39%Ij+/81c0KiQGSI/g/
bHO\t;VmT\X;.$KoDMA^>[I;)2UWN1K0!=gpWaMV[*>V\%9A\"&`^eXtY3=P?AmBGYNG&]AB0
'st<"#&m##G+%\3"\md7mu&:lf?uM+Gs;\!Sbk3CUiEb2#*-`_`)8`U7l[X9_U/#sNTnB<@O
irZ:IRo"&W\am1I/f+ctopG=]Ao!cOI9f@1>XSA2cIeZCP<X"Jh)kL_$aMV/1hqMr3@0mgfPj
*:7VP]A%%WSN7p4mKF;np)Uo%%;Th&4M=TG7NT\1-Y"+^nBA$DI8D%LAPW60q%p!&=qb]AMenb
?D.2+>HMpPp/+)V7OUgM&^^uL$JD_L6;gKmP/I2%bt/Cajn:5JBJK0"#!jN]A'L2"`;NV7U!-
D.?PgSpkb;BtTOG9dp:8U,YBt`\iK`YLkP2_UlIr_QMliRCrhpUEg`OG/6:@mNXdq@8*e\94
-=(>kDQBG@7C6m[EdAG3d]A2/jR1ha\A1,KA^lWLq*R2?o(R)U).0J0Oe>QVl&#-e^$L,\Q$g
UWKrk_lYD/LccZl*4b2kD=0_mb=VdVF*WXe:r'GKC/Bgm?%56P'HRbQn#79CK1Cm5!$<"hIr
W,A"--JOC$=J\O-,a9_s/-fpL2aL3#.%_2@Lcd6mnBu1[,i!Z`4VYQRACD.U'Lb[\@&sZZjj
$M1Z!buVTUY-/2hh0q4MJ==f1=WS[N,kE0k`BOHkm=!D58)Mlk(KDJmN6shg8O$;7ZO(s[qR
`64p@H2,bLYOBP6QWN<$`HZ=a]AciPAXi:c+qnR-QZXTfi>9DTk]AQ2LsU+CXq\J2ulu[frh,r
W\1#jm\\1ud3O3b:+V1A_oO8g0CLrR@ne<7\ND'PO8![IKrM:tpRAZl__!3\&!h1)27_:"Bp
Pf@CU#2@ZY&!V".?[\_O0QKkJ>?$:/8g2SLt=7\/&AF,f!Pqn:ZAgJ"c.uO?;^!NCZ>PWcBp
;0l@Q,8O)@)n-\u(/>^!_a\M7"LODQIP$O51fB.taP.XiL4I'ZI>*!f?m2WQm$bt0%bH/&X/
H+gT*VV"Vkl>1O$I7_4NW+6NVpaCCmqTO@/VUh^f]A>ntO#hY`l6k6XHg'Xi]ABYq^^@9"5ki-
1bp48ddfF=k>4o.8cbN4,NPSN9Cp<'93U\r8gA:qT,#d@Q(kZiRaC;',FPbjkDL<>cs0\M;D
Vm[[m#Uua"Pt3nI;Up%8\@-48l<m?m01[fqWWXsYorXSt&+U)5"`gnM_Fd[h$!OZKD048"<p
p%+2<o!/REBLr\2?VoHuYlR7JVDdP2aXYF1t.["5/9<OR`L^Yh^PEmb[--Z]AfGG$@0),ZH=*
&I%6$2Xhr3sX77BBh[kH*6=Ar$\'pqh]A4"(1QZ?&f.hb$pBhksT^Pq#GdeJ6m]AU8#"#D+YC7
_G`)Ih`dO*V69*%YH^`cYi4:Wg1TMQ]AuhsV6g$Bcis(8UZ@h:#k!!gGM#"E0'9Qh.o?__Bre
*/EBe?mXo%.`<J%Mt$"6Go]AOHL4#;4&:YNX4I/'\$!VZsAff3li5qU5)6NH<F0B&A=.+$-V*
bLtfg,pA5)ZgYpdLm,EKC)hA,i3/SsTCsO9*ZT;km8lYe4D=-)5iec4>*7lJDTo#;;tQ^elX
$4g6:?iZWrnDS^kGQ2`YR'1RRffJH&En4$n*R*i\R#h*l$S0Y3JR&YQdG4-^0U'X-CQVWe?o
Y]A'ljQ.o3W9IY\NihPXUj@U]AD[99Ll:DslPdF[.ZIiZJ_?^8[hmr9#!p#]ALBWdg"e"cDdHDV
Lkir9bRbMg7=FLoT2PNqk$#Lm53t-dus`Un9DM8nk0Sq!/J1ErVJng04EDP6;B+j4jNAO9Qe
gr2<b>%bUXXa.pFh?&.,j['hHXNptQj1N.J'9eGn-^1iu0CW\=8jjJm=kfu[;NZ[uuJ4%Op-
+sIDPM\]AL5GIR&/LCLfkpJdA=n@cK+C4%F5XEWOKR0jG.[`8%l<*f'2qsc?Z?>,>$=n&&&Ft
Fla8&Ol1jGqTTKu>A5]AVE`Z.s'Wa9-(+dGQg0ibqVkl6a\o+qZaa20;j3,cld+fnuu;!`Y4S
(gZ`4u##C1N>gOM[[]A3t!Vti\c>aKR3,H5bZcQqr`[^""]Aa'rY/[(3GuS3`K+$ejK5pL@>S1
So5ar8Hd=23WPb922Mg*#!L!S%*ab8DCQ@VF3"^Y^J@l5lc5E<&ipK>^H1Cn$SC\d;V>@j"$
*X0S62W6r!r7RLYQp*e,$gd%@Ih2m&XMB]A,4>]AI9pKG`C1kXoMd8Ke>UBhDVLe59$G:0cA@7
aj2t*f6_0\C+KpUAJ-8,'plG0jZ=0d^WX2u<@u\qPB^T_Y+?NUC#Nh0NqP9bci'PG(?J97Xr
)?__+OJODhBOojLR,NZc:7\N'VtY21Kp_dTerf)d5Z=HP$5a2I)6ljpMHm^2cfc8bEkLJU8h
-6rjR*TkW*)hImFCVSPR2g\Y8,I;c9:<XR6,Iq*QY@Teqa2sdQO<KM6hCYV_:H6mVg0$E?KJ
(AZnQ4_=3f4QZMAqHZ'`>+mrl'hBEs42kCM$03cAY!))S(XLA:-3?kiDTb(p'?/E+L*`Pb<s
$+9Eh_SP/>cDNe6AGpR+K$aIp5;c&FGZ,Gh0h!]AQaV*fK-?7I.@kWEBrbH_tMU,Yl]A:bmuo\
qIY$p->5TC-K/4P4.;mHLLp$O2gS7`7I`-1SRbsg&9.2#r5&kn>lGglcK[AO0g&d;Qp1#2q=
qFc24&KD)T'S!J=r^Bq(<XNqNnBOn_C",2.$qU>P.Pokcl:,"MG^p7ilKpa;G;[L7iU_?@!'
jaZuED5T'."X,@Z1LcXifR$4\@9[lfXnRlc(h/B]AC"<-*%"S7MD.#6;X!D(GN,]AH"uHUJs[F
8m7J20p5QZ]Ak?*K^+<1:.VWIU;5ChaC4\sEnc1_^RR(BUZ(rbP_dNPC$T3RjbGmpdlnZ-bqW
4AB&'`..8J#E5Lq'qWF`MVp)f)dQnbANq-5a6J"u+^h'1a5?;ag\,^1D#L9JAO1qKN),p$XC
3W]AeelCiH8B:ac[1GCH8Vs_gWhm@d7m1VK$9dueIL+fsV;lF//IABR9)k/00OOi.`Pl--0TX
Sf0<j-N1+RZel]Ang_B5N$1;e1U&[T*_r,DAENq'YGQF^!>hl/#ZA!GM[T2i=Sc00cmX5=h%Y
a7s:)]A=aEjgpQK:TH##U6\!s1V0>3N"_$Se.j"YM6V'Tqdi!d>GmZ<p9rY9csJ<iX0RAuCEq
P8)C"a/!aoJbtBW\fYtkXnGHXt>%5k&2P+0+,bpKg]AH-1BTd7/l<QhQT]AUg8$k_dJVmA+7L%
o5lj*Q$m6jFMrngh)8a(:+mCW5'N9\HjkK/7517Q!r`M@^2CEbm;Ffk&3D6/<k[7<nqTV"SV
)sN@a#V[6F?=5l,o\I*o4=$n+;EUiO!oYRaL3)WVa--S4IRo="Z2^b11nGCX)]ANb'\=[2p2i
d_B/8pAfkEb8F.5X:JmR2Lu^6W+5SKL\o*CIg<^N$4l$7^RR9__p%K&k0m,DJ$:2.@Up"sV(
,)*TAGH*KhNe5AteF\3ttIL$Z=`lf_h7bq%DFL)[SVXXS%+ba6,fH)4/q[p/1r7_H`N[-*g(
J?"l%>,1b+q4Bq'WQ:FlB^.hrJa<7X_OIH'E8gs;CHUkOjQ)j7aPkM<&A*mL7eKi%8$CiLEW
)i(C6UpSn_8XM$V,'mCFBEgofGC$]A^[cj$5Ah]AVijsk)oc;NqbogP5.D-\4&FA55nq@ZVucK
=s]AgE"POs,V>="lFs+UVXTTgRi$>llC%0NE<V/qI\9'HXZU7s*nbCsT(,_"CEiI(qMB7aFe#
Ne]AUbSM6o9V(\^AWQp;G_dNY"ln)(R:>C^k+A8JbaNN!mG=Qd9i[(g"L-^]AAQ3_=BrE;&8o/
mX(<=5'#3;$).iKI:PmtHf=AZhd*rmLe$-jYAeCfK,\V7_mA41ud2FN]AY>_*>ILp,Vi4%gu6
^P/GlIZ\f,QGFJ=HaGZ;iRC5\4d'N4?PsOUJO4a]AA5A0kgjO0n]AcVp&]AQ%s$e@"j(lB2]AOJI
[e(dVMfH5R-r"seL@*I5T=8CB)8Sb@N^Z<FJo-)ULSeUoJ:I(UBqlK9To1aRn\_j`BLecZN/
lFM&3[S*[3&-,;mA8>U0Y8:q0.V`:(dpo]A*bX]A(_X(*J4%iq\XdO^H;`XW1EAXa==`g:&8R>
He#iU/hH@XV)ObbOT"&MpaRB`@8lpZ.g0keAf]A?aL+Md$GMGWk/6D[q9k8a05?GS_4EO\C(C
]Am,BERRnkW=bLKtuI^bX/Zia]A,Rd,13F$Iku]A,"$M<b4+"9FV(Ab/A(#?C9]A1&>@&NZa!l[T
X!53p)sr[Ne$7&lF(7&Si4h);,BS25!V+`74L70*MdV]A<Cu0PW;qeD"*o-h(fk4%1`U8kc":
C7S3uog4hC@-<_uZ?'JN4aO9H8*WcFgriU_cWBNIus1T/k?HWXEBW@L0`\ATGrqO&/!f.crH
9`QHjK))`Nq77&JL+>7[4]AgqDgp<>hk`9qMY-,r*aUoQmdW)^4Q1@o7KmY1=5sn#H@-``SoR
C%Vo)6tMX&e@qFhtk)C"88DZ+B(W4]A4@FV+ji?fk'`pD_]ADo`^(&q[a(dW93s31T3D.BCZ18
)0C^/g'Y-oV2X]A+Y$;NASktWX:<ZE)8fnj,[e/,Q9[QBAq$&D(9gjGY`aJU]Al2T2P90MsqM!
Q2'6-4q2qj]ASO<L"EO+nphjg,RfEjI)TPsgFD(q'$FT;]AjGL*,i[c[@%pqo\e4(c>MEdB/mB
q-"SU)Z5&>5;+@VfU(bUi,,mFFhr6/Y.X,8/jdpVCE^6<?R(fhE_B+q8rQlP/Y9HB\"gMk\`
e9iYFr(?fn?`jQAJ<rmRd6fN4nW#B3EhbHRr1<[)-6-<-%+%eYYY5ZU*-EBs:4,KmT@N:iF&
qSFbKge-P-XJoP.FuP.*`Rf)Io,>L4s*.OdAY8KCna3W_$NUK=^PF4Nl>@2qkHf?X2`VVKqo
/3sDq<+qJV-\e"pM-)sBc*?PMABt)Wm\$E`sb,;RlA]ApF'B<4h_j)3_):J?[n!&;lj".%/s\
m06qUpNNg+JC,M=J.8pg(VsW.joZWdZeq7:!jq]An]AIe4G<=UPJ_?7M//5AHHV6gcV+hB-<Qn
#IP)O4B'D">p=a2m&/A+N_.opYLA$sjN!KkXL,ImX6XI83MbGK@<^T^r]AN]AS.CgCqKj!+a.h
6McB^B@r]AoN`hd+a)"b:3@\V$*>O:i=(M8&@-0E`Mbt9[6Wu@O_q0V;UpTDDT\:8VH$F1oM^
h*FJ?ar=Yk6q75X``rLBL&dkmq7QTZmOu-G86Im*2Fa]AIF'p^G7BA8,Xb6UQlP[%G<@VC*tj
\C0\A5P8*5AFXbG$n>T^+?)3TglE"`5,I,[7N74-nk&R\_i]Ad*XZPOQZ4<oZ9T4BXkfamU:A
`=crV<tC8"$;Ub2^OMLENjmBT;Gpo?6;q171ZgbTQ."?5l_#QIH9OUYSLN959+*,4uV96g0_
W)JcVh4aht,WXcFr8r1k4+F6hYdla[VJTKiOIJC"*]AEM']ARrSn^!qD%m<G#QGA?`)VA6E;:W
o0U6eDuN4.8g^F;Ub%f8hoT4hl6kOS]Ahj:r&t^Q9.QPZ:%@7YG]AN^OF24hu%STZl74iWW'Ij
lhu0!E`c\iL^We"OOg"<=uX45crt"*TiV/9f&F>sh-pZl:EdfHlUsbG>@=Bp,uU'Du[FXEY_
"0h<^ZeFKcS^>g$c"'Gpd@F,L3fa_bsg&h=tXpKTl[:5_#?NHP'F%^/qUB'O1m2tL;`tS3,>
+7n^k=`YZ92^+1"p,1RgKB9_8iS@oWLE%;28]AuL.N9e^J\geDp*5URKm!a(c<YROCL7]A<p.k
#1e5`jg]ACH<:qPl&N_l?\L[5J>]AXaf&6I(dFCna$k*k:''lk4/^IZIb]A7@2GIkgE04pW.I(C
AL@raXQ/`X]Aa)NW>lV>loO:G_=6,[qC>9?7Bs$!m@X@ccH-Jm1-Rn'5Xa?4J%J=4MNLpknXn
COM%OLpLg>Mo5i?N9DnM&iGrO*g)J,Y1jQ%#]AXT=nP_&2+t]A@i%TT[u"Q;F1q#k4VAl]AAYgI
1>l@Sq;<fObRaPg%50c]AJ8d,N&/oSI*WT3``I,'3mR91\TUdSRcGSL,D;/B#)*pWV,l!i^oJ
U6d^:NNOR0kqO1>C?0UCtp$8P2"&eS:7Pq2=bAANj*]A9<kP`]A\\nS'%H$a*E''4D05eeg":F
Y'Y"6erJl.D:k10n(UFrsX9_-&0T548+W?03qU1ggHK[D2^hCP<e\MWNZlp>-L!cYmW_.OZT
FgPlU!oK9F5pa>PXZR0"4b@=7*QNW+oN!$"Qf6I]A0AmRr8$N9qiMtG04%R3=da5#VD-Erjpq
F1l.9-gi5.2gq[0p#N<<5914HQ[dAUTXR-M),2#i.';*sYZ-\5[JO0Z\!0BN8:kSZU5o2"AG
P:97NAp1mZ'i$]AL4!Y/fPg=!O0VOGjd.M\;A>4#,UqX%<r7!Vn@0R0XO`%tfHZbo.<ol/8q]A
Al8h3D&+UIt1MuIki`PpAh+/PmoSaLZ#h%HV4f\oeHJYkU5n\&XWN=7&0^tY\e<.h%JKj%Y1
aFY7RL&.a,<=]Am+eSF^(^Ss"$+N-*8Fu,e%:"F;(mqaLLjuk9geL'HSG.\sJ\"7QlB2]A5ZM<
SIkZ>aE@pCj'!"?9`(CW/$ST[Zh*L1l\V^ej;SW1S=!Xn3L:Q*g\L5DBmnY)CeO7Y!-SO:C@
/)5L\0f]A/W\8M\E!MnK)&ALC*Q)/>Tc8cdi21<d+\lLdq<gCGZ;#a,^6J%Wu^TP9sq<\Dj+s
UT=[*$Y?ul^Y&[!7HKRKAj@'6^E$ucc:[SH$>RIF%Be3dNdb?e7j@P5"WiN;rpTD9U*"Tr"E
Odp.XgsJrCa,\_K1&,.>qdaOUC96LITW[ajPQeG_`8-?aHV)Dl?P7XC.IM,<Dh?@X=Z)4NtB
Il6CoWgV:VJ50\U*Lg/E'>e:kKdD#R7r7qE_1+]AT&uXo?7W,,<7t!:]AjHM"X89e.B!keMGoj
58ghMb.:ncWZ6pB$^0<s;hI**?WYgAB3J*fVr`sPn]A;W6*q":5eDf&5En7MsW]A/IM^6WGtAo
9V5UNFOWb*)"$CI+CGH^,&P(,=>YR8shNk.h=G<s:9%6Q"N\=/5fmK[4[?12A:PiMUK1QYPj
ggNRoX+9]A8+fAdoOb5j0-^,6F9fLP,ur3_4+WDFot0Vn.qp<p"3E8j#`BEN[1NI<V>cO]A%`$
6H_CB.:Ke[Up\Q2E2d=U2tV="W/*dNo5<JlJm1.EHVcZ=:8ug31M#-(.Kl<+_DjESsOg74m&
5BOU\OlS^<&'b\+D/*=l6a'XQe-c$mQ`.EZo\icP&q:ikb7_lGP%`H8pl2*QF;#koSRAD$"7
"r_"*W's/N7>r:NpVDZG++M`A6='8BR,1=&"^0'_TI$ptO)q$r)%1Y*USClH)\h3A/[+87gc
0[uiBpDn=BPZ"h82i]AfZGPLAU<Kq0%Y%NMke'dL]A"ta"jAWlX'/HHr(ft6e1Rr^D\a!T'ZT;
u<B[@7[%*'_f?W^WZ94(T0=;2\)O^@%cKBi5an^kC1pn:OMS'iUFpcj_5D99?`N3%A`Rn^HM
/77-OHuB*qNo^Lf'3W`[uSg9P:<OJ(0!-:1hC./,Nnogn[kF`Hn-FZ>NTAZqcK8U5sf$Dl9h
1WClh`ok$i6h`+c2'c:$0ECX2j+;d"9bdF-jWrFXZDH[3S-N$mjGRUT1kXQMn<OPid;<`NoR
8.5)'TF<X)4f@,G/_e/tJ^>`<'[jGn^H#nT-BiT2PsV3X;jtM#GV3@K@^'VN2j3Cn#A/--1J
d5aW:+eco5i:5V3g6rCU7(pI8:fr^[J_b@jYO)@_^Er"ja['IhSIK<!lQ1#*>p(euZ'W@("+
h-ek;n9X<2A'"-c^U5`PMBZjp8dSYH5%.AnL["ch3GGiW0AFT9`C7hU'aG;L?4M4%"3BdcZH
)!Ba7_.E:?eY#fU5Fb_&EGQ<94`#;N&l\:qs(q<d70>B96WpX\BdgJ+DP=0#HabW[+.(NSh%
P;]A)-CD6"2[$8C3V:c-TklRe>V4b_;Vdjj'^^\".%SO[-$KmcqOQb7d.;2:]AgVY919![WgL[
/%Agbb]Au-6n)K`$1`H@'W(e>=#!K"k5E,\3nr.X*\)j7n?hGq"_,Tc;,_*&0*n\J1oQVD$R[
s9:r!lSlCZIJ@KNa?kQ^0IWPCCgEqEuLC:L5Fh=edf+`Z>j<$fK3nYF+ALp6HJR&*YCCrRuS
e^:'W46$cCRjk]ACr,+"gplf;W^$bIs%9,gF*jm?lA'IR/Gd_esYQ+AS,WA"J'':7]AVoN^%HP
GsRPR%M*0"@TsQ,:+8<kW@1jMu/-Y,@]AC>K#CK_YkY0eXg_s9ROael#?DAO[>"45)DULO\ee
%aRdksi@V:?jfs;R45a[X0Fe7"tj^bo^S"H&/MN]AO'e["K-\(c7mHlZSI@JtoZJO:U>\GbH'
Zc;(BXp@X,%\Kmg6nE]A*l1"WZmt\bR'O.]AQh*T<5qc5gBPS<;ZMe(\8K8kS6p+5\@9+A%h&R
(Pt/XcX)#Y+(aEP&[;1QqKI_=u&[(V>E@2R;t>08E&nYnELHahc7*[gA\OdEumcT=Ud^7.UW
9[tfMS$]A[?1DcY0XW7FcW7tWMo7mWb6V9Z2H"ZU$X]AmM]AmnqCe;@fFpEgWq=`@]A0Lk<,q[K'
,/no#:FoC4+=a[B++*)GbdrW2&:-ui8mhlg4ZW$i5D9.`7ITTc<8/cDl.0>Wfkd[6@,iFYCT
RQj/dUdjCGn2+JcHcUQCFlrUI_:"p/u<pQ<hgo7`UCL*FIb]A17fVA,)'6_]A^sW\GV0Yf>=7L
XB8^u4>Ac+Y,g@8XUWSlGTfH9A1.A$B:D81=cnIKL\Xm<)Thm'(dF,8!pajsr_p"O>$&_<0@
L#oqHJ+$,2(mBMR(J1q>;`sB.7K#X8WVlI-l0i@-t\g@B5^0'!M]A26\52XCKq1;7#rVVETa8
Yeoc#/[.3%UgK=>0InNDH1>VE?K]A4TSRa\2O<:,V_/Z5gD5U(ITOU3d\BoUhhF`TE%QTKT7Y
Q<GGD/a\'s+H*Ch!8@!XILsq=/X9n6Gk6AB;0SFMHc9=>1\FGedm&QLA@1U4X("U\<guV;;&
)lODe#;HuYeH'2;70@3n]Ai-[[Kj5>:i3`<GmDFE>L`ALHj(]ATu00I'q#0K08d0*_!FocXGr+
7&t6hCf(eX8#"Jd(<3&^!>%r!^SQp<?'Ln`ILd7hFLV6$]A]A@oupBUORR\JMa*F/K_U4cUfj/
@QH@2]A4sq(n:\a_4D>^do8rNH8?45=t=p7g1+pOrh9E4m]AP46APJ#89T%g6j9ui+p=J'jGn.
SIo4>N=D)qlEbS8?^3LbUU:?hd'RDm,2f55>M0C$.?L#@R:$2:LcpbN-W+t\(M+]AZj@9FR;k
)-H/XC7hiVUt=*YBf0[@5X$C?:dXH0pOKlf((8O-E+,(FSkn`Tu*s_NsG[&kVFu;2V&VKpld
ao%l!HL/8ZYD-I-M[))TtJ+@)EZ7JG1JB`hLB1i=K,bCSGt_OPtqCD,T"mo)O>RR6#P-gBGS
/h2t-p[s.i7\W=PRT`DlC73Hk>R`nf"LhBMi0YWkF>!n.F2X&0:/#7iP^%IEGI;jmRXh2Pfm
Ic/>]ANp)?Yec`,$/!Doi`/P$cE<U%Z-*XA#Ue$Jfbhn3R%bP46"K\B,-N.o]A3dqkOoD<V8*4
tCE4KWDJI#@m]A_pKVC2\;+7._Oh='l*3`R9--S[.!6;$uK%$\8UPeslI%_?n0]A#W`B<Sd$iO
Z!*S%e%1%_PK6a,.kk\C1uCTIY/"&#In6J,[n+WH)C>H'953jWgN^E0/TkN?R1lJi5niuKjK
qtd'=Wuc'HZ,9<@I88%;<ZMg(FRU:StG+?)uSe5+gNij#bhbObZ*j8GIac%AB2rl.C5hofKn
WKp1mWHn"\^WXbrD]AB_3lH%s8RT<K]A9Q--JC%A?ZGl_?"<:.t8!!Rqr$K?3^V^=:i'/po]Aj2
kt_&Yns-I^U+lpeD/6H>7Da>l=5h+6L`!pOQ'&SQa%N^Y\[W1lKp7>^]A%QL\eBjLkeVqO*Uc
K6Au9#;>9rU<>US`-/0e2.=2fGYk_hsdY^ucHc+OUYY6u(QG%N#XDZ!0AVc.M@-s6WZ`$KGe
XgV]A),K-#(5?/]ASNC!#m*Z(e9hZPmem\5RnnBkHn$'=9#&Q6Yb(Qllo,?*Wr>-BFX="f2HqD
WHiJuR0eG`/H/bg'O]A6EbT(4<I>m`^A`=gj4f2EX.pF_A4]AZF^:CG4oY.32*hD:#Ne6HG.nT
\O5e$Q-RDVLu5C4knU>41>an,*@KT4bRs^'"[C[WqtcZ&fH$B.a/Po)PV$^T(M:0>2/VV/`W
hs@\?5C8rKF45R+.Es4/"LGQBI/5XQ/3>BdTW<2;kpj3/rjk&:eQE3a_]A\Xi@I!:@lA-Lecc
P<:)FU%)GW:gKQ3<Pg]A'/UBY[D3i6XuWon,iLL/;jXKh)=Ls6N2<;RALFMdopo&/;,7*/tM]A
dlq4*s?T(,4R>REh6_VSC(VOc5SU(iS#[Ida=@FgG?!X'VX"O>2&FA++R)35o`At*PP]A$Kef
@s4kPQ%C&DRRn`4FTNV=S.mUFbXN_;%C*SP"%\Eb(!`q.o@Bg=/:B<*.To=&U#S5"Y4mKh_@
cKFuLZ(.V"MeE(Mo<%C`8JeBdeZSrM`1J,rA\RajUT;'n"dqM_1:4)DDj:)>QhMNFr:?1=ij
Q'a_Gc,Rc@0N4&^;9g8?\al[kZWRD)HH-V$IX9)>ZLRS($QV\o6eUe`m&.^1-o9m*Zo=OG\%
),Vsk+6\74C#<kK1\@p@i%^s7BSMIl4iIfSPIV^-Pi,e"Tq9';d+FegujA'-3=+UR1eYsuB4
_'.ml^-e@,+)`9f3)XtJbbemC[jP%>I.*HI?Ic^Nq,<#^ZWq0_pHu[eVIbDoN!dmY!/A-Cg8
]AC7#iAnK:[sR-%TXX6]AepAg:?G]AGL?Y+a]AJ?455rm4DYpaFZt!Qk(U-X8,OeQL(""\X'@OCh
Oed'BIg&kd\')mH8FBBCh/Vc<U=6O)/qi.q6L=`mFXab@#)espLj2Idp>lRS0*nNKKBf:@3O
qdao.uG3r)5!R>+*mXZ7NBDs*<!n`!qLghb\6R'tp;@"cA<>_rfrM41+&bs2Y)mP%&%$[EVD
FLDB?F;#FKrA"B7h=Dk^"%W(?@.lqAL+4U_LQi?.f\p44G9_q;=hlrR>fT"3kPhmtO,=pWJE
Pqh.l9Q4S&_qd960=6'$[P5CZ7,FjONiVLs,aUCr2JTVJ`cX=0A1<VeG+Ma^kZ"H+t^+/CEX
mK"OI(Zm?UL@,E0[<VEcid5DB;c`eB:f(']Ahfs'nS$:$Q@hN5o#J6e),"1;(]AbrT[D<^X'aq
pKl#0@s:Xcs*@?rn4B7[,'WOkFE.:+rET@q)s?.oXR<jqEM!,9R!cU/,E_T(5[@_Z)<p+q:'
pI:!Nl%18DK7>V[!dt;rXR;OSEgfR(c*^4BR)e.3\6)HA>AW[ia3dRZKM6B0GAiHadk%Dn'_
qDn'_qDn'_qDn'_qDn'_qDn,g3ikC/)T\Wt)kbEs\=0^LJm.g;(TAs`H>p!BcHJP^_rq^:I`
XUs_EPo:pgb<U_2iJ]Aq%HE1__mj.10Q%^dN#TbGj+_Qk\7,=8b':`Rioft]Ansh,Thb>k1gg@
eeBA6SWqKb6VD0+bqQ<cQd6<inE>MG]A$5P=?Erri~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="224"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="60" width="375" height="224"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="ab0227ea-8508-489f-8dfa-d2963dd77989"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="0"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[502023,1524000,1524000,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[3479800,0,2641600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[公司]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="ZBLX"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font style='font-weight:500;'>"+$$$+"</font>"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="BRANCH_NO"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex reselect="true"/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',$$$)]]></Attributes>
</O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-12999178" hor="0" ver="0"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="a"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[


_g().getWidgetByName("gs").setValue(a);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.DictPresent">
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[单指标查询]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
</Present>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="DW"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="单指标查询" columnName="指标值"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(ISNULL($$$),'--',IF(B3 = '户',FORMAT($$$,"#,##0"),
IF(FIND('%',B3)>0,FORMAT($$$,"#0.00%"),FORMAT($$$,"#,##0.00"))))]]></Attributes>
</O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="A3" upParentDefault="false" up="C2">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C3"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑 Light" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<WJK;ch_VDr4_GO?,'k[8^$-5t!#-#Seo?.R>$\UC7cHMI?ijTN2(2;]A5\!!$YEBL+&1PO;
:M4"qj?NKERA>#VlPh&2;Sd;;8fJhtaErcB%S[>l<Nkf^$i>H`b!4HLCZZSs=a;k,c-sBCi]A
WlVrrDE:hYkFmD?9^PuS#/VNQK([KWm><6s"aI-0.oH="pcEWKH5Q>H,CYn/3DQH'dp"A;_N
/Y@Zgn*l\EBE_ZCp5\rS)Ni2/9Uobr(GLoFIkV<nJA>mngsg/:`)8Ngr:Fp((Ldo>$#ILBos
]A0F:]AX.S8hE2MFW__]At3Z11cJ9lFf<NJLin9XY=l`cq;Y7Y[NE`-1[acNd*TK%)/\h$&"<'S
..K)mmH.-Z27nG>J$8\Y?fgeKm>t&X`I="(6']A4_mr,b#%6fc6p37S_Y'tt0"iF:<:G+Q!*r
S%DDMUKk/(p)n*OrUumMF$N90K292uO,onu+[p40c._?BhR'd6:5pq=+B+j;Ls!P+koQSh[5
N$>[D*6KP(K&nG_Lljf9N&RQ9.3oF@P]AlANh?/dN7H1/;)ZGF]A2rK78)B5]A]At?$tN,JH$U&q
8!pc3sBaLf`jSWq</ge)kcR[CL3\1?@<*k?NsLU#.l6iK8t%]AU"(>/0,C9,]AD^m0=7k1!_+*
7@G6n+LpRq^5VVN1SG^A4p3)MHt5pqcTk9a;%OToO#Ale6,U>pobPNKKA%h@8%4'0E(ZX73'
F%F;ND=;WF(.QQX9\iL1@5Nq*1<tii=P%Tkd1toYrNN\j%O`GMO[n._nna(3j0K&l0(MttU)
1Jr9k:+iZY&!!\iC]AFKo+K7dgj6gWOt:V!ZeP4V"c1:o\cOcIJJ'JGCFiJ(ecVNA]Ad>T]AOMe
H,V",Q4>=0!h^3YMDkPtanRK7eI=l&`]A^<RgJG-9*rS3VY"2"?X1.Uk8Q4M\HA%hhU<5_DuH
Z[DhB>j#4k39>!NiCLmLF7n,0f99DOFs,UDFia`iu<Pn^$nS5SUCK(75U"c<t]A/VjH@qShW4
A='9/frbL1/#@kYS2Fbc9MRG(PfZPH*U&cOW]AU'$lo?@b*gYiP1uX6H)ZDj8Hm&2DEa1Zp[Q
8^iTY,pnOm_cn:BPAb9I$,V1iE16S<n]ATDH2/PWc/$fl+gPPf&Ip+<K^^?@_7NEU>NK,(5@&
RR)MMbjJ:qkCF<ou5l$Wr$0JTm.U7,.Nh:ZT_4]A\iGM:Y0=n#jN%VM9fp]ABS[RT$3/dU<#+(
mKI!fLrZ5+rA_=rQe`5)LESh.*XdOahrD*L_8'*sL,&Li2n"CrZ?SPLMcMQtZV._"YB(]A+Dm
gc:,qL"*N[&e>o";]AkhP.CdU4O?2N3Ro`\Zc*aPYAQ&`!eHQlZS`,;UBUDqT8C;IK\3i@<!)
LVPo>mUMf[R+PiVT[J8uhL-VWa_4,/r#o2npkMYBPslOSE-oR.?*q\O2C32TU4dZjcnHsO9E
Wq1b1:Jkg%1#+p<Ojh&3rKT7?XPs+p6f2T<gPq-Y'L-O#n7B%U(#&R<a=g7P7ch9Y>]AbBum.
MVE(ijV:dCN7"f\r9nO4NQF/(?1Gd4TQRhu)Xg2`3u:R`7MUSg&r`VjjC=7Dn8l"2po-]A%8A
LT!es$q>6bkKoQ"$I1\9O1n&j_r,j"'HFG/;61\*goi.3Z:]A+sul$"i',j3<,We2_&n+Wa+r
I,1pBJ8;)mVmksoF+s(YV+JgO)@1"WdT+?()Kb,QLO7.16>19E5:pjIHj@%D\p[U@WU:/]A1I
,><-ThJG&ee"A#o[B8\##*&MR>H9[Db@8)Ba"'I+0]ABH#67C8YYH;D+9.IDERAIu#31dfaQ7
^:,l=mE(atZV#AskUWO[-;cOsR!_@*q_9Lf^?=:rEcCUgF5X+'q0C,<*l)(-%e]A'JZ9,;!54
1F)He[dF,?u[UE,SN-0ip0o;'[O/Yp!:9_.42<,+W-%lCG;:Td9N0X-1VL_s0VO3B/!,\T"e
/gNEf_$^YSaG4Z\j`3et*WK.'.mm/a3)j01p<-YZR(YRs8Xr5U<HedE2,Y5U[mV\nsJ72nUq
pu%cRo4?13Z)2gCE6:,KSl.A6(66.C(ih8`,2AqUsA2@ChBu#a8QPPB/MuXd+-aF??nF%o?V
@rZRnN;f:=kC([Hk9Makf3Qm:.nTbVoN#DRgbe3\h#IK844FYE3/NmI@`VFV?H5,iH7.45GV
'YBUWMga)e2;/+(L;',`m1TOEl(t@7BU%-H(%BM?74h=CN)R4N>Mmrf[CTnFf]A:j$6P%&Umr
`LM-#4VrZ#6MtJ!t(i&RsPOPD<1u6L67=h&VJ?,2Q1Q9S1LU@Hd:pO8`I5B7$e6I8?s4eb)D
H>4FEU(JR*"W^V:s'[<pVM<6&ESqi@H%j8"D.]A\P;?c/,grp>Y*l9J)K2c1jH^:cN*E"b$MG
C`gI:\:Kd,<jWFj-ki4H4SSM5-[6!Ek+Dho</ad:jtSr[\j1V0#ZKL^D(l&<ntEm@Ap@Z+9P
]A9p2Q-K:)JTXqZ^k9\I`%.GUo"u?R?Wfi]AJ#;=l@Ko)"QuDSg5%DEbpDU6VUt]A#+ruU&q`+X
d$!ego=\YdF/soA\g4.J8CThm>ZfL/T5u.&5u9h?of7K^Vi"(&kkS:j\c[En4m_EnSr'm:\<
:tCp$7[?"%rf)!^F4opS\tb,Ud[!fTg(,'B5RJ@mXl-Yu-fS50nAjs41Cu6FL+oW(Y[%p;22
$BObW5po-)\Rg`FN#.FK09>bV6fQ"?M5-)jK%Bbpu,j_R9i>k.8_.PLCna[uG>p%&Qra9e(Z
?Jc?rJ='R7D[\,BUL?B-ofU$Q92Dtn6^a:)ULjWYI<@?Ia$2+ST4s9mq0kj9&s[D6S2+:4,>
[bNW`Bbe6nG-rtcKb`Bgd-cG!bp]APIN^_%H6k]A>`r8mDuX4J[cu?-PR#t8-T4U*rPM6qu*8Y
F!_R\Pb2ao]A6mMr[)hn3,E8s=Sf$Chr@2!3]Ao0u5QsH)E\K1)+.SAY*RCsU%4/2>jX^PQ4j5
8X=<ZSS5A6)q?5*\jOeSnucA(jt6O3Xe*edUoJ+u5V1R&RYG4]AXneH;;!2Z073lHbO.&HCm^
KbhUiL,d8MjE'mibPJguZI<-bKAsmMM.eGpU7##@1R.ntFFJFAI:i5OjCokDg=p;jaMj11sH
BuSP6LOIbN)$mcC,H"R-ITe$Y,U+'S-!*gr8K5*_r8fXo_^Jqp-&!N&OEWZ!["%KPE_"_UTD
b?@$,!IOoDSq=[-BAqt&V?Y6!lRoT\"O7n)gi.kOC-0.gCpQ6kl'>=*-=ekR8gr?(ptH[HSC
i,6uGlM`m%1"D'W@#dFi.o(j_OL3gIh*:f#?!LYh+b19G-4^op10G:"6O:o3%q=Z<W;)TR&-
]A9X0>V*S>1aU4XjeqEVN/&UbtdP!ic#QcYJIt0oCg]Ahi!["!6t&&6S#:$rg>rr7GP="%`flN
SIO]A,@r/W[eT"?`Ks02Q`@11"QGnA-u%[d!d/GJQFKdJQnI!gl5R+:-K.Ce*`i1ehu;gMHuf
dX]A']AT.Or^_3><Tth$ka@MRdo7n'c<Ed$02kKchnD]A0HRBi(W)"=$\@&`Ut#O:Hc#:G!YF`N
r>S+6t8mF,@k7\\.!GmU5pjdRQ71dM0@/W-TC\es(I@0$EO(IR3G,s?;gq)MM4%qDb=m*Fp_
Z;*3ZX1f+XQ!J1Zi.BqD@'?q$a14MI-(G?X$A7%'X02C"Y79bSs3PmQ)M+siR[P2i'WU?NTR
E:*,Z[90a2*;1g.\-"]A:#!q0e='9d<n`C/4(b=0*9Eu/]As>h;q3#j664`h?LjqcJLh020>2g
'%tA^P17e6^$dbL1fVMGcajs'NS(#I9Tfd_&`aPK#ke+N78k/!GOIE&g'74rc0q+#KaV/kSo
87bHAMeXTVI0%R*QcLgUaMeP?NF"c^s@jqE<Y%[3/\lWkuW.NP`n(Ydjc5-94>;ZA@<mOQG;
hmZeI0sl/dcQ'pVNDPr=rsBkAla(+ke2UJ11Y3KUq5?@AX%!%P\MP^I?rQIL>5rjp&PEo,"!
Y[6t]APEG/m?n-tA9K5,XIpKLUM#7cpio**?W"O5KW2Y,'FSH1Snm3[\:nCo08^X?!T]A+-Y+T
ugl[XZh&1HO[R_'M.9_cG98>'Tf`05L^JN\/E3c`sDAA*m?%))9"h3L"kume9rb%Yre)Pp_V
WUG8I=^S\C;fQh7pC'4LL76IqcDtn%[/a)(/hs;G1-^Y;gdYqc)j]AK-]ADhVhG$qbHaKbfT.h
nfdG-:[n5Z*f:g<(._pishDMC,ba*Z]AAW0T[Y#1nZD7uUIMPkL3pcn),-WNp$7C2GJU3ZZfT
:f/OOn<^ddZ0?2ms3r['E)FjOJK:TIH%)u7KI:+!8o3narR`MZ';-8eu<bCaJ^P74nBb[>$/
c;W"%g$O[lU%K3@p'C($psMf%i3o+_;M]A^VBs3>k%c`<S-4!2G,QEl"O*6,V"r`W^e^;j+bS
!fjDXJ-lRVD2a;M6#65Lsl[kB"jfmUTV2?qfm=B^#sIkXdBP1KT^[He9Bl*f<<\$mq&u,h0k
Rl`76:>1?j7P/X2Z=<9%TLf5P*I)!V)[?Yq*UD`b_-F,r;S,[%hT2TE,$eA/g^UPOm!qPjhq
g`<<E:&*gL?uO(2q=L/V6KiO$sdT\b0t)oVJZA)c'Ni57G0t,4,lP/ncPc6'E2RY7'f[.!WL
s\ID8+uYTms<N'GsXh-<E:Sk9ft,;l_-pcjI^:H)%L3;JW"MkI@Bm:V"a<07LO+7BLS#K+HN
rO'_]AJ^mQr*LZn"_n\b12k@id(]AWWY]ARRLj$c]A,d/mq;d?N&jPmDB%gnt&bed%d70,S.qrK_
7&$b::O9%pX"C4?!RY%B0<n;+QEH)6[GomebLpg.eN"X-G2jSG)hKN:4FAd6oVBc]A7kf);Uk
6Xbi\=0LiiYn!oSFm4^/5rhKn(GbQ3U#GQ\s^C#_^O<P`+IC^f;"9R3Pj#X=3F>3j:pfrsY.
.7gXmt0Y6?EcCd'%[BYlb6@Zfo#3sA#04RGCSD#O;t*ko0Q=N4f6s&b:``jYBnop'&$TRP$H
FTWm[q_1j31!Hi;#s(Ki2\0SmF]A8Sa>Z<Rm'pcUQ7;:Fc24%Lpe7(-c.BKir*T]A^Z9#EP1,H
6P@;$3gL<?7#CO[)\^\]AWgKR:,h/,@HRE-32eor)/EfCg1pm;:G4)IE)*C=GHUf_X082o>5=
s]AWR3qe,XZe/RWYSg6[sB5i/o\_(`QN(0>Vo7.:N3*Ud(WG/*oC&RFk?)9=\GbFZkuSRGkHE
'LC"!i=/%8f<1N:c--H2c=Kf8<`3;O&%+KRB%.;ZLfQ8C?`br>Z5K""F\/TiY[%8;6GYU"U)
.#pG65."DUYB[IQ'EY9ZN^b1eX5e05?$q-c[X?od%b2+]Au')3ZaW(_Q7fFi9=]A0gXRo891j@
j0BKc9-0-VF7>]AT97GOTKf#r3l"4a(s$$-D(+T4Bg9Sr[9C]AgtcA4bV,9=0I3#hZE^aP%Z2Q
K?,<dC">&1Ys3EW@)OpJ^fiDb`@e[M']A?$WWh#Wu5b)e<bR&3UR;rh;Xs:r4b`W<\j%*6T!K
X3WOu,AH[bsNuDD&:MD%k03\2`iS9Ns%g6Yc/nP@T39eVrbelTF-k_g*#@>YMjcqRtduHNdW
sSUIo>.s&6!5b',q70-H5`1USoOYBDe/Xr$7f7`%!N?2n%-eG&G6VPah(A1A+h;Sf4,a619-
/u0hU*,?5`c)koj]AKV!/Pk:!GP<:+k`P-dSpDO(#<Oj`-;6YW&mYGhpA(9eY59GNEfqqAflJ
dqA/'q+`e8b8P$8c*QCPZ*Js*;EI#AVJNq^JaW+[iLk%l/--lFkO3n*OheBU=^EO]Anl!F"Ue
\+PWOmI!e#2#0H=T*T$2lKL%:-@%,?1blL[W[;/\OVu5@k&n1^Ib@3YF/+E,o!DJu7AA6;R,
Ye)o^b>]A'J7Jr@_e0i7$dsk@_8J@1`9K;4)>M!Y7"K]Al\Guo,EeK24d$TD&L1a?daM`WMHQU
a]A#5XjH8TqKPu-p/p=5-aos7FI^Okj(2^EdO]AcKr8Bg+rJ:h'<;]AN\W^^q:VV#WcWuAMAS5g
5mjkTQcDD6*2Z!.rinT-:]APojV3]A>$^NeD72MW/U/"oB//d0ehp%<!nmlR"FM;g-Ih+rF+#.
fjm1,QReA^PB_2&V91?6u1;7513=sL@hMfYI1(%<[k27huO^Gs=DqU0E:?ujFac3qMYpkX<M
JJkp-_mC(%EYkZCiL?&U[_6"8D:*<E"ckd8?H(/B0<Bj#oPV9hqEBj@O"Ii;;s8ZNbGk_P&T
JN#)-KnoE[dPBf_8O]A)r9G.g9T]A&7Bkm_Yb3)/?rThb=YSA8_@[Klg<;\0FV#^oB$%nJa\Z;
q\%+?C;k.TBC>>_<[MnkFhD*Y3;8-8G.&AL_dV^u)8^JjlppoMP*Tiu+I52a#YFS*r3/RonB
+-q<^:DVpK3G3`%;uG]Ac8c=92#$Qp,?ubOG<!"&k$uE_CTLoe_+LGJpS]A=7Er6TIe"SuMGMN
T-)f/.J%SL4go,0-fgMY=F^b:$WJeCdhmRlN(Ll'5/g<1ibb]AJ#/k$_0-@WPSj!;Tu*m/-44
7p)=1W^&3.0*Z2R:2DY'rDd43BZ\o]AC5?E^1is!1p^`ULT)8g1*fm3_UTLIkH(K4+b;5<[er
.eXhH]A*mL*2u4>+4sks&`J[2j-GPPk:OA:=O<2Aiu#N5I3<cM7:1U)`DqSA3[K.*g31r#G*I
+:-JEeoSpERTS')res17Jg)$dZO,\WFo-GaFgdYNY&pRP_8,ONiYjIZ->hrd!Mm+N-aSPEO-
Ql<.n6+l-F_qmFEpdm"n_;tT9TmU$ICsrsc#S8MS\(@_o^'fg5X8/LGpG-:9B3JB+<3Ub\?2
]A*-a']A4Ku4[#j&T>2X]A9/gER7g6__:/SF'ki(!W0J+%h=5p00$-;JU$UE)hltccFoM<+'&-.
RF5@IpMG*(gYINp]A\q&N@GsYe+IIu_8p8*F<(,=k]A>W;+N6FYtI7Up@5.#pl4Ds$b^XShM7p
,JYjus=#L-7OVo<sqB_l&_/4TN*0":,&d=t3,R`.hIj;dZDGUm6C36hX/Xk0d;93]A2>(hoa5
!c,%Xlo'Qg+5^hnYgJ'(A/K<Ue?`K,-irpe'?'KDqhV%up9cS/aBTHJ3,7*S',1ZdmY*H)['
p&--X`$ULe8LZdbq@9`VHbG@U3(R/>,'k*32pH/Tsiu]A&,3hUi^KW":>3Km15pDJ+*>d!4+6
]A`I@<b\1a_r^M/T\-[SXndM6ha`n$K>H.A]Aeb)P+5oa-h'9rl&JB;DSt=Yh]A]AXX"&=Dpd?p#
TUqKYd\[u-)m*hBIK(>McUk2,a.$*0S(N4C@>+MdS*8E^2@Nf<(?K*38n5:g/0H\5aF8Eol<
$=KjbdNV>LbWj/H'?#oq&6]AMhHKr30\W]A;rD57<`p-PIei9*g?`$OpuTo=-FY0X:ApKmJVk0
Q.]A3qq;M_WP5$tMXp#%R673"O4J%d(52U,b"[=_Ug)ji2b*"@:9$tifb4^Ns6nVsUaq]AD8i>
V?O)0cNf4H2D0m]A\j<VoCZ#"</tMio:2XAWj'kS$L=`"nWgsC@+:D1J>Mm!$VLm8%,_:QfH3
uFrh/G\:T8SuCg7s6M%;b#cgj-tI:Zgm&fL(T(l\:9:QkKQQB@#mou[XOI>Be5*Y-)7`JpF*
-S&`*fQX]A7CbHOI]Aic$Nm\qYEZDoh80+O**6pW@=:7]APGh<QWIL,qR:/]A;';3`$&ZBlP.ee:
F'fKDs27bcf<&IQ$aqq@6>Y[hQOekYUdSM/onHi#2<s%X!=.0:2m<J26H5(Ag$km0)rlhVKd
7k'mkD"W*c^"/0;#n<("56X]Alpc:i<>`nEqb<l*LGZ'm(4XDR3_W.c0[fW>54.t03bJ4t)i_
$g<1hu[6R5To%.]A6iWd<*BR$V<hH2[Ult;IP]A[gP-]A9b\C*QPWGhP^g\:Y.r!(FW#;PhV/?t
YckMDnS2?l6,GB%k-iED,C)Rrb,fR)U'0d0-9$e@db0\>d@!d3nP^r<.#+[Oqn9Hjdd,,+l0
G"=Dm-kBX@>:;7\;;k4rK2;Y^`@8HaQY?rqOgQBM$a-Hp=)t*'+ub+6q=:<G/G*A'=$s3:;]A
sHL:\9+thO:B:_&g4_P;"iE.V!;aRr&e*-%o3O0Z&k7EspD"j.\sUBqgbTa<C9X12rp2OQ-K
FdO`L7Vd==1PVP7RO(;p>pa2knqVQQ25^4-(0+8j1oRXm'I!%X!/4]Au$Cg.RRN'(`;H`DobQ
u%BKI'PgZHoEig4:jhM^):h\d9]As:ncDh>=#ks8q!$XnFk5[.b03]AelG-6"[k/&d;J\(iSji
`>cRI<Xd.0=^ke&51W*SsDIJtEhU!,KL2rs<:/HRhh&NWPKo58M"7U4"N^&P4N&SkcUUjkE=
CMC]AtFSPrE):[^S2#3O(pOEoYk)-%aEQ&DDa(!&njZ8h*"jc(k&qM4_^EIln8::sKSQX@$?^
fr0hgmAHapaCoXm,M]AO.C`B(Ngj5:*L%[7a0+`NE0Y#)?GO,?!iR2%8;A$dIkO;EEC8THYZO
+FsA,AVq1/lMXJ]ArD]Aq7163WpuF0?uB$GfJma'$s37+]A;[.:#K<n#Ups\++%?;g*5'T^9VkT
*!$;*PD?5?bZ6T$2LVsn9J;'hrA3:IcE7$68f&:l`Bua]A(NB7*OY?'r8"5$mI`nqXN#\X5S*
/P51JB%dbhl&Zg10T=Q>0_gq/1c!3%XLR9tjeb-94HaB`.cVB0H>ekp%EMOB/rb1"CLJ)6?_
#*o0GN>fPHO>Ah.KOlGHgmb$M2=6BllEffbAD2LRjgE1i)WD]Ath$N/i`#@'?l3e^?q8q6(Cl
DoYTi_#DkLGf^Z:(rk/L;r;V_KrMm*.8*&Dep?+VQ@Om9@f2A$$r0"B,ZHPk7r@HITNJ\s5@
&(]A-N"LX8'D,H-:YE+V7&Ln'N@*A7Kf@@(75Mk.ZcUcGG`e44T;i3H>#,Tdd\-_A?W3H<O+6
H/3u)S31BFiZ2B*j2FVP#qMnPXU`&Rn4#2Wg0C,/Xb0)4B+G:+_bM>cdYhl[i0A\nV`0bJh*
?/FK508`4YarFcjrC.(m@E9Ym98C9%+g0e-5ZfZ7:*X7Z@@,G)e"F#BH7^6[6U;l*\X,j;k.
a7:l;8_)`Gf<h[Vcu.9J51,qK$Lq.H^24S0-T+`43p*AQ\%'YQOrg@$JftG*.tebYVe/;Imi
@Dh'Kh#VO*Pm\:-h<Re^C869g@l!q\I%J%IV]A+!d:hE6%a4<moM.19\*</go;Wq#@d?Yrl.H
l,3=B`.3$+@M5%>5p=.Z%79`kKo,S2f$6Ag``LjVN"CUW)lN\bBl^W(fD&@G*Y2MVJdPp<7c
uCOp>/D7[aK^dBH]AK!,X1oa'MKC7#X1VQK=#BTDRe,)b4$bJQh\[=g>4^1QM%ud(b*onMPg`
_t/su1<%;"&uJdtMk]A!RrnqE4//<lYQO4gN!SfJ%c&E4rFqiW:>17@kN9+ZIT13VF&ua$>9\
Das"Z,"<*&<6W0G0_]AU-DD'hk]AXc/a1n:I.i"P"-Z.^:"`]ARKtdaQcfoR7%Y[R$92drX@M4Z
(%$kY*oj;3jK,R/p7:%@[]APXIG7W#4Z)E^F9&;7?1"5,+N"-h@o_edF#ZndIbsck%D&D5U#*
RTo-,nK@mY7[mUiA1^GG^OJW0#j384S4<c+dp$!u/bH?K'qUs\<:0<-:6q7d,#q1=KVO_kqb
g@NPQ59<P87^Ar,ff(N^K2.t.Nk*ki*MQ-;!HIs6u?"a&>RemVr.jBR`__f[-SM@>hOnZMcn
/5?g:l.H(tUg/9?J/`<Qh'F1,s#gWs0!L^W#:Ble\BTjf[Tej?U9'YVY&0g$\_!oH-@-!6>,
duidDb]A8(`W]A')6BWl.fMhkbU7M%&#&Z"IZ&$l@XRk2!`%/XFs%LjE3s0s@QW2L#;M<!*@g"
8mh5hLihT'2ab?!uN@($]AI/:ING!9Kd%^j^s):6*b:7)(@kAe@(-ToOE+YBY*%3EJES1p!DL
)\Xj#E5"Uo0pj$e:GI<B:YLN"cI1od"r%>RHN<uH+eR%h>il"K!?W\sMfLZHgPE4I26\Q,]A[
tAe)9A,,!dZ>k_8;UlE-VJ.T^QB5T_D9UAFK:!g@_@(#PA/T1nX;Q$5afdQNJ>#pa-WuH:hr
G4\8t&T4NXL;;0I<NStnt_%#@T@lDM<R/?d,9om??6_3N-A"/q"fMM>d-O/HdAs-sN#aJr3M
SgKd]ACeU3V<5jr$1f?I%q'#?MV3edA<L1Yq5l^he%<$-nhWJ4>Ft-N!O&sA5`*;a*8U/dO2M
"N8Um9ZR_Mcjc^Gb=r0H`rAder5V"V7t6&4c7jg6BT!BZWJ!i-P_uQt<_CpOU&4%41aMe="^
LH+l:e+2Q.O^G[e*[Obt"E9?nEX0l,gitE:pX`.uqYD?PXIstP0!Dif^!ph)Fp?SFY6?*S_+
sQ4D3mL.7Vcq\&@qss^Q.@E+;EX0TDFG/#(FFV>%D4[8bR#SMZ#__a+k`m]A4,D,2&?pOn>P+
"1HI0H'g64'Jb7DMs!5Yb+(/dM7iut^"P62k:Ue9Y_Zc3e2fsjD[aJi@^2W.4iCtX#OT=F]AK
1p6S^@u0-SnNn";Ag#fWj`6<adA:sQPpuH6.Ig;O?-E#8\l%n:#^&g@B`a-\:N2:&5p:Z!"L
^=#*AeXfpHha,LO8@iC>BVF=fKP]A,>b21/bJk4:TiYs/pMNl`\<LB=LBPi>`7:29JB$\%.<&
;J52/[X3RXja0_u68=\Al$bs`@$mc`S=H\/@DPesARck&[)7Ve`i4Ua#%9@;/[c_`9Mk-Xp6
>)Zbn!n5/e[.7cD4^XD]AX*Dephk74Ek'@oi$505)4#[YX(`Ke9c3O`^F+m9hs*l_f[a87'T`
3@WF`B31T[I!Gp%da^%piELb]A!(&ep-t\&hcc>4U\P6(W;;TW0aN\(O=&3=Gb(>!Une"(aTc
lMhVslFMh:5gToh$EJ)`NX]A-3dM8gGig7)_U!#q`p@)uS?Bu8o,Co/g4fTd/W[CQC/)ij7;!
n?3)Fq<#0EF_A`sVWs^4F1\BtL=#+\QSf,NcD'ZRi1V+.W\'*i(H/7k,(jQd'Sj%Dtc#h+]AH
4%.V&J*/[hMS"#-.)1NHa^Gg*4!/2L+%p1</h!k+0"S"#d[1q,;H+&WCa6t'iBeo[PDsYT^`
>pmQ$=%K9HK^jZD8E!;Cj^HAkb<K<qN\a;R24:bZ\,@+1]A*HX=Ss!I5uh2MTR0T>Ydt$2Mma
_[OXYS,d@2XYPK$V,^eB<X*&k[nAP_68#hgFq'"9:8X'@S,Zo0`$o1d3)Cc^PW5)Vr/#4!=l
IE71M/6#8KkqId&mW^J?Vn.'pW8k<%]AUtj6";3;Z,$'%L?-2l?5"@)CImGs.NBi"<IVZi:Z5
WV("V6>Ik]Ar]AI_rr6(iR>t\Vj!VVqS(C;p@]AF8(iC-PK$V.\GK+A=#Ba]AI'3BLGT$^r-.?3U
jZE`qEJPmgA'=:UKm+^lmU;_eEO,pr)Zb1V77Vss>Tq@rR#=Y4?3#);e9bU/7+9S,1J5)2jl
6;Sh&7%"U5O#K!CgF+(EFQe1E"!F]AZ`RK:rOAgq'=[k@m/9i!,NBlSC+EGjM8aFrfmQJVglD
*8]A"I8lPb+kDe@*Et+nIZ7Dt1T,GTY4LrV??9leo*6S.'sr3)B5+`7+G[7T8:p1p'Dpl@/X]A
1]AA=m\M)RjM;J5?KDtMf#@"J$X%aZEhmsmL5#B),?b]AiufAJN2%:4\=i&,`g?A$kf20]AT0Ig
aHeD.7thcp"-/6+CjOYIh%)j6oo1hQHCBI_O+@lbhOoi#9*0Y(:BH)Rp!SJSX0^\-B*eCZ$,
@#-NMmi#9*0Y(:BHs)6ftI2h8kVKH5u.dAQZ5DT4*nDV=:\h+KT.@>7TUE@k'^O/#go7$pmF
s:16!DY,G='cpE*d4p1iPNq@!r~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="284" width="375" height="413"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="absolute1"/>
<Widget widgetName="KJSM01_c"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="697"/>
<tabFitAttr index="1" tabNameIndex="1"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="47" width="375" height="733"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="true" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="n"/>
<WidgetID widgetID="8390532a-b98d-46b2-bdcd-17550db4b517"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
<WidgetTheme class="com.fr.widgettheme.theme.widget.theme.ParaEditorTheme">
<FollowingTheme styleSetting="true"/>
<ThemeColor>
<FineColor color="-12999178" hor="-1" ver="-1"/>
</ThemeColor>
<selectBackgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</selectBackgroundColor>
<BorderStyle radius="2.0" lineType="1">
<borderColor>
<FineColor color="-2433305" hor="-1" ver="-1"/>
</borderColor>
</BorderStyle>
<Background name="ColorBackground">
<color>
<FineColor color="16777215" hor="-1" ver="-1"/>
</color>
</Background>
<FontStyle fontSize="12" name="宋体" bold="false" italic="false">
<fontColor>
<FineColor color="-16179648" hor="-1" ver="-1"/>
</fontColor>
</FontStyle>
</WidgetTheme>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[0]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[152400,1009650,228600,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[254758,3181350,3181350,254758,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="I">
<![CDATA[0]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="分支机构查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="1" s="2">
<O t="I">
<![CDATA[1]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="1" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="n"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$$$]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[_g().options.form.getWidgetByName("tabpane0").showCardByIndex(n);
_g().options.form.getWidgetByName("n").setValue(n);  ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$=$n]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="1" size="80"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="单指标查询"]]></Content>
</Present>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C2"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="A1"/>
</cellSortAttr>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="0" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand leftParentDefault="false" upParentDefault="false">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" textStyle="1" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@&<&dmk=sIk(j-i=Z\h3>>?VC(1Q&7F6Qi_#k6qB]AVe='!8r93TQ!I$?f>T_.DSf>8Cd%XI
>e5E_(_2.k2*<#m>p3Jk\',!KdMD8K'nq0APA)s7D^A:S)q]Ao9l$@IHR/h?fjq;O-bWfL9Up
c6!Bj.='l52/":sj<0_r3k@nQ06,&JQY0J-.(@[]A"G7(*>>:),u5Ib[Zda5*h"m<53c!WKf=
`;olFVepafY5E[WsIh6mAoSafc091NEfi!"nPF4(,rNHce$W/$R778@r8H\/1n>mKSsTE5mZ
2&_T+\?ILVK&a-TXu8n`JL45%O=;o/U1qo,^?_%6cb9R@b0:lu\!IIdbb[DgYhTfXmSV6-sb
rnVkF'sQ?u-a)clj(I:_C0aWZ(8:KSqmrkI>>*l3X(^Z!cS.8sW"s<p0NM01qi9ODdUm'4$=
-O$EA.-GH]AE13^(OGb.+T0BgRI9[>M(F*Zf#i"g7_Qh51pR$=.8l1s'`A2pU8O!/Z:9"IY3K
O\oFqD!A8!\OZ90:HI*`;l<-$e5)d1f*u(!2(Cl=]A]AgmoWR1DK=(JERol(EBA[1rX;N@9$Ie
5IH5r9h.+jV<EP4I7%oH#r`7qBF^ueZ0fiCQ]A.)I#iE=1e#[DNdVrs@`f,3,<DlCUYR>$nEg
?/r4M,]A1/O4O@D\<elFhEl9IdP'1fC6<?_<BWKlp6a^mLSe(ZcI)(R9s$C7MZ>/-M[D/D\EE
lCh;5R\6@iKA;m2Nl2r6I7uZPnmKOe<dadK7UE:"=:_IU=]AM=-[!^ko-d)`n#'7NSJqZg$QE
'qV*#Reg.gE[n[-SM<`1(,=BrPil1jNOb6^Ma;%AcM#q?8V/]A!JghR_bp=G=Uh[@R"d\_FEN
3d^eL!`C#3o?)G\YP>C/fmb1X-h2_e[@tQM1QJKsQZ:cY_$U*X#>aXg<(ak0iFQT8gP_]A&DO
(l*>QleX(URE8Gb>,D.%E$/7/]A9ioHf%`S.Q<!qRH3O'7[]AFI'Oc]AX<-![P:rG.#r35p1>VJ
AZ)i"mk9Rg:YeXq7&fiN8/?>ZSp^IHXJ]A7tYCgb.KnD0Q7"_b8QVPg[VZ>T.KY7t4cZ9L>p3
1.7C,BP-iZW[6q"5l&oXU^?K<%/[hbo9[N5b2eq9Z'1AOJOmTUktGO1fA]A0ACK4R`LFsf"&W
C>EEFB'a3[)6)_75?`?@bB[iU0$5E+URQ)E#ochPr:`FGtLth.%*NH'UgGQs4Mi29&F83/0@
DS[d!2ftT_-;jdQ6nQQq6e'Pp;86Psp+313#nJ7X![>PYJD*_(>ON_M2Bi$m(S_RIYjZPl/I
o8C0jNTQeO7OkV1L$BRaX>;+4^O$Pq,CgFFL"\L<`a!'gMO,<U+VdZ/3#[kNS:di5$95,LF9
k-7jNe+4'YWf`QEg7lYOe=T)=W*r)+q<h=!p([hrG>)1Rm+f5Z15Gq3H(b89/k6An$tAhPGi
UA\R+?Lhg,+Sd*rBEqTKAhf/&[J_=^BHH\i4Q6_9OAuauR2'O-`g^lU;B8m&X-5QYoqg1o@Z
A8H'gE3^Lnt$;pr';IB*J#CDj#Mso3=Y$]AoG2fI,-Q0kJDhB1AOV6X"!@,7=2,MiXc^LT!F3
C8.sq*Zf!![Q^lVW@s=pXGMqeHW7kGc=.P.\cg.Hb5NRgNRr/#/1ZEHk(Ipu8cu)Y$pIWcn!
l%:WD8(5?gW*k38eMV1CDgHSRdit(A;NU!0E6;Y'TkeoMIm?7@LSjfRYotY?S0ksB2BXk!TL
?IoNg2]AT"0BY(ncSZbaF<NG?=QB3NI!FO(]A2E^>J5+Y1Dq2YlEaOW>?b:4k6*LouQd#-q&Wc
K-D*GSh=nfkIntI(k,0a_S'2)T,^qqE.aq/2OE!jP$UhBbcjHIR`o]A!6"k2T>>\ff>Y<3u/o
FR'qKW;XPGsCQ0cbNo)UAmQ."35HmYq5Kc1>>1\\.=i"OV0O6%O/74&Xacn[&a^giQJ!'o)J
9\Y'6>btW]At-XcSI3r*+/4P>ZdQq>$(k2b`dH<KC_1i=P_SolZ%\<:[X7O-l[><CuKH>+Dh9
D=PRVoHL$4W<0-T/k=beA(Z&UGB0^@0NQ]A=_m3KEabF_McoFIb_2"MKYO+.Q?(',-_LpqO2u
0QFacL(D)GCU9MQ]ADa1ZJ9K'@Uu'as_>mmD[m$<oPLI=p*^'2GW5q0E%tH%'/cPhY$!o(acl
p9W)H?aeL_3IXB#H\[;+-b0so%t:1[U5[qo]AJO1WW'#IaQnY0!GDmLiRPT&BLR`WSQ?i`b`=
[TeE&[ZlMuTu*cM_*GBcf=OeGa57PX>X,k)H+!?Stf$4qIA:6cp4'TSs'8PQhgu5QM$&:d>$
b'Mq$q"VacurFW&]AZ+n.EEUW-C$PYP1U0%80m_T#$L,aCd!(#iAF#OpIe;$)Yr98)(0Rs04?
iU9G^]A4QmJ,p'd!!=(R!!Y--!UTQ:IGU["5$M2C8`X[PT/N4ims-B9'H>lkrYG]A'jV`/OZOf
c6V#hLL!!Y--!=W?9!uSfR"u(Q.$t'&;(r-.F42WP/&XLal-:o/0hki5FJ88/8!=W?9!uSfR
"u(Q.$t'&;(r5!c/Z8$cE'9ub(,B+bFRsN!m6N,u"9~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
</MobileWidgetList>
<FrozenWidgets>
<Widget widgetName="FILTOP"/>
</FrozenWidgets>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="客户分析折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="客户分析柱形图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_jygl_zb的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cpxsfx_xzkh" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zbx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_cjzsyj_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_tb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_rzrjfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="交易分析头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_tab" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额头部指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhbhqs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1的副本" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zdzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb_right" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="下拉1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx01" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_xykh_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_dzbcx_dw" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="信用客户重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="信用客户折线图" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="data_jyfx_jyjsr" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="DATE_CJZSYJ" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="查询机构名" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_cnqq_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_fzjg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_khfx_khmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_zcfx_qsfx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="date_gmfe" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="市场份额重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="场内期权重点指标" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="bp_jyhx_djg_zb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="单指标查询" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="xyjcnqq_cnqq_qqkhmx" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="para_分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="eb964ad9-cfe6-4899-8446-421fe3892538"/>
</TemplateIdAttMark>
</Form>
