<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_pany" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select 
    t1.branch_no 权限编号,emp_no,t2.tree_level,
    (case when t2.tree_level = '1' then '华福证券' 
         when t2.tree_level in ('2') then branch_name
         when t2.tree_level = '3' then simple_name
    end ) as 名称 
from ggzb.ads_bi_ygxx_s t1
left join ggzb.branch_simple t2 
on t1.branch_no=t2.branch_no
-- where t2.tree_level='2'
-- where t1.branch_no='2059'
where emp_no= '${user}' and t2.tree_level in ('1','2','3')
order by tree_level asc
--select distinct up_branch_no,up_branch_name from ads.ads_branch_simple 
--ads.ads_bi_ygxx_s对应ggzb.ads_bi_ygxx_s
--ads.ads_branch_simple对应ggzb.branch_simple]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[setTimeout(function() {
	bakdis();
}, 10)]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('CJABS01').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="CJABS01"/>
<WidgetID widgetID="11255f72-62cb-40a8-90c4-9fbdd1f78556"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="level1"/>
<WidgetID widgetID="a81e383f-97c4-4ae3-99ac-8cab0e27c031"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=value("data_pany",3,1,$pany_sx)]]></Attributes>
</O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="pany_sx"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboBox0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="pany_sx"/>
<WidgetID widgetID="e2cb58af-900b-4cca-a9c3-b1407cc19d89"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="pany_sc"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="false" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="微软雅黑" style="0" size="80">
<foreground>
<FineColor color="-10000537" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</LabelFont>
<ValueFont>
<FRFont name="微软雅黑" style="0" size="104"/>
</ValueFont>
<controlStyle borderType="1" borderRadius="8.0" isCustomWidth="false" isFloatWidthFollow="true">
<backgroundColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</backgroundColor>
<borderColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</borderColor>
</controlStyle>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<allowBlank>
<![CDATA[false]]></allowBlank>
<fontSize>
<![CDATA[9]]></fontSize>
<DirectEdit>
<![CDATA[false]]></DirectEdit>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="权限编号" viName="名称"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[data_pany]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="0" y="211" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="22" y="1" width="330" height="65"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0_c"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0_c"/>
<WidgetID widgetID="eb269fec-84f0-47f1-b948-02176fd323d3"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList/>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting/>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList/>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="67"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="67"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="report0_c"/>
<Widget widgetName="pany_sx"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="211" width="375" height="55"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DEL');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DEL"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DEL"/>
<WidgetID widgetID="b9b7d774-c9dc-4b0f-b5ce-84ff4dfe0418"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DEL"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,1524000,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[762000,5237018,571500,5237018,762000,5237018,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[取 消]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="1" value="45" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[FR.closeMobilePopup();]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[确 认]]></O>
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="1" value="0" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="移动战情室">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="rqsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$rqsx]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[var a= _g().getWidgetByName("pany_sx").getValue();
var b= _g().getWidgetByName("level1").getValue();  
var url = encodeURI("${servletURL}?viewlet=/HuaFu_YDZQS/1st_Menu/Home_page.frm&pany1="+a+"&level1="+b+"&date="+rqsx);
FR.doHyperlinkByGet({url:url,title:'移动战情室',target:'_self'});]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="96">
<foreground>
<FineColor color="-7500403" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-1775374" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-152825" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@%`gP?/[Cdo*T=>-ncK=c=@P\4rbf=\V/c7V,F.N+EA1<)gdY.C+]A<CITRL<0Ssq'G(>>O[
<SO:dJVn[X\q+(.(P_=L(3K/Ft4U3J3s$BBIDuF6;ON5.p?*J,fD\\^.t8oALu:p-u2gBt=o
YJZLo:J$1L.nd1ufIC^DG1HAcZ%tVMq#PRTUMoua="&lLM_2[VtbFWZ3roCZ<q!b^F9VO.FF
XYtRajn^bjaL^SdE@9=287rn>l*M1C?+M)DpNU`e^`5M[Y)'"d#Md-N/3KL+/!2#=g/5kH(T
)Jh-t.&0EgaPZEM1j+i"B49['CZRbM$o*_@a:m@WhJg8g8nh>N($[*F5U?6h-I5Zc:V[hiN#
BnhMlet@.s8E#Sc:<<\Y:E.EeM>tt-R-THWJkomJ_H\&b)Yk\h+NXCu$oE;_E%7u."U%`dYS
NVMl9f17C\;!ul9k.-&aq[#5(Z%9g?T>Gf!9L#1a<:eiq\K.'k(*Ci1p'(3&8I=7VX=1Ir.e
7?(sQMZ[@U?9n?%ckj]ADTa9+a2#UD6b\C=RFko/C,:tJTPnsrFTN5J_2B*Tf-Ulk$'&*ZK*-
!s8%\3[F(g(4cIql7cVT78XZeVD"+N-$j>+,\-aO*c5uLZno$Y=g@c*Tu5>/0'0,kuiqW5)A
onj25M/#4c9Poq,;AQVU[dZ+t>qm:&b>'M"M2#):G2/"":+;<aZ?C6K?Ro@St,NsLA$[)6jS
Nd[>pi3.1P@lS6d!=ZZdQ\CR:KkBJ&ELso@1-OfQcil*k[de[gn3ML*oD1^k\cEDOh"(d\gV
&C+_>4=kGaf?Y%0o7u]A'e^m]AZDmKI/S>V'a>T+\ho`(([_F&OXaB`[8"U&c`[@E%K+D#jg[r
jKYbA)GKT7R@F]A^p(DTUE#acC!4A4A>MP\n+$>)Y3cB$b253OIU6rJ,s8ucOB#L&OrC<U'U5
$/)FoDQV^-gobKG:^:gmrE9i&P:<T*6r'N$02E#cXOj\quKeQdsQ@EPX6^d*b`*F$MgAYgDR
sr`(Y<O2""bR!@E+!F,"JT7UL9>*-C+H!%#0F'!(`a_\W[W4(bfV0GRjQ3(JcY]A?WlQOThQf
^tj1YXCp='2)*a4pq[G^aLPJgDmVt[B,(m&#GXHYCZ9@($R7T6E_-P7=-QU;P?<\hW2.g(ri
#_U&Zjh$XB`\-_=,V.[b5_ZTc@5QdK1h3/;dNS2RY_0dF)8OAdgLPl'+%jHXd4TrBu7?A4Yd
tc?ud,X/^gaQs^WfdnC-6kf>0;cj;'$`$69VAc)b*a4K'TMopQZh5bklBTI,D5er"E"WOlFl
&)NS31c@0ol0fGak8DaGZ,M>eQK7^cs*g#3EB,'NdA*jgL,UCHE6P-WE)\i=t1^$V?SG=9=E
:%(BX+WZ/nK8JYL$28]Ao68CWMQ(W0X`*')iH9;M&4'n3o;V;lhtDV,[/1EYbaQGN--g,:#4n
aJq@DNpX:3[Xfq#(2bY)P;#W>?V[BNh,b+ap8OU#ijINXDDM#ACj?-C9Q0G3[MF70&k1g4D>
SHJF8n05mu`c\V_b0!1CW`n.5;\4'+WBoBLf!)$s3VIAu*egjMg7S(t.]AVO.s3Z4;MR/S\PB
S)N%jCc`:J0-"KGV"2%";.;!.="]ATRgf;:$AXk)>sO2&YQb`=U1-AlSK'g(*:kGh9=a\'q(A
>_J<PKp'Z#Mm%,puug"^*T\h7agPKKj9Iron>snNmP69E\USt[K$+B!9)[7gP%1$]Abe1l89p
Z73Kj+nr0O.)l:N6cc)dn88+YrM/#[e^/Ah2G*arED<%bebpt^Lrc2hEj<NU;EV0o>lX+=+\
8FPZe;-m.F+tErN<ZGb%Wb?GnB]AB,\'@l4T;5"?iPi(sE@k->hRS^*,WGB?%1CQ".(W_5t2e
.,,Y"/:a#'l-LMf?%+I_ffU:2$RB(M>67o/)rI$U]Agu[;&`ebsZCnJT,&*o4#KNCug>iLju;
C"K#R-e6c61M!KRa3tAg"If+bOBt2qkA]ARLZWJiXB$hkaLE'.b&_L<,%QjTl36jfKYMB(`F6
Mcq/<XS7o<cYUd?5:f,._C(,cXeQ3K@(A]AKeP?Mk<"oQ5$:^^r/k?lkfJV+)+nl]A4/K$d&ZY
K@f7uE4\(=DCf.TU@X`It[]A#e<)FrQ=Mr=E6!=&Ke-g/$q&XGk'39<eqE^K3"eU?nIi[<76L
@ogls%joQ1E>a9T1Zgm!nAriY`_V$RR;e5+@Fm*=AU,0q_<%_q6]A"SajFuA99-@Y[ii#Is03
VEBgMT;X/kQ2AipDs"7lA=e)(;;TmC=R*BhoC"I:doUAR/uAC=fGF?'QAE9`n$[Af,>):fKI
4mO[QoU.h:"R+Ju\p2[+JebN;]AXYc`bOB6j43f$)j(Bsr+SHK\8W@KU6T&Gj?q_E?i%fY$RI
#n28Wg_R>/dq2NE)a$]Ar@c<G*#1#KD4.uFmj9+-qRA&Yi8p'ZNgdlL]Aj^9-)#"Yb0TiNbO>a
lV3)g<s.rN<X,+^/n!cNd@nnFth10Z916=Cs)*O/F!pPXAp$RK>7,VY9Ce[SBqTK?o'`.9@M
CFo5*Eps+56^6g1=DBZSL39-CP0P8.qe3O>0kPTAf:PoWh;i4CHC1s;k$K5j>-Nqs'XPi9[_
kQmXW_n/eG$c]An*h&'Z>^$JrgX=e/3^\*bFVHrNP_;pR-[1C1n^-hVQ,3A@?btq0*.!NEfJM
BVkWrq-2e<pSZ_s0+4=1.Ws2&S5Xj"88QE+\[f+RX)\iHWB'n:NB6hVABf_#a&0s,e"]Ac?+Q
4Ms`"#PXq\<rgYl:#$1R$*(BH[YdE7]Ae03`2@[d#RmUCK0I+\*H[89=[H0#nX5<<HtKa#I-_
XHG3h'3$8'(s$cIDn<o$)!;\:nP"sh%@fJKkK"8b]A\U6OpQAa"F/B(qMoiruO4dR3-L),pF#
;f:a=<:`FS?/jq/)cH`]AGm?N`"UFZtK:U%8S!df%%kVg)a?hSsUtQM%_[W=,fIdQ'ai:CN/I
+02*U.ViKq4'q7OK,m"mUaW[*ks_,X351UZa8KAgB[tq1pIA2U3P+cRRea*4jEM?&[/''Ql5
)N+LkcK12*kU,T@2!e]AQZ[>EPd!mCC=$YMPm;ibo2<a0t4m+@?]A)MOiKC^bQ$48BsM53@Lsj
S8X@>FX&hT*'hSm+ZU@*beEO?soHsE3h>*VK*Iq&sC&tI3N.l4Q7Ts?BTSXd+`?@aUCR(WDb
^GEO`99^jADF\mo3P%1d#lUaTa(kl)`%mc/nPg7kJrJ[Du&/3Fmn_KOlP-Hj4lCb)6iNHR6?
_6UL9^hY3CNrWHr.1>"hWp6s/XLLQh9$dojB-8ns?HVCY;/)OQqoMs[m#aEA-lo9AeKq3>:6
S8=Cf2?qi5:G1kZYWUs#"i]A@;Iu1o*;mg5MW,8]A&)?mJ\@\H:i6%Z0-^bh59XE1qPYD`b`2P
)pE9J@"8s,U;m4KL/\5XMs/nHH$7nVc>MI@s:hD;1,b("%>Q'jC'AVgT;BHBr*T[/`gC@;rY
4spoqVO/dpa,ohGL]A&2<S//>=dr"-G(J%Aem0@,.%mUH>$8qE]A(uDHrDY9NJ[js-#-urWMaK
,"eh%,I<HjlT)m=")5p0P#;*CpLor&q^UO#n#l;9^E8+if%eY@FiO6TS*qL=P-RL?ufg+Ne<
%r6K&;<IV[mib`PF]AtATFR[2?FJE]AeegZBcl(mUIr\j.VrOR:*4u:W>H;G.;5s;.Ooq3AUK6
g?)l9RS3#QG/S&b=c(p"/%"KZhpN%t*iZQg,?4q&]A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="294"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="266" width="375" height="294"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('CL_R2');
ment.style.borderRadius = '12px 12px 0px 0px'; 
ment.children[0]A.style.borderRadius = '12px 12px 0px 0px'; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="CL_R2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="CL_R2"/>
<WidgetID widgetID="47eb05bd-2d0e-4647-a687-bec0a1f403d9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="CL_R2"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[457200,723900,457200,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[762000,0,0,0,6299200,762000,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="1" s="1">
<O>
<![CDATA[请选择需要切换的分支机构]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="1" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?hZc;s>0MUM/2cP!O(e0Vl*^AZGtda?=(90M"L8W"jL_2lQlk+\(N*E`726$NaJM!cgj>#\
XYipZ7]Ak80UNZ&1)A?5nXeQL&cdXge0moS`ri&+8pIs:1h6'4m.+^Sb^S,hD=VkDh@nqI:;^
N"9R9=J&:@_"L?G,r7h.-PmDEinS8EKiNg5EYqrA5+3-oNfVN'uo7SA8/r^QLZ>WrHkem.Un
HDkXb:`_?f@t1K5Bm+nG[t,GT$tcIipD"g3J_aMJ*D6-5L=#g:!<djo2c`VXHrhA=PQ7P>L\
jpW+mMh\eX._QE+ZJ#&WO/Si`T%ciD>5@8KS/4q9Ni#mNgtUa:"d3IP^T7Z]AM=\P;R&)7hfu
`PLC2;P48Ah+BMObU;%sAoGDANr/ORfK4K*d<"&jLJge3c!!Ah]Ae.BQj2rduhthrg:5LuUkY
B5GY@-[]AdC$9a)cd,j7k?kP8Be;?)*[q7HL:W:d6Fhr\?4oQ+_*5jG2qDMDbWHc"2oI<&hR%
1$D*fB1FEQEqQJT3cWI[")b7tH2JVe6\,D"AJForUics<\+A=VV;SBJDeMlUNZM0ESpR3m!N
g1huVrR\uPI/dRPfVZGr6b4A/AU%?XtK:`EsR:k#gcJ@Rt`25j5]A>SG=aSVdB9WKZmqGbm4n
o\]AQ5!F=Al@>QBDm1"?YUJ-TkiK2clfWXm$e@Z4'Wqqr@hpCZ3f031O>A-Yn:KlA4RlOu,fA
+)rdC3oJ>*B[j`PlI\#h"KuC8=XF79j"i1V4G+7`6'+%0>V`.WNm8OTE2DMk7[#N#juE5H)P
U3<l=O68SnC#IY-#r6\Sj]ANaPgqW\'OK[,23m6T/>c]APUeDukF$g[7JEJim1WL%Ze?Q2O^h2
iVJQ8&8S-7>>-WM!l!KDs7.@47gr^XAVFlqqm$+U9Vc><)2&INU?X]A7!A2TgSW__\!e>QX*S
smURr7hR`#5`bH<"7;]ALD%X5bNBC.2<ll#pJ.$$jt3UQ;phJ,k-1[Bb,<&&p!Q&U<`,<eIL>
%SP%bW*L/O)pc#_ei8%pMY`eZgep@)H^9;[.cY,"!5TA1FkC^D-HV.f[HB&X"APVdM&$EX.i
d]A#B4->n+<^a#oFF>nE?rse(l&VnIr(Xj4c$n=U:IS=8pW+QRmPnl)3ZcnK<>Zr;_3heC,:t
(r^jL5s"Js%!9m-po:5,Yt.Nn>VqU(_)+NORj,A\X,e/gJP944gqE[&SfH8j1F?p1KL"dh\R
?4>U(,g-5HHj/%]AfmJ?J-g[=^!3n?6#:q<(OF/o*EdG<VgXXPURn5++X90%%W$#6OO/PTir<
S4eE8%$HtGTMU_@t*'b?%.YSMXm.p2msVXgAp*u(L$!"T5Q`_k<%GKS[O:7et`!P:&1$13sW
j3q.,ni:-N\V.XAokJtUOBB,H%S[-3d%"U9[F0"oQb6Nt!`VEEQ.PHJDRDk%8?c%*e_KZeEM
A[(M$6R6Kgk6]Ao>2A&I`dt5^]A^eBBQ*FA([R24"Qh4n+tc(BJaC1Y@e?6AbeghC4YBZ/AMG'
75;>ahLc@_C!qO]A=b'VJ^&/dZY$,[T!01g@#8*./;!]API6US+CkW7<^2Y0Yi,[ZG-).0e?_J
^#N9pt4]ACrL%Y#m!>*L5lMj-h^Dmgi@6!;8"-Ks1[U4^,@?Vo*7A,!iSWNIrQY\M8]Al8?&'T
;q0:5hAC!W<8\4qgF(@PL"5g8P`/)IPaVEVg#CW:rM?4C1TAU)_,*K\NAIo&=W*i(d,*RdOh
BNmZFgX!NpSSl_OdkW@NltJ`TWfcM$1C`!FuBb?`YW6-:mY_B9pG.X4Sh>#q`:h91+"r,#;@
MIAc8_U=FK=SZoLG!<P73uo3^'&O3BVd'i[$,Y1FSaG,Momb!u@@>:PG:D0icYurnI:NFWr@
TA4+;4qPj7^gS/rNKJ@``0!U"a@P@6E6^(3>H1[5B0(MTCd(L$YMnm&u/k4_9R5j$J3"(&hs
^ctVu?32(S\V>MgC\&!TE.iu('5UmlH7d!8TGpIq(Y7WIA:@qljla#;bXlisjM^Z6[Du<Y)q
O[0-BN[mCpJ4&D)g]A:!lV1H,@\5a$)oX!?Ko17,0-;<MLWMs#jpEDAc:WtXl8$K0_<X/%/Bt
F6co!_hbGe3'Otr0,%LjFBo'>r@jup*n(OQsqd4(?T^c/cpjkJC0.QJYMf!/^!82Vq)9J^SD
-Q]ARQ>*'G+NFb)77/oNIphn$BXtE>\?-K:Z0d@0n/</4.NGi/4h%+`jPg\g"N$$oa89B)s=.
UH)S5%5,/>kk`aepM.o$C)@d7A'_9H)!dTRi0L#53g!1f&:0F;\#Gc5LMoKKJ/e!t6*7IsgZ
.#u4DAdL>-2m-Fg>42k_u"c:_;MFeIf9TTOm>u[$5iGE)]A[^q]Aa8ukcL?LgpDbiAWjBqML'q
J]AX,TM(4K\]A6M"i#@`5FORqYfE++)7c/(Jcc#p3B37:!_r]AFc1:PZf-[=Y.ldSPOj/j&i!B)
-FbM]A:uLOf9uM:[Hj*B-g(RSW@g$3FV7)h&H\q+iI=V2gD1Q(RC)Qba1f&$2md\[m38&,u<4
4KYm_fE[t:JB^p;k#9LR<m)f\V[!@12fS0LTD#DLLO97LL`h9T,)@@n#U5'IJd<QV+G'kCL`
h;ZL8L(aR(9m:q;jr,h+Nh+S'O9%NBc1,g5`hSo.+m7'#HW5g9Q\0?'>ej1:K+akO,I\8itI
&]A9Ek'"]A\A&<DWj!:^'iq"]A\A&<DWj!:^'iqrP,Au?67ccm+>Wihb=$`5sA:uN4eZk~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="211"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="211"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="CL_R2"/>
<Widget widgetName="CJABS01"/>
<Widget widgetName="DEL"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_pany" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1692320229115"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="6834df43-b7a7-49fc-bcce-fd1fedd97786"/>
</TemplateIdAttMark>
</Form>
