<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="rq"/>
<O>
<![CDATA[2023-12-29]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with tmp_px as
(select *
from (select b.up_branch_name,sum(nrjlcbyl_jskj_ytj0430),sum(nrjlcbyl_jskj_ytj0930),sum(nrjlcbyl_jskj_ytj),sum(nrjlcbyl_jskj_ytj_xz) from
(select a.branch_no,
case when a.oc_date = '2023-04-30' then nrjlcbyl_ytj-0.9*nrjlcbyl_hbjj_ytj+nrjbyl_jtcp_ytj-0.9*nrjbyl_jthj_new+nrjlcbyl_qy_ytj+nrjlcbyl_jtqy_ytj else 0 end as nrjlcbyl_jskj_ytj0430,
case when a.oc_date = '2023-09-30' then nrjlcbyl_ytj-0.9*nrjlcbyl_hbjj_ytj+nrjbyl_jtcp_ytj-0.9*nrjbyl_jthj_new+nrjlcbyl_qy_ytj+nrjlcbyl_jtqy_ytj else 0 end as nrjlcbyl_jskj_ytj0930,
case when a.oc_date = '${rq}' then nrjlcbyl_ytj-0.9*nrjlcbyl_hbjj_ytj+nrjbyl_jtcp_ytj-0.9*nrjbyl_jthj_new+nrjlcbyl_qy_ytj+nrjlcbyl_jtqy_ytj else 0 end as nrjlcbyl_jskj_ytj,
nrjlcbyl_jskj_ytj_xz
from
(select a.ds as oc_date,a.branch_no,jtcpnrjbyl_new/10000 as nrjbyl_jtcp_ytj,jthjnrjbyl_new/10000 as nrjbyl_jthj_new from ads.ads_ytj_yyb_jtcpnrjbyl a 
left join ads.ads_ytj_yyb_jthjnrjbyl b
on a.ds=b.ds
and a.branch_no = b.branch_no
where a.ds in ('2023-04-30','2023-09-30','${rq}')
and b.ds in ('2023-04-30','2023-09-30','${rq}')) a
left join
(select oc_date,branch_no,max(case when base_index = '14-1' then childindex_value_new/10000 else 0 end) as nrjlcbyl_ytj,
max(case when base_index = '14-2' then childindex_value_new/10000 else 0 end) as nrjlcbyl_hbjj_ytj,
max(case when base_index = '14-3' then childindex_value_new/10000 else 0 end) as nrjlcbyl_tszg_ytj,
max(case when base_index = '14-4' then childindex_value_new/10000 else 0 end) as nrjlcbyl_qy_ytj,
max(case when base_index = '14-5' then childindex_value_new/10000 else 0 end) as nrjlcbyl_jtqy_ytj
 from ads.ads_ytj_yyb_childindex a
where oc_date in ('2023-04-30','2023-09-30','${rq}') and base_index in ('14-1','14-2','14-3','14-4','14-5')
group by oc_date,branch_no) b
on cast(a.branch_no as string)= b.branch_no
and a.oc_date = b.oc_date
left join (
select a.ds,a.branch_no,sum(nrjlcbyl_ytj-0.9*nrjlcbyl_hbjj_ytj+nrjbyl_jtcp_ytj-0.9*nrjbyl_jthj_new+nrjlcbyl_qy_ytj+nrjlcbyl_jtqy_ytj) as nrjlcbyl_jskj_ytj_xz
from
(select a.ds,a.branch_no,jtcpnrjbyl_new/10000 as nrjbyl_jtcp_ytj,jthjnrjbyl_new/10000 as nrjbyl_jthj_new from ads.ads_ytj_yyb_jtcpnrjbyl_js_0930 a 
left join ads.ads_ytj_yyb_jthjnrjbyl_js_0930 b
on a.ds=b.ds
and a.branch_no = b.branch_no
where a.ds = '${rq}'
and b.ds = '${rq}') a
left join
(select oc_date,branch_no,max(case when base_index = '14-1' then childindex_value_new/10000 else 0 end) as nrjlcbyl_ytj,
max(case when base_index = '14-2' then childindex_value_new/10000 else 0 end) as nrjlcbyl_hbjj_ytj,
max(case when base_index = '14-3' then childindex_value_new/10000 else 0 end) as nrjlcbyl_tszg_ytj,
max(case when base_index = '14-4' then childindex_value_new/10000 else 0 end) as nrjlcbyl_qy_ytj,
max(case when base_index = '14-5' then childindex_value_new/10000 else 0 end) as nrjlcbyl_jtqy_ytj
 from ads.ads_ytj_yyb_childindex_js_0930 a
where oc_date = '${rq}' and base_index in ('14-1','14-2','14-3','14-4','14-5')
group by oc_date,branch_no) b
on cast(a.branch_no as string)= b.branch_no
group by a.ds,a.branch_no
)c
on a.branch_no = c.branch_no
and a.oc_date = c.ds
) a
left join cdm.dim_branch_simple b
on cast(a.branch_no as string)= b.branch_no
where b.tree_level = '3'
and b.branch_no not in ('2097','2098','2099','8103')
group by b.up_branch_name
) b
left join ads.ads_hfbi_fgslcgmjsbb a
on a.fgs = b.up_branch_name
and a.ds = '${rq}'  
${if(len(fgs)==0,"","and a.fgs in('"+fgs+"')")}
--${if(len(yyb)==0,"","and a.branch_no in('"+yyb+"')")}
 -- ${if(fine_username == 'admin',"","and a.fgsmc_1 in
--  (select branch_name from TMP_a)")}
)
  select * from tmp_px]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='2'
and branch_no <> '2097'
${if(fine_username=='admin',"","and branch_no in (select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[337]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct branch_no from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='3'
and branch_no not in ('2099','2098','9999','8103')
${if(len(fgs)==0,"","and up_branch_name in ('"+fgs+"')")}
${if(fine_username=='admin',"","and branch_no in (select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[2362200,2857500,1219200,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[432000,5753100,5295900,7886700,8229600,7886700,7886700,7886700,7886700,7886700,7886700,6743700,7315200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" cs="3" s="0">
<O>
<![CDATA[营业部理财规模竞赛报表-分公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_dialog]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName>
<![CDATA[/口径/口径_理财规模竞赛报表.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand/>
</C>
<C c="5" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="3">
<O>
<![CDATA[查询日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="3">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="3">
<O>
<![CDATA[第一阶段基期年日均理财保有量-竞赛口径（万元）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="3">
<O>
<![CDATA[第二阶段基期年日均理财保有量-竞赛口径（万元）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="3">
<O>
<![CDATA[目前年日均理财保有量-竞赛口径（万元）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="3">
<O>
<![CDATA[第一阶段增量（万元）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="3">
<O>
<![CDATA[第二阶段增量（万元）]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="3">
<O>
<![CDATA[第一阶段基期年日均理财保有量-竞赛口径（万元）-易调捷后]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="1" s="3">
<O>
<![CDATA[第二阶段基期年日均理财保有量-竞赛口径（万元）-易调捷后]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="1" s="3">
<O>
<![CDATA[目前年日均理财保有量-竞赛口径（万元）-易调捷后]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="1" s="3">
<O>
<![CDATA[第一阶段增量（万元）-易调捷后]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="1" s="3">
<O>
<![CDATA[第二阶段增量（万元）-易调捷后]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="oc_date"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="fgs"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="dyjd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="dejd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="mqnrjlcbyl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="dyjdzl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="dejdzl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="sum(nrjlcbyl_jskj_ytj0430)"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="sum(nrjlcbyl_jskj_ytj0930)"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="sum(nrjlcbyl_jskj_ytj)"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($rq >= '2023-04-30' && $rq <= '2023-09-30',K3 - I3,J3 - I3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="2" s="5">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if($rq > '2023-09-30' && $rq <= '2023-12-31',K3 - J3,null)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="3" cs="2" s="3">
<O>
<![CDATA[合计]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(D3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(E3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(F3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(G3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(H3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(I3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(J3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(K3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(L3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="12" r="3" s="6">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(M3)]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="5">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="8">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="12">
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="129600000" height="129600000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="7"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="fgs"/>
<LabelName name="分公司:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="branch_name" viName="branch_name"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="372" y="14" width="131" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelfgs"/>
<LabelName name="结束日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[分公司:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="292" y="14" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="677" y="25" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="rq"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sql("hfzj","select to_date(max(jyr),'yyyy-mm-dd') from ggzb.txtjyr where  zrr < to_char(sysdate, 'yyyymmdd')",1,1)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="127" y="14" width="128" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelrq"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[查询日期:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="47" y="14" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="rq"/>
<Widget widgetName="fgs"/>
<Widget widgetName="Search"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="960"/>
<NameTagModified>
<TagModified tag="fgs" modified="true"/>
<TagModified tag="rq" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="fgs" tag="分公司:"/>
<NameTag name="rq" tag="日期:"/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="960" height="80"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="120"/>
<Background name="NullBackground"/>
<Border>
<Bottom style="1"/>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6709E3A17B2712D6B23823F2C27AC440">
<IM>
<![CDATA[!CI4,oncL?7h#eD$31&+%7s)Y;?-[sJ,fQLJ-Z>Z!!'o#@ESWN$49?m5u`*!m9L*Y>t#XNpk
Th6-o&H1pkfAe<.%)2S#hING@l#bh6o+\Qe'RfXbf@5&Q0\oO=KdSc[]A%.!J(B2&VV\uUHp2
^YuFrD2740f2R^#-.sCAAF#Q,Ys4M1_AaeH%B',V5cFNNm?acG?c^Fh1s*:2cAkk?4+sJ3T+
sJ3T+sJ3T+sJ5*`n2g'1XaCuZP+L['!=D$]AG>a1b2i^S=$(qZi:fV.Euqi!\2p'4[1E7"#Cj
))E-Yg5okg[)RlXfWmtg[pj&>=]A;VMp@!/BaIJce6'13GhZbXs$Ws"n!<QnkYIn[1$C[X"Zg
R&r@XS2(KM2V.*?S3DJMMYs3deQ'ZcW]A*f@0bVR'HnmP'(P)#,K*1gG35$E-)u3LNjlNJgB2
S!E3+-fc2%>5q7IB:^H1qBl'@aVD*[_p\nc9Z:;e$qLKktA`$kVmM)EU+eJh;pX(P,s)#N.H
-`3DtPoIHjt%MCaaL)POh[?E)uI[E`67]Agl2kRI#B!B?u4_Q;^/h$J?Z[J$E[9ZWQZ8?M=s"
NR/mGRq2(8-"&]A9,lU:(M1JFQZ8";g*`./?SDdOplL;;67VVCW-qT*^sK8]AM]AFKqG?V&fW-q
V`^q`#5&34jX2_kXIPJ7MkgtJu05lc?4:T+H!-(?S88=BAi\WA]A2r.KG0[1BA?F56Le$ePII
b,2(4VtO\1UdD0m5eMhJhNMg/n5O.p%ZN*ikL5@?38R!hf:d_C(A.Jc!t_PRljC._g:tfTjn
a.8Tn_JX,NR1EC&L5;<OX;[D,-J0#!Y]AtTl.qQjg^a8!Ig\66I%)PRC[uRTI%*r0rmjS%;VT
"9hmmYLeWWIf-a7A&>!qZn43QQb]AV3t0.Y5$f"`:'!6"-P&p4Ipni)bui]A_4,SB9q@q94S:>
m"92n+j?6$SX)sg@7>h?f0fmcRdo/8c<7Ghh+mOG*lT;[Z*Yl#lOd=`rl>E<)q[J\B;?FS(p
i=4"CKEAI:j#4/qg[+<EMAIL>?tLm"R52Q-CZVII/nVcA*bHldQgq&gf92g3/+$;C5aF
B!c!Ic67<o;kG3hIL5Xe)lp+AS&l's9RW0W!1:?Kbj[g;`l=TJlK6TmFYL<=\oM:h3qOmM^q
XRb5X#hGmTc+hMlQG(>^rGn6_X=PX8R']AjTZM$%SA7S&4\3U*5_At(k<SKEP;^^ks$%7dq]A5
2fi4Aj;#<ID<VsBp^!jCe>qXI@P=+oY*\]AIbGOU4so&=Ij50*?lp^DE8iJP%r]Ak;0[dp:-\'
VP1"L+!&iohbAJQDmIQYQh_R-XHXn:piKnY2rt*R&ihYq`@-HFX_Z@i-^*/bEh2#5$>Ec6J^
m(k+P&*f+1UL6+:mp_,&&,Bu]AfbCu:Qd]AokXY"e;-okVNj+VP]Apg0A/u1!16f)>Aif^/VD>p
Pqg1p(]Ap797ds:01+s#kC.Vo7mYpIJaunREk;nFp>?e58`C$`,Y:)`nDd2@8!D`D6<<-Frfp
"K]An'>=aa)/6$20paSZDlO?fp6t-0Y43AZ7[q7a)'Yt1@@\d)CMR:'6;+$gjO0]A9V+jg&5m1
81u6SQmul?-m2%EWB)$`7p.k8c>J4jn*F?h:kAADu(C=:d?E/3&-rM3Q1!eA(:G-^[o]AblCK
^!F-lBU?P=(L%;O'MfEW)uk]AZbIBA5iMG@QY/T9I\XS_qJVTZq"0!\+SB#\k>k-?g><Y1?.A
!1^9J@nqA41IWY9;`-KV58>+*@)CoQgk[k.Gi.A%B+6'X0oP-;7C$8[k()%!\[0S$jj>6*TE
.(MVYYp3;QE7)g>h8(V0qLL="j&u<Ga:0VkHh#`L(S-^CRf8gRQJK:QNu9R>W!t5EG9:s$.g
M't18OcngX1)0F%1"?Bk6KU=.pD>>"*>L;?=SaSE;7#?sXk(6/F'Ge&iIRj:&R>I<^-eV4Y\
/#_6-XNU]A`ucK;,7oWsl^gG+N+\G^7+A4%foS'#e9Dm+B8`)(n#*S/LBrAkJWcNE+'eAh9Y^
<K4WW)`>WXn6cfc`Urr/I'#j&8LUK-XQ*1UnMqHlf+kppo+=f&R>A>^nnIX_#OEcFnlF"l`1
D,_B:O@nc7f/g,WM%I9%]A$g".@N$.H\ClYkJ0So5Yn0>@rZ:'9gs&7fkWEa"eLLd(I>&Cc6,
i42^+INALgQ@e51WJtoYefshIZ)eDEZZ)6Ik9JF+ltZ\WaFV=mrk3@R"":V\I30b*DNFr6Tt
aXHeg=X>J_AMD"$ljJg*h=5:QJZZGi'G?,(>=&-qb>X%8@*.moS#i\R->m!'&ugq2:uS8WSO
GjHUr*!-`]A!!(7aFC"\2s%P:<<1:n*%l`pSb&Kc^qc;%$HURV<llJpC674ub.=]ApMK`OgY85
bWX/MY$iU_t>p.,:!*6M"\fa#!ej5_Xo-L!=(>br92UjjQ/&3KWBSBo5N"6huK.74e(0P631
J>#;='<hl\d>Qb9HZHLO6A![+UV$R!dem-:EEVnI\WdK18gIcoD/rIKe0Fnn!R.KPV:rI3(1
*XnDF>XiRPR/rE#Wsa0^[(1ZN**t`5ETj`QlP9JCC&t!r^YH/BLI3(_#"P<iGVknX3Q&1=LI
9)Co:Z/F"X(\'#(ZHlT:uTS:S?YIYTpL0m,AaLW*]AcP`lJ=6iGuV=:KerZKmQ?WEs"\bj4TE
0W;`Zqd1--uG:.Z)DstJjC10RV!;,G:]A?_"\E3o2r1n-2^rt2Z2#6I]A"U_]ArGI.W7#>QfjDI
4_N8"2?S`"E79@oM^5c(T]AnJ?goM%hN]ANbkOh=tdVs=b[6Le?BrR#h`N)(?OcA(U:4Q2W/Nt
*Y8YPiTqp,YuT]Aagu;Kf&<jqXR3Wu;U:WrnV7GZ4^]Aj<`=8##Cp7a_',OI,D+/d'%:3(HG`<
cQ"U?W!-R:pgGkb'7f!aE@Y?S9C84kJEPS9I2fUR13Ec.5=gm6Qa?1a>,dSoG,s?@&MbI7VE
p/r!cU\A1dZW4gI[u25W1cR3f68kQjWU"#!B<M_4o&R6!ik#d&Al4*[jp)cTtL4X1`"/83s@
nm=;6GL8jp%S96rEs"[hb43q!AKj)MdTU0r:,lksqVU.EpgO18H^78B57KOB!;Z+$-6BaM.s
1&<U,cUGS.A5UD'*qGh*.r7RO7Q)AUU5Uh.QX^*92dGr9VC9sT>(U9V4J_,(uE=Cgt35+(#C
QQLaWe$6(;6S-IgR;Ib13>!$&NO\-III"@)(#$SY[ER]Au^ALcP7"D2-72%(%R1b8BXta;ck!
NeiLILkpkCLkpkCLkpkC#Qar+1WVn[%&g;-z8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="96"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.0000]]></Format>
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="5"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="e1413b4a-f907-40b6-a345-7fafcec40cb7"/>
</TemplateIdAttMark>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1678781635425"/>
</TemplateCloudInfoAttrMark>
</WorkBook>
