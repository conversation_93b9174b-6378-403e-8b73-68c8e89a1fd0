<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="用户TOP10" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-09-03]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-09-10]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[/**
日志表:SELECT * FROM ADS_HFBI_FINEREPORT_RECORDLOG
人员权限表: SELECT * FROM ADS_BI_YGXX_S
公司名册: SELECT * FROM BRANCH_SIMPLE

       SELECT
	  BRANCH_NO,SIMPLE_NAME,COUNT(1) A
       FROM BRANCH_SIMPLE 
       GROUP BY BRANCH_NO,SIMPLE_NAME
**/
SELECT
*
FROM (
	SELECT  
		LOGO.NAME,
		SUM(LOGO.CKCS) CKCS
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
	AND (BFWZY LIKE 'HuaFu_Security%' OR BFWZY LIKE 'HuaFu_YDZQS%')
	AND LOGO.NAME NOT IN ('admin','3036','2941')  
	GROUP BY LOGO.NAME  
	ORDER BY CKCS DESC
) M
WHERE ROWNUM<=10]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="访问统计" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-09-03]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-09-10]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,BRANCH_NO 
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)    
	) M 
	WHERE GSMC IS NOT NULL AND SA='1'  
) 
	SELECT  
		${IF(DATEDIF(STRDATE,ENDDATE,"D")<=7,"TO_CHAR(RQ,'yyyy-MM-dd')",IF(AND(DATEDIF(STRDATE,ENDDATE,"D")>7,DATEDIF(STRDATE,ENDDATE,"D")<=90),"'第'||TO_CHAR(RQ,'IW')||'周'","TO_CHAR(RQ,'yyyy-MM')"))} RQ,
		SUM(LOGO.CKCS) CKCS
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO   
	INNER JOIN EMP ON LOGO.USERNAME=TO_CHAR(EMP.EMP_NO)  
	WHERE TO_CHAR(LOGO.RQ,'yyyy-MM-dd') BETWEEN '${STRDATE}' AND '${ENDDATE}'
	AND (BFWZY LIKE 'HuaFu_Security%' OR BFWZY LIKE 'HuaFu_YDZQS%')
	AND LOGO.NAME NOT IN ('admin','3036','2941')   
	${IF(AND(LEN(fgs)>0,LEN(yyb)=0),"AND EMP.BRANCH_NO IN ('"+fgs+"')",IF(LEN(yyb)>0,"AND EMP.BRANCH_NO IN ('"+yyb+"')",""))} 
	GROUP BY ${IF(DATEDIF(STRDATE,ENDDATE,"D")<=7,"TO_CHAR(RQ,'yyyy-MM-dd')",IF(AND(DATEDIF(STRDATE,ENDDATE,"D")>7,DATEDIF(STRDATE,ENDDATE,"D")<=90),"'第'||TO_CHAR(RQ,'IW')||'周'","TO_CHAR(RQ,'yyyy-MM')"))}
	ORDER BY RQ ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="分公司TOP10" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-09-03]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-09-10]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,BRANCH_NO 
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)    
	) M 
	WHERE GSMC IS NOT NULL AND SA='1' AND TREE_LEVEL=2
) 
SELECT * FROM (
	SELECT  
		EMP.GSMC,
		SUM(LOGO.CKCS) CKCS
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	LEFT JOIN EMP ON LOGO.USERNAME=TO_CHAR(EMP.EMP_NO)
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
	AND (BFWZY LIKE 'HuaFu_Security%' OR BFWZY LIKE 'HuaFu_YDZQS%')
	AND LOGO.NAME NOT IN ('admin','2941','3036')  AND EMP.GSMC IS NOT NULL
	GROUP BY EMP.GSMC 
	ORDER BY SUM(LOGO.CKCS) DESC
) M
WHERE GSMC!='华福证券' AND ROWNUM<=10
order by CKCS ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="营业部TOP10" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-09-03]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-09-10]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,BRANCH_NO 
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)    
	) M 
	WHERE GSMC IS NOT NULL AND SA='1' AND TREE_LEVEL=3
) 
SELECT * FROM (
	SELECT  
		EMP.GSMC,
		SUM(LOGO.CKCS) CKCS
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	LEFT JOIN EMP ON LOGO.USERNAME=TO_CHAR(EMP.EMP_NO)
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
	AND (BFWZY LIKE 'HuaFu_Security%' OR BFWZY LIKE 'HuaFu_YDZQS%')
	AND LOGO.NAME NOT IN ('admin','2941','3036') AND EMP.GSMC IS NOT NULL
	GROUP BY EMP.GSMC 
	ORDER BY SUM(LOGO.CKCS) DESC
) M
WHERE ROWNUM<=10  
order by CKCS ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="Branch_Fgs" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-12-07]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-12-14]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,BRANCH_NO
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)    
	) M 
	WHERE GSMC IS NOT NULL AND SA='1' AND TREE_LEVEL='2'
) 
	SELECT DISTINCT 
	EMP.BRANCH_NO,EMP.GSMC
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	INNER JOIN EMP ON TO_CHAR(LOGO.USERNAME)=TO_CHAR(EMP.EMP_NO)
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
     AND BFWZY LIKE 'HuaFu_YDZQS%' OR BFWZY LIKE 'HuaFu_Security%'
	AND LOGO.NAME NOT IN ('admin','3036','2941')   
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="Branch_Yyb" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-10-01]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-10-06]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,TO_CHAR(BRANCH_NO) BRANCH_NO
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)     
	) M 
	WHERE GSMC IS NOT NULL AND SA='1' AND TREE_LEVEL='3'
) 
	SELECT DISTINCT 
	EMP.BRANCH_NO,EMP.GSMC 
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	INNER JOIN EMP ON LOGO.USERNAME=TO_CHAR(EMP.EMP_NO)
	LEFT JOIN BRANCH_SIMPLE BS ON BS.BRANCH_NO=EMP.BRANCH_NO
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
     AND BFWZY LIKE 'HuaFu_YDZQS%' OR BFWZY LIKE 'HuaFu_Security%'
	AND LOGO.NAME NOT IN ('admin','2941','3036') 
     ${if(len(fgs)==0,"","AND BS.UP_BRANCH_NO IN ('"+fgs+"')")}
 
 
 ]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="总访问量" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="STRDATE"/>
<O>
<![CDATA[2023-09-24]]></O>
</Parameter>
<Parameter>
<Attributes name="ENDDATE"/>
<O>
<![CDATA[2023-09-24]]></O>
</Parameter>
<Parameter>
<Attributes name="fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[WITH EMP AS (
	SELECT
	M.EMP_NO,M.GSMC,TREE_LEVEL,BRANCH_NO 
	FROM (
		SELECT  
			T1.EMP_NO,
			T2.TREE_LEVEL,
			T1.BRANCH_NO,
			CASE WHEN T2.TREE_LEVEL = '1' THEN '华福证券' 
			WHEN T2.TREE_LEVEL = '2' THEN BRANCH_NAME
			WHEN T2.TREE_LEVEL = '3' THEN SIMPLE_NAME
			END GSMC,
			ROW_NUMBER()OVER(PARTITION BY EMP_NO ORDER BY TREE_LEVEL) SA
		FROM ADS_BI_YGXX_S T1  
		LEFT JOIN BRANCH_SIMPLE T2 ON TO_CHAR(T1.BRANCH_NO)=TO_CHAR(T2.BRANCH_NO)    
	) M 
	WHERE GSMC IS NOT NULL AND SA='1'  
) 
SELECT * FROM (
	SELECT  
		EMP.GSMC,
		LOGO.USERNAME,
		SUM(LOGO.CKCS) CKCS
	FROM ADS_HFBI_FINEREPORT_RECORDLOG LOGO  
	INNER JOIN EMP ON LOGO.USERNAME=TO_CHAR(EMP.EMP_NO) 
	WHERE TO_CHAR(LOGO.RQ,'YYYY-MM-DD') BETWEEN '${STRDATE}' AND '${ENDDATE}'
	AND (BFWZY LIKE 'HuaFu_Security%' OR BFWZY LIKE 'HuaFu_YDZQS%')
	AND LOGO.NAME NOT IN ('admin','2941','3036')  AND EMP.GSMC IS NOT NULL
	${IF(AND(LEN(fgs)>0,LEN(yyb)=0),"AND EMP.BRANCH_NO IN ('"+fgs+"')",IF(LEN(yyb)>0,"AND EMP.BRANCH_NO IN ('"+yyb+"')",""))} 
	GROUP BY LOGO.USERNAME,EMP.GSMC 
	ORDER BY SUM(LOGO.CKCS) DESC
) M 
order by CKCS ASC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_collect" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[财富业务]]></O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O>
<![CDATA[3036]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM DIM_FILL_ZQFXS_COLLECT
WHERE PAGENAME='${pagename}' 
AND "USER"='${user}']]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters>
<Parameter>
<Attributes name="pagename"/>
<O>
<![CDATA[用户行为分析]]></O>
</Parameter>
</Parameters>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<CustomTitleName name="移动战情室"/>
<MobileBookMark useBookMark="false" bookMarkName="form" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[window.w = window.innerWidth; 
window.url = location.href;  ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="0">
<Margin top="0" left="0" bottom="0" right="0"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.cardlayout.WCardMainBorderLayout">
<WidgetName name="tablayout0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="tablayout0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="1" borderRadius="0" type="1" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<NorthAttr size="36"/>
<North class="com.fr.form.ui.container.cardlayout.WCardTitleLayout">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<EastAttr size="25"/>
<East class="com.fr.form.ui.CardAddButton">
<WidgetName name="Add"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<AddTagAttr layoutName="cardlayout0"/>
</East>
<Center class="com.fr.form.ui.container.cardlayout.WCardTagLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('TABPANE0').style.marginTop = '-10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="tabpane0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<appFormTabPadding class="com.fr.base.iofile.attr.FormTabPaddingAttrMark">
<appFormTabPadding interval="0">
<Margin top="0" left="10" bottom="0" right="10"/>
</appFormTabPadding>
</appFormTabPadding>
<LCAttr vgap="0" hgap="1" compInterval="0"/>
<Widget class="com.fr.form.ui.CardSwitchButton">
<WidgetName name="ab3f2ee9-08be-4b97-bdea-65f8db11185d"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[标题0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<SwitchTagAttr layoutName="cardlayout0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<DisplayPosition type="0"/>
<FLAttr alignment="0"/>
<ColumnWidth defaultValue="80">
<![CDATA[80,80,80,80,80,80,80,80,80,80,80]]></ColumnWidth>
<FRFont name="SimSun" style="0" size="72"/>
<TextDirection type="0"/>
<TemplateStyle class="com.fr.general.cardtag.DefaultTemplateStyle"/>
<TitleFitConfig titleFitByFont="true"/>
<MobileTemplateStyle class="com.fr.general.cardtag.mobile.DefaultMobileTemplateStyle">
<initialColor>
<color>
<FineColor color="-13072146" hor="-1" ver="-1"/>
</color>
</initialColor>
<TabCommonConfig showTitle="false" showDotIndicator="false" canSlide="false" dotIndicatorPositionType="0">
<indicatorInitialColor>
<FineColor color="-1381654" hor="-1" ver="-1"/>
</indicatorInitialColor>
<indicatorSelectColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</indicatorSelectColor>
</TabCommonConfig>
<tabFontConfig>
<FRFont name="宋体" style="0" size="112"/>
<selectFontColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</selectFontColor>
</tabFontConfig>
<custom custom="false"/>
</MobileTemplateStyle>
</Center>
<CardTitleLayout layoutName="cardlayout0"/>
</North>
<Center class="com.fr.form.ui.container.WCardLayout">
<WidgetName name="cardlayout0"/>
<WidgetID widgetID="fef3d4cb-99eb-4750-baf1-1edb58b801bf"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="1" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13400848" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="1.0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.cardlayout.WTabFitLayout">
<WidgetName name="Tab00"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj3=document.getElementById('SM04');
kj3.style.marginTop='15px';
kj3.style.borderRadius = '12px 12px 0px 0px'; 
kj3.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="SM04"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="SM04"/>
<WidgetID widgetID="25aede64-a99f-4fd6-a92c-d40fe5b866bd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="SM03_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[用户量访问量Top10]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?RKKP@kZO?`Fjfate-8TTB;md8<CG;;$f5/6XVb[0k&A^aPj,O[_\D,Z,C8(6__"lk$3"W>
1%&'kKsQ6/oru*E8=i!f82@(pZKL4tiH%`L1f00?=[m&'oSdc[G6P^%Yho&0JB-jVKEJ!*Hp
Y:\;*rYcML-F'8VDa_KDj!<@FdDfjKQ\2qSs$Mj+HM5O8aNRtN9H7IL0OG$BT0`LS(]A]A2`L.
i01@2LOd0"]A*n6qod]A'2ue9,C#dU:RIf<alZ#&94fe*f;q7J!$i+M^-cte>W!]A-OdWmc7j&'
jZSXi23V6To#Es1e\CUQ8ErK_(GX@Rf)1V/"oqn=YoT5%NP!Plq4i^]AjKe<6+&!'p.%1?Hqq
T,#/P2p5D-Y.0)ohlH;#[=3IZ#65`Me'f6Vla@4HHE(MOlFG@u!(ct+c"i;Oe7Sc#-eG"E5E
>#9%pq@X+%,l#OWAtZSP_EEO8kR2cJ'*6Ys%7*,;.`"c]AG1p"G94Z;2l4Z:OZRqhRuRW7bWg
=86o,3%]AWtShEbIRC$`A0MMl6q<B:+=1+kVGU^]Ab<mBWVC-DA\apG[A^(V[Z/aOj02=QY>O8
/=2uWNpOQ"6`]AAVC$<'5-M,1F%ZuO9HS9j`Al7YD*jsgf07uT2b).i+C@/R(4OGd*-Q;nhH#
.&GA"YCfHHVlk/-a,&`tO%Kc2Kdb@YY>)SX)]A%j`>B]A!V.Q]A(f+T5OMDY*C<C?INU^00Ej+F
*23QBJt^hZoV!dTYRlD"%6\Es7CV?"%'*,4Z?VKPRoBr9)on,"QW.U$rHJNW6R*oM.=N_T'$
[Te2jsu1^$s61?JPP*]AiREq+Oiu73/-a`-*&;=#+]AGW=nNc_)`;h9!LclEE[0aO%BYI9BA[B
uY[Z.$djDL?WiQgaoEN8N.38t;o#i6ADS`dfQ.3%k.dZAU9%ggl#n>+=flf_RloH$l#uXqf6
uA/4Ck\SRAsUBQp83PR1Bf^;L@I`b2d#lul70c8mcr#N&B+(ir]Aq9=Y=Z&;H7_!B;K[e5)]AN
DXlC*4T;D1jP#hg@WI_*-EFPc,SV#O#2!aep<d"euC]A[$o/Os&uuBGcA>1J=Ql/t?H9*7gl9
j:t65pn]AU:=k?qN$f/p9n!nW>m('_q@"N#%`Hk@Q6a]A%>'k7^H<S(b;X650^Rk,@]A`:p.G%F
=&[-oc,8r6Wq^GbbmVCq[DuCUFmN'k^DTC5G;7N6"r/]A3Q$-'?/[V+d51;Q<g8Th\DX1i;9_
Ag/S79.5<\2cPk70R4&s/Fk/TK[U==l"YRC;<U;TUJ&V8^74cP!1Fhn!d\R`@f@_`kfq8m&8
R_38-n,.+,f&q+5AM#%2mZ.s+$=4.WF=f4Vt0.Wje9F>^OdD3G4I`"RFnJk,sg40/JNQ?ZD'
[k]A1_[M00rI@]AX'a^?EY"1GSa15qa>aBqKh/Z<%*^%WbC]A\@5[nEgG2isH`@CH9,j6f[T1@5
HilYMX*&X6KPX#8Uil0%$QItUqI-^@6M=+H$:PQm'_t5s$_Kt(*C_dcdt2-Q'Is'<l>h\,@W
mq-i74hQhI7jnr9[>m3Iq_jfSVej-s(uBaEAG\Gtg<W*>"D(Tng0^jB=[V9RF4[r8:A4hV3J
<@"_NSA&Q1iB8d$9.8nPGB4>b_J+P,0p'm]Ae-GHRH$`bRgY0m,\ctlM%r+IGRlq4BnN8Md$n
"GB:1O-i`(?QpV5Z!jGWHu9=4Wq@YJOVDQ]AdFiZ`Ms/ND[m%I,Lqo7ZW=XNqYg#ng,ps)gin
Eu3CSE\._Ho\>'$chX'*cR=oKe9e;"9u+KXMt!r#@Iq8Y)kj"U8t$?`0/6_L`QBGSL>0?+J[
ET:\H"hLSqCb0+!6\S;-q/_E&7)*[n.W1bkGb<%`,tJ+%eS*[ce=P?n(cN@4EA/O;'SY"f3t
q:q@=+>KkN*pp4X`gPOJ&dIkq?meW.H8.`%G+!MNe\g1#/(*F^8-V"_[3HF#;KQp=##))%s[
Y_>=Ni5<"cn`LXXF\_"&B$D6CPh;ISD22@6]AI*IS5W"W_Bk>$?8c"`C_]AZeW!4sooJAi?h,E
Qlo2R*QS,5OlHADME1^0R;N[_A<,/P\GYgiT]A`j,H(WF3eZT?Ej%p>l$p64XV(R2<55$,n!Y
;lE;rgfbC?5W*S6@FBEn:p;I%W(D&Km%[%KX00dnm)iD:GZ-k-hjQf;2<nh"Sr'l;OF5$G6n
O8+Z]AES_+DXE,sKE4/as8^dHe4F,rH_#BCA,LTc*U#j+<bech=h0nmK6`AKF5KT-XI0ZX:\r
Ct3M.sCc>(B7'X?Tr2eFl<9P>RA]Adh!X!iULU6g5oI$*,3;pa+DrYMX2,61@sfO`J6GEo0it
-\u\HU<F@\un.@K.-!re(0K,AcI?('PoBuoa;#nTO"%h1^E>T]AVT5*:(3=7MP<3;Y-R>J/UL
j0C\^[n3q#35&4Y[SE(@Wcbq!:7:;W$k_$eX.7lgQ6_$/[+`NPRmr7D6YA<0I%WcJSse[Qs'
SDbmqN7-bXFS_0l^%J125Y.+@4<]AKqeu!`i7mIcUJY`p$98&RH5S(BF67k>@9-'6*E<q(:kF
\@T0^J\?tGp"[-tKt7(13$SHr^#0(pIU!-S"ml$eBEqZ&CrbqQ*Ok'PglC-/4)W++\/i'=G2
/,4EBA'YmC41Gj*(A#i'p-&W3"4%@4tpep;Y$@1VZU6q)8~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="64"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="335" width="375" height="64"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DATE04');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATE04"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATE04"/>
<WidgetID widgetID="71d72de9-19dc-4b7d-bf01-a403492f02b7"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATE03_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[9791700,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[13716000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="3"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="true"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="6" align="9" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${VALUE}"/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
</AttrLabel>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange maxValue="=MAX(营业部TOP10.SELECT(CKCS))*1.25"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="20.0" categoryIntervalPercent="20.0" fixedWidth="true" columnWidth="15" filledWithImage="false" isBar="true"/>
</Plot>
<ChartDefinition>
<MoreNameCDDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[营业部TOP10]]></Name>
</TableData>
<CategoryName value="GSMC"/>
<ChartSummaryColumn name="CKCS" function="com.fr.data.util.function.SumFunction" customName="访问量"/>
</MoreNameCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="41d04f6f-75db-4319-a2c4-dd18c64cee72"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m94j!e,0/HST'`[Tp+KSO\V%fNT2pf,cBZj2n.RG68=d:/i1[Bah93i=SIa>O=c2YElhl<2M
NNEL!;=+Cbts:[NWA_n+Uh9^W5f(h]AI/,HFuJGceb;gqt59MDU6r#/`s9sQ2HO4/#8u:j:D7
(s/;13Ej4cBX>KFDs6Q?_UnfQR%dsAaGfY;:(Gt!V+7UdFrtMlhi9+1qMGMtK,_Y5!)./j&U
6U-tMuH5`.i^D+J6a<TUT86eLDnstK:npRB#W7!&q2mP9Q@JAFpntk1?td0:9KMo*,Su@fmU
n'dQX=Aq[]Ao5T@A!brN8,^,$Qiq47='kRjKjVe!Vj:RQHWQ-knOEk8mTplBHe0p;UAY<4L@F
V(dXC=rH7=2-)dqMU)2qC)!bK$d_[uMd!jVB13Qnn\^D&95iXLPli)RXp-QTj,^/#;Gn<T?U
[V%CKE'PP1mNs^Z39P9&M!n;Er<>lL$ePK*J2Z]A:p<C/$hZWGh:_cP/g;%ZLFhbMNDV3jDf?
S(F*2E4O4O$EVa1gD5X.Q+3,-rU-ZtQM9Is($B/"SnkH%V3k()(I^RH6J)@'VO\iEWFFL,Fp
W6/\AT,PUI3IJif@,/G3O?_*0alDY>$E1,/jFK+ZI=,8i:2>.8"qs3rkVlCkG<?Bep5#W-6F
=SnQ@e(LB,!D]A1Z#__37g#-t'lL?6V\l'oP(dJCH5:HrP,;RmprIVqVS`U'j(e..=i7I5RlH
qO\sk9HD<]AeBu0,.!M_EWFMc2$qscZ3X&Nc0JHO;Wh*8I*,95M<))1'[]A0,6:;3AO!c!iE3R
XtJ'psYZbq*#QW2t&Z%[d@"OW:%$U4&7]AR/DZ_=$&XI'4nBoa[q]AIpT[bm0k$DB!=E%34jA=
SQ^_QHU^_RCI)AIaV00>$CnmcD?`duZ#'$tqZZK0bm#<b.J.dkg0^u(^6soHY0@"fN7#9OGh
=N-SAHZXN`sCgGGaWj:Fo5h@B<@XQ@aUX+86PN1;s+4oS*kBnUNjq-=%gR8h(%7V4K[%b#R%
5K5AMGI"<n3N1VU#SpJ[RY\7g)!<#!3-3ptb4Z*CP:*S_/'W3HQ^TQKJ"<ji%`ah?R#V?O+U
/DMNR[qT?jTtS1t0rt.8BB=>/-Y"M4.Ak]A,[4tR<.[4ri/?rFn17]AK+C.sDOQLZ,q#lBe^h.
miGA9dM5@02<&`ZG.neuiRW$0]A^uPc:,U\[,(?$&Y?TLNtD$7<TL68-,#R\V_`\8nn*u3U%m
"dLa*,mC"D_2^QKddGPtf0cb,tU!`]AVi$/XgpG]AkgJ.I.E1b6?S(Knl=]A]Akd=mg,l^kuY46!
[_?O8MO#-F[[E)[^WMj!%+:7#m-W8LMh/"bF;g&5<S@VcnfZWUXBQ<CV\L3I)%tI]AWAm<IY6
I)mFcD[<A%RM/+8K6U=[E0I%iX]AO<gY=-QHVJ[hd3^OT6np<aAH=kl+%YS0B[M(".VlBa\#6
Crf[o]A<CARJB_j==*&&jmV%S<9K*0D?)Q_dRAXRtk-(8hb!mo0F[%i6"MlM#mV=Ht5B;KWgl
pn.D=]A38;o<rd,)-!=;M-nfa"JZNdJ.4*2Gnq'i8I/W(P]At724[dnF3c@nRP^<K[M.62H`1i
_1>%R(:2"jH@*_U0:&15XXGZaB5%F!MQ,\!9mD^jW7S,Vm>`F[pk6]A3-UdRpJ%r0-nFicX.4
;[,5NZBCf?q3n[7uDWji8&$=nG]AWE-bGfoF0O]A1\;1OjV"GY:GZ_k]AjBP^qAHFo1>K`n:H_J
jlN0(]A@Z)_Q<"rLL[Dubl?FI=TM$R;q`HN`Eo!e7l36Q_/:/KW'o16=)2>%Dd_/rWf<NU0UM
S&?AH$:]AJ!%qTEr:"R,jZ9T#/+Ah#F6QSi0KY?\rA)#)e9\$F$i<A$W;qJa(9+j#e*Osn==h
30c&k^cqU:9q4H?+jcb>R*kY>b\'j\Qju$1VW`A6_pse=LM4d\*n%((>X4WMK8;O-::(8C+!
>[GoM=n=W"HD8U-<'`&_iT<iEAV@$@8SGu>_pXW&qj#_]A,eE:_5QI0n.#[jMU,kVh'dY?^^W
e6#'b01<YYe]Ad).[19<VgYcCXp2YK+ot!+Kt>FUqtWd",)i.u_X8e.8JrnM\c#0L$9R+Lpn\
6(#7`hPh9)CfW7@9E)P^\2R(GHA6dJl$GO1&t=Z(hh[aK67Sdkg*:AQ`m%s"]A7&pD!*W>H`^
e+)A]A!e?U`Y^Y.Y<#Qc@roli9h4c\K&0dMki+<lm]A'8t9rNYRnP0J_N62@;RQaEN3[7+5+-D
#'W$RX<C.K*aBJhLr"2@\itSh$t3dKcS9_f%8@+?TorW?JCSj;a]Ah_V@>]A2%1$5j_F@DFUji
L4"(rp6s]ADj!eQ8_oh5RW0r(TqQ\)Oi\o2,B)X:U7Kq5kQ#GKq;l"X3S5]AXaW>b^<0_!XY'q
!SUKqLXdK9N'ZdGbseC]AN9AAO.C=5:oQU#:#qt5'!:#7Ln\fgA5?o*>4<6t9QSL@X-q3,BGg
V#]A5:dABrn>-kd]AK/G<a@dlV!%(:B"unKf@D!07C"h9#%Glo$1rUm(g<%gLW8OCp(#BZ(gba
h2Pdo"M$0<ZWV<>Q;%%7P5TYlY`QSDD)PA8c"=>$-i0kj($\OJqEtjR1kEodS%-:*JV/iG;>
Kd;3D*qE2MFma/k'_Q_kAbR,IN:e.TjuC',o;3C)Wt@\@p":3]AOSGhA4i+`.!Ya?Olkn0cd8
Egf!-"*u-NDP?UJD,tlel)lW$H^IAo?*Z0f&V's2RJ)ah5-3P.WUa@cl;=[(p--s.rf(kaok
/Jj$]Aq+1m%hS-I1*)Dk^D4dm^j)]AA`(6IgWI\Xo>qfrjE&uSZ$QWbc6,<XN@D'm@1`DGhRbt
YP(BeJmp.Hg&/n&h]A;#Kj'$3D\!b'>IUJdsU042]Ae>b]A0'#npTXbODSUT]AtVX#DC\6o'0A(S
6eG3W!?s>=S2L6g&k4,[rPo7fSED"/2=1PTa&'`foAFe_i`-;cNJ_*d]Ap7GqQXT2IV^k!)3q
K91\Ee&Uf41\U[KV#gLVam(;.2Z<nSN/g6hoj@2meXDZ309ON4iaD=J@AHF4jK&aO"lfP?[,
M%G-2QX8*A+CD$pJ0F'q?Bh>nhX]A!aINU?AJU;g?O1O&,b[TfFG2a=ccj9P_NDiS:rOfb#bQ
mH=,GN%#bC^GVSD?]A6l>`EocXh<\=k@s^==JgoM/jgr\eFiRI@f)`Sab?DS^PAKqPE:-sIEj
X7hE!h**V:PjR9TcWWGXo%:hX$k).Ej7--q@)le4b*iad_Y^t/<8;Iu/djLqZ17s,V;VQ6?8
In.82mQ[7)r_J^P66;J3Gr^dn&rNL!+l_ZQZrOE4$0##""$nj+)-T]A$S8bGF>"W4M'3=eT_r
\PNV;]A7QA]A/Ik6"if").(J@nUZtJVrm*\`DT9R9tbS^*2sepN.E+V`;BP7,mjO+'e/ZoVHAO
t#=ugb?#IL5GL)-pp*)B[W3j22NIU.&CUL_@1`u't63&T]AKt8Et8.B\aioD?uT&STn0?gd8Z
Tba"@dFDoMFNP!M2%K$nsKtoJE>rN%YW4_D\`]Aq1h6*G2hL)4f_a7DpM\.pgWadg7e[GP6[s
d<M\+H14KGDo,l$LaN!f%k'\+<3V)),r3W8o$I^d_sLc)r?S(B1!UVkM97bYMI`,3+[Kl['-
duEc$8g<ctMO:FPi1hW0V]AqV+B8ttJ(`)bLpbN9p$n-sc1$F_\%n+iLVi9$E:o7"YZl!']A`h
lqd/?1;=$m%Ho::pLqnbRVpgc[+"=<F,)p3"_bG\F8pg$]AL'_U4F_!\-""W-e(8HT[-0Gcu2
*SLHlI-(Lt.g0AeKKcZJ(Ca#$:0W96ie\GgL>k7F\#T7#*<n:e_hM%0VLJH_F[PP2\M?uV*C
=.Bo)o4MPb.+AL(NAs"-.b)(gS.j`G++r(i'tQYpYE/[p=Q19e/ud*WJ)_`T;rf(bnMG4Hsg
Hl8oH^9Bn:ui??bZkO-UY#rNr9B/1u6V1sIF2GSPq9atO)/=[bWi<>ZRTd8`thgMCD8&7+\:
UUa0bFRWh3PqKhtAG+!KE']Aa1eeK.o,q^?45(Od;JK]A(3eg#<k-^)j"*g9H6&IfaD*PusaA/
>&77tT+BPOcq'5>4!m8oQap)13tI*CMJKHml`lF!*!78>b!>q0.NVl3_nHJAIN_ACij<S)T3
5Y0_VdQjAoB!P'B<)=iOP\a)/<]AF#E2C$^k)%g-N44LH:p*8*rR3TugO1N_H:mmk0)3^?@/e
*MIWOhRa23ROLT,kGK]Ah:ohK5cEu!mcbdM=uV1.M_2Sp\kcre!n$i7ISrIRIS5:'&6Y`kH7$
;+$iYg\UV:p#9KZS'eOE_S@s!FD4ssQl3rpbb_BKe]A?-fR+_[TnSj1Y<4bH$KH%^mj;fLq54
Vlu/(!Aa_1')]A)IG.3OVmR"i>S$e%0pZ0BW<;nVmGf$tu9NM%+DIZ[hgIhJiGRia=Fc%<%45
D?X.\+j7'g22L;\u:i`WK)latq3(PpHN1%A(Xq"UH<FPbqJh)M5rkp.LJ$^1r$Z7W:(@T'ko
hQn;fWZjO&3ZpQfUq"u-1LlhoFnr7?uT"!m6XUAp\b[I"Id'm(VO%\HOH'*bL[TVh)C]AMTE2
T6*R@>%kcT]A6&J\L3ulC^44#55"^s+XVp1>tRnJpU&K4nCdnr()sO-i:a(5H#'VN\Du=?L2^
%7-RSWs_1K=#21JMtccOYN!'#%E(kA,m!3>R5*CPnGGncjkp'qDlZkln*.ra3i;Eeig(ru1T
6jIG01K'S-gN>#fUS<=/?)m#A62jBuW=2g#H_!g3(/B+D+mo+u[4813IHE#Xh!^e6J.UWYbY
)dp.fq<]A^JAWh8FM-@5#:VqRYiCH)Ua*Er5HUWcRemQ;<roBacc9bM[!Tpi\>Md>6B@SaRrU
'KU/4=K#*p:Na(E)cgSH$!mBULoX0V]AU%7^#)8K+bm'r7I\tA&)J94jtg+qO/qT>R$oKN>Ia
lRpm%i3q]Ar9l,de=]AGpO47Q'`A,;Z#+VC9"h?OG985*ETJ&[`[1.";`J!H`_(Oj&(i1&'YU8
8IE:`S`K)EfR;j_>0%E@eBXHl4J/,\G^5"cH$3_.E2lGkOXB9Ze$aAq.08+re(r1Z%NJ=$XV
<J\;TSVuh+@TY)<iZEKMU'V6PhRfa9hsd^"Y#A\aWas6\dZfbZ=0p`&#@Z3lcWorQ-Z>t)8s
3^5Y67>RkoM:%e1QI5n-K?MD&)c"r[!$n*n<M(U2omFQj=\4BL,>s2u>W,.nC67"P3t5<"r;
]AKO:LZV]A-PUNh9C!OjiK9JAdq([u?!@JB;RP8%dYMSE(jWKVG/k\1C>[Hm+4iaf5<PR]A![ff
rZJ,)/&t\7<2cE)-r.I!g*fT<htF"r=Wj_'X[HpbNE;"j['B$abo^c;fXelihhWi&QQ)M+5S
u@Yc5cQn'/tT>[B,?7/eBQC@+]AqjIZLZs/KXu2gn'.@d2(GR</bJelMbWi(bok;pG`V0K[RV
g:SHEZ!QDJ]A#VOQrYi^R0E7V0O6e71rnbfQDKYa/h8tA=oimDJO]A8SAqmYIFHgp)\3SiuY.'
n;)a5G_G]AKrLZY!D"XOaNA?]AtZ#35*7dY]AN@Z!q$V*#A9W-p8SYWSW8Ok8VFW5td5*P7.MIg
tlAY;VX@-:IdsN,ZrHuQ_g!UA]A/U"&C"<_1UFi/dW7-q1q"d&8U($TVUjZ:HXC:=/M!ea&RW
\O[KN9,=%lLS;-\]Ah&@Ic:N[Gj_#GT&n`Y$\pFC;)p&,KI$JC4F%"IfIW?SC@_m)A?DlAqWU
jfF3,QW_$L(?F4F=46J&0Ci<VfUZM2@sNA16?'LLH8_bYGu`cpr6>r]A?H&i9DVClFu$0s&Z(
6B%jMR^=+a(X7emmIqF85im64_+ShVO6;R5fI8K;+kl6_@)`_?`b,`T@aTsONE@`8s%@UN<I
.g:HVXj_/8V8Dp.k8YN-2]AWmM5Vj6Kp`Y%^$j>4U6V!B:<glq(B5R$[5B5hf;iG*j*p)[>24
&`aBYg$QH!@>.".K&!?Gnrs6!GQ1!V0qlRG&0Ck`^fRhPkno)fcS;>`?Zbo:Yh*G5_XS(njR
R_tdHJlB@>('?Z,7(R'drdDMN*e=*A>URefOTR'd;m'DZbtmkQA=KQ7^(b'/>;9UJN%]AJ;1&
9lF"^Ts&b>"WQ(7B$Ef]A<oDWUbd--^SZ=NmVPIZk^7T[&UcXhd*LlX9g1HABd4UfM3ZiS1_Q
j^6Zs?>r,i:-HiIFC:@9?k@Y@;;!9aqG#K79RsdW7=/Bb#S.QEG8NUe8nQfL$A.+Map_XV$8
0"De&H]A-7Xgj9&JT6fK@2Zj#1RZY*Gc@C5B]AS<Pi.2Gh8^O<cse's!XCgJG)(=2A@ATQ[ae'
3-m8)%OVdf)[LqU5+GHmp_-=Q\M'&EsfiQ05J6tRg-=r![E[Y`A;CH8O;apmqo]A8s6Y<Butp
<LcNf0/?(Y5W(NM1[q%$`/U->?\%m;r_2'roC!6@7*8.>\&JD>6[t2ljCt@';Q['D"GCH&XW
QIJ/M6q=-f<.mcd1F:okj_'FIt?8%u&BOKhRndiA8INAV<r)=H>H+`>@63@g:1e*k/93?gOD
X2'At[R"*o*8lYq)>^rqCi%O)RQ.d;[!I/f)G`<@D2CH1EPibUs%:QB?%\_?+m9k'Kqo=8?V
M>n]Ajhu&if)Aqoj#VOah*nn#6Uam$]A]A4%>K*FWZ]Ae&(>:lf`=$Z(ifB"bKODbfGeMQQWdG>D
WJ5"%S^48P&?%L7p7PU00+O504<<CDkU7pdfqd!4qG()asea54E5;NB'5_sf@MU29J:N9o<7
8>pF^TMdJf1\-L1<5']AQ,hS$a0-I*7f+>?&I9!LS9:X4,Gmtm=n\775^j7SG%S7^qZQj%YEt
*u#;Z?j83J'F?rkDfG;fgr;u7\j3-;r07;H/:iO?j-F\:VfV##D\9UQ"8H2sTr\X%i8.0NDV
..+?Oq42(mnQ_a&Zq)LN`lrkc\?,gU'0)Us.UXT93&?pBhE0Ad;*(P+eJ7M1&<aW,6*A;ngE
m*7VDuj+>I213Y&Z-@eLU\cc5AK.7gmZA+=D?k$k%&'\?V[ec$"_"Q1`drNO>`_C*Inn%JU;
s]AmkZd=Metk?dk\s"Q,<+bFVElQOO/-8taVK[.uXTEf)QS;hKG%KMCoJHJJD:Q)E-/ft/i$Y
K=n3l-3^!%7g0kC]A)OCr:a+]ACBML=A67&fs5Ln2LG13sGO=95'Wcg3(H*T#WT8Qd[a20g.jh
H"eERTO%SqDN1c><iiIr%!qK&N)<S8_8&%au2IYXO3qS3?Bn9o<?gApf>$1<3\27Hes%p#BW
k1qs,m06EWb`;[,Hp-h&QOYI<XETO`i34MM?L0kK!HsJh5LGuM^V)b<.q4_Wg%B8s'1E]A(m6
GuB%)D(T8%r<9h2Fe_IjG8I(A/bh?gW(E.UH?@CQj9j>*B`>c_m?gm.[cBdT2b@k723,Ub=8
FPi<I!A;f5S![?p"r-)H7P%nu7pO?X7&*X(+!LZc;@5u".\:4U1E8?pk8pd/&#!?%r5@uC(q
)=NlQ%c4L8c:Ua]Ak`V8bk@*=.p:0,W!0jCbC>9<k#pSG$!<#]ApaladK@^%@.-Q;dp8rh`>RO
aXQ#l]A<G2HtADX*=K]A4O0X\`j!t;FeDhb=gL[r(iBLDaLs.`Nj$[q*al0?6!C*/MH2[CXd@U
Fe8'pbp*><EMAIL>^R-P"&Kl&M4-X,G!Jch6`qcU!$R`614^#q>[5iIM*WC'?h]A2"SP
&?(jUd9m+,5N5W^Ch'g>n*kjHCEhE7*d+T(-W(F)+4)dkUjN"D@``$C<A;HEXt"7]AVFZJ#;a
^>F,Y)XR\$jWa;doFV,J=6@cWNM`f!-+5!MVJ<d*g9t/'0N(akpoS7^pcZ+d<Q7J0_@0I7Q0
re-TDU@m(.Wp94Z9u;s(m%@.d1&&[S=@ZrRpJrdS(4r&Qk.D]A08c,RVdf04"B@cF+ChOg7a&
M/deW4-6U5e_^`n^)b1qZJTtN)3W64loOD9ajh,&nX2@&A4/'-GC%Y)aG>=Z3Z52?cO5?ji$
U.&b6m7=*\*L,j7'S_s9X'ZlB33nrX1iCL]AFu135bmo-!`#l9t,u'@6?dF'm:GdR`1mAW$eZ
=@njGt:d7]A;^gabH#I=/oYBmks*,0"&P##O(KFBDO0)"?7ZsUEA7cFh?!J0<F.W,:!(!KKk,
Velt,b#I7CL7h"+[q7V7.jcW\J<idis/)2bqjpH6,h=p:!4nPTASLShe5N_@3%a0.:kNMC4W
YeV1+eYn2NUR^[/-l$jSF*d,onC(Hms&-Qm%aHlXh<sdN9K-(TL%7`2GER#p[1saRaA:`Bt[
rh:M8/-.]A;fW8m$LJqDS2W@.%-[.jqErK.JSC,lr,peP*9I;-(jGk20$]A"D$11*k,S[E^YFq
l6,2PpCLt>q#/\i;WCJK95f^D"NQD,]AK9S:?LZ(&U$uQ[No"D5k3m9!!%=Q$r.CYQ5RC!.FK
k%*lT`?2obZBk9h:;C;'sGe/\$VNUgf"[>TSu]A/[A5Dira>UCn5Y$d9JR_O?:<U1S@DGGCcc
1qM6%/7=DFfDtJVj60c.pV$IJ'1E?'I^L[:Z[7SXiBB[#=8).Kf3"r>H&4^f@m2%+GE2Eg,l
CGo<b'SKlC2>]A7L.Hj#gBA]AVCs=0J71%)rPnsW,94'+Gm2k8WdF*q3njUhWM)>8&ESb&br&;
)'h+;:_5#:I0/QQO2.[>KP=r/EZ`;b9%UB;sEV+Q[+ij^s#Nf2rW\2Z+$MrWgl^E"s7WtREA
M.g%`YX@)OWK,KMT-P'[.#BYD,.U5sP)JBF\O\?9MrZ(m81p'`j<D-*/s;.R,.U5sP)JBFq>
ZcQWF#82M"j_qUp!UQ>Q[+.s!7Hi"KCtL`IEua8H(R/dZ;"U^-?ko\6MEB['\MYZgZs^rrW
~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="36"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="299" width="375" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj3=document.getElementById('SM03');
kj3.style.marginTop='15px';
kj3.style.borderRadius = '12px 12px 0px 0px'; 
kj3.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="SM03"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="SM03"/>
<WidgetID widgetID="25aede64-a99f-4fd6-a92c-d40fe5b866bd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="SM02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[营业部访问量Top10]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?[WN;caaTD\n@A.E9kO)&ilsD!;Wg76j+*10=q4WjFfU>300kTF.&U<,P54(9GFZ7d6Wi8@
%MSTo2u;H2)oC2N[5L!ZHd^87E3!J-\`fS"%GDG(/]AR0RlB;\3JcT5%gM$pNN7CcahBq-3h-
^cKbH1"=3Bg\IS[f#!AO81AAr#/W1AE5_#E51]AF$)[<]AlO?h:TLrGnoW=oRQd/(\tR>^u8UJ
(addFmk:EgV*'./"_oIs)n>HOlo@N0Ri3>hqsM0\W="=#O6"ijI;XQ"l;DV/i%G)BJESP)a"
gMBc=m(^gX@)c^3]A8E]AJQJFL/D72a9?QU@ZGHj-*9R5>BC*!<?.e?,A'*EN@RWCB-o]AZO4*d
:&Gq1d(J@RiW]AhO2'=\)5&^0S^>T(=nfFRcgr4EZJCDhl42S0Dh/TgRY1rg0QI<MK5g#mbK=
ES5$,F=P><O+]ARBXg=kW`6#Y.=9kVJZ4IrbK+rQ2rS`b#BG3Q@&o;cfO&?Gkd7R2/]A4;VM[r
a(e*/qK7YOc;laJ@hBUg3XNDe'nDM/@!-$FZN2OpENU8lj;/Du[cD$?LCqfkBE`QE(K&:/j(
[c$@a6q/0fS4ZH'1\;E,O$8&,ntL8.%4dq"A<:ad5%.60AL,?',jG!,t\m&1Nd1=Fr;5*`Vo
V?Y*hN"oQVu`79?Wb5GL:V_MTdc_(rIKe5j)7<!#iRVA2]AuhO1p'@<rR_H!K#S:F%JSDGoA<
?L$uc@1S\a%?2.+V6=bXEg'@\\HBKJ[-_2Zh@+X<Cg,;296#F3Wao_/X#RI;/tM/Zf1E)rkb
7[ZC%EU,c#UJ@8PT[H2J,bYbr%@;b]Aci(>8$)V,O,Yk@RZt.[Kj-9*6KMd`#/6u\4t61d9LF
V3`\WWL;dRoUHRCr4K[o7"KQ>I]A=^5R10$$`e=Rn#m?):-<G)(5T+l#:7pkL`Tiuqb4o-m\W
N_^KEMTkR+g>nIA'rYH/T/:n^XAcA?GV/R8oeZ%J(sSD4t1K5T7dXM9a1*hK;,0'qT''d6\%
:o2#i(GAC?J;;X8o?_/ua5^h12':rBH&3J=t]A)',:`':JOKV.Z<lB)Vt$7@Cl)(qp"D3L;R^
qtJ*B8UHP?_(I76Nc@gnn^4;W29[_-&T!qU<mN>%)kT%J:KAX2adhK72,&Ab':ddqj+CUt$k
uZ#Ac+D?mc_e2:Wa+VaKTA%>0?^s),INj"N6RF`f\q;U`r["jlOtian9=1,B5Z++s]A)%*879
`]A26OqDK\X]A[n"-38Kt"'FHui64Dop,N'qs$p1I.JZf["L;r_"c>Y6nbTZqqS;-W&X%71#uI
&8pA0fMaj_,=d6bb!(%GZ)rrTrGn#Ru$Hj/>'YYAo/p+3^nD]AZj$<B5]A!uBb[$@>4)B-&);$
6YAT0N(BW%SnB3-+Y8.+$$F(aMX\[A`SpD^)qYu'dnj2u7"pTp3XSoD`<V`->Qkh#ZG3g)40
2E[gRgS-Ynjq6s#KOMOq$i]A;Gi3@2sOi>oA*-RiQSU)@uJ%)a?dUDS&eX(j$VHYgq$SkpZ\(
iQ[K4epFeX*)8)'IT"-VnL\e6$EBEea?)%E81_,lPIMeU9kL/.5:MBha9-<r4AX0H:`)CgO4
*d3]A3Tj.ZuAWr)A8&')Anr6_m=.Q+H)K2.BD++-G,XAIc93>MrjSk*_c.O]A1cCljR15s[n++
kKOD$Gl&Sg$lgN,N[DGbVeJ6*<j/u=k.4j1SV(:3IU$nG0]A'4D+r*)rN%TiErV(:r^gA'r-g
DLOTo`3,gs36+0a@!`d`d)GAlgG0RYqR;48%)o7U+LJlaW[e%<s=6U.;Rk9-'cQE'W00!&cA
P[,$CCWf:C54?9BICVFTWqhPVD3-*#pV-E=cB5iXbhQ9*9<addB!kMkL5T%\F?c+K;ps:pB!
;$9>lGKD*2Kk3MF19P'CgiV*K&TU(sq5S8t%SA?!f#)0)n$)EY($_M$au*;P;Yp$k$fR]A]AJe
LCUYMO]A!ApCLPi(QN`P3@M/K7jJ&Y]A?&DG4$]Ati.r7;o;EpW)uVBDZR1N8h">B\qFYQ2lWQ<
e0jm42P7NdXk3`*(EnY04ePGi5NlWjcl!_NS&&Sj!\bLbQiV9V(V5^p2oW59o/?4,,3P7FbH
H)@b5UDoj#Bf4_AT/8i5e[D!:<W[bK9=Yth:AC#Q_--puq/o^F\@dV)@Z;chi2D<YFDkMZY2
gk)HSdDUs8-O*#]A/9`pfFduk1p?\)4]A_QqQ*L=hI.Bm</2KAfKnm(n)6U\afS_lbY0&e@l\K
`!iIJ$CRW:Vl=@TgWG%.TbJ+DP:E)LUCUO]A&G:gsEZH:)8,O5,ONim3-AD4?YU3b^'f;]AI)1
"N>>P^T>3/dW$h]A\r3J:(NuHODA-*ppN$Mq?_@D'\]A5oIa&4O##PkaacE]AgcE4Cc1E0-`I-c
.Tfl``)H2`>D\8RCNb8ki/d&`G0Qi7_5ce2(GQ8%jQTGBPNXh(\e`lVG`M'cR<,(DJ1>Kk*i
;p>l(7@+a#oGq$,tZ@!1I4:;D*U4>$e01gZW[X0<mOH_S+Kba2tSDJj#N'S3PqYP2[kV+(W+
0)s7$Ch4`8a#ZOk*NS\bVap=cm%d:LbOg5pGsOMHrZlY0=8aAuraM#\]A?E043;ph7a/bS-@u
UW3XMK&>LsYOur"3ON@\2$U&Qg=24f.07N5&cLU^J+YmYN;1%Xm9=e7lffb<Qug*4*:K'mM!
_S#Q4(\7kNQmjg_.-Ic>k1n1Lk(cJWM]AfAuNSj!<-.hLIj"XPOF!SWTIT!<n\bbZBVQX=HZ`
3legH\X9rLO\1d,qF#$q#CC=jMG+.\<lY_b]A"oXmVt8_D3j9V'frtHnIBjG^)>MfEliRBP>A
+X!8n3_F%R")9>S^oVhlIFYW0Y[QBlb6$DM!jVV!Oi-bCgM4U6Ldrr!%boJQAi-l(]Ad4Ik9l
Jc8O2SkQr2,e)jMjc/]AJ%.&;hm1QXCKmP'IQ(G>a8RC21HZ:_,`P&btpYoN7MgKFsm_ulL(\
coqgl$N!0CHgZ+79R8r+tP[&B4HW^pELi]AfXtlA=!3"~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="81"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="218" width="375" height="81"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DATE03');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATE03"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATE03"/>
<WidgetID widgetID="71d72de9-19dc-4b7d-bf01-a403492f02b7"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATE02_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[9791700,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[13716000,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="3"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="false" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="72"/>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: left;&quot;&gt;&lt;img data-id=&quot;${CATEGORY}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${SERIES}&quot;/&gt;&lt;br&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${CATEGORY}${SERIES}${VALUE}"/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.AttrLabel">
<AttrLabel>
<labelAttr enable="true"/>
<labelDetail class="com.fr.plugin.chart.base.AttrLabelDetail">
<AttrBorderWithShape>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
<shapeAttr isAutoColor="true" shapeType="RectangularMarker"/>
</AttrBorderWithShape>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
<Attr showLine="false" isHorizontal="true" autoAdjust="false" position="9" align="9" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="宋体" style="0" size="72">
<foreground>
<FineColor color="33023" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="&lt;p style=&quot;text-align: center;&quot;&gt;&lt;img data-id=&quot;${VALUE}&quot;/&gt;&lt;br&gt;&lt;/p&gt;" isAuto="true" initParamsContent="${VALUE}"/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="center" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0]]></Format>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
</labelDetail>
</AttrLabel>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="宋体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange maxValue="=MAX(分公司TOP10.SELECT(CKCS))*1.25"/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="Verdana" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="20.0" categoryIntervalPercent="20.0" fixedWidth="true" columnWidth="15" filledWithImage="false" isBar="true"/>
</Plot>
<ChartDefinition>
<MoreNameCDDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[分公司TOP10]]></Name>
</TableData>
<CategoryName value="GSMC"/>
<ChartSummaryColumn name="CKCS" function="com.fr.data.util.function.SumFunction" customName="访问量"/>
</MoreNameCDDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="47f4f6a7-df6d-4b06-bf7c-e71ba55c2e28"/>
<tools hidden="true" sort="false" export="false" fullScreen="false"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@/B#PLm7YEBquN`Me<EMNTGg5`-<gO[QAq=t2L.7KF_8ec`34":,PD6j<[o!brQO6r^$K8.
YiZL.R+j'd3l<J-/1^+j:K'anl\PL0e6E]Al1Trf)#R$Eh.,u_thU2B:lkeH[d48Lpc?MDqBE
<TV/OUcp@85$4;Z0aa_YMi(W<P^Dp'HYcp0!^<\\cL!.IY;oZZk^XN%/Fu\!Yr:$Og]AMiV<X
tR#"-93'r'c4P#[Ck_,I:SObjFV8S;9U\KP-i[fe!$jT5&>YOf\`@/>-[Q?"/ca7=n@!_pf6
]AbK0S36Z7EZZqW/mi!LJM.:Eb4pkTB\NUd;Qh-N>eGfWr+19lB-Q.-UF""g(NqW17**aq9@H
,8n#9V?&i\'blG(#Aep!??kW2&q\?5=a0rdN<A8-9>IS%m[6-:LBfJbP`?a2e--=(mYaokec
r/VLMo(J(TtIU;?@>X>6/+#mEZB%5VG>ji=m!8:_tT0ls46\^.%)=m@;2LhPLf.[5GL[Q-.\
:i^qeGUn6OQp3+_3Rld#SNnrBOBMu=_lFYoD^jW^jD,'(d9r,Urbb/Hi19TRd9;.8oo5P;n\
#gkH[c1BCD"G:3]AB[UJ<;//Ia)km@`(lBZSB7U]Ag<L0_(>J<hG?D$@M@KoU/BPJ:SDk,)DKW
%_k95o]A*NrN;:4*;:rb3Z4NGU6lCOtM%hZL3kc&n%Ic^300Agd6p*bT3B7d1GXG@,%moIr0P
NG0Y[0YU7Co7CK0Ip.\fMoi7DW&d1>%LH!PF/hj@+)';mk#1<-*>GYS@rDp-1]A#6ZSu(ZcI[
T0%8iQKp=?hpXE15Q_#a0?T$U`dP2>o.K#I%KepOaepqt3tY"UYU+Q>,K3YU:VmZ$.+L2WD-
uck*K51r-5FP?6=BIi-$S)*352Cm=RuA5\l&I2J6"ci]AS:le9'=GO&q4p^U)e'GR&NHH-d(^
kKGR0al"(>Mc]A4N5=%J0J\h8[mR`l$_Qq+9/fdRgu;*>O?r/5$>FOo1-s3_>:BXXObafZ=$c
+SZPSTN-`kB3*@c'UR\>oK]ArouF63@FZXbg:bB4N=;l+mW?ps"%g$$1WM0?MM#`dOW,dOiP(
=NFeS0^ZbSWb_BVHJolj,B3h`Ia]Aa4F?fpE%SUYgQ;Ga@3X0m<futp'=Ab0iVfn#nio)eP7J
9DYiWZCX0-=oIHR+LSo$&2hrB[lhVqngTP[TF^EXKGS)YL+9?A1FT+sX1G+Ej0a?fr[)Ql:$
6^dJZqdS#%6*eR[FA]ADG9(nWXqDsG0MfnVmoi'4<5elKie`P?_?W5jq>NEcXY]AS&")g6O3$7
%TLb<Z>0$DSKg:Y>Whn==qVL@3mYa,R:msE:McoOL'OoP#]A=aO!NI4m>j&,aEVOVeMD%4DS,
hSR9cb8U$t[O_o(`!A8AXU'k(-(+M(H<Z8Q;b+UEj[Ft@\;+g*MU#sqV1jkI$WB*iS2G-'F]A
?#a"O<NA\1>oGH>KbfAXUq6^kD.DFgN#,EY5'n^ARZ9)bD&#^KB"fWgF3bMib4-[/dE!^R?c
_KqB)V]AP35Xc\^7ckd%s$N0lj>i6&UW6iTUbDW`o**Yg*X.j\Qn[@.JG9IHbTKH0^\jTc;4Q
CjLc8^5PTtclt&mT&`))hHkF<Q?A"^hri"K\$tmQ;oYg$QW]A!ZIj:mZmU*IRt^?tZ14k%q!R
+#T../m]A%.`84oGPaZ;a`%*fEN_jG)9TM'bm)-o5J[qn:89[iO4+ceQ;X1:"Ln$%YFo>Nk1_
U&>3$WA4Mj06$F^'pXgpZW7Bk[:qDWSR+S9+bp/IM/&ZIK+pYp=LnWXg(]AOIjU>?Q]AjGS%f&
S+`Tn9dZB<'p\C.lGM%Y+ZTI4hb[L]APK?(`"_/ao0k/)TiF'O0h44:llLWNN\4`UL2DXdNKO
rF%Afjdh'$GM6MB?:P8tdQ^O\j5HQi]AOHfn=a4ok%c*8EH8<1I7t%)h(g%bDg.mH/YB(`??<
c@W@'In>E<0V9Vf@D,rK--$hs+V_a1n`cO6mac.@'RD3p)fcGNV3jsd^7Vb8?nIB=oR4*]AE#
1Q_]A?TaR-Fu"h'%D\toAVt>a`sFdC5&K^Z,ttVhVC6B9VXKP/dbT:/@I/?ehOS,ER@0b7?/(
b/h7:RrObYgZ\n`'t@hd]Au@%7_#raU165jW\!)c=Z^rr/`.(U:ltPK#9]A_=R892j_t=-F"#/
g^tAgJ77a7bc\OY$KQbpUWXNT*.Mg?ROO3RdpB64Go(Gg;:;BVRA@qNg4VZ:8]AWj(Wa'.o7e
YtMXX;aSR2kRqfH(eL*l^`tEFW#$jRU2?)meq;@>7"$N44h)_@f]A=ftR8oD0;_RZ!b(q"9'(
qM]Ak-hhF<lk,]A@rdi/kl0+RCLDQ/pP'mV?f.Ma#hIS;7I"+^:j1Dp_IS).PU%9X2r9>9hXRQ
=c0;+4r56F,n)5g(H\&mP?Pe1/qK.^Dk'P(&34pZ@REc)gOepachISeW)DEYgiVN/=;ks?7K
hq;T\cn+@OXmcBld-E>7NFFfMjIY1KfM8D,a;FcnX[4[5Zih*&_Y/CKlU>oaKm)`CQL==oG0
0d5t1f'/+d*rHgaEodLMJkm9HrBO\Q;ED"ap&)%/BV4R9k6rr7(ED<K2iBb^&ArGZ`]Ajmoj`
bi/pLbC>%-e"=WU<&&V)`1-Hs[C$Y4H4*;M40N]Ad8f?^oI(7l:c!e5s3H)`a7s'//X5N>l)%
6dL3lg32pPL_$.1gQ:[k']Afjeg#.XUK*%ensGq#()e^^O$b6gXekYm>$OC5I#SrD7[Ic-HmP
"`s59@aKt87o:)qrqUlCIC0>SQ7'U@h-t]ATuV_GQs(kWkM=PR)rbhI;6tg[qYXA/\=ZY.^MZ
,Y*:'Z+6H@\)6UQm+8Mpm[0bX,g$X0A-=5a;r+:Ap`Y=I$N:W>NpG2C_g$/$iVU;Ea,J5o/B
P7c>fU+Y?HIX?t%@:"aSDmF>.?I?9R9rjZ!p9567K%N;,TLua._>6EK,;'0/-LPM)4@/"qX&
*+s?KlT<Gp;jjh/9&m@sBE7'o,,#AjGg\P>c10_CLbpG6`?JN-ucjC9?JnFV5P#-#.BqPGtD
0S:T5`>C.eRLV>CF:\aWt]A?[K(`QYE6`8:2+Ffh0_;6HW=3;!n.V3lI;j&&I(Fa=5[c-P"9Y
;jO8ajJQE\C*Mcr!1b"%=^obe7&XOk-3;c&6u`P;T:,rHh[IZ6GiK[:d2kXp@c^bFPq^");L
W9(@FMCfBFdAWmd5NVjX$7RPU\5n]AcDQ[l8LAQ+GJE-Cq_&F,P\CQ=f:HC^'Y+1N/j(?"M@t
;\/j66<1]AJS+7@HR1i`<KP4cgZWe65e]Ab7V\e]Aa+mmWXB2mS(Se;l4bC/@qTJbs^uW)$ZGmY
b)$@=_k6q)C-IM,!DR2(XTSP"#CeL$7t^%.RSgk*!Ppkn"^"-851nGK4,$5GY4a@BChs5E?s
YkCI!:p?<\'7=+7?<]ANuUJ\+G4QkBPGZ_nQ*QsE,EWk/rIId=7J9>1<dHN/5a>.>er^LXmhO
VpaZ3m#B/L?i0@GRoa2a'%f'Gp$ojRD_QWAbTGd`p'_XUdjh2*As!".X`fm%S,^?n'K$MXJk
<^"ln4A<O4eNrGHpdM/%EbU.a^#EI6&9\&POf'9>GT9=)3pDK'LX!QJF-9n%MWk>i`%Vn/U+
SE3ibk_TQGO1nJcUq)_[3tUAVmgGXYLR2WgOa8D"Tkl_j3,[cQjsE2WmQ6ESC-t:+Y2MKM!+
e$$>rH7*Ctc$;(JtW11,t,=>afrO3MSZcasXDt=KH8m<@Vf:"u\!nh!rjm'5_Wbko;@(/O-o
6;5;XJpZA!%h^_uip_30FO5Bf@oFL88dpB`&DE_(T\Pk2>!Fb$BIl0NGDlXh&HH4<P-6oeJ!
&k?k?>LpS_cY0h@pI.D^KgZnlqk]Al<9W4o]AiadfkNYXHNVqVorQMpE(U#tgI2$J`qCB=7;Qa
`epKpeN6b&pfmL1PjZL?+=#n9pOeG[Hffi*9T@t-+QmDid9i8b=t/SVaEUE^d/oGO*i%`RDG
OHG3YKPT]A1G=1T8"6kfL(/:UFq"&g4FF;`S$/!Z.eTlR=Tu%/*fm[%M:WY!RkbIZbe48i`Lp
#Q4%k`lUc=18P-6<P;VNAo&19,=AT0dP42dq/;r0`\WLYB3Yg/"3:5r>kTHge^5*#8`o.`[I
fHJ:SRlbLF_0X3DS*0pASgq#G/HCY`,k]AJNG$hrKA9N2d.GC4k#D7e+J%INATR8..3'!g3PM
^ZS)nOp`-emr"T]A.l"P.02A\DfV@'R(BAq:.=+87qCaU]A:t0R]AC=NtgK^?TR*UubGV;Q"ia&
.]A?]ABFL^Rj?72NfCD_<N9InCf?UjjEk?*oR=X'bYUQ&8fDdZbN[K_RUV_!B-AJR>-]AE8"s=3
e[tqUVW.H]Ac8'3RZJ4f2X?JG!X-c0uQ_uof,F0<GDTj![oX,1ZGa5#,()MaBe3p0a".oj#_!
dc6Sd^S9D1M?CSgB&7n\S-]A@f:h^j^W6+36CG=gL$<(SUgNUqEI4'9hgN39f\]ABO4<,.cUW[
j"F#qN*uli.BkQmJGlp1No3]AF%!gA7?Xt^Dr9oOgnZ<+$A5:5=PGq"_W`If%`NB<tm.>c6-O
$JNr2t@['dXo2.XJ)Nn8H.+t(dR!Im_%q-Qs^&g%#%QOaMs7,dH%O4>7h1VAiHWk>@SH6N>@
^n8D28,oSm`NQbn&&r*qiA=nH%Vf;ELS86\=nSR3r#9YHpX,=eEiHh=GcI4W_k\Ka4gpl^cG
F%N.Dg>'&J+EcRto&5Z*?MWU[9':k@>h>$;a/<FiI3+!?H3L,4'iBGZI=j?UK(hmIQCA22Q"
>H]A8s>lAjmulk+a#m3>nkQl>-c/5POHr(]AkfAPY,(#iO/#t6b:8;uE@P,+:CEo_mki(eBKqf
ndarg%%F'YbOX:X8;7M$d'+BkAPqC*^0X6J0Yi:_Y/(jbun<H06m`X2E/of#+@5R^qF[\"AA
Z*ttXmF"em'mi`Kl@802Cc2?^nGIFFY!&g%f>b=<qbpN<*ER#@5QHTa;Eli`s3OO6i?@YZpg
:.d`\K)WKa4!k<l.1Uj5Ar*U!?g.l+k"C'W^`k&-LMGt#_001q`m0*0\c2O,1qFs[Pd>7M,O
o8K86IbRL_#,`Gd%dW'7X,&iMCIka]AM&-Sre*%UYXlC[,R&V;el0r'8[G.EB*IV@%*6S]A+IE
/`ArWp,]AfOk(33;r'2ki4c2_J1p<M%<W\H)gbhdRppOTd'`\0R[]A-d[.NPE$L3Dcp2WZ?j+P
EH9-pcD1lCNR%A\^.CdVC6!tGK<)@c\g%DeDac.Y+]AC@d#IeFUQC>R4MK[2^KBS2US7B_dab
Ih<!Z28V8$-k%?M\=XsJ>B`b,5pdQmrb<`I\@T]AW9,O"LKh5pVY290M'7//]A8k!4%mWZdj86
V6r]A--T+rB%_e#h%lFHfhlUAjO(,%F'F^-GWkHKeM!Ml?,`eP<K&7.p7+B3IdnYuGC#/+]AC.
UcB8%,`8k7\lC]ANc9>IIDpC0h^MrGh$]A,4J.UT.rSp'#jpH1"AVgr`D['4*6QPchp]AO'\_*i
bfdXAEsV[T+[[iHsmC9:*2[I@\UIs$]A)q]A>@>sKY4C@oclIe<_(s$feNqkgo"%8XB,Yd,+u*
+6,U-<R%E6hg7]A#Ld0;SEn4LA]Ae27(rcQ-*oc_=+#$4qmb"TTVk2f%'XNdH;9<_?HZelA^"J
B\tb>4(S!nImcImdQY(Tl7GV7,0*QqK3QiDK-V[@%&]A-0O3g!6pD@K/Tcj"83XhVkU7GuK:G
`YGGpmZ'E$^4`@Y2i1^:DiQ8!?'M`a-G01(cZ^L3o]AEN]A[*&?D;7%A>,0Q,F/O@*/D(_esNS
@L%FoD\fg1j1KBOe0L+k"lXXQ79d#1U=Vq5V5Y_5A9MF83ae^sQh'W7-V..`quFT;[QNME=T
jJ5)e"GK?9[6J,C(["KE5qlLAS^\*Z/tmS0Sae'O`>^3dT.J&+&)>:r,WdPjDnuF).\E5oo#
N8T\=`/WpCuG).9\C$k$MrFEU[pQoT.`^'/JEQW->'H:LKd-Zie^]A%ZuY(8&6gAg2t;?A>uF
W>*kbfS3N57ni1k_N7K,qI[\5:bt%4gf5rqNp*]AEZWM*%4tFn/F$WCRaEd.SGgVF59<"iHOX
Q;g8tSXI>&(\/Jq,Xr2HQiJYSTfU&r`%!dZU_]A^a=o6\65SLtqGUJq?WT/N%@V:OnK.==seZ
FU`!IQPr&,4B*(k4$fYTYKr`\d1(;^UEp_S/A$6(r&'P/8"3nu"1K]AAQsNc&`JXq(rIu5[Mu
5a=MkD+2iSD[K4YPQ8l:up_(]A^/O/l*gaBJ0f36XuO<@3*?\/="tF?]Aj+"0@KT3HS'CN50<-
F0O>7%+j[d.D2di70(FGZ0fZq=!jTc2TSBfrI[gIt]A)qt9o)DEm:U1/(=2X]A"T5?)LY52RtQ
XeuB-oFCc[oaE3McR`B]AmG&Qms(&9KY^UT&l^nR>MbQ[+9P3'IcG74r]A5;rS`!)TUoS-"q$b
CW+\7(^7:1S!9G(b[oE=:_$2#i"&4UrSG:LBc)*cn)fc=iN8D\2lFj7K&aRBD&ok]Abp72KrF
Aq3<]A#56fdL3,;t1DRVtR'T1pf5N&,21K-)#"*K.)KN'mAh`$[kgI(_L?;ra/Ef?@hB&tUbE
)0#E_.5%Z&YsJf&nQIL'7S*TpJkb&+`JEE^]A(MjbhnYs83qSRN+,!F3,_pO,fjo?[%?nUf^j
"?(NqO='^+4^uH$;8pQjcNid"OismlU'_42:58ADoYR6VD"LFfRbJF;[Y!p>m'm^W(CV\=OX
:I"<\+^;V0aRq18QPZMLFL"L@Na"!alg(:-3:hMkRW*_$1;fq]AB8`6SEFhS\^;0)L%G0I.t7
3KN]Ab[OW.`"8;M8$M]Aj%B@5+N$QV$%]AuqWfCA`7U_4GrR6[Q4,eFoMCVVr&79:MgLs3<t[=6
"io]A=&FoH`d&Qn;r?i#u#GPjH"^-fm2LYUjWh<8142DI&eOTUmkT0I-_L3F`VSB#\TX9pVXJ
N;9`Q<r#eEc@pkEF=Vkimn2Y<hA\:Z_j\A@EBfmn,&FraqP_A)Ds_j5X@SCosPc9/j+#=jFK
"V1f$pr<d(j]A,m;%.Hb\BGsF=qgmnn$K]At_75=e\\n'^WhYG8\Uj\mAd>:Ac$ZK-+!bXdK$<
!KD[NKs<T8X3(IqA!kEA>lJsq5'6@F9dl8-4Vap\V1iB5<=AIin&'j$e#hH=2TO7qsrPJBrT
5!`/Ic-Z00@cIM[#Rlh?*'iGSTDch1mLf80MhSrO-ROBZ5_E+YSE>j,GucTe[MW5\BSesoTQ
^YE=s(7up.j@>3V7%2<KTc-YYpUk"8nfRufTaQO#^*X>CQ7i[Cqn)%E??2OQ7WBa:N-HHiW0
9V.^K\NphtT%=46E]AoAP$P;LfdOo*8-bPF2*uFUj3N498-^N)F_b\&-#!'YThKr;l^,;q"Dn
b>dtL>jbrK952e*6DmfU&nG7I5Dgr@$F]A3gl_Uhs%<d=C3M@@p\Vsa!`E<^/bUGRHSO]A3lL=
2)*tNRtX_g```'dr(Nh2I9>/3-)=<9tX\nq578(\37Z]AdaMT#4JK(^6gl'_=9<q1H.&uFDpP
%;qlp<aa*'W[+&j9/obE3V'o,98D>e-5[QAE=jJ`uEd\#:C[8]AZV9Yekr]AO)o9`8$2o2HiI9
&b(<UQX&OpH;<07B$HgDaA#&=gI5H9aZ+IgDZo@_cQ]AUGjnm_ocWiE`qp\@<iD&_H;bb+onO
l?KFh3pbOos7hkrs2DWH+=?/cN]AuY>a\R?>#"\R0cmR::90c+bJQOhO_gJh(f+ig.!+]A/q/4
qR9p26OJRLg2b%5Elj7sk`$qu2\Yr_<&opuTkeS5*qRMea5`N3/&r'>')`[Hrh=#5Qa>FF6`
>FRq&s]AZf.3C0Ej/,a>Q!1i\9@q@j?#sFI8g(hfN?N:TRC(S@>=V<j;$-2eK:oJ.-)[KT%%T
bOMU0gp]A_%u4Jj"-:Rs[*9N&nOTA%]AAW*@T5$9-[s1YT6340\'LJ(9qS#C%$ICBNp[f[4((8
QW*/XiiBkM%N^U0:&MQNHUPM)(tl7QTtrCt4q]A3J1_"!EOa.G7gh;c&/n/^SflWA5W>(De=/
&:`al4_0U_&,br!;6jQVU2CYqB67dHfScb!i0@d$47nHWfA%dI6)S;%kB1B59Rp%oNu3Y?m$
C1/X[m3kJi07KXnecq`FN?I>f8U@-\#HUd+lDK(A8aK#BJh;,'0=g]Aqa2!GC4LW@>X@*-l(q
V"FhQ_h(\LJ;e@%.!d3-gWO3eLX!G=AS[%11?=g?;1(koVX:WWI-(I/["5-<k5.,,/[u/lWr
DS._sf%O:<[U>p*=.&i0!Bj%]AY1#8<tiY:PiSWqt@be?2^!W)s2@<+]AM&AT:!LVbkn]AAbMEF
lk3gtMD37L@Kl<*qLlj`&r#4(eDD<$Z(?*U%HDq9HikO#Of+q)2.#*eh-us'be8Nb33XP<__
1*/(a<N=#drE@]A^!i_j`]Aejidnr2FtlSjEUS_?*A4n+<jJSW?HiXE"-o\=E]APG852?&mY!eu
/r1DcfM0O`B?]A`A-R,l"B<;0VdpV0GuWj@Sa!b'%YPkP[_:LLo'r$KFopjSEmK&kh^*R_X#Y
\DR645\>moBgoehp/_&m<h"Qr^Y^?EIn#;`=h-mL_=qYmeJe>,!E/.HGdkn,nrEocAV;R]AJq
RiMt>r@3%rR8oA[DB0@#LIik.6(cAV;R]AJqSD585cX(@7Ccq5M4pYoAOGTOY7D[I5D+@8_?!
;#4h(oAX"piJqR&Ll*3'ZR!sB$Z#@HJ"41D?N~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="55"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="163" width="375" height="55"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DATE05');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATE05"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATE05"/>
<WidgetID widgetID="cbfcf63f-b570-4f12-a930-cc25fdbfa2b9"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[381000,1143000,1333500,114300,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,2286000,7353300,3429000,381000,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[用户]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[访问量]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="5" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SEQ()]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__413A4477FA5DC879A7062C04A560B7BD">
<IM>
<![CDATA[!>5b,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$/%m<5u`*!m@.sO>ZDZ^ca
GpuSCsI$h(A.#Ta;fW;1gSq6pi\[@*oMs:lA5@(srK*01KI$-JEQYU*!3I-u'$]AgF^#nF7-^
t]A:i,#"CHs%D.B;l,_?%R[.:*2d[@Z7rh"7Ep1;CnfpQ"2b^*(Ucd'hk\bD]AQr6ICLF3_=T,
pb!38P2mDP*Daf,q2DWb*j+'&?YR?hr$_M6G/=E\?=g!?BO./(ATZ&RM@J8PIJtN4MY"?1\g
R5ipVJ$ZhbEldC>l%3mZ_c6*+5ZJns1AIi^h]A7=Fs=m]Ag-8h:i1M)S.[)Q[h`E[F0!)X`0r!
WOm4Wh1sp!.t.<]A'r"f'LC?t)8t9i0E?E&N=jN0op.VaK[J6P7a_sN'E%JQ?1W1:n2NM>ifA
AOAM@lJ8[LNq[*!"cG)gnpD7AtG?J;h:dh!-D8%:Hih<FUl=T\38CXGcPZVI4R-`@>Gu<@pc
HE#Wb4c7Op3!<$ZJrM$?g_Kl/#Qq_J;O+QC6JhCrk.f'l",639@\"Q_"W&s'CV^-K(=dK096
s(@?!672X`;ko!*!4S'COiJ1!Sj3pWg5M%Ln]A@7!!_>uGEa)f\Nf;Io!=pN^\2g!k+P]Ade$^
^OH_%=8cb[FH@9HpqR;srcEbVXLmh:4[]AFSsM*-4*W]AKu`HMWuEO*Wg$W80V^M(F^&>$3Ud&
O2+1,9M(jp%SXSe/8)\)c]A(]APAnu(4!H*p&-$?S$SJN1a_4o6^;L!/NH$MBO&:sEZMN7,H=c
qo2%1[o**+p(^o@5[6f`284F'NXBflJ%>DSVBer0aX0=ZA8K\"k3e;e%Dq/ZJuaC>Gj7H7o>
d>:8@klekT:EgHuSBFNhoElVGdE*i[VmfQH*:``$d*iY(oY/:7K&)_=*\@4>JF#6R\X&O;ur
0cJp-H0.T<1mAXqB8YlC]Al:c,b^EOEn?K73(?m;;nn_!1VfN4(/R<sF[?HPa1M]A1:I)r$q-*
j.\H1!lh[nCYMJ6\'k_IU3V%FWtX;+bc?>t18LAX@O3,1e!3k0e\go(+FGh?eb.cslDYu[k+
i6rNOO25\f1cU-R-;3duQ[pAXebi#K%QK)j*5I"rEEB=k3,uXpED?TIhn#Dp8V$\@"DhrH;Y
47m!:DZ/!Sdp!o9?_1CCP,PHHLW-YP?0]AJf=d$O.dan]AlChga8n+*3e,Ml?&P3PB2$Yj$34r
bi6E8QE8hl<pQq8@/]Aj6$!aMV[_.=1ap!\5>SghK`nTf^dRnYQ95JYP/5^-^'!Gua)>X9$M'
M[uP4b8CHg/Y-H#D[=\;^(fE\>#dJm9`!XpP`mD*M,m\hCIaigeiUVe8O87&?Gc8B9fhlTf+
iegbZFa;lOM-c>0;oi,JB7iSAnb%KjR"a;oV+5m4HQ`OFS)c5P$>9&A+!9Z&_ca+C;G7!SGo
]A\iiZkWrM`_/2\gjYkEto9HU$ZHFIDSD1u6;N>@J/%opCL[-_MrBo83=M^[t)km#B&Pr*+>g
4kjS[?\H#gO@3TWd[PlP,t7o5ZZgE8bVZ!cFLo@YSBhgIuTL>+cH>\FKd\9f-*Vlbt_8nt-=
$:7N7R3'b(Y6bV#1\0Vq7BKk[F`2BW=$#XPnXukPZggBuNlGA,HA?Le)S87\";;jaHH,Hh1-
N=T+NDtj$$?nWIQm7p#`-uAQc?6g,$>]AoJ*nPmoEqWak'OM$Vmff$$qc<P!c:@JPJ(<^>q('
tEgUS+j%GqpS3$kGAj"IkF:*C(9*SKE4`MK.E^<i9DD$]A=EUGW6Z;_Yb:ee24:KBoB+&n4tc
17tGab8WtErjS;Xm6MnD)k,_P>Q%em=L!JLUG[UGqj*Scl0M,EUcWa^1Ri!0!JRE$K@PK.;K
,tIb3@M$c28X*N!?V`LYH(iVe5q+Gekj!m&Kmt<6QeIaU6s+B5'XZUXokFR]A6O1[.p^=T`qL
AVtHu\I@M?ab?=3jRXII8#I"UEB65,=@A*Dag4gBRIj&-;L,e#*W!7Ke?Nl"%AQH$-IC@"Il
`,\uU]A@fa@@dcb"t@^s@g$crfBHQd;26LtfYO6A`=8uP5,AFqai^o'SdJk)la#s[,c!o[4HB
J"ZuRL*CS52Ccm$F:cK$X#HK,T"Ynun-g3q.DRi,Z6e/55$c@FHl7@cH@?bdQWWGZUFi7S:=
bH<KHCXE,9)hG'<U5N?Y?(pk`?Z^?poPrE+_("n)T,*;GYCu?Zq,_5c%EYO'GD8/-\HpKSk7
Wa'Ec"8!oN7I<Sqn+$ij_(`VEJn!e>ij05"&h6>D1Jrd9_uD))ur0,Ci+4)JRhWMEAC@o.Z'
Z+"t.`n8:Ybh>I*a1sL*?H.q=+]Arr^5">1%Cac4B$&V?hCo<39=c7ABIZ%<@bd>J)_D0DL0o
#Cc[8FM>_Rg.>7i!tdR8faH$$ufoNJGr6!?/7>;gD&8X$4amd3j^YLmh?biV0`5]AfVcNN2@g
E[g<jI#S,G\MIZ1j&7!eK;gA^3b+!mY1%NmE[3ntUg:EqrHH`Z.1;\$DDjduBL#F4f+J,KB$
TJHYGG?E^PSFZc73=Cg?^dHb*o7%#'"G*?8@?)M>i4:9slJIi9b;!P?b?TKF462`K4L!8V2c
?]AYKS,D/LYfVB;O8U\.q5/sTge5\i3O's[mrN@[_nQHe-WXR-*Mr1j]A8!,gOtHC>_gJ*G''j
\.GbI=5Jl`0F'dRQEa4ARMGLDoSB2kRQ/uVH8;J/=GVm<8)Wqq_XAujWLYGRIe5%MZCCmOQF
1"[3F+?0u2rJ?R__XfdKaYJKl./u7JQbXZWA/PG5N%LJ'Lp^Q>s:HZ2UX<%^C9%&T7BQfM)I
tmA2^[1Z.M>oPMSB!&mkjMTa%kf!%1i_:-Wf=$2Ug>`sli^`Un_MQ'TUeod`"JB]ABc9ktY^"
*fD*D\BP#o"pNcdhiOK&j4di&"54?GneV9G)^KC0#C$M?l?a+)4m_LjO`-W1:4&9dJ-"UKJ7
F3;:+,3j=_'3od,U7lXu3R_M<>GNm=aDBg;#l(i#'8Ws#6W,J+ME"1gZG@nJn^467"8b1e(Y
Ei$_hJ6hB,n9$nuVQb!<3!uRkLq4Nt!B4,:RJ38"gK%!L-ibo=IHk`PY&X=`"1]Aj:dWKE_I1
=#KMYN-pgid8@:G"Z4S=JYs-E-0;nJBQktn&B]A9>pXAYr:_>9#=O*LOH4F4'#/W"533PYh[Q
ZL.g%;gFc+'<i7L;H91i*FP*Daf,q2FM^AJMT(Xr0kOK$_0!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__C6BB7A0212D9D7AF5B063C7AE49C5CB6">
<IM>
<![CDATA[!@eH,oncL?7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^$1UST5u`*!m@8N>*5rr=a0
?(oi`[U]AU@dFDJS5]AhOe>P7,=7[jVj1Em/V19,X&R5KD:M%N84AB(R)J^Smd0;rmDEfSgDBa
1AfY@oPR+9QH%]A(q+B>EiU4e[dB`c3Z+Ur.)*;Z5:?2OQ:kIYO'cJ=Vsg;iUKb^Ah;^\uF;N
fZ6>.8Fl?;k2h]AV`;XE:;nOEpp6r^gAV<?&9YpZb:_P!P?O\NbODPU16\^Qg`<?/T:u!(P:]A
6(kj)&nomsIJg%;=9T`rjrnLF0>LeLgei5BFm*&Y))I2jG0'eQp5$5D^3.KC7LA$lI-eLCP7
K8[Rbi8t/d]AFpJf!gWSO:K/DqS'NEBB8(.Di[8l8s&sXA&->l?gW`n<s,lI=5>4-;RSK4,"5
TMiq=eu44K-qblAg)(Yf/#"`OV[FSe!\GA?)XE>`4?Z_HQpr*>j9g]A&#eja0*_jf(B%;&HH$
ir?^)S!ZV5#P(5Su:>2BE"E98!XpO$P"5HN)&RX'ncS?J(/bCdeV',di!46PhB[mNOT\tL6H
kkQ[<>BPn0TNo"c^%Kgf>)_/:]AU]Ao&rOTuqq->t3!+'/`Zp5lin3pi!*h%GE>S'!(H-3mZPM
`,S./XRJ<RPHql9a8R64NB]A/a=%>V_ipO/0&aPpa01.2^24U%]A`)-V</"BSj)dZo!(VXoas)
2\1BeF2SmIcKoDL4'+)]AeN,[t0.X'r!N=8e.R4:jL]A+q)_+f(7+h5g^#N0l#m7?3Mb_4LJ*0
HNqE+D7bPEjKk0kjp^`Q8nHVr/8>Wrok#!*9\%Eb)"DD(!g5E;7YXRfq$\K1c3;HnKIV+uo?
O$)mo0"2HeQge*+O!-!1`MC/Dg'mq@GR8\Zs5mICm+5hSVE45r);EV:*'"Emm\%%td!3UtEB
hG&?;:emtiSZe/jeO,n!-_unbHB?3Ik/ed"I+,(pRncO_M_('Fm#GW@)3&_I1bD?$GdUa\5$
U`?81#^F$U\@QEumm_7HqE6"O<bC]AL3"^fK;VMRVe8^q(KC=qir;@=e;(.Zo;=WC2eVQ@O9G
+PqqP(+fZ.NG(e*!o-HK1LPofY:IVShcbPGWb$/5$2>%)nGg3BcmC;>]AT41<_tMef"P4'eb6
i'3e(k8:ee5K=5VjU<<WEZgY-!.l"t*!3rr@be(cetMmlA+?Z7V7$CM.*]A<<KB[V#o'!W9'Y
Q5_[Aj<@7N(P\Z8c\cg39X"(om)mUQB33#XLXYNp$hP->2*L5@d4:b^P3QHHZaa-LVWW9Mf,
$)&J%Wpb^dR4-(.@Li-_0UGXAU1l++(YGl=KbV&J3u,+eSR$=Tr']Ae*<)V!)'Xeqr!hK@s/?
DhIY!s@S#ZX]A&R$.BX-c\g`!_=[/N9ffKX0PPh%G&aoka8aLR8+C1Ep_:M6n07CP&r7W`8'k
rO9$aO"b@2R/5j]A8.U,IGJ3S_HN(N5[Hf0.m]AVNaS6XG##W\%<G(hDh']A<jG#=nN_\N\^UX4
pjPk?lYST%#OuNKJg.Vu.IXnt3]AjTeb3]A#[<;I"lPcQ-P%jnoIY0[jr/Cm/e=HO`M>YT,6aF
TLbGNkrj?9UEf/6&iUHqNgS^p"K4&gpOA@O4*EC]ApK.9&8/Pggj:CjOa'HCSkT;"%>/>e7ha
*#uuN47k65hM.[D&^50fL.=*:3XX#!G+\Z@U'2!Z,[-&e^;UfEqm4m%Ei,!\oXA50$i&75nD
:g`c1n:kTQH]A*Z'_(F%M.*NYEH_m*km2m%!"\7/"MQYJ:P-3`sR'X\05VgD`foapFJmU%W3Y
YbdcD7$k5`o)-cadC8:pjpX+\):?%Gh"Bp-`E`HpVC\Llp7cu;inJoj*kp`lOW%=RHPsqim*
g:!m-@YmUiH[@-ueai4\/Ja=e^'5,C]A7s&dbko*4RljGV9,'YtarF8AW,L]AK&1uj,YP9@O#2
/W3ceS"!iF]AA2;&ZdD^q[.+R^IPeA%8D(iIjHdn?a6OJ"RPS;M+oSmp&RkFTZr&t+-bF"?_[
DbA)"m:]A)"t>`RnZ]AC5Ft-HU<t.YC-;6a0>*+d:!91lsU9WN"aTh?/#rN_?hBrEo6sA@V?uO
@!>$`FNK0*;cS';OTJG8XH=u]A?ONm[LQN./(Okf]AT2?=$R59r,]A0,smeV)_F$R]Au`kq\C<r(
gP9@H%A=MgCL^Mqai("$o%'\2I0VLakn9[]ASVn1%_MW@M&=uA[j"@tV8LQSpd%6O=QR0bhs1
p`(of:h%=rG\_,5-#bG`p?8Oo.L?qLia3GNrpd?2?,I[S2]A,E4hTNV.Ce\DsAg'>B"M+I:Ja
2*@WsMS;i,b4BIDZk%2f[>n*c%2qq6BaOX=?!P&&:)C&@g';uMfo^O]AP]Ana8/7g]Aa`gj0[q8
/^=q-LG%YKFe7b$Pk,K'nVY>>o4]AR!Bbl_B7TRo4l`Z('HAoa=!3ZUT";S18g*ojj$,#5i7h
-Ci-mtublH?@JSB-2L!A-fWOtuMr(UaQ)_i=]AF0slXT#HFIBU/ql+tU0$qCOj\,/HEOAm5^W
E//%`6^l!#poII'()P+.R1^Vnomp"H"MUu#@'qSKK$uK(K4]AtDTg3O\:l-uYn9kU0;bg]A,i2
t.D9,n&P9>P@aq;ODhcSGQFNV%Gcc&4]A&JUdWF$$i,_(UXaQNte*m%MOIXeqee9Drt=3>9&@
#q(/W3$@pfcF)hlbkcEA"L_CCDoH?5iL>tL`!:D\Fm5jJZ,>DSO>gN1\?k'$r=c(`fbPMSsl
&W3]AiLk1@BYGM'IIkR!N4NI[T[d*K\3.EMC(:/chA(#uWA@YOFT/<EUp[<5_jY<@-g+Lu0>Z
p+Qc<]AnD[`a"H><EMAIL>/%L&$68IW*[55J++Gc$p>X:XW/Oj+=WMi*WJnFn8;6CX4
b?Ac@A8gI#[!I2bHVSdtHY+a?21h%'Q\I^nMFt<:LZ\CgJ?,k"E$;kq+p)s\6A*d+^M44Jlr
2aHT/YX]AV-KYd1@ea'U"(nXH*.Hl[4Ee-RFOte2G5p>FWUAF6[kYl!s8o3S/_`ra7j$>4>":
6:`M1D^L4SL3U1kTANm+&$OiQ\Y=F\e!QT\_:5T!FU0oG556>4gA%S?*oFdp75g@\/<,hZ*8
*7!Z"V;.hQ`RoSb0Dk<,l]AgfLsb>_Sp#B;:&Eh65Bnuq!Gk:"rsE!5c<k.mi`:0RfBKHiMf[
327fl4o%*(R=(pDuT1Uq?b3qo,rNpQ/QpASYe4a7-/^iHgf/l$DD;k2h]AV`;Ypr:F>pY&*d4
+$MI\!!!!j78?7R6=>B~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$$$ = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.BackgroundHighlightAction">
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__941F354D1F9462B64D43B5EFBB8169AC">
<IM>
<![CDATA[!UU.*pPD^A7h#eD$31&+%7s)Y;?-[s:&k7o:'_%(!!&%Z+<:F^#dd/k5u`*!m@7s.*7YeEf5
#3#+b;l[#XQ<B,!^emPa7Z[UHFePHltO%5bb!<.$YopLuW`(B@dGUJ6M`"J0Me6aj]AeJll(a
H#m%"?%>h*<d#-6Ud\]Au/5iIhBa:n/,pWhE$a0'W!2jmfmY4U>kbg,!,pG_8!cJ1K#Ma]A'e;
2S89U(jFP74Ci+.PBg623M]Aq.<UPcWHZbA!U8/&PWBK.T\*(p(#3m>mm2@&l6(UhUP@-HZdW
Y74V9A)/T+rLqXJM`2ltqD%2t[*f;$tp"Uf2bK,hQ;!ar16XX8!?LB*^BCEd^0\-Uk\=r.6J
Y%ECu>`KkI"PnuU#=g9YKB2LfPklWi@>ODH/u]Ao;VptZj$Y@gnBGS/kh2tL5\X`C"f;tb!rW
\>hNBGgfWM(8kr"!c,!$Ju.]A>G?H?N>B\(fpasCg`clVGNWd\V+_U\es@7q)49h.&gr@YCUI
a!npeIF!\jJ-,72:!g.oFDeJssOTt&Y"W+LD]A"lJ6W:a%*$F16L2mYXV5X,ku3B8HucIE(jR
/IW4RIa<gXJjH:Wc8)qm>JKWdfmcL)3,`dT5PHrMrbbT)&2(*$DmrXm/l:Z/23C82>^Mmap@
C3Hme*\?@_f%U`GhgJ]AR:Sj<jhihe;(0a#h>A)OqMC7XL:#m_u^@!`_/0fi/m"0#2TiT':qW
^Lq1!%pdfuZg,BG$3KQ[!c/5".Gthk^srE;%\-]AFeIA:Kj?(0-GenM_i$?Y!NYda:E3_-Y4Q
'rT2\,;?2Xa^tHNH/bOPWA)7:/%N'Q)d?0#0=HlS?8H!4[=kS3ZCTXWh^71#/fE$V^6@%<pM
?A6#\0@T)#%E-6E^<5?bP@-[g@bBNj1JPh.<hkU/647oA?<:,55);q@l0!<$eNAh2@nRf6TO
^gaqXX8"0ia%)@f`&CJEF)KbdqTh0mKVHd@hIKA$IS2?4"LsFq/>9!&P2S)!ZX\%!P#Om$^b
#nAfk#3K+5k%1UoK)rjgM)+1^#<GX,KPbrWNV_:@L]A%oAA$7hj\8Wk0/IUtWW?LW9Q&bl_kl
i6DWl\E,;mCK4FibkN`&BV'Bc%6a,)f1,#+7%jYE(c57\Ek_GQIpYK\I>mb5aZ+57quMKQgk
lk1)Qtf)e4UN+n2KG&-`U<<3Ym,gdqWJbPH&oe!>TCB*?oIY8Tdg8*\^WhD6hZH=\E$md%74
E[G:no.0=A.S</7ga4("e2.+:#fcn6q>?Iiac?%\<W_ui_.XE&\C'"Vf]Ar8:SGZu\Q<MWn%e
Fm-N>p/(l2Ut&-H`U+O`t1hTDY!Dn#7QV6<8tbShY)8l]AT%;>%sn*,VU!@V:gYBL>LBNi6Ld
=WFgnC)>XD_Y:K8qMh1Jd6k66Xme`1eeXd%EP/=#CQ:CIpWj0fWbl6!3o\"Pa9GageXE'@U;
X!1k?S(TtqSJ!>17c]AnI;lZ`^i;Q#+f*hnEhGrRlN-Jn5N)n([\A"7"eZ;$P@Jk`po1kE;Ip
P[HO"bqh624WrkW/8H"(msR]Auu_0Wo:U-_!<?<HT)#o(P#2bs#[Adb-<nHpHS$5Y_sM3V::]A
[Jce=UL4!4dSc7C7H)d"jicI8U:+@eMO1;k:1o#RW'/GlAP`b9Yg/O'&<$s>9<bIP5$BHd]A(
92dYT\q6>`Y,;mDQ67.$st:XOgtO;ek;Uj'amd^eO,U\a^Z&QU8@IGU;:2@6E'X-T\si:#4k
fn5)([16lJQeZloU>-PnOhirX5NZY8iWU!5^MFeR!$bN<SZQZMQF6>>h'Diibm96s6KS.3+I
=15&2JL?*6Pq?UF9$^U";"VOa2/pe4/q$HPq-K`AY&3p\3?8MHghpI^Rt8$O!4C[.f@@0='8
R/^mQFW=Z[>'f$5QlMc[#@Bgk"&eKF(sE)B@U%BVZ_M%K0%nq/Q>tEhERGNo'"&^P:L?_rr>
jrH3<^a1GqdKOq1EOsX+=ZcPQ[Es_0U*'<Qq'O93eYN<or=4g"a^W\aFldCe*8*\C:"fE0f,
R.s$S]A':Egb?OY(FV7t#>a$=s6=>lkE8Z.#`u^kXp[o<m0D6(0Qtp_&b@J;*'IU<322skT\s
j&)8i=,$F(OXa5gNF$+#e/3,fem6:VnN]A1d1gJV'WsB63f/I2=*0U"?k/QE9e1,7kq%\>*nb
iF11)3!8\o^Tje>7U6f]A\2H497f#&-$QosDF1*XY8e-C;Cf8;pCb_Kr<fYqA7i1fM9:<20Ho
@]A>5&]A""fKCf_E&fASV9[,SJ,1S3!MDuF+4i1t<C^Be!4ImS]Aq4_-#EY5PDRp4;o1hYkq#SN
YO.q@o_)ZLG/[#,#+,/??>B;;Kl2c)>qjVH/KPMk%r1%hp'@`Z>]A[iMD!tK[]A(V@Qb:'0k,.
EoP"/ArUB-OG_rfL7u4E<,D0g=+=k`6t;]AC*!#>(`I?A%_QG&GebA-m?CaX\$+;YJ^3TdfB0
;f'<c2ilaHTlES/g4;B=CL.?*1qmB8VahB+SM6^1V5!B6d'<I1%JGlYPi32j[?HEh8J:clQ"
0!,fls#G%+7f2G\kA`<<eGr@ElpZ*fP;n=j#CuMAG>c1qF,6CG"D;Fn(Lm==J=q;uZmTS>`c
lde\>^*[ns[s:[QEjHQf]A*fN;<f!R^/O"b,@dKg1[j\6>\pgi&4u\-"979(]Ac#]A(H[,InO:E
JV:AqoIkchm2N/r$mQBe%SF[Ykn!=>s(\t^<^2;pq62uZs@)H(LVk<*lc8f\ok57*Fl%+&=!
bsZ6XXn=;Meh%Wrbh9ig<KZt$(SY;3It]A'k#:OT=jOpu]AG.j**r'4oWRffa[Xlbb>XCTlV_$
&[aKYq&[&O0DV'nb]A8l:IDPbM-+q>g^?_C*<C!@e'L!!#SZ:.26O@"J~
]]></IM>
</FineImage>
</Background>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ValueHighlightAction">
<O>
<![CDATA[]]></O>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="C3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B3"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="用户TOP10" columnName="NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="用户TOP10" columnName="CKCS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="2" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand upParentDefault="false" up="B3">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="B4"/>
</cellSortAttr>
</Expand>
</C>
<C c="2" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="3" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="simhei" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCNU";qi*AN)p0%,+sOpTO:&N"9bOD<Jh,-TQCH."@<c`:4e')TmJXr+UZXj6qZYDV'r$.%1
jWr6OOH?!i[j2rR^eg]A^O*(hopF'Sf)2"RI#"GhKtOFDlnG\p[@?e*YCBK`l?#D>ORk4)r!Z
Mh1(44=a0d-E.2D/Zho.IQI^]A(G>$sdlTH4,1a.HQKc8o7cedZ;m$P@+J$._#YHdZEVqe\+J
":Q'ldF]AOHep+7j%n_!O&78@cCL"S^2,>$qjd%@nRJ471R]A`L3+iEC[COXBCU\Nl%))au+M/
d2g>oI4`lb`fc,dfM`cn70+0=F4_ToBjM1!tH&n,9sO`O:dh9qk@Dso76aZOoTdj#+o>0Cf9
An-1,QtK09mc]A"UQ1EQa;/WgT(CpEDEC3>AQ7GSf;7BL`hi71]Ak;*HKkLeDq:5F+0rGUQ&3n
tnN1N_M31cWU5=8WT;*s]A0Ms6GD*qZ"!M'bRq-f]AF7d!)3-lV.^jLP2f@CZ?>uYSH`ErERlK
(A'5Yg-#TnABjT&RHuj0-<48jl]A8INO)O;_MpF\dLnrNfKBcl5^]Ao]A;kf+SZ[g"<HA-!oa?C
-(RA7$Yp/TQbqRrSd5]Aj_t%"r7BFeT'T+fCS:80oB]At.q-s8Eqq3:/G8C59?cHuKqS,q]AAQ8
&HV.%LqX_BbI()G-qTYRHiBJQ)=ORsCJ"H^&JKo2ck\,R$X@mo+Us"_r:@Qaf))lf;=1><$\
E<Ite)P`^3>=tRk#^]A"3>m4P:E/WIg]AgSB$qDHs8H=(JR3i@]At(kcU*Ul\1HP_#/DS-28r!/
9!ehF.+jpjRWg-+Iko0OEeWoqa8oR7<edBsB?.rh!+!"4X1o&cnnMT=3U41Dg:OKEcn!Sfm_
0L:&s6but_TG@^)(;nnMd!`@N4,D.4)g]AMLf-<DM=&P"(.03"0<H(i@_l*&k;E6P<O\Am<=J
3F:FR<Re8i/t]Ai.->`oNtN@X+c/KqWb,U`-NZW4qPRd@]AR.+<2QkDuL5\0]A=f[!R[\^GW.[+
kr8L;(Nk,-hHl/t9=Aa"tK]A&<:-+2)7r3,71]A#,a1^"*eIB;M3s#SHb=c/SS'r#f<qTr'!()
":Q5Oiru!"19IQ:Cs/c9"IP12dlH.QR'Xl$(mB<8H1V)_//P*A&?!`rR]A&65e_!EnoHK@PUE
$mlVC^R0%bF$(gP=WMa]Ae!,$Bj@"JUp['4%*(`Gbu.mQT=H*$ArWPd"H(fN&m<VH;8c?<leO
D&K7,bbBOoSHL3O%7D1.pV7,+G]A(SO8&T$WKokD.7N;SJc`=+[6ofikX[[`fFRU,NUUTKQ*:
%7@4Xed6O5aki/kBs5m<DkCo'I0@TYte/E]AFItQjL0fe5/T5.cp\_p'PgK1:WR^4;1*orJQ=
MLe[aqIp4i-m:`=)\=VI4$qd.t;#PiMd'k0nWG.OnV<mphk"muLB'RpRlOu9Z>s-fBa,NLKM
-=fA[ro&PqlS%dO-2(C[P&_C&"\1j*4Ye1e4(8Be$H7@h=cTo,q$0iN;;$/5Y.XhY@*Qqb_.
@=-]Ap(qV)ODtDrTg/D.XqE'!#/L%f+lcIdO?_Cf*GMdR&e"k*&S-]AX9))h!3(VMG0J2$DYT8
BLd0:&<.X<_N!\7-,us#eo7LTk5?C0\i/FL%J$M'.(&n`+8IpVJ31([h3i=AmRpj<+B'2%sN
BOJ;p@E<i8cQ+Yp!:QDCc?S\HR*L<CXR:(B4HHINJoK!'fCY"C4KMP,7^-fL4d"M4m>I_H!r
gL*H=Wupr((5aB\<e,$-TfS"2+]AT"Q_=<?C5\bF[X:N1?;d[+u72nT(KT`]Ab':]AeFqu&r_Pc
2B_b&NQLi,bBl=S1YT4:"#crp?3.RDRNeq.V)s*FXaCCR_000@o*T!+%9L/DnT0qFl=Hch(>
/ro5EE"/"7E]A"`Kju3XmIkQL!H[nb,GeF39,;$nXm&TQ4LqSCsPT;gXr48g8X^FA\cS9Dd1'
mraS4K<nV)Sj$YPQ*W]A[(;Jhj]A(&MO,3%XT4X=^kh71sgtG.g6o3k95bY-gY`,e-g2,@!79)
7k433ZIW]AmiQV^-Zh#hr1Z3d%PZJ]Ak2`cR.Giu^:@cK7_m1"4'LhbE&cO+*/;(B/SnoH5o8'
6O-j/Zt5Eb+=6K.UEZqRAKX4JogGUrJQW26sdF4?LDNmYfpI".3F]AW,scf@BKAb-\$9k%sn8
+3oN,hn",#iG;Opb:,4dS@i1pisoJi8t3R]A%0&MV04-iHZ!''KDT)qgXPH)E^="]AFj3/<io*
&USOZ79c1?*B2do>;N>lTdm,L$H(!*=7\\2lc_mKF7+!Ke7*-aKEU_/L$c1iVB2alirf`KlH
Q_o6HdqOSS/>!@G&H;]A?cj$7tHLh"uFo8K%3^f[,H9?hu>cC'Ku#+C/3<iB(MDBKLh%?CNfg
B*P^ESBp9cOI8!H'ue_i$)Lt$$,ldXstIos&K^2VLVJ^L\>TcIjhM!(Yd+P7bIbcdt+W.n#2
hN2<eQNRps9g@_,I;##D\QQW-4e+O0m*BD5aW[`>QVC=QHLoh0F9:L\OL=a@F'r:A?$-8VpX
pgJ]ANQrdP5*/F$-]AGLH$1]A3^S5j<8TC*5Y,"EPjlno0OkK]A9SO?Dk%mp7hi4`Qoib&B\Hb>'
(fj?:1/*>:dO;qcK_pNEM"7CoqHs7_<:/>P%RdT&0M&'8MAA&LB%+>BZdV%<%ZW(Z0#B.a&8
$:P@B%%4,"$(<6:/'8*9n[h#1>_J=RKEq,0Jr6eKOqR/7[<\AV^Y:pkMA:1"V1SC3FTt.m#W
\r$dW!?\XEYu;=ZK:IE8qE-q:4@PWiO@5VOp'B4jKB8>g_kR2FrHl.ZkI=AFZ;$^SCfDC]A;!
stWl"2&]AVQ)<+Z6]At,-P89G&PIPdK[(^OVu>1[A%Z$,pgm]AZ)DdY5L/:F&Qua1[4pdog-4sT
%d;F6f!%U)eXL=H[hAKC_,q$,do=,je*beVh\Z(5jo1-8<hN<t&Ifeu!@[]A;@q'bhqo!Wm@k
$]A(VnjY8A8ic!8a>>9k1GFN@!QEuJgdrgJ=2[hO!"'4n^O!;5`o\M(&POXbhoN[-`DMDo*R8
^JW'U;$&b_"Fl;/j/-G@*g%Dp/YKA32(le4#$$C9I.[5bF;PR1r<,q[=DXA/7r_%/;6#hn/G
19)Kc0G/ta8TsPbUpO,T:[1dak!Q)`(Lm^_$-<HO#3NT"9ON@(0<_iTtDu!84?k#P>mHA$H%
'i3uaNi1*S2@;&A(njot<HJh8R>7%G+RZ^L<8Ck9V8#Gqf-Th*Qg%_Sa@2fl]A*?XPah*8#!D
;*pa->eZqZ6#9)8EC4VMiHd%VoI^=uI/G[<H\DmKG\6MJ*m>h]AJSZnZ3Y0HsCWh/OH.D>EVA
$q9FUdalG`]AGC2m5)Wr;>:jN2Y#B+E<P?pGRo:Vk]AhCgdbV,cr8j_PFDo1)e_s\A-HZKD-g4
c[bX[kcoCi(2bG2l<YKT%hl>'ROafK.i%WL$?.@l2MO">r,L[Z>,6's^GOFA\$;Cq>&fEnV/
d7?qK\Z@Z>F.W'DCp@SdJU[Fm#l1mVK9sJ5?+Ddm[qEbf$IB4jV`Y3FU5Tgm;O-cpk5qbK:5
k%i\<F;_&W>Q[KdnaY'53c^:j@uKR;/V]A]A4_[$0u<iak4,6H.t+8'?glX]A^SVe<lsteceFqC
[Q7tI%L&&-Xrb7>T+SL6"+kh3B)BS\"-2rr&=kUFbA-_A=[=7!Rc-fa=D4pY'5UoIF;tJ@H(
#d^4nA*C[).]A<5BIDOG(+3bA^FJ/0cO.ub!q&<*/%L]A0m<6/F=e]AD8A2oR:@?B4Qn6aMh[DU
nfuf2Q.T9'jP"HC.lRYucS@i%WYU3**^Jg\NPXk)BTp+MIP)OP!9Tu_Sd,;<sph-SO\N"5/X
_G\<7\mUBL_5^Y!$B9,4!AfadM82AR?h%*'!TuU2d'a'H/r!U7Kd1[@IQ9pP.-FWYk@,pP3C
`FRK>Q:BDt':alsi3NE+OHes2jR6WV)r4U.EV4:3iHhk.3eNYskPIcao;j>6c5_fdsk9n1?%
$#'cPebW\o!:R2.lN&f_`juua+%7-cVWYO7c;*HrKt#PDCCiC*fL&U")g15Vo8cT\\tN*SBA
/Y^=in$?FDWr;WA?WHCrZAiOW'Gc&rk9%j,;S.%rXI"_M>VEDcEcf'*Y&@#ce\4UeQa&MV@J
SCFCFWS*M>+Vf3BWZ0?>O=1N41_ZBqQl"9<,pX=dFGN@"bOLLhNe$U*MmuGEC`,/P;LqSja=
#BIj\DT?$)N9q#+\hYUapaeK>lPJ7>G3Q;1fXuY!K4FdMfre&(nj(`do\6C$4Ps*)Mg:aBH@
")"EMb9V2?&:Xb'I&r1i(hU[B7H%>$8gg"DCp9OplLCOEh35F$^gRGtHXg%!bG(E):;0s>%2
Xab>O!F9i,BIWT'Ed/0>la&b\Ufl:937&utp!i2r)d'!hG:=3pe`b$U?5b=3Xm)`>NNKl![p
"W=^jL:tK>P9VH$&:Pjn`*l@Ns""XL_W[SZ!BkU,BUl)!]A+Cq,_GS_tfCJ]A<GqdFlLF4C[/@
=*5T#;IBY86+!f]A>AU0,#oA"iWV/<Z8Y0"OBh]AC9GW8HNA*gD/(JD88Vo[m?$k$+\^!f:e,/
bJ)>-5_d+.]Aa4'Ko;2,_*T4556JFMgAMkJjYHEW@HOZEco?^WQR_,":9&DQ;V`)[BRprKX0m
"GqnAY+]Ah@O)UOL(_+#W+4C*f2KBt4?bCJf#T?7X#2LVbmTfLa5?LS3]ARDIlB*YqZ%uFst"@
p6@jT1d?(t%nJRF,mLe\!$iKcklrr/4IRFBN.(F6%uobJl!_S591h,e:Phk`/n<Wb\n]Au*ng
jUq,@2r$0M1?_B1;K^$V)D?8)o5J^$Mn$X%l;pX@.QFUh/W0l,BfqcB?#1GLHZ-=*EMgG6)-
h-_/r0^e3uaD#O9/AP.3Keq=OcQG%i4E_X_j(N'`&$G5I8Ib3Ka\ZM\Y-&>!G`>t;pHW1aYr
[_`hD?%o\)!4(pB1*RG^*Y<;=YZJlJ!mbp4=1E`Ou@eN]A)>;]A)`%E1Q)1$]A`Up#s*-mcTK)-
UjhO"r9C(ruC5_XWT>N``Jf'qNIY-m1)c\t=R;bIqKg4K!\Mb#0?+SrXJmH>tDF!E3r9su@Z
@JMKV/?SnRn@te@DXk157$`%*:uM'KM1L:2&M;@E]AW*=YcI^Wp^lsuk%9hFj/7uL(a*c-`G9
je.)7A!c<+MNc1ZMu/-g2ak2l)c^^F&i)SIShBDZ#V'aNi,<1qoF+q3/D.b*1NXT2$'Ck20D
agnoQC-Wb;D6Pk^..*VMh]A"s(U<i#`so(<P)F*!?jcY1!P`cn+7F.Oi(DLH,t$uk/A,A)LjC
JZnJ(ILAOc2>/f_=OpE7oArt!;2-`__>!4IP9irUS3I%+MAZ*/;PglIo8lUe>(84;+c>Y8*[
3pM-hI3`9l;B4pCp]A[_'3I&e#FkHkNAId0Ig0YCu:0FGs&'S2V8a(?(PA/tGTJCW?mqI!J4B
'Mio/I*GHg9:<$J?StCZp5@QpCg!T3I:V=cC;$?W^1OnPos:/YYm,^NP8ZCMkt.4dJIcXDFR
\m/!:a[,Y]A@f5Gu1sWPqm89]A8Kt=TD!uW36$^WXAN3>KQ_tH*=A;K'Rpjm&VT?#s"Tb"1pLt
HS32Ogf;#`5_#]A,>!bNL<V9=YYHknuC5Pr%1b?C?*lcj\FQ8Dt?/^Oh\Ooo+X0_%::DA'NaD
g5.d6E"*`dhtqR?opm+%c&Gh0n9;$F8c(6W#ir0UNO-;?p>5@8rd-l?/*V7bN.29brRKrdJE
o,Y@7Q--G*/B)=<6E#Dr@km4!OV]AiC%0anTr8UY=4J4I-E!5;,^Uq]A=kN;X9F\V'`3dJBc@q
X`lE0(2eVKR>g4:o3K0_f(+8FE$r\uH-b#]A+>)<4L#4UZ=:I%QDG7h-C9RZ,Kj++#hSo;*H8
h?0qXa0+>PO*!?j@33Gq$E@flDFRqfi<=C;d+M-lp@=1#d0F-1o2dIu>E4&gV@J6)DStH_mo
$DJ^pK+>^%"]ACgg.JFj#Y?aT99ijY4::sE%4qL<R3k^V%M:K>sVT6]A_Km8'uM>B%fI.!_L,F
4q`RHk!d\LJ-(\9-_7rKm?=7[eHG0X_D8rhm-PH(6^,Tq.L7ak;#\3K+j5GJ(<>W<f"m>N65
NT:_*3DKq8Y3i]A.k#p/NZnrn!D$o+cMg@dgscl8QUs-ub?0g/\)mkR[!tl^YCMh0_q`%KZBT
WToY9d,MY5-/Z?)LdBb/1spe:B3o@7+*_8p3uKpWft/crkuC@pS>:tY-h^/5;s_lV6bCmj`t
U5,>Eu/L1f5@5o]Af>iKYH?<?I#,BY[?0iR_,(Gqm3e1j^U1Yr[@1;q_R<[;B'&,bOlh1WW>t
QWLu+c`uV(Gf@_(1a6r=4_#^KQipMlJ^l%*L6OQINa:>P(]AgIrcPQ'JAKD=rQXfgeZ!=Lp0R
0oLPeVZK^jgqaS35s9j^(O\Vn<U0)eUNl3G72.[Q#b"+:LAVmUCbAd_qE-;/r'&RTN4WgDa0
In8_luj$L4BCp>g#1/"2>IYQ2-1j`'GmYeu;/r:hu`>9W(`UJ@u<hI;3ec(1Zfm#DHnT0"IW
!uc.i7h@5%9FAedMiMP$LA!k:Y"eNE;dHOcg+aI19QGFo3WZOC0PM),@scGiGGS=q!"Rl0#1
2,l3M6C_hX(J*+i8+`52XZ&4m4:]AoHLJ-]A55++7_qZe+mqtMOCG]Ac#4_n^ie^]A?K,W=m.>6f
>TGo_L?#`IMgN+Z)*(VNJ%6op"Pf4j/#IQ8PT_2eaWcR=JCJQJk9HNqKmfL'8,+8mU\_,QW,
FV>>pI`1Xe0P.)ij-@./I.3GLi,]A(aU^b2Cj`sh#:tiDXgJ7ud-aAsU9g/,obdcD4'3G"5_P
&47ZrH=lli,*lWFIYDclfl*A,Ae//gnO>PP8`=U-V@0U&NnUd^Yb9/iIF>UZ^i4`*XbBP^Rq
0<oipqaGb.Ohu:Cj/G%mjn:3I90U'Y@n04E3:C\fZa$>4s(f[R9.G]AFoM6&N<$I41X?cKrCO
ls+<K;ZgeBOP`XEQ=]A9pgcd*I0FgZ<fQ&NIpZm/q'G]ABmff.">X:0$)-!-JWs)/j416Jj3p/
,(Xul6#XZ(\TLZoN:k1"Af18A,9baJ8f"rrt8T,#[R]Ar((O2]A2V0U!"Y]A-SDur`S?$/<;Vnd
@dADX3ED%)q@Tim@pMr_b82U2Repr\02b]A6jFYtEI##0_*+kkF:I*a\FSnMETF*P@n5[g%DL
Wn7/e1@dh(A6aD#DRRM..'or\c_U*)S=WAM%EYqN`nY;C&3!AmNWe'&`9+(`S`fshf`raVEc
]AoW%IZDGD&H?A:F:\@Tk#\MJXU"\8n90+C5[SC0nUT[L^SG_C&7GO_l8NHnBcV!WmBSEnu)S
j/K#i-C0<KNI0*"._M8R#9"^4H*Uqfh$PVeNWl]A+g9(^r`\P1\6U1hUjohd,Q8E<JBakKRIZ
[rr;1@plIuee<Pu=!V6ME.^8h&GB@_#<6O5++Qb4:'F`]AW[SIV1:/E<TK?c]Ae6L!&\RG.EF7
KsAk(UHXio.QJ,L7#,Uj\j%NW^ALBCYfUM&!>u?-C:8W$P0EL/dfie;uR:4*@uJQ*h2K,;1H
a<HqNPeELpu7ZO3;a0TQP/C-HG:0uid`Vf(Z^=d@&O9G7L_2?=\*(\Pf)j.S/s)[DTp,]AN;t
N$0%$KFP_J&^*YsT?H#]A'OpdB2(dL0K'hsfco%Pb\*X3'f#,lRCuoo%6TbA$+*ACM(N+"@R?
8,#*oCs%58N8=A6U8Ch&LktfsF-FCcS^TU1LkB]A+SS[o&D&u6Pcmq^AIa8m1O]Ad=d,<O4Yu'
J^Z*dOe@^VWg%?#Ol.X[$U\%jZ9dUmT[&.Kb]Ac^o)lh_2X$+Wd%aYs,U28Bg\He50-?r)SDa
c47Ad"I'Wo_sEY@U\f^k8%,;\#7EKYe$f8JePeA7BChrr#R:dr*Lt/R#9JiPf6Y*J?dsU5"$
rZ%1Z%i:6KjE?%jD$k)PJi\'o_I/c]A3u+@tiQ:]AHD)jG=H-m1NYhHL.==Jk((DM1=%"99oTK
b'PtDolmFXmlK`"M:FjJc.L`EYd,=7*N)HUcKTEQe\<#60mfB<+7>=<a^g9%0ikFUpcl*Sr)
.2E;<_<6/QRf9RQKpHJ=?`(X8*#,alh6JAPfbZ#1BI26r\cR<hhi33n&T.Pu$W.%kY+;LomS
j;U<D>YZt)RpGMgb:VWI]Af7=k@B_h]AV:QO4!B>T:dY<A_Q+lI?dK&;(sKXD8AAjABnXJs@Q-
;l+6hBV"u(SL0Qm8eZr1rnhRbQhK;b`(M]A<1m%)($Y$q^UIbX->-(\J4Y6#rePR\@'RM-\)X
F0Pu8Hsip^K!#ooV[8TIidpP9OWYP)Y@l>2:uB;,+R^kU^,rfr,5+eR1YP$47_*A<uc"\&*c
cQ9IVG0uF"HoLf$_)!WXEm(]AfensShKCj]AR:,G$HJ)c^grp_K]A%]AI=lL7R">g`XI;@*&^,NM
N[>ppY(ti\A0]A^-6`H5%1Fo`L2qThfq.Ck'5#7^!bDm!tWh,)P2Tq[Zc,/C/o@O039!`\U,E
`Z>u$oE=EJEkW:#@%FRS0>:QLG&qYlm.)o;;9\F2K%L^Uo6TSA\Q=jk]A2R*UE1CN$O\rt9T)
1391``SBh(.!E^`j5K%0`\5[lIe@lR-!o63K+m*Sp?m&YWm>:&f]Au*+D82\XXIYBMS(;u]AS0
@7h`AnCs-EH=C[S3kdQ([ENV@DR5)s,88d/^\`G>6[6BkT"[G'!,rSIf)1iFX?qi(65+=ASn
&>t!4ZZQHE!]A_pfK[BMmPE)$'5Yq#8Bu,.[$CKj9Neo3O!X=U*_6q(IN?1gV9quSf8J^n.#]A
JrJZ"$4<Wh2$WIU1Y]A\uGJW(P.`U`Vsb]A7<B-V6XjH8E54Q#T6+j'*R=[YRp(;2j<#@]AZk6P
TV+jX6<N`qXdNekn=Wp%M,&daHljt68r3Qi1GoJSTp.1u_En2dI_K<>N\tW.]AMblBh)&G,TA
lHgE&=A1L"?t.Q"RCsg0C@lI6ToSkh$In14n=,C,7g_Q4Z`X;0,_r+Kc(H^:+13RQVmX)S8;
ogHsm2pC6m)D_Tjji(!;+gC%Vh@9RB^jgbZ#;6<,%M=t*5q&rKX!J[p2Lh2muA,'`e%>Iti1
!`JlX+gO^\hJtOfYBsjU%IJDQN>'+V4-lp[ic,.p\ITuF5]ANQ6mmjd_Meoj0HZX&o]As3AJ#^
)R5ne,:Ha/b)Nh'aEhCDG7VEi&V1RS![G:1UrWRb$S&<GtR+kf[9e'T$!fnRA#LrkR:3ku/%
fk+<aQlX!Y(7;*tC^&u&Y;a%2$b/,a2Cdt#&`sg%@$#S.m'^-guTRm>>FKbYh32-09>^c:Gg
*Is?i2R1OU$5<:.nc);5^Fde,\#'b.N#MnWb5<'Z+%=t'MXY2XP6P&(;(Poe!f]A7Tn7FS_+F
ItL[Qo3!%6Xs;e`WQdpREcAf-0?"B50U&@>$kqHLa*=73+h*U8)<[b>oan7#00'N"qk8TXKR
=r>F3a(fagOJBlX3Af!2Ut`gglU:=K8?aTqlt-<D7!"/'it:J:52%aR$o_(5`>g;3NmO6_Ke
4P!#MU=JMQRta$#DN39H>]A?h2l\`9tPjXGXFtd]ALj[)#L@UmB^8R,46=Z6<$X^2@bu"-(\!U
kg,N8@8S:!OiufE9(%6f:`4S`HC7:n2W2mYTOku;k,ZiTjUcUNhVY."WcM]AM;nkT'6]A06RC_
*C!7e\CWqenN>m0b+iXZQbJL2303S-?0/_QAS)ekt9["'WAB60\GU9BNF)jSDS&omXbMElg+
KF+,jk2^a,goQg!BcgHo4dOi.`Si&$[Zn*X"/<q7PYbu13X+u?3uN)%7LCh)K@dq4ql.^L+2
h^(@38p#tJ0.hXUL9aDT-(6d$+oN^PDBs,G/lK"/9Mk%5i@WkrCn&[AJk\YV&F/?rAZf;Z(%
B%uU(82QeX^*r6eIS(k=ZI>Rb?eo9m\2M)+_CSh^(mt]AQWL^OSTS:H`X6?1J9glb!M!6U8W:
,#-m)`hORX#nKZ)Q!q4^Lf\!@,G#jHG@fsKbO']AdIJo\=P=*?$%a1).7i8j_pE/CF260MQca
@]AtOMOZk,@(Cd`Yf'$hl;Dng)nf)f9e'hg2%jVT8UTt\j&1T!HgWb#'/7.3eZ3'<P-<Md9a+
XE$Y@:LoGmZans\j-XgQi\Rd]AA^bEZL`K]Aq)s^FDsi`9eUI+StWW"Fdp+LrWK5*&o4%lJ><K
GHD^E;^7_p`]ASYsQHqek4Cj5^phWH`_']A6WViPqfLgepdE(S@A9-`<!Gt)RJ05o45@C&7<*j
`1W[#DFW_qc%p@#'K<eBDnBDS\%$HSirH,?c?cX/G\"4-@qu7E_2,LRn^/]A&m-aK&cIjTW%:
O>+jjtYpTJ#MHdq<ifo:nqUQH-U%Eb$2sa%$@D.Ifn!7j<<#9H^JeQ)>?+'f\q*K]AJDITp[O
3#>bh/\:Lb*%C(I*M:+Pg;7,8pSZfNBp5]A&%c()LWkPLUh,:i34Ol902YE5kA<U&75?:J+F;
+GPb3(0*_)AkrCBo1mu[#-;b')cY1[odfdmQtR&gLt/RM=6)Ddi$b'hm%/@,2]AhHo52DJo_f
k^^IpkGkuhU'^q/S#m2-cU4SD]AS%*9`E"rR9O4i]A\o_>(;5QlA1Y]AF^bbNu]Adcl?U^4?\9G;
P2BAquq0hi<M=0WVFL+jO@tV7[Y71[u`Zc[0q#.GtUZ!tSjr(?N'*^H@@NOQJ(dSJ-dDkALi
(B7+.4I^@$f.6f\]A1@@87HQ"&E-=?X9F=da+;:3:e_Y4B`>Uo@6-2TQ_a'fLm<o*,Gmo%$M9
!]AWEK^\i*oO=/.f;U9C`6)+cSTG2M%,>#3^#u:34!KZN4WjSG]Adb!uCmE@qPId;fgQ:MK]A^=
BVC/H\S`J."iJlhWH<<b*I[cnX]A3'^sQ&DH?89kbH-eH?a#0#2$`=)Zdb\(l_MEZJCEbKA7N
&o>rhl4uEPA=/pA#";7Ham(IGUjF,B:jYM[CDR#;W/[)%ZH!#t%Q5HsU#=%;!n+U.p\h/*,W
\pe/<i55&2G-\W$T!0RiAj?q6/(,lQ?4J7FirMRO<@jbY,`r``TdZJt>/.cN!**2g-VBJM"I
5+QV2s!4,+D,[1\iLh3;>Y1li#3<``AiY5N4F2Nh+:pk@3m6"t(\Z!\6KJR_cJ+>UTm4!E`5
lf%TiW-?ph;rJnfr3L4Qq]AfbMM:J:9@5*u8q.>%aG9;%lDak#r[J/GGBMkMY,U/_,W-,TD^`
B&r)OV`,]A%8C%-?$HN%]A%Vh`YH!WHB!]Ad(`]AYf*..(VI-+U%AC?Q;u7"]Al&KQ/n:O67Pa'5U
MeM(6`*+>31k5,oUut*__Q$<EMAIL>"F4D3b\V]A$+55E,F9q2FaGWjCdHmcXEBb"YBE6RXZ
'ge4_qe=6M^8RII`LY;e7/]AR_XFEK@j)1_X.4<aQ1#F[n":TH[LdX06V=254)-^4EcRoFf(j
"HXlKTN9hkhN31<0#Wk+iVGPHeJ<RXI.3n`/2a^PF@7r*KcBSgbCX:>a05(tJon0q]AU3akt^
Kb9/\fnl-P6[cfEIQW*Eid6-(d.6As'@S=UM@_$VN\0sFm;Ys4S>gVWUYoO-=B$AX84V0iO)
MYi<.FUJE*s;:\_X6:U\Jra-q\qiiekacn>m.WKL-OD'>"6F1^NoQ..98#IHb^n-olBO1G+e
3GGWk35YkCEd,@tH+Q6R.m1AZl37?o#(%b&\n5'%p;i=Y^=i[nWMKDmV_r_]AEgls$=cCTs*o
juPnjWUQjI3_EWW)%"je(Y=aM*REZ\'g(Hf&MLE]AB=j88i5[pW%ks3j_W$+F"[JM;?7]AG4*D
"^SA$]A!?Q`$:5FP(f"S(Tpq\OgRTMUQ>ucO_Z&BgO8[:)\BOqd+0a]AQX`a)dZ=<h0T:EXWR.
%V$Siil[P4$no)0r%V/9&kRc5&bs3!@EfMeQrlag\>trao4"Yh_%E-Pam$bOU0*(!\3L(\9+
9N>/ZHqE:H/liSRtG+HHccT\MbN_1[ua;i]AnbV^:6F7R)s%IN(d.>#qNU0U_0P-%5F@;?Feg
8lHDV->-F[s/L+eoBoqjcLpRsKa)m#g,)6ud?\tc=%j>>6B1'F5mNuN2e<jc$894F&(GkI"]A
Ehi&n=[9Eb@t2FCE`aqUh_fVt(dkDnlQ]Ao"/"!;'=gEgEKh0KU/us4;7B/InJ>\DhU.c)(\o
G)3o5uH\pM3&#]AYm90-1^8K\1$*@%=V#HW4/ql.),Tn4DPOmW<.'-7M+Q<6k&/)@a5C9<uHh
]AEpB7iRGqF$L)B`D(KK#2.g=VQ$@r:\7#ET^.dU\bW1pO;9%3OA?3$;:(6WQ_!g%;5WDqjH-
Zs=f.6p!rp`i[01u(*7^iKoCJB8R71?:3Yl@.;Or/*jX_J:Y\IjT28.W0PG0APTDkmLfd82L
-MqH)a\-I_J]A`!P4PBD<U1MG1-YFMTZt2mV>jAO(7/CYRmRN9->-V1)Jm799Mf/os*lIBj!h
"kab?>R*i@WXomuEocK)?\/<lU$4LDM?nNBC0B*-]A"?##qR<Vk>u'i"pdD<brg=8-;(-/20d
*15Nj'=4N4']A\K3/Ii)V=MS5T#X0l*>rLAC,c%CEu8#*2b?H5)T!SQ;mJ!^I^YA4``>%1F1P
8J!#Un40aYV>c0X=p,Yn91]A9M=i05n?in"Gd6[n.43-?miB_"RJ6;d:]A'W"e<Bt_9i1k_F\f
NjOQBDo!_S[i2uYemA*"?NTRii^)4<EU1"nn<Y;u-<a8B,.+]A2AMnfD-SO:hXY3XP-N9@=qG
gV5>ok/#Xcl>d4o7?u262*SIoa85$54@T\H`g(sCBq$V&Jsfdp9Gt*&(FWp:fdV5W+$C99-p
<_"$g!Nj[_Mm?UTd&dE*6.`_J5.Lk)Tf-(DXIl74P?AAWOn;3M*7VV`*FJdlq4#%'2MjSIt0
^h@Hgb>%*th1;qp`mh^?s&\W3S`+\<)9LH">Q7>Hh?9?_((tJWP\osUBCuYmOq2l(f-dUr$7
BoWm7+nbY(m>In@"52oFb@4B+Uuf)FfW!rX!YDcpCr0c7HBD`QK28_SLTq#J92/m]AHj5#*Bs
)-6H*U-)-D8Ta@Ya7jYi"^SURkDgE37MD?i!014\ULbjY`B7TD"ja@Ob[`^)2A,kRd"Lq;@7
^cfP?l,.t+a%UDDmm(&pB<[o2oSC\JV)*8'q!jJ!oZ>XmEZnCZ:Q"2YKer?"7]ALe2>,Es?oT
#GR>P@u7X1QtkV+Y]AkV"Kf-/R(SC6jsnXQU!OTb!-Z@UnO"uQ;--X-I8TVD,D[pT22n%DsjP
M/c*lAjL/7EB1)X#DC['/.%a`AoM(F)eH4)Fa1g3lE`WEEW]A]A7l8_h.i+"Cn`_;@R"#=&IG&
,g.(.^QlOcBgCLWW3r?U_\SZ'@30?)4[mD/Dfo>ksQV?]A%6,PP;b0X@<JaL1.W#VNd\'$6Lo
AlioDntkmO^)gH5cqi'LVk^.4*"G:N\OQ@d)kOU!&kTa$7*SUt*/kVVJ6A;@5318:J41_Z2R
POH2E@I-fuW_D-$k"5J$p#/!D!9iA49-RujVsEmr.uWajffT0d6F8Bc,B/R]APDdXUc]AOn;oZ
!!K>CcEC^*DI/[:Ke2^uH_6B%LFYT1*>k+E.9gQSK@Vf_L@]Af>PA5lHb<5hbQM9[J1+>5(TX
$d;S3XmUq=#E/g0:alrI<8!7.":%9.%X\ERBbfeeJ;S_0,j=hnud9`;kh6d2pkhng:C&]A5Y]A
R]Al-Xg<W_=!#3I)Q73,OCR0O^@/qTr2nFCfd%)mO6T@19Yb4-_.ZL':k%$q65IQpH4quK+,Q
i0Y[/`(isC+Ro=(q$98!BpT:7thq-e.1=^cGVi@SsV20Q9Q172PWCP6@=?5^1:,p275YNf'M
QP_Pb;Of\6$P@0q1WDlX@N8s7IYJPk[gY=ON3`nf*e$3[:9sZ,D.Jf3mCqddZ:0FXD"N3gmd
cFnrTp%:-N-Yoj=^0ODg+48r>P6H.Lmh(p.FN%bjH4UFXc:^JP_K0:ZGog%XsdRK(.`0L[(&
oEtls,+Bpb1]AVEmG8+tSbd6]A5O^-,XJ5b$T)KdlC=$E<ckgE:mt!F`?nXH>60FasUb&\V]A?0
MabSK)_"dbNCc`."-ZGHD13lgt4$M'M5BOf&N[0g&4JF8[n5>Dj4.JW&5UH<pNo#O;l!CTk4
Qlit$>prTf"'a@Ypi(ll^'p"lt=/a?L'"KrCtG%G>4nIWqX7=<^L-b?aQDRQKFe/tXOii^=5
#t0T@F>PC67b_4nD>aejHc1St]AA"b1Wb%Tpo%UhoHM?CkCP=nC:>l84d^d*!`MXa8a\MMR$a
<9OpH_&L:Hp&n/TH=_r"c==.sSIhA&JBgg_UX:osNL!VmDn'/GU;bO)Tef]A52b(E*c(70@V-
ic&Wq&Oqe=Ki&AN/GI<..H6dS\`YW_1egCO,r'FeNP'Q&5`</=l]A,tGqNlWkfTV]AU$H:6k]Ap
"V,]AG?Mj^k[`oYUW<]A(4OGpoJ`=aHB45U!7%cT]AA0c)n[YA*a>=\EYTfjcn(iCA.la5IBIMU
tME.'J&1DuI7(HB86JEb!eC^lcQ#dk9Fid1#;(MmQ,AUqHS@9rEYkq&PF2ScV(QQ[M"Zr%,p
p7012_[/Co)l</=;MIDL"56pEW8Z'H;_R8?Ht.l4jjG@oNh=)m\PuGpeq'_.Ar.srH4n>.[L
(-rBk]AVs`1S0OV4VT[\W&ASr3/#gi"-DGjuKf$i>ajd\F^p*2VI*[`F&"P%kWS0G=@<W$@%U
7SMR0PEK$oF"crX5GhYo8Q\CrbZX#I\+edMd<hJ`IF.D/cLq(JcHejG?ZK=M<<+Lc`Q,cq6c
bN(R^4)H0R[UDWdY3@GCMH+WD4meNS5)WtA9S?1QA9TRB3<WtW[C1cQ`o;I!gPne<n))&gIl
I#hZOL">Wa_]AG%^j;6TDm$N.Bd)1`bVd'BVJVZ8mDZM:h/EkYs;D)J5l50l/Q-!3&(GC:A(s
N@E;jT_FJ->R&SrGgR!_;).5L[c.[m`a.SNmQ_mFoPJmej$8/?'Vr0X_j[MTB7<&ki+HN`<s
b]A^/*3>rIcV]AQL_ccE&)WE/rg3apbT5_ISbPF'U.p!V<Wgp>?NL$i\X<_br(mS$B!C1W<r"3
JcZ61=&Z=^^D"f_5P_/?-T=MtqiCe/jK%!pbhZUl^o3Ah#q-Y!UVu"`FaBI;s4K#:QnG.0oK
,l%e@\*"[k]AX[-q`Tek=Mtb!rk9bbJ_[1id9\G[WnA\)(HGX;&H:VlJVU<g:92U*dC5&D^3?
Kgb"tZF6<uMmOs]Ah>FHZB:J+OHQXQAp]ABb%eBYYjfbP^/mC?M,uBpsm[aXRQ,\mnU2A!GHa`
.P)5m<,:$gYh]A2#"3+Y-lsANPZ&FZ(N@f?=&7qZ9F)/b5Z\Dh\Y'r:&b<;s+nJDYmK_4BH2]A
i<D=\[PLb8#FZj#QGX>M&J).YoZF2jO7qdJGbU(+PYGKXY4:A3XF?4R;B4c!7-ofIfcSfX:V
"h2\3&pLt*9\7u&Hl0\472H@t]ACCS8/a1X=a,+A:@&dH;'ijkBDKr5t@l4k+PH,8LV+o7,2<
BYd\@`P?JLqTC:D@@X$l?-RY7b8kOK,!kkFJ"M_/%0R@R.F"(k0bRE4gN_HDK?@Rhuri04,_
/:G"/)ZBKJo(kC7+9#%`:.3>/DK>8"GNM.n5*99sJQG]A?b-i\P6)4;-PIDPCS(gDY7hEIWSe
eOcC@)iWc^HYb\F5=`?@VR)=sq$'^T<)^+!ip8G,YdGc_^h`_O.)djj%^EG#lR'#:dG\^*p(
p`)lXN9:j!l=4^o1FN&oD+hNaaef3t=L(YiO>9GKL`UT6>rSS8_Hd2\-u(FDM:Wp&j4tOa'G
FEhRrLX0Mu=UZ,`\J"_HSVFV&jj$[=2/bjH3Uej5tJ"'-+I:GV3fSPmaoEQmY*"V4c_4/-[%
iao=5PY\643OkS\O%c(D%5C\FiWm'%^R%FHn\tMi"p=2\>%@9Ub2JV)=u1\#$il??tg.7\t/
nb-/ftR5J]A.s;(oO$;d2X.>geSb/G\Q\7I!1!j\%/J:kZ78UG1TNNntTF`^PH;6Y*)fYF`PG
ep*1P2qmR,1=tFGd6tSZ"R6^U21Fc[kd'Bc'10K1/IdR[YcdHk'a5U5r/7>b[C^>[VrG/6CT
aj^d=Zj`&(Y'1]ALO("4d?R2l?td3h]A+p_p'3+<E*ci;A>*Q8AjAC!f6?Yf[p<o,`:D-U-?"Y
4g0h#f5gOAUdIN8jj,;H_B=%J^O7Xb\&*QSj:t4g6OFKQ%;M!XhpqGDW'RX`qTu9FmQjT2Nc
bplf)b:*Jhr]A-;raNaV>r_:09]AMRLQ0q`S`nIn[,J#G.M;Fq9JdZW-\f7HE=j:;QJ1V+VH?T
7F0g]A9DpZ_B;Qk-m;=UU(M!#YdPEu>"HI2$9c;O@k]Aai64a7_3@3G#1;fi6U[5s.#;(S/X?-
i74`!(L'm&P8`]AMI19CSPItL149+MW[;1`uc7(A,FedQC'0bo8!W+Ns7SH$(QY:arEO2=#r[
l7#B"+OQ:L-GaeAJQGlf[:@D2q![h.lFmE5!1H!Ir:N[=;K1'uVPgO;@QDbSg:_@W+Li\s-#
[o>Y^W;B4_@*oO5K%X9f]AnA!V.3VK$mLQ-[aS935i96M;N2iZLiJbCX!/1f4o0_>HKBNSaTN
4<Tn+.[`NQ#^VnOAY@Fcuu*MnTQ(CR0\=$mQ%dEfUTeE8A`"ci1-Ej`uB+5X#nu-cO7jCo=b
k=U2M3lkeo33c>ZAj00NA!.^JN'V#%^YegOu3HLMIVnF\>8O;j@:Q(aZeW#hIn9m^?HCS1+/
.@P7"M?-pY?$t&.f1@t!GDFbQY")!n8P?NB1MXFhNG<`9ch]AtbC)U=n'qJbALhS_rD\7M"D3
6U_mX#$jhaDQ0D.EN5S?a6k'$/;p:93KRrET#L4Js,KC>CQ,L&)bpPkBo4(c&SW'R9iXK&mW
BYI*CE\is<ID/<EMAIL>/]A08nUs:JiSl*/D8_C<r+:tp,_5d1E(D"Z1?`&2*q.$j
jSuk#M3ech<hbZYCE_l!cbdhHOiAtgYs0M7PEr[9>']A#)j%.-RG65Fl;\HJRA?"Wjm75W`I>
Iib`^Z%Q_Q<NGs!\%K/oJ('(abK\I=]As#)5!,kq.&WZS&P@Q`332\FlI/97)B[.L,+]A>ZO,4
RB$$OEAZ_7`IB9Qcgu>cS`Q\+OtZ8RUs\YqP_aY#4Cu*P[nCi1]AE5D,hR-;P(VXQ=F))HW<3
i9?gMP:t(Kc'mpiS4b#`hQA`BE/K!6NC72O.,iJjt.g`jHQD=.1LFY6;cbr'qISrBRR5)\.&
[%;X*oAQKnQmc'u/-?ZN%f-u7.q%$$mZ<*FDgNU7QYXQs5AbMR77nZehL6]Ad)BNokt3f?#<6
\sV>>M5+;Jk;'_$eJT$[j2%Icc9L+%>`g!N=lGqdnsRUA1/FsDako6,1(ldg&0fW3F24C1X?
3&`af+DTupk]A9g\%B[Lf@%El69P?TEu'TV<(?K1ur*TUMI:/qG'XMg)DQI[p0[:OUn2Q_>32
e(^U["Ro1qS7&J*<Do1ob(i+AgS9'tCCITs&TU*KbdVN^MUo.^hg5Y[YD00<&tGAF%ojO;]A7
:Eto_f.RjQL7j_Fc2t(PD^lmM:M`+0#LKm6p(,l`?pc%"QAaNgWk=bhf>A<CboJW@WF79>K6
8F@c6,d,M9$m<G,&8fl5UWi3,>k<$1<Reuq]AKi#F52-6SihsIn3GIP(HFP<:5q"g/P2gILc<
@3F*7/?1mSkm0BIita<Nd,d)60Bd)CJC:+=@rY>^5PV[:mFP:,6rmdh<W\H1hGCh_IV_Sh;!
/a["(dqJm[-6F%j^EB4H:uHWY+YKLm/+.D8N\*A(:JacpZNDd9*/%r_9oQ?JNM7Gfl:?dmLb
IO,h@\V)VW"o'nbI#;SD<-KjpRGD61@i.]A"pF8-6jHoR.L5"76k'mY4/]Ajnd5SdmADqsm#ri
/APh+)c;NZ&0CXPNI$\SW;UFn^O6Pah\4E0Ea4@@Fgj^dRTbp$MJCml4#E-<BSh,Xr*Y`/.p
`aoJbK^d7\)J0u3N`l.SO>h5?m'6@`7cnQ<&`DeDLVGFje7,jE3(tC`59<QRP'sg7`3k1BP>
7&mtQHb;fG>CXBcDbD`L[(.dSVXl*Nb=O5#flu?`0J$IB8+)N)LYsr+i@nM2r-8Li(1Ga-CB
.gEn4aeZR@)mO^Y5Gnd23pS%9i`oKoH.(DX.:D..rgZdrQsX#'NA@I^cU1j:u<.4-VN-Aq;]A
`u$c`DGTkt[dWYWY[Dp>`S[!ael%Z;-A7?&X;%dEpA1"@Rsp:7@`r1;V=rJi0@0WI/C-Lh\S
=R<'n\Cd_54F(.r=\X-tJE%1H1D.5N(sAJ-0F]Aq</Hg80`:+;e<FN)2HDlEAqpOUV8!'9=Z%
#q+Hmsi0e,fIB*^K[#c<N4`SWlIb\JB/"On+YWO^5.X&KhZ:sC-"Q(d&&GVO:]A>Ha$mP+[(j
BN@;%om7PlTG0U]AaPo(Q5^>2@m*Y=Y[eF&9A#FCT^h`T#`k27Il*s$,s]Aa8g(a<djg]A16pA&
?j5-fPuXQ?[3L*':dm=kLHDf/8%F8]AJ`fI6HOFaOSabI%Pp:Tu]A.O1bX2QhqF]A+KAdmd"*V<
1h=D$8oZX;O%Knu>'<34Im9-mn=7)N]A8+mEBtc>qLNqNq6fLU'95mMl-6#VC#>M>aQT-l!.,
]AdHB%,e?9:pMo?7k'/hNRULesbGW"_PT$;djkbADQ+B(E7]AJ>\ao")p:0pAFnD$kI,_ad2R4
QYqjB==7co$Wm::+8V.WP0qKFN^'Mp\n[4Kbd,mRBB`]Afjhc<bt]AYTIJl<]Ah2dZb>rB>gj.P
J=Kdf9kmY[G^`a(G'P@B]AW#P`abiYC]AEr>K;X4&:ZOLXEi>e'^6ST0m\4T/r:M@0d?k/ZSCD
tb`hRHNK1-J?RW$A9H*IZ?P]AAp1rL-8XJ^]AsUE=XTC2&]A66o(&gV';`qd(;"=02.#%Xc'Dh,
Cu]AG2`knHiA<ZC3^4uRQ^?!:b9QFlDki5K3=hBK)@j_Wg.;=<8E'^A.H9-?dX30(dM$_n'NU
r=Wc1'tZ=N6a0_-?(a<_uIZX)I.?]ABM/]A;HIJjQQqY.B^k)3^jPOlcGlBO"-C)Mgod?l&>6g
<=ff(Q81^lI)(cqW"/l+i:E<2lJZQi7oYVQ9GmQ7M[=kb\W71BX8c<tU%,.m(cpl_Pq+05P%
TsY?05")e8@13obQjZ2]A6*X]A$pC`1c0t,i_KU@`[fCHoVuQa>I.U@:(.R_!dHj*D_Z%T8h%R
3rq""F9.FdB?H(VitZEZQQ2[V?*aC[ps#7[W;m9]AWt]A>%``'AY7sZ`Zg1LM,;mSk8=($T>H3
o;j=FY0X_1RZ6]A(&rUP1Y&CpP'%UXaHgpL?^Bac[r-Rr%_7p6T*rLNSi,.UIRH*.&Zku.h;f
:gi2"U&CDu)&7Mr3O'paprQhr;-?/UiW8ij\\7IZ*+C\+hP(Mr3O'pauJPLdGP#e%:,!s7B3
=EVQO.lX-LBUu11@pjX'VKa$"W("j_rmXK+ihn)nbVd9)t!!~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="173"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="399" width="375" height="173"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj3=document.getElementById('SM02');
kj3.style.marginTop='15px';
kj3.style.borderRadius = '12px 12px 0px 0px'; 
kj3.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="SM02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="SM02"/>
<WidgetID widgetID="25aede64-a99f-4fd6-a92c-d40fe5b866bd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="SM01_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[分公司访问量Top10]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?lR6'3Q*k91?W:Zk\\1_/5WT&6+`C)1')S-qrq4-qJ"g7:_5ld?$V&,78<;2\_`-N_Z'E=[
YOK"9Bqd,\k8*J4(e[THSuMIIG%@#A5+?0??LD<8DKKqOMuZq;(YAF+9+`/6TK/+TML8_R>6
d#(Uu)Z^7LKo&Oc/N"Q,\G(bPPs.)t9)UA?L:mSh$dWFs0\dtsQ^-D8Mm?+@G5>^q&L>6f$1
2cm+m$ECom%V5KrR3]AD%u%0,S\J\%%Wt(YnmLn=n13IQ.)gsC(Km<Ds8%aPW72F*P*4^5h@I
EnX-^40PJj<75U!.K0oNs]AQF:<42eTDi7N\"9(?chM!:@d.hn0!E=p;o\*mhVGDkQm9AKh,m
KN[!3YR=bG!:C4-P?coNk5Yg(K@>#C_orJg.b5;?3d8uj*&pr/<o\>A)N!b,gLar2Qqqp[jN
H@Z(S>j,H+!\a[CiP%AD;T0rF/k,`W*S'E,ia#s1'1PDgXSN3*#c`FqWYB+Ko>B1MH]An*j%'
GH6:j?>K=-rZrTel6q<B0,NWBQ!-kF%jJe_S31!ApN<V7FP0oeol+n8ZIh8WE9Ogn\DRraRE
Ng.j9Zo@5]AYJNDJ4C6=I4r*^EOt8J:lUJkZr;UL'V%/\X$&k8oQkOMrN_+G01rc2F_29<r0p
/"03bre7J(-;i>H@8#!2bS:\6Jt+%'3YhFS@1F(E@YEf',qhIag0G<afZj*SJ4S^k[XL\Ed1
Jh89OT8TH@#+]Ae3T<_[Gng_D%-ucbG*ZIs:1]AFRn&_t8thk?g9Fi,R2G@=Jl2YY/#@M1bg2f
iIk*Ch$4.2nSaD=1D`M0_U5c=_?I10XRK6oHSC/%r/MlN<(a%Pu8OOP$,(IJqc/qWMrK,r#7
*/eInkg#(\[Vk5,c9QsUihdjfWAReOm=4Bj'k-7#.Ft>uSEC>4ES^G$s_MRdPpnoX04rX4Nc
qah*pB%31PFe>$`obDa.W/_;l>7TEdiBlWMtEp<FU>\oJLnb_%UGAVo7Gf6OB6%,&OL(6"=8
Gd5c8dbLL/+HZp?H"Y=@*69f!d@oHp=k6'>@W*sPi#hSa9'd)m\9=hO*]A:$'AVR^_7IAI[o*
#j$^$*/jI"!DVK`D_R:W:;jF/e[3P<4)&Fc9FZff)0bGd26J]ALo@FR@D*>1?$V"P99dYlO_J
[n^E0LtB/n?pgCH.5nJ<#HVF]AY4LnRg78%D6<DTO,a<&B,oD$b6DnK:?[&+bJDnQZ,=<<>s%
(8ZlsB!*pMbFP>e*a]Amh'X;Jg\`n,NK^Moi\-Ylk-@YV<%basXPqr<&AL*fr=#3m(?O0g)k.
$\F=gnpQeguH5BC(W6_$JEa#:fO"2pLq-6l_P-Q!B^Zg%5SIaC/F;Y449ZL.9Er))gF:m,V4
g=W_R\8TI&bFH[eLR4`)%h[pLt1i9HXaH4p"mKe0g9^h.\c(1Qj6\RaB!kHYMN=s39?-n]A%5
;eZFRSPISKc>4a\>5SNZ$Up$mKSjKDV$"aVZjMuiDA)66rM,bg'c]AWWV&XUg[N*MumRl2[1*
n6#>`R?og.5YrAuDEkF53b:V.Tl@j_LWZRXW"2-tu]AZFIl2[PA*$l9nVc/b"hN5n+Mg4aZQ;
TH$,dh:lH*=hE@Ha`(]A`'%cN+\HlP50.b.*n1r`3dFmh]Ak"nl6CMe_$b9;d"e)m*\F85t]AfN
-=PgC11bMb00"q0_7L0UZ?>!!P##Q\.4CF!m@;k"fI-RYE,4<>T/^dO4:SaeqjK-p[YC/=SB
?ajCSd*/:t!\(l-><S?g8=9/NhkAEc":;9at\M'R\m`IOcDk$4[I:I?)uX3"Sa',@p'gp*u^
3'?J;a'd`Q8LB:6,.f%Mo(sIHG=/'$"ed0fRQ$n4Io91ZbASYUKqK:;HT!jFMDnIr[3FKd9n
.;2]AX!ssb+1+8`V8K\`">&t"oSKoA*>.6rQt]A&kZS6`05cj>D'Z.eq$'9N_(Xj?^O>I,Q_nD
:,An["8^\GuX0aO1N``Z\oXp8te-X[\+WI<DRFK8o/nQX,[c<m.-V0QMd#b1c2P]AN0+fjV!7
obU[AcPi&MUc`gJu1Gd8n@9Z/@)=WBV#ee_s7BRju1.R9$iO=a$&mbWT$`)E'%8&D@Zt([cD
\dQ[8tJlEaV=Sn?u`%;,jSpN+UT[N(pYUG[=Oa#Dc$W5GF1VI[!Xnq2'$>d"p;ND0Uo>aMU<
a_=c,MTA\e]A'j1%1Up=hG(Q1rbG5cbG0c9!WfJ";VBGsn+'^g2,L3Y.HGM@FNHauB45YOjV?
:5R.'b*-p^J9C[Va"7%7uf;//0Ej`_4TIpO^;&<oAMG_mTW%A']A3s4RdsIj"@Ph6%@0)AA4:
2I`uo7[$E$^[8Y/HiRc:B730<+GbU_$^oGb+PeFUQB"g(A'=?fpK+Jp/k80a;AF>+.Yf$SA7
f"[\[L58_Oi`&'\bdM%WE3VG-`U?*Y#UC-]AYF0+orG^_M!4N'%JP9@i8^FTn:d_<A%1Q%`#`
=fql4RKc#+Q]AD0T#.Z0NWmXJf,4q%C.U2u$lQ8uY,+X@`>O<N>Gq6uE>ph5:f&J*"*C1Ifnn
m2iurX5749B-p2g)"tco4"g@"[MfT2/l'd5.IT1N[f%$/dk2C*QQc(]Ac=g=#RP5GB)BrrR=3
Yee[6&KHff&Jn'LaUUN#JK/aIZ1,KDW.t$_[foR$CDdGOVmo*U?:o8q$WPNlJ76Z9O^!MA9!
K!^K*;j-Xb1PNJNYSVARr8&B'%7)#tV&TtIH1r'9Y@gRJ8,Tf]A%Y=B(9j-cu>q5uaphNMtP4
gUeZ:O$o7K:Uh1G1,jl>/p]A8\3bc'MH'#0I19EhUose?"s_%$jY]ASp"5B.e71H>$J=QJOe_/
4K#b+h9C<^R)nX$:I$NR\iR;`gW_G.NH2rQ+=KUZooDSf,X#rgfigk;/9'5kZ]A\-`1"pfldK
\\/#mro&debl38)$]APFkqB7$N%f~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="142" width="375" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('DATE02');
ment.style.borderRadius = '0px 0px 12px 12px'; 
ment.children[0]A.style.borderRadius = '0px 0px 12px 12px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="DATE02"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATE02"/>
<WidgetID widgetID="71d72de9-19dc-4b7d-bf01-a403492f02b7"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="DATA02"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[9791700,0,0,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[13716000,0,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="CC">
<LayoutAttr selectedIndex="0"/>
<ChangeAttr enable="false" changeType="button" timeInterval="5" showArrow="true">
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="PingFangSC-Regular" style="0" size="96">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<buttonColor>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</buttonColor>
<carouselColor>
<FineColor color="-8421505" hor="-1" ver="-1"/>
</carouselColor>
</ChangeAttr>
<Chart name="默认" chartClass="com.fr.plugin.chart.vanchart.VanChart">
<Chart class="com.fr.plugin.chart.vanchart.VanChart">
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="true">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-1118482" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<ChartAttr isJSDraw="true" isStyleGlobal="false"/>
<Title4VanChart>
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-6908266" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[新建图表标题]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="1" size="120">
<foreground>
<FineColor color="-13945534" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="false" position="0"/>
</Title>
<Attr4VanChart useHtml="false" floating="false" x="0.0" y="0.0" limitSize="false" maxHeight="15.0"/>
</Title4VanChart>
<SwitchTitle>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[默认]]></O>
</SwitchTitle>
<Plot class="com.fr.plugin.chart.custom.VanChartCustomPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="1"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="true"/>
<PredefinedStyle themed="false"/>
<ColorList>
<OColor>
<colvalue>
<FineColor color="-152825" hor="-1" ver="-1"/>
</colvalue>
</OColor>
</ColorList>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="normal" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr rotation="-45" alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartCustomPlotAttr customStyle="column_line"/>
<CustomPlotList>
<VanChartPlot class="com.fr.plugin.chart.column.VanChartColumnPlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.chart.base.AttrBorder">
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="3"/>
<newColor autoColor="true" themed="false">
<borderColor>
<FineColor color="-1" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
</Attr>
<Attr class="com.fr.chart.base.AttrAlpha">
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="0"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="false"/>
<PredefinedStyle themed="true"/>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="custom" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr rotation="-45" alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="堆积和坐标轴1">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrSeriesStackAndAxis">
<AttrSeriesStackAndAxis>
<Attr xAxisIndex="0" yAxisIndex="0" stacked="false" percentStacked="false" stackID="堆积和坐标轴1"/>
</AttrSeriesStackAndAxis>
</Attr>
</AttrList>
<Condition class="com.fr.data.condition.ListCondition"/>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
</stackAndAxisCondition>
<VanChartColumnPlotAttr seriesOverlapPercent="20.0" categoryIntervalPercent="20.0" fixedWidth="true" columnWidth="15" filledWithImage="false" isBar="false"/>
</VanChartPlot>
<VanChartPlot class="com.fr.plugin.chart.line.VanChartLinePlot">
<VanChartPlotVersion version="20170715"/>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor/>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isNullValueBreak="true" autoRefreshPerSecond="6" seriesDragEnable="false" plotStyle="0" combinedSize="50.0"/>
<newHotTooltipStyle>
<AttrContents>
<Attr showLine="false" position="1" isWhiteBackground="true" isShowMutiSeries="false" seriesLabel="${VALUE}"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
<PercentFormat>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.##%]]></Format>
</PercentFormat>
</AttrContents>
</newHotTooltipStyle>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name="">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrTooltip">
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="true" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="true" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="2"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.5"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrLine">
<VanAttrLine>
<Attr lineType="solid" lineWidth="2.0" lineStyle="0" nullValueBreak="true"/>
</VanAttrLine>
</Attr>
<Attr class="com.fr.plugin.chart.base.VanChartAttrMarker">
<VanAttrMarker>
<Attr isCommon="true" anchorSize="22.0" markerType="AutoMarker" radius="3.5" width="30.0" height="30.0"/>
<Background name="NullBackground"/>
</VanAttrMarker>
</Attr>
</AttrList>
</ConditionAttr>
</DefaultAttr>
</ConditionCollection>
<Legend4VanChart>
<Legend>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-3355444" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr position="4" visible="true" themed="true"/>
<FRFont name="Microsoft YaHei" style="0" size="88">
<foreground>
<FineColor color="-10066330" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Legend>
<Attr4VanChart floating="false" x="0.0" y="0.0" layout="aligned" customSize="false" maxHeight="30.0" isHighlight="true"/>
</Legend4VanChart>
<DataSheet>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="true">
<borderColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<Attr isVisible="false" themed="true"/>
<FRFont name="WenQuanYi Micro Hei" style="0" size="80">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##]]></Format>
</DataSheet>
<DataProcessor class="com.fr.base.chart.chartdata.model.NormalDataModel"/>
<newPlotFillStyle>
<AttrFillStyle>
<AFStyle colorStyle="0"/>
<FillStyleName fillStyleName=""/>
<isCustomFillStyle isCustomFillStyle="false"/>
<PredefinedStyle themed="true"/>
</AttrFillStyle>
</newPlotFillStyle>
<VanChartPlotAttr isAxisRotation="false" categoryNum="1"/>
<GradientStyle>
<Attr gradientType="gradual">
<startColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</startColor>
<endColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</endColor>
</Attr>
</GradientStyle>
<VanChartRectanglePlotAttr vanChartPlotType="custom" isDefaultIntervalBackground="true"/>
<XAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="1" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="3"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr rotation="-45" alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="2" secTickLine="0" axisName="X轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
</VanChartAxis>
</XAxisList>
<YAxisList>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true" mainGridPredefinedStyle="true">
<mainGridColor>
<FineColor color="-1578256" hor="-1" ver="-1"/>
</mainGridColor>
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="2"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="false">
<FRFont name="黑体" style="0" size="64">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="dashed"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
<VanChartAxis class="com.fr.plugin.chart.attr.axis.VanChartValueAxis">
<Title>
<GI>
<AttrBackground>
<Background name="NullBackground"/>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="0" isRoundBorder="false" roundRadius="0"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-16777216" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="1.0"/>
</AttrAlpha>
</GI>
<O>
<![CDATA[]]></O>
<TextAttr>
<Attr rotation="-90" alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<TitleVisible value="true" position="0"/>
</Title>
<newAxisAttr isShowAxisLabel="true"/>
<AxisLineStyle AxisStyle="0" MainGridStyle="1"/>
<newLineColor themed="true">
<lineColor>
<FineColor color="-2171168" hor="-1" ver="-1"/>
</lineColor>
</newLineColor>
<AxisPosition value="4"/>
<TickLine201106 type="2" secType="0"/>
<ArrowShow arrowShow="false"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
<AxisRange/>
<AxisUnit201106 isCustomMainUnit="false" isCustomSecUnit="false" mainUnit="=0" secUnit="=0"/>
<ZoomAxisAttr isZoom="false"/>
<axisReversed axisReversed="false"/>
<VanChartAxisAttr mainTickLine="0" secTickLine="0" axisName="Y轴2" titleUseHtml="false" labelDisplay="interval" autoLabelGap="true" limitSize="false" maxHeight="15.0" commonValueFormat="true" isRotation="false" isShowAxisTitle="false" displayMode="0" gridLineType="NONE"/>
<HtmlLabel customText="function(){ return this; }" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
<alertList/>
<styleList>
<VanChartAxisLabelStyle class="com.fr.plugin.chart.attr.axis.VanChartAxisLabelStyle">
<VanChartAxisLabelStyleAttr showLabel="true" labelDisplay="interval" autoLabelGap="true"/>
<TextAttr>
<Attr alignText="0" themed="true">
<FRFont name="WenQuanYi Micro Hei" style="0" size="88">
<foreground>
<FineColor color="-8747891" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</Attr>
</TextAttr>
<AxisLabelCount value="=0"/>
</VanChartAxisLabelStyle>
</styleList>
<customBackgroundList/>
<VanChartValueAxisAttr isLog="false" valueStyle="false" baseLog="=10"/>
<ds>
<RadarYAxisTableDefinition>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
<attr/>
</RadarYAxisTableDefinition>
</ds>
</VanChartAxis>
</YAxisList>
<stackAndAxisCondition>
<ConditionCollection>
<DefaultAttr class="com.fr.chart.chartglyph.ConditionAttr">
<ConditionAttr name=""/>
</DefaultAttr>
<ConditionAttrList>
<List index="0">
<ConditionAttr name="堆积和坐标轴1">
<AttrList>
<Attr class="com.fr.plugin.chart.base.AttrSeriesStackAndAxis">
<AttrSeriesStackAndAxis>
<Attr xAxisIndex="0" yAxisIndex="1" stacked="false" percentStacked="false" stackID="堆积和坐标轴1"/>
</AttrSeriesStackAndAxis>
</Attr>
</AttrList>
<Condition class="com.fr.data.condition.ListCondition"/>
</ConditionAttr>
</List>
</ConditionAttrList>
</ConditionCollection>
</stackAndAxisCondition>
</VanChartPlot>
</CustomPlotList>
</Plot>
<ChartDefinition>
<CustomDefinition>
<DefinitionMapList>
<DefinitionMap key="column">
<NormalReportDataDefinition>
<Series>
<SeriesDefinition>
<SeriesName>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="访问量"]]></Attributes>
</O>
</SeriesName>
<SeriesValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=B3]]></Attributes>
</O>
</SeriesValue>
</SeriesDefinition>
</Series>
<Category>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=A3]]></Attributes>
</O>
</Category>
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
</NormalReportDataDefinition>
</DefinitionMap>
<DefinitionMap key="line">
<OneValueCDDefinition function="com.fr.data.util.function.SumFunction">
<Top topCate="-1" topValue="-1" isDiscardOtherCate="false" isDiscardOtherSeries="false" isDiscardNullCate="false" isDiscardNullSeries="false"/>
</OneValueCDDefinition>
</DefinitionMap>
</DefinitionMapList>
</CustomDefinition>
</ChartDefinition>
</Chart>
<UUID uuid="32522b8b-58de-4cb9-b47c-db428b7cc73c"/>
<tools hidden="true" sort="false" export="true" fullScreen="true"/>
<VanChartZoom>
<zoomAttr zoomVisible="false" zoomGesture="true" zoomResize="true" zoomType="xy" controlType="zoom" categoryNum="8" scaling="0.3"/>
<from>
<![CDATA[]]></from>
<to>
<![CDATA[]]></to>
</VanChartZoom>
<refreshMoreLabel>
<attr moreLabel="false" autoTooltip="true"/>
<AttrTooltip>
<Attr enable="true" duration="4" followMouse="false" showMutiSeries="false" isCustom="false"/>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<AttrToolTipContent>
<TextAttr>
<Attr alignText="0" themed="false"/>
</TextAttr>
<richText class="com.fr.plugin.chart.base.AttrTooltipRichText">
<AttrTooltipRichText>
<Attr content="" isAuto="true" initParamsContent=""/>
<params>
<![CDATA[{}]]></params>
</AttrTooltipRichText>
</richText>
<richTextValue class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</richTextValue>
<richTextPercent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</richTextPercent>
<richTextCategory class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="false"/>
</AttrToolTipCategoryFormat>
</richTextCategory>
<richTextSeries class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="false"/>
</AttrTooltipSeriesFormat>
</richTextSeries>
<richTextChangedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</richTextChangedPercent>
<richTextChangedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="false"/>
</AttrTooltipChangedValueFormat>
</richTextChangedValue>
<TableFieldCollection/>
<Attr isCommon="true" isCustom="false" isRichText="false" richTextAlign="left" showAllSeries="false"/>
<value class="com.fr.plugin.chart.base.format.AttrTooltipValueFormat">
<AttrTooltipValueFormat>
<Attr enable="true"/>
</AttrTooltipValueFormat>
</value>
<percent class="com.fr.plugin.chart.base.format.AttrTooltipPercentFormat">
<AttrTooltipPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipPercentFormat>
</percent>
<category class="com.fr.plugin.chart.base.format.AttrTooltipCategoryFormat">
<AttrToolTipCategoryFormat>
<Attr enable="true"/>
</AttrToolTipCategoryFormat>
</category>
<series class="com.fr.plugin.chart.base.format.AttrTooltipSeriesFormat">
<AttrTooltipSeriesFormat>
<Attr enable="true"/>
</AttrTooltipSeriesFormat>
</series>
<changedPercent class="com.fr.plugin.chart.base.format.AttrTooltipChangedPercentFormat">
<AttrTooltipChangedPercentFormat>
<Attr enable="false"/>
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#.##%]]></Format>
</AttrTooltipChangedPercentFormat>
</changedPercent>
<changedValue class="com.fr.plugin.chart.base.format.AttrTooltipChangedValueFormat">
<AttrTooltipChangedValueFormat>
<Attr enable="true"/>
</AttrTooltipChangedValueFormat>
</changedValue>
<HtmlLabel customText="" useHtml="false" isCustomWidth="false" isCustomHeight="false" width="50" height="50"/>
</AttrToolTipContent>
<GI>
<AttrBackground>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Attr gradientType="normal" shadow="false" autoBackground="false" themed="false">
<gradientStartColor>
<FineColor color="-12146441" hor="-1" ver="-1"/>
</gradientStartColor>
<gradientEndColor>
<FineColor color="-9378161" hor="-1" ver="-1"/>
</gradientEndColor>
</Attr>
</AttrBackground>
<AttrBorder>
<Attr lineStyle="1" isRoundBorder="false" roundRadius="4"/>
<newColor autoColor="false" themed="false">
<borderColor>
<FineColor color="-15395563" hor="-1" ver="-1"/>
</borderColor>
</newColor>
</AttrBorder>
<AttrAlpha>
<Attr alpha="0.8"/>
</AttrAlpha>
</GI>
</AttrTooltip>
</refreshMoreLabel>
<ThemeAttr>
<Attr darkTheme="false"/>
</ThemeAttr>
</Chart>
</O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="2" s="0">
<O t="DSColumn">
<Attributes dsName="访问统计" columnName="RQ"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="0">
<O t="DSColumn">
<Attributes dsName="访问统计" columnName="CKCS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCRX#Pl:VHhL/J4Te`IGQ-)X2Z":%!@u:hT,,S6HZ=P,t'G4iC[gFG*h+U@+(rd2OU9[I&br
TASlWD/c=j6fH&`*3Yp\B3_pRL^0F\ZSP3nNtG?*EGaHp4-_kOOMEBEf4),.V:@\of4[cP]Ao
h>IP1gpq'*'p`G,1Y8Z#p]ADhPM__[V\;lBK1rc9[L`^#VPKHAmq;0lon?*@1:O%f;Y\]AT/_S
uASC@<eU\+6A5+gGC;8%R:5*iBpO)\<D:"S8:t:*\uDSp9a'\jK8#4[YQh=0a2RpE:JrO@I(
6YgtX/S?Uq3HL_ff-pe&]AQA7nL^U35JK\,i^GEeYHR)/Eq-i5T)HW:icb$Df=5ld4K?(h<3k
i,pfYhJkIS0=j;r+UjmX0OEq=@K?Q0rFeFl:LEUp9==(q+.H*IX-a\<064dNgn04UaDJUXP0
iZ2*&?1Jq.&6lI=#tle*BY]Ak;tUt5fa%-E?k-b4V</QqUL\'K^'u!e:6@'mY[oco]A@kiqNIO
9AUX>3X:0.hSf<O'!K[-(2@ImhRu?b4N.#N\LKA`+>j57.lQ&&/P3g3?DMa@k_<JCZA@Dm>r
o<SsK;@VjLs@Qb4ni9kf/8`3fB;%R9.N5`TObc]AJJH-:>D=%sT09's_9A:LB;(3F[jK&1HH<
%nA5A%_fkmWk>TKA;A$#KV+#GB_CVEdBk?gQQ;"C^Cr;-'uRYKsoG<tX=eJ^M>ee<5J9/@#'
-fC52X/(4pML5`[G=%5H8JJ"$SLTn=I/+7tCEE)@5&r.YCD)#2h0C%(QBu[Q%j)kd$p&riq;
0@eP_I)/"fCkpIKXN+hbidHO$C16\!#L)P#^j[LNJ=OER*.KZ1h)tbU-^]A,J"NM1&R(FS?ZO
KpMH_uU!-hdjXe:^Wi*o;Tp1+!6HG7U+XAD$OA:X!;#G'ic\7?T-eSkJ\W4cMlm5:l$XeO,7
^X"H:VjLYRcjJ#.m<?[QCrQ3ZpLZ\CZ]Akofgq"*i/KZ9E7,[FP.HR\0r34Xdb$od$>b)BA*j
`LYnV%4n%\adFloge0^2&9Vfc(mDCm'7J#aT!Y^YE>djGPUmYQsCpAV?J$L+IuK'f2^rM"di
os;CcYA^MEX]A8(jr0%l4HXokB9_t`am>sQ6[gP'3KXiZ)SGmZ;HGW!cDEm7H07/,R%nhMF;H
*2#)&Tl*7>IjV!nt/%h7[%.Hcm9+O3?$-\\<3#?^N2Al%<V7)lXXSY\U-4E.3)elD(bZd$t8
dMi:D7Vu>]A.)m*ClZ$$)u]A>7^c]Ai`fP$6$B)?\'Cr?!LPMQ'&7N'0+K#$7l_pHtYohGG3O<4
B7s;oc;>EUN('!3qC5Ai)CH_>6qoW?_ig>^fnLD<)IIeR3aaUBH_JD?=*LIG^/H9J/H[$VF$
7]AFT([]AaI5;%LcY8[B]AI@L<tu*/f]A?>j953r7.>Pi[>l)!R2%W/S/=o'dnEQL%@t]A-o[%8qF
p&4W"b0pP3EQ*7E[Ri.]AOr;m?V-glm-AB*21[$!RRu;fGG\HO>*c]Aj!f/iTM*]Ai[:c8;gIbE
P0L0):ori)AC'+#l_oM#(4]A!B(i`:UD_Q3bbX-9[JTQ4CCL-3Ng'9DMW\<BH0gWI&qst]AU8P
46++l+[<$NS]AYKPoYS8mTIFBCT-Mh_AO3&cqOR$8hP3L!aGpWm:@J`["qVO_<T=/W6cN&JqZ
Fuu-=N[W'i`q[YKfURl:46CB:oB)M1efU9S!cn\?Jj<?JYGZP-'%6q>os<XV\Dh52MgFYH1X
?XB>CIN9$*/iGF@;VT43bW<;J]A;S^E!A2:9`@ont7YYeX*)K@*7:gLf(=3W[^Q]AcB!(iabg"
E0e5>?CWQHf,b'g8A;^-*V_[hFj.k=0S<$%O\8%31OH+sGGNZtVnHtf,C+q>iuoST>koa_DY
XX0CtQKo\$.GmTkW[ckRiALr#P-J(8U+Pr.,Jl6pa.KKH@0\OtQK^(+8k[hPF%:,s78-V.gA
`*?A]A55BBI%qh0<Xp-86/SW<8(9:M`/&PL8aA'Q&Ml_1R7'FUAgV3q8Y,C)NeYMO4HVek$$5
K3sH1cJl906%3oq6RtZ6<W\P3Jtg<:<,IcZ%Q'Tb9(Vmc8$TQ(*Tr+^91^]A9V+W0e9jn^_sN
u:jY40"R6Als1q>mqUEqJQE(@W3WSY&Ob^_JGP@B:!C):O>="U`pbV]Apa^Kt+^h+YtTYm$HY
9gP63C6mGeO0Mk@.Rrce<7aC\6>OZpog>iTZ.N>W3D<=(@tKi)/I'2ZW2U@K<pZ^Z&Ge%'h[
?*ai+:H!CC9\e"JVkXY`HTu:N(ORW:1U<*Z-u1,Za?af)XA@AgDaY5jP1=??j.cn_(8R6^.Q
Jn`_4;3#Cs/1[<1Ua*desPu6j]A]A_In*j=\oBoDq&]AK4cfoRIeFY+[jRf:e*A8QbDV>=X8Pg4
,X3qBSj\['M7rbe^LDEVn/^.qIQ-SR:0.CZC=cc/TVGmB,"</FGS?%rLe!KRFQq3s7q'Iks:
s1WSsf9no"9-OOr8%o9Y^t[`<O6BV'f=A2AJNclT#NnI'6%/KtG>93t#Iqnma2?n,X?Q$+j5
oI5&Q<]Ar9uC6G9PVen-TrpBAc0>,SHf6aI36nW-/+%2AB-DN\9(V[:WGt*rTS%T@pI'`]A7;O
%KYB'@9>NNN6De%U$[OBJt^P`21l_3b.PR%_PKh%mUQIUcCdAu9CLijk*p^OS&Z9B`G-eF8k
H*r+AI2M<d_$0D3tT@c(4ei'IGE[#/Bqm\5f@*0a\6A5R!r=m!A`4c:,Jr\$:0ZEi?L"3ZT\
uDk&!B</)EOT\5s*JUgj%TsM+f3OM["L>XDS+cUo#WkJ%)$6XH1[^ZEJ.$CctRjO,KNCMNP$
<Mkoq\Unea<h(+BI(M!Ffl>-;P(L#qc.>8P\7'ZL+%Jr=dYSR&V8FZro>I##0SR8rDDHoF`_
5RVePjE.PY&N(Hm<Xm"&-Ne^WiT(*!X;:>n&fc$J7Q%pg>5aBl=SUKOg_bN`F>43,*h.')ia
@BuSTDf;GHNAOBK/aunh']ANpko9Q`.'q#0!n=b`<@[abF-OZ76,NQIQjtOTec.l;7JPVOdL!
NSo^T,FK7T#/\KcBlF%1I5#lb:ptbpB@$$"/J+]AdO'XOrScE1+?>VaLt/NJQ+b0hDi[k%P&<
e9HL)oR*q)1#$"ZBrUVf@X*:20]A1<l^%(Ih.n,(!sY'<,#uQnUn2`#0CV3PLj>(-GdfZ^-g0
rr=C$'O"h`-d[#pUID#E/L9AuY^^/T+[r[]AYYJ>8TDrX7Y@gLe#)'fC8"`Cp[O-a_dq!Ds//
r@lA-$+Fr#G+6M[81dIQ)VcfXNjKleEbk.E=&_n6bE(+b"KOQ<A?b:jO05Y"h@$Q2.46Q/^8
[H-]A3ScG_'AS;WFp!&2fZN@K`%LhI&jKhq;>b20+kCuDA9gKbM7Ku=!(:A8C51'C$1)1j-%M
[-Bp_tAc/KlYVCJU(;I[,F;C'h$5oilF"FY;%6mPn"F'Xc,O[,DX.;C#^ZF9Q0&ufYA1n?Df
331Bc%$&S"DmHu`2&,[F]A3VN";fK8WQ\.LPdcfUPTWO^MQh$%V=-'UPcG([bFTM3.VB#1ql[
s$9P5nF9o\#0UYru#l"*ZY?(sE1i7'iFVS?P[a'^&.QUmFmeP]ALOaNOLi\&&Bq]A]AT(3<uYgU
Wc</\6cZpJePEL!=BmLJrIm8(_mCXgZ_'I8B/TDfd(UQ&CMRNt/p=p#W+/N#X_FkZ%,\U<>(
A-6i>%<[IdDk0>!j;"qSbj&7]Aj4-Q?-\p,.Ab3XJ8[%qKT(8CM7gF&:#InDQCn7Ma\4dZ9JO
-Vh9/M>@j=2\;Y*h1\>O08h!m@V<(jOH[(7a#,\Up[lum7+T+2J17VMZESBkmna&gLU(cnO?
)6&YGH40n=6!X"(rgES1*ipes66/=B\_#5`7@dI&VI1[CJ.P3h\L-Q4(^J_2]A.$B&3D?D&MT
=Be7o+%gjNjq)j9q,mWOB<%?A[s=,Ha?BB=`]A`m8g1^4"A=c$kDmo%K)-+:PN(!f<?eH0:R\
#_AiFJV5!3"2\5W^Z`kR_DWE=[AP2$lp1(sQU%FiCY[/(m@E+,[lfu\mg/8B%9XukKQ1*D+V
6SEQbNX%I'QAoP.]AqU<Xple3/N8\r5,4GBJF+.;B_=@d`0e_Z\sphb:mRi^.\T*=:qh5#mOE
_!(;\Wh0LnudMB%3'GW`F,Mj3mr_#Fh)\+?ieYF<.3pPKLH3nXFD7W&HgAN\lY;Eu<&Pc&F2
4NCllq;BOeT"Tu6]Al';\G.`1$)9>Wb<jLWd*W@#]Ak-`O$SJ"`f!&AE)%^BgY4)9,#T^[s+L'
)]Amk0k+&D>p=Q-O!sn,lp_p$kDd#Ac$`!bg<SVqmjH`RUJ>UQFa^W_tim'tS53IS:;1?<5(:
lbC(3g4.q6Hb+1M0rlb\BLsqn<\rpX*<sfkV;LV^b9:6_BL(/R$\H[(nLnkO(!,00NG@36k>
4X8%Op58g?J+642d;j0FutVK:AXYFY22N=H=S>]A(V@6@,plP<at$oE!9YR-[iPNajn]AEC^7'
!B(J&5?%[XSdFrTnN0IDS5Lq8V$oT'Wh%TCE64nml5a8h%@VQhl*f^[WkY!f#_3VWe_HeUZ`
2V)\ma2r"WB,QTmb/<$\IfL!@2a:@^h<B")Qh_m]A=ZpZrTea4$r[0aVsOd*5g$2n1W<W^:]A@
TH1\RABX=eA]A0dJOXl"q&6J$ou@)7`sP>eG^)?!-,gCGAUZkfip?U&&.;/Zu7#4"J<";\]A:n
'"k3iADT2)Y`+K]A]AQmfgcG(U]A"pa%gVs,hSc<?I'PY:2j(-Lo&M!HG"#;U?jWib\CrK'8Bi;
s#oraTiqr42VpmKP)nqJ>mG(?*j#K.6&'YY&^kd$PbG\k<ZkoSDi#,ZLP9$E9&n&P,lAO8?_
-HBkJTJk_g1QHD&qkjd\Folkn.=`AYBD(Te/\l@?Vh/Xi;e.8b`[C]A5mVa9b5gU/lYl;.g\-
ODsq%tg&SG"<]A)#L_X9nsGf#8?RW]A1B7&.Krd%HD^ElPX+#Z:YV033X%\4$bA1SNTggR"F9j
H^&2Ib5I3863P32oL-7(Jck!_l<C[a^-GdmeeF+IsiB@SQ:(n7^d"[V3J=&kTpq/b8R=eCPs
&_/s$j39A1p!pj%Ol)/H<RE*q%KkHtH8O+YJckK4Xic14)36e3PQf"5T7aa_f6Wat8S]A?s3P
mYTgNa6X@WCoBUqK'sd=*u^79j8"C4t]AYJfCg^j'=@8AFN5f4[U!Ybl6HRfVb-2e*3?ir+-A
\g/:!n7<cjJ.2<qmp26)#$E=fB^?sc?q=rA1?Hjkk0`Ou.2l&doAN\(>)9qsClpuBE0B4@+&
VJH=ihukQ2]ArUMlSRej*Dq`@fb8Wjo9YTXAh)GV5A@(IA1rmJj7PtMfB8V-n&rgBFE?nI^3h
6km0rsB3T',%!td1`V*)G9'`nLreXD.8R9`\Kpp3TP\!e/T4E@hlYTk`0hu-t>AVupm,s2_\
2a>(`B77[bb&POc^!p=KFd]AVHTf1K8?PN_f-j7C?0K;qRIg3,8\__F.mM?'f>GkK0nT@h&dd
kH%M=R-g%ri\a"W3#W9X>rn!cP:t3UqXA>aK^K)B]As%YA,/>eHgU*+47.5IofqR.a;nCQt58
tF*+I5=^)-?_>iZO0*OG/'K-abCCJV:lWaTL;[@YUPS"s3:C6TXg"S^IS:7doF*iD7gYc2/(
''9gca8$#!WX7UrG!3*3g;on[,HfcC+s(>0YXp325pBIPq0eXkH&&24Q':%6j?OmDL9h1"Cf
=)af"hufACfn%+1>%Pt8-9aAVF_1MSEY"J@e1iUlk;%jhgoisjh]AiPSNP?K[Y$na1ee@m<Xb
rbFq.\Z)[G9rK\f5c.Ts:JJF?.j&G7c(8+8#LT`'\jKe8eWT8^hn;(`4gWjY:3)9>AT"&X;C
jH:C-:D/a53>N)f/geVJQ)6*?tT's,/jQ`&HNHDOK[:2L,^UJ@N<[Bp(hp22<-7s(#TFXHJ$
jNU(0OlKTuEL9PR^e!^(ik`\ar>Ip98kfIA+M96-LEg-8&2)5E(qU&1c=*dL4Vs<\U]A%0C&;
HAtK=:WRb+W'=JSC>A_@On;Ikt`7]ArAtF"^aXA3X3568-61*K.5G+Hn6'BW'O;ZEHd,/7*aC
<X3l]A`(mG3?g,lDej%7^]A6QW[45LSC2X_a=G%%bO-(Q*]Ald7;`V:5\@E+^*oF@a/WH@qN8J7
*31"#pd&PSLF2WOA+74@GYlk%B6hHrY*'froI"p`bh]A<UdVD&,GmU>tVb;Vr#"tOS^@PW.p2
f(\Z<V^1XW2$RI*:fN3_&lJ]Arj'0ju,!o%B#RnVU"uu3BrIo>DR\iilCpE8<EDc2Z.\Z4+JI
3Wf(p5XZb$7H,";KWZ@#hcfZ:k?t!Y0U2UF:rOopih`AnS9t(Ws>AA('=#DZjO"AF]A2opE;6
Y+0N5qtTcb>m*e(r3SUUF)r+8]Ad((L)d[<\sK2^2<m[+#4<K)Pa&GC_['-LU"?UioaooL[RT
]A.@h[+YkJtcd!2S^TZH[s7kUbcfN0&oC3n,EBcG0X4'Ve7_#jC>@n]AeVGO3cmpV9i=c+Xl92
Z$")B)b`qcIPTi')/lhTh;GR[)HU"1gQ9_i)=\[3(q!S6q.E&SW[09u.G7X"-iQgcf#iM91#
CX"m:CdIRK&k@nThiZJl2g*[$`&On1LEZ#h:ts2\r%M']A%EP`BM/NZM8)IVWJ=N3N\+gB.j`
h538f1kNBtjPOJJ(N`!=jI5'Y\>M.-E>jWU+22\Vt1t9:kr:Vo\2/Uao*Atf"Bc]A5s')BH@[
e18p4,$u=Tl^0m#n8Q=re?j6W!=6t+J.:5VRglpDJ7-N.*]A3&A($3:Zt&E:<JGC)i]AYZnQFp
fpG_TQb+WYm)iU2VuAfn?bc5`bOa[[iGhNnt[qA[)pF/?<EMAIL>:"\fr>nWI,Xd?E""
YL8H'MSss\3<u"ek@a'*QO=)[/MY<Ke8M\bJ%0"fOf9@.:es(t&E#DC1i//p8M!WO"5e;/RP
09+Md,)4:.G(\0j]Aur*M[R4Y(Ze!^!Iu3[C&i3^rBY_X$j#E%/"PMTTl;6XMsS?BR?R9)Wrn
jOt\X7HDU4R(NaJ0\%V_!N&=<#knVf9VQO0VQ6A9Wi(l,`&r3MbhdZoNYQ`OLI@l%3WN!qqH
&1;.%CjbcbS)i"\RM98iXP*dqU@[CC[FZ"?@%LN->@oe;1hf=3tl*:s)0dIHNB@Le=!^+e@7
tRF]A+XlpV&bb"I$dJ&l6:^)>$b]AdaRTNl!B[Z^XqjCq]AG[6re).V':GDQ1$;;.pTkIDV/-;$
l3lKH!h7P^B_V--I_j'aW3#AY#7NVbT$fgRS6L!-`&e<=^IgBJQU&^iQD&4T:8bijb,+9d1<
B("`Ohu!2#WN)+d8p'nlCXU?9-unEnQE&N3Y_SY+u3OLlJ_fI^/7]ABO(e[7OZsUB'PF=PWK=
AgEFDaW>t35r1rL5h_qK"Ss)4MXIt6?.%iYfOXp=b*e",^Ad-B8J">`8iK"t7$Z4^h)1BUWJ
A>-Al=IL5_!8Q)N!Vj2GU$7"AZJ[@!DBSpZHS`p<UD\om`C^;^qjaK?oa?+rB^]A-!f[<U<lc
:cE"7#pg(&[q<s.RI4.tAN%?q="`XjtDIi^qj(aKol-jd#p-UGg\-QVRPEPDE<AP5ZW.h;0[
HfZl9S;KVQ4]A_%QBXCBMB*[qVdh@-q+op=Nm:,Y:&`iYqR>/[=)/(dYg`fed@!ahG,c8@?fh
CA&D%uDleF$iH%K8CCK/^p'Zh<FNo>ebh1PC_m/OSaX7G,o^]A_m3q@cm$71;4JUZuD]A>Oqn1
N29[Y+7>&3&XQ@i$b^)"o#M&XLg33cbO.u/<k@81jUC2U+Nd^iaQu"@`_VOW_h`cWMEdj$/q
(rF[l'@.#5&21X!n]A>\KZ`bUKh(.$Yc@Uo<uhiN^&''<Z2je017g;d>k*Bo-Bdo/g;f^u+P4
Bli_.EoNF]A3a>\]AApH+XZOc]AN95efuuPgR"&FW>]Ae(TOhJ2!!,26F0E7flIJAGPI^6i'kU)l
(W3Oh@_r>2mY9f\YF+<N\^R+/A,E%X_Q]ALMnh5!p)]Ar2oQDXJ%7o,h9/-M9JcF2gYW)*H3ih
5O^8,e(f5(c03ZXfQY2BcQ)*_H=3OG`L*1%hg`\K_;)%jkNsl6d#:RuK[p@%2ELha)!?&2Wd
:-]AOLAo'2d/p.bfG=4g1q=Al^Iq9J5"c.G@AVGP!d+QmWnU=02l)L\XL;G%ubV><Op/+&@3c
<i_s$D):a:BfVJ\iI@[?+J[BJ0T"N.r6\8MP#(lrWamG!6T]Ad/C,jg(M!0oI%N[I8jIFpb`C
A!.c7B1dgm*.2X#cqW%I8aPB`JY&J2r1:XPJOb%EI7UFC(?'T3nUc]AL4WR1+_gfMbGNqjrfL
$&bWp9$(hm8So9>hT7"ph<W*prW]A+Zk^9;?gbUR3[UAS,D_)&PG<#kO_^jq-kYN@!m58?tW4
-#g'\&Jc(nh]Aa-#:%(B!eWSd4=/EA$F*Qkn@dgJ#j1H<+IjJs%$*C*^#"JoT4<3&,WP2<ms8
Y>f+>QZbra@;r.o(3:b2PL>:Ro^IPM<6kPmepR0.2lA'tnA4e:Daj,C#cSSqRJdDdUk5tJB#
!9g)X([R2.A^`Tp05mB$4#Dsk-XjM7:Vp2Acn(k3SOH%9,t?`&lg%\Om5ZdE<SJEZO[r7hBd
3S1HA/^(fUee[8!IP5(A68;mQ))^M=PH!uBA.SD\X.N(p^Nm9LL\b#Fa5MM(@P_!`D=!p@N*
$;tNOF0D_8F<@3^Ck%_<b"l_,:m"/lYAq4Yn!E7s2:H)!@]A8QR<-BbpMjd;Ge>mT]AhN11&h&
.B?'#VH=SWn1Y'r?t;H-)7=`pa@V(L7eZ!3&$3OX6SoRF*[+UT)Z_.tfS%$`^lR\`WGhSn&5
AcW2BMiB/i)Q16*2Kq"p=dR7g:,Y2<G`oFoaI;?d.'u(NT<K.HU9ke<,/\bt%:^s*UC3=UYK
JZV'``AGgAaGV6ha1U_VRH#.%*XrL'm2Hu+sOj*[eK!%>YcF4k^HQ\,2S1aq&-We>57g8%L_
/7C;hZS[cC*Sq$`4LQ6ct'Fq"/Os1=QZb4lZ7H*HB&$uN%D,3G.%R3P"KRHmBYA.1$I:Hnc"
.7r;W&T*OP>[l%T!3BYOQ*r7cA/Zqa]A.(J`g*i>)R>),2:NhX(ST)Sibd3Y5'ML^<Nf)8L,0
Au(12aF7C_<s$.mC/jUMF%B+leYp^WLOqSL/1a!o!;:DL50="g7!RmscXJl60t]AB((m1^l7A
CJ"3D-A6@M%W&`_`#XAIYS43!fq"YN=J5aO<l8e%'lT^t-$H@QnT(_$SL?)biq(rK$COX832
SJXWdp7SXUSDnM@dY*<.s279G\aP'b'?/#?6/lIAU:/6OrH+2\r-hE_T##M.5AA`S_N>h+_^
;*Z*c`TXhs3iq>&Ir/]A$&S/,26hMI<Zq%dej=UhNeHWHfBOh<J`W[,Zi+IHL:C5q<1qG3700
]AGhHu\M?5Nq7SL>g5%a%M.B:_i(Q<l9ahd*YB'L2-0om>;?]AWmYIfEAGotgYTZc>5POLj6f>
a$MqOe:n=iTe^0.W!2"15.>L(tKr1HM+3&>EB!Xl=:/dV0=qX_Gu5+ce'KB9']A*3hgA(;F>3
0lF:]A_<A\:OI7mUpC8s"Ib8#"t35&ldogF"JW8;\gG\L<\HgP;eAm1759;gSXF>6ltXj]A))<
L4Q?_2GUf@W2W=P`M^#j.m4=*\#skkP<lfG#nNBAtdpS]AX__m,[!HPMf5^k5Un>[&q'S%U58
%;,"I!&XXkG,f/dZ5g/O?r9ot%TJqKBl(TGB+<b?7s3;_*Yb;%8;7ASP.*q(U?^LugA'?+9s
Aiq))H(gJ"O.q'fPCbpc;`Tl>F)*`22?72&N/&>X\e@$g^XF^H;mk^fMe_9AA.I?Rf60`_o1
@gSJ%o,.ocp0l7<nT4"&15=Trc`;)@V&D-@b-N"W6RK6c7nAC"5Ep96Ms10r`)Y_S7A2k$/R
a#BTJWkYlM-bQ#ji=%e&:HQa^]ACmi;fBhN?a_rapXlE<&u!?[^8+QY?X+`((Uf0a0e,M1]A,U
",bUnT2%@j]Aq`2F"gP>rUA^2qPEaOVu%Y*EgOVA$9lQ/ndi63*1;*S`?t\fb4$dQqq?[eQ`8
Z(I]A)<\AZqs?!K8LtWpdPN#Wm2)K@)84)iu<;k*IblFm&'Io=k>GX"2,1?uSi*aia;,n3+<`
8*-&J1;dIM;c[NhaS$Bk;SH%^M,t#lBCu77Di9"L"JZY2Zm^?o#63%]A23O.VQ"gd2=6sQ)LV
&sEP4\["Qi3YZj`#/K#G)-cHl'#P2O#3g,;(W(3sn?d/p?'rrV5ae%fR:apZb&$M9V'1-!In
k=T(o+'Yju?P+]A4-GKbm0R':7RJ85EN4JiBLGZQX8Gk7(j9LfN:)7.e`kpB<qd5YbKRVkTtp
3qF\62!89VlAEs]AGmCiZW`M9H3(%?\2ZkHqsc;Rl<V'Feq^8V\cp<ORX+:0J^7IRa?D*TjER
Ar#-[rL1I@,u$nSsI5OSEt0'2uGP)VOP&%onD^p`s4+f,EGkZ:G>!^>eaoYW;OHTjj2%k_el
G;5?E^mfS3dYABf4,Ye2WXH,G5KZ5"0)'_H&gRKa8^LgED41XAMV)WnjH87h\qBY=b9D]AXcH
KVldbFHhhTG+$s/Up%PpQ%<h/+H]AM_"a,]A2Q_&q/X0>&1R=DUGp[q9X7^B,3*9g[QAH:nM*&
pZ]AS4k2R/-fASu:]AY;JjHp+"2J")5q`T&c1+A*j5G]A,OE0R_ZA*W2cLfV!Z9)3^>OT["g.oj
Q#,434Kd<08H8A@58W"2?&itoYaj3cakm(?CO_i=#<j]AhPPX0qsl%=^nQcA7N\SpO$h!o:Hr
IBinu01BPRjtaN`t]AVt$$6+ccLNBrdePhtHSop1*HmiYghe-=SWP?:[\8Fa+i8TN;U('tW!:
ZA_,1b]A@Io4to&:<7R(H7'r<NT##o6JD9D`#/&(IVAk0IK#/4&.CkEMfk]AdB_LW9l?X4k4P[
qsI=&2L]A?^4Z8`8GMd,foE_TC)%XfQIRu`9.GS-`7O2I>S+kq+kn^s-(j"TlC+DdY:+d\6WQ
c8:E`aU]AJhq,Y9(tB<Y:7QeA>haT#S/!c<Fpi%BRKj@AR%+(2:u^YE\)OR>0D9T9$rs!ZRsY
o$tF$!53sXr[U/:hF^NH-B')G:il^i=LdVOfI/VF3-k".M9\5V#IdJBC:jqV4?DIU4@+h28f
SM]A:gVnFFWgb&*9b/UA\a7a=9uLL'XY0HlE8R`]A(04\8N[/XZO.f\aOc&V\AEG^8Q+.km=_>
LN<dhMsF:^+BK*dpg4O:?J63*d[8lUX*(a53VY9hPQ'mUi*`o^h^KUIck[+so&(/HRBf=F^.
u"K@2p[\)d<>`*8\"5@F1jSmH7`Mg@&N;E7.i4i8D)nS2s#VjXNH3$R1kOA*c0o3d<?26&r#
OEl9kK?0-S4EQ>/QMKfKc-_P?3?OSX&pRf^1jT#gsSR]A*&P_$FOr![GN)2KR>,`YRg@=Ml^j
Wr&p;9gXka]A3563-P2^:>ss`O[Lmtg%d2?4aQeOfdSEXKT*9I\R5QuI_gem=WWoYhZb4+"/$
f'lJM^e%r<GVc+[qo.T%UM<hC"hS`6(k:ptPY`Tj-Sb0>q*G<0ui\Hb_7N&?#k,r]A-(j4bkV
h`F?.$pJ-g[#mln%%h)g5K&AF:C!518^O&[&V:q1oUs(b>8ojY)=i?II>B(Z5o;m#,X9\"66
n&P^tlVP9lJ"I+5m1!f(!i>HtI:3WQGEti`NqMaMpiDKUiB$B/`6<BZ`,;Su.-Yo->6@4,kW
"7H_A'B^!H#AWEQVj'4q9XB'*:5k9#/qfMKdUT0e>d2.l%IBkS6gjj)OL41e)eP@tcnFS,kQ
[&GE)>Y)W_5r*SMZ1*1_Scq5E<YW]AjK,W4A6u?$0b$AtL5UP#$&@hR@c,3O-7jME'T0:miH+
%9BumT/R\p4uA0KIK''+LW=_G.UVj3m)neEsQk#cQR40'#KNBCC%l5@YV)+85Nb9:_QI6g/=
[aY#tUWshaf+Dki;C$Dr*o9CQQ-uhjEalYiF+OYCOe39RcZ1!uQ)22^V.:[j6ZddI9\9%sDI
[EDFj_c>k-8Q$HRnk.JYe*Vhd.AiE(;in5eR3Fc`=A>4.^32q2l5>*'!-&J_(5K7ohM).]A/[
U)hW53eT4Zh)4l8ZP*6q&3ZR<#YK-$a3FqMX=-"Glb`B@)St;5hKfe^*Z&AG"MkaSckZZJ&X
cF1kE0VIE)7a7'WQUXI4Fe_<_+)G9R[Z>PK-d_bSu`&k<$-+e=)97g!j)L-h,4?S*;Wt[BWY
7e2)`qrBAje56@6H>O1CAZA=k]A3'VD.K&ioMcgVR#g!+]ArN+Rh]A.Th?MDrAW17O'@MhqdFg=
qV(JF;7K$_#=Yge.X.%UFOc#tER_P!gZE2_&97E7T6^&e_uuW2l<FH2T!dmg%Mr)-8uR2+U'
?KLJ&4+A6KJSgGq-(N4^)?=\ne&+.TMnV+B/-YW`XefRq$,XQaXCsocr;VF1LUlW[3cN_,[f
F=q'TE.ZXf7\iZ)cL4AHVoPj:<4,$hE"B?(c`8j-q%Q=-7Ef#/LYjPdWpE]AgCo%;52MR=%>f
P)If]AX[$Tid\BES*^gsD[dn+85_XQao<:9SDbLeC!R&2FButl$^Eoc<>H?m4_pe7!s9^AWT=
(E%EX\Bds#qM^lhBL2S_u)fs-u+G6uc;+(kcpJmhFu"/m3]AZn^VGHp0!oG`>"\"ki?oR]A9Z=
.n=^LYc!ghmX_HP)ZDa9#7G-V7kf#Qisd0m>P_5Q]A-1BoVUNIZ@ROk/7WQTCR\I(\0I^MDe^
ee7)E`?s%^$l'm,6XjL_P8093l0Rpfc'l<V8.B\7=?IO2$1GS5.KiVI)oLO<(f*p4ej^m9">
I5K6PFalJiY*^N:"JBq\>U#bgdaACX!!@&>97nt&1bC9s0<]As->JRY9_j#mB#T`U2BE,fPJ)
YYOG6[M%$.'!Xmm`]A-Z)%"t1N5!0@4Es<oTs_Fr6Xf0@]AI3r8Z8&"-6h2j`S5V"-\.^soE.O
at-"-&Xg6%,7pmt"b)CL-Q$fg1j?uesB9GAd'8F3q"7V*987[B8>C;X9j)FD1>d,Brq)$j]AM
:BJrgolO@jg+?Do/V[D)odT2kWq!Tf6UDha/':Z+X5Vur4W`Z\$'>b@1O,$s$#dO[\3i)\DT
PXn9t^s:+N).>fD>uaMM[bpcekHu+Wd["s-e3&$Tl,$>0rI]AnNmfDh<D*mi;6E;Wb5-hQEMf
\R4g,S#-3mmg6!IsnA$DM4"LZjhXl^KRIjfLODG<rWYdk7gdU`TKG1GQO8Z(H:<KcomcMCp7
7`+WF-BoER&4d!)[Nu@U;Y@Ci:MNNe:3Wk.1="Z<J(9\`euKn<>pH\,YO*#L$um*Wi$H/^YE
b-`S]AY8^,=0-"2JW>dq=mu?V^8Eg&iT%8[?ll+I*,&8Vnce"4U;nK]A=FX2?$LpX*hnAq#(M,
"<ek,kmb.L=:;AE]AeWq2Q$NW[_Md6%/_[CbJC&R(EjgOSF%+"cduY8i4Ck\KiOZ^#csMrh2V
,C&>og.`l/7Z6+4V$Co3MQuLuJW"/n40EURSS0*%Cup>%6[lK^*D?)#+$N/U*=S9\I*;rfb3
s`p9<kjuCTqnilJAj5.9;clJTq$1]A-0-CmV:l:9%X;tt1)#3$3*m[_%7g+%4G5Qg99??*uoK
Z6WagC^[NEbkEt+i7]Ae)J[q([`^tDMKThbl'\0Lr^FhLUbZQ^W&p+K-LVOcJK4b6&X>SpiC`
D/rsRll(CrWX(Ji"m2Bb>N[-:##/;i9nn9i!??!asg0=_*lb_)`l@mHn[C"`*Qf=skn?65*G
\a97E!*ndd0WLSa\.tI,nZ\Ug0`d"OO`7=nVk#&TS2&bZlnYE/73K<+W?qe*0n5N#;An69>d
i9kO+rp[2Hmc'cf#ie([X$r@nXgPg_S,DAD_M90CuMId>;CuoX3RWGcepMDTH4*.,L^!mcY&
oX['b7j7#<=L7fW+jD61^HXRhX$1JjAin.:2+jtS/fa4Of#aR:kRVapdh[*/3``05?/5&:5^
mCCth&!B]A/WT3<@fn?qCL?l^:,BBTjtO2=..q70UFA@^DP??AfUF-q:NV&+dP]Alh$kAjDU9S
/:qh.?[A<DCKDk:?!c4*QTTi!tl)"e!u?qENAJu7ic-S5Vkhs'_s)>9p%D=3MMnhf`#?Jc$0
,+tX0bNi9!h59*1jm"2"Wr>HaOb"nXM/b^e3t,G]A_*PI.#Cp+eps73'D;u#bftGh3!:$GhPF
%0WT/Bpu/FIVK>g.gEXZrrRnF2'6)ol,.^3XT&oh^\NdP)>ODIs"/c+te/,gCC^?4U@4p^U>
6*2NKpQHMf[ro7;W2CHmpb?`W9IqMdV&TIQ>jD#D]A5CIEGTIZkHW@H9&k,%.NRp3$8(XB<EF
2V;hD.@A;eDI"#,u\q(0X5Z3);FW):j,_frfcWpCD)OCogp<=j7T4kkpQ7/SV0T9D$#7/)mu
P,ALNu2.93OOWPA@IG"aS?1:`p`1/2=DHUZXTNNMf"$dL,;j5Z73K/U'4ChCX_/2$?"b"paI
OGp+rL^2p&gSXDnBE*"]A6):M.s/Z"8#*[`F7t^FTr^`e[e[pEQ,b]A#$dhV$7=!^:qWS^DjbB
Stub>2$eg8,5o"NFPASmlt('Fb%tjB-SH4=HOpSh6kmII@EBGL&A:gQ,Y]A_LV:@/rcB)5V]A(
o6)L0H_/lXm5gl!G-%bLr$?4lZZaH-MUt[h./\O%dCNo36rXM\;\TPbn39s'Ma#M[.BNqWdd
8rt?@HO;j*9N!Gl"&nVCOc9lO]Ah$SiAUH(W9o0Fm1'?Rcd3(8^.7KoO2h"3Li,1eKDC]AA[s:
tACnH\<_Kh:<K-,'U%hJ-J`r-&6?bM17&3Q%p*;Cs"kR#E:50SrYBuWPkM2@*ULhjVM.ej9Q
kZ>dcWucH@GBE97T1NEpVI,$$>3/M5Pd.gAS58;qG/&QR&`uW\/"iZ'9uOq^H\K>p4Vhe-"&
CuU`.pb*4pA8oG306l3B;srb@M^pW#5,md/pdp_a,B$*6(j"%[CXGYJf)9L4a>?dQht2FD`t
rU#pqQO_.qChe'99kCnruoLish`Lm="<<#\76>_HfP]A'*AdZ5QQcG'%nibiR.ZVS2^!5KZro
a#T_S5ot3L,eu%9=V(#!(W.83+rl]AP2;Q^T]AnD2aIfI(^-6LniA$\t;.s4O["NK7k7Xh'GHl
%]A6eQA??5gb?mMhoaYm)^X"rLe&'mJEP-5<n2V2j-;$R1W%ZjI#CiKCd*[<*V?K!<ao?s/o&
PUi&sBC)\55"W!#J4)CVMZu\&B/0)6J\>SJjd"Gt(=>^^S#$hIO'GGOTDm4fVjnUHmHrp&GV
M4!Df:N%\]ABSKM?CIT*ed8aAKSXQ6->lWjamNY>_Ctd+TpUXSE5?8AH4<pP>d>iIHhg7$/^J
i0*@WFHed>]An2Y)_Z"2G'r\CT('sPFPnhYuDN'_LZ(*KSO):1SNjG'24XT7G1?2r<K,fCS.9
1Uj5.+r%8\)K9E@cs4>o4.;e+;V0/pduu+"EkJM/._@K1J>sd6N?^AS&#A&cQlP`:G$`bpR3
cEGUNgX@DFsm`&^*B-bOjA0*sM5eI0Y<a\[RrJ\'<16f+V93l1j'0hmYeIRs+cCe=?rdF?>l
_B;?IAjig&7%gnNiBsjR`54hmROQ#9"i+KKB+hIWd$FU3U&E#lnOEIES`Nr?gBKu&riX6:?a
,!94X'acBt+oB5q0U90SZF]AlP$?ce+ZFV/+dt>)?M7QH9(ki(m:BNhR_qnjesPHCW/(@-?Bo
20?)fmp2LVgQ+KT5Fe:d&?rKR%O,a$l[rO<So%(j^"Jm%haGtP$2m!l9,A=LIKl3qOW.h@bk
^W:mFoJ)Wm@fRBm4AV&%)jB7ORt"molI!3R@ZQWTY.Q7e+FF/6RLoA2=f<dr.)Q6_04<#[JV
sh'_9hDK*gBfG<pha9B0?(J(0CKX*cn\5k1n@X&K8tWD67ONaKj[.IsCpLsp<KQCJ1)JH"0*
j70VE&r]A)6%XXk.6[SfjK_?iBJL1$rL5ChgnIf9XhS("6L`>+6aB+-TnK*k-%4dned;h'HQ9
/05I`K<,E*,DYUP%5!F:Zk]A%PL\'>rk\CIYAP8!YW6n+(g1lrM*A2LE&KX^jIs;b,.D+R241
)inrlsgZ1DJqh`kH!Jg)g]APV04Dhi"``U]AP_A%?dU.]A2R75=t`(Ji"`83mf?KN-f`[O^RItI
AMBe$YkJ/qHW7"\o[1l[%POM@D<_SNP>MmI'lpn$7us89O#\e6%[@4p(t15+:^Oth2<+!\fq
#`e\p8eA0^!$n9\d)Pcu?!kLNCH"lV4qoTn_h,7)/UdpC_k9@Y>i*XYOG"E"'@IFZ0s>0$Vm
a8O>:63$f_WGUY;J,JUDhER?pE8V84Hh(C4TEj6:nF0'+nNtO?m`Fi,e&4+@a/AmoSYft/f_
Jnmm>+dB.gMF<FS==q3'?hWQe`<C'(D'LK4BO@Q76F^8D?PK&9.(N*Bt5hQ?]Am?\!ta4,u_.
J"ES#I9_TemIU<dsA:%"%omR%D!iq]Aj-=5d`k]Ag"/#%dXcRO%Iee2Ga`i4.o3M's9t&L(t-'
<NbJ$,8Ki^;6PZY!/OLIGo7hMs0N\Z.uZ#rgB$`\WT+leD^LlZHdi?M&pKt]AAV21=`@QfJR8
rWOF+uRm`g)*>^e0R#Z"/e)q+A?l7H&,p2dY8Rc"QCg;83.$e=d/a"lN[S2IY$,BlrK;c^u@
o,]A,BJLleSXfeJ=%]Ag]AGY.?ABfC:&2E1UReqa?<+:$Bg3.QDOJR*WMX.%p:E%gsmg=5\Lt3^
Mt/&m9>h4HQ+j&OfBk6tolcNOSGT16:,R`:(<]A\_a5jAk[$..;^+W@tuo9;ij2T@U$/`%t!f
);>fmlCSm@1(Ai6^qPt>gkF96SEUqIR#7+NHUKg/65.@u;1Tukk[hO/\_7)Nukh>".H71dTV
k,:T]A?=ndY@%*[Dhe"=c<VXa;3SoF1"MN7I0Rbq_sYXP<9:IG_4&82q-_G6Wf,eZ.dmd<%o3
JN*Q-tJ2*L#&Y]A+k4U'OKA3#MHr#0L,:%@dn4*aNeQe:PDOc.fOin\JjF]AN*XncgKb#O0/U:
HV';6"5=!E9M+ipMFh8!i!PSgSa(B7G[VqNMi5P]A4L0?M4".bu.$K3[R.e^Ep0oMHG.hnf\o
dO\DRc<Pquu=Jc`<;moq-p)9*(W^'0jhfFCr%dgkTYV2cI0[M9oCkk^>sI:PF7j5o<j2_l(Q
B\c6@BL#B>hD_<O]An4cJY"`R&84W>%-'lHEr`gTi+!RkY:X<7"tI9nf`fBQ1<>/*G6,Phi-R
J.`;XNe'mjpY'XqqesRm9QM*AMNtugtKe:p[1rg]A:*coB,V/snZ8lmSb@uT/NpsmoD11V5gd
qih'[:[Vg32O:86<6:=j'8<sqdBr%6lS$']A5V;</DM(9#+\$tTs*#n(V?4;Y(8LC^O<#kHul
KI?G/^[lf=)Vl33j6J.XJt_A\\Ui)aC8.>1=)P+</$RPF?ZBu%Gpn>nHMbKnkWr'Bs"SBOJ6
[qE/DkUI!"N`iA8u;::Ha/=06I)AL1!j1:.RZA=>3r%319N6;cElMISKH\lY0[<X+SY.2;S.
;DX5I0S\K#hgC-gCKEJ*`&h#qYV)!?eNgK$rEg!FAj%LmWNSF(/8TL@1T't,kkpG514T7Asg
s'$dA>A>^j)!7bNf\jV5&>9d9<\?R@8GJ8f9&;_123W%p+*b>>W#(1eb;4P5SR8gi;GP!lt,
se3VjWN((=)$(VC`Gf;[XN9@b^;HbZrm41HNHk":Pajq+rXqi/rP*V.K"#n&196A$2Fm6*n^
ao*-SDRH7Qht`<MLqV.8N+3.\"\Xq)eli[@ne8U"A*%j?kC0GuN2qXN@.(mT!2JE;bSAC/g7
,)oV%7CC-V);bW[d9W(2Jp[(Kg]AZ>^OcCpCg?u#6)Na`e^@8@H`-5cCKCFN`2m5%.=Wo.:T0
fgr.OTb[nThP3WKZSFLcF$&;TuH6NCA',`JN:kR/=o10$3Ip.@TPC!&,R!k/1hKSZaF%5R6N
)t[$57nUi?,f&d@Q.nu1FKQDd^jIqK^83QK9<u@l*sP!D5?eB1!5W4"O(t/b*;9;.A0;`V4d
0n8H9=9:BL6^aG?@"pD0:3/8%okIR1h)ZK0;u+_ocdSukA!7M.Y-Lq;Om#!'+BfQKKE@Z@K3
C))X`GZZYiRHDS?l*,Tq;WWc1mQJ0?Pe*J%Nu6OABn/B>kTjl-HV.AbGHH_F-[(L"c<:D#^R
#A_T<63fEUeEs1eq@U"1cjT#k,<D0.(!_<UjA\?E!I?"Na2C[Chq;+^`i'(a*tE%u!I,BT1L
3*4r$W.h-?QfC&lVg)\!<rkg:A?Cud$lf"LHbm&g7]A=(/b^.Af-GC9Eq<NRiD`-c(p,H56Ko
%mHb!_`%Qi!-R\+1o+n?8*Zh&G8n>ZqMrU1$!"g6gf'E->2SM$jh?]A[*q77qTGFkQ%2:LOh\
:n5@1)1CPB/3p^FQ>+q6YGH]A.W:]A.9g_g>->Vc3;D0iAl&AXk5V><MF%BN@QV'Tb<F+b%jRp
$iJjD=>(Fc>e*FCD;_TlCNl0SLKYiTSr?:p3jRQQ'i9SE>,B8>3bFjqs*X@Z9[`RmZ1rc>W+
q,XbN_H`(GHp)RYUoU,SutgrcQk%S]Ao]AXL\UB0LMd&2\@,EGgIT$QCOG[(4o`jkOoMVOY7/M
TErG<Odg:DI@/4r1e9;%bFb2j0eQ7KQet(*FqYg<gp\4.Fhqu3KLYl6`O^l(t0XNI&Rk%_\I
o(*4*O8Qn>,Pm[LjeTl,cof#*kbGP>A*n4JF`$_*Y:`kFn^']AjOEMPXAf^[MuFLj/c&]AD\*.
bXhkIkSIuF:Ar@RkMoXF%&dGL\57=U6p(B#i]A>P,DgDpeTWkhlA<='"ba:\E`3NWd8WlM<+/
>P,Dgs'9a0_PJU6D/VU]A#l\rf@V2#%fCcBJ%f~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="34"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="108" width="375" height="34"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="DATE01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="DATE01"/>
<WidgetID widgetID="002d9109-1ea0-4291-bba3-8a88aaaf81fe"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[228600,342900,190500,1368795,897774,342900,342900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[533400,3810000,3810000,3810000,533400,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="5" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="3" s="1">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="2" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="2" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" s="3">
<O>
<![CDATA[总访问量]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="3" s="4">
<O>
<![CDATA[日均访问量]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="3" s="5">
<O>
<![CDATA[活跃用户数]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="4" s="6">
<O t="DSColumn">
<Attributes dsName="总访问量" columnName="CKCS"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font>"+FORMAT($$$,"#,##0")+"</font>&nbsp;<font style='color:#8D96A8;font-size:12px;'>次</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="4" s="7">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=IF(OR(B5=0,D5=0),0,B5/D5)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="4" s="8">
<O t="DSColumn">
<Attributes dsName="总访问量" columnName="USERNAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.CountFunction]]></FN>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<font>"+FORMAT($$$,"#,##0")+"</font>&nbsp;<font style='color:#8D96A8;font-size:12px;'>人</font>"]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="5" cs="3" s="9">
<PrivilegeControl/>
<CellOptionalAttrHolder>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="true" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="true" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="6" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" vertical_alignment="3" imageLayout="1" spacingBefore="5">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="simhei" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="simhei" style="0" size="80"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Right style="1">
<color>
<FineColor color="-1644045" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="1" size="96"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[be/.!P?G7Ob`$BU.?5NA*t&G(Ue!-G+tD#/H4pmP/g)a-6q+ru$6m/QMKB+*&m>n_0hMhe"V
NQ<&g0FF+UK<!i]A)#J>OHpkHIqnnp&-&WSEG1gSA!64HiNfr^QYSHQ=g'0MuV)?_o$uklK+K
t49nQjs*d&N)):tgfuLuD9Y5KDd]AM_bQ._Om39iT-Ff<K%;q]AdcKE#V$rbOUfFO59Tn&1IT\
bS%JZ]A50ZQ9CS!o[g4bZ[Xs"2#8UT!CeogXu_<6K*e_o\h<.,"t[9aqgp5BA9Slm#(M2%(d?
;J5jkD"(-63$htl)iP<F(bo'i/&@IK<Z4?S$G4'kimi"=7jb**GZ*f?4&B<ki6P()NW1Fe^N
iH]AU'1N8r1]AlsAtBAhUK&EhcZj8[r[p2-)lGTI@&JI_83J;nDjG`Kh.I!Hd]AcO(cPH)THn57
dW,bB!W$/^/9_mo]A*CjV8i^_gRK-dQA$rSM&D;[tg[0$k.JNDem]AFQ>XWdNW8dl)p[5=\F0k
#qtgIH(K07@gmQEtY%YhC`^O*SZ@(N$bo3Cq!B`IPkZXA*00D69l7P>GcII-jIl%%WkFV\Tk
C*(7E+F6+ro9Cb;m&ZEUE#C#1H71:fQN]A>ku[F?hK8/*/_laI]A]A<BLk:30$Q@$l.Qh,>[k1j
`tc50$n?^s5rm,L):o`7Z*k3.d$IHRs:Nuo8FO8eTSP;GdDdNBk5E<\lH@@k3K56Q8gp%64+
g>$;!C>DNo**`s74O9-ld+!9JMl6Lt/F^\n/i>tXZV2@dV#DolZs=P)=QJOok.D8%fY%6V+m
GWj0AcFk@o/W:5CMgp`"*p'3&k3$]AG/O(;aXSF6/]AiR`E;O9IgSF8Mm<&<p\aP[?+LiUmT[*
8^G>SXLSIG\LOSiAAO)Jnrd-q)jmin*39=&M2'0C@JMGQ#WP>eZ"66lf2T6o]A75oI9CAY$Ok
!Z[Q":ZEB61_U_]AdVcl1CfGXk01Y*OlXTK^X$G([g.55:`Cqa0!BFZg]AA>-I$=0A-=k7"T%f
oe%:LQ>A1Dq5HKL,9_"c!#jm2B:\0mm?g<%7;/$F8qXW7.cX6Mq!iIbZ_?9*-1qO[r*5dAlO
D&.VEhJGRUaaY_=5Ak,C<i*J(E:jP:o55Q$I/c(DIhh\GgA\.'s8"'T,n]A<IbK^&k`>RD]AnX
SNU2WO#dLY6#6Ns5K3C\:/t3>O]Al\0Kl0cPlf4k^WGDFjc=.aSnsCrEnHHOS.nRT9Ar/rr88
#r\lbV<iR\-gVC%M/;a\E@tOdI=L7S@1nr5k!#WLp\:$/FB9`nBlR`m*qsH1T@Qg;rSEa4OJ
_/[Z"K3_Z/5ZGfn0:.0Z6Z^m=qgR[?m8J'$lCZaY%MnMV0^:%3]A43Y"BH)k11>Gi/JK3E:_A
U(_A7@P%KWmj\L8&JT?B"GkcmX2,:\WX\\/5D/:-*\#t\0$)qS;]AUnu:aeXIXTh%#Ia@]A>[i
)rK+jHp)QS:GN"h1W[pSDo?]AK>\d<<`,$`:*p,L>*Pc?`(u\srfG)@bJ\L0&kiTU0X-D3ci&
]ATdZXh<F6pZ1AVcG7#A,sjk\C9c[D@:8*H*A.l(!q!Go-.jp]A?g7S9ML<rpfdc:#V?A_[ed\
tgu?mBRGIPP0IGch)n/UhrEXD+I6JolG%0.6?n->nWG&a#Mad->*jp8=gAop-_L?u[!&\S&-
DQnc_&b[k.^'i>lC?m1?4W7=p(31k2k!//#2W[[kCtU)U"j7cegV&6\B^oY"/q<[%P2IDaqp
+k.sJo`/:Q4MBo#&5!&0F2j<F.W4je4[c6P<HPP?h<6sL2$^Gt5\lFU.8,qkeuq,[;Rh^rm6
D00,]AE&dP)+rKr[h?kA;e84`ROC*)"'l=*=f!Mg9:QAYt.>>^ii2"rl-fUrCd:h='r4lb-K+
X%.\#q`Z\-l5ndfH]A+%Vf:*HAfLX.X6F2:uAEdDH=ti^[!WZ/Abi\mFR(sF!Fb`(O==Bgra$
B_+]A+DI,qZl@`]A%:b^T<nA*)jM:*Sta_)m(QO;lN0-`F[Yd<EYAW4gm(e1XZo"3#7`M#"UnQ
,d5mAlmTq"nteq[a=k<JA,@s;7"bf^#KchBKUVDPn[!h#4n3oY05!>,8MoI<``M0CP[ugC-@
107Kt.4^mYIL9p]As&>(n:TM3QKL*amRJc9j#VC'5`1G'BWadt?&h&e@!\(_=BtlX7Y7`e_Dj
J&!,Z3&6S4efD^+JjOO2:YmNidpo$kpPWZ2PkRB7@<t<@2:8Q'fAgFN9O+YraR(lKr4K=LmT
G2(04&2+F9$ZnA#+)2QG+NZK"Y*6;I\d).EbA3ik:h"Lt]A%&RT]A1FNbk+eE(Aj>F"2rZZEAY
olSlJ.-4L-$^)LVa%2-De2Kpu]AI=\%1^%r$RFtuOOPm#62.LbZ4#*SXA'to)1Q(]Ak)3P,26U
Dn3I;Y&D,ik`4a#Z?NFj/d9]AU89Q7ULQ%/+_$9#O([(5f;08pCkaKIEQu->EZL,0<<4`H-@_
l9V+0@uR*C`l`58beZ?a]AJlB-`d1P(r_oce^!amL=ip%N[aPH(85gJlh8YhlsD70,"a#`FdD
K5DfaoiI@`^8COW$"kKTIOMalS*"$/+-'gUJoBgil!*7e[1nTU)otPb`Klm%Q^P'5bbIhK"K
b1fT[D'p]A5dP4Yc1UkAo^K#[U@>-9:7'\mK-W150k^SlKBYrFND1'%EtW:#-V@nYskp54?CV
g6gENg'7mMmUVQ"k%KDOenIi^kQp$>Iq-brNc'R,(Sl]AX=E7irGH_ABcJ/++^L4/p&MJ']AM7
*+^HTb1,PDK`KH@a"dE-\PJ2Kfa5j2C+18aXh&tSq!)X^#-dNRi;%7'V0rLhj>=\`B<n<Xa!
]A<'S>b5IY_,[,fbI;cjlF@ieq6k*o($Q'O</'2YbmUq1WCHPkOg)Z*B\M&73mnR?c%lMkJ7q
6m>TE3R):9H)&8?_!2e$X07s[I5hEf+MA;%/S?hL`1E;MrL,6"`9ZN9`%**?<&<bIB/^N)`g
?K*8B3Kj0YN2[@ZuQ<<E9`mja9Qf8c^kZ?mJH[gi.BR,NhhF$mZiiM7*_Mj)(Y]A!>(1Md7$a
a6?kGHicX;5O\OA*n?.iY%c[[drWeEG\0=Yh6&Y4k24@[.;2T&!-)!!HbT%O"pBec,]AIDA*$
cuQu;sSYi`k(VghqG\saZHT:qr+?05!9"'`U0MC_R##=XuBn<J'5i$!CWOF-@-PTd"rEJdQK
FCQDAq`$YQMP'irgO\R'k1J_-PkmPlkuQ9DVW'%M[GRpu"]A6Dk6>3&?+/ME6dIT>'8DE.Vq+
E;[#2>8*.O3=[.UhR_7hbc;s*NdZ"?b&qKE5VP58g`ktm<Js`=nXLrIOY@6q)AICfSN,2,UM
+n`[751kKpo4LH-Kq_ImOTa-TbVrL7,\f=8BX3'eHUmDn]AA1rePOE#Z4?4T0Rp]A;ZGf8)ema
i<X[TZ`H6h)rnQ$W]AC;:AGP9l&a3/S9Okja#O[DF<_la2/aCF;f)6_IGFb:h$c+udoNKiDYn
h2XCgDRc8AV4\g\7G?9Qc<kq\usf,B?c@0@)1<%+gIC0ODhOoFMHUf(Zd/W)GdIm&=-o_I::
+h!0F;VrKYg<<iO"p>;rO1mV]A-78Wuo0Z-$*t^U/HO;,i^*\-_WcE&9!OH78`9;_j.EqlC[M
qbZr7_f[Lup_mPikP+b]AaOc877D9WFi(1$uGS0`S0:tJMUkf*Nk0.ji^V6MW51d2oBC?.p\K
@d]AqMC,6:@m(Dds.V,FrBiH*9U*bOBDQL^gMP16BO*?8LeEa4;BF(g')/>m2KH'^GXkHN?)4
TpN;LQ,:NS@iNr6ShE@7orK@MG]Ap]AOa)!l.'8,O^/^i,+X=e%d(2nnLh">_U(Q"rt%o1]A2iU
gljUKoY'()B+PX%dIG<pWCnnDp:P*lr\d7H\7To<4SS74fsH^?$o%n3shs)Il(kV0;Cj:,0:
'M$VGQR-C;d"rF2mKphWPI5"^]A=<"]AF%V[bZ)^$(r)*e2ollAW2-<&ja$B4,1uW>8sr6Ia7n
B"J5LE7aTkUj;e$<>?ujY09#GlfH3=)h4aepAKq3@H4(,61V:Qm\[e.*f?@OhISj"Bd8di*;
uH<SkE.L*-r_NVWK(^08&2S$T48r(/!Iaa?16u$S8m:gBl9sr_)p`o`OODFEthU]A_c]A-Q;^A
E/jXD#2PLto?%^i%`(bs&hbT5ldAq"12t:Wq"!83&i"ZrN9mZnTK19ogG&;`^63+CFQ0ZFW"
dGCi&+;J4$jkZqmLAKmjK1Fb2uSa+OB2dNlo9LYMm'P;":(lM.('rG$J`L.qmGp5?W*r(',O
QEmZft3@Ra(;:R#N]AakI#IhgSHjpQQVZlXLX1rX8boRJ4W:Nsm)-^#=t>\RGT-&i^[b'8dR-
g%-YICdfgmU4J*<:<p9G9%e1eQc!JA[sPCp@Ja$-*(E57fAeE":c'J3G3Q%h?rQB8Y71rl_!
^EaoO5&Z`mmh&E.Y@_VZ/$d*I$PVG(7`69Lt"B&g#UO=Pm;qP`U_0E:VLGeD2rT*r%qJ[p%l
i!=e_tK4c7)W0UcNS4gCW3<KoCKPoq@]AE*+n&D+j&9P'#T<)^.E%_hs'Q'&NA1@>dl\s#BZl
q)DND!1HFq:,C^%TK;P2R!L3:a48TDoAjp'A&f_>Y429qou!%_k",hRWnG.L%O^:Z8+*@Cme
MhXtT]AC?VknO7cu_3+&#_4oeH&q`oQ)a!iZM,6oLcT`*C4<>ArO=k4<6q0sX=3fk):[&LZ]A3
+%gY4q&jEp>$.f$4_-itAYO<9lgc!SVlsX'O+8jNMo6]AeIE4P_6.JYHd8@P([\\J^%Y)ohi\
,/=#O6#-47btj!kqXkB<\%^2smRDG@UltLNhc?aEnda/j@.oYd`O]AloC8e;7=`"B1,.;AIZT
sYGN%nK8(DAmn[nKgSph$oTs\om&qM>^=JnI?uEC(ij/k/>W4\Q@6pe;0l9GDKuJ;gHE\cHE
<DU'8?Y4FS,`DndD62uYXk&g=gPtmp2Hji5.k'@h%gKXh20UKs)ct*aFh/[NV)'a\R`@C&bC
.;bcOW7n%\E,88?iPE$M3Y3q[MAZudl!mP?0:o'&\]A/8"X+cq4ms=7jLH@1/8ilaC?bqYV@b
O&rGiCo[?OXjfO,Q&j7KSKS\ipC)68rVlZO!@@3c%%Y,PQ]AXb\`S#"52@ND=.,8h3,u3,bnt
a+3JAE"NG<ot7$jeFf'Pm$QGO"Z<n%W`njRg![2]A7@?n`FSmRc?[%Vs#/lQu_^]A>'t629l5@
jESY;P-oG/09aP7LADM^P16J0Z3e#Od@\mA^MQ4%..3^,@!'#'""rW?QV*apuB_AIUog+4kY
J'o9$6k9"2$%/NYg<4[@sI_*C4L_W;UKBem9/\L1]A,uM%'XF?g]A/q)<LXSc.L=l?#k\b8?48
2=5n-Z*[=u6F^HuoOIq(j\+ETEa'WVL?N;4e7Pt-n_mJC$AW<mpTQRilJ,RdocVoajTjsI5E
A_;![A)0>d,d.WBG>Z8TC7"n#FMspW3RPJh$/t$q?bZ_d6#_W3Drrcm27P/Z4*3df`h]A/\')
_!jY*t4G']AJ"TZMFa669]A@4giS\CZgCn!_I9t@O`ReV;N[1=fmu#.hG!n^b#L;"mV[_t7M\!
?Cnn@fQL1pnmk$?d^iJXn?J9hu?W!@O40d@WH+N6MU["V/^Yg^:<a?HJmJ0,d?%21&MMd<j]A
qKp8+0j:s+pHWb5:LD'WP-F\B=D`o6BMY4O#P>Sp6SYpoV0bJGu1=MklG'@$^3s-Umqc%[VB
l8Q?N3/S6n/i^V(Kqko8Xcat5f^>JX.o[SkH-X+;[5hrS"Qi'jM?CEluG@jo&[e>I$N=g_CM
#[kJ!g!mm"!?JN$K":Lj2'n.$E!F>i+KkoQQ4_LB.\$KQ51Y/Bg[gLd>8oq^H=Cp?h+>@=F#
WW$,L0&aWM)<3;fin@`UDjR7]A`(\l>jp!Pc+u,(9.(\Nbf71$]AM4Y=i5,LIf>Wu0LYHQ5Y^>
Wn'eE"H[Icp\&:Y6$cq%:FVfTu\T#&79^![L^-6:SQ'T?\eqpR"VE!8Z>=+_lp*$3p%TDhf"
"2"Bq\V,P=+8X>+;jltD\]ALUWYtLk^PclI[<OLbiV:F/hM.ahrM'+E\MqK]ARgW=o_lj>*X3p
?S<r$`9Qth(nkBHC_&'>L@3>lX:LCr3Kbpp-F$$pb_%<fTtTOLhaLK[Le?Y3n7:q3niJ/9_U
`aosi:"d"0!KDoR49pY\\&ce4+h@D28;YdtL"+tCQLkYN;j*T#.I57LS0JMI<'i9l2\#-0h3
dQ0Tl6l>enXuUKfTC`fg6<D3=e+S*Aom&CCL,[\Y<L'e;=&H*n@de(q1)?m>5>`hu<PmD]ArF
9EQ3r4^JMI3-f4[>,:rZJR@FShGUD:;6.t^*KEBQHIf7+rHV$j6mlB=VdZ;/epO"uU7IrR@4
]A.\M"+*==a1Y\EehCq&XG>qJFd;F'R.qbjC"i8H"M^qH;m^q=eV%-@+ota\L$WqT$BjcigX?
(6?&u-ips/-_WG;f]A'?b<8^G[MkoI_=Olb?l1Gm(`h2dI.lLAJc.^Oj!V1$"72g1uFEmtna0
2A!Ge+^fV!M#WDO^T!mRDOi4>@bdr7*+Mh4('P]AWJ0ZRc0.+Ekk+[[)Q8Suu+>\Olp3S8W,1
ploa!R:kTQoh^gROOYG8LT6O?]AUCCC!(-C5GqlppZpVYg7%U,lbp`(V>"g?1(_sO1oqbGG=U
rcRZ2*(;+_&mU38#>IR89_.bl918F8kjF!I>*K4H*>%aM#G%r`<>W);d)\;<P??WWp:N1@FS
%HJl%@Q6<NrOpmj!Xc,f;#IU[;MgC^V9EEd>DIO@L6&TP]AV"_nPE\L;T3G=YO<H/WOk,:N[C
30DDe6I!c<n-qs1.QhECU!UXiWU.WM.?HX+'r9"&cG.Q'&rE[C)j;]A7q+SlO9!]AG)B&^0Z?T
`=Y'8LW[_jkPH+-,!6D#ZGM<T>9BT[S"R3<V=qF#q^7OQ1O_&r;ZeXCFDu7BlKeN)&/6!KGc
i@Kk1fXqeSAF&@Z<gqNJ3QAGbO-1QV=O7#:#:s*;dK$n9H@DP:]A1P-%=)LC2pO^GU++b=;$i
Be=9t*g*h._`>n\HCAd$Ugp8m`ZoX''mC[D3XK(+g29ZBhEJ8Qej.XkX!#Wle^ZkB^D@^ZHf
Z8'K1Gk;THb"ij<eD;@_.dRE_e;0->o,t115Me]AllGNS"(7$2nCKKmA4K\3kGgX$k$2A(498
>8+89N\33V-QjI9^'A[]AFs)N`@L^c)_QIXUd4\1r:&BS?JKUTSX^X2&8[(`4kEP@fj1W4TAo
k\@PXTM^l&5*]AN"_G.U:>.Zl^7dp:]A$/@S;4&o,6@Cp0<D0KZ$\'oKXhrlc4N=N828g_(\_3
%Fu-T8X[DLMmNFPAC3I`4b>,(Q#T&i"7i[4%8B.!WS7G6ZF+$MPBbH,WD2GJ<$W@qa_F"Z#q
/r$/6kFl,J1a+5ijW;3?'o$M6)_[Bea?7F1^+WAe_a@GESI+UmH=^oZ:9N&B:rAC`%Q"q=oX
g_oZ0bV[0A(Fj(7-@h@;n,$+V*<g`9=ZQ2$6)B+=C'_\@]AVVf(?<!'OBAqs>$i?b&KskhiFF
`TYD?%c*=V2_T,h]ASXDi<&csq]AX.rndm+H4p5&q<IkMS_l2O1bTeYl]ADQ:Ia[IVA^Gsb'h'^
3X('39sK$"Vq>8Kd^Fk1mp`5p@,qqum5rTD'p5>rD"KT#/k@7a(PN3%p/X-#b5PNP?-:5FJ4
=RpDZ]AWGTZAdYfcE/ZNKojt$2pnAi0p?b9DVoJZ@!]AlC\hB%*P3arB&+UVH&q?r3m7%Kausr
%@!<%]A5tJ1YTSLe/+/s7Tn'p>=B0Ee'>(<nm]ArJc<j,!R<g6GMfJOoW(GN:+*F++eLa,%p`M
W+1([X2+:cVZ<uaJUuGlCtD2&548;>t)!l0BmkmQrRI,h"GoUir0oZZ1<:M`IdTeGJNJc5o@
kflE4P8(_Acb5:p^@r<<'X5X8I/aX78$M<A0CO$\qg+[KJBP-I1<?+betFs?5)K>@<4jK=3-
6E[I\<!&!#G@./P]AKqa=I!3Z'=mc[@r-<a3`0qi+[n8X@MrSVYBJ)-RoZ5@K^Sg8;,k'E<k5
-c0?1Z/Z$t;dQ>*=^m&bQ7E;6fiSWq/`X'u/VtY\;tu-cs%Q3-F#/bs(Vd9KMo9\.(iM=\S.
QhYaR,/H;DDak=\jZCcB4Z^PK4bUDYI?^sttrE*YoeE@k9#.Sj=[kEo*H/5KY0*3uUh#;\Mm
n$A)[j3f>pEYXmU7llZaKEmoeC'=&'6*ng5Z4n%d+@E?J/&?EcYtWhqB\m=&$O?;,\")bK'c
UGBGD$O+DVWuf,I5M[m*K#gd7Pf;U!!oCm*US)eR5dL;Eoo2sP5S!>VoeZcgNeb"n$X.E\EV
pQc$*M%de'daXcioN3X_<r]ALJ>$.NH\]AMX84fIJP<:u#j=./C;P&gh/C;:lfZVZcXk&<6$?:
10e_o/m2fsbYLqgo-YJob^SLJ;qS[Po3@2/X[G(_tD)qHDkcYnf<shYTn\F,HT_T=U:EFXO^
eal_LtXqN0IW6dUak9+(Y_cZY32[V#.`#7aoe;:bb=1K-C+=Cj'r;!:0+mTN!q7.V[$kUFs_
HaNI']AK>uF<O<E.QLZB>!MSYId-/PcW?mo9AZFR(LZn<[_Q4?U+5$Ep'K-?,Z2LCX:W-\;]A\
d%W&.mR63P_"n_,.nLQ)L^"rEatZ4bWpE69N+q#rt"bulj!hn9NA-Ve7q(8.]A)bN3.F)r]A34
kSF*7<cu0M'"t^lfcoPe^"mL<7eb147\.&Ui]A4kWC\b0je,lhmQN<IknB$h)X!*Rni11q,Sb
H6_rT1]AhH>q<^DTD'P_2^hY?c0'/BmO)#)jcN9H=K;5W7TY)>oZq)cdM"kMY#]AjhLBBfei$b
/iKWFFT"MO4CG&_$ActZC<>`!<EhQY]AkH.K]Af\o^oO0/eG?'u1bTl6GX\1Fu"DC->5(<C9+3
p52jWnjAH6uF%p[%IA'`=G147!*,f7c+hiSBE`C<ECr,e>cKX+qPf9m=^^'EZJV9)G'ENJ+i
]Am7Ah^Mg5'W]A@n<_-5la.7iPoOm[NXVf&*SW+p`a'TC.@RB08K=R#!E268(RmhV:0A:qFp<$
Cb0)F('i#4R&>-eXM>02(U;0Ek)f\nG6KCgLC8GHk#RF8Z_t4bcrW8E>W;pr`jen8:LCHf4"
"edI9:tLA>LA("SB=PGM')[EgD%=Y^p-,-Zd"L)-$c4$P&8jnB!';I57F#?U7ha!@<;h'XQ&
IEHPI4o#A"n8e5L$LLaXOUr</7-V2$5./$\,c\n48S;J-EkP2$GqV'H]A&Q*n!BtFNL72ZWFA
&u&')0a$]A`(h#(lpbJ'M+/?4(m-o$LIJ]A.4<@OgeLS+nO,h1YL#ft4%?FZ%J2oq"H2#PA#t1
nQHP&eRhgrFXOC@dYI!;'l9"oDi"DWuI$BdKb-$^.fc2hHlRR,\Y0#B)Ue(210/:4:8nXeC*
jbEo9gUhssH8:eY\lnm`W7Y\t/^+T[e"oF!#`BM0&)2:'VXC<cLch8,r0VJ2]A/]A^\l.qrOC2
l5Z7.`2;X:-Ir&Tr$GqGTDG30s9EcY!';DE*f^rqu/IdppUn@lZl"auI\ICb'pO_n5PY)bqh
kIeRgf?tA[ZS@(7l,ckC>c)_^49]AhNULjSN50pB@X,7@BcboVMJWMR:s5:+3f</'1sqPm(rO
moLoAUt4JH4,mWfnp!tK*[:XG$dHq1"3f$JjJhoWH3kQ+/"/'E9@mo#]A<P'&FT?T4@pY[2>j
E,%,=4^H-.M,+'A#VrO4O-(EaY<Psr^-l@-3VOMqnH3V1PH;J._o4M3F;_4Z0D66hn:4$W2p
d2,sSmnVPj[^f2u^j9Q%%!QQ9^&D[D@]AjSYF1C?ugVdl!s!$7]A0e>;k+!q?!b$p6B=G?bNnH
i)W]ABB\sj?ao`dulXXrVC<]A6.P^<!9Ue)Boeo*=t4:=\4n(1l?`mY(IJGD>bdG:6?Q2R]A2E5
?YquCsA8Qm2_mqCI:%Fp(l,T*D5^[j'&+5i"C[/7<.cSSjG@[=50--@jAQ&6tHj<ngA"]A2l<
IY/Ins(bU429@jrLpnQ4A-aJ>OGnRj-0%?d3l8;f)?Z_bBDq4,t.RGR/Ra.ep%)0>B8$^h[P
jj]A)Ah]AD,+$C1=5`2H\]AS#q>kYt,Kj0o:'U(7qAN@%K@,C]A)@0i[6.,ZchY,gqF-JtAhlTG/
NNnS]AZPaNZX..j!Y+^#LDTh7)ku>'9j2\K%^iV7<\N/-Nql3<-0pVn`S'PGRESD`GA8_ID4P
Q2d/^)a!GVEGLo:/tRj`:=[>;7dMg[gInGq6.[MZ0dY"4[Bm'@Q!s]A)&>Dj2$#mc0$6L*.(K
F&.X"SWs^c$A%6j-=."#+);_P?d$kSWY8cI03U;<Ea#@d_.VbJ9PkUsL"`rFHIcjGjP1IZE*
PZgO<53.i\(>D,BC?J8+F?:6JLDDb/$J7D?np1T<MBuke<*6X?bhk&eCM"6GO0?R@8U?F(<j
E&3(n;d:jf.Fk(S\[RdV;&#LVfXpa7,tnEP;V0707J#,j8f[^r^pV-nl>'&4C0Y;.)u7ja!s
noaEZHR#jfL\q$?lh?s!K#W*,W7F,U:UKqi3oU)_q0tK\s4emZr:p:W<LBQZ47HTu,lZ7ks$
%`+r6K`js05am1VQ+SI^f=r&cUCOHB$2L%oYLo_-i"Icli=%qtC:g3dH,.a4$]AY%^d%s5sE\
01-E)=46I6-]A2%HOdRj+"[pbHg\:NRTMipO!k).k(,lT^^?QOmBOH[`PY.Q9*^TWNO,RlFSD
d3,b'28%T3Y1+sZ7M5N*.'B5UrcHd;<UO1_uK,mVS5;krd]A(/Em5=?Ek3O"`tpN2Y*A*R58Y
1WX%=AKBK+?0na9SK60rk6]A7<ZHdc?3h"Q8'0E0Ao]A-:*el+?sn+7f]AYVoEQ2Ls,SEu`ijK%
dj8c%/Qf;njY_KGP.XS&k;Ja_nUJ-eF?o6(Ol++c8B8_2Y]Agip^A8Nk7&N`!8gESq8%Mb/0I
3HP?YbA'e,sQZG4_k6!RH858`9#4ScnNTIN)5gK,Go\_F_Xu&d90XbTc5m`%PlK<#73YL6FZ
MjRGMnfnfpFIrr^Tk-J<k37(1/=<huJX;ksI2[`&]A\8\Kq_p3g=$g7;(C'uqeM1b6`d_'p($
Q!2Cb$VP'Do\J$kTT#j0aki'PZlL:=#n[W$oE$"B(nJ,U=JM=d5kN4;.u=T6/\%4aaTc8k!L
DAg7OVlT'`H!I[gg+,e6:DMWT*!7M[kF:>pc!,rFFmns:a=XfLM\/f%'_oqh'C<1rNNTuKHf
D7^eu>AGr1V-#j.JE@5;/0YWm`2W]A4fK2X*]Ab,^;4:3diJYS)FnD%t&bDf%mCCWR&3oH;Dj/
Ecl)SXct;K@1Tm\D9*DT>HV1D:MX+(>_a5d3iL$8@s!YaPqeb0W=R4fPPi/F!A+[3l6'g'H@
/H7/pq]Alj*=D3iK(No3t-rHO1$E@9X1>[7Uh*sWq=iL)MVRtb.g&&D[7@/%4uqjoM6<_ATug
\73oZGl8I,n`hFfh<pJ#CQVqDoY'B\`ajGc"Tmd+UG(p6AGGE<+3AT]A".4M2(fJL_nE.Mrb8
X8BTZBq))lp_>\UnsLt^?)OQmH:@g1Qp[`-.5f]A%.9[0HY$1BXAO\ajBoN?/?(\/XEk>4"]A'
75@`@5p6oc.1B<kHZ.tgFeTPY&S1$R[@>nms7/WkMeHS7gO?VP]A0?F_/;j\6"phf^;380%o5
>aR)NnCkSLVrMk''+3`9#i8J(K'6(su#VMUERTU*MFX)'?.oi@%a=H'&1Sd62M"8qLk02grU
5e66O#X>3%0HMo!7Rr?hIO'thJo#WJ1EUZ#5[97Pb_%O))@M?Nm&bs*c9kNt+dn7!WJa(7<R
7M.q8t2@$De$"25f[PVH%l,$CSIm/oD'\%>[%&=/95@cPL^XfbBK\(-\C4<CZr!f<>DTjJ6;
(hRrl!b"1;(E3iNe`c`,^Ze>_R([^5"ac1NPf9-B]AZ1h),Car-&>!jFI$=Ns\V8hH'me$oQU
\<=q&H]A=CI=lK@i-\l4jAWk5)B>RQ7T3c.\`QW!?f=PH0]A""lg298/.hH;[BCH?^,JkBKgIh
5&WdTY&!Nr+=6!Xu!I-;$':3#_Skr#i3t$?s8gC'(po_nl3UN5sN81aCa2)j8m#ob3-=.Y?E
__c7OOF44TDHoqf5U3':0H`J3175]AulZ0nVWdpEEnEbX)m=d9gOnf\7i[jtct\X#KdjB0:tf
a'9OFsA;*EbF+cjF3N3MlJWu5ZOB:AASeMLVQ*Mf@djXN&h!kVT]AH-LQZ.)!nf5eOkJP?T]AM
LFhfE#.6cppXXYJMY=^NTHlt1Sle%eXg.WPPt:@82RUVTV`R)I5\9N=8nI9Jgm80FcbBA\:]A
[iG]As-Coek.`W"4(CTBGljMA&E\U88"\$C02&'hl*6W2hMIr:jfWBgkOQIX2/HAudc&COj(5
R\FZ%%#?"9@%#Sd]Ag+mb\Q<<BhmCnfb1IS^Vb8Bjg]ADTb=[[!cRbgB`Go:>%/MEf1?9mjm@"
oIk5p4`0U\Z2RCuRP'C,=E@Ct.%OZ1jJj6mmHsZ^d7_R,j$8,^3eq^=?]A:uVh8X\!NAfS8S)
:IDk8gm:&N1f0*q+WpBhEC9U_GclqAT0hS:!_`4d6N3Vcd1,IDNt?e9r@UA:9]A;,:)\2DrR#
tVqWTbc0\Y9lClp-'B\EZO4Yl+j]A.tahjuCElPsF%@XgW"4,XqL]AZ>8uV`?op96u2SKHAr>9
cCip2H_W#eo9K9`@_p^\Rcj.MJ-\*#LjD_Sp')R^Cjfc?2D_Yr3[f+hiDY_rX)XPE%r"tTGu
KPXSA"4'B51ja'L%XN`lgLo+XE:'o/lMM3SN\CInoWD(<4r=pt%u&\->og)Y?_E0IrM`O5a2
5,S6]AHL?LGWM!PUV`)?8a;^Pm!W9')9njT_m'fILRW=>neKoS2=Hr*c:ar@WZ=D`M+]A,t/uW
jSl1)hokP4Uj%Tj82ufXo"mgT"&G$51hm6`Lc.MXcMV@b>56PWji@\$`tT"kfu&;ZKMWQkd(
/g'J1662%NXVcR>E$V&,<fhgW.n9&NVeh%moHNBcQ8hWR"mlrp9dq_[7Ij+8O4_'#HRq#-U3
5H3,f_@E1%R2;[p)j>TshgD*>-Il-lf:A+l-P6YOPC.@9lje6m!m/FF@8]A7onB@ESh7nQjV+
Wh#QD67:p)RCtV,c_LXK9/7,KZ$(Kb:(M%qF3MHbnmc33mQuUM?(L@WORAL2kBiC<c^#C;al
:T`cU=;Ge.MpmC@>POX"YUi?>dE5Y!$o`QZ'!g_(oh"<u'r0hX6#5R.hHqH7A7kWA*amX!TE
JpWB>WB,HEPJ\"Xk03e[kB;g6;r?l6Jse?mAqRHG0uBeo[ZK,=:0-'A>iP(YhS>5khI@SHW/
I-DhnZsbJlJO,(&)#1LG"%_`R1E>BXuZ^Ik!C,s1$i(21u$JmI.L3NQ\1M]A<&lTG`PPPhMU]A
(U[=J:L]A9U>.c3#GtTAK*tiq<WmSQjYmjfN,^U"8qA&+5#q4J&[$Ok#QBg!nG6&dsJ#gJ\3-
da<+%Vg]A/H"^T'K0c+lmDaqH/^E+15bUE6F"eV1ZZHF-a\bmpGZIfYZ&:#qhB&!;?G>GqPrg
I:Lj%IFKZa$7,oH^^#4p]A*,N$lg;I6aKW0Yn$4DHn\1V7UfAua+hX]Aa68enqS>*h@oY9(HTH
(n*$<84e0g3DKrkW[?>&5.:?^lsg6<R-T&`3$hGG>uu2W9(GlqLWR#8u]A0"5^GPABJp4Qo.g
`^3u0$/l,3n#=,7/d6.BC(FhS?U:p]A,S!96pQ35Dg<8Iq]ABgA<ZfJoW8o;4dQ@)Tf[9>m%]Ab
L\n'UL:M8,T#[GK8b<PW>\2Na?.b]Ag<\2?_TFl/57DbJ*8sP,8=2?4PjGW"Z@Z=77<3X"#pP
L,TbcfoZ6U2Yb3a"sRG6gZjbcbA'KM>L(r1/P(dkOL8)&mY*L,Wo6o++U-i#(3>!t_a'@adK
@(_;b(<AK6MC4jkq:!*=IZeEr6I==na3ToM9;CKUBXh=nb(lq&fR#4gLenLT#0,"`:=^C6M+
iiU4HJbO>k8%$nW7kjrI`h#b@u(HPp>E2/'Ub9:0`tibIgA^K&tR"#?L`o9F@P79+6SNqEE<
Vdo<G'Y6>bU`a,[='&e?XFI!)*tSfrsAWNR=a=,N_pi4Z;T15rmt4qq:Tf$FM7"<Bc!H\W<5
69\fQS7#L5Mh#o(k3r'fNWZ-'Na!A3E8]A5eF5lkuZN3&><dhY6QY=P,9N#-;_JEjWBE&E-rg
KTYh&URSCR87]APYuFN\"<2e/o0U9([[Pe@>=sk!qB0d]A525dFnMo]A5$rr&cn<<#\5;^[A\ZI
KLLS1s)@iMB3%k)=l@'P!nq?FflMsEAkdX:,8-d+Nm:N$&k7n8>+_Ij]A[iQ+_,3dW`&3"&n;
.=8,'.r$'kkLaOfNF"EJI>VU0X8Gmbdg^"bYII=?GE8eCRaLK*7uOacIH.6Z-sQZoTl:9&jJ
p"2!bWl\R)Su207Z^k6M+VP\F9=n:Q"enkH:,'G!+4^'K5_#j-TZ-e2U/S-dEoC7>]AiNF+YM
#Q%=cf?YOmg[#'ToL**iOk<Bo]A%fuOTLA4dcL,+Pk:h:d0<eu"#"JJlh\5*-;UhcD1Z8o.3+
0'Gc/L0XON`sRqiW/72eqJuFr+#bLE^LJ-J"V>aQF0b]AXL*#Br>c&DI?rih$1'LDEJN:FC'9
X?(Bin\#h>MNp'3k@eK("T^4BC5#2md*]AC!%4u[:?1j"%5%M#>-ic6UGQ.S`AjG'KN%UsM<;
iKO174T6h)+Deu?kK!+d\#'#>=9Do?dpEWcF_ZD["04?o=`_p+Kd<&=uSW:LaLm$j*@abG9O
'Fc7arO?VH7L;3'C@C:ota#DKYJLd/2%<=o*$+i^9E;Or"@B5[kN.]A:8Xppa+)6K_%@(d6r<
2<906A=9G&?N6E[#@*S<.ukY?f66&JRQJaKn%0fLW[Xo"I"-5J.^h?72E(r#_H&SOIg.(O'Y
EUc%p<jRJX8cu-A#hS-:iHe42=I]AaO6E=a2Cja"u^^#c,/.-UYDA6Vmq(jS%Ou#F@IVB:o48
-`C`-[^e>*Fndb9dGf3A4_S"r`X2ep8.:KBlQ\>c9HP'TqW&W[4?46lBN*j.&4rWQ!]A.-_[W
]A+IF@9ao4)rqMPG;GFO1D04N+_-eYk9$G&2j)`UZj$jF@OIn'7t)<Z03&';#Sf@3.*,65U$b
WdIA.]ALAW#js<>f#l@sgjBZ3!d^A/go(s/.9VYb#djV98hgF.V-r0g0R+%eU=\)9>YVg.(mh
C2hcDA@*5MeRp9OFL:.1YE*EFB;bMFH(B0h0_i52E((u7Q:>t8*'lo5mV4F4,>\Qu?iA[Ho.
891Xt4$WT:nWGc<Y?*qNMj%_MZr&.j(WnrJT.\[;La!A6R3Qg'r!%5si$H)F;gTj`%NfJKP`
Qn^Lf'H;aP5l#<U9VW#Br,rjD7OKF$OkRVQ6b,I:U+V?l.:Z%8eG-VmJ)*+Od0Jq0=9*We:e
);fSY4TNU"_h]A/M+.%!qdo8DgGG$8KbNZNB_ZV0ZXDQk@/Y?:Y0UIeY9m!jh]A#4:,L6&=)1Q
5cL\Zn5JpZ5ZBZc)pnH6RrnYTBp7NGfC"HQJ#4]Ap1=k1aTP2;*0_iO9EKEK=u!h)WE7p<@!Q
CYZ]AGF?6%nTYVCnZ>t4]A//ESks4nQe_/[O"`ka,>j7@h;/`ejF/)I>/E/#W0cc8/+Di*?lCE
+k9A0G-&>XN)&XA%/X)=gKsX^_-k1qaNYc57fZU3=Q59dgHP.X-m=(SN2.S.)%^2Bk\%o3*$
D@9KD8doI(1XZihH,sAic93SMBd!2"Ql3$=d6ri8FY7`PNI<LWq&&3_al^r\V(*Di$%W:TtD
]AK:s=lI2n8l7\1hX-ME]Aa+!%J0%-62DQLdLUi6ma0s,2"A`@p>qhIg^dj!Mp>/Aj!4/o.X:3
U&#s?U('-AC;3s;%_#NF41bttP[qY\FdlZs$l.7WTPZ#7Q?oNr89r"*Ms'fpW9+WI\5m!22g
gTgZENq<\<W?_.Q#9HStcaAr$Xn)D%2L9+'\l8so:GM*c'4%[R&dLA8DD2El^O=e%C>a@>0Q
\h2@LD@dTs'`=b@FgALrn2"0m<C/[F`9^iCfL-$KVr7%^Doh=.QYa&/A0dHte@i"*LuFB8Qc
!9%g^^1$(aN!&`\G!+\9KB_:''dM8m*O9J9TkDpm,)BYHClZ7EQp:`U*M/$=`YcS6u@^@t*2
%+Q/iTqH`f1%[-o$P&sk5aoX9>8@@RT,Ir]Af9WqLJpZ]AJ_.0AO-[:>r7_Xih"C7.hfMuHe2]A
^(QQ_9RCjSR>=^8;`pe4Z!'kD0%Ig4m99ZuG^,uN9bKO\2K;!Q3_NONu_JP4iP`:.F$fcK.0
:3A:DO,p+lM'e3,H@`93:+):uef9;&@R;+"+@M'T@[&a@:DT7UnTBWgVm!.m#`S8.>VkAIcX
STDD.>6R:5kua;ARUJ>l2tf)Ua_0=\6KqmB4>Y=$-;8'4\h_MT3g)d4Rm?!?c]A;eYj6IW&J6
2$G1Gl[LYc_a#25WC5D(K=#oI0WPc')=N$B8X7D4h\Xs7H9)>g>)?r.<:*9m%:BE<j?e\cK!
NZ%D)S]A=")b"\f"+,Oe'@(0=\j!unpZd>T_XH8g4cLTO[C6h.U'>8IbCZ2Z&QtBUn[Tn;*c2
>9dlW"QF4s\aW]Ak@F:l8Mdlh<:kfc#3LBk+9LS[gHY_lO6>Q9th-:A!`dra3(66PHdQ&jbW9
hd87h,qFrfpSZ6A#`r#m<)d+:\ED8B7Juk&PVD`5>(`9[B=10(KI]AHHL@ogS"*idlc1ABW0%
&leR]AW/K9sU$5EU=KlW"nk4#%D_f+^sL3Y&hf?N`@..PR_&W76=;^5^fJjP1Nn[V.nWig`kq
9F/!eOP8bL.ATN(Dl.9s8-Wb>p'i*E9h[47SEBWEH6Z3S3]AdZGbe0$QdF7>Uj7SeL*rY$9!8
l35WA7_AVXX,E1#$??oiNBOiQ>S\)gopk5e.1dr73Ce2b.SfNH*34T^t??@c(n;?fpkI9/Wa
;>mqts.gL*jgOoT'CHb&nqiB\!<R?5QS>CqCE;+]Ao#GMR9FrNdH+AkkJO_H]Apnd0HWUh(4+k
`VXS4kW4fS$&kgTB]Ai:]APC(G"!a+Ecrh:he2@a&+8OVkLb=t4s]A=^+1&g<uN`!MV457jXL?:
4Tr9,ct6%%)p=\(hq(l>@H>ST]A00o=^A!HHs$7r"l!SJ9<Ap=C$6#r;M]A*&oubOo.YF+@DB0
=LqCamj)9^K]A>RXH6*bLmlU+\\Rl+@I8P9C1HU,q+jjWu$f#O>gprmg(2X=WM<O\]AV[KGKpg
ZFR'/fh[&EC*d-T3FHM=KS6E%@^G&D$JeRjG;"J)OGLc^]AM%f/Y-:iSK(=M>d,6Zp\Yf%H=\
?_KsP))BrRc@CK'=%jX<%QXR``pENNSV:$c%&Xb7#FkKg'U4)7aHRT1M:42DPLHq<TAB!.FA
eOtS%WuH.driQMI/\^t&EK#6rj%e^+R#G@ed"*&(Vqg^Em.7Y5^TbY;T.RQ;>[hG-lmN>#d&
rm[gb.pgE0@A:EcJrVTIqB]AX+T8Y-s4/P`<"iXWq(mS"YBC,@)A0X58bKFDLSsrO`.76lBdp
Z/4c"$BX*&VbKRFHPJi]Ae4?c_.`7C)oDkgu.<TV.^(3usnL3D@HZ=2l$_1mS&CfcrZM`4`^L
T@IUC1"^G[LT8YSRR7Z]AkW<i\aJh6T>EO8ht-4"4A1r-o9u\$XE9j(prJUVp\:8;\0]A'a4A3
Ci!'bFU7>sA*Wti_kLt55\J.;RYc=jo!n]A7iAJbJa[2t;uh=j<MtEH$hQooNT+>*5#\gl?(D
\H@eG1u;5ji_r-I!o6H8A(Y42'lO`beE7?oA#lfj^R=Z7=-CnDK"We!io%gk_A04:W%`YVWf
CM8/.(Woi-sUS>MC-I!@C&;81(VcZqL)]AJ\(!SS;VSu5PfJn8nUk2I17(lI7j`>c8<Sh_Aq1
P4W'Zoo3C/N(70DS\!+]Aqfu#Z`,5c#-@tWJ^b4d/%.]AbMa?]AC.MVJFf'!F(3:Q?tbc0&,!Z!
1P9CoRbZ%FsEh$Ng=^]Aj1/%TA[lYE6<9[A3n/E<$'X)1CT^)FMar+@hs@f$>OANIjhObn<FH
>0jLr(C$5b?@K;s#N%,cYScT!o!iB_if3iapKRA"rZoG8Rh_R_jEp\4!r:IrT6P+9UYV:!18
BADHk/fS%rFG8>C)V]AJ3)mFdES'HKB7rS@V:66[/T%:&B\SSV.\6OSHiXn<QQ")=pOK?g7lj
8^KW/GD>:7&Z]Aq*/]A_P-eV@!7s$iCfq>W-::R6`7CHo.qeDT)t,f`$O(o&;FZ*k"\5]A;!!6P
am14pVQ%X&V;'^6q;3&1K)p^jf#BWOpk8XVgY40Rf&):L*Ut`Y_D(4X!83,p[Dlo@^)oI]A3Y
BSomT(pk)EA3Y>@s@*N6N]AD/4sb-b.0CTJo',d:,fD;[Tgtf:'/:$?`04Dk`u\NeP\K-gO0P
Z%8-q5H-:9&+"[4C7)7hPriqD;n]A@:rJm5nVO>-8CO&@cq('![/F75GabI]AE1t:t_A68#?bW
cRt1KI#hr(h7d66X>+%WmeH+YS9H=p<]A+ah:%C+d_nj?h+O2@D@X)irpQ_M%Z@>n9039BhdW
?'=%&LTFJgI;U'$MZ]AQe]A:b#H46Jd;"#b<Pm.ZPdN!A=@Pl/qi(%k.S[b."436'o:94gW!n*
W?6*6uG1:Is^VA^GQl>pX6ra-^cm"&]A3cd$2Y!Rtb<cJ^r@lXoi$kicG8"))uZA(,oQC2##_
OrUje5'7=^Ft=H)M%#i-<2<KqKlr-BJ0W@V81%`>i1DL,#k=h\YWnbhf+DtBjb:]AZ?#,!QSN
sYf1nT484/[$RQ?]A$3cr+Q\GV0V-C'Sdp,ha3L/E;Ual!L2gUC@0;dYrFM,k60F,l[V=2`7f
'9oQVgq(0*4P+XrCt!;l1J$9kH9/tPi%]A`?Z[Rk^T;Tet6n119o[U5]Ajj=9Ee%dGo]ADLGtI>
Sfdkt7?_JaTYQ,Rd3j=LcE'IOd+[;ENrrU?Qqa!XCo7(>k$[+*-"?*O:<&S_)7(-sg(D1>\I
X(nU$/A3Bt4pRi9P]As8nh%/4VY]AUYa;ZXEA^9O"*15"CaaVj]A`BZb:D!\[WVpoY&mVE9,+S:
?'Slf(jr$#`EFB\V^$l`*A!#GqVSd'R\cc'lVcO*7M)L=fd]AB8LJf;l$k?%<%.3=db=*<ocT
P4o?!HrTUN#3(L&BTmnM@T,j4N;8S@hK^ab[-?-JA$jAH3Xqmig("RL)Va\$!jm'`RNiuD</
o!3MO8&pY.^/Te":+4oX[(.M3A(2:pAZa>(>?k]ApNXk+P)$eA%3*<91`]Au`OJg=ssUCJudg%
7Wo[n2&V&PTU`)"-r8C`u?u>((lSZ^3:Yp:U4W'Q#A,Q[_3;HKrUNYo`iE*TtsCNoCk.qt8-
Wh5f_"(@aD7om;[DK+EBZ1?-W9]A(W_kMhA7?>N^:XAKU>#,>7I,6t[i35nkm7N&s35:(NZ#/
2rRg%g&JCZ4uZ4E.=cAoe9ML'HIeN!LU/#+iE3*(ah#_eap3^Z$hZ:6__1TPCq+gQ$f"J%g'
_/VdZUi-X^E'3Mtb'`:*!EZOI^H)>"IhP=k>q,R@gO,%^SLSXX7r7StiU54"JQ0UcApPb_/W
<)[:-"r@:\fNHSQ%u,8I6Q:5.Er8kJpW#9Ga)\CsN3[-2e%i$PTMN*=:&&(td*bTuPs5%<nN
FrV_.m3B8JUi;T&Za3#FI:RF[`.;lpg6)1%36?#/EXuNs_:e+&P)`N&HW26j6=As+/SB*#Pf
nCJ%K\F\&km0D7Z0+C9ZfGMAN,=2,)MQ6!9(L5K!cDuU1.Z%^oXA=Y+h)_3)2WAjoY1U!h./
';_'NEAJRIMK>3X$"A9Wc;Z:B6!'//=g)(@H_>kkHf56]A)]AWDMN!94?K+b$cYa;AMe;Vf?G=
Go*6E-GmcHt#W"Z;DAE@e1o(PtOM+6J,HV^E%"t^aR?6;U/dFgth]ApF<pYe6#7RD$V_;r]A`e
+:H^$lFV9AAaq)'5DD8to/Il]A^_k?tm#I!ZYe3R,a9um";7,Wq.=CB&`TXAQ7Ht6X*)N8@P:
@>hiD/pk%em'3.FWZfTRh,t?:\tAD%R#3he_Zj$?k)@Ph=^K7&851rS_7G@_m@RGKpj1\M8t
g/@YiMpU9(A;6HLVJ__KLIU<(fTl1-n/G^5R;-Xu:0'*P-"+SPA=FQ8pCs'^+M9Yo[o81U&,
Q[T"IrrBp3p%i>IC!/>>&Fk*h!0M<QXePCpOR@p#J:RWqprA!f"g_mPB5dJ$DK#\l,q>G[>k
D%#?7Rm&Do(LNCRt-<9&.FCgg0S\k"5?q[1835d+K=O(f/1E2"gFlZO^JIq!V#bLGU4J(ohX
Y-LH>5aLcKSTu'Nj87\s)dOm3*5)#X`DFh>[P;Ss2&EEI#eOR8M;,%-Sa^$UUZec81>.,k&l
U)5*lP]Aj6[M`Df*+Bc-@a0?F9$]API<Y@"IY.%X^@8SA<Se<#-UE?lWSO\jIT:fAEK]A*\4Y3t
V4`U5p+nFFI?bF(OSX-qc\c'W[7N,irX2Oo.5$+LFT0N4!I)+!bbauH9dp!&u]AQMQOE@O9&q
fLH_rkH''(SZfUh:8)umT=JkUYUH3qg7QT"1\0&Z]A3S9j?Lg'c-j:Omtf2[']AC<GG(7/hjdY
hA.*Q!XHbof+U_K0P&=d2'\YdC%Wd6;\8hA-7qDo'W""oh^C%HCA>n+@>&$i\nC1$8GCDCYV
)=T8"3@M)PK<W,O:&C+eb*bb#,1C")Xgr*#U/QXFSGupF=X<]A>Re/Ng+gCM7f@HR!K=kOVk)
9st\U:0==S9C35Ef%DV8O1K1RgHE,s<Wur:CFI:)J27>7W'qSKNR<:\U^bBTJL='ca6jKP4E
*9W6'\Hf`f4e&@J&S_F/mH0/#<'HH1A0M>A2ke<`88Xe>()c`7B9Zse-aZC)d.94qhCY!Ja[
mXR;Q:RRScWjKtU3$9nQ6Yl`M&6W3_GUc;m'rpk/QMEt8uVK\Vt4IUM,s8LWg?)61<H8nhH,
Qh'CI0%XFYO&eU=>82<BR^c71sNr@7kWS_DBWe'`LOnUdRk_t.A=LN<l;1R*NHB>On`LBU?X
9-5kG2pkNmHT+W%J4$&%f@n7KJ#Z@0>'+#t9]ACu>,ZiMG(=L^DC9Wl]AEl)^8,YgT;S\sM[MF
>bSZtqg.^A6`W>',=7]A.q]A]Aaj3S"[R%Vlko5+b"4P4^`,f337Wu^WLScqpDA=^p%=dPh@!4M
gnn6+U7pfNjOdCiPF-Tp:H"e,Mgp7V>ET39&s.tGDFoJsi?/#<6A<uXgh:GWsrul%;J,'P5+
.0E9V>T[0Pg."E)aVK0[E&"HHjGfebEWD/5Tc&k+CakfHdLie]Aep^AP5B#)b1bq%_]AikiZk5
CX$\;NI#+.s_"3GKr'3Kl)U\E:3s*NV\Tn`6]AD;ZJSA'fp^8n*c^;]A!pmML&.)VW8rT9I#OD
7!esch\eMjGA)p3_[3k/kj,mYMd:U:aGAMMl=r57&[[Z.A;L%2cYTLI8Pf"c9c8u0)O7_9ni
ZWgiBpZlW':ccTmAZDJsSr"B"5U&J.4rBBn,_EkfXK5a`.t'\iKe0JTT)G4oSpq@GGHL_Ehc
m%k"$J?QYPoSVa]As:a`sG>"o.fH&,u.$^0^s;LQNRp0W(qHY=>tci28fqJf5&5fqXFC3/uVc
2DBCe*p_hp4&Yac?/cdrf.^TD!rl]AI6SIR3qgr[cm7&5h!DFQ_t(S"SKDsOmXZUp^6\]AADd2
?UIP2E_%c;EGDXhM^j/rd0HgU"rH#gBO^ZIh'(L)L@_QRh@G<oMnR51Dls8OeOT'a/,o<-+u
26bgL4Bqj8B%MZ&qDso3rP0>mfR=hpFfaG;qR5PZY/&YukPtS3&*[*-s/hblGWP*.T2,')HX
;)h>CLLi`2\5=Qd++9AaB81o3-:+LRm7HpX<_UqnE%)F'W7S&S*X>I,fo']Af-N`mBU:8Aq+?
_!@')mp$k1p^YRJMCAW</H@>UmQco2?c`i;I>c9h;IIBMqT?$NsGFu'eGPQF8mG."+g,I.(l
YGBi=$N<as6maCfubZUpY().OZ`/]Ah7(Y:Vrd4DI.TUFbjtQLj,]A7&m=)==]A;f/NXs/@ACmj
Fobu_fW7Ftn<=8hRLmfVp"#%':fI^AkJU?eH)b]Ac%\J$HM5hTq`O?(>MDqgS.M9W.ZW#HW9A
q5dV]A*t`VXYt8l'/)DmWF<X%ic\Fkro?1Lkg"D"%T"`IQrs`*:PIpd2eag7^\"noURF_)ncA
>o>p\VY[h`jim^W5@33U?Ed_To><HkgM-pj-cP?.\h#kCV"6aH$-[bB:GaVht@jABSij)ca?
t*qsiMhi[LM4>$Lnma!+ts0l4mhsF1.o=k0CT66ag:&he#PF92SSTOkS*;7;5AgZ@:<iWj0s
-DC+s"nl3cDuP<WtUOirP%s^%q)m^f:LjnpMGA,d'q?ms8UdEramjg/UC2$=;=$@c*+jpWSd
84"7rlYg&(euM[n1Is7C57F)o$4RF_5LY>QJJ$2%PO%Q]AYb1RC.939kQKCk3SDJXTa2Kl@G*
@L.!L4*b<tisBaB'=;QW-=&du5(&Ge+qK`dD<1s?^Dss#&'.mGOJB.9"cG5gPCD.<0Gs/.Y/
4D6<Z:`q8I2us&6*ItOtsOH";Q1N<I(6X'N@I%9>qp"+@Bc+Ddi1(%kLaY8lDT/`.CJ4.TdR
gVj;RN3Cthn2KTtD@ut[;["o<o!aak>J=6e)!M9,SHi`uXFi88rA:rKOU)^*84q4S@%sBp4U
c\_b36VRq#EW7%17*Qc_%0KM!Pr"eT^.0E$ET;>Tqi'0%hQAb!@7aNL'up?,m2&:i)'SG%2^
s1e::BuYT*(As-jrH6/2PLorf=d.!IF#,e-/Le.)QGO-g2RI\7'XI+T(9BcE9Z[YnmP/R.69
2LAEhW7FhJUKU0qNbTTb3i[=%l_rB3XeT&i[>nuBeN0^Q<9pL6;6?Ts8%"@l7G#+;eliL#PB
`7/4/#VBE&>'o[>nuBeN0^q19F75_i6ib4P4ih`:J*DF'7[jcia=~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="33"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="75" width="375" height="33"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<WidgetName name="ABS01"/>
<WidgetID widgetID="b413c587-cf50-42fe-a23e-e83e5f480df7"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('ABS01').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="yyb"/>
<WidgetID widgetID="e9bf2587-bc3a-4a08-9a0e-1aefdf7b7eed"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboCheckBox0" frozen="false" index="-1" oldWidgetName="fgs_c"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="true" borderType="1" borderRadius="8.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="微软雅黑" style="0" size="96"/>
</LabelFont>
<ValueFont>
<FRFont name="微软雅黑" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</ValueFont>
<controlStyle borderType="1" borderRadius="2.0" isCustomWidth="false" isFloatWidthFollow="true"/>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<borderColor>
<FineColor color="-3155995" hor="-1" ver="-1"/>
</borderColor>
<iconColor>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[营业部]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="GSMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[Branch_Yyb]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="193" y="8" width="170" height="35"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('ABS01').style.background='white';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="fgs"/>
<WidgetID widgetID="e9bf2587-bc3a-4a08-9a0e-1aefdf7b7eed"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="comboCheckBox0" frozen="false" index="-1" oldWidgetName="comboCheckBox0"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.combo.SimpleComboStyle" isCustom="true" borderType="1" borderRadius="2.0">
<ExtraAttr isCustom="true" expandIcon="unfold" unexpandIcon="fold">
<LabelFont>
<FRFont name="微软雅黑" style="0" size="96"/>
</LabelFont>
<ValueFont>
<FRFont name="微软雅黑" style="0" size="96">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</ValueFont>
<controlStyle borderType="1" borderRadius="2.0" isCustomWidth="false" isFloatWidthFollow="true"/>
</ExtraAttr>
<Background name="ColorBackground"/>
<FRFont name="SimSun" style="0" size="128"/>
<iconColor>
<FineColor color="-14701083" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<watermark>
<![CDATA[分公司]]></watermark>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="GSMC"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[Branch_Fgs]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="12" y="8" width="170" height="35"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="fgs"/>
<Widget widgetName="yyb"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="23" width="375" height="52"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const kj3=document.getElementById('SM01');
kj3.style.borderRadius = '12px 12px 0px 0px'; 
kj3.children[0]A.style.borderRadius = '12px 12px 0px 0px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="SM01"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="SM01"/>
<WidgetID widgetID="25aede64-a99f-4fd6-a92c-d40fe5b866bd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="report1"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[495300,419100,114300,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[304800,304800,800100,228600,1219200,1219200,647700,1401221,1612669,647700,647700,228600,304800,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="13" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="true" topRight="true" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="1" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='width:100%;height:11px;'><div style='background:#FDAB07;width:70%;height:100%;border-radius:2px;'></div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="1" cs="6" s="2">
<O>
<![CDATA[访问统计]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="1" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="10" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="11" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="12" r="2" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="simhei" style="1" size="64"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m9"R3e(M0if7JgC_/ZOjFc^SGU;fYq*\n3T$7ZB4,Ua!sl"ECX1'8b4<&14X6qUF['E_6"9-
OEm&kGCsQiIH38-pOe+beG?bJ!5Dp[3@OXF&Q'li"e*/)NrZkPY$6Hgbm&4'>(%]AM+Zo>m:@
]A9Fi3>iA_+MYf6*8\chfj@]A;`r!e'6Sn->!f$2Kbe7Q2Mf9lGelQQA1GrmaW%'45\LphK-J"
WJL7=hae3J]Abb+HLp-lj+[^fml]ALAYOlX0H`QTE9@At<[bpHL^,9eXH0V]ACMk>sQ.XgpGjg,
?IVra*Em\:WekH('G0JrAjbj7TtF(JJ:rEZLLWmh@YHE\O+e>TDC^1Bs)@FWL<LKn*G&RfqA
o7#*\Wp@+*+i"AuR(A`mh]Aa]Ai(qXs;/>(X`g"?Cb[J+Oed9li^Xm$E*c\A8>L?XufRkl/'HS
cq91PUsP^u9c_X4ZHms1NkYKji8@cP8&#SIdk,s"0`>FJI>]AL,l(@s,Z=oS`R\frc)L$s5I(
sO3c$u:Q6/Ms-?CZ[*_QNXX;9q"EG>=Uc3mCCC%nT-TLFq:"/rl+tHMWgFK>3gg'R5Rl\KA!
Q6JRn@#*CU_i@J'*%8[nT"&pbOZq:$)L[oOd)b@(K5j^4^iWG/KGJiG<m@"(4psI5=Gk7S=`
u5=g/:q3F]AM;1:,q@Q;%a<p$:aD^Ycnp4[nLQQ'!qdqH5/j?fe[)&m.<[:?KHtY(\-7<AG=b
YNFs+KRH]A,AM1<]ADjP^b<*_%FErQWKm-.J+:*$:j*Us$9kkahIqr'RJ:7mL)>^CS!@If\mX:
;%bc,CI5gPTnN-MBS:OD*9'rUR0HP;8>ocW-:!i2b0g0C/!`^$0<OhJ!2>3U4.l@8/&cV1]AL
Zfi_"G9LV;+\je-D`-V8-UD&%HA\XD-Z8)\JRY+TugW5jkolJaTP]A,r6g]ADY-ZLG4_E%hoJ3
mGYfJW7]ADSnEJnMhX-YB=gb7Yeu:d.A&Q(.LTgaqmAfgAj(&h.DTP5e$&/GJ5,YCVa]Aj@2,8
$Pfahj0IX?6)!N1BSgZfJ04l#>fH]AR:o'=&la!):F9N;IYOWW"o<h'<X\oeuRM+;4L2n<e50
VqMBGWumCU_qGAQ%=b<8RQC=WicfdmKk_r6I6oN[iD@ut7KFCX8Jbt6UMeC_1t0^j=&B(m04
<$_]A'0XKp7/fm7K4*LK]A/mI'L'(B8.Gr$&[u)%b&LTN0WUqmg9n0ckr,i%<CZ,^HpgC^MVk#
1J19cNn/91d8>$O%@9Z,/C8_3I]ANTKnrh1/7?NO_//A!004)sCN,Xp;%g=7-A3@I7.L0+&^<
6RIW@?.b+f1V-b90NWmqYEO]A4/OgGFX&g@kEm^<hU26O:NaitPcp'!'>VBCfV*sC'Li1#/**
AaIF5G#2JQ!0Gk_JrS!qSQ*2g%.o"@SO8#n$S]A6LuKLq;GYYKPT^f@i4=]A6hCBdbia\WT@U>
@TV=p+VgQK(/`UWX"WtM`IUm/Ti4NFSXd>B=kTk[_XjVsbQB(jP_cB5`krX=98?(fr2kE)IC
$>oSAY)DQZ_Ub"N<sLaS2r#.8eu)YF6l]A&@!Yt`?gjtfb\NJ.J@qb]A:bD-Z/Z0:ERU(LGEAF
eq_DQJ+_Oo!ijdc0oYd38THiO$>'XSms,D*q5O8%),CLc;U>`D(MG.br3Jd%,UTQ^lqo=@kF
>0goh_D77/EC.D8=lpPWpW;$T/8rAM7qZ*Z'LJ>5X(lgh\_'GX@J$L_0T+>86m!j.7(9lg\A
OR%-?LTCI]A/oh6NZaj6)VQAbLM[F'-nKJcWSa4MV"U`H0n,9hQm3h*642]ADoV7d=V.[BHZ-8
2hVkKQZ_.gHLLh5\uuNJ@*'<1C_:A9A"GRu>1oe>]AoV7BhNKrjS<;*Yq)3P1S!mU<p#A*$D\
!0]AlPVoY8ietdCm]AZ]A@ZQWN[*WVH,&/#N(rsf;"=%i)/NeXGm/b]Ar"tCRh;nhHQR)bWD4RK9
@XrO7P;69*dJQdd)>KP?Uo$o^o(q?&#[;%s_-3IR)&"LF#X]AoZf(iZ+,]A9DNKfT57QGNh4eH
*`oqFb"Pj9MJknT^2;I"ieFmbON)$j^!I-+c;M^Ei?4u?-/hdi@<"c/c:WQ%Dqs\rV;uW6a*
&B(51G/DIP>GOYs$o<dW.m/eXq*i;llb,MC?6IVGKb&m%%eeBL+nk2Z+Q(_FFGU(,]AAOK2M+
<]A(@>ej<+EOB(qqG!=$0H0.68+KGFG_?/&ikOZ7^23PI>UhcGU4!UasF-,)**LZOXD20=%>Q
d7Q"CC,AFG!=N02bfkILTt<Uu*oXFhiQ_(qp'Z02)X;ouFM_7Oh<m$C0m\FNC4$o#]Ajj(QMk
3n1VT%EQ[E_6D?;t(Sq#8LGj$+3_#"0B)-4D>F5=E1Zmf+&tC^fP$p^;_3G3JF;Ug*,/TH&'
n.8)au1I`a9eK#C*6PP.dD@0QeN[/dOB^kP8tIefaeCm,_sDECYBq#C$r=rHjcb^a3PT"12g
@h0su1Q?O@J-]A0jX,i(L=]A1^$nIqIsM+:`#-Pj%!'k%qX:>l+?Sh^u#JTe&A$dlQd.)Du8<3
JW#ZmoT]AS6AWsYhP))EI>/Ahu(VN;<L=)W;WC\O40o:E[e()]A:2eg<AAlWADc2+tC;u!$lLL
,Is9`$DLe&(;n7cK@#T[Q,pYLb'\kKSY_hpNG<,!gRXE'p<mgLYTO/)X.WN$)VYg`Oui^\^9
/5O1d?R8a'X-&E_t+^[E2UM$'GAn'keFO_rWUsa@f.e)`58#+^/=KuN[fIi@_:EAgqYTP>2G
O[oS&(@WEkrQ+df\V&Si,piU,\#c7a^T6Q9(5P"78im#*aX_Gg;WLD*cAj9R482S2Nr+?hSI
2!k6(=\Wc>#G@\6.e@H]AX%@6N6)@d]Aat_p*BW@I]ANl0'W+EK^lFbA6O'jX\1phmFk-t5R1M9
%fE%_Rj3tu&.!S</>Y(ggpfNs-o"mdVJm<8<i5U(q2`kk<I@!J7Q]Aa4iXMba\P0bEQpC`,cq
_7G/(DETd#iWHG7cicnJqh,G?,D,hc-EeiGeYi1jY0Ka\YgtVb?8k-Z[_$QKU`(FR4*(]Ae.;
b"<r(hDm=V4?iBUGf5sd9Ob*7!5Tk@,V^8-t0MhMSD!;E?<ce!#*EL"[G.!-2[q*q#Il@Eq`
l=#42D')b,?"F'8J?7C)S"NN(lH%4mLI`1`qGo;Om^+oiLclj(kqD6l^If4\hheDYLDu65FR
i8+s(jbIh(;`+60>EJ&a50_9LD\<Ci:\[f9"A$uf1S&r7gL*Mg95'[219WcQkXLfJq?%L`2I
STXd_/QHYAL8T2-/6hlZh9m`6E8n^532?.$s.NeAWp%g-Cdcn\j8/JoMoFj7XD5akFktB:]A/
6%><2E2P'#=WTCF9QoJN[CqI_,gK0["(Kd?\jkh(BFf1]Am@L0P0f+!t`O"QoVNUk`aMLGBYu
WiqEpq%YY7@!lI^Wh*W&E;Ve5P.YWd_mB^UG$'jXZ2]AMJ08o?SO3nLh0.t@7"rZ6NDE.F&.a
F'#=$4kFGDEhsCKkUu0`o?Tc%Uq+mHp(-]AahDF3C(O8@^omG2N%[,%HMMV<>\Z>_XX1_.K!2
gA[U0O<6i7e>%<T:*4&u+5di<he!\AP6!V3n*\N54Q8L#[5-DIQ>m5VN9UQ6o591]AGJ8UNHQ
-o-QlVKGfq3/q$5_f7jUp>Fe!pT4F)\ok0C<nciMnCX9MJX?8PZ"Ej''u?I%\2Va'6>O)&Je
u)pithF^GionBc<KD<kOIstEX3.'@qu[dLUP_&Vg67P5ZU$:VKdS`8?d!ETotOWQmi(9E&Sn
:Vt+\q'^')CHO;/7bgX#,a"7_jQ'bq_#l(t/cl&aVa&X.9PI]A+.?0.hX1,gYp]A(MOXi%BE"G
OaB1O/2Z,.<tn0]A9BEGk/JEO\@I*0?s;KfRcOA6nkV\fMM0e`<"XdK\-H5gdF-!C2Ng%MX&7
^DG03gp!1f+aHEJfUYeXb``$E2DZeE%T.B8HI6:FV\n\ZZ+/^3[2q]A=Kqj0hK!I%:"VL1Ej8
@4B*ZK[,j1R#JXW/h%dGm,\U_3\c(?AJ+7f1kFRXE:gH_J$t-7d4K#dF.GFnSHtnE^[gR.IZ
g^i[EjnZUE;H9+/C"L2BM9QH<ce-4r<1O8P00$&l!$%NWpj0Ll/0Yk/Q<<7h\lG1VFki]AcDS
lKJr*84AtC'j;Hffi#=5FD5.kCO_"qDI<E@gFC1i0&7ZcX(ec_%lB=DX"EB84_#E3WF*as?p
m4Grm"]A-tS4Fno-K9]AJo-/?kWjRAu*0)!UKdtXrKSdo`DRY0e#kb<%1\"SZpB**\k(=073NX
'UUc<3SkEaW5GCL85`32YQ,V!jQ,O8$YA*pQn<7QZnT?ENA%<rg8e5(;UW6^R&a6qE?/6d!o
?=`SK0XXqfpUYJ]AVOu*Wmn.n+/3,8`.SI7I%]A-?Z7L=BO4>?[B3Ibar#.mt-a!N\84$_1#X+
P,:$<<4t)]A4V?1du-_1D:6=^;Ti0Ga`<4:'Z+&1ARR3]Aj73\30V+S9Dj5h)A!)d&iuB_'kd>
c@k!>GO!43"aEB[4mMMKcmhSbO4bBsSVU5^AH3^k[:uu+WD^h-IS!<P0LrpK[Zfio)?%<r$X
_kd!GhEYI-'C-@MeiT@H7pWGoGonK=+MrR^@4(a(s-r\o#[C'F;PCIDLMB8LRl'S9'+Stl:]A
u@'CZf;HnatFr(?-)Q:`JPl#$@(X.!?3qfWDkd)YA3#"j:`%.lu^YX*S9q4YoHh>.^PlTtE"
Wj@Tl]AqSQ4K5_c@FerSp2(;%8Pb8L:n$7XrrlnA16as3,k^Ei+;DOb4o::06r:9>5IpoM2<`
j/>DCjrrM^[O'jF]Aq+g,"J@[L$$Q\Qm#9[0EFEmn4QAc&^Xr[qtk2`>R-o6KuU:d:@'B_Jke
_/fVZLX$k88h:l(%Kk]AL<0QROTC#T;3%on!u,*/W.K>k3Kj1C@tZ(ds3VJ=[j,E$I-/k-AEH
N2h9^n9`GMtk)@l&:SsEe(E_LY]A_$/$T4h'Ok;X>6*poH(nYZF)Tu#?^">c7]A)^/(#+kIkW<
m?fKd'.0+tej3<,Nf9Wu77%Umi$9s*6tREbm@V)^\!l@uEX>q##OlOB91S6fi,*s;,IeTA%l
0)t<JnV$4p(3S]A%3p$G2h>a'Yl-[7#;L&D@Qou^.f+-l#N4ImFZ407\>]A[Y?]AU$&Nb;SY[h3
uSQWDfC]Apc;i1m+$]Ar/RF/e=aj,5iRH3Om0=$iJT]AT[Y;+N;ol7Kdrn_t@aION-SG]A`giOMR
:4KpOrn?FFeGRtd@$!p,;Lg(B3NG\,>A66]A4og9ArjP-kA*'^7j(<$hV<A45pE9HSSd_5Q5K
+iHQB]A!/qYIQNS<]Ab35aU%OfM60sSd@BPPSM:?MA#kC(64h5ti&o<F4O2tLcJq%4j#kWI4O2
tLcJq%4j#kWI4O3!e");diP_7r0a_cOsP&(:p2hLJsZ5NlBa_o!/&ErJsbQT-9.E`:\p]A('^
Q7]AO78E`h(-c<fRqgEp<\aOA"If]A~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="23"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SM01"/>
<Widget widgetName="ABS01"/>
<Widget widgetName="DATE01"/>
<Widget widgetName="DATE02"/>
<Widget widgetName="SM02"/>
<Widget widgetName="DATE03"/>
<Widget widgetName="SM03"/>
<Widget widgetName="DATE04"/>
<Widget widgetName="SM04"/>
<Widget widgetName="DATE05"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="0" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="572"/>
<tabFitAttr index="0" tabNameIndex="0"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<carouselAttr isCarousel="false" carouselInterval="1.8"/>
</Center>
</InnerWidget>
<BoundsAttr x="0" y="125" width="375" height="608"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[//document.getElementById('REPORT4').style.top='-10px';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="report4"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report4" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report4"/>
<WidgetID widgetID="8a3bda2f-6d37-4060-8723-5ad2e01b98e5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="0.04"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[190500,1524000,190500,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[952500,381000,952500,2220035,381000,381000,952500,2220035,952500,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" cs="3" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="<div style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/back_top.png) no-repeat;background-size:contain;background-position: center;'></div><div style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>返回顶部</div></div>"]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[document.getElementById('FILTOP').scrollIntoView();
setTimeout(function() {
	document.getElementById('TITLE').scrollIntoView();
	setTimeout(function() {
		document.getElementById('FILTOP').scrollIntoView();
	}, 5);
}, 10);]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="1" cs="3" s="2">
<O t="DSColumn">
<Attributes dsName="data_collect" columnName="TYPE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.SummaryGrouper">
<FN>
<![CDATA[com.fr.data.util.function.SumFunction]]></FN>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsHTML="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellPropertyErrorMarker class="com.fr.report.cell.cellattr.CellPropertyReader$CellPropertyErrorMarker" bottomLeft="false" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" bottomRight="false" topLeft="false" topRight="false" plugin-version="11.0.64" oriClass="com.fr.plugin.cell.attr.BorderRadiusProperty" borderRadiusType="0" value="12" percent="50"/>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[="<div onclick=CollectSc() style='border:1px solid #CFD7E5;width:97%;height:25px;border-radius:20px;float:left;'><div style='width:15%;height:100%;float:left;'></div><div id='CollectImg' style='width:15%;height:100%;float:left;background:url(../../help/HuaFu/"+IF($$$=0,"Vec_sc","Vec_qx")+".png) no-repeat;background-size:contain;background-position: center;'></div><div id='CollectFont' style='width:60%;height:100%;float:left;line-height:25px;text-aligh:center;margin-left:6px;'>"+IF($$$=0,"收藏此页","取消收藏")+"</div></div>"]]></Content>
</Present>
<Expand/>
</C>
<C c="8" r="1" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="0" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="1" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="3" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="4" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="5" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="6" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="7" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="8" r="2" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<NtZdcoW&SS%/WXR(nCR3q*fR8UAu1)F22b*K:h1UfutnYE^^,V#NfN\t8)Ysi_"`]AZ9-gc
$'ePS/[T&/Bm5EE<bU7(*o%c8>.,mrc=qn,!"^q#(6F-'*HBkC32TTB6(-T+?u9\X_K\D=9A
?274Ifc^heOVY6jMo'`bd'NZ8%?hGi(e'=RLGpOeMEEp3B8i6C^*U\]AtRpH=om/EmNTBEpBe
G/m=DLl]A8gUjB@m2@uor9!Y'$bLNYT9LO,<p?'&@34?Ri*8lhmn\l;eUnXir$%cEr)^J@XFG
PBkssXT5c9\R(:[Y$ri00UPY@#F#H^Kj#hn?a!p*&Pr*A::JCIHa^sHNlj'dSDg^4OC7GV9b
Z^3c8B]A]A3C7U/7A^Tmh'C'ch!EddON/8O_3*\PCD>;>T[F5`V[-segtV4u^/U@W<f/8Cocl"
SKO[WQTD[mZ@QK*(k8X]Aqp=i%_cdl(%-;V#J0?V_#rso`56e(2Yo)V>0]A,1ph4/#S7aCmCPF
d&:I7V1?G7<oH6=>!jV,_e`da.5Fo),!)\5E+H_CLBY-W=;q&H'i`26ChXcji9RYn:$DbdEg
M+I.Ct#K/)_'\q`J%I<Jk4#7.$Xb`i0)/^l_-I+l)/-@l:\EOU7,FZ51n_7(:<0B,cY[cFE$
]ASoP6r=H(2+Wk`E"&n\j]ALN6CJ"$&0Vt3M5Q;\GUF#\>GJc*hCrsMafU7i`#X(Z8grOeK3k'
0n>WsD$_tSaRL7l"P:f(IFAB;5Ldl(\3nRC36&Nu]AQ$CqPR]AY.s'F#i'@S-S\0\#IkqnjuT@
MOZVVqFaYq5R(LoTQJO)iSA-GdiB'2NjPj%Nko2:Z"eN6+$sEs]AdSm4/b=='$[Q'B;I@0u0T
h0AT+H4qH@>E,[oP@?TYT=!>dJ)CJdMpF>*Z"\Wi8TPn-(hqf2%Lp.&-Rt>:HC`Umipo+!*+
?Sqp+d'1)G]A]A.<g54oIQaO]A_:J*;Il`S@rVMhtrfZla&Z$"i5_"7kr'X%skZ,>sShG`"Jdnb
njX6J^E0r,@TZ\t?"MpW_+*B'F&_HH!V#5Re5/^&(`"B&cYkMooer[GV#"p=e8PO:`0J_?qI
V-1=1C5r*K<jXg_K[?snU&B]A.*sE<!i#smeNC2Q&+6*(Qq0AK\pC::%,OFm4N8_1ancUDQk0
n7hRNOpDPTGRI"#o!\V/'1MH`h)H,%49\H\TTN0=b5)PT!1Kf!>^MS;rE`R&-;KY+NEQc_$<
8+Z>ua44u\K'[[f[rtdQ#HXgmV.HeV^5HSqEk\$YN%-d>Yi-EB%W9JmtYPFTF[%lXfO9m2>l
WD[7?m(VLZH[k<`C/^@egP!`q47oPc#+,K,iQ3(0IC+pfZBs:h+>IfKFG]Aq)BG75#E[!/7,!
29J(ri4OSi</GR)9nd9;JeBo^>UkHlhJ:%N(,Gt'/?B9R/`!1eiHb=@#;Fe.aK!r.4L81,P@
[NrF*I6;n*Fj@6K\(oWL=;#pjg>"C>kg]AUXCY*q6\ar+nA*oO;*KN??,<Ond1(3+<]Abnk-]Ae
.h"GDBcs_?j+kmoBl"3)PZDk"B'hGej_T%Z-7V%V)-h#9JcIQbNFilDE\#3d/<jWn'7U>jb>
ZagP!f-Ac?X7g%\DiEpG@<q7X#jL(-mJGs<_N=ABmD?ikZb8,R!FJ]Aor:,b7:;<gNT%2l/GG
g4DB8q)Gu`bL,1LsVtq5%@JEfWk56O[d:GAP.'pj8A9t4s/0_D5V"s0:8DKo;3&C$WsuemST
?5&k/u7ULt'6AKT`;4_Go\gicX66/MRW,T7)\.mhn*<bIg`RH?;d3rKlWgB,t)$7?"BFUPaa
q9m0'WT1JFli<@j"?(nu&uLp!1D:HWd?2AK4>(D&`U<cT)O^St[1m#'h(C-`s!H?rqpN*AR!
6:QRe-2Sp'YK]A8s^S!V_?pZncQD5S9B8SrTE#/3<-.pgYJhlZciK=o9o!q[Eg(Q?U;e`-Ju?
fjfGj#=LL5o@+Zi53p%K9)c2UJ2[fS30FX*BI%*8o*)Kop2<.Q(gneMPBFqXDRA"$LM4:_qd
(6k.T=#$b(i4,h*4JXjWsrBBDTVme\:.4X\=tk;&A_$CJjB,nb(\%!@dFm1M#-VMa1&bK*K3
Vt&F.qq`jBYTe1d#,qpPYU?m"Prg:=DT+N_fJ_$_oiFfkUfe%L9[bH^#OCu$?Mc\f.+)s!F$
K!QG\#ZoJ0`\MY.Sbq^;rN8(_Qd++t;7^[odM)S>#hl5:=^MY($YJjGhJ-G_NjI&:M'DmFV+
k&h?BKZ+^i"+oH=pmQIBeldR(4KOmPu@/HcFCl=8h<V$<Weh2NuO.lnb*]A^(3#D7i"U0&C9)
5Bp">NXCYU>3D4"2!bg<]AR:3Vfq6C8H&egW]Am5]ALP;g@i/SaME;65qK2UMRD$miIlAgt<?6k
5k%*S?.cic'bT8)8c6-LC;EmgIZhK&Ya?fqd\AXs"=7D`MlR+((1:*qWjWH8(gZD'@=XtKf)
^0QKA32CZ7pR??p:W/Z4ls*gG,-%]A'CS`U6s@,8T-:2d'E8R+\;^BkMpX1WF)G4@o1[\gY:J
_Ei1]A=#ofG.JZ:#IbFGoQ",`eCB<8m&R'<lf2l1%p6WIreq]A:4*ef,]AX9"dEAp/N99Ypu,P)
jNtqd3!(]ASfF]A4%BWq2-7.<O1-fka)Wgl(;V77%Vm03,2()m$IgkP@]A)$D_dlnuNN^%cGDnW
%I2LFPN4ZKb9/:#*$-R$q%7J<m(c[k6Ud)KH3aV2L'\r/6Di:>K7*':G_k[.4YNl^!q!aH>?
'/K&U;k)?75jXe),"'u)tAGOc5I"'Ui\A;;>sb5G(J]Ao5WZ$X[4F;O^jIKa\+Wj06U.Vm8N0
em9K#W<!_]AXXkSCVh<IL9s8eCIQIckft_&0TkD\n1r+eo(hEX>O%D/9TO+"X6&d`fQW\:fO4
?B-eT"flL/T8r#-00'7UPL6hd#bY2ZFN&p@R$^<sLE:^X^`2MBA<:!ErE]A1Q^s'$GMqN]A5++
S4q1/+,=7dD/+2SgeMmk8!af>>3L$;Jmo-:&CsZQ8&W?'4JWY,mqY@-\oaB\IVV.Q$>@c#1\
&Y-T^8p3kS#_ZCX@6JXZP8a8>C%Gma&H\fs/s1MT^"gcghBrX[.`PY-OFd`IF@bR5R`3,.Tl
G;a!!nc$r=_QXMWLftrgrRjGQouVYh*d>@MN+HuHQc"6V*MIbag5gng&Z=Y#NI:NP1LGufrg
b!ZDnUHLa>&)MQiEbl9KhPYN%Y^r*l)(f86lX\sacm&KO9h_*.]Ak[eq/(?HRjeh9-5d"H-R+
(bV[*=+_ki=7ObIpJF@m@7YCq88]A_s5O:6Ej%=t>bp0Z7m^n9/;Zql&?-Pm%!mF7%l;l(=eP
VUm*0UR`8=\4'hB!#p^0X/7%@Ol;2<Q\Uo+dP=[.[<Lr7j+"#W3*!^gE`XlFe)'NE%Z>6aC:
eS`6+@U:1e=q&YV6q930Rom2*DQe,=O.H,0=jZl=gj>_),1.K.?E&@Zs0/&N"abZ]A:fNbQ%!
/+l/lil]AgUe@f8IK,#tnF?1a`2ssQK#[>jg%TdO!GU,JiH'L-pIq,EoRDo9h(.Uu!s&o$(jE
rfeT9k>kN:k^:?DJarZPOF%,k>J=$7EPJ[\DQj51?$*>:oJ`#7<og;PWQ90EC!(8I+E^b(k\
&F;qY4`J@ooGVXmeL$8;q8qb!G'oOke8\2\!Bp:uIoKGM6g@SF6Pe>hmIb@<%l$G-(J7oGfD
XEg*<U[*B%FKI]AHDGO^>lrqQPW,p?TLSjZ`ocfAfTn<SHc8adMIO?#[4i3hM;^mlYo54LG9]A
5`lGU;IY!QA5QZn$7u"WQO*<tdpe61mMuO?Klp>p77VNl("X?G^AK#>#dgct=8FX`%Cf$-Hn
c0bcL/JdSKh\$UF4u8*^XXK=D"%.OL6UlYMENZi\mqk[Suk<*^IF^ZBj+pmTQ'G7^H]A(JKR:
iZ6420ZK^Y>0Fu6c1Rs]A$S?bDKb=m69R-eOKlJ;MqKqT#Wi<\or`e\,4/m6ia=A=rk?co3Ho
1LWk.N@Hemmg6%.J:;<O$Wf<g-&36f[,SuPVR'c9V`kEjQW#"#lFdIi_"(+dlH/VB-(QQO\@
Ce.JlIMJft9>G?ULC<)I?pfGp*tplO#@ck<)?QE0t\:I+SE/jqYWYLqnBeLgYGABM#("E$2@
>]A]Ac%&GYg#+VVg'#8lsf$r.+Ema3&A--RAZ#D8C'eqI'@>Pd0!D`)Q_lbYQ&.UKR-iO!7<-]A
^EItC/.aOr3oV\m<#ckQWs(0mK//Hf5-$)O8g2f?*noRgSc3Tf;=-E9'lTEX%p8as&U0/*(s
eRX]A?bX4Gql2jLU'/m.KH6\D:tV1d)PQ^C2[#f@;hO4h<@&j0/+:-df#^DeV/#qD\bk=Ph7U
CIcU\@pi]AE=b3*]A'g]Acg"bK$RjH*01Pdun\Gg#\[>8?9Y^.lE#WGhb*"FlJDkjZW:5DU@SjI
Wc9X)QeiRpgk`q'u,;=X&!2D?/'[L)Rrm,-2p94c,"2`QD/SXqS*WqW?K3%B+C5KgsHc=YV%
$l3`FT^iT!S#LF<<es020E[1Rg]A?=k^<\pg$(d6<pbkT<=c*K&b7+kTQZ<Pju-SUq`b_",^I
gM]A(`ZRDB^7.)U"!aW"FNPSjk0?5J\l//?h0:h$3t:Q-:bEI,2o"5tQC^><F<a0D-_dF7r>F
4t;CuTIM]A+S$\%Om#?:olO[3DJ("(a$,"rQ5/ZWK=c]A+DJDS&F53+Kn6NdDKuOK]At*^EogU9
WD?p]Aj#-*F,Dd_WY[Gm@OFc:SnO7s[I-kG6UnI5FQCI4X[8<@.AF7;#T%.cj&%`4#c</mTf$
0iON;&-o!!Xi-rE;RuoMRP/9JjsT8?`W('o4^,@'Bcp#[bsb^+;OA!24^``O?9FIP"iM9up-
n`eht#>#+"8!4X33Qk\L^.jgda6]A.$2Z;MC>YV3%UaGAk!N+i.]AL#Uq@F%>Q/n]AgZ&B%U_fK
=RL'>3Odtbb[8LB1lp[B]AJa?O..j'e3[AoN@Mk[O=R@;_)>$JY4_YDPh15Vb-sMf<^Y-6_XC
63jmSRC8t*kQOQFLH1Nr)j"WX&H,9n%X61q+:1<ef\d8<$>eo4]A%`a`NIRaZ)a^6,65n$?u7
X]A:/l\s%K'Rd,H<nu2Z.-f*Q4,cu?#ijBd*O[0SE744QtQG9=\aK^Zb;J8ZI0I^3h3@rYu,b
%bdZP(I/"f),jY-bu"2u5h&F,#=:fKss:eE%7^2;@uQUm^5&Rr\\3otdL>isGdP`P0b3G!&)
'X(#^'eBK7hc6\.5=<]APN3WSp@%RGF#b*u+XQ55SX^0gp!P\M]A-?50?)<Ds)R6t@YM$VRC/;
s0bnfN6)1f;M1]A+("ro>gcochS0E(Ke<jRUl'emA"$\;piZ'C^>ZZ-=_Sll&0P)tQg3:D>^P
D6OJU@9e=p^'G`0q88f:KP42%EoY$h!/e3IV@SKmm^+eR@bH8ja$$*$?,mK&)R5O<V9Ql?tE
6,kFVLLf/nNk*4_m!>]AKZp!qi>3ArV9>,5<X4AlSUX-<'YLJ8u9UtXdh7;95P0okrC%KRkSq
k?&.7L;i)\ps!bscYSGnVAEnWsZcL8Z1$Y)0O_X+6VbhDCE8pG4ft5TM)68ku,Lh#,$W`g.l
n:d$5'@d;[I^LKnn-R&=]Ah"MKH0M2n#=Kd&T%j,T",C_p@o9#MYg,r$L&4`9`"?\8'f?ehM*
bB=bLm6RNaNA$@o0THqoQX?jeK3/b!4.jNYj=3f0,-4"29;>TP>,5Zh`7.s??_ni<7Xf^.5T
KH/;8H7H*7OFC7(BH^37(@M#S9qLr@`s)bQUU:.ScP5"T1FbV'es%kMA'a^SK_iaY,]A;^c_#
\H?Al_q=VGU:IDYdEJ'YYbW0MmXt#=BKi#Db&AH'(Vj?U7,&M-b(\\Fc)Z\&k$SY*kC`j2,,
G8$*[EfRSb(*lZON(W>"mL6QI/^cmG>-"U0LZ-)`]Ank;aS1['uOpL>&`sDYOhA%2<L'0Cc`1
UJK[SSf*D]A25_=),6CrTW]ASZ#XoWKhD2WeJ]ApgWA?G+^%SA3jN:b`3;:#l$8]ALaOmm11GYQ0
%VdEH[u%_SkI=I*t`<7[)ZI)7``j>VMuZ/`Phc-!n#fb^XNGPF-m(jg4L%8DW8ncX/O*m4a)
=F[#)B)Ks=B*Z?rE;F+?aBR*tbs'Iu9`WnACM(G\@n?mg=3AILWX[`;3C/aVBQ,Z/"kmG_UH
".5Ita-BM\(*)"WJV<%hj\J;\3uH@0n6*cMg8)627*:qK-9)kE,.)rRf'd*"f`'3KaYstI9C
Jg$W]A72\:kFpmI$-K;4U?-A;YHW"LA>&!aTiMX95WK;=Qs0!g\QqZd"5l>+8e,Sq9gPW0@,c
+A6[@7.[>2n&AK^^PTHB"#uIQU#+^<.Il2Eq`kRLfQ0)X^P!?D-F`sjP:FJ+8%8ekT@ojfuo
3i985(Y5!^FNmsjD0Fdl%&n@\[9'>/g7_n]AtY%J1-^sP^"91'hk?VE!S4`6ED;0%<DpdEJDJ
"V#2a8`qYi6/=4fRO/A_9oAb3"74`tgMe%D?dgNiPG?ru[MadBWlqHokC/kh4>410GXgY;$!
3#fRX%eMtri.%b:j;[p+C>F7pot+'uk/a!@W^S@(p]A!.16C^sHJQtQrk7Fh`[OreKS)?f'+^
8*t]AeADJ.96og$'M\T[-Nf[Y`@(h")\CAQC4"7$C$5N%;7FfA"V"O`87h]A*(_^a%QfIsa0b&
;\UOOpPjT:%+MV]A7d+pPf8A,hK3nl#'NF0EI$F\Q*P^Gmt!,Qm5SpKNK)p*:r`d?4W%\L$L.
eWJb^e)$k(@H:UX7P3UCr81QX]A5cq).lHnqlKnAnEhAMH(nrZ$n,=lk0di)quJ1S1Je$IJ,.
7/:eMu-#4JI)'*u?ErgAHKAH`O&qZ+>E--kJ<X@e2:d42miL5)kA&tZD_Q.P6P<WJ6KqSLqQ
H.o`W-qbRLJuWar5N!606lK1brc#W2mqs",[e?*)@"8XZpl#ek+iY72T&[TNK6GLKchSSK=#
\s,khq,T2tKi0JDi#70RgmHqG>RV52U@R:%38>1YrR@e;%+9-$Zso=TVL2@dO8fiG/D&^!ZC
.MPFEC0F>d2fgO)SZb;Z;]AI;&L)1a\"^ak39/(AVQ#aq"6<`X$r_SI;eQtStU9Z_jD=a+NEl
/CV0G7_Tb[E:Mo^jjUAH`skCQi)K3N4(^C#7mHd:uI1g]AhEN#C"T'TH2\Sb!*9\Kbr.hWnq0
]AAWF1(ldYP@ZZcgm%gZ$nSku9]A<H;Kt;Q;#Fa%tT/G)^J!en-Sp,oR^jqi80eQ!KQ*]ANMY+S
,9@<PgWN;I#"B3A(E?b&a7BeuE\EsL1)3,1VOU"\BrjBo:)<FQp;Ah`k'l^=:,`9ul=<bkG2
0Rb_N<P^g4_WN:8<rc2?QfB]Ajo[k$00LJfoF<s_WBti<bi9u+U`a2#Iqe%NgBVBc^#C/e@1l
-++D=TBO9KFejfI>JEDCj\!l<;q0,!dhQ1.08MmD6n;a'aHZX5"`&2!iRUn_5IF`t=eh%UlR
B++G4%71fYldHJR5(tU\#70e;#>&>Aq48T2\k$Gjt)Bmr1#PH5J=MB_O+gIZYBTM4Zji-%<I
cZ\RIa7c,ERZ25\TM=0kXer`%G2QTd,N9&ITos/,bg4u6S5nc/R6#F\C>2__RJA#NT2_o/<!
hZCn3T+5BG'h=+8dB<rXTDaQXI0`u7\?bonqOuR3>K)<j@:s`heM^_ET5Klpe:.<6q%mV3pJ
\f33FS.jUhk5u<Nlg2_(&OjX'c[DJlT#^<l$7f"fl#GXFgJ%LF%!)U:9^2NIMQ8-\g&E/,oJ
NWV*+YP%W`CpStEf2CY)A]A8\aO($F@1qE+X6[`N)4A`K>inJ*ORMQ3DNW%+g)WhQMl.]ASB+U
8k.AWf!gNWi.DIMQ3DNW;idFYB5'WVpeJZHcf.ISQnp"^Us$o561~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="47"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="733" width="375" height="47"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WAbsoluteLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const filment=document.getElementById('FILTOP');
filment.style.margin='0px 0px 0px 0px';   
filment.children[1]A.children[1]A.children[0]A.style=w+'px'; 
filment.style.background='url(../../help/HuaFu/titbg.png) center center / 100% 100% no-repeat'; 
filment.style.position='inherit';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="FILTOP"/>
<WidgetID widgetID="df5165ca-9116-4eda-ae18-c13e83c13ebd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="absolute0"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="url"/>
<WidgetID widgetID="07a486fd-c6c0-4f72-9064-c5c0b22c3147"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="url_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="SCTYPE"/>
<WidgetID widgetID="70e9772f-2c78-4248-bdc2-40efeb2a02e6"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName="SCTYPE_c"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SUM(VALUE("data_collect",4))]]></Attributes>
</O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="false" isSupportScan="false" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="click" name="点击1">
<JavaScript class="com.fr.js.Commit2DBJavaScript">
<Parameters/>
<Attributes dsName="hfzj" name="提交1"/>
<DMLConfig class="com.fr.write.config.IntelliDMLConfig">
<Table schema="GGZB" name="DIM_FILL_ZQFXS_COLLECT"/>
<ColumnConfig name="USER" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$fine_username]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="PAGENAME" isKey="true" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pagename]]></Attributes>
</O>
</ColumnConfig>
<ColumnConfig name="URL" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="url"/>
</O>
</ColumnConfig>
<ColumnConfig name="TYPE" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.stable.js.WidgetName">
<WidetName name="SCTYPE"/>
</O>
</ColumnConfig>
<ColumnConfig name="CRAETETIME" isKey="false" skipUnmodified="false">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=FORMAT(NOW(),'yyyy-MM-dd HH:mm:ss')]]></Attributes>
</O>
</ColumnConfig>
<Condition class="com.fr.data.condition.ListCondition"/>
</DMLConfig>
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var statu = _g().getWidgetByName("SCTYPE").getValue();
if (fr_submitinfo.success) {
	if (statu == 0) {
		FR.Msg.toast("取消收藏！"); 
	} else {
		FR.Msg.toast("收藏成功！"); 
	}
} else {
	FR.Msg.toast("收藏失败，请刷新后重试！");
}]]></Content>
</JavaScript>
<JavaScriptResourceInfo/>
</JavaScript>
</Listener>
<WidgetName name="SCTJ"/>
<WidgetID widgetID="d0697922-b843-45af-a0dd-1b6bce190382"/>
<WidgetAttr disabled="true" invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="SCTJ_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[button0]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="0" y="0" width="0" height="0"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="BACK"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="BACK"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1066800,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1066800,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="Image">
<IM>
<![CDATA[!G;S'qMA$D7h#eD$31&+%7s)Y;?-[s;ucmu<!W[.!!"!S&A\>H"YNA75u`*!m@?O6'O_]Ar4n
$p]Af$T,:]AZ"3$YBu?RDm>'MCCnn=.1i;l]Ad+!i*kGkbHI.IAO;/,!;Q*!i4K'DAA.b%t&9W?
EF(T+@rFTYcI.-shcMG@!6u0O8Ie1;B]Am8M)D`]Au_1]AZ`]AP]A]ArE<-2)_<:P(67^i&<r&5qjs
7AA;1-H+Wbo(oK;9f8Q/"e2Rm9LBD:8d>rZF0bO1*OFmPu@q>),.LuN@?G6M2EkoecVo#)Uf
!.>R+1?!3Z"2K'RDN:[Mao0[6`&GucpR!)?E$Mm6<%4a3r0V+N_%S8"Ah!^KRTrt5<L9I\AA
;[LL1d06l,f?6X;A)pos4d`84la2Pq=b/ZZgVGXFQ:aC?+l-pGSBB>8$N^ND=r4,Fjoc)^MN
/LNK'S*<&@>@B=X82hcs@d,;%4k+WGUJho5ehdFmSDQqUEmi_=$Tu!,-@emV*$`WNP@#*UKq
f5Ikn$f49@9T^ZRs/-&k<R[a03jpKd+fXJ9!WQ,f2Ism]AucrpT<Mc333Fb[_#X\TGO$NSb6\
j]A^KY*CJuW+fnemr,C'#_:H]AU'#kk@%A`ukptO]AJ_<(kNu"'\"qsun@\$@MB.fhPrqjb!#6N
g/&UOBOj9`="!SUs_YP!9W@@4do+%_#4S$[,j^C^Y`s!@^ML!$eW=f-N[bf,^YpB!TV.MDrR
V7\cOl/9,i#4p)QnZ;ckeB.rG%d8M$J&B'm;HpJOk!$HuIqfg6.0u.`7>ERLcfHKQ[<hNT-!
>%+k%UiR7Kr0+Zu@P1Tc%Yo!puhq+=8s>6`_$J(lcY'/8<s^IsZGb/E(#@9-`WO1]AT+h3[>g
4FMp4tCRc&5D,L]A_%\ie./cj0]AM_"mW*)p0Y3<k080&4Nd7eP"7eRI8lMM''^Y5)H7oN`UJR
?Bhs%e*Qf?Wg;"MsEKhpHJf'2B\7KV^;iM<W,+nIVsq95B`\`<#gE;W0fM?ougd?>(HI,8sX
FN20_Jgi>fmte-Zm)PoJiW^oDj'IHLU1]AfIDkHdC(e>,eO:r>j='(Y`'&9b^R03*#&[5L/,f
')#0W>5&m='n=QW+$N[Za/#$0.ZlEa_A]AT5:Xon"^3M":`O*61*mS.qKu0ImqU#;M^q/8L9r
`H&GflZ/>%qVQR$D-(8mS5s+HdHT2LU!TI72S?c*Bng<N>F(ikYQ7-=,nD9MfRI@Z$5NO),A
@`q4dA'c-6NLM'+-RN\L\G2[n`$KcE`oUVhYD+`Wk8!m_SL;_Z2<<:'QT,MX/WB#t6=7lq0@
/)JK\FO"QM5jmu=Hp=&Fht0bZs2_/d90<0E%BW,fo)m;?n1N&I_>rQSQ"p&8\2i>fGe_!\ua
CL\IN+$C1AoH!TQIH3e=#*&jBJWDk-p%Ci:KWqUnEm9N'h1qW`7]A#L5/6;q;h:b*FEo!XMDr
K\+qH[HEA?LSC%"-mE?N1Ig@\S-Ytso]A-"(UH764"<Q(*J:U6'bu&A?c6M#2($uTNOO@rEK<
!6E&HFsS#`A`Ka\cnK:trfaq%",I5Y_,qQPSdGm]Am%unZ!6,okOe8!u9kG1^*j@Nst_$[(`l
p[pCuAT_4TNZq>VaXp:G/X[N8pi`$P)Q!mB@=S1PA#?XQ76GV=3B4tS4[13+b$'h@?)R4f[I
nZiJ"lKjjr>/EBZj"CHjp\s3gk-.F*Y>]A[8dK85X48b>)AbZcQVdV_I68&==0^D(z8OZBBY!
QNJ~
]]></IM>
</O>
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="移动战情室">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="pany"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$pany]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="rqsx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="user"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$user]]></Attributes>
</O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_self]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HuaFu_YDZQS/1st_Menu/Home_page.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style imageLayout="4">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@!iIP@kTMbG#J4"D9;[XgR)o'+0=-&-R[C+B<0IZK[*']AM*LB'+-.>/i'4:[YeUg)hlZ`Qq
3)Q-Do/@7)V+Qjb5E(Cgg^f.j?PsYhid$]AWH\#ZK-:p-L^+[kBkL"DXS+6'mn@WIQf91&.!q
rC'nr6!jRjJpj"Nu>7sH04[pP\;5qL0IAc@;/*!h`54Q4b+5/1m/F[/0s#_Dpegf+2RQgR!.
Y`9/f/0Z?mquf:/6TW9:$kWjQ??G1N*aA-]AYAf[k.T'kK[_kX7<t<kPNB+pl21bOU,$l#3bE
7kL15Z/D3ijrW!8`*Tfk!eDWPU^SLH3tPF-!o1+n&_r)2[LA(*.LnV6?U&O`GDM>TDfIH[u(
__!Fdoa6)5OhC&#n!8).TaZN"p;,*c$LIN[EfulpD\'S/ciGF+kmlJ,SeeoBQan]A)3RaF&R_
e1G*7dhe27TCQ87V7d&ArNJ>R9.&<`guq)a!3(5`)G<:h*X[$]AsiPbDkk4*7Bp9.$?eUR3iX
2#iK*c,e?k3`T0p$V'!U?ZrQ[k>OO_:#FZ]A[$H`"^9A;lDFI:!YL6Ud:YsBI\AB@%H.uisV*
B7JVRoL\]AdVr7=`Qt,1.6A38*Gb80j4u4_^E_]ABra=74n49GU`?Vp.\=bEF*\Ub'f9Sm\k3L
S*mg("J\-jHIA(d-:9\P>"s7B3Kkc6rWA$`6p#Oe)AFZM>;7reQ,BX_N^AeGgeLJ#T,>e.5q
i+l/mA')TaL-XC>'a<keYIQet@&r=D9s4tgWk"O__rrHA3#*m"nK\)%S=Z+Gan#IZT<"1a@<
CPZQCtXF`MO5O#mGX@W3T)f.-b`!kX`a=cn-I6_jU\4po9i5>?%dXnW5bh<`m9%3R>gd&(n2
@gmlo!B;_Bq]AaDW49S<<=h>gVT//>/>G[/d!T\"[mYc9';k6I("QXb3/UM3Rq=E"8C]AbjE_=
cMR_p$@H4jr+Q<]A!nGEWm4eFq-A!Z&['4hC2lRr5TObf950@/dl4Y?Zbn^"hcKb%.m+,,YnL
6J5ilX%DM-?B]Ac"!FH3Y%>DMbhEcB9Uk@kP\7ZFt0Kd:]AR3>m-kY+@TMrRI1O_"_ZEB_f/TH
e,Bm?Pp8Ye027_75V2n1QVT&L66VV>c]AZ.BHgCjEU?pMq!B.taaZ7V9PMHRrkAue(]AY<?T2g
7$mAplJbf[d+=T@XJkHfI<_$p&HDIA9CsFh#Im(f></[nu["oii^ig8Z->lS[L?p"=S>D3FG
QaR_VnOu(ok%/5':Z"5r-5@%PQ]A`*7\(J/)E1erL051BO%#1(n,_EfGKH!."+$fS3XZ0m\k8
1peD'3d$/)t,2-GD)5WB?jVd3\TWP3mB:%4h>WLRB/X$HoaCZ0Bmqp1!FM\"Guc[4PFOTm%g
^E!POIE:h#+ZmL-Y,V^I]A%"bo8mfr*Zsp9"elm,7Ge4DmMSkl-*U?ifnqo*P`cp`^o^T%X_-
bkkrDc.bFpc1"s^m<a".\n.$L@cS0b#.NeuIGXJ?9f]A@iO8IdWN>W?2bNIpo*%7$?#rK[1@O
/%l=m\tm.n;B'+@(GW!<NB0&0O5g!!*-(#S8+DJ,oTo['lai:sSds$V('5r,pPmrbHhdZd3n
Af[W/knY+FD/]AICt#0GER%c-GeN.+[lC='8;'M*ch8:_IGJJ:Y+'M*ch8:_IGJJ:ZT8(,@Z$
bbd^7h:V@.$^#d'I4bas"=~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="32" height="29"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="13" y="11" width="32" height="29"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('LEFT1');
ment.style.background='url(../../help/HuaFu/Vector.png) center center / 80% 100% no-repeat';
ment.style.backgroundSize='contain';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left1"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="206" y="60" width="7" height="15"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.FreeButton">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const ment=document.getElementById('LEFT');
ment.style.background='url(../../help/HuaFu/Vector.png) center center / 80% 100% no-repeat';
ment.style.backgroundSize='contain';]]></Content>
</JavaScript>
</Listener>
<WidgetName name="left"/>
<WidgetID widgetID="a0b4688b-4037-41e5-9bd7-8d72fa4241b6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="button0" frozen="false" index="-1" oldWidgetName="left_c"/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<initial>
<Background name="ColorBackground"/>
</initial>
<FRFont name="SimSun" style="0" size="72">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<isCustomType isCustomType="true"/>
</InnerWidget>
<BoundsAttr x="15" y="60" width="7" height="15"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const strd=document.getElementById("ENDDATE");	
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="ENDDATE"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="enddate"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="18.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=DATEDELTA(today(),-1)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="203" y="51" width="160" height="33"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0"/>
<WidgetID widgetID="84f88100-9781-4522-90d1-dc7c8e3a8170"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="label0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[至]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="88">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="172" y="51" width="30" height="33"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var h = document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.offsetHeight;  
document.getElementById('FILTOP').children[1]A.children[1]A.children[0]A.children[0]A.style.height=(h-2)+'px';
document.getElementById('R0').style.width=w+'px'; 
 
]]></Content>
</JavaScript>
</Listener>
<WidgetName name="R0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="R0"/>
<WidgetID widgetID="34677d75-3236-4284-ac55-f1e22211a7cd"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="R0_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ImageBackground" layout="2">
<FineImage fm="png" imageId="__ImageCache__E06A636B9F69CEC5CD01541A3C3A3E5F">
<IM>
<![CDATA[[8^:.;caZ]A%%;u=&?,\`->I+k`=NXH_!Hq,&?.EfOH<EF'UPa)!Pr6DFGU8L(/h"R=>]A8o(4
r@m!<jIsBUB6.1f?T4KHO\1P6C,30E1b27t9AV_`dn`Zer;PD4f^'@j<KK"@8![pX&gM`%V8
#ceoV7hh/gRiXo^@PW-iCn4_e[=5+&#*cmD@5PCITRCk;cCNIPc(PCMDAmskN:G&8bY,aD/H
ZH[>b,Fu2f'p,EdMle4r&$&3Y*70MZPuRaDL)#i0p/dkpS[Y\aGEg2ppu$UZLDmS5^7LSl=I
;Cl*1h#R%!:7CMLIHqFi1B,ZX+Q89.R(h2RhV/SatPkt01!Ht#cd3BNT]A3I=%4gEQ+GE&A='
S16V_f,(Q+GUKU;D!W?FXRnumpR%9^5AYN/F28S@K3:)=>c+NqSa.7VeL%Z'kF9>I$G!`eR%
oX9g@TUJ'341pGs#)8ItQ1BVp.TXa`X$mm<rpH[$-,\s#IUJ6"$0D<*e=K?@e#GOK8-(UKpe
MSA0fReKI5TP\L)n[,?2LdY\-seY(dCc(L(gKAD6LQ#YfHJ!d@`an@74n2B92f9TKtpQ+oZV
(Q)eDp_C0_MTbN4#maRa]ArCK*>OR<_B.gZk;XOfn:2>1e3k[5$ooVEmd+B8R^_O[/r8^fd,&
iikg;/?Q3]A\QWHikiCqR,<:5X?gYO*FP7E8s#2&O9XarS+Tg9[ikk^M(5#P?EjKoJDWZ-+f,
<p)A*3oh^6002mQ8g\oel5=i;C8(OsVpAfE*;O-"^FWH3COGm>"73FVTu0.KO;4Pf@Z2>14J
kV?0S(^3B%oef"&.4TbjE+:<EMAIL>-r[W^^45pjDgU/GZG[HOZ'kju
FK:FPlX)&uH07^3]A-@35Q&cVk(imH;D90l.=*@VQ5oNE)a^^gVKFt:F.1=D?d;4mE^SFL2?C
5r3n4;X^&><qE207q1]Ag"l?8R^YYZmcGY[;0IoNDe2>D1^?:kofGe;X:IYtRI@2[OIRAa;4%
CU&Y&S0FN$RbP(j)fVbF*Hf`L_[2A7?]AB\i]AQ=`X]AG9X5&GO6/&^/W\u((RknEApnZtCZd_\
e1tT,?A;Te8"`M(%>TQF36lYiGEFlmNC_Wn>)'98NaUoj3?^"HKuL;f8YKh.p[&"CoQAPE:@
&Tq]AI8rtK0CU$lu\C'+)kFtS%'!-Cn>KL/fJmDUClE&;]ARo64<l/3`Am-qE)p\.hDjXg<_0d
r)U8oPY6ge;<G-qnb3(Y21.R1Z(BG&3?DWnYC!*M3#o9(siQoS*8RhJe4!\^]AgA#Pn<;-tZ.
OW-3L9`KRjG=Pb1NTss?Ju8lDn5<si`HOqB8#t;q1:_TG8#Kk\NVVp0b'Tb#H.1-92(d5Ml7
AN4+0I#Ua7gL2`nQ)")S7p54=FJ&=:`i";4O@)6kL#1l&aYI]AkC$5ijH1-3u&9GU4@DM?;\%
d3a(YBEZ\QC0@;.-iIl,k(`99bYMYnQHuZV\Xeh!kmFrnK'NJ1'ZLHZo<Lis'g.p2d&HCL)L
?ioYkA,!fMYdh&>.GuMM=df<tkrZ+H9oE.4"5Q>O0l=nF*ONd!%%(9:9ZjJMc2<19G'6XXcj
XJn+/#AL*4lIkX0TD"3k(?%Ug_\?:AF(X^!RH$XK-,NhV^8l'DB7B.!mIU]AaAL/!^VbJ5njr
LiA&-QP$qd3@1@:fX0A]A)@8+1.L-Mj!3O^LGCI,eF0g6;I9ed\DFjqBGEU3kPkV~
]]></IM>
</FineImage>
</Background>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320@UNS-!l>pn5`q!_T>!*8Lin'U3/1DVH$dT:l`bqjX5hmJqG1ar<O
o*C=0gaQGh]A@goiIOt(SCT8G?m/BgjOXE`nb^a"n'ucjK!DW1,+q!*FX-P@:7_A=[A&mM1M@
/0$?`i!G2Oj2_#j79bIjC$0mO/!^Ks-X>q@[.KD'7nWID>q)?M//N;<*.Q.8952b$lOWY/i_
If&^Wg8kFQ^N=Z5RdSq[.T0O!'dIqrE6caL*/;'/(D,O^V="=#upn:J+7`UC5)t@+I=p`J/m
i.$"Tc^-ch8\<'XdZ&-CO]A_?k<I_^(<e%%a6:8!T,b+&tM)HrTp?nknMDb"%_X.N_YSWdIeA
W:EgIUWQ)lO")5O5,t-1b5X2`m8%&k-hJpH!Mn&lcXiYG65'>~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="4" vertical="4" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="27"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="98" width="375" height="27"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[const strd=document.getElementById("STRDATE");	
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[1]A.remove();
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.textAlign = "center";
strd.firstChild.childNodes[1]A.firstChild.firstChild.firstChild.childNodes[0]A.style.marginLeft = "0px";;]]></Content>
</JavaScript>
</Listener>
<WidgetName name="STRDATE"/>
<WidgetID widgetID="21c3af7a-0167-4a76-a570-5d9c5336c587"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="dateEditor0" frozen="false" index="-1" oldWidgetName="strdate"/>
<PrivilegeControl/>
<MobileStyle class="com.fr.form.ui.mobile.DefaultMobileStyle" isCustom="true" borderType="0" borderRadius="18.0">
<Background name="ColorBackground">
<color>
<FineColor color="-276839" hor="-1" ver="-1"/>
</color>
</Background>
<FRFont name="SimSun" style="0" size="136">
<foreground>
<FineColor color="-5614080" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<iconColor>
<FineColor color="-276839" hor="-1" ver="-1"/>
</iconColor>
</MobileStyle>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=DATEDELTA(today(),-8)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="12" y="51" width="160" height="33"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TITLE"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TITLE"/>
<WidgetID widgetID="b17c8dab-1bef-4361-8c84-6d83fa28bd7e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="BACK_c"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="黑体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Refresh class="com.fr.plugin.reportRefresh.ReportExtraRefreshAttr" pluginID="com.fr.plugin.reportRefresh.v11" plugin-version="1.5.8">
<Refresh state="0" interval="0.0" refreshArea="" customClass="false"/>
</Refresh>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1123950,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[381000,4927600,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[用户行为分析]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="2"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="simhei" style="1" size="120">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@!iQ'5&AA6`j#5[\R(@$NXM?/KPKrWH(_=-o#j]A6]AkAY+X"`U.$r!f";3Jp=J61WkoPmc&=
Ac^fL!-W,.Bt0@Tp=\OG9EjKSpC.#U)qcjLtb>[su2brFp`%R65QIB.iZMH/J@dmsOpCM>6?
scZLWMlI?"*7it'kee4LfQi=@4#![+&+,('G3=m5(p\_&%;+?1f?IWijc>+MM<##,XC[59#?
=BbFaM!]AO^%2s8`]A"jl9a@H=I5;Yt;?SWMNUrM#Z?=RLhE"n"Z,WVObNGH\LT]AGpGjU4E,6d
/IU<)i?>AYCDWN$qJ<SO@H)P-.ki=H*AjUWiJp;MTnrpSk38?Z>l!l?_lJUq-0Go2dG/M>=s
o[@`f'^M9FHM]AM5Hc(Q?cG-'&*YeIuS&?"[q4[!]Ak!0cDGD.gpe2`9B$$T7i7/"(uhTg:W\&
esE4cYpY,44s3l;&`M[sEH=0lfCaXO3@IhO-k*SbNd0-!6R`VTno1kgCRu;eSeM766fAiaAM
+S'oQP<*&iUHgCBVk#+EbkBP_A,VaE-TLoE!d+\6aQ>3pC;%k!DIh[`(5?q![^-N(#97BonM
BMKok#-b">Y8Nq1_`[3Z`jH-P3S@lEN_r"V3Qr.U7=L81_9PYqq@=YO)5s^\2+AY4XJP)U]A+
:s,\u>@<=2s\gFs!b\,*joEpC:bE]AqPYZ2FDT>XmXG8l-6YTg]A*g::,Dk^&_X]A)EfOBct==C
b6mZ"UCbt&^u37SNX2l.Z0ugebA*rG=\hQqC:N"61+d:hbP"UsTd&()=lYcmRjluV#4lqR2D
J]A:2q4nul"n`oF+$2g\#m6)JB1&4iE%%;N"#YA@\cTY4EfD/?hZfh>YXO?8@E<&MPFr2=^(,
Gjrp=GXl&DOh>&[ET?K`6s!O3ZfIo./0*"S+(hRu(8eR^SZD?GWI7K]A,Ec@YO!Ypn?RiM.j\
'=5X>d3+k4Ggb>e=@,MF*IfK,#tttATbqjF'I>1$o1>T\AID^n-\O/SnU\qN<<F\d.mqsoYo
ghpuSbuqtIch5c'Y*.>bTrViknNG#jHA@niT]AG4/iX-rSqA3k-8;UJ.BY%E7Z`\&A^BTIW)?
I4Wo4^XK']AAp(&T6Y+$2l>]AeIW-YeMF^qMb1Ii7AHc-iZ'Su3I`>SeMF,cqj:'<g;rlGki*+
hbqOkObNqhU9T!E'=1EEn?B+(shDMCEB/mlU4ICn#8Do+c]A,EpV^p8#[82E8*e/)akb38j*%
g(Cg.p!8'o-G@.PcKg1^D<;'V?kf6V;n:#2maj=ES01&RKnE*?cnXVohS-hJiThE&pl@Y`PZ
VfLX/:DqO)&09,"f36k(,u48dq2\O]ACc0fHN]Am,2rK);KgGFOB(inD8*^u!c46t&Yq,kRd(p
?/kT#n=B%Q0R#0U8pZeEaKqDCb=+`$?noio9m+?HoRgi1)WP`po=R*_Go/uZsb.Kh$3nGuh8
/mNg\W/3NpP:+%2ASLR9RpI97es-Bq1%oWpD@[ccA*t>7aIKtRl+-E'_q>D#0:3h6U0:rgTR
:,pTan*\B=-:oWF<*!4js-9s7DE#9`7=lo+?A:ZLF\61HD]Ad#`hF!<f6^CRWj-Z1l"K*a\lg
MRGe/s,KsLBkA&:;5'hSG`db9LTn4T!E:@-lG&:.N02,896&[e;b^M[ndSX$q,'OF>2-_/V.
J[N40Pl/_.GmT<)BW;RU*Vi=3@7Ba3Lm8;\DmH4+&2P!i)#4logtNsD#&OUj"l]AE?"H&u3tm
3@]Ah;T,GdP.%R(4m-A2+gYpL!i%ZZE&.Y^?%j#b;Ha)GkS!2mPcR]AD@MYYt&-PQc@>j0>ck:
iD"&-0iHf4X,(]A!SL`R]A%4`<7o^t-oF%hdYib1L(gB=j\7%'ib5VO-Gnt>]Asi-2H'Af0"@RS
pn>b-rBR&+u!DY!EosbZ]Aa\b'9s5."9J*#,f2QJ`J%K!n^*Q'ZXi$<!g#t]A)sC59fe`o-k+5
?L`j5a:#4:J1UPjqZE:b>;JftM5b8J<9?oV-#SbL_FFE]Ar&mBV_BQmDJcRP!W%*"KKD5B145
LJWUm-*1-;n(*qe*!/_*Pn:\.IqF9/hb28YqebXi';V5O+IbJ`WE*>]AJenS"6<BJK<`=T!/$
O%l9>]AEN\+Bh\Y(A,OToPhea5NA-'1*P\&N^LdtqKYpM/Vl4"*(RE)W.,G`U"O>E/&'r/=Gh
<GS+['/'g9laA@6+sJ+n$s5t0]AA!bRQ0d"'XsrN3/\o:P2D(A6r,o[0G+^J&:rUi%SP<)WjY
e-WPFfg%pgl&@be-t`g"nAX%!=8SVZlpPfL5LTmugSBrs`&.ck26JlttN=[ug#*n9R`[N@3i
n%/6QZZ"YS,7L^/M+Y*pUUbgc?@8PfkgmO!H?4'rC5[6t?E-E52G6eToN[t`.P2p9EV$K0Eh
BQn*,Yl*e:F9"`GFC#Q#3CloFph&A&X6uaEu*f;nNrgnr5614-a8Y+fSnbr=#VOlQON"K/m]A
8=1fOH;TS0:pMbNUB%),7,ckTh>J#u!R:GaQB3MPS>Q?_q?j!O+$>>i/nf`WkP\MfY^+2=^^
?J6Pq^Wo@t\`erF3]A/"t0\%d:poIRb`7*O594C1U[Y?g?!T!r::06g_]Aft)k=0ES4^be"K,i
gdkNl9GPbR7dPYC:<%-ajRr]AU6%8o]Amg^fTQZiH.?"lG^nT8?9K,KADSEnBU53!T,Et094K$
Vc(OD/2`2j6?8[)GXF!0B4Z;kT4Pe9&W]AG=iO3O\Q^:-VQhOKU.c.@NqVr[U0r*oRAD.0pAI
QGG>721>5ag7mqf"9&PR7>2NfnPY+?aP(2p)T3=^&pBOi^p_;Ta:X*d\)nZ>is@]A?XMU6[qQ
#oUIj*kS:TLJ>T"h5JRr%"ck@8MT7d1Zf9%p,kjS-uZL>FWX"T+B<CFkgO'A3Y#,jlp,)@@n
#U5'IJd<QV+G'kCL`h9T,)@@nru8=(Gt2[k_WJm<lrUaPf/nnam&/*g>[8q)2K)m'oUamK_t
gDIeaWR<I%7EEogeC3jSXgf,6)GdoSof*MPu>gW/->0Ll/-eMPu>gW/->0Ll//2Is&Zc*_`R
+$?*?:!<O^]AEUWFlIff~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="49" y="10" width="140" height="30"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="SCTJ"/>
<Widget widgetName="TITLE"/>
<Widget widgetName="BACK"/>
<Widget widgetName="STRDATE"/>
<Widget widgetName="label0"/>
<Widget widgetName="ENDDATE"/>
<Widget widgetName="left"/>
<Widget widgetName="left1"/>
<Widget widgetName="R0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetScalingAttr compState="0"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="125"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="FILTOP"/>
<Widget widgetName="tablayout0"/>
<Widget widgetName="report4"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="780"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="6"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="false">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="date_cpxsfx_gmsmzg" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="营业部TOP10" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="Branch_Fgs" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="总访问量" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="Branch_Yyb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="访问统计" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="分公司TOP10" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_collect" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_jzzb" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="用户TOP10" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.25.0.20240306">
<TemplateCloudInfoAttrMark createTime="1690793028529"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="e10f48ea-5539-4709-ac88-f9c0e91a4bb6"/>
</TemplateIdAttMark>
</Form>
