<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="明细" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="s_sswbbl1"/>
<O t="D">
<![CDATA[0.0]]></O>
</Parameter>
<Parameter>
<Attributes name="s_sswbbl2"/>
<O t="D">
<![CDATA[0.0]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[22504]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_ssdbzc2"/>
<O t="D">
<![CDATA[0.0]]></O>
</Parameter>
<Parameter>
<Attributes name="s_sszt"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_ssdbzc1"/>
<O t="D">
<![CDATA[0.0]]></O>
</Parameter>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[2024-03-29]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct case when q1.emp_no='22504' then '8050' else branch_no end branch_no
from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select *
from ads.ads_hfbi_tzbjs_rzrqkhylcs A
where A.ds = '${riqi}'
AND A.branch_no != '9999'
--and sswbbl<>-1--应业务需求，维保比例为-1的无负债，不展示
${if(len(s_yyb)==0,"","and A.branch_no='"+s_yyb+"'")}
${if(len(s_fgs)==0,"","and A.up_branch_no='"+s_fgs+"'")}
${if(len(s_ssdbzc1)==0,"","and A.ssdbzc>="+s_ssdbzc1+"")}
${if(len(s_ssdbzc2)==0,"","and A.ssdbzc<="+s_ssdbzc2+"")}
${if(len(s_sswbbl1)==0,"","and A.sswbbl>="+s_sswbbl1+"")}
${if(len(s_sswbbl2)==0,"","and A.sswbbl<="+s_sswbbl2+"")}
${if(len(s_sszt)==0,"","and A.sszt='"+s_sszt+"'")}
 ${if(fine_username=='admin',"","and A.branch_no in
(select * from TMP)")}
order by A.up_branch_no]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="营业部" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[22504]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct case when q1.emp_no='22504' then '8050' else branch_no end branch_no
from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='3'
and branch_no not in ('2099','2098','9999','8103')
${if(len(s_fgs)==0,"","and up_branch_no='"+s_fgs+"'")}
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="分公司" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[22504]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct case when q1.emp_no='22504' then '8050' else branch_no end branch_no
from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098'))
select branch_no,branch_name from 
ads.ads_branch_simple
where tree_level='2'
and branch_no <> '2097'
${if(fine_username=='admin',"","and branch_no in
(select * from TMP)")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="证券类型占比" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="s_sswbbl1"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_sswbbl2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[1844]]></O>
</Parameter>
<Parameter>
<Attributes name="s_yyb"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_fgs"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_ssdbzc2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_sszt"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="s_ssdbzc1"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="riqi"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[Impala]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with TMP as 
(select real_branch from
(select distinct case when q1.emp_no='22504' then '8050' else branch_no end branch_no
from ads.ads_bi_ygxx_s q1
where q1.emp_no = '${fine_username}') p1
left join 
(select      
       a.branch_no real_branch,b.branch_no from ads.ads_branch_simple a
       left join ads.ads_branch_simple b on a.up_branch_no=b.branch_no
       where a.tree_level='3' union
select  c.branch_no real_branch,c.branch_no from ads.ads_branch_simple c) r
on r.branch_no=p1.branch_no
union 
select distinct t.branch_no from ads.ads_branch_simple t
left join ads.ads_bi_ygxx_s q1
on 1=1
where q1.emp_no = '${fine_username}' 
and (q1.branch_no like '%9999%'
or q1.branch_no like '%8013%')
and t.branch_no not in ('2099','2098')) 
select a.*,fin_close_balance+slo_close_balance lrsdgm
from ads.ads_hfbi_tzbjs_rzrqkhylcs_xz A
left join cdm.dwd_ass_uf_his_assetdebit_di b
 on a.client_id=b.client_id and a.ds=b.ds
where A.ds = '${riqi}'
AND A.branch_no != '9999'
--and sswbbl<>-1--应业务需求，维保比例为-1的无负债，不展示
${if(len(s_yyb)==0,"","and A.branch_no='"+s_yyb+"'")}
${if(len(s_fgs)==0,"","and A.up_branch_no='"+s_fgs+"'")}
${if(len(s_ssdbzc1)==0,"","and A.ssdbzc>="+s_ssdbzc1+"")}
${if(len(s_ssdbzc2)==0,"","and A.ssdbzc<="+s_ssdbzc2+"")}
${if(len(s_sswbbl1)==0,"","and A.sswbbl>="+s_sswbbl1+"")}
${if(len(s_sswbbl2)==0,"","and A.sswbbl<="+s_sswbbl2+"")}
${if(len(s_sszt)==0,"","and A.sszt='"+s_sszt+"'")}
 ${if(fine_username=='admin',"","and A.branch_no in
(select * from TMP)")}
order by A.up_branch_no]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebPageContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.page.First">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_First')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[first]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Previous">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Previous')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[previous]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.PageNavi">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
</Widget>
<Widget class="com.fr.report.web.button.page.Next">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_ReportServerP_Next')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[next]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.page.Last">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Report_ReportServerP_Last')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[last]]></IconName>
</Widget>
<Widget class="com.fr.report.web.button.Export">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Export')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[export]]></IconName>
<ExtraButton ButtonName="Word-plugin-export-pdf">
<Buttons Word-plugin-export-pdf="true"/>
</ExtraButton>
<ExtraButton ButtonName="Word-plugin-export">
<Buttons Word-plugin-export="true"/>
</ExtraButton>
<Buttons pdf="true" excelP="true" excelO="true" excelS="true" word="true" image="true" html="true"/>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<WebPage isPage="false" showAsImage="false" autoScale="false" tdHeavy="false" pageFixedRow="false" pageFixedRowCount="30"/>
</WebPageContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="1"/>
<FR/>
<HC/>
<FC/>
<UPFCR COLUMN="false" ROW="true"/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1562100,1943100,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,3619500,4114800,4000500,3196557,2552700,2552700,2933700,4152900,3886200,3048000,2743200,2667000,4076700,2552700,2933700,4152900,3886200,3048000,2667000,2857500,2743200,2743200,2743200,3124200,2743200,2743200,2743200,3124200,3657600,3086100,3581400,2971800,3048000,3733800,2743200,2743200,2743200,2743200,2743200,2743200,0,2743200,2743200,3619500,3619500,3657600,3581400,3695700,3619500,3619500,5524500,4572000,4838700,4572000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="3" s="0">
<O>
<![CDATA[两融客户压力测试报表]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="0" s="1">
<PrivilegeControl/>
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters/>
<TargetFrame>
<![CDATA[_dialog]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName>
<![CDATA[/口径/口径_两融客户压力测试报表.frm]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
<Expand/>
</C>
<C c="4" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="9" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="10" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="11" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="14" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="15" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="16" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="17" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="18" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="19" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="20" r="0">
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="1" s="2">
<O>
<![CDATA[日期]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="1" s="2">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="1" s="2">
<O>
<![CDATA[分公司编号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="1" s="2">
<O>
<![CDATA[营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="4" r="1" s="2">
<O>
<![CDATA[营业部编号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="5" r="1" s="2">
<O>
<![CDATA[客户号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="2">
<O>
<![CDATA[客户姓名]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="1" s="2">
<O>
<![CDATA[开户日期]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="1" s="2">
<O>
<![CDATA[账户状态]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="1" s="2">
<O>
<![CDATA[授信额度]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="1" s="2">
<O>
<![CDATA[可用保证金余额]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="1" s="2">
<O>
<![CDATA[负债总额]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="1" s="2">
<O>
<![CDATA[客户两融时点规模]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="13" r="1" s="2">
<O>
<![CDATA[收市担保资产]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="1" s="2">
<O>
<![CDATA[收市维保比例]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="1" s="2">
<O>
<![CDATA[收市状态]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="1" s="2">
<O>
<![CDATA[预警线]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="1" s="2">
<O>
<![CDATA[平仓线]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="1" s="2">
<O>
<![CDATA[即时平仓线]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="1" s="2">
<O>
<![CDATA[现金担保资产比例]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="20" r="1" s="2">
<O>
<![CDATA[20%涨跌幅证券市值比例]]></O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="21" r="1" s="2">
<O>
<![CDATA[10%涨跌幅证券市值比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="22" r="1" s="2">
<O>
<![CDATA[5%涨跌幅证券市值比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="23" r="1" s="3">
<O>
<![CDATA[其他资产比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="24" r="1" s="2">
<O>
<![CDATA[1个跌停维保比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="25" r="1" s="2">
<O>
<![CDATA[1个跌停状态]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="26" r="1" s="2">
<O>
<![CDATA[2个跌停维保比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="27" r="1" s="2">
<O>
<![CDATA[2个跌停状态 ]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="28" r="1" s="2">
<O>
<![CDATA[3个跌停维保比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="29" r="1" s="2">
<O>
<![CDATA[3个跌停状态 ]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="30" r="1" s="2">
<O>
<![CDATA[最大持仓证券市值集中度]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="31" r="1" s="2">
<O>
<![CDATA[最大持仓证券代码]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="32" r="1" s="2">
<O>
<![CDATA[最大持仓证券名称]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="33" r="1" s="2">
<O>
<![CDATA[第二大持仓证券市值集中度]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="34" r="1" s="2">
<O>
<![CDATA[第二大持仓证券代码]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="35" r="1" s="2">
<O>
<![CDATA[第二大持仓证券名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="36" r="1" s="2">
<O>
<![CDATA[A组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="37" r="1" s="2">
<O>
<![CDATA[B组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="38" r="1" s="2">
<O>
<![CDATA[C组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="39" r="1" s="2">
<O>
<![CDATA[D组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="40" r="1" s="2">
<O>
<![CDATA[E组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="41" r="1" s="2">
<O>
<![CDATA[F组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="42" r="1" s="2">
<O>
<![CDATA[G组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="43" r="1" s="2">
<O>
<![CDATA[H组证券持仓比例]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="44" r="1" s="2">
<O>
<![CDATA[ETF基金持仓比例]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="45" r="1" s="3">
<O>
<![CDATA[风险证券板块持仓集中度]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="46" r="1" s="3">
<O>
<![CDATA[第一大风险证券持仓集中度]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="47" r="1" s="3">
<O>
<![CDATA[第一大风险证券证券持仓代码]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="48" r="1" s="3">
<O>
<![CDATA[第一大风险证券证券持仓名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="49" r="1" s="3">
<O>
<![CDATA[第二大风险证券持仓集中度]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="50" r="1" s="3">
<O>
<![CDATA[第二大风险证券证券持仓代码]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="51" r="1" s="3">
<O>
<![CDATA[第二大风险证券证券持仓名称]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="52" r="1" s="2">
<O>
<![CDATA[开发员工所属营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="53" r="1" s="2">
<O>
<![CDATA[开发员工姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="54" r="1" s="2">
<O>
<![CDATA[开发员工工号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="55" r="1" s="2">
<O>
<![CDATA[服务关系员工所属营业部]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="56" r="1" s="2">
<O>
<![CDATA[服务关系员工姓名]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="57" r="1" s="2">
<O>
<![CDATA[服务关系员工工号]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="58" r="1" s="3">
<O>
<![CDATA[收益权重]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="0" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="oc_date"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="1" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="up_branch_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="2" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="up_branch_no"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="3" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="branch_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="4" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="branch_no"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="2" s="4">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="client_id"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="6" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="client_name"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="7" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="open_date"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="8" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zhzt"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="9" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="sxed"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="10" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="kybzjye"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="11" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fzze"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="12" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="lrsdgm"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="13" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="ssdbzc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="14" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="sswbbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="15" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="sszt"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="16" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="yjx"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="17" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="pcx"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="18" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="jspcx"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="19" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="xjdbzcbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="20" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdfzqszbl20"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="21" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdfzqszbl10"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="22" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdfzqszbl5"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="23" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="qtzcbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="24" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtwbbl1"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="25" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtzt1"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="26" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtwbbl2"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="27" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtzt2"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="28" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtwbbl3"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="29" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dtzt3"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="30" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdcczqszjzd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="31" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdcczqdm"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="32" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="zdcczqmc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="33" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="drdcczqszjzd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="34" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="drdcczqdm"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="35" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="drdcczqmc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="36" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="bzzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="37" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dzzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="38" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="ezzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="39" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="czzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="40" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fzzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="41" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="azzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="42" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="gzzqccbl"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="43" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="hzzqccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="44" r="2" s="7">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="etfjjccbl"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="45" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fxzqbkccjzd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="46" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dydfxzqccjzd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="47" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dydfxzqzqccdm"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="48" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dydfxzqzqccmc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="49" r="2" s="5">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dedfxzqccjzd"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="50" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dedfxzqzqccdm"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="51" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="dedfxzqzqccmc"/>
<Complex/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="52" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="kfygyybmc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="53" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="kfygxm"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="54" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="kfyggh"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="55" r="2" s="6">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fwgxygyybmc"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="56" r="2" s="8">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fwygxm"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="57" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="fwyggh"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="58" r="2" s="3">
<O t="DSColumn">
<Attributes dsName="证券类型占比" columnName="syqz"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="216000000" height="42768000"/>
<Margin top="0" left="0" bottom="0" right="0"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="true" currentIndex="16"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_sszt"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="正常" value="正常"/>
<Dict key="预警" value="预警"/>
<Dict key="平仓" value="平仓"/>
<Dict key="即平" value="即平"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="932" y="99" width="103" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_sszt"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[收市状态:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="858" y="99" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.NumberEditor">
<WidgetName name="s_ssdbzc2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr>
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</InnerWidget>
<BoundsAttr x="426" y="56" width="92" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_ssdbzc2"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[收市担保资产上限:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="312" y="56" width="118" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.NumberEditor">
<WidgetName name="s_ssdbzc1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr>
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</InnerWidget>
<BoundsAttr x="426" y="100" width="92" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_ssdbzc1"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[收市担保资产下限:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="312" y="100" width="114" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.NumberEditor">
<WidgetName name="s_sswbbl2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr>
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</InnerWidget>
<BoundsAttr x="696" y="56" width="92" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_sswbbl2"/>
<LabelName name="收市担保资产上限:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[收市维保比例上限:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="586" y="56" width="110" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.NumberEditor">
<WidgetName name="s_sswbbl1"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<NumberAttr>
<MobileTextEditAttr allowOneClickClear="true"/>
<widgetValue/>
</NumberAttr>
</InnerWidget>
<BoundsAttr x="696" y="100" width="92" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_sswbbl1"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[收市维保比例下限:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="586" y="100" width="110" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_fgs"/>
<LabelName name="分公司:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[分公司]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="123" y="64" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_fgs"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[分公司:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" noWrap="true" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="43" y="64" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="Search"/>
<LabelName name="日期:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="858" y="43" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="s_yyb"/>
<LabelName name="s_yyb:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="BRANCH_NO" viName="BRANCH_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[营业部]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="123" y="100" width="121" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labels_yyb"/>
<LabelName name="s_fgs:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[营业部:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="43" y="100" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.DateEditor">
<WidgetName name="riqi"/>
<LabelName name="riqi:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<DateAttr/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=now()-1]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="123" y="22" width="100" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelriqi"/>
<LabelName name="s_sswbbl2:"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[日期:]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="0" autoline="true"/>
<FRFont name="SimSun" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="43" y="22" width="80" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="riqi"/>
<Widget widgetName="Search"/>
<Widget widgetName="s_ssdbzc2"/>
<Widget widgetName="s_sswbbl2"/>
<Widget widgetName="s_fgs"/>
<Widget widgetName="s_sszt"/>
<Widget widgetName="s_yyb"/>
<Widget widgetName="s_ssdbzc1"/>
<Widget widgetName="s_sswbbl1"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="true"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="1102"/>
<NameTagModified>
<TagModified tag="s_sswbbl1" modified="true"/>
<TagModified tag="s_sswbbl2" modified="true"/>
<TagModified tag="s_yyb" modified="true"/>
<TagModified tag="Search" modified="true"/>
<TagModified tag="s_fgs" modified="true"/>
<TagModified tag="s_ssdbzc2" modified="true"/>
<TagModified tag="s_sszt" modified="true"/>
<TagModified tag="s_ssdbzc1" modified="true"/>
<TagModified tag="riqi" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="s_sswbbl1" tag="s_sswbbl1:"/>
<NameTag name="s_sswbbl2" tag="日期:"/>
<NameTag name="s_yyb" tag="s_yyb:"/>
<NameTag name="Search" tag="日期:"/>
<NameTag name="s_fgs" tag="分公司:"/>
<NameTag name="s_ssdbzc2" tag="日期:"/>
<NameTag name="s_sszt" tag="s_sszt:"/>
<NameTag name="s_ssdbzc1" tag="日期:"/>
<NameTag name="riqi" tag="riqi:"/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.report.mobile.EmptyMobileParamStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="1102" height="140"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style imageLayout="1">
<FRFont name="微软雅黑" style="1" size="112"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="SimSun" style="0" size="72" underline="1">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ImageBackground" layout="4">
<FineImage fm="png" imageId="__ImageCache__6709E3A17B2712D6B23823F2C27AC440">
<IM>
<![CDATA[!CI4,oncL?7h#eD$31&+%7s)Y;?-[sJ,fQLJ-Z>Z!!'o#@ESWN$49?m5u`*!m9L*Y>t#XNpk
Th6-o&H1pkfAe<.%)2S#hING@l#bh6o+\Qe'RfXbf@5&Q0\oO=KdSc[]A%.!J(B2&VV\uUHp2
^YuFrD2740f2R^#-.sCAAF#Q,Ys4M1_AaeH%B',V5cFNNm?acG?c^Fh1s*:2cAkk?4+sJ3T+
sJ3T+sJ3T+sJ5*`n2g'1XaCuZP+L['!=D$]AG>a1b2i^S=$(qZi:fV.Euqi!\2p'4[1E7"#Cj
))E-Yg5okg[)RlXfWmtg[pj&>=]A;VMp@!/BaIJce6'13GhZbXs$Ws"n!<QnkYIn[1$C[X"Zg
R&r@XS2(KM2V.*?S3DJMMYs3deQ'ZcW]A*f@0bVR'HnmP'(P)#,K*1gG35$E-)u3LNjlNJgB2
S!E3+-fc2%>5q7IB:^H1qBl'@aVD*[_p\nc9Z:;e$qLKktA`$kVmM)EU+eJh;pX(P,s)#N.H
-`3DtPoIHjt%MCaaL)POh[?E)uI[E`67]Agl2kRI#B!B?u4_Q;^/h$J?Z[J$E[9ZWQZ8?M=s"
NR/mGRq2(8-"&]A9,lU:(M1JFQZ8";g*`./?SDdOplL;;67VVCW-qT*^sK8]AM]AFKqG?V&fW-q
V`^q`#5&34jX2_kXIPJ7MkgtJu05lc?4:T+H!-(?S88=BAi\WA]A2r.KG0[1BA?F56Le$ePII
b,2(4VtO\1UdD0m5eMhJhNMg/n5O.p%ZN*ikL5@?38R!hf:d_C(A.Jc!t_PRljC._g:tfTjn
a.8Tn_JX,NR1EC&L5;<OX;[D,-J0#!Y]AtTl.qQjg^a8!Ig\66I%)PRC[uRTI%*r0rmjS%;VT
"9hmmYLeWWIf-a7A&>!qZn43QQb]AV3t0.Y5$f"`:'!6"-P&p4Ipni)bui]A_4,SB9q@q94S:>
m"92n+j?6$SX)sg@7>h?f0fmcRdo/8c<7Ghh+mOG*lT;[Z*Yl#lOd=`rl>E<)q[J\B;?FS(p
i=4"CKEAI:j#4/qg[+<EMAIL>?tLm"R52Q-CZVII/nVcA*bHldQgq&gf92g3/+$;C5aF
B!c!Ic67<o;kG3hIL5Xe)lp+AS&l's9RW0W!1:?Kbj[g;`l=TJlK6TmFYL<=\oM:h3qOmM^q
XRb5X#hGmTc+hMlQG(>^rGn6_X=PX8R']AjTZM$%SA7S&4\3U*5_At(k<SKEP;^^ks$%7dq]A5
2fi4Aj;#<ID<VsBp^!jCe>qXI@P=+oY*\]AIbGOU4so&=Ij50*?lp^DE8iJP%r]Ak;0[dp:-\'
VP1"L+!&iohbAJQDmIQYQh_R-XHXn:piKnY2rt*R&ihYq`@-HFX_Z@i-^*/bEh2#5$>Ec6J^
m(k+P&*f+1UL6+:mp_,&&,Bu]AfbCu:Qd]AokXY"e;-okVNj+VP]Apg0A/u1!16f)>Aif^/VD>p
Pqg1p(]Ap797ds:01+s#kC.Vo7mYpIJaunREk;nFp>?e58`C$`,Y:)`nDd2@8!D`D6<<-Frfp
"K]An'>=aa)/6$20paSZDlO?fp6t-0Y43AZ7[q7a)'Yt1@@\d)CMR:'6;+$gjO0]A9V+jg&5m1
81u6SQmul?-m2%EWB)$`7p.k8c>J4jn*F?h:kAADu(C=:d?E/3&-rM3Q1!eA(:G-^[o]AblCK
^!F-lBU?P=(L%;O'MfEW)uk]AZbIBA5iMG@QY/T9I\XS_qJVTZq"0!\+SB#\k>k-?g><Y1?.A
!1^9J@nqA41IWY9;`-KV58>+*@)CoQgk[k.Gi.A%B+6'X0oP-;7C$8[k()%!\[0S$jj>6*TE
.(MVYYp3;QE7)g>h8(V0qLL="j&u<Ga:0VkHh#`L(S-^CRf8gRQJK:QNu9R>W!t5EG9:s$.g
M't18OcngX1)0F%1"?Bk6KU=.pD>>"*>L;?=SaSE;7#?sXk(6/F'Ge&iIRj:&R>I<^-eV4Y\
/#_6-XNU]A`ucK;,7oWsl^gG+N+\G^7+A4%foS'#e9Dm+B8`)(n#*S/LBrAkJWcNE+'eAh9Y^
<K4WW)`>WXn6cfc`Urr/I'#j&8LUK-XQ*1UnMqHlf+kppo+=f&R>A>^nnIX_#OEcFnlF"l`1
D,_B:O@nc7f/g,WM%I9%]A$g".@N$.H\ClYkJ0So5Yn0>@rZ:'9gs&7fkWEa"eLLd(I>&Cc6,
i42^+INALgQ@e51WJtoYefshIZ)eDEZZ)6Ik9JF+ltZ\WaFV=mrk3@R"":V\I30b*DNFr6Tt
aXHeg=X>J_AMD"$ljJg*h=5:QJZZGi'G?,(>=&-qb>X%8@*.moS#i\R->m!'&ugq2:uS8WSO
GjHUr*!-`]A!!(7aFC"\2s%P:<<1:n*%l`pSb&Kc^qc;%$HURV<llJpC674ub.=]ApMK`OgY85
bWX/MY$iU_t>p.,:!*6M"\fa#!ej5_Xo-L!=(>br92UjjQ/&3KWBSBo5N"6huK.74e(0P631
J>#;='<hl\d>Qb9HZHLO6A![+UV$R!dem-:EEVnI\WdK18gIcoD/rIKe0Fnn!R.KPV:rI3(1
*XnDF>XiRPR/rE#Wsa0^[(1ZN**t`5ETj`QlP9JCC&t!r^YH/BLI3(_#"P<iGVknX3Q&1=LI
9)Co:Z/F"X(\'#(ZHlT:uTS:S?YIYTpL0m,AaLW*]AcP`lJ=6iGuV=:KerZKmQ?WEs"\bj4TE
0W;`Zqd1--uG:.Z)DstJjC10RV!;,G:]A?_"\E3o2r1n-2^rt2Z2#6I]A"U_]ArGI.W7#>QfjDI
4_N8"2?S`"E79@oM^5c(T]AnJ?goM%hN]ANbkOh=tdVs=b[6Le?BrR#h`N)(?OcA(U:4Q2W/Nt
*Y8YPiTqp,YuT]Aagu;Kf&<jqXR3Wu;U:WrnV7GZ4^]Aj<`=8##Cp7a_',OI,D+/d'%:3(HG`<
cQ"U?W!-R:pgGkb'7f!aE@Y?S9C84kJEPS9I2fUR13Ec.5=gm6Qa?1a>,dSoG,s?@&MbI7VE
p/r!cU\A1dZW4gI[u25W1cR3f68kQjWU"#!B<M_4o&R6!ik#d&Al4*[jp)cTtL4X1`"/83s@
nm=;6GL8jp%S96rEs"[hb43q!AKj)MdTU0r:,lksqVU.EpgO18H^78B57KOB!;Z+$-6BaM.s
1&<U,cUGS.A5UD'*qGh*.r7RO7Q)AUU5Uh.QX^*92dGr9VC9sT>(U9V4J_,(uE=Cgt35+(#C
QQLaWe$6(;6S-IgR;Ib13>!$&NO\-III"@)(#$SY[ER]Au^ALcP7"D2-72%(%R1b8BXta;ck!
NeiLILkpkCLkpkCLkpkC#Qar+1WVn[%&g;-z8OZBBY!QNJ~
]]></IM>
</FineImage>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="Calibri" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00]]></Format>
<FRFont name="Calibri" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<FRFont name="Calibri" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0.00%]]></Format>
<FRFont name="Calibri" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="SimSun" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1"/>
<Bottom style="1"/>
<Left style="1"/>
<Right style="1"/>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<WatermarkAttr class="com.fr.base.iofile.attr.WatermarkAttr">
<WatermarkAttr fontSize="20" horizontalGap="200" verticalGap="100" valid="true">
<color>
<FineColor color="-13421799" hor="-1" ver="-1"/>
</color>
<Text>
<![CDATA[=sql("hfzj","select emp_no||emp_name from ggzb.hfqq_ygxx where emp_no="+$fine_username,1,1)]]></Text>
</WatermarkAttr>
</WatermarkAttr>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="证券类型占比" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="明细" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="营业部" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="分公司" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="793bcc7e-3766-4b0e-8f1d-3aa22999af6f"/>
</TemplateIdAttMark>
</WorkBook>
