<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="data_title" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="jsjx"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT 
* 
FROM ADS_HFBI_ZQFXS_CFJS_TITLE 
WHERE  
ZDMC IS NOT NULL
AND JSJX='${jsjx}'
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="data_body" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="jslx"/>
<O>
<![CDATA[夏季竞赛]]></O>
</Parameter>
<Parameter>
<Attributes name="jsjx"/>
<O>
<![CDATA[回归账户奖]]></O>
</Parameter>
<Parameter>
<Attributes name="tp"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hfzj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT * FROM (
	SELECT 
	* 
	FROM ADS_HFBI_ZQFXS_CFJS
	WHERE JSLX='${jslx}'
	AND JSFA='${jsjx}'
	ORDER BY PM ASC
) M
WHERE 1=1 ${IF(tp="T","","AND ROWNUM<=3")}]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="false"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="num"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$num]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="tp"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$tp]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[if(num<=3){
_g().options.form.getWidgetByName("REPORT0").setVisible(true);	
}else{
_g().options.form.getWidgetByName("REPORT1").setVisible(true);	
}

if(tp=="T"){
_g().options.form.getWidgetByName("TIT").setVisible(true);	
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<appFormBodyPadding class="com.fr.base.iofile.attr.FormBodyPaddingAttrMark">
<appFormBodyPadding interval="4">
<Margin top="0" left="4" bottom="0" right="4"/>
</appFormBodyPadding>
</appFormBodyPadding>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="TIT"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report3" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="TIT"/>
<WidgetID widgetID="b25acb67-6001-4733-b44a-06beff532110"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="TIT"/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1219200,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[10248900,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$jsjx]]></Attributes>
</O>
<PrivilegeControl/>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="1" size="80"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m@!iI;casS0CkE!D28IP%N$("9*6[bJg+0VB0_uffgpo)#W*Ju'I&cC&Xe4\W7upN6)_qZ0d
@Au>&9n*#V'*f%Rt`Njfq\cjj>j?/`"+)c<ln9"m73k3M2N4q4@1@kM0FYkLLH3+]AeINmtW%
,.P*FG9*d;m,HmLAgg`!N&OOhl6XbNncV^\WimYm.aTc$CHah"%2f>i1=FOKOYf&!Yk1dlhk
a-[?!?7U+]Akr:HFR/+`Qi?b3&'_h:`&Ll+r]AKDGS`IVLJC]Adjb%t5AV4=<XF`@qP:>BJrq5K
R+=V?"Oi3H`nL#&`J0>3(3W\Y'5@o;P+aW@%kA:DZ[-$1?Wm+s2*&^5A>-LJ-)isd-p>!k$X
/"e6kmYq*$hN`?dB*1(CDc`B;"97i=nE387ZQ(7*cOSr`8n82?d;,&Z+L?[.lnua1UD10me3
N*JPnABLhLZHn1i`e4]AZADq(VdX[be>hgraNl!$)\u1"P$X7Jm4Cq+bEc**f=eg@,49bf'ob
S+m0<>!W'>X`qIe"[B%3+^?2[)]ApO^9'h]AOHAh2?a_.U=X<0=S#C/N3ci&E7O(jo@j?LI2%_
qh8Nj*FU6'CG#eN4_*?aLffBZdKg'(\)O!cYCNnR!=nnH[g_S_3`![>+)3HF-AI3IEK+2\bR
"PN_)[CnmQbHPt`".gK]Al98::o%gh[3:76-o6eG>#^]Aha"B9qa#9dhDC!M_*_R<DSR-Agp-m
dZPK+6ge]AH%lK'/>'h!VF/J*pB"pu0R@:3@]A^6BMWU\TD@sZZBh[MfuR&BfHoqAdInb%c>+T
mq3T'S)RY83G\:TN7dREX"Riu.[^!mBnRU7"dlaSp@Rl\W,E_PKikYTnb?g)eqC$d-k(Xnqj
"qqs8s&D@AXKoV\rcAYJ_bY]APGX>dh[n?&V:/n+HCp4gSu,8pKLPpthD;6T(Ihos'pMc=`>0
AJD;Rs!-f$M@DkP&c\i3Zh%:,j\2T2Q!NYZq[%Y#&?@sCLTbo`OkPL5h[9hA`H8QMD:>');<
]A/eWS2NX$<O@1u%?/m#C*Ul&q:/'fL2Pc*;/p%pT"cO[%9=?H^Vr(sA088DLNR.[>lfUbpmn
8N`_Ck]Act3\(ch/>WiQE+k?:aHZR>-r]AQfJ,ebV$hYHq(YJ44ZJ$?XoPtI?cC(_-mAYm;^_]A
qT+iMi64ELLe.KD7pEnA0jK6o;X97PT=0&S%ASR?W18[kI#.rE2"up='rC[<VIbhYSmn8P#%
i`R%3F*Q6W!($gU94`6u5@cQWD/RT4=n((?u&.hh&]AK7`V^2#uXS,`Tn#S8+DJ,fTO":,P]A5
_&h8!X&c?i.%Nu&]AWE;WNFk`nQ+*iO(4a>[Y6)LNr_]A*5q9KCfCdt"0P$ZWCGVNCq[D\W0cd
7C6J,A\SH]AdT;AU"F5QrW6JI5AL!YqY!"Y*?"$<3Z"ot>++k=72@2+&46_L`s6)ZsQQrrW~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report2"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report2" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report2"/>
<WidgetID widgetID="5ca23398-1e8a-487c-a621-08df3eec8696"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1984188,4076700,3924300,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="3" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[="点击查看全部"]]></Attributes>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="JavaScript脚本1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="date"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$date]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="jslx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$jslx]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="jsjx"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$jsjx]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="num"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$num]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="tp"/>
<O>
<![CDATA[T]]></O>
</Parameter>
</Parameters>
<Content>
<![CDATA[window.parent.FR.mobilePopup({
	target: "template",//设置跟随弹窗
	parameters: {},
	setting: {
		templatePath: "/HuaFu_YDZQS/3nt_Menu/营销竞赛穿透.frm&date="+date+"&jslx="+jslx+"&jsjx="+jsjx+"&num="+num+"&tp="+tp,//设置子模板地址
		border: {
			type: 0,
			color: "rgb(0,0,0,0)",
			borderRadius: 8.0
		},
		background: {
			color: "rgb(255,255,255,0)"
		},
		mobileRegular: {
			type: "custom" || "auto_height",
			heightPercent: 60.0,
			widthPercent: 95.0
		},
		padRegular: {
			type: "custom" || "auto_height",
			heightPercent:95.0,
			widthPercent: 95.0
		}//设置弹窗大小格式
	}
}); ]]></Content>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[len($tp)>0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="64">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m?rl2'A"/<(DW&^0<&&AP-udG'o<cQKpPu*>-0c$YIqi)K^oNg+@&J,BouR,=Kt0mM3_3/<D
4ML#Q]AMd&fuUS8,r`[#UI&LJ06"2DSY*jF6Cj(^L27tBD8mA^HH5Tme;-BcJH'MhUgt^II@.
9@!(P;;dTTV5($ktZ!DRmZA6F'!8,1FVbVW1<]AWH[p`qW[7*?4kCtm#!(UsEjs4WK'#?l=93
dKo%cVEkNqYm7.@NGXT#CR<_Jld;-[Pp:hm*Y$8`rggm*kH=j:?;Z9Qa-DA,0fga*'P3`nr`
n6H/b+:_(g8"[(Bl2%V_Jd^)beb.3?AXUs#5S;-Go0_E%Tq9m$>]AFVf#-c:,q/U[(fkaho=3
\;[hrN#';_/U?f[[K+*8V)97iD8Fe=eioU@!&,E%d@n)\cYOdMRPU[frZ*-SR%lYAgdcQ'd<
CnF![G<]AcN1hhAuc3"6fLrcnZ3U5%fhh1_fb`_in:^UK7q0UoclkWrH#)hnC)N%'&,"?-9YL
C]A`Qf+VQ7kaHgjrtrcqeN>,4D`[f5Qn"1/Vp(9<l;<sm6=0h-Ms9rt,K[K(8O@(T0p^nr_=-
lM#jot>u_c+GiC%de$Z]AnSg(^Hrl:JqWd#9@`Rn5,#/0YKR;==SZa16fZ<iZLTXmF2;V<M"9
CFf`dMJ;X]AiOoeIC/:hp`PCMS?G"g)mWMOQU0(%Wh#,r?#S1Z/JD!95"U5L7hd`#A\AY8up%
P-84Rmmphu@i6%HD-m!9(Hf`qokLe!Q&!uj.9tHe05=q7&k9R3ibk894'U!T`XLD1)%91(a;
LaIdV7E,DOt$nUhJ#5(l_gT@#OffdQ&.Q4n@qV2L(.uV@9dNkb$b_FIMH[dCt$oe0DUpa*<G
&rA'^[C$Z1Iip8APWXqha:9A;W8W$"?)D?4J/EGI[E'.3:QF]Ae@e\,.6\?.(7r0[XsiiBa>>
ng%iLfa"]AIJ6_<a2cO'T6,n\T"&@l6H"XEW0=C.@h$;i<\Yp,FXu2@&;uiml0S=GXh?/i^?s
6GOM+]Ak*Xc_]ARe@(:?"(_;r1t[k>nlRjnLU4>.:dbT8RD+=l9^cQneN'A$!T5'[<0q*[?/g^
o9-OL1&(ibhS*-ml]AJLGgsK-j[JIG>X,CD$m]A<XF[i/q4)G@c!-R9u#+66K'o_D6W/jfLe2C
*"6hBO@thC6oXGM*/+/G*iZ7J$#)no^QHLF6pQS7#u="6`T\X/50-0.f!ha/W4aZ6aW>7RZ6
om<O^]AZ%AnWbmbRM]AqJAJOX?m2EP2$MB4-Pk<1C;XXh*s<Y%R#`0`+N>>Td@9ln^qZiE=AP?
=igA[.?9/807)jiV4)(LTVCA*mbtsL0!Dn1GIc37qgp6"q<$"68$te]A4#G3Ng'qf_oq^EaE)
Z7KI8PL"-Ms/\-O$WXf8.:`C!D$%*cBh"kp6ZApgJEc[&T!m5aupd#X#5$4TrE89[r)(E-8!
0hpW^IA>2Cois@br9i;n]AKr"dR_KeJEQF<;BJTs1;Q_lBNFBXE@lo%c]A:$[TQ5?rF5POXZ]A[
2%`o40MeiWa_&k)(YAC$K!Wq>CW3^@TV<+lU"Lno80*A?nuMH^BZ\(Z[^F,5"L-3X)&t^dTE
1B0>!tF!-p6]AnC&,%l$L5')Q/[m/ij1bk5)<_0G"odOFML"9XIWaX5><dRE2n;4a!3AM[dX.
eP5kd;DIJ-e@:8T<$:J*&/>sjVIF7G,\VriF\aGOeVZ0."?C;!L,qQP>@a<FLIo6M;Ik9'\W
KA'B>Lol:PUJp(_Z+MXDfXL50uf>Vn7kLL.`cVU3NV;XoH56e:GhQKC2f_D$YE#<A8fJW(iG
"gQ"aL]AGj>R>A+XheO#^gr_e-(#u?<Yf9WC?02kB7)6N\$/sf4L+oHpgr:dYl1u;?<NsFu-I
qF_`[98R+Y1<,lE3c^?B,2aS`tJ?Vg_$'36HJYb=\qt?rgj1_p_+d1\L]A29q%;G6J&fF-=Vj
+C"L&Vio\qLZ0)lL!H*%l)=^i)X*MZ/`(9HW#@rYQ'nSSpG?i_6pkDYV1h(!u[bh7$MQ+5&`
$trFhgT_1nDQTH,E!863=F(-T8VoC#EJPN'?Yr_C@.:TB=tM%Q(#kYS:M1TK9lqU*j`UYN%s
mY+cAcAc&uWhROu38bl.,W[8]A:.pR0ap;goF]A7p^@qAdV$iW>'Jn+MUiXMZ:&IR`t:um6Rel
a=]Ahh_F0.dWb!@B"h&G1'!:[Iq;Y@=Zb=dJBetlL=/jUX!_fGj,G\=K8.0e/h\NbVc7%0u@c
]A]Aa08B%&H'\E+d`VOJZ(%c2`anZ5gp`+G.Yu6,n46fljJ)Hke)KNAhYu@lc\pS9#rd[/E\0N
!kABj`"'Q?^Sb7<P#BAQA'4o^C7WTg@,j/&F3EsoH/p]AdHbh_T'%V?]AGahebG=C/R^M=l.@.
E(]A<eF#\C(K7i7$)G#7:0cQFK#!=e`"]Ah4PCc$:nVD"cS*n\pJ^&5ra16e-DF[/k'[J=a>*L
bo,JrZp-%dQoV4.X=eJ9ch?a"!]A.M<5d#g/Wg%*:]A9-ZTBGRHs$0a0ouBcV<qpV"e%eUV+0/
;-LbPa@O-O^ahIOSG`fe,fIZt6)-f>/n7rd"ut4a[oc^S,Qu^1Zht&?%]A8Q$\4h4;addQtG=
=frIj.<X`MCshLLFmm3UUjVHW1YV7/K6l=40$2=g':]A;sR&/=*Ui83G9L$8tV[BE*&aBg:iQ
>U5jqa40_JW`g?mgK??5l7G]ArAjmqJ=PO717d"Fl<Of,JMb-4-tLT;(]A):5E;c\,F9'&eYVJ
[r%NApM^XUIKX\>jtIN+7pVL8&fX@>R@&.,qjsSI)-;WF`MA[,sD:!^I59\"0WR0JC\%(I<R
W7PWi'cAYu?_4-!^'%fS_>>>?$miU^<^26d"]A**=2cDu-GNcjh%r.gm\2O-_b!VY0[938*[I
LAS?n$fdW67,^@Jg>s8@H1aZ^"35#"j(ap%T"H6=:O:6=P2,[7SQ>9=5uNH<?LL*k@eqq$#Z
PEfGh#Q4`p;mHZK(hW;HO$T\jep[H^):3aR:7`*mDeCi!gT7!d!Lf::;dtP*s!M?J7__\O#Z
#RZ0'j)j;fYI4a=pCq;Y,cqR,5GPjUgL+SK#Ro:1'Uc0'V2p<!"D5LHDhX(95P(4S)4L9nR2
f&F/Wpe[I5D>hj!F42=c0m^pTV\+n*CU/j[[,C3_Z_%;1]AZG6L`h9T,)@@n#U5'IJd<QV+G'
kCL`h9Tir@rr\n&m%"'Y,..gmS.,7X<f&9)FV^6Yj[PtV1^\73'iTYA1G-ja/--Z.T2mH6A;
QVVGq8-4=48'M'2"]A\A&<DWj!:^'iq"]A\A&<DWj!:^'iqr@^#IaH?]AN->YQW)ctA\S3Q]A=jQ
3Im!<~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="38"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="522" width="375" height="38"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report1"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report1" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report1"/>
<WidgetID widgetID="683845d7-8698-45ff-93fb-6fcdf3676381"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC F="0" T="1"/>
<FC/>
<UPFCR COLUMN="true" ROW="false"/>
<USE REPEAT="true" PAGE="true" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[952500,952500,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1009766,2873828,0,2109290,381000,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp="T"]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction">
<RowHeight i="1333500"/>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="1333500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="0">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp="T"]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="4000500"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="2" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="data_title" columnName="ZDBH"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C1"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="0" s="0">
<O t="DSColumn">
<Attributes dsName="data_title" columnName="ZDMC"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="3810000"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="0" s="0">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="PM"/>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[num>3]]></Formula>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="1">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="FGS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0"/>
</C>
<C c="2" r="1" cs="2" s="2">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=data_body.select(C1)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$tp='T']]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="SimSun" style="0" size="80">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
</Highlight>
</HighlightList>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(REGEXP($$$,"^-?[1-9]A\\d*$"),FORMAT($$$,'#,##0'),IF(FIND('率',D1)>0,FORMAT($$$,'#0.00%'),FORMAT($$$,'#,##0.00')))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="3">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="56"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m<SM3P3L`89f:U923hWgV3>aK&\0De2;)GRA[sGtM5dJRM&T(A\=uZ,\M'55)2amBL.\:u.E
^_+JjNr+JkCoNMSP-uRa:W?s1n@g^5"pt*08EgT?hpKf6uK<H/9&<(N%K7h6^gYS=JBcF(^!
;/17nJ[.5UEZg<]AmB*C_!mZb]A$hbRJ`c^>]A/W,W3ir#E*3`4?u`2H2m1H?`56GR*WXc#(L"[
Q`n/Y:Sq!O$@ltU@jkeZLncSCO*\.;u6Q/AUoR,a8PjQ4NdH$?`<kFJE*Q?Qn4F47d:rYQ^=
ac)J[Sm2V.#[;oX^i3Dku#kJadT0k8C@:6:/-M=^fG<[[GO.JP8n3e%Y;Z3/%0[NcNWarf_#
IT@]Ar<Ip7h8_*_.P%E_L4F4&Ecu7!Y9V!dai.QN>gJLge9\OgGc.39`)p^2WaKSnR(:D)E53
oX6PhhW=mt\S>MgkA4m:X9!ZHhTt3ANBSC7<+VZL?8kH$GmF#JqV;.G$Gr"6m@Znhm]Alj8-R
@2*kH6iLC+ne:<]A+1.EaI,GJEZ]A8m*_;K>-0btjB5GD'BQ\:[+$]A[mMOQn,4p*l&(Hki+E<H
gd@7l0B!4j)E4/`A8\Z.7&<FnER;lFRm9rs&`Wp45IBU>b%C-ass<=C_T?@[c5mgN]A,aor[4
,A/:bM9VB/%kLJ4^j.uqM/\YaK)"0e%'knldpe]A_KTHl%./2S+3mHlTE,cJ6L'N"@lY'^`Fg
V);1g8H"sFW(?3l(^L?>E/PW3,D)DdG)p`']AVq%.4(WW6U]ANbQS++N?jr6#b"k8LVJG#mf0S
\9QHWD=;i8DhBGcd?qhVbObRrC"dR$sc''VDZs'jPB6\H#7-52b\8`F<8g('69^/mKm]AQ&;]A
C!l19]A=Na:gi]AMg3>KBU44Z8sTCt6EuKHb.%EVM`o;"4+)A`m-8L&Ps_]A(iUJ6?fa2`;E*a,
>t,NKtLf,d,/2#P%Jn^/XW9AqP6"+B&5!2kn+V+TH,pI(68o+7WX*mgb>^c>M+a8Dp]AEkm9C
DS:g[Tghd;ig)!W%UR-%I01r1Ym^gpNY6!WL&":3+tgD_Y&GbMO7BN*0[?bSNI:,HEPG_kVX
7b%sR=mhFteNip92N]Ajjb0/kqHKXJ'S+?idHOo,rO%nA(6usg*97n@:=M?^@-$ebcMN]AUHKB
34+JLp),n]A8$mq(iE8c@<I)219RW3h]AM)YajD+FH:7Q)siT"L+CDK;!H^:]Ahi&Z4E75#LBq*
_?8tPu^&eGjSrkYf`Eb%qW=YiE]AKHFn`[_LFUQ<kueo+g=@SQCbZT5F_os6=7+G[^*7c(VY@
0DKM#Y3fH@4Q-COYIRtY7f18P74;;Q.8HQ5S]A.O1Z7835-?rpD.Z85I]Auc#Rq%MBcF9ba(%A
4>c,EU"Y)U<<M/BlNW*<6'XQ>r>,'q,6^5;'V)$&\Tc^cJ@;kroC2$:d/o<Lh_1n0&'`+b1g
HpHtb#L6P=U&:R<?u#B`bm2j'#7"9<"d_L,\\^YYWJGcpGkEl;NM?QPUD1uPnU3/:#/5'/[9
1`_K6*E$NfH2:/$mDnmW[qbWHfbeM+5O<Gk(JU-*)@T/$/Qm!1tr?Y!u$]A=iB5cemm&-Ja2,
AAdpUK,ANSLG^:k(c,UcGF>QN/;86b9W-*ap5LO1"b[TEp&1Yq6.m.;[(+'*O&t-oEp>Hqt)
POm*ACSUA(+qd.#W_mu+OP8Q&qV.S.OKijej*"0iCb<Llt>NYD)%`4Spj9S\h<Y0X@9W@_Hr
l&G1?e:kE?]AJ=4#91SNOV'/q2gs=CcPPIpBSP+/UGdLVfK9!*;nI4hS^'V#3kQq8O)3^4/;.
5Fi5D`>@1k.Y-\'qPLO&[s<Dt8]A(_?LH6A<-aEr@,feBo$_#e&<3=IC.7QmGl]AjT97aJH[o3
!!'-!&kD+\gEQH:ilY;ECQZ'7C$EZfnLZG[`"@$qB23S:i&\@+!%0oaUOq.2Cl#dK$[To2JA
ljeh4;>^+BfS?]AC6X8O2mHPLZWRA-eYQ0?+HO9^j*8"2+oX@RI*7,n.LC@p-l@r>@ak!GpX`
o\L]Ai@ZDk&sk%UIqc:e$uR\Z2!4+th5Bne[#4l#'Q[95HqdGMIV262f),<Y$aB&9I!X/ul":
.jU5\Sua!S]A&`,lDq!!)T6i1K0cWs%#&GMc:pM7C2[%J!T#]AEOr(b4;T3Grm6'Cjf#fCtUsG
X#6(0FKqkOB.cm/9Xgf[1$2D>#B`scF;=J?<g%4u4tLUdU-Uh5D!;^[,'cP;0i(f9WB/1'Lb
Ka<5mfT*4pYfjasJ3kl2XaYj7U[8d'^1Orq:0LPIh]AB$UVU,U-:g_<8o6pZ]A.sTgbEKWP64m
i0_JJV^*>m9Y6B--)'dZ"*P2Ik\T]AF_:ISRt/SQ<]AA&ZYT$:fbE\(>:UWrU/JSF$KE.r!6]AR
9mY@*Al>_5%$H/YZ$T=%bGQU*gk;N=lC%9UcfT<<4S+;Ej4BpAc:V.Sif+fffT>*fq:YGBu4
'KCIVtdc6o0CbUNuPkA+1,5^KVN[8JEs2N#<F76%>=l&Bq3WlF9Ce@AQ0r*77n"+@e*MOL"d
JUcVs-,qhYK"r?Ee(CSa0p7'^NKr#b"eskPAhqaG.I\fThHJTU9,LRHU;.1_k`lU_-ILF]ARD
,QM5QU^CjMK"Mp4Ds>@OBf<&]A%!,9HbKUj)=Ad6D*FOI/_Pl'LmA)h[qM??\PgRRJ\\PhXU:
X[MOg[\u&/\.7Ue?&s<ug&bBo-c$\>aW\K=_S#=>B#&Nj@cQ&EMndu%.FLLrj[S06)o8JP^,
"\&Gp/jG;-4Nja=[:UjN2>b*5LS<p-)8akJK4F\]Ab6DeYi(-%-8WPb%BM%Vp_b2Hn-@?9BO#
'$CW^&M=<2.%Wsj[soLiq*GsTQo+-DYWZ5gA#9QYJ?oJ(0^e<eN9)aGJ^daC=#e/tCYgj?%m
ShfV"G!kc44lK&0'smLr&;eQb_14Y^0$_VpTbf-+4^V7@Ln.*S2C!fT,'56[)6'-jpB,$t=R
Dr?e.H&FI"SaF3,!XIZB?"[EN277>6AZ+CR>@SaLACb<(3tpG)J\H(n^SUp([':fEpX0a"^Z
/SNm`;NPKk8;Wi6#j4.I-=Oprp[V%E23-((U\B%ACN$&R\IdUp?qT$qO,CXt(kS]A(/*iV@o*
#S9hYdrNu!)YLi7g/K:=C_8X1Xo?K*upN.=kY!LQZMn`G,;*n?I(5.NHndCYf+T(G[fi"ih<
X<2=nMtmcB">C(ViJrQnSl_5$&o,#4H^kh@?1#+?5c&lJ*<K[("#73gG0[\eb*L/\KrW[!p!
KRDtIdP.Q>Dp]Ac/@F+p=*NY7baEnOh<1"aB'I4p>>YZO[W"iSk+G]A/f7;J#W,87TEp7*g2J_
m\=QG3(J@$!-AVU_uq%P25JrRXW+HB?5!@o,O!SU;>]AMNIZuc[hWaO)gZ4=IeAN?kVmB_G22
n`"dX^72PW<hE<(VR*GEV\tK1g.FFcC$^-t*VCs1).<tM9*@^7W#1+i.:$55W0I\Zfe!u&:<
"\aLiZYH/4RnDXC&fN"\[T27lLEs>?d1O$.I)p698quFrbWhg\=964lLQ.V0$,`dD-9=d8a2
E68;"ij!o]A$!ZF01SPRlH)*4SF.dKn\MW=#@<J\fdt7kdac&dU6&hOrF=RbV2DkBeB"i8P:E
bRk-/?:s.C[N+R'VUuW-4Ej;.nhZ6k^.&RdobK@M^c[pnjQO[[8h(_O0hepSj^q<3\O]Ac_X?
>PPg'$G[3=dILOR7u.0BW$n]AUk=]A>0&mK(IM"PS2pc1";!&4.I`(<[eNO>S)*f"IuqG$!Vt8
]AMB5Q`?!%[AZtI=!8M*;$Ui4H:#$>Q\UiKCKO<oS)(i@2(i4V,I,df>4f;<M9L,6[52q@=e1
?tTMfs">#8r71pmK.A+SdoC/l:*1Y@aJ,;qX<jj2>^Gcog.1USFb?+BqX@WKf$9iZp9,eOZ+
nt9i:s4`^Y&]Ae]AES.H\9ZZdJ-I/#7lqA:u/ZAmlaWf$$kb%R?es@h0dgPB"D9G'l#9EWAf:*
L1$jp;%e.,R?D>,E,sOOLoQa\qTV-1JM^BRa;qO5=uX\'16DuI+8PTWJLdS8eq%d?+Ks+mn=
+F!m8MIhJ9DZ;$)+/.ieo%(KV!D6#Ckqr,+5t@i?KeEjWRa%(3p;49sPSHXre)`TLPil`YLT
[YpQ\W^c=f,:Xkkm#hjgWDTa2Q-\K=WD9nl!i-'V\f[Z;>3V_U3j"FsK:&!Enqc?E5g-mIBF
3\Pt5G1E[Uo59ARX4c(7p5L`m1&Wd4Mr#41%+JRb1Tr'@*"3h\ShI6?uE4-]A1loXe(;Q[lMI
6YOfCJJXP-i!Slg@<?X@K]AE*9:lA!`6MbDKp<i_Iff.Pn`ae8+kd>[@U^RZnm65RfU/W65Um
AE7giLUXUJljW!mHr?mt2d:=MO_cYq8QrAWcg*W#(,3W+l_3WJG'_ep%_b#n><aINqLu^0J)
EVNlp]AM!&$<o))D[JFN(i:#<DKqJhQZ2Do!23roW^Emp=,4nT7'%D(Ajq=<Y%ej^9GQp\Eqm
L2(1d*BrDt(HSauf+BMTO1Z)I!J6\g3fj!Xqa,HjPs&6+D9et1m]A;#9is.q':a\KOtqiDBB?
MK`7o6'<Kgl@$6.r9]AekgXJbNl5&<QeAQSn1:^;S>4MiC(UP@o0JIAFc\P,q$'%l,++^>0GW
k,+9+\&K`2?-O#5\o!:D;DOL!SsAQj9;h5L.o+of8k%a;?k$7JS0F3T(rrP2EJL\Ya4caD4r
rR'DXmQt$eeO-M-gp[Ba9XJ;<.S3lQB8gihMRUP)XIutVW'`3*8<>1Bgh;:3Km,MUQf%UXg<
lT<2a<\cN-4P/\\crj!TPqI5EDn"rOAMX*,,,kHH0<sAq&Ja%EM-4cknP6JN'[Pc+j]A?ISdr
\0GZpc/,rrqEGWnns*Z@KQ'GtZ_R8(2In%)%^r5^%GA&:oDHJ]ACr9'#c<W;T!5Z_gp`&I"QZ
rUdmK"%NtSC)JIQkt!]AIQKeYE8M,a4=aX;lIL?lV8p>983.[Y#C1a*^(UJ5$>SsBC+D=D@;%
:RF^='6j`n?O1Z^jakN<m:kJ:q0[tC3#-!b>0]A.FB(Uq^rG5'h4?SX"IG-\@ngoXNUYG;;1S
K=m]AXKb[W1!!.;d7KB=6`_V)(_C:qOc]A"G+e+Kp"CM8(Hq:F7HNOZ8X-31F3S[XRfH(Ad8ch
R1uT]AuP[[aQpX1Lt#&E6Zo%Ic^)9G?TK:U>0,0U2`R1HtW@qN9.q>#Crtg>=MkD,Y9Sh^:`<
.)glCG)E,a%@[&iiduVeQ.p"E77.++oBIXjh:\q]AYs4$clAg$G70;Eq[9cgP?Jj*#YkMt!k%
Y7KMlmHc7[0Kq-Ef>WQ&OD)mF%rNV=0-30QD81E-X:=e`?5aVY;%@^XX.+=Lp8ZS\7$,',Y_
>r-1"D2B0<e.dHuMXZRsq[Wq2.1Z(pQT&diNPn)a`F^;Y,@jC12sM["Dd0jO!7=R(kjTp.oV
&aH8b<Em3Sab<:ZMVY"S/>Os9?Ir;?ClVL%=9s[G9Gjh-q-ri1+t^^+45CPmmnWIe<J-T5'V
#7Epj"08?i_9JVkY4/dl;at:mmf*fD,F8hKST@5pO*W8PABg-`:Tk3"j68JU;o?O@i\\-tST
\0TBHLc<q)d$H5jO7Iu5!@UCjgdNl7@)d$5Y$`.KICm66R"Wh+tGGV$D&lT.W0-%++e\1pmV
UMmS3Pe:1r`0+";AunZE:Pcq*S6Q%#MNtdVkV2WV8sX7-in:/>3K/Yc09O=S*(@s>PbkP2j-
foNtI]ABdS$WS^82$A,HnPrTJjB49"l0)"02<qg,!,^Ma%?!&&hRsq9(h]A,%KbR&i@8ooq_^Q
Ig7[$"DYr@C+-N=DcBDbafRN[GYQTTCmN>[M$*AMr,`J-?L8pu>:S=m\aZ:S\\20l2\fZb&1
N*N/SpsgK(6e,A$LY%**6=kBhG(JE9&[[517kTnkU=mo0eHD!si@eIFP?=@sQ]A%,7R]AdiVm]A
9%SkXU&@U!Rk0%k9=I%[SWnOJ9,DIqXB]AS^i11I,(/-U+5[kL/Tai+qN#._."UkRQ6br4BW^
)J?P\9Hh?H[XOY<:!7*V"ops+-n@_d+t3^IQ[<ijnE[L5YECh\JBM@o@qj3hP'Wio8@BdM;2
AT/amQ)4X)8L3p[4b.o,t#6cD$B/q3(;;E)s9C)DW6;35T2=^3VWel_eA'gkQ+%:kEHPukJ?
_*U9A?'+L6>9r#=Zt0csEAP2^-mi!oYJA.Hp?T<#>/$r:oBJjmhhfi]AA5<i^!DKPgNN42U.m
(9`Q^F1JP5L6ke)NAH-.RtiZ@#$:EUcfPAYOXtC]A^9.Y6L+&j/0+d)"pip1$$6KQ'FRV=8Af
G=f@&?X`@mg<j^n&s7XO#CTD%4iOXdnIAn;OnS(``C09-:,[a#[%uj?a`PLPH6:Rk[2>!e(o
XXa^39*Cg"s)Rad_kDGB4A?M^qF?8%?=mGJ$Y4-i#F[L`_PqI=7%b,Q;+4!V"3N]A`jHi$9J]A
Q]A=4Rnhn,3euObnac/Hdrt%@.Cj91Hnr<8/eB#A^N9qNJ?0LG[&\\!('pX69T6Ztdg)*P=&j
=Aj@QE%ls^'O7i$R,<A[B<lnd(Yl4$6*OLX,`]Ah-X7@1r6BCJ)fikC[d<6@*Iu.Fb#f0HJUF
fbO-A4`Ce&[W/"D:dK&gK3bhR"(4d8>r>DLLt_"Yl"j.(InDAQ^r/gtpuBA'<,l`3]A=E@VO:
_fDi)>\/n=0hVo=LY-O!ooH`oG4Z<R$I/NGGNe+KB1no;PIRhq$)uA%/NJ^C#="*dngQ1&2?
i%PHkR&^![9L)nEnMs0D;[gFDO&gD-VBhC5_NDG15Sq%ONc0IZ_?a'5rKuZkH>*'hD]AY1kTr
=mWG%N#cg/5#.Rqu?ca?]AUafOSCqC0a`oJl&smj`nAGZV/k!TXk@Ws7?Rs!RACJ1O!Wq[RLe
F(WY8cCn3S)q.,A!%sT&R"+c)LP>MM4+>??UCMRa-8aHO=+)sY,DGCU7H:_PSUKY7I.Sa\Hn
)VA;<C=_\TBKF##Il?`(DaL0m0.0&N/BWnuJmn<)?;Fp<3PaJ3BnOh(1FiT_(k4msQ'g%]Ae$
([u6XbKXN/a`iC^a>&9.Pm^P[CCSAaTd3[\l$3J\sWLTn;PHar-Ta1FqXCE0\o8u>YYlk"9d
8%7ObP.n]AA/`L[MpIVn^Ulru$r]A-]Ap/]AJ4nK`D"NGL=s/mnVAU.O]ATW2HW3ap?SIX5q*ID#^
M=s#%cO?<UW\*]A%o]AICT^N$.7IBAr9_=DJs5=l`+8@8!GoLq!SX%\@,Jl!N$>!6au/p[@]A":
7h6F4C>DhJHOp*#>d":M<glq-b+^[ZWU>J$KpLQdP*`@)A-Q7,i*i41J"EP78Mdj]Ab-pZ\ZO
g\1kJrpU=Do<NA`\2$X/'RY5TG2Y00XlC]AR8Q@o1CD[Y)L:HX8KP`NF@\]A_>k$*HML"#&'tK
Tc,32NpbY?FhU7pO9WfG,5#mnY'#NL]A'NHAH(F"rp)g?%lA8K?55KlP3$^T'XYffBnpmVlpV
K2-RYG*Y6h0,T9RI/qt+6j[dr>ODQ4LXJ%;s^2d')]A`7<:#oEA]Aj8h[:00mY,%cVJiTOZf$)
s6@0pZ%C4rPC#E+/II,Y]AoWHYNY0`K8"Q1UO>^Kc]AQV@dl16A2ZS,^oI:"P3<P!r$?s;M@di
nj_(670hFc=aH?<4*/SM1@>V6ANI9IOU7hS#AhWe]A3_!H6d2Ch,H<PY@"JQ?mGA6&MsM3Igs
%qqkZDZ;\23Y1>saqmN4dY_E"D?4Wd'=(hmVj-W0#;]AEdD`cdUYep2S13X=0oqmabI1mni;9
aLnr\6,"9a3ApMe@IrOkRUR$\!!O@<kj3If!m-.,^8g00a2#JRMXFr04Y"/d@")qWOIPe1Z,
4+Fp*_2Z)L:nQ^lG*&NJC\Wji\'U.Nl=5?(HrM!^XQ3'#XI%9H??s&:R6hp,n8^V)$A^3hnJ
uQ1gl,UGLoe)cYgUjXn8!*-Y'ELZkag*_V-fOZ<eO3e$!.;FkaXPcd0rY'[eXE=c8X>m_M8a
e7`[s:h=(@b3s+RB6COI,L$p<)HE9XDDV<-K]AC]A6,[G`W-n@V2T5'Y(-Zig%bh!*&m@k'hh`
-?oE6GB2MgVROE*o.#Yskfp_W@LV4^ftLNALWIn>U#gTCe$*N<Xi='RYB-;u3YYPN.N@*,Mp
98,7,k"(/#3/Z-u.=>BdCMsQZYBb8Y`GA3jXG-)P@4gDFhq;hDT\\d$j36m7B.d4hp=A4ug+
qK_BSZ`?GE:=kLG:JlXF!oZeedl!.enCt&HdtbpQJuReRrm84>%*SNQ8[\7XW24.SD2b(SX;
F.7M!N33^f7_Mf29JplQ!WP%$X=[E+%ZXr)g]A+-fqQZ,Y$4)tGT.%ul/Z@pSmk-YWT&i<e_!
8=B8+28=&#l$enEKcuG'Qk#4'T8dR\-12^sGJ!i*WFd]A(Z3SIfIONn\/=s'j\1fIl-N[pcHY
hU:+-6GY/G+b\pD.Ri#M6L,D&u>g\_10(nF_:PP.n@I6p2dr]AEV[e,_:<jr-*eW\jhp:,;9K
Lq0Gu@]A/*T,^JH8:31*"lfJgerk&g@XoDE,=*j`!E_MWZ(AVXeJ2M@TLg7da2?BKZ<k2qHr7
mes7oo5*4C\6P.?#HcTM.<He;;1]A.Au+\@B?LlWp`FkZEfUG=p2PTYo$_pZJflZ&_RUM2`5p
9UGDlI9#Hi/?/6K<Z`e2P&SO(Q^L+uBY^eO6P0/ph,p0$(V]A\Mf1)ihVCdMWBRCfI2be&OBC
p#a`^FT4\H&1hp2aH1bjZtsuq"m>os'O!mDV]A^ECYkT;eH5i+)^jGiurNlh5,9$kf&&Id9ea
6T?P,mf"Abukp+>`'Qrc)^oECVu3A1FtplrRHK@Z@.[TarX1CT*R`0k)=nHH%M4Mb<@\%6pc
1Ds8MK'AlDe&4V+7+dkuEp9-*9'I_D:hFbqH5SpWT5PK`_l+8;l`1%Ip9sQU%EATi9*Iji@U
ls$Nrdca)Ahn,15Cd%T*Xhtqg>41!k>!+GYkO]AKE/5GX9OTgSS^N-DKl8[I:R*fVW*Rj'$bQ
b27*9[[F"=N8Fu8/C?MLA9n=`oiNnEE8!?U+md6?$aS.P("^C_+4P&M*(`4<b:S1sI_gQi^D
Ys>m$.O=%OariU#e\l1@DSJC(b$=54['qn1r@g1"e^9CT0q*)%SK;!=qJV7MN7tIJD@1'(jc
;ko&ZEk"LB3"(n!Y)%g%PgE?`jmdQLdhm>l3j3:&d`06l@=lpKu%=k!P,h$=8ao\0r@$)Mu[
?hf8V%'ZOUpS/48ODYu_t`697!GDWL&S7MR2C]Ad`kjF.L7VPJ7ID>L,1EdusG2S\%*^#(Apg
nE9fC3VVK)I<lVL0)9niRRF4GW!,P?4F)m=2Y&V_bu)TRJ+eLb^^`95Br*sc9Ad_pV2-UO]A/
p_D1($nPn+CGGLpJVKB]AbGI"@HmG2b^shgB8%-VQOu5JPIJ$=/@g6<Bk]AcrK9ZF;;@o?%oFJ
=.u\A<TkoS'tO`XKs!aDU&R>Bf7`'E^?!PV[9!8gGM;b7m)a&;rs&~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="2" vertical="2" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="true"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="265"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="257" width="375" height="265"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="e2992dea-7c3f-4797-8512-d427e3181ee8"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="宋体" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[1828800,1828800,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[1984188,4076700,0,3429000,381000,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" s="0">
<O>
<![CDATA[排名]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="true" topRight="false" bottomLeft="false" bottomRight="false"/>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="0" s="1">
<O>
<![CDATA[分公司]]></O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0" showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
<C c="2" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="data_title" columnName="ZDBH"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="C1"/>
</cellSortAttr>
</Expand>
</C>
<C c="3" r="0" s="1">
<O t="DSColumn">
<Attributes dsName="data_title" columnName="ZDMC"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$num = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="10287000"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[2]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$num = 2]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="5143500"/>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[3]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$num = 3]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction">
<ColumnWidth i="3429000"/>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="D1"/>
</cellSortAttr>
</Expand>
</C>
<C c="4" r="0" s="1">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
<CellBorderRadiusProperty class="com.fr.plugin.cell.attr.BorderRadiusProperty" pluginID="com.fr.plugin.cell.attr.borderRadius.v11" plugin-version="11.0.64" borderRadiusType="0" value="8" percent="50" topLeft="false" topRight="true" bottomLeft="false" bottomRight="false"/>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="PM"/>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$num <= 3]]></Formula>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" s="2">
<O t="DSColumn">
<Attributes dsName="data_body" columnName="FGS"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand dir="0"/>
</C>
<C c="2" r="1" cs="2" s="3">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=data_body.select(C1)]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Present class="com.fr.base.present.FormulaPresent">
<Content>
<![CDATA[=IF(REGEXP($$$,"^-?[1-9]A\\d*$"),FORMAT($$$,'#,##0'),IF(FIND('率',D1)>0,FORMAT($$$,'#0.00%'),FORMAT($$$,'#,##0.00')))]]></Content>
</Present>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="1" s="4">
<PrivilegeControl/>
<CellGUIAttr adjustmode="0"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand/>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1" paddingLeft="15">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="88">
<foreground>
<FineColor color="-10985104" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-657670" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#0]]></Format>
<FRFont name="simhei" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="simhei" style="0" size="104"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[mCRR=dolGucK_EcZH*C$@?jh)PTihtf`5<?,"O@R,Tn*>64s>8MUGsf6qX828/4H)#Z2-E#e
bm&#QbW"0n^f;890@KY3pN.2nqFqmpC9LL,h%3c?mi`hYK@t%/2?t9EYME"o.lf">?90GF=M
1S,28#s*!68M,&./+.pI?Kla/o_pT;iGEt=9+B=a$!3>jM9&sMl-FQINZ]AF(%6e/B<L;W5g,
ieWX?EH<8pZsmjhXK:Mhb7OA$<DKl&pCnC]A=%Ofm2ilh]ADFmA]AC<5akg>mR(bU6Y9%QQ&b&m
ZI@<ta>+oS@mig\<6iJMrl0b-bad%&JJ?Um!9ZhpQ>h7&Z1n!7,9I9#!q+^8#tcf+Eu^o`CT
Sr7PLRXGc'@39Ek7J]A-#L$u2:VZ_?cB7Ak#H_BIik0mS\Pk/e2*U#\5VP=Qkp^Y%E.pe=dkF
JaL&5N)4Z,+T9.&>6BqiV*_i,]A/<V8P,$ipB^Z;gh0m6QpVsTdPrK@O,*/l$CLthSI6VC,cR
2[JrU17%To0H=:joYu/qk)lJIhh/@`O\=^DuSMfbp6$gW>R[n",fJ;Bj]AUc4o"o_sS0=ROpY
F`<!]AD6uLV=+'IQMR-N;X3/_XEO:`rqe8*e$I%obVs*q/%gS^VlBsYP$I;gWi+[s-F0-IL(N
4nS4c(0s0;^@qL?b,0WjU8Ad#CXmJh8eK[-R8n<k;,i:#:Bjm2oO658uTk/R6VipPr@4HKl!
J,]AZfIQE:j'lMj\Ee0=.XnR,)b8iN-[2iF-e4eD\+7:0@RJF<<M5nCs4*OrY?">d(8@LYb?l
rmWK@hX@0im+)Ln=>(%1n.rYHLtg;s(EiPp(W6E=ipHf^o6!lMd.M)h3c8@d=i<_%r\f\:Qp
R<RPHj.'aEiFN=8mAOK-bj?m9U`(lmhjB$oEWp%#c@A&-3cVcPjIp!MqS7I42K):ssn%%jf&
!fiilP[sG14.d'#;VD?<cAS,VG_ItPr\3`(k]AcD]A_k.=CFPi,)1ff$EtVq'psD>s#2,,Cq=_
=[rjm5ck;L41)>7S_mCE8&>9DOG<u4oFA9+rT+E_]AP]AE<>uQ)r4e/3@3!AqnZ@[X+Z^MQ"8;
_mg3hiGCW/$?SN<.?*BrKs5Lp408HIkYMJ`b2f@:R`]A7<<K-bfb\AeRE_-:KC';NnlZAmO/K
n5m4_5uq@8+dCKgXjKS6X@fDRtB/aE[',D3&r%#9&>CXiW8h)'6]A3-4!1CA&W'LQ`?C%HL>,
tk)BAeMW-e0FmPq`'T11%@d$:*4[]A8@bed:_dL:]A`pWX(L\O7+;=m&MW5,E)>:-1VmiO2,ML
(g?@@L9RuCPTO+pL#P83Pl1]Acp(HX3!5n6g3;:eja"^Zce6o5&Iu%!RQd<7*k/d.^&lmKaf8
k8SPYa[@u+S\kj,6q?k-J<)bshk!UHpO?2Dt?0>EQ;_V(S<m0;=a>L/A4<KSI8;d^78m=%N(
d/6rMdUM"QCa"E>kMu.k$`"fKc<&WH6&)DRHgonBpfRgkWAd-F@g,,PmDam`a/`U>0s;pe:m
d]A-J(+gROZt?r_Ff,"R?/W'4aDDT:\$`LE\?;;N"L=%$5s@-f)out3tJ5T>T0MrK$<3$Lt^%
aeH)PWY]A@G<=0S/2$b[g'MjV\(X3TM0<1&Fb^r,m2kZN%>hilhU=3aC&HdoKU!-1ZHRn8e3T
SiaSQOGt]A&Ki'LX.ScaTPHn7q#H_Lb>ru!\^,Eoe^`-4=Cihbcl+5=au1UuJjrW(jV[!r8BM
OD=^CI>aOp,EhIVIa:EZPB2!gX0W\1p'R73!Y3_"_4_FF\P4Q58c8TTN9_f!8V_uSOO2rI;V
_A[EnY%rqmoGhIcfgfDijL`85;rLpuV,1!FW1ds1gCpX^*\YQaKjA5A7UE4A)sT.r33qsr.h
0M-[:?b,nM"oIcl!U$\NR;k;qn'j.3t!0:S]AtreB)DWQ5<W.B-slh&N=\[H681.lEaYlBR>u
$1MsSGm?!5c@$o0!M)`dUdp&5eIg+0D?_NH/a[[ST0SqfW%QG-W!<G+E`6G`,>Vg!ARUi5)S
n@M-cJ/R+@FMLL#l>?A_Sd=8p%Q1*QN]A",9qNaH6fiW4ElM>3*bg[Gj'Cu64s+a[g)W9()jf
_l2pJV%[4"m3*L([NBd/F#F%SXEHmPn`P.B^h-$:C9G"D$MMR=[\):h--]A7cG%,14BB^^<+R
8:P!iajkMNmdplnC>[ai&#j*%KHq"9FOTfPafnZ1QO,<A1mdML38=gEWl1P^Tq7,HC$o#_T9
<P09UEH;%!7t4#B@.JOX)1+?_F:\r`J>-=nAB7#+SiX=;q<U1pppXp80^uXSpKIc^+MD']Akt
W_uc$InhR5W1+u@kl\@et$Lh!7?0"6aaKt`3Y$fHf/<,[u?TVC)B2NL7qiU6#W(0%j=A;/EY
Q;J=G+'*aUSu-0BL4dB)u>$S[I=4WP$r;9VW)GuCq`(jb<%]A+5COX8!ab4qj-ZspY'i=TPce
9Ol1Z`8fdU2?J#7B?Y5R-=Jc]Ag8#bO+]Aeb-p9;F\F:dj8ICUST-gei0(H/qD#mKUEG/^QEfg
rPWRP9P>k:ZiZt'0t^FjQOokO7dd5O7AfT<m$H;"aNp^A%P'nmgajc*m,PXDh-lg#nChLmr7
DSDJ`(`\q'$U-;iUbRW.<I$Ybb$@7u8NY6BG*<f^8lVU(au3Ch1dGZDXh![Z-t;%b<8>c)8S
WKa4qRBeE6IOLGS=0s*M]AUV6$LC[&1[hIdY(e[oI@`5u%"7PW8T<N\Pn:N=sb*T)*!OLaDe>
PikA"i%W/1?eAs1re*TXja)XEpO*_]ARut+$eWfi;a#3K?4LX"2!dt>/ANZ-;T'<R.Z!<OcIS
I&:-VpO]AP;geAusg"e$mbTq=Y>P6mm/cD/K-BJ@t+1J<^rcldHl&?bgD56B!oBg1C8cK0-Y&
i+<0C\aN]Aa*XeekqdCC!#Z[R4:Y;;O-YY.Cj*uFD!^kTja`@@&;0b+c3g-.7DAP0$fj<jhJ<
Pqjbh'o+%l!0Sbr_KnVf1pI096WI16^"cEjmseQq[anabaIcL6J[j6R/2G%h:PB!&5iu@I%k
62S?$uQdLr$;f)iNU).a2%<1TQUe&7a@<u<sGb);kmmPTcYqYY`gN#a-U58&McS:\oes4TXe
<a,1JWl4Dmm2hDd)H'<B(#JA/JoZ0\PIP?P:2Wo:FT7p(p4=8#//=7fL:.q1f&:jZpJW!&T=
W4fVKE/UkT[-2tSThp-1bBNNp&*&3-.R+i#ca1U2=Lk"YV35onbeccCY*Kf0BRCMkD7N5%qe
p8aF*48=5A>XV-H0BPk3\Wc"fA`]A([cLqZXKWi8?/Z@4+*:`i1]A^h+d=nVl#oI/W1Z@;8)=&
&Cnm[fGYm:`Vb3B`94dX*kjK\m-^6n"S7_YUF:c5Sook+]AP*[HI6*Bsf=gW-NH8mA,iH]AAO=
/SO*5epki>o:'IqG++19#eV\jh:BBFaE.>FpaJkmON"f.%_sNOTn$aW)aP:echAqB@=VGR8=
43p<M>.Of:]AJgq>'no=o#*>IM4/m2PPC+M*&RH$jP&Fd=.U:8B5o<g'r&4j^$n*1k3[?@&*2
!i18+<u/*f$'=iiB5`?@4e6Mmg?os%FDO-Mc`E`;:mUs*+IFquTU8UpBThtJp)Gr%_i]A)a&g
Un7L?K\CR8mi"84K3pc'XGb\QT,<;Dkit)@VLq]AZ-$&LQW8M[C[5$W%Jn"OO#<p47le.!;F"
qV@pg?c:Tj;4\VT1MT-#d/@Bs)!V.UIDhrgO5_,gPuP4C]A5-7@ab7>9EO<V(./c<n``TOtBm
tq$gtOY,`Wms%/UnB(\<qf]A,FbLb<R`b/RQ_<,7<jpUm`$DLZR0Z`M=g@-]AU-Pe]AGC%b0;be
Xa(CTD&4/Pg:Epc^.$%Fnb?1<jTm0DbmBuI3>sEDW5'a)G,)Wq&M^[VHH$T6@V<[@UW=+;h@
XtrCVD08#nuL(M.*r/c.4/J;AkP0O5uF1_?(a:Bgu'b'F1nXD,JP]A]AZGD?%M*[Ie^(Dr#q^c
e>flbgb^+=#%Djj,&DGmC$mI6]AO.\TbE?Zg3GZ+H6r^IN`_h:p<1"FUR/;b+1=SAqSU.<Lm&
m]ARc"ek<$]A/T"39<TSh+"YVBlSWE:q]AoS]AB[7N]A2\>Dk0h8;e"17r_AQC/2'Dr9T;C/%7%(J
(4+WBd*I4XD'V[PM=7MH<<3'hZ`<!5bfCqT"Llf+Z4/qqAdf4BGn-pV#IIOk'FOi;&PIl.YT
\)D3<B^AeaYqQo-;tN((obiu]ABY)2M*0p7n(Cj=2.j"/\\E=(R.FNEeOYScKaD<)Xm%*.Q05
')<*Bi5PhRP=UJ";`#P=uVGdsfa;49T2Vc[ZN$*)BW):g<q]AgUEm92[A/hHnnU3RK9dZt.i7
.rNh'VKNa4C)ZVmOSUeg@1t\4q=9_51N&77`80'T+E,g*0EAWO^d<YMWMW8Biebu.L.<p.;-
@QIKP#`'10J6k8%_JpOiY]ADTO%(n[5LlF4d4'^A%sF??C+9;Nq*`75^rU*+B[+_T%*>X)V)O
uqS,\5PRO?AYSkf"Gm';)0/RnOVrsODh&qRPWA1k,ZG/G^<lDYj*+=!)4qJItZ!">XV:LYh$
_Jm*f[)!.pRLF>lHgCu7pXde>&.)hgB<(qi1@tm0a_52\$U$ln%s:JqtuYoD-$,0n@G$$_Rq
h\eg]A7o_!hhOWt73\(9qfW*#8-#BlV_Q-#_\![Hk/U)u]A6WfOPQR6VE3<"ubg\IG'5RT!`=J
T,+sNV`;'jY]Ab.+DBQ$8L]A)pX`oC41/hhBZhK*75eAL`gE8iG4[A=kI=ajP@@\CWuA6:F9H'
Fu;%3\">kV_(.230*>dNG$L5<5>h[;SOo`8VH;0!E)?nLXW4@;,-%;VJ8Z;BR>J`@8J.b6'`
:mD:Y?"L^]Ao]A;DFW?A=eM3(hD]Ao;as=qFjHr3nkj#<jV@5=8ETNIr7&5G.GZ!=V6Kq>"cD>Q
u)\KiWuLk),.]AHdjA;mRC+D^W3]AmTkJhDTKN`BL,hDP\4A3(pG>s3Ai`fO;2s_hWV(ST?j/.
i,o<u%cM(NM'UJ"WnH76P^4q_;s^*,-.(JRE]AAcf-sV#EiA@#IYi4d@=K`;5>sGj"R#J;4#g
\',5Ymhdk,76QD$n;-A.T9JHo"f/&3>O3Wpk$-e%7&,O-3T_95Y3^#D5t4<<'Lgf-f&lH()N
@7U6UZHi9B.TAS^@@JE%O)H9#Fg-cCA2.^`QNj*_;>C1[c#=[@jG?VCs^H\)"/>)Y,<?no%>
a4/2Z&ku[KUeUBlD`ZD4:>?YlLTt7gnL%4A7jZ<.jZUQ$si1)VNd.b6<4n;EMS#7O@nGm4Bf
n+L54YgYE8&FFZGF9At2VQOp=QV@sV'tlMkFiHLN:e7"5'4GXm31hiH0-H/S6"%.*4[JP!)8
L]AiD<3OJXAaT-Ip&oha9nb/XU+H/G$D,CR*;h,+nQRTIdiBEd]AK:?Be6jq.uad\gq<n,+3-_
Kj7R\P!<LE\<^MOM>?p?haQ+u,9+oc?qgr.:$ZQs#[t?_Er8T@Xq0D>HcTQY2=3%@'DGm\"m
nnL"!I_A1"qin1e&?O+Aut@7Bm3-:f!u[*Xu/th3aUq&Q=n#^ABG-7@R;fU!B6I$Rt+E.2qh
pg:RiMmFJeFe7H+d0IG"Kf8"a.Q=V$!Um*8?h/GXPW58CbAA%9@$G>,2O2-;XXM\&4jfNo`@
1__j4p5Q'$eU$9jg(p9<H.M)5W,Zh*Xd:MAY-u1&#b+l1+cN81AZH(3,8,7C'sGJW<R4HH;8
#TeSeJHB.XB\;aB.NI'##?jMG'NIJ6[V9Ai&s[>\p?Z@%$``MfSts+j;7np3uVdnk/*:S7!O
>tN3D\^o+H`[+o!^Mc7!;u*)FR93feem7Q#gP^:7p77Tfd0Fmk!Y"1O1iFh;a4HhZo!l::fZ
_jUZ_XgAXZJM]AX8F=Ra84!gT^Vj-D@UkB#9JX/<7[#QkXZ9DUm.b&5?G>qHa`2kg%S$;+^Bi
M1+cbJDoW%,Mpd&/R)k,("6q>YT"]A(L-fm1ee1*5GLKDMmU"Tl.=Z;/.^C+QJ9#3Ff?)6Ha7
.=Rq/LF_43#h=<Sr*fCTaE^;`%RDd^m\;&(R's=O77tK&GaBP7^r*.0hZGpr<lUl*I]AjjqFn
f67<YFg/`SRN5=h,`n*TXcFQ^a(N"..u2T32LW^gC/pjN4>gg&$4DC>41:2b:/&W%o9obbMl
_:&/e4tj0ePf,WMCLQQtZ9C[*2-tE:\H#`3f7bTS>.K#C!]Af"/G;9U=iACWDpajqM^me7dXK
)7@;:ni<0cS"#C06B^hgt80_r<3o72%mddp4o0^[Zb_^Nph(-h'9=L#VaMjR]A<*,iUoe8)\o
?QaIAVaf[P(P>r[H',s0Bo"/i?j+,?Xfk%$)H*7@YgUo`We`;Q+hTI0US]AKpEBTI+lDu2*na
YG_M'Ui=-T"c:?(=g/'W4iA(+8]A<9"'/NFQYJ?QMsR^Y-&FAKGpt#Y,68euV(34AGp7-:&/N
IFTK:+?_le&en"@:0G+#>DM<]AB]Ar1/`X&%b_l3Y)S7FZW=MC+6@#o+)!)XA<QaH&O%uollJ<
C&.7oN"k63:#*<BPam2f$3GUoAY>?^Q"AS1ihmS9F#n\7]Am6n[c8DDdB.%]A#>)C+9lR8Q?Tu
*AGM2/.k^[08]ACV8LGK2eoD7(_3Q*<M+7bEkjj#L4L3VT&]ADpe.%T^?[;edO_G+nd?%BK:M+
3B:H:Ul&R2;V\lRaGYDUke?=!aP7r4*Rbp("I\.R)kKAgK*U.=Ke#"V#l0sXE]AP-+1ZLl+",
u)am'Ht)f(5rg*s0rVFTS-MW_lp)]AmT%AGZ9uJjg$C;$bdpN@T6/urlE8&TK'>m6!aA-H*Yh
$u&Q$g1!Q=5C-^8:hh@;d5>J<7JVQCH5F&6cCM<;;l61nrE[?380.N.3S`^8Km-)kOkbOmRj
^;TDg?6!5nn>)Z`0>V>5:`O3l?t@XX!PHo@eISmSfiRn"S40s.ME>tPjldj`mdd=Unt(`1j7
:SgUXQ(!`8`KF(h<td,@t^cBOcm&<sB76N=S)3dtfgBD2]APEK?>C$@A>%H^3`o-`J)Vm]A=NZ
KipU-[P[;/Q<4Wj=++*'YOB-=+9d'h,@=1DG8k2ke@iP94ZMJ-cK]AjV1<9QC6!*_"Q:+;J5O
F5Cb:(#7h]AMb:@BYe9qM4XDj"H>#F-"UBT8?trED>!l0s1X_H+p71b/f(RG2P;88dEuK(kTE
X1&]A_))GR6G&RK`Cj;?t+/SJc_\:jPQ1Wj;>0`#;,F(YlApAk&/o'X2knib:nSm<E2a")aJt
bkl&kQ.*[5P]A8R7\'28[Ir-'eF_:;EeHi%a%M3V"n6VA<W#McS.qD<'P;Rr=pgYZ:h;B?eOP
S?;W2G98BndqHU5%RK3#T_=0+E[[,XR@3f@q"Gn0clsR@b?1mD\d*0%LVReWB'T([T!<\.Xf
gHEqs9A3XDKR<ZIuf-H"r%_\""W\1O:bJ_J-Rs)O1dC;[E&F)>kdUF[r[<i[K<+CEB2f!ah(
V+?XH\meP)Vol1(HS8n7@0ERX4#F>Q$dg*F5t`'?&MNA7]A:HUPl#H@QONE-NK"d&4GAb/rO8
E<UJtrC@Up!Y,\755!jm5CXFf,E@#WH34T!?EC0td;[=:i%+,G#`b[tk6ecdVN>)He<k/K\4
p*hTUI8;<2470qc1g\#cBP^VGYt>jufG<Snj<6QoGZu"sZWlE&)fT7=&5C&IKWMoA,hmk"Ot
s]Aqe*t9a,+@T(dZskuo]A?EEmJg*Hob#bZGsW7%"Nj[hX6g!=\WN.j,i%5<="S$e=!>Fa8N@,
6<`6_!?!at72m;up(WG1NC#5**=`n;GX1!P)j'<hGLPr1m\"Xa>[f4HG7M"\%`c1;k*D4Weo
hJqUTDeN]A"0^h,bbU07e)V]AR<YekFVEMBDJXt->J\IVCd=(h^=M(gLNi$'2j0i;a7-?l3)Wk
lMg/Rd+CPs]A!<:Ek@p,#6#<@7JhBX3<>k%Z*i28%QB>:+0>6p7<k)oH[n8)Pb\qWeWq2Q<GZ
R,/FG>g"a3d?l*;#AmC!&d.a_b)_e`q00i"4Gj?tXnmi6%-!L#Q#o;f\BrqMs"NNMf*p0]AE6
r/+5NM"%J)\A\?<Bbg@qu7e(`b$&$2d-87-0qXk/@U$qa.#8[pfM[4m2k6S`R%6$&[U'G?*%
Jcg*gPHN&<EMAIL>);hWEKMUXO$>tXuA+"'L"mpe0e/i#dIlp'ro
*OZKOLk>qh^tUtY::j=,+IT$$L+P81OlF2mFUVt2qa3RFl+_Q\ld589peLQ7@Q[gF0_f_>-:
kN-9d!7moFTn`;>H_HR%rDGQCmB.,MIgG$Z;'Q[68pf(N(n]Afbu2&s#S\#hOpA)<?Y<^iQgJ
&b_/+[FHZN:[lIP"@9b#rfrfU0H"JkF*dG#kS0^nI(iQAc/^W5\WUBI3G6*Uol=N[d*=.f2r
`!<d6+/@4.-.ib5cE<c1pA#^nT[1J*O<UaLLu*&+]Al'EkAIBjbb`Y5lPZCfpmeS5@4?"I?CW
N):Hui*c;K8P`8g=B_5Vep3CD]Ao?5jJ!cC81$CbEhl0:.8;u_WGhoA2+Qq!D;PrK\4ALf:;X
pLQ<Vk7REl;FF[f-nn_=&'h7nq,t]A;<7Tpf72ZKoqLYPG!Phu'\^+Ujr]A'o.13'3lR!,mhe2
)%?-b2ecI[+7"0.KQ2jEQUVFaH1GqMqF<E;&#TiT=NHCXpJC\XBQG?>'jVf1ds'2/"FJQqpE
/1Sm_EMA=K*Lr8T@_g@,o5[X%+,>lmR-bR`=OpC;Nb$eiXm8$%DjpA[N`\qY\YY&iK!UJ9!f
/(RM?UsVF7BEfASi6U@_#=a]AaDf_?"Uj6]AeD>;BPZN3m-pVp=H#PC_Dj@d[_SruG3^B?U4L,
unWiWfjk>EY$G6bI[7SSb__B'Gq\@2&HJ_tG@jJQW`WfT-5$A^N_bqm6D3jO'$8qa/>6n;l/
mIIdU2PnkET_,Uh*..u4&1+kO85M"dU^_PMoos%@Gu6]AS@0YC\P@M@Oeu%1rZh#14U1jPTIh
pl&]A+O352cG,eFic([U&%,/%D?l0V1)5%c]AH,md\=7Y,*)P?9CiF',KQ+<YcR.f;7!E6lVmr
8(-[j(eTL/6r\PG(n8ke_9`[[pFY9)DT?(oGNu#18LPkQBW_<j8RG;i^7(B-q)>1;-r:;i=d
q=):YGt0M2m"?Oc<<e(KE*<b]AbCcpDu5nX(tml.Ft79+1bEM*a6J)PHRh^i'q#h^:Nd(`tVg
!?rkB+<GSkDVFAJ%nTei0r:/R`NCg\%nmt@%gqeA57'rme(qQTNZ\'t><5?sC]AVt?p=%0kBM
#U_p*.Zj?X%EGoEBL*H"3/6SQ.\>slRFr<G]Aq9H^V]A;=1fE/9;bnc$?M3tH`+a>RTE1f%Y!;
^YD?lg#7'.AECC]AM941(f4I1GrLUkp(-7H]A0g-0(OQW?R%;XT3BX9nik!H_B<j@o[ZJ&K0I9
Fcf1:Lh[KWVEOEBW`s\NW9_.-[aIpBSX4'+G)!jI+)kc%GNm$)^rq?K\:b+G:C@U4_MlHA09
`>gT\VfNDoh.&A:)Da$Z6P%D+r4aMCMK+11BM)r\aZ+5N>kJ84,DV*f,b+g+L_"[O5!Iq!Us
?*2:/@%iq69odsrRF,*62A^:`>8)4mK<6e([#-dcW=2rOA23:d&*EBi\C^6^GE:&baqGLjT;
VYd$:$.T(IFpr<PQ.+$&uX!_IX/Z0geVW+V.]AY4[C&Os7S/+7&XZs8mgM<W2>uN&AV^!3#2(
Srpj=7u-uTBmGGMR_.m&,F1kla';5)UeT!`tJq`?HgU6'p##/d@$[2SbS]A5D9<aGF;.c\P9P
=8dsIbg<?4.j(0Y72N0q]AFhN`^rtsIE$N.t^Z*2R"<+BI*jP9.!FiGJ:f(+o;dZtP=`D`q@-
39/ZtE4R=_hoQUhsK>.p/M@Y2;iXkeLWq6/lr0mFQ@H&kHABL-F+<?>\^eko`pC:afr0GVn>
2&+[mCX^h]AVhgX=Pj"D^^[2d=/Z-ho"C`Au+O4al&#)6TkAstc"J]A!ol;+NW(*?Y)=/,8hEK
4o1J_]A3<lV;KDpY+Sg_.N(-c<Xr$B]A^@Qe1/UXabt\<!;GW%kJb@Wk3OYgTHSg,]A;sCE>;&b
h2P,+[X3qrtk:Q&CID:)[r'B?S_QG(/[]Aa?o0Mm(G=gk[Vp_uJ9RZ)[4$,-P)Y)7s&D9`":B
^:q)^65<ij5<cnVe6n30BI4&NHFu1miQd@XS$Jc#hM#,m>`:5^jXR1"[A9iu/ui,n;t8WX'*
R`haQ-n9?-3sUI(%,d'/G@qmCA-=M5d[]A*LaP"OC%#neCd#B+V)F6JfHgPY^$q0a$o1FDEC6
TQIlYk-tfa>QbSHl!EEOQ2J2JSW)e@^<@G9/+!tkGopk5I2*)j^\VAehnp9@fo%L"ZL25W.>
6rpKO;po=kOG))3*#TB`mnl9Nd1kSD;K\(V1.M0d=(f3&M%Fr1=F1ET[#BbQBdlr'<bX)<C)
sXYJ5-M96'^;<RVu>0q=B.g%'k18;o&V05*SI1&1#U7AMtTd!ki*?0Fh%:l.b/X7)#:fEGTc
%.C1r-il3]A/H"EHNkDm)DC[BF?*K0s*:7pHkVZ1R4)dWh4XR=R_lAQG>46qY;8pks5;Z-J)?
!Z8W<#NT15plKp)#@3;;kP!>\MZhN/gLJMK\Q=l3Z[3:3[6[2me<"n(I4^`*K<M@U!8dmiNh
or/'*uF!^sS:\(*WWYbU(b$p?UIi\Gm("*>u<;??M5<VA`Lj)UR3\=E8,jVOR#2-H4W_!mX"
qdoqW-b=@/0%)\5)shGdp]AEl63:PlAIk;W7QZWYZoV.n(b7?d^*6OrQQEq[9W<s[B6UnOMS&
4&O5B68e7/=GR(^T=WsrO3/`%[bdJ,-mf`$>\^p+`PUNHpek5.a`g47o[QO/2%7uLq`L5BRM
PS[XT^"ej9^OA[:hDY;!/5(KgK7"L%o=H%4_E(Tjaag@'F9DJrH@,j:'I8q$GOBV]AYB<b6Hk
jribO'^pbOW32]Ar<f<V_cKnZ%mmYYb%n)Y)1:k`'4*5ZsQ)c@O;$PnLn0V#O:ZcX1QQ,Tp4P
nEr.m65b_j[42c#E]Ab'#1Ibe'e:@sDO[\W*]AQ]Af>:?7t2X<R6Mghe^DM[i.CcHAZG8G/D&We
`<Y'Mjeh)n.)[N2T"BuDpCeeE,M[?`8n0F]ADsJa6hq\@9"$[,()U06mb,@jYCBFDMH%;j^9h
>=N.,N91f0T+08X5[9I2bQ/J]Agoe'hZ-243r4ZgA<AiSgJEbOM+iF88Au_GomAqKrVD4H;9S
D#aWseo@8.Uo52N]AGS;d"l'^=b[$SDc4e/Z"E7D@P9gNQ!r;5R/+A_g(NF;5C(\613i%@nSJ
'EaPU$aG$.hs*8da#6XUpTo43+MdXDC&JN^Kh9qqHF)0_8:m3L^If/'k406eU\_N4rdoU$F[
6K4%bcI#tX3$.]ATuoO^,dl5cZZOAC&4i[Y8D2ON&j=d]AQ@UK(;f+5$.(S-4p3O36)u*Ve4,0
*B`NMK4sT#sgt=`f\Vs0hB)jl&%D'=(,!Dj*'k:@r'=HiargYMYXf(<U-HN=a\X'T`\jW>W3
"r0,R19+<HtQ&Y=A)\G\_#W3$^jZ#(_9q)R*qeB3apqk`"99M;,2n-hl+Jr[?)E5`Snb-B,j
[;_#qj&OXL'=P'E%:Vta)]A0PZ??,_Y5B38L$pd^QfB<r,F6Fq]Apqa>5"GB#G2*gd+^>)Am8l
>_sK3iFno/>$Dk2.C=#i1$qJqV`fLJ_]AB?k]AWbMmLqIr2el4)U,IMH6tqT[:UfhR'Fa+[[;f
s':b'aU\+EAk/mu"(>+^QCq:SnrHB,NjT0OahD&&Nm8ApK$Zdf\Z.[7c"i0fF+oYE(6h2.hr
rgU4d(ZHLH\"Xp&cgO3n4Hmp/9bNCmd9PL[8'POjVm6IDlC)F-S+/B\De]AI-@)q3-m^q@)Kl
-8GK-<RX]AMXLXN]AR[IdlpM@HIf$hI;QjQoOmG3g`N&F&@:op^4t#D[;m+cqMjODbU'Fb]Ain)
,u<_f'#6#Hc#50m+T)B?HPA]A;)U+5RUhF_8nWW>p=8e*0X*i?7j--TgkO<P2@>A";X@K+]As.
%4VqR)Q6Q:L0FDh).?lu8tDOZ'*JV9%G\loBPt,(@NtVODPM0VK,Jr?m$HW=D'=T5OV>33\f
:64)(9*bB6u8GY'#^G`T)2-S>a<D&MGqPdFA.VNUjo[`sUWkkJlE:!Hu-ElIK[i9k]A`^GdIG
:,:nD,Ah,TN%iL5>mJBMN#3aQh9i\"-+YspUZj6;t.sBCGL+,cFR$($l)/$&/Ncm\/,WBbW+
!L?Hk.2dd!adBKal5?e0`&+pP`;9oCn`]AmONoQ[)b]AQN)AJ*oD0V;A9;=!hUuG(HS3hG_Ane
38Q"[$`niU2)rIjci3aCTms>"=9=[Xic:Dg#:uj.LF?@@Bq3u0P@I0*^Z^&kJ9hPI[1-aF<!
8/frnN<>0)Tk#P=PCKrWH_#GJWnP>;!_/_:03OnCsn!@iM^dc+.#>&'(\>D?N@&.ClU8GRRZ
2`XAJ35iFf!/-h66='Y;jM(^5'"XOs;)QDErG1?9]AVLCh`27P'$N"8'=k>dE"?_k35O%l9Yb
jOm*H9iWpUepG&>7[TI0WC6fXu[V@:NdUlP)]A8&""at,s4:cIpb!&8!Zgl(orB>XCC34'k\<
moOd)QL`QEj'd%@GZQ?>24C[,_lCM&4bT06)B6[OT:9%]Aa_i`.t:+M$%sLCL2SK=gbAG$=k3
Q(\=s&kli_%ZiXjf2W[qTgLcY;PcO!=nCsjWX_[YJ!QKg)oS3U/Y\HY]A=orR7#jE?Jo]AM>&d
jemNd_]AFFSj1G]AD/m!R<H_sU@#0n1AbnmX8ehJj/8(-h!]A=Q=h#,-;V5#m,k,o%M3gce(F"F
/jOJ7ucmMM3((_\r!?WuraCD68&)=c,)ZA+<$`3>.=t9l_>!PNDZl]A$@g'K5>aW2hM,.-/EU
?i1%6IkKL_kmc<RQL$PU\9bIS:SdNPEIl6qjT'i)5+:GJ-06!QlRBL/#s&LL9/mn&1rZn[Ab
lL&JVb%8lsn2ft$BR:h;5e(qVYPMPT`S3Uht+'2dm3?^r)9Wu6uqCF4L8&"60=?G.j;RfJaY
muGX@WT,skUk`[>@*[ODU#h`>6X3)(o-X#FY;FS3E8Tdmd5YY*j,]AfVS'&EV49ZnBqJ2]AE)o
f!Oq3C\^0Er&o9-9=U&8r]A.(0<JRpoBoN[=Et);Mkq(V$I?0XJiM![.ES;CdM/1E%o=uaNH^
E(H%_I2M4P]Ag!:.d/%Rd,r!N]AK.brAON,`Y0oh)#b'/G&d@DmB6cN$M1F,&5"s7TbI_L@JY3
'U-rPmMR5WD0@&j+`kUU^1hJ3EuZAc?*-_'1nJ)>$B,F1nA#pX4jg?Z+fZt+Lh7-;Pj)<^LE
csMn0,6DQsZp'n=^2Cr\@g7?Cd6T[#-&>(Q75gW[#"=-d-/Ib\KLm]At2g8(F5ik[MDiA76uO
dnaAA[bfSmi?lp2,5r,Kq[$_Q<RB!lqL)1LM<:Xp>C]A*oMLVZp9";a6WBi0_f;e9TkAN'>O:
XQs%C;?[_c'Lfb#AORF(qt14Jj4S9fkRh>E/Ru[INKt8=t(q:*G7`q7ShP;R>UB8E&Hq+CAj
;rLaf--jnENQXWU7'/20\'MpTT$S&4tp/^u^`@4X:6ZJ=O?!f36^@B'&5)XTM:Q&;V[_SNFf
$TJY$4nh5jsc+'>'X_#P3H2]AaKNs,h3+Y"HOPlqc?4(9i#\>"ZW./HE087C%o*!=^b9);bJR
Rp?Ej1'U(s3@f#Xh#HFOnleqVgcg#VsWn=ej!5mMI;e;u.,(rQEl8j`5Vp?BJ>n!(*.VL6nS
C7gp^ilWQ!_DSYt$kK2[`itb.E=64T_gi%Z"4*"%Dd;!'M5V6t9<1,<pC^"R(m!D*RG^rW#t
c5d[467T@4ja@CPaR6\:7"@NP\%m-gOS)[H91`SKlKN6!n]A]AJH!]A]AXXkbY?h2QR%^0CSs3"[
hNSes=eNOGZEQ?DB,iPaE5>(+&OMISe<r0i#Sj':NLtnTBe4oMD;*C>C$Pe[g3\Qf?A1rCuB
-sqB:N(LUmb4OuDGS0AB]AiiL@$TTE67.F"rCZL)GUqI)4hW6CSju$SPIbrinB`T>E^CqaoIi
$^]AF3bm`Ue,hj>O-u6$lCNGm#H%A1qtF2K6mj,lg<!/K$FL.lQKY*\6Ar%JWW?[2Ho/b^=G5
SBu\*V.6?%<:6"rjd'^6d[B[>Fo[<`:AY/3if-CMOB:2k'2#F".^:g!BjC+TTm!M?_7+588/
(F$A7[g1aU%?BmtoBVJM[BG)E!7I^0L=J>noNYaH(ULZ`0K(&&#r7eUb4D7DO&tUY(RfJ:Rd
NZ0YMOr4Otn(j<<DkedOOfH7_&fW&XGO35L%P<Da%a)Bf_$=-G'kZ5t$_pAYYRLUgQ>NLl)K
hnV!IYmZ(OJ%hr`[<iR(B0(p#I]A,2?aJ<iK&s)Ohq*"eQ@_Et%b@$0r2%'UW\E^nl*>JDek=
nEV8CGcqge#OqI%a9M:ZO.cqThr/$/t-([2On52a9M[S6_TVZaD^5/*MUmf)^35(DqDVak"F
q11%l>gSer%Vo&F]A3:MiZ;gjSR9(H0/Ffpg<n"LoO&lbaJBIZ-.Z+/4"]AE&kB[]AQKW-I-qj+
7@-UH'YP+2U6?kt?M()^K;flHYdZ,9sP[W&5Pg9r7YT5on":?,OS*9<X:$CE22]A$K;g%dac[
&R@q)VWN+7-o6S9\/2><_>eVZ1Td_?_ms#HfB03J7CbWk7pr73Klss4a>NFP2Fu]A0(hNpu2>
VY!P=0tO'ha9^u?VCgei?FOSmd[9or9B7C9jtL8kaYO;fr"T%%<r:&@nA%\2YpB`;m,qa^\a
%h>18^[>7'R0g*b[Af^Gi:_>%PcA\hBDU]ASm5@Hi5aWRka0*1eIXoCB0@jsr"?MQs-gG#qug
FIh's#0<uqkksO;/e>u3I7t_6>4f/G1P"!Nlk3hcAKA5H\1o>:V!h[IA'>AK"8HPCp!slZ%7
VI>7%c>&VpbP7J7iV*Q%Pp(#@ahphWUQlA117cP*iW4fr6)Wr32l#3/N+BFYOBfKME6X"\f:
_<mcDbU+qS*<FF_i5p%i?!=M%*I?3VT2dA5B:t+gFUK2Y?pjgamE3)%\[DUaO%Cqo;6cLX#F
FP7B+#I>jBqf,giKjI0WA:I75QUe,PL>*k7@Q2q['-->\N;8lZCM5WLek:adVc3Ul`)G0_hW
1"[+5sk#=s'k"l)>0]AF&SHB50I--PNoR?Q.j_hJ)l84+K^8T;%4%,`d>'Vj7Dr.39SPK/dd!
(ML8!as@.%ooFB?f5L6fik9T2OMU5&LJRH>2Ub)iB[TP;+m-#*lJY@COL]Aho!Ie:4@NaJ%*6
Y8%X'1uI2_=K`H%G,\8AThVq?=MfBG<YP:;>J/_=Qk1/@!kFo$RtLH*q?9>m_rlX(RVSTEfT
7q<=cK'/*AN(=,Z/4uWhjoisl\jV)31)/Dk3??J1"(,*Embth"FWbs^5h114Q"%2l'/6isI!
aX<Vb_fI2IKbKY!8pR/J+Lh#cBj9-$upEr\Z&.HaSJQ)>%2HNJ.FZGpg$;14a/,C,a[W8"2A
ISdj7MjW=CSgs4Rh6'oi,X;3"\uZ!\mB/N,PAJMCH)(mXFm([./,8)80u2:OcQ]AD,c??QcD<
8cuB$*0^?6lO2$>K43Glg?M]A@:l0ZUC(i$7:KEuZj*sS3@NRiaK@`.oWk[[A&ZUf-`BZU)nQ
#.INZ-GV=]AQ%c5Be*VgXRHXi/2<Db-bT5]A[U7=p>5t2iKdSD+.oMLVCr<4kcY]AOXn2i]Ag'g4
fP)8tE^^15f>O+p3XD6R/qHrHT'^l5g@QY#^)M\['NV>O/mZI*6F-M6g^fHN&:JofqXlKN9A
66OtB%HjihBHD&c_q<VE>\q\k8qMJWT-=-X%7/i=`_Eg@&3l-N6U]Ab?gV>U`]A&*$ETM@t6UK
BD?:qP@V^P!g5DK+kmI9,i<IsZ%N0X/3pSsHoJ5UhWOaR&dhga2i!Kr\Z(Qs%X84o5P790""
H/hEt"Z0uo8b)NA<o<UEVXOFkd;O\Y="JAb8!6s!j)7&`ZIo\Cg=cSe!T<-XOkRt-F/HpW[J
lGZ?P^h.e7Laj%454#6`#,t.;<%%CpeQIO]Ab&QXdr\'G"\PD'/E!2&$:sc]AQneX6"ZdeB'cR
r&9U9lUGuUpY@>+AE0J&8Y/a[nacqM*I._fZ$UZ'(OhaF%O-`!7Y+HD&ebG)^dnKH-nJs23d
'b0cG`s@fJEqtCnd:$;4HEa.[#HMg/A$:T4US0l:@Yqi?/o'9_CH>dL]A!?):@V:h3W&L2c-M
WYK5^@Z>N![2p`BA2A,J(\(k7K/`ZO6%n]A1E7r-r>6i%>G;D>r;egZP*$'Ym5JSNI$ipL*T)
T'u';:&)oP\*r%;q6,;&7&PFA[NRTmo,[e6$*X0-T&.E'I!iPdn(et4M#Ita+o4RuLZf8r+/
1tfIu+(8r?;#)oS;W@d3#)I7":$l&H*dK6hH/t&(1D#RO=kWBQF4EIOtH;DS;hBJ(X(Bkl2C
FBKk6r[p$qXbU..T]AjPAoY5$5k49#Z~
]]></IM>
<ReportFitAttr fitStateInPC="3" fitFont="false" minFontSize="0"/>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="219"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="38" width="375" height="219"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="true"/>
<MobileWidgetList>
<Widget widgetName="TIT"/>
<Widget widgetName="report0"/>
<Widget widgetName="report1"/>
<Widget widgetName="report2"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="data_title" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_body" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
<StrategyConfig dsName="data_fwgcj" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="true" timeToLive="1500000" timeToIdle="86400000" updateInterval="28800000" terminalTime="" updateSchema="0 30 8,13 ? * MON-FRI" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1700204376960"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="9d00bdec-2b8a-4b08-860f-47c21cef94f5"/>
</TemplateIdAttMark>
</Form>
