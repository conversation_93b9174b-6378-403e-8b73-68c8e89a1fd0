<?xml version="1.0" encoding="UTF-8"?>
<Form xmlVersion="20211223" releaseVersion="11.0.0">
<FormMobileAttr>
<FormMobileAttr refresh="false" isUseHTML="false" isMobileOnly="true" isAdaptivePropertyAutoMatch="true" appearRefresh="false" promptWhenLeaveWithoutSubmit="false" allowDoubleClickOrZoom="true"/>
</FormMobileAttr>
<Parameters/>
<Layout class="com.fr.form.ui.container.WBorderLayout">
<WidgetName name="form"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<ShowBookmarks showBookmarks="false"/>
<Center class="com.fr.form.ui.container.WFitLayout">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[
window.url = location.href; ]]></Content>
</JavaScript>
</Listener>
<WidgetName name="body"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WScaleLayout">
<WidgetName name="lable"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="textEditor0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<Listener event="afterinit" name="初始化后1">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters/>
<Content>
<![CDATA[var val=url;
this.setValue(url);]]></Content>
</JavaScript>
</Listener>
<WidgetName name="lable"/>
<WidgetID widgetID="b2608016-a321-4315-9c55-2c0f1845bd9f"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<fontSize>
<![CDATA[9]]></fontSize>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false" nfcContentType="0"/>
<MobileTextEditAttr allowOneClickClear="true"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="36"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.container.WTitleLayout">
<WidgetName name="report0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="report0" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ElementCaseEditor">
<WidgetName name="report0"/>
<WidgetID widgetID="2cb5002e-620e-4206-af23-0c4bbbf4daee"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="true"/>
<Margin top="0" left="11" bottom="11" right="11"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0"/>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="WenQuanYi Micro Hei" style="1" size="128"/>
<Position pos="2"/>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<BackgroundOpacity opacity="0.04"/>
<InsetImage padding="4" insetRelativeTextLeft="true" insetRelativeTextRight="false" name="ImageBackground" layout="3">
<FineImage fm="png" imageId="__ImageCache__9BDAD1A694F2AE09931BEB5B979DA1F5">
<IM>
<![CDATA[lO<9(kN.ld@UNU%p%320!n&&RXMhpZ,a0ckg]Ag[)Sh?$H'm#O$mX9@nDg03/<C4dC'hs7\:U
CrUFIA*cmN+n1!@hUKFS0]AXkEO<r!!~
]]></IM>
</FineImage>
</InsetImage>
</WidgetTitle>
<Background name="ColorBackground">
<color>
<FineColor color="-10243346" hor="-1" ver="-1"/>
</color>
</Background>
<Alpha alpha="0.04"/>
</Border>
<FormElementCase>
<ReportPageAttr>
<HR/>
<FR/>
<HC/>
<FC/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[723900,723900,723900,723900,723900,723900,723900,723900,723900,723900,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0" cs="7" rs="18" s="0">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$tab]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="0" r="18" cs="7" rs="18" s="1">
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$state]]></Attributes>
</O>
<PrivilegeControl/>
<CellGUIAttr showAsDefault="true"/>
<CellPageAttr/>
<CellOptionalAttrHolder>
<DesensitizationAttr class="com.fr.report.cell.cellattr.CellDesensitizationAttr">
<Desensitizations desensitizeScope="0"/>
</DesensitizationAttr>
</CellOptionalAttrHolder>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="24688800" height="43891200"/>
</PaperSetting>
<FollowingTheme background="true"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
</ReportAttrSet>
</FormElementCase>
<StyleList>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="160"/>
<Background name="ColorBackground">
<color>
<FineColor color="-600992" hor="2" ver="0"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="WenQuanYi Micro Hei" style="0" size="160"/>
<Background name="ColorBackground">
<color>
<FineColor color="-13531737" hor="1" ver="4"/>
</color>
</Background>
<Border/>
</Style>
</StyleList>
<DesensitizationList/>
<heightRestrict heightrestrict="false"/>
<heightPercent heightpercent="0.75"/>
<IM>
<![CDATA[m8s`q;cgOoLk$4k>$=aJBj7ARMb()q,"/Q!\e)*tBi0/oMAT=06QcDm&=$F66."=$.g0[jJ.
LLu!""Jm,U,QS"@>FqOB6ZQ#7/EpS0[9P)5o?,*m6T#leZc&Z<#O_-e[DVp\t3]As7ZJbZ[2b
$c'GM9$35V0Y"s9>(B?!Uk.XKIFh\=qe9CtDkIOq\[f%p0Dd8N3:*WZPafD,#K`)e6_3N=Cl
%;(4]AAjVaEVE#=R>l1VCK"`H[>Sn105GZ(>-m2\-%:d3<bJQqGaT]ALFlUlmoT/qqD3g]A;o/h
OH^rsLkW8D1\&M:@+c#7<0?ofWn?UZfQ&*9i;5*A$bXiLnE2+Yjpj`\h+dq%:E![HXXA/T,n
!!)nUSWt*)__qPPgG*ROSD8P6!I43AV#dfCD1r.toF9UW<aXpW!(LTM!-!D=\DbQYNBR@35W
<9?2VhWrY\qUKe[i<,LLu-*#G7Eu!?A3>LJr;qloD7R%esbk0^q"o<M$OG.?bi[1&RSHHXTu
7jh4IiqP8i"JsU&GCg.#nbP=Y,rk]ANWG(7+<I<*_.R5@Y=Ic&`UGn;l'At8_Aj\t*uo2g$;Z
[:=tR;>'$r]A@U_c\#VaGMOrfoGXqWj;"6:h.6@[-<EAhpt,?3ihYsCS;faI=D\%cmIuK5'.+
U0PY&d;rs!qb39dI.qmVKchZ[o<^7s'dgUOn\ahq=WrXJdMERH]A?MA;#tMh)W/?dWRbrC/GO
o'bhj^Q>6]A+'TUh%6G(#pDdPfBmA4PRJ_DXoY3l6NG>q,?OE1Xdg'*V;i4t[01[2sTD6eID*
"5]AE0/q&ZT]AcoKa_TZb[SKk*LH06G%G[icf*NW$a*EeT.J<oS_q6!;l\5#<b3uFLh]AFNDJQ6
-E@.J&133gJohJl5QEPI0hRTk3?8HIPR\Fd75@tm5IHPZ=.]At9JNAbE'W<sIMU-2[gWXlgZp
?Xi"G1q]A0T<'bQ3RHurWUcNCq^9X%TJ22A2lR)j+c!in@)MNKR/pt699tgbqVp`HXuM]AUEc7
>'Egum;6cr1oM/+UnNe'_mQbTGZL8<*ie^S7^BYpoB8bmatQ1Ib^Z`*HIID1u6TF_Y+o3h$7
8=S=>J<7"WGdoFe5T0W'<<R@Kac(o84tR"jUIX]ASP"2Lq#r;-@Mc(pj*^gB2"I@M6/&G':X^
Vi$e(Tb,<2&j'<"kV2^L/?4P!)Lki$uqB[R%d8WgQ/^2WQ>,RYfYe*(90<35!<OOrSl[_<>]A
E4IbZtcqke=E,=T9)?'qQ!j]AZJW$\.>T'&o%NcZ5Y7m2uNGt0o)J_`H08tXO4H$>R[Nb=[]A=
5?4<,T`bP+\%N-eNtG\Q'7"lFQb\/T`F>"'P2t'_7[A,?_h_E0T7bB.B:7j$P=1iX=oM7l`V
Kifkcrj@I,dE\-IA\nH*I7qU@WopMCYTo8g'[?'fa@gq/[ccn!Cm6V%oTb6hL=;X`\-;18q!
p4aq1[G[-NGJ'M+eg&;;8a_hoUM<?tN)T"IKR_0O!PNKjEh85e]A1l32;PrnPG@f=7PqM@$:#
/D%-8Lu+[Et[c<cHCQQUqaZOD,FIeh3<N4U%98^EKn;[jC_T2*e[#b:*/2+.`D:?EgQ@d\O`
)5t$MLK/Rn1-[Y+=mQR%C+=jJV\G8We9ljBp1f`sE2Z-=Kn1kY1^Z!:U9iWDX9t_q#_oK#_7
#HE4_%\Q`a.&,+*QXm_Of'4o`#r2hm;+5C8^jsj)&0+C@SfBo?'I#&9@;83+S.LPqaC)c2:q
g*Ue1lt>j9?a0`4XYF92!E'$cdJBN8L^V6XkJUfFCrU0SMmTHA(brKEpjITAjn'RZpKc@m!!
1E%SmL*Gct**aL"XE>W"lAb+YHN,!BG\c;diq;`05'3Fa^+<N+T3Bcq7S.250Y(sVV4Pa$F7
9TcSL3B1d"nPXQae>Rr4$=$A2Xru_;*IYYE]A$A(Js9FRhtK-dg&,6Y=&"4IAs_Rb%)YQ>PNo
&&2j6bUKbHfA><6@hO[-c=9^odC%2klOJ%GO%]AD-0"="fE(9-km0eEp73I>GS&YuctP2suQ:
ZcukbmHl+WJ^"-e(arlURFkJX+H).+R_Ym<)!t09r6*Pco>3$+tk;']AO2E<BTJ03^"t^p06t
&YcZ9G8r,ZCkfCP=5.WtT6g59!6eL8&_!5aipl*BU$0dL!B7_4BZANt9JTn>b'q%J5"2#gVh
W)DNk;4o[<]AKbH/rU8#IGL6Ws;T\3lTl]AOe</j<`VI)W[n0iq32MP1$5,LT(TmXT=ZLeqa5F
E,@-J/q91:&\_[_WLiU2)&S'c\gs;%0)%%&OSQRIs)nHMH-<+[,XR@7hWeA[q)(Y3e!CDWH.
]A76eIRQEN?uJjdmT5^!mj$.bD_hM>$/hVTi)SC)l_F[+@sFY.BZZ'r\A6j8g[/?Kk:FtuPg>
=<$YDBb]Ad87*rt+g;A+o9rbIlk?q?JmT1OLIGe6:&Qs:7FD20gZAD9;&uJj:D2)plmVeuU30
k)&.69"1k-U>NI`ENYaZr1d2W58i<*GHf8E',NWfkiP`k\a@I`/VB5]A7&SROT!1ZC2r)D>Mb
@]AGlTkqq@dNVs+f*t-j:h>08NeZ!R5-E_-;nj-7%<)#.V8#sC"lI)rf[G4tql;2\_b409=4S
&!O'DER,F3r!]AXuo]AZ.;`ej.R^/D@6RV_c+D0^YK&dR+kI/2Uj;De]A]A-C.8`/dr3uraIU[N<
sSW:G5q%&AXW(B=`OTD\aV&8k_#(VQEZ#Hr%@:gsmk8QS=okaXm`ccdPU+]A[A--Q=YLhH*(X
Z&"BkV"l3cVrn7AQ]AJ;5=W,r<^cHSU-ob,<n>pQ-PkAG'7++tLqT!)AdXM_71W+TRSLgIYMk
o&g<Q<CG4?nZ5+LR*Kme^_]AC$HL\jV?&Z^47sZN[=(9aQ\`e)]A`LhJ_O>\t[&V_TFmgh.f!o
VpOaT+i8=7/$<Y68l-sdkfi]A1)'O0d$<B;XZ@!UnqnVI`q)4^#/Tci/meM^o*1Ap&PoQt6^9
pm?TJftCQ-ERBKU#eg(qFdM3+tEKYjdP9)s>S&;N(#gb]ALi$g`iag+Jm[d;__tGDD=\mmo/R
;+,Wa@?UP:-JPLXi3MlCDWX$a69gs%f?q3e1R^_]Aac3GhK%B_o^TjI&%lOn?n-2%)E[<^km8
:2-,JoUMd@Kb_E0WQDS-1!'Qlqgk@96+-Vn;-pU5-nI"kZ_t[$[_4[ba>Kl2,mcoN!4GiVBm
?,^On:[Dg.W\Lj;)XeC_UL>rV7ZZf1;+?$F@2YkM+&F@V&6,:)V&EI:02R8p*.Z7I<$;a3^[
(mU6&<*rR=;1g&ESKO-K-M=d&L?)H<8FDTV-dDj\g2:%!cR!WCV&cg@AGLVc0c5!SAFX&I0!
Ct3)O?jbR``#[;/4+oPX/5)Q&M'fe>(km4bG&CKe5$GjRG?aR-)4W*d9-ff3q17HAb68FV4e
Q"D6,EkL`Y'Li1=rg_*UQkM&C%b\9@s]A^10i%di4o6J!LU:0-j&j@tSdQHkjp#L)Nl(C([gD
=r\[NGUX#r<#DQ_JW]AZ5d3WBCs<ke5q^ZG)^7qI?L>u-QYK!oH&B_W1(nlfLMF_?98[2^At0
^-e:]AWOe>,"T*^_bISUNoVmP#UEZ*1akie$dVD9>!8\6?NZ08U*$l`9P8I:aY]ACgY@5(CK1Z
%AIe>Kh%ke#d#Ni-7H'S`$UkrrJ;CPA]A"[O'!\Wob1hOC:"(OD'gHT*@:f2?::Ti)EZamp$s
V"Bbik4YS(>;Wpls05eL34<3u*_0'=m7URjr0/>OlM;XIN)_VeQAZ>a@<3@DB]AhTmJHk/`f:
gfUd&$5GT3VL2H:".5s*u/\6re[WFuGP2",n89$j+cE=\a&!8i)RR<l)k/5gPWqF)i$^=tH,
<I*rVk%u4$l=?JcD/p=>Db)<UUFI;+<N^f)*3^`(X%21p8aPTM3Nn+a604t6XZl0fNj)k3bf
QIk">NSGXNG.>2RXh@(?m<1Xu6YRK`LY+,MjHGXN6)@q'hY)qo^6%QMg2!jri=n,4NjXjT$l
f82X2`)]AkYNW]Aq6m[QYq?TsUe1F$Q/,"5V*9fRc:St>F799F*`T11G'jDL>]A05UO/P_qi$N]A
W5[hWc=2Xd7/me'q)GJK#62QZ8g:V->%/2(%0:-HVU/E7*>*'7GN!&3Nn&oI`Gabj1"=-.M4
&a;;<T*KFK#o@5PC*\J.&RJ`s'@ng_^G/&0nD1Pa;K5+[ING>Z)R#eOHm#aY7,Oq_';dbiSQ
E%&sU]A'RO7+#P7-P&'5H?W]A*TWpH#'5ih%3BY)#(%;,r&-Uk2lqEE$F%2h#O+pemjMRBt\+l
5Q55pe`(Zl(=&D+<e4*g+Hqq_dUPFGO7Sn#utr?sl57ZPqX;tREde]AfWYP,qXrg5VeeN;J$t
AYq[s&E6C_=U$C]A:RlI19U9JKEhGP)DQm1)Dk)&aE\`q%LUIV.TaCt?r2S.;[(TTp#<jCu/A
lYB`8.W_(??T[j)tulHI0\#nTW_*")UFDh(_JA7WoDY+6O@]A1L9@>WFA.A/lNdsf_!%naKMN
r$m]A3S)P%I;p]Aj]Ag$+/\+>Q_&@hDtCkjso;>-q,Q]AbBO55J`k?W:8dspFLjE%T'"Q]AU?k/i*
K5U]AHhA3NFO/&/Ntrfp.rDTn59-K[P__69\Sqfdido-Ae"/R=[W=.c]A#=ti*Wj1b6pVs+=I@
NA@gREMcWUdVg:UF#k8A['n404J(tE\EZLb*o'52l?2?r#N1\;sC?3)[`HMb\3,(CY)oYk=#
MLW'AkDgW_qTR%:_FSlT/Tup9o#PgZAu@JF'a>"ZAB-?EX+bcp66sAunkS1MIlOZf$@UID;-
`(:W`OEPisT#FV\,Z0$oWQbC9t,=[uC/0[$u$D'dK;C&^-iERZ`Dp7+k*HF1*Y<.U^%eQl\>
Hln-KJ=nDp0.),%srAheb<i4*+IY?@SU)\K.]AQHK(?8H94#Y8Q]AL=Ri'l'f=_&_c$J<aN@?q
,OdUU'l,rpFa$aX7Yc$X+ns0H'aE:@s'5sbBj)"g0e:+]A)!M%KS3_a+d!\j=,a:65JL$ECtc
%L?BkHZJqX(rp,I25NP9Yd^MhPEi;*pghgt)TnGg.W.h"?E:'3b$`&%$+6NBZG2#B[N!9cTo
T>-E1NE;Fibl`K7s'In^U,CobNH:/YB,U@6r[Wm-^p<N,s0"TP0K8?Ii%m;s3_u._2QZ`')@
*`%kF?_m.pY8ZY'[;*AF<M^:_2%XoftZ:&(C^S5WqpSe-#A/4\u[="(),>4L2TEYhe0DB^TT
dc@?q3gCKnTDZ~
]]></IM>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
<MobileFormCollapsedStyle class="com.fr.form.ui.mobile.MobileFormCollapsedStyle">
<collapseButton showButton="true" foldedHint="" unfoldedHint="" defaultState="0">
<color>
<FineColor color="-6710887" hor="-1" ver="-1"/>
</color>
</collapseButton>
<collapsedWork value="false"/>
<lineAttr number="1"/>
</MobileFormCollapsedStyle>
</InnerWidget>
<BoundsAttr x="0" y="0" width="375" height="524"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
</InnerWidget>
<BoundsAttr x="0" y="36" width="375" height="524"/>
</Widget>
<ShowBookmarks showBookmarks="true"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="lable"/>
<Widget widgetName="report0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<WidgetZoomAttr compState="1" scaleAttr="2"/>
<AppRelayout appRelayout="true"/>
<Size width="375" height="560"/>
<BodyLayoutType type="0"/>
</Center>
</Layout>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="经典稳重" dark="false"/>
</TemplateThemeAttrMark>
<TemplateLayoutIdAttrMark class="com.fr.base.iofile.attr.TemplateLayoutIdAttrMark">
<TemplateLayoutIdAttrMark LayoutId="9ebf6aff-ad53-45a9-a175-9633f4162a3a"/>
</TemplateLayoutIdAttrMark>
<MobileOnlyTemplateAttrMark class="com.fr.base.iofile.attr.MobileOnlyTemplateAttrMark"/>
<NewFormMarkAttr class="com.fr.form.fit.NewFormMarkAttr">
<NewFormMarkAttr type="1" tabPreload="true" fontScaleFrontAdjust="true" supportColRowAutoAdjust="true" supportExportTransparency="false" supportFrontEndDataCache="false"/>
</NewFormMarkAttr>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1698913439836"/>
</TemplateCloudInfoAttrMark>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="5094368c-e1bb-49d4-a502-2e555f32aed3"/>
</TemplateIdAttMark>
</Form>
